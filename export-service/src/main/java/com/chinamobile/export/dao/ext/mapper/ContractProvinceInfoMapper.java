package com.chinamobile.export.dao.ext.mapper;

import com.chinamobile.export.pojo.ContractProvinceInfo;
import com.chinamobile.export.pojo.ContractProvinceInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ContractProvinceInfoMapper {
    /**
     *
     * @mbg.generated Tue Jul 18 10:30:29 CST 2023
     */
    long countByExample(ContractProvinceInfoExample example);

    /**
     *
     * @mbg.generated Tue Jul 18 10:30:29 CST 2023
     */
    int deleteByExample(ContractProvinceInfoExample example);

    /**
     *
     * @mbg.generated Tue Jul 18 10:30:29 CST 2023
     */
    int deleteByPrimaryKey(String mallCode);

    /**
     *
     * @mbg.generated Tue Jul 18 10:30:29 CST 2023
     */
    int insert(ContractProvinceInfo record);

    /**
     *
     * @mbg.generated Tue Jul 18 10:30:29 CST 2023
     */
    int insertSelective(ContractProvinceInfo record);

    /**
     *
     * @mbg.generated Tue Jul 18 10:30:29 CST 2023
     */
    List<ContractProvinceInfo> selectByExample(ContractProvinceInfoExample example);

    /**
     *
     * @mbg.generated Tue Jul 18 10:30:29 CST 2023
     */
    ContractProvinceInfo selectByPrimaryKey(String mallCode);

    /**
     *
     * @mbg.generated Tue Jul 18 10:30:29 CST 2023
     */
    int updateByExampleSelective(@Param("record") ContractProvinceInfo record, @Param("example") ContractProvinceInfoExample example);

    /**
     *
     * @mbg.generated Tue Jul 18 10:30:29 CST 2023
     */
    int updateByExample(@Param("record") ContractProvinceInfo record, @Param("example") ContractProvinceInfoExample example);

    /**
     *
     * @mbg.generated Tue Jul 18 10:30:29 CST 2023
     */
    int updateByPrimaryKeySelective(ContractProvinceInfo record);

    /**
     *
     * @mbg.generated Tue Jul 18 10:30:29 CST 2023
     */
    int updateByPrimaryKey(ContractProvinceInfo record);

    /**
     *
     * @mbg.generated Tue Jul 18 10:30:29 CST 2023
     */
    int batchInsert(@Param("list") List<ContractProvinceInfo> list);

    /**
     *
     * @mbg.generated Tue Jul 18 10:30:29 CST 2023
     */
    int batchInsertSelective(@Param("list") List<ContractProvinceInfo> list, @Param("selective") ContractProvinceInfo.Column ... selective);
}