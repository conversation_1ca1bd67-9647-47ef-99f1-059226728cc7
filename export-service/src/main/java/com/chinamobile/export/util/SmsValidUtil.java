package com.chinamobile.export.util;

import com.alibaba.fastjson.JSONObject;
import com.chinamobile.export.pojo.dto.RedisSmsValidCodeDTO;
import com.chinamobile.iot.sc.common.utils.BaseUtils;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/27
 * @description 短信验证工具类
 */
public class SmsValidUtil {

    public static void checkSmsValid(Boolean isAdmin,
                                     String exportMask,
                                     String exportPhone,
                                     RedisTemplate redisTemplate){
        if (StringUtils.isEmpty(exportMask) || StringUtils.isEmpty(exportPhone)) {
            throw new BusinessException(BaseErrorConstant.EXPORT_MASK_ERROR);
        }

        String redisKey = BaseUtils.getExportSMSValidKey(exportPhone);
        String jsonStr = (String) redisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNotEmpty(jsonStr)) {
            RedisSmsValidCodeDTO redisSmsValidCode = JSONObject.parseObject(jsonStr, RedisSmsValidCodeDTO.class);
            if (!exportMask.equals(String.valueOf(redisSmsValidCode.getCode()))) {
                throw new BusinessException(BaseErrorConstant.EXPORT_MASK_NOT_EQUAL);
            }
            redisTemplate.delete(redisKey);
        } else {
            throw new BusinessException(BaseErrorConstant.EXPORT_MASK_NOT_EQUAL);
        }
    }

}
