package com.chinamobile.retail.lakala;

import com.chinamobile.retail.util.RSAUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


/**
 * 接口对接请求示例
 */
public class App {

    // 对接方RSA 私钥
    private static final String privateKeyStr = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDTyrPZuAjILMsqTPxkkh3\n" +
            "Rjp4/Cl94mw+8HP2lyN71m4GvOiAQQiT2JgsT/G//6XfcDqwPnc7VTIY7+3NN2dTR/D40dE9IHhLTBjMyuBAU4vJ9iwzBHv0dPfY17DTBNv\n" +
            "d/MP7Rsp+j+YGaOhWaGlsgm6TVtlQey7QsmvWAV0YXGlyAf07ssmh1/M2bfxE95YrnpyUhjDZQzA+rq51kS7EAuPE/UNSOgDklwOKLi5guA\n" +
            "hvI1Q6rLjtufdC+JqYrIPmxjRhjW1RFE2UNCw7LqBfByb9n8mxgP4LKBjiCLYAfEMfzieIQUNpLSj+a3xo1S4rpMGiR7TYxFLBlzdcxif6N\n" +
            "AgMBAAECggEAWfSyxzq/oCZGdMUWPrFBMQzecfA59MQHvuKhaZUT/kX6oy6RB9bQVCx8cBS8jXngivtAYbGpdDd4nGmE5AAtwLLeyPDN69e\n" +
            "6Lx9nB5feXMC4NlKlLDG5WH5E4UpebeKm5MMuuGqiG8eSIKujGT0wj30MWimDOUFUWc3HkKeBAbZOo7xRs28cSHk81G7mpiYmCQUJVyOCbd\n" +
            "DOwGMkE51t07iVGNswXRqYaavVI85eEB4QRuPAdCGOZff/ll6rVrJPEBj8ch03oDEszdQb+0B39pUi3sfex2AGvLISSJTSy9cab43MCcGYZ\n" +
            "dL4leX7Eq+x3exZO3fmVPTm3dtJyhiPEQKBgQD0VWXMofRAHPl8cxf/lq1PAcalffBUo24Fv1DfsD9yNxagM8s5PzChsrTpi4BxEUdMZeeQ\n" +
            "rUtlrHgxWF9sBp21LczObkkqKQ9/b0M3rXIS91bqsQsFWS2OsGFzLo+voxtGip5KK9ORI34cwSaXxuJ5kwC5hAXnrG8I9Ef7IyWLewKBgQD\n" +
            "d54gdKsR80HIuRLsirQZP/cKbHgcjQY1OATtk8q6b/rtvO9t0wSOeCCRMuv/EuKGk7POtqyuvwutqg1WKNxMy1bY9ty4bRXd44ii4aCYGDO\n" +
            "Al8NFtJCSMWr++t6XzNHbwClbLSbY/tOGGIVumg1p+udu+btOb8lpHg7/WyHBblwKBgQC+3D6rPQ/JTVjuCBIFC7TR9Lcx61DjLM7zGmGYe\n" +
            "tr042d/OTZUv7HDfg+oJ9rrd+3UFf5vm488Gx/AnCHeBsUHFIHZ93ibwHtktosxYQGtIxz4M9hCE0ltHwbgrMx9DNJvpjTEB7w6shj/aTo2\n" +
            "cZvUECsOv7zFHoOV4QyhdvELJQKBgBkluvwrM5c9fCMYMOjuGNAJ3vr7PS3WqO/VHekDw5v0E0O40db6aFHpdEupyYB+t/rby4W75ziE627\n" +
            "nsVL3iNpy87MsxjHa/n4ZiynSy7RV0zUZhHJM7UNmqWIwp8LXCD+NvGZPVTMFCaXMs/k7246O+4MqRhrfLTH7kUsC7hDDAoGBAMs+rMVstr\n" +
            "9udOIsKLmADBXPGK+UuWbtJAVCSyX61EuKz8gJL8KyXOlc3Dnokf4Qhh5ZMtslM6VPeSwGkHCH0CA66t/Aq56TCkbQi1IrJbc7gCL/CuX1j\n" +
            "ly8pOQs8widY/U7UrVxsJ87POVVXsf3I35FD198uYrqXzklLNGX7X6l";

    // 对接方RSA 公钥
    private static final String publicKeyStr = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA08qz2bgIyCzLKkz8ZJId0Y6e\n" +
            "PwpfeJsPvBz9pcje9ZuBrzogEEIk9iYLE/xv/+l33A6sD53O1UyGO/tzTdnU0fw+NHRPSB4S0wYzMrgQFOLyfYsMwR79HT32New0wTb\n" +
            "3fzD+0bKfo/mBmjoVmhpbIJuk1bZUHsu0LJr1gFdGFxpcgH9O7LJodfzNm38RPeWK56clIYw2UMwPq6udZEuxALjxP1DUjoA5JcDii4\n" +
            "uYLgIbyNUOqy47bn3QviamKyD5sY0YY1tURRNlDQsOy6gXwcm/Z/JsYD+CygY4gi2AHxDH84niEFDaS0o/mt8aNUuK6TBoke02MRSwZ\n" +
            "c3XMYn+jQIDAQAB";

    //平台RSA 公钥
    private static final String supplierPublicKeyStr = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoBkjQADiGzay1O8Ar3q3\n" +
            "ky18dp0OdmkEaRnuARblzNZS0v5eEbl5MVgJWrRsT7kK7cMXdxsriAcvicSLiVFp\n" +
            "P3oYLizbeTj7NiVH+J6MwggVYqnXZul3T6yVh5Jq0TJGXzyWHbtmMNLDxL/iWO4C\n" +
            "8a1tNIhd07rVKF/SrMZ6jxpUyrUc8Y8cr59tND0IQpwVRcFevyMOLVZQPEtlGAq4\n" +
            "rbHZ6JMTb6/I1f+k6MLqcnvXQ6Hd6MNwV9uOjmVDEtt0mgUeuSoUX1hXzMBZR7NO\n" +
            "2OVXrJSWgFkP5TzTXXNoNIdURnWhUuJ6teJliMvEHYyFaPXVQmWvygzbSdPg2sX8\n" +
            "RwIDAQAB";

    String s1 = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoBkjQADiGzay1O8Ar3q3ky18dp0OdmkEaRnuARblzNZS0v5eEbl5MVgJWrRsT7kK7cMXdxsriAcvicSLiVFpP3oYLizbeTj7NiVH+J6MwggVYqnXZul3T6yVh5Jq0TJGXzyWHbtmMNLDxL/iWO4C8a1tNIhd07rVKF/SrMZ6jxpUyrUc8Y8cr59tND0IQpwVRcFevyMOLVZQPEtlGAq4rbHZ6JMTb6/I1f+k6MLqcnvXQ6Hd6MNwV9uOjmVDEtt0mgUeuSoUX1hXzMBZR7NO2OVXrJSWgFkP5TzTXXNoNIdURnWhUuJ6teJliMvEHYyFaPXVQmWvygzbSdPg2sX8RwIDAQAB";

    // hashkey（身份证、姓名 签名使用）
    private static final String hashKey = "3a321ee3f81525a8";

    // 测试环境地址
    private static final String url = "https://wopen.dshytest.com/w/openapi/proxypay";

//    private static final String url = "http://127.0.0.1:8506/w/openapi/proxypay";

    /**
     * 调用示例
     * @param args
     * @throws Exception
     */
    /*public static void main(String[] args) {

        // 公共报文
        Map<String, String> pubMap = new HashMap<String, String>();
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timestamp = dateFormat.format(date);
        pubMap.put("channel", "ff03f5a1eeee490e");      // 业务渠道标识（见接口文档）
        pubMap.put("method", "getUserSignInfo");        // 接口名（见接口文档）
        pubMap.put("format", "JSON");                   // 请求数据格式（固定）
        pubMap.put("charset", "UTF-8");                 // 请求编码格式（固定）
        pubMap.put("signType", "RSA2");                 // 请求签名方式（固定）
        pubMap.put("timestamp", timestamp);             // 请求时间
        pubMap.put("version", "1.0");                   // 接口版本号

        // 业务参数（bizContent）
        ImmutableMap.Builder<String, String> bizBuilder = ImmutableMap.builder();
        ImmutableMap<String, String> map = bizBuilder
                .put("subChannel", "")
                .put("idCard", RSAUtils.hashMessage("140429199403282012",hashKey))  //身份证号进行 签名
                .put("acceptName", RSAUtils.hashMessage("张三",hashKey))             //姓名进行 签名
                .put("returnUrl", "http://sdffff.com")
                .build();
        HashMap<String, String> hashMap = Maps.newHashMap(map);
        // 拼接传输参数
        try {
            // 1、对业务参数进行URLEncoder转码（UTF-8），否则将出现中文乱码问题
            String bizContentJsonStr = JSON.toJSONString(hashMap);
            System.out.println("bizContent原生参数===：" + bizContentJsonStr);
            String bizContent = java.net.URLEncoder.encode(JSON.toJSONString(hashMap),"UTF-8");
            System.out.println("bizContent转码后===：" + bizContent);

            // 2、对业务参数进行RSA加密
            String biz_content = RSAUtils.encryptByPrivateKey(bizContent, privateKeyStr);
            System.out.println("bizContent加密后===: " + biz_content);

            pubMap.put("bizContent", biz_content);

            // 确认业务参数正常加解密是否正确
            String biz_content2 = java.net.URLDecoder.decode(RSAUtils.decryptByPublicKey(biz_content, publicKeyStr),"UTF-8");
            System.out.println("bizContent解密后===: " + biz_content2);

            // 3、对请求参数进行RSA私钥签名
            String signContent = RSAUtils.getEncodeSignContent(pubMap);
            System.out.println("签名原生内容: " + signContent);
            String sign = RSAUtils.signByPrivateKey(signContent.getBytes(),privateKeyStr);

            System.out.println("接口参数RSA签名: " + sign);
            pubMap.put("sign", sign);

            // 确认签名是否正确（公钥验签）
            boolean a = RSAUtils.verifyByPublicKey(signContent.getBytes(),publicKeyStr,sign);
            System.out.println("验签结果:" + a);

            // 4、接口调用
            String res = HttpRequest.sendPost(url, pubMap);
            System.out.println("响应数据res:" + res);
            JSONObject jsonObject = JSONObject.parseObject(res);

            //5、使用平台公钥对响应数据（data） 进行解密
            String responseData = RSAUtils.decryptByPublicKey(URLDecoder.decode(jsonObject.get("data").toString(),"UTF-8"), supplierPublicKeyStr);
            System.out.println("接口处理结果:" + responseData);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }*/


    public static void main(String[] args) {

        // 公共报文
        Map<String, String> pubMap = new HashMap<String, String>();
        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        pubMap.put("channel", "ff03f5a1eeee490e");      // 业务渠道标识（见接口文档）
        pubMap.put("outTradeNo", "1186685617772376064");        // 接口名（见接口文档）
        pubMap.put("tradeNo", "700e4a74bae8a24012b905");                   // 请求数据格式（固定）
        pubMap.put("amount", "0.01");                 // 请求编码格式（固定）
        pubMap.put("tradeStatus", "SUCCESS");                 // 请求签名方式（固定）
        pubMap.put("tradeCreateTime", "2023-12-19 15:04:59");                 // 请求签名方式（固定）
        pubMap.put("tradePaymentTime", "");                 // 请求签名方式（固定）
        pubMap.put("signType", "RSA2");                 // 请求签名方式（固定）
        pubMap.put("failMsg", "");                   // 接口版本号
                // 接口版本号


        // 拼接传输参数
        try {

            // 3、对请求参数进行RSA私钥签名
            String signContent = RSAUtils.getEncodeSignContent(pubMap);
            System.out.println("签名原生内容: " + signContent);
//            String sign = RSAUtils.signByPrivateKey(signContent.getBytes(),privateKeyStr);
            String sign = "HCHBPs4voadNZgZMmCFP3G17Kvy1/4mVtvPU4l7qpjHM1tiyjTlv+gyG4Xc2WI2LcAeCHJ3YDcN13dXso6Q9Iqrs79bzxZOVjH6RXbI6fa8l2F5jjxL37yP3ypqKgnhvX85V64gJP/X+FrjSX5xzIpZt/MfjwHIVbfuepI1DJM+QdNrlOmUr7eJ7a14eBDFImmtOXrmIdQYqgkLALNu34wvMawy8hTOrVLtkdtESiZ8pco4qfnIK/HqCSDttz+Kt1x55FF2zaKnB8mjQO/1mzAVi0xFBupaGFMHDXAKKrGw1bx4tZFLBd/vG8VsVH/HJBprDpDAvyTXEKK8WlTgRIA==";

            System.out.println("接口参数RSA签名: " + sign);

            // 确认签名是否正确（公钥验签）
            boolean a = RSAUtils.verifyByPublicKey(signContent.getBytes(),publicKeyStr,sign);
            System.out.println("验签结果:" + a);



        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
