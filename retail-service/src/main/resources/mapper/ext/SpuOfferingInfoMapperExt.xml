<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.SpuOfferingInfoMapperExt">

<!--    <select id="pageQueryProductFrontList" resultType="com.chinamobile.retail.pojo.mapper.ProductFrontListDO">-->
<!--        SELECT-->
<!--        spu.id spuId,-->
<!--        spu.offering_code spuOfferingCode,-->
<!--        spu.offering_name spuOfferingName,-->
<!--        <if test="cityCode != null and cityCode != ''">-->
<!--            (SELECT max(price) FROM sku_offering_info sku LEFT JOIN sku_release_target srt ON sku.offering_code =-->
<!--            srt.sku_offering_code WHERE sku.spu_code = spu.offering_code and sku.delete_time IS NULL and-->
<!--            sku.offering_status = '1' AND sku.point_status = 2 and ( srt.province_code = 000 OR (srt.province_code =-->
<!--            #{provinceCode} and srt.city_code is null) OR (srt.province_code = #{provinceCode} and srt.city_code =-->
<!--            #{cityCode}))) price,-->
<!--        </if>-->
<!--        <if test="cityCode == null or cityCode == ''">-->
<!--            (SELECT max(price) FROM sku_offering_info sku LEFT JOIN sku_release_target srt ON sku.offering_code =-->
<!--            srt.sku_offering_code WHERE sku.spu_code = spu.offering_code and sku.delete_time IS NULL and-->
<!--            sku.offering_status = '1' AND sku.point_status = 2 and ( srt.province_code = 000 OR srt.province_code =-->
<!--            #{provinceCode} )) price,-->
<!--        </if>-->

<!--        <if test="cityCode != null and cityCode != ''">-->
<!--            (SELECT max(sku.price * re.point_percent/100) FROM sku_offering_info sku LEFT JOIN sku_release_target srt ON-->
<!--            sku.offering_code = srt.sku_offering_code LEFT JOIN sku_role_relation re ON re.sku_id = sku.id WHERE-->
<!--            sku.spu_code = spu.offering_code and sku.delete_time IS NULL and sku.offering_status = '1' AND-->
<!--            sku.point_status = 2 and ( srt.province_code = 000 OR (srt.province_code = #{provinceCode} and srt.city_code-->
<!--            is null) OR (srt.province_code = #{provinceCode} and srt.city_code = #{cityCode})) AND re.partner_role_id =-->
<!--            #{partnerRoleId} ) point,-->
<!--        </if>-->
<!--        <if test="cityCode == null or cityCode == ''">-->
<!--            (SELECT max(sku.price * re.point_percent/100) FROM sku_offering_info sku LEFT JOIN sku_release_target srt ON-->
<!--            sku.offering_code = srt.sku_offering_code LEFT JOIN sku_role_relation re ON re.sku_id = sku.id WHERE-->
<!--            sku.spu_code = spu.offering_code and sku.delete_time IS NULL and sku.offering_status = '1' AND-->
<!--            sku.point_status = 2 and ( srt.province_code = 000 OR srt.province_code = #{provinceCode} ) AND-->
<!--            re.partner_role_id = #{partnerRoleId}) point,-->
<!--        </if>-->

<!--        <if test="cityCode != null and cityCode != ''">-->
<!--            (SELECT max(re.point_limit) FROM sku_offering_info sku LEFT JOIN sku_release_target srt ON sku.offering_code-->
<!--            = srt.sku_offering_code LEFT JOIN sku_role_relation re ON re.sku_id = sku.id WHERE sku.spu_code =-->
<!--            spu.offering_code and sku.delete_time IS NULL and sku.offering_status = '1' AND sku.point_status = 2 and (-->
<!--            srt.province_code = 000 OR (srt.province_code = #{provinceCode} and srt.city_code is null) OR-->
<!--            (srt.province_code = #{provinceCode} and srt.city_code = #{cityCode})) AND re.partner_role_id =-->
<!--            #{partnerRoleId} ) pointLimit,-->
<!--        </if>-->
<!--        <if test="cityCode == null or cityCode == ''">-->
<!--            (SELECT max(re.point_limit) FROM sku_offering_info sku LEFT JOIN sku_release_target srt ON sku.offering_code-->
<!--            = srt.sku_offering_code LEFT JOIN sku_role_relation re ON re.sku_id = sku.id WHERE sku.spu_code =-->
<!--            spu.offering_code and sku.delete_time IS NULL and sku.offering_status = '1' AND sku.point_status = 2 and (-->
<!--            srt.province_code = 000 OR srt.province_code = #{provinceCode} ) AND re.partner_role_id = #{partnerRoleId})-->
<!--            pointLimit,-->
<!--        </if>-->
<!--        &#45;&#45; (SELECT MAX(point_status) FROM sku_offering_info WHERE spu_code = spu.offering_code AND delete_time is null AND offering_status = '1') pointStatus,-->
<!--        case when point is not null and point != '' and pointLimit is not null and pointLimit != ''-->
<!--        then least(point, pointLimit),-->
<!--        else point-->
<!--        end as orderPoint,-->
<!--        spu.url,-->
<!--        spu.img_url imgUrl,-->
<!--        (SELECT sum(sku_quantity) FROM order_2c_atom_info WHERE spu_offering_code = spu.offering_code) skuQuantity-->
<!--        FROM-->
<!--        spu_offering_info spu-->
<!--        WHERE spu.delete_time is NULL and spu.offering_status = '1'-->
<!--        <if test="queryParam != null and queryParam != ''">-->
<!--            AND ( (spu.offering_name LIKE concat('%',#{queryParam},'%'))-->
<!--            or-->
<!--            EXISTS ( SELECT * FROM sku_offering_info so WHERE so.spu_code = spu.offering_code AND so.offering_name LIKE-->
<!--            concat('%',#{queryParam},'%') ))-->
<!--        </if>-->
<!--        HAVING price is not null-->
<!--        <if test="orderType == 1 and orderSort == 1 ">-->
<!--            order by (SELECT sum(sku_quantity) FROM order_2c_atom_info WHERE spu_offering_code = spu.offering_code )-->
<!--            DESC-->
<!--        </if>-->
<!--        <if test="orderType == 1 and orderSort == 2 ">-->
<!--            order by (SELECT sum(sku_quantity) FROM order_2c_atom_info WHERE spu_offering_code = spu.offering_code ) ASC-->
<!--        </if>-->
<!--        <if test="orderType == 2 and orderSort == 1 ">-->
<!--            order by orderPoint DESC-->
<!--        </if>-->
<!--        <if test="orderType == 2 and orderSort == 2 ">-->
<!--            order by orderPoint ASC-->
<!--        </if>-->
<!--        <if test="orderType == 3 and orderSort == 1 ">-->
<!--            order by price DESC-->
<!--        </if>-->
<!--        <if test="orderType == 3 and orderSort == 2 ">-->
<!--            order by price ASC-->
<!--        </if>-->
<!--        limit #{start},#{pageSize}-->
<!--    </select>-->

    <select id="pageQueryProductFrontList" resultType="com.chinamobile.retail.pojo.mapper.ProductFrontListDO">
        SELECT
            spu.id AS spuId,
            spu.offering_code AS spuOfferingCode,
            spu.offering_name AS spuOfferingName,
            t.max_price AS price,
            t.max_point AS point,
            t.max_point_limit AS pointLimit,
            CASE WHEN t.max_point IS NOT NULL AND t.max_point_limit IS NOT NULL
                     THEN LEAST(t.max_point, t.max_point_limit)
                 ELSE t.max_point
                END AS orderPoint,
            spu.url,
            spu.img_url AS imgUrl,
            (SELECT SUM(sku_quantity)
             FROM order_2c_atom_info
             WHERE spu_offering_code = spu.offering_code) AS skuQuantity
        FROM spu_offering_info spu
                 JOIN (
            SELECT
                sku.spu_code,
                MAX(sku.price) AS max_price,
                MAX(CASE WHEN re.partner_role_id = 1
                             THEN sku.price * re.point_percent/100
                    END) AS max_point,
                MAX(CASE WHEN re.partner_role_id = 1
                             THEN re.point_limit
                    END) AS max_point_limit
            FROM sku_offering_info sku
                     LEFT JOIN sku_release_target srt
                               ON sku.offering_code = srt.sku_offering_code
                     LEFT JOIN sku_role_relation re
                               ON re.sku_id = sku.id
            WHERE
                sku.delete_time IS NULL
              AND sku.offering_status = '1'
              AND sku.point_status = 2
              AND (srt.province_code = 000
                <if test="cityCode == null or cityCode == ''">
                    OR (srt.province_code = #{provinceCode})
                </if>
                <if test="cityCode != null and cityCode != ''">
                    OR (srt.province_code = #{provinceCode} AND srt.city_code IS NULL)
                    OR (srt.province_code = #{provinceCode} and srt.city_code = #{cityCode})
                </if>
                )
            GROUP BY sku.spu_code
        ) t ON t.spu_code = spu.offering_code
        WHERE
            spu.delete_time IS NULL
          AND spu.offering_status = '1'
          AND t.max_price IS NOT NULL  -- 替代原HAVING条件
        <if test="orderType == 1 and orderSort == 1 ">
            order by (SELECT sum(sku_quantity) FROM order_2c_atom_info WHERE spu_offering_code = spu.offering_code )
            DESC
        </if>
        <if test="orderType == 1 and orderSort == 2 ">
            order by (SELECT sum(sku_quantity) FROM order_2c_atom_info WHERE spu_offering_code = spu.offering_code ) ASC
        </if>
        <if test="orderType == 2 and orderSort == 1 ">
            order by orderPoint DESC
        </if>
        <if test="orderType == 2 and orderSort == 2 ">
            order by orderPoint ASC
        </if>
        <if test="orderType == 3 and orderSort == 1 ">
            order by price DESC
        </if>
        <if test="orderType == 3 and orderSort == 2 ">
            order by price ASC
        </if>
            limit #{start},#{pageSize};
    </select>

    <select id="pageCountProductFrontList" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM
        spu_offering_info spu
        WHERE spu.delete_time is NULL and spu.offering_status = '1' AND EXISTS
        <if test="cityCode != null and cityCode != ''">
            (SELECT * FROM sku_offering_info sku LEFT JOIN sku_release_target srt ON sku.offering_code =
            srt.sku_offering_code WHERE sku.spu_code = spu.offering_code and sku.delete_time IS NULL and
            sku.offering_status = '1' AND sku.point_status = 2 and ( srt.province_code = 000 OR (srt.province_code =
            #{provinceCode} and srt.city_code is null) OR (srt.province_code = #{provinceCode} and srt.city_code =
            #{cityCode})))
        </if>
        <if test="cityCode == null or cityCode == ''">
            (SELECT * FROM sku_offering_info sku LEFT JOIN sku_release_target srt ON sku.offering_code =
            srt.sku_offering_code WHERE sku.spu_code = spu.offering_code and sku.delete_time IS NULL and
            sku.offering_status = '1' AND sku.point_status = 2 and ( srt.province_code = 000 OR srt.province_code =
            #{provinceCode} ))
        </if>
        <if test="queryParam != null and queryParam != ''">
            AND ( (spu.offering_name LIKE concat('%',#{queryParam},'%'))
            or
            EXISTS ( SELECT * FROM sku_offering_info so WHERE so.spu_code = spu.offering_code AND so.offering_name LIKE
            concat('%',#{queryParam},'%') ))
        </if>
    </select>

    <select id="pageMiniProgramProduct" resultType="com.chinamobile.retail.pojo.vo.MiniProgramProductListVO"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageProductParam">

        SELECT
        spu.offering_code AS spuCode,
        spu.offering_name AS spuName,
        spu.img_url AS image,
        IFNULL(MIN(sku.price), 0) AS price,
        spu.tag AS saleTag,
        spu.product_description AS spuRemark,
        sc.core_component_name AS coreComponentName,
        sc.core_component_img AS coreComponentImg,
        (SELECT GROUP_CONCAT(label) FROM spu_sale_label where (type = 0 or type is null) and spu_code = spu.offering_code) mainSaleLabel,
        (SELECT GROUP_CONCAT(label) FROM spu_sale_label where type = 1 and spu_code = spu.offering_code) subSaleLabel
        FROM spu_offering_info spu force index (idx_offering_code)
        INNER JOIN sku_offering_info sku ON sku.spu_code = spu.offering_code AND sku.offering_status = 1 AND
        sku.delete_time is null
        <if test="param.thirdDirectoryId != null and param.thirdDirectoryId != ''">
            INNER JOIN navigation_info ni ON ni.spu_offering_code = spu.offering_code and ni.level3_navigation_code =#{param.thirdDirectoryId}
        </if>
        LEFT JOIN sku_release_target srt ON srt.sku_offering_code = sku.offering_code
        LEFT JOIN spu_core_component sc ON sc.spu_code = spu.offering_code and sc.is_delete = 0 and sc.status = 1
        <if test="param.keyWord != null and param.keyWord != ''">
            LEFT JOIN atom_offering_info ao ON ao.spu_code = spu.offering_code and ao.sku_code = sku.offering_code and ao.delete_time is null
            LEFT JOIN atom_std_service ass ON ass.atom_id = ao.id
            LEFT JOIN standard_service std ON std.id = ass.std_service_id
        </if>
        WHERE spu.offering_status = 1 AND spu.delete_time is null
        AND (spu.secretly_listed = '1' or spu.secretly_listed is null)
        <if test="param.provinceCode != null and param.provinceCode != ''">
            AND srt.province_code IN ('000',#{param.provinceCode})
        </if>

        <if test="param.spuCode != null and param.spuCode != ''">
            AND spu.offering_code like concat(#{param.spuCode},'%')
        </if>
        <if test="param.spuName != null and param.spuName != ''">
            AND spu.offering_name like concat(#{param.spuName},'%')
        </if>
        <if test="param.cityCode != null and param.cityCode != ''">
            AND (srt.city_code = #{param.cityCode} OR srt.city_code IS NULL)
        </if>
        <if test="(param.cityCode == null or param.cityCode == '')
        and (param.roleType == null or param.roleType == '' or param.roleType == '1' or param.roleType == '2' or param.roleType == '3' or param.roleType == '4') ">
            AND (srt.city_code IS NULL or exists (SELECT s.sku_offering_code
            FROM sku_release_target s
            JOIN contract_city_info cci
            ON s.city_code = cci.mall_code
            WHERE cci.province_mall_code = #{param.provinceCode} and s.sku_offering_code = srt.sku_offering_code
            GROUP BY s.sku_offering_code
            HAVING COUNT(DISTINCT cci.mall_code) =
            (SELECT COUNT(1)
            FROM contract_city_info
            WHERE province_mall_code = #{param.provinceCode})))
        </if>
        <if test="param.saleTag != null and param.saleTag != ''">
            AND spu.tag = #{param.saleTag}
        </if>
        <if test="param.keyWord != null and param.keyWord != ''">
            AND (
            spu.offering_name like concat(#{param.keyWord},'%')
            or spu.product_description like concat(#{param.keyWord},'%')
            or  spu.product_keywords like concat(#{param.keyWord},'%')
            or  sc.core_component_name like concat(#{param.keyWord},'%')
            or  std.real_product_name like concat(#{param.keyWord},'%')
            )
        </if>
        GROUP BY spu.offering_code
    </select>

    <select id="searchProduct" resultType="com.chinamobile.retail.pojo.vo.MiniProgramProductListVO">
        SELECT
        spu.offering_code AS spuCode,
        spu.offering_name AS spuName,
        spu.img_url AS image,
        IFNULL(MIN(sku.price), 0) AS price,
        spu.tag AS saleTag,
        spu.product_description AS spuRemark,
        sc.core_component_name AS coreComponentName,
        sc.core_component_img AS coreComponentImg,
        (SELECT GROUP_CONCAT(label) FROM spu_sale_label where (type = 0 or type is null) and spu_code = spu.offering_code) mainSaleLabel,
        (SELECT GROUP_CONCAT(label) FROM spu_sale_label where type = 1 and spu_code = spu.offering_code) subSaleLabel
        FROM spu_offering_info spu force index (idx_offering_code)
        INNER JOIN sku_offering_info sku ON sku.spu_code = spu.offering_code AND sku.offering_status = 1 AND
        sku.delete_time is null
        LEFT JOIN sku_release_target srt ON srt.sku_offering_code = sku.offering_code
        LEFT JOIN spu_core_component sc ON sc.spu_code = spu.offering_code and sc.is_delete = 0 and sc.status = 1
        <if test="searchWord != null and searchWord != ''">
            LEFT JOIN atom_offering_info ao ON ao.spu_code = spu.offering_code and ao.sku_code = sku.offering_code and ao.delete_time is null
            LEFT JOIN atom_std_service ass ON ass.atom_id = ao.id
            LEFT JOIN standard_service std ON std.id = ass.std_service_id
        </if>

        WHERE spu.offering_status = 1 AND spu.delete_time is null AND (spu.secretly_listed = '1' or spu.secretly_listed is null)
        <if test="searchWord != null and searchWord != ''">
            AND (
            spu.offering_name like concat(#{searchWord},'%')
            or spu.product_description like concat(#{searchWord},'%')
            or  spu.product_keywords like concat(#{searchWord},'%')
            or  sc.core_component_name like concat(#{searchWord},'%')
            or  std.real_product_name like concat(#{searchWord},'%')
            )
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            AND srt.province_code IN ('000', #{provinceCode})
        </if>
        <if test="cityCode != null and cityCode != ''">
            AND (srt.city_code = #{cityCode} OR srt.city_code IS NULL)
        </if>
        <if test="(cityCode == null or cityCode == '')
        and (roleType == null or roleType == '' or roleType == '1' or roleType == '2' or roleType == '3' or roleType == '4') ">
            AND (srt.city_code IS NULL or exists (SELECT s.sku_offering_code
            FROM sku_release_target s
            JOIN contract_city_info cci
            ON s.city_code = cci.mall_code
            WHERE cci.province_mall_code = #{provinceCode} and s.sku_offering_code = srt.sku_offering_code
            GROUP BY s.sku_offering_code
            HAVING COUNT(DISTINCT cci.mall_code) =
            (SELECT COUNT(1)
            FROM contract_city_info
            WHERE province_mall_code = #{provinceCode})))
        </if>
        <if test="saleTag != null and saleTag != ''">
            AND spu.tag = #{saleTag}
        </if>
        GROUP BY spu.offering_code,spu.update_time
        ORDER BY spu.update_time desc
        LIMIT ${pageSize} OFFSET ${(pageNum - 1) * pageSize}
    </select>

    <select id="searchProductCount" resultType="java.lang.Long">
        select count(*) from (
        SELECT
        spu.offering_code AS spuCode
        FROM spu_offering_info spu force index (idx_offering_code)
        INNER JOIN sku_offering_info sku ON sku.spu_code = spu.offering_code AND sku.offering_status = 1 AND
        sku.delete_time is null
        LEFT JOIN sku_release_target srt ON srt.sku_offering_code = sku.offering_code
        LEFT JOIN spu_core_component sc ON sc.spu_code = spu.offering_code and sc.is_delete = 0 and sc.status = 1
        <if test="searchWord != null and searchWord != ''">
            LEFT JOIN atom_offering_info ao ON ao.spu_code = spu.offering_code and ao.sku_code = sku.offering_code and ao.delete_time is null
            LEFT JOIN atom_std_service ass ON ass.atom_id = ao.id
            LEFT JOIN standard_service std ON std.id = ass.std_service_id
        </if>

        WHERE spu.offering_status = 1 AND spu.delete_time is null AND (spu.secretly_listed = '1' or spu.secretly_listed is null)
        <if test="searchWord != null and searchWord != ''">
            AND (
            spu.offering_name like concat(#{searchWord},'%')
            or spu.product_description like concat(#{searchWord},'%')
            or  spu.product_keywords like concat(#{searchWord},'%')
            or  sc.core_component_name like concat(#{searchWord},'%')
            or  std.real_product_name like concat(#{searchWord},'%')
            )
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            AND srt.province_code IN ('000', #{provinceCode})
        </if>
        <if test="cityCode != null and cityCode != ''">
            AND (srt.city_code = #{cityCode} OR srt.city_code IS NULL)
        </if>
        <if test="(cityCode == null or cityCode == '')
        and (roleType == null or roleType == '' or roleType == '1' or roleType == '2' or roleType == '3' or roleType == '4') ">
            AND (srt.city_code IS NULL or exists (SELECT s.sku_offering_code
            FROM sku_release_target s
            JOIN contract_city_info cci
            ON s.city_code = cci.mall_code
            WHERE cci.province_mall_code = #{provinceCode} and s.sku_offering_code = srt.sku_offering_code
            GROUP BY s.sku_offering_code
            HAVING COUNT(DISTINCT cci.mall_code) =
            (SELECT COUNT(1)
            FROM contract_city_info
            WHERE province_mall_code = #{provinceCode})))
        </if>
        <if test="saleTag != null and saleTag != ''">
            AND spu.tag = #{saleTag}
        </if>
        GROUP BY spu.offering_code
                             )temp
    </select>
    <select id="countMiniProgramProduct" resultType="java.lang.Integer"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageProductParam">

        SELECT COUNT(*)
        FROM (SELECT spu.offering_code AS spuCode,
        IFNULL(MIN(sku.price), 0) AS price
        FROM spu_offering_info spu force index (idx_offering_code)
        INNER JOIN sku_offering_info sku ON sku.spu_code = spu.offering_code AND sku.offering_status = 1 AND
        sku.delete_time is null
        INNER JOIN navigation_info ni ON ni.spu_offering_code = spu.offering_code AND
        ni.level3_navigation_code = #{param.thirdDirectoryId}
        LEFT JOIN sku_release_target srt ON srt.sku_offering_code = sku.offering_code
        WHERE spu.offering_status = 1 AND spu.delete_time is null  AND (spu.secretly_listed = '1' or spu.secretly_listed is null)
        <if test="param.spuCode != null and param.spuCode != ''">
            AND spu.offering_code like concat(#{param.spuCode},'%')
        </if>
        <if test="param.spuName != null and param.spuName != ''">
            AND spu.offering_name like concat(#{param.spuName},'%')
        </if>
        <if test="param.provinceCode != null and param.provinceCode != ''">
            AND srt.province_code IN ('000', #{param.provinceCode})
        </if>
        <if test="param.cityCode != null and param.cityCode != ''">
            AND (srt.city_code = #{param.cityCode} OR srt.city_code IS NULL)
        </if>
        <if test="(param.cityCode == null or param.cityCode == '')
        and (param.roleType == null or param.roleType == '' or param.roleType == '1' or param.roleType == '2' or param.roleType == '3' or param.roleType == '4') ">
            AND (srt.city_code IS NULL or exists (SELECT s.sku_offering_code
            FROM sku_release_target s
            JOIN contract_city_info cci
            ON s.city_code = cci.mall_code
            WHERE cci.province_mall_code = #{param.provinceCode} and s.sku_offering_code = srt.sku_offering_code
            GROUP BY s.sku_offering_code
            HAVING COUNT(DISTINCT cci.mall_code) =
            (SELECT COUNT(1)
            FROM contract_city_info
            WHERE province_mall_code = #{param.provinceCode})))
        </if>
        <if test="param.saleTag != null and param.saleTag != ''">
            AND spu.tag = #{param.saleTag}
        </if>
        GROUP BY spu.offering_code) product
    </select>

    <select id="getMiniProgramSkuList" resultType="com.chinamobile.retail.pojo.vo.MiniProgramSkuInfoVO"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.ProductDetailParam">
        SELECT
        sku.id,
        sku.offering_name AS skuName,
        sku.sku_abbreviation AS skuShortName,
        sku.spu_code AS spuCode,
        sku.offering_code AS skuCode,
        sku.price skuPrice,
        sku.unit skuUnit,
        sc.core_component_name AS coreComponentName,
        spc.status spuStatus
        FROM sku_offering_info sku
        LEFT JOIN sku_release_target srt ON srt.sku_offering_code = sku.offering_code
        LEFT JOIN spu_core_component spc ON spc.spu_code = sku.spu_code and spc.status = 1 and spc.is_delete = 0
        LEFT JOIN sku_core_component sc ON sc.spu_code = sku.spu_code and sc.sku_code = sku.offering_code and sc.is_delete = 0 and spc.id is not null
        WHERE sku.spu_code = #{param.spuCode} AND sku.offering_status = 1 AND sku.delete_time is null
        <if test="param.provinceCode != null and param.provinceCode != ''">
            AND srt.province_code IN ('000', #{param.provinceCode})
        </if>
        <if test="param.cityCode != null and param.cityCode != ''">
            AND (srt.city_code = #{param.cityCode} OR srt.city_code IS NULL)
        </if>
        <if test="(param.cityCode == null or param.cityCode == '')
        and (param.roleType == null or param.roleType == '' or param.roleType == '1' or param.roleType == '2' or param.roleType == '3' or param.roleType == '4') ">
            AND (srt.city_code IS NULL or exists (SELECT s.sku_offering_code
            FROM sku_release_target s
            JOIN contract_city_info cci
            ON s.city_code = cci.mall_code
            WHERE cci.province_mall_code = #{param.provinceCode} and s.sku_offering_code = srt.sku_offering_code
            GROUP BY s.sku_offering_code
            HAVING COUNT(DISTINCT cci.mall_code) =
            (SELECT COUNT(1)
            FROM contract_city_info
            WHERE province_mall_code = #{param.provinceCode})))
        </if>
        GROUP BY sku.offering_code;
    </select>

    <select id="getMiniProgramSkuListByActivityId" resultType="com.chinamobile.retail.pojo.vo.MiniProgramSkuInfoVO"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.ProductDetailParam">
        SELECT
        sku.id AS id,
        sku.offering_name AS skuName,
        sku.spu_code AS spuCode,
        sku.offering_code AS skuCode,
        sku.price skuPrice,
        sku.unit skuUnit,
        sc.core_component_name AS coreComponentName
        FROM sku_offering_info sku
        LEFT JOIN sku_release_target srt ON srt.sku_offering_code = sku.offering_code
        LEFT JOIN spu_core_component spc ON spc.spu_code = sku.spu_code and spc.status = 1 and spc.is_delete = 0
        LEFT JOIN sku_core_component sc ON sc.spu_code = sku.spu_code and sc.sku_code = sku.offering_code and sc.is_delete = 0 and spc.id is not null
        WHERE sku.spu_code = #{param.spuCode} AND sku.offering_status != 2 AND sku.delete_time is null
        <if test="param.provinceCode != null and param.provinceCode != ''">
            AND srt.province_code IN ('000', #{param.provinceCode})
        </if>
        <if test="param.cityCode != null and param.cityCode != ''">
            AND (srt.city_code = #{param.cityCode} OR srt.city_code IS NULL)
        </if>
        GROUP BY sku.offering_code
        order by skuPrice
        ;
    </select>

    <select id="getMinSkuPrice" resultType="java.lang.Long"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.ProductDetailParam">
        SELECT IFNULL(MIN(sku.price), 0)
        FROM sku_offering_info sku
        LEFT JOIN sku_release_target srt ON srt.sku_offering_code = sku.offering_code
        WHERE sku.spu_code = #{param.spuCode} AND sku.offering_status = 1 AND sku.delete_time is null
        <if test="param.provinceCode != null and param.provinceCode != ''">
            AND srt.province_code IN ('000', #{param.provinceCode})
        </if>
        <if test="param.cityCode != null and param.cityCode != ''">
            AND (srt.city_code = #{param.cityCode} OR srt.city_code IS NULL)
        </if>
        <if test="param.cityCode == null or param.cityCode == ''">
            AND srt.city_code IS NULL
        </if>
        GROUP BY sku.spu_code
    </select>

    <select id="productDetail" resultType="com.chinamobile.retail.pojo.vo.MiniProgramProductDetailVO"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.ProductDetailParam">
        SELECT
        spu.offering_code AS spuCode,
        spu.offering_name AS spuName,
        spu.img_url AS image,
        IFNULL(MIN(sku.price), 0) AS price,
        spu.tag AS saleTag,
        spu.product_description AS spuRemark,
        ci.offering_class spuOfferingClass,
        spu.inventory_type inventoryType,
        sku.price*srr.point_percent/100 as productPoint,
        sc.status AS coreStatus,
        sc.core_component_name AS coreComponentName,
        sc.core_component_img AS coreComponentImg
        FROM spu_offering_info spu
        INNER JOIN sku_offering_info sku ON sku.spu_code = spu.offering_code AND sku.offering_status = 1 AND
        sku.delete_time is null
        LEFT JOIN sku_release_target srt ON srt.sku_offering_code = sku.offering_code
        LEFT JOIN category_info ci on ci.spu_id = spu.id
        LEFT JOIN sku_role_relation srr on srr.sku_id = sku.id
        LEFT JOIN spu_core_component sc on sc.spu_code = spu.offering_code and sc.is_delete = 0 and sc.status = 1
        WHERE spu.offering_code = #{param.spuCode} and
        spu.offering_status = 1 AND spu.delete_time is null
        <if test="param.provinceCode != null and param.provinceCode != ''">
            AND srt.province_code IN ('000', #{param.provinceCode})
        </if>
        <if test="param.cityCode != null and param.cityCode != ''">
            AND (srt.city_code = #{param.cityCode} OR srt.city_code IS NULL)
        </if>
        <if test="(param.cityCode == null or param.cityCode == '')
        and (param.roleType == null or param.roleType == '' or param.roleType == '1' or param.roleType == '2' or param.roleType == '3' or param.roleType == '4') ">
            AND (srt.city_code IS NULL or exists (SELECT s.sku_offering_code
            FROM sku_release_target s
            JOIN contract_city_info cci
            ON s.city_code = cci.mall_code
            WHERE cci.province_mall_code = #{param.provinceCode} and s.sku_offering_code = srt.sku_offering_code
            GROUP BY s.sku_offering_code
            HAVING COUNT(DISTINCT cci.mall_code) =
            (SELECT COUNT(1)
            FROM contract_city_info
            WHERE province_mall_code = #{param.provinceCode})))
        </if>
        limit 1
    </select>

    <select id="productDetailByActivityId" resultType="com.chinamobile.retail.pojo.vo.MiniProgramProductDetailVO"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.ProductDetailParam">
        SELECT (SELECT group_concat(CONCAT(srt.province_code, '-', IFNULL(srt.city_code, 'allCity')))
                FROM sku_release_target srt
                         JOIN sku_offering_info s ON s.offering_code = srt.sku_offering_code
                WHERE s.spu_code = #{param.spuCode}) provinceCityCodeList,
               spu.offering_code                  AS spuCode,
               spu.offering_name                  AS spuName,
               spu.img_url                        AS image,
               IFNULL(MIN(sku.price), 0)          AS price,
               spu.tag                            AS saleTag,
               spu.product_description            AS spuRemark,
               ci.offering_class                     spuOfferingClass,
               spu.inventory_type                    inventoryType,
               sc.core_component_name                   AS coreComponentName,
               sc.core_component_img                   AS coreComponentImg
        FROM spu_offering_info spu
                 INNER JOIN sku_offering_info sku
                            ON sku.spu_code = spu.offering_code AND sku.offering_status != 2 AND sku.delete_time is null
                 LEFT JOIN category_info ci on ci.spu_id = spu.id
                LEFT JOIN spu_core_component sc on sc.spu_code = spu.offering_code and sc.is_delete = 0 and sc.status = 1
        WHERE spu.offering_code = #{param.spuCode}
          and spu.offering_status != 2
          AND spu.delete_time is null
            limit 1
    </select>

    <select id="countSkuByProvinceAndCity" resultType="java.lang.Long">
        SELECT
        count(*)
        FROM
        sku_offering_info sku
        LEFT JOIN sku_release_target srt ON srt.sku_offering_code = sku.offering_code
        WHERE sku.spu_code = #{spuCode} AND sku.delete_time IS NULL AND sku.offering_status = '1'
        <if test="cityCode != null and cityCode != ''">
            and ( srt.province_code = 000 OR (srt.province_code = #{provinceCode} and srt.city_code is null) OR
            (srt.province_code = #{provinceCode} and srt.city_code = #{cityCode}))
        </if>
        <if test="(cityCode == null or cityCode == '')
        and (roleType == null or roleType == '' or roleType == '1' or roleType == '2' or roleType == '3' or roleType == '4')">
            and (srt.province_code = 000 OR srt.province_code = #{provinceCode}) and (srt.city_code is null or exists
            (SELECT s.sku_offering_code
            FROM sku_release_target s
            JOIN contract_city_info cci
            ON s.city_code = cci.mall_code
            WHERE cci.province_mall_code = #{provinceCode} and s.sku_offering_code = srt.sku_offering_code
            GROUP BY s.sku_offering_code
            HAVING COUNT(DISTINCT cci.mall_code) =
            (SELECT COUNT(1)
            FROM contract_city_info
            WHERE province_mall_code = #{provinceCode})))
        </if>

    </select>

    <select id="webSearchProduct" resultType="com.chinamobile.retail.pojo.vo.MiniProgramProductListVO">
        SELECT
        spu.offering_code AS spuCode,
        spu.offering_name AS spuName
        FROM spu_offering_info spu force index (idx_offering_code)
        INNER JOIN sku_offering_info sku ON sku.spu_code = spu.offering_code AND sku.offering_status = 1 AND
        sku.delete_time is null
        LEFT JOIN sku_release_target srt ON srt.sku_offering_code = sku.offering_code
        WHERE spu.offering_status = 1 AND spu.delete_time is null AND (spu.secretly_listed = '1' or spu.secretly_listed is null)
        <if test="param.keyWord != null and param.keyWord != ''">
            AND spu.offering_name like concat(#{param.keyWord},'%')
        </if>
        <if test="param.provinceCode != null and param.provinceCode != ''">
            AND srt.province_code IN ('000', #{param.provinceCode})
            AND (srt.city_code IS NULL or exists (SELECT s.sku_offering_code
            FROM sku_release_target s
            JOIN contract_city_info cci
            ON s.city_code = cci.mall_code
            WHERE cci.province_mall_code = #{param.provinceCode} and s.sku_offering_code = srt.sku_offering_code
            GROUP BY s.sku_offering_code
            HAVING COUNT(DISTINCT cci.mall_code) =
            (SELECT COUNT(1)
            FROM contract_city_info
            WHERE province_mall_code = #{param.provinceCode})))
        </if>
        <if test="param.spuCode != null and param.spuCode != ''">
            AND spu.offering_code = #{param.spuCode}
        </if>
        GROUP BY spu.offering_code
        LIMIT ${param.pageSize} OFFSET ${(param.pageNum - 1) * param.pageSize}
    </select>

    <select id="countWebSearchProduct" resultType="java.lang.Long"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.WebSearchProductParam">

        SELECT COUNT(*)
        FROM (SELECT
        spu.offering_code AS spuCode,
        spu.offering_name AS spuName
        FROM spu_offering_info spu force index (idx_offering_code)
        INNER JOIN sku_offering_info sku ON sku.spu_code = spu.offering_code AND sku.offering_status = 1 AND
        sku.delete_time is null
        LEFT JOIN sku_release_target srt ON srt.sku_offering_code = sku.offering_code
        WHERE spu.offering_status = 1 AND spu.delete_time is null  AND (spu.secretly_listed = '1' or spu.secretly_listed is null)
        <if test="param.keyWord != null and param.keyWord != ''">
            AND spu.offering_name like concat(#{param.keyWord},'%')
        </if>
        <if test="param.provinceCode != null and param.provinceCode != ''">
            AND srt.province_code IN ('000', #{param.provinceCode})
            AND (srt.city_code IS NULL or exists (SELECT s.sku_offering_code
            FROM sku_release_target s
            JOIN contract_city_info cci
            ON s.city_code = cci.mall_code
            WHERE cci.province_mall_code = #{param.provinceCode} and s.sku_offering_code = srt.sku_offering_code
            GROUP BY s.sku_offering_code
            HAVING COUNT(DISTINCT cci.mall_code) =
            (SELECT COUNT(1)
            FROM contract_city_info
            WHERE province_mall_code = #{param.provinceCode})))
        </if>
        <if test="param.spuCode != null and param.spuCode != ''">
            AND spu.offering_code = #{param.spuCode}
        </if>
        GROUP BY spu.offering_code) product
    </select>

    <select id="webSceneSearchProduct" resultType="com.chinamobile.retail.pojo.vo.MiniProgramProductListVO"
    >
        SELECT
        spu.offering_code AS spuCode,
        spu.offering_name AS spuName
        FROM spu_offering_info spu force index (idx_offering_code)
        WHERE spu.offering_status = 1 AND spu.delete_time is null
        <if test="param.keyWord != null and param.keyWord != ''">
            AND (
            spu.offering_name like concat(#{param.keyWord},'%')
            OR spu.offering_code like concat(#{param.keyWord},'%')
            )
        </if>
        GROUP BY spu.offering_code
        LIMIT ${param.pageSize} OFFSET ${(param.pageNum - 1) * param.pageSize}
    </select>

    <select id="countWebSceneSearchProduct" resultType="java.lang.Long"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.WebSearchProductParam">

        SELECT COUNT(*)
        FROM (SELECT
        spu.offering_code AS spuCode,
        spu.offering_name AS spuName
        FROM spu_offering_info spu force index (idx_offering_code)
        WHERE spu.offering_status = 1 AND spu.delete_time is null
        <if test="param.keyWord != null and param.keyWord != ''">
            AND (
            spu.offering_name like concat(#{param.keyWord},'%')
            OR spu.offering_code like concat(#{param.keyWord},'%')
            )
        </if>
        GROUP BY spu.offering_code) product
    </select>

</mapper>
