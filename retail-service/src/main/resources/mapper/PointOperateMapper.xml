<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.PointOperateMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.PointOperate">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 11:25:53 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="supplier_id" jdbcType="VARCHAR" property="supplierId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="point" jdbcType="BIGINT" property="point" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="channel" jdbcType="INTEGER" property="channel" />
    <result column="activity_id" jdbcType="VARCHAR" property="activityId" />
    <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 11:25:53 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 11:25:53 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 11:25:53 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, order_id, user_id, supplier_id, type, reason, point, operator_id, create_time, 
    channel, activity_id, delete_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.PointOperateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 11:25:53 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from point_operate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 11:25:53 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from point_operate
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 11:25:53 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from point_operate
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.PointOperateExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 11:25:53 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from point_operate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.PointOperate">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 11:25:53 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into point_operate (id, order_id, user_id, 
      supplier_id, type, reason, 
      point, operator_id, create_time, 
      channel, activity_id, delete_time
      )
    values (#{id,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, 
      #{supplierId,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{reason,jdbcType=VARCHAR}, 
      #{point,jdbcType=BIGINT}, #{operatorId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{channel,jdbcType=INTEGER}, #{activityId,jdbcType=VARCHAR}, #{deleteTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.PointOperate">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 11:25:53 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into point_operate
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="point != null">
        point,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="activityId != null">
        activity_id,
      </if>
      <if test="deleteTime != null">
        delete_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="point != null">
        #{point,jdbcType=BIGINT},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=INTEGER},
      </if>
      <if test="activityId != null">
        #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="deleteTime != null">
        #{deleteTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.PointOperateExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 11:25:53 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from point_operate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 11:25:53 CST 2025. by MyBatis Generator, do not modify.
    -->
    update point_operate
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierId != null">
        supplier_id = #{record.supplierId,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.point != null">
        point = #{record.point,jdbcType=BIGINT},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=INTEGER},
      </if>
      <if test="record.activityId != null">
        activity_id = #{record.activityId,jdbcType=VARCHAR},
      </if>
      <if test="record.deleteTime != null">
        delete_time = #{record.deleteTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 11:25:53 CST 2025. by MyBatis Generator, do not modify.
    -->
    update point_operate
    set id = #{record.id,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      supplier_id = #{record.supplierId,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=INTEGER},
      reason = #{record.reason,jdbcType=VARCHAR},
      point = #{record.point,jdbcType=BIGINT},
      operator_id = #{record.operatorId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      channel = #{record.channel,jdbcType=INTEGER},
      activity_id = #{record.activityId,jdbcType=VARCHAR},
      delete_time = #{record.deleteTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.PointOperate">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 11:25:53 CST 2025. by MyBatis Generator, do not modify.
    -->
    update point_operate
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="point != null">
        point = #{point,jdbcType=BIGINT},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=INTEGER},
      </if>
      <if test="activityId != null">
        activity_id = #{activityId,jdbcType=VARCHAR},
      </if>
      <if test="deleteTime != null">
        delete_time = #{deleteTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.PointOperate">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 11:25:53 CST 2025. by MyBatis Generator, do not modify.
    -->
    update point_operate
    set order_id = #{orderId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      supplier_id = #{supplierId,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      reason = #{reason,jdbcType=VARCHAR},
      point = #{point,jdbcType=BIGINT},
      operator_id = #{operatorId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      channel = #{channel,jdbcType=INTEGER},
      activity_id = #{activityId,jdbcType=VARCHAR},
      delete_time = #{deleteTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 11:25:53 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into point_operate
    (id, order_id, user_id, supplier_id, type, reason, point, operator_id, create_time, 
      channel, activity_id, delete_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, 
        #{item.supplierId,jdbcType=VARCHAR}, #{item.type,jdbcType=INTEGER}, #{item.reason,jdbcType=VARCHAR}, 
        #{item.point,jdbcType=BIGINT}, #{item.operatorId,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.channel,jdbcType=INTEGER}, #{item.activityId,jdbcType=VARCHAR}, #{item.deleteTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 11:25:53 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into point_operate (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
        <if test="'supplier_id'.toString() == column.value">
          #{item.supplierId,jdbcType=VARCHAR}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=INTEGER}
        </if>
        <if test="'reason'.toString() == column.value">
          #{item.reason,jdbcType=VARCHAR}
        </if>
        <if test="'point'.toString() == column.value">
          #{item.point,jdbcType=BIGINT}
        </if>
        <if test="'operator_id'.toString() == column.value">
          #{item.operatorId,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'channel'.toString() == column.value">
          #{item.channel,jdbcType=INTEGER}
        </if>
        <if test="'activity_id'.toString() == column.value">
          #{item.activityId,jdbcType=VARCHAR}
        </if>
        <if test="'delete_time'.toString() == column.value">
          #{item.deleteTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>