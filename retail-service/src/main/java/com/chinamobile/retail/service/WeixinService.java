package com.chinamobile.retail.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.retail.pojo.vo.TimeAndNonceAndSignVO;
import com.chinamobile.retail.pojo.vo.WeixinAccessTokenVO;

public interface WeixinService {
    BaseAnswer<TimeAndNonceAndSignVO> getTimeAndNonceAndSign(String url);

    BaseAnswer<Void> refreshAccessToken();

    BaseAnswer<Void> refreshJsapiTicket();

    BaseAnswer<String> appLogin(String jsCode);
}
