package com.chinamobile.retail.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.param.ExportDistributeOrderParam;
import com.chinamobile.retail.pojo.param.OrderBackListParam;
import com.chinamobile.retail.pojo.vo.OrderBackListVO;

import javax.servlet.http.HttpServletResponse;

public interface OrderPointService {
    BaseAnswer<PageData<OrderBackListVO>> getOrderBackList(OrderBackListParam param);

    void exportOrderBackList(OrderBackListParam param);

    void exportDistributeOrder(ExportDistributeOrderParam param, HttpServletResponse response);
}
