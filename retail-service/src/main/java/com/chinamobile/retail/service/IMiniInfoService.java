package com.chinamobile.retail.service;

import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.param.MiniProgramInfoRequestOnlineParam;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.miniprogram.InfoDetailVO;
import com.chinamobile.retail.pojo.vo.miniprogram.KnowledgeHomeVO;
import com.chinamobile.retail.pojo.vo.miniprogram.PageInfoVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16 09:43
 * @description TODO
 */
public interface IMiniInfoService {

    InfoDetailVO getInfoDetail(String infoId,String userId,String provinceCode,LoginIfo4Redis loginIfo4Redis);

    PageData<PageInfoVO> pageInfoList(PageInfoListParam param,LoginIfo4Redis loginIfo4Redis);

    /**
     * 单独作为小程序使用
     * @param param
     * @return
     */
    PageData<PageInfoVO> pageInfoListMini(PageInfoListParam param);
    KnowledgeHomeVO pageKnowledgeHome();
    void create(InfoParam param, String userId);
    void edit(InfoParam param, String userId);
    void setPopular(String id,Integer isPopular);

    void audit(InfoAuditParam param, LoginIfo4Redis loginIfo4Redis);

    void offline(InfoOfflineParam param, LoginIfo4Redis loginIfo4Redis);

    Boolean judgeProduct(String spuCode, String userId,String provinceCode);

    void publish(PublishInfoParam param);

    List<PageInfoVO> searchInfo(String keyWord, Integer contentType, List<Integer> categoryList);
    void delete(String id);

    void requestOnline(MiniProgramInfoRequestOnlineParam param,LoginIfo4Redis loginIfo4Redis);
}
