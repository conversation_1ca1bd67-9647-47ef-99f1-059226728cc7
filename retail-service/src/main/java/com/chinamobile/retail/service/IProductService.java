package com.chinamobile.retail.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.entity.ContractProvinceInfo;
import com.chinamobile.retail.pojo.param.miniprogram.PageProductParam;
import com.chinamobile.retail.pojo.param.miniprogram.ProductDetailParam;
import com.chinamobile.retail.pojo.param.miniprogram.SearchProductParam;
import com.chinamobile.retail.pojo.param.miniprogram.WebSearchProductParam;
import com.chinamobile.retail.pojo.vo.MiniProgramProductDetailVO;
import com.chinamobile.retail.pojo.vo.MiniProgramProductListVO;
import com.chinamobile.retail.pojo.vo.ShareUrlVO;
import com.chinamobile.retail.pojo.vo.miniprogram.ProductNavigationDirectoryVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/22 17:26
 * @description TODO
 */
public interface IProductService {

    PageData<MiniProgramProductListVO> pageMiniProgramProduct(PageProductParam param, String userId);
    PageData<MiniProgramProductListVO> searchProduct(SearchProductParam param);
    String getShareCode(String scene,String page,String width,Integer expireInterva);
    String shareCodeScene(String scene);

    String getShareLink(String path,String query,Integer expireInterval);
    MiniProgramProductDetailVO productDetail(ProductDetailParam param, String userId);

    ShareUrlVO getShareUrl(String spuCode, String userId);

    PageData<MiniProgramProductListVO> searchProductForWeb(WebSearchProductParam param);

    MiniProgramProductDetailVO productDetailByActivityId(String spuCode, String activityId, String provinceCode);

    List<ProductNavigationDirectoryVO> listDirectory();

    PageData<MiniProgramProductListVO> searchProductForWebScene(WebSearchProductParam param);

    BaseAnswer<List<ContractProvinceInfo>> getProvinces();
}
