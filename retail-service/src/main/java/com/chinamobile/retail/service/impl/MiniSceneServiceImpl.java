package com.chinamobile.retail.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.enums.log.MiniProgramOperateEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.TransactionUtil;
import com.chinamobile.retail.config.ProvinceCityConfig;
import com.chinamobile.retail.config.SmsConfig;
import com.chinamobile.retail.constant.InfoAuditStatusEnum;
import com.chinamobile.retail.constant.InfoStatusEnum;
import com.chinamobile.retail.constant.RedisLockConstant;
import com.chinamobile.retail.dao.*;
import com.chinamobile.retail.dao.ext.MiniProgramInfoMapperExt;
import com.chinamobile.retail.dao.ext.MiniProgramSceneMapperExt;
import com.chinamobile.retail.dao.ext.SpuOfferingInfoMapperExt;
import com.chinamobile.retail.exception.StatusContant;
import com.chinamobile.retail.pojo.dto.SceneDirectoryDTO;
import com.chinamobile.retail.pojo.entity.*;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.MiniProgramSkuInfoVO;
import com.chinamobile.retail.pojo.vo.miniprogram.*;
import com.chinamobile.retail.service.IMiniSceneService;
import com.chinamobile.retail.util.cache.RedisCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.PARAM_ERROR;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 */
@Slf4j
@Service
public class MiniSceneServiceImpl implements IMiniSceneService {
    private static final int TARGET_MASK_MANAGER = 0x01;
    private static final int TARGET_MASK_DISTRIBUTOR = 0x02;
    private static final int TARGET_MASK_CHANNEL = 0x04;
    @Resource
    private RedisUtil redisUtil;

    @Resource
    private LogService logService;

    @Resource
    private MiniProgramSceneMapperExt miniProgramSceneMapperExt;

    @Resource
    private MiniProgramSceneMapper miniProgramSceneMapper;

    @Resource
    private MiniProgramSceneSpuMapper miniProgramSceneSpuMapper;

    @Resource
    private MiniProgramSceneDirectoryMapper miniProgramSceneDirectoryMapper;

    @Resource
    private UserMiniProgramMapper userMiniProgramMapper;

    @Resource
    private SpuOfferingInfoMapperExt spuOfferingInfoMapperExt;

    @Resource
    private SpuOfferingInfoMapper spuOfferingInfoMapper;

    @Resource
    private MiniProgramInfoMapperExt miniProgramInfoMapperExt;

    @Resource
    private ProvinceCityConfig provinceCityConfig;


    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private RedisCacheUtil redisCacheUtil;

    @Resource
    private ProductFlowInstanceAttachmentMapper productFlowInstanceAttachmentMapper;

    @Resource
    private MiniProgramSceneRequirementsMapper miniProgramSceneRequirementsMapper;

    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private SmsFeignClient smsFeignClient;

    /**
     * 派发短信模板
     */
    private String dispatchRequirementTemplateId;

    @Resource
    private MiniProgramSceneRequirementsTemplateMapper miniProgramSceneRequirementsTemplateMapper;

    @Resource
    private MiniProgramSceneRequirementsTemplateQuestionMapper miniProgramSceneRequirementsTemplateQuestionMapper;

    @Resource
    private MiniProgramSceneRequirementsAnswerMapper miniProgramSceneRequirementsAnswerMapper;

    @Resource
    private SmsConfig smsConfig;

    @Value("${partnerLoginUrl}")
    private String partnerLoginUrl;

    @Resource
    private SpuSkuAttachmentMapper spuSkuAttachmentMapper;

    private ExecutorService executorService = new ThreadPoolExecutor(50, 50, 1L, TimeUnit.MINUTES, new LinkedBlockingDeque<>(10000));

    /*********************************** 客户端接口 START ***********************************/
    /**
     * 获取场景目录
     *
     * @return
     */
    @DS("query")
    @Override
    public List<SceneDirectoryVO> listSceneDirectory() {
        return redisCacheUtil.loadAndCache(
                Constant.REDIS_KEY_MINI_SCENE_DIRECTORY,
                RedisLockConstant.LOCK_MINI_SCENE_DIRECTORY,
                () -> {
                    List<MiniProgramSceneDirectory> first = miniProgramSceneDirectoryMapper.selectByExample(
                            new MiniProgramSceneDirectoryExample().createCriteria()
                                    .andParentIdEqualTo("-1")
                                    .andDeletedEqualTo(false)
                                    .example().orderBy("sort")
                    );
                    if (CollectionUtils.isNotEmpty(first)) {
                        return first.stream().map(item -> {
                            SceneDirectoryVO sceneDirectoryVO = new SceneDirectoryVO();
                            BeanUtils.copyProperties(item, sceneDirectoryVO);
                            List<MiniProgramSceneDirectory> second = miniProgramSceneDirectoryMapper.selectByExample(
                                    new MiniProgramSceneDirectoryExample().createCriteria()
                                            .andParentIdEqualTo(item.getId())
                                            .andDeletedEqualTo(false)
                                            .example().orderBy("sort")
                            );
                            if (CollectionUtils.isNotEmpty(second)) {
                                List<SceneDirectoryVO> children = second.stream().map(child -> {
                                    SceneDirectoryVO childVO = new SceneDirectoryVO();
                                    BeanUtils.copyProperties(child, childVO);
                                    return childVO;
                                }).collect(Collectors.toList());
                                sceneDirectoryVO.setChildren(children);
                            }
                            return sceneDirectoryVO;
                        }).collect(Collectors.toList());
                    }
                    return Collections.emptyList();
                }
        );
    }

    @DS("query")
    @Override
    public PageData<SceneFrontendItemVO> pageSceneFrontend(PageSceneFrontendParam param) {
        PageData<SceneFrontendItemVO> pageData = new PageData<>();
        String pageKey = param.getSecondDirectoryId() + "-" + param.getPageNum() + "-" + param.getPageSize();
        List<SceneFrontendItemVO> sceneFrontendItemVOS = redisCacheUtil.loadAndCache(
                Constant.REDIS_KEY_MINI_SCENE_LIST + pageKey,
                RedisLockConstant.LOCK_MINI_SCENE_LIST + pageKey,
                30, TimeUnit.MINUTES,
                () -> miniProgramSceneMapperExt.pageSceneFrontend(param)
        );
        String countKey = param.getSecondDirectoryId();
        BigInteger count = redisCacheUtil.loadAndCache(
                Constant.REDIS_KEY_MINI_SCENE_COUNT + countKey,
                RedisLockConstant.LOCK_MINI_SCENE_COUNT + countKey,
                30, TimeUnit.MINUTES,
                () -> BigInteger.valueOf(miniProgramSceneMapperExt.countSceneFrontend(param))
        );
        if (CollectionUtils.isNotEmpty(sceneFrontendItemVOS)) {
            for (SceneFrontendItemVO item : sceneFrontendItemVOS) {
                List<SceneFrontendItemVO.RelatedSpuVO> relatedSpuVOS = redisCacheUtil.loadAndCache(
                        Constant.REDIS_KEY_MINI_SCENE_RELATED_SPU + item.getId(),
                        RedisLockConstant.LOCK_MINI_SCENE_RELATED_SPU + item.getId(),
                        1, TimeUnit.DAYS,
                        () -> miniProgramSceneMapperExt.selectRelatedSpu(item.getId())
                );
                item.setRelatedSpus(relatedSpuVOS);
            }
        }
        pageData.setData(sceneFrontendItemVOS);
        pageData.setCount(count != null ? count.longValue() : 0);
        pageData.setPage(param.getPageNum());
        return pageData;
    }

    @DS("query")
    @Override
    public SceneDetailFrontendVO getSceneDetailFrontend(SceneDetailFrontendParam param) {
        SceneDetailFrontendVO sceneDetailFrontendVO = redisCacheUtil.loadAndCache(
                Constant.REDIS_KEY_MINI_SCENE_DETAIL + param.getSceneId(),
                RedisLockConstant.LOCK_MINI_SCENE_DETAIL + param.getSceneId(),
                1, TimeUnit.DAYS,
                () -> {
                    SceneDetailFrontendVO vo = miniProgramSceneMapperExt.getSceneDetailFrontend(param.getSceneId());
                    if (null == vo) {
                        throw new BusinessException(StatusContant.MINI_PROGRAM_SCENE_NOT_EXIST);
                    }
                    ProductDetailParam productDetailParam = new ProductDetailParam();
                    productDetailParam.setSpuCode(vo.getSpuCode());
                    List<MiniProgramSkuInfoVO> skuInfoVOS = spuOfferingInfoMapperExt.getMiniProgramSkuList(productDetailParam);
                    if (CollectionUtils.isNotEmpty(skuInfoVOS)) {
                        MiniProgramSkuInfoVO skuInfoVO = skuInfoVOS.get(0);
                        List<SpuSkuAttachment> attachments = redisCacheUtil.loadAndCache(
                                Constant.REDIS_KEY_SPU_SKU_ATTACHMENT + skuInfoVO.getSpuCode(),
                                RedisLockConstant.LOCK_SPU_SKU_ATTACHMENT + skuInfoVO.getSpuCode(),
                                1,
                                TimeUnit.DAYS,
                                () -> spuSkuAttachmentMapper.selectByExample(
                                        new SpuSkuAttachmentExample().createCriteria()
                                                .andSpuCodeEqualTo(skuInfoVO.getSpuCode())
                                                .example()
                                )
                        );
                        if (CollectionUtils.isNotEmpty(attachments)) {
                            attachments.forEach(attachment -> {
                                switch (attachment.getType()) {
                                    case 3:
                                        vo.setDetailImage(attachment.getFileUrl());
                                        break;
                                    case 4:
                                        vo.setRealProductImage(attachment.getFileUrl());
                                        break;
                                    case 5:
                                        vo.setAfterSaleImage(attachment.getFileUrl());
                                        break;
                                }
                            });
                        }
                    }
                    return vo;
                }
        );
        List<SceneFrontendItemVO.RelatedSpuVO> relatedSpuVOS = redisCacheUtil.loadAndCache(
                Constant.REDIS_KEY_MINI_SCENE_RELATED_SPU + param.getSceneId(),
                RedisLockConstant.LOCK_MINI_SCENE_RELATED_SPU + param.getSceneId(),
                1, TimeUnit.DAYS,
                () -> miniProgramSceneMapperExt.selectRelatedSpu(param.getSceneId())
        );
        sceneDetailFrontendVO.setRelatedSpus(relatedSpuVOS);
        return sceneDetailFrontendVO;
    }

    @Override
    public SceneRequirementTemplateFrontendVO getSceneRequirementTemplate(String sceneId) {
        SceneDetailFrontendParam param = new SceneDetailFrontendParam();
        param.setSceneId(sceneId);
        SceneDetailFrontendVO sceneDetailFrontendVO = getSceneDetailFrontend(param);
        final String templateId = sceneDetailFrontendVO.getTemplateId();
        SceneRequirementTemplateFrontendVO result = redisCacheUtil.loadAndCache(
                Constant.REDIS_KEY_MINI_SCENE_REQUIREMENT_TEMPLATE + templateId,
                RedisLockConstant.LOCK_MINI_SCENE_REQUIREMENT_TEMPLATE + templateId,
                1, TimeUnit.DAYS,
                () -> {
                    SceneRequirementTemplateFrontendVO sceneRequirementTemplateFrontendVO = new SceneRequirementTemplateFrontendVO();
                    MiniProgramSceneRequirementsTemplate template = miniProgramSceneRequirementsTemplateMapper.selectByPrimaryKey(templateId);
                    if (null == template) {
                        return null;
                    }
                    BeanUtils.copyProperties(template, sceneRequirementTemplateFrontendVO);
                    List<MiniProgramSceneRequirementsTemplateQuestion> questions = miniProgramSceneRequirementsTemplateQuestionMapper.selectByExample(
                            new MiniProgramSceneRequirementsTemplateQuestionExample().createCriteria()
                                    .andTemplateIdEqualTo(templateId)
                                    .example()
                    );
                    if (CollectionUtils.isNotEmpty(questions)) {
                        List<SceneRequirementTemplateFrontendVO.Fields> fields = questions.stream().map(question -> {
                            SceneRequirementTemplateFrontendVO.Fields field = new SceneRequirementTemplateFrontendVO.Fields();
                            field.setFieldsId(question.getId());
                            field.setFieldsName(question.getQuestion());
                            field.setHint(question.getHint());
                            field.setRequired(question.getRequired());
                            return field;
                        }).collect(Collectors.toList());
                        sceneRequirementTemplateFrontendVO.setFields(fields);
                    }
                    return sceneRequirementTemplateFrontendVO;
                }
        );
        if (null == result) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_SCENE_REQUIREMENT_TEMPLATE_NOT_EXIST);
        }
        return result;
    }

    /**
     * 小程序提交场景方案设计
     */
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    @Override
    public void submitSceneRequirement(SceneRequirementSubmitParam param, LoginIfo4Redis loginIfo4Redis) {
        MiniProgramScene miniProgramScene = miniProgramSceneMapper.selectByPrimaryKey(param.getSceneId());
        if (null == miniProgramScene) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_SCENE_NOT_EXIST);
        }
        MiniProgramSceneRequirements miniProgramSceneRequirements = new MiniProgramSceneRequirements();
        BeanUtils.copyProperties(param, miniProgramSceneRequirements);
        miniProgramSceneRequirements.setId(BaseServiceUtils.getId());
        miniProgramSceneRequirements.setCreateUid(loginIfo4Redis.getUserId());
        miniProgramSceneRequirements.setAuditState(0);
        miniProgramSceneRequirements.setDeleted(false);
        miniProgramSceneRequirements.setCreateTime(new Date());
        miniProgramSceneRequirements.setUpdateTime(new Date());
        miniProgramSceneRequirementsMapper.insertSelective(miniProgramSceneRequirements);

        List<SceneRequirementSubmitParam.Fields> fields = param.getFields();
        SceneRequirementTemplateFrontendVO template = getSceneRequirementTemplate(param.getSceneId());
        if (template != null && CollectionUtils.isNotEmpty(template.getFields())) {
            // 找出空字段的id集合
            List<String> emptyFieldIds = fields.stream()
                    .filter(field -> ObjectUtils.isEmpty(field.getFieldsValue()))
                    .map(SceneRequirementSubmitParam.Fields::getFieldsId)
                    .collect(Collectors.toList());
            // 根据空字段的id集合，判断是否有必填的字段
            SceneRequirementTemplateFrontendVO.Fields emptyField = template.getFields().stream()
                    .filter(field -> field.getRequired() && emptyFieldIds.contains(field.getFieldsId()))
                    .findFirst()
                    .orElse(null);
            if (emptyField != null) {
                throw new BusinessException(PARAM_ERROR, emptyField.getFieldsName() + "不能为空");
            }
        }

        List<MiniProgramSceneRequirementsAnswer> answers = fields.stream().map(field -> {
            MiniProgramSceneRequirementsAnswer answer = new MiniProgramSceneRequirementsAnswer();
            answer.setId(BaseServiceUtils.getId());
            answer.setRequirementId(miniProgramSceneRequirements.getId());
            answer.setQuestionId(field.getFieldsId());
            answer.setAnswer(field.getFieldsValue());
            return answer;
        }).collect(Collectors.toList());

        miniProgramSceneRequirementsAnswerMapper.batchInsert(answers);
    }

    /*********************************** 客户端接口 STOP ***********************************/

    @Override
    @DS("query")
    public SceneVO getSceneDetail(String sceneId, LoginIfo4Redis loginIfo4Redis) {
        SceneVO vo = redisCacheUtil.loadAndCache(Constant.REDIS_KEY_MINI_SCENE_ID + sceneId,
                RedisLockConstant.LOCK_SCENE_ID + sceneId,
                1, TimeUnit.DAYS,
                () -> getSceneDetailInner(sceneId));
        if (ObjectUtils.isEmpty(vo)) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_SCENE_NOT_EXIST);
        }
        String content = "【查看】小场景\n" + vo.getName();
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.SCENE.code, content);
        return vo;
    }

    private SceneVO getSceneDetailInner(String sceneId) {
        SceneVO vo = new SceneVO();
        MiniProgramScene scene = miniProgramSceneMapper.selectByPrimaryKey(sceneId);
        if (scene == null) {
            return null;
        }
        BeanUtils.copyProperties(scene, vo);
        vo.setCreateUserName(miniProgramInfoMapperExt.getUserName(scene.getCreateUid()));
        vo.setFirstDirectoryName(miniProgramSceneDirectoryMapper.selectByPrimaryKey(scene.getFirstDirectoryId()).getName());
        vo.setSecondDirectoryName(miniProgramSceneDirectoryMapper.selectByPrimaryKey(scene.getSecondDirectoryId()).getName());
        if (StringUtils.isNotBlank(scene.getTemplateId())) {
            vo.setTemplateName(miniProgramSceneRequirementsTemplateMapper.selectByPrimaryKey(scene.getTemplateId()).getName());
        }
        List<SpuOfferingInfo> spuOfferingInfos = spuOfferingInfoMapper.selectByExample(new SpuOfferingInfoExample().createCriteria()
                .andOfferingCodeEqualTo(vo.getSpuCode()).example());
        if (CollectionUtils.isNotEmpty(spuOfferingInfos)) {
            vo.setSpuName(spuOfferingInfos.get(0).getOfferingName());
        }
        vo.setProducts(miniProgramSceneMapperExt.getSceneProduct(sceneId));
        return vo;
    }

//    @Override
//    @DS("query")
//    public SceneVO getSceneMini(String sceneId) {
//
//        return null;
//    }

    @Override
    @DS("query")
    public PageData<SceneVO> pageSceneList(PageSceneListParam param, LoginIfo4Redis loginIfo4Redis) {
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        if (pageNum < 0 || pageSize < 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "页码和每页数量必须大于0");
        }
        PageData<SceneVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        Long total = miniProgramSceneMapperExt.countSceneList(param);
        pageData.setCount(total != null ? total : 0);
        if (pageData.getCount() > 0) {
            List<SceneVO> list = miniProgramSceneMapperExt.pageSceneList(param);
            pageData.setData(list);
        }
        return pageData;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void create(SceneParam param, String userId) {
//        if (CollectionUtils.isNotEmpty(miniProgramSceneMapper.selectByExample(new MiniProgramSceneExample().createCriteria()
//                .andDeletedEqualTo(false).andStatusNotEqualTo(InfoStatusEnum.OFFLINE.getStatus()).andSpuCodeEqualTo(param.getSpuCode()).example()))) {
//            throw new BusinessException(PARAM_ERROR, "当前区域已有小场景，不能再配置");
//        }
        log.info("创建订单param:{}",JSON.toJSONString(param));
        Date now = new Date();
        MiniProgramScene scene = new MiniProgramScene();
        BeanUtils.copyProperties(param, scene);
        scene.setId(BaseServiceUtils.getId());
        scene.setCreateUid(userId);
        scene.setCreateTime(now);
        scene.setUpdateTime(now);
        scene.setStatus(param.getOprType() == 1 ? InfoStatusEnum.DRAFT.getStatus() : InfoStatusEnum.AUDITING.getStatus());
        scene.setAuditStatus(param.getOprType() == 1 ? InfoAuditStatusEnum.DRAFT.getStatus() : InfoAuditStatusEnum.AUDITING.getStatus());

        miniProgramSceneMapper.insertSelective(scene);


        miniProgramSceneSpuMapper.batchInsert(param.getProducts().stream().map(x -> {
            MiniProgramSceneSpu code = new MiniProgramSceneSpu();
            code.setId(BaseServiceUtils.getId());
            code.setSceneId(scene.getId());
            code.setSpuCode(x.getSpuCode());
            code.setX(x.getX());
            code.setY(x.getY());
            code.setCreateTime(now);
            code.setUpdateTime(now);
            return code;
        }).collect(Collectors.toList()));

        String content = "【创建】小场景\n" + scene.getName();
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.SCENE.code, content);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void edit(SceneParam param, String userId) {
        log.info("编辑订单param:{}",JSON.toJSONString(param));
        if (StringUtils.isBlank(param.getId())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "首页ID为空");
        }
        Date now = new Date();
        MiniProgramScene scene = miniProgramSceneMapper.selectByPrimaryKey(param.getId());
        if (ObjectUtils.isEmpty(scene)) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_SCENE_NOT_EXIST);
        }

        if (!InfoStatusEnum.DRAFT.getStatus().equals(scene.getStatus())
                && !InfoStatusEnum.REJECTED.getStatus().equals(scene.getStatus())
                && !InfoStatusEnum.OFFLINE.getStatus().equals(scene.getStatus())) {
            // 只有已上传和已驳回状态的素材才可以编辑
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_CANNOT_EDIT);
        }

//        if (!StringUtils.equals(param.getSpuCode(), scene.getSpuCode())) {
//            if (CollectionUtils.isNotEmpty(miniProgramSceneMapper.selectByExample(new MiniProgramSceneExample().createCriteria()
//                    .andDeletedEqualTo(false).andStatusNotEqualTo(InfoStatusEnum.OFFLINE.getStatus()).andSpuCodeEqualTo(param.getSpuCode()).example()))) {
//                throw new BusinessException(PARAM_ERROR, "当前SPU已有小场景，不能再配置");
//            }
//        }

        MiniProgramScene old = new MiniProgramScene();
        BeanUtils.copyProperties(scene, old);
        BeanUtils.copyProperties(param, scene);
        scene.setUpdateTime(now);
        // 充值素材状态和审核状态
        scene.setStatus(param.getOprType() == 1 ? InfoStatusEnum.DRAFT.getStatus() : InfoStatusEnum.AUDITING.getStatus());
        scene.setAuditStatus(param.getOprType() == 1 ? InfoAuditStatusEnum.DRAFT.getStatus() : InfoAuditStatusEnum.AUDITING.getStatus());
        miniProgramSceneMapper.updateByPrimaryKey(scene);


        List<MiniProgramSceneSpu> oldSpuCodes = miniProgramSceneSpuMapper.selectByExample(new MiniProgramSceneSpuExample().createCriteria()
                .andSceneIdEqualTo(scene.getId()).example());


        miniProgramSceneSpuMapper.deleteByExample(new MiniProgramSceneSpuExample().createCriteria()
                .andSceneIdEqualTo(scene.getId()).example());


        List<MiniProgramSceneSpu> spuCodes = param.getProducts().stream().map(x -> {
            MiniProgramSceneSpu code = new MiniProgramSceneSpu();
            code.setId(BaseServiceUtils.getId());
            code.setSceneId(scene.getId());
            code.setSpuCode(x.getSpuCode());
            code.setX(x.getX());
            code.setY(x.getY());
            code.setCreateTime(now);
            code.setUpdateTime(now);
            return code;
        }).collect(Collectors.toList());
        miniProgramSceneSpuMapper.batchInsert(spuCodes);

        TransactionUtil.afterCommit(() -> invalidScene2Redis(scene.getId()));
        String log = getEditContent(old, scene, oldSpuCodes, spuCodes);
        if (ObjectUtils.isNotEmpty(log)) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.SCENE.code, log);
        }

        TransactionUtil.afterCommit(() -> {
            redisCacheUtil.deleteAll(Constant.REDIS_KEY_MINI_SCENE_LIST);
            redisCacheUtil.deleteAll(Constant.REDIS_KEY_MINI_SCENE_COUNT);
            redisCacheUtil.delete(Constant.REDIS_KEY_MINI_SCENE_DETAIL + param.getId());
            redisCacheUtil.delete(Constant.REDIS_KEY_MINI_SCENE_RELATED_SPU + param.getId());
        });
    }

    private String getEditContent(MiniProgramScene old, MiniProgramScene scene, List<MiniProgramSceneSpu> oldSpuCodes,
                                  List<MiniProgramSceneSpu> spuCodes) {
        boolean writeLog = false;
        StringBuilder content = new StringBuilder();

        //小场景头图
        if (!StringUtils.equals(old.getHeadImageUrl(), scene.getHeadImageUrl())) {
            writeLog = true;
            content.append("\n").append("小场景头图").append("由").append(old.getHeadImageUrl())
                    .append("修改为").append(scene.getHeadImageUrl());
        }

        //小场景头图
        if (!StringUtils.equals(old.getTemplateId(), scene.getTemplateId())) {
            writeLog = true;
            content.append("\n").append("小场景需求模板").append("由").append(StringUtils.isBlank(old.getTemplateId()) ? "空"
                            : miniProgramSceneRequirementsTemplateMapper.selectByPrimaryKey(old.getTemplateId()).getName())
                    .append("修改为").append(StringUtils.isBlank(scene.getTemplateId()) ? "空"
                            : miniProgramSceneRequirementsTemplateMapper.selectByPrimaryKey(scene.getTemplateId()).getName());
        }

        //一级目录
        if (!StringUtils.equals(old.getFirstDirectoryId(), scene.getFirstDirectoryId())) {
            writeLog = true;
            content.append("\n").append("一级目录").append("由").append(
                            miniProgramSceneDirectoryMapper.selectByPrimaryKey(old.getFirstDirectoryId()).getName())
                    .append("修改为").append(miniProgramSceneDirectoryMapper.selectByPrimaryKey(scene.getFirstDirectoryId()).getName());
        }

        //二级目录
        if (!StringUtils.equals(old.getSecondDirectoryId(), scene.getSecondDirectoryId())) {
            writeLog = true;
            content.append("\n").append("二级目录").append("由").append(
                            miniProgramSceneDirectoryMapper.selectByPrimaryKey(old.getSecondDirectoryId()).getName())
                    .append("修改为").append(miniProgramSceneDirectoryMapper.selectByPrimaryKey(scene.getSecondDirectoryId()).getName());
        }

        //解决方案产品
        if (!StringUtils.equals(old.getSpuCode(), scene.getSpuCode())) {
            writeLog = true;
            content.append("\n").append("解决方案产品").append("由").append(
                            spuOfferingInfoMapper.selectByExample(new SpuOfferingInfoExample().createCriteria()
                                    .andOfferingCodeEqualTo(old.getSpuCode()).example()).get(0).getOfferingName())
                    .append("修改为").append(spuOfferingInfoMapper.selectByExample(new SpuOfferingInfoExample().createCriteria()
                            .andOfferingCodeEqualTo(scene.getSpuCode()).example()).get(0).getOfferingName());
        }

        //场景图片
        if (!StringUtils.equals(old.getImageUrl(), scene.getImageUrl())) {
            writeLog = true;
            content.append("\n").append("场景图片").append("由").append(old.getImageUrl())
                    .append("修改为").append(scene.getImageUrl());
        }

        //关联商品
        List<String> oldSpuCodesList = oldSpuCodes.stream().map(MiniProgramSceneSpu::getSpuCode).collect(Collectors.toList());
        List<String> spuCodesList = spuCodes.stream().map(MiniProgramSceneSpu::getSpuCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(CollectionUtils.subtract(oldSpuCodesList, spuCodesList))
                || CollectionUtils.isNotEmpty(CollectionUtils.subtract(spuCodesList, oldSpuCodesList))) {
            writeLog = true;
            String oldSpuStr = "空";
            if (CollectionUtils.isNotEmpty(oldSpuCodes)) {
                oldSpuStr = JSON.toJSONString(spuOfferingInfoMapper.selectByExample(new SpuOfferingInfoExample().createCriteria()
                        .andOfferingCodeIn(oldSpuCodesList).example()).stream().map(SpuOfferingInfo::getOfferingName).collect(Collectors.toList()));
            }

            String spuStr = "空";
            if (CollectionUtils.isNotEmpty(spuCodes)) {
                spuStr = JSON.toJSONString(spuOfferingInfoMapper.selectByExample(new SpuOfferingInfoExample().createCriteria()
                        .andOfferingCodeIn(spuCodesList).example()).stream().map(SpuOfferingInfo::getOfferingName).collect(Collectors.toList()));
            }
            content.append("\n").append("关联商品").append("由").append(oldSpuStr).append("修改为").append(spuStr);
        }


        if (writeLog) {

            //发布区域
            if (!StringUtils.equals(old.getName(), scene.getName())) {
                content.insert(0, new StringBuilder().append("\n").append("名称").append("由").append(old.getName())
                        .append("修改为").append(scene.getName()));
            } else {
                content.insert(0, new StringBuilder().append("\n").append("名称").append(scene.getName()));
            }

            content.insert(0, "【编辑小场景】");
        }
        return content.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void delete(InfoOfflineParam param, LoginIfo4Redis loginIfo4Redis) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "所删除的场景ID不能为空");
        }
        List<MiniProgramScene> scenes = miniProgramSceneMapper.selectByExample(new MiniProgramSceneExample().createCriteria()
                .andIdIn(param.getIds()).example());

        List<MiniProgramScene> cannotDelete = scenes.stream().filter(x -> !x.getStatus().equals(InfoStatusEnum.DRAFT.getStatus())
                && !x.getStatus().equals(InfoStatusEnum.OFFLINE.getStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(cannotDelete)) {
            throw new BusinessException(PARAM_ERROR, "所删除的场景"
                    + JSON.toJSONString(cannotDelete.stream().map(MiniProgramScene::getName).collect(Collectors.toList()))
                    + "不可以被删除");
        }
        Date now = new Date();
        scenes.forEach(x -> {
            x.setUpdateTime(now);
            x.setDeleted(true);

            miniProgramSceneMapper.updateByPrimaryKeySelective(x);
        });

        TransactionUtil.afterCommit(() -> invalidScene2Redis(param.getIds()));

        String header = param.getIds().size() > 1 ? "【批量删除】" : "【删除】";

        StringBuilder sb = new StringBuilder();
        sb.append(header);
        scenes.forEach(x -> sb.append("\n").append("小场景 ").append(x.getName()));

        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.SCENE.code, sb.toString());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void audit(InfoAuditParam param, LoginIfo4Redis loginIfo4Redis) {
        List<MiniProgramScene> scenes = miniProgramSceneMapper.selectByExample(new MiniProgramSceneExample().createCriteria()
                .andIdIn(param.getIds()).example());
        if (scenes.size() != param.getIds().size()) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "所审核的小场景"
                    + JSON.toJSONString(CollectionUtils.subtract(param.getIds(),
                    scenes.stream().map(MiniProgramScene::getId).collect(Collectors.toList())))
                    + "不存在");
        }
        List<MiniProgramScene> notInProcess = scenes.stream().filter(x -> !x.getAuditStatus().equals(InfoAuditStatusEnum.AUDITING.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notInProcess)) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_AUDIT_STATUS_WRONG, "所审核的场景"
                    + JSON.toJSONString(notInProcess.stream().map(MiniProgramScene::getName).collect(Collectors.toList()))
                    + "不可以被审核");
        }

        Date now = new Date();
        scenes.forEach(x -> {
            x.setUpdateTime(now);
            x.setAuditStatus(param.getApprove() ? InfoAuditStatusEnum.PASSED.getStatus() : InfoAuditStatusEnum.DENIED.getStatus());
            x.setStatus(param.getApprove() ? InfoStatusEnum.PUBLISHED.getStatus() : InfoStatusEnum.REJECTED.getStatus());

            miniProgramSceneMapper.updateByPrimaryKeySelective(x);
        });

        TransactionUtil.afterCommit(() -> invalidScene2Redis(param.getIds()));
        String header = null;
        if (param.getApprove()) {
            header = param.getIds().size() > 1 ? "【批量同意】" : "【同意】";
        } else {
            header = param.getIds().size() > 1 ? "【批量驳回】" : "【驳回】";
        }
        StringBuilder sb = new StringBuilder();
        sb.append(header);
        scenes.forEach(x -> sb.append("\n").append("小场景 ").append(x.getName()));

        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.SCENE.code, sb.toString());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void offline(InfoOfflineParam param, LoginIfo4Redis loginIfo4Redis) {
        List<MiniProgramScene> scenes = miniProgramSceneMapper.selectByExample(new MiniProgramSceneExample().createCriteria()
                .andIdIn(param.getIds()).example());
        if (scenes.size() != param.getIds().size()) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "所下线的小场景"
                    + JSON.toJSONString(CollectionUtils.subtract(param.getIds(),
                    scenes.stream().map(MiniProgramScene::getId).collect(Collectors.toList())))
                    + "不存在");
        }
        List<MiniProgramScene> notPublished = scenes.stream().filter(x -> !x.getStatus().equals(InfoStatusEnum.PUBLISHED.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notPublished)) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_AUDIT_STATUS_WRONG, "所下线的小场景"
                    + JSON.toJSONString(notPublished.stream().map(MiniProgramScene::getName).collect(Collectors.toList()))
                    + "不可以被下线");
        }

        Date now = new Date();
        scenes.forEach(x -> {
            x.setUpdateTime(now);
            x.setStatus(InfoStatusEnum.OFFLINE.getStatus());
            miniProgramSceneMapper.updateByPrimaryKeySelective(x);
        });
        TransactionUtil.afterCommit(() -> invalidScene2Redis(param.getIds()));

        StringBuilder sb = new StringBuilder("【下线】");
        scenes.forEach(x -> sb.append("\n").append("小场景 ").append(x.getName()));
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.SCENE.code, sb.toString());
    }

    @Transactional
    @Override
    @DS("save")
    public void publish(HomePublishParam param, LoginIfo4Redis loginIfo4Redis) {
        List<MiniProgramScene> scenes = miniProgramSceneMapper.selectByExample(new MiniProgramSceneExample().createCriteria()
                .andIdIn(param.getIds()).example());
        if (scenes.size() != param.getIds().size()) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "所发布的小场景"
                    + JSON.toJSONString(CollectionUtils.subtract(param.getIds(),
                    scenes.stream().map(MiniProgramScene::getId).collect(Collectors.toList())))
                    + "不存在");
        }
        List<MiniProgramScene> notDraft = scenes.stream().filter(x -> !InfoAuditStatusEnum.DRAFT.getStatus().equals(x.getAuditStatus())
                && !InfoAuditStatusEnum.DENIED.getStatus().equals(x.getAuditStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notDraft)) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_AUDIT_STATUS_WRONG, "所发布的小场景"
                    + JSON.toJSONString(notDraft.stream().map(MiniProgramScene::getName).collect(Collectors.toList()))
                    + "不可以被发布");
        }
        Date now = new Date();
        scenes.forEach(x -> {

            x.setUpdateTime(now);
            x.setStatus(InfoStatusEnum.AUDITING.getStatus());
            x.setAuditStatus(InfoAuditStatusEnum.AUDITING.getStatus());
            miniProgramSceneMapper.updateByPrimaryKeySelective(x);
        });
        TransactionUtil.afterCommit(() -> invalidScene2Redis(param.getIds()));
        StringBuilder sb = new StringBuilder("【发布】");
        scenes.forEach(x -> sb.append("\n").append("小场景 ").append(x.getName()));
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.INFO.code, sb.toString());
    }

    /**
     * 添加活动进缓存
     */
    private void invalidScene2Redis(List<String> sceneIds) {
        //异步加载首页进缓存
        executorService.execute(() -> sceneIds.forEach(sceneId -> {
            redisCacheUtil.delete(Constant.REDIS_KEY_MINI_SCENE_ID + sceneId);
            redisCacheUtil.delete(Constant.REDIS_KEY_MINI_SCENE_DETAIL + sceneId);
            redisCacheUtil.delete(Constant.REDIS_KEY_MINI_SCENE_RELATED_SPU + sceneId);
        }));
        redisCacheUtil.deleteAll(Constant.REDIS_KEY_MINI_SCENE_LIST);
        redisCacheUtil.deleteAll(Constant.REDIS_KEY_MINI_SCENE_COUNT);
    }

    private void invalidScene2Redis(String sceneId) {
        //异步加载首页进缓存
        List<String> homeIds = new ArrayList<>();
        homeIds.add(sceneId);
        invalidScene2Redis(homeIds);
    }

    @Override
    @DS("query")
    public List<SceneDirectoryDTO> getSceneDirectoryList(LoginIfo4Redis loginIfo4Redis) {
        String key = Constant.REDIS_KEY_SCENE_NAVIGATION_DIRECTORY;
        String lockKey = RedisLockConstant.LOCK_SCENE_DIRECTORY_LIST;

        List<SceneDirectoryDTO> directory = redisCacheUtil.loadAndCache(key, lockKey, () -> {
            List<MiniProgramSceneDirectory> first = miniProgramSceneDirectoryMapper.selectByExample(
                    new MiniProgramSceneDirectoryExample().createCriteria()
                            .andParentIdEqualTo("-1")
                            .andDeletedEqualTo(false)
                            .example().orderBy("sort")
            );
            List<SceneDirectoryDTO> result = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(first)) {
                result = first.stream().map(item -> {
                    SceneDirectoryDTO vo = new SceneDirectoryDTO();
                    BeanUtils.copyProperties(item, vo);
                    List<MiniProgramSceneDirectory> second = miniProgramSceneDirectoryMapper.selectByExample(
                            new MiniProgramSceneDirectoryExample().createCriteria()
                                    .andParentIdEqualTo(item.getId())
                                    .andDeletedEqualTo(false)
                                    .example().orderBy("sort")
                    );
                    if (CollectionUtils.isNotEmpty(second)) {
                        List<SceneDirectoryDTO> children = second.stream().map(child -> {
                            SceneDirectoryDTO childVO = new SceneDirectoryDTO();
                            BeanUtils.copyProperties(child, childVO);
                            return childVO;
                        }).collect(Collectors.toList());
                        vo.setChildren(children);
                    }
                    return vo;
                }).collect(Collectors.toList());
            }
            return result;
        });


        return directory;
    }

    @Override
    @DS("save")
    @Transactional(rollbackFor = Exception.class)
    public void createDir(SceneDirectoryParam param, String userId) {
        Date now = new Date();
        MiniProgramSceneDirectory directory = new MiniProgramSceneDirectory();
        BeanUtils.copyProperties(param, directory);
        directory.setId(BaseServiceUtils.getId());
        directory.setSort(-1);
        directory.setCreateTime(now);
        directory.setUpdateTime(now);
        directory.setDeleted(false);

        MiniProgramSceneDirectory parent = null;
        boolean isFirst = false;
        if (StringUtils.equals(param.getParentId(), "-1")) {
            isFirst = true;
        } else {
            parent = miniProgramSceneDirectoryMapper.selectByPrimaryKey(param.getParentId());
            if (parent == null) {
                throw new BusinessException(PARAM_ERROR, "父目录不存在");
            }
        }

        miniProgramSceneDirectoryMapper.insertSelective(directory);

        //排序，将新增的目录放到第一个
        List<MiniProgramSceneDirectory> updateSortDir;
        if (isFirst) {
            updateSortDir = miniProgramSceneDirectoryMapper.selectByExample(new MiniProgramSceneDirectoryExample().createCriteria()
                    .andParentIdEqualTo("-1").example());
        } else {
            updateSortDir = miniProgramSceneDirectoryMapper.selectByExample(new MiniProgramSceneDirectoryExample().createCriteria()
                    .andParentIdEqualTo(directory.getParentId()).example());
        }
        updateSortDir.forEach(x -> {
            x.setSort(x.getSort() + 1);
            x.setUpdateTime(now);
            miniProgramSceneDirectoryMapper.updateByPrimaryKeySelective(x);
        });


        String content = isFirst ? "【新增目录】\n目录名称" + directory.getName()
                : "【新增子目录】\n一级目录名称" + parent.getName() + "下新增子目录" + directory.getName();
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.SCENE.code, content);

        TransactionUtil.afterCommit(() -> {
            String key = Constant.REDIS_KEY_SCENE_NAVIGATION_DIRECTORY;
            redisTemplate.delete(key);
            redisCacheUtil.delete(Constant.REDIS_KEY_MINI_SCENE_DIRECTORY);
        });
    }

    @Override
    @DS("save")
    @Transactional(rollbackFor = Exception.class)
    public void editDir(SceneDirectoryParam param, String userId) {
        if (StringUtils.isBlank(param.getId())) {
            throw new BusinessException(PARAM_ERROR, "id不能为空");
        }
        Date now = new Date();
        MiniProgramSceneDirectory directory = miniProgramSceneDirectoryMapper.selectByPrimaryKey(param.getId());
        if (directory == null) {
            throw new BusinessException(PARAM_ERROR, "目录不存在");
        }
        String oldName = directory.getName();
        BeanUtils.copyProperties(param, directory);
        directory.setUpdateTime(now);
        miniProgramSceneDirectoryMapper.updateByPrimaryKeySelective(directory);

        String content = "【编辑目录】\n目录名称" + oldName + "修改为" + directory.getName();
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.SCENE.code, content);

        TransactionUtil.afterCommit(() -> {
            String key = Constant.REDIS_KEY_SCENE_NAVIGATION_DIRECTORY;
            redisTemplate.delete(key);
            redisCacheUtil.delete(Constant.REDIS_KEY_MINI_SCENE_DIRECTORY);
        });
    }

    @Override
    @DS("save")
    @Transactional(rollbackFor = Exception.class)
    public void deleteDir(InfoOfflineParam param, LoginIfo4Redis loginIfo4Redis) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "所删除的目录ID不能为空");
        }
        List<MiniProgramSceneDirectory> directories = miniProgramSceneDirectoryMapper.selectByExample(new MiniProgramSceneDirectoryExample()
                .createCriteria().andIdIn(param.getIds()).example());
        if (CollectionUtils.size(directories) != CollectionUtils.size(param.getIds())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "所删除的目录"
                    + JSON.toJSONString(CollectionUtils.subtract(param.getIds(),
                    directories.stream().map(MiniProgramSceneDirectory::getId).collect(Collectors.toList())))
                    + "不存在");
        }
        Date now = new Date();
        StringBuilder content = new StringBuilder();
        directories.forEach(dir -> {
            boolean isFirst = StringUtils.equals(dir.getParentId(), "-1");
            if (isFirst) {
                if (CollectionUtils.isNotEmpty(miniProgramSceneDirectoryMapper.selectByExample(new MiniProgramSceneDirectoryExample()
                        .createCriteria().andParentIdEqualTo(dir.getId()).example()))) {
                    throw new BusinessException(StatusContant.PARAM_ERROR, "删除失败！请先删除二级目录！");
                }
                content.append("\n一级目录名称").append(dir.getName());
            } else {
                content.append("\n一级目录名称").append(miniProgramSceneDirectoryMapper.selectByPrimaryKey(dir.getParentId()).getName())
                        .append("下子目录名称").append(dir.getName());
            }
            List<MiniProgramScene> scenes = isFirst ? miniProgramSceneMapper.selectByExample(new MiniProgramSceneExample().createCriteria()
                    .andFirstDirectoryIdEqualTo(dir.getId()).andDeletedEqualTo(false).example())
                    : miniProgramSceneMapper.selectByExample(new MiniProgramSceneExample().createCriteria()
                    .andSecondDirectoryIdEqualTo(dir.getId()).andDeletedEqualTo(false).example());
            if (CollectionUtils.isNotEmpty(scenes)) {
                throw new BusinessException(StatusContant.PARAM_ERROR, "删除失败！请先下线该子目录下所有解决方案！");
            }
            dir.setDeleted(true);
            dir.setUpdateTime(now);
            miniProgramSceneDirectoryMapper.updateByPrimaryKeySelective(dir);

        });
        content.insert(0, directories.stream().anyMatch(x -> StringUtils.equals(x.getParentId(), "-1")) ? "【删除一级目录】" : "【删除子目录】");
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.SCENE.code, content.toString());
        TransactionUtil.afterCommit(() -> {
            String key = Constant.REDIS_KEY_SCENE_NAVIGATION_DIRECTORY;
            redisTemplate.delete(key);
            redisCacheUtil.delete(Constant.REDIS_KEY_MINI_SCENE_DIRECTORY);
        });
    }

    @Override
    @DS("save")
    @Transactional(rollbackFor = Exception.class)
    public void sortDir(List<SortNavigationParam> params) {
        List<MiniProgramSceneDirectory> list = new ArrayList<>();
        for (int i = 0; i < params.size(); i++) {
            SortNavigationParam param = params.get(i);
            MiniProgramSceneDirectory sceneNavigationDirectory = new MiniProgramSceneDirectory();
            sceneNavigationDirectory.setId(param.getDirectoryId());
            sceneNavigationDirectory.setSort(i);
            list.add(sceneNavigationDirectory);

            List<String> childIds = param.getChildIds();
            for (int j = 0; j < childIds.size(); j++) {
                MiniProgramSceneDirectory child = new MiniProgramSceneDirectory();
                child.setId(childIds.get(j));
                child.setSort(j);
                list.add(child);
            }
        }
        list.forEach(sceneNavigationDirectory ->
                miniProgramSceneDirectoryMapper.updateByPrimaryKeySelective(sceneNavigationDirectory)
        );

        String content = "【一级/子目录 上移/下移】\n修改场景目录排序";
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.SCENE.code, content);
        TransactionUtil.afterCommit(() -> {
            String key = Constant.REDIS_KEY_SCENE_NAVIGATION_DIRECTORY;
            redisTemplate.delete(key);
            redisCacheUtil.delete(Constant.REDIS_KEY_MINI_SCENE_DIRECTORY);
        });

    }

    @Override
    @DS("query")
    public PageData<SceneRequirementVO> pageRequirementList(PageRequirementListParam param, LoginIfo4Redis loginIfo4Redis) {
        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (ObjectUtils.isEmpty(dataPermissionCodes) || (
                !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_SCENE_REQUIREMENT_SYSTEM)
                        && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_SCENE_REQUIREMENT_PERSONAL)
        )) {
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
        }

        if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_SCENE_REQUIREMENT_PERSONAL)) {
            param.setPartnerBusinessId(loginIfo4Redis.getUserId());
        }

        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        if (pageNum < 0 || pageSize < 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "页码和每页数量必须大于0");
        }
        PageData<SceneRequirementVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        Long total = miniProgramSceneMapperExt.countRequirementList(param);
        pageData.setCount(total != null ? total : 0);
        if (pageData.getCount() > 0) {
            List<SceneRequirementVO> list = miniProgramSceneMapperExt.pageRequirementList(param);
            pageData.setData(list);
        }
        return pageData;
    }

    @Override
    @DS("query")
    public SceneRequirementVO getRequirementDetail(String requirementId, LoginIfo4Redis loginIfo4Redis) {
        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (ObjectUtils.isEmpty(dataPermissionCodes) || (
                !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_SCENE_REQUIREMENT_SYSTEM)
                        && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_SCENE_REQUIREMENT_PERSONAL)
        )) {
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
        }

        MiniProgramSceneRequirements miniProgramSceneRequirements = miniProgramSceneRequirementsMapper.selectByPrimaryKey(requirementId);
        if (ObjectUtils.isEmpty(miniProgramSceneRequirements)) {
            throw new BusinessException(PARAM_ERROR, "场景需求不存在");
        }
        if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_SCENE_REQUIREMENT_PERSONAL)
                && !StringUtils.equals(loginIfo4Redis.getUserId(), miniProgramSceneRequirements.getPartnerBusinessId())) {
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
        }
        SceneRequirementVO vo = new SceneRequirementVO();
        BeanUtils.copyProperties(miniProgramSceneRequirements, vo);
        vo.setSceneName(miniProgramSceneMapper.selectByPrimaryKey(vo.getSceneId()).getName());
        vo.setAnswers(miniProgramSceneMapperExt.getRequirementAnswers(vo.getId()));
        if (StringUtils.isNotBlank(vo.getPartnerBusinessId())) {
            vo.setPartnerBusinessName(miniProgramSceneMapperExt.getPartnerBusinessName(vo.getPartnerBusinessId()));
        }

        String content = "【查看】\n需求场景名称" + vo.getSceneName() + "\n需求创建时间"
                + DateUtils.dateToStr(vo.getCreateTime(), DateUtils.DEFAULT_DATETIME_FORMAT);
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.SCENE.code, content);
        return vo;
    }

    @Override
    @DS("save")
    @Transactional(rollbackFor = Exception.class)
    public void deleteRequirement(InfoOfflineParam param, LoginIfo4Redis loginIfo4Redis) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "所删除的需求ID不能为空");
        }
        List<MiniProgramSceneRequirements> requirements = miniProgramSceneRequirementsMapper.selectByExample(new MiniProgramSceneRequirementsExample()
                .createCriteria().andIdIn(param.getIds()).example());
        if (CollectionUtils.size(requirements) != CollectionUtils.size(param.getIds())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "所删除的需求"
                    + JSON.toJSONString(CollectionUtils.subtract(param.getIds(),
                    requirements.stream().map(MiniProgramSceneRequirements::getId).collect(Collectors.toList())))
                    + "不存在");
        }
        Date now = new Date();
        requirements.forEach(x -> {
            x.setDeleted(true);
            x.setUpdateTime(now);
            miniProgramSceneRequirementsMapper.updateByPrimaryKeySelective(x);

        });
        String header = param.getIds().size() > 1 ? "【批量删除】" : "【删除】";

        StringBuilder sb = new StringBuilder();
        sb.append(header);
        requirements.forEach(x -> sb.append("\n").append("需求场景名称")
                .append(miniProgramSceneMapper.selectByPrimaryKey(x.getSceneId()).getName())
                .append("\n需求创建时间").append(DateUtils.dateToStr(x.getCreateTime(), DateUtils.DEFAULT_DATETIME_FORMAT)));
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.SCENE.code, sb.toString());
    }

    @Override
    @DS("save")
    @Transactional(rollbackFor = Exception.class)
    public void dispatchRequirement(RequirementDispatchParam param, LoginIfo4Redis loginIfo4Redis) {
        List<MiniProgramSceneRequirements> requirements = miniProgramSceneRequirementsMapper.selectByExample(new MiniProgramSceneRequirementsExample()
                .createCriteria().andIdIn(param.getIds()).example());
        if (CollectionUtils.size(requirements) != CollectionUtils.size(param.getIds())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "所派发的需求"
                    + JSON.toJSONString(CollectionUtils.subtract(param.getIds(),
                    requirements.stream().map(MiniProgramSceneRequirements::getId).collect(Collectors.toList())))
                    + "不存在");
        }
        Date now = new Date();
        requirements.forEach(x -> {
            x.setPartnerBusinessId(param.getPartnerBusinessId());
            x.setUpdateTime(now);
            x.setAuditState(2);
            miniProgramSceneRequirementsMapper.updateByPrimaryKeySelective(x);

        });

        //TODO 发短信
        silentSendMsg(param.getPartnerBusinessId(), requirements, smsConfig.getDispatchRequirementTemplateId());

        BaseAnswer<Data4User> baseAnswer = userFeignClient.userInfo(param.getPartnerBusinessId());
        if (baseAnswer == null || !baseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode()) || baseAnswer.getData() == null) {
            log.error("调用获取用户信息失败。合作伙伴ID:{}", param.getPartnerBusinessId());
            throw new BusinessException(BaseErrorConstant.GET_COOPERATOR_INFO_FAILED, "调用获取用户信息失败。合作伙伴ID:" + param.getPartnerBusinessId());
        }

        String header = param.getIds().size() > 1 ? "【批量派发】" : "【派发】";

        StringBuilder sb = new StringBuilder();
        sb.append(header);
        requirements.forEach(x -> sb.append("\n").append("需求场景名称")
                .append(miniProgramSceneMapper.selectByPrimaryKey(x.getSceneId()).getName())
                .append("\n需求创建时间").append(DateUtils.dateToStr(x.getCreateTime(), DateUtils.DEFAULT_DATETIME_FORMAT))
                .append("\n派发对象名字").append(StringUtils.defaultString(baseAnswer.getData().getName()))
                .append("电话").append(baseAnswer.getData().getPhone()));
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.SCENE.code, sb.toString());

    }

    @Override
    @DS("save")
    @Transactional(rollbackFor = Exception.class)
    public void rejectRequirement(InfoOfflineParam param, LoginIfo4Redis loginIfo4Redis) {
        List<MiniProgramSceneRequirements> requirements = miniProgramSceneRequirementsMapper.selectByExample(new MiniProgramSceneRequirementsExample()
                .createCriteria().andIdIn(param.getIds()).example());
        if (CollectionUtils.size(requirements) != CollectionUtils.size(param.getIds())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "所派发的拒绝"
                    + JSON.toJSONString(CollectionUtils.subtract(param.getIds(),
                    requirements.stream().map(MiniProgramSceneRequirements::getId).collect(Collectors.toList())))
                    + "不存在");
        }
        Date now = new Date();
        requirements.forEach(x -> {
            x.setUpdateTime(now);
            x.setAuditState(1);
            miniProgramSceneRequirementsMapper.updateByPrimaryKeySelective(x);

        });
        String header = param.getIds().size() > 1 ? "【批量拒绝】" : "【拒绝】";

        StringBuilder sb = new StringBuilder();
        sb.append(header);
        requirements.forEach(x -> sb.append("\n").append("需求场景名称")
                .append(miniProgramSceneMapper.selectByPrimaryKey(x.getSceneId()).getName())
                .append("\n需求创建时间").append(DateUtils.dateToStr(x.getCreateTime(), DateUtils.DEFAULT_DATETIME_FORMAT)));
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.SCENE.code, sb.toString());
    }

    private void silentSendMsg(String partnerBusinessId, List<MiniProgramSceneRequirements> requirements, String templateId) {
        try {
            //排除软件订单商品

            BaseAnswer<Data4User> baseAnswer = userFeignClient.userInfo(partnerBusinessId);
            if (baseAnswer == null || !baseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode()) || baseAnswer.getData() == null) {
                log.error("调用获取用户信息失败。合作伙伴ID:{}", partnerBusinessId);
                throw new BusinessException(BaseErrorConstant.GET_COOPERATOR_INFO_FAILED, "调用获取用户信息失败。合作伙伴ID:" + partnerBusinessId);
            }
            String phone = baseAnswer.getData().getPhone();
            List<String> phones = new ArrayList<>();
            Msg4Request msg4Request = new Msg4Request();

            phones.add(phone);
            if (CollectionUtil.isNotEmpty(phones)) {
                msg4Request.setMobiles(phones);
                msg4Request.setTemplateId(templateId);
                Map<String, String> msgMap = new HashMap<>();
                msgMap.put("param1", partnerLoginUrl);
                msg4Request.setMessage(msgMap);
                smsFeignClient.asySendMessage(msg4Request);
            }
        } catch (Exception e) {
            log.warn("发送取消订单短信失败，失败原因:{}", e.toString());
        }
    }


    @Override
    @DS("query")
    public TemplateVO getTemplateDetail(String templateId, LoginIfo4Redis loginIfo4Redis) {
        TemplateVO vo = redisCacheUtil.loadAndCache(Constant.REDIS_KEY_MINI_TEMPLATE_ID + templateId,
                RedisLockConstant.LOCK_TEMPLATE_ID + templateId,
                1, TimeUnit.DAYS,
                () -> {
                    TemplateVO templateVO = new TemplateVO();
                    MiniProgramSceneRequirementsTemplate template = miniProgramSceneRequirementsTemplateMapper.selectByPrimaryKey(templateId);
                    if (template == null) {
                        return null;
                    }
                    BeanUtils.copyProperties(template, templateVO);
                    templateVO.setCreateUserName(miniProgramInfoMapperExt.getUserName(template.getCreateUid()));
                    templateVO.setQuestions(miniProgramSceneRequirementsTemplateQuestionMapper.selectByExample(
                            new MiniProgramSceneRequirementsTemplateQuestionExample().createCriteria().andTemplateIdEqualTo(templateId).example()));
                    return templateVO;
                });
        if (ObjectUtils.isEmpty(vo)) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_TEMPLATE_NOT_EXIST);
        }
        String content = "【查看】模板名称\n" + vo.getName();
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.SCENE.code, content);
        return vo;
    }

    @Override
    @DS("query")
    public PageData<TemplateVO> pageTemplateList(PageTemplateListParam param, LoginIfo4Redis loginIfo4Redis) {
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        if (pageNum < 0 || pageSize < 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "页码和每页数量必须大于0");
        }
        PageData<TemplateVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        Long total = miniProgramSceneMapperExt.countTemplateList(param);
        pageData.setCount(total != null ? total : 0);
        if (pageData.getCount() > 0) {
            List<TemplateVO> list = miniProgramSceneMapperExt.pageTemplateList(param);
            pageData.setData(list);
        }
        return pageData;
    }

    @Override
    @DS("query")
    public List<TemplateVO> searchTemplate(PageTemplateListParam param, LoginIfo4Redis loginIfo4Redis) {
        return miniProgramSceneMapperExt.searchTemplateList(param);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void createTemplate(TemplateParam param, String userId) {
        Date now = new Date();
        MiniProgramSceneRequirementsTemplate template = new MiniProgramSceneRequirementsTemplate();
        BeanUtils.copyProperties(param, template);
        template.setId(BaseServiceUtils.getId());
        template.setCreateUid(userId);
        template.setCreateTime(now);
        template.setUpdateTime(now);

        miniProgramSceneRequirementsTemplateMapper.insertSelective(template);


        miniProgramSceneRequirementsTemplateQuestionMapper.batchInsert(param.getQuestions().stream().map(x -> {
            MiniProgramSceneRequirementsTemplateQuestion question = new MiniProgramSceneRequirementsTemplateQuestion();
            BeanUtils.copyProperties(x, question);
            question.setId(BaseServiceUtils.getId());
            question.setRequired(false);
            question.setTemplateId(template.getId());
            if (ObjectUtils.isEmpty(question.getRequired()) || StringUtils.isBlank(question.getQuestion())) {
                throw new BusinessException(PARAM_ERROR,"问题名称不能为空");
            }
            return question;
        }).collect(Collectors.toList()));

        String content = "【新建模板】\n模板名称" + template.getName();
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.SCENE.code, content);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void editTemplate(TemplateParam param, String userId) {
        if (StringUtils.isBlank(param.getId())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "首页ID为空");
        }
        Date now = new Date();
        MiniProgramSceneRequirementsTemplate template = miniProgramSceneRequirementsTemplateMapper.selectByPrimaryKey(param.getId());
        if (ObjectUtils.isEmpty(template)) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_TEMPLATE_NOT_EXIST);
        }


        MiniProgramSceneRequirementsTemplate old = new MiniProgramSceneRequirementsTemplate();
        BeanUtils.copyProperties(template, old);
        BeanUtils.copyProperties(param, template);
        template.setUpdateTime(now);
        miniProgramSceneRequirementsTemplateMapper.updateByPrimaryKey(template);


        List<MiniProgramSceneRequirementsTemplateQuestion> oldQuestions = miniProgramSceneRequirementsTemplateQuestionMapper.selectByExample(
                new MiniProgramSceneRequirementsTemplateQuestionExample().createCriteria()
                        .andTemplateIdEqualTo(template.getId()).example());


        miniProgramSceneRequirementsTemplateQuestionMapper.deleteByExample(new MiniProgramSceneRequirementsTemplateQuestionExample().createCriteria()
                .andTemplateIdEqualTo(template.getId()).example());


        List<MiniProgramSceneRequirementsTemplateQuestion> questions = param.getQuestions().stream().map(x -> {
            MiniProgramSceneRequirementsTemplateQuestion question = new MiniProgramSceneRequirementsTemplateQuestion();
            BeanUtils.copyProperties(x, question);
            question.setRequired(false);
            if (StringUtils.isBlank(question.getId())) {
                question.setId(BaseServiceUtils.getId());
            }
            question.setTemplateId(template.getId());
            if (ObjectUtils.isEmpty(question.getRequired()) || StringUtils.isBlank(question.getQuestion())) {
                throw new BusinessException(PARAM_ERROR,"问题名称不能为空");
            }
            return question;
        }).collect(Collectors.toList());
        miniProgramSceneRequirementsTemplateQuestionMapper.batchInsert(questions);

        String log = getEditTemplateContent(old, template, oldQuestions, questions);
        if (ObjectUtils.isNotEmpty(log)) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.SCENE.code, log);
        }

        TransactionUtil.afterCommit(() -> {
            redisCacheUtil.delete(Constant.REDIS_KEY_MINI_TEMPLATE_ID + template.getId());
            redisCacheUtil.delete(Constant.REDIS_KEY_MINI_SCENE_REQUIREMENT_TEMPLATE+ template.getId());
        });
    }

    private String getEditTemplateContent(MiniProgramSceneRequirementsTemplate old, MiniProgramSceneRequirementsTemplate template,
                                          List<MiniProgramSceneRequirementsTemplateQuestion> oldQuestions,
                                          List<MiniProgramSceneRequirementsTemplateQuestion> questions) {
        boolean writeLog = false;
        StringBuilder content = new StringBuilder();

        if (!StringUtils.equals(old.getName(), template.getName())) {
            writeLog = true;
        }

        //关联商品
        List<String> oldQuestionList = oldQuestions.stream().map(x -> x.getQuestion() + "_" + StringUtils.defaultString(x.getHint())).collect(Collectors.toList());
        List<String> questionList = questions.stream().map(x -> x.getQuestion() + "_" + StringUtils.defaultString(x.getHint())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(CollectionUtils.subtract(oldQuestionList, questionList))
                || CollectionUtils.isNotEmpty(CollectionUtils.subtract(questionList, oldQuestionList))) {
            writeLog = true;
            String oldSpuStr = "空";
            if (CollectionUtils.isNotEmpty(oldQuestionList)) {
                oldSpuStr = JSON.toJSONString(oldQuestionList);
            }

            String spuStr = "空";
            if (CollectionUtils.isNotEmpty(questionList)) {
                spuStr = JSON.toJSONString(questionList);
            }
            content.append("\n").append("问题列表").append("由").append(oldSpuStr).append("修改为").append(spuStr);
        }


        if (writeLog) {
            //发布区域
            if (!StringUtils.equals(old.getName(), template.getName())) {
                content.insert(0, new StringBuilder().append("\n").append("模板名称").append("由").append(old.getName())
                        .append("修改为").append(template.getName()));
            } else {
                content.insert(0, new StringBuilder().append("\n").append("模板名称").append(template.getName()));
            }

            content.insert(0, "【编辑】");
        }
        return content.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void deleteTemplate(InfoOfflineParam param, LoginIfo4Redis loginIfo4Redis) {
        if (CollectionUtils.isEmpty(param.getIds())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "所删除的需求模板ID不能为空");
        }
        List<MiniProgramSceneRequirementsTemplate> templates = miniProgramSceneRequirementsTemplateMapper.selectByExample(
                new MiniProgramSceneRequirementsTemplateExample().createCriteria()
                        .andIdIn(param.getIds()).example());

        Date now = new Date();
        templates.forEach(x -> {
            x.setUpdateTime(now);
            x.setDeleted(true);

            miniProgramSceneRequirementsTemplateMapper.updateByPrimaryKeySelective(x);
        });

        List<MiniProgramScene> scenes = miniProgramSceneMapper.selectByExample(new MiniProgramSceneExample().createCriteria()
                .andTemplateIdIn(templates.stream().map(MiniProgramSceneRequirementsTemplate::getId).collect(Collectors.toList())).example());
        scenes.forEach(x->{
            x.setUpdateTime(now);
            x.setTemplateId(null);
            miniProgramSceneMapper.updateByPrimaryKey(x);
        });
        TransactionUtil.afterCommit(() -> {
            templates.forEach(x -> {
                redisCacheUtil.delete(Constant.REDIS_KEY_MINI_TEMPLATE_ID + x.getId());
                redisCacheUtil.delete(Constant.REDIS_KEY_MINI_SCENE_REQUIREMENT_TEMPLATE + x.getId());
            });
            if (CollectionUtils.isNotEmpty(scenes)) {
                invalidScene2Redis(scenes.stream().map(MiniProgramScene::getId).collect(Collectors.toList()));
            }
        });

        String header = param.getIds().size() > 1 ? "【批量删除】" : "【删除】";

        StringBuilder sb = new StringBuilder();
        sb.append(header);
        templates.forEach(x -> sb.append("\n").append("模板名称").append(x.getName()));

        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.SCENE.code, sb.toString());
    }
}
