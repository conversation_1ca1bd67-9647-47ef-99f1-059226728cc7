package com.chinamobile.retail.service;

import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.param.OrderInfoMiniProgramParam;
import com.chinamobile.retail.pojo.vo.MiniProgramOrderDetailVO;
import com.chinamobile.retail.pojo.vo.OrderInfoMiniProgramVO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/4
 * @description 订单service接口类
 */
public interface OrderInfoService {

    /**
     * 分页获取极客小程序的订单信息
     * @param orderInfoMiniProgramParam
     * @param loginIfo4Redis
     * @return
     */
    PageData<OrderInfoMiniProgramVO> pageMiniProgramOrderInfo(OrderInfoMiniProgramParam orderInfoMiniProgramParam,
                                                              LoginIfo4Redis loginIfo4Redis);

    /**
     * 极客小程序获取订单详情
     * @param orderId
     * @return
     */
    MiniProgramOrderDetailVO getMiniProgramOrderDetail(String orderId,LoginIfo4Redis loginIfo4Redis);

    /**
     * 迁移今年的订单信息至小程序用户订单表
     */
    void migrateUserOrderSince2024();
}
