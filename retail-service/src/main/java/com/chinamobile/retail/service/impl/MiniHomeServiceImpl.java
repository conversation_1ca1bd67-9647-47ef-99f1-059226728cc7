package com.chinamobile.retail.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.MiniProgramOperateEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.util.TransactionUtil;
import com.chinamobile.retail.config.ProvinceCityConfig;
import com.chinamobile.retail.constant.*;
import com.chinamobile.retail.dao.*;
import com.chinamobile.retail.dao.ext.MiniProgramActivityMapperExt;
import com.chinamobile.retail.dao.ext.MiniProgramHomeMapperExt;
import com.chinamobile.retail.dao.ext.MiniProgramInfoMapperExt;
import com.chinamobile.retail.dao.ext.SpuOfferingInfoMapperExt;
import com.chinamobile.retail.exception.StatusContant;
import com.chinamobile.retail.pojo.entity.*;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.MiniProgramProductListVO;
import com.chinamobile.retail.pojo.vo.miniprogram.*;
import com.chinamobile.retail.service.IMiniHomeService;
import com.chinamobile.retail.util.PinyinUtils;
import com.chinamobile.retail.util.cache.RedisCacheUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.PARAM_ERROR;
import static com.chinamobile.retail.service.impl.ProductServiceImpl.REDIS_SPUQUANTITY_KEY;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 */
@Slf4j
@Service
public class MiniHomeServiceImpl implements IMiniHomeService {
    private static final int TARGET_MASK_MANAGER = 0x01;
    private static final int TARGET_MASK_DISTRIBUTOR = 0x02;
    private static final int TARGET_MASK_CHANNEL = 0x04;
    @Resource
    private RedisUtil redisUtil;

    @Resource
    private LogService logService;

    @Resource
    private MiniProgramHomeMapperExt miniProgramHomeMapperExt;

    @Resource
    private MiniProgramHomeMapper miniProgramHomeMapper;

    @Resource
    private MiniProgramHomeBannerMapper miniProgramHomeBannerMapper;

    @Resource
    private MiniProgramHomeAdMapper miniProgramHomeAdMapper;


    @Resource
    private MiniProgramHomeSpuMapper miniProgramHomeSpuMapper;

    @Resource
    private MiniProgramHomeInfoMapper miniProgramHomeInfoMapper;

    @Resource
    private UserMiniProgramMapper userMiniProgramMapper;

    @Resource
    private SpuOfferingInfoMapperExt spuOfferingInfoMapperExt;

    @Resource
    private SpuOfferingInfoMapper spuOfferingInfoMapper;

    @Resource
    private ProvinceCityConfig provinceCityConfig;

    @Resource
    private MiniProgramInfoMapper miniProgramInfoMapper;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RedisCacheUtil redisCacheUtil;

    @Resource
    private MiniProgramInfoMapperExt miniProgramInfoMapperExt;

    private ExecutorService executorService = new ThreadPoolExecutor(50, 50, 1L, TimeUnit.MINUTES, new LinkedBlockingDeque<>(10000));
    @Autowired
    private MiniProgramActivityMapperExt miniProgramActivityMapperExt;

    @Override
    @DS("query")
    public HomeVO getHomeDetail(String homeId, LoginIfo4Redis loginIfo4Redis) {
        MiniProgramHome home = miniProgramHomeMapper.selectByPrimaryKey(homeId);
        HomeVO vo = redisCacheUtil.loadAndCache(Constant.REDIS_KEY_MINI_HOME_ID + homeId,
                RedisLockConstant.LOCK_HOME_ID + homeId, () -> getHomeDetailInner(homeId));
        if (ObjectUtils.isEmpty(vo) || home.getIsDelete() == 1) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_HOME_NOT_EXIST);
        }

        String content = "【查看配置详情】\n" + "发布区域" + provinceCityConfig.getProvinceCodeNameMap().get(home.getProvinceCode());
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.HOME.code, content);
        return vo;
    }

    private HomeVO getHomeDetailInner(String homeId) {
        HomeVO vo = new HomeVO();
        MiniProgramHome home = miniProgramHomeMapper.selectByPrimaryKey(homeId);
        BeanUtils.copyProperties(home, vo);
        vo.setCreateUserName(miniProgramHomeMapperExt.getUserName(home.getCreatorId()));
        vo.setProvinceName(provinceCityConfig.getProvinceCodeNameMap().get(vo.getProvinceCode()));
        vo.setBanners(miniProgramHomeBannerMapper.selectByExample(new MiniProgramHomeBannerExample().createCriteria()
                .andHomeIdEqualTo(homeId).example()));
        vo.setAds(miniProgramHomeAdMapper.selectByExample(new MiniProgramHomeAdExample().createCriteria()
                .andHomeIdEqualTo(homeId).example()));
        List<MiniProgramProductListVO> homeProduct = miniProgramHomeMapperExt.getHomeProduct(homeId);
        for (MiniProgramProductListVO productListVO : homeProduct) {
            if(StringUtils.isNotEmpty(productListVO.getSubSaleLabel())){
                productListVO.setSubLabelList(Arrays.asList(productListVO.getSubSaleLabel().split(",")));
            }
            if(StringUtils.isNotEmpty(productListVO.getMainSaleLabel())){
                productListVO.setMainLabelList(Arrays.asList(productListVO.getMainSaleLabel().split(",")));
            }
        }
        vo.setProducts(homeProduct);
        vo.setVideos(miniProgramHomeMapperExt.getHomeInfo(homeId, InfoContentTypeEnum.VIDEO.getType()));
        vo.setInfos(miniProgramHomeMapperExt.getHomeInfo(homeId, InfoContentTypeEnum.IMAGE_TEXT.getType()));
        return vo;
    }

    @Override
    @DS("query")
    public HomeVO getHomeMini(String userId, String provinceCode) {
        if (StringUtils.isNotBlank(userId)) {
            UserMiniProgram userMiniProgram = redisCacheUtil.loadAndCache(Constant.REDIS_KEY_MINI_USER + userId,
                    RedisLockConstant.LOCK_MINI_USER + userId, 1, TimeUnit.DAYS,
                    () -> userMiniProgramMapper.selectByPrimaryKey(userId));
            if (userMiniProgram == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "用户不存在");
            }
            if (StringUtils.equals(MiniRoleEnum.IOT.getType(),userMiniProgram.getRoleType())
                    && StringUtils.isBlank(provinceCode)) {
                provinceCode = "000";
            } else {
                provinceCode = userMiniProgram.getBeId();
            }

        } else if (StringUtils.isBlank(provinceCode)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "缺少省编码");
        }
        final String beId = provinceCode;
        String homeId = redisCacheUtil.loadAndCache(Constant.REDIS_KEY_MINI_HOME_REGION + beId,
                RedisLockConstant.LOCK_HOME_REGION + beId,
                () -> {
                    List<MiniProgramHome> homes = miniProgramHomeMapper.selectByExample(new MiniProgramHomeExample().createCriteria()
                            .andProvinceCodeEqualTo(beId).andStatusEqualTo(InfoStatusEnum.PUBLISHED.getStatus()).example());
                    return CollectionUtils.isNotEmpty(homes) ? homes.get(0).getId() : null;
                });
        if (StringUtils.isEmpty(homeId)) {
            return null;
        }

        HomeVO home = redisCacheUtil.loadAndCache(Constant.REDIS_KEY_MINI_HOME_ID + homeId,
                RedisLockConstant.LOCK_HOME_ID + homeId,
                () -> getHomeDetailInner(homeId));
        if (home == null || !Objects.equals(home.getStatus(), InfoStatusEnum.PUBLISHED.getStatus())) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "首页不存在");
        }
        return home;
    }

    @Override
    @DS("query")
    public PageData<HomeVO> pageHomeList(PageHomeListParam param, LoginIfo4Redis loginIfo4Redis) {
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        if (pageNum < 0 || pageSize < 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "页码和每页数量必须大于0");
        }
        PageData<HomeVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        Long total = miniProgramHomeMapperExt.countHomeList(param);
        pageData.setCount(total != null ? total : 0);
        if (pageData.getCount() > 0) {
            List<HomeVO> list = miniProgramHomeMapperExt.pageHomeList(param);
            pageData.setData(list);
        }
        return pageData;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void create(HomeParam param, String userId) {
        List<MiniProgramHome> miniProgramHomes = miniProgramHomeMapper.selectByExample(new MiniProgramHomeExample().createCriteria()
                .andProvinceCodeEqualTo(param.getProvinceCode()).andStatusNotEqualTo(InfoStatusEnum.OFFLINE.getStatus()).andIsDeleteEqualTo(0).example());

        if (CollectionUtils.isNotEmpty(miniProgramHomes)) {
            throw new BusinessException(PARAM_ERROR, "当前区域已有首页，不能再配置");
        }

        Date now = new Date();
        MiniProgramHome home = new MiniProgramHome();
        BeanUtils.copyProperties(param, home);
        home.setId(BaseServiceUtils.getId());
        home.setCreatorId(userId);
        home.setCreateTime(now);
        home.setUpdateTime(now);
        home.setIsDelete(0);
        home.setStatus(InfoStatusEnum.DRAFT.getStatus());
        home.setAuditStatus(InfoAuditStatusEnum.DRAFT.getStatus());

        miniProgramHomeMapper.insertSelective(home);

        param.getBanners().forEach(x -> {
            x.setId(BaseServiceUtils.getId());
            x.setHomeId(home.getId());
        });

        miniProgramHomeBannerMapper.batchInsert(param.getBanners());

        param.getAds().forEach(x -> {
            x.setId(BaseServiceUtils.getId());
            x.setHomeId(home.getId());
        });
        miniProgramHomeAdMapper.batchInsert(param.getAds());

        List<String> spuCodes = param.getSpuCodes();
        if (CollectionUtils.isNotEmpty(spuCodes)) {
            List<MiniProgramHomeSpu> spus = new ArrayList<>();
            for (int i = 0; i < spuCodes.size(); i++) {
                MiniProgramHomeSpu spu = new MiniProgramHomeSpu();
                spu.setId(BaseServiceUtils.getId());
                spu.setHomeId(home.getId());
                spu.setSpuCode(spuCodes.get(i));
                spu.setSort(i);
                spus.add(spu);
            }
            miniProgramHomeSpuMapper.batchInsert(spus);
        }

        miniProgramHomeInfoMapper.batchInsert(param.getVideos().stream().map(x -> {
            MiniProgramHomeInfo info = new MiniProgramHomeInfo();
            info.setId(BaseServiceUtils.getId());
            info.setHomeId(home.getId());
            info.setInfoId(x.getId());
            info.setContentType(x.getContentType());
            return info;
        }).collect(Collectors.toList()));

        miniProgramHomeInfoMapper.batchInsert(param.getInfos().stream().map(x -> {
            MiniProgramHomeInfo info = new MiniProgramHomeInfo();
            info.setId(BaseServiceUtils.getId());
            info.setHomeId(home.getId());
            info.setInfoId(x.getId());
            info.setContentType(x.getContentType());
            return info;
        }).collect(Collectors.toList()));

        TransactionUtil.afterCommit(() -> invalidHome2Redis(home.getId()));

        String content = "【新增】\n" + "发布区域" + provinceCityConfig.getProvinceCodeNameMap().get(home.getProvinceCode());
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.HOME.code, content);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void edit(HomeParam param, String userId) {
        if (StringUtils.isBlank(param.getId())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "首页ID为空");
        }
        Date now = new Date();
        MiniProgramHome home = miniProgramHomeMapper.selectByPrimaryKey(param.getId());
        if (ObjectUtils.isEmpty(home) || home.getIsDelete() == 1) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_HOME_NOT_EXIST);
        }

        if (
                !InfoStatusEnum.DRAFT.getStatus().equals(home.getStatus()) &&
                        !InfoStatusEnum.REJECTED.getStatus().equals(home.getStatus())
                        && !InfoStatusEnum.OFFLINE.getStatus().equals(home.getStatus())
        ) {
            // 只有已上传,已驳回,已下线状态的素材才可以编辑
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_CANNOT_EDIT);
        }

        if (!StringUtils.equals(param.getProvinceCode(), home.getProvinceCode())) {
            if (CollectionUtils.isNotEmpty(miniProgramHomeMapper.selectByExample(new MiniProgramHomeExample().createCriteria()
                    .andProvinceCodeEqualTo(param.getProvinceCode()).andStatusNotEqualTo(InfoStatusEnum.OFFLINE.getStatus()).example()))) {
                throw new BusinessException(PARAM_ERROR, "当前区域已有首页，不能再配置");
            }
        }

        MiniProgramHome old = new MiniProgramHome();
        BeanUtils.copyProperties(home, old);
        BeanUtils.copyProperties(param, home);
        home.setUpdateTime(now);
        // 充值素材状态和审核状态
        home.setStatus(InfoStatusEnum.DRAFT.getStatus());
        home.setAuditStatus(InfoAuditStatusEnum.DRAFT.getStatus());
        miniProgramHomeMapper.updateByPrimaryKey(home);

        List<MiniProgramHomeBanner> oldBanners = miniProgramHomeBannerMapper.selectByExample(new MiniProgramHomeBannerExample().createCriteria()
                .andHomeIdEqualTo(home.getId()).example());
        List<MiniProgramHomeAd> oldAds = miniProgramHomeAdMapper.selectByExample(new MiniProgramHomeAdExample().createCriteria()
                .andHomeIdEqualTo(home.getId()).example());
        List<MiniProgramHomeSpu> oldSpuCodes = miniProgramHomeSpuMapper.selectByExample(new MiniProgramHomeSpuExample().createCriteria()
                .andHomeIdEqualTo(home.getId()).example());
        List<MiniProgramHomeInfo> oldVideos = miniProgramHomeInfoMapper.selectByExample(new MiniProgramHomeInfoExample().createCriteria()
                .andHomeIdEqualTo(home.getId()).andContentTypeEqualTo(InfoContentTypeEnum.VIDEO.getType()).example());
        List<MiniProgramHomeInfo> oldInfos = miniProgramHomeInfoMapper.selectByExample(new MiniProgramHomeInfoExample().createCriteria()
                .andHomeIdEqualTo(home.getId()).andContentTypeEqualTo(InfoContentTypeEnum.IMAGE_TEXT.getType()).example());


        miniProgramHomeBannerMapper.deleteByExample(new MiniProgramHomeBannerExample().createCriteria()
                .andHomeIdEqualTo(home.getId()).example());
        miniProgramHomeAdMapper.deleteByExample(new MiniProgramHomeAdExample().createCriteria()
                .andHomeIdEqualTo(home.getId()).example());
        miniProgramHomeSpuMapper.deleteByExample(new MiniProgramHomeSpuExample().createCriteria()
                .andHomeIdEqualTo(home.getId()).example());
        miniProgramHomeInfoMapper.deleteByExample(new MiniProgramHomeInfoExample().createCriteria()
                .andHomeIdEqualTo(home.getId()).example());

        param.getBanners().forEach(x -> {
            x.setId(BaseServiceUtils.getId());
            x.setHomeId(home.getId());
        });
        miniProgramHomeBannerMapper.batchInsert(param.getBanners());

        param.getAds().forEach(x -> {
            x.setId(BaseServiceUtils.getId());
            x.setHomeId(home.getId());
        });
        miniProgramHomeAdMapper.batchInsert(param.getAds());

        List<String> spuCodes = param.getSpuCodes();
        List<MiniProgramHomeSpu> spus = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(spuCodes)) {
            for (int i = 0; i < spuCodes.size(); i++) {
                MiniProgramHomeSpu spu = new MiniProgramHomeSpu();
                spu.setId(BaseServiceUtils.getId());
                spu.setHomeId(home.getId());
                spu.setSpuCode(spuCodes.get(i));
                spu.setSort(i);
                spus.add(spu);
            }
            miniProgramHomeSpuMapper.batchInsert(spus);
        }

        List<MiniProgramHomeInfo> videos = param.getVideos().stream().map(x -> {
            MiniProgramHomeInfo info = new MiniProgramHomeInfo();
            info.setId(BaseServiceUtils.getId());
            info.setHomeId(home.getId());
            info.setInfoId(x.getId());
            info.setContentType(x.getContentType());
            return info;
        }).collect(Collectors.toList());
        miniProgramHomeInfoMapper.batchInsert(videos);

        List<MiniProgramHomeInfo> infos = param.getInfos().stream().map(x -> {
            MiniProgramHomeInfo info = new MiniProgramHomeInfo();
            info.setId(BaseServiceUtils.getId());
            info.setHomeId(home.getId());
            info.setInfoId(x.getId());
            info.setContentType(x.getContentType());
            return info;
        }).collect(Collectors.toList());
        miniProgramHomeInfoMapper.batchInsert(infos);

        TransactionUtil.afterCommit(() -> invalidHome2Redis(home.getId()));
        String log = getEditContent(old, home, oldBanners, oldAds, oldSpuCodes, oldVideos, oldInfos, param.getBanners(),
                param.getAds(), spus, videos, infos);
        if (ObjectUtils.isNotEmpty(log)) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.HOME.code, log);
        }
    }

    private String getEditContent(MiniProgramHome old, MiniProgramHome home, List<MiniProgramHomeBanner> oldBanners,
                                  List<MiniProgramHomeAd> oldAds, List<MiniProgramHomeSpu> oldSpuCodes,
                                  List<MiniProgramHomeInfo> oldVideos, List<MiniProgramHomeInfo> oldInfos,
                                  List<MiniProgramHomeBanner> banners, List<MiniProgramHomeAd> ads,
                                  List<MiniProgramHomeSpu> spuCodes, List<MiniProgramHomeInfo> videos,
                                  List<MiniProgramHomeInfo> infos) {
        boolean writeLog = false;
        StringBuilder content = new StringBuilder();

        //banner
        StringBuilder oldBannerStr = new StringBuilder();
        StringBuilder newBannerStr = new StringBuilder();

        for (MiniProgramHomeBanner banner : oldBanners) {
            if (Objects.equals(banner.getType(), HomeBannerTypeEnum.ACTIVITY.getNum())) {
                oldBannerStr.append(HomeBannerTypeEnum.ACTIVITY.getName()).append(banner.getImgUrl()).append(",");
            }
            if (Objects.equals(banner.getType(), HomeBannerTypeEnum.INFO.getNum())) {
                oldBannerStr.append(HomeBannerTypeEnum.INFO.getName()).append(banner.getImgUrl()).append(",");
            }
            if (Objects.equals(banner.getType(), HomeBannerTypeEnum.WEB.getNum())) {
                oldBannerStr.append(HomeBannerTypeEnum.WEB.getName()).append(banner.getImgUrl()).append(",");
            }
        }
        //去除oldBannerStr最后一个","号
        if (oldBannerStr.length() > 0) {
            oldBannerStr.deleteCharAt(oldBannerStr.length() - 1);
        }
        for (MiniProgramHomeBanner banner : banners) {
            if (Objects.equals(banner.getType(), HomeBannerTypeEnum.ACTIVITY.getNum())) {
                oldBannerStr.append(HomeBannerTypeEnum.ACTIVITY.getName()).append(banner.getImgUrl()).append(",");
            }
            if (Objects.equals(banner.getType(), HomeBannerTypeEnum.INFO.getNum())) {
                oldBannerStr.append(HomeBannerTypeEnum.INFO.getName()).append(banner.getImgUrl()).append(",");
            }
            if (Objects.equals(banner.getType(), HomeBannerTypeEnum.WEB.getNum())) {
                oldBannerStr.append(HomeBannerTypeEnum.WEB.getName()).append(banner.getImgUrl()).append(",");
            }
        }
        //去除newBannerStr最后一个","号
        if (newBannerStr.length() > 0) {
            newBannerStr.deleteCharAt(newBannerStr.length() - 1);
        }
        if (!oldBannerStr.toString().equals(newBannerStr.toString())) {
            writeLog = true;
            content.append("\n").append("banner").append("由").append(oldBannerStr).append("修改为").append(newBannerStr);
        }


        //广告
        List<String> oldAdIds = oldAds.stream().map(MiniProgramHomeAd::getActivityId).collect(Collectors.toList());
        List<String> newAdIds = ads.stream().map(MiniProgramHomeAd::getActivityId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(CollectionUtils.subtract(oldAdIds, newAdIds))
                || CollectionUtils.isNotEmpty(CollectionUtils.subtract(newAdIds, oldAdIds))) {
            writeLog = true;
            String oldAdStr = "空";
            if (CollectionUtils.isNotEmpty(oldAdIds)) {
                oldAdStr = JSON.toJSONString(oldAds.stream().map(MiniProgramHomeAd::getActivityName).collect(Collectors.toList()));
            }

            String adStr = "空";
            if (CollectionUtils.isNotEmpty(newAdIds)) {
                adStr = JSON.toJSONString(ads.stream().map(MiniProgramHomeAd::getActivityName).collect(Collectors.toList()));
            }
            content.append("\n").append("广告").append("由").append(oldAdStr).append("修改为").append(adStr);
        }

        //热门产品
        List<String> oldSpuCodesList = oldSpuCodes.stream().map(MiniProgramHomeSpu::getSpuCode).collect(Collectors.toList());
        List<String> spuCodesList = spuCodes.stream().map(MiniProgramHomeSpu::getSpuCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(CollectionUtils.subtract(oldSpuCodesList, spuCodesList))
                || CollectionUtils.isNotEmpty(CollectionUtils.subtract(spuCodesList, oldSpuCodesList))) {
            writeLog = true;
            String oldSpuStr = "空";
            if (CollectionUtils.isNotEmpty(oldSpuCodes)) {
                oldSpuStr = JSON.toJSONString(spuOfferingInfoMapper.selectByExample(new SpuOfferingInfoExample().createCriteria()
                        .andOfferingCodeIn(oldSpuCodesList).example()).stream().map(SpuOfferingInfo::getOfferingName).collect(Collectors.toList()));
            }

            String spuStr = "空";
            if (CollectionUtils.isNotEmpty(spuCodes)) {
                spuStr = JSON.toJSONString(spuOfferingInfoMapper.selectByExample(new SpuOfferingInfoExample().createCriteria()
                        .andOfferingCodeIn(spuCodesList).example()).stream().map(SpuOfferingInfo::getOfferingName).collect(Collectors.toList()));
            }
            content.append("\n").append("热门产品").append("由").append(oldSpuStr).append("修改为").append(spuStr);
        }

        //热门视频
        List<String> oldVideoIdList = oldVideos.stream().map(MiniProgramHomeInfo::getInfoId).collect(Collectors.toList());
        List<String> videoIdList = videos.stream().map(MiniProgramHomeInfo::getInfoId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(CollectionUtils.subtract(oldVideoIdList, videoIdList))
                || CollectionUtils.isNotEmpty(CollectionUtils.subtract(videoIdList, oldVideoIdList))) {
            writeLog = true;
            String oldVideoStr = "空";
            if (CollectionUtils.isNotEmpty(oldVideoIdList)) {
                oldVideoStr = JSON.toJSONString(miniProgramInfoMapper.selectByExample(new MiniProgramInfoExample().createCriteria()
                        .andIdIn(oldVideoIdList).example()).stream().map(MiniProgramInfo::getName).collect(Collectors.toList()));
            }

            String videoStr = "空";
            if (CollectionUtils.isNotEmpty(videoIdList)) {
                videoStr = JSON.toJSONString(miniProgramInfoMapper.selectByExample(new MiniProgramInfoExample().createCriteria()
                        .andIdIn(videoIdList).example()).stream().map(MiniProgramInfo::getName).collect(Collectors.toList()));
            }
            content.append("\n").append("热门视频").append("由").append(oldVideoStr).append("修改为").append(videoStr);
        }

        //热门资讯
        List<String> oldInfoIdList = oldInfos.stream().map(MiniProgramHomeInfo::getInfoId).collect(Collectors.toList());
        List<String> infoIdList = infos.stream().map(MiniProgramHomeInfo::getInfoId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(CollectionUtils.subtract(oldInfoIdList, infoIdList))
                || CollectionUtils.isNotEmpty(CollectionUtils.subtract(infoIdList, oldInfoIdList))) {
            writeLog = true;
            String oldInfoStr = "空";
            if (CollectionUtils.isNotEmpty(oldInfoIdList)) {
                oldInfoStr = JSON.toJSONString(miniProgramInfoMapper.selectByExample(new MiniProgramInfoExample().createCriteria()
                        .andIdIn(oldInfoIdList).example()).stream().map(MiniProgramInfo::getName).collect(Collectors.toList()));
            }

            String infoStr = "空";
            if (CollectionUtils.isNotEmpty(infoIdList)) {
                infoStr = JSON.toJSONString(miniProgramInfoMapper.selectByExample(new MiniProgramInfoExample().createCriteria()
                        .andIdIn(infoIdList).example()).stream().map(MiniProgramInfo::getName).collect(Collectors.toList()));
            }
            content.append("\n").append("热门资讯").append("由").append(oldInfoStr).append("修改为").append(infoStr);
        }


        if (writeLog) {

            //发布区域
            if (!StringUtils.equals(old.getProvinceCode(), home.getProvinceCode())) {
                content.insert(0, new StringBuilder().append("\n").append("发布区域").append("由").append(provinceCityConfig.getProvinceCodeNameMap().get(old.getProvinceCode()))
                        .append("修改为").append(provinceCityConfig.getProvinceCodeNameMap().get(home.getProvinceCode())));
            } else {
                content.insert(0, new StringBuilder().append("\n").append("发布区域").append(provinceCityConfig.getProvinceCodeNameMap().get(home.getProvinceCode())));
            }

            content.insert(0, "【编辑】");
        }
        return content.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void audit(InfoAuditParam param, LoginIfo4Redis loginIfo4Redis) {
        List<MiniProgramHome> homes = miniProgramHomeMapper.selectByExample(new MiniProgramHomeExample().createCriteria()
                .andIdIn(param.getIds()).andIsDeleteEqualTo(0).example());
        if (homes.size() != param.getIds().size()) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "所审核的首页"
                    + JSON.toJSONString(CollectionUtils.subtract(param.getIds(),
                    homes.stream().map(MiniProgramHome::getId).collect(Collectors.toList())))
                    + "不存在");
        }
        List<MiniProgramHome> notInProcess = homes.stream().filter(x -> !x.getAuditStatus().equals(InfoAuditStatusEnum.AUDITING.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notInProcess)) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_AUDIT_STATUS_WRONG, "所审核的首页"
                    + JSON.toJSONString(notInProcess.stream().map(MiniProgramHome::getId).collect(Collectors.toList()))
                    + "不可以被审核");
        }

        Date now = new Date();
        homes.forEach(x -> {
            x.setUpdateTime(now);
            x.setAuditStatus(param.getApprove() ? InfoAuditStatusEnum.PASSED.getStatus() : InfoAuditStatusEnum.DENIED.getStatus());
            x.setStatus(param.getApprove() ? InfoStatusEnum.PUBLISHED.getStatus() : InfoStatusEnum.REJECTED.getStatus());

            miniProgramHomeMapper.updateByPrimaryKeySelective(x);
        });
        TransactionUtil.afterCommit(() -> invalidHome2Redis(homes.stream().map(MiniProgramHome::getId).collect(Collectors.toList())));
        TransactionUtil.afterCommit(() -> invalidHomeRegion2Redis(homes));
        String header = null;
        if (param.getApprove()) {
            header = param.getIds().size() > 1 ? "【批量同意】" : "【同意】";
        } else {
            header = param.getIds().size() > 1 ? "【批量驳回】" : "【驳回】";
        }
        StringBuilder sb = new StringBuilder();
        sb.append(header);
        homes.forEach(x -> sb.append("\n").append("发布区域 ").append(provinceCityConfig.getProvinceCodeNameMap().get(x.getProvinceCode())));

        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.HOME.code, sb.toString());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void offline(InfoOfflineParam param, LoginIfo4Redis loginIfo4Redis) {
        List<MiniProgramHome> homes = miniProgramHomeMapper.selectByExample(new MiniProgramHomeExample().createCriteria()
                .andIdIn(param.getIds()).andIsDeleteEqualTo(0).example());
        if (homes.size() != param.getIds().size()) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "所下线的首页"
                    + JSON.toJSONString(CollectionUtils.subtract(param.getIds(),
                    homes.stream().map(MiniProgramHome::getId).collect(Collectors.toList())))
                    + "不存在");
        }
        List<MiniProgramHome> notPublished = homes.stream().filter(x -> !x.getStatus().equals(InfoStatusEnum.PUBLISHED.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notPublished)) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_AUDIT_STATUS_WRONG, "所下线的首页"
                    + JSON.toJSONString(notPublished.stream().map(MiniProgramHome::getId).collect(Collectors.toList()))
                    + "不可以被下线");
        }

        Date now = new Date();
        homes.forEach(x -> {
            x.setUpdateTime(now);
            x.setStatus(InfoStatusEnum.OFFLINE.getStatus());
            miniProgramHomeMapper.updateByPrimaryKeySelective(x);
        });
        TransactionUtil.afterCommit(() -> {
            invalidHome2Redis(param.getIds());
            invalidHomeRegion2Redis(homes);
        });

        StringBuilder sb = new StringBuilder("【下线】");
        homes.forEach(x -> sb.append("\n").append("发布区域 ").append(provinceCityConfig.getProvinceCodeNameMap().get(x.getProvinceCode())));
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.HOME.code, sb.toString());
    }

    @Transactional
    @Override
    @DS("save")
    public void publish(HomePublishParam param, LoginIfo4Redis loginIfo4Redis) {
        List<MiniProgramHome> homes = miniProgramHomeMapper.selectByExample(new MiniProgramHomeExample().createCriteria()
                .andIdIn(param.getIds()).andIsDeleteEqualTo(0).example());
        if (homes.size() != param.getIds().size()) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "所发布的首页"
                    + JSON.toJSONString(CollectionUtils.subtract(param.getIds(),
                    homes.stream().map(MiniProgramHome::getId).collect(Collectors.toList())))
                    + "不存在");
        }
        List<MiniProgramHome> notDraft = homes.stream().filter(x -> !InfoAuditStatusEnum.DRAFT.getStatus().equals(x.getAuditStatus())
                && !InfoAuditStatusEnum.DENIED.getStatus().equals(x.getAuditStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notDraft)) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_INFO_AUDIT_STATUS_WRONG, "所发布的首页"
                    + JSON.toJSONString(notDraft.stream().map(MiniProgramHome::getId).collect(Collectors.toList()))
                    + "不可以被发布");
        }
        Date now = new Date();
        homes.forEach(x -> {

            x.setUpdateTime(now);
            x.setStatus(InfoStatusEnum.AUDITING.getStatus());
            x.setAuditStatus(InfoAuditStatusEnum.AUDITING.getStatus());
            miniProgramHomeMapper.updateByPrimaryKeySelective(x);
        });
        TransactionUtil.afterCommit(() -> invalidHome2Redis(homes.stream().map(MiniProgramHome::getId).collect(Collectors.toList())));
//        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.INFO.code, "【发布素材】" + "\n" + "素材主题 " + miniProgramInfo.getName());
    }

    /**
     * 添加活动进缓存
     */
    private void invalidHome2Redis(List<String> homeIds) {
        //异步加载首页进缓存
        executorService.execute(() -> homeIds.forEach(homeId -> redisCacheUtil.delete(Constant.REDIS_KEY_MINI_HOME_ID + homeId)));
    }

    private void invalidHome2Redis(String homeId) {
        //异步加载首页进缓存
        List<String> homeIds = new ArrayList<>();
        homeIds.add(homeId);
        invalidHome2Redis(homeIds);
    }

    private void invalidHomeRegion2Redis(String homeId, String provinceCode) {
        //异步加载首页区域进缓存
        executorService.execute(() -> {
            String key = Constant.REDIS_KEY_MINI_HOME_REGION + provinceCode;
            String lock = RedisLockConstant.LOCK_HOME_REGION + provinceCode;
            redisUtil.smartLock(lock, () -> {
                redisTemplate.opsForValue().set(key, homeId);
                return null;
            });
        });
    }

    private void invalidHomeRegion2Redis(List<MiniProgramHome> homes) {
        //异步加载首页区域进缓存
        executorService.execute(() ->
                homes.forEach(x -> redisTemplate.delete(Constant.REDIS_KEY_MINI_HOME_REGION + x.getProvinceCode())));
    }

    /**
     * 每天凌晨0点重建首页缓存(去掉)
     */
    @Override
    @DS("query")
//    @Scheduled(cron = "0 0 0 * * ? ")
    public void loadHome2Redis() {
        List<MiniProgramHome> homes = miniProgramHomeMapper.selectByExample(new MiniProgramHomeExample());
        List<HomeVO> homeVOS = homes.stream().map(x -> getHomeDetailInner(x.getId())).collect(Collectors.toList());
        List<HomeVO> publishedHomes = homeVOS.stream().filter(x -> InfoStatusEnum.PUBLISHED.getStatus().equals(x.getStatus())).collect(Collectors.toList());
        Map<String, HomeVO> homeRedisMap = new LinkedHashMap<>();
        Map<String, String> regionRedisMap = new LinkedHashMap<>();
        homeVOS.forEach(x ->
                homeRedisMap.put(Constant.REDIS_KEY_MINI_HOME_ID + x.getId(), x)
        );
        publishedHomes.forEach(x -> regionRedisMap.put(Constant.REDIS_KEY_MINI_HOME_REGION + x.getProvinceCode(), x.getId()));

        if (!homeRedisMap.isEmpty()) {
            redisTemplate.opsForValue().multiSet(homeRedisMap);
        }
        if (!regionRedisMap.isEmpty()) {
            redisTemplate.opsForValue().multiSet(regionRedisMap);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        MiniProgramHome miniProgramHome = miniProgramHomeMapper.selectByPrimaryKey(id);
        if (miniProgramHome == null) {
            throw new BusinessException(PARAM_ERROR, "首页不存在");
        }
        if (!(
                miniProgramHome.getStatus().equals(InfoStatusEnum.DRAFT.getStatus()) ||
                        miniProgramHome.getStatus().equals(InfoStatusEnum.OFFLINE.getStatus()) ||
                        miniProgramHome.getStatus().equals(InfoStatusEnum.REJECTED.getStatus())

        )) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_DEACTIVATE_STATUS_WRONG);
        }
        miniProgramHome.setIsDelete(1);
        miniProgramHome.setUpdateTime(new Date());
        miniProgramHomeMapper.updateByPrimaryKeySelective(miniProgramHome);
        List<MiniProgramHome> homes = new ArrayList<>();
        homes.add(miniProgramHome);

        invalidHomeRegion2Redis(homes);
        invalidHome2Redis(id);
        StringBuilder sb = new StringBuilder();
        sb.append("【删除首页】").append("/n").append("首页主题").append(provinceCityConfig.getProvinceCodeNameMap().get(miniProgramHome.getProvinceCode()));
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.HOME.code,
                sb.toString(), null, 0, LogResultEnum.LOG_SUCESS.code, null);
//        executorService.execute(() ->
//                homes.forEach(x -> redisTemplate.delete(Constant.REDIS_KEY_MINI_HOME_REGION + x.getProvinceCode())));
    }

    @Override
    @DS("save")
    @Transactional(rollbackFor = Exception.class)
    public void migrateSpu() {
        List<MiniProgramHome> homes = miniProgramHomeMapper.selectByExample(new MiniProgramHomeExample().createCriteria()
                .andIsDeleteEqualTo(0).example());
        Map<String,String> idProvinceCodeMap = homes.stream().collect(Collectors.toMap(MiniProgramHome::getId,MiniProgramHome::getProvinceCode));
        List<MiniProgramHomeSpu> spuList = miniProgramHomeSpuMapper.selectByExample(new MiniProgramHomeSpuExample().createCriteria()
                .andHomeIdIn(new ArrayList<>(idProvinceCodeMap.keySet())).example());
        spuList.forEach(x-> {
            String provinceCode = idProvinceCodeMap.get(x.getHomeId());
            ProductDetailParam param = new ProductDetailParam();
            param.setSpuCode(x.getSpuCode());
            param.setProvinceCode(provinceCode);
            if (CollectionUtils.isEmpty(spuOfferingInfoMapperExt.getMiniProgramSkuList(param))){
                miniProgramHomeSpuMapper.deleteByPrimaryKey(x.getId());
            }
        });



    }

    @Override
    public PageData<HomeSearchVO> search(HomeSearchParam param) {
        PageData<HomeSearchVO> pageData = new PageData<>();
        pageData.setPage(param.getPageNum());
        String keyWord = param.getKeyWord();
        Integer type = param.getType();
        String userId = param.getUserId();
        String provinceCode = param.getProvinceCode();
        if (StringUtils.isNotBlank(userId)) {
            UserMiniProgram userMiniProgram = redisCacheUtil.loadAndCache(
                    Constant.REDIS_KEY_MINI_USER + userId,
                    RedisLockConstant.LOCK_MINI_USER + userId,
                    1,
                    TimeUnit.DAYS,
                    () -> userMiniProgramMapper.selectByPrimaryKey(userId)
            );
            if (userMiniProgram == null) {
                throw new BusinessException(StatusContant.USER_NOT_FOUNT);
            }

            provinceCode = userMiniProgram.getBeId();
            param.setProvinceCode(userMiniProgram.getBeId());
            param.setCityCode(userMiniProgram.getLocation());
            param.setRoleType(userMiniProgram.getRoleType());

        } else if (StringUtils.isBlank(provinceCode)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "缺少省编码");
        }
        String pageKey = param.getKeyWord() +"-"+param.getType()+ "-" + param.getProvinceCode() + "-" + param.getCityCode()
                + "-" + param.getPageNum() + "-" + param.getPageSize() + "-" + param.getRoleType();
        if(type == 1){
            //搜索产品，按照发布范围
            PageProductParam pageProductParam = new PageProductParam();
            BeanUtils.copyProperties(param, pageProductParam);
            List<MiniProgramProductListVO> data = redisCacheUtil.loadAndCache(
                    Constant.REDIS_KEY_MINI_PRODUCT_LIST + pageKey,
                    RedisLockConstant.LOCK_MINI_PRODUCT_LIST + pageKey,
                    30,
                    TimeUnit.MINUTES,
                    () -> {
                        //需要根据销量排序，而销量存在redis中，所以需要先查出所有信息，否则无法根据销量排序后分页
                        List<MiniProgramProductListVO> productList = spuOfferingInfoMapperExt.pageMiniProgramProduct(pageProductParam);
                        pageData.setCount(productList.size());
                        if (org.springframework.util.CollectionUtils.isEmpty(productList)) {
                            return Collections.emptyList();
                        }
                        for (MiniProgramProductListVO miniProgramProductListVO : productList) {
                            String spuQuantity = (String) stringRedisTemplate.opsForHash().get(REDIS_SPUQUANTITY_KEY, miniProgramProductListVO.getSpuCode());
                            Long amount = Long.valueOf(spuQuantity == null ? "0" : spuQuantity);
                            miniProgramProductListVO.setAmount(amount);
                        }

                        int begin = (param.getPageNum() - 1) * param.getPageSize();
                        int end = param.getPageNum() * param.getPageSize();
                        begin = Math.max(begin, 0);
                        end = Math.min(productList.size(), end);
                        if (begin >= end) {
                            return Collections.emptyList();
                        }
                        productList.sort(Comparator.comparing(MiniProgramProductListVO::getAmount).reversed());
                        List<MiniProgramProductListVO> subList = new ArrayList<>(productList.subList(begin, end));
                        for (MiniProgramProductListVO miniProgramProductListVO : subList) {
                            if(StringUtils.isNotEmpty(miniProgramProductListVO.getSubSaleLabel())){
                                miniProgramProductListVO.setSubLabelList(Arrays.asList(miniProgramProductListVO.getSubSaleLabel().split(",")));
                            }
                            if(StringUtils.isNotEmpty(miniProgramProductListVO.getMainSaleLabel())){
                                miniProgramProductListVO.setMainLabelList(Arrays.asList(miniProgramProductListVO.getMainSaleLabel().split(",")));
                            }
                        }
                        return subList;
                    }
            );
            List<HomeSearchVO> voList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(data)){
                voList = data.stream().map(d -> {
                    HomeSearchVO homeSearchVO = new HomeSearchVO();
                    BeanUtils.copyProperties(d, homeSearchVO);
                    return homeSearchVO;
                }).collect(Collectors.toList());
            }
            pageData.setData(voList);
            return pageData;
        }else if(type == 2){
            //搜索活动(查询量不大，且更新很频繁，暂不走缓存)
            PageHelper.startPage(param.getPageNum(), param.getPageSize());
            List<HomeSearchVO> activityList = miniProgramActivityMapperExt.homeSearchMiniActivity(keyWord, provinceCode, param.getCityCode());
            if(CollectionUtils.isEmpty(activityList)){
                return pageData;
            }
            PageInfo<HomeSearchVO> pageInfo = new PageInfo<>(activityList);
            activityList.stream().forEach(x -> {
                List<String> userIds = redisCacheUtil.get(Constant.REDIS_KEY_MINI_ACTIVITY_USER + x.getActivityId());
                if (CollectionUtils.isEmpty(userIds) || !userIds.contains(userId)) {
                    x.setIsParticipate(false);
                } else {
                    x.setIsParticipate(true);
                }
            });
            pageData.setData(activityList);
            pageData.setCount(pageInfo.getTotal());
            return pageData;
        }else if(type ==3 || type == 4 || type == 5){
            //小程序前端只看发布的
            PageInfoListParam pageInfoListParam = new PageInfoListParam();
            BeanUtils.copyProperties(param, pageInfoListParam);
            pageInfoListParam.setStatus(InfoStatusEnum.PUBLISHED.getStatus());
            pageInfoListParam.setIsMini(1);
            if(type == 4){
                pageInfoListParam.setCategoryNum(2);
            }
            if(type == 3){
                pageInfoListParam.setCategoryNum(1);
            }
            if(type == 5){
                pageInfoListParam.setCategoryNum(4);
                pageInfoListParam.setKnowledgeType("3");
            }
            PageData<PageInfoVO> infoVOPageData = redisCacheUtil.loadAndCache(Constant.REDIS_KEY_MINI_INFO_LIST_PARAM+pageKey, RedisLockConstant.LOCK_INFO_LIST_PARAM + pageKey, 30, TimeUnit.MINUTES, () -> {
                PageData<PageInfoVO> infoPageData = new PageData<>();
                infoPageData.setPage(param.getPageNum());
                Long count = miniProgramInfoMapperExt.countInfoList(pageInfoListParam);
                if(count == 0){
                    return infoPageData;
                }
                List<PageInfoVO> list = miniProgramInfoMapperExt.pageInfoList(pageInfoListParam);
                infoPageData.setData(list);
                infoPageData.setCount(count);
                return infoPageData;

            });
            if(infoVOPageData.getCount() == 0){
                return pageData;
            }
            List<HomeSearchVO> data = infoVOPageData.getData().stream().map(d -> {
                HomeSearchVO homeSearchVO = new HomeSearchVO();
                homeSearchVO.setInfoId(d.getId());
                homeSearchVO.setInfoName(d.getName());
                homeSearchVO.setInfoContent(d.getContent());
                homeSearchVO.setInfoCreateTime(d.getCreateTime());
                homeSearchVO.setCategory(d.getCategory());
                homeSearchVO.setContentType(d.getContentType());
                homeSearchVO.setHeadImgUrl1(d.getHeadImgUrl1());
                homeSearchVO.setHeadImgUrl2(d.getHeadImgUrl2());
                return homeSearchVO;
            }).collect(Collectors.toList());
            pageData.setData(data);
            pageData.setCount(infoVOPageData.getCount());
            return pageData;
        }else {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "搜索类型错误");
        }
    }

    @Override
    @DS("query")
    public List<ActivityProvinceVO> getAllRegions() {
        return redisCacheUtil.loadAndCache(
                Constant.REDIS_KEY_HOME_PROVINCE_AND_CITY,
                RedisLockConstant.PRODUCT_MINI_HOME_PROVINCE_AND_CITY,
                () -> {
                    List<ActivityProvinceVO> allProvinces = miniProgramActivityMapperExt.getAllRegions();
                    List<ActivityProvinceVO> provinces = allProvinces.stream()
                            .peek(activityProvinceVO -> {
                                activityProvinceVO.setPinyin(PinyinUtils.getUpperCase(activityProvinceVO.getProvinceName(), true));
                                if ("ZHONGQING".equals(activityProvinceVO.getPinyin())) {
                                    activityProvinceVO.setPinyin("CHONGQING");
                                }
                            })
                            .filter(activityProvinceVO -> !"001".equals(activityProvinceVO.getProvinceCode()) && !"002".equals(activityProvinceVO.getProvinceCode()))
                            .sorted(Comparator.comparing(ActivityProvinceVO::getPinyin)).collect(Collectors.toList());
                    ActivityProvinceVO all = allProvinces.stream().filter(activityProvinceVO -> "000".equals(activityProvinceVO.getProvinceCode())).findFirst().orElse(null);
                    if (all != null) {
                        all.setProvinceName("全国");
                        provinces.add(0, all);
                    }
                    return provinces;
                }
        );
    }
}
