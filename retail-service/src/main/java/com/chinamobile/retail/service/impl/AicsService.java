package com.chinamobile.retail.service.impl;

import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import com.chinamobile.retail.constant.StatusConstant;
import com.chinamobile.retail.dao.ext.LogisticsInfoMapperExt;
import com.chinamobile.retail.exception.ServicePowerException;
import com.chinamobile.retail.pojo.vo.aics.LogisticsVO;
import com.chinamobile.retail.request.SyncCommonRequest;
import com.chinamobile.retail.service.IAicsService;
import com.chinamobile.retail.util.SignUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class AicsService implements IAicsService {

    @Resource
    private LogisticsInfoMapperExt logisticsInfoMapperExt;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;

    @Override
    public LogisticsVO getLogisticsInfo(SyncCommonRequest syncCommonRequest) {
        // 原子订单号
        String input = syncCommonRequest.getInput();
        String sign = syncCommonRequest.getSign();
        try {
            SignUtils.checkSign(input, sign);
        } catch (ServicePowerException e) {
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, e.getMessage());
        }
        LogisticsVO logisticsVO = logisticsInfoMapperExt.getLogisticsInfo(input);
        logisticsVO.setContactPhone(IOTEncodeUtils.decryptSM4(logisticsVO.getContactPhone(), iotSm4Key, iotSm4Iv));
        return logisticsVO;
    }
}
