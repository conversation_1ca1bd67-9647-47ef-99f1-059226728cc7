package com.chinamobile.retail.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseUtils;
import com.chinamobile.iot.sc.common.utils.RegexUtil;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.enums.log.AfterSaleServiceOperateEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.LoginOperateEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.util.GeneralUtils;
import com.chinamobile.retail.config.SmsConfig;
import com.chinamobile.retail.constant.StatusConstant;
import com.chinamobile.retail.dao.UserMiniProgramMapper;
import com.chinamobile.retail.dao.UserRetailMapper;
import com.chinamobile.retail.pojo.entity.UserMiniProgram;
import com.chinamobile.retail.pojo.entity.UserMiniProgramExample;
import com.chinamobile.retail.pojo.entity.UserRetail;
import com.chinamobile.retail.pojo.entity.UserRetailExample;
import com.chinamobile.retail.pojo.vo.RedisSmsValidCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;



/**
 * @version: V1.0
 * @author: hewenfeng
 * @description:
 * @data: 2022/9/13 15:09
 **/
@Service
@Slf4j
public class CaptchaServiceImpl {
    //验证码有效时间
    private int expireTimeInSeconds = 5 * 60;
    @Value("${sms.editCodeTempId:106036}")
    private String editCodeTempId;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private SmsConfig smsConfig;
    @Autowired
    private SmsFeignClient smsFeignClient;


    @Resource
    private UserRetailMapper userRetailMapper;

    @Resource
    private UserMiniProgramMapper userMiniProgramMapper;

    @Resource
    private LogService logService;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 验证
     * @param validCode
     * @param sessionId
     */
    public void valid(String validCode,String sessionId,String account){
        String captchaSessionIdKey = BaseUtils.getCaptchaSessionIdKey(sessionId);
        String code = (String)redisTemplate.opsForValue().get(captchaSessionIdKey);
        if(code == null){
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, BaseErrorConstant.VAILD_CODE_EXPIRED.getMessage(),account);

            throw new BusinessException(BaseErrorConstant.VAILD_CODE_EXPIRED);
        }
        //需要删除,防止一直使用该验证码
        redisTemplate.delete(captchaSessionIdKey);
        if(!validCode.equals(code)){
            //验证码错误
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, BaseErrorConstant.VAILD_CODE_VAILD_FAIL.getMessage(),account);

            throw new BusinessException(BaseErrorConstant.VAILD_CODE_VAILD_FAIL);
        }
    }




    /**
     * 获取分销中心登录时的短信验证码
     * @param phone
     * @param type:0 登录，1：注册
     * @return
     */
    public BaseAnswer<Void> getSmsValidCodeRetail(String phone,int type){
        log.debug("getSmsValidCodeRetail phone = {}, type = {}",phone, type);
        return redisUtil.smartLock("SC:SMS_RETAIL_LOCK:" + phone, () -> {
            //校验手机是否注册
            if (!RegexUtil.regexPhone(phone)) {
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code,StatusConstant.PHONE_ERROR.getMessage(),phone);

                throw new BusinessException(StatusConstant.PHONE_ERROR);
            }

            if(type==0){
                //手机号是否被注册
                List<UserRetail> users = userRetailMapper.selectByExample(new UserRetailExample().createCriteria().andPhoneEqualTo(phone).example());
                if (CollectionUtils.isEmpty(users)) {
                    logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code,StatusConstant.PHONE_NOT_REGISTER.getMessage(),phone);

                    throw new BusinessException(StatusConstant.PHONE_NOT_REGISTER);
                }
            }

            BaseAnswer<Void> answer  = new BaseAnswer<>();
            String redisKey = BaseUtils.getSMSValidKey(phone);
            //查看一分钟之内有没有发送短信
            String jsonStr = (String)redisTemplate.opsForValue().get(redisKey);
            if(StringUtils.isNotBlank(jsonStr)){
                RedisSmsValidCode redisSmsValidCode = JSONObject.parseObject(jsonStr, RedisSmsValidCode.class);
                if((System.currentTimeMillis() - redisSmsValidCode.getTime() < 60000)){
                    //超过1分钟
                    logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code,"验证码请求过于频繁，请稍后再试",phone);

                    throw new BusinessException(BaseErrorConstant.SYSTEM_BUSY, "验证码请求过于频繁，请稍后再试");
                }
            }
            Integer code = 0;
            if(type == 0){
                code = sendSmsValidCode(phone, smsConfig.getRetailCodeLoginTempId());
            }else if(type == 1){
                code = sendSmsValidCode(phone, smsConfig.getRetailCodeRegTempId());
            }
//        Integer code = sendSmsValidCode(phone, smsConfig.getRetailCodeTempId());
            log.debug("获取短信验证码： sms_code,phone:{},code:{}",phone,code);
            RedisSmsValidCode redisSmsValidCode = new RedisSmsValidCode(code,System.currentTimeMillis());
            log.debug("getSmsValidCodeRetail redisKey = {} redisSmsCodeString = {}",redisKey,JSONObject.toJSONString(redisSmsValidCode));
            redisTemplate.opsForValue().set(redisKey,JSONObject.toJSONString(redisSmsValidCode), expireTimeInSeconds,TimeUnit.SECONDS);
            log.debug("getSmsValidCodeRetail get redisContent = {}",(String)redisTemplate.opsForValue().get(redisKey));
            return answer;
        });
    }


    public Integer getSmsLoginMiniProgram(String phone) {
        log.debug("getSmsValidCodeRetail phone = {}",phone);
        return redisUtil.smartLock("SC:SMS_MINI_PROGRAM_LOCK:" + phone, () -> {
            // 校验手机号是否合法
            if (!RegexUtil.regexPhone(phone)) {
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code,StatusConstant.PHONE_ERROR.getMessage(),phone);
                throw new BusinessException(StatusConstant.PHONE_ERROR);
            }

            // 检查手机号是否存在
            List<UserMiniProgram> users = userMiniProgramMapper.selectByExample(new UserMiniProgramExample().createCriteria().andPhoneEqualTo(phone).example());
            if (CollectionUtils.isEmpty(users)) {
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code,StatusConstant.PHONE_NOT_REGISTER.getMessage(),phone);
                throw new BusinessException(StatusConstant.PHONE_NOT_REGISTER);
            }

            String redisKey = BaseUtils.getSMSValidKey(phone);
            //查看一分钟之内有没有发送短信
            String jsonStr = (String)redisTemplate.opsForValue().get(redisKey);
            if(StringUtils.isNotBlank(jsonStr)){
                RedisSmsValidCode redisSmsValidCode = JSONObject.parseObject(jsonStr, RedisSmsValidCode.class);
                if((System.currentTimeMillis() - redisSmsValidCode.getTime() < 60000)){
                    //超过1分钟
                    logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code,"验证码请求过于频繁，请稍后再试",phone);

                    throw new BusinessException(BaseErrorConstant.SYSTEM_BUSY, "验证码请求过于频繁，请稍后再试");
                }
            }
            Integer code = sendSmsValidCode(phone, smsConfig.getValidCodeTempId());
            log.debug("获取短信验证码： sms_code,phone:{},code:{}", phone,code);
            RedisSmsValidCode redisSmsValidCode = new RedisSmsValidCode(code,System.currentTimeMillis());
            log.debug("getSmsValidCodeRetail redisKey = {} redisSmsCodeString = {}",redisKey,JSONObject.toJSONString(redisSmsValidCode));
            redisTemplate.opsForValue().set(redisKey,JSONObject.toJSONString(redisSmsValidCode), expireTimeInSeconds,TimeUnit.SECONDS);
            log.debug("getSmsValidCodeRetail get redisContent = {}",(String)redisTemplate.opsForValue().get(redisKey));
            return code;
        });
    }


    /**
     * 验证短信验证码
     * @param phone
     * @param code
     */
    public void validSmsCode(String account,String phone,Integer code){
        String redisKey = BaseUtils.getSMSValidKey(phone);
        log.debug("validSmsCode phone = {}, code = {}",phone, code);
        String jsonStr = (String)redisTemplate.opsForValue().get(redisKey);
        log.debug("validSmsCode redis jsonStr = {}",jsonStr);
        String numErrorsKey = BaseUtils.getNumErrorsRetailKey(phone);
        Integer numErrors = (Integer)redisTemplate.opsForValue().get(numErrorsKey);
        if(StringUtils.isNotBlank(jsonStr)){
            RedisSmsValidCode redisSmsValidCode = JSONObject.parseObject(jsonStr, RedisSmsValidCode.class);
            log.debug("validSmsCode redis smsCode = {}",redisSmsValidCode.getCode());
            if(!code.equals(redisSmsValidCode.getCode())){
                long passTime = (System.currentTimeMillis() - redisSmsValidCode.getTime())/1000;
                if (numErrors !=null){
                    if (numErrors == 3){
                        redisTemplate.delete(redisKey);
                        redisTemplate.delete(numErrorsKey);
                        logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, "输入验证码错误3次，验证码失效请重新获取验证码",account);

                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "输入验证码错误3次，验证码失效请重新获取验证码");
                    }
                }
                //如果输入错误验证码，记录一次错误记录，错了3次就删除验证码至失效，需重新获取
                if (numErrors == null){
                    redisTemplate.opsForValue().set(numErrorsKey,1,expireTimeInSeconds-passTime,TimeUnit.SECONDS);
                }else if(numErrors ==1){
                    redisTemplate.opsForValue().set(numErrorsKey,2,expireTimeInSeconds-passTime,TimeUnit.SECONDS);
                }else if(numErrors ==2){
                    redisTemplate.opsForValue().set(numErrorsKey,3,expireTimeInSeconds-passTime,TimeUnit.SECONDS);
                }

                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, "验证码错误",account);

                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "验证码输入错误！请重新输入");
            }
        }else{
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, "验证码错误或已过期",account);

            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"验证码错误或已过期，请重新发送");
        }
    }

    /**
     * 验证修改短信验证码
     * @param phone
     * @param code
     */
    public void validEditCode(String phone,Integer code,String account){
        String redisKey = BaseUtils.getEditSMSKey(phone);
        String jsonStr = (String)redisTemplate.opsForValue().get(redisKey);
        if(StringUtils.isNotBlank(jsonStr)){
            RedisSmsValidCode redisSmsValidCode = JSONObject.parseObject(jsonStr, RedisSmsValidCode.class);
            if(!code.equals(redisSmsValidCode.getCode())){
                logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code, "验证码错误",account);

                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "验证码错误");
            }
        }else{
            logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.OS_LOGIN.code,"-", 0, LogResultEnum.LOG_FAIL.code,"验证码已过期",account);

            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"验证码已过期");
        }
    }
    /**
     * 删除保存在redis的短信验证码相关信息
     * @param phone
     */
    public void deleteSmsCodeInRedis(String phone){
        String redisKey = BaseUtils.getSMSValidKey(phone);
        String numErrorsKey = BaseUtils.getNumErrorsRetailKey(phone);
        Integer numErrors = (Integer)redisTemplate.opsForValue().get(numErrorsKey);
        redisTemplate.delete(redisKey);
        if (numErrors !=null){
            redisTemplate.delete(numErrorsKey);
        }
    }
    /**
     * 删除保存在redis的修改短信验证码相关信息
     * @param phone
     */
    public void deleteEditSmsCodeInRedis(String phone){
        String redisKey = BaseUtils.getEditSMSKey(phone);
        redisTemplate.delete(redisKey);
    }

    /**
     * 验证短信验证码
     * @param phone
     * @param code
     * @param type
     * @return
     */
    public BaseAnswer<String> validSmsCode(String phone,Integer code,String type){
        BaseAnswer<String> answer = new BaseAnswer<>();
        //validSmsCode(phone,code);
        //验证通过后，对于不同的type，生成不同的key，用于进行下一步
        String redisKey = BaseUtils.getSmsCodeValidNextKey(phone, type);
        //生成的key依旧保存获取该次短信验证码的信息
        String key = BaseUtils.uuid();
        redisTemplate.opsForValue().set(redisKey,key, expireTimeInSeconds,TimeUnit.SECONDS);
        answer.setData(key);
        return answer;
    }

    /**
     * 验证获取短信验证码后的下一步
     * @param phone
     * @param type
     * @param key
     */
    public void validSmsCodeValidNextStep(String phone,String type,String key){
        String redisKey = BaseUtils.getSmsCodeValidNextKey(phone, type);
        String redisValue = (String)redisTemplate.opsForValue().get(redisKey);
        if(StringUtils.isBlank(redisValue)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "该操作已无效，请返回上一步重新操作");
        }
        if(!key.equals(redisValue)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "该操作已无效，请返回上一步重新操作");
        }
        //验证通过后先不删除，在整个请求成功后，再删除
        //redisTemplate.delete(redisKey);
    }

    /**
     * 删除保存在redis的验证下一步的相关信息
     * @param phone
     * @param type
     */
    public void deleteSmsCodeValidNextStepInRedis(String phone,String type){
        String redisKey = BaseUtils.getSmsCodeValidNextKey(phone, type);
        redisTemplate.delete(redisKey);
    }

    /**
     *@Description: 发送短信验证码
     *@param phone: 手机号
     *@return: java.lang.Integer
     *@Author: zyj
     *@date: 2021/11/1 9:52
     */
    private Integer sendSmsValidCode(String phone, String tempId){
        Integer code = (int)((Math.random()*9+1)*100000);
        Msg4Request request = new Msg4Request();
        List<String> mobiles = new ArrayList<>();
        mobiles.add(phone);
        Map<String,String> message = new HashMap<>();
        message.put("code",code.toString());

        request.setMobiles(mobiles);
        request.setMessage(message);
        request.setTemplateId(tempId);
        BaseAnswer<Void> messageAnswer = smsFeignClient.asySendMessage(request);
        GeneralUtils.dealResult(messageAnswer);
        return code;
    }


}

