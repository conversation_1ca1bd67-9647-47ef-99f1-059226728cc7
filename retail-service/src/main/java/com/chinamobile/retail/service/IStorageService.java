package com.chinamobile.retail.service;


import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.entity.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;


/**
 * 对象存储接口
 */
public interface IStorageService {
    /**
     * 文件方式上传
     * @param file  文件
     * @param fname  文件key
     * @param isCover 是否覆盖（相同key是否覆盖）
     * @param expiredDay
     */
     BaseAnswer<UpResult> uploadFile(File file, String fname, boolean isCover, int expiredDay) throws Exception ;

    /**
     *  字节方式上传
     * @param bytes 字节数组
     * @return
     * @throws Exception
     */
     BaseAnswer<UpResult> uploadByte(ByteArrayUpload bytes) throws Exception ;

     BaseAnswer<UpResult> uploadBase64(Base64Upload base64) throws Exception ;
    /**
     * 批量删除
     * @param keys
     * @return
     * @throws Exception
     */
     BaseAnswer<DelResult> delete(String... keys) throws Exception ;

    /**
     * 设置过期时间
     * @param days
     * @param keys
     * @return
     * @throws Exception
     */
     BaseAnswer<DelResult> deleteAfterDays(int days, String... keys) throws Exception ;

    /**
     * 根据key查询对象存储地址
     * @param key
     * @return
     */
    BaseAnswer<QueryResult> getUrl(String key);

    /**
     * 预览打开文件
     * @param key
     * @return
     */
    void preview(String key, HttpServletResponse response);

    /**
     * 文件复制
     * @param copyUpload
     * @return
     * @throws Exception
     */
    BaseAnswer<UpResult> copy(CopyUpload copyUpload) throws Exception;
}
