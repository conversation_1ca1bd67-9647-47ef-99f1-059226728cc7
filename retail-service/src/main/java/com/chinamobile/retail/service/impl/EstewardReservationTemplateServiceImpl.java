package com.chinamobile.retail.service.impl;

import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.enums.log.EstewardOperateEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.retail.dao.EstewardReservationExtraTemplateMapper;
import com.chinamobile.retail.pojo.entity.EstewardReservationExtraTemplate;
import com.chinamobile.retail.pojo.entity.EstewardReservationExtraTemplateExample;
import com.chinamobile.retail.service.EstewardReservationTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EstewardReservationTemplateServiceImpl implements EstewardReservationTemplateService {

    @Resource
    private EstewardReservationExtraTemplateMapper templateMapper;

    @Resource
    private LogService logService;

    public void createTemplate(EstewardReservationExtraTemplate request) {
        EstewardReservationExtraTemplate template = new EstewardReservationExtraTemplate();
        String id = BaseServiceUtils.getId();
        template.setId(id);
        template.setQuestion(request.getQuestion());
        template.setTip(request.getTip());
        template.setCreateTime(new Date());
        template.setUpdateTime(new Date());
        templateMapper.insert(template);
    }

    public void updateTemplate(EstewardReservationExtraTemplate request) {
        EstewardReservationExtraTemplate template = templateMapper.selectByPrimaryKey(request.getId());
        if (template == null) throw new RuntimeException("模板不存在");
        template.setQuestion(request.getQuestion());
        template.setTip(request.getTip());
        template.setUpdateTime(new Date());
        templateMapper.updateByPrimaryKey(template);
    }

    public void deleteTemplate(String templateId) {
        templateMapper.deleteByPrimaryKey(templateId);
    }

    public List<EstewardReservationExtraTemplate> listAllTemplates() {
        return templateMapper.selectByExample(new EstewardReservationExtraTemplateExample());
    }

    public void batchProcessTemplates(List<EstewardReservationExtraTemplate> templates) {
        Set<String> existingTemplateIds = listAllTemplates().stream()
                .map(EstewardReservationExtraTemplate::getId)
                .collect(Collectors.toSet());

        Set<String> newTemplateIds = templates.stream()
                .map(EstewardReservationExtraTemplate::getId)
                .collect(Collectors.toSet());

        // 删除数据库中不存在于新模板数组中的模板
        existingTemplateIds.stream()
                .filter(id -> !newTemplateIds.contains(id))
                .forEach(this::deleteTemplate);

        StringBuilder logContent = new StringBuilder("【编辑模版】\n");
        // 处理新增和修改的模板
        for (EstewardReservationExtraTemplate template : templates) {
            if (template.getId() == null) {
                createTemplate(template);
            } else {
                updateTemplate(template);
            }
            // 记录日志
            logContent.append("修改为问题：").append(template.getQuestion()).append(", 描述: ").append(template.getTip()).append("\n");
        }
        logService.recordOperateLog(ModuleEnum.ESTEWARD_MANAGE.code,
                EstewardOperateEnum.RESERVATION_OPERATE.code,
                logContent.toString(), LogResultEnum.LOG_SUCESS.code, null);
    }
}