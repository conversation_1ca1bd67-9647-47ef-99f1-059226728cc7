package com.chinamobile.retail.service;

import com.chinamobile.retail.config.ServiceConfig;
import com.chinamobile.retail.util.sm4.tool.SM4;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

@Service
public class Sm4Service {

    @Autowired
    private ServiceConfig serviceConfig;

    public SM4 sm4;

    @PostConstruct
    public void init() {
        sm4 = new SM4(serviceConfig.getUnionPaySm4Key(), "UTF-8");
    }

    public  String encrypt(String str) {
        String encrypted = sm4.encryptDataToString_ECB(str);
        return encrypted;
    }

    public  String decrypt(String str) {
        String decrypted = sm4.decryptDataToString_ECB(str);
        return decrypted;
    }

}
