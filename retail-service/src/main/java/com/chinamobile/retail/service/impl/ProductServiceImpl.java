package com.chinamobile.retail.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.entity.b2b.Qry3rdInventoryResp;
import com.chinamobile.iot.sc.entity.b2b.QueryInventoryDTO;
import com.chinamobile.iot.sc.entity.iot.IotSearchMallLinkRequest;
import com.chinamobile.iot.sc.entity.iot.SearchMallLinkRequest;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.MiniProgramOperateEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.feign.ActivityFeignClient;
import com.chinamobile.iot.sc.feign.B2BFeignClient;
import com.chinamobile.iot.sc.feign.IotFeignClient;
import com.chinamobile.iot.sc.mode.*;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.HttpUtil;
import com.chinamobile.retail.config.CommonConstant;
import com.chinamobile.retail.config.WeixinConfig;
import com.chinamobile.retail.constant.MiniRoleEnum;
import com.chinamobile.retail.constant.RedisLockConstant;
import com.chinamobile.retail.dao.*;
import com.chinamobile.retail.dao.ext.SpuOfferingInfoMapperExt;
import com.chinamobile.retail.exception.IOTException;
import com.chinamobile.retail.exception.StatusContant;
import com.chinamobile.retail.pojo.dto.ActivityDataRankDTO;
import com.chinamobile.retail.pojo.entity.*;
import com.chinamobile.retail.pojo.param.miniprogram.PageProductParam;
import com.chinamobile.retail.pojo.param.miniprogram.ProductDetailParam;
import com.chinamobile.retail.pojo.param.miniprogram.SearchProductParam;
import com.chinamobile.retail.pojo.param.miniprogram.WebSearchProductParam;
import com.chinamobile.retail.pojo.vo.*;
import com.chinamobile.retail.pojo.vo.miniprogram.ActivityDataRankVO;
import com.chinamobile.retail.pojo.vo.miniprogram.ProductNavigationDirectoryVO;
import com.chinamobile.retail.pojo.vo.miniprogram.WeixinShareLinkUrlVO;
import com.chinamobile.retail.pojo.vo.miniprogram.WeixinShareUrlVO;
import com.chinamobile.retail.service.IProductService;
import com.chinamobile.retail.util.cache.RedisCacheUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/22 17:29
 * @description TODO
 */
@Service
@Slf4j
public class ProductServiceImpl implements IProductService {

    @Resource
    private SpuOfferingInfoMapperExt spuOfferingInfoMapperExt;

    @Resource
    private UserMiniProgramMapper userMiniProgramMapper;

    @Resource
    private SpuOfferingInfoMapper spuOfferingInfoMapper;

    @Resource
    private ProductFlowInstanceSpuMapper productFlowInstanceSpuMapper;

    @Resource
    private AtomOfferingInfoMapper atomOfferingInfoMapper;
    @Resource
    private MiniProgramSharecodeMapper miniProgramSharecodeMapper;

    @Resource
    private InventoryCutInfoMapper inventoryCutInfoMapper;

    @Resource
    private SkuOfferingInfoMapper skuOfferingInfoMapper;

    @Autowired
    private B2BFeignClient b2BFeignClient;

    @Resource
    private RedisCacheUtil redisCacheUtil;

    @Resource
    private ProductFlowInstanceAttachmentMapper productFlowInstanceAttachmentMapper;
    @Value("${weixin.weixinShareCode.appId}")
    private String weixinAppId;
    @Value("${weixin.weixinShareCode.appSecret}")
    private String weixinAppSecret;
    @Resource
    private IotFeignClient iotFeignClient;
    @Value("${weixin.getShareCode}")
    private String getWeixinShareCode;
    @Value("${weixin.getShareUrl}")
    private String getWeixinShareUrl;
    public static final String urlFormat = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
    @Resource
    miniProgramCodeService miniProgramCodeService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private ActivityFeignClient activityFeignClient;
    @Resource
    private SkuRoleRelationMapper skuRoleRelationMapper;
    @Resource
    private ProductNavigationDirectoryMapper productNavigationDirectoryMapper;

    public static final String REDIS_SPUQUANTITY_KEY = "cache:spu:quantity";

    @Resource
    private ContractProvinceInfoMapper provinceInfoMapper;

    @Resource
    private SpuSkuAttachmentMapper spuSkuAttachmentMapper;

    @Override
    @DS("query")
    public PageData<MiniProgramProductListVO> pageMiniProgramProduct(PageProductParam param, String userId) {
        if (StringUtils.isNotBlank(userId)) {
            UserMiniProgram userMiniProgram = redisCacheUtil.loadAndCache(
                    Constant.REDIS_KEY_MINI_USER + userId,
                    RedisLockConstant.LOCK_MINI_USER + userId,
                    1,
                    TimeUnit.DAYS,
                    () -> userMiniProgramMapper.selectByPrimaryKey(userId)
            );
            if (userMiniProgram == null) {
                throw new BusinessException(StatusContant.USER_NOT_FOUNT);
            }
            param.setProvinceCode(userMiniProgram.getBeId());
            param.setCityCode(userMiniProgram.getLocation());
            param.setRoleType(userMiniProgram.getRoleType());
        } else if (StringUtils.isBlank(param.getProvinceCode())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "缺少省编码");
        }
        Date date = DateTimeUtil.addMonth(new Date(), -12);
        Date previousMonthStart = DateTimeUtil.getMonthBegin(date);
        param.setOrderTimeStart(DateTimeUtil.formatDate(previousMonthStart, DateTimeUtil.DB_TIME_STR));
        log.info("二级目录订单销售额排序开始时间:{}", param.getOrderTimeStart());
        PageData<MiniProgramProductListVO> pageData = new PageData<>();
        String pageKey = param.getThirdDirectoryId() + "-" + param.getProvinceCode() + "-" + param.getCityCode()
                + "-" + param.getPageNum() + "-" + param.getPageSize() + "-" + param.getSaleTag() + "-" + param.getRoleType();

//        String countKey = param.getSecondDirectoryId() + param.getProvinceCode() + param.getCityCode()+":" + param.getSaleTag();
//        Integer count = redisCacheUtil.loadAndCache(
//                Constant.REDIS_KEY_MINI_PRODUCT_COUNT + countKey,
//                RedisLockConstant.LOCK_MINI_PRODUCT_COUNT + countKey,
//                30,
//                TimeUnit.MINUTES,
//                () -> spuOfferingInfoMapperExt.countMiniProgramProduct(param)
//        );

        List<MiniProgramProductListVO> data = redisCacheUtil.loadAndCache(
                Constant.REDIS_KEY_MINI_PRODUCT_LIST + pageKey,
                RedisLockConstant.LOCK_MINI_PRODUCT_LIST + pageKey,
                30,
                TimeUnit.MINUTES,
                () -> {
                    List<MiniProgramProductListVO> miniProgramProductListVOS = spuOfferingInfoMapperExt.pageMiniProgramProduct(param);
                    if (CollectionUtils.isEmpty(miniProgramProductListVOS)) {
                        return Collections.emptyList();
                    }


                    for (MiniProgramProductListVO vo : miniProgramProductListVOS) {
                        String spuQuantity = (String) stringRedisTemplate.opsForHash().get(REDIS_SPUQUANTITY_KEY, vo.getSpuCode());
                        Long amount = Long.valueOf(spuQuantity == null ? "0" : spuQuantity);
                        vo.setAmount(amount);
                    }

                    int begin = (param.getPageNum() - 1) * param.getPageSize();
                    int end = param.getPageNum() * param.getPageSize();
                    begin = Math.max(begin, 0);
                    end = Math.min(miniProgramProductListVOS.size(), end);
                    if (begin >= end) {
                        return Collections.emptyList();
                    }
                    miniProgramProductListVOS.sort(Comparator.comparing(MiniProgramProductListVO::getAmount).reversed());
                    List<MiniProgramProductListVO> subList = new ArrayList<>(miniProgramProductListVOS.subList(begin, end));

                    for (MiniProgramProductListVO miniProgramProductListVO : subList) {
                        if (StringUtils.isNotEmpty(miniProgramProductListVO.getSubSaleLabel())) {
                            miniProgramProductListVO.setSubLabelList(Arrays.asList(miniProgramProductListVO.getSubSaleLabel().split(",")));
                        }
                        if (StringUtils.isNotEmpty(miniProgramProductListVO.getMainSaleLabel())) {
                            miniProgramProductListVO.setMainLabelList(Arrays.asList(miniProgramProductListVO.getMainSaleLabel().split(",")));
                        }
                    }

                    return subList;
                }
        );
        String countKey = param.getThirdDirectoryId() + param.getProvinceCode() + param.getCityCode() + ":" + param.getSaleTag();
        Integer count = redisCacheUtil.get(Constant.REDIS_KEY_MINI_PRODUCT_COUNT + countKey);
        pageData.setCount(count != null ? count : 0);
        pageData.setData(data);
        pageData.setPage(param.getPageNum());
        return pageData;
    }

    @Override
    @DS("query")
    public PageData<MiniProgramProductListVO> searchProduct(SearchProductParam param) {
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        if (pageNum < 0 || pageSize < 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "页码和每页数量必须大于0");
        }
        if (StringUtils.isNotBlank(param.getUserId())) {
            UserMiniProgram userMiniProgram = redisCacheUtil.loadAndCache(
                    Constant.REDIS_KEY_MINI_USER + param.getUserId(),
                    RedisLockConstant.LOCK_MINI_USER + param.getUserId(),
                    1,
                    TimeUnit.DAYS,
                    () -> userMiniProgramMapper.selectByPrimaryKey(param.getUserId())
            );
            if (userMiniProgram == null) {
                throw new BusinessException(StatusContant.USER_NOT_FOUNT);
            }
            param.setProvinceCode(userMiniProgram.getBeId());
            param.setCityCode(userMiniProgram.getLocation());
            param.setRoleType(userMiniProgram.getRoleType());
        } else if (StringUtils.isBlank(param.getProvinceCode())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "缺少省编码");
        }
        PageData<MiniProgramProductListVO> pageData = new PageData<>();
        pageData.setPage(pageNum);

        String searchKey = param.getSearchWord() + param.getProvinceCode() + param.getCityCode()
                + param.getSaleTag() + "-" + param.getPageNum() + "-" + param.getPageSize()
                + "-" + param.getSaleTag() + "-" + param.getRoleType();

        List<MiniProgramProductListVO> data = redisCacheUtil.loadAndCache(
                Constant.REDIS_KEY_MINI_PRODUCT_SEARCH + searchKey,
                RedisLockConstant.LOCK_MINI_PRODUCT_SEARCH + searchKey,
                30,
                TimeUnit.MINUTES,
                () -> spuOfferingInfoMapperExt.searchProduct(param)
        );


        for (MiniProgramProductListVO productListVO : data) {
            if (StringUtils.isNotEmpty(productListVO.getSubSaleLabel())) {
                productListVO.setSubLabelList(Arrays.asList(productListVO.getSubSaleLabel().split(",")));
            }
            if (StringUtils.isNotEmpty(productListVO.getMainSaleLabel())) {
                productListVO.setMainLabelList(Arrays.asList(productListVO.getMainSaleLabel().split(",")));
            }
        }

        String countKey = param.getSearchWord() + param.getProvinceCode() + param.getCityCode()
                + param.getSaleTag()+ "-" + param.getRoleType();

        BigInteger count = redisCacheUtil.loadAndCache(
                Constant.REDIS_KEY_MINI_PRODUCT_SEARCH_COUNT + countKey,
                RedisLockConstant.LOCK_MINI_PRODUCT_SEARCH_COUNT + countKey,
                30,
                TimeUnit.MINUTES,
                () -> BigInteger.valueOf(spuOfferingInfoMapperExt.searchProductCount(param))
        );

        pageData.setCount(count.longValue());
        pageData.setData(data);
        return pageData;
    }

    @Override
    @DS("save")
    public String getShareCode(String scene, String page, String width, Integer expireInterval) {
        try {
            String accessToken = "";
            String url = String.format(urlFormat, weixinAppId, weixinAppSecret);
            Object o = HttpUtil.get(url, null, 10000, 10000);
            log.info("获取ccess_token响应:{}", o);
            if (o != null) {
                String str = (String) o;
                WeixinAccessTokenVO weixinAccessTokenVO = JSONObject.parseObject(str, WeixinAccessTokenVO.class);
                if (weixinAccessTokenVO.getAccess_token() != null && weixinAccessTokenVO.getExpires_in() != null) {
                    accessToken = weixinAccessTokenVO.getAccess_token();

                }
            }
            if (width == null) {
                width = "340";
            }
            url = getWeixinShareCode + "?access_token=" + accessToken;
            //微信小程序限制35位，将scene存入redis
            //将scene 进行md5加密
            String sceneMd5 = DigestUtils.md5Hex(scene);
            expireInterval = expireInterval == null ? -1 : expireInterval;
            //目前都是永久有效
            if (expireInterval == -1) {
                MiniProgramSharecode miniProgramSharecode = miniProgramSharecodeMapper.selectByPrimaryKey(sceneMd5);
                if (miniProgramSharecode == null) {
                    MiniProgramSharecode newMiniProgramSharecode = new MiniProgramSharecode();
                    newMiniProgramSharecode.setId(sceneMd5);
                    newMiniProgramSharecode.setScene(scene);
                    newMiniProgramSharecode.setCreateTime(new Date());
                    miniProgramSharecodeMapper.insertSelective(newMiniProgramSharecode);
                }
//                stringRedisTemplate.opsForValue().set(sceneMd5,"1");
            } else {
//                stringRedisTemplate.opsForValue().set(sceneMd5,"1",expireInterval, TimeUnit.DAYS);
            }

            String params = "{\"scene\":\"" + sceneMd5 + "\",\"page\":\"" + page + "\",\"width\":" + width + ",\"env_version\":" + "\"release\"" + "}";
//            BufferedReader result = miniProgramCodeService.doPost(url,params);
            String result = miniProgramCodeService.fetchImageAsBase64(url, params);
            // 处理返回结果
            if (result.contains("Error")) {
                log.info("获取微信分享码失败:{}", result);
                throw new BusinessException(BaseErrorConstant.MINI_PROGRAM_GET_SHARE_CODE_ERROR, result);
            } else {
                return result;
            }


        } catch (Exception e) {
            throw new BusinessException(BaseErrorConstant.MINI_PROGRAM_GET_SHARE_CODE_ERROR, e.getMessage());
        }


    }

    @Override
    @DS("query")
    public String shareCodeScene(String scene) {
        MiniProgramSharecode miniProgramShareCode = miniProgramSharecodeMapper.selectByPrimaryKey(scene);
        return miniProgramShareCode == null ? null : miniProgramShareCode.getScene();
    }

    @Override
    @DS("query")
    public String getShareLink(String path, String query, Integer expireInterval) {
        try {
            String accessToken = "";
            String url = String.format(urlFormat, weixinAppId, weixinAppSecret);
            Object o = HttpUtil.get(url, null, 10000, 10000);
            log.info("获取ccess_token响应:{}", o);
            if (o != null) {
                String str = (String) o;
                WeixinAccessTokenVO weixinAccessTokenVO = JSONObject.parseObject(str, WeixinAccessTokenVO.class);
                if (weixinAccessTokenVO.getAccess_token() != null && weixinAccessTokenVO.getExpires_in() != null) {
                    accessToken = weixinAccessTokenVO.getAccess_token();

                }
            }

            url = getWeixinShareUrl + "?access_token=" + accessToken;

            if (expireInterval == null) {
                expireInterval = 7;
            }
            String params = "{\"path\":\"" + path + "\",\"query\":\"" + query + "\",\"expire_type\":" + 1 + ",\"expire_interval\":" + expireInterval + ",\"env_version\":" + "\"release\"" + "}";


            String result = miniProgramCodeService.doPost(url, params);
            // 处理返回结果
            if (result != null) {
                WeixinShareLinkUrlVO weixinShareUrlVO = JSONObject.parseObject(result, WeixinShareLinkUrlVO.class);
                if (weixinShareUrlVO.getErrcode() == 0) {
                    return weixinShareUrlVO.getUrl_link();
                } else {
                    log.info("获取微信url出错:" + weixinShareUrlVO.getErrmsg());

                    throw new BusinessException(BaseErrorConstant.MINI_PROGRAM_GET_SHARE_CODE_ERROR, weixinShareUrlVO.getErrmsg());
                }
            }


        } catch (Exception e) {
            log.info("获取微信url出错:" + e.getMessage());
            throw new BusinessException(BaseErrorConstant.MINI_PROGRAM_GET_SHARE_CODE_ERROR, e.getMessage());
        }
        return null;
    }


    @Override
    @DS("query")
    public MiniProgramProductDetailVO productDetail(ProductDetailParam param, String userId) {
        if (StringUtils.isNotBlank(param.getUserId())) {
            UserMiniProgram userMiniProgram = redisCacheUtil.loadAndCache(
                    Constant.REDIS_KEY_MINI_USER + param.getUserId(),
                    RedisLockConstant.LOCK_MINI_USER + param.getUserId(),
                    1,
                    TimeUnit.DAYS,
                    () -> userMiniProgramMapper.selectByPrimaryKey(param.getUserId())
            );
            if (userMiniProgram == null) {
                throw new BusinessException(StatusContant.USER_NOT_FOUNT);
            }
            param.setProvinceCode(userMiniProgram.getBeId());
            param.setCityCode(userMiniProgram.getLocation());
            param.setRoleType(userMiniProgram.getRoleType());

            if (!StringUtils.equals(userMiniProgram.getRoleType(), MiniRoleEnum.IOT.getType())
                    && StringUtils.isBlank(param.getProvinceCode())
                    && StringUtils.isBlank(param.getCityCode())) {
                throw new BusinessException(StatusContant.PARAM_ERROR, "用户缺少区域信息");
            }
        } else if (StringUtils.isBlank(param.getProvinceCode())) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "缺少省编码");
        }

        // 缓存产品详情
        String key = param.getSpuCode() + "-" + param.getProvinceCode() + "-" + param.getCityCode() + "-" + param.getRoleType();
        MiniProgramProductDetailVO detailVO = redisCacheUtil.loadAndCache(
                Constant.REDIS_KEY_MINI_PRODUCT_DETAIL + key,
                RedisLockConstant.LOCK_MINI_PRODUCT_DETAIL + key,
                1,
                TimeUnit.DAYS,
                () -> {
                    MiniProgramProductDetailVO detail = spuOfferingInfoMapperExt.productDetail(param);
                    if (detail != null) {
                        // 查询sku
                        List<MiniProgramSkuInfoVO> skuInfoVOS = spuOfferingInfoMapperExt.getMiniProgramSkuList(param);
                        detail.setSkus(skuInfoVOS);
                    }
                    return detail;
                }
        );
        if (null == detailVO || CollectionUtils.isEmpty(detailVO.getSkus())) {
            redisCacheUtil.delete(Constant.REDIS_KEY_MINI_PRODUCT_DETAIL + key);
            throw new BusinessException(StatusContant.PRODUCT_NOT_FOUND, "您所在地市无法查看该产品！");
        }

        if (param.getRoleType() != null) {
            // 计算积分
            detailVO.getSkus().forEach(s -> {
                // 缓存sku积分
                BigInteger point = redisCacheUtil.loadAndCache(
                        Constant.REDIS_KEY_MINI_PRODUCT_SKU_POINT + s.getId() + param.getRoleType(),
                        RedisLockConstant.LOCK_MINI_PRODUCT_SKU_POINT + s.getId() + param.getRoleType(),
                        1,
                        TimeUnit.DAYS,
                        () -> {
                            List<SkuRoleRelation> skuRoleRelations = skuRoleRelationMapper.selectByExample(
                                    new SkuRoleRelationExample().createCriteria()
                                            .andSkuIdEqualTo(s.getId())
                                            .andPartnerRoleIdEqualTo(Integer.valueOf(param.getRoleType()))
                                            .example()
                            );
                            if (!CollectionUtils.isEmpty(skuRoleRelations)) {
                                SkuRoleRelation skuRoleRelation = skuRoleRelations.get(0);
                                //积分不可超过设置的上限
                                long pointLimit = skuRoleRelation.getPointLimit() == null ? Long.MAX_VALUE : skuRoleRelation.getPointLimit();
                                double pointPercent = skuRoleRelation.getPointPercent() != null ? skuRoleRelation.getPointPercent() : 0;
                                double v = (s.getSkuPrice() != null ? s.getSkuPrice() : 0) * pointPercent / 100;
                                long calculatedPoint = (long) v;
                                return BigInteger.valueOf(Math.min(calculatedPoint, pointLimit));
                            }
                            return BigInteger.valueOf(0);
                        }
                );
                s.setPoint(point.longValue());
            });
        }

        for (MiniProgramSkuInfoVO skuInfoVO : detailVO.getSkus()) {
            // 缓存sku图片
            List<SpuSkuAttachment> attachments = redisCacheUtil.loadAndCache(
                    Constant.REDIS_KEY_SPU_SKU_ATTACHMENT + skuInfoVO.getSpuCode(),
                    RedisLockConstant.LOCK_SPU_SKU_ATTACHMENT + skuInfoVO.getSpuCode(),
                    1,
                    TimeUnit.DAYS,
                    () -> spuSkuAttachmentMapper.selectByExample(
                            new SpuSkuAttachmentExample().createCriteria()
                                    .andSpuCodeEqualTo(skuInfoVO.getSpuCode())
                                    .example()
                    )
            );
            if (!CollectionUtils.isEmpty(attachments)) {
                attachments.forEach(attachment -> {
                    switch (attachment.getType()) {
                        case 1:
                            if (StringUtils.equals(attachment.getSkuCode(), skuInfoVO.getSkuCode())) {
                                skuInfoVO.getBanners().add(attachment.getFileUrl());
                            }
                            break;
                        case 3:
                            skuInfoVO.getDetailImage().add(attachment.getFileUrl());
                            break;
                        case 4:
                            skuInfoVO.getRealProductImage().add(attachment.getFileUrl());
                            break;
                        case 5:
                            skuInfoVO.getAfterSaleImage().add(attachment.getFileUrl());
                            break;
                        case 7:
                            if (detailVO.getCoreStatus() != null && detailVO.getCoreStatus() == 1
                                    && StringUtils.equals(attachment.getSkuCode(), skuInfoVO.getSkuCode())) {
                                skuInfoVO.getCoreBanners().add(attachment.getFileUrl());
                            }
                            break;
                        case 8:
                            if (detailVO.getCoreStatus() != null && detailVO.getCoreStatus() == 1
                                    && StringUtils.equals(attachment.getSkuCode(), skuInfoVO.getSkuCode())) {
                                skuInfoVO.getCoreVideos().add(attachment.getFileUrl());
                            }
                            break;
                    }
                });
            }
        }
        return detailVO;
    }

    private long getThirdMinCount(List<Qry3rdInventoryResp.InventoryRet> countList) {
        long minCount = -1;
        for (Qry3rdInventoryResp.InventoryRet item : countList) {
            //           String materialNum = item.getMaterialNum();
            long quatity = item.getQuatity();
            //暂时取第一个
            minCount = quatity;
        }
        return minCount;
    }

    private Long getMinQuantity(List<AtomOfferingInfo> result) {
        //查出所有的原子商品信息，然后根据库存去计算规格数量剩余
        AtomicReference<Long> minQuantity = new AtomicReference<>(null);
        //查询当前库存模式，拍下减库存模式复用原逻辑返回库存数，付款减库存返回 总库存数=库存数+预占库存数
        BaseAnswer<String> inventoryCutNow = iotFeignClient.getInventoryCutNow();
        if (!inventoryCutNow.getStateCode().equals(StatusContant.SUCCESS.getStateCode())) {
            log.error("查询当前库存模式出错:{}", inventoryCutNow.getMessage());
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, inventoryCutNow.getMessage());
        }
        String nowInventoryPattern = iotFeignClient.getInventoryCutNow().getData();
        if (org.apache.commons.lang.StringUtils.isNotEmpty(nowInventoryPattern)) {
            if ("拍下减库存".equals(nowInventoryPattern)) {
                for (AtomOfferingInfo x : result) {
                    //这里获取（总库存-预占库存）/规格下原子商品数量 比较最小值
                    if (x.getInventory() == null || x.getReserveInventory() == null) {
                        minQuantity.set(0L);
                        break;
                    }
                    long quantity = x.getInventory() / x.getQuantity();
                    if (minQuantity.get() == null) {
                        minQuantity.set(quantity);
                    }
                    minQuantity.set(quantity < minQuantity.get() ? quantity : minQuantity.get());
                }
            } else {
                for (AtomOfferingInfo x : result) {
                    //这里获取（总库存）/规格下原子商品数量 比较最小值
                    if (x.getInventory() == null || x.getReserveInventory() == null) {
                        minQuantity.set(0L);
                        break;
                    }
                    long inventorySum = x.getInventory() + x.getReserveInventory();
                    long quantity = inventorySum / x.getQuantity();
                    if (minQuantity.get() == null) {
                        minQuantity.set(quantity);
                    }
                    minQuantity.set(quantity < minQuantity.get() ? quantity : minQuantity.get());
                }
            }
        } else {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "库存切换模式未配置，查询库存失败");
        }
        return minQuantity.get();
    }

    @Override
    @DS("query")
    public ShareUrlVO getShareUrl(String spuCode, String userId) {
        log.error("getShareUrl参数，spuCode:{},userId:{}", spuCode, userId);
        UserMiniProgram userMiniProgram = redisCacheUtil.loadAndCache(
                Constant.REDIS_KEY_MINI_USER + userId,
                RedisLockConstant.LOCK_MINI_USER + userId,
                1,
                TimeUnit.DAYS,
                () -> userMiniProgramMapper.selectByPrimaryKey(userId)
        );
        if (userMiniProgram == null) {
            throw new BusinessException(StatusContant.USER_NOT_FOUNT);
        }
        // 通过商城提供的接口，查询商品的分享链接
        SearchMallLinkRequest request = new SearchMallLinkRequest();
        String roleType = userMiniProgram.getRoleType();
        if ("1".equals(roleType) || "2".equals(roleType)) {
            // 分销员
            request.setLoginRole("1");
        } else if ("3".equals(roleType)) {
            // 渠道商
            request.setLoginRole("3");
        } else if ("4".equals(roleType)) {
            // 客户经理
            request.setLoginRole("2");
        } else {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "当前用户角色无法获取分享链接");
        }
        // 商品分享链接
        request.setLinkType("1");
        request.setSessionVerification("2");
        request.setSpuCode(spuCode);
        List<SearchMallLinkRequest.UserInfo> userInfos = new ArrayList<>();
        SearchMallLinkRequest.UserInfo userInfo = new SearchMallLinkRequest.UserInfo();
        userInfo.setUserID(userMiniProgram.getUserId());
        userInfos.add(userInfo);
        request.setUserInfo(userInfos);
        IotSearchMallLinkRequest innerRequest = new IotSearchMallLinkRequest();
        innerRequest.setBeId(userMiniProgram.getBeId());
        BeanUtils.copyProperties(request, innerRequest);

        ShareUrlVO vo = new ShareUrlVO();
        try {
            BaseAnswer<String> shareUrl = iotFeignClient.getShareUrl(innerRequest);
            if (!ExcepStatus.getSuccInstance().getStateCode().equals(shareUrl.getStateCode())) {
                log.error("iotFeignClient.getShareUrl失败:{}，spuCode:{}", shareUrl.getMessage(), spuCode);
                throw new BusinessException(StatusContant.INTERNAL_ERROR, "获取分享链接失败");
            }
            vo.setUrl(shareUrl.getData());
        } catch (Exception e) {
            log.error("iotFeignClient.getShareUrl发生异常，spuCode:{}", spuCode, e);
            throw new BusinessException(StatusContant.INTERNAL_ERROR, "获取分享链接异常");
        }
        return vo;
    }

    @Override
    @DS("query")
    public MiniProgramProductDetailVO productDetailByActivityId(String spuCode, String activityId, String provinceCode) {
        String traceId = BaseServiceUtils.getId();
        log.info("{} productDetailByActivityId 入参:{}", traceId, "spuCode:" + spuCode + ",activityId:" + activityId + "_" + ",provinceCode:" + provinceCode);
        ProductDetailParam param = new ProductDetailParam();
        param.setSpuCode(spuCode);
        if (activityId != null) {
            //优先 根据专区的范围来判断是否显示区域,专区目前只有省区域（全国是null），没有市区域
            BaseAnswer<ActivityFeignDTO> activityAnswer = activityFeignClient.activityById(activityId);
            if (!ExcepStatus.getSuccInstance().getStateCode().equals(activityAnswer.getStateCode())) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "获取专区信息失败:" + activityAnswer.getMessage());
            }
            ActivityFeignDTO activityFeignDTO = activityAnswer.getData();
            String activityProvinceCode = activityFeignDTO.getProvinceCode();
            param.setProvinceCode(StringUtils.isEmpty(activityProvinceCode) ? "000" : activityProvinceCode);
        } else if (provinceCode != null) {
            // 湖南商企小程序需求，无专区，手动传入省区域
            param.setProvinceCode(provinceCode);
        } else {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "无法获取区域信息,activityId和provinceCode不允许都为空:");
        }

        MiniProgramProductDetailVO productDetailVO = spuOfferingInfoMapperExt.productDetailByActivityId(param);
        //校验区域
        checkProvinceAndCity(param, productDetailVO);

        String spuOfferingClass = productDetailVO.getSpuOfferingClass();
        String spuOfferingCode = productDetailVO.getSpuCode();
        if (StringUtils.isEmpty(spuOfferingClass) || StringUtils.isEmpty(spuOfferingCode)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "spu商品不存在或不存在上架流程信息");
        }
        // 查询sku
        if ("000".equals(param.getProvinceCode())) {
            //如果是全国，则不需要匹配省编码
            param.setProvinceCode(null);
        }
        List<MiniProgramSkuInfoVO> skuInfoVOS = spuOfferingInfoMapperExt.getMiniProgramSkuListByActivityId(param);
        List<String> skuCodeList = skuInfoVOS.stream().map(s -> {
            return s.getSkuCode();
        }).collect(Collectors.toList());

        //复用IOT的库存查询接口
        IOTRequest iotRequest = new IOTRequest();
        iotRequest.setMessageSeq(traceId);
        //封装库存查询业务参数
        InventoryInfoRequest inventoryInfoRequest = new InventoryInfoRequest();
        //小程序设置区域
        inventoryInfoRequest.setRegionId(param.getProvinceCode());
        List<InventoryInfoRequest.InventoryInfo> inventoryInfo = new ArrayList<>();
        inventoryInfoRequest.setInventoryInfo(inventoryInfo);
        InventoryInfoRequest.InventoryInfo info = new InventoryInfoRequest.InventoryInfo();
        inventoryInfo.add(info);
        //小程序没有BookId
        info.setBookId(null);
        List<InventoryInfoRequest.SpuOfferingInfo> spuOfferingInfo = new ArrayList<>();
        info.setSpuOfferingInfo(spuOfferingInfo);
        InventoryInfoRequest.SpuOfferingInfo spuInfo = new InventoryInfoRequest.SpuOfferingInfo();
        spuOfferingInfo.add(spuInfo);
        spuInfo.setSpuOfferingCode(spuCode);
        spuInfo.setOfferingClass(spuOfferingClass);
        //sku编码以及对应的原子信息map
        Map<String, List<MiniProgramSkuInfoVO.AtomItem>> skuCodeAndAtomItemMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(skuCodeList)) {
            spuInfo.setSkuOfferingInfo(skuCodeList.stream().map(s -> {
                InventoryInfoRequest.SkuOfferingInfo skuInfo = new InventoryInfoRequest.SkuOfferingInfo();
                skuInfo.setSkuOfferingCode(s);
                AtomOfferingInfoExample atomExample = new AtomOfferingInfoExample().createCriteria()
                        .andSpuCodeEqualTo(spuOfferingCode)
                        .andSkuCodeEqualTo(s)
                        .andDeleteTimeIsNull()
                        .example();
                List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper.selectByExample(atomExample);
                if (!CollectionUtils.isEmpty(atomOfferingInfos)) {
                    skuInfo.setAtomOfferingInfo(atomOfferingInfos.stream().map(a -> {
                        InventoryInfoRequest.AtomOfferingInfo atomInfo = new InventoryInfoRequest.AtomOfferingInfo();
                        atomInfo.setAtomOfferingCode(a.getOfferingCode());
                        return atomInfo;
                    }).collect(Collectors.toList()));
                }

                List<MiniProgramSkuInfoVO.AtomItem> atomList = atomOfferingInfos.stream().map(atom -> {
                    MiniProgramSkuInfoVO.AtomItem atomItem = new MiniProgramSkuInfoVO.AtomItem();
                    atomItem.setAtomId(atom.getId());
                    atomItem.setAtomCode(atom.getOfferingCode());
                    atomItem.setAtomName(atom.getOfferingName());
                    atomItem.setAtomUnit(atom.getUnit());
                    atomItem.setAtomPrice(atom.getAtomSalePrice());
                    atomItem.setInventory(atom.getInventory());
                    atomItem.setReserveInventory(atom.getReserveInventory());
                    return atomItem;
                }).collect(Collectors.toList());

                skuCodeAndAtomItemMap.put(s, atomList);
                return skuInfo;
            }).collect(Collectors.toList()));
        }

        iotRequest.setContent(JSON.toJSONString(inventoryInfoRequest));
        IOTAnswer iotAnswer = null;
        try {
            log.info("{} productDetailByActivityId 请求参数:{}", traceId, iotRequest.getContent());
            iotAnswer = iotFeignClient.qryInventoryFromMini(iotRequest);
            log.info("{} productDetailByActivityId 查询库存响应:{}", traceId, iotAnswer.getContent());
        } catch (Exception e) {
            if (e instanceof IOTException) {
                IOTException iotException = (IOTException) e;
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "查询库存出错:" + iotException.getAnswer().getResultDesc());
            } else {
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "查询库存出错");
            }
        }
        //根据响应，封装库存数据
        LinkedHashMap mapResp = (LinkedHashMap) iotAnswer.getContent();
        ObjectMapper mapper = new ObjectMapper();
        String json = null;
        try {
            json = mapper.writeValueAsString(mapResp);
        } catch (Exception e) {
            log.info("{} productDetailByActivityId 库存响应转化json失败:{}", traceId, e);
        }
        InventoryInfoResponse inventoryInfoResponse = JSONObject.parseObject(json, InventoryInfoResponse.class);
        InventoryInfoResponse.SpuOfferingInfo spuInventoryInfo = inventoryInfoResponse.getInventoryInfo().getSpuOfferingInfo().get(0);
        List<InventoryInfoResponse.SkuOfferingInfo> skuInventoryInfo = spuInventoryInfo.getSkuOfferingInfo();
        Map<String, Long> skuCodeAndQuantityMap = new HashMap<>();
        //原子编码和原子库存map
        Map<String, Long> skuCodeAtomCodeAndQuantityMap = new HashMap<>();
        for (InventoryInfoResponse.SkuOfferingInfo skuOfferingInfo : skuInventoryInfo) {
            skuCodeAndQuantityMap.put(skuOfferingInfo.getSkuOfferingCode(), skuOfferingInfo.getQuantity());
            List<InventoryInfoResponse.AtomOfferingInfo> atomOfferingInfo = skuOfferingInfo.getAtomOfferingInfo();
            if (!CollectionUtils.isEmpty(atomOfferingInfo)) {
                atomOfferingInfo.forEach(atom -> {
                    skuCodeAtomCodeAndQuantityMap.put(skuOfferingInfo.getSkuOfferingCode() + atom.getAtomOfferingCode(), atom.getQuantity());
                });
            }

        }
        for (MiniProgramSkuInfoVO skuInfoVO : skuInfoVOS) {
            //为sku设置库存
            skuInfoVO.setSkuQuantity(skuCodeAndQuantityMap.get(skuInfoVO.getSkuCode()));
            List<MiniProgramSkuInfoVO.AtomItem> atomItems = skuCodeAndAtomItemMap.get(skuInfoVO.getSkuCode());
            //为原子设置库存
            if (!CollectionUtils.isEmpty(atomItems)) {
                Iterator<MiniProgramSkuInfoVO.AtomItem> iterator = atomItems.iterator();
                while (iterator.hasNext()) {
                    MiniProgramSkuInfoVO.AtomItem atom = iterator.next();
                    Long atomQuantity = skuCodeAtomCodeAndQuantityMap.get(skuInfoVO.getSkuCode() + atom.getAtomCode());
                    if(atomQuantity == null){
                        //没有原子库存的，不要返回，避免影响前端判断
                        iterator.remove();
                    }else {
                        atom.setAtomQuantity(atomQuantity);
                    }
                }
            }
            if(!CollectionUtils.isEmpty(atomItems)){
                skuInfoVO.setAtomInfoList(atomItems);
            }
            //如果skuQuantity = null,处理为9999
            if(skuInfoVO.getSkuQuantity() == null){
                skuInfoVO.setSkuQuantity(9999L);
            }
        }
        //库存查询和处理结束

        productDetailVO.setSkus(skuInfoVOS);

        if (!CollectionUtils.isEmpty(skuInfoVOS)) {
//            List<String> flowInstanceIds = skuInfoVOS.stream().map(MiniProgramSkuInfoVO::getFlowInstanceId).collect(Collectors.toList());
            for (MiniProgramSkuInfoVO skuInfoVO : skuInfoVOS) {
                List<SpuSkuAttachment> attachments = spuSkuAttachmentMapper.selectByExample(
                        new SpuSkuAttachmentExample().createCriteria()
                                .andSpuCodeEqualTo(skuInfoVO.getSpuCode())
                                .example()
                );
                if (!CollectionUtils.isEmpty(attachments)) {
                    attachments.forEach(attachment -> {
                        switch (attachment.getType()) {
                            case 1:
                                if (StringUtils.equals(attachment.getSkuCode(), skuInfoVO.getSkuCode())) {
                                    skuInfoVO.getBanners().add(attachment.getFileUrl());
                                }
                                break;
                            case 3:
                                skuInfoVO.getDetailImage().add(attachment.getFileUrl());
                                break;
                            case 4:
                                skuInfoVO.getRealProductImage().add(attachment.getFileUrl());
                                break;
                            case 5:
                                skuInfoVO.getAfterSaleImage().add(attachment.getFileUrl());
                                break;
                        }
                    });
                }
            }
        }
        log.info("{} productDetailByActivityId 返回:{}", traceId, JSON.toJSONString(productDetailVO));
        return productDetailVO;
    }

    @Override
    public List<ProductNavigationDirectoryVO> listDirectory() {
        return redisCacheUtil.loadAndCache(
                Constant.REDIS_KEY_MINI_PRODUCT_NAVIGATION_DIRECTORY,
                RedisLockConstant.PRODUCT_MINI_NAVIGATION_DIRECTORY_LOCK,
                () -> {
                    List<ProductNavigationDirectory> first = productNavigationDirectoryMapper.selectByExample(
                            new ProductNavigationDirectoryExample().createCriteria()
                                    .andParentIdEqualTo("-1")
                                    .andMenuEqualTo("1")
                                    .andIsDeleteEqualTo(false)
                                    .example().orderBy("sort")
                    );
                    if (!CollectionUtils.isEmpty(first)) {
                        return first.stream().map(item -> {
                            ProductNavigationDirectoryVO vo = new ProductNavigationDirectoryVO();
                            BeanUtils.copyProperties(item, vo);
                            List<ProductNavigationDirectory> second = productNavigationDirectoryMapper.selectByExample(
                                    new ProductNavigationDirectoryExample().createCriteria()
                                            .andParentIdEqualTo(item.getId())
                                            .andIsDeleteEqualTo(false)
                                            .example().orderBy("sort")
                            );
                            if (!CollectionUtils.isEmpty(second)) {
                                List<ProductNavigationDirectoryVO> children = second.stream().map(child -> {
                                    ProductNavigationDirectoryVO childVO = new ProductNavigationDirectoryVO();
                                    BeanUtils.copyProperties(child, childVO);
                                    childVO.setIsNew(childVO.getImage() == null);

                                    List<ProductNavigationDirectory> third = productNavigationDirectoryMapper.selectByExample(
                                            new ProductNavigationDirectoryExample().createCriteria()
                                                    .andParentIdEqualTo(child.getId())
                                                    .andIsDeleteEqualTo(false)
                                                    .example().orderBy("sort")
                                    );
                                    if (!CollectionUtils.isEmpty(third)) {
                                        childVO.setChildren(third.stream().map(thirdChild -> {
                                            ProductNavigationDirectoryVO thirdChildVO = new ProductNavigationDirectoryVO();
                                            BeanUtils.copyProperties(thirdChild, thirdChildVO);
                                            thirdChildVO.setIsNew(thirdChildVO.getImage() == null);
                                            return thirdChildVO;
                                        }).collect(Collectors.toList()));
                                    }

                                    return childVO;
                                }).collect(Collectors.toList());
                                vo.setChildren(children);
                            }
                            return vo;
                        }).collect(Collectors.toList());
                    }
                    return Collections.emptyList();
                }
        );
    }

    private void checkProvinceAndCity(ProductDetailParam param, MiniProgramProductDetailVO productDetailVO) {
        String provinceCityCodeListStr = productDetailVO.getProvinceCityCodeList();
        if (StringUtils.isEmpty(provinceCityCodeListStr)) {
            throw new BusinessException(BaseErrorConstant.AREA_ERROR, "商品没有发布范围");
        }
        String[] provinceCityCodeArr = provinceCityCodeListStr.split(",");
        List<String> provinceCityCodeList = Arrays.asList(provinceCityCodeArr);
        String provinceCode = param.getProvinceCode();
        String cityCode = param.getCityCode();
        boolean regionMatch = false;
        for (String dbProvinceCityCode : provinceCityCodeList) {
            String[] split = dbProvinceCityCode.split("-");
            String dbProvinceCode = split[0];
            String dbCityCode = split[1];
            if (StringUtils.isNotEmpty(provinceCode)) {
                //专区是全国，或者商品发布省是全国，或者专区的省和商品发布省一致，就可以看
                if ("000".equals(provinceCode) || "000".equals(dbProvinceCode) || provinceCode.equals(dbProvinceCode)) {
                    regionMatch = true;
                }
            }
            if (!regionMatch) {
                //省没匹配上，就不用匹配地市了
                continue;
            }
            if (StringUtils.isNotEmpty(cityCode)) {
                if ("allCity".equals(dbCityCode) || cityCode.equals(dbCityCode)) {
                    regionMatch = true;
                } else {
                    regionMatch = false;
                }
            }
            if (regionMatch) {
                break;
            }
        }
        if (!regionMatch) {
            throw new BusinessException(BaseErrorConstant.AREA_ERROR, "区域和商品发布区域不匹配");
        }
    }

    @Override
    @DS("query")
    public PageData<MiniProgramProductListVO> searchProductForWeb(WebSearchProductParam param) {
        if (StringUtils.isBlank(param.getProvinceCode())) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "省编码不能为空");
        }
        if (StringUtils.equals("000", param.getProvinceCode())) {
            param.setProvinceCode(null);
        }
        PageData<MiniProgramProductListVO> pageData = new PageData<>();
        pageData.setPage(param.getPageNum());
        pageData.setCount(0);
        Long count = spuOfferingInfoMapperExt.countWebSearchProduct(param);
        if (count > 0) {
            pageData.setCount(count);
            pageData.setData(spuOfferingInfoMapperExt.webSearchProduct(param));
        }

        return pageData;
    }

    @Override
    @DS("query")
    public PageData<MiniProgramProductListVO> searchProductForWebScene(WebSearchProductParam param) {
        PageData<MiniProgramProductListVO> pageData = new PageData<>();
        pageData.setPage(param.getPageNum());
        pageData.setCount(0);
        Long count = spuOfferingInfoMapperExt.countWebSceneSearchProduct(param);
        if (count > 0) {
            pageData.setCount(count);
            pageData.setData(spuOfferingInfoMapperExt.webSceneSearchProduct(param));
        }

        return pageData;
    }

    @Override
    public BaseAnswer<List<ContractProvinceInfo>> getProvinces() {
        List<String> excludeProvince = new ArrayList<>();
        excludeProvince.add("000");
        excludeProvince.add("001");
        excludeProvince.add("002");
        BaseAnswer<List<ContractProvinceInfo>> result = new BaseAnswer<>();
        List<ContractProvinceInfo> data = provinceInfoMapper.selectByExample(new ContractProvinceInfoExample()
                .createCriteria().andMallCodeNotIn(excludeProvince).example());
        result.setData(data);
        return result;
    }
}
