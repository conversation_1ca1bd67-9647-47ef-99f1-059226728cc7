package com.chinamobile.retail.service.impl;

import com.alibaba.fastjson.JSON;
import com.amazonaws.services.dynamodbv2.xspec.B;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.entity.iot.IotSearchMallLinkRequest;
import com.chinamobile.iot.sc.entity.iot.SearchMallLinkRequest;
import com.chinamobile.iot.sc.entity.retail.PointPercentBySkuAndRoleParam;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.feign.IotFeignClient;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.mode.PointPercentBySkuAndRoleVO;
import com.chinamobile.retail.config.CommonConstant;
import com.chinamobile.retail.config.ProvinceCityConfig;
import com.chinamobile.retail.config.ServiceConfig;
import com.chinamobile.retail.dao.*;
import com.chinamobile.retail.dao.ext.SkuOfferingInfoMapperExt;
import com.chinamobile.retail.dao.ext.SpuOfferingInfoMapperExt;
import com.chinamobile.retail.enums.PointStatusEnum;
import com.chinamobile.retail.enums.RetailUserRoleEnum;
import com.chinamobile.retail.exception.StatusContant;
import com.chinamobile.retail.pojo.dto.SkuPartnerRoleDTO;
import com.chinamobile.retail.pojo.entity.*;
import com.chinamobile.retail.pojo.mapper.ProductBackListDO;
import com.chinamobile.retail.pojo.mapper.ProductFrontListDO;
import com.chinamobile.retail.pojo.mapper.SkuPointListDO;
import com.chinamobile.retail.pojo.param.ConfigPointParam;
import com.chinamobile.retail.pojo.param.ConfigPointStatusParam;
import com.chinamobile.retail.pojo.param.ProductBackListParam;
import com.chinamobile.retail.pojo.param.ProductFrontListParam;
import com.chinamobile.retail.pojo.vo.ProductBackListVO;
import com.chinamobile.retail.pojo.vo.ProductFrontListVO;
import com.chinamobile.retail.pojo.vo.ShareUrlVO;
import com.chinamobile.retail.pojo.vo.SkuPointListVO;
import com.chinamobile.retail.service.ProductPointService;
import com.chinamobile.retail.util.cache.RedisCacheUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * created by liuxiang on 2022/8/31 11:01
 */
@Component
@Slf4j
public class ProductPointServiceImpl implements ProductPointService {

    @Resource
    private SkuOfferingInfoMapper skuOfferingInfoMapper;

    @Resource
    private SkuRoleRelationMapper skuRoleRelationMapper;

    @Resource
    private SkuOfferingInfoMapperExt skuOfferingInfoMapperExt;

    @Resource
    private SpuOfferingInfoMapperExt spuOfferingInfoMapperExt;

    @Resource
    private ServiceConfig serviceConfig;

    @Resource
    private UserRetailMapper userRetailMapper;

    @Resource
    private SpuOfferingInfoMapper spuOfferingInfoMapper;

    @Autowired
    private IotFeignClient iotFeignClient;

    @Resource
    private SupplierAssociateProductMapper supplierAssociateProductMapper;

    @Autowired
    private ProvinceCityConfig provinceCityConfig;

    private ExecutorService executorService = new ThreadPoolExecutor(50,50,1L, TimeUnit.MINUTES,new LinkedBlockingDeque<>(10000));

    @Resource
    private RedisCacheUtil redisCacheUtil;

    @Override
    @DS("query")
    public BaseAnswer<PageData<ProductBackListVO>> getProductBackList(ProductBackListParam param) {
        PageData<ProductBackListVO> pageData = new PageData<>();
        Integer partnerRoleId = param.getPartnerRoleType();
        List<Integer> pointStatus = param.getPointStatus();
        List<String> pointSupplierId = param.getPointSupplierId();
        List<String> productType = param.getProductType();
        String queryParam = param.getQueryParam();
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        pageData.setPage(pageNum);
        if(CollectionUtils.isEmpty(productType)){
            //不选择范式时,返回空数据
            return BaseAnswer.success(pageData);
        }
        Boolean nationwide = param.getNationwide();
        List<String> provinceCodeList = param.getProvinceCodeList();
        List<String> cityCodeList = param.getCityCodeList();
        if(nationwide != null && nationwide && ( (!CollectionUtils.isEmpty(provinceCodeList)) || (!CollectionUtils.isEmpty(cityCodeList)))){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"发布范围选择全国时，不能选再择省和市");
        }
        if(nationwide != null && nationwide){
            //发布范围是全国，则只是全国
            provinceCodeList = new ArrayList<>();
            provinceCodeList.add(CommonConstant.nation_wide);
        }else if(nationwide != null && !nationwide) {
            //发布范围是选择的省市，则需要加上全国
            if(!CollectionUtils.isEmpty(provinceCodeList)){
                provinceCodeList.add(CommonConstant.nation_wide);
            }
            if(!CollectionUtils.isEmpty(cityCodeList)){
                //如果发布范围是某省或全国，对应的cityCode = null,但是In不能查询null，所以sql语句中用ifNull把null值转换成空字符串查询
                cityCodeList.add("");
            }
        }
        log.info("partnerRoleId:{}",partnerRoleId);
        List<ProductBackListDO> list = skuOfferingInfoMapperExt.pageQueryProductBackList(partnerRoleId,pointStatus,pointSupplierId,productType,queryParam,provinceCodeList,cityCodeList,(pageNum-1)*pageSize,pageSize);
        int count = skuOfferingInfoMapperExt.pageCountProductBackList(partnerRoleId, pointStatus, pointSupplierId, productType, queryParam,provinceCodeList,cityCodeList);
        List<ProductBackListVO> collect = list.stream().map(p -> {
            ProductBackListVO vo = new ProductBackListVO();
            BeanUtils.copyProperties(p, vo);
            RetailUserRoleEnum retailUserRoleEnum = RetailUserRoleEnum.fromCode(vo.getPartnerRoleId());
            if(retailUserRoleEnum == null){
                log.error("数据错误，合伙人角色类型不存在");
            }else {
                vo.setPartnerRoleName(retailUserRoleEnum.name);
            }
            //单独查询实际产品名称，比子查询速度更快
            String realProductName = skuOfferingInfoMapperExt.getRealProductName(p.getSkuOfferingCode());
            vo.setRealProductName(realProductName);
            //设置发布范围省市名称,逗号分隔
            String provinceCode = p.getProvinceCode();
            if(StringUtils.isNotEmpty(provinceCode)){
                //去重复和排序，因为数据库中group_concat没有排序，每次查询结果顺序可能不同
                HashSet<String> provinceCodeSet = new HashSet<>(Arrays.asList(provinceCode.split(",")));
                ArrayList<String> provinceList = new ArrayList<>(provinceCodeSet);
                Collections.sort(provinceList);
                List<String> provinceNameList = provinceList.stream().map(pCode -> {
                    if(CommonConstant.nation_wide.equals(pCode)){
                        return "全国";
                    }else {
                        return provinceCityConfig.getProvinceCodeNameMap().get(pCode);
                    }
                }).filter(provinceName -> {return StringUtils.isNotEmpty(provinceName);}).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(provinceNameList)){
                    vo.setProvinceName(String.join(",",provinceNameList));
                }
            }
            String cityCode = p.getCityCode();
            if(StringUtils.isNotEmpty(cityCode)){
                HashSet<String> cityCodeSet = new HashSet<>(Arrays.asList(cityCode.split(",")));
                ArrayList<String> cityList = new ArrayList<>(cityCodeSet);
                Collections.sort(cityList);
                List<String> cityNameList = cityList.stream().map(cCode -> {
                    String cityName = provinceCityConfig.getCityCodeNameMap().get(cCode);
                    return cityName;
                }).filter(cityName -> {return StringUtils.isNotEmpty(cityName);}).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(cityNameList)){
                    vo.setCityName(String.join(",",cityNameList));
                }
            }
            return vo;
        }).collect(Collectors.toList());
        pageData.setCount(count);
        pageData.setData(collect);
        return BaseAnswer.success(pageData);
    }

    @Override
    @DS("save")
    public BaseAnswer<Void> configPointStatus(ConfigPointStatusParam param) {
        List<String> idList = param.getIdList();
        Integer pointStatus = param.getPointStatus();
        Date now = new Date();
        idList.forEach(i -> {
            SkuOfferingInfo skuOfferingInfo = new SkuOfferingInfo();
            skuOfferingInfo.setId(i);
            skuOfferingInfo.setPointStatus(pointStatus);
            skuOfferingInfo.setUpdateTime(now);
            skuOfferingInfoMapper.updateByPrimaryKeySelective(skuOfferingInfo);
        });
        return BaseAnswer.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public BaseAnswer<Void> configPoint(ConfigPointParam param) {
        List<SkuPartnerRoleDTO> itemList = param.getItemList();
        checkParam(itemList,true);
        //同一个商品，对不同的合伙人，积分比例和上限是独立设置的
        configPointPercentOrLimit(itemList,true);
        return BaseAnswer.success(null);
    }

    /**
     * 配置积分百分比或者上限
     */
    private void configPointPercentOrLimit(List<SkuPartnerRoleDTO> itemList,boolean available) {
        Date now = new Date();
        List<SkuRoleRelation> skuRoleRelationList = new ArrayList<>();
        for (SkuPartnerRoleDTO item : itemList) {
            Integer partnerRoleType = item.getPartnerRoleId();
            if(RetailUserRoleEnum.fromCode(partnerRoleType) == null){
                throw new BusinessException(StatusContant.ROLE_NOT_FOUNT);
            }
            String skuId = item.getSkuId();
            Long pointLimit = item.getPointLimit();
            Double pointPercent = item.getPointPercent() == null ? 0 : item.getPointPercent();
            SkuRoleRelationExample skuRoleExample = new SkuRoleRelationExample().createCriteria().andSkuIdEqualTo(skuId).andPartnerRoleIdEqualTo(partnerRoleType).example();
            List<SkuRoleRelation> skuRoleRelations = skuRoleRelationMapper.selectByExample(skuRoleExample);
            if(skuRoleRelations.isEmpty()){
                //新增合伙人商品积分配置
                SkuRoleRelation skuRoleRelation = new SkuRoleRelation();
                skuRoleRelation.setId(BaseServiceUtils.getId());
                skuRoleRelation.setSkuId(skuId);
                skuRoleRelation.setPartnerRoleId(partnerRoleType);
                skuRoleRelation.setPointPercent(pointPercent);
                //null表示不对上限进行设置
                if(pointLimit != null){
                    skuRoleRelation.setPointLimit(pointLimit);
                }
                if(pointLimit != null && pointLimit == 0L){
                    //0表示清空上限
                    skuRoleRelation.setPointLimit(null);
                }
                skuRoleRelation.setCreateTime(now);
                skuRoleRelation.setUpdateTime(now);
                skuRoleRelationList.add(skuRoleRelation);
            }else {
                //更新合伙人商品积分配置
                SkuRoleRelation skuRoleRelation = skuRoleRelations.get(0);
                skuRoleRelation.setPointPercent(pointPercent);
                //null表示不对上限进行设置
                if(pointLimit != null){
                    skuRoleRelation.setPointLimit(pointLimit);
                }
                if(pointLimit != null && pointLimit == 0L){
                    //0表示清空上限
                    skuRoleRelation.setPointLimit(null);
                }
                skuRoleRelation.setUpdateTime(now);
                skuRoleRelationMapper.updateByPrimaryKey(skuRoleRelation);
                redisCacheUtil.deleteAll(Constant.REDIS_KEY_MINI_PRODUCT_SKU_POINT + skuRoleRelation.getSkuId());
            }
            //更新sku积分状态生效，更新时间，便于排序
            SkuOfferingInfo skuOfferingInfo = new SkuOfferingInfo();
            skuOfferingInfo.setId(skuId);
            if(available){
                skuOfferingInfo.setPointStatus(PointStatusEnum.AVAILABLE.code);
            }
            skuOfferingInfo.setUpdateTime(now);
            skuOfferingInfoMapper.updateByPrimaryKeySelective(skuOfferingInfo);
        }
        if(!skuRoleRelationList.isEmpty()){
            skuRoleRelationMapper.batchInsert(skuRoleRelationList);
        }

    }

    private void checkParam(List<SkuPartnerRoleDTO> itemList,boolean checkSupplierAssociate) {
        for (SkuPartnerRoleDTO item : itemList) {
            if(item.getPointPercent().doubleValue() > 100){
                throw new BusinessException(StatusContant.PONIT_PERCENT_ERROR);
            }
        }
        List<String> skuIdList = itemList.stream().map(i -> {
            return i.getSkuId();
        }).collect(Collectors.toList());
        SkuOfferingInfoExample example = new SkuOfferingInfoExample().createCriteria().andIdIn(skuIdList).example();
        List<SkuOfferingInfo> skuOfferingInfos = skuOfferingInfoMapper.selectByExample(example);
        Set<Integer> pointStatusSet = new HashSet<>();
        skuOfferingInfos.forEach(s -> {
            pointStatusSet.add(s.getPointStatus());
        });
        if(pointStatusSet.size() > 1){
            throw new BusinessException(StatusContant.BATCH_POINT_STATUS_ERROR);
        }
        if(checkSupplierAssociate){
            //必须先关联积分供应商，才能生效
            for (SkuOfferingInfo skuOfferingInfo : skuOfferingInfos) {
                String offeringCode = skuOfferingInfo.getOfferingCode();
                String offeringName = skuOfferingInfo.getOfferingName();
                SupplierAssociateProductExample associateProductExample = new SupplierAssociateProductExample();
                associateProductExample.createCriteria().andProductIdEqualTo(offeringCode).andDeleteTimeIsNull();
                List<SupplierAssociateProduct> associateProducts = supplierAssociateProductMapper.selectByExample(associateProductExample);
                if(associateProducts.isEmpty()){
                    throw new BusinessException(StatusContant.PARAM_ERROR,"规格编码:"+offeringCode+",规格名称:"+offeringName+"对应商品未绑定积分供应商，请绑定后再操作");
                }
            }
        }

        Integer pointStatus = new ArrayList<>(pointStatusSet).get(0);
        if(pointStatus.intValue() != PointStatusEnum.PAUSE.code.intValue()){
            throw new BusinessException(StatusContant.MUST_PAUSE);
        }
        List<Integer> partnerRoleTypeList = itemList.stream().map(i -> {
            return i.getPartnerRoleId();
        }).collect(Collectors.toList());
        //查询合伙人的角色是否合法
        List<Integer> leagalRoleTypeList = Arrays.stream(RetailUserRoleEnum.values()).map(r -> {
            return r.code;
        }).collect(Collectors.toList());
        if(!leagalRoleTypeList.containsAll(partnerRoleTypeList)){
            throw new BusinessException(StatusContant.ROLE_ERROR);
        }
    }

    @Override
    @DS("query")
    public BaseAnswer<PageData<ProductFrontListVO>> getProductFrontList(ProductFrontListParam param, String userId) {
        String queryParam = param.getQueryParam();
        Integer orderType = param.getOrderType();
        Integer orderSort = param.getOrderSort();
        Integer pageSize = param.getPageSize();
        Integer pageNum = param.getPageNum();
        UserRetail userRetail = userRetailMapper.selectByPrimaryKey(userId);
        if(userRetail == null){
            throw new BusinessException(StatusContant.USER_NOT_FOUNT);
        }
        Integer partnerRoleId = userRetail.getRoleType();
        String provinceCode = userRetail.getProvinceCode();
        String cityCode = userRetail.getCityCode();
        if(RetailUserRoleEnum.fromCode(partnerRoleId) == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"当前合伙人角色无法获取商品列表");
        }
        List<ProductFrontListDO> list = spuOfferingInfoMapperExt.pageQueryProductFrontList(provinceCode,cityCode,queryParam,orderType,orderSort,(pageNum-1)*pageSize,pageSize,partnerRoleId);
        List<ProductFrontListVO> data = list.stream().map(d -> {
            if(d.getSkuQuantity() == null){
                d.setSkuQuantity(0);
            }
            //最大积分不能超过积分限制
            Long pointLimt = d.getPointLimit();
            Long point = d.getPoint();
            if(pointLimt != null && point != null && point > pointLimt){
                d.setPoint(pointLimt);
            }
            ProductFrontListVO vo = new ProductFrontListVO();
            BeanUtils.copyProperties(d, vo);
            return vo;
        }).collect(Collectors.toList());
        int count = spuOfferingInfoMapperExt.pageCountProductFrontList(provinceCode,cityCode,queryParam,orderType,orderSort,(pageNum-1)*pageSize,pageSize);
        //提前获取分享链接,提高用户体验
        CountDownLatch countDownLatch = new CountDownLatch(data.size());
        for (ProductFrontListVO product : data) {
            String spuOfferingCode = product.getSpuOfferingCode();
            executorService.execute(() -> {
                try {
                    BaseAnswer<ShareUrlVO> baseAnswer = getShareUrl(spuOfferingCode, userId);
                    String shareUrl = baseAnswer.getData().getUrl();
                    if(StringUtils.isNotEmpty(shareUrl)){
                        product.setShareUrl(shareUrl);
                    }
                } catch (Exception e) {
                    log.error("商品列表获取商品spu:{}分享链接发生异常",spuOfferingCode,e);
                }finally {
                    countDownLatch.countDown();
                }
            });
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        PageData<ProductFrontListVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        pageData.setCount(count);
        pageData.setData(data);
        return BaseAnswer.success(pageData);
    }

    @Override
    @DS("query")
    public BaseAnswer<ShareUrlVO> getShareUrl(String spuCode, String userId) {
        UserRetail userRetail = userRetailMapper.selectByPrimaryKey(userId);
        if(userRetail  == null){
            throw new BusinessException(StatusContant.USER_NOT_FOUNT);
        }
        //通过商城提供的接口，查询商品的分享链接
        SearchMallLinkRequest request = new SearchMallLinkRequest();
        int roleType = userRetail.getRoleType().intValue();
        RetailUserRoleEnum roleTypeEnum = RetailUserRoleEnum.fromCode(roleType);
        if(roleTypeEnum == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"当前合伙人角色无法获取分享链接");
        }
        if(roleType == RetailUserRoleEnum.FIRST_LEVEL_DISTRIBUTOR.code.intValue() || roleType == RetailUserRoleEnum.SECOND_LEVEL_DISTRIBUTOR.code.intValue()){
            request.setLoginRole("1");
        }
        if(roleType == RetailUserRoleEnum.AGENT.code.intValue()){
            request.setLoginRole("3");
        }
//        if(roleType == RetailUserRoleEnum.MANAGER.code.intValue()){
//            request.setLoginRole("2");
//        }
        //商品分享链接
        request.setLinkType("1");
        request.setSessionVerification("2");
        request.setSpuCode(spuCode);
        List<SearchMallLinkRequest.UserInfo> userInfo = new ArrayList<>();
        SearchMallLinkRequest.UserInfo  userInfo1= new SearchMallLinkRequest.UserInfo();
        userInfo1.setUserID(userRetail.getUserId());
        userInfo.add(userInfo1);
        request.setUserInfo(userInfo);
        IotSearchMallLinkRequest innerRequest = new IotSearchMallLinkRequest();
        innerRequest.setBeId(userRetail.getBeId());
        BeanUtils.copyProperties(request,innerRequest);

        ShareUrlVO vo = new ShareUrlVO();
        try {
            BaseAnswer<String> shareUrl = iotFeignClient.getShareUrl(innerRequest);
            if(!ExcepStatus.getSuccInstance().getStateCode().equals(shareUrl.getStateCode())){
                log.error("iotFeignClient.getShareUrl失败:{},,spuCode:{}",shareUrl.getMessage(),spuCode);
                throw new BusinessException(StatusContant.INTERNAL_ERROR,"获取分享链接失败");
            }
            vo.setUrl(shareUrl.getData());
        } catch (Exception e) {
            log.error("iotFeignClient.getShareUrl发生异常,spuCode:{}",spuCode,e);
            throw new BusinessException(StatusContant.INTERNAL_ERROR,"获取分享链接异常");
        }
        return BaseAnswer.success(vo);
    }

    @Override
    @DS("query")
    public BaseAnswer<PointPercentBySkuAndRoleVO> getPointPercentBySkuAndRole(PointPercentBySkuAndRoleParam param) {
        log.info("getPointPercentBySkuAndRole入参:{}", JSON.toJSONString(param));
        Integer roleType = param.getRoleType();
        String skuId = param.getSkuId();
        SkuOfferingInfo skuOfferingInfo = skuOfferingInfoMapper.selectByPrimaryKey(skuId);
        SkuRoleRelationExample example = new SkuRoleRelationExample().createCriteria().andSkuIdEqualTo(skuId).andPartnerRoleIdEqualTo(roleType).example();
        List<SkuRoleRelation> skuRoleRelationList = skuRoleRelationMapper.selectByExample(example);
        if(skuRoleRelationList.isEmpty()){
            return BaseAnswer.success(null);
        }
        SkuRoleRelation skuRoleRelation = skuRoleRelationList.get(0);
        PointPercentBySkuAndRoleVO vo = new PointPercentBySkuAndRoleVO();
        BeanUtils.copyProperties(skuRoleRelation,vo);
        boolean available = skuOfferingInfo.getPointStatus().intValue() == PointStatusEnum.AVAILABLE.code ? true : false;
        vo.setPointAvailable(available);
        return BaseAnswer.success(vo);
    }

    @Override
    @DS("save")
    public BaseAnswer<Void> clearSkuRoleRelation() {
        skuRoleRelationMapper.deleteByExample(new SkuRoleRelationExample());
        redisCacheUtil.deleteAll(Constant.REDIS_KEY_MINI_PRODUCT_SKU_POINT);
        return BaseAnswer.success(null);
    }

    @Override
    @DS("save")
    public BaseAnswer<Void> addSkuRoleRelation(String skuId) {
        log.info("开始添加SkuRoleRelation,skuId:{}",skuId);
        RetailUserRoleEnum[] values = RetailUserRoleEnum.values();
        List<Integer> allRole = Arrays.stream(values).map(r -> {
            return r.code;
        }).collect(Collectors.toList());
        Date now = new Date();
        List<SkuRoleRelation> skuRoleRelationList = new ArrayList<>();
        for (Integer partnerRoleType : allRole) {
            SkuRoleRelationExample example = new SkuRoleRelationExample().createCriteria().andSkuIdEqualTo(skuId).andPartnerRoleIdEqualTo(partnerRoleType).example();
            List<SkuRoleRelation> skuRoleRelations = skuRoleRelationMapper.selectByExample(example);
            if(!skuRoleRelations.isEmpty()){
                log.info("SkuRoleRelation已存在，无需添加");
                continue;
            }
            SkuRoleRelation skuRoleRelation = new SkuRoleRelation();
            skuRoleRelation.setId(BaseServiceUtils.getId());
            skuRoleRelation.setSkuId(skuId);
            skuRoleRelation.setPartnerRoleId(partnerRoleType);
            skuRoleRelation.setPointPercent(Double.valueOf(0));
            skuRoleRelation.setPointLimit(null);
            skuRoleRelation.setCreateTime(now);
            skuRoleRelation.setUpdateTime(now);
            skuRoleRelationList.add(skuRoleRelation);
        }
        skuRoleRelationMapper.batchInsert(skuRoleRelationList);
        log.info("完成添加SkuRoleRelation,skuId:{},数量:{}",skuId,skuRoleRelationList.size());
        return BaseAnswer.success(null);
    }

    @Override
    @DS("save")
    public BaseAnswer<Void> deleteSkuRoleRelation(String skuId) {
        SkuRoleRelationExample example = new SkuRoleRelationExample().createCriteria().andSkuIdEqualTo(skuId).example();
        skuRoleRelationMapper.deleteByExample(example);
        redisCacheUtil.deleteAll(Constant.REDIS_KEY_MINI_PRODUCT_SKU_POINT + skuId);
        return BaseAnswer.success(null);
    }

    @Override
    @DS("save")
    public BaseAnswer<Void> configPointPercent(ConfigPointParam param) {
        List<SkuPartnerRoleDTO> itemList = param.getItemList();
        checkParam(itemList,false);
        configPointPercentOrLimit(itemList,false);
        return BaseAnswer.success(null);
    }

    @Override
    @DS("query")
    public BaseAnswer<PageData<SkuPointListVO>> getSkuPointList(String spuCode, String userId, Integer pageNum, Integer pageSize) {
        UserRetail userRetail = userRetailMapper.selectByPrimaryKey(userId);
        if(userRetail  == null){
            throw new BusinessException(StatusContant.USER_NOT_FOUNT);
        }
        String provinceCode = userRetail.getProvinceCode();
        String cityCode = userRetail.getCityCode();
        Integer roleType = userRetail.getRoleType();
        PageHelper.startPage(pageNum,pageSize);
        List<SkuPointListDO> list = skuOfferingInfoMapperExt.getSkuPointList(spuCode,roleType,provinceCode,cityCode);
        PageInfo<SkuPointListDO> pageInfo = new PageInfo<>(list);
        List<SkuPointListVO> collect = list.stream().map(s -> {
            SkuPointListVO vo = new SkuPointListVO();
            BeanUtils.copyProperties(s, vo);
            //积分不可超过设置的上限
            Long pointLimit = s.getPointLimit() == null ? Long.MAX_VALUE : s.getPointLimit();
            Double pointPercent = s.getPointPercent();
            double v = vo.getPrice() * pointPercent / 100;
            Long calculatedPoint = (long)v;
            vo.setPoint(calculatedPoint > pointLimit ? pointLimit : calculatedPoint);
            return vo;
        }).collect(Collectors.toList());
        PageData<SkuPointListVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        pageData.setCount(pageInfo.getTotal());
        pageData.setData(collect);
        return BaseAnswer.success(pageData);
    }

    @Override
    @DS("save")
    public BaseAnswer<Void> pausePoint(String skuOfferingCode) {
        log.info("pausePoint入参:{}",skuOfferingCode);
        SkuOfferingInfoExample example = new SkuOfferingInfoExample().createCriteria().andOfferingCodeEqualTo(skuOfferingCode).example();
        List<SkuOfferingInfo> skuOfferingInfos = skuOfferingInfoMapper.selectByExample(example);
        if(skuOfferingInfos.isEmpty()){
            log.error("skucode:{}对应的sku不存在",skuOfferingCode);
            return BaseAnswer.success(null);
        }
        SkuOfferingInfo skuOfferingInfo = skuOfferingInfos.get(0);
        skuOfferingInfo.setUpdateTime(new Date());
        skuOfferingInfo.setPointStatus(PointStatusEnum.PAUSE.code);
        skuOfferingInfoMapper.updateByPrimaryKey(skuOfferingInfo);
        return BaseAnswer.success(null);
    }


}
