package com.chinamobile.retail.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.ExcelUtils;
import com.chinamobile.retail.dao.ext.Order2cAtomInfoMapperExt;
import com.chinamobile.retail.dao.ext.SkuOfferingInfoMapperExt;
import com.chinamobile.retail.enums.OrderStatusInnerEnum;
import com.chinamobile.retail.exception.StatusContant;
import com.chinamobile.retail.pojo.dto.ExportDistributeOrderDTO;
import com.chinamobile.retail.pojo.dto.OrderExportListDTO;
import com.chinamobile.retail.pojo.mapper.ExportDistributeOrderDO;
import com.chinamobile.retail.pojo.mapper.OrderBackListDO;
import com.chinamobile.retail.pojo.mapper.OrderExportListDO;
import com.chinamobile.retail.pojo.param.ExportDistributeOrderParam;
import com.chinamobile.retail.pojo.param.OrderBackListParam;
import com.chinamobile.retail.pojo.vo.OrderBackListVO;
import com.chinamobile.retail.service.OrderPointService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * created by liuxiang on 2022/8/31 14:35
 */
@Component
@Slf4j
public class OrderPointServiceImpl implements OrderPointService {

    @Resource
    private Order2cAtomInfoMapperExt order2cAtomInfoMapperExt;

    @Resource
    private SkuOfferingInfoMapperExt skuOfferingInfoMapperExt;

    @Override
    @DS("query")
    public BaseAnswer<PageData<OrderBackListVO>> getOrderBackList(OrderBackListParam param) {
        String startTimeStr = param.getStartTime();
        String endTimeStr = param.getEndTime();
        try {
            startTimeStr = StringUtils.isEmpty(startTimeStr) ? null : DateTimeUtil.getDbTimeStr(DateTimeUtil.getFormatDate(startTimeStr, DateTimeUtil.DEFAULT_DATE_DEFAULT));
            endTimeStr = StringUtils.isEmpty(endTimeStr) ? null : DateTimeUtil.getDbTimeStr(DateTimeUtil.getFormatDate(endTimeStr, DateTimeUtil.DEFAULT_DATE_DEFAULT));
        } catch (ParseException e) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"时间格式错误");
        }
        // 商品名称搜索支持反斜杠适配
        if(param.getSkuOfferingName() != null){
            param.setSkuOfferingName(param.getSkuOfferingName().replaceAll("\\\\","\\\\\\\\"));
        }
        if(param.getSpuOfferingName() != null){
            param.setSpuOfferingName(param.getSpuOfferingName().replaceAll("\\\\","\\\\\\\\"));
        }
        String orderId = param.getOrderId();
        Integer orderStatus = param.getOrderStatus();
        String partnerName = param.getPartnerName();
        String phone = param.getPhone();
        String skuOfferingName = param.getSkuOfferingName();
        String spuOfferingName = param.getSpuOfferingName();
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        List<OrderBackListDO> list = order2cAtomInfoMapperExt.pageQueryOrderBackList(orderId,startTimeStr,endTimeStr,orderStatus,partnerName,phone,skuOfferingName,spuOfferingName,(pageNum - 1)*pageSize,pageSize);
        log.info("查出条数:{}",list.size());
        Integer count = order2cAtomInfoMapperExt.pageCountOrderBackList(orderId, startTimeStr, endTimeStr, orderStatus, partnerName, phone, skuOfferingName, spuOfferingName);
        List<OrderBackListVO> collect = list.stream().map(o -> {
            OrderBackListVO vo = new OrderBackListVO();
            BeanUtils.copyProperties(o, vo);
            try {
                vo.setCreateTime(DateTimeUtil.formatDate(DateTimeUtil.getFormatDate(vo.getCreateTime(),DateTimeUtil.DB_TIME_STR),DateTimeUtil.DEFAULT_DATE_DEFAULT) );
            } catch (ParseException e) {
                log.error("日期格式错误",e);
            }
            vo.setOrderStatus(OrderStatusInnerEnum.getDescribe(o.getOriginalOrderStatus()));
            //单独查询实际产品名称，比子查询更快
            vo.setRealProductName(skuOfferingInfoMapperExt.getRealProductName(o.getSkuOfferingCode()));
            return vo;
        }).collect(Collectors.toList());
        PageData<OrderBackListVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        pageData.setCount(count);
        pageData.setData(collect);
        return BaseAnswer.success(pageData);
    }

    @Override
    @DS("query")
    public void exportOrderBackList(OrderBackListParam param) {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        try {
            String startTimeStr = param.getStartTime();
            String endTimeStr = param.getEndTime();
            try {
                startTimeStr = StringUtils.isEmpty(startTimeStr) ? null : DateTimeUtil.getDbTimeStr(DateTimeUtil.getFormatDate(startTimeStr, DateTimeUtil.DEFAULT_DATE_DEFAULT));
                endTimeStr = StringUtils.isEmpty(endTimeStr) ? null : DateTimeUtil.getDbTimeStr(DateTimeUtil.getFormatDate(endTimeStr, DateTimeUtil.DEFAULT_DATE_DEFAULT));
            } catch (ParseException e) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"时间格式错误");
            }
            //默认导出当前自然月的数据
            if(StringUtils.isEmpty(startTimeStr) && StringUtils.isEmpty(endTimeStr)){
                Date now = new Date();
                Date monthStart = DateTimeUtil.getMonthStart(now, 0);
                startTimeStr = DateTimeUtil.getDbTimeStr(monthStart);
                endTimeStr = DateTimeUtil.getDbTimeStr(now);
            }

            String orderId = param.getOrderId();
            Integer orderStatus = param.getOrderStatus();
            String partnerName = param.getPartnerName();
            String phone = param.getPhone();
            String skuOfferingName = param.getSkuOfferingName();
            String spuOfferingName = param.getSpuOfferingName();
                List<OrderExportListDO> list = order2cAtomInfoMapperExt.getOrderExportList(orderId,orderStatus,startTimeStr,endTimeStr,partnerName,phone,skuOfferingName,spuOfferingName);
                if(list.isEmpty()){
                    throw new BusinessException(StatusContant.NO_DATA);
                }
                List<OrderExportListDTO> collect = list.stream().map(o -> {
                    OrderExportListDTO dto = new OrderExportListDTO();
                    BeanUtils.copyProperties(o, dto);
                    try {
                        dto.setCreateTime(DateTimeUtil.formatDate(DateTimeUtil.getFormatDate(dto.getCreateTime(), DateTimeUtil.DB_TIME_STR), DateTimeUtil.DEFAULT_DATE_DEFAULT));
                    } catch (ParseException e) {
                        log.error("日期格式错误",e);
                    }
                    dto.setOrderStatus(OrderStatusInnerEnum.getDescribe(o.getOriginalOrderStatus()));
                    dto.setAvailable("1".equals(o.getAvailable()) ? "可兑换" : "不可兑换");
                    return dto;
                }).collect(Collectors.toList());
                Workbook workBook = ExcelUtils.exportSimpleExcel("order-list", OrderExportListDTO.class, collect);

                if (null != workBook ) {
                    workBook.write(response.getOutputStream());// 将数据流写到响应中
                    workBook.close();
                }
            } catch (Exception e) {
            //便于前端拿到异常，将异常信息放入header
            try {
                if(e instanceof BusinessException){
                    BusinessException businessException = (BusinessException) e;
                    response.addHeader("stateCode",businessException.getStatus().getStateCode());
                    response.addHeader("message", URLEncoder.encode(businessException.getStatus().getMessage(), "UTF-8"));
                }else {
                    response.addHeader("stateCode",BaseErrorConstant.INTERNAL_ERROR.getStateCode());
                    response.addHeader("message", URLEncoder.encode(BaseErrorConstant.INTERNAL_ERROR.getMessage(),"UTF-8"));
                }
            } catch (UnsupportedEncodingException ex) {
                ex.printStackTrace();
            }
        }
    }

    @Override
    @DS("query")
    public void exportDistributeOrder(ExportDistributeOrderParam param, HttpServletResponse response) {
        log.info("导出分销订单列表开始:{}", JSON.toJSONString(param));
        String startTimeStr = param.getStartTime();
        String endTimeStr = param.getEndTime();
        try {
            startTimeStr = StringUtils.isEmpty(startTimeStr) ? null : DateTimeUtil.getDbTimeStr(DateTimeUtil.getFormatDate(startTimeStr, DateTimeUtil.DEFAULT_DATE_DEFAULT));
            endTimeStr = StringUtils.isEmpty(endTimeStr) ? null : DateTimeUtil.getDbTimeStr(DateTimeUtil.getFormatDate(endTimeStr, DateTimeUtil.DEFAULT_DATE_DEFAULT));
        } catch (ParseException e) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"时间格式错误");
        }
        //默认导出当前自然月的数据
        if(StringUtils.isEmpty(startTimeStr) && StringUtils.isEmpty(endTimeStr)){
            Date now = new Date();
            Date monthStart = DateTimeUtil.getMonthStart(now, 0);
            startTimeStr = DateTimeUtil.getDbTimeStr(monthStart);
            endTimeStr = DateTimeUtil.getDbTimeStr(now);
        }
        List<ExportDistributeOrderDO> list = order2cAtomInfoMapperExt.getExportDistributeOrderList(startTimeStr,endTimeStr);
        log.info("导出分销订单列表查询完毕，数量:{}",list.size());
        //按照订单对分销员分组，一个订单可能同时对应一级和二级分销员
        Map<String,List<ExportDistributeOrderDO>> orderIdDataMap = new HashMap<>();
        list.forEach(d -> {
            String orderId = d.getOrderId();
            List<ExportDistributeOrderDO> dataList = orderIdDataMap.get(orderId);
            if(CollectionUtils.isEmpty(dataList)){
                dataList = new ArrayList<>();
                orderIdDataMap.put(orderId,dataList);
            }
            dataList.add(d);
        });
        //拼装导出数据
        List<ExportDistributeOrderDTO> exportList = new ArrayList<>();
        for (Map.Entry<String, List<ExportDistributeOrderDO>> entry : orderIdDataMap.entrySet()) {
            List<ExportDistributeOrderDO> dataList = entry.getValue();
            //订单基本信息是相同的，只有分销员信息不同(如果有多级分销员)
            ExportDistributeOrderDO orderDO = dataList.get(0);
            ExportDistributeOrderDTO dto = new ExportDistributeOrderDTO();
            BeanUtils.copyProperties(orderDO,dto);
            //时间格式转换
            try {
                dto.setCreateTime(DateTimeUtil.formatDate(DateTimeUtil.getFormatDate(dto.getCreateTime(), DateTimeUtil.DB_TIME_STR), DateTimeUtil.DEFAULT_DATE_DEFAULT));
            } catch (ParseException e) {
                log.error("时间格式转换错误",e);
            }
            //设置多级分销员信息
            dataList.forEach(d -> {
                String distributorLevel = d.getDistributorLevel();
                if("1".equals(distributorLevel)){
                    //一级分销
                    dto.setDistributorPhoneOne(d.getDistributorPhone());
                }
                if("2".equals(distributorLevel)){
                    //二级分销
                    dto.setDistributorPhoneTwo(d.getDistributorPhone());
                }
            });
            exportList.add(dto);
        }
        Workbook workBook = ExcelUtils.exportSimpleExcel("order-list", ExportDistributeOrderDTO.class, exportList);
        ExcelUtils.downLoadExcel("distribute-order-list", response, workBook);
        log.info("导出分销订单列表完毕");
    }

}
