package com.chinamobile.retail.service.impl;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.common.utils.BaseUtils;
import com.chinamobile.iot.sc.enums.log.EstewardOperateEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.retail.constant.EstewardProductAuditStatusEnum;
import com.chinamobile.retail.constant.EstewardProductStatusEnum;
import com.chinamobile.retail.dao.*;
import com.chinamobile.retail.dao.ext.EstewardProductMapperExt;
import com.chinamobile.retail.pojo.entity.*;
import com.chinamobile.retail.pojo.param.EstewardProductParam;
import com.chinamobile.retail.pojo.vo.EstewardProductVO;
import com.chinamobile.retail.service.EstewardProductService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * EstewardProductServiceImpl 类实现了 EstewardProductService 接口，
 * 提供了对商客商品的增删改查、审核、上下线等操作的具体实现。
 * 包含事务管理、数据校验和业务逻辑处理。
 */
@Service
public class EstewardProductServiceImpl implements EstewardProductService {

    private static final Logger logger = LoggerFactory.getLogger(EstewardProductServiceImpl.class);

    @Resource
    private EstewardProductMapper mapper;
    @Resource
    private LogService logService;

    @Resource
    private EstewardProductMapperExt mapperExt;
    @Resource
    EstewardDirectoryMapper estewardDirectoryMapper;
    @Resource
    SpuOfferingInfoMapper spuOfferingInfoMapper;
    @Resource
    EstewardProductDirectoryMapper estewardProductDirectoryMapper;
    @Resource
    UserMapper userMapper;

    /**
     * 获取商客商品分页列表
     *
     * @param param 查询参数，包含分页信息和筛选条件
     * @return 分页数据，包含商品列表和总数
     */
    @Override
    public PageData<EstewardProductVO> getList(EstewardProductParam param) {
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        if (pageNum < 0 || pageSize < 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "页码和每页数量必须大于0");
        }
        PageData<EstewardProductVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        PageHelper.startPage(pageNum, pageSize);

        List<EstewardProductVO> entityList = mapperExt.getList(param);

        PageInfo<EstewardProductVO> pageInfo = new PageInfo<>(entityList);
        pageData.setCount(pageInfo.getTotal());
        pageData.setData(entityList);
        for (EstewardProductVO estewardProductVO : entityList) {
            // 设置状态描述，便于前端展示
            estewardProductVO.setStatusDesc(EstewardProductStatusEnum.fromCode(estewardProductVO.getStatus()));
            estewardProductVO
                    .setAuditStatusDesc(EstewardProductAuditStatusEnum.fromCode(estewardProductVO.getAuditStatus()));
        }
        if (param.getId() != null) {
            // 添加日志
            String content = "【商品查看】\n" + "商品名称 " + entityList.get(0).getProductName() + "\n" + "商品SPU " + entityList.get(0).getSpuCode();
            logService.recordOperateLog(ModuleEnum.ESTEWARD_MANAGE.code, EstewardOperateEnum.PRODUCT_OPERATE.code,
                    content);
        }
        return pageData;
    }

    /**
     * 验证导入的实体数据
     *
     * @param entity 要验证的实体
     * @return 验证通过返回true，否则返回false
     */
    private boolean validateImportEntity(EstewardProduct entity) {
        // 根据实际业务需求添加验证逻辑
        // 如果验证不通过，可以记录日志
        return true;
    }

    private String getEditContent(EstewardProduct oldInfo, List<String> oldCategoryIds, EstewardProductParam newInfo) {
        StringBuilder content = new StringBuilder();
        content.append("【商品编辑】");
        if (!Objects.equals(oldInfo.getProductName(), newInfo.getProductName())) {
            content.append("\n").append("商品名称").append("由").append(oldInfo.getProductName()).append("修改为")
                    .append(newInfo.getProductName());
        }
        // 展示类型
        if (!Objects.equals(oldInfo.getShowType(), newInfo.getShowType())) {
            content.append("\n").append("展示类型").append("由").append(oldInfo.getShowType() == 1 ? "外链" : "详情页")
                    .append("修改为").append(newInfo.getShowType() == 1 ? "外链" : "详情页");
        }
        // 展示目录
        if (!Objects.equals(oldCategoryIds, newInfo.getCategoryIds())) {
            content.append("\n").append("展示目录").append("由").append(getCategoryNames(oldCategoryIds))
                    .append("修改为").append(getCategoryNames(newInfo.getCategoryIds()));
        }

        return content.toString();
    }

    private String getCategoryNames(List<String> categoryIds) {
        if (categoryIds == null || categoryIds.isEmpty()) {
            return "无";
        }
        return categoryIds.stream()
                .map(id -> estewardDirectoryMapper.selectByPrimaryKey(id).getName())
                .collect(Collectors.joining(","));
    }

    /**
     * 插入一条商客商品记录
     *
     * @param record 商客商品参数
     * @param userId 操作用户ID
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer insertOne(EstewardProductParam record, String userId) {
        // 校验必填字段
        if (record.getShowType() == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "展示类型不能为空");
        }
        EstewardProduct record1 = new EstewardProduct();
        BaseUtils.copyNonNullProperties(record, record1);
        record1.setId(BaseServiceUtils.getId());

        // 根据展示类型进行不同的字段校验
        if (record.getShowType() == 1) {
            if (record.getProductName() == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "商品名称不能为空");
            }

            if (record.getProductLink() == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "商品链接不能为空");
            }

            if (record.getChannel() == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "渠道不能为空");
            }
            if (record.getBusinessCategory() == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "业务分类不能为空");
            }
            if (record.getSubBusinessType() == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "子业务类型不能为空");
            }

        } else if (record.getShowType() == 2) {
            if (record.getSpuCode() == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "spuCode不能为空");
            }
            // 验证 spuCode 是否存在
            List<SpuOfferingInfo> spuOfferInfos = spuOfferingInfoMapper.selectByExample(
                    new SpuOfferingInfoExample().createCriteria().andOfferingCodeEqualTo(record.getSpuCode())
                            .example());
            if (spuOfferInfos.size() == 0) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "spuCode不存在");
            }
            //验证spu是否当前有生效的产品
//            List<EstewardProduct> estewardProducts = mapper.selectByExample(new EstewardProductExample().createCriteria().andSpuCodeEqualTo(record.getSpuCode())
//                            .andDeleteTimeIsNull()
//                            .andStatusEqualTo(EstewardProductStatusEnum.PUBLISHED.getStatus())
//                            .example());
//            if (estewardProducts.size() > 0) {
//                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "spuCode已存在上线产品");
//            }
        }

        // 插入商品目录关联关系
        List<EstewardDirectory> estewardDirectoryList = estewardDirectoryMapper.selectByExample(
                new EstewardDirectoryExample().createCriteria().andIdIn(record.getCategoryIds()).example());
        if (estewardDirectoryList.size() != record.getCategoryIds().size()) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "目录id不存在");
        }

        estewardProductDirectoryUpdate(record1.getId(), estewardDirectoryList);

        // 设置创建用户和时间
        User user = userMapper.selectByPrimaryKey(userId);
        record1.setStatus(record.getStatus());
        record1.setAuditStatus(record.getStatus() == EstewardProductStatusEnum.UNPUBLISHED.getStatus()
                ? EstewardProductAuditStatusEnum.DRAFT.getStatus()
                : EstewardProductAuditStatusEnum.IN_PROGRESS.getStatus());
        record1.setCreateUserId(userId);
        record1.setCreateTime(new Date());
        record1.setUpdateTime(new Date());
        mapper.insertSelective(record1);

        String content = String.format("【商品新建】\n商品名称：%s\n展示类型：%s\n所属目录：%s",
                record.getProductName(),
                record.getShowType() == 1 ? "外链" : "详情页",
                getCategoryNames(record.getCategoryIds()));

        logService.recordOperateLog(ModuleEnum.ESTEWARD_MANAGE.code, EstewardOperateEnum.PRODUCT_OPERATE.code, content);
        return BaseAnswer.success(null);
    }

    @Override
    @Transactional
    public BaseAnswer updateOne(EstewardProductParam record) {
        EstewardProduct record1 = mapper.selectByPrimaryKey(record.getId());
        if (record1 == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "商客商品id不存在");
        }
        // 对于待审核/已下线/已驳回状态下的商品，可编辑
        if (!Objects.equals(record.getStatus(), EstewardProductStatusEnum.UNPUBLISHED.getStatus())
                && !Objects.equals(record.getStatus(), EstewardProductStatusEnum.OFFLINE.getStatus())
                && !Objects.equals(record.getStatus(), EstewardProductStatusEnum.REJECTED.getStatus())) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "只能编辑待审核/已下线/已驳回状态下的商品");
        }
        List<String> oldCategoryIds = estewardProductDirectoryMapper
                .selectByExample(new EstewardProductDirectoryExample().createCriteria()
                        .andProductIdEqualTo(record.getId()).example())
                .stream()
                .map(EstewardProductDirectory::getDirectoryId)
                .collect(Collectors.toList());
        String content = getEditContent(record1, oldCategoryIds, record);
        // 只复制非null属性，防止覆盖已有数据
        BaseUtils.copyNonNullProperties(record, record1);

        // 验证spuCode是否存在
        if (record.getSpuCode() != null) {
            List<SpuOfferingInfo> spuOfferInfos = spuOfferingInfoMapper.selectByExample(new SpuOfferingInfoExample()
                    .createCriteria().andOfferingCodeEqualTo(record.getSpuCode()).example());
            if (spuOfferInfos.size() == 0) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "spuCode不存在");
            }
            //验证spu是否当前有生效的产品
//            List<EstewardProduct> estewardProducts = mapper.selectByExample(new EstewardProductExample().createCriteria().andSpuCodeEqualTo(record.getSpuCode())
//                    .andDeleteTimeIsNull()
//                    .andStatusEqualTo(EstewardProductStatusEnum.PUBLISHED.getStatus())
//                    .example());
//            if (estewardProducts.size() > 0) {
//                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "spuCode已存在上线产品");
//            }
            record1.setSpuCode(record.getSpuCode());
        }

        if (record.getCategoryIds() == null || record.getCategoryIds().size() == 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "商品目录不能为空");
        }
        // 验证目录id是否存在
        List<EstewardDirectory> estewardDirectoryList = estewardDirectoryMapper.selectByExample(
                new EstewardDirectoryExample().createCriteria().andIdIn(record.getCategoryIds()).example());
        if (estewardDirectoryList.size() != record.getCategoryIds().size()) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "目录id不存在");
        } else {
            // 删除目录
            estewardProductDirectoryMapper.deleteByExample(new EstewardProductDirectoryExample().createCriteria()
                    .andProductIdEqualTo(record1.getId()).example());
            // 插入目录
            List<EstewardProductDirectory> estewardProductDirectoryList = new ArrayList<>();
            estewardProductDirectoryUpdate(record1.getId(), estewardDirectoryList);

        }
        // 根据显示类型清空非该类型字段
        if (record.getShowType() != null) {
            if (record.getShowType() == 1) {
                // 显示类型为1时，清空类型2相关字段
                record1.setSpuCode(null);
                record1.setSpuCode(null);
            } else if (record.getShowType() == 2) {
                // 显示类型为2时，清空类型1相关字段
//                record1.setProductName(null);
                record1.setProductLink(null);
            }
        }

        record1.setUpdateTime(new Date());
        mapper.updateByPrimaryKey(record1);
        logService.recordOperateLog(ModuleEnum.ESTEWARD_MANAGE.code, EstewardOperateEnum.PRODUCT_OPERATE.code, content);
        return BaseAnswer.success(null);
    }

    private void estewardProductDirectoryUpdate(String productId , List<EstewardDirectory> estewardDirectoryList) {
        List<EstewardProductDirectory> estewardProductDirectoryList = new ArrayList<>();
        for (EstewardDirectory estewardDirectory : estewardDirectoryList) {
            EstewardProductDirectory estewardProductDirectory = new EstewardProductDirectory();
            estewardProductDirectory.setId(BaseServiceUtils.getId());
            estewardProductDirectory.setProductId(productId);
            estewardProductDirectory.setDirectoryId(estewardDirectory.getId());
            estewardProductDirectory.setCreateTime(new Date());
            estewardProductDirectory.setUpdateTime(new Date());
            estewardProductDirectory.setIsBottom(estewardDirectory.getLevel() == estewardDirectoryList.size());
            estewardProductDirectoryList.add(estewardProductDirectory);
        }
        estewardProductDirectoryMapper.batchInsert(estewardProductDirectoryList);
    }

    @Override
    @Transactional
    public BaseAnswer audit(List<EstewardProductParam> records) {
        if (records.size() == 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "商客商品id不能为空");
        }

        // 确定审核类型（同意或驳回）
        boolean isApproval = Objects.equals(records.get(0).getAuditStatus(),
                EstewardProductAuditStatusEnum.PASSED.getStatus());
        StringBuilder contentBuilder = new StringBuilder();

        // 根据审核类型设置日志标题
        if (isApproval) {
            contentBuilder.append("【同意");
        } else {
            contentBuilder.append("【驳回");
        }

        // 批量处理标记
        if (records.size() > 1) {
            contentBuilder.append("/批量");
            if (isApproval) {
                contentBuilder.append("同意");
            } else {
                contentBuilder.append("驳回");
            }
        }
        contentBuilder.append("】");

        // 处理所有记录
        for (EstewardProductParam record : records) {
            EstewardProduct record1 = mapper.selectByPrimaryKey(record.getId());
            if (record1 == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "商客商品id不存在");
            }
            if (!Objects.equals(EstewardProductAuditStatusEnum.IN_PROGRESS.getStatus(), record1.getStatus())) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "当前状态不为审核中");
            }
            if (!Objects.equals(record.getAuditStatus(), EstewardProductAuditStatusEnum.DENIED.getStatus())
                    && !Objects.equals(record.getAuditStatus(), EstewardProductAuditStatusEnum.PASSED.getStatus())) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "审核状态不正确");
            }

            // 根据审核类型设置状态
            if (isApproval) {
                record1.setStatus(EstewardProductStatusEnum.PUBLISHED.getStatus());
                record1.setAuditStatus(EstewardProductAuditStatusEnum.PASSED.getStatus());
            } else {
                record1.setStatus(EstewardProductStatusEnum.REJECTED.getStatus());
                record1.setAuditStatus(EstewardProductAuditStatusEnum.DENIED.getStatus());
            }

            record1.setUpdateTime(new Date());
            mapper.updateByPrimaryKey(record1);

            // 添加日志内容
            contentBuilder.append("\n商品名称").append(record1.getProductName() != null ? record1.getProductName() : null);
            contentBuilder.append("\n商品SPU").append(record1.getSpuCode() != null ? record1.getSpuCode() : null);
        }

        // 记录操作日志
        logService.recordOperateLog(ModuleEnum.ESTEWARD_MANAGE.code, EstewardOperateEnum.PRODUCT_OPERATE.code,
                contentBuilder.toString());

        return BaseAnswer.success(null);
    }

    @Override
    @Transactional
    public BaseAnswer offlineEstewardProduct(EstewardProductParam param) {
        // 判断是否存在
        EstewardProduct record = mapper.selectByPrimaryKey(param.getId());
        if (record == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "商客商品id不存在");
        }
        if (!Objects.equals(record.getStatus(), EstewardProductStatusEnum.PUBLISHED.getStatus())) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "商客商品状态不正确");
        }
        record.setStatus(EstewardProductStatusEnum.OFFLINE.getStatus());
        record.setAuditStatus(EstewardProductAuditStatusEnum.DRAFT.getStatus());
        record.setUpdateTime(new Date());
        mapper.updateByPrimaryKey(record);
        String content = "【商品下线】\n" + "商品名称 " + record.getProductName() + "\n" + "商品SPU " + record.getSpuCode();
        logService.recordOperateLog(ModuleEnum.ESTEWARD_MANAGE.code, EstewardOperateEnum.PRODUCT_OPERATE.code, content);
        return BaseAnswer.success(null);
    }

    @Override
    @Transactional
    public BaseAnswer publishEstewardProduct(EstewardProductParam param) {
        // 判断商品是否存在
        EstewardProduct record = mapper.selectByPrimaryKey(param.getId());
        if (record == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "商客商品id不存在");
        }

        // 只有未发布或已下线状态的商品才能发布
        if (!Objects.equals(record.getStatus(), EstewardProductStatusEnum.UNPUBLISHED.getStatus())
                && !Objects.equals(record.getStatus(), EstewardProductStatusEnum.OFFLINE.getStatus())
                && !Objects.equals(record.getStatus(), EstewardProductStatusEnum.REJECTED.getStatus())) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "只有未发布,已下线,驳回状态的商品才能发布");
        }
        // 只复制非null属性，防止覆盖已有数据
        BaseUtils.copyNonNullProperties(param, record);

        // 验证spuCode是否存在
        if (record.getSpuCode() != null) {
            List<SpuOfferingInfo> spuOfferInfos = spuOfferingInfoMapper.selectByExample(new SpuOfferingInfoExample()
                    .createCriteria().andOfferingCodeEqualTo(record.getSpuCode()).example());
            if (spuOfferInfos.size() == 0) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "spuCode不存在");
            }
            //验证spu是否当前有生效的产品
//            List<EstewardProduct> estewardProducts = mapper.selectByExample(new EstewardProductExample().createCriteria().andSpuCodeEqualTo(record.getSpuCode())
//                    .andDeleteTimeIsNull()
//                    .andStatusEqualTo(EstewardProductStatusEnum.PUBLISHED.getStatus())
//                    .example());
//            if (estewardProducts.size() > 0) {
//                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "spuCode已存在上线产品");
//            }
            record.setSpuCode(record.getSpuCode());
        }

        if (param.getCategoryIds() != null && param.getCategoryIds().size() > 0) {
            // 验证目录id是否存在
            List<EstewardDirectory> estewardDirectoryList = estewardDirectoryMapper.selectByExample(
                    new EstewardDirectoryExample().createCriteria().andIdIn(param.getCategoryIds()).example());
            if (estewardDirectoryList.size() != param.getCategoryIds().size()) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "目录id不存在");
            } else {
                // 删除目录
                estewardProductDirectoryMapper.deleteByExample(new EstewardProductDirectoryExample().createCriteria()
                        .andProductIdEqualTo(param.getId()).example());
                // 插入目录

                estewardProductDirectoryUpdate(param.getId(), estewardDirectoryList);


            }
        }

        // 根据显示类型清空非该类型字段
        if (param.getShowType() != null) {
            if (record.getShowType() == 1) {
                // 显示类型为1时，清空类型2相关字段
                record.setSpuCode(null);
                record.setSpuCode(null);
            } else if (record.getShowType() == 2) {
                // 显示类型为2时，清空类型1相关字段
//                record.setProductName(null);
                record.setProductLink(null);
            }
        }

        // 更新商品状态为已发布
        record.setAuditStatus(EstewardProductAuditStatusEnum.IN_PROGRESS.getStatus());
        record.setStatus(EstewardProductStatusEnum.UNDER_REVIEW.getStatus());
        record.setUpdateTime(new Date());
        mapper.updateByPrimaryKey(record);
        String content = "【商品发布】\n" + "商品名称 " + record.getProductName() + "\n" + "商品SPU " + record.getSpuCode();
        logService.recordOperateLog(ModuleEnum.ESTEWARD_MANAGE.code, EstewardOperateEnum.PRODUCT_OPERATE.code, content);
        return BaseAnswer.success(null);
    }

    @Override
    @Transactional
    public BaseAnswer deleteOne(String id) {
        // 判断是否存在
        EstewardProduct record = mapper.selectByPrimaryKey(id);
        if (record == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "商客商品id不存在");
        }
        if (!Objects.equals(record.getStatus(), EstewardProductStatusEnum.UNPUBLISHED.getStatus())
                && !Objects.equals(record.getStatus(), EstewardProductStatusEnum.OFFLINE.getStatus())
                && !Objects.equals(record.getStatus(), EstewardProductStatusEnum.REJECTED.getStatus())) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "只能编辑待审核/已下线/已驳回状态下的商品");
        }
        record.setDeleteTime(new Date());
        record.setUpdateTime(new Date());
        mapper.updateByPrimaryKey(record);

        String content = "【商品删除】\n" + "商品名称 " + record.getProductName() + "\n" + "商品SPU " + record.getSpuCode();
        logService.recordOperateLog(ModuleEnum.ESTEWARD_MANAGE.code, EstewardOperateEnum.PRODUCT_OPERATE.code, content);
        return BaseAnswer.success(null);
    }

}