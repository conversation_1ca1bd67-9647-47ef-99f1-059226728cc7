package com.chinamobile.retail.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.JWTUtil;
import com.chinamobile.iot.sc.entity.retail.FindRetailUserParam;
import com.chinamobile.iot.sc.entity.retail.ReceiptParam;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.LoginOperateEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.*;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.ExcelUtils;
import com.chinamobile.iot.sc.util.HttpUtil;
import com.chinamobile.retail.config.CommonConstant;
import com.chinamobile.retail.config.ProvinceCityConfig;
import com.chinamobile.retail.config.ServiceConfig;
import com.chinamobile.retail.constant.StatusConstant;
import com.chinamobile.retail.dao.ContractCityInfoMapper;
import com.chinamobile.retail.dao.UserMiniProgramMapper;
import com.chinamobile.retail.dao.UserRetailMapper;
import com.chinamobile.retail.dao.ext.UserRetailMapperExt;
import com.chinamobile.retail.enums.RetailUserRoleEnum;
import com.chinamobile.retail.exception.IOTException;
import com.chinamobile.retail.exception.StatusContant;
import com.chinamobile.retail.pojo.dto.DistriCenterLinkDTO;
import com.chinamobile.retail.pojo.dto.ProvinceDTO;
import com.chinamobile.retail.pojo.dto.RetailUserExceltemDTO;
import com.chinamobile.retail.pojo.dto.unionpay.LoginResponseDTO;
import com.chinamobile.retail.pojo.dto.unionpay.SendSignResponseDTO;
import com.chinamobile.retail.pojo.entity.UserMiniProgram;
import com.chinamobile.retail.pojo.entity.UserMiniProgramExample;
import com.chinamobile.retail.pojo.entity.UserRetail;
import com.chinamobile.retail.pojo.entity.UserRetailExample;
import com.chinamobile.retail.pojo.mapper.UserInfoListDO;
import com.chinamobile.retail.pojo.param.UserListParam;
import com.chinamobile.retail.pojo.param.unionpay.SendSignParam;
import com.chinamobile.retail.pojo.param.unionpay.SignResultNotifyParam;
import com.chinamobile.retail.pojo.vo.CheckSignVO;
import com.chinamobile.retail.pojo.vo.CodeNameVO;
import com.chinamobile.retail.pojo.vo.RetailRoleVO;
import com.chinamobile.retail.pojo.vo.UserInfoVO;
import com.chinamobile.retail.service.Sm4Service;
import com.chinamobile.retail.util.IOTEncodeUtils;
import com.chinamobile.retail.util.Md5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.util.DateUtils.DATETIME_FORMAT_NO_SYMBOL;

@Slf4j
@Service
public class RetailUserServiceImpl {

    @Value("${iot.encodeKey}")
    private String encodeKey;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;

    @Resource
    private UserRetailMapper userRetailMapper;

    @Resource
    private UserRetailMapperExt userRetailMapperExt;

    @Resource
    private ContractCityInfoMapper cityInfoMapper;
//    用户密码登录加密用
//    @Value("${rsa.private.key}")
//    private String RSAPrivateKey;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private ProvinceCityConfig pcConfig;

    @Resource
    private ServiceConfig serviceConfig;

    @Resource
    private LogService logService;

    @Resource
    private UserMiniProgramMapper userMiniProgramMapper;

    @Autowired
    private Sm4Service sm4Service;


    @DS("query")
    public BaseAnswer login(String phone,String id) {
        log.info("login phone = {}",phone);
        BaseAnswer baseAnswer = new BaseAnswer();
        List<UserRetail> users = userRetailMapper.selectByExample(new UserRetailExample().createCriteria().andPhoneEqualTo(phone).example());
        if (CollectionUtils.isEmpty(users)) {
            throw new BusinessException(StatusConstant.USER_NO_EXIST);
        }
        UserRetail user = users.get(0);
//        log.info("loginUser = {}, authStatus = {}",user,user.getAuthStatus());
//        if(user.getAuthStatus()!=1){
//            log.info("loginStatus != 1");
//            throw new BusinessException(StatusConstant.USER_IN_AUTH);
//        }
        String token = generateToken(users.get(0));
        return baseAnswer.setData(token);
    }

//    @Transactional(rollbackFor = Exception.class)
//    public BaseAnswer register(UserRegisterParam registerUser){
//        log.info("register registerUser = {}",registerUser);
//        /**
//         * 校验传入的注册信息
//         **/
////        if (!RegexUtil.regexOperatorName(registerUser.getUserName())) {
////            throw new BusinessException(StatusConstant.NAME_FORMAT_ERROR);
////        }
//        if (StringUtils.isNotEmpty(registerUser.getPhone())) {
//            //手机格式校验
//            if (!RegexUtil.regexPhone(registerUser.getPhone())) {
//                throw new BusinessException(StatusConstant.PHONE_ERROR);
//            }
//            //手机号是否被注册
//            List<UserRetail> users = userRetailMapper.selectByExample(new UserRetailExample().createCriteria().andPhoneEqualTo(registerUser.getPhone()).example());
//            if (!CollectionUtils.isEmpty(users)) {
//                throw new BusinessException(StatusConstant.PHONE_IS_EXIST);
//            }
//        }
//
//        //Todo: 注册的时候获取省市信息
////        String pCode = pcConfig.getProvinceNameCodeMap().get(registerUser.getProvince());
////        String cCode = pcConfig.getCityNameCodeMap().get(registerUser.getCityCode());
//        String cCode = registerUser.getCityCode();
//        String city = pcConfig.getCityCodeNameMap().get(cCode);
//        List<ContractCityInfo> cityInfos = cityInfoMapper.selectByExample(new ContractCityInfoExample().createCriteria().andMallCodeEqualTo(cCode).example());
//        if(CollectionUtils.isEmpty(cityInfos)){
//            throw new BusinessException(StatusConstant.CITYCODE_ERROR);
//        }
//        String pCode = cityInfos.get(0).getProvinceMallCode();
//        String province = cityInfos.get(0).getProvinceMallName();
//        //保存用户信息
//        String oneId = BaseServiceUtils.getId();
//        UserRetail user = new UserRetail();
//        user.setId(oneId);
//        user.setName(registerUser.getUserName());
//        user.setPhone(registerUser.getPhone());
//        user.setProvince(province);
//        user.setProvinceCode(pCode);
//        user.setCity(city);
//        user.setCityCode(cCode);
//        user.setWorkNum(registerUser.getWorkNum());
//        user.setRoleType(registerUser.getRoleType());
//        user.setCustCode(registerUser.getCustCode());
//        user.setAuthStatus(0);
//        user.setRegTime(new Date());
//        user.setUpdateTime(new Date());
//
//        userRetailMapper.insert(user);
//
//        return new BaseAnswer();
//    }


//    public BaseAnswer loginOutScreen(String userId) {
//        redisTemplate.delete(Constant.REDIS_KEY_USER_TOKEN + userId);
//        return new BaseAnswer();
//    }


//    @Override
//    public BaseAnswer<DataUserScreen> userScreenInfo(String userId) {
//        BaseAnswer<DataUserScreen> baseAnswer = new BaseAnswer<>();
//        DataUserScreen userScreen = new DataUserScreen();
//        UserScreen screen = userScreenMapper.selectByPrimaryKey(userId);
//        BeanUtils.copyProperties(screen,userScreen);
//        //查询角色业务类型
//        Role role = roleMapper.selectByPrimaryKey(userScreen.getRoleId());
//        userScreen.setRoleType(role.getRoleType());
//        userScreen.setRoleName(role.getName());
//        // 查询用户-角色-权限
//        BaseAnswer<RoleAuthInfo> answer = roleAuthService.roleInfo(userScreen.getRoleId());
//        List<AuthCode> authCodes = answer.getData().getAuthCodes();
//        userScreen.setAuthCodes(authCodes);
//        baseAnswer.setData(userScreen);
//        return baseAnswer;
//    }


    /**
     * 生产token，并且放入到redis
     *
     * @param
     * @return
     */
    private String generateToken(UserRetail user) {
        String userId = user.getId();
        log.info("UserRetail generateToken Eneter, userId = {}",userId);
        //生成token
        AccessToken accessToken = new AccessToken();
        accessToken.setUserId(userId);
        //由于存在账号的注销等操作，这样就需要将注销用户踢下线，所以需要将登陆信息保存到redis
        //这里只是使用jwt作为生产token的工具，验证还是在redis验证
        String token = JWTUtil.getToken(accessToken, null, null, null);
        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setUserId(userId);
        loginIfo4Redis.setToken(token);
        loginIfo4Redis.setPhone(user.getPhone());
        // 先写死
        loginIfo4Redis.setRoleName("分销员");
//        loginIfo4Redis.setIsAdmin(user.getIsAdmin() == null ? false : user.getIsAdmin());
//        loginIfo4Redis.setRoleId(user.getRoleId());
        // 封装loginIfo4Redis
        // 查询角色信息
//        BaseAnswer<RoleAuthInfo> answer = roleAuthService.roleInfo(user.getRoleId());
//        RoleAuthInfo roleAuthInfo = answer.getData();
//        loginIfo4Redis.setRoleType(roleAuthInfo.getRoleType());
//        loginIfo4Redis.setAuthCodes(roleAuthInfo.getAuthCodes());
        // loginIfo4Redis.setProvince(user.getProvince());
        log.debug("分销中心缓存的登录数据;{}", loginIfo4Redis);
        //将权限信息放入redis
        //分销中心用户暂定为永不过期
        redisTemplate.opsForValue().set(Constant.REDIS_KEY_USER_TOKEN + userId, loginIfo4Redis);
//        if (user.getIsAdmin()){
//            redisTemplate.opsForValue().set(Constant.REDIS_KEY_USER_TOKEN + userId, loginIfo4Redis);
//        }else {
//            redisTemplate.opsForValue().set(Constant.REDIS_KEY_USER_TOKEN + userId, loginIfo4Redis, 24, TimeUnit.HOURS);
//        }
        return token;
    }

    @DS("query")
    public BaseAnswer<String> findRetailUserId(FindRetailUserParam param) {
        log.debug("findRetailUserId入参:{}", JSON.toJSONString(param));
        String custCode = param.getCustCode();
        String phone = param.getPhone();
        UserRetailExample userRetailExample = new UserRetailExample();
        UserRetailExample.Criteria criteria = userRetailExample.createCriteria();
        if(StringUtils.isNotEmpty(custCode)){
            criteria.andCustCodeEqualTo(custCode);
        }
        if(StringUtils.isNotEmpty(phone)){
            criteria.andPhoneEqualTo(phone);
        }

        if(StringUtils.isNotEmpty(param.getUserId())){
            criteria.andUserIdEqualTo(param.getUserId());
        }
        List<UserRetail> userRetails = userRetailMapper.selectByExample(userRetailExample);
        if(userRetails.isEmpty()){
            return BaseAnswer.success(null);
        }
        return BaseAnswer.success(userRetails.get(0).getId());
    }

    public BaseAnswer<List<RetailRoleVO>> getAllRole() {
        RetailUserRoleEnum[] values = RetailUserRoleEnum.values();
        List<RetailRoleVO> collect = Arrays.stream(values).map(r -> {
            RetailRoleVO vo = new RetailRoleVO();
            vo.setRoleName(r.name);
            vo.setRoleType(r.code);
            return vo;
        }).collect(Collectors.toList());
        return BaseAnswer.success(collect);
    }


//    @Transactional(rollbackFor = Exception.class)
//    public BaseAnswer<List<ProvinceDTO>> verifyIdentity(String orgProductUrl, String userId){
//        //todo: base64先转换
//        BASE64Decoder decoder = new BASE64Decoder();
//        String productUrl;
//        try{
//            productUrl = new String(decoder.decodeBuffer(orgProductUrl),"UTF-8");
//        }catch(IOException e){
//            e.printStackTrace();
//            throw new BusinessException(StatusConstant.SERVER_INTERNAL_ERROR);
//        }
//        log.info("verifyIdentity productUrl = {}, userId = {},\r\n decodedUrl = {}",orgProductUrl, userId, productUrl);
//        //校验分享链接
//        boolean isShareUrl = productUrl.contains("&u=")&&productUrl.contains("&share=");
//        log.info("verifyIdentity isSharedUrl = {}",isShareUrl);
//        if(isShareUrl){
//            int indexBegin = productUrl.indexOf("&u=");
//            log.info("verifyIdentity subIndexBegin = {}",indexBegin);
//            int indexEnd = productUrl.indexOf("&share=");
//            log.info("verifyIdentity subIndexEnd = {}",indexEnd);
//            String sharedCode = productUrl.substring(indexBegin+3,indexEnd);
//            log.info("verifyIdentity sharedCode = {}",sharedCode);
//            try{
//                String encodedCode = URLEncoder.encode(sharedCode,"UTF-8");
//                log.info("verifyIdentity encodedSharedCode = {}",encodedCode);
//                UserRetail user = new UserRetail();
//                user.setId(userId);
//                user.setShareCode(encodedCode);
//                int ret = userRetailMapper.updateByPrimaryKeySelective(user);
//                log.info("verifyIdentity Updated User SharedCode ret = {}",ret);
//            }catch(Exception e){
//                e.printStackTrace();
//                throw new BusinessException();
//            }
//        }else{
//            throw new BusinessException(StatusConstant.NOT_SHARE_URL);
//        }
//        return new BaseAnswer<>();
//    }


    public BaseAnswer<List<ProvinceDTO>> getProvinceCity(){
        List<ProvinceDTO> pcList = pcConfig.getProcityList();
        return BaseAnswer.success(pcList);
    }

//    @Transactional(rollbackFor = Exception.class)
//    public BaseAnswer<Void> uploadAccounts(MultipartFile upfile){
//        if(upfile.isEmpty()){
//            throw new BusinessException(StatusConstant.FILE_NOT_EXIST);
//        }
//        String oldName = upfile.getOriginalFilename();
//        if(oldName==null||(!oldName.endsWith(".xlsx")&&!oldName.endsWith(".xls"))){
//            throw new BusinessException(StatusConstant.FILE_TYPE_ERROR);
//        }
//        log.info("解析上传用户文件: {}",oldName);
//        try{
////            List<Object> list = EasyExcel.read(upfile.getInputStream(), RetailUserImportDTO.class, new RetailUserExcelListener())
////                    .sheet(0).headRowNumber(1).doReadSync();
//            RetailUserExcelListener listener = new RetailUserExcelListener();
//            EasyExcel.read(upfile.getInputStream(), RetailUserImportDTO.class, listener)
//                    .sheet(0).headRowNumber(1).doReadSync();
//            List<RetailUserImportDTO> list = listener.getCachedDataList();
//            log.info("list size = {}",list.size());
//            if(list.size()==0){
//                throw new BusinessException(StatusConstant.EMPTY_EXCEL);
//            }
//            log.info("读取到Excel成功条数 successExcelNum = {}",list.size());
//            HashMap<String, String> pNameCodeMap = pcConfig.getProvinceNameCodeMap();
//            HashMap<String, String> cNameCodeMap = pcConfig.getCityNameCodeMap();
//            for(Object o:list){
//                RetailUserImportDTO retailUser = (RetailUserImportDTO) o;
//                String province = retailUser.getProvince();
//                String city = retailUser.getCity();
//                String custCode = retailUser.getCode();
//                String name = retailUser.getName();
//                String workNum = retailUser.getWorkNum();
//                String phone = retailUser.getPhone();
//                String regTime = retailUser.getRegTime();
//                String provinceCode = pNameCodeMap.get(province);
//                String cityCode = cNameCodeMap.get(city);
//                Integer roleType = 0;
//                if("客户经理".equals(retailUser.getRoleType())){
//                    roleType = 0;
//                }
//
//                //判断是否已经被自主注册过了
//                List<UserRetail> users = userRetailMapper.selectByExample(new UserRetailExample().createCriteria().andPhoneEqualTo(phone).example());
//                if (CollectionUtils.isEmpty(users)) {
//                    //没有这个用户则插入
//                    //todo： 查漏补缺要更新哪些
//                    String userId = BaseServiceUtils.getId();
//                    UserRetail user = new UserRetail();
//                    user.setId(userId);
//                    user.setCustCode(custCode);
//                    user.setRoleType(roleType);
//                    user.setName(name);
//                    user.setProvince(province);
//                    user.setCity(city);
//                    user.setProvinceCode(pNameCodeMap.get(province));
//                    user.setCityCode(cNameCodeMap.get(city));
//                    user.setWorkNum(workNum);
//                    user.setPhone(phone);
//                    user.setAuthStatus(1);
//                    user.setRegTime(new Date());
//                    user.setAuthTime(new Date());
//                    user.setUpdateTime(new Date());
//                    userRetailMapper.insert(user);
//                }else{
//                    //有这个用户，用后台数据更新
//                    UserRetail user = users.get(0);
//                    user.setCustCode(custCode);
//                    user.setRoleType(roleType);
//                    user.setName(name);
//                    user.setWorkNum(workNum);
//                    user.setAuthStatus(1);
//                    user.setRegTime(new Date());
//                    user.setAuthTime(new Date());
//                    user.setUpdateTime(new Date());
//                    userRetailMapper.updateByPrimaryKeySelective(user);
//                }
//
//            }
//        }catch(IOException ioe){
//            log.error("读取文件异常，文件名：{}， 异常描述：{}",oldName, ioe);
//            throw new BusinessException(StatusConstant.FILE_PARSE_ERROR);
//        }
//
//        return new BaseAnswer<>();
//    }


//    public BaseAnswer authRegisterReq(UserAuthParam listparam){
//
//        //检查是否有改用户申请
//        for(UserAuthParam.AuthParam param:listparam.getAuthParam()){
//            log.info("authRegisterReq params accountId = {}",param.getAccountId());
//            UserRetail user = userRetailMapper.selectByPrimaryKey(param.getAccountId());
//            if(user==null){
//                //没有用户
//                throw new BusinessException(StatusConstant.USER_NO_EXIST);
//            }
//            log.info("authRegisterReq user id = {}",user.getId());
//            if(user.getAuthStatus()!=0){
//                //不在待审批状态
//                throw new BusinessException(StatusConstant.NOT_IN_REGAUTH_STATUS);
//            }
//
//            if(param.getPassed()){
//                user.setAuthStatus(1);
//                userRetailMapper.updateByPrimaryKeySelective(user);
//            }else{
////                user.setAuthStatus(2);
//                int deletedCount = userRetailMapper.deleteByPrimaryKey(user.getId());
//                log.info("authRegisterReq deleted userCount = {}",deletedCount);
//            }
//        }
//        return new BaseAnswer<>();
//    }


    @DS("query")
    public BaseAnswer getAllUsers(UserListParam param){

        Integer pageIndex = param.getPageNum();
        Integer pageCount = param.getPageSize();

        Page<UserInfoListDO> page = new Page<>(pageIndex, pageCount);
        Page<UserInfoListDO> userInfoVOPage = userRetailMapperExt.pageQueryUserInfoList(page, param);
//        long count = userRetailMapperExt.countUsers(param);
        PageDataPugin<UserInfoVO> pageData = new PageDataPugin<>();
        pageData.setCurrentPage(page.getCurrent());
        pageData.setPageCount(page.getSize());
        pageData.setTotalCount(page.getTotal());

        List<UserInfoVO> userInfoList = new ArrayList<>();

        for(UserInfoListDO item:userInfoVOPage.getRecords()){
            UserInfoVO userInfo = new UserInfoVO();
            userInfo.setId(item.getId());
            userInfo.setPhone(item.getPhone());
            userInfo.setProvince(item.getProvince());
            userInfo.setCity(item.getCity());
//            String area = item.getProvince()+"-"+item.getCity();
//            userInfo.setArea(area);
            userInfo.setWorkNum(item.getWorkNum());
            userInfo.setName(item.getName());
            userInfo.setRoleType(item.getRoleType());
            userInfo.setUniqueCode(item.getUniqueCode());
            userInfo.setRegTime(item.getRegTime());
            userInfo.setLatestTime(item.getLatestLoginTime());
            userInfoList.add(userInfo);
        }
        pageData.setList(userInfoList);
        return new BaseAnswer<PageDataPugin<UserInfoVO>>().setData(pageData);
    }


    public BaseAnswer<List<CodeNameVO>> getProvinceCodeNameList() {
        HashMap<String, String> provinceCodeNameMap = pcConfig.getProvinceCodeNameMap();
        List<CodeNameVO> list = new ArrayList<>();
        for (Map.Entry<String, String> entry : provinceCodeNameMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            CodeNameVO vo = new CodeNameVO();
            vo.setCode(key);
            vo.setName(value);
            list.add(vo);
        }
        CodeNameVO vo = new CodeNameVO();
        vo.setName("全国");
        list.add(vo);
        return BaseAnswer.success(list);
    }



    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public IOTAnswer<JSONObject> getDistriCenterLink(IOTRequest baseRequest, HttpServletResponse response){
        log.debug("Retail获取分销中心链接请求:{}", JSON.toJSONString(baseRequest));
        IOTAnswer<JSONObject> iotAnswer = new IOTAnswer<>();
        iotAnswer.setMessageSeq(baseRequest.getMessageSeq());
        DistriCenterLinkDTO centerLinkDTO;
        try{
            centerLinkDTO = JSON.parseObject(baseRequest.getContent(), DistriCenterLinkDTO.class);
        }catch(Exception e){
            log.error("解析异常："+e);
            throw new IOTException(iotAnswer,"json数据解析异常");
        }
        DistriCenterLinkDTO.UserInfo userInfo = centerLinkDTO.getUserInfo().get(0);
        log.info("getDistriCenterLink userInfo = {}",userInfo);
        String userPhone = IOTEncodeUtils.decryptSM4(userInfo.getUserPhone(), iotSm4Key, iotSm4Iv);
        log.debug("getDistriCenterLink userphone = {}",userPhone);
        log.info("getDistriCenterLink userphone = {}",userPhone);
//        //todo 临时增加白名单验证
//        log.info("getDistriCenterLink whiteList = {}",pcConfig.getWhiteList());
//        log.info("getDistriCenterLink whiteList contains phone = {}",pcConfig.getWhiteList().contains(userPhone));
//        log.info("getDistriCenterLink userphone equals = {}","13594176527".equals(userPhone));
//        log.info("getDistriCenterLink whiteList contains phone = {}",pcConfig.getWhiteList().contains("13594176527"));
//        if(pcConfig.getWhiteList()!=null&&!pcConfig.getWhiteList().contains(userPhone)){
//            log.info("retail user not in white list");
//            throw new IOTException(iotAnswer,"不在访问权限内");
//        }

        UserRetail userRetail = new UserRetail();
        userRetail.setBeId(baseRequest.getBeId());
        String loginRole = centerLinkDTO.getLoginRole();

        if(loginRole==null){
            throw new IOTException(iotAnswer,"未知用户角色");
        }else if("1".equals(loginRole)){
            //一级分销员
            userRetail.setRoleType(1);
            userRetail.setRecommendCode(userInfo.getUserCode());
            userRetail.setCustCode(userInfo.getUserCode());
        }else if("2".equals(loginRole)){
            //二级分销员
            userRetail.setRoleType(2);
            userRetail.setRecommendCode(userInfo.getUserCode());
            userRetail.setCustCode(userInfo.getUserCode());
        }else if("3".equals(loginRole)){
            //客户经理

            JSONObject contentJSON = new JSONObject();
            contentJSON.put("link",serviceConfig.getDismgLink());
            iotAnswer.setContent(contentJSON);
            return iotAnswer;
//            userRetail.setRoleType(0);
//            userRetail.setCustCode(userInfo.getUserCode());
//            userRetail.setName(IOTEncodeUtils.decryptSM4()(userInfo.getUserName(), iotSm4Key, iotSm4Iv));
//            log.info("test Decrypt iotSm4Key, iotSm4Iv = {}; DecryptedContent = {}",iotSm4Key, iotSm4Iv, IOTEncodeUtils.decryptSM4()(userInfo.getUserName(), iotSm4Key, iotSm4Iv));
//            userRetail.setWorkNum(userInfo.getUserNumber());
        }else if("4".equals(loginRole)){
            //渠道商
            userRetail.setRoleType(3);
            userRetail.setCustCode(userInfo.getUserCode());
            userRetail.setWorkNum(userInfo.getUserNumber());
            userRetail.setName(IOTEncodeUtils.decryptSM4(userInfo.getUserName(),iotSm4Key, iotSm4Iv));
        }
        userRetail.setPhone(IOTEncodeUtils.decryptSM4(userInfo.getUserPhone(), iotSm4Key, iotSm4Iv));
        userRetail.setProvinceCode(userInfo.getUserProvinceCode());
        userRetail.setCityCode(userInfo.getUserCityCode());
        HashMap<String, String> pCodeNameMap = pcConfig.getProvinceCodeNameMap();
        HashMap<String, String> cCodeNameMap = pcConfig.getCityCodeNameMap();
        userRetail.setProvince(pCodeNameMap.get(userInfo.getUserProvinceCode()));
        userRetail.setCity(cCodeNameMap.get(userInfo.getUserCityCode()));
        userRetail.setUserId(userInfo.getUserID());

        //通过商城UserId判断是否已经注册
        String userId = userInfo.getUserID();
        UserRetailExample userRetailExample = new UserRetailExample();
        UserRetailExample.Criteria criteria = userRetailExample.createCriteria();
        if(StringUtils.isNotEmpty(userId)){
            criteria.andUserIdEqualTo(userId);
        }
        List<UserRetail> userRetails = userRetailMapper.selectByExample(userRetailExample);
        if(userRetails.isEmpty()){
            //没有注册过，注册
            userRetail.setId(userId);
            userRetail.setRegTime(new Date());
            userRetail.setLatestLoginTime(new Date());
            userRetailMapper.insertSelective(userRetail);
        }else{
            //注册过，更新
            userRetail.setLatestLoginTime(new Date());
            userRetailMapper.updateByExampleSelective(userRetail, userRetailExample);
        }


        //注册完，重新登录分销系统
        String token = login(userId);
        log.info("getDistriCenterLink token = {}",token);

        //生成凭证
        String receipt = IOTEncodeUtils.encryptIOTMessage(userId+System.currentTimeMillis(), encodeKey);
        //TODO 测试环境有效时间
//        redisTemplate.opsForValue().set(receipt, token, 30, TimeUnit.SECONDS);
        redisTemplate.opsForValue().set(receipt, token, 1, TimeUnit.DAYS);
//        Cookie cookieToken = new Cookie("distri_token",token);
//        cookie_token.setDomain(".cmcconenet.com");
//        //设置cookie持久化时间，30天
//        cookie_token.setMaxAge(30 * 24 * 60 * 60);
//        //设置当前项目下都携带这个cookie
//        cookie_token.setPath(request.getContextPath());
//        response.addCookie(cookieToken);
        log.info("生成登陆凭证 receiption={}; token={}",receipt, token);
        Object otoken = redisTemplate.opsForValue().get(receipt);
        log.info("获取生成的token otoken={}",otoken);
//        iotAnswer.set
        //设置返回内容，分销中心链接
        JSONObject contentJSON = new JSONObject();
        contentJSON.put("link",serviceConfig.getDisLink()+"?receipt="+receipt);
        iotAnswer.setContent(contentJSON);

        return iotAnswer;

    }


    @DS("query")
    public String login(String userId){
        log.info("login userId = {}",userId);
//        BaseAnswer baseAnswer = new BaseAnswer();
        List<UserRetail> users = userRetailMapper.selectByExample(new UserRetailExample().createCriteria().andIdEqualTo(userId).example());
        if (CollectionUtils.isEmpty(users)) {
            throw new BusinessException(StatusConstant.USER_NO_EXIST);
        }
        UserRetail user = users.get(0);
        log.info("loginUser = {}",user);
        String token = generateToken(users.get(0));
//        return baseAnswer.setData(token);

        logService.recordOperateLog(ModuleEnum.LOGIN.code, LoginOperateEnum.RETAIL_LOGIN.code, "-",user.getId(),0, LogResultEnum.LOG_SUCESS.code,null);
        return token;
    }


    public BaseAnswer<String> exchangeToken(ReceiptParam param){
        log.info("RetailUserService exchange token receipt = {}",param);
        String receipt = param.getReceipt();
        if(StringUtils.isEmpty(receipt)){
            throw new BusinessException(StatusConstant.NULL_RECEIPT);
        }
        Object otoken = redisTemplate.opsForValue().get(receipt);
        log.debug("exchangeToken otoken = {}",otoken);
        if(otoken==null){
            throw new BusinessException(StatusConstant.RECEIPT_NOT_EXIST);
        }
        BaseAnswer<String> answer = new BaseAnswer<>();
        answer.setData(String.valueOf(otoken));
        return answer;
    }


    @DS("query")
    public void exportRetailUsers(UserListParam param, HttpServletResponse response){
        List<UserInfoListDO> userInfoList = userRetailMapperExt.queryUserInfoList(param);
        List<RetailUserExceltemDTO> userExcelList = new ArrayList<>();
        for(UserInfoListDO item : userInfoList){
            RetailUserExceltemDTO eitem = new RetailUserExceltemDTO();
            BeanUtils.copyProperties(item, eitem);
            if(item.getRoleType()==1){
                eitem.setRoleType("一级分销员");
            }else if(item.getRoleType()==2){
                eitem.setRoleType("二级分销员");
            }else if(item.getRoleType() == 3){
                eitem.setRoleType("渠道商");
            }
            log.info("after copy, eitem = {}",eitem);
            userExcelList.add(eitem);
        }
        log.info("after copy, userExcelList = {}",userExcelList);
        try {
            Date date = new Date();
            String dateStr = DateUtils.dateToStr(date, DATETIME_FORMAT_NO_SYMBOL);
            String fileName = "retailUsers"+dateStr;
            ExcelUtils.exportExcel(userExcelList, "分销用户列表",
                    "",
                    RetailUserExceltemDTO.class,
                    fileName,
                    response);
        } catch (IOException e) {
            throw new BusinessException(StatusContant.POINT_EXCEL_EXPORT_ERROR);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String syncSignResult(SignResultNotifyParam param) {
        //和银联确认，签约结果回调只会在签约成功后回调，所以不用判断code
        Date now = new Date();
        log.info("syncSignResult 收到签约回调:{}",JSON.toJSONString(param));
        String notifyType = param.getNotify_type();
        if(!"notify_sign_result".equals(notifyType)){
            //不处理其他类型,直接返回
            return "success";
        }
        String encryptedResult = param.getResult();
        String decryptedResult = sm4Service.decrypt(encryptedResult);
        SignResultNotifyParam.Result result = JSONObject.parseObject(decryptedResult, SignResultNotifyParam.Result.class);
        log.info("syncSignResult 收到签约回调解密后:{}",decryptedResult);

        //相同手机号，不同用户Id的情况，如果一个手机号签约完成，则所有用户都签约完成
        String phone = result.getPhone();
        //同时更新分销用户和小程序用户签约状态
        UserMiniProgramExample userMiniProgramExample = new UserMiniProgramExample().createCriteria().andPhoneEqualTo(phone).andSignStatusEqualTo(0).example();
        for (UserMiniProgram userMiniProgram : userMiniProgramMapper.selectByExample(userMiniProgramExample)) {
            userMiniProgram.setSignStatus(1);
            userMiniProgram.setUpdateTime(now);
            userMiniProgramMapper.updateByPrimaryKeySelective(userMiniProgram);
        }
        UserRetailExample userRetailExample = new UserRetailExample().createCriteria().andPhoneEqualTo(phone).example();
        for (UserRetail userRetail : userRetailMapper.selectByExample(userRetailExample)) {
            userRetail.setSignStatus(1);
            userRetail.setUpdateTime(now);
            userRetailMapper.updateByPrimaryKeySelective(userRetail);
        }
        return "success";
    }

    public BaseAnswer<CheckSignVO> checkWithUnionPay(LoginIfo4Redis loginIfo4Redis) {
        Date now = new Date();
        //相同手机号，不同用户Id的情况，如果一个手机号签约完成，则所有用户都签约完成
        BaseAnswer<CheckSignVO> baseAnswer = new BaseAnswer<>();
        CheckSignVO vo = new CheckSignVO();
        try {
            String phone = loginIfo4Redis.getPhone();
            if(StringUtils.isEmpty(phone)){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"登录信息中缺少电话,请联系管理员");
            }
            log.info("checkWithUnionPay 手机号 = {}",phone);

            String url = serviceConfig.getUnionPayUrl() + CommonConstant.sendSign;
            Map<String, String> headerParams = new HashMap<>();
            headerParams.put("Authorization","Bearer "+getUnionPayToken());
            headerParams.put("Content-Type","application/json");

            SendSignParam sendSignParam = new SendSignParam();
            sendSignParam.setCompany_id(serviceConfig.getUnionPaySignCompanyId());
            sendSignParam.setPhone(sm4Service.encrypt(phone));
            sendSignParam.setNotify_url(serviceConfig.getUnionPaySignResultNotifyUrl());
            log.info("checkWithUnionPay 查询签约状态请求参数:{},token:{}",JSONObject.toJSONString(sendSignParam),headerParams.get("Authorization"));
            String responseStr = HttpUtil.doPostJson(url, headerParams, JSONObject.parseObject(JSONObject.toJSONString(sendSignParam)), 10000, 10000);
            log.info("checkWithUnionPay 查询签约状态,手机号{},返回结果{}",CommonConstant.sendSign,phone,responseStr);
            SendSignResponseDTO sendSignResponseDTO = JSONObject.parseObject(responseStr, SendSignResponseDTO.class);
            if(!"200".equals(sendSignResponseDTO.getCode())){
                throw new BusinessException(BaseErrorConstant.API_RETURN_ERROR,sendSignResponseDTO.getMessage());
            }
            String encryptedResult = sendSignResponseDTO.getResult();
            String decrypted = sm4Service.decrypt(encryptedResult);
            SendSignResponseDTO.Result result = JSONObject.parseObject(decrypted, SendSignResponseDTO.Result.class);
            Integer status = null;
            Integer signStatus = result.getSign_status();
            Integer realStatus = result.getReal_status();
            Integer addCardStatus = result.getAdd_card_status();
            boolean signed = false;
            if(realStatus == 1){
                status = 1;
            }else if(signStatus == 1){
                status = 2;
            }else if(addCardStatus == 1){
                status = 3;
                signed = true;
            }else {
                status = 4;
                signed = true;
            }
            vo.setStatus(String.valueOf(status));
            if(result.getApplet_jump_info() != null){
                vo.setAppletJumpInfo(result.getApplet_jump_info());
            }
            if(result.getH5_jump_info() != null){
                vo.setH5JumpInfo(result.getH5_jump_info());
            }
            baseAnswer.setData(vo);
            if(signed){
                //对于已经签约的，更新数据库状态
                //同时更新分销用户和小程序用户签约状态
                UserMiniProgramExample userMiniProgramExample = new UserMiniProgramExample().createCriteria().andPhoneEqualTo(phone).andSignStatusEqualTo(0).example();
                for (UserMiniProgram userMiniProgram : userMiniProgramMapper.selectByExample(userMiniProgramExample)) {
                    userMiniProgram.setSignStatus(1);
                    userMiniProgram.setUpdateTime(now);
                    userMiniProgramMapper.updateByPrimaryKeySelective(userMiniProgram);
                }
                UserRetailExample userRetailExample = new UserRetailExample().createCriteria().andPhoneEqualTo(phone).example();
                for (UserRetail userRetail : userRetailMapper.selectByExample(userRetailExample)) {
                    userRetail.setSignStatus(1);
                    userRetail.setUpdateTime(now);
                    userRetailMapper.updateByPrimaryKeySelective(userRetail);
                }
            }
        } catch (Exception e) {
            log.error("查询签约状态出错",e);
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"查询签约状态出错");
        }
        return baseAnswer;
    }


    /**
     * 获取银联token,用于接口调用
     */
    public String getUnionPayToken() throws Exception {
        Object o = redisTemplate.opsForValue().get(CommonConstant.REDIS_UNIONPAY_TOKEN_KEY);
        if(o != null){
            return (String)o;
        }
        String url = serviceConfig.getUnionPayUrl() + CommonConstant.unionpayLogin;
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        header.put("appid", serviceConfig.getUnionPayAppId());
        header.put("secret", Md5Util.md5DigestAsHexToLowerCase(serviceConfig.getUnionPaySecret()));
        JSONObject body = new JSONObject();
        body.put("sm4Key", serviceConfig.getUnionPaySm4Key());
        String responseStr = HttpUtil.doPostJson(url, header, body, 10000, 10000);
        LoginResponseDTO loginResponseDTO = JSONObject.parseObject(responseStr, LoginResponseDTO.class);
        if(!"200".equals(loginResponseDTO.getCode())){
            throw new BusinessException(BaseErrorConstant.API_RETURN_ERROR,"获取银联token出错:"+loginResponseDTO.getMessage());
        }
        String encryptedResult = loginResponseDTO.getResult();
        //解密
        String decrypted = sm4Service.decrypt(encryptedResult);
        LoginResponseDTO.Result result = JSONObject.parseObject(decrypted, LoginResponseDTO.Result.class);
        String token = result.getToken();
        //缓存token，减少10秒有效期，剔除网络的影响造成缓存中有token但实际token已过期
        redisTemplate.opsForValue().set(CommonConstant.REDIS_UNIONPAY_TOKEN_KEY, token, result.getExpires_in()-10, TimeUnit.SECONDS);
        return token;
    }
}
