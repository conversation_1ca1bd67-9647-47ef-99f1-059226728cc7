package com.chinamobile.retail.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.MiniProgramOperateEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.BasePageQuery;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.retail.dao.UserMinProgramRequestHeaderMapper;
import com.chinamobile.retail.dao.ext.UserMinProgramRequestHeaderMapperExt;
import com.chinamobile.retail.pojo.entity.UserMinProgramRequestHeader;
import com.chinamobile.retail.pojo.entity.UserMiniProgram;
import com.chinamobile.retail.pojo.param.AuditUserHeaderParam;
import com.chinamobile.retail.pojo.vo.UserHeaderVO;
import com.chinamobile.retail.service.IMiniProgramUserService;
import com.chinamobile.retail.service.UserRetailRequestHeaderService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/30
 * @description 分销中心用户头像申请记录service实现类
 */
@Service
public class UserRetailRequestHeaderServiceImpl implements UserRetailRequestHeaderService {

    @Resource
    private UserMinProgramRequestHeaderMapper userMinProgramRequestHeaderMapper;

    @Resource
    private UserMinProgramRequestHeaderMapperExt userMinProgramRequestHeaderMapperExt;

    @Resource
    private IMiniProgramUserService miniProgramUserService;

    @Resource
    private LogService logService;

    @Override
    @DS("save")
    public void addUserRetailRequestHeader(UserMinProgramRequestHeader userRetailRequestHeader) {
        userMinProgramRequestHeaderMapper.insert(userRetailRequestHeader);
    }

    @Override
    @DS("query")
    public PageData<UserHeaderVO> pageUserHeaderVO(BasePageQuery basePageQuery) {
        PageData<UserHeaderVO> pageData = new PageData<>();
        Integer pageNum = basePageQuery.getPageNum();
        Integer pageSize = basePageQuery.getPageSize();

        Page<UserHeaderVO> page = new Page<>(pageNum, pageSize);

        List<UserHeaderVO> userHeaderVOList = userMinProgramRequestHeaderMapperExt.listUserHeaderRequest(page);
        if (CollectionUtils.isNotEmpty(userHeaderVOList)) {
            userHeaderVOList.stream().forEach(userHeaderVO -> {
                userHeaderVO.setCreateTimeStr(DateUtils.dateToStr(userHeaderVO.getCreateTime(), DateUtils.DEFAULT_DATETIME_FORMAT));
            });
        }

        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(userHeaderVOList);

        return pageData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void auditUserHeader(AuditUserHeaderParam auditUserHeaderParam,
                                String userId) {
        String id = auditUserHeaderParam.getId();
        UserMinProgramRequestHeader userMinProgramRequestHeader = userMinProgramRequestHeaderMapper.selectByPrimaryKey(id);
        if (!Optional.ofNullable(userMinProgramRequestHeader).isPresent()) {
            throw new BusinessException("10004", "审核的头像信息不存在");
        }

        Integer oldAuditStatus = userMinProgramRequestHeader.getAuditStatus();
        if (oldAuditStatus != 1) {
            throw new BusinessException("10004", "已经审核过无需再审核");
        }

        Date date = new Date();
        String userRetailId = userMinProgramRequestHeader.getUserRetailId();
        String hearderUrl = userMinProgramRequestHeader.getHearderUrl();
        String fileKey = userMinProgramRequestHeader.getFileKey();
        Integer auditStatus = auditUserHeaderParam.getAuditStatus();
        String auditReason = auditUserHeaderParam.getAuditReason();
        userMinProgramRequestHeader = new UserMinProgramRequestHeader();
        userMinProgramRequestHeader.setId(id);
        userMinProgramRequestHeader.setAuditStatus(auditStatus);
        userMinProgramRequestHeader.setAuditReason(auditReason);
        userMinProgramRequestHeader.setAuditUserId(userId);
        userMinProgramRequestHeader.setUpdateTime(date);
        userMinProgramRequestHeaderMapper.updateByPrimaryKeySelective(userMinProgramRequestHeader);

        // 更新用户相关信息
        UserMiniProgram userMiniProgram = new UserMiniProgram();
        userMiniProgram.setId(userRetailId);
        userMiniProgram.setAuditHeaderNotice(1);
        userMiniProgram.setAuditStatus(auditStatus);
        userMiniProgram.setAuditReason(auditReason);
        userMiniProgram.setUpdateTime(date);
        if (auditStatus == 2) {
            userMiniProgram.setHeaderImgUrl(hearderUrl);
            userMiniProgram.setFileKey(fileKey);
        }
        miniProgramUserService.updateUserSelectedById(userMiniProgram);

        if (2 == auditStatus) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.USER_AGREEMENT.code, "【同意】\n账号" + userMiniProgram.getPhone(), userId,0, LogResultEnum.LOG_SUCESS.code,null);
        } else if (3 == auditStatus) {
            logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.USER_AGREEMENT.code, "【驳回】\n账号" + userMiniProgram.getPhone(), userId,0, LogResultEnum.LOG_SUCESS.code,null);
        }

    }
}
