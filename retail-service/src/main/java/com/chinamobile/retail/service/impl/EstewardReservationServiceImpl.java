package com.chinamobile.retail.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.enums.log.EstewardOperateEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.retail.config.RestTemplateConfig;
import com.chinamobile.retail.constant.StatusConstant;
import com.chinamobile.retail.dao.*;
import com.chinamobile.retail.pojo.entity.*;
import com.chinamobile.retail.pojo.param.*;
import com.chinamobile.retail.pojo.vo.EstewardExtraInfoVO;
import com.chinamobile.retail.pojo.vo.EstewardReservationListVO;
import com.chinamobile.retail.request.esteward.OppfRequest;
import com.chinamobile.retail.response.esteward.OppfResponse;
import com.chinamobile.retail.service.EstewardReservationService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EstewardReservationServiceImpl implements EstewardReservationService {

    @Resource
    private EstewardReservationMapper estewardReservationMapper;
    @Resource
    private LogService logService;

    @Resource
    private EstewardReservationExtraInfoMapper estewardReservationExtraInfoMapper;

    @Resource
    private EstewardReservationExtraTemplateMapper estewardReservationExtraTemplateMapper;

    @Resource
    private EstewardProductMapper estewardProductMapper;

    @Resource
    private SkuOfferingInfoMapper skuOfferingInfoMapper;

    @Resource
    private SpuOfferingInfoMapper  spuOfferingInfoMapper;

    ThreadFactory springThreadFactory = new CustomizableThreadFactory("springThread-pool-");

    ThreadPoolExecutor executor = new ThreadPoolExecutor(4, 10, 4L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(100000), springThreadFactory);

    @Value("${esteward.oppf.url}")
    private String url;

    @Value("${esteward.oppf.method}")
    private String method;
    @Value("${esteward.oppf.appId}")
    private String appId;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addProduct(EstewardAddProductParam param) {
        String userId = param.getUserId();

        // 处理商品添加
        if (CollectionUtils.isNotEmpty(param.getProductInfos())) {
            for (EstewardAddProductParam.ProductInfo productInfo : param.getProductInfos()) {
                if (productInfo.getProductId() != null) {
                    handleSingleSku(userId, productInfo.getProductId(), productInfo.getCount());
                } else if (productInfo.getSpuCode() != null) {
                    handleMultiSku(userId, productInfo.getSpuCode(), productInfo.getCount());
                }
            }
        }
    }

    // 处理单SKU商品添加
    private EstewardReservation handleSingleSku(String userId, String productId, Integer count) {
        // 验证商品是否存在
        SkuOfferingInfo product = skuOfferingInfoMapper.selectByPrimaryKey(productId);
        if (product == null) throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "商品不存在");

        // 查询是否存在未提交的同商品条目
        EstewardReservationExample example = new EstewardReservationExample();
        example.createCriteria()
                .andUserIdEqualTo(userId)
                .andProductIdEqualTo(productId)
                .andReservationOrderIdIsNull(); // 关键点：未提交的条目

        List<EstewardReservation> existingItems = estewardReservationMapper.selectByExample(example);

        if (!existingItems.isEmpty()) {
            // 合并数量
            EstewardReservation item = existingItems.get(0);
            item.setProductCount(item.getProductCount() + count);
            item.setUpdateTime(new Date());
            estewardReservationMapper.updateByPrimaryKeySelective(item);
            return item;
        } else {
            // 创建新条目
            EstewardReservation newItem = new EstewardReservation();
            newItem.setId(BaseServiceUtils.getId());
            newItem.setUserId(userId);
            newItem.setProductId(productId);
            newItem.setSpuCode(product.getSpuCode()); // 记录SPU信息
            newItem.setProductCount(count);
            newItem.setIsHidden("0");
            newItem.setCreateTime(new Date());
            estewardReservationMapper.insert(newItem);
            return newItem;
        }
    }

    // 处理多SKU商品添加（未指定具体SKU）
    private void handleMultiSku(String userId, String spuCode, Integer count) {
        // 验证SPU是否存在
        SkuOfferingInfoExample productExample = new SkuOfferingInfoExample();
        productExample.createCriteria().andSpuCodeEqualTo(spuCode);
        if (skuOfferingInfoMapper.countByExample(productExample) == 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "SPU不存在");
        }

        // 检查SPU下的SKU数量
        List<SkuOfferingInfo> skuList = skuOfferingInfoMapper.selectByExample(productExample);
        if (skuList.size() == 1) {
            // 如果只有一个SKU，直接添加该SKU
            handleSingleSku(userId, skuList.get(0).getId(), count);
            return;
        }

        // 查询是否存在未提交的同SPU未指定SKU条目
        EstewardReservationExample example = new EstewardReservationExample();
        example.createCriteria()
                .andUserIdEqualTo(userId)
                .andSpuCodeEqualTo(spuCode)
                .andProductIdIsNull() // 关键点：未指定具体SKU
                .andReservationOrderIdIsNull(); // 未提交的条目

        List<EstewardReservation> existingItems = estewardReservationMapper.selectByExample(example);

        if (!existingItems.isEmpty()) {
            // 合并数量
            EstewardReservation item = existingItems.get(0);
            item.setProductCount(item.getProductCount() + count);
            item.setUpdateTime(new Date());
            estewardReservationMapper.updateByPrimaryKeySelective(item);
        } else {
            // 创建新条目
            EstewardReservation newItem = new EstewardReservation();
            newItem.setId(BaseServiceUtils.getId());
            newItem.setUserId(userId);
            newItem.setSpuCode(spuCode);
            newItem.setProductCount(count);
            newItem.setIsHidden("0");
            newItem.setCreateTime(new Date());
            estewardReservationMapper.insert(newItem);
        }
    }


    // 提交预约单
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitReservation(EstewardSubmitReservationParam param) {
        String orderId = BaseServiceUtils.getId();
        for(String reservationId : param.getReservationId()){
            if (reservationId == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "预约单不存在");
            }

            EstewardReservation reservation = estewardReservationMapper.selectByPrimaryKey(reservationId);
            reservation.setUserName(param.getUserName());
            reservation.setUserPhone(param.getUserPhone());
            reservation.setUserAddr(param.getUserAddr());
            reservation.setUserAddrDetail(param.getUserAddrDetail());
            reservation.setUpdateTime(new Date());
            estewardReservationMapper.updateByPrimaryKeySelective(reservation);

            param.getExtraInfos().forEach(extraInfoParam -> {
                EstewardReservationExtraInfo extraInfo = new EstewardReservationExtraInfo();
                extraInfo.setId(BaseServiceUtils.getId());
                extraInfo.setReservationId(reservationId);
                extraInfo.setTemplateId(extraInfoParam.getQuestionId());
                extraInfo.setAnswer(extraInfoParam.getAnswer());
                extraInfo.setCreateTime(new Date());
                extraInfo.setUpdateTime(new Date());
                estewardReservationExtraInfoMapper.insert(extraInfo);
            });
            executor.execute(() -> syncEstewardToOppf(reservation,orderId));
        }
    }

    // 立即预约
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitReservationImmediately(EstewardSubmitReservationImmediatelyParam param) {
        String orderId = BaseServiceUtils.getId();
        EstewardReservation reservation = handleSingleSku(param.getUserId(), param.getProductId(), param.getCount());
        reservation.setUserName(param.getUserName());
        reservation.setUserPhone(param.getUserPhone());
        reservation.setUserAddr(param.getUserAddr());
        reservation.setUserAddrDetail(param.getUserAddrDetail());
        reservation.setUpdateTime(new Date());
        estewardReservationMapper.updateByPrimaryKeySelective(reservation);

        param.getExtraInfos().forEach(extraInfoParam -> {
            EstewardReservationExtraInfo extraInfo = new EstewardReservationExtraInfo();
            extraInfo.setId(BaseServiceUtils.getId());
            extraInfo.setReservationId(reservation.getId());
            extraInfo.setTemplateId(extraInfoParam.getQuestionId());
            extraInfo.setAnswer(extraInfoParam.getAnswer());
            extraInfo.setCreateTime(new Date());
            extraInfo.setUpdateTime(new Date());
            estewardReservationExtraInfoMapper.insert(extraInfo);
        });
        executor.execute(() -> syncEstewardToOppf(reservation,orderId));
    }
    /**
     * 同步预约单给能开
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncEstewardToOppf(EstewardReservation reservation, String orderId) {

        // 请求能开系统
        try {
            List<EstewardProduct> product = estewardProductMapper.selectByExample(new EstewardProductExample()
                    .createCriteria()
                    .andSpuCodeEqualTo(reservation.getSpuCode())
                    .andDeleteTimeIsNull()
                    .andStatusEqualTo(4)
                    .example());
            if(product == null){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "商品不存在");
            }
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String dateTime = formatter.format(new Date());
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url)
                    .queryParam("method", method)
                    .queryParam("format", "json")
                    .queryParam("appId", appId)
                    .queryParam("status", "1")
                    .queryParam("timestamp",dateTime)
                    .queryParam("flowdId", BaseServiceUtils.getId());

            String estewardToOppfUrl = builder.toUriString();
            OppfRequest oppfRequest = new OppfRequest();
            OppfRequest.Params params = new OppfRequest.Params();
            params.setServiceNumber(reservation.getUserId());
            params.setSubmitTime(dateTime);
            params.setSubmitServNumber(reservation.getUserPhone());
            params.setBusiType(product.get(0).getProductName());
            params.setBusiChannel("200020001");
            params.setBusinessId(product.get(0).getBusinessCategory() == 1 ? "GOVERNMENT_AND_ENTERPRISE" : "HOME_BROADBAND");
            params.setBusinessType(product.get(0).getBusinessType() == 1 ? "NG_KHYY" : "H_CPBL");
            params.setSubBusinessType(product.get(0).getSubBusinessType() == 1 ? "00030020002663308" : "00030020002664437");
            params.setAppoimentLevel("2");
            params.setAppoimentRemark(reservation.getUserAddr()+"——"+reservation.getUserAddrDetail());
            oppfRequest.setParams(params);
            HttpHeaders headers = new HttpHeaders();
            headers.add("content-type", "application/json;charset=utf-8");

            HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(oppfRequest), headers);

            log.info("请求能开接口requestUrl:{}",estewardToOppfUrl);
            log.info("请求能开接口request:{}",JSON.toJSONString(requestEntity));
            RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
            ResponseEntity<String> response = restTemplateHttps.postForEntity(estewardToOppfUrl, requestEntity, String.class);
            log.info("请求能开接口response:{}",JSON.toJSONString(response));
            OppfResponse response1 = JSON.parseObject(response.getBody(), OppfResponse.class);
            if(!response1.getResult().getRtnCode().equals("0")){
                log.info("请求能开接口失败:{}", response1.getResult().getRtnMsg());
            }else{
                reservation.setReservationTime(new Date());
                reservation.setReservationOrderId(orderId);
                reservation.setReservationWfId(response1.getResult().getObject().getWfOrder());
                estewardReservationMapper.updateByPrimaryKeySelective(reservation);
            }
        }catch (Exception e) {
            throw new BusinessException(StatusConstant.INTERNAL_ERROR, e.getMessage());
        }


    }
    //查询预约单列表，根据type进行区分
    @Override
    public PageData<EstewardReservationListVO> listReservations(EstewardReservationListParam param) {
        // 1. 构建分页查询条件
        PageHelper.startPage(param.getPageNum(), param.getPageSize(), true); // 确保分页逻辑正确执行

        if(param.getType() == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"type不能为空");
        }
        // 2. 构建查询条件
        EstewardReservationExample example = new EstewardReservationExample();
        EstewardReservationExample.Criteria criteria = example.createCriteria();
        //1：后台 2：购物车 3：预约单
        if(param.getType().equals("1")){
            if(param.getReservationOrderId() != null){
                criteria.andReservationOrderIdEqualTo(param.getReservationOrderId());
            }else{
                criteria.andReservationOrderIdIsNotNull();
            }
            if(param.getUserAddr() != null){
                criteria.andUserAddrEqualTo(param.getUserAddr());
            }
            if(param.getUserId() !=null){
                criteria.andUserIdEqualTo(param.getUserId());
            }
            criteria.andIsHiddenEqualTo("0");
        }else if (param.getType().equals("2")) {
            if(param.getUserId() == null){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"用户id必填");
            }
            criteria.andUserIdEqualTo(param.getUserId());
            criteria.andReservationOrderIdIsNull();

        }else if (param.getType().equals("3")) {
            if(param.getUserId() == null){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"用户id必填");
            }
            criteria.andUserIdEqualTo(param.getUserId());
            criteria.andReservationOrderIdIsNotNull();
        }else{
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"type错误");
        }

        // 3. 执行分页查询
        List<EstewardReservation> reservations = estewardReservationMapper.selectByExample(example);
        PageInfo<EstewardReservation> pageInfo = new PageInfo<>(reservations);

        // 4. 转换VO（使用并行流优化大数据量处理）
        List<EstewardReservationListVO> vos = reservations.parallelStream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        // 5. 封装分页结果

        PageData<EstewardReservationListVO> pageData = new PageData<>();
        pageData.setPage(pageInfo.getPageNum());
        pageData.setData(vos);
        pageData.setCount(pageInfo.getTotal());
        return pageData;
    }



    //已经提交的预约单
    @Override
    public PageData<List<EstewardReservationListVO>> listReservationGroup(EstewardReservationListParam param) {
        // 1. 构建分页查询条件
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        // 2. 构建查询条件
        EstewardReservationExample example = new EstewardReservationExample();
        EstewardReservationExample.Criteria criteria = example.createCriteria();
        if(param.getUserId() == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"用户id必填");
        }
        criteria.andUserIdEqualTo(param.getUserId());
        criteria.andReservationOrderIdIsNotNull();
        int pageSize = param.getPageSize();
        int pageNum = param.getPageNum();

        // 3. 执行分页查询
        List<EstewardReservation> reservations = estewardReservationMapper.selectByExample(example);

        // 4. 转换VO（使用并行流优化大数据量处理）
        List<EstewardReservationListVO> vos = reservations.parallelStream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        List<String> uniqueIds = reservations.stream()
                .map(EstewardReservation::getReservationOrderId)
                .distinct()
                .sorted() // 按 reservationOrderId 升序排序（或自定义规则）
                .collect(Collectors.toList());

        // 2. 计算分页参数
        int total = uniqueIds.size();
        int pages = (int) Math.ceil((double) total / pageSize); // 精确计算总页数
        int start = Math.max((pageNum - 1) * pageSize, 0);
        int end = Math.min(start + pageSize, total);
        List<String> pageIds = uniqueIds.subList(start, end);

        // 3. 预分组：先将 vos 按 reservationOrderId 分组（提升性能）
        Map<String, List<EstewardReservationListVO>> groupedMap = vos.stream()
                .collect(Collectors.groupingBy(
                        EstewardReservationListVO::getReservationOrderId,
                        Collectors.toList()
                ));

        // 4. 按 pageIds 顺序提取分组数据（确保分页顺序一致）
        List<List<EstewardReservationListVO>> currentPageGroups = pageIds.stream()
                .map(id -> groupedMap.getOrDefault(id, Collections.emptyList()))
                .collect(Collectors.toList());

        // 5. 构建分页结果
        PageData<List<EstewardReservationListVO>> pageData = new PageData<>();
        pageData.setPage(pageNum);       // 当前页码
        pageData.setCount(total);        // 总唯一组数（按 reservationOrderId）
        pageData.setData(currentPageGroups); // 当前页的分组数据

        return pageData;
    }

    private EstewardReservationListVO convertToVO(EstewardReservation reservation) {
        EstewardReservationListVO vo = new EstewardReservationListVO();
        BeanUtils.copyProperties(reservation, vo);

        // 处理商品信息
        vo.setProduct(getReservationProducts(reservation.getProductId()));

        // 处理额外信息
        vo.setExtraInfos(getReservationExtras(reservation.getId()));

        // 处理spu信息
        vo.setSpuOfferingInfo(getReservationSpuInfo(reservation.getSpuCode()));

        // 处理spu下商品列表信息
        vo.setSkuOfferingInfoList(getReservationSkuList(reservation.getSpuCode()));

        // 处理配置的商品信息
        vo.setEstewardProduct(getEstewardProducts(reservation.getSpuCode()));

        return vo;
    }

    private SkuOfferingInfo getReservationProducts(String productId) {
        return skuOfferingInfoMapper.selectByPrimaryKey(productId);
    }

    private EstewardProduct getEstewardProducts(String spuCode) {
        List<EstewardProduct> products = estewardProductMapper.selectByExample(
                new EstewardProductExample().createCriteria()
                        .andSpuCodeEqualTo(spuCode)
                        .andDeleteTimeIsNull()
                        .andStatusEqualTo(4)
                        .example()
        );
        if(CollectionUtils.isEmpty(products)){
            return null;
        }else{
            return products.get(0);
        }
    }
    private SpuOfferingInfo getReservationSpuInfo(String spuCode) {
        List<SpuOfferingInfo> spuOfferingInfoList = spuOfferingInfoMapper.selectByExample(
                new SpuOfferingInfoExample().createCriteria()
                        .andOfferingCodeEqualTo(spuCode)
                        .andDeleteTimeIsNull()
                        .example()
        );
        if(CollectionUtils.isEmpty(spuOfferingInfoList)){
            return null;
        }else{
            return spuOfferingInfoList.get(0);
        }
    }

    private List<SkuOfferingInfo> getReservationSkuList(String spuCode) {
        SkuOfferingInfoExample example = new SkuOfferingInfoExample();
        example.createCriteria()
                .andDeleteTimeIsNull()
                .andOfferingStatusNotEqualTo("2")
                .andSpuCodeEqualTo(spuCode);
        example.setOrderByClause("price ASC"); // 按照 skuPrice 升序排序
        return skuOfferingInfoMapper.selectByExample(example);
    }


    private List<EstewardExtraInfoVO> getReservationExtras(String reservationId) {
        // 1. 查询当前预约单的所有额外答案
        EstewardReservationExtraInfoExample infoExample = new EstewardReservationExtraInfoExample();
        infoExample.createCriteria().andReservationIdEqualTo(reservationId);
        List<EstewardReservationExtraInfo> extraInfos =
                estewardReservationExtraInfoMapper.selectByExample(infoExample);

        if (CollectionUtils.isEmpty(extraInfos)) {
            return Collections.emptyList();
        }

        // 2. 提取所有模板ID用于批量查询
        Set<String> templateIds = extraInfos.stream()
                .map(EstewardReservationExtraInfo::getTemplateId)
                .collect(Collectors.toSet());

        // 3. 批量查询问题模板
        EstewardReservationExtraTemplateExample templateExample =
                new EstewardReservationExtraTemplateExample();
        templateExample.createCriteria().andIdIn(new ArrayList<>(templateIds));
        List<EstewardReservationExtraTemplate> templates =
                estewardReservationExtraTemplateMapper.selectByExample(templateExample);

        // 4. 构建模板映射表
        Map<String, String> templateMap = templates.stream()
                .collect(Collectors.toMap(
                        EstewardReservationExtraTemplate::getId,
                        EstewardReservationExtraTemplate::getQuestion
                ));

        // 5. 组合结果
        return extraInfos.stream().map(info -> {
            EstewardExtraInfoVO vo = new EstewardExtraInfoVO();
            vo.setAnswer(info.getAnswer());
            // 安全获取问题，防止模板被删除的情况
            vo.setQuestion(templateMap.getOrDefault(info.getTemplateId(), "问题已删除"));
            return vo;
        }).collect(Collectors.toList());
    }

    //用于后台隐藏生成的预约单
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void hideReservation(String reservationId) {
        EstewardReservation reservation = estewardReservationMapper.selectByPrimaryKey(reservationId);
        if (reservation == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "预约单不存在");
        }
        reservation.setIsHidden("1");
        reservation.setUpdateTime(new Date());
        estewardReservationMapper.updateByPrimaryKeySelective(reservation);

        SkuOfferingInfo skuOfferingInfo = skuOfferingInfoMapper.selectByPrimaryKey(reservation.getProductId());
        // 记录操作日志
        StringBuilder logContent = new StringBuilder("【删除】\n");
        logContent.append("请求人Id: ").append(reservation.getUserId()).append("\n");
        logContent.append("预约商品: ").append(skuOfferingInfo.getOfferingName());
        logService.recordOperateLog(ModuleEnum.ESTEWARD_MANAGE.code,
                EstewardOperateEnum.RESERVATION_OPERATE.code,
                logContent.toString(), LogResultEnum.LOG_SUCESS.code, null);
    }

    // 删除商品（支持购物车状态操作）
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProduct(String reservationId) {
        EstewardReservation reservation = estewardReservationMapper.selectByPrimaryKey(reservationId);
        if (reservation == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "对应商品不存在");
        }
        estewardReservationMapper.deleteByPrimaryKey(reservationId);

    }

    // 切换SKU（仅限未提交的预约单）
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void switchSKU(EstewardSwitchReservationParam param) {
        // 1. 验证预约单状态
        EstewardReservation reservation = estewardReservationMapper.selectByPrimaryKey(param.getReservationId());

        // 2. 验证新旧SKU是否属于同一SPU
        SkuOfferingInfo newProduct = skuOfferingInfoMapper.selectByPrimaryKey(param.getProductId());
        if (newProduct == null || !param.getSpuCode().equals(newProduct.getSpuCode())) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "无效的SKU切换");
        }

        // 3. 检查当前用户下是否存在此SKU
        EstewardReservationExample example = new EstewardReservationExample();
        example.createCriteria()
                .andUserIdEqualTo(reservation.getUserId())
                .andProductIdEqualTo(param.getProductId())
                .andReservationOrderIdIsNull(); // 未提交的条目

        List<EstewardReservation> existingItems = estewardReservationMapper.selectByExample(example);

        if (!existingItems.isEmpty()) {
            // 合并数量
            EstewardReservation existingReservation = existingItems.get(0);
            existingReservation.setProductCount(existingReservation.getProductCount() + reservation.getProductCount());
            existingReservation.setUpdateTime(new Date());
            estewardReservationMapper.updateByPrimaryKeySelective(existingReservation);

            // 删除当前预约单
            estewardReservationMapper.deleteByPrimaryKey(reservation.getId());
        } else {
            // 保持原有逻辑
            reservation.setProductId(param.getProductId());
            estewardReservationMapper.updateByPrimaryKeySelective(reservation);
        }
    }

//    @Override
//    public List<EstewardProduct> getSpuProducts(String spuCode) {
//        EstewardProductExample example = new EstewardProductExample();
//        example.createCriteria().andSpuCodeEqualTo(spuCode);
//        List<EstewardProduct> products = estewardProductMapper.selectByExample(example);
//        return products;
//    }

}