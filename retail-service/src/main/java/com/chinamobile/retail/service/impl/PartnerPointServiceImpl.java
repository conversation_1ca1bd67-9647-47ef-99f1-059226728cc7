package com.chinamobile.retail.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.common.utils.NumberUtil;
import com.chinamobile.iot.sc.entity.retail.FindRetailUserParam;
import com.chinamobile.iot.sc.entity.retail.SyncSkuPointParam;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.RetailManageOperateEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.service.BaseSmsService;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.ExcelUtils;
import com.chinamobile.iot.sc.util.HttpUtil;
import com.chinamobile.retail.config.CommonConstant;
import com.chinamobile.retail.config.ServiceConfig;
import com.chinamobile.retail.config.SmsConfig;
import com.chinamobile.retail.dao.*;
import com.chinamobile.retail.dao.ext.OrderInfoMapperExt;
import com.chinamobile.retail.dao.ext.PartnerPointMapperExt;
import com.chinamobile.retail.dao.ext.PointExchangeMapperExt;
import com.chinamobile.retail.dao.ext.PointOperateMapperExt;
import com.chinamobile.retail.enums.PointExchangeStatusEnum;
import com.chinamobile.retail.enums.PointOperateTypeEnum;
import com.chinamobile.retail.enums.RetailUserRoleEnum;
import com.chinamobile.retail.exception.StatusContant;
import com.chinamobile.retail.pojo.dto.*;
import com.chinamobile.retail.pojo.dto.unionpay.QueryAccountBalanceResponseDTO;
import com.chinamobile.retail.pojo.dto.unionpay.QueryOrderResultResponseDTO;
import com.chinamobile.retail.pojo.dto.unionpay.UploadOrderResponseDTO;
import com.chinamobile.retail.pojo.entity.*;
import com.chinamobile.retail.pojo.mapper.PartnerStatisticsDO;
import com.chinamobile.retail.pojo.mapper.PointDetailDO;
import com.chinamobile.retail.pojo.param.*;
import com.chinamobile.retail.pojo.param.miniprogram.MiniprogramPointDetailParam;
import com.chinamobile.retail.pojo.param.unionpay.*;
import com.chinamobile.retail.pojo.vo.*;
import com.chinamobile.retail.pojo.vo.miniprogram.MiniprogramPointDetailVO;
import com.chinamobile.retail.service.PartnerPointService;
import com.chinamobile.retail.service.Sm4Service;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.common.BaseConstant.SYSTEM_MINI;
import static com.chinamobile.iot.sc.common.BaseConstant.*;

/**
 * 合伙人积分服务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PartnerPointServiceImpl implements PartnerPointService {
    @Resource
    private UserRetailMapper userRetailMapper;
    @Resource
    MiniProgramActivityRankAwardMapper miniProgramActivityRankAwardMapper;
    @Resource
    MiniProgramActivityWeeklyFunAwardMapper miniProgramActivityWeeklyFunAwardMapper;
    @Resource
    MiniProgramActivityUserAwardMapper miniProgramActivityUserAwardMapper;
    @Resource
    private MiniProgramActivityMapper miniProgramActivityMapper;

    @Resource
    private PartnerPointMapper partnerPointMapper;

    @Resource
    private PointOperateMapper pointOperateMapper;

    @Resource
    private PartnerPointMapperExt partnerPointMapperExt;
    @Resource
    private UserMiniProgramMapper userMiniProgramMapper;
    @Resource
    private PointSupplierMapper pointSupplierMapper;

    @Resource
    private OrderInfoMapperExt orderInfoMapperExt;

    @Resource
    private SupplierAssociateProductMapper supplierAssociateProductMapper;

    @Resource
    private PointExchangeMapperExt pointExchangeMapperExt;
    @Resource
    private PointOperateMapperExt pointOperateMapperExt;

    @Resource
    private PointExchangeMapper pointExchangeMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ServiceConfig serviceConfig;

    @Autowired
    private PartnerPointServiceImpl partnerPointService;

    @Resource
    private LogService logService;

    @Autowired
    private SmsConfig smsConfig;


    private ThreadPoolExecutor executor = new ThreadPoolExecutor(8, 16, 1, TimeUnit.MINUTES, new LinkedBlockingDeque<>(10000));

    private static final String dealProcessExchangeTaskLock = "dealProcessExchangeTaskLock";

    @Resource
    private PointExchangePartnerMapper pointExchangePartnerMapper;

    //    @Resource
//    private UserMapper userMapper;
    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private RetailUserServiceImpl retailUserService;
    @Autowired
    private Sm4Service sm4Service;
    @Autowired
    private BaseSmsService baseSmsService;


    @Override
    @DS("query")
    public BaseAnswer<PageInfo<PartnerPointItemDTO>> getPartnerPoints(PartnerPointQueryParam param) {
        PageHelper.startPage(param.getPage(), param.getPageSize());
        if (param.getChannel() == null) {
            param.setChannel(0);
        }
        List<PartnerPointItemDTO> points = partnerPointMapperExt.queryPartnerPointList(param);
        PageInfo<PartnerPointItemDTO> dataPage = new PageInfo<>(points);
        BaseAnswer<PageInfo<PartnerPointItemDTO>> result = new BaseAnswer<>();
        result.setData(dataPage);
        return result;
    }

    @Override
    @DS("query")
    public void exportPartnerPoints(PartnerPointQueryParam param, HttpServletResponse response) {
        List<PartnerPointItemDTO> points = partnerPointMapperExt.queryPartnerPointList(param);
        List<PartnerPointExcelItemDTO> lst = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(points)) {
            lst = points.stream()
                    .map(item -> {
                        PartnerPointExcelItemDTO excelItemDTO = new PartnerPointExcelItemDTO();
                        excelItemDTO.setCity(item.getCity());
                        excelItemDTO.setProvince(item.getProvince());
                        excelItemDTO.setSupplier(item.getSupplier());
                        excelItemDTO.setRoleStr(RetailUserRoleEnum.fromCode(item.getRole()).name);
                        excelItemDTO.setPhone(item.getPhone());
                        excelItemDTO.setName(item.getName());
                        Integer signStatus = item.getSignStatus();
                        excelItemDTO.setSignStatus(signStatus.intValue() == 1 ? "已签约" : "未签约");
                        if (item.getAvailable() != null) {
                            excelItemDTO.setAvailable(new BigDecimal(item.getAvailable()).divide(new BigDecimal(1000)));
                        } else {
                            excelItemDTO.setAvailable(new BigDecimal(0));
                        }

                        if (item.getRedeemed() != null) {
                            excelItemDTO.setRedeemed(new BigDecimal(item.getRedeemed()).divide(new BigDecimal(1000)));
                        } else {
                            excelItemDTO.setRedeemed(new BigDecimal(0));
                        }

                        if (item.getPaying() != null) {
                            excelItemDTO.setPaying(new BigDecimal(item.getPaying()).divide(new BigDecimal(1000)));
                        } else {
                            excelItemDTO.setPaying(new BigDecimal(0));
                        }

                        return excelItemDTO;
                    }).collect(Collectors.toList());
        }
        try {

            ExcelUtils.exportExcel(lst, "合伙人积分清单",
                    "",
                    PartnerPointExcelItemDTO.class,
                    "partner-points-export-",
                    response);
        } catch (IOException e) {
            throw new BusinessException(StatusContant.POINT_EXCEL_EXPORT_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void importPointRedeemedExcel(MultipartFile file, String userId) {
        try {
            List<PointRedeemedImportExcelDTO> pointRedeemedImportExcelDTOList = ExcelUtils.importExcel(file, 0, 1, PointRedeemedImportExcelDTO.class);
            if (CollectionUtils.isNotEmpty(pointRedeemedImportExcelDTOList)) {
                Date now = new Date();
                pointRedeemedImportExcelDTOList.stream().forEach(importExcelDTO -> {
                    if (StringUtils.isEmpty(importExcelDTO.getPhone())) {
                        throw new BusinessException(StatusContant.POINT_EXCEL_IMPORT_ERROR, "兑换积分导入异常，客户经理电话号码不能为空");
                    }

                    if (StringUtils.isEmpty(importExcelDTO.getSupplier())) {
                        throw new BusinessException(StatusContant.POINT_EXCEL_IMPORT_ERROR, "兑换积分导入异常，积分供应商不能为空");
                    }

                    if (importExcelDTO.getRedeemed() == null) {
                        throw new BusinessException(StatusContant.POINT_EXCEL_IMPORT_ERROR, "兑换积分导入异常，兑换积分不能为空");
                    }

                    //根据手机号查找客户经理
                    UserRetailExample userExample = new UserRetailExample();
                    UserRetailExample.Criteria userCriteria = userExample.createCriteria();
                    userCriteria.andPhoneEqualTo(importExcelDTO.getPhone());

                    List<UserRetail> users = userRetailMapper.selectByExample(userExample);
                    if (CollectionUtils.isEmpty(users)) {
                        throw new BusinessException(StatusContant.POINT_EXCEL_IMPORT_ERROR, "兑换积分导入异常，找不到客户经理"
                                + importExcelDTO.getName() + ":" + importExcelDTO.getPhone());
                    }
                    UserRetail user = users.get(0);

                    //根据供应商简称查找供应商
                    PointSupplierExample pointSupplierExample = new PointSupplierExample();
                    PointSupplierExample.Criteria supplierCriteria = pointSupplierExample.createCriteria();
                    supplierCriteria.andFullNameEqualTo(importExcelDTO.getSupplier());

                    List<PointSupplier> pointSuppliers = pointSupplierMapper.selectByExample(pointSupplierExample);
                    if (CollectionUtils.isEmpty(pointSuppliers)) {
                        throw new BusinessException(StatusContant.POINT_EXCEL_IMPORT_ERROR, "兑换积分导入异常，找不到积分供应商"
                                + importExcelDTO.getSupplier());
                    }
                    PointSupplier pointSupplier = pointSuppliers.get(0);

                    //更新客户经理在供应商的积分数据，并记录兑换操作
                    PartnerPointExample partnerPointExample = new PartnerPointExample();
                    PartnerPointExample.Criteria partnerPointCriteria = partnerPointExample.createCriteria();
                    partnerPointCriteria.andPartnerIdEqualTo(user.getId());
                    partnerPointCriteria.andChannelEqualTo(0);
                    partnerPointCriteria.andSupplierIdEqualTo(pointSupplier.getId());
                    List<PartnerPoint> partnerPoints = partnerPointMapper.selectByExample(partnerPointExample);
                    if (CollectionUtils.isEmpty(partnerPoints)) {
                        throw new BusinessException(StatusContant.POINT_EXCEL_IMPORT_ERROR, "兑换积分导入异常，"
                                + importExcelDTO.getName() + "在" + importExcelDTO.getSupplier() + "找不到积分积分记录");
                    }

                    PartnerPoint partnerPoint = partnerPoints.get(0);
                    Long redeemPoint = importExcelDTO.getRedeemed().multiply(new BigDecimal(1000)).longValue();
                    partnerPoint.setAvailable(partnerPoint.getAvailable() - redeemPoint);
                    partnerPoint.setRedeemed(partnerPoint.getRedeemed() != null ?
                            (partnerPoint.getRedeemed() + redeemPoint) : redeemPoint);
                    partnerPoint.setUpdateTime(importExcelDTO.getTime() != null ? importExcelDTO.getTime() : now);

                    partnerPointMapper.updateByPrimaryKeySelective(partnerPoint);

                    PointOperate pointOperate = new PointOperate();

                    pointOperate.setPoint(redeemPoint);
                    pointOperate.setUserId(user.getId());
                    pointOperate.setSupplierId(pointSupplier.getId());
                    pointOperate.setOperatorId(userId);
                    pointOperate.setCreateTime(importExcelDTO.getTime() != null ? importExcelDTO.getTime() : now);
                    pointOperate.setType(1);
                    pointOperate.setId(BaseServiceUtils.getId());
                    pointOperateMapper.insertSelective(pointOperate);
                });
            }
        } catch (IOException e) {
            throw new BusinessException(StatusContant.POINT_EXCEL_IMPORT_ERROR, "导入表格解析异常");
        }
    }

    @Override
    @DS("query")
    public AvailablePointVO getAvailablePoint(Integer channel, String userId, LoginIfo4Redis loginIfo4Redis) {
        if (channel == null) {
            channel = 0;
        }

        UserMiniProgram userMiniProgram = null;
        if (StringUtils.equals(SYSTEM_MINI,loginIfo4Redis.getSystem())) {
            userMiniProgram = userMiniProgramMapper.selectByPrimaryKey(userId);
            if (userMiniProgram == null) {
                throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_USER_NOT_EXIST, "活动用户不存在");
            }
            if (channel == 1) {
                userId = userMiniProgram.getUserId();
            } else if (StringUtils.equals("4",userMiniProgram.getRoleType())) {
                //客户经理暂无产品积分
                AvailablePointVO availablePointVO = new AvailablePointVO();
                availablePointVO.setPoint(0L);
                return availablePointVO;
            } else {
                FindRetailUserParam param = new FindRetailUserParam();
                param.setUserId(userMiniProgram.getUserId());
                BaseAnswer<String> answer = retailUserService.findRetailUserId(param);
                if (answer == null || StringUtils.isBlank(answer.getData())) {
                    AvailablePointVO availablePointVO = new AvailablePointVO();
                    availablePointVO.setPoint(0L);
                    return availablePointVO;
                }

                userId = answer.getData();
            }
        }
        List<PartnerPoint> partnerPointList = partnerPointMapper.selectByExample(
                new PartnerPointExample().createCriteria()
                        .andPartnerIdEqualTo(userId)
                        .andChannelEqualTo(channel)
                        .example()
        );
        Long totalAvailable = new Long(0);
        if (partnerPointList != null) {
            for (PartnerPoint partnerPoint : partnerPointList) {
                Long available = partnerPoint.getAvailable();
                if (available != null) {
                    totalAvailable = Long.sum(totalAvailable, available);
                }
            }
        }
        AvailablePointVO availablePointVO = new AvailablePointVO();
        availablePointVO.setPoint(totalAvailable);
        return availablePointVO;
    }

    @Override
    @DS("query")
    public PageData<PointOperateVO> getPointDetails(String userId, PointDetailParam param) {
        if (null == param.getPage()) {
            param.setPage(1);
        }
        if (null == param.getNum()) {
            param.setNum(10);
        }
        if (param.getChannel() == null) {
            param.setChannel(0);
        }
        PageHelper.startPage(param.getPage(), param.getNum());

        List<PointOperate> pointOperateList = pointOperateMapper.selectByExample(
                new PointOperateExample().createCriteria()
                        .andUserIdEqualTo(userId)
                        .andChannelEqualTo(param.getChannel())
                        .example()
        );
        List<PointOperateVO> pointOperateVOS = new ArrayList<>();
        if (pointOperateList != null) {
            pointOperateVOS = pointOperateList.stream().map(pointOperate -> {
                PointOperateVO pointOperateVO = new PointOperateVO();
                BeanUtils.copyProperties(pointOperate, pointOperateVO);
                return pointOperateVO;
            }).collect(Collectors.toList());
        }
        PageInfo<PointOperateVO> pageInfo = new PageInfo<>(pointOperateVOS);
        PageData<PointOperateVO> pageData = new PageData<>();
        pageData.setPage(pageInfo.getPageNum());
        pageData.setData(pageInfo.getList());
        pageData.setCount(pageInfo.getTotal());
        return pageData;
    }

    @Override
    @DS("query")
    public PageData<OrderInfoVO> getOrderInfos(String userId, OrderInfoParam param) {
        if (null == param.getPage()) {
            param.setPage(1);
        }
        if (null == param.getNum()) {
            param.setNum(10);
        }
        PageHelper.startPage(param.getPage(), param.getNum());

        param.setUserId(userId);
        List<OrderInfoVO> orderInfoVOS = orderInfoMapperExt.getOrderInfoList(param);
        PageInfo<OrderInfoVO> pageInfo = new PageInfo<>(orderInfoVOS);
        final SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月d日");
        if (orderInfoVOS != null) {
            orderInfoVOS.stream().forEach(orderInfoVO -> {
                if (0 == orderInfoVO.getStatus()) {
                    // 已付款，订单创建，确认收货后10天到账
                    orderInfoVO.setStatusStr("已付款");
                    orderInfoVO.setDesc("已付款，确认收货后10天到账");
                } else if (1 == orderInfoVO.getStatus()) {
                    // 确认收货
                    Date confirmTime = orderInfoVO.getOrderStatusTime() != null ? orderInfoVO.getOrderStatusTime() : orderInfoVO.getUpdateTime();
                    Calendar doneCalendar = Calendar.getInstance();
                    doneCalendar.setTime(confirmTime);
                    // 确认收货+10天，积分到账
                    doneCalendar.add(Calendar.DAY_OF_YEAR, 10);
                    orderInfoVO.setStatusStr("已收货");
                    orderInfoVO.setDesc("已收货，预计" + sdf.format(doneCalendar.getTime()) + "到账");

                } else if (3 == orderInfoVO.getStatus()) {
                    // 交易完成，积分到账
                    Date date = orderInfoVO.getOrderStatusTime() != null ? orderInfoVO.getOrderStatusTime() : orderInfoVO.getUpdateTime();
                    orderInfoVO.setStatusStr("完成交易");
                    orderInfoVO.setDesc("完成交易，" + sdf.format(date) + "到账");

                } else if (4 == orderInfoVO.getStatus() || 5 == orderInfoVO.getStatus() || 6 == orderInfoVO.getStatus()) {
                    // 已退款,对于退款这类交易失败的订单，不计算积分
                    orderInfoVO.setStatusStr("已退款");
                    orderInfoVO.setDesc("已退款");
                }
            });
        }
        if (null == orderInfoVOS) {
            orderInfoVOS = new ArrayList<>();
        }

        PageData<OrderInfoVO> pageData = new PageData<>();
        pageData.setPage(pageInfo.getPageNum());
        pageData.setData(orderInfoVOS);
        pageData.setCount(pageInfo.getTotal());
        return pageData;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @DS("save")
    public void syncSkuPoint(SyncSkuPointParam param) {
        Date now = new Date();
        log.info("订单积分同步：{}", JSON.toJSONString(param));
        //积分数量转换，避免出现非整十的厘（金额最小一分钱）
        Long point = param.getPoint();
        point = NumberUtil.getMoneyUnit(point);
        //不再校验用户是否已经同步到OS，先把分记上
//        //找到合伙人在对应积分供应商瞎对应的积分记录
//        UserRetail user = userRetailMapper.selectByPrimaryKey(param.getUserId());
//        if (user == null) {
//            throw new BusinessException(StatusContant.ORDER_POINT_ERROR, "找不到对应合伙人");
//        }

        PointSupplier supplier = pointSupplierMapper.selectByPrimaryKey(param.getSupplierId());
        if (supplier == null) {
            throw new BusinessException(StatusContant.ORDER_POINT_ERROR, "找不到对应积分供应商");
        }

        PartnerPointExample partnerPointExample = new PartnerPointExample();
        PartnerPointExample.Criteria partnerCriteria = partnerPointExample.createCriteria();
        partnerCriteria.andSupplierIdEqualTo(param.getSupplierId());
        partnerCriteria.andPartnerIdEqualTo(param.getUserId());
        partnerCriteria.andChannelEqualTo(0);
        List<PartnerPoint> partnerPoints = partnerPointMapper.selectByExample(partnerPointExample);
        PartnerPoint partnerPoint = null;
        boolean created = false;
        if (CollectionUtils.isEmpty(partnerPoints)) {
            created = true;
            partnerPoint = new PartnerPoint();
            partnerPoint.setId(BaseServiceUtils.getId());
            partnerPoint.setPartnerId(param.getUserId());
            partnerPoint.setSupplierId(param.getSupplierId());
            partnerPoint.setCreateTime(now);
            partnerPoint.setChannel(0);
        } else {
            partnerPoint = partnerPoints.get(0);
        }

        //订单状态为0,订单创建，添加总积分
        //订单状态为3,订单完成（不可退款），添加积分奖励记录
        //订单状态为4，订单对款，减少总积分，添加扣减记录
        //订单状态为11，部分退款

        switch (param.getOrderStatus()) {
            case 0:
                partnerPoint.setTotal(partnerPoint.getTotal() != null ? partnerPoint.getTotal() + point : point);
                partnerPoint.setCurrentMonth(partnerPoint.getCurrentMonth() != null ? partnerPoint.getCurrentMonth() + point : point);

                break;
//            case 3:
//                partnerPoint.setCurrentMonth(partnerPoint.getCurrentMonth() != null ? partnerPoint.getCurrentMonth() + param.getPoint() : param.getPoint());
//                break;
            case 4:
            case 11:
                partnerPoint.setTotal(partnerPoint.getTotal() - point);
                if (isCurrentMonthOrder(param.getCreateTime())) {
                    partnerPoint.setCurrentMonth(partnerPoint.getCurrentMonth() - point);
                }
//                partnerPoint.setAvailable(partnerPoint.getAvailable() != null ? partnerPoint.getAvailable() + param.getPoint() : param.getPoint());
                break;
            default:
                break;
        }
        partnerPoint.setUpdateTime(now);

        if (created) {
            partnerPointMapper.insertSelective(partnerPoint);
        } else {
            partnerPointMapper.updateByPrimaryKeySelective(partnerPoint);
        }

        //订单完成添加积分奖励
        if (param.getOrderStatus() == 3) {
            PointOperate pointOperate = new PointOperate();
            pointOperate.setPoint(point);
            pointOperate.setUserId(param.getUserId());
            pointOperate.setSupplierId(param.getSupplierId());
            pointOperate.setCreateTime(now);
            pointOperate.setId(BaseServiceUtils.getId());
            pointOperate.setType(1);
            pointOperate.setReason("订单激励");
            pointOperate.setChannel(0);
            pointOperate.setOrderId(param.getOrderId());
            pointOperateMapper.insertSelective(pointOperate);
            log.info("订单完成添加积分奖励：{}", JSON.toJSONString(pointOperate));
        }
        //订单退款完成（特殊退款）,软删除积分奖励,扣除月初已经计算过的可兑换积分
        if (param.getOrderStatus() == 4 && StringUtils.isNotEmpty(param.getOrderId())) {
            Date monthBegin = DateTimeUtil.getMonthBegin(now);
            PointOperateExample example = new PointOperateExample().createCriteria().andOrderIdEqualTo(param.getOrderId()).example();
            List<PointOperate> pointOperateList = pointOperateMapper.selectByExample(example);
            if(pointOperateList.isEmpty()){
                return;
            }
            for (PointOperate pointOperate : pointOperateList) {
                Date createTime = pointOperate.getCreateTime();
                if(createTime.before(monthBegin) && partnerPoint.getAvailable() != null ){
                    partnerPoint.setAvailable(partnerPoint.getAvailable() - pointOperate.getPoint());
                    partnerPoint.setUpdateTime(now);
                    partnerPointMapper.updateByPrimaryKeySelective(partnerPoint);
                    log.info("订单退款完成，扣除月初已经计算过的可兑换积分：｛｝",JSON.toJSONString(pointOperate));
                }
                pointOperate.setDeleteTime(now);
                pointOperateMapper.updateByPrimaryKeySelective(pointOperate);
                log.info("订单退款完成，软删除积分奖励：｛｝",JSON.toJSONString(pointOperate));
            }
        }
        //部分退款成功,更新积分奖励的数量，扣除月初已经计算过的可兑换积分
        if (param.getOrderStatus() == 11 && StringUtils.isNotEmpty(param.getOrderId())) {
            Date monthBegin = DateTimeUtil.getMonthBegin(now);
            PointOperateExample example = new PointOperateExample().createCriteria().andOrderIdEqualTo(param.getOrderId()).example();
            List<PointOperate> pointOperateList = pointOperateMapper.selectByExample(example);
            if(pointOperateList.isEmpty()){
                return;
            }
            //同一个订单的激励的创建时间是一样的，随便取一个
            Date createTime = pointOperateList.get(0).getCreateTime();
            if(createTime.before(monthBegin) && partnerPoint.getAvailable() != null ){
                //这里的point是应该扣掉的积分
                partnerPoint.setAvailable(partnerPoint.getAvailable() - point);
                partnerPoint.setUpdateTime(now);
                partnerPointMapper.updateByPrimaryKeySelective(partnerPoint);
                log.info("订单部分退款完成，扣除月初已经计算过的部分退款的可兑换积分：｛｝",point);
            }
            //平均扣除订单激励中部分退款的部分,到下月初计算可兑换积分的时候金额才是对的
            int size = pointOperateList.size();
            long eachPoint = (point / size);
            for (int i = 0; i < pointOperateList.size(); i++) {
                //最后一条数据把剩余的积分扣掉
                if(i == pointOperateList.size() -1 ){
                    eachPoint = point - eachPoint*(pointOperateList.size() -1);
                }
                PointOperate pointOperate = pointOperateList.get(i);
                pointOperate.setPoint(pointOperate.getPoint() - eachPoint);
                pointOperateMapper.updateByPrimaryKeySelective(pointOperate);
                log.info("订单部分退款完成，扣除部分退款的积分奖励：｛｝",JSON.toJSONString(pointOperate));
            }
        }
        log.info("订单同步积分成功，{}：{}", created ? "新建":"更新",JSON.toJSONString(partnerPoint));
    }

    private boolean isCurrentMonthOrder(String orderCreateTime) {
        try {
            Date orderDate = DateTimeUtil.getFormatDate(orderCreateTime, DateTimeUtil.DB_TIME_STR);
            return DateUtil.month(orderDate) == DateUtil.month(new Date());
        } catch (ParseException exception) {
            return false;
        }
    }

    @Override
    @DS("query")
    public BaseAnswer<PartnerPointInfoVO> getPointInfo(Integer channel, String userId) {
        if (channel == null) {
            channel = 0;
        }
        PartnerPointExample example = new PartnerPointExample().createCriteria().andPartnerIdEqualTo(userId).andChannelEqualTo(channel).example();
        List<PartnerPoint> partnerPoints = partnerPointMapper.selectByExample(example);
        PartnerPointInfoVO vo = new PartnerPointInfoVO();
        if (partnerPoints.isEmpty()) {
            return BaseAnswer.success(vo);
        }
        //合伙人积分是区分供应商保存的，所以将各个供应商的积分加起来
        for (PartnerPoint partnerPoint : partnerPoints) {
            vo.setPayingPoint(vo.getPayingPoint() + (partnerPoint.getPaying() == null ? 0L : partnerPoint.getPaying()));
            vo.setCurrentMonthPoint(vo.getCurrentMonthPoint() + (partnerPoint.getCurrentMonth() == null ? 0L : partnerPoint.getCurrentMonth()));
            vo.setAvailablePoint(vo.getAvailablePoint() + (partnerPoint.getAvailable() == null ? 0L : partnerPoint.getAvailable()));
        }
        return BaseAnswer.success(vo);
    }

    @Override
    @DS("query")
    public BaseAnswer<PageData<PointDetailVO>> getPointInAndOutDetail(PointDetailParam param, String partnerId,LoginIfo4Redis loginIfo4Redis) {
        Integer pageNum = param.getPage();
        Integer pageSize = param.getNum();
        PageData<PointDetailVO> data = new PageData<>();
        data.setPage(pageNum);
        if (param.getChannel() == null) {
            param.setChannel(0);
        }
        if (StringUtils.equals(SYSTEM_MINI,loginIfo4Redis.getSystem())) {
            //小程序用户查产品积分
            UserMiniProgram userMiniProgram = userMiniProgramMapper.selectByPrimaryKey(partnerId);
            if (userMiniProgram == null) {
                throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_USER_NOT_EXIST, "小程序用户不存在");
            }
            if (StringUtils.equals("4",userMiniProgram.getRoleType())) {
                //客户经理暂无产品积分
                return BaseAnswer.success(data);
            }

            FindRetailUserParam findRetailUserParam = new FindRetailUserParam();
            findRetailUserParam.setUserId(userMiniProgram.getUserId());
            BaseAnswer<String> answer = retailUserService.findRetailUserId(findRetailUserParam);
            if (answer == null || StringUtils.isBlank(answer.getData())) {
                return BaseAnswer.success(data);
            }

            partnerId = answer.getData();

        }
        PointOperateExample example = new PointOperateExample().createCriteria().andUserIdEqualTo(partnerId).andChannelEqualTo(param.getChannel()).example();
        example.orderBy("create_time DESC");
        PageHelper.startPage(pageNum, pageSize);
        List<PointOperate> pointOperateList = pointOperateMapper.selectByExample(example);
        if (pointOperateList.isEmpty()) {
            return BaseAnswer.success(data);
        }
        PageInfo<PointOperate> pageInfo = new PageInfo<>(pointOperateList);
        data.setCount(pageInfo.getTotal());
        List<PointDetailVO> voList = pointOperateList.stream().map(p -> {
            PointDetailVO vo = new PointDetailVO();
            vo.setPoint(p.getPoint());
            vo.setTime(p.getCreateTime());
            PointOperateTypeEnum operateTypeEnum = PointOperateTypeEnum.fromCode(p.getType());
            if (operateTypeEnum.code.intValue() == PointOperateTypeEnum.DECREASE.code.intValue() && StringUtils.isNotEmpty(p.getReason())) {
                //扣减积分需要加上备注
                vo.setName(operateTypeEnum.desc + "(" + p.getReason() + ")");
                //扣减返回负数
                vo.setPoint(0 - vo.getPoint());
            } else {
                vo.setName(operateTypeEnum.desc);
            }
            vo.setType(operateTypeEnum.code);
            return vo;
        }).collect(Collectors.toList());
        data.setData(voList);
        return BaseAnswer.success(data);
    }

    @Override
    @DS("query")
    public BaseAnswer<PageData<MiniprogramPointDetailVO>> getPointInAndOutDetailMiniprogramFront(MiniprogramPointDetailParam param, String partnerId) {
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        PageData<MiniprogramPointDetailVO> data = new PageData<>();
        data.setPage(pageNum);
        UserMiniProgram userMiniProgram = userMiniProgramMapper.selectByPrimaryKey(partnerId);
        if (userMiniProgram == null || userMiniProgram.getUserId() == null) {
            throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_USER_NOT_EXIST);
        }
        PageHelper.startPage(pageNum, pageSize);
        List<MiniprogramPointDetailDTO> pointOperateList = pointOperateMapperExt.getPointInAndOutDetailMiniprogramFront(userMiniProgram.getUserId());
        if (pointOperateList.isEmpty()) {
            return BaseAnswer.success(data);
        }
        PageInfo<MiniprogramPointDetailDTO> pageInfo = new PageInfo<>(pointOperateList);
        data.setCount(pageInfo.getTotal());

        List<MiniprogramPointDetailVO> voList = pointOperateList.stream().map(p -> {
            MiniprogramPointDetailVO vo = new MiniprogramPointDetailVO();
            BeanUtils.copyProperties(p, vo);
            PointOperateTypeEnum operateTypeEnum = PointOperateTypeEnum.fromCode(p.getType());
            if (operateTypeEnum.code.intValue() == PointOperateTypeEnum.DECREASE.code.intValue() && StringUtils.isNotEmpty(p.getReason())) {
                //扣减积分需要加上备注
                vo.setName(operateTypeEnum.desc + "(" + p.getReason() + ")");
                //扣减返回负数
                vo.setPoint(0 - vo.getPoint());
            } else {
                vo.setName(operateTypeEnum.desc);
            }
            vo.setType(operateTypeEnum.code);
            return vo;
        }).collect(Collectors.toList());
        data.setData(voList);
        return BaseAnswer.success(data);
    }
    private void createGetPointExchangeDetailLog(String supplierId,String queryUserId,String userId,LoginIfo4Redis loginIfo4Redis) {
        //记录日志
        String content = "【查看详情】";
        if (StringUtils.equals(SYSTEM_MINI,loginIfo4Redis.getSystem())) {
            UserMiniProgram userMiniProgram = userMiniProgramMapper.selectByPrimaryKey(userId);
            content = content.concat("\n合作人角色");
            String roleType = userMiniProgram.getRoleType();
            if(StringUtils.equals(roleType,"4")){
                content = content.concat("客户经理");
            }else if(StringUtils.equals(roleType,"1")){
                content = content.concat("一级分销员");
            }else if(StringUtils.equals(roleType,"2")){
                content = content.concat("二级分销员");
            }else if(StringUtils.equals(roleType,"3")){
                content = content.concat("渠道商");
            }else{}
            if(userMiniProgram.getName()!=null){
                content = content.concat("\n合作人姓名").concat(userMiniProgram.getName());
            }
        } else {
            UserRetail userRetail = userRetailMapper.selectByPrimaryKey(queryUserId);
            if(userRetail != null){
                content = content.concat("\n合作人角色");
                Integer retailRoleType = userRetail.getRoleType();
                if(retailRoleType == 0){
                    content = content.concat("客户经理");
                }else if(retailRoleType == 1){
                    content = content.concat("一级分销员");
                }else if(retailRoleType == 2){
                    content = content.concat("二级分销员");
                }else{}
                if(userRetail.getName()!=null){
                    content = content.concat("\n合作人姓名").concat(userRetail.getName());
                }

            }
        }

        PointSupplier pointSupplier = pointSupplierMapper.selectByPrimaryKey(supplierId);
        if(pointSupplier != null){
            content = content.concat("\n积分供应商").concat(pointSupplier.getFullName());
        }
        logService.recordOperateLog(ModuleEnum.RETAIL_MANAGE.code,
                RetailManageOperateEnum.PARTNER_POINTS_MANAGE.code,
                content, LogResultEnum.LOG_SUCESS.code, null);
    }
    @Override
    @DS("query")
    public BaseAnswer<PageData<PointDetailVO>> getPointExchangeDetail(PointPayDetailParam param, String userId,LoginIfo4Redis loginIfo4Redis) {
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        String selectedUserId = param.getSelectedUserId();
        String supplierId = param.getSupplierId();
        PageData<PointDetailVO> data = new PageData<>();
        data.setPage(pageNum);
        //优先用传入的用户id查询
        String queryUserId = StringUtils.isEmpty(selectedUserId) ? userId : selectedUserId;
        //小程序用户只能使用token对应的UserRetail中的ID
        if (StringUtils.equals(SYSTEM_MINI,loginIfo4Redis.getSystem())) {
            UserMiniProgram userMiniProgram = userMiniProgramMapper.selectByPrimaryKey(userId);
            if(userMiniProgram == null || userMiniProgram.getUserId() == null){
                throw new BusinessException(StatusContant.MINI_PROGRAM_ACTIVITY_USER_NOT_EXIST);
            }
            if(param.getChannel() != null && param.getChannel() == 1) {
                //查询活动积分，直接使用商城UserId查询
                queryUserId = userMiniProgram.getUserId();
            } else {
                //查询产品积分,
                //客户经理没有积分
                if (StringUtils.equals(userMiniProgram.getRoleType(),"4")) {
                    createGetPointExchangeDetailLog(supplierId,queryUserId,userId,loginIfo4Redis);
                    return BaseAnswer.success(data);
                }
                FindRetailUserParam findRetailUserParam = new FindRetailUserParam();
                findRetailUserParam.setUserId(userMiniProgram.getUserId());
                BaseAnswer<String> answer = retailUserService.findRetailUserId(findRetailUserParam);
                if(answer == null || StringUtils.isBlank(answer.getData())){
                    createGetPointExchangeDetailLog(supplierId,queryUserId,userId,loginIfo4Redis);
                    return BaseAnswer.success(data);
                }

                queryUserId = answer.getData();
            }
        }
        //前端和后端需要的时间不同，前端是兑换更新时间，后端是兑换发生时间
        Boolean front = StringUtils.isEmpty(selectedUserId) ? true : false;
        PageHelper.startPage(pageNum, pageSize);
        if (param.getChannel() == null) {
            param.setChannel(0);
        }
        List<PointDetailDO> doList = pointExchangeMapperExt.getPointExchangeDetail(queryUserId, supplierId,param.getChannel());

        createGetPointExchangeDetailLog(supplierId,queryUserId,userId,loginIfo4Redis);
        if (doList.isEmpty()) {
            return BaseAnswer.success(data);
        }
        PageInfo<PointDetailDO> pageInfo = new PageInfo<>(doList);
        data.setCount(pageInfo.getTotal());
        List<PointDetailVO> dataList = doList.stream().map(p -> {
            PointDetailVO vo = new PointDetailVO();
            BeanUtils.copyProperties(p, vo);
            vo.setTime(front ? p.getUpdateTime() : p.getCreateTime());
            PointExchangeStatusEnum exchangeStatusEnum = PointExchangeStatusEnum.fromCode(p.getStatus());
            vo.setName(exchangeStatusEnum.desc);
            vo.setType(exchangeStatusEnum.code);
            //加上负号
            vo.setPoint(0 - vo.getPoint());
            return vo;
        }).collect(Collectors.toList());
        data.setData(dataList);
        return BaseAnswer.success(data);
    }

    @Override
    @DS("query")
    public BaseAnswer<PointExchangeInfoVO> pointExchangeInfo(PointExchangeParam param, String userId) {
        //加锁，任何用户不能操作同一个积分供应商下的“发起兑换”,避免超额兑换。10分钟后或者提交兑换任务后且后台处理完该批次用户的可用积分后解锁
        Boolean getLock = stringRedisTemplate.opsForValue().setIfAbsent(CommonConstant.redis_exchange_exist_key, new Date().getTime() + "_" + userId);
        if (!getLock) {
            throw new BusinessException(StatusContant.EXCHANGE_PLAN_SUBMIT_RUNNING);
        }
        try {
            stringRedisTemplate.expire(CommonConstant.redis_exchange_exist_key, serviceConfig.getExchangeUnlockSeconds(), TimeUnit.SECONDS);
            PointExchangeItem pointExchangeItem = param.getExchangeList().get(0);
            String supplierId = pointExchangeItem.getSupplierId();
            List<PartnerPointItem> partnerPointList = pointExchangeItem.getPartnerPointList();
            PointSupplier pointSupplier = pointSupplierMapper.selectByPrimaryKey(supplierId);
            if (pointSupplier == null) {
                throw new BusinessException(StatusContant.NO_DATA, "找不到积分供应商");
            }
            PointExchangeInfoVO vo = new PointExchangeInfoVO();
            vo.setSupplierName(pointSupplier.getFullName());
            vo.setPartnerCount(partnerPointList.size());
            //查询用户积分，判断是否足够
            List<String> partnerIdList = partnerPointList.stream().map(p -> {
                return p.getPartnerId();
            }).collect(Collectors.toList());
            Long minAvailablePoint = partnerPointMapperExt.getMinAvailablePoint(partnerIdList, supplierId);
            if (minAvailablePoint < 0L) {
                throw new BusinessException(StatusContant.PARTNER_PONIT_NOT_ENOUGH);
            }
            //一期的积分都是全额兑换
            long pointCount = partnerPointMapperExt.getSumAvailablePoint(partnerIdList, supplierId);
            vo.setPointCount(pointCount);
            return BaseAnswer.success(vo);
        } catch (Exception e) {
            //发生异常，删除锁
            if (getLock != null && getLock) {
                stringRedisTemplate.delete(CommonConstant.redis_exchange_exist_key);
            }
            if (e instanceof BusinessException) {
                throw e;
            } else {
                log.error("获取积分兑换信息发生错误", e);
                throw new BusinessException(StatusContant.INTERNAL_ERROR);
            }
        }
    }

    private PartnerPoint selectBySupplierAndPartner(String supplierId, String partnerId,Integer channel) {
        PartnerPointExample example = new PartnerPointExample().createCriteria().andSupplierIdEqualTo(supplierId).andPartnerIdEqualTo(partnerId).andChannelEqualTo(channel).example();
        return partnerPointMapper.selectByExample(example).get(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public BaseAnswer<Void> pointExchange(PointExchangeParam param, String userId) {
        log.info("发起积分兑换开始,发起人:{},请求参数:{}", userId, JSON.toJSONString(param));
        PointExchangeItem pointExchangeItem = param.getExchangeList().get(0);
        String supplierId = pointExchangeItem.getSupplierId();
        if (param.getChannel() == null) {
            param.setChannel(0);
        }
        List<PartnerPointItem> partnerPointList = pointExchangeItem.getPartnerPointList();
        PointSupplier pointSupplier = pointSupplierMapper.selectByPrimaryKey(supplierId);
        if (pointSupplier == null) {
            throw new BusinessException(StatusContant.NO_DATA, "找不到积分供应商");
        }
        if (partnerPointList.isEmpty()) {
            //空集合表示该积分供应商下全部可兑换积分大于0的用户
            List<String> data = getAllPartnerIdBySupplier(supplierId, param.getChannel()).getData();
            if (data.isEmpty()) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "没有可兑换积分大于0的用户");
            }
            partnerPointList = data.stream().map(d -> {
                PartnerPointItem item = new PartnerPointItem();
                item.setPartnerId(d);
                return item;
            }).collect(Collectors.toList());

        }

        if(param.getChannel()==1){
            MiniProgramActivity miniProgramActivity = miniProgramActivityMapper.selectByPrimaryKey(param.getActivityId());
            if(miniProgramActivity==null){
                throw new BusinessException(StatusContant.NO_DATA,"找不到对应活动");
            }
        }
        //从银联接口获取供应商积分余额，判断是否足够
        Long supplierBalance = getSupplierBalance(pointSupplier);
        if (supplierBalance == null) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "获取【" + pointSupplier.getFullName() + "】积分失败");
        }
        if (supplierBalance == 0L) {
            throw new BusinessException(StatusContant.PARAM_ERROR, "积分供应商【" + pointSupplier.getFullName() + "】余额为0,无法发起兑换");
        }

        //加锁，不能再操作同一个积分供应商下的“发起兑换”,避免超额兑换。30分钟后或者提交兑换任务且后台处理完该批次用户的可用积分后解锁
        Boolean getLock = null;
        String lockKey = CommonConstant.redis_exchange_exist_key + supplierId;
        try {
            getLock = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1");
            if (!getLock) {
                throw new BusinessException(StatusContant.EXCHANGE_PLAN_SUBMIT_RUNNING);
            }
            stringRedisTemplate.expire(lockKey, serviceConfig.getExchangeUnlockSeconds(), TimeUnit.SECONDS);
            //插入积分兑换计划
            PointExchange pointExchange = addPointExchange(supplierId, userId, null, partnerPointList.size(), null);

            //异步进行积分兑换，否则等待兑换过程太长时间
            List<PartnerPointItem> finalPartnerPointList = partnerPointList;
            executor.execute(() -> {
                Date now = new Date();
                boolean pointNotEnough = false;
                //用户id和用户积分实体类对应关系
                HashMap<String, Long> partnerIdPointMap = new HashMap<>();
                HashMap<String, String> partnerIdPhoneMap = new HashMap<>();
                HashMap<String, PartnerPoint> partnerIdDataMap = new HashMap<>();
                List<String> allPartnerIdList = new ArrayList<>();
                //总积分量按照实际计算的为准
                Long totalPoint = 0L;
                //对积分数量进行判断，避免超额发放。这里大量查询速度会很慢,所以异步进行积分兑换
                log.info("开始查询用户积分");
                for (PartnerPointItem item : finalPartnerPointList) {
                    allPartnerIdList.add(item.getPartnerId());
                    Long point = item.getPoint() == null ? 0L : item.getPoint();
                    PartnerPoint partnerPoint = selectBySupplierAndPartner(supplierId, item.getPartnerId(), param.getChannel());
                    if (partnerPoint == null || partnerPoint.getAvailable() == null
                            || partnerPoint.getAvailable() <= 0L || partnerPoint.getAvailable().longValue() < point.longValue()) {
                        //存在可兑换积分不足的用户，则整个兑换计划失败（正常情况不会发生）
                        log.error("存在可兑换积分不足的用户:{}，兑换直接失败,不请求银联，用户无感知", item.getPartnerId());
                        failPointExchange(pointExchange, "存在可兑换积分不足的用户:" + item.getPartnerId(), 0, 0L,now);
                        pointNotEnough = true;
                        break;
                    }
                    if (param.getChannel() == 1) {
                        List<UserMiniProgram> userMiniPrograms = userMiniProgramMapper.selectByExample(new UserMiniProgramExample().createCriteria().andUserIdEqualTo(item.getPartnerId()).example());
                        if (userMiniPrograms.isEmpty()) {
                            log.error("用户:{}不存在或其手机号不存在", item.getPartnerId());
                            failPointExchange(pointExchange, "用户:" + item.getPartnerId() + "不存在或其手机号不存在", 0, 0L, now);
                            pointNotEnough = true;
                            break;
                        }
                        partnerIdPhoneMap.put(item.getPartnerId(), userMiniPrograms.get(0).getPhone());
                        //查询活动用户获得的积分
                        List<MiniProgramActivityUserAward> activityUserAwards = miniProgramActivityUserAwardMapper.selectByExample(new MiniProgramActivityUserAwardExample().createCriteria().andUserIdEqualTo(item.getPartnerId()).andActivityIdEqualTo(param.getActivityId()).example());
                        if(activityUserAwards.isEmpty()){
                            failPointExchange(pointExchange, "用户:" + item.getPartnerId() + "未获得积分", 0, 0L, now);
                            pointNotEnough = true;
                            break;
                        }
                        MiniProgramActivityUserAward activityUserAward = activityUserAwards.get(0);
                        MiniProgramActivity miniProgramActivity = miniProgramActivityMapper.selectByPrimaryKey(param.getActivityId());
                        if(miniProgramActivity.getActivityType()==1){
                            MiniProgramActivityRankAward miniProgramActivityRankAward = miniProgramActivityRankAwardMapper.selectByPrimaryKey(activityUserAward.getAwardId());
                            if (miniProgramActivityRankAward == null || miniProgramActivityRankAward.getType() != 1) {
                                failPointExchange(pointExchange, "用户:" + item.getPartnerId() + "未获得积分,奖品积分不存在", 0, 0L, now);
                                pointNotEnough = true;
                                break;
                            }
                            point = miniProgramActivityRankAward.getPoints()*1000;
                        }else if(miniProgramActivity.getActivityType()==2){
                            MiniProgramActivityWeeklyFunAward miniProgramActivityWeeklyAward = miniProgramActivityWeeklyFunAwardMapper.selectByPrimaryKey(activityUserAward.getAwardId());
                            if (miniProgramActivityWeeklyAward == null || miniProgramActivityWeeklyAward.getType() != 1) {
                                failPointExchange(pointExchange, "用户:" + item.getPartnerId() + "未获得积分,奖品积分不存在", 0, 0L, now);
                                pointNotEnough = true;
                                break;
                            }
                            point = miniProgramActivityWeeklyAward.getPoints()*1000;
                        }
                        log.info("用户:{},兑换活动积分:{}", item.getPartnerId(), point);
                        partnerIdPointMap.put(item.getPartnerId(), point);

                    } else {
                        UserRetail userRetail = userRetailMapper.selectByPrimaryKey(item.getPartnerId());
                        //用户不存在或其手机号不存在，则整个兑换计划失败（正常情况不会发生）
                        if (userRetail == null || StringUtils.isEmpty(userRetail.getPhone())) {
                            log.error("用户:{}不存在或其手机号不存在", item.getPartnerId());
                            failPointExchange(pointExchange, "用户:" + item.getPartnerId() + "不存在或其手机号不存在", 0, 0L, now);
                            pointNotEnough = true;
                            break;
                        }
                        partnerIdPhoneMap.put(item.getPartnerId(), userRetail.getPhone());
                        partnerIdPointMap.put(item.getPartnerId(), point == 0L ? partnerPoint.getAvailable() : point);
                    }
                    partnerIdDataMap.put(item.getPartnerId(), partnerPoint);
                    totalPoint += point == 0L ? partnerPoint.getAvailable() : point;
                }
                log.info("结束查询用户积分");

                //将算出来的兑换总积分，更新到兑换计划中
                pointExchange.setPoint(totalPoint);
                pointExchangeMapper.updateByPrimaryKey(pointExchange);

                if (pointNotEnough) {
                    //有用户积分不足，直接返回结果
                    return;
                }

                if (totalPoint.longValue() > supplierBalance.longValue()) {
                    //供应商积分不足，兑换失败
                    failPointExchange(pointExchange, pointSupplier.getFullName() + "【账号余额不足】", 0, 0L, now);
                    return;
                }
                //按照需求文档，需要将电话号码相同的用户积分兑换合并起来兑换（这种用户实质上是同一个人）
                HashMap<String, List<String>> phonePartnerIdListMap = new HashMap<>();

                for (Map.Entry<String, String> entry : partnerIdPhoneMap.entrySet()) {
                    String partnerId = entry.getKey();
                    String phone = entry.getValue();
                    if(phonePartnerIdListMap.containsKey(phone)){
                        phonePartnerIdListMap.get(phone).add(partnerId);
                    }else {
                        List<String> partnerIdList = new ArrayList<>();
                        partnerIdList.add(partnerId);
                        phonePartnerIdListMap.put(phone,partnerIdList);
                    }
                }

                String url = serviceConfig.getUnionPayUrl() + CommonConstant.uploadOrder;
                Map<String, String> headerParams = new HashMap<>();
                try {
                    headerParams.put("Authorization","Bearer "+retailUserService.getUnionPayToken());
                } catch (Exception e) {
                    failPointExchange(pointExchange, "获取银联token出错,无法发起兑换", 0, 0L, now);
                    return;
                }
                headerParams.put("Content-Type","application/json");

                UploadOrderParam uploadOrderParam = new UploadOrderParam();
                uploadOrderParam.setCompany_id(sm4Service.encrypt(pointSupplier.getUniqueId()));
                uploadOrderParam.setShop_batch_number(sm4Service.encrypt(pointExchange.getBatchNo()));
                uploadOrderParam.setNotify_url(sm4Service.encrypt(serviceConfig.getUnionPayExchangeResultNotifyUrl()));
                uploadOrderParam.setPayment_gateway(sm4Service.encrypt(uploadOrderParam.getPayment_gateway()));
                List<UploadOrderParam.OrderItem> list = new ArrayList<>();
                //拼装具体的兑换子订单
                for (Map.Entry<String, List<String>> entry : phonePartnerIdListMap.entrySet()) {
                    UploadOrderParam.OrderItem orderItem = new UploadOrderParam.OrderItem();
                    list.add(orderItem);
                    String phone = entry.getKey();
                    List<String> partnerIdList = entry.getValue();
                    //合并多个手机号相同的合伙人的积分
                    Long originalPoint = partnerIdList.stream().mapToLong(p -> {
                        return partnerIdPointMap.get(p);
                    }).sum();
                    //银联那边的金额单位要求是"元"
                    String point = Double.valueOf(originalPoint) / 1000 + "";

                    orderItem.setShop_order_number(BaseServiceUtils.getId());
                    orderItem.setPhone(phone);
                    orderItem.setAmount(point);

                    //更新用户可兑换积分,兑换中积分
                    for (String partnerId : partnerIdList) {
                        PartnerPoint partnerPoint = partnerIdDataMap.get(partnerId);
                        Long userPoint = partnerIdPointMap.get(partnerId);
                        partnerPoint.setTotal(partnerPoint.getTotal() - userPoint);
                        partnerPoint.setAvailable(partnerPoint.getAvailable() - userPoint);
                        partnerPoint.setPaying((partnerPoint.getPaying() == null ? 0L : partnerPoint.getPaying()) + userPoint);
                        partnerPoint.setUpdateTime(now);
                        partnerPointMapper.updateByPrimaryKeySelective(partnerPoint);
                    }
                    //添加合伙人积分兑换记录，兑换中(相同手机号的合伙人积分记录，合并兑换，使用相同的shopOrderNumber)
                    addPartnerPointExchange(orderItem.getShop_order_number(), pointExchange.getId(), supplierId, partnerIdList, param.getChannel(), partnerIdPointMap, PointExchangeStatusEnum.PAYING.code, null, param.getActivityId(),now);
                }
                uploadOrderParam.setOrders(sm4Service.encrypt(JSON.toJSONString(list)));
                log.info("银联提现接口 请求参数:{}", JSON.toJSONString(uploadOrderParam));
                String responseStr = null;
                try {
                    responseStr = HttpUtil.doPostJson(url, headerParams, JSONObject.parseObject(JSONObject.toJSONString(uploadOrderParam)), 10000, 10000);
                    log.info("银联提现接口 响应原文:{}", responseStr);
                } catch (Exception e) {
                    //请求接口出错，可能是超时等情况，实际状态依赖主动查询或结果推送接口去更新
                    log.error("银联提现接口 发生异常",e);
                    return;
                }
                UploadOrderResponseDTO uploadOrderResponseDTO = JSONObject.parseObject(responseStr, UploadOrderResponseDTO.class);
                if(!"200".equals(uploadOrderResponseDTO.getCode())){
                    //提现直接失败,银联没有接收此订单，订单不会走后续流程
                    failPointExchange(pointExchange, pointSupplier.getFullName() + "【该批次所有用户兑换失败:"+uploadOrderResponseDTO.getMessage()+"】", 0, 0L, now);
                    restorePartnerPoint(allPartnerIdList, partnerIdPointMap,partnerIdDataMap,now);
                    failPointExchangePartner(pointExchange.getId(),now);
                    return;
                }
                String encryptedResult = uploadOrderResponseDTO.getResult();
                String decrypted = sm4Service.decrypt(encryptedResult);
                UploadOrderResponseDTO.Result result = JSONObject.parseObject(decrypted, UploadOrderResponseDTO.Result.class);
                log.info("银联提现接口 解密后:{}", decrypted);
                //返回Code=200,只需等待银联的体现结果回调或者主动查询结果。无需其他处理
            });
        } catch (Exception e) {
            log.error("发起兑换出错", e);
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR);
        } finally {
            if (getLock != null && getLock) {
                //删除锁
                stringRedisTemplate.delete(lockKey);
            }
        }
        return BaseAnswer.success(null);
    }

    private void failPointExchangePartner(String exchangeId, Date now) {
        PointExchangePartner pointExchangePartner = new PointExchangePartner();
        pointExchangePartner.setUpdateTime(now);
        pointExchangePartner.setStatus(PointExchangeStatusEnum.FAIL.code);
        PointExchangePartnerExample pointExchangePartnerExample = new PointExchangePartnerExample().createCriteria().andExchangeIdEqualTo(exchangeId).example();
        pointExchangePartnerMapper.updateByExampleSelective(pointExchangePartner, pointExchangePartnerExample);
    }

    @DS("save")
    public void restorePartnerPoint(List<String> partnerIdList, HashMap<String, Long> partnerIdPointMap, HashMap<String, PartnerPoint> partnerIdDataMap, Date now) {
        for (String partnerId : partnerIdList) {
            Long point = partnerIdPointMap.get(partnerId);
            PartnerPoint partnerPoint = partnerIdDataMap.get(partnerId);
            partnerPoint.setTotal(partnerPoint.getTotal() + point);
            partnerPoint.setAvailable(partnerPoint.getAvailable() + point);
            partnerPoint.setPaying(partnerPoint.getPaying() - point);
            partnerPoint.setUpdateTime(now);
            partnerPointMapper.updateByPrimaryKeySelective(partnerPoint);
        }

    }

    @DS("save")
    public void addPartnerPointExchange(String shopOrderNumber, String exchangeId, String supplierId, List<String> partnerIdList, Integer channel, HashMap<String, Long> partnerIdPointMap, Integer status, String failReason, String activityId, Date now) {
        for (String partnerId : partnerIdList) {
            PointExchangePartner pointExchangePartner = new PointExchangePartner();
            pointExchangePartner.setId(BaseServiceUtils.getId());
            pointExchangePartner.setShopOrderNumber(shopOrderNumber);
            pointExchangePartner.setActivityId(activityId);
            pointExchangePartner.setExchangeId(exchangeId);
            pointExchangePartner.setSupplierId(supplierId);
            pointExchangePartner.setPartnerId(partnerId);
            pointExchangePartner.setPoint(partnerIdPointMap.get(partnerId));
            pointExchangePartner.setStatus(status);
            if (StringUtils.isNotEmpty(failReason)) {
                pointExchangePartner.setFailReason(failReason);
            }
            pointExchangePartner.setCreateTime(now);
            pointExchangePartner.setUpdateTime(now);
            pointExchangePartner.setChannel(channel);
            pointExchangePartnerMapper.insert(pointExchangePartner);
        }

    }

    @DS("save")
    public void  failPointExchange(PointExchange pointExchange, String failReason, Integer successPartnerCount, Long successPointCount, Date now) {
        if (StringUtils.isNotEmpty(failReason)) {
            pointExchange.setFailReason(failReason);
        }
        if (successPartnerCount != null) {
            pointExchange.setSuccessPartnerCount(successPartnerCount);
        }
        if (successPointCount != null) {
            pointExchange.setSuccessPoint(successPointCount);
        }
        pointExchange.setStatus(PointExchangeStatusEnum.FAIL.code);
        pointExchange.setUpdateTime(now);
        pointExchangeMapper.updateByPrimaryKey(pointExchange);
    }

    private Long getSupplierBalance(PointSupplier supplier) {
        try {
            String url = serviceConfig.getUnionPayUrl() + CommonConstant.queryAccountBalance;
            Map<String, String> headerParams = new HashMap<>();
            headerParams.put("Authorization","Bearer "+retailUserService.getUnionPayToken());
            headerParams.put("Content-Type","application/json");

            QueryAccountBalanceParam param = new QueryAccountBalanceParam();
            param.setCompany_id(supplier.getUniqueId());
            log.info("获取余额接口请求参数:{}", JSON.toJSONString(param));
            String responseStr = HttpUtil.doPostJson(url, headerParams, JSONObject.parseObject(JSONObject.toJSONString(param)), 10000, 10000);
            log.info("获取余额接口响应原文:{}", responseStr);
            QueryAccountBalanceResponseDTO sendSignResponseDTO = JSONObject.parseObject(responseStr, QueryAccountBalanceResponseDTO.class);
            if(!"200".equals(sendSignResponseDTO.getCode())){
                throw new BusinessException(BaseErrorConstant.API_RETURN_ERROR,sendSignResponseDTO.getMessage());
            }
            String encryptedResult = sendSignResponseDTO.getResult();
            String decrypted = sm4Service.decrypt(encryptedResult);
            QueryAccountBalanceResponseDTO.Result result = JSONObject.parseObject(decrypted, QueryAccountBalanceResponseDTO.Result.class);
            log.info("获取余额接口响应业务参数解密后:{}", decrypted);
            return ((long) Double.parseDouble(result.getList().get(0).getAccount())) * 1000;
        } catch (Exception e) {
            log.error("获取商户{}余额出错", supplier.getId(), e);
            return null;
        }
    }

    @DS("save")
    public PointExchange addPointExchange(String supplierId, String userId, Long pointCount, int partnerCount, String failReason) {
        Date now = new Date();
        //每天的批次号依次叠加
        Date dayBeginDate = DateTimeUtil.getDayBeginDate(now);
        Date dayEndDate = DateTimeUtil.getDayEndDate(now);
        String dayStr = DateTimeUtil.getDayStr(now.getTime());
        PointExchangeExample pointExchangeExample = new PointExchangeExample().createCriteria().andCreateTimeLessThanOrEqualTo(dayEndDate)
                .andCreateTimeGreaterThanOrEqualTo(dayBeginDate)
                .example();
        pointExchangeExample.orderBy("create_time DESC").setLimit(1);
        List<PointExchange> exchangeList = pointExchangeMapper.selectByExample(pointExchangeExample);
        String batchNo = null;
        if (exchangeList.size() == 0) {
            batchNo = CommonConstant.batch_no_prefix + dayStr + "001";
        } else {
            PointExchange pointExchange = exchangeList.get(0);
            String lastestBatchNo = pointExchange.getBatchNo().substring(2);
            batchNo = CommonConstant.batch_no_prefix + (Long.parseLong(lastestBatchNo) + 1) + "";
        }
        PointExchange pointExchange = new PointExchange();
        pointExchange.setId(BaseServiceUtils.getId());
        pointExchange.setBatchNo(batchNo);
        pointExchange.setOperateUserId(userId);
        pointExchange.setSupplierId(supplierId);
        pointExchange.setPartnerCount(partnerCount);
        pointExchange.setPoint(pointCount);
        if (failReason != null) {
            pointExchange.setSuccessPartnerCount(0);
            pointExchange.setSuccessPoint(0L);
            pointExchange.setStatus(PointExchangeStatusEnum.FAIL.code);
            pointExchange.setFailReason(failReason);
        } else {
            pointExchange.setStatus(PointExchangeStatusEnum.PAYING.code);
        }
        pointExchange.setCreateTime(now);
        pointExchange.setUpdateTime(now);
        pointExchangeMapper.insert(pointExchange);
        return pointExchange;
    }

    private List<PointExchangePartner> getFailedPartnerExchange(String exchangeId, Integer channel) {
        PointExchangePartnerExample example = new PointExchangePartnerExample();
        PointExchangePartnerExample.Criteria criteria = example.createCriteria();
        criteria.andExchangeIdEqualTo(exchangeId);
        criteria.andChannelEqualTo(channel);
        criteria.andStatusEqualTo(PointExchangeStatusEnum.FAIL.code);
        example.orderBy("create_time DESC");
        return pointExchangePartnerMapper.selectByExample(example);
    }

    @Override
    @DS("query")
    public PageData<PointExchangeDTO> getExchangeList(Integer page, Integer pageSize, Integer channel) {
        PageData<PointExchangeDTO> data = new PageData<>();
        data.setPage(page);
        if (channel == null) {
            channel = 0;
        }
        PointExchangeExample example = new PointExchangeExample().createCriteria().example();
        example.orderBy("create_time DESC");
        PageHelper.startPage(page, pageSize);
        List<PointExchange> pointExchanges = pointExchangeMapper.selectByExample(example);

        if (CollectionUtils.isNotEmpty(pointExchanges)) {
            Integer finalChannel = channel;
            PageInfo<PointExchange> pageInfo = new PageInfo<>(pointExchanges);
            data.setCount(pageInfo.getTotal());
            List<PointExchangeDTO> dtoList = pointExchanges.stream().map(p -> {
                PointExchangeDTO dto = new PointExchangeDTO();
                BeanUtils.copyProperties(p, dto);
//                User operator = userMapper.selectByPrimaryKey(p.getOperateUserId());
                BaseAnswer<Data4User> answer = userFeignClient.userInfo(p.getOperateUserId());
                if (answer != null && answer.getData() != null) {
                    dto.setOperateUser(answer.getData().getName());
                }
                PointSupplier supplier = pointSupplierMapper.selectByPrimaryKey(p.getSupplierId());
                if (supplier != null) {
                    dto.setSupplier(supplier.getAbbreviationName());
                }
                if (p.getSuccessPartnerCount() != null) {
                    dto.setSuccessPercent(new BigDecimal(p.getSuccessPartnerCount())
                            .divide(new BigDecimal(p.getPartnerCount()), 3, RoundingMode.HALF_UP));
                }
                dto.setHasPartnerFailed(CollectionUtils.isNotEmpty(getFailedPartnerExchange(p.getId(), finalChannel)));
                return dto;
            }).collect(Collectors.toList());
            data.setData(dtoList);
        }
        return data;
    }


    @Override
    @DS("query")
    public void exportExchangeFailedExcel(String exchangeId, HttpServletResponse response, Integer channel) {
        if (channel == null) {
            channel = 0;
        }
        List<PointExchangePartner> partners = getFailedPartnerExchange(exchangeId, channel);
        List<PartnerPointExchangeExcelItemDTO> lst = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(partners)) {
            lst = partners.stream()
                    .map(item -> {
                        PartnerPointExchangeExcelItemDTO excelItemDTO = new PartnerPointExchangeExcelItemDTO();
                        BeanUtils.copyProperties(item, excelItemDTO);
                        excelItemDTO.setPoint(new BigDecimal(item.getPoint()).divide(new BigDecimal(1000)));
                        UserRetail userRetail = userRetailMapper.selectByPrimaryKey(item.getPartnerId());
                        if (userRetail != null) {
                            excelItemDTO.setName(userRetail.getName());
                        }
                        PointSupplier supplier = pointSupplierMapper.selectByPrimaryKey(item.getSupplierId());
                        if (supplier != null) {
                            excelItemDTO.setSupplier(supplier.getAbbreviationName());
                        }
                        return excelItemDTO;
                    }).collect(Collectors.toList());
        }
        try {

            ExcelUtils.exportExcel(lst, "积分兑换失败清单",
                    "",
                    PartnerPointExchangeExcelItemDTO.class,
                    "partner-points-exchange-failed-export-",
                    response);
        } catch (IOException e) {
            throw new BusinessException(StatusContant.POINT_EXCHANGE_EXCEL_EXPORT_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public void adjustPartnerPoint(String operateId, AdjustPointParam param) {
        String supplierId = param.getSupplierId();
        String partnerId = param.getPartnerId();
        PartnerPointExample example = new PartnerPointExample();
        PartnerPointExample.Criteria criteria = example.createCriteria();
        criteria.andPartnerIdEqualTo(partnerId);
        criteria.andSupplierIdEqualTo(supplierId);
        if (param.getChannel() != null) {
            criteria.andChannelEqualTo(param.getChannel());
        } else {
            criteria.andChannelEqualTo(0);
        }
        long operatePoint = 0;
        if (param.getType().equals(PointOperateTypeEnum.INCREASE.code)) {
            operatePoint = param.getPoint();
        } else if (param.getType().equals(PointOperateTypeEnum.DECREASE.code)) {
            operatePoint = -param.getPoint();
        }
        List<PartnerPoint> partnerPoints = partnerPointMapper.selectByExample(example);
        PartnerPoint partnerPoint = null;
        Date now = new Date();
        if (CollectionUtils.isEmpty(partnerPoints)) {
            //插入合伙人积分
            partnerPoint = new PartnerPoint();
            partnerPoint.setId(BaseServiceUtils.getId());
            partnerPoint.setPartnerId(partnerId);
            partnerPoint.setSupplierId(supplierId);
            partnerPoint.setTotal(operatePoint);
            partnerPoint.setPaying(0L);
            partnerPoint.setAvailable(operatePoint);
            partnerPoint.setRedeemed(0L);
            //当月积分只包含订单积分，积分调整不算
            partnerPoint.setCurrentMonth(0L);
            partnerPoint.setCreateTime(now);
            partnerPoint.setUpdateTime(now);
            partnerPoint.setChannel(0);
            partnerPointMapper.insertSelective(partnerPoint);
        } else {
            partnerPoint = partnerPoints.get(0);
            partnerPoint.setTotal(partnerPoint.getTotal() != null ? (partnerPoint.getTotal() + operatePoint) : operatePoint);
            partnerPoint.setAvailable(partnerPoint.getAvailable() != null ? (partnerPoint.getAvailable() + operatePoint) : operatePoint);
            partnerPointMapper.updateByPrimaryKey(partnerPoint);
        }

        PointOperate operate = new PointOperate();
        BeanUtils.copyProperties(param, operate);
        operate.setCreateTime(now);
        operate.setUserId(partnerId);
        operate.setOperatorId(operateId);
        operate.setId(BaseServiceUtils.getId());
        operate.setReason(param.getDesc());
        operate.setChannel(param.getChannel());
        log.info("积分调整记录参数：{}", operate);
        pointOperateMapper.insertSelective(operate);

    }

    @Override
    @DS("query")
    public BaseAnswer<PageData<PointOperateItemDTO>> adjustHistory(AdjustHistoryQueryParam param, LoginIfo4Redis loginIfo4Redis) {
        String roleType = loginIfo4Redis.getRoleType();
        if (!ADMIN_ROLE.equals(roleType) && !OPERATOR_ROLE.equals(roleType) && !POINT_MANAGER.equals(roleType)){
            throw new BusinessException(StatusContant.NO_DATA,"无权限");
        }
        PageData<PointOperateItemDTO> data = new PageData<>();
        data.setPage(param.getPage());
        PointOperateExample example = new PointOperateExample();
        PointOperateExample.Criteria criteria = example.createCriteria();
        criteria.andUserIdEqualTo(param.getPartnerId());
        if (param.getChannel() == null) {
            param.setChannel(0);
        }
        criteria.andChannelEqualTo(param.getChannel());
        criteria.andSupplierIdEqualTo(param.getSupplierId());
        //过滤订单激励
        criteria.andTypeNotEqualTo(PointOperateTypeEnum.ORDER_POINT.code);
        example.orderBy("create_time DESC");
        PageHelper.startPage(param.getPage(), param.getPageSize());
        List<PointOperate> operates = pointOperateMapper.selectByExample(example);

        if (CollectionUtils.isNotEmpty(operates)) {
            PageInfo<PointOperate> pageInfo = new PageInfo<>(operates);
            data.setCount(pageInfo.getTotal());
            List<PointOperateItemDTO> dtoList = operates.stream().map(p -> {
                PointOperateItemDTO dto = new PointOperateItemDTO();
                BeanUtils.copyProperties(p, dto);
                dto.setDesc(p.getReason());
                //积分管理员信息再USER表中
//                User user = userMapper.selectByPrimaryKey(p.getOperatorId());
                BaseAnswer<Data4User> answer = userFeignClient.userInfo(p.getOperatorId());
                if (answer != null && answer.getData() != null) {
                    dto.setOperatorName(answer.getData().getName());
                }
                return dto;
            }).collect(Collectors.toList());
            data.setData(dtoList);
        }

        return BaseAnswer.success(data);
    }

    /**
     * 每月1日凌晨0点重置当月积分
     */
    @Scheduled(cron = "0 0 0 1 * ? ")
    @DS("save")
    public void resetMonthPoint() {
        PartnerPointExample example = new PartnerPointExample();
        PartnerPoint record = new PartnerPoint();
        record.setCurrentMonth((long) 0);
        partnerPointMapper.updateByExampleSelective(record, example);
    }

    @DS("save")
    public void updateExchangeTask(String exchangeId, PointExchangePartner partner, Date updateTime) {
        PointExchange pointExchange = pointExchangeMapper.selectByPrimaryKey(exchangeId);
        if (pointExchange != null) {
            pointExchange.setUpdateTime(updateTime);
            if (partner != null) {
                //单人更新
                if (PointExchangeStatusEnum.SUCCESS.code.equals(partner.getStatus())) {
                    pointExchange.setSuccessPoint(pointExchange.getSuccessPoint() != null ?
                            (pointExchange.getSuccessPoint() + partner.getPoint()) : partner.getPoint());
                    pointExchange.setSuccessPartnerCount(pointExchange.getSuccessPartnerCount() != null ?
                            (pointExchange.getSuccessPartnerCount() + 1) : 1);
                    pointExchange.setStatus(PointExchangeStatusEnum.SUCCESS.code);
                } else if (PointExchangeStatusEnum.FAIL.code.equals(partner.getStatus())) {
                    //所有兑换子任务都失败，则主兑换任务失败
                    PointExchangePartnerExample example = new PointExchangePartnerExample();
                    PointExchangePartnerExample.Criteria criteria = example.createCriteria();
                    criteria.andStatusNotEqualTo(PointExchangeStatusEnum.FAIL.code);
                    criteria.andExchangeIdEqualTo(exchangeId);
                    if (CollectionUtils.isEmpty(pointExchangePartnerMapper.selectByExample(example))) {
                        pointExchange.setStatus(PointExchangeStatusEnum.FAIL.code);
                    }
                }
            } else {
                /**
                 * 根据子任务表更新
                 * 有一个小任务成功->大任务成功；
                 * 没有小任务成功，且没有全部失败->大任务提现中；(任务创建就是提现中，故不更新状态)
                 * 全部小任务失败->大任务失败
                 * **/
                PointExchangePartnerExample example = new PointExchangePartnerExample();
                PointExchangePartnerExample.Criteria criteria = example.createCriteria();
                criteria.andStatusEqualTo(PointExchangeStatusEnum.SUCCESS.code);
                criteria.andExchangeIdEqualTo(exchangeId);
                List<PointExchangePartner> partners = pointExchangePartnerMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(partners)) {
                    pointExchange.setStatus(PointExchangeStatusEnum.SUCCESS.code);
                    pointExchange.setSuccessPartnerCount(partners.size());
                    pointExchange.setSuccessPoint(partners.stream().mapToLong(PointExchangePartner::getPoint).sum());
                } else {
                    example.clear();
                    PointExchangePartnerExample.Criteria criteria1 = example.createCriteria();
                    criteria1.andStatusNotEqualTo(PointExchangeStatusEnum.FAIL.code);
                    criteria1.andExchangeIdEqualTo(exchangeId);
                    if (CollectionUtils.isEmpty(pointExchangePartnerMapper.selectByExample(example))) {
                        //不等于失败的任务为空，则表示所有小任务都处于失败
                        pointExchange.setStatus(PointExchangeStatusEnum.FAIL.code);
                    }
                }
            }

            pointExchangeMapper.updateByPrimaryKeySelective(pointExchange);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public String receiveExchangeResult(UploadOrderNotifyParam resultDataDTO) {
        Date now = new Date();
        log.info("receiveExchangeResult兑换结果回调:{}", JSON.toJSONString(resultDataDTO));
        String code = resultDataDTO.getCode();
        String encryptedResult = resultDataDTO.getResult();
        String notifyType = resultDataDTO.getNotify_type();
        /**
         * 退票是针对批次下某个订单：有订单产生退票时，会触发退票回调，每次回调都会返回该批次下所有订单
         * 挂起是针对整个批次，因为余额计算银联是按整个批次进行计算的,每次回调都会返回该批次下所有订单
         * 其他情况，每次回调也都会返回该批次下所有订单
         */
        String decrypted = sm4Service.decrypt(encryptedResult);
        resultDataDTO.setResult(decrypted);
        log.info("receiveExchangeResult兑换结果回调解密后:{}", JSON.toJSONString(resultDataDTO));
        UploadOrderNotifyParam.Result result = JSONObject.parseObject(decrypted, UploadOrderNotifyParam.Result.class);
        Integer batchStatus = result.getBatch_status();
        String batchNumber = result.getShop_batch_number();
        List<PointExchange> pointExchanges = pointExchangeMapper.selectByExample(new PointExchangeExample().createCriteria().andBatchNoEqualTo(batchNumber).example());
        if(CollectionUtils.isEmpty(pointExchanges)){
            log.error("兑换批次任务不存在，停止执行");
            return "兑换批次任务不存在，停止执行";
        }
        PointExchange pointExchange = pointExchanges.get(0);
        List<UploadOrderNotifyParam.Result.Order> orders = result.getOrders();
        dealExchangeResult(orders, now, pointExchange, batchStatus);
        return "success";
    }

    private void dealExchangeResult(List<UploadOrderNotifyParam.Result.Order> orders, Date now, PointExchange pointExchange, Integer batchStatus) {
        Integer successPartnerCount = 0;
        Long successPoint = 0L;
        for (UploadOrderNotifyParam.Result.Order order : orders) {
            Integer orderStatus = order.getOrder_status();
            String shopOrderNumber = order.getShop_order_number();
            //兑换时已经将相同手机号的partnerPointExchange合并为批次下的一个订单进行兑换，此时需要找出来partnerPointExchange并更新状态
            PointExchangePartnerExample pointExchangePartnerExample = new PointExchangePartnerExample().createCriteria().andShopOrderNumberEqualTo(shopOrderNumber).example();
            List<PointExchangePartner> pointExchangePartnerList = pointExchangePartnerMapper.selectByExample(pointExchangePartnerExample);

            for (PointExchangePartner pointExchangePartner : pointExchangePartnerList) {
                PartnerPointExample example = new PartnerPointExample();
                PartnerPointExample.Criteria criteria = example.createCriteria();
                criteria.andSupplierIdEqualTo(pointExchangePartner.getSupplierId());
                criteria.andPartnerIdEqualTo(pointExchangePartner.getPartnerId());
                criteria.andChannelEqualTo(pointExchangePartner.getChannel());
                PartnerPoint partnerPoint = partnerPointMapper.selectByExample(example).get(0);

                if(orderStatus == 1 || orderStatus == 5 || orderStatus == 6){
                    //失败
                    pointExchangePartner.setStatus(PointExchangeStatusEnum.FAIL.code);
                    pointExchangePartner.setFailReason(orderStatus == 6 ? "发生退票，请用户去分销大厅首页跳转至银联进行银行卡换绑":order.getError_msg());
                    pointExchangePartner.setUpdateTime(now);
                    pointExchangePartnerMapper.updateByPrimaryKeySelective(pointExchangePartner);

                    partnerPoint.setPaying(partnerPoint.getPaying() - pointExchangePartner.getPoint());
                    partnerPoint.setAvailable(partnerPoint.getAvailable() + pointExchangePartner.getPoint());
                    partnerPoint.setTotal(partnerPoint.getTotal() + pointExchangePartner.getPoint());
                    partnerPoint.setUpdateTime(now);
                    partnerPointMapper.updateByPrimaryKeySelective(partnerPoint);
                }else if(orderStatus == 4){
                    //成功
                    if (partnerPoint.getChannel() == 1) {
                        //添加积分记录（小程序活动）
                        PointOperate pointOperate = new PointOperate();
                        pointOperate.setPoint(pointExchangePartner.getPoint());
                        pointOperate.setUserId(partnerPoint.getPartnerId());
                        pointOperate.setSupplierId(partnerPoint.getSupplierId());
                        pointOperate.setOperatorId(partnerPoint.getPartnerId());
                        pointOperate.setCreateTime(now);
                        pointOperate.setType(3);
                        pointOperate.setId(BaseServiceUtils.getId());
                        pointOperate.setActivityId(pointExchangePartner.getActivityId());
                        pointOperate.setChannel(1);
                        pointOperateMapper.insertSelective(pointOperate);
                    }

                    pointExchangePartner.setStatus(PointExchangeStatusEnum.SUCCESS.code);
                    pointExchangePartner.setUpdateTime(now);
                    pointExchangePartnerMapper.updateByPrimaryKeySelective(pointExchangePartner);

                    partnerPoint.setPaying(partnerPoint.getPaying() - pointExchangePartner.getPoint());
                    partnerPoint.setRedeemed(partnerPoint.getRedeemed() != null ?
                            (partnerPoint.getRedeemed() + pointExchangePartner.getPoint()) : pointExchangePartner.getPoint());
                    partnerPoint.setUpdateTime(now);
                    partnerPointMapper.updateByPrimaryKeySelective(partnerPoint);
                    //统计成功的订单数和积分数
                    successPoint += pointExchangePartner.getPoint();
                    successPartnerCount++;
                }
            }
        }
        pointExchange.setSuccessPoint(successPoint);
        pointExchange.setSuccessPartnerCount(successPartnerCount);
        pointExchange.setUpdateTime(now);
        switch (batchStatus){
            case 1:
            case 2:
            //成功
                pointExchange.setStatus(PointExchangeStatusEnum.SUCCESS.code);
                //兑换成功则清掉挂起状态
                pointExchange.setHundUp(false);
                break;
            case 3:
            //失败
                pointExchange.setStatus(PointExchangeStatusEnum.FAIL.code);
                pointExchange.setFailReason("该批次所有用户兑换失败");
                break;
            case 5:
            //失败（退票）
                pointExchange.setStatus(PointExchangeStatusEnum.FAIL.code);
                pointExchange.setFailReason("发生退票，请用户去分销大厅首页跳转至银联进行银行卡换绑");
                break;
            case 4:
            //支付中
                pointExchange.setStatus(PointExchangeStatusEnum.PAYING.code);
                break;
            case 6:
                //处理挂起，设置标志位。状态是兑换中
                pointExchange.setHundUp(true);
                pointExchange.setStatus(PointExchangeStatusEnum.PAYING.code);
                break;
            default:
                break;
        }
        pointExchangeMapper.updateByPrimaryKeySelective(pointExchange);
    }

    @Override
    @DS("query")
    public BaseAnswer<List<String>> getAllPartnerIdBySupplier(String supplierId, Integer channel) {
        List<String> allPartnerIdBySupplier = partnerPointMapperExt.getAllPartnerIdBySupplier(supplierId, channel);
        return BaseAnswer.success(allPartnerIdBySupplier);
    }

    /**
     * 每天凌晨0点处理2天还没有结果的提现订单
     */
    @Scheduled(cron = "0 0 0 * * ? ")
    @DS("save")
    @Transactional(rollbackFor = Exception.class)
    public void dealProcessExchangeTask() {
        Boolean getLock = null;
        try {
            getLock = stringRedisTemplate.opsForValue().setIfAbsent(dealProcessExchangeTaskLock, "1", 10, TimeUnit.MINUTES);
            if (getLock) {
                //通过还没结束的小兑换任务找到大兑换任务
                PointExchangePartnerExample example = new PointExchangePartnerExample();
                PointExchangePartnerExample.Criteria criteria = example.createCriteria();
                criteria.andStatusEqualTo(PointExchangeStatusEnum.PAYING.code);
                criteria.andCreateTimeLessThan(DateUtils.addDay(-2));
                List<PointExchangePartner> exchangePartners = pointExchangePartnerMapper.selectByExample(example);
                if(CollectionUtils.isEmpty(exchangePartners)){
                    return;
                }
                Map<String, List<PointExchangePartner>> exchangeIdAndParterExchangeMap = exchangePartners.stream().collect(Collectors.groupingBy(PointExchangePartner::getExchangeId));
                List<String> pointExchangeIdList = exchangePartners.stream().map(p -> {
                    return p.getExchangeId();
                }).distinct().collect(Collectors.toList());
                List<PointExchange> pointExchangeList = pointExchangeMapper.selectByExample(new PointExchangeExample().createCriteria().andIdIn(pointExchangeIdList).example());
                if(CollectionUtils.isEmpty(pointExchangeList)){
                    return;
                }
                log.info("dealProcessExchangeTask，本次处理的任务{}", JSON.toJSONString(pointExchangeList));
                for (PointExchange pointExchange : pointExchangeList) {
                    //采用内部事务
                    try {
                        partnerPointService.dealPayingTask(pointExchange,exchangeIdAndParterExchangeMap.get(pointExchange.getId()));
                    } catch (Exception e) {
                        log.error("dealProcessExchangeTask - dealPayingTask 发生异常",e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("dealProcessExchangeTask发生异常", e);
        }
    }

    //Propagation.REQUIRES_NEW，开启新的事务，互相独立。
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @DS("save")
    public void dealPayingTask(PointExchange pointExchange,List<PointExchangePartner> itemList) throws Exception {
        PointSupplier pointSupplier = pointSupplierMapper.selectByPrimaryKey(pointExchange.getSupplierId());

        String url = serviceConfig.getUnionPayUrl() + CommonConstant.queryOrderResult;
        Map<String, String> headerParams = new HashMap<>();
        headerParams.put("Authorization","Bearer "+retailUserService.getUnionPayToken());
        headerParams.put("Content-Type","application/json");

        QueryOrderResultParam param = new QueryOrderResultParam();
        param.setCompany_id(sm4Service.encrypt(pointSupplier.getUniqueId()));
        param.setShop_batch_number(sm4Service.encrypt(pointExchange.getBatchNo()));

        log.info("dealProcessExchangeTask - dealPayingTask 请求参数:{}", JSON.toJSONString(param));
        String responseStr = HttpUtil.doPostJson(url, headerParams, JSONObject.parseObject(JSONObject.toJSONString(param)), 10000, 10000);
        log.info("dealProcessExchangeTask - dealPayingTask 响应原文:{}", responseStr);
        QueryOrderResultResponseDTO queryOrderResultResponseDTO = JSONObject.parseObject(responseStr, QueryOrderResultResponseDTO.class);
        if(!"200".equals(queryOrderResultResponseDTO.getCode())){
            throw new BusinessException(BaseErrorConstant.API_RETURN_ERROR,queryOrderResultResponseDTO.getMessage());
        }
        String encryptedResult = queryOrderResultResponseDTO.getResult();
        String decrypted = sm4Service.decrypt(encryptedResult);
        QueryOrderResultResponseDTO.Result result = JSONObject.parseObject(decrypted, QueryOrderResultResponseDTO.Result.class);
        log.info("dealProcessExchangeTask - dealPayingTask 解密后:{}", decrypted);
        List<QueryOrderResultResponseDTO.Result.Order> orders = result.getOrders();
        List<UploadOrderNotifyParam.Result.Order> notifyOrderList = orders.stream().map(o -> {
            UploadOrderNotifyParam.Result.Order order = new UploadOrderNotifyParam.Result.Order();
            BeanUtils.copyProperties(o, order);
            return order;
        }).collect(Collectors.toList());
        dealExchangeResult(notifyOrderList,new Date(),pointExchange,result.getBatch_status());
    }


    @Override
    public void testQueryExchangeResult() {
        dealProcessExchangeTask();
    }

    @Override
    @DS("query")
    public BaseAnswer<PartnerStatisticsVO> partnerStatistics(String userId, Integer channel) {
        //获取上个月的时间范围
        Date date = DateTimeUtil.addMonth(new Date(), -1);
        Date previousMonthStart = DateTimeUtil.getMonthBegin(date);
        Date previousMonthEnd = DateTimeUtil.getMonthEnd(date);
        if (channel == null) {
            channel = 0;
        }
        PartnerStatisticsVO vo = new PartnerStatisticsVO();
        PartnerStatisticsDO partnerStatisticsDO = partnerPointMapperExt.getPartnerStatistics(userId, previousMonthStart, previousMonthEnd, channel);
        if (partnerStatisticsDO == null) {
            return BaseAnswer.success(vo);
        }
        if (partnerStatisticsDO.getLastMonthRedeemedPoint() == null) {
            partnerStatisticsDO.setLastMonthRedeemedPoint(0L);
        }
        if (partnerStatisticsDO.getOrderAmount() == null) {
            partnerStatisticsDO.setOrderAmount(0L);
        }
        if (partnerStatisticsDO.getOrderCount() == null) {
            partnerStatisticsDO.setOrderCount(0);
        }
        if (partnerStatisticsDO.getTotalRedeemedPoint() == null) {
            partnerStatisticsDO.setTotalRedeemedPoint(0L);
        }
        BeanUtils.copyProperties(partnerStatisticsDO, vo);
        return BaseAnswer.success(vo);
    }

    @Transactional (rollbackFor = Exception.class)
    @Override
    @DS("save")
    public void makeupPoint(List<AdjustPointParam> param) {
        if (CollectionUtils.isNotEmpty(param)) {
            List<PointOperate> pointOperateList = new ArrayList<>();
            Date time;
            try {
                time = DateUtils.strToDate("2024-08-30", DateUtils.DEFAULT_DATE_FORMAT);
            } catch (ParseException exception) {
                return;
            }
            List<AdjustPointParam> added = new ArrayList<>();
            List<AdjustPointParam> notAdded = new ArrayList<>();
            param.forEach(item -> {
                List<PartnerPoint> partnerPoints = partnerPointMapper.selectByExample(new PartnerPointExample().createCriteria()
                        .andPartnerIdEqualTo(item.getPartnerId()).andSupplierIdEqualTo(item.getSupplierId()).andChannelEqualTo(0).example());
                if (CollectionUtils.isNotEmpty(partnerPoints)) {
                    //不存在，则新增记录
                    PartnerPoint partnerPoint = partnerPoints.get(0);
                    partnerPoint.setAvailable(partnerPoint.getAvailable() != null ? partnerPoint.getAvailable() + item.getPoint() : item.getPoint());
                    partnerPointMapper.updateByPrimaryKeySelective(partnerPoint);

                    PointOperate pointOperate = new PointOperate();
                    pointOperate.setPoint(item.getPoint());
                    pointOperate.setUserId(item.getPartnerId());
                    pointOperate.setSupplierId(item.getSupplierId());
                    pointOperate.setCreateTime(time);
                    pointOperate.setId(BaseServiceUtils.getId());
                    pointOperate.setType(1);
                    pointOperate.setReason("订单激励");
                    pointOperate.setChannel(0);
                    pointOperateList.add(pointOperate);
                    added.add(item);
                } else {
                    notAdded.add(item);
                }
            });

            if (CollectionUtils.isNotEmpty(pointOperateList)) {
                pointOperateMapper.batchInsert(pointOperateList);
            }

            log.info("积分补发成功：{}",JSON.toJSONString(added));
            log.info("积分补发不成功：{}",JSON.toJSONString(notAdded));
        }
    }

    @Override
    public void sendSignMsg(SendSignMsgParam param) {
        List<String> partnerIdList = new ArrayList<>(new HashSet<>(param.getPartnerIdList()));
        List<UserRetail> userRetailList = userRetailMapper.selectByExample(new UserRetailExample().createCriteria().andIdIn(partnerIdList).example());
        if(CollectionUtils.isEmpty(userRetailList)){
            return;
        }
        //相同电话号码的不同用户只需要发送一次短信
        Set<String> phoneSet = userRetailList.stream().filter(u -> {
            return StringUtils.isNotEmpty(u.getPhone());
        }).map(u -> {
            return u.getPhone();
        }).collect(Collectors.toSet());
        baseSmsService.sendManyMsg(new ArrayList<>(phoneSet),smsConfig.getUnionPaySignMsgTemplateId(),null);
    }
}
