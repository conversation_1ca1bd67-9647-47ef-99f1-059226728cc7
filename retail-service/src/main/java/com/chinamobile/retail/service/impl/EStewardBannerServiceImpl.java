package com.chinamobile.retail.service.impl;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.enums.log.EstewardOperateEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.retail.dao.EstewardBannerMapper;
import com.chinamobile.retail.dao.EstewardDirectoryMapper;
import com.chinamobile.retail.dao.ext.EstewardBannerMapperExt;
import com.chinamobile.retail.pojo.entity.EstewardBanner;
import com.chinamobile.retail.pojo.entity.EstewardBannerExample;
import com.chinamobile.retail.pojo.entity.EstewardDirectory;
import com.chinamobile.retail.pojo.param.EStewardBannerListParam;
import com.chinamobile.retail.pojo.param.EStewardBannerOnOfflineParam;
import com.chinamobile.retail.pojo.param.SaveEStewardBannerParam;
import com.chinamobile.retail.pojo.vo.EStewardBannerDetailVO;
import com.chinamobile.retail.pojo.vo.EStewardBannerListVO;
import com.chinamobile.retail.service.EStewardBannerService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class EStewardBannerServiceImpl implements EStewardBannerService {

    @Resource
    private EstewardBannerMapper bannerMapper;

    @Resource
    private EstewardDirectoryMapper directoryMapper;

    @Resource
    private EstewardBannerMapperExt bannerMapperExt;

    @Resource
    private LogService logService;

    @Override
    public BaseAnswer<Void> saveBanner(SaveEStewardBannerParam param, String userId) {
        Date now = new Date();
        String id = param.getId();
        String secondDirectoryId = param.getSecondDirectoryId();
        EstewardDirectory directory = directoryMapper.selectByPrimaryKey(secondDirectoryId);
        if(directory == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "二级目录不存在");
        }
        Integer type = param.getType();
        if(type == 1 && StringUtils.isEmpty(param.getProductUrl())){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "展示类型为外链时必须传递链接");
        }
        if(type == 2 && StringUtils.isEmpty(param.getSpuCode())){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "展示类型为详情页时必须传递spuCode");
        }
        boolean create = false;
        String oldName = null;
        String newName = param.getName();
        if(StringUtils.isNotEmpty(id)){
            //更新
            EstewardBanner banner = bannerMapper.selectByPrimaryKey(id);
            oldName = banner.getName();
            if(banner == null){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "banner不存在");
            }
            //状态 0 - 待发布， 1- 已上线， 2- 已下线
            if(banner.getStatus() != 0 && banner.getStatus() != 2){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "只有待发布和已下线的banner才可编辑");
            }
            BeanUtils.copyProperties(param, banner);
            banner.setDirectoryId(secondDirectoryId);
            banner.setUpdateTime(now);
            bannerMapper.updateByPrimaryKeySelective(banner);
        }else {
            create = true;
            //新增
            EstewardBanner banner = new EstewardBanner();
            BeanUtils.copyProperties(param, banner);
            banner.setId(BaseServiceUtils.getId());
            banner.setDirectoryId(secondDirectoryId);
            banner.setStatus(0);
            banner.setCreateTime(now);
            banner.setCreatorId(userId);
            banner.setUpdateTime(now);
            bannerMapper.insertSelective(banner);
        }
        //日志记录
        StringBuilder logContent = new StringBuilder(create ? "【新建】" : "【编辑】").append("\n");
        logContent.append("目录banner名称").append(create ? newName : (oldName+"修改为"+newName));
        logService.recordOperateLog(ModuleEnum.ESTEWARD_MANAGE.code,
                EstewardOperateEnum.DIRECTORY_OPERATE.code,
                logContent.toString(), LogResultEnum.LOG_SUCESS.code, null);

        return BaseAnswer.success(null);
    }

    @Override
    public BaseAnswer<EStewardBannerDetailVO> getDetail(String id) {
        EStewardBannerDetailVO detailVO = bannerMapperExt.selectDetailById(id);
        if (detailVO == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "Banner不存在");
        }
        //日志记录
        StringBuilder logContent = new StringBuilder("【查看】").append("\n");
        logContent.append("目录banner名称").append(detailVO.getName());
        logService.recordOperateLog(ModuleEnum.ESTEWARD_MANAGE.code,
                EstewardOperateEnum.DIRECTORY_OPERATE.code,
                logContent.toString(), LogResultEnum.LOG_SUCESS.code, null);
        return BaseAnswer.success(detailVO);
    }

    @Override
    public BaseAnswer<PageData<EStewardBannerListVO>> bannerList(EStewardBannerListParam param) {
        PageData<EStewardBannerListVO> pageData = new PageData<>();
        pageData.setPage(param.getPageNum());
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<EStewardBannerListVO> list = bannerMapperExt.bannerList(param);
        if (CollectionUtils.isEmpty(list)){
            return BaseAnswer.success(pageData);
        }
        PageInfo<EStewardBannerListVO> pageInfo = new PageInfo<>(list);
        pageData.setCount(pageInfo.getTotal());
        pageData.setData(list);
        return BaseAnswer.success(pageData);
    }

    @Override
    public BaseAnswer<Void> onOffline(EStewardBannerOnOfflineParam param) {
        Date now = new Date();
        EstewardBanner banner = bannerMapper.selectByPrimaryKey(param.getId());
        if (banner == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "Banner不存在");
        }
        Integer status = banner.getStatus();
        if(status.intValue() == param.getStatus().intValue()){
            return BaseAnswer.success(null);
        }
        if(status == 0 && param.getStatus() == 0){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "Banner状态为待发布时，不能进行下线操作");
        }
        banner.setStatus(param.getStatus());
        banner.setUpdateTime(now);
        bannerMapper.updateByPrimaryKeySelective(banner);
        //日志记录
        StringBuilder logContent = new StringBuilder(param.getStatus().intValue() == 1 ? "【上线】" : "【下线】").append("\n");
        logContent.append("目录banner名称").append(banner.getName());
        logService.recordOperateLog(ModuleEnum.ESTEWARD_MANAGE.code,
                EstewardOperateEnum.DIRECTORY_OPERATE.code,
                logContent.toString(), LogResultEnum.LOG_SUCESS.code, null);
        return BaseAnswer.success(null);
    }

    @Override
    public BaseAnswer<Void> delete(String id) {
        EstewardBanner banner = bannerMapper.selectByPrimaryKey(id);
        if (banner == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "Banner不存在");
        }
        if(banner.getStatus() == 1){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "Banner状态为已上线时，不能进行删除操作");
        }
        bannerMapper.deleteByPrimaryKey(id);
        //日志记录
        StringBuilder logContent = new StringBuilder("【删除】").append("\n");
        logContent.append("目录banner名称").append(banner.getName());
        logService.recordOperateLog(ModuleEnum.ESTEWARD_MANAGE.code,
                EstewardOperateEnum.DIRECTORY_OPERATE.code,
                logContent.toString(), LogResultEnum.LOG_SUCESS.code, null);
        return BaseAnswer.success(null);
    }
}
