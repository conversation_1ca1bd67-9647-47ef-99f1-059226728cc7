package com.chinamobile.retail.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.BasePageQuery;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.dto.PartnerPointItemDTO;
import com.chinamobile.retail.pojo.dto.SupplierProductDTO;
import com.chinamobile.retail.pojo.entity.PointSupplier;
import com.chinamobile.retail.pojo.param.AssociateProductParam;
import com.chinamobile.retail.pojo.param.PartnerPointQueryParam;
import com.chinamobile.retail.pojo.param.SupplierParam;
import com.chinamobile.retail.pojo.param.SupplierProductQueryParam;
import com.chinamobile.retail.pojo.vo.PointSupplierVO;
import com.chinamobile.retail.pojo.vo.SupplierIdNameListVO;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SupplierService {
    /**积分供应商列表*/
    BaseAnswer<PageInfo<PointSupplierVO>> getSupplierList(Integer page,Integer pageSize,String key);

    /**积分供应商创建*/
    BaseAnswer<Void> createSupplier(SupplierParam param);

    /**积分供应商创建*/
    BaseAnswer<Void> editSupplier(SupplierParam param);

    /**积分供应商关联商品列表*/
    BaseAnswer<PageInfo<SupplierProductDTO>> getProductList(Integer page, Integer pageSize, String supplierId, String key, LoginIfo4Redis loginIfo4Redis);

    /**删除关联商品*/
    BaseAnswer<Void> deleteProducts( List<String> ids, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<PageInfo<SupplierProductDTO>> getUnassociatedProductList(SupplierProductQueryParam param);

    BaseAnswer<Void> associateProduct(AssociateProductParam param);

    /**同步商城删除关联商品*/
    BaseAnswer<Void> syncSkuDelete( List<String> skuOfferingCodes);

    BaseAnswer<PageData<SupplierIdNameListVO>> getSupplierIdNameList(BasePageQuery param);
}
