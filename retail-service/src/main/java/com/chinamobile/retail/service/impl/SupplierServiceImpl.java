package com.chinamobile.retail.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.BasePageQuery;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.dao.*;
import com.chinamobile.retail.dao.ext.PointSupplierMapperExt;
import com.chinamobile.retail.dao.ext.SupplierAssociateProductMapperExt;
import com.chinamobile.retail.exception.StatusContant;
import com.chinamobile.retail.pojo.dto.PartnerPointItemDTO;
import com.chinamobile.retail.pojo.dto.SupplierProductDTO;
import com.chinamobile.retail.pojo.entity.*;
import com.chinamobile.retail.pojo.mapper.SupplierIdNameListDO;
import com.chinamobile.retail.pojo.param.AssociateProductParam;
import com.chinamobile.retail.pojo.param.PartnerPointQueryParam;
import com.chinamobile.retail.pojo.param.SupplierParam;
import com.chinamobile.retail.pojo.param.SupplierProductQueryParam;
import com.chinamobile.retail.pojo.vo.PointSupplierVO;
import com.chinamobile.retail.pojo.vo.SupplierIdNameListVO;
import com.chinamobile.retail.service.PartnerPointService;
import com.chinamobile.retail.service.SupplierService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.common.BaseConstant.*;

/**
 * 合伙人积分服务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SupplierServiceImpl implements SupplierService {

    @Resource
    private PointSupplierMapper pointSupplierMapper;

    @Resource
    private SupplierAssociateProductMapper supplierAssociateProductMapper;

    @Resource
    private SkuOfferingInfoMapper skuOfferingInfoMapper;

    @Resource
    private SupplierAssociateProductMapperExt supplierAssociateProductMapperExt;

    @Resource
    private PointSupplierMapperExt pointSupplierMapperExt;

    @Override
    @DS("query")
    public BaseAnswer<PageInfo<PointSupplierVO>> getSupplierList(Integer page, Integer pageSize, String key) {
        PointSupplierExample example = new PointSupplierExample();
        example.setOrderByClause("create_time DESC");
        if (StringUtils.isNotEmpty(key)) {
            PointSupplierExample.Criteria criteria1 = example.createCriteria();
            criteria1.andFullNameLike("%" + key + "%");
            PointSupplierExample.Criteria criteria2 = example.createCriteria();
            criteria2.andAbbreviationNameLike("%" + key + "%");
            example.or(criteria2);
        }

        //TODO 待SQL优化
        PageHelper.startPage(page, pageSize);
        List<PointSupplier> pointSuppliers = pointSupplierMapper.selectByExample(example);
        PageInfo<PointSupplier> data = new PageInfo<>(pointSuppliers);
        List<PointSupplierVO> resultData = null;
        PageInfo<PointSupplierVO> pageInfo = new PageInfo<>();
        BeanUtils.copyProperties(data, pageInfo);
        if (CollectionUtils.isNotEmpty(pointSuppliers)) {
            resultData = pointSuppliers.stream().map(item -> {
                PointSupplierVO vo = new PointSupplierVO();
                BeanUtils.copyProperties(item, vo);

                //查询关联的商品数量及生效产品
                SupplierAssociateProductExample supplierAssociateProductExample = new SupplierAssociateProductExample();
                SupplierAssociateProductExample.Criteria supplierAssociateProductCriteria = supplierAssociateProductExample.createCriteria();
                supplierAssociateProductCriteria.andSupplierIdEqualTo(item.getId()).andDeleteTimeIsNull();
                supplierAssociateProductExample.setOrderByClause("create_time DESC");
                List<SupplierProductDTO> products = supplierAssociateProductMapperExt.queryAssociateProducts(item.getId(),null);
                if (CollectionUtils.isNotEmpty(products)) {
                    vo.setAssociated(products.size());
                    //监测积分是否生效
                    vo.setValid((int)products.stream().filter(SupplierProductDTO::getValid).count());
                } else {
                    vo.setAssociated(0);
                    vo.setValid(0);
                }


                return vo;
            }).collect(Collectors.toList());
            pageInfo.setList(resultData);
        }
        BaseAnswer<PageInfo<PointSupplierVO>> result = new BaseAnswer<>();
        result.setData(pageInfo);
        return result;
    }

    @Override
    @DS("save")
    public BaseAnswer<Void> createSupplier(SupplierParam param) {
        PointSupplierExample example = new PointSupplierExample();
        PointSupplierExample.Criteria criteria = example.createCriteria();
        criteria.andCodeEqualTo(param.getCode());
        List<PointSupplier> suppliers = pointSupplierMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(suppliers)) {
            throw new BusinessException(StatusContant.SUPPLIER_ERROR, "社会信用代码与供应商["
                    + suppliers.get(0).getAbbreviationName() + "]重复");
        }
        PointSupplier supplier = new PointSupplier();
        BeanUtils.copyProperties(param, supplier);
        supplier.setId(BaseServiceUtils.getId());
        Date date = new Date();
        supplier.setCreateTime(date);
        supplier.setUpdateTime(date);
        pointSupplierMapper.insertSelective(supplier);
        return new BaseAnswer<>();
    }

    @Override
    @DS("save")
    public BaseAnswer<Void> editSupplier(SupplierParam param) {
        PointSupplierExample example = new PointSupplierExample();
        PointSupplierExample.Criteria criteria = example.createCriteria();
        criteria.andCodeEqualTo(param.getCode());
        List<PointSupplier> suppliers = pointSupplierMapper.selectByExample(example);
        PointSupplier supplier = pointSupplierMapper.selectByPrimaryKey(param.getId());
        if (CollectionUtils.isNotEmpty(suppliers) && !StringUtils.equals(supplier.getId(),suppliers.get(0).getId())) {
            throw new BusinessException(StatusContant.SUPPLIER_ERROR, "社会信用代码与供应商["
                    + suppliers.get(0).getAbbreviationName() + "]重复");
        }

        BeanUtils.copyProperties(param, supplier);
        Date date = new Date();
        supplier.setUpdateTime(date);
        pointSupplierMapper.updateByPrimaryKeySelective(supplier);
        return new BaseAnswer<>();
    }

    @Override
    @DS("query")
    public BaseAnswer<PageInfo<SupplierProductDTO>> getProductList(Integer page, Integer pageSize, String supplierId, String key, LoginIfo4Redis loginIfo4Redis) {
        String roleType = loginIfo4Redis.getRoleType();
        if (!ADMIN_ROLE.equals(roleType) && !OPERATOR_ROLE.equals(roleType) && !POINT_MANAGER.equals(roleType)){
            throw new BusinessException(StatusContant.NO_DATA,"无权限");
        }

        PageHelper.startPage(page, pageSize);
        List<SupplierProductDTO> productDTOS = supplierAssociateProductMapperExt.queryAssociateProducts(supplierId, key);
        PageInfo<SupplierProductDTO> pageInfo = new PageInfo<>(productDTOS);
        BaseAnswer<PageInfo<SupplierProductDTO>> result = new BaseAnswer<>();
        result.setData(pageInfo);
        return result;
    }

    @Override
    @DS("save")
    public BaseAnswer<Void> deleteProducts(List<String> ids, LoginIfo4Redis loginIfo4Redis) {
        String roleType = loginIfo4Redis.getRoleType();
        if (!ADMIN_ROLE.equals(roleType)  && !POINT_MANAGER.equals(roleType)){
            throw new BusinessException(StatusContant.NO_DATA,"无权限");
        }
        List<SupplierProductDTO> productDTOS = supplierAssociateProductMapperExt.queryCannotDelProduct(ids);
        if (CollectionUtils.isNotEmpty(productDTOS)) {
            String msg = "关联商品" + productDTOS.stream().map(SupplierProductDTO::getSkuName).collect(Collectors.toList())
                    + "处于积分生效状态，不能删除";
            throw new BusinessException(StatusContant.ASSOCIATE_PRODUCT_ERROR, msg);
        }
        SupplierAssociateProductExample example = new SupplierAssociateProductExample();
        SupplierAssociateProductExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(ids);
        SupplierAssociateProduct product = new SupplierAssociateProduct();
        product.setDeleteTime(new Date());
        supplierAssociateProductMapper.updateByExampleSelective(product,example);
        return new BaseAnswer<>();
    }

    @Override
    @DS("query")
    public BaseAnswer<PageInfo<SupplierProductDTO>> getUnassociatedProductList(SupplierProductQueryParam param) {
        PageHelper.startPage(param.getPage(), param.getPageSize());
        List<SupplierProductDTO> productDTOS = supplierAssociateProductMapperExt.queryUnassociatedProducts(param);
        PageInfo<SupplierProductDTO> pageInfo = new PageInfo<>(productDTOS);
        BaseAnswer<PageInfo<SupplierProductDTO>> result = new BaseAnswer<>();
        result.setData(pageInfo);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("save")
    public BaseAnswer<Void> associateProduct(AssociateProductParam param) {
        Date time = new Date();
        for (String productId : param.getProductIds()) {
            String supplierId = param.getSupplierId();
            SupplierAssociateProductExample example = new SupplierAssociateProductExample();
            example.createCriteria().andProductIdEqualTo(productId).andSupplierIdEqualTo(supplierId);
            supplierAssociateProductMapper.deleteByExample(example);

            SupplierAssociateProduct  product = new SupplierAssociateProduct();
            product.setId(BaseServiceUtils.getId());
            product.setSupplierId(supplierId);
            product.setProductId(productId);
            product.setCreateTime(time);
            supplierAssociateProductMapper.insertSelective(product);
        }
        return new BaseAnswer<>();
    }

    @Override
    @DS("save")
    public BaseAnswer<Void> syncSkuDelete(List<String> skuOfferingCodes) {
        SupplierAssociateProductExample example = new SupplierAssociateProductExample();
        SupplierAssociateProductExample.Criteria criteria = example.createCriteria();
        criteria.andProductIdIn(skuOfferingCodes);
        SupplierAssociateProduct product = new SupplierAssociateProduct();
        product.setDeleteTime(new Date());
        supplierAssociateProductMapper.updateByExampleSelective(product,example);
        return new BaseAnswer<>();
    }

    @Override
    @DS("query")
    public BaseAnswer<PageData<SupplierIdNameListVO>> getSupplierIdNameList(BasePageQuery param) {
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        if(pageNum <=0 || pageSize <= 0){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"页码和每页数量必须大于0");
        }
        PageData<SupplierIdNameListVO> pageData = new PageData<>();
        PageHelper.startPage(pageNum,pageSize);
        List<SupplierIdNameListDO> doList = pointSupplierMapperExt.getSupplierIdNameList();
        if(CollectionUtils.isEmpty(doList)){
            return BaseAnswer.success(pageData);
        }
        PageInfo<SupplierIdNameListDO> pageInfo = new PageInfo<>(doList);
        pageData.setCount(pageInfo.getTotal());
        List<SupplierIdNameListVO> list = doList.stream().map(d -> {
            SupplierIdNameListVO vo = new SupplierIdNameListVO();
            BeanUtils.copyProperties(d, vo);
            return vo;
        }).collect(Collectors.toList());
        pageData.setPage(pageNum);
        pageData.setData(list);
        return BaseAnswer.success(pageData);
    }
}
