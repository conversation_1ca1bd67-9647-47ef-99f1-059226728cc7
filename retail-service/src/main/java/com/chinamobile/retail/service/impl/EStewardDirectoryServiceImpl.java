package com.chinamobile.retail.service.impl;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.enums.log.EstewardOperateEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.OrderManageOperateEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.retail.config.CommonConstant;
import com.chinamobile.retail.constant.RedisLockConstant;
import com.chinamobile.retail.dao.EstewardDirectoryMapper;
import com.chinamobile.retail.dao.EstewardProductDirectoryMapper;
import com.chinamobile.retail.dao.ext.EstewardDirectoryMapperExt;
import com.chinamobile.retail.dao.ext.EstewardProductMapperExt;
import com.chinamobile.retail.pojo.entity.EstewardDirectory;
import com.chinamobile.retail.pojo.entity.EstewardDirectoryExample;
import com.chinamobile.retail.pojo.entity.EstewardProductDirectory;
import com.chinamobile.retail.pojo.entity.EstewardProductDirectoryExample;
import com.chinamobile.retail.pojo.param.EStewardDirectoryListParam;
import com.chinamobile.retail.pojo.param.EStewardDirectorySortParam;
import com.chinamobile.retail.pojo.param.SaveEStewardDirectoryParam;
import com.chinamobile.retail.pojo.vo.EStewardDirectoryDetailVO;
import com.chinamobile.retail.pojo.vo.EStewardDirectoryListVO;
import com.chinamobile.retail.pojo.vo.EStewardDirectoryWithProductVO;
import com.chinamobile.retail.service.EStewardDirectoryService;
import com.chinamobile.retail.util.cache.RedisCacheUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EStewardDirectoryServiceImpl implements EStewardDirectoryService {

    @Resource
    private EstewardDirectoryMapper directoryMapper;

    @Resource
    private EstewardDirectoryMapperExt directoryMapperExt;

    @Resource
    private RedisCacheUtil redisCacheUtil;

    @Resource
    private EstewardProductDirectoryMapper productDirectoryMapper;
    @Autowired
    private RedisUtil redisUtil;

    @Resource
    private LogService logService;
    @Autowired
    private EstewardProductMapperExt estewardProductMapperExt;

    @Override
    public BaseAnswer<Void> saveDirectory(SaveEStewardDirectoryParam param, String userId) {
        Date now = new Date();
        String id = param.getId();
        String parentId;
        if(param.getLevel() == 2){
            //二级目录
             if(StringUtils.isEmpty(param.getFirstDirectoryId())){
                 throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"一级目录id不能为空");
             }
             parentId = param.getFirstDirectoryId();
        }else if(param.getLevel() == 3){
            //三级目录
             if(StringUtils.isEmpty(param.getFirstDirectoryId()) || StringUtils.isEmpty(param.getSecondDirectoryId())){
                 throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"一级目录id和二级目录id不能为空");
             }
             parentId = param.getSecondDirectoryId();
        } else {
            parentId = "-1";
        }
        boolean create = false;
        String oldName = null;
        String oldLevel = null;
        String oldParentName = null;
        String newName = null;
        String newLevel = null;
        String newParentName = null;
        if(StringUtils.isNotEmpty(id)){
            //更新
            EstewardDirectory directory = directoryMapper.selectByPrimaryKey(id);
            oldName = directory.getName();
            oldLevel = directory.getLevel() +"级";
            if(!"-1".equals(directory.getParentId())){
                EstewardDirectory parentDirectory = directoryMapper.selectByPrimaryKey(directory.getParentId());
                if(parentDirectory != null){
                    oldParentName = parentDirectory.getName();
                }
            }
            if(directory == null){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"目录不存在");
            }
            BeanUtils.copyProperties(param,directory);
            directory.setParentId(parentId);
            directory.setUpdateTime(now);
            try{
                directoryMapper.updateByPrimaryKeySelective(directory);
            }catch (Exception e){
                String message = e.getMessage();
                if(message != null && message.contains("Duplicate entry") && message.contains("idx_parent_id_name")){
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"同一个父目录下的目录名称不允许重复");
                }else {
                    log.error("目录保存失败",e);
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"目录保存失败");
                }
            }
        }else {
            create = true;
            //新建
            EstewardDirectory directory = new EstewardDirectory();
            BeanUtils.copyProperties(param,directory);
            directory.setId(BaseServiceUtils.getId());
            directory.setParentId(parentId);
            directory.setCreateTime(now);
            directory.setUpdateTime(now);
            //避免并发时相同目录下sort值重复
            String lockKey = param.getFirstDirectoryId()+"_"+param.getSecondDirectoryId();
            redisUtil.smartLock(lockKey,() -> {
                // 获取最大sort值并加1
                int maxSort = directoryMapperExt.selectMaxSortByParentId(parentId);
                directory.setSort(maxSort + 1);
                try {
                    directoryMapper.insertSelective(directory);
                }catch (Exception e){
                    String message = e.getMessage();
                    if(message != null && message.contains("Duplicate entry") && message.contains("idx_parent_id_name")){
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"同一个父目录下的目录名称不允许重复");
                    }else {
                        log.error("目录保存失败",e);
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"目录保存失败");
                    }
                }
                return null;
            });
        }
        EstewardDirectory newParentDirectory = directoryMapper.selectByPrimaryKey(parentId);
        if(newParentDirectory != null){
            newParentName = newParentDirectory.getName();
        }
        newLevel = param.getLevel() +"级";
        newName = param.getName();
//        redisCacheUtil.deleteAll(Constant.REDIS_KEY_ESTEWARD_DIRECTORY_LIST);
        //日志记录
        StringBuilder logContent = new StringBuilder("【目录").append(create ? "新建" : "编辑").append("】").append("\n");
        if(create){
            logContent.append("新建目录名称").append(newName).append(";\n");
            logContent.append("目录级别").append(newLevel).append(";\n");
            logContent.append("父目录").append(newParentName).append(";");
        }else {
            logContent.append("目录名称").append(oldName).append("修改为").append(newName).append(";\n");
            logContent.append("目录级别").append(oldLevel).append("修改为").append(newLevel).append(";\n");
            logContent.append("父目录").append(oldParentName).append("修改为").append(newParentName).append(";");
        }
        logService.recordOperateLog(ModuleEnum.ESTEWARD_MANAGE.code,
                EstewardOperateEnum.DIRECTORY_OPERATE.code,
                logContent.toString(), LogResultEnum.LOG_SUCESS.code, null);
        return BaseAnswer.success(null);
    }


    @Override
    public BaseAnswer<PageData<EStewardDirectoryListVO>> directoryTreeData(EStewardDirectoryListParam param) {
//        String key = param.getPageNum()+"_"+param.getPageSize();
//        List<EStewardDirectoryListVO> voList = redisCacheUtil.loadAndCache(Constant.REDIS_KEY_ESTEWARD_DIRECTORY_LIST + key, RedisLockConstant.REDIS_KEY_ESTEWARD_DIRECTORY_LIST + key, 1, TimeUnit.DAYS, () -> {
//            // 获取所有目录
//            List<EstewardDirectory> allDirectories = directoryMapperExt.selectAllDirectories();
//
//            // 构建目录映射
//            Map<String, EStewardDirectoryListVO> directoryMap = new HashMap<>();
//            for (EstewardDirectory directory : allDirectories) {
//                EStewardDirectoryListVO vo = new EStewardDirectoryListVO();
//                BeanUtils.copyProperties(directory, vo);
//                directoryMap.put(directory.getId(), vo);
//            }
//
//            // 构建树形结构
//            List<EStewardDirectoryListVO> rootDirectories = new ArrayList<>();
//            for (EStewardDirectoryListVO vo : directoryMap.values()) {
//                if ("-1".equals(vo.getParentId())) {
//                    rootDirectories.add(vo);
//                } else {
//                    EStewardDirectoryListVO parentVo = directoryMap.get(vo.getParentId());
//                    if (parentVo != null) {
//                        if (parentVo.getChildren() == null) {
//                            parentVo.setChildren(new ArrayList<>());
//                        }
//                        parentVo.getChildren().add(vo);
//                    }
//                }
//            }
//            return rootDirectories;
//        });
//        // 分页处理
//        int page = param.getPageNum();
//        int pageSize = param.getPageSize();
//        PageData<EStewardDirectoryListVO> pageData = new PageData<>();
//        pageData.setPage(page);
//
//        if(CollectionUtils.isEmpty(voList)){
//            return BaseAnswer.success(pageData);
//        }
//
//        int offset = (page - 1) * pageSize;
//        int end = Math.min(offset + pageSize, voList.size());
//        List<EStewardDirectoryListVO> paginatedDirectories = voList.subList(offset, end);
//
//        // 构建分页数据
//        pageData.setCount(voList.size());
//        pageData.setData(paginatedDirectories);
//        return BaseAnswer.success(pageData);
        return null;
    }

    @Override
    public BaseAnswer<PageData<EStewardDirectoryListVO>> listByParentId(EStewardDirectoryListParam param) {
        String parentId = param.getParentId();
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        if (pageNum < 0 || pageSize < 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "页码和每页数量必须大于0");
        }
        PageData<EStewardDirectoryListVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        EstewardDirectoryExample example = new EstewardDirectoryExample().createCriteria().andParentIdEqualTo(parentId).example();
        example.setOrderByClause("sort asc");
        PageHelper.startPage(pageNum,pageSize);
        List<EstewardDirectory> directoryList = directoryMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(directoryList)){
            return BaseAnswer.success(pageData);
        }
        PageInfo<EstewardDirectory> pageInfo = new PageInfo<>(directoryList);
        pageData.setCount(pageInfo.getTotal());
        List<EStewardDirectoryListVO> voList = directoryList.stream().map(d -> {
            EStewardDirectoryListVO vo = new EStewardDirectoryListVO();
            BeanUtils.copyProperties(d, vo);
            return vo;
        }).collect(Collectors.toList());
        pageData.setData(voList);
        return BaseAnswer.success(pageData);
    }

    @Override
    public BaseAnswer<EStewardDirectoryDetailVO> getDetail(String id) {
        EstewardDirectory directory = directoryMapper.selectByPrimaryKey(id);
        if (directory == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "目录不存在");
        }

        EStewardDirectoryDetailVO detailVO = new EStewardDirectoryDetailVO();
        BeanUtils.copyProperties(directory, detailVO);

        if (directory.getLevel() == 2) {
            // 二级目录，获取一级目录信息
            EstewardDirectory firstDirectory = directoryMapper.selectByPrimaryKey(directory.getParentId());
            if (firstDirectory != null) {
                detailVO.setFirstDirectoryId(firstDirectory.getId());
                detailVO.setFirstDirectoryName(firstDirectory.getName());
            }
        } else if (directory.getLevel() == 3) {
            // 三级目录，获取一级和二级目录信息
            EstewardDirectory secondDirectory = directoryMapper.selectByPrimaryKey(directory.getParentId());
            if (secondDirectory != null) {
                detailVO.setSecondDirectoryId(secondDirectory.getId());
                detailVO.setSecondDirectoryName(secondDirectory.getName());

                EstewardDirectory firstDirectory = directoryMapper.selectByPrimaryKey(secondDirectory.getParentId());
                if (firstDirectory != null) {
                    detailVO.setFirstDirectoryId(firstDirectory.getId());
                    detailVO.setFirstDirectoryName(firstDirectory.getName());
                }
            }
        }
        return BaseAnswer.success(detailVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
public BaseAnswer<Void> sort(EStewardDirectorySortParam param) {
    String id = param.getId();
    Integer sort = param.getSort();
    if (sort != 1 && sort != -1) {
        throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "排序只能是1或-1");
    }
    EstewardDirectory directory = directoryMapper.selectByPrimaryKey(id);
    if (directory == null) {
        throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "目录不存在");
    }
    redisUtil.smartLock("EStewardDirectorySortParam_"+param.getId()+"_"+param.getSort(),() -> {
        EstewardDirectory swapDirectory = null;
        if (sort == 1) {
            // 下移，表示和下一个目录交换位置
            EstewardDirectoryExample example = new EstewardDirectoryExample()
                    .createCriteria()
                    .andParentIdEqualTo(directory.getParentId())
                    .andSortGreaterThan(directory.getSort())
                    .example();
            example.setOrderByClause("sort asc");
            List<EstewardDirectory> nextDirectories = directoryMapper.selectByExample(
                    example
            );
            if (CollectionUtils.isNotEmpty(nextDirectories)) {
                swapDirectory = nextDirectories.get(0);
            } else {
                // 没有下一个目录，直接返回成功
                return BaseAnswer.success(null);
            }
        } else {
            // 上移，表示和上一个目录交换位置
            EstewardDirectoryExample example = new EstewardDirectoryExample()
                    .createCriteria()
                    .andParentIdEqualTo(directory.getParentId())
                    .andSortLessThan(directory.getSort())
                    .example();
            example.setOrderByClause("sort desc");
            List<EstewardDirectory> preDirectories = directoryMapper.selectByExample(
                    example
            );
            if (CollectionUtils.isNotEmpty(preDirectories)) {
                swapDirectory = preDirectories.get(0);
            } else {
                // 没有上一个目录，直接返回成功
                return BaseAnswer.success(null);
            }
        }

        if (swapDirectory != null) {
            // 交换两个目录的sort值
            int tempSort = directory.getSort();
            directory.setSort(swapDirectory.getSort());
            swapDirectory.setSort(tempSort);

            directoryMapper.updateByPrimaryKeySelective(directory);
            directoryMapper.updateByPrimaryKeySelective(swapDirectory);
        }
        //日志记录
        StringBuilder logContent = new StringBuilder("【目录 "+(sort > 0 ? "下移" : "上移")+"】"+"\n");
        logContent.append("修改目录 "+directory.getName()+" 排序");
        logService.recordOperateLog(ModuleEnum.ESTEWARD_MANAGE.code,
                EstewardOperateEnum.DIRECTORY_OPERATE.code,
                logContent.toString(), LogResultEnum.LOG_SUCESS.code, null);
        return null;
    });
    return BaseAnswer.success(null);
}

    @Override
    public BaseAnswer<Void> delete(String id) {
        EstewardDirectory directory = directoryMapper.selectByPrimaryKey(id);
        if (directory == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "目录不存在");
        }
        //若删除的目录下存在子目录或商品，则提示“删除失败，请先清空子目录或目录下所有商品！"
        EstewardDirectoryExample example = new EstewardDirectoryExample();
        example.createCriteria().andParentIdEqualTo(directory.getId());
        List<EstewardDirectory> childDirectories = directoryMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(childDirectories)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "删除失败，请先清空子目录");
        }
        Integer count = estewardProductMapperExt.countByDirectoryId(id);
        if(count > 0 ){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "删除失败，请先清空目录下所有商品");
        }
        directoryMapper.deleteByPrimaryKey(id);
        //日志记录
        StringBuilder logContent = new StringBuilder("【目录删除】").append("\n");
        logContent.append("删除目录").append(directory.getName());
        logService.recordOperateLog(ModuleEnum.ESTEWARD_MANAGE.code,
                EstewardOperateEnum.DIRECTORY_OPERATE.code,
                logContent.toString(), LogResultEnum.LOG_SUCESS.code, null);
        return BaseAnswer.success(null);
    }

    @Override
    public BaseAnswer<List<EStewardDirectoryWithProductVO>> listWithProduct(EStewardDirectoryListParam param) {
        String parentId = param.getParentId();
        if(!"-1".equals(parentId)){
            // 判断父级目录是否存在
            EstewardDirectory parentDirectory = directoryMapper.selectByPrimaryKey(parentId);
            if(parentDirectory == null){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "父级目录不存在");
            }
        }
        List<EStewardDirectoryWithProductVO> directories = directoryMapperExt.selectDirectoriesWithChildren(parentId);
        //获取父目录及当前目录下的所有商品信息
        List<String> directoryIdList = new ArrayList<>();
        if(!"-1".equals(parentId)){
            //商品最低需要绑定一级目录
            directoryIdList.add(parentId);
        }
        if(CollectionUtils.isNotEmpty(directories)){
            for (EStewardDirectoryWithProductVO directory : directories) {
                if(StringUtils.isNotEmpty(directory.getDirectoryId())){
                    directoryIdList.add(directory.getDirectoryId());
                }
                //商品不需要获取再下一级，前端不需要获取子目录下的商品信息
/*                if(CollectionUtils.isNotEmpty(directory.getChildren())){
                    for (EStewardDirectoryWithProductVO childDirectory : directory.getChildren()) {
                        if(StringUtils.isNotEmpty(childDirectory.getDirectoryId())){
                            directoryIdList.add(childDirectory.getDirectoryId());
                        }
                    }
                }*/
            }
        }
        if(CollectionUtils.isNotEmpty(directoryIdList)){
            //根据目录id获取商品信息
            List<EStewardDirectoryWithProductVO> products = directoryMapperExt.selectProductsByDirectoryIds(directoryIdList);
            if(CollectionUtils.isNotEmpty(products)){
                Map<String, List<EStewardDirectoryWithProductVO>> directoryIdAndProductDataMap = products.stream().collect(Collectors.groupingBy(EStewardDirectoryWithProductVO::getDirectoryId));
                //把商品信息设置到当前级别目录中
                for (EStewardDirectoryWithProductVO directory : directories) {
                    String directoryId = directory.getDirectoryId();
                    List<EStewardDirectoryWithProductVO> productVOList = directoryIdAndProductDataMap.get(directoryId);
                    if(CollectionUtils.isNotEmpty(productVOList)){
                        directory.getChildren().addAll(productVOList);
                    }
                }
                //把商品信息设置到父级别目录中
                List<EStewardDirectoryWithProductVO> parentProductVoList = directoryIdAndProductDataMap.get(parentId);
                if(CollectionUtils.isNotEmpty(parentProductVoList)){
                    directories.addAll(parentProductVoList);
                }
            }
        }
        return BaseAnswer.success(directories);
    }

}
