package com.chinamobile.retail.service.impl;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.util.HttpUtil;
import com.chinamobile.retail.config.CommonConstant;
import com.chinamobile.retail.config.WeixinApplicationConfig;
import com.chinamobile.retail.pojo.dto.WeixinAppLoginDTO;
import com.chinamobile.retail.pojo.vo.TimeAndNonceAndSignVO;
import com.chinamobile.retail.pojo.vo.WeixinAccessTokenVO;
import com.chinamobile.retail.schedule.WeixinAccessTokenRefreshTask;
import com.chinamobile.retail.schedule.WeixinJsapiTicketRefreshTask;
import com.chinamobile.retail.service.WeixinService;
import com.chinamobile.retail.util.LakalaRSAUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * created by liuxiang on 2022/12/28 10:17
 */
@Service
@Slf4j
public class WeixinServiceImpl implements WeixinService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private WeixinJsapiTicketRefreshTask weixinJsapiTicketRefreshTask;

    @Autowired
    private WeixinAccessTokenRefreshTask weixinAccessTokenRefreshTask;

    @Autowired
    private WeixinApplicationConfig weixinApplicationConfig;

    @Override
    public BaseAnswer<TimeAndNonceAndSignVO> getTimeAndNonceAndSign(String url) {
        String jsapiTicket = stringRedisTemplate.opsForValue().get(CommonConstant.redis_weixin_jsapi_ticket_key);
        if(jsapiTicket == null){
            weixinJsapiTicketRefreshTask.work();
            jsapiTicket = stringRedisTemplate.opsForValue().get(CommonConstant.redis_weixin_jsapi_ticket_key);
        }
        //随机字符串
        String nonceStr = BaseServiceUtils.getId();
        //时间戳
        Long timeStamp = System.currentTimeMillis() / 1000;
        Map<String,String> map = new HashMap<>();
        map.put("noncestr",nonceStr);
        map.put("jsapi_ticket",jsapiTicket);
        map.put("timestamp",timeStamp+"");
        map.put("url",url);
        //参数排序
        String weixinSignContent = LakalaRSAUtils.getWeixinSignContent(map);
        //签名
        String signature = BaseServiceUtils.getSha1(weixinSignContent);
        TimeAndNonceAndSignVO vo = new TimeAndNonceAndSignVO();
        vo.setNonceStr(nonceStr);
        vo.setTimestamp(timeStamp);
        vo.setSignature(signature);
        return BaseAnswer.success(vo);
    }

    @Override
    public BaseAnswer<Void> refreshAccessToken() {
        weixinAccessTokenRefreshTask.work();
        return BaseAnswer.success(null);
    }

    @Override
    public BaseAnswer<Void> refreshJsapiTicket() {
        weixinJsapiTicketRefreshTask.work();
        return BaseAnswer.success(null);
    }

    @Override
    public BaseAnswer<String> appLogin(String code) {
        String appId = weixinApplicationConfig.getAppId();
        String secret = weixinApplicationConfig.getAppSecret();
        Object o = null;
        try {
            o = HttpUtil.get("https://api.weixin.qq.com/sns/jscode2session?appid=" + appId + "&secret=" + secret + "&js_code=" + code + "&grant_type=authorization_code", null, 10000, 10000);
        } catch (Exception e) {
            log.error("微信小程序登录发生异常",e);
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"微信小程序登录发生异常");
        }
        if( o != null){
            WeixinAppLoginDTO dto = JSON.parseObject((String) o, WeixinAppLoginDTO.class);
            if(dto.getErrcode() != null && dto.getErrmsg() != null){
                log.error("微信小程序登录返回失败,errcode:{},errmsg:{}",dto.getErrcode(),dto.getErrmsg());
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"微信小程序登录返回失败");
            }
            return BaseAnswer.success(dto.getOpenid());
        }
        return null;
    }

}
