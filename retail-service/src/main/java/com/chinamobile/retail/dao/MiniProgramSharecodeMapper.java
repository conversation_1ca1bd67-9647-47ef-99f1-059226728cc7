package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.MiniProgramSharecode;
import com.chinamobile.retail.pojo.entity.MiniProgramSharecodeExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MiniProgramSharecodeMapper {
    /**
     *
     * @mbg.generated Wed Oct 16 09:51:01 CST 2024
     */
    long countByExample(MiniProgramSharecodeExample example);

    /**
     *
     * @mbg.generated Wed Oct 16 09:51:01 CST 2024
     */
    int deleteByExample(MiniProgramSharecodeExample example);

    /**
     *
     * @mbg.generated Wed Oct 16 09:51:01 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Oct 16 09:51:01 CST 2024
     */
    int insert(MiniProgramSharecode record);

    /**
     *
     * @mbg.generated Wed Oct 16 09:51:01 CST 2024
     */
    int insertSelective(MiniProgramSharecode record);

    /**
     *
     * @mbg.generated Wed Oct 16 09:51:01 CST 2024
     */
    List<MiniProgramSharecode> selectByExample(MiniProgramSharecodeExample example);

    /**
     *
     * @mbg.generated Wed Oct 16 09:51:01 CST 2024
     */
    MiniProgramSharecode selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Wed Oct 16 09:51:01 CST 2024
     */
    int updateByExampleSelective(@Param("record") MiniProgramSharecode record, @Param("example") MiniProgramSharecodeExample example);

    /**
     *
     * @mbg.generated Wed Oct 16 09:51:01 CST 2024
     */
    int updateByExample(@Param("record") MiniProgramSharecode record, @Param("example") MiniProgramSharecodeExample example);

    /**
     *
     * @mbg.generated Wed Oct 16 09:51:01 CST 2024
     */
    int updateByPrimaryKeySelective(MiniProgramSharecode record);

    /**
     *
     * @mbg.generated Wed Oct 16 09:51:01 CST 2024
     */
    int updateByPrimaryKey(MiniProgramSharecode record);

    /**
     *
     * @mbg.generated Wed Oct 16 09:51:01 CST 2024
     */
    int batchInsert(@Param("list") List<MiniProgramSharecode> list);

    /**
     *
     * @mbg.generated Wed Oct 16 09:51:01 CST 2024
     */
    int batchInsertSelective(@Param("list") List<MiniProgramSharecode> list, @Param("selective") MiniProgramSharecode.Column ... selective);
}