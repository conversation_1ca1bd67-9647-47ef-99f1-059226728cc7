package com.chinamobile.retail.dao;

import com.chinamobile.retail.pojo.entity.MiniProgramHomeAd;
import com.chinamobile.retail.pojo.entity.MiniProgramHomeAdExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MiniProgramHomeAdMapper {
    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    long countByExample(MiniProgramHomeAdExample example);

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    int deleteByExample(MiniProgramHomeAdExample example);

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    int deleteByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    int insert(MiniProgramHomeAd record);

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    int insertSelective(MiniProgramHomeAd record);

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    List<MiniProgramHomeAd> selectByExample(MiniProgramHomeAdExample example);

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    MiniProgramHomeAd selectByPrimaryKey(String id);

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    int updateByExampleSelective(@Param("record") MiniProgramHomeAd record, @Param("example") MiniProgramHomeAdExample example);

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    int updateByExample(@Param("record") MiniProgramHomeAd record, @Param("example") MiniProgramHomeAdExample example);

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    int updateByPrimaryKeySelective(MiniProgramHomeAd record);

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    int updateByPrimaryKey(MiniProgramHomeAd record);

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    int batchInsert(@Param("list") List<MiniProgramHomeAd> list);

    /**
     *
     * @mbg.generated Mon Oct 21 11:10:57 CST 2024
     */
    int batchInsertSelective(@Param("list") List<MiniProgramHomeAd> list, @Param("selective") MiniProgramHomeAd.Column ... selective);
}