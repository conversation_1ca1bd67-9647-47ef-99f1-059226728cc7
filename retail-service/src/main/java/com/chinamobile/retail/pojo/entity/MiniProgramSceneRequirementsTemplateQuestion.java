package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 小程序场景需求模版问题表
 *
 * <AUTHOR>
public class MiniProgramSceneRequirementsTemplateQuestion implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    private String id;

    /**
     * 模版id
     *
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    private String templateId;

    /**
     * 问题
     *
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    private String question;

    /**
     * 提示
     *
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    private String hint;

    /**
     * 是否必传
     *
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    private Boolean required;

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements_template_question.id
     *
     * @return the value of supply_chain..mini_program_scene_requirements_template_question.id
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public MiniProgramSceneRequirementsTemplateQuestion withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements_template_question.id
     *
     * @param id the value for supply_chain..mini_program_scene_requirements_template_question.id
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements_template_question.template_id
     *
     * @return the value of supply_chain..mini_program_scene_requirements_template_question.template_id
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public String getTemplateId() {
        return templateId;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public MiniProgramSceneRequirementsTemplateQuestion withTemplateId(String templateId) {
        this.setTemplateId(templateId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements_template_question.template_id
     *
     * @param templateId the value for supply_chain..mini_program_scene_requirements_template_question.template_id
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements_template_question.question
     *
     * @return the value of supply_chain..mini_program_scene_requirements_template_question.question
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public String getQuestion() {
        return question;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public MiniProgramSceneRequirementsTemplateQuestion withQuestion(String question) {
        this.setQuestion(question);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements_template_question.question
     *
     * @param question the value for supply_chain..mini_program_scene_requirements_template_question.question
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public void setQuestion(String question) {
        this.question = question;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements_template_question.hint
     *
     * @return the value of supply_chain..mini_program_scene_requirements_template_question.hint
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public String getHint() {
        return hint;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public MiniProgramSceneRequirementsTemplateQuestion withHint(String hint) {
        this.setHint(hint);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements_template_question.hint
     *
     * @param hint the value for supply_chain..mini_program_scene_requirements_template_question.hint
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public void setHint(String hint) {
        this.hint = hint;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements_template_question.required
     *
     * @return the value of supply_chain..mini_program_scene_requirements_template_question.required
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public Boolean getRequired() {
        return required;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public MiniProgramSceneRequirementsTemplateQuestion withRequired(Boolean required) {
        this.setRequired(required);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements_template_question.required
     *
     * @param required the value for supply_chain..mini_program_scene_requirements_template_question.required
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public void setRequired(Boolean required) {
        this.required = required;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", templateId=").append(templateId);
        sb.append(", question=").append(question);
        sb.append(", hint=").append(hint);
        sb.append(", required=").append(required);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramSceneRequirementsTemplateQuestion other = (MiniProgramSceneRequirementsTemplateQuestion) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTemplateId() == null ? other.getTemplateId() == null : this.getTemplateId().equals(other.getTemplateId()))
            && (this.getQuestion() == null ? other.getQuestion() == null : this.getQuestion().equals(other.getQuestion()))
            && (this.getHint() == null ? other.getHint() == null : this.getHint().equals(other.getHint()))
            && (this.getRequired() == null ? other.getRequired() == null : this.getRequired().equals(other.getRequired()));
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTemplateId() == null) ? 0 : getTemplateId().hashCode());
        result = prime * result + ((getQuestion() == null) ? 0 : getQuestion().hashCode());
        result = prime * result + ((getHint() == null) ? 0 : getHint().hashCode());
        result = prime * result + ((getRequired() == null) ? 0 : getRequired().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        templateId("template_id", "templateId", "VARCHAR", false),
        question("question", "question", "VARCHAR", false),
        hint("hint", "hint", "VARCHAR", false),
        required("required", "required", "BIT", false);

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}