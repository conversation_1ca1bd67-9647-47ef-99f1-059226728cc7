package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 小程序场景需求-问答填报
 *
 * <AUTHOR>
public class MiniProgramSceneRequirementsAnswer implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    private String id;

    /**
     * 需求id
     *
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    private String requirementId;

    /**
     * 问题id
     *
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    private String questionId;

    /**
     * 答案
     *
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    private String answer;

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements_answer.id
     *
     * @return the value of supply_chain..mini_program_scene_requirements_answer.id
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public MiniProgramSceneRequirementsAnswer withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements_answer.id
     *
     * @param id the value for supply_chain..mini_program_scene_requirements_answer.id
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements_answer.requirement_id
     *
     * @return the value of supply_chain..mini_program_scene_requirements_answer.requirement_id
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public String getRequirementId() {
        return requirementId;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public MiniProgramSceneRequirementsAnswer withRequirementId(String requirementId) {
        this.setRequirementId(requirementId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements_answer.requirement_id
     *
     * @param requirementId the value for supply_chain..mini_program_scene_requirements_answer.requirement_id
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public void setRequirementId(String requirementId) {
        this.requirementId = requirementId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements_answer.question_id
     *
     * @return the value of supply_chain..mini_program_scene_requirements_answer.question_id
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public String getQuestionId() {
        return questionId;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public MiniProgramSceneRequirementsAnswer withQuestionId(String questionId) {
        this.setQuestionId(questionId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements_answer.question_id
     *
     * @param questionId the value for supply_chain..mini_program_scene_requirements_answer.question_id
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements_answer.answer
     *
     * @return the value of supply_chain..mini_program_scene_requirements_answer.answer
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public String getAnswer() {
        return answer;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public MiniProgramSceneRequirementsAnswer withAnswer(String answer) {
        this.setAnswer(answer);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements_answer.answer
     *
     * @param answer the value for supply_chain..mini_program_scene_requirements_answer.answer
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public void setAnswer(String answer) {
        this.answer = answer;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", requirementId=").append(requirementId);
        sb.append(", questionId=").append(questionId);
        sb.append(", answer=").append(answer);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramSceneRequirementsAnswer other = (MiniProgramSceneRequirementsAnswer) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getRequirementId() == null ? other.getRequirementId() == null : this.getRequirementId().equals(other.getRequirementId()))
            && (this.getQuestionId() == null ? other.getQuestionId() == null : this.getQuestionId().equals(other.getQuestionId()))
            && (this.getAnswer() == null ? other.getAnswer() == null : this.getAnswer().equals(other.getAnswer()));
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getRequirementId() == null) ? 0 : getRequirementId().hashCode());
        result = prime * result + ((getQuestionId() == null) ? 0 : getQuestionId().hashCode());
        result = prime * result + ((getAnswer() == null) ? 0 : getAnswer().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:41 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        requirementId("requirement_id", "requirementId", "VARCHAR", false),
        questionId("question_id", "questionId", "VARCHAR", false),
        answer("answer", "answer", "VARCHAR", false);

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:41 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}