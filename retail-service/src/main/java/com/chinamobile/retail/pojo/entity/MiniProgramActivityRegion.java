package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 小程序活动包含区域
 *
 * <AUTHOR>
public class MiniProgramActivityRegion implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    private String id;

    /**
     * 小程序活动id
     *
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    private String activityId;

    /**
     * 省份编码
     *
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    private String provinceCode;

    /**
     * 城市编码
     *
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    private String cityCode;

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_region.id
     *
     * @return the value of supply_chain..mini_program_activity_region.id
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    public MiniProgramActivityRegion withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_region.id
     *
     * @param id the value for supply_chain..mini_program_activity_region.id
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_region.activity_id
     *
     * @return the value of supply_chain..mini_program_activity_region.activity_id
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    public String getActivityId() {
        return activityId;
    }

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    public MiniProgramActivityRegion withActivityId(String activityId) {
        this.setActivityId(activityId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_region.activity_id
     *
     * @param activityId the value for supply_chain..mini_program_activity_region.activity_id
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_region.province_code
     *
     * @return the value of supply_chain..mini_program_activity_region.province_code
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    public MiniProgramActivityRegion withProvinceCode(String provinceCode) {
        this.setProvinceCode(provinceCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_region.province_code
     *
     * @param provinceCode the value for supply_chain..mini_program_activity_region.province_code
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_region.city_code
     *
     * @return the value of supply_chain..mini_program_activity_region.city_code
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    public MiniProgramActivityRegion withCityCode(String cityCode) {
        this.setCityCode(cityCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_region.city_code
     *
     * @param cityCode the value for supply_chain..mini_program_activity_region.city_code
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", activityId=").append(activityId);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", cityCode=").append(cityCode);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramActivityRegion other = (MiniProgramActivityRegion) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getActivityId() == null ? other.getActivityId() == null : this.getActivityId().equals(other.getActivityId()))
            && (this.getProvinceCode() == null ? other.getProvinceCode() == null : this.getProvinceCode().equals(other.getProvinceCode()))
            && (this.getCityCode() == null ? other.getCityCode() == null : this.getCityCode().equals(other.getCityCode()));
    }

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getActivityId() == null) ? 0 : getActivityId().hashCode());
        result = prime * result + ((getProvinceCode() == null) ? 0 : getProvinceCode().hashCode());
        result = prime * result + ((getCityCode() == null) ? 0 : getCityCode().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Jul 15 16:50:16 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        activityId("activity_id", "activityId", "VARCHAR", false),
        provinceCode("province_code", "provinceCode", "VARCHAR", false),
        cityCode("city_code", "cityCode", "VARCHAR", false);

        /**
         *
         * @mbg.generated Mon Jul 15 16:50:16 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Jul 15 16:50:16 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Jul 15 16:50:16 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Jul 15 16:50:16 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Jul 15 16:50:16 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Jul 15 16:50:16 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Jul 15 16:50:16 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Jul 15 16:50:16 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Jul 15 16:50:16 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Jul 15 16:50:16 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Jul 15 16:50:16 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Jul 15 16:50:16 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Jul 15 16:50:16 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Jul 15 16:50:16 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Jul 15 16:50:16 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Jul 15 16:50:16 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Jul 15 16:50:16 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}