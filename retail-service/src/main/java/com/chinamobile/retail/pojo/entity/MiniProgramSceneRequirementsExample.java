package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MiniProgramSceneRequirementsExample {
    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirementsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirementsExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirementsExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public static Criteria newAndCreateCriteria() {
        MiniProgramSceneRequirementsExample example = new MiniProgramSceneRequirementsExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirementsExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirementsExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public MiniProgramSceneRequirementsExample distinct(boolean distinct) {
        this.setDistinct(distinct);
        return this;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSceneIdIsNull() {
            addCriterion("scene_id is null");
            return (Criteria) this;
        }

        public Criteria andSceneIdIsNotNull() {
            addCriterion("scene_id is not null");
            return (Criteria) this;
        }

        public Criteria andSceneIdEqualTo(String value) {
            addCriterion("scene_id =", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("scene_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSceneIdNotEqualTo(String value) {
            addCriterion("scene_id <>", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdNotEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("scene_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSceneIdGreaterThan(String value) {
            addCriterion("scene_id >", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdGreaterThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("scene_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSceneIdGreaterThanOrEqualTo(String value) {
            addCriterion("scene_id >=", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdGreaterThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("scene_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSceneIdLessThan(String value) {
            addCriterion("scene_id <", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdLessThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("scene_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSceneIdLessThanOrEqualTo(String value) {
            addCriterion("scene_id <=", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdLessThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("scene_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSceneIdLike(String value) {
            addCriterion("scene_id like", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdNotLike(String value) {
            addCriterion("scene_id not like", value, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdIn(List<String> values) {
            addCriterion("scene_id in", values, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdNotIn(List<String> values) {
            addCriterion("scene_id not in", values, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdBetween(String value1, String value2) {
            addCriterion("scene_id between", value1, value2, "sceneId");
            return (Criteria) this;
        }

        public Criteria andSceneIdNotBetween(String value1, String value2) {
            addCriterion("scene_id not between", value1, value2, "sceneId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdIsNull() {
            addCriterion("first_directory_id is null");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdIsNotNull() {
            addCriterion("first_directory_id is not null");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdEqualTo(String value) {
            addCriterion("first_directory_id =", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("first_directory_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdNotEqualTo(String value) {
            addCriterion("first_directory_id <>", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdNotEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("first_directory_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdGreaterThan(String value) {
            addCriterion("first_directory_id >", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdGreaterThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("first_directory_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdGreaterThanOrEqualTo(String value) {
            addCriterion("first_directory_id >=", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdGreaterThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("first_directory_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdLessThan(String value) {
            addCriterion("first_directory_id <", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdLessThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("first_directory_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdLessThanOrEqualTo(String value) {
            addCriterion("first_directory_id <=", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdLessThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("first_directory_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdLike(String value) {
            addCriterion("first_directory_id like", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdNotLike(String value) {
            addCriterion("first_directory_id not like", value, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdIn(List<String> values) {
            addCriterion("first_directory_id in", values, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdNotIn(List<String> values) {
            addCriterion("first_directory_id not in", values, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdBetween(String value1, String value2) {
            addCriterion("first_directory_id between", value1, value2, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdNotBetween(String value1, String value2) {
            addCriterion("first_directory_id not between", value1, value2, "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdIsNull() {
            addCriterion("second_directory_id is null");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdIsNotNull() {
            addCriterion("second_directory_id is not null");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdEqualTo(String value) {
            addCriterion("second_directory_id =", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("second_directory_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdNotEqualTo(String value) {
            addCriterion("second_directory_id <>", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdNotEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("second_directory_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdGreaterThan(String value) {
            addCriterion("second_directory_id >", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdGreaterThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("second_directory_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdGreaterThanOrEqualTo(String value) {
            addCriterion("second_directory_id >=", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdGreaterThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("second_directory_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdLessThan(String value) {
            addCriterion("second_directory_id <", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdLessThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("second_directory_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdLessThanOrEqualTo(String value) {
            addCriterion("second_directory_id <=", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdLessThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("second_directory_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdLike(String value) {
            addCriterion("second_directory_id like", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdNotLike(String value) {
            addCriterion("second_directory_id not like", value, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdIn(List<String> values) {
            addCriterion("second_directory_id in", values, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdNotIn(List<String> values) {
            addCriterion("second_directory_id not in", values, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdBetween(String value1, String value2) {
            addCriterion("second_directory_id between", value1, value2, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdNotBetween(String value1, String value2) {
            addCriterion("second_directory_id not between", value1, value2, "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlIsNull() {
            addCriterion("attachment_file_url is null");
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlIsNotNull() {
            addCriterion("attachment_file_url is not null");
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlEqualTo(String value) {
            addCriterion("attachment_file_url =", value, "attachmentFileUrl");
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("attachment_file_url = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlNotEqualTo(String value) {
            addCriterion("attachment_file_url <>", value, "attachmentFileUrl");
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlNotEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("attachment_file_url <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlGreaterThan(String value) {
            addCriterion("attachment_file_url >", value, "attachmentFileUrl");
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlGreaterThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("attachment_file_url > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlGreaterThanOrEqualTo(String value) {
            addCriterion("attachment_file_url >=", value, "attachmentFileUrl");
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlGreaterThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("attachment_file_url >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlLessThan(String value) {
            addCriterion("attachment_file_url <", value, "attachmentFileUrl");
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlLessThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("attachment_file_url < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlLessThanOrEqualTo(String value) {
            addCriterion("attachment_file_url <=", value, "attachmentFileUrl");
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlLessThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("attachment_file_url <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlLike(String value) {
            addCriterion("attachment_file_url like", value, "attachmentFileUrl");
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlNotLike(String value) {
            addCriterion("attachment_file_url not like", value, "attachmentFileUrl");
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlIn(List<String> values) {
            addCriterion("attachment_file_url in", values, "attachmentFileUrl");
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlNotIn(List<String> values) {
            addCriterion("attachment_file_url not in", values, "attachmentFileUrl");
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlBetween(String value1, String value2) {
            addCriterion("attachment_file_url between", value1, value2, "attachmentFileUrl");
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlNotBetween(String value1, String value2) {
            addCriterion("attachment_file_url not between", value1, value2, "attachmentFileUrl");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIsNull() {
            addCriterion("province_name is null");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIsNotNull() {
            addCriterion("province_name is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceNameEqualTo(String value) {
            addCriterion("province_name =", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("province_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotEqualTo(String value) {
            addCriterion("province_name <>", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("province_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThan(String value) {
            addCriterion("province_name >", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("province_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanOrEqualTo(String value) {
            addCriterion("province_name >=", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("province_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThan(String value) {
            addCriterion("province_name <", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("province_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanOrEqualTo(String value) {
            addCriterion("province_name <=", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("province_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLike(String value) {
            addCriterion("province_name like", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotLike(String value) {
            addCriterion("province_name not like", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIn(List<String> values) {
            addCriterion("province_name in", values, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotIn(List<String> values) {
            addCriterion("province_name not in", values, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameBetween(String value1, String value2) {
            addCriterion("province_name between", value1, value2, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotBetween(String value1, String value2) {
            addCriterion("province_name not between", value1, value2, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNull() {
            addCriterion("province_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIsNotNull() {
            addCriterion("province_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualTo(String value) {
            addCriterion("province_code =", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("province_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualTo(String value) {
            addCriterion("province_code <>", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("province_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThan(String value) {
            addCriterion("province_code >", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("province_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_code >=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeGreaterThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("province_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThan(String value) {
            addCriterion("province_code <", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("province_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("province_code <=", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLessThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("province_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLike(String value) {
            addCriterion("province_code like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotLike(String value) {
            addCriterion("province_code not like", value, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeIn(List<String> values) {
            addCriterion("province_code in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotIn(List<String> values) {
            addCriterion("province_code not in", values, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeBetween(String value1, String value2) {
            addCriterion("province_code between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("province_code not between", value1, value2, "provinceCode");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNull() {
            addCriterion("city_name is null");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNotNull() {
            addCriterion("city_name is not null");
            return (Criteria) this;
        }

        public Criteria andCityNameEqualTo(String value) {
            addCriterion("city_name =", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("city_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameNotEqualTo(String value) {
            addCriterion("city_name <>", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("city_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThan(String value) {
            addCriterion("city_name >", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("city_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("city_name >=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("city_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLessThan(String value) {
            addCriterion("city_name <", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("city_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanOrEqualTo(String value) {
            addCriterion("city_name <=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("city_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLike(String value) {
            addCriterion("city_name like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotLike(String value) {
            addCriterion("city_name not like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameIn(List<String> values) {
            addCriterion("city_name in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotIn(List<String> values) {
            addCriterion("city_name not in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameBetween(String value1, String value2) {
            addCriterion("city_name between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotBetween(String value1, String value2) {
            addCriterion("city_name not between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNull() {
            addCriterion("city_code is null");
            return (Criteria) this;
        }

        public Criteria andCityCodeIsNotNull() {
            addCriterion("city_code is not null");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualTo(String value) {
            addCriterion("city_code =", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("city_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualTo(String value) {
            addCriterion("city_code <>", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("city_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThan(String value) {
            addCriterion("city_code >", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("city_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("city_code >=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeGreaterThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("city_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThan(String value) {
            addCriterion("city_code <", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("city_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualTo(String value) {
            addCriterion("city_code <=", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeLessThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("city_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityCodeLike(String value) {
            addCriterion("city_code like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotLike(String value) {
            addCriterion("city_code not like", value, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeIn(List<String> values) {
            addCriterion("city_code in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotIn(List<String> values) {
            addCriterion("city_code not in", values, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeBetween(String value1, String value2) {
            addCriterion("city_code between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andCityCodeNotBetween(String value1, String value2) {
            addCriterion("city_code not between", value1, value2, "cityCode");
            return (Criteria) this;
        }

        public Criteria andContactIsNull() {
            addCriterion("contact is null");
            return (Criteria) this;
        }

        public Criteria andContactIsNotNull() {
            addCriterion("contact is not null");
            return (Criteria) this;
        }

        public Criteria andContactEqualTo(String value) {
            addCriterion("contact =", value, "contact");
            return (Criteria) this;
        }

        public Criteria andContactEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("contact = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContactNotEqualTo(String value) {
            addCriterion("contact <>", value, "contact");
            return (Criteria) this;
        }

        public Criteria andContactNotEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("contact <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContactGreaterThan(String value) {
            addCriterion("contact >", value, "contact");
            return (Criteria) this;
        }

        public Criteria andContactGreaterThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("contact > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContactGreaterThanOrEqualTo(String value) {
            addCriterion("contact >=", value, "contact");
            return (Criteria) this;
        }

        public Criteria andContactGreaterThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("contact >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContactLessThan(String value) {
            addCriterion("contact <", value, "contact");
            return (Criteria) this;
        }

        public Criteria andContactLessThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("contact < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContactLessThanOrEqualTo(String value) {
            addCriterion("contact <=", value, "contact");
            return (Criteria) this;
        }

        public Criteria andContactLessThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("contact <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContactLike(String value) {
            addCriterion("contact like", value, "contact");
            return (Criteria) this;
        }

        public Criteria andContactNotLike(String value) {
            addCriterion("contact not like", value, "contact");
            return (Criteria) this;
        }

        public Criteria andContactIn(List<String> values) {
            addCriterion("contact in", values, "contact");
            return (Criteria) this;
        }

        public Criteria andContactNotIn(List<String> values) {
            addCriterion("contact not in", values, "contact");
            return (Criteria) this;
        }

        public Criteria andContactBetween(String value1, String value2) {
            addCriterion("contact between", value1, value2, "contact");
            return (Criteria) this;
        }

        public Criteria andContactNotBetween(String value1, String value2) {
            addCriterion("contact not between", value1, value2, "contact");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNull() {
            addCriterion("phone is null");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNotNull() {
            addCriterion("phone is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneEqualTo(String value) {
            addCriterion("phone =", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhoneNotEqualTo(String value) {
            addCriterion("phone <>", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThan(String value) {
            addCriterion("phone >", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("phone >=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhoneLessThan(String value) {
            addCriterion("phone <", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanOrEqualTo(String value) {
            addCriterion("phone <=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhoneLike(String value) {
            addCriterion("phone like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotLike(String value) {
            addCriterion("phone not like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneIn(List<String> values) {
            addCriterion("phone in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotIn(List<String> values) {
            addCriterion("phone not in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneBetween(String value1, String value2) {
            addCriterion("phone between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotBetween(String value1, String value2) {
            addCriterion("phone not between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andCreateUidIsNull() {
            addCriterion("create_uid is null");
            return (Criteria) this;
        }

        public Criteria andCreateUidIsNotNull() {
            addCriterion("create_uid is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUidEqualTo(String value) {
            addCriterion("create_uid =", value, "createUid");
            return (Criteria) this;
        }

        public Criteria andCreateUidEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("create_uid = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateUidNotEqualTo(String value) {
            addCriterion("create_uid <>", value, "createUid");
            return (Criteria) this;
        }

        public Criteria andCreateUidNotEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("create_uid <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateUidGreaterThan(String value) {
            addCriterion("create_uid >", value, "createUid");
            return (Criteria) this;
        }

        public Criteria andCreateUidGreaterThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("create_uid > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateUidGreaterThanOrEqualTo(String value) {
            addCriterion("create_uid >=", value, "createUid");
            return (Criteria) this;
        }

        public Criteria andCreateUidGreaterThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("create_uid >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateUidLessThan(String value) {
            addCriterion("create_uid <", value, "createUid");
            return (Criteria) this;
        }

        public Criteria andCreateUidLessThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("create_uid < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateUidLessThanOrEqualTo(String value) {
            addCriterion("create_uid <=", value, "createUid");
            return (Criteria) this;
        }

        public Criteria andCreateUidLessThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("create_uid <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateUidLike(String value) {
            addCriterion("create_uid like", value, "createUid");
            return (Criteria) this;
        }

        public Criteria andCreateUidNotLike(String value) {
            addCriterion("create_uid not like", value, "createUid");
            return (Criteria) this;
        }

        public Criteria andCreateUidIn(List<String> values) {
            addCriterion("create_uid in", values, "createUid");
            return (Criteria) this;
        }

        public Criteria andCreateUidNotIn(List<String> values) {
            addCriterion("create_uid not in", values, "createUid");
            return (Criteria) this;
        }

        public Criteria andCreateUidBetween(String value1, String value2) {
            addCriterion("create_uid between", value1, value2, "createUid");
            return (Criteria) this;
        }

        public Criteria andCreateUidNotBetween(String value1, String value2) {
            addCriterion("create_uid not between", value1, value2, "createUid");
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdIsNull() {
            addCriterion("partner_business_id is null");
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdIsNotNull() {
            addCriterion("partner_business_id is not null");
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdEqualTo(String value) {
            addCriterion("partner_business_id =", value, "partnerBusinessId");
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("partner_business_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdNotEqualTo(String value) {
            addCriterion("partner_business_id <>", value, "partnerBusinessId");
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdNotEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("partner_business_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdGreaterThan(String value) {
            addCriterion("partner_business_id >", value, "partnerBusinessId");
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdGreaterThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("partner_business_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdGreaterThanOrEqualTo(String value) {
            addCriterion("partner_business_id >=", value, "partnerBusinessId");
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdGreaterThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("partner_business_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdLessThan(String value) {
            addCriterion("partner_business_id <", value, "partnerBusinessId");
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdLessThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("partner_business_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdLessThanOrEqualTo(String value) {
            addCriterion("partner_business_id <=", value, "partnerBusinessId");
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdLessThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("partner_business_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdLike(String value) {
            addCriterion("partner_business_id like", value, "partnerBusinessId");
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdNotLike(String value) {
            addCriterion("partner_business_id not like", value, "partnerBusinessId");
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdIn(List<String> values) {
            addCriterion("partner_business_id in", values, "partnerBusinessId");
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdNotIn(List<String> values) {
            addCriterion("partner_business_id not in", values, "partnerBusinessId");
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdBetween(String value1, String value2) {
            addCriterion("partner_business_id between", value1, value2, "partnerBusinessId");
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdNotBetween(String value1, String value2) {
            addCriterion("partner_business_id not between", value1, value2, "partnerBusinessId");
            return (Criteria) this;
        }

        public Criteria andAuditStateIsNull() {
            addCriterion("audit_state is null");
            return (Criteria) this;
        }

        public Criteria andAuditStateIsNotNull() {
            addCriterion("audit_state is not null");
            return (Criteria) this;
        }

        public Criteria andAuditStateEqualTo(Integer value) {
            addCriterion("audit_state =", value, "auditState");
            return (Criteria) this;
        }

        public Criteria andAuditStateEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("audit_state = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAuditStateNotEqualTo(Integer value) {
            addCriterion("audit_state <>", value, "auditState");
            return (Criteria) this;
        }

        public Criteria andAuditStateNotEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("audit_state <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAuditStateGreaterThan(Integer value) {
            addCriterion("audit_state >", value, "auditState");
            return (Criteria) this;
        }

        public Criteria andAuditStateGreaterThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("audit_state > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAuditStateGreaterThanOrEqualTo(Integer value) {
            addCriterion("audit_state >=", value, "auditState");
            return (Criteria) this;
        }

        public Criteria andAuditStateGreaterThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("audit_state >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAuditStateLessThan(Integer value) {
            addCriterion("audit_state <", value, "auditState");
            return (Criteria) this;
        }

        public Criteria andAuditStateLessThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("audit_state < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAuditStateLessThanOrEqualTo(Integer value) {
            addCriterion("audit_state <=", value, "auditState");
            return (Criteria) this;
        }

        public Criteria andAuditStateLessThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("audit_state <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAuditStateIn(List<Integer> values) {
            addCriterion("audit_state in", values, "auditState");
            return (Criteria) this;
        }

        public Criteria andAuditStateNotIn(List<Integer> values) {
            addCriterion("audit_state not in", values, "auditState");
            return (Criteria) this;
        }

        public Criteria andAuditStateBetween(Integer value1, Integer value2) {
            addCriterion("audit_state between", value1, value2, "auditState");
            return (Criteria) this;
        }

        public Criteria andAuditStateNotBetween(Integer value1, Integer value2) {
            addCriterion("audit_state not between", value1, value2, "auditState");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("deleted = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("deleted <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("deleted > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("deleted >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("deleted < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("deleted <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(MiniProgramSceneRequirements.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andSceneIdLikeInsensitive(String value) {
            addCriterion("upper(scene_id) like", value.toUpperCase(), "sceneId");
            return (Criteria) this;
        }

        public Criteria andFirstDirectoryIdLikeInsensitive(String value) {
            addCriterion("upper(first_directory_id) like", value.toUpperCase(), "firstDirectoryId");
            return (Criteria) this;
        }

        public Criteria andSecondDirectoryIdLikeInsensitive(String value) {
            addCriterion("upper(second_directory_id) like", value.toUpperCase(), "secondDirectoryId");
            return (Criteria) this;
        }

        public Criteria andAttachmentFileUrlLikeInsensitive(String value) {
            addCriterion("upper(attachment_file_url) like", value.toUpperCase(), "attachmentFileUrl");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLikeInsensitive(String value) {
            addCriterion("upper(province_name) like", value.toUpperCase(), "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceCodeLikeInsensitive(String value) {
            addCriterion("upper(province_code) like", value.toUpperCase(), "provinceCode");
            return (Criteria) this;
        }

        public Criteria andCityNameLikeInsensitive(String value) {
            addCriterion("upper(city_name) like", value.toUpperCase(), "cityName");
            return (Criteria) this;
        }

        public Criteria andCityCodeLikeInsensitive(String value) {
            addCriterion("upper(city_code) like", value.toUpperCase(), "cityCode");
            return (Criteria) this;
        }

        public Criteria andContactLikeInsensitive(String value) {
            addCriterion("upper(contact) like", value.toUpperCase(), "contact");
            return (Criteria) this;
        }

        public Criteria andPhoneLikeInsensitive(String value) {
            addCriterion("upper(phone) like", value.toUpperCase(), "phone");
            return (Criteria) this;
        }

        public Criteria andCreateUidLikeInsensitive(String value) {
            addCriterion("upper(create_uid) like", value.toUpperCase(), "createUid");
            return (Criteria) this;
        }

        public Criteria andPartnerBusinessIdLikeInsensitive(String value) {
            addCriterion("upper(partner_business_id) like", value.toUpperCase(), "partnerBusinessId");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        private MiniProgramSceneRequirementsExample example;

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        protected Criteria(MiniProgramSceneRequirementsExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        public MiniProgramSceneRequirementsExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Mon Dec 16 16:23:16 GMT+08:00 2024
         */
        void example(com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirementsExample example);
    }
}