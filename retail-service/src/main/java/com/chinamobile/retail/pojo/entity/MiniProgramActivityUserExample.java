package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.List;

public class MiniProgramActivityUserExample {
    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    public MiniProgramActivityUserExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    public MiniProgramActivityUserExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    public MiniProgramActivityUserExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        MiniProgramActivityUserExample example = new MiniProgramActivityUserExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    public MiniProgramActivityUserExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    public MiniProgramActivityUserExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andActivityIdIsNull() {
            addCriterion("activity_id is null");
            return (Criteria) this;
        }

        public Criteria andActivityIdIsNotNull() {
            addCriterion("activity_id is not null");
            return (Criteria) this;
        }

        public Criteria andActivityIdEqualTo(String value) {
            addCriterion("activity_id =", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("activity_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityIdNotEqualTo(String value) {
            addCriterion("activity_id <>", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("activity_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThan(String value) {
            addCriterion("activity_id >", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThanColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("activity_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThanOrEqualTo(String value) {
            addCriterion("activity_id >=", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThanOrEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("activity_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThan(String value) {
            addCriterion("activity_id <", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThanColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("activity_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThanOrEqualTo(String value) {
            addCriterion("activity_id <=", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThanOrEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("activity_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityIdLike(String value) {
            addCriterion("activity_id like", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotLike(String value) {
            addCriterion("activity_id not like", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdIn(List<String> values) {
            addCriterion("activity_id in", values, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotIn(List<String> values) {
            addCriterion("activity_id not in", values, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdBetween(String value1, String value2) {
            addCriterion("activity_id between", value1, value2, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotBetween(String value1, String value2) {
            addCriterion("activity_id not between", value1, value2, "activityId");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(String value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("user_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(String value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("user_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(String value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("user_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("user_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(String value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("user_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(String value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("user_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLike(String value) {
            addCriterion("user_id like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotLike(String value) {
            addCriterion("user_id not like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<String> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<String> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(String value1, String value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(String value1, String value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andIsDrawIsNull() {
            addCriterion("is_draw is null");
            return (Criteria) this;
        }

        public Criteria andIsDrawIsNotNull() {
            addCriterion("is_draw is not null");
            return (Criteria) this;
        }

        public Criteria andIsDrawEqualTo(Integer value) {
            addCriterion("is_draw =", value, "isDraw");
            return (Criteria) this;
        }

        public Criteria andIsDrawEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("is_draw = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsDrawNotEqualTo(Integer value) {
            addCriterion("is_draw <>", value, "isDraw");
            return (Criteria) this;
        }

        public Criteria andIsDrawNotEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("is_draw <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsDrawGreaterThan(Integer value) {
            addCriterion("is_draw >", value, "isDraw");
            return (Criteria) this;
        }

        public Criteria andIsDrawGreaterThanColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("is_draw > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsDrawGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_draw >=", value, "isDraw");
            return (Criteria) this;
        }

        public Criteria andIsDrawGreaterThanOrEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("is_draw >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsDrawLessThan(Integer value) {
            addCriterion("is_draw <", value, "isDraw");
            return (Criteria) this;
        }

        public Criteria andIsDrawLessThanColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("is_draw < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsDrawLessThanOrEqualTo(Integer value) {
            addCriterion("is_draw <=", value, "isDraw");
            return (Criteria) this;
        }

        public Criteria andIsDrawLessThanOrEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("is_draw <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsDrawIn(List<Integer> values) {
            addCriterion("is_draw in", values, "isDraw");
            return (Criteria) this;
        }

        public Criteria andIsDrawNotIn(List<Integer> values) {
            addCriterion("is_draw not in", values, "isDraw");
            return (Criteria) this;
        }

        public Criteria andIsDrawBetween(Integer value1, Integer value2) {
            addCriterion("is_draw between", value1, value2, "isDraw");
            return (Criteria) this;
        }

        public Criteria andIsDrawNotBetween(Integer value1, Integer value2) {
            addCriterion("is_draw not between", value1, value2, "isDraw");
            return (Criteria) this;
        }

        public Criteria andIsAddIsNull() {
            addCriterion("is_add is null");
            return (Criteria) this;
        }

        public Criteria andIsAddIsNotNull() {
            addCriterion("is_add is not null");
            return (Criteria) this;
        }

        public Criteria andIsAddEqualTo(Integer value) {
            addCriterion("is_add =", value, "isAdd");
            return (Criteria) this;
        }

        public Criteria andIsAddEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("is_add = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsAddNotEqualTo(Integer value) {
            addCriterion("is_add <>", value, "isAdd");
            return (Criteria) this;
        }

        public Criteria andIsAddNotEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("is_add <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsAddGreaterThan(Integer value) {
            addCriterion("is_add >", value, "isAdd");
            return (Criteria) this;
        }

        public Criteria andIsAddGreaterThanColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("is_add > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsAddGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_add >=", value, "isAdd");
            return (Criteria) this;
        }

        public Criteria andIsAddGreaterThanOrEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("is_add >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsAddLessThan(Integer value) {
            addCriterion("is_add <", value, "isAdd");
            return (Criteria) this;
        }

        public Criteria andIsAddLessThanColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("is_add < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsAddLessThanOrEqualTo(Integer value) {
            addCriterion("is_add <=", value, "isAdd");
            return (Criteria) this;
        }

        public Criteria andIsAddLessThanOrEqualToColumn(MiniProgramActivityUser.Column column) {
            addCriterion(new StringBuilder("is_add <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsAddIn(List<Integer> values) {
            addCriterion("is_add in", values, "isAdd");
            return (Criteria) this;
        }

        public Criteria andIsAddNotIn(List<Integer> values) {
            addCriterion("is_add not in", values, "isAdd");
            return (Criteria) this;
        }

        public Criteria andIsAddBetween(Integer value1, Integer value2) {
            addCriterion("is_add between", value1, value2, "isAdd");
            return (Criteria) this;
        }

        public Criteria andIsAddNotBetween(Integer value1, Integer value2) {
            addCriterion("is_add not between", value1, value2, "isAdd");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andActivityIdLikeInsensitive(String value) {
            addCriterion("upper(activity_id) like", value.toUpperCase(), "activityId");
            return (Criteria) this;
        }

        public Criteria andUserIdLikeInsensitive(String value) {
            addCriterion("upper(user_id) like", value.toUpperCase(), "userId");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Tue Sep 10 16:51:19 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Tue Sep 10 16:51:19 CST 2024
         */
        private MiniProgramActivityUserExample example;

        /**
         *
         * @mbg.generated Tue Sep 10 16:51:19 CST 2024
         */
        protected Criteria(MiniProgramActivityUserExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Tue Sep 10 16:51:19 CST 2024
         */
        public MiniProgramActivityUserExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Tue Sep 10 16:51:19 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Sep 10 16:51:19 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Sep 10 16:51:19 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Tue Sep 10 16:51:19 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Tue Sep 10 16:51:19 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Tue Sep 10 16:51:19 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Tue Sep 10 16:51:19 CST 2024
         */
        void example(com.chinamobile.retail.pojo.entity.MiniProgramActivityUserExample example);
    }
}