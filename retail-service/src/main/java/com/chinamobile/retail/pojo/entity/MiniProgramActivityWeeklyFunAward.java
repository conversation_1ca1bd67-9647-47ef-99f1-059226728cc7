package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 小程序周周乐活动奖品
 *
 * <AUTHOR>
public class MiniProgramActivityWeeklyFunAward implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    private String id;

    /**
     * 小程序活动id
     *
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    private String activityId;

    /**
     * 奖品类型 1-积分，2-产品
     *
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    private Integer type;

    /**
     * 积分额度
     *
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    private Long points;

    /**
     * 产品名称
     *
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    private String product;

    /**
     * 产品图片url
     *
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    private String productImg;

    /**
     * 积分供应商id
     *
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    private String supplierId;

    /**
     * 中奖概率
     *
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    private Integer probability;

    /**
     * 奖项，如“一等奖”
     *
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    private String awardName;

    /**
     * 本次活动中的最多中奖次数
     *
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    private Integer maxAwards;

    /**
     * 每日最多中奖次数
     *
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    private Integer maxAwardsDaily;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun_award.id
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun_award.id
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public MiniProgramActivityWeeklyFunAward withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun_award.id
     *
     * @param id the value for supply_chain..mini_program_activity_weekly_fun_award.id
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun_award.activity_id
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun_award.activity_id
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public String getActivityId() {
        return activityId;
    }

    /**
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public MiniProgramActivityWeeklyFunAward withActivityId(String activityId) {
        this.setActivityId(activityId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun_award.activity_id
     *
     * @param activityId the value for supply_chain..mini_program_activity_weekly_fun_award.activity_id
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun_award.type
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun_award.type
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public Integer getType() {
        return type;
    }

    /**
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public MiniProgramActivityWeeklyFunAward withType(Integer type) {
        this.setType(type);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun_award.type
     *
     * @param type the value for supply_chain..mini_program_activity_weekly_fun_award.type
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun_award.points
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun_award.points
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public Long getPoints() {
        return points;
    }

    /**
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public MiniProgramActivityWeeklyFunAward withPoints(Long points) {
        this.setPoints(points);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun_award.points
     *
     * @param points the value for supply_chain..mini_program_activity_weekly_fun_award.points
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public void setPoints(Long points) {
        this.points = points;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun_award.product
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun_award.product
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public String getProduct() {
        return product;
    }

    /**
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public MiniProgramActivityWeeklyFunAward withProduct(String product) {
        this.setProduct(product);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun_award.product
     *
     * @param product the value for supply_chain..mini_program_activity_weekly_fun_award.product
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public void setProduct(String product) {
        this.product = product;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun_award.product_img
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun_award.product_img
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public String getProductImg() {
        return productImg;
    }

    /**
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public MiniProgramActivityWeeklyFunAward withProductImg(String productImg) {
        this.setProductImg(productImg);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun_award.product_img
     *
     * @param productImg the value for supply_chain..mini_program_activity_weekly_fun_award.product_img
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public void setProductImg(String productImg) {
        this.productImg = productImg;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun_award.supplier_id
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun_award.supplier_id
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public String getSupplierId() {
        return supplierId;
    }

    /**
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public MiniProgramActivityWeeklyFunAward withSupplierId(String supplierId) {
        this.setSupplierId(supplierId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun_award.supplier_id
     *
     * @param supplierId the value for supply_chain..mini_program_activity_weekly_fun_award.supplier_id
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun_award.probability
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun_award.probability
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public Integer getProbability() {
        return probability;
    }

    /**
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public MiniProgramActivityWeeklyFunAward withProbability(Integer probability) {
        this.setProbability(probability);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun_award.probability
     *
     * @param probability the value for supply_chain..mini_program_activity_weekly_fun_award.probability
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public void setProbability(Integer probability) {
        this.probability = probability;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun_award.award_name
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun_award.award_name
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public String getAwardName() {
        return awardName;
    }

    /**
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public MiniProgramActivityWeeklyFunAward withAwardName(String awardName) {
        this.setAwardName(awardName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun_award.award_name
     *
     * @param awardName the value for supply_chain..mini_program_activity_weekly_fun_award.award_name
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public void setAwardName(String awardName) {
        this.awardName = awardName;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun_award.max_awards
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun_award.max_awards
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public Integer getMaxAwards() {
        return maxAwards;
    }

    /**
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public MiniProgramActivityWeeklyFunAward withMaxAwards(Integer maxAwards) {
        this.setMaxAwards(maxAwards);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun_award.max_awards
     *
     * @param maxAwards the value for supply_chain..mini_program_activity_weekly_fun_award.max_awards
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public void setMaxAwards(Integer maxAwards) {
        this.maxAwards = maxAwards;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun_award.max_awards_daily
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun_award.max_awards_daily
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public Integer getMaxAwardsDaily() {
        return maxAwardsDaily;
    }

    /**
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public MiniProgramActivityWeeklyFunAward withMaxAwardsDaily(Integer maxAwardsDaily) {
        this.setMaxAwardsDaily(maxAwardsDaily);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun_award.max_awards_daily
     *
     * @param maxAwardsDaily the value for supply_chain..mini_program_activity_weekly_fun_award.max_awards_daily
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public void setMaxAwardsDaily(Integer maxAwardsDaily) {
        this.maxAwardsDaily = maxAwardsDaily;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun_award.create_time
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun_award.create_time
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public MiniProgramActivityWeeklyFunAward withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun_award.create_time
     *
     * @param createTime the value for supply_chain..mini_program_activity_weekly_fun_award.create_time
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_activity_weekly_fun_award.update_time
     *
     * @return the value of supply_chain..mini_program_activity_weekly_fun_award.update_time
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public MiniProgramActivityWeeklyFunAward withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_activity_weekly_fun_award.update_time
     *
     * @param updateTime the value for supply_chain..mini_program_activity_weekly_fun_award.update_time
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", activityId=").append(activityId);
        sb.append(", type=").append(type);
        sb.append(", points=").append(points);
        sb.append(", product=").append(product);
        sb.append(", productImg=").append(productImg);
        sb.append(", supplierId=").append(supplierId);
        sb.append(", probability=").append(probability);
        sb.append(", awardName=").append(awardName);
        sb.append(", maxAwards=").append(maxAwards);
        sb.append(", maxAwardsDaily=").append(maxAwardsDaily);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramActivityWeeklyFunAward other = (MiniProgramActivityWeeklyFunAward) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getActivityId() == null ? other.getActivityId() == null : this.getActivityId().equals(other.getActivityId()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getPoints() == null ? other.getPoints() == null : this.getPoints().equals(other.getPoints()))
            && (this.getProduct() == null ? other.getProduct() == null : this.getProduct().equals(other.getProduct()))
            && (this.getProductImg() == null ? other.getProductImg() == null : this.getProductImg().equals(other.getProductImg()))
            && (this.getSupplierId() == null ? other.getSupplierId() == null : this.getSupplierId().equals(other.getSupplierId()))
            && (this.getProbability() == null ? other.getProbability() == null : this.getProbability().equals(other.getProbability()))
            && (this.getAwardName() == null ? other.getAwardName() == null : this.getAwardName().equals(other.getAwardName()))
            && (this.getMaxAwards() == null ? other.getMaxAwards() == null : this.getMaxAwards().equals(other.getMaxAwards()))
            && (this.getMaxAwardsDaily() == null ? other.getMaxAwardsDaily() == null : this.getMaxAwardsDaily().equals(other.getMaxAwardsDaily()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getActivityId() == null) ? 0 : getActivityId().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getPoints() == null) ? 0 : getPoints().hashCode());
        result = prime * result + ((getProduct() == null) ? 0 : getProduct().hashCode());
        result = prime * result + ((getProductImg() == null) ? 0 : getProductImg().hashCode());
        result = prime * result + ((getSupplierId() == null) ? 0 : getSupplierId().hashCode());
        result = prime * result + ((getProbability() == null) ? 0 : getProbability().hashCode());
        result = prime * result + ((getAwardName() == null) ? 0 : getAwardName().hashCode());
        result = prime * result + ((getMaxAwards() == null) ? 0 : getMaxAwards().hashCode());
        result = prime * result + ((getMaxAwardsDaily() == null) ? 0 : getMaxAwardsDaily().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Jul 16 11:34:38 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        activityId("activity_id", "activityId", "VARCHAR", false),
        type("type", "type", "INTEGER", false),
        points("points", "points", "BIGINT", false),
        product("product", "product", "VARCHAR", false),
        productImg("product_img", "productImg", "VARCHAR", false),
        supplierId("supplier_id", "supplierId", "VARCHAR", false),
        probability("probability", "probability", "INTEGER", false),
        awardName("award_name", "awardName", "VARCHAR", false),
        maxAwards("max_awards", "maxAwards", "INTEGER", false),
        maxAwardsDaily("max_awards_daily", "maxAwardsDaily", "INTEGER", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Tue Jul 16 11:34:38 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Jul 16 11:34:38 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Jul 16 11:34:38 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Jul 16 11:34:38 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Jul 16 11:34:38 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Jul 16 11:34:38 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Jul 16 11:34:38 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Jul 16 11:34:38 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Jul 16 11:34:38 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Jul 16 11:34:38 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Jul 16 11:34:38 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Jul 16 11:34:38 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Jul 16 11:34:38 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Jul 16 11:34:38 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Jul 16 11:34:38 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Jul 16 11:34:38 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Jul 16 11:34:38 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}