package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ShopCustomerInfoExample {
    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        ShopCustomerInfoExample example = new ShopCustomerInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public ShopCustomerInfoExample distinct(boolean distinct) {
        this.setDistinct(distinct);
        return this;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCustCodeIsNull() {
            addCriterion("cust_code is null");
            return (Criteria) this;
        }

        public Criteria andCustCodeIsNotNull() {
            addCriterion("cust_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustCodeEqualTo(String value) {
            addCriterion("cust_code =", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("cust_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeNotEqualTo(String value) {
            addCriterion("cust_code <>", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("cust_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThan(String value) {
            addCriterion("cust_code >", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("cust_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanOrEqualTo(String value) {
            addCriterion("cust_code >=", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("cust_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThan(String value) {
            addCriterion("cust_code <", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("cust_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanOrEqualTo(String value) {
            addCriterion("cust_code <=", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("cust_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLike(String value) {
            addCriterion("cust_code like", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotLike(String value) {
            addCriterion("cust_code not like", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeIn(List<String> values) {
            addCriterion("cust_code in", values, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotIn(List<String> values) {
            addCriterion("cust_code not in", values, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeBetween(String value1, String value2) {
            addCriterion("cust_code between", value1, value2, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotBetween(String value1, String value2) {
            addCriterion("cust_code not between", value1, value2, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustIdIsNull() {
            addCriterion("cust_id is null");
            return (Criteria) this;
        }

        public Criteria andCustIdIsNotNull() {
            addCriterion("cust_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustIdEqualTo(String value) {
            addCriterion("cust_id =", value, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("cust_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustIdNotEqualTo(String value) {
            addCriterion("cust_id <>", value, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("cust_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustIdGreaterThan(String value) {
            addCriterion("cust_id >", value, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("cust_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustIdGreaterThanOrEqualTo(String value) {
            addCriterion("cust_id >=", value, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("cust_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustIdLessThan(String value) {
            addCriterion("cust_id <", value, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("cust_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustIdLessThanOrEqualTo(String value) {
            addCriterion("cust_id <=", value, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("cust_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustIdLike(String value) {
            addCriterion("cust_id like", value, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdNotLike(String value) {
            addCriterion("cust_id not like", value, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdIn(List<String> values) {
            addCriterion("cust_id in", values, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdNotIn(List<String> values) {
            addCriterion("cust_id not in", values, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdBetween(String value1, String value2) {
            addCriterion("cust_id between", value1, value2, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdNotBetween(String value1, String value2) {
            addCriterion("cust_id not between", value1, value2, "custId");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(String value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("user_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(String value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("user_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(String value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("user_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("user_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(String value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("user_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(String value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("user_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLike(String value) {
            addCriterion("user_id like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotLike(String value) {
            addCriterion("user_id not like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<String> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<String> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(String value1, String value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(String value1, String value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andCustNameIsNull() {
            addCriterion("cust_name is null");
            return (Criteria) this;
        }

        public Criteria andCustNameIsNotNull() {
            addCriterion("cust_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustNameEqualTo(String value) {
            addCriterion("cust_name =", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("cust_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameNotEqualTo(String value) {
            addCriterion("cust_name <>", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("cust_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThan(String value) {
            addCriterion("cust_name >", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("cust_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThanOrEqualTo(String value) {
            addCriterion("cust_name >=", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("cust_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameLessThan(String value) {
            addCriterion("cust_name <", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("cust_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameLessThanOrEqualTo(String value) {
            addCriterion("cust_name <=", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("cust_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameLike(String value) {
            addCriterion("cust_name like", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotLike(String value) {
            addCriterion("cust_name not like", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameIn(List<String> values) {
            addCriterion("cust_name in", values, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotIn(List<String> values) {
            addCriterion("cust_name not in", values, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameBetween(String value1, String value2) {
            addCriterion("cust_name between", value1, value2, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotBetween(String value1, String value2) {
            addCriterion("cust_name not between", value1, value2, "custName");
            return (Criteria) this;
        }

        public Criteria andRegisterDateIsNull() {
            addCriterion("register_date is null");
            return (Criteria) this;
        }

        public Criteria andRegisterDateIsNotNull() {
            addCriterion("register_date is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterDateEqualTo(Date value) {
            addCriterion("register_date =", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("register_date = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotEqualTo(Date value) {
            addCriterion("register_date <>", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("register_date <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterDateGreaterThan(Date value) {
            addCriterion("register_date >", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("register_date > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterDateGreaterThanOrEqualTo(Date value) {
            addCriterion("register_date >=", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("register_date >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterDateLessThan(Date value) {
            addCriterion("register_date <", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("register_date < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterDateLessThanOrEqualTo(Date value) {
            addCriterion("register_date <=", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("register_date <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterDateIn(List<Date> values) {
            addCriterion("register_date in", values, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotIn(List<Date> values) {
            addCriterion("register_date not in", values, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateBetween(Date value1, Date value2) {
            addCriterion("register_date between", value1, value2, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotBetween(Date value1, Date value2) {
            addCriterion("register_date not between", value1, value2, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRoleTypeIsNull() {
            addCriterion("role_type is null");
            return (Criteria) this;
        }

        public Criteria andRoleTypeIsNotNull() {
            addCriterion("role_type is not null");
            return (Criteria) this;
        }

        public Criteria andRoleTypeEqualTo(String value) {
            addCriterion("role_type =", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("role_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRoleTypeNotEqualTo(String value) {
            addCriterion("role_type <>", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("role_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRoleTypeGreaterThan(String value) {
            addCriterion("role_type >", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("role_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRoleTypeGreaterThanOrEqualTo(String value) {
            addCriterion("role_type >=", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("role_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRoleTypeLessThan(String value) {
            addCriterion("role_type <", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("role_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRoleTypeLessThanOrEqualTo(String value) {
            addCriterion("role_type <=", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("role_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRoleTypeLike(String value) {
            addCriterion("role_type like", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeNotLike(String value) {
            addCriterion("role_type not like", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeIn(List<String> values) {
            addCriterion("role_type in", values, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeNotIn(List<String> values) {
            addCriterion("role_type not in", values, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeBetween(String value1, String value2) {
            addCriterion("role_type between", value1, value2, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeNotBetween(String value1, String value2) {
            addCriterion("role_type not between", value1, value2, "roleType");
            return (Criteria) this;
        }

        public Criteria andBeIdIsNull() {
            addCriterion("be_id is null");
            return (Criteria) this;
        }

        public Criteria andBeIdIsNotNull() {
            addCriterion("be_id is not null");
            return (Criteria) this;
        }

        public Criteria andBeIdEqualTo(String value) {
            addCriterion("be_id =", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("be_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdNotEqualTo(String value) {
            addCriterion("be_id <>", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("be_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThan(String value) {
            addCriterion("be_id >", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("be_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanOrEqualTo(String value) {
            addCriterion("be_id >=", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("be_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLessThan(String value) {
            addCriterion("be_id <", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("be_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanOrEqualTo(String value) {
            addCriterion("be_id <=", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("be_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLike(String value) {
            addCriterion("be_id like", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotLike(String value) {
            addCriterion("be_id not like", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdIn(List<String> values) {
            addCriterion("be_id in", values, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotIn(List<String> values) {
            addCriterion("be_id not in", values, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdBetween(String value1, String value2) {
            addCriterion("be_id between", value1, value2, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotBetween(String value1, String value2) {
            addCriterion("be_id not between", value1, value2, "beId");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIsNull() {
            addCriterion("province_name is null");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIsNotNull() {
            addCriterion("province_name is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceNameEqualTo(String value) {
            addCriterion("province_name =", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("province_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotEqualTo(String value) {
            addCriterion("province_name <>", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("province_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThan(String value) {
            addCriterion("province_name >", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("province_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanOrEqualTo(String value) {
            addCriterion("province_name >=", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("province_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThan(String value) {
            addCriterion("province_name <", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("province_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanOrEqualTo(String value) {
            addCriterion("province_name <=", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("province_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLike(String value) {
            addCriterion("province_name like", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotLike(String value) {
            addCriterion("province_name not like", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIn(List<String> values) {
            addCriterion("province_name in", values, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotIn(List<String> values) {
            addCriterion("province_name not in", values, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameBetween(String value1, String value2) {
            addCriterion("province_name between", value1, value2, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotBetween(String value1, String value2) {
            addCriterion("province_name not between", value1, value2, "provinceName");
            return (Criteria) this;
        }

        public Criteria andLocationIsNull() {
            addCriterion("location is null");
            return (Criteria) this;
        }

        public Criteria andLocationIsNotNull() {
            addCriterion("location is not null");
            return (Criteria) this;
        }

        public Criteria andLocationEqualTo(String value) {
            addCriterion("location =", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("location = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationNotEqualTo(String value) {
            addCriterion("location <>", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("location <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThan(String value) {
            addCriterion("location >", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("location > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThanOrEqualTo(String value) {
            addCriterion("location >=", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("location >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationLessThan(String value) {
            addCriterion("location <", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("location < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationLessThanOrEqualTo(String value) {
            addCriterion("location <=", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("location <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationLike(String value) {
            addCriterion("location like", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotLike(String value) {
            addCriterion("location not like", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationIn(List<String> values) {
            addCriterion("location in", values, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotIn(List<String> values) {
            addCriterion("location not in", values, "location");
            return (Criteria) this;
        }

        public Criteria andLocationBetween(String value1, String value2) {
            addCriterion("location between", value1, value2, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotBetween(String value1, String value2) {
            addCriterion("location not between", value1, value2, "location");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNull() {
            addCriterion("city_name is null");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNotNull() {
            addCriterion("city_name is not null");
            return (Criteria) this;
        }

        public Criteria andCityNameEqualTo(String value) {
            addCriterion("city_name =", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("city_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameNotEqualTo(String value) {
            addCriterion("city_name <>", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("city_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThan(String value) {
            addCriterion("city_name >", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("city_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("city_name >=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("city_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLessThan(String value) {
            addCriterion("city_name <", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("city_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanOrEqualTo(String value) {
            addCriterion("city_name <=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("city_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLike(String value) {
            addCriterion("city_name like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotLike(String value) {
            addCriterion("city_name not like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameIn(List<String> values) {
            addCriterion("city_name in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotIn(List<String> values) {
            addCriterion("city_name not in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameBetween(String value1, String value2) {
            addCriterion("city_name between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotBetween(String value1, String value2) {
            addCriterion("city_name not between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andRegionIdIsNull() {
            addCriterion("region_id is null");
            return (Criteria) this;
        }

        public Criteria andRegionIdIsNotNull() {
            addCriterion("region_id is not null");
            return (Criteria) this;
        }

        public Criteria andRegionIdEqualTo(String value) {
            addCriterion("region_id =", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("region_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdNotEqualTo(String value) {
            addCriterion("region_id <>", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("region_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThan(String value) {
            addCriterion("region_id >", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("region_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanOrEqualTo(String value) {
            addCriterion("region_id >=", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("region_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThan(String value) {
            addCriterion("region_id <", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("region_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanOrEqualTo(String value) {
            addCriterion("region_id <=", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("region_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLike(String value) {
            addCriterion("region_id like", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotLike(String value) {
            addCriterion("region_id not like", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdIn(List<String> values) {
            addCriterion("region_id in", values, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotIn(List<String> values) {
            addCriterion("region_id not in", values, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdBetween(String value1, String value2) {
            addCriterion("region_id between", value1, value2, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotBetween(String value1, String value2) {
            addCriterion("region_id not between", value1, value2, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionNameIsNull() {
            addCriterion("region_name is null");
            return (Criteria) this;
        }

        public Criteria andRegionNameIsNotNull() {
            addCriterion("region_name is not null");
            return (Criteria) this;
        }

        public Criteria andRegionNameEqualTo(String value) {
            addCriterion("region_name =", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("region_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameNotEqualTo(String value) {
            addCriterion("region_name <>", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("region_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThan(String value) {
            addCriterion("region_name >", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("region_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThanOrEqualTo(String value) {
            addCriterion("region_name >=", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("region_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThan(String value) {
            addCriterion("region_name <", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("region_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThanOrEqualTo(String value) {
            addCriterion("region_name <=", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("region_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameLike(String value) {
            addCriterion("region_name like", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotLike(String value) {
            addCriterion("region_name not like", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameIn(List<String> values) {
            addCriterion("region_name in", values, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotIn(List<String> values) {
            addCriterion("region_name not in", values, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameBetween(String value1, String value2) {
            addCriterion("region_name between", value1, value2, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotBetween(String value1, String value2) {
            addCriterion("region_name not between", value1, value2, "regionName");
            return (Criteria) this;
        }

        public Criteria andClientStatusIsNull() {
            addCriterion("client_status is null");
            return (Criteria) this;
        }

        public Criteria andClientStatusIsNotNull() {
            addCriterion("client_status is not null");
            return (Criteria) this;
        }

        public Criteria andClientStatusEqualTo(String value) {
            addCriterion("client_status =", value, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("client_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientStatusNotEqualTo(String value) {
            addCriterion("client_status <>", value, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("client_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientStatusGreaterThan(String value) {
            addCriterion("client_status >", value, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("client_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientStatusGreaterThanOrEqualTo(String value) {
            addCriterion("client_status >=", value, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("client_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientStatusLessThan(String value) {
            addCriterion("client_status <", value, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("client_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientStatusLessThanOrEqualTo(String value) {
            addCriterion("client_status <=", value, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("client_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientStatusLike(String value) {
            addCriterion("client_status like", value, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusNotLike(String value) {
            addCriterion("client_status not like", value, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusIn(List<String> values) {
            addCriterion("client_status in", values, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusNotIn(List<String> values) {
            addCriterion("client_status not in", values, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusBetween(String value1, String value2) {
            addCriterion("client_status between", value1, value2, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusNotBetween(String value1, String value2) {
            addCriterion("client_status not between", value1, value2, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdIsNull() {
            addCriterion("distributor_channel_id is null");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdIsNotNull() {
            addCriterion("distributor_channel_id is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdEqualTo(String value) {
            addCriterion("distributor_channel_id =", value, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_channel_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdNotEqualTo(String value) {
            addCriterion("distributor_channel_id <>", value, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_channel_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdGreaterThan(String value) {
            addCriterion("distributor_channel_id >", value, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_channel_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdGreaterThanOrEqualTo(String value) {
            addCriterion("distributor_channel_id >=", value, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_channel_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdLessThan(String value) {
            addCriterion("distributor_channel_id <", value, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_channel_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdLessThanOrEqualTo(String value) {
            addCriterion("distributor_channel_id <=", value, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_channel_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdLike(String value) {
            addCriterion("distributor_channel_id like", value, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdNotLike(String value) {
            addCriterion("distributor_channel_id not like", value, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdIn(List<String> values) {
            addCriterion("distributor_channel_id in", values, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdNotIn(List<String> values) {
            addCriterion("distributor_channel_id not in", values, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdBetween(String value1, String value2) {
            addCriterion("distributor_channel_id between", value1, value2, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdNotBetween(String value1, String value2) {
            addCriterion("distributor_channel_id not between", value1, value2, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameIsNull() {
            addCriterion("distributor_channel_name is null");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameIsNotNull() {
            addCriterion("distributor_channel_name is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameEqualTo(String value) {
            addCriterion("distributor_channel_name =", value, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_channel_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameNotEqualTo(String value) {
            addCriterion("distributor_channel_name <>", value, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_channel_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameGreaterThan(String value) {
            addCriterion("distributor_channel_name >", value, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_channel_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameGreaterThanOrEqualTo(String value) {
            addCriterion("distributor_channel_name >=", value, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_channel_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameLessThan(String value) {
            addCriterion("distributor_channel_name <", value, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_channel_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameLessThanOrEqualTo(String value) {
            addCriterion("distributor_channel_name <=", value, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_channel_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameLike(String value) {
            addCriterion("distributor_channel_name like", value, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameNotLike(String value) {
            addCriterion("distributor_channel_name not like", value, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameIn(List<String> values) {
            addCriterion("distributor_channel_name in", values, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameNotIn(List<String> values) {
            addCriterion("distributor_channel_name not in", values, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameBetween(String value1, String value2) {
            addCriterion("distributor_channel_name between", value1, value2, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameNotBetween(String value1, String value2) {
            addCriterion("distributor_channel_name not between", value1, value2, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeIsNull() {
            addCriterion("distributor_referral_code is null");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeIsNotNull() {
            addCriterion("distributor_referral_code is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeEqualTo(String value) {
            addCriterion("distributor_referral_code =", value, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_referral_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeNotEqualTo(String value) {
            addCriterion("distributor_referral_code <>", value, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_referral_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeGreaterThan(String value) {
            addCriterion("distributor_referral_code >", value, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_referral_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeGreaterThanOrEqualTo(String value) {
            addCriterion("distributor_referral_code >=", value, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_referral_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeLessThan(String value) {
            addCriterion("distributor_referral_code <", value, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_referral_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeLessThanOrEqualTo(String value) {
            addCriterion("distributor_referral_code <=", value, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_referral_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeLike(String value) {
            addCriterion("distributor_referral_code like", value, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeNotLike(String value) {
            addCriterion("distributor_referral_code not like", value, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeIn(List<String> values) {
            addCriterion("distributor_referral_code in", values, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeNotIn(List<String> values) {
            addCriterion("distributor_referral_code not in", values, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeBetween(String value1, String value2) {
            addCriterion("distributor_referral_code between", value1, value2, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeNotBetween(String value1, String value2) {
            addCriterion("distributor_referral_code not between", value1, value2, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfIsNull() {
            addCriterion("distributor_mrg_inf is null");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfIsNotNull() {
            addCriterion("distributor_mrg_inf is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfEqualTo(String value) {
            addCriterion("distributor_mrg_inf =", value, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_inf = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfNotEqualTo(String value) {
            addCriterion("distributor_mrg_inf <>", value, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_inf <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfGreaterThan(String value) {
            addCriterion("distributor_mrg_inf >", value, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_inf > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfGreaterThanOrEqualTo(String value) {
            addCriterion("distributor_mrg_inf >=", value, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_inf >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfLessThan(String value) {
            addCriterion("distributor_mrg_inf <", value, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_inf < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfLessThanOrEqualTo(String value) {
            addCriterion("distributor_mrg_inf <=", value, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_inf <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfLike(String value) {
            addCriterion("distributor_mrg_inf like", value, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfNotLike(String value) {
            addCriterion("distributor_mrg_inf not like", value, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfIn(List<String> values) {
            addCriterion("distributor_mrg_inf in", values, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfNotIn(List<String> values) {
            addCriterion("distributor_mrg_inf not in", values, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfBetween(String value1, String value2) {
            addCriterion("distributor_mrg_inf between", value1, value2, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfNotBetween(String value1, String value2) {
            addCriterion("distributor_mrg_inf not between", value1, value2, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeIsNull() {
            addCriterion("distributor_mrg_code is null");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeIsNotNull() {
            addCriterion("distributor_mrg_code is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeEqualTo(String value) {
            addCriterion("distributor_mrg_code =", value, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeNotEqualTo(String value) {
            addCriterion("distributor_mrg_code <>", value, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeGreaterThan(String value) {
            addCriterion("distributor_mrg_code >", value, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeGreaterThanOrEqualTo(String value) {
            addCriterion("distributor_mrg_code >=", value, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeLessThan(String value) {
            addCriterion("distributor_mrg_code <", value, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeLessThanOrEqualTo(String value) {
            addCriterion("distributor_mrg_code <=", value, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeLike(String value) {
            addCriterion("distributor_mrg_code like", value, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeNotLike(String value) {
            addCriterion("distributor_mrg_code not like", value, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeIn(List<String> values) {
            addCriterion("distributor_mrg_code in", values, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeNotIn(List<String> values) {
            addCriterion("distributor_mrg_code not in", values, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeBetween(String value1, String value2) {
            addCriterion("distributor_mrg_code between", value1, value2, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeNotBetween(String value1, String value2) {
            addCriterion("distributor_mrg_code not between", value1, value2, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorNameIsNull() {
            addCriterion("distributor_name is null");
            return (Criteria) this;
        }

        public Criteria andDistributorNameIsNotNull() {
            addCriterion("distributor_name is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorNameEqualTo(String value) {
            addCriterion("distributor_name =", value, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorNameNotEqualTo(String value) {
            addCriterion("distributor_name <>", value, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorNameGreaterThan(String value) {
            addCriterion("distributor_name >", value, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorNameGreaterThanOrEqualTo(String value) {
            addCriterion("distributor_name >=", value, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorNameLessThan(String value) {
            addCriterion("distributor_name <", value, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorNameLessThanOrEqualTo(String value) {
            addCriterion("distributor_name <=", value, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("distributor_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorNameLike(String value) {
            addCriterion("distributor_name like", value, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameNotLike(String value) {
            addCriterion("distributor_name not like", value, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameIn(List<String> values) {
            addCriterion("distributor_name in", values, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameNotIn(List<String> values) {
            addCriterion("distributor_name not in", values, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameBetween(String value1, String value2) {
            addCriterion("distributor_name between", value1, value2, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameNotBetween(String value1, String value2) {
            addCriterion("distributor_name not between", value1, value2, "distributorName");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andChannelTypeIsNull() {
            addCriterion("channel_type is null");
            return (Criteria) this;
        }

        public Criteria andChannelTypeIsNotNull() {
            addCriterion("channel_type is not null");
            return (Criteria) this;
        }

        public Criteria andChannelTypeEqualTo(String value) {
            addCriterion("channel_type =", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("channel_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelTypeNotEqualTo(String value) {
            addCriterion("channel_type <>", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("channel_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelTypeGreaterThan(String value) {
            addCriterion("channel_type >", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("channel_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelTypeGreaterThanOrEqualTo(String value) {
            addCriterion("channel_type >=", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("channel_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelTypeLessThan(String value) {
            addCriterion("channel_type <", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("channel_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelTypeLessThanOrEqualTo(String value) {
            addCriterion("channel_type <=", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("channel_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChannelTypeLike(String value) {
            addCriterion("channel_type like", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeNotLike(String value) {
            addCriterion("channel_type not like", value, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeIn(List<String> values) {
            addCriterion("channel_type in", values, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeNotIn(List<String> values) {
            addCriterion("channel_type not in", values, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeBetween(String value1, String value2) {
            addCriterion("channel_type between", value1, value2, "channelType");
            return (Criteria) this;
        }

        public Criteria andChannelTypeNotBetween(String value1, String value2) {
            addCriterion("channel_type not between", value1, value2, "channelType");
            return (Criteria) this;
        }

        public Criteria andRinseTimeIsNull() {
            addCriterion("rinse_time is null");
            return (Criteria) this;
        }

        public Criteria andRinseTimeIsNotNull() {
            addCriterion("rinse_time is not null");
            return (Criteria) this;
        }

        public Criteria andRinseTimeEqualTo(Date value) {
            addCriterion("rinse_time =", value, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andRinseTimeEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("rinse_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRinseTimeNotEqualTo(Date value) {
            addCriterion("rinse_time <>", value, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andRinseTimeNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("rinse_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRinseTimeGreaterThan(Date value) {
            addCriterion("rinse_time >", value, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andRinseTimeGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("rinse_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRinseTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("rinse_time >=", value, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andRinseTimeGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("rinse_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRinseTimeLessThan(Date value) {
            addCriterion("rinse_time <", value, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andRinseTimeLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("rinse_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRinseTimeLessThanOrEqualTo(Date value) {
            addCriterion("rinse_time <=", value, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andRinseTimeLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("rinse_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRinseTimeIn(List<Date> values) {
            addCriterion("rinse_time in", values, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andRinseTimeNotIn(List<Date> values) {
            addCriterion("rinse_time not in", values, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andRinseTimeBetween(Date value1, Date value2) {
            addCriterion("rinse_time between", value1, value2, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andRinseTimeNotBetween(Date value1, Date value2) {
            addCriterion("rinse_time not between", value1, value2, "rinseTime");
            return (Criteria) this;
        }

        public Criteria andIsRinseIsNull() {
            addCriterion("is_rinse is null");
            return (Criteria) this;
        }

        public Criteria andIsRinseIsNotNull() {
            addCriterion("is_rinse is not null");
            return (Criteria) this;
        }

        public Criteria andIsRinseEqualTo(Boolean value) {
            addCriterion("is_rinse =", value, "isRinse");
            return (Criteria) this;
        }

        public Criteria andIsRinseEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("is_rinse = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsRinseNotEqualTo(Boolean value) {
            addCriterion("is_rinse <>", value, "isRinse");
            return (Criteria) this;
        }

        public Criteria andIsRinseNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("is_rinse <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsRinseGreaterThan(Boolean value) {
            addCriterion("is_rinse >", value, "isRinse");
            return (Criteria) this;
        }

        public Criteria andIsRinseGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("is_rinse > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsRinseGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_rinse >=", value, "isRinse");
            return (Criteria) this;
        }

        public Criteria andIsRinseGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("is_rinse >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsRinseLessThan(Boolean value) {
            addCriterion("is_rinse <", value, "isRinse");
            return (Criteria) this;
        }

        public Criteria andIsRinseLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("is_rinse < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsRinseLessThanOrEqualTo(Boolean value) {
            addCriterion("is_rinse <=", value, "isRinse");
            return (Criteria) this;
        }

        public Criteria andIsRinseLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("is_rinse <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsRinseIn(List<Boolean> values) {
            addCriterion("is_rinse in", values, "isRinse");
            return (Criteria) this;
        }

        public Criteria andIsRinseNotIn(List<Boolean> values) {
            addCriterion("is_rinse not in", values, "isRinse");
            return (Criteria) this;
        }

        public Criteria andIsRinseBetween(Boolean value1, Boolean value2) {
            addCriterion("is_rinse between", value1, value2, "isRinse");
            return (Criteria) this;
        }

        public Criteria andIsRinseNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_rinse not between", value1, value2, "isRinse");
            return (Criteria) this;
        }

        public Criteria andAgentNumberIsNull() {
            addCriterion("agent_number is null");
            return (Criteria) this;
        }

        public Criteria andAgentNumberIsNotNull() {
            addCriterion("agent_number is not null");
            return (Criteria) this;
        }

        public Criteria andAgentNumberEqualTo(String value) {
            addCriterion("agent_number =", value, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("agent_number = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNumberNotEqualTo(String value) {
            addCriterion("agent_number <>", value, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("agent_number <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNumberGreaterThan(String value) {
            addCriterion("agent_number >", value, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("agent_number > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNumberGreaterThanOrEqualTo(String value) {
            addCriterion("agent_number >=", value, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("agent_number >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNumberLessThan(String value) {
            addCriterion("agent_number <", value, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("agent_number < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNumberLessThanOrEqualTo(String value) {
            addCriterion("agent_number <=", value, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("agent_number <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNumberLike(String value) {
            addCriterion("agent_number like", value, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberNotLike(String value) {
            addCriterion("agent_number not like", value, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberIn(List<String> values) {
            addCriterion("agent_number in", values, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberNotIn(List<String> values) {
            addCriterion("agent_number not in", values, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberBetween(String value1, String value2) {
            addCriterion("agent_number between", value1, value2, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberNotBetween(String value1, String value2) {
            addCriterion("agent_number not between", value1, value2, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNameIsNull() {
            addCriterion("agent_name is null");
            return (Criteria) this;
        }

        public Criteria andAgentNameIsNotNull() {
            addCriterion("agent_name is not null");
            return (Criteria) this;
        }

        public Criteria andAgentNameEqualTo(String value) {
            addCriterion("agent_name =", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("agent_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNameNotEqualTo(String value) {
            addCriterion("agent_name <>", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameNotEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("agent_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNameGreaterThan(String value) {
            addCriterion("agent_name >", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameGreaterThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("agent_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNameGreaterThanOrEqualTo(String value) {
            addCriterion("agent_name >=", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameGreaterThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("agent_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNameLessThan(String value) {
            addCriterion("agent_name <", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameLessThanColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("agent_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNameLessThanOrEqualTo(String value) {
            addCriterion("agent_name <=", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameLessThanOrEqualToColumn(ShopCustomerInfo.Column column) {
            addCriterion(new StringBuilder("agent_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNameLike(String value) {
            addCriterion("agent_name like", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameNotLike(String value) {
            addCriterion("agent_name not like", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameIn(List<String> values) {
            addCriterion("agent_name in", values, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameNotIn(List<String> values) {
            addCriterion("agent_name not in", values, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameBetween(String value1, String value2) {
            addCriterion("agent_name between", value1, value2, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameNotBetween(String value1, String value2) {
            addCriterion("agent_name not between", value1, value2, "agentName");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andCustCodeLikeInsensitive(String value) {
            addCriterion("upper(cust_code) like", value.toUpperCase(), "custCode");
            return (Criteria) this;
        }

        public Criteria andCustIdLikeInsensitive(String value) {
            addCriterion("upper(cust_id) like", value.toUpperCase(), "custId");
            return (Criteria) this;
        }

        public Criteria andUserIdLikeInsensitive(String value) {
            addCriterion("upper(user_id) like", value.toUpperCase(), "userId");
            return (Criteria) this;
        }

        public Criteria andCustNameLikeInsensitive(String value) {
            addCriterion("upper(cust_name) like", value.toUpperCase(), "custName");
            return (Criteria) this;
        }

        public Criteria andRoleTypeLikeInsensitive(String value) {
            addCriterion("upper(role_type) like", value.toUpperCase(), "roleType");
            return (Criteria) this;
        }

        public Criteria andBeIdLikeInsensitive(String value) {
            addCriterion("upper(be_id) like", value.toUpperCase(), "beId");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLikeInsensitive(String value) {
            addCriterion("upper(province_name) like", value.toUpperCase(), "provinceName");
            return (Criteria) this;
        }

        public Criteria andLocationLikeInsensitive(String value) {
            addCriterion("upper(location) like", value.toUpperCase(), "location");
            return (Criteria) this;
        }

        public Criteria andCityNameLikeInsensitive(String value) {
            addCriterion("upper(city_name) like", value.toUpperCase(), "cityName");
            return (Criteria) this;
        }

        public Criteria andRegionIdLikeInsensitive(String value) {
            addCriterion("upper(region_id) like", value.toUpperCase(), "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionNameLikeInsensitive(String value) {
            addCriterion("upper(region_name) like", value.toUpperCase(), "regionName");
            return (Criteria) this;
        }

        public Criteria andClientStatusLikeInsensitive(String value) {
            addCriterion("upper(client_status) like", value.toUpperCase(), "clientStatus");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdLikeInsensitive(String value) {
            addCriterion("upper(distributor_channel_id) like", value.toUpperCase(), "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameLikeInsensitive(String value) {
            addCriterion("upper(distributor_channel_name) like", value.toUpperCase(), "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeLikeInsensitive(String value) {
            addCriterion("upper(distributor_referral_code) like", value.toUpperCase(), "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfLikeInsensitive(String value) {
            addCriterion("upper(distributor_mrg_inf) like", value.toUpperCase(), "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeLikeInsensitive(String value) {
            addCriterion("upper(distributor_mrg_code) like", value.toUpperCase(), "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorNameLikeInsensitive(String value) {
            addCriterion("upper(distributor_name) like", value.toUpperCase(), "distributorName");
            return (Criteria) this;
        }

        public Criteria andChannelTypeLikeInsensitive(String value) {
            addCriterion("upper(channel_type) like", value.toUpperCase(), "channelType");
            return (Criteria) this;
        }

        public Criteria andAgentNumberLikeInsensitive(String value) {
            addCriterion("upper(agent_number) like", value.toUpperCase(), "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNameLikeInsensitive(String value) {
            addCriterion("upper(agent_name) like", value.toUpperCase(), "agentName");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Thu Jun 06 17:05:54 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        private ShopCustomerInfoExample example;

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        protected Criteria(ShopCustomerInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        public ShopCustomerInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Thu Jun 06 17:05:54 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Thu Jun 06 17:05:54 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Thu Jun 06 17:05:54 CST 2024
         */
        void example(com.chinamobile.retail.pojo.entity.ShopCustomerInfoExample example);
    }
}