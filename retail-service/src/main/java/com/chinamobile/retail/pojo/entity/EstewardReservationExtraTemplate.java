package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 商企预约单额外信息模版表
 *
 * <AUTHOR>
public class EstewardReservationExtraTemplate implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    private String id;

    /**
     * 预约单额外问题
     *
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    private String question;

    /**
     * 预约单额外问题提示
     *
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    private String tip;

    /**
     *
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    private Date createTime;

    /**
     *
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..esteward_reservation_extra_template.id
     *
     * @return the value of supply_chain..esteward_reservation_extra_template.id
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    public EstewardReservationExtraTemplate withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..esteward_reservation_extra_template.id
     *
     * @param id the value for supply_chain..esteward_reservation_extra_template.id
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..esteward_reservation_extra_template.question
     *
     * @return the value of supply_chain..esteward_reservation_extra_template.question
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    public String getQuestion() {
        return question;
    }

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    public EstewardReservationExtraTemplate withQuestion(String question) {
        this.setQuestion(question);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..esteward_reservation_extra_template.question
     *
     * @param question the value for supply_chain..esteward_reservation_extra_template.question
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    public void setQuestion(String question) {
        this.question = question;
    }

    /**
     * This method returns the value of the database column supply_chain..esteward_reservation_extra_template.tip
     *
     * @return the value of supply_chain..esteward_reservation_extra_template.tip
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    public String getTip() {
        return tip;
    }

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    public EstewardReservationExtraTemplate withTip(String tip) {
        this.setTip(tip);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..esteward_reservation_extra_template.tip
     *
     * @param tip the value for supply_chain..esteward_reservation_extra_template.tip
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    public void setTip(String tip) {
        this.tip = tip;
    }

    /**
     * This method returns the value of the database column supply_chain..esteward_reservation_extra_template.create_time
     *
     * @return the value of supply_chain..esteward_reservation_extra_template.create_time
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    public EstewardReservationExtraTemplate withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..esteward_reservation_extra_template.create_time
     *
     * @param createTime the value for supply_chain..esteward_reservation_extra_template.create_time
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..esteward_reservation_extra_template.update_time
     *
     * @return the value of supply_chain..esteward_reservation_extra_template.update_time
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    public EstewardReservationExtraTemplate withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..esteward_reservation_extra_template.update_time
     *
     * @param updateTime the value for supply_chain..esteward_reservation_extra_template.update_time
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", question=").append(question);
        sb.append(", tip=").append(tip);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        EstewardReservationExtraTemplate other = (EstewardReservationExtraTemplate) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getQuestion() == null ? other.getQuestion() == null : this.getQuestion().equals(other.getQuestion()))
            && (this.getTip() == null ? other.getTip() == null : this.getTip().equals(other.getTip()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getQuestion() == null) ? 0 : getQuestion().hashCode());
        result = prime * result + ((getTip() == null) ? 0 : getTip().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Apr 16 17:11:17 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        question("question", "question", "VARCHAR", false),
        tip("tip", "tip", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Wed Apr 16 17:11:17 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Apr 16 17:11:17 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Apr 16 17:11:17 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Apr 16 17:11:17 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Apr 16 17:11:17 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Apr 16 17:11:17 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Apr 16 17:11:17 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Apr 16 17:11:17 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Apr 16 17:11:17 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Apr 16 17:11:17 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Apr 16 17:11:17 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Apr 16 17:11:17 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Apr 16 17:11:17 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Apr 16 17:11:17 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Apr 16 17:11:17 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Apr 16 17:11:17 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Apr 16 17:11:17 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}