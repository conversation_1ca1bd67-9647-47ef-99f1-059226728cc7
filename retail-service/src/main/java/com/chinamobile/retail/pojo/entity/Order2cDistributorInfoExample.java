package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Order2cDistributorInfoExample {
    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    public Order2cDistributorInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    public Order2cDistributorInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    public Order2cDistributorInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    public static Criteria newAndCreateCriteria() {
        Order2cDistributorInfoExample example = new Order2cDistributorInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    public Order2cDistributorInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    public Order2cDistributorInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andDistributorLevelIsNull() {
            addCriterion("distributor_level is null");
            return (Criteria) this;
        }

        public Criteria andDistributorLevelIsNotNull() {
            addCriterion("distributor_level is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorLevelEqualTo(String value) {
            addCriterion("distributor_level =", value, "distributorLevel");
            return (Criteria) this;
        }

        public Criteria andDistributorLevelEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("distributor_level = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorLevelNotEqualTo(String value) {
            addCriterion("distributor_level <>", value, "distributorLevel");
            return (Criteria) this;
        }

        public Criteria andDistributorLevelNotEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("distributor_level <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorLevelGreaterThan(String value) {
            addCriterion("distributor_level >", value, "distributorLevel");
            return (Criteria) this;
        }

        public Criteria andDistributorLevelGreaterThanColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("distributor_level > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorLevelGreaterThanOrEqualTo(String value) {
            addCriterion("distributor_level >=", value, "distributorLevel");
            return (Criteria) this;
        }

        public Criteria andDistributorLevelGreaterThanOrEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("distributor_level >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorLevelLessThan(String value) {
            addCriterion("distributor_level <", value, "distributorLevel");
            return (Criteria) this;
        }

        public Criteria andDistributorLevelLessThanColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("distributor_level < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorLevelLessThanOrEqualTo(String value) {
            addCriterion("distributor_level <=", value, "distributorLevel");
            return (Criteria) this;
        }

        public Criteria andDistributorLevelLessThanOrEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("distributor_level <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorLevelLike(String value) {
            addCriterion("distributor_level like", value, "distributorLevel");
            return (Criteria) this;
        }

        public Criteria andDistributorLevelNotLike(String value) {
            addCriterion("distributor_level not like", value, "distributorLevel");
            return (Criteria) this;
        }

        public Criteria andDistributorLevelIn(List<String> values) {
            addCriterion("distributor_level in", values, "distributorLevel");
            return (Criteria) this;
        }

        public Criteria andDistributorLevelNotIn(List<String> values) {
            addCriterion("distributor_level not in", values, "distributorLevel");
            return (Criteria) this;
        }

        public Criteria andDistributorLevelBetween(String value1, String value2) {
            addCriterion("distributor_level between", value1, value2, "distributorLevel");
            return (Criteria) this;
        }

        public Criteria andDistributorLevelNotBetween(String value1, String value2) {
            addCriterion("distributor_level not between", value1, value2, "distributorLevel");
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneIsNull() {
            addCriterion("distributor_phone is null");
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneIsNotNull() {
            addCriterion("distributor_phone is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneEqualTo(String value) {
            addCriterion("distributor_phone =", value, "distributorPhone");
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("distributor_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneNotEqualTo(String value) {
            addCriterion("distributor_phone <>", value, "distributorPhone");
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneNotEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("distributor_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneGreaterThan(String value) {
            addCriterion("distributor_phone >", value, "distributorPhone");
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneGreaterThanColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("distributor_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("distributor_phone >=", value, "distributorPhone");
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneGreaterThanOrEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("distributor_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneLessThan(String value) {
            addCriterion("distributor_phone <", value, "distributorPhone");
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneLessThanColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("distributor_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneLessThanOrEqualTo(String value) {
            addCriterion("distributor_phone <=", value, "distributorPhone");
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneLessThanOrEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("distributor_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneLike(String value) {
            addCriterion("distributor_phone like", value, "distributorPhone");
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneNotLike(String value) {
            addCriterion("distributor_phone not like", value, "distributorPhone");
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneIn(List<String> values) {
            addCriterion("distributor_phone in", values, "distributorPhone");
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneNotIn(List<String> values) {
            addCriterion("distributor_phone not in", values, "distributorPhone");
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneBetween(String value1, String value2) {
            addCriterion("distributor_phone between", value1, value2, "distributorPhone");
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneNotBetween(String value1, String value2) {
            addCriterion("distributor_phone not between", value1, value2, "distributorPhone");
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeIsNull() {
            addCriterion("distributor_share_code is null");
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeIsNotNull() {
            addCriterion("distributor_share_code is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeEqualTo(String value) {
            addCriterion("distributor_share_code =", value, "distributorShareCode");
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("distributor_share_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeNotEqualTo(String value) {
            addCriterion("distributor_share_code <>", value, "distributorShareCode");
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeNotEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("distributor_share_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeGreaterThan(String value) {
            addCriterion("distributor_share_code >", value, "distributorShareCode");
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeGreaterThanColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("distributor_share_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeGreaterThanOrEqualTo(String value) {
            addCriterion("distributor_share_code >=", value, "distributorShareCode");
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeGreaterThanOrEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("distributor_share_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeLessThan(String value) {
            addCriterion("distributor_share_code <", value, "distributorShareCode");
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeLessThanColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("distributor_share_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeLessThanOrEqualTo(String value) {
            addCriterion("distributor_share_code <=", value, "distributorShareCode");
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeLessThanOrEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("distributor_share_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeLike(String value) {
            addCriterion("distributor_share_code like", value, "distributorShareCode");
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeNotLike(String value) {
            addCriterion("distributor_share_code not like", value, "distributorShareCode");
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeIn(List<String> values) {
            addCriterion("distributor_share_code in", values, "distributorShareCode");
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeNotIn(List<String> values) {
            addCriterion("distributor_share_code not in", values, "distributorShareCode");
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeBetween(String value1, String value2) {
            addCriterion("distributor_share_code between", value1, value2, "distributorShareCode");
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeNotBetween(String value1, String value2) {
            addCriterion("distributor_share_code not between", value1, value2, "distributorShareCode");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(Order2cDistributorInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andOrderIdLikeInsensitive(String value) {
            addCriterion("upper(order_id) like", value.toUpperCase(), "orderId");
            return (Criteria) this;
        }

        public Criteria andDistributorLevelLikeInsensitive(String value) {
            addCriterion("upper(distributor_level) like", value.toUpperCase(), "distributorLevel");
            return (Criteria) this;
        }

        public Criteria andDistributorPhoneLikeInsensitive(String value) {
            addCriterion("upper(distributor_phone) like", value.toUpperCase(), "distributorPhone");
            return (Criteria) this;
        }

        public Criteria andDistributorShareCodeLikeInsensitive(String value) {
            addCriterion("upper(distributor_share_code) like", value.toUpperCase(), "distributorShareCode");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Thu Sep 15 16:50:00 CST 2022
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Thu Sep 15 16:50:00 CST 2022
         */
        private Order2cDistributorInfoExample example;

        /**
         *
         * @mbg.generated Thu Sep 15 16:50:00 CST 2022
         */
        protected Criteria(Order2cDistributorInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Thu Sep 15 16:50:00 CST 2022
         */
        public Order2cDistributorInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Thu Sep 15 16:50:00 CST 2022
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Sep 15 16:50:00 CST 2022
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Sep 15 16:50:00 CST 2022
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Thu Sep 15 16:50:00 CST 2022
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Thu Sep 15 16:50:00 CST 2022
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Thu Sep 15 16:50:00 CST 2022
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Thu Sep 15 16:50:00 CST 2022
         */
        void example(com.chinamobile.retail.pojo.entity.Order2cDistributorInfoExample example);
    }
}