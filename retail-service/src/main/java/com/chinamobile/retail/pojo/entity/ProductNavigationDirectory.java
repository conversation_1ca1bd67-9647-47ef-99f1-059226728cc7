package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 一级导航目录与二级导航目录对应表
 *
 * <AUTHOR>
public class ProductNavigationDirectory implements Serializable {
    /**
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public static final Boolean DELETED = IsDelete.DELETED.value();

    /**
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public static final Boolean NOT_DELETED = IsDelete.NOT_DELETED.value();

    /**
     *
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    private String id;

    /**
     * 目录名称
     *
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    private String name;

    /**
     * 父目录id，父目录为-1表示当前是一级目录
     *
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    private String parentId;

    /**
     * 排序
     *
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    private Integer sort;

    /**
     * 图片
     *
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    private String image;

    /**
     * 导航目录菜单,1:物联网导航目录 2:视联网导航目录
     *
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    private String menu;

    /**
     * 是否删除
     *
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    private Boolean isDelete;

    /**
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..product_navigation_directory.id
     *
     * @return the value of supply_chain..product_navigation_directory.id
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public ProductNavigationDirectory withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_navigation_directory.id
     *
     * @param id the value for supply_chain..product_navigation_directory.id
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..product_navigation_directory.name
     *
     * @return the value of supply_chain..product_navigation_directory.name
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public ProductNavigationDirectory withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_navigation_directory.name
     *
     * @param name the value for supply_chain..product_navigation_directory.name
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..product_navigation_directory.parent_id
     *
     * @return the value of supply_chain..product_navigation_directory.parent_id
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public String getParentId() {
        return parentId;
    }

    /**
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public ProductNavigationDirectory withParentId(String parentId) {
        this.setParentId(parentId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_navigation_directory.parent_id
     *
     * @param parentId the value for supply_chain..product_navigation_directory.parent_id
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public void setParentId(String parentId) {
        this.parentId = parentId == null ? null : parentId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..product_navigation_directory.sort
     *
     * @return the value of supply_chain..product_navigation_directory.sort
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public Integer getSort() {
        return sort;
    }

    /**
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public ProductNavigationDirectory withSort(Integer sort) {
        this.setSort(sort);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_navigation_directory.sort
     *
     * @param sort the value for supply_chain..product_navigation_directory.sort
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method returns the value of the database column supply_chain..product_navigation_directory.image
     *
     * @return the value of supply_chain..product_navigation_directory.image
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public String getImage() {
        return image;
    }

    /**
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public ProductNavigationDirectory withImage(String image) {
        this.setImage(image);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_navigation_directory.image
     *
     * @param image the value for supply_chain..product_navigation_directory.image
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public void setImage(String image) {
        this.image = image == null ? null : image.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..product_navigation_directory.menu
     *
     * @return the value of supply_chain..product_navigation_directory.menu
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public String getMenu() {
        return menu;
    }

    /**
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public ProductNavigationDirectory withMenu(String menu) {
        this.setMenu(menu);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_navigation_directory.menu
     *
     * @param menu the value for supply_chain..product_navigation_directory.menu
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public void setMenu(String menu) {
        this.menu = menu == null ? null : menu.trim();
    }

    /**
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public void andLogicalDeleted(boolean deleted) {
        setIsDelete(deleted ? IsDelete.DELETED.value() : IsDelete.NOT_DELETED.value());
    }

    /**
     * This method returns the value of the database column supply_chain..product_navigation_directory.is_delete
     *
     * @return the value of supply_chain..product_navigation_directory.is_delete
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public Boolean getIsDelete() {
        return isDelete;
    }

    /**
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public ProductNavigationDirectory withIsDelete(Boolean isDelete) {
        this.setIsDelete(isDelete);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_navigation_directory.is_delete
     *
     * @param isDelete the value for supply_chain..product_navigation_directory.is_delete
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public void setIsDelete(Boolean isDelete) {
        this.isDelete = isDelete;
    }

    /**
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", DELETED=").append(DELETED);
        sb.append(", NOT_DELETED=").append(NOT_DELETED);
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", parentId=").append(parentId);
        sb.append(", sort=").append(sort);
        sb.append(", image=").append(image);
        sb.append(", menu=").append(menu);
        sb.append(", isDelete=").append(isDelete);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ProductNavigationDirectory other = (ProductNavigationDirectory) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getParentId() == null ? other.getParentId() == null : this.getParentId().equals(other.getParentId()))
            && (this.getSort() == null ? other.getSort() == null : this.getSort().equals(other.getSort()))
            && (this.getImage() == null ? other.getImage() == null : this.getImage().equals(other.getImage()))
            && (this.getMenu() == null ? other.getMenu() == null : this.getMenu().equals(other.getMenu()))
            && (this.getIsDelete() == null ? other.getIsDelete() == null : this.getIsDelete().equals(other.getIsDelete()));
    }

    /**
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getParentId() == null) ? 0 : getParentId().hashCode());
        result = prime * result + ((getSort() == null) ? 0 : getSort().hashCode());
        result = prime * result + ((getImage() == null) ? 0 : getImage().hashCode());
        result = prime * result + ((getMenu() == null) ? 0 : getMenu().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public enum IsDelete {
        NOT_DELETED(new Boolean("0"), "未删除"),
        DELETED(new Boolean("1"), "已删除");

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        private final Boolean value;

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        private final String name;

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        IsDelete(Boolean value, String name) {
            this.value = value;
            this.name = name;
        }

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        public Boolean getValue() {
            return this.value;
        }

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        public Boolean value() {
            return this.value;
        }

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        public String getName() {
            return this.name;
        }

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        public static IsDelete parseValue(Boolean value) {
            if (value != null) {
                for (IsDelete item : values()) {
                    if (item.value.equals(value)) {
                        return item;
                    }
                }
            }
            return null;
        }

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        public static IsDelete parseName(String name) {
            if (name != null) {
                for (IsDelete item : values()) {
                    if (item.name.equals(name)) {
                        return item;
                    }
                }
            }
            return null;
        }
    }

    /**
     *
     * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        name("name", "name", "VARCHAR", true),
        parentId("parent_id", "parentId", "VARCHAR", false),
        sort("sort", "sort", "INTEGER", false),
        image("image", "image", "VARCHAR", false),
        menu("menu", "menu", "VARCHAR", false),
        isDelete("is_delete", "isDelete", "BIT", false);

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Dec 11 09:29:08 GMT+08:00 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}