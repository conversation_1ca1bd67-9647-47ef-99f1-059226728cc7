package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 产品流程实例的附件表
 *
 * <AUTHOR>
public class ProductFlowInstanceAttachment implements Serializable {
    /**
     *
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    private String id;

    /**
     * 流程实例id
     *
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    private String flowInstanceId;

    /**
     * 附件文件名
     *
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    private String fileName;

    /**
     * 附件文件在对象存储的key
     *
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    private String fileKey;

    /**
     * 附件文件下载地址
     *
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    private String fileUrl;

    /**
     * 附件类型 0-商品头图 1-商品轮播图 2-视频 3-商品详情页素材（移动端）4-实质性产品图片（移动端） 5-售后政策图片（移动端） 6-其他
     *
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    private Integer type;

    /**
     *
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    private Date createTime;

    /**
     *
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_attachment.id
     *
     * @return the value of supply_chain..product_flow_instance_attachment.id
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public ProductFlowInstanceAttachment withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_attachment.id
     *
     * @param id the value for supply_chain..product_flow_instance_attachment.id
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_attachment.flow_instance_id
     *
     * @return the value of supply_chain..product_flow_instance_attachment.flow_instance_id
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public String getFlowInstanceId() {
        return flowInstanceId;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public ProductFlowInstanceAttachment withFlowInstanceId(String flowInstanceId) {
        this.setFlowInstanceId(flowInstanceId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_attachment.flow_instance_id
     *
     * @param flowInstanceId the value for supply_chain..product_flow_instance_attachment.flow_instance_id
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public void setFlowInstanceId(String flowInstanceId) {
        this.flowInstanceId = flowInstanceId;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_attachment.file_name
     *
     * @return the value of supply_chain..product_flow_instance_attachment.file_name
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public String getFileName() {
        return fileName;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public ProductFlowInstanceAttachment withFileName(String fileName) {
        this.setFileName(fileName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_attachment.file_name
     *
     * @param fileName the value for supply_chain..product_flow_instance_attachment.file_name
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_attachment.file_key
     *
     * @return the value of supply_chain..product_flow_instance_attachment.file_key
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public String getFileKey() {
        return fileKey;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public ProductFlowInstanceAttachment withFileKey(String fileKey) {
        this.setFileKey(fileKey);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_attachment.file_key
     *
     * @param fileKey the value for supply_chain..product_flow_instance_attachment.file_key
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public void setFileKey(String fileKey) {
        this.fileKey = fileKey;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_attachment.file_url
     *
     * @return the value of supply_chain..product_flow_instance_attachment.file_url
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public String getFileUrl() {
        return fileUrl;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public ProductFlowInstanceAttachment withFileUrl(String fileUrl) {
        this.setFileUrl(fileUrl);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_attachment.file_url
     *
     * @param fileUrl the value for supply_chain..product_flow_instance_attachment.file_url
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_attachment.type
     *
     * @return the value of supply_chain..product_flow_instance_attachment.type
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public Integer getType() {
        return type;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public ProductFlowInstanceAttachment withType(Integer type) {
        this.setType(type);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_attachment.type
     *
     * @param type the value for supply_chain..product_flow_instance_attachment.type
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_attachment.create_time
     *
     * @return the value of supply_chain..product_flow_instance_attachment.create_time
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public ProductFlowInstanceAttachment withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_attachment.create_time
     *
     * @param createTime the value for supply_chain..product_flow_instance_attachment.create_time
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance_attachment.update_time
     *
     * @return the value of supply_chain..product_flow_instance_attachment.update_time
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public ProductFlowInstanceAttachment withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance_attachment.update_time
     *
     * @param updateTime the value for supply_chain..product_flow_instance_attachment.update_time
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", flowInstanceId=").append(flowInstanceId);
        sb.append(", fileName=").append(fileName);
        sb.append(", fileKey=").append(fileKey);
        sb.append(", fileUrl=").append(fileUrl);
        sb.append(", type=").append(type);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ProductFlowInstanceAttachment other = (ProductFlowInstanceAttachment) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getFlowInstanceId() == null ? other.getFlowInstanceId() == null : this.getFlowInstanceId().equals(other.getFlowInstanceId()))
            && (this.getFileName() == null ? other.getFileName() == null : this.getFileName().equals(other.getFileName()))
            && (this.getFileKey() == null ? other.getFileKey() == null : this.getFileKey().equals(other.getFileKey()))
            && (this.getFileUrl() == null ? other.getFileUrl() == null : this.getFileUrl().equals(other.getFileUrl()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getFlowInstanceId() == null) ? 0 : getFlowInstanceId().hashCode());
        result = prime * result + ((getFileName() == null) ? 0 : getFileName().hashCode());
        result = prime * result + ((getFileKey() == null) ? 0 : getFileKey().hashCode());
        result = prime * result + ((getFileUrl() == null) ? 0 : getFileUrl().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Jun 04 15:42:06 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        flowInstanceId("flow_instance_id", "flowInstanceId", "VARCHAR", false),
        fileName("file_name", "fileName", "VARCHAR", false),
        fileKey("file_key", "fileKey", "VARCHAR", false),
        fileUrl("file_url", "fileUrl", "VARCHAR", false),
        type("type", "type", "INTEGER", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Tue Jun 04 15:42:06 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Jun 04 15:42:06 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Jun 04 15:42:06 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Jun 04 15:42:06 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Jun 04 15:42:06 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Jun 04 15:42:06 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Jun 04 15:42:06 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Jun 04 15:42:06 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Jun 04 15:42:06 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Jun 04 15:42:06 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Jun 04 15:42:06 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Jun 04 15:42:06 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Jun 04 15:42:06 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Jun 04 15:42:06 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Jun 04 15:42:06 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Jun 04 15:42:06 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Jun 04 15:42:06 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}