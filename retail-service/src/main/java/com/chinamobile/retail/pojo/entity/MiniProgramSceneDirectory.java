package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 小程序场景目录
 *
 * <AUTHOR>
public class MiniProgramSceneDirectory implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    private String id;

    /**
     * 目录名称
     *
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    private String name;

    /**
     * 顺序
     *
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    private Integer sort;

    /**
     * 上级目录id
     *
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    private String parentId;

    /**
     * 是否已删除
     *
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    private Boolean deleted;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_directory.id
     *
     * @return the value of supply_chain..mini_program_scene_directory.id
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public MiniProgramSceneDirectory withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_directory.id
     *
     * @param id the value for supply_chain..mini_program_scene_directory.id
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_directory.name
     *
     * @return the value of supply_chain..mini_program_scene_directory.name
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public MiniProgramSceneDirectory withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_directory.name
     *
     * @param name the value for supply_chain..mini_program_scene_directory.name
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_directory.sort
     *
     * @return the value of supply_chain..mini_program_scene_directory.sort
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public Integer getSort() {
        return sort;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public MiniProgramSceneDirectory withSort(Integer sort) {
        this.setSort(sort);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_directory.sort
     *
     * @param sort the value for supply_chain..mini_program_scene_directory.sort
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_directory.parent_id
     *
     * @return the value of supply_chain..mini_program_scene_directory.parent_id
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public String getParentId() {
        return parentId;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public MiniProgramSceneDirectory withParentId(String parentId) {
        this.setParentId(parentId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_directory.parent_id
     *
     * @param parentId the value for supply_chain..mini_program_scene_directory.parent_id
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public void setParentId(String parentId) {
        this.parentId = parentId == null ? null : parentId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_directory.deleted
     *
     * @return the value of supply_chain..mini_program_scene_directory.deleted
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public MiniProgramSceneDirectory withDeleted(Boolean deleted) {
        this.setDeleted(deleted);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_directory.deleted
     *
     * @param deleted the value for supply_chain..mini_program_scene_directory.deleted
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_directory.create_time
     *
     * @return the value of supply_chain..mini_program_scene_directory.create_time
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public MiniProgramSceneDirectory withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_directory.create_time
     *
     * @param createTime the value for supply_chain..mini_program_scene_directory.create_time
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_directory.update_time
     *
     * @return the value of supply_chain..mini_program_scene_directory.update_time
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public MiniProgramSceneDirectory withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_directory.update_time
     *
     * @param updateTime the value for supply_chain..mini_program_scene_directory.update_time
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", sort=").append(sort);
        sb.append(", parentId=").append(parentId);
        sb.append(", deleted=").append(deleted);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramSceneDirectory other = (MiniProgramSceneDirectory) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getSort() == null ? other.getSort() == null : this.getSort().equals(other.getSort()))
            && (this.getParentId() == null ? other.getParentId() == null : this.getParentId().equals(other.getParentId()))
            && (this.getDeleted() == null ? other.getDeleted() == null : this.getDeleted().equals(other.getDeleted()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getSort() == null) ? 0 : getSort().hashCode());
        result = prime * result + ((getParentId() == null) ? 0 : getParentId().hashCode());
        result = prime * result + ((getDeleted() == null) ? 0 : getDeleted().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        name("name", "name", "VARCHAR", true),
        sort("sort", "sort", "INTEGER", false),
        parentId("parent_id", "parentId", "VARCHAR", false),
        deleted("deleted", "deleted", "BIT", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
         */
        private static final String BEGINNING_DELIMITER = "`";

        /**
         *
         * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
         */
        private static final String ENDING_DELIMITER = "`";

        /**
         *
         * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Dec 10 15:29:23 GMT+08:00 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}