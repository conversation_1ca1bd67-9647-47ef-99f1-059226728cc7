package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MiniProgramActivityWeeklyFunExample {
    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public MiniProgramActivityWeeklyFunExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public MiniProgramActivityWeeklyFunExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public MiniProgramActivityWeeklyFunExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        MiniProgramActivityWeeklyFunExample example = new MiniProgramActivityWeeklyFunExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public MiniProgramActivityWeeklyFunExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public MiniProgramActivityWeeklyFunExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andActivityIdIsNull() {
            addCriterion("activity_id is null");
            return (Criteria) this;
        }

        public Criteria andActivityIdIsNotNull() {
            addCriterion("activity_id is not null");
            return (Criteria) this;
        }

        public Criteria andActivityIdEqualTo(String value) {
            addCriterion("activity_id =", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("activity_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityIdNotEqualTo(String value) {
            addCriterion("activity_id <>", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("activity_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThan(String value) {
            addCriterion("activity_id >", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("activity_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThanOrEqualTo(String value) {
            addCriterion("activity_id >=", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdGreaterThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("activity_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThan(String value) {
            addCriterion("activity_id <", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("activity_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThanOrEqualTo(String value) {
            addCriterion("activity_id <=", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdLessThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("activity_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andActivityIdLike(String value) {
            addCriterion("activity_id like", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotLike(String value) {
            addCriterion("activity_id not like", value, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdIn(List<String> values) {
            addCriterion("activity_id in", values, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotIn(List<String> values) {
            addCriterion("activity_id not in", values, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdBetween(String value1, String value2) {
            addCriterion("activity_id between", value1, value2, "activityId");
            return (Criteria) this;
        }

        public Criteria andActivityIdNotBetween(String value1, String value2) {
            addCriterion("activity_id not between", value1, value2, "activityId");
            return (Criteria) this;
        }

        public Criteria andRuleIsNull() {
            addCriterion("rule is null");
            return (Criteria) this;
        }

        public Criteria andRuleIsNotNull() {
            addCriterion("rule is not null");
            return (Criteria) this;
        }

        public Criteria andRuleEqualTo(String value) {
            addCriterion("rule =", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("rule = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleNotEqualTo(String value) {
            addCriterion("rule <>", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleNotEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("rule <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleGreaterThan(String value) {
            addCriterion("rule >", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleGreaterThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("rule > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleGreaterThanOrEqualTo(String value) {
            addCriterion("rule >=", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleGreaterThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("rule >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleLessThan(String value) {
            addCriterion("rule <", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleLessThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("rule < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleLessThanOrEqualTo(String value) {
            addCriterion("rule <=", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleLessThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("rule <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleLike(String value) {
            addCriterion("rule like", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleNotLike(String value) {
            addCriterion("rule not like", value, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleIn(List<String> values) {
            addCriterion("rule in", values, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleNotIn(List<String> values) {
            addCriterion("rule not in", values, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleBetween(String value1, String value2) {
            addCriterion("rule between", value1, value2, "rule");
            return (Criteria) this;
        }

        public Criteria andRuleNotBetween(String value1, String value2) {
            addCriterion("rule not between", value1, value2, "rule");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("description = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("description <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("description > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("description >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("description < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("description <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andWheelPartsIsNull() {
            addCriterion("wheel_parts is null");
            return (Criteria) this;
        }

        public Criteria andWheelPartsIsNotNull() {
            addCriterion("wheel_parts is not null");
            return (Criteria) this;
        }

        public Criteria andWheelPartsEqualTo(Integer value) {
            addCriterion("wheel_parts =", value, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andWheelPartsEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("wheel_parts = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andWheelPartsNotEqualTo(Integer value) {
            addCriterion("wheel_parts <>", value, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andWheelPartsNotEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("wheel_parts <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andWheelPartsGreaterThan(Integer value) {
            addCriterion("wheel_parts >", value, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andWheelPartsGreaterThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("wheel_parts > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andWheelPartsGreaterThanOrEqualTo(Integer value) {
            addCriterion("wheel_parts >=", value, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andWheelPartsGreaterThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("wheel_parts >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andWheelPartsLessThan(Integer value) {
            addCriterion("wheel_parts <", value, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andWheelPartsLessThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("wheel_parts < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andWheelPartsLessThanOrEqualTo(Integer value) {
            addCriterion("wheel_parts <=", value, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andWheelPartsLessThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("wheel_parts <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andWheelPartsIn(List<Integer> values) {
            addCriterion("wheel_parts in", values, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andWheelPartsNotIn(List<Integer> values) {
            addCriterion("wheel_parts not in", values, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andWheelPartsBetween(Integer value1, Integer value2) {
            addCriterion("wheel_parts between", value1, value2, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andWheelPartsNotBetween(Integer value1, Integer value2) {
            addCriterion("wheel_parts not between", value1, value2, "wheelParts");
            return (Criteria) this;
        }

        public Criteria andOrderStartIsNull() {
            addCriterion("order_start is null");
            return (Criteria) this;
        }

        public Criteria andOrderStartIsNotNull() {
            addCriterion("order_start is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStartEqualTo(Date value) {
            addCriterion("order_start =", value, "orderStart");
            return (Criteria) this;
        }

        public Criteria andOrderStartEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("order_start = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStartNotEqualTo(Date value) {
            addCriterion("order_start <>", value, "orderStart");
            return (Criteria) this;
        }

        public Criteria andOrderStartNotEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("order_start <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStartGreaterThan(Date value) {
            addCriterion("order_start >", value, "orderStart");
            return (Criteria) this;
        }

        public Criteria andOrderStartGreaterThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("order_start > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStartGreaterThanOrEqualTo(Date value) {
            addCriterion("order_start >=", value, "orderStart");
            return (Criteria) this;
        }

        public Criteria andOrderStartGreaterThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("order_start >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStartLessThan(Date value) {
            addCriterion("order_start <", value, "orderStart");
            return (Criteria) this;
        }

        public Criteria andOrderStartLessThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("order_start < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStartLessThanOrEqualTo(Date value) {
            addCriterion("order_start <=", value, "orderStart");
            return (Criteria) this;
        }

        public Criteria andOrderStartLessThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("order_start <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStartIn(List<Date> values) {
            addCriterion("order_start in", values, "orderStart");
            return (Criteria) this;
        }

        public Criteria andOrderStartNotIn(List<Date> values) {
            addCriterion("order_start not in", values, "orderStart");
            return (Criteria) this;
        }

        public Criteria andOrderStartBetween(Date value1, Date value2) {
            addCriterion("order_start between", value1, value2, "orderStart");
            return (Criteria) this;
        }

        public Criteria andOrderStartNotBetween(Date value1, Date value2) {
            addCriterion("order_start not between", value1, value2, "orderStart");
            return (Criteria) this;
        }

        public Criteria andOrderStopIsNull() {
            addCriterion("order_stop is null");
            return (Criteria) this;
        }

        public Criteria andOrderStopIsNotNull() {
            addCriterion("order_stop is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStopEqualTo(Date value) {
            addCriterion("order_stop =", value, "orderStop");
            return (Criteria) this;
        }

        public Criteria andOrderStopEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("order_stop = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStopNotEqualTo(Date value) {
            addCriterion("order_stop <>", value, "orderStop");
            return (Criteria) this;
        }

        public Criteria andOrderStopNotEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("order_stop <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStopGreaterThan(Date value) {
            addCriterion("order_stop >", value, "orderStop");
            return (Criteria) this;
        }

        public Criteria andOrderStopGreaterThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("order_stop > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStopGreaterThanOrEqualTo(Date value) {
            addCriterion("order_stop >=", value, "orderStop");
            return (Criteria) this;
        }

        public Criteria andOrderStopGreaterThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("order_stop >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStopLessThan(Date value) {
            addCriterion("order_stop <", value, "orderStop");
            return (Criteria) this;
        }

        public Criteria andOrderStopLessThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("order_stop < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStopLessThanOrEqualTo(Date value) {
            addCriterion("order_stop <=", value, "orderStop");
            return (Criteria) this;
        }

        public Criteria andOrderStopLessThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("order_stop <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStopIn(List<Date> values) {
            addCriterion("order_stop in", values, "orderStop");
            return (Criteria) this;
        }

        public Criteria andOrderStopNotIn(List<Date> values) {
            addCriterion("order_stop not in", values, "orderStop");
            return (Criteria) this;
        }

        public Criteria andOrderStopBetween(Date value1, Date value2) {
            addCriterion("order_stop between", value1, value2, "orderStop");
            return (Criteria) this;
        }

        public Criteria andOrderStopNotBetween(Date value1, Date value2) {
            addCriterion("order_stop not between", value1, value2, "orderStop");
            return (Criteria) this;
        }

        public Criteria andOrderCountIsNull() {
            addCriterion("order_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderCountIsNotNull() {
            addCriterion("order_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCountEqualTo(Integer value) {
            addCriterion("order_count =", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("order_count = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCountNotEqualTo(Integer value) {
            addCriterion("order_count <>", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountNotEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("order_count <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCountGreaterThan(Integer value) {
            addCriterion("order_count >", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountGreaterThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("order_count > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_count >=", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountGreaterThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("order_count >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCountLessThan(Integer value) {
            addCriterion("order_count <", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountLessThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("order_count < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_count <=", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountLessThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("order_count <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCountIn(List<Integer> values) {
            addCriterion("order_count in", values, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountNotIn(List<Integer> values) {
            addCriterion("order_count not in", values, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountBetween(Integer value1, Integer value2) {
            addCriterion("order_count between", value1, value2, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_count not between", value1, value2, "orderCount");
            return (Criteria) this;
        }

        public Criteria andRegisterStartIsNull() {
            addCriterion("register_start is null");
            return (Criteria) this;
        }

        public Criteria andRegisterStartIsNotNull() {
            addCriterion("register_start is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterStartEqualTo(Date value) {
            addCriterion("register_start =", value, "registerStart");
            return (Criteria) this;
        }

        public Criteria andRegisterStartEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("register_start = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterStartNotEqualTo(Date value) {
            addCriterion("register_start <>", value, "registerStart");
            return (Criteria) this;
        }

        public Criteria andRegisterStartNotEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("register_start <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterStartGreaterThan(Date value) {
            addCriterion("register_start >", value, "registerStart");
            return (Criteria) this;
        }

        public Criteria andRegisterStartGreaterThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("register_start > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterStartGreaterThanOrEqualTo(Date value) {
            addCriterion("register_start >=", value, "registerStart");
            return (Criteria) this;
        }

        public Criteria andRegisterStartGreaterThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("register_start >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterStartLessThan(Date value) {
            addCriterion("register_start <", value, "registerStart");
            return (Criteria) this;
        }

        public Criteria andRegisterStartLessThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("register_start < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterStartLessThanOrEqualTo(Date value) {
            addCriterion("register_start <=", value, "registerStart");
            return (Criteria) this;
        }

        public Criteria andRegisterStartLessThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("register_start <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterStartIn(List<Date> values) {
            addCriterion("register_start in", values, "registerStart");
            return (Criteria) this;
        }

        public Criteria andRegisterStartNotIn(List<Date> values) {
            addCriterion("register_start not in", values, "registerStart");
            return (Criteria) this;
        }

        public Criteria andRegisterStartBetween(Date value1, Date value2) {
            addCriterion("register_start between", value1, value2, "registerStart");
            return (Criteria) this;
        }

        public Criteria andRegisterStartNotBetween(Date value1, Date value2) {
            addCriterion("register_start not between", value1, value2, "registerStart");
            return (Criteria) this;
        }

        public Criteria andRegisterStopIsNull() {
            addCriterion("register_stop is null");
            return (Criteria) this;
        }

        public Criteria andRegisterStopIsNotNull() {
            addCriterion("register_stop is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterStopEqualTo(Date value) {
            addCriterion("register_stop =", value, "registerStop");
            return (Criteria) this;
        }

        public Criteria andRegisterStopEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("register_stop = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterStopNotEqualTo(Date value) {
            addCriterion("register_stop <>", value, "registerStop");
            return (Criteria) this;
        }

        public Criteria andRegisterStopNotEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("register_stop <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterStopGreaterThan(Date value) {
            addCriterion("register_stop >", value, "registerStop");
            return (Criteria) this;
        }

        public Criteria andRegisterStopGreaterThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("register_stop > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterStopGreaterThanOrEqualTo(Date value) {
            addCriterion("register_stop >=", value, "registerStop");
            return (Criteria) this;
        }

        public Criteria andRegisterStopGreaterThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("register_stop >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterStopLessThan(Date value) {
            addCriterion("register_stop <", value, "registerStop");
            return (Criteria) this;
        }

        public Criteria andRegisterStopLessThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("register_stop < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterStopLessThanOrEqualTo(Date value) {
            addCriterion("register_stop <=", value, "registerStop");
            return (Criteria) this;
        }

        public Criteria andRegisterStopLessThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("register_stop <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegisterStopIn(List<Date> values) {
            addCriterion("register_stop in", values, "registerStop");
            return (Criteria) this;
        }

        public Criteria andRegisterStopNotIn(List<Date> values) {
            addCriterion("register_stop not in", values, "registerStop");
            return (Criteria) this;
        }

        public Criteria andRegisterStopBetween(Date value1, Date value2) {
            addCriterion("register_stop between", value1, value2, "registerStop");
            return (Criteria) this;
        }

        public Criteria andRegisterStopNotBetween(Date value1, Date value2) {
            addCriterion("register_stop not between", value1, value2, "registerStop");
            return (Criteria) this;
        }

        public Criteria andMaxPlayerIsNull() {
            addCriterion("max_player is null");
            return (Criteria) this;
        }

        public Criteria andMaxPlayerIsNotNull() {
            addCriterion("max_player is not null");
            return (Criteria) this;
        }

        public Criteria andMaxPlayerEqualTo(Long value) {
            addCriterion("max_player =", value, "maxPlayer");
            return (Criteria) this;
        }

        public Criteria andMaxPlayerEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("max_player = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaxPlayerNotEqualTo(Long value) {
            addCriterion("max_player <>", value, "maxPlayer");
            return (Criteria) this;
        }

        public Criteria andMaxPlayerNotEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("max_player <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaxPlayerGreaterThan(Long value) {
            addCriterion("max_player >", value, "maxPlayer");
            return (Criteria) this;
        }

        public Criteria andMaxPlayerGreaterThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("max_player > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaxPlayerGreaterThanOrEqualTo(Long value) {
            addCriterion("max_player >=", value, "maxPlayer");
            return (Criteria) this;
        }

        public Criteria andMaxPlayerGreaterThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("max_player >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaxPlayerLessThan(Long value) {
            addCriterion("max_player <", value, "maxPlayer");
            return (Criteria) this;
        }

        public Criteria andMaxPlayerLessThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("max_player < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaxPlayerLessThanOrEqualTo(Long value) {
            addCriterion("max_player <=", value, "maxPlayer");
            return (Criteria) this;
        }

        public Criteria andMaxPlayerLessThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("max_player <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaxPlayerIn(List<Long> values) {
            addCriterion("max_player in", values, "maxPlayer");
            return (Criteria) this;
        }

        public Criteria andMaxPlayerNotIn(List<Long> values) {
            addCriterion("max_player not in", values, "maxPlayer");
            return (Criteria) this;
        }

        public Criteria andMaxPlayerBetween(Long value1, Long value2) {
            addCriterion("max_player between", value1, value2, "maxPlayer");
            return (Criteria) this;
        }

        public Criteria andMaxPlayerNotBetween(Long value1, Long value2) {
            addCriterion("max_player not between", value1, value2, "maxPlayer");
            return (Criteria) this;
        }

        public Criteria andSloganIsNull() {
            addCriterion("slogan is null");
            return (Criteria) this;
        }

        public Criteria andSloganIsNotNull() {
            addCriterion("slogan is not null");
            return (Criteria) this;
        }

        public Criteria andSloganEqualTo(String value) {
            addCriterion("slogan =", value, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("slogan = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSloganNotEqualTo(String value) {
            addCriterion("slogan <>", value, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganNotEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("slogan <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSloganGreaterThan(String value) {
            addCriterion("slogan >", value, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganGreaterThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("slogan > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSloganGreaterThanOrEqualTo(String value) {
            addCriterion("slogan >=", value, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganGreaterThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("slogan >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSloganLessThan(String value) {
            addCriterion("slogan <", value, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganLessThanColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("slogan < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSloganLessThanOrEqualTo(String value) {
            addCriterion("slogan <=", value, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganLessThanOrEqualToColumn(MiniProgramActivityWeeklyFun.Column column) {
            addCriterion(new StringBuilder("slogan <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSloganLike(String value) {
            addCriterion("slogan like", value, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganNotLike(String value) {
            addCriterion("slogan not like", value, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganIn(List<String> values) {
            addCriterion("slogan in", values, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganNotIn(List<String> values) {
            addCriterion("slogan not in", values, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganBetween(String value1, String value2) {
            addCriterion("slogan between", value1, value2, "slogan");
            return (Criteria) this;
        }

        public Criteria andSloganNotBetween(String value1, String value2) {
            addCriterion("slogan not between", value1, value2, "slogan");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andActivityIdLikeInsensitive(String value) {
            addCriterion("upper(activity_id) like", value.toUpperCase(), "activityId");
            return (Criteria) this;
        }

        public Criteria andRuleLikeInsensitive(String value) {
            addCriterion("upper(rule) like", value.toUpperCase(), "rule");
            return (Criteria) this;
        }

        public Criteria andDescriptionLikeInsensitive(String value) {
            addCriterion("upper(description) like", value.toUpperCase(), "description");
            return (Criteria) this;
        }

        public Criteria andSloganLikeInsensitive(String value) {
            addCriterion("upper(slogan) like", value.toUpperCase(), "slogan");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Thu Nov 28 17:54:55 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        private MiniProgramActivityWeeklyFunExample example;

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        protected Criteria(MiniProgramActivityWeeklyFunExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        public MiniProgramActivityWeeklyFunExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Thu Nov 28 17:54:55 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Thu Nov 28 17:54:55 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Thu Nov 28 17:54:55 CST 2024
         */
        void example(com.chinamobile.retail.pojo.entity.MiniProgramActivityWeeklyFunExample example);
    }
}