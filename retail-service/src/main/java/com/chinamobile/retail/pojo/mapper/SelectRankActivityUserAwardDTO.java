package com.chinamobile.retail.pojo.mapper;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/23 15:02
 * @description TODO
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class SelectRankActivityUserAwardDTO extends BasePageQuery {

    private String activityId;
    private Date startTime;
    private Date stopTime;
    private Date confirmTime;
    private List<String> offeringClasses;
    private List<String> spuCodes;
    private List<String> orderTypes;
    private List<String> businessCodes;
    private Integer sortType;

}
