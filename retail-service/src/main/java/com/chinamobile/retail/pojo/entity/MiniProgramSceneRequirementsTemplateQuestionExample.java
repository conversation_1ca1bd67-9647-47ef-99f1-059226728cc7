package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.List;

public class MiniProgramSceneRequirementsTemplateQuestionExample {
    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public MiniProgramSceneRequirementsTemplateQuestionExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public MiniProgramSceneRequirementsTemplateQuestionExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public MiniProgramSceneRequirementsTemplateQuestionExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        MiniProgramSceneRequirementsTemplateQuestionExample example = new MiniProgramSceneRequirementsTemplateQuestionExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public MiniProgramSceneRequirementsTemplateQuestionExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public MiniProgramSceneRequirementsTemplateQuestionExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNull() {
            addCriterion("template_id is null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNotNull() {
            addCriterion("template_id is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdEqualTo(String value) {
            addCriterion("template_id =", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("template_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotEqualTo(String value) {
            addCriterion("template_id <>", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("template_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThan(String value) {
            addCriterion("template_id >", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("template_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanOrEqualTo(String value) {
            addCriterion("template_id >=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanOrEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("template_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThan(String value) {
            addCriterion("template_id <", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("template_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanOrEqualTo(String value) {
            addCriterion("template_id <=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanOrEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("template_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdLike(String value) {
            addCriterion("template_id like", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotLike(String value) {
            addCriterion("template_id not like", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIn(List<String> values) {
            addCriterion("template_id in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotIn(List<String> values) {
            addCriterion("template_id not in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdBetween(String value1, String value2) {
            addCriterion("template_id between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotBetween(String value1, String value2) {
            addCriterion("template_id not between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andQuestionIsNull() {
            addCriterion("question is null");
            return (Criteria) this;
        }

        public Criteria andQuestionIsNotNull() {
            addCriterion("question is not null");
            return (Criteria) this;
        }

        public Criteria andQuestionEqualTo(String value) {
            addCriterion("question =", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("question = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuestionNotEqualTo(String value) {
            addCriterion("question <>", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionNotEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("question <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuestionGreaterThan(String value) {
            addCriterion("question >", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionGreaterThanColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("question > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuestionGreaterThanOrEqualTo(String value) {
            addCriterion("question >=", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionGreaterThanOrEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("question >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuestionLessThan(String value) {
            addCriterion("question <", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionLessThanColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("question < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuestionLessThanOrEqualTo(String value) {
            addCriterion("question <=", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionLessThanOrEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("question <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuestionLike(String value) {
            addCriterion("question like", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionNotLike(String value) {
            addCriterion("question not like", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionIn(List<String> values) {
            addCriterion("question in", values, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionNotIn(List<String> values) {
            addCriterion("question not in", values, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionBetween(String value1, String value2) {
            addCriterion("question between", value1, value2, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionNotBetween(String value1, String value2) {
            addCriterion("question not between", value1, value2, "question");
            return (Criteria) this;
        }

        public Criteria andHintIsNull() {
            addCriterion("hint is null");
            return (Criteria) this;
        }

        public Criteria andHintIsNotNull() {
            addCriterion("hint is not null");
            return (Criteria) this;
        }

        public Criteria andHintEqualTo(String value) {
            addCriterion("hint =", value, "hint");
            return (Criteria) this;
        }

        public Criteria andHintEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("hint = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHintNotEqualTo(String value) {
            addCriterion("hint <>", value, "hint");
            return (Criteria) this;
        }

        public Criteria andHintNotEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("hint <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHintGreaterThan(String value) {
            addCriterion("hint >", value, "hint");
            return (Criteria) this;
        }

        public Criteria andHintGreaterThanColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("hint > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHintGreaterThanOrEqualTo(String value) {
            addCriterion("hint >=", value, "hint");
            return (Criteria) this;
        }

        public Criteria andHintGreaterThanOrEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("hint >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHintLessThan(String value) {
            addCriterion("hint <", value, "hint");
            return (Criteria) this;
        }

        public Criteria andHintLessThanColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("hint < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHintLessThanOrEqualTo(String value) {
            addCriterion("hint <=", value, "hint");
            return (Criteria) this;
        }

        public Criteria andHintLessThanOrEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("hint <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHintLike(String value) {
            addCriterion("hint like", value, "hint");
            return (Criteria) this;
        }

        public Criteria andHintNotLike(String value) {
            addCriterion("hint not like", value, "hint");
            return (Criteria) this;
        }

        public Criteria andHintIn(List<String> values) {
            addCriterion("hint in", values, "hint");
            return (Criteria) this;
        }

        public Criteria andHintNotIn(List<String> values) {
            addCriterion("hint not in", values, "hint");
            return (Criteria) this;
        }

        public Criteria andHintBetween(String value1, String value2) {
            addCriterion("hint between", value1, value2, "hint");
            return (Criteria) this;
        }

        public Criteria andHintNotBetween(String value1, String value2) {
            addCriterion("hint not between", value1, value2, "hint");
            return (Criteria) this;
        }

        public Criteria andRequiredIsNull() {
            addCriterion("required is null");
            return (Criteria) this;
        }

        public Criteria andRequiredIsNotNull() {
            addCriterion("required is not null");
            return (Criteria) this;
        }

        public Criteria andRequiredEqualTo(Boolean value) {
            addCriterion("required =", value, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("required = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequiredNotEqualTo(Boolean value) {
            addCriterion("required <>", value, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredNotEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("required <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequiredGreaterThan(Boolean value) {
            addCriterion("required >", value, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredGreaterThanColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("required > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequiredGreaterThanOrEqualTo(Boolean value) {
            addCriterion("required >=", value, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredGreaterThanOrEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("required >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequiredLessThan(Boolean value) {
            addCriterion("required <", value, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredLessThanColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("required < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequiredLessThanOrEqualTo(Boolean value) {
            addCriterion("required <=", value, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredLessThanOrEqualToColumn(MiniProgramSceneRequirementsTemplateQuestion.Column column) {
            addCriterion(new StringBuilder("required <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequiredIn(List<Boolean> values) {
            addCriterion("required in", values, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredNotIn(List<Boolean> values) {
            addCriterion("required not in", values, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredBetween(Boolean value1, Boolean value2) {
            addCriterion("required between", value1, value2, "required");
            return (Criteria) this;
        }

        public Criteria andRequiredNotBetween(Boolean value1, Boolean value2) {
            addCriterion("required not between", value1, value2, "required");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLikeInsensitive(String value) {
            addCriterion("upper(template_id) like", value.toUpperCase(), "templateId");
            return (Criteria) this;
        }

        public Criteria andQuestionLikeInsensitive(String value) {
            addCriterion("upper(question) like", value.toUpperCase(), "question");
            return (Criteria) this;
        }

        public Criteria andHintLikeInsensitive(String value) {
            addCriterion("upper(hint) like", value.toUpperCase(), "hint");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Mon Dec 16 16:44:00 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        private MiniProgramSceneRequirementsTemplateQuestionExample example;

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        protected Criteria(MiniProgramSceneRequirementsTemplateQuestionExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        public MiniProgramSceneRequirementsTemplateQuestionExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Mon Dec 16 16:44:00 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Mon Dec 16 16:44:00 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Mon Dec 16 16:44:00 CST 2024
         */
        void example(com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirementsTemplateQuestionExample example);
    }
}