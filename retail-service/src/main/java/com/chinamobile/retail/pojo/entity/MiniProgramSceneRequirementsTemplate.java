package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 小程序场景需求模版
 *
 * <AUTHOR>
public class MiniProgramSceneRequirementsTemplate implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    private String id;

    /**
     * 模版名称
     *
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    private String name;

    /**
     * 创建人用户id
     *
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    private String createUid;

    /**
     * 是否已删除，0-否，1-是
     *
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    private Boolean deleted;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements_template.id
     *
     * @return the value of supply_chain..mini_program_scene_requirements_template.id
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    public MiniProgramSceneRequirementsTemplate withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements_template.id
     *
     * @param id the value for supply_chain..mini_program_scene_requirements_template.id
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements_template.name
     *
     * @return the value of supply_chain..mini_program_scene_requirements_template.name
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    public MiniProgramSceneRequirementsTemplate withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements_template.name
     *
     * @param name the value for supply_chain..mini_program_scene_requirements_template.name
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements_template.create_uid
     *
     * @return the value of supply_chain..mini_program_scene_requirements_template.create_uid
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    public String getCreateUid() {
        return createUid;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    public MiniProgramSceneRequirementsTemplate withCreateUid(String createUid) {
        this.setCreateUid(createUid);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements_template.create_uid
     *
     * @param createUid the value for supply_chain..mini_program_scene_requirements_template.create_uid
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    public void setCreateUid(String createUid) {
        this.createUid = createUid;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements_template.deleted
     *
     * @return the value of supply_chain..mini_program_scene_requirements_template.deleted
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    public Boolean getDeleted() {
        return deleted;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    public MiniProgramSceneRequirementsTemplate withDeleted(Boolean deleted) {
        this.setDeleted(deleted);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements_template.deleted
     *
     * @param deleted the value for supply_chain..mini_program_scene_requirements_template.deleted
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements_template.create_time
     *
     * @return the value of supply_chain..mini_program_scene_requirements_template.create_time
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    public MiniProgramSceneRequirementsTemplate withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements_template.create_time
     *
     * @param createTime the value for supply_chain..mini_program_scene_requirements_template.create_time
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_scene_requirements_template.update_time
     *
     * @return the value of supply_chain..mini_program_scene_requirements_template.update_time
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    public MiniProgramSceneRequirementsTemplate withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_scene_requirements_template.update_time
     *
     * @param updateTime the value for supply_chain..mini_program_scene_requirements_template.update_time
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", createUid=").append(createUid);
        sb.append(", deleted=").append(deleted);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramSceneRequirementsTemplate other = (MiniProgramSceneRequirementsTemplate) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getCreateUid() == null ? other.getCreateUid() == null : this.getCreateUid().equals(other.getCreateUid()))
            && (this.getDeleted() == null ? other.getDeleted() == null : this.getDeleted().equals(other.getDeleted()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getCreateUid() == null) ? 0 : getCreateUid().hashCode());
        result = prime * result + ((getDeleted() == null) ? 0 : getDeleted().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Dec 16 10:38:45 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        createUid("create_uid", "createUid", "VARCHAR", false),
        deleted("deleted", "deleted", "BIT", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:45 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:45 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:45 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:45 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:45 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:45 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:45 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:45 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:45 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:45 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:45 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:45 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:45 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:45 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:45 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:45 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Dec 16 10:38:45 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}