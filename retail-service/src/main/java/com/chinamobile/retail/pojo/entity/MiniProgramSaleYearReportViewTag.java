package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 年度报告用户访问标记
 *
 * <AUTHOR>
public class MiniProgramSaleYearReportViewTag implements Serializable {
    /**
     * id
     *
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    private String id;

    /**
     * 用户id
     *
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    private String userId;

    /**
     * 年份
     *
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    private Integer year;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    private Date createTime;

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report_view_tag.id
     *
     * @return the value of supply_chain..mini_program_sale_year_report_view_tag.id
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    public MiniProgramSaleYearReportViewTag withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report_view_tag.id
     *
     * @param id the value for supply_chain..mini_program_sale_year_report_view_tag.id
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report_view_tag.user_id
     *
     * @return the value of supply_chain..mini_program_sale_year_report_view_tag.user_id
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    public MiniProgramSaleYearReportViewTag withUserId(String userId) {
        this.setUserId(userId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report_view_tag.user_id
     *
     * @param userId the value for supply_chain..mini_program_sale_year_report_view_tag.user_id
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report_view_tag.year
     *
     * @return the value of supply_chain..mini_program_sale_year_report_view_tag.year
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    public Integer getYear() {
        return year;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    public MiniProgramSaleYearReportViewTag withYear(Integer year) {
        this.setYear(year);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report_view_tag.year
     *
     * @param year the value for supply_chain..mini_program_sale_year_report_view_tag.year
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    public void setYear(Integer year) {
        this.year = year;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_sale_year_report_view_tag.create_time
     *
     * @return the value of supply_chain..mini_program_sale_year_report_view_tag.create_time
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    public MiniProgramSaleYearReportViewTag withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_sale_year_report_view_tag.create_time
     *
     * @param createTime the value for supply_chain..mini_program_sale_year_report_view_tag.create_time
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", userId=").append(userId);
        sb.append(", year=").append(year);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramSaleYearReportViewTag other = (MiniProgramSaleYearReportViewTag) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getYear() == null ? other.getYear() == null : this.getYear().equals(other.getYear()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()));
    }

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getYear() == null) ? 0 : getYear().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Thu Jan 02 10:26:28 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        userId("user_id", "userId", "VARCHAR", false),
        year("year", "year", "INTEGER", false),
        createTime("create_time", "createTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Thu Jan 02 10:26:28 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Jan 02 10:26:28 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Jan 02 10:26:28 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Thu Jan 02 10:26:28 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Thu Jan 02 10:26:28 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Thu Jan 02 10:26:28 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Thu Jan 02 10:26:28 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Jan 02 10:26:28 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Jan 02 10:26:28 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Thu Jan 02 10:26:28 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Thu Jan 02 10:26:28 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Thu Jan 02 10:26:28 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Thu Jan 02 10:26:28 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Thu Jan 02 10:26:28 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Thu Jan 02 10:26:28 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Thu Jan 02 10:26:28 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Thu Jan 02 10:26:28 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}