package com.chinamobile.retail.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 素材要求上架表
 *
 * <AUTHOR>
public class MiniProgramInfoRequestOnline implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    private String id;

    /**
     * 素材id
     *
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    private String infoId;

    /**
     * 用户ID
     *
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    private String userId;

    /**
     * 手机号
     *
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    private String phone;

    /**
     * 省编码
     *
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    private String provinceCode;

    /**
     * 城市编码
     *
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    private String cityCode;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..mini_program_info_request_online.id
     *
     * @return the value of supply_chain..mini_program_info_request_online.id
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public MiniProgramInfoRequestOnline withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info_request_online.id
     *
     * @param id the value for supply_chain..mini_program_info_request_online.id
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info_request_online.info_id
     *
     * @return the value of supply_chain..mini_program_info_request_online.info_id
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public String getInfoId() {
        return infoId;
    }

    /**
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public MiniProgramInfoRequestOnline withInfoId(String infoId) {
        this.setInfoId(infoId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info_request_online.info_id
     *
     * @param infoId the value for supply_chain..mini_program_info_request_online.info_id
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public void setInfoId(String infoId) {
        this.infoId = infoId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info_request_online.user_id
     *
     * @return the value of supply_chain..mini_program_info_request_online.user_id
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public MiniProgramInfoRequestOnline withUserId(String userId) {
        this.setUserId(userId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info_request_online.user_id
     *
     * @param userId the value for supply_chain..mini_program_info_request_online.user_id
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info_request_online.phone
     *
     * @return the value of supply_chain..mini_program_info_request_online.phone
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public String getPhone() {
        return phone;
    }

    /**
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public MiniProgramInfoRequestOnline withPhone(String phone) {
        this.setPhone(phone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info_request_online.phone
     *
     * @param phone the value for supply_chain..mini_program_info_request_online.phone
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info_request_online.province_code
     *
     * @return the value of supply_chain..mini_program_info_request_online.province_code
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public MiniProgramInfoRequestOnline withProvinceCode(String provinceCode) {
        this.setProvinceCode(provinceCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info_request_online.province_code
     *
     * @param provinceCode the value for supply_chain..mini_program_info_request_online.province_code
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info_request_online.city_code
     *
     * @return the value of supply_chain..mini_program_info_request_online.city_code
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public MiniProgramInfoRequestOnline withCityCode(String cityCode) {
        this.setCityCode(cityCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info_request_online.city_code
     *
     * @param cityCode the value for supply_chain..mini_program_info_request_online.city_code
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info_request_online.create_time
     *
     * @return the value of supply_chain..mini_program_info_request_online.create_time
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public MiniProgramInfoRequestOnline withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info_request_online.create_time
     *
     * @param createTime the value for supply_chain..mini_program_info_request_online.create_time
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..mini_program_info_request_online.update_time
     *
     * @return the value of supply_chain..mini_program_info_request_online.update_time
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public MiniProgramInfoRequestOnline withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..mini_program_info_request_online.update_time
     *
     * @param updateTime the value for supply_chain..mini_program_info_request_online.update_time
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", infoId=").append(infoId);
        sb.append(", userId=").append(userId);
        sb.append(", phone=").append(phone);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MiniProgramInfoRequestOnline other = (MiniProgramInfoRequestOnline) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getInfoId() == null ? other.getInfoId() == null : this.getInfoId().equals(other.getInfoId()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getPhone() == null ? other.getPhone() == null : this.getPhone().equals(other.getPhone()))
            && (this.getProvinceCode() == null ? other.getProvinceCode() == null : this.getProvinceCode().equals(other.getProvinceCode()))
            && (this.getCityCode() == null ? other.getCityCode() == null : this.getCityCode().equals(other.getCityCode()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getInfoId() == null) ? 0 : getInfoId().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getPhone() == null) ? 0 : getPhone().hashCode());
        result = prime * result + ((getProvinceCode() == null) ? 0 : getProvinceCode().hashCode());
        result = prime * result + ((getCityCode() == null) ? 0 : getCityCode().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Sat Jan 04 17:28:59 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        infoId("info_id", "infoId", "VARCHAR", false),
        userId("user_id", "userId", "VARCHAR", false),
        phone("phone", "phone", "VARCHAR", false),
        provinceCode("province_code", "provinceCode", "VARCHAR", false),
        cityCode("city_code", "cityCode", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Sat Jan 04 17:28:59 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Sat Jan 04 17:28:59 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Sat Jan 04 17:28:59 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Sat Jan 04 17:28:59 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Sat Jan 04 17:28:59 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Sat Jan 04 17:28:59 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Sat Jan 04 17:28:59 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Sat Jan 04 17:28:59 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Sat Jan 04 17:28:59 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Sat Jan 04 17:28:59 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Sat Jan 04 17:28:59 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Sat Jan 04 17:28:59 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Sat Jan 04 17:28:59 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Sat Jan 04 17:28:59 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Sat Jan 04 17:28:59 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Sat Jan 04 17:28:59 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Sat Jan 04 17:28:59 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}