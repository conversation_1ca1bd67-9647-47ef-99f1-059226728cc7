package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 
 *
 * <AUTHOR>
public class Order2cAtomInfo {
    /**
     * 原子商品订单信息
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String id;

    /**
     * 订单id
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String orderId;

    /**
     * 订单类型00：代客下单（商城直销订单） 01：自主下单 02：代客下单（省内融合集团客户订单） 03:代客下单（省内融合个人客户订单）
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String orderType;

    /**
     * spu code
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String spuOfferingCode;

    /**
     * 规格商品编码
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String skuOfferingCode;

    /**
     * 商品规格名
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String skuOfferingName;

    /**
     * 订购数量,下单时用户填写的规格商品订购数量
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private Long skuQuantity;

    /**
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private Long skuPrice;

    /**
     * 营销案名称
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String marketName;

    /**
     * 营销案编码
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String marketCode;

    /**
     * 供应商名称
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String supplierName;

    /**
     * 颜色
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String color;

    /**
     * 类型
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String model;

    /**
     * 原子商品类型
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String atomOfferingClass;

    /**
     * 原子商品编码
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String atomOfferingCode;

    /**
     * 原子商品名称
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String atomOfferingName;

    /**
     * 抵扣金额，单位：厘
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String deductPrice;

    /**
     * 订单列表的单价，单位：厘
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private Long atomPrice;

    /**
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private Long atomSettlePrice;

    /**
     * 规格配置的原子商品的数量
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private Long atomQuantity;

    /**
     * 订单状态，0 待发货、1 待收货、2 已收货、3 开票、4 退款中、
5 退货退款中、6 换货中、7 交易完成、8 交易失败 、9 订单部分退款成功、10 待接单（代客下单）、11 待省侧审批（代客下单）、12 待出账（仅代客下单)、13 草稿单、14 草稿单删除、15 制卡中、16 待交付（仅代客下单）

     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private Integer orderStatus;

    /**
     * 合作伙伴ID
考虑到后期可能会更换合作伙伴无法通过原子商品信息去匹配订单。
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String cooperatorId;

    /**
     * 终结时的合作伙伴，对于已完成订单换绑合作伙伴，cooperator_id保存新的合作伙伴，finish_cooperator_id保存订单完成时的合作伙伴；对于在途订单，同时修改cooperator_id和finish_cooperator_id为新的合作伙伴。查询时以cooperator_id为条件，finish_cooperator_id用于展示
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String finishCooperatorId;

    /**
     * 省编码
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String beId;

    /**
     * 区域编码
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String regionId;

    /**
     * 异常处理信息主键
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String exHandleId;

    /**
     * 卡服务商名称
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String skuCardName;

    /**
     * 正式服务号码
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String skuMsisdn;

    /**
     * 接单状态 1--接单 2--拒单
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private Integer allowOrderStatus;

    /**
     * 拒绝接单的原因
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String allowOrderFailureReason;

    /**
     * 是否部分退款 1--是
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private Integer partReturn;

    /**
     * 保理状态 1--可用 2--不可用
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private Integer baoliStatus;

    /**
     * 订单创建时间 取IOT商城传递过来的
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private Date updateTime;

    /**
     * 代客下单订单的待出账时间
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String valetOrderCompleteTime;

    /**
     * 订购结果  0--开通失败  1--开通成功  2--退订失败 3--退订成功
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private Integer carOpenStatus;

    /**
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String spuOfferingVersion;

    /**
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String skuOfferingVersion;

    /**
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private String atomOfferingVersion;

    /**
     * 软件服务状态，0-开通成功，1-开通失败， 2-开通中， 3-退订成功， 4-退订失败， 5-退订中， 6-使用中
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private Integer softServiceStatus;

    /**
     * 结算状态，0-销售草稿（锁定），1-销售草稿， 2-销售审批中， 3-销售已审批， 4-计收完成， 5-订单取消 6--待发起 7--草稿单 8--审批中 9--已审批 10--取消中 11--已取消  12--同步失败
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private Integer settleStatus;

    /**
     * 同步给市场销售系统的时间
     *
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    private Date billNoTime;

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.id
     *
     * @return the value of supply_chain..order_2c_atom_info.id
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.id
     *
     * @param id the value for supply_chain..order_2c_atom_info.id
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.order_id
     *
     * @return the value of supply_chain..order_2c_atom_info.order_id
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.order_id
     *
     * @param orderId the value for supply_chain..order_2c_atom_info.order_id
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.order_type
     *
     * @return the value of supply_chain..order_2c_atom_info.order_type
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getOrderType() {
        return orderType;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withOrderType(String orderType) {
        this.setOrderType(orderType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.order_type
     *
     * @param orderType the value for supply_chain..order_2c_atom_info.order_type
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setOrderType(String orderType) {
        this.orderType = orderType == null ? null : orderType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.spu_offering_code
     *
     * @return the value of supply_chain..order_2c_atom_info.spu_offering_code
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getSpuOfferingCode() {
        return spuOfferingCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withSpuOfferingCode(String spuOfferingCode) {
        this.setSpuOfferingCode(spuOfferingCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.spu_offering_code
     *
     * @param spuOfferingCode the value for supply_chain..order_2c_atom_info.spu_offering_code
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setSpuOfferingCode(String spuOfferingCode) {
        this.spuOfferingCode = spuOfferingCode == null ? null : spuOfferingCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.sku_offering_code
     *
     * @return the value of supply_chain..order_2c_atom_info.sku_offering_code
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getSkuOfferingCode() {
        return skuOfferingCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withSkuOfferingCode(String skuOfferingCode) {
        this.setSkuOfferingCode(skuOfferingCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.sku_offering_code
     *
     * @param skuOfferingCode the value for supply_chain..order_2c_atom_info.sku_offering_code
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setSkuOfferingCode(String skuOfferingCode) {
        this.skuOfferingCode = skuOfferingCode == null ? null : skuOfferingCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.sku_offering_name
     *
     * @return the value of supply_chain..order_2c_atom_info.sku_offering_name
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getSkuOfferingName() {
        return skuOfferingName;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withSkuOfferingName(String skuOfferingName) {
        this.setSkuOfferingName(skuOfferingName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.sku_offering_name
     *
     * @param skuOfferingName the value for supply_chain..order_2c_atom_info.sku_offering_name
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setSkuOfferingName(String skuOfferingName) {
        this.skuOfferingName = skuOfferingName == null ? null : skuOfferingName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.sku_quantity
     *
     * @return the value of supply_chain..order_2c_atom_info.sku_quantity
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Long getSkuQuantity() {
        return skuQuantity;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withSkuQuantity(Long skuQuantity) {
        this.setSkuQuantity(skuQuantity);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.sku_quantity
     *
     * @param skuQuantity the value for supply_chain..order_2c_atom_info.sku_quantity
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setSkuQuantity(Long skuQuantity) {
        this.skuQuantity = skuQuantity;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.sku_price
     *
     * @return the value of supply_chain..order_2c_atom_info.sku_price
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Long getSkuPrice() {
        return skuPrice;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withSkuPrice(Long skuPrice) {
        this.setSkuPrice(skuPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.sku_price
     *
     * @param skuPrice the value for supply_chain..order_2c_atom_info.sku_price
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setSkuPrice(Long skuPrice) {
        this.skuPrice = skuPrice;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.market_name
     *
     * @return the value of supply_chain..order_2c_atom_info.market_name
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getMarketName() {
        return marketName;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withMarketName(String marketName) {
        this.setMarketName(marketName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.market_name
     *
     * @param marketName the value for supply_chain..order_2c_atom_info.market_name
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setMarketName(String marketName) {
        this.marketName = marketName == null ? null : marketName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.market_code
     *
     * @return the value of supply_chain..order_2c_atom_info.market_code
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getMarketCode() {
        return marketCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withMarketCode(String marketCode) {
        this.setMarketCode(marketCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.market_code
     *
     * @param marketCode the value for supply_chain..order_2c_atom_info.market_code
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setMarketCode(String marketCode) {
        this.marketCode = marketCode == null ? null : marketCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.supplier_name
     *
     * @return the value of supply_chain..order_2c_atom_info.supplier_name
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getSupplierName() {
        return supplierName;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withSupplierName(String supplierName) {
        this.setSupplierName(supplierName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.supplier_name
     *
     * @param supplierName the value for supply_chain..order_2c_atom_info.supplier_name
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName == null ? null : supplierName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.color
     *
     * @return the value of supply_chain..order_2c_atom_info.color
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getColor() {
        return color;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withColor(String color) {
        this.setColor(color);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.color
     *
     * @param color the value for supply_chain..order_2c_atom_info.color
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setColor(String color) {
        this.color = color == null ? null : color.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.model
     *
     * @return the value of supply_chain..order_2c_atom_info.model
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getModel() {
        return model;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withModel(String model) {
        this.setModel(model);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.model
     *
     * @param model the value for supply_chain..order_2c_atom_info.model
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.atom_offering_class
     *
     * @return the value of supply_chain..order_2c_atom_info.atom_offering_class
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getAtomOfferingClass() {
        return atomOfferingClass;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withAtomOfferingClass(String atomOfferingClass) {
        this.setAtomOfferingClass(atomOfferingClass);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.atom_offering_class
     *
     * @param atomOfferingClass the value for supply_chain..order_2c_atom_info.atom_offering_class
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setAtomOfferingClass(String atomOfferingClass) {
        this.atomOfferingClass = atomOfferingClass == null ? null : atomOfferingClass.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.atom_offering_code
     *
     * @return the value of supply_chain..order_2c_atom_info.atom_offering_code
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getAtomOfferingCode() {
        return atomOfferingCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withAtomOfferingCode(String atomOfferingCode) {
        this.setAtomOfferingCode(atomOfferingCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.atom_offering_code
     *
     * @param atomOfferingCode the value for supply_chain..order_2c_atom_info.atom_offering_code
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setAtomOfferingCode(String atomOfferingCode) {
        this.atomOfferingCode = atomOfferingCode == null ? null : atomOfferingCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.atom_offering_name
     *
     * @return the value of supply_chain..order_2c_atom_info.atom_offering_name
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getAtomOfferingName() {
        return atomOfferingName;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withAtomOfferingName(String atomOfferingName) {
        this.setAtomOfferingName(atomOfferingName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.atom_offering_name
     *
     * @param atomOfferingName the value for supply_chain..order_2c_atom_info.atom_offering_name
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setAtomOfferingName(String atomOfferingName) {
        this.atomOfferingName = atomOfferingName == null ? null : atomOfferingName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.deduct_price
     *
     * @return the value of supply_chain..order_2c_atom_info.deduct_price
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getDeductPrice() {
        return deductPrice;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withDeductPrice(String deductPrice) {
        this.setDeductPrice(deductPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.deduct_price
     *
     * @param deductPrice the value for supply_chain..order_2c_atom_info.deduct_price
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setDeductPrice(String deductPrice) {
        this.deductPrice = deductPrice == null ? null : deductPrice.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.atom_price
     *
     * @return the value of supply_chain..order_2c_atom_info.atom_price
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Long getAtomPrice() {
        return atomPrice;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withAtomPrice(Long atomPrice) {
        this.setAtomPrice(atomPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.atom_price
     *
     * @param atomPrice the value for supply_chain..order_2c_atom_info.atom_price
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setAtomPrice(Long atomPrice) {
        this.atomPrice = atomPrice;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.atom_settle_price
     *
     * @return the value of supply_chain..order_2c_atom_info.atom_settle_price
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Long getAtomSettlePrice() {
        return atomSettlePrice;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withAtomSettlePrice(Long atomSettlePrice) {
        this.setAtomSettlePrice(atomSettlePrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.atom_settle_price
     *
     * @param atomSettlePrice the value for supply_chain..order_2c_atom_info.atom_settle_price
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setAtomSettlePrice(Long atomSettlePrice) {
        this.atomSettlePrice = atomSettlePrice;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.atom_quantity
     *
     * @return the value of supply_chain..order_2c_atom_info.atom_quantity
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Long getAtomQuantity() {
        return atomQuantity;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withAtomQuantity(Long atomQuantity) {
        this.setAtomQuantity(atomQuantity);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.atom_quantity
     *
     * @param atomQuantity the value for supply_chain..order_2c_atom_info.atom_quantity
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setAtomQuantity(Long atomQuantity) {
        this.atomQuantity = atomQuantity;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.order_status
     *
     * @return the value of supply_chain..order_2c_atom_info.order_status
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Integer getOrderStatus() {
        return orderStatus;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withOrderStatus(Integer orderStatus) {
        this.setOrderStatus(orderStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.order_status
     *
     * @param orderStatus the value for supply_chain..order_2c_atom_info.order_status
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.cooperator_id
     *
     * @return the value of supply_chain..order_2c_atom_info.cooperator_id
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getCooperatorId() {
        return cooperatorId;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withCooperatorId(String cooperatorId) {
        this.setCooperatorId(cooperatorId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.cooperator_id
     *
     * @param cooperatorId the value for supply_chain..order_2c_atom_info.cooperator_id
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setCooperatorId(String cooperatorId) {
        this.cooperatorId = cooperatorId == null ? null : cooperatorId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.finish_cooperator_id
     *
     * @return the value of supply_chain..order_2c_atom_info.finish_cooperator_id
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getFinishCooperatorId() {
        return finishCooperatorId;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withFinishCooperatorId(String finishCooperatorId) {
        this.setFinishCooperatorId(finishCooperatorId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.finish_cooperator_id
     *
     * @param finishCooperatorId the value for supply_chain..order_2c_atom_info.finish_cooperator_id
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setFinishCooperatorId(String finishCooperatorId) {
        this.finishCooperatorId = finishCooperatorId == null ? null : finishCooperatorId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.be_id
     *
     * @return the value of supply_chain..order_2c_atom_info.be_id
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getBeId() {
        return beId;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withBeId(String beId) {
        this.setBeId(beId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.be_id
     *
     * @param beId the value for supply_chain..order_2c_atom_info.be_id
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setBeId(String beId) {
        this.beId = beId == null ? null : beId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.region_id
     *
     * @return the value of supply_chain..order_2c_atom_info.region_id
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getRegionId() {
        return regionId;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withRegionId(String regionId) {
        this.setRegionId(regionId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.region_id
     *
     * @param regionId the value for supply_chain..order_2c_atom_info.region_id
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setRegionId(String regionId) {
        this.regionId = regionId == null ? null : regionId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.ex_handle_id
     *
     * @return the value of supply_chain..order_2c_atom_info.ex_handle_id
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getExHandleId() {
        return exHandleId;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withExHandleId(String exHandleId) {
        this.setExHandleId(exHandleId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.ex_handle_id
     *
     * @param exHandleId the value for supply_chain..order_2c_atom_info.ex_handle_id
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setExHandleId(String exHandleId) {
        this.exHandleId = exHandleId == null ? null : exHandleId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.sku_card_name
     *
     * @return the value of supply_chain..order_2c_atom_info.sku_card_name
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getSkuCardName() {
        return skuCardName;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withSkuCardName(String skuCardName) {
        this.setSkuCardName(skuCardName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.sku_card_name
     *
     * @param skuCardName the value for supply_chain..order_2c_atom_info.sku_card_name
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setSkuCardName(String skuCardName) {
        this.skuCardName = skuCardName == null ? null : skuCardName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.sku_msisdn
     *
     * @return the value of supply_chain..order_2c_atom_info.sku_msisdn
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getSkuMsisdn() {
        return skuMsisdn;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withSkuMsisdn(String skuMsisdn) {
        this.setSkuMsisdn(skuMsisdn);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.sku_msisdn
     *
     * @param skuMsisdn the value for supply_chain..order_2c_atom_info.sku_msisdn
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setSkuMsisdn(String skuMsisdn) {
        this.skuMsisdn = skuMsisdn == null ? null : skuMsisdn.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.allow_order_status
     *
     * @return the value of supply_chain..order_2c_atom_info.allow_order_status
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Integer getAllowOrderStatus() {
        return allowOrderStatus;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withAllowOrderStatus(Integer allowOrderStatus) {
        this.setAllowOrderStatus(allowOrderStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.allow_order_status
     *
     * @param allowOrderStatus the value for supply_chain..order_2c_atom_info.allow_order_status
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setAllowOrderStatus(Integer allowOrderStatus) {
        this.allowOrderStatus = allowOrderStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.allow_order_failure_reason
     *
     * @return the value of supply_chain..order_2c_atom_info.allow_order_failure_reason
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getAllowOrderFailureReason() {
        return allowOrderFailureReason;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withAllowOrderFailureReason(String allowOrderFailureReason) {
        this.setAllowOrderFailureReason(allowOrderFailureReason);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.allow_order_failure_reason
     *
     * @param allowOrderFailureReason the value for supply_chain..order_2c_atom_info.allow_order_failure_reason
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setAllowOrderFailureReason(String allowOrderFailureReason) {
        this.allowOrderFailureReason = allowOrderFailureReason == null ? null : allowOrderFailureReason.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.part_return
     *
     * @return the value of supply_chain..order_2c_atom_info.part_return
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Integer getPartReturn() {
        return partReturn;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withPartReturn(Integer partReturn) {
        this.setPartReturn(partReturn);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.part_return
     *
     * @param partReturn the value for supply_chain..order_2c_atom_info.part_return
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setPartReturn(Integer partReturn) {
        this.partReturn = partReturn;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.baoli_status
     *
     * @return the value of supply_chain..order_2c_atom_info.baoli_status
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Integer getBaoliStatus() {
        return baoliStatus;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withBaoliStatus(Integer baoliStatus) {
        this.setBaoliStatus(baoliStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.baoli_status
     *
     * @param baoliStatus the value for supply_chain..order_2c_atom_info.baoli_status
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setBaoliStatus(Integer baoliStatus) {
        this.baoliStatus = baoliStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.create_time
     *
     * @return the value of supply_chain..order_2c_atom_info.create_time
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withCreateTime(String createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.create_time
     *
     * @param createTime the value for supply_chain..order_2c_atom_info.create_time
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setCreateTime(String createTime) {
        this.createTime = createTime == null ? null : createTime.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.update_time
     *
     * @return the value of supply_chain..order_2c_atom_info.update_time
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.update_time
     *
     * @param updateTime the value for supply_chain..order_2c_atom_info.update_time
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.valet_order_complete_time
     *
     * @return the value of supply_chain..order_2c_atom_info.valet_order_complete_time
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getValetOrderCompleteTime() {
        return valetOrderCompleteTime;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withValetOrderCompleteTime(String valetOrderCompleteTime) {
        this.setValetOrderCompleteTime(valetOrderCompleteTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.valet_order_complete_time
     *
     * @param valetOrderCompleteTime the value for supply_chain..order_2c_atom_info.valet_order_complete_time
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setValetOrderCompleteTime(String valetOrderCompleteTime) {
        this.valetOrderCompleteTime = valetOrderCompleteTime == null ? null : valetOrderCompleteTime.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.car_open_status
     *
     * @return the value of supply_chain..order_2c_atom_info.car_open_status
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Integer getCarOpenStatus() {
        return carOpenStatus;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withCarOpenStatus(Integer carOpenStatus) {
        this.setCarOpenStatus(carOpenStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.car_open_status
     *
     * @param carOpenStatus the value for supply_chain..order_2c_atom_info.car_open_status
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setCarOpenStatus(Integer carOpenStatus) {
        this.carOpenStatus = carOpenStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.spu_offering_version
     *
     * @return the value of supply_chain..order_2c_atom_info.spu_offering_version
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getSpuOfferingVersion() {
        return spuOfferingVersion;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withSpuOfferingVersion(String spuOfferingVersion) {
        this.setSpuOfferingVersion(spuOfferingVersion);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.spu_offering_version
     *
     * @param spuOfferingVersion the value for supply_chain..order_2c_atom_info.spu_offering_version
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setSpuOfferingVersion(String spuOfferingVersion) {
        this.spuOfferingVersion = spuOfferingVersion == null ? null : spuOfferingVersion.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.sku_offering_version
     *
     * @return the value of supply_chain..order_2c_atom_info.sku_offering_version
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getSkuOfferingVersion() {
        return skuOfferingVersion;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withSkuOfferingVersion(String skuOfferingVersion) {
        this.setSkuOfferingVersion(skuOfferingVersion);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.sku_offering_version
     *
     * @param skuOfferingVersion the value for supply_chain..order_2c_atom_info.sku_offering_version
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setSkuOfferingVersion(String skuOfferingVersion) {
        this.skuOfferingVersion = skuOfferingVersion == null ? null : skuOfferingVersion.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.atom_offering_version
     *
     * @return the value of supply_chain..order_2c_atom_info.atom_offering_version
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public String getAtomOfferingVersion() {
        return atomOfferingVersion;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withAtomOfferingVersion(String atomOfferingVersion) {
        this.setAtomOfferingVersion(atomOfferingVersion);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.atom_offering_version
     *
     * @param atomOfferingVersion the value for supply_chain..order_2c_atom_info.atom_offering_version
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setAtomOfferingVersion(String atomOfferingVersion) {
        this.atomOfferingVersion = atomOfferingVersion == null ? null : atomOfferingVersion.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.soft_service_status
     *
     * @return the value of supply_chain..order_2c_atom_info.soft_service_status
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Integer getSoftServiceStatus() {
        return softServiceStatus;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withSoftServiceStatus(Integer softServiceStatus) {
        this.setSoftServiceStatus(softServiceStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.soft_service_status
     *
     * @param softServiceStatus the value for supply_chain..order_2c_atom_info.soft_service_status
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setSoftServiceStatus(Integer softServiceStatus) {
        this.softServiceStatus = softServiceStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.settle_status
     *
     * @return the value of supply_chain..order_2c_atom_info.settle_status
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Integer getSettleStatus() {
        return settleStatus;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withSettleStatus(Integer settleStatus) {
        this.setSettleStatus(settleStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.settle_status
     *
     * @param settleStatus the value for supply_chain..order_2c_atom_info.settle_status
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setSettleStatus(Integer settleStatus) {
        this.settleStatus = settleStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_atom_info.bill_no_time
     *
     * @return the value of supply_chain..order_2c_atom_info.bill_no_time
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Date getBillNoTime() {
        return billNoTime;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public Order2cAtomInfo withBillNoTime(Date billNoTime) {
        this.setBillNoTime(billNoTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_atom_info.bill_no_time
     *
     * @param billNoTime the value for supply_chain..order_2c_atom_info.bill_no_time
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public void setBillNoTime(Date billNoTime) {
        this.billNoTime = billNoTime;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderId=").append(orderId);
        sb.append(", orderType=").append(orderType);
        sb.append(", spuOfferingCode=").append(spuOfferingCode);
        sb.append(", skuOfferingCode=").append(skuOfferingCode);
        sb.append(", skuOfferingName=").append(skuOfferingName);
        sb.append(", skuQuantity=").append(skuQuantity);
        sb.append(", skuPrice=").append(skuPrice);
        sb.append(", marketName=").append(marketName);
        sb.append(", marketCode=").append(marketCode);
        sb.append(", supplierName=").append(supplierName);
        sb.append(", color=").append(color);
        sb.append(", model=").append(model);
        sb.append(", atomOfferingClass=").append(atomOfferingClass);
        sb.append(", atomOfferingCode=").append(atomOfferingCode);
        sb.append(", atomOfferingName=").append(atomOfferingName);
        sb.append(", deductPrice=").append(deductPrice);
        sb.append(", atomPrice=").append(atomPrice);
        sb.append(", atomSettlePrice=").append(atomSettlePrice);
        sb.append(", atomQuantity=").append(atomQuantity);
        sb.append(", orderStatus=").append(orderStatus);
        sb.append(", cooperatorId=").append(cooperatorId);
        sb.append(", finishCooperatorId=").append(finishCooperatorId);
        sb.append(", beId=").append(beId);
        sb.append(", regionId=").append(regionId);
        sb.append(", exHandleId=").append(exHandleId);
        sb.append(", skuCardName=").append(skuCardName);
        sb.append(", skuMsisdn=").append(skuMsisdn);
        sb.append(", allowOrderStatus=").append(allowOrderStatus);
        sb.append(", allowOrderFailureReason=").append(allowOrderFailureReason);
        sb.append(", partReturn=").append(partReturn);
        sb.append(", baoliStatus=").append(baoliStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", valetOrderCompleteTime=").append(valetOrderCompleteTime);
        sb.append(", carOpenStatus=").append(carOpenStatus);
        sb.append(", spuOfferingVersion=").append(spuOfferingVersion);
        sb.append(", skuOfferingVersion=").append(skuOfferingVersion);
        sb.append(", atomOfferingVersion=").append(atomOfferingVersion);
        sb.append(", softServiceStatus=").append(softServiceStatus);
        sb.append(", settleStatus=").append(settleStatus);
        sb.append(", billNoTime=").append(billNoTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Order2cAtomInfo other = (Order2cAtomInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getOrderType() == null ? other.getOrderType() == null : this.getOrderType().equals(other.getOrderType()))
            && (this.getSpuOfferingCode() == null ? other.getSpuOfferingCode() == null : this.getSpuOfferingCode().equals(other.getSpuOfferingCode()))
            && (this.getSkuOfferingCode() == null ? other.getSkuOfferingCode() == null : this.getSkuOfferingCode().equals(other.getSkuOfferingCode()))
            && (this.getSkuOfferingName() == null ? other.getSkuOfferingName() == null : this.getSkuOfferingName().equals(other.getSkuOfferingName()))
            && (this.getSkuQuantity() == null ? other.getSkuQuantity() == null : this.getSkuQuantity().equals(other.getSkuQuantity()))
            && (this.getSkuPrice() == null ? other.getSkuPrice() == null : this.getSkuPrice().equals(other.getSkuPrice()))
            && (this.getMarketName() == null ? other.getMarketName() == null : this.getMarketName().equals(other.getMarketName()))
            && (this.getMarketCode() == null ? other.getMarketCode() == null : this.getMarketCode().equals(other.getMarketCode()))
            && (this.getSupplierName() == null ? other.getSupplierName() == null : this.getSupplierName().equals(other.getSupplierName()))
            && (this.getColor() == null ? other.getColor() == null : this.getColor().equals(other.getColor()))
            && (this.getModel() == null ? other.getModel() == null : this.getModel().equals(other.getModel()))
            && (this.getAtomOfferingClass() == null ? other.getAtomOfferingClass() == null : this.getAtomOfferingClass().equals(other.getAtomOfferingClass()))
            && (this.getAtomOfferingCode() == null ? other.getAtomOfferingCode() == null : this.getAtomOfferingCode().equals(other.getAtomOfferingCode()))
            && (this.getAtomOfferingName() == null ? other.getAtomOfferingName() == null : this.getAtomOfferingName().equals(other.getAtomOfferingName()))
            && (this.getDeductPrice() == null ? other.getDeductPrice() == null : this.getDeductPrice().equals(other.getDeductPrice()))
            && (this.getAtomPrice() == null ? other.getAtomPrice() == null : this.getAtomPrice().equals(other.getAtomPrice()))
            && (this.getAtomSettlePrice() == null ? other.getAtomSettlePrice() == null : this.getAtomSettlePrice().equals(other.getAtomSettlePrice()))
            && (this.getAtomQuantity() == null ? other.getAtomQuantity() == null : this.getAtomQuantity().equals(other.getAtomQuantity()))
            && (this.getOrderStatus() == null ? other.getOrderStatus() == null : this.getOrderStatus().equals(other.getOrderStatus()))
            && (this.getCooperatorId() == null ? other.getCooperatorId() == null : this.getCooperatorId().equals(other.getCooperatorId()))
            && (this.getFinishCooperatorId() == null ? other.getFinishCooperatorId() == null : this.getFinishCooperatorId().equals(other.getFinishCooperatorId()))
            && (this.getBeId() == null ? other.getBeId() == null : this.getBeId().equals(other.getBeId()))
            && (this.getRegionId() == null ? other.getRegionId() == null : this.getRegionId().equals(other.getRegionId()))
            && (this.getExHandleId() == null ? other.getExHandleId() == null : this.getExHandleId().equals(other.getExHandleId()))
            && (this.getSkuCardName() == null ? other.getSkuCardName() == null : this.getSkuCardName().equals(other.getSkuCardName()))
            && (this.getSkuMsisdn() == null ? other.getSkuMsisdn() == null : this.getSkuMsisdn().equals(other.getSkuMsisdn()))
            && (this.getAllowOrderStatus() == null ? other.getAllowOrderStatus() == null : this.getAllowOrderStatus().equals(other.getAllowOrderStatus()))
            && (this.getAllowOrderFailureReason() == null ? other.getAllowOrderFailureReason() == null : this.getAllowOrderFailureReason().equals(other.getAllowOrderFailureReason()))
            && (this.getPartReturn() == null ? other.getPartReturn() == null : this.getPartReturn().equals(other.getPartReturn()))
            && (this.getBaoliStatus() == null ? other.getBaoliStatus() == null : this.getBaoliStatus().equals(other.getBaoliStatus()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getValetOrderCompleteTime() == null ? other.getValetOrderCompleteTime() == null : this.getValetOrderCompleteTime().equals(other.getValetOrderCompleteTime()))
            && (this.getCarOpenStatus() == null ? other.getCarOpenStatus() == null : this.getCarOpenStatus().equals(other.getCarOpenStatus()))
            && (this.getSpuOfferingVersion() == null ? other.getSpuOfferingVersion() == null : this.getSpuOfferingVersion().equals(other.getSpuOfferingVersion()))
            && (this.getSkuOfferingVersion() == null ? other.getSkuOfferingVersion() == null : this.getSkuOfferingVersion().equals(other.getSkuOfferingVersion()))
            && (this.getAtomOfferingVersion() == null ? other.getAtomOfferingVersion() == null : this.getAtomOfferingVersion().equals(other.getAtomOfferingVersion()))
            && (this.getSoftServiceStatus() == null ? other.getSoftServiceStatus() == null : this.getSoftServiceStatus().equals(other.getSoftServiceStatus()))
            && (this.getSettleStatus() == null ? other.getSettleStatus() == null : this.getSettleStatus().equals(other.getSettleStatus()))
            && (this.getBillNoTime() == null ? other.getBillNoTime() == null : this.getBillNoTime().equals(other.getBillNoTime()));
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getOrderType() == null) ? 0 : getOrderType().hashCode());
        result = prime * result + ((getSpuOfferingCode() == null) ? 0 : getSpuOfferingCode().hashCode());
        result = prime * result + ((getSkuOfferingCode() == null) ? 0 : getSkuOfferingCode().hashCode());
        result = prime * result + ((getSkuOfferingName() == null) ? 0 : getSkuOfferingName().hashCode());
        result = prime * result + ((getSkuQuantity() == null) ? 0 : getSkuQuantity().hashCode());
        result = prime * result + ((getSkuPrice() == null) ? 0 : getSkuPrice().hashCode());
        result = prime * result + ((getMarketName() == null) ? 0 : getMarketName().hashCode());
        result = prime * result + ((getMarketCode() == null) ? 0 : getMarketCode().hashCode());
        result = prime * result + ((getSupplierName() == null) ? 0 : getSupplierName().hashCode());
        result = prime * result + ((getColor() == null) ? 0 : getColor().hashCode());
        result = prime * result + ((getModel() == null) ? 0 : getModel().hashCode());
        result = prime * result + ((getAtomOfferingClass() == null) ? 0 : getAtomOfferingClass().hashCode());
        result = prime * result + ((getAtomOfferingCode() == null) ? 0 : getAtomOfferingCode().hashCode());
        result = prime * result + ((getAtomOfferingName() == null) ? 0 : getAtomOfferingName().hashCode());
        result = prime * result + ((getDeductPrice() == null) ? 0 : getDeductPrice().hashCode());
        result = prime * result + ((getAtomPrice() == null) ? 0 : getAtomPrice().hashCode());
        result = prime * result + ((getAtomSettlePrice() == null) ? 0 : getAtomSettlePrice().hashCode());
        result = prime * result + ((getAtomQuantity() == null) ? 0 : getAtomQuantity().hashCode());
        result = prime * result + ((getOrderStatus() == null) ? 0 : getOrderStatus().hashCode());
        result = prime * result + ((getCooperatorId() == null) ? 0 : getCooperatorId().hashCode());
        result = prime * result + ((getFinishCooperatorId() == null) ? 0 : getFinishCooperatorId().hashCode());
        result = prime * result + ((getBeId() == null) ? 0 : getBeId().hashCode());
        result = prime * result + ((getRegionId() == null) ? 0 : getRegionId().hashCode());
        result = prime * result + ((getExHandleId() == null) ? 0 : getExHandleId().hashCode());
        result = prime * result + ((getSkuCardName() == null) ? 0 : getSkuCardName().hashCode());
        result = prime * result + ((getSkuMsisdn() == null) ? 0 : getSkuMsisdn().hashCode());
        result = prime * result + ((getAllowOrderStatus() == null) ? 0 : getAllowOrderStatus().hashCode());
        result = prime * result + ((getAllowOrderFailureReason() == null) ? 0 : getAllowOrderFailureReason().hashCode());
        result = prime * result + ((getPartReturn() == null) ? 0 : getPartReturn().hashCode());
        result = prime * result + ((getBaoliStatus() == null) ? 0 : getBaoliStatus().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getValetOrderCompleteTime() == null) ? 0 : getValetOrderCompleteTime().hashCode());
        result = prime * result + ((getCarOpenStatus() == null) ? 0 : getCarOpenStatus().hashCode());
        result = prime * result + ((getSpuOfferingVersion() == null) ? 0 : getSpuOfferingVersion().hashCode());
        result = prime * result + ((getSkuOfferingVersion() == null) ? 0 : getSkuOfferingVersion().hashCode());
        result = prime * result + ((getAtomOfferingVersion() == null) ? 0 : getAtomOfferingVersion().hashCode());
        result = prime * result + ((getSoftServiceStatus() == null) ? 0 : getSoftServiceStatus().hashCode());
        result = prime * result + ((getSettleStatus() == null) ? 0 : getSettleStatus().hashCode());
        result = prime * result + ((getBillNoTime() == null) ? 0 : getBillNoTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Mar 31 15:09:26 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        orderType("order_type", "orderType", "VARCHAR", false),
        spuOfferingCode("spu_offering_code", "spuOfferingCode", "VARCHAR", false),
        skuOfferingCode("sku_offering_code", "skuOfferingCode", "VARCHAR", false),
        skuOfferingName("sku_offering_name", "skuOfferingName", "VARCHAR", false),
        skuQuantity("sku_quantity", "skuQuantity", "BIGINT", false),
        skuPrice("sku_price", "skuPrice", "BIGINT", false),
        marketName("market_name", "marketName", "VARCHAR", false),
        marketCode("market_code", "marketCode", "VARCHAR", false),
        supplierName("supplier_name", "supplierName", "VARCHAR", false),
        color("color", "color", "VARCHAR", false),
        model("model", "model", "VARCHAR", false),
        atomOfferingClass("atom_offering_class", "atomOfferingClass", "VARCHAR", false),
        atomOfferingCode("atom_offering_code", "atomOfferingCode", "VARCHAR", false),
        atomOfferingName("atom_offering_name", "atomOfferingName", "VARCHAR", false),
        deductPrice("deduct_price", "deductPrice", "VARCHAR", false),
        atomPrice("atom_price", "atomPrice", "BIGINT", false),
        atomSettlePrice("atom_settle_price", "atomSettlePrice", "BIGINT", false),
        atomQuantity("atom_quantity", "atomQuantity", "BIGINT", false),
        orderStatus("order_status", "orderStatus", "INTEGER", false),
        cooperatorId("cooperator_id", "cooperatorId", "VARCHAR", false),
        finishCooperatorId("finish_cooperator_id", "finishCooperatorId", "VARCHAR", false),
        beId("be_id", "beId", "VARCHAR", false),
        regionId("region_id", "regionId", "VARCHAR", false),
        exHandleId("ex_handle_id", "exHandleId", "VARCHAR", false),
        skuCardName("sku_card_name", "skuCardName", "VARCHAR", false),
        skuMsisdn("sku_msisdn", "skuMsisdn", "VARCHAR", false),
        allowOrderStatus("allow_order_status", "allowOrderStatus", "INTEGER", false),
        allowOrderFailureReason("allow_order_failure_reason", "allowOrderFailureReason", "VARCHAR", false),
        partReturn("part_return", "partReturn", "INTEGER", false),
        baoliStatus("baoli_status", "baoliStatus", "INTEGER", false),
        createTime("create_time", "createTime", "VARCHAR", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        valetOrderCompleteTime("valet_order_complete_time", "valetOrderCompleteTime", "VARCHAR", false),
        carOpenStatus("car_open_status", "carOpenStatus", "INTEGER", false),
        spuOfferingVersion("spu_offering_version", "spuOfferingVersion", "VARCHAR", false),
        skuOfferingVersion("sku_offering_version", "skuOfferingVersion", "VARCHAR", false),
        atomOfferingVersion("atom_offering_version", "atomOfferingVersion", "VARCHAR", false),
        softServiceStatus("soft_service_status", "softServiceStatus", "INTEGER", false),
        settleStatus("settle_status", "settleStatus", "INTEGER", false),
        billNoTime("bill_no_time", "billNoTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Mon Mar 31 15:09:26 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Mar 31 15:09:26 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Mar 31 15:09:26 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Mar 31 15:09:26 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Mar 31 15:09:26 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Mar 31 15:09:26 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Mar 31 15:09:26 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Mar 31 15:09:26 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Mar 31 15:09:26 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Mar 31 15:09:26 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Mar 31 15:09:26 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Mar 31 15:09:26 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Mar 31 15:09:26 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Mar 31 15:09:26 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Mar 31 15:09:26 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Mar 31 15:09:26 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Mar 31 15:09:26 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}