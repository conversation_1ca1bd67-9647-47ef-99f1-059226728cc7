package com.chinamobile.retail.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AfterMarketStdServiceExample {
    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public AfterMarketStdServiceExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public AfterMarketStdServiceExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public AfterMarketStdServiceExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public static Criteria newAndCreateCriteria() {
        AfterMarketStdServiceExample example = new AfterMarketStdServiceExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public AfterMarketStdServiceExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public AfterMarketStdServiceExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeIsNull() {
            addCriterion("after_market_code is null");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeIsNotNull() {
            addCriterion("after_market_code is not null");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeEqualTo(String value) {
            addCriterion("after_market_code =", value, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("after_market_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeNotEqualTo(String value) {
            addCriterion("after_market_code <>", value, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeNotEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("after_market_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeGreaterThan(String value) {
            addCriterion("after_market_code >", value, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeGreaterThanColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("after_market_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeGreaterThanOrEqualTo(String value) {
            addCriterion("after_market_code >=", value, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeGreaterThanOrEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("after_market_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeLessThan(String value) {
            addCriterion("after_market_code <", value, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeLessThanColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("after_market_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeLessThanOrEqualTo(String value) {
            addCriterion("after_market_code <=", value, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeLessThanOrEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("after_market_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeLike(String value) {
            addCriterion("after_market_code like", value, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeNotLike(String value) {
            addCriterion("after_market_code not like", value, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeIn(List<String> values) {
            addCriterion("after_market_code in", values, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeNotIn(List<String> values) {
            addCriterion("after_market_code not in", values, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeBetween(String value1, String value2) {
            addCriterion("after_market_code between", value1, value2, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeNotBetween(String value1, String value2) {
            addCriterion("after_market_code not between", value1, value2, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andStdServiceIdIsNull() {
            addCriterion("std_service_id is null");
            return (Criteria) this;
        }

        public Criteria andStdServiceIdIsNotNull() {
            addCriterion("std_service_id is not null");
            return (Criteria) this;
        }

        public Criteria andStdServiceIdEqualTo(String value) {
            addCriterion("std_service_id =", value, "stdServiceId");
            return (Criteria) this;
        }

        public Criteria andStdServiceIdEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("std_service_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStdServiceIdNotEqualTo(String value) {
            addCriterion("std_service_id <>", value, "stdServiceId");
            return (Criteria) this;
        }

        public Criteria andStdServiceIdNotEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("std_service_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStdServiceIdGreaterThan(String value) {
            addCriterion("std_service_id >", value, "stdServiceId");
            return (Criteria) this;
        }

        public Criteria andStdServiceIdGreaterThanColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("std_service_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStdServiceIdGreaterThanOrEqualTo(String value) {
            addCriterion("std_service_id >=", value, "stdServiceId");
            return (Criteria) this;
        }

        public Criteria andStdServiceIdGreaterThanOrEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("std_service_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStdServiceIdLessThan(String value) {
            addCriterion("std_service_id <", value, "stdServiceId");
            return (Criteria) this;
        }

        public Criteria andStdServiceIdLessThanColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("std_service_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStdServiceIdLessThanOrEqualTo(String value) {
            addCriterion("std_service_id <=", value, "stdServiceId");
            return (Criteria) this;
        }

        public Criteria andStdServiceIdLessThanOrEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("std_service_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStdServiceIdLike(String value) {
            addCriterion("std_service_id like", value, "stdServiceId");
            return (Criteria) this;
        }

        public Criteria andStdServiceIdNotLike(String value) {
            addCriterion("std_service_id not like", value, "stdServiceId");
            return (Criteria) this;
        }

        public Criteria andStdServiceIdIn(List<String> values) {
            addCriterion("std_service_id in", values, "stdServiceId");
            return (Criteria) this;
        }

        public Criteria andStdServiceIdNotIn(List<String> values) {
            addCriterion("std_service_id not in", values, "stdServiceId");
            return (Criteria) this;
        }

        public Criteria andStdServiceIdBetween(String value1, String value2) {
            addCriterion("std_service_id between", value1, value2, "stdServiceId");
            return (Criteria) this;
        }

        public Criteria andStdServiceIdNotBetween(String value1, String value2) {
            addCriterion("std_service_id not between", value1, value2, "stdServiceId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(AfterMarketStdService.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeLikeInsensitive(String value) {
            addCriterion("upper(after_market_code) like", value.toUpperCase(), "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andStdServiceIdLikeInsensitive(String value) {
            addCriterion("upper(std_service_id) like", value.toUpperCase(), "stdServiceId");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Wed Feb 01 15:27:22 CST 2023
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        private AfterMarketStdServiceExample example;

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        protected Criteria(AfterMarketStdServiceExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        public AfterMarketStdServiceExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Wed Feb 01 15:27:22 CST 2023
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Wed Feb 01 15:27:22 CST 2023
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Wed Feb 01 15:27:22 CST 2023
         */
        void example(com.chinamobile.retail.pojo.entity.AfterMarketStdServiceExample example);
    }
}