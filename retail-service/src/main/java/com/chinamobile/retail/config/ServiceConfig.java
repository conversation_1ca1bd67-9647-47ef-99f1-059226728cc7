package com.chinamobile.retail.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * created by l<PERSON>xiang on 2022/9/2 14:43
 */
@Component
@Data
public class ServiceConfig {

    @Value("${product.shareUrl.suffix}")
    private String shareUrlSuffix;

    @Value("${exchange.unlock.seconds:1800}")
    private Integer exchangeUnlockSeconds;

    @Value("${distribution.link}")
    private String disLink;

    @Value("${distribution.mglink}")
    private String dismgLink;

    @Value("${unionpay.url}")
    private String unionPayUrl;

    @Value("${unionpay.appId}")
    private String unionPayAppId;

    @Value("${unionpay.secret}")
    private String unionPaySecret;

    @Value("${unionpay.sm4key}")
    private String unionPaySm4Key;

    @Value("${unionpay.signResultNotifyUrl}")
    private String unionPaySignResultNotifyUrl;

    @Value("${unionpay.exchangeResultNotifyUrl}")
    private String unionPayExchangeResultNotifyUrl;

    //签约公司，线上固定为 和缘盛世
    @Value("${unionpay.signCompanyId}")
    private String unionPaySignCompanyId;



}
