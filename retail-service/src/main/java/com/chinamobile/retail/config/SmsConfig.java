package com.chinamobile.retail.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "sms")
@Data
@RefreshScope
public class SmsConfig {

    private String validCodeTempId;

    /**
     * 分销中心登录短信验证码模板
     */
    private String retailCodeLoginTempId;


    /**
     * 分销中心注册短信验证码模板
     */
    private String retailCodeRegTempId;

    /**
     * 商机经理派发短信模板
     */
    private String dispatchRequirementTemplateId;

    /**
     * 提醒用户进行银联签约的短信模板id
     */
    private String unionPaySignMsgTemplateId;
}
