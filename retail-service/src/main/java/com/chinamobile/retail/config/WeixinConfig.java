package com.chinamobile.retail.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * created by l<PERSON><PERSON><PERSON> on 2022/12/28 11:04
 */
@Data
@ConfigurationProperties(prefix = "weixin")
@Configuration
public class WeixinConfig {

    private String appId;

    private String appSecret;
}
