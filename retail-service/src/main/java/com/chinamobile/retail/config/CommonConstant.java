package com.chinamobile.retail.config;

/**
 * created by l<PERSON><PERSON><PERSON> on 2022/8/31 10:48
 */
public class CommonConstant {

    public static final String batch_no_prefix = "BN";

    //redis发起兑换锁前缀，一个积分供应商一把锁
    public static final String redis_exchange_exist_key = "exchange_";

    //提现文件头格式 : 总笔数&|总金额&|
    public static final String exchange_file_header_format = "%d|%d|%s";
    //提现文件体格式
    public static final String exchange_file_data_format = "%s|%s|%s|%s|%s|%s|%s|%s";
    //提现文件名格式
    public static final String exchange_file_name_format = "%s_%s_%s.txt";
    //拉卡拉校验签约接口
    public static final String lakala_third_user_sign_info_method = "getThirdUserSignInfo";
    //拉卡拉获取商户余额接口
    public static final String lakala_bus_balance_method = "getBusBalance";
    //拉卡拉提现接口
    public static final String lakala_payment_method = "agentPayment";

    //拉卡拉提现状态查询接口
    public static final String lakala_trade_info_method = "getTradeInfo";

    //拉卡拉业务接口返回成功
    public static final String lakala_business_success = "000000";
    //提现接口业务参数表示直接失败的code，未通过基础验证
    public static final String lakala_cash_fail = "999999";

    //redis中保存微信access_token的key
    public static final String redis_weixin_access_token_key = "SC:WEIXIN_ACCESS_TOKEN";

    //redis中保存微信jsapi_ticket的key
    public static final String redis_weixin_jsapi_ticket_key = "SC:WEIXIN_JSAPI_TICKET";

    //发布范围"全国"的省编码
    public static final String nation_wide = "000";

    //银联接口token缓存的key
    public static final String REDIS_UNIONPAY_TOKEN_KEY = "UNIONPAY_TOKEN";

    //银联登录，获取token
    public static final String unionpayLogin = "/api/conglomerate/login";

    //发起银联 实名/签约/绑卡，获取对应跳转地址
    public static final String sendSign = "/api/conglomerateSign/sendSign";

    //发起银联兑换订单
    public static final String uploadOrder = "/api/conglomerate/uploadOrder";

    //查询银联兑换结果
    public static final String queryOrderResult = "/api/conglomerate/queryOrderResult";

    //查询余额
    public static final String queryAccountBalance = "/api/conglomerate/queryAccountBalance";




}
