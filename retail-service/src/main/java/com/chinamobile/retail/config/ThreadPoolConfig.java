package com.chinamobile.retail.config;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @description: 线程池配置信息
 * @author: zhushiwu
 * @data: 2021/5/25
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "thread.pool")
public class ThreadPoolConfig {
    /**核心线程数*/
    private Integer codeSize = 10;
    /**最大线程数*/
    private Integer maxSize = 50;
    /**空闲保留时长，单位秒*/
    private Integer keepAliveTime = 60;
    /**自定义拒绝策略触发时，休眠时间 毫秒*/
    private Integer rejectedTime = 100;
    /**等待队列大小(这里考虑到 有base64 较大的数据存放，所以设置小一点)*/
    private Integer queueSize = 300;

}
