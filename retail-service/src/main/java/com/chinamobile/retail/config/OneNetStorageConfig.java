package com.chinamobile.retail.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @description: oneNet 平台
 * ConfigurationProperties 本身支持自动刷新不能与@RefreshScope 一起使用
 * @author: z<PERSON><PERSON><PERSON>
 * @data: 2021/4/27
 **/
@ConfigurationProperties(prefix = "onenet-storage")
@Configuration
@Data
public class OneNetStorageConfig {
    //配置访问代理
    private String queryHttpInner;
    private String queryHttpOuter;
    private String endpoint;
    private String bucketName;
    private String accessKey;
    private String secretKey;

    public String getQueryHttpInner(String key) {
        return queryHttpInner+(queryHttpInner.endsWith("/") ? "" :"/") .concat(bucketName).concat("/").concat(key);
    }
    public String getQueryHttpOuter(String key) {
        return queryHttpOuter+(queryHttpOuter.endsWith("/") ? "" :"/").concat(bucketName).concat("/").concat(key);
    }

}
