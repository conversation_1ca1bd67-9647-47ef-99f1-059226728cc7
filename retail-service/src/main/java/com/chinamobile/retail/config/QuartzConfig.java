package com.chinamobile.retail.config;

import com.alibaba.nacos.api.exception.NacosException;
import org.quartz.spi.JobFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Properties;

@Configuration
@EnableScheduling
public class QuartzConfig {
    @Resource
    private JobFactory jobFactory;
    //quartz配置
    @Resource(name = "quartzProperties")
    private Properties quartzProperties;

    @Bean
    public SchedulerFactoryBean schedulerFactoryBean() throws IOException, NacosException {
        SchedulerFactoryBean factory = new SchedulerFactoryBean();
        factory.setOverwriteExistingJobs(true);
        // 延时启动
        factory.setStartupDelay(10);
        // 加载quartz数据源配置
        factory.setQuartzProperties(quartzProperties);
        // 自定义Job Factory，用于Spring注入
        factory.setJobFactory(jobFactory);
        return factory;
    }

}
