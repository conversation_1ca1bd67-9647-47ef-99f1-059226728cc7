package com.chinamobile.retail.config;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.retail.dao.SkuOfferingInfoMapper;
import com.chinamobile.retail.dao.SkuRoleRelationMapper;
import com.chinamobile.retail.enums.RetailUserRoleEnum;
import com.chinamobile.retail.pojo.entity.SkuOfferingInfo;
import com.chinamobile.retail.pojo.entity.SkuOfferingInfoExample;
import com.chinamobile.retail.pojo.entity.SkuRoleRelation;
import com.chinamobile.retail.pojo.entity.SkuRoleRelationExample;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * created by liuxiang on 2022/9/15 10:21
 * 初始化sku商品和角色积分配置
 */
@Component
@Slf4j
public class SkuRoleRelationConfig implements CommandLineRunner {

    @Resource
    private SkuOfferingInfoMapper skuOfferingInfoMapper;

    @Resource
    private SkuRoleRelationMapper skuRoleRelationMapper;

    @Override
    @DS("save")
    public void run(String... args) throws Exception {
        log.info("开始初始化SkuRoleRelation");
        //添加新的合伙人角色，必须加到RetailUserRoleEnum中
        RetailUserRoleEnum[] values = RetailUserRoleEnum.values();
        List<Integer> allRole = Arrays.stream(values).map(r -> {
            return r.code;
        }).collect(Collectors.toList());

        //找出商品角色积分关系中，已有的角色
        List<SkuRoleRelation> skuRoleRelationList = skuRoleRelationMapper.selectByExample(new SkuRoleRelationExample());
        List<Integer> existedRole = skuRoleRelationList.stream().map(s -> {
            return s.getPartnerRoleId();
        }).collect(Collectors.toList());
        //将商品角色积分关系中，没有的角色加入
        allRole.removeAll(existedRole);
        if(allRole.isEmpty()){
            log.info("未新增角色，无需初始化SkuRoleRelation");
            return;
        }
        List<SkuOfferingInfo> skuOfferingInfos = skuOfferingInfoMapper.selectByExample(new SkuOfferingInfoExample().createCriteria().andDeleteTimeIsNull().example());
        List<String> skuIdList = skuOfferingInfos.stream().map(s -> {
            return s.getId();
        }).collect(Collectors.toList());
        Date now = new Date();
        List<SkuRoleRelation> skuRoleRelations = new ArrayList<>();
        for (String skuId : skuIdList) {
            for (Integer partnerRoleType : allRole) {
                SkuRoleRelation skuRoleRelation = new SkuRoleRelation();
                skuRoleRelation.setId(BaseServiceUtils.getId());
                skuRoleRelation.setSkuId(skuId);
                skuRoleRelation.setPartnerRoleId(partnerRoleType);
                skuRoleRelation.setPointPercent(Double.valueOf("0"));
                skuRoleRelation.setPointLimit(null);
                skuRoleRelation.setCreateTime(now);
                skuRoleRelation.setUpdateTime(now);
                skuRoleRelations.add(skuRoleRelation);
            }
        }
        //分批次入库
        int start = 0;
        int size = 1000;
        while (true){
            int end = (start+size) <= skuRoleRelations.size() ? (start+size) : skuRoleRelations.size();
            List<SkuRoleRelation> subList = skuRoleRelations.subList(start, end);
            if(CollectionUtils.isNotEmpty(subList)){
                skuRoleRelationMapper.batchInsert(subList);
            }
            if(subList.size() < size){
                break;
            }
            start += size;
        }
        log.info("完成初始化SkuRoleRelation");
    }

}
