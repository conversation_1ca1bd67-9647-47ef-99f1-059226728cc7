package com.chinamobile.retail.config;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.servlet.MultipartConfigElement;

/**
 * <AUTHOR>
 * 文件上传配置
 */
@Data
@ConfigurationProperties(prefix="file.upload")
@Configuration
public class FileConfig {
    private final MultipartConfigElement configElement;
    private String filedir;
    private String whiteSuffix;

    public FileConfig(MultipartConfigElement multipartConfigElement) {
        this.configElement = multipartConfigElement;
    }

    public String getFiledir() {
        if(StringUtils.isBlank(filedir)) {
            String location = configElement.getLocation();
            return location.endsWith("/")?location:location+"/";
        }
        return filedir.endsWith("/")?filedir:filedir+"/";
    }

}
