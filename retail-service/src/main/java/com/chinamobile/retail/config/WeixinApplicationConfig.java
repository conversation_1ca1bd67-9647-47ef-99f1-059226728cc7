package com.chinamobile.retail.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import javax.validation.Valid;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2023/2/15 11:24
 * 微信小程序配置
 */
@Data
@ConfigurationProperties(prefix = "weixin.application")
@Configuration
public class WeixinApplicationConfig {

    private String appId;

    private String appSecret;
}
