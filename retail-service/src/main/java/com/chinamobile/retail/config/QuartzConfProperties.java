package com.chinamobile.retail.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

@RefreshScope
@Configuration
public class QuartzConfProperties {
    @Value("${org.quartz.dataSource.sgs_quartz.URL}")
    private String url;
    @Value("${org.quartz.dataSource.sgs_quartz.driver}")
    private String driver;
    @Value("${org.quartz.dataSource.sgs_quartz.maxConnections}")
    private String maxConnections;
    @Value("${org.quartz.dataSource.sgs_quartz.password}")
    private String password;
    @Value("${org.quartz.dataSource.sgs_quartz.user}")
    private String user;
    @Value("${org.quartz.dataSource.sgs_quartz.validateOnCheckout}")
    private String validateOnCheckout;
    @Value("${org.quartz.dataSource.sgs_quartz.validationQuery}")
    private String validationQuery;
    @Value("${org.quartz.jobStore.class}")
    private String jobStore_class;
    @Value("${org.quartz.jobStore.clusterCheckinInterval}")
    private String clusterCheckinInterval;
    @Value("${org.quartz.jobStore.dataSource}")
    private String dataSource;
    @Value("${org.quartz.jobStore.driverDelegateClass}")
    private String driverDelegateClass;
    @Value("${org.quartz.jobStore.isClustered}")
    private String isClustered;
    @Value("${org.quartz.jobStore.maxMisfiresToHandleAtATime}")
    private String maxMisfiresToHandleAtATime;
    @Value("${org.quartz.jobStore.misfireThreshold}")
    private String misfireThreshold;
    @Value("${org.quartz.jobStore.selectWithLockSQL}")
    private String selectWithLockSQL;
    @Value("${org.quartz.jobStore.tablePrefix}")
    private String tablePrefix;
    @Value("${org.quartz.jobStore.txIsolationLevelSerializable}")
    private String txIsolationLevelSerializable;
    @Value("${org.quartz.jobStore.useProperties}")
    private String useProperties;
    @Value("${org.quartz.plugin.shutdownhook.class}")
    private String shutdownhook_class;
    @Value("${org.quartz.plugin.shutdownhook.cleanShutdown}")
    private String cleanShutdown;
    @Value("${org.quartz.plugin.triggHistory.class}")
    private String triggHistory_class;
    @Value("${org.quartz.scheduler.instanceId}")
    private String instanceId;
    @Value("${org.quartz.scheduler.instanceName}")
    private String instanceName;
    @Value("${org.quartz.scheduler.skipUpdateCheck}")
    private String skipUpdateCheck;
    @Value("${org.quartz.threadPool.class}")
    private String threadPool_class;
    @Value("${org.quartz.threadPool.threadCount}")
    private String threadCount;
    @Value("${org.quartz.threadPool.threadPriority}")
    private String threadPriority;
    @Value("${org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread}")
    private String threadsInheritContextClassLoaderOfInitializingThread;

    @Bean(name = "quartzProperties")
    public Properties quartzProperties() {
        Properties properties = new Properties();
        properties.setProperty("org.quartz.dataSource.sgs_quartz.URL", url);
        properties.setProperty("org.quartz.dataSource.sgs_quartz.driver", driver);
        properties.setProperty("org.quartz.dataSource.sgs_quartz.maxConnections", maxConnections);
        properties.setProperty("org.quartz.dataSource.sgs_quartz.password", password);
        properties.setProperty("org.quartz.dataSource.sgs_quartz.user", user);
        properties.setProperty("org.quartz.dataSource.sgs_quartz.validateOnCheckout", validateOnCheckout);
        properties.setProperty("org.quartz.dataSource.sgs_quartz.validationQuery", validationQuery);
        properties.setProperty("org.quartz.jobStore.class", jobStore_class);
        properties.setProperty("org.quartz.jobStore.clusterCheckinInterval", clusterCheckinInterval);
        properties.setProperty("org.quartz.jobStore.dataSource", dataSource);
        properties.setProperty("org.quartz.jobStore.driverDelegateClass", driverDelegateClass);
        properties.setProperty("org.quartz.jobStore.isClustered", isClustered);
        properties.setProperty("org.quartz.jobStore.maxMisfiresToHandleAtATime", maxMisfiresToHandleAtATime);
        properties.setProperty("org.quartz.jobStore.misfireThreshold", misfireThreshold);
        properties.setProperty("org.quartz.jobStore.selectWithLockSQL", selectWithLockSQL);
        properties.setProperty("org.quartz.jobStore.tablePrefix", tablePrefix);
        properties.setProperty("org.quartz.jobStore.txIsolationLevelSerializable", txIsolationLevelSerializable);
        properties.setProperty("org.quartz.jobStore.useProperties", useProperties);
        properties.setProperty("org.quartz.plugin.shutdownhook.class", shutdownhook_class);
        properties.setProperty("org.quartz.plugin.shutdownhook.cleanShutdown", cleanShutdown);
        properties.setProperty("org.quartz.plugin.triggHistory.class", triggHistory_class);
        properties.setProperty("org.quartz.scheduler.instanceId", instanceId);
        properties.setProperty("org.quartz.scheduler.instanceName", instanceName);
        properties.setProperty("org.quartz.scheduler.skipUpdateCheck", skipUpdateCheck);
        properties.setProperty("org.quartz.threadPool.class", threadPool_class);
        properties.setProperty("org.quartz.threadPool.threadCount", threadCount);
        properties.setProperty("org.quartz.threadPool.threadPriority", threadPriority);
        properties.setProperty("org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread", threadsInheritContextClassLoaderOfInitializingThread);

        return properties;
    }

}
