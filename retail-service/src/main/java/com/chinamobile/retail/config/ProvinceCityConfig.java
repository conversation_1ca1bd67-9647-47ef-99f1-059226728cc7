package com.chinamobile.retail.config;

import com.chinamobile.retail.dao.ContractCityInfoMapper;
import com.chinamobile.retail.dao.ContractProvinceInfoMapper;
import com.chinamobile.retail.dao.RegionInfoMapper;
import com.chinamobile.retail.exception.IOTException;
import com.chinamobile.retail.pojo.dto.CityDTO;
import com.chinamobile.retail.pojo.dto.ProvinceDTO;
import com.chinamobile.retail.pojo.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Component
public class ProvinceCityConfig implements CommandLineRunner, Ordered {

    @Resource
    private ContractProvinceInfoMapper provinceMapper;

    @Resource
    private ContractCityInfoMapper cityMapper;

    @Resource
    private RegionInfoMapper regionMapper;

    private List<ProvinceDTO> procityList = new ArrayList<>();

    public List<ProvinceDTO> getProcityList(){
        return procityList;
    }

    private HashMap<String, String> provinceNameCodeMap = new HashMap<>();

    private HashMap<String, String> provinceCodeNameMap = new HashMap<>();

    private HashMap<String, String> cityNameCodeMap = new HashMap<>();

    private HashMap<String, String> cityCodeNameMap = new HashMap<>();

    private HashMap<String, String> regionCodeNameMap = new HashMap<>();

    private HashMap<String, String> regionNameCodeMap = new HashMap<>();

    public HashMap<String, String> getProvinceNameCodeMap(){
        return provinceNameCodeMap;
    }

    public HashMap<String, String> getProvinceCodeNameMap(){
        return provinceCodeNameMap;
    }

    public HashMap<String, String> getCityNameCodeMap(){
        return cityNameCodeMap;
    }

    public HashMap<String, String> getCityCodeNameMap(){
        return cityCodeNameMap;
    }

    public HashMap<String, String> getRegionNameCodeMap(){
        return regionNameCodeMap;
    }

    public HashMap<String, String> getRegionCodeNameMap(){
        return regionCodeNameMap;
    }

//    @Value("#{'${whitelist.visitor}'.replaceAll(' ','').split(',')}")
//    private List<String> whiteList;

//    @Value("${whitelist.visitor}")
//    private String whiteListString;

//    private List<String> whiteList = new ArrayList<>();

//    public List<String> getWhiteList(){
//        return whiteList;
//    }


    @Override
    public void run(String... args) throws Exception {
        log.info("ProvinceCityConfig run Enter...");
        List<String> excludeProvince = new ArrayList<>();
        excludeProvince.add("000");
        excludeProvince.add("001");
        excludeProvince.add("002");

        List<ContractProvinceInfo> provinceInfoList = provinceMapper.selectByExample(new ContractProvinceInfoExample().createCriteria().andMallCodeNotIn(excludeProvince).example());
        for(ContractProvinceInfo pinfo: provinceInfoList){
            ProvinceDTO pdto = new ProvinceDTO();
            List<CityDTO> cityList = new ArrayList<>();
            String pname = pinfo.getMallName();
            String pcode = pinfo.getMallCode();
            //构建省map
            provinceNameCodeMap.put(pname, pcode);
            provinceCodeNameMap.put(pcode,pname);

            pdto.setName(pname);
            pdto.setValue(pcode);
            List<ContractCityInfo> cityInfoList = cityMapper.selectByExample(new ContractCityInfoExample().createCriteria().andProvinceMallCodeEqualTo(pcode).example());
            for(ContractCityInfo cinfo: cityInfoList){
                CityDTO cdto = new CityDTO();
                cdto.setName(cinfo.getMallName());
                cdto.setValue(cinfo.getMallCode());

                //构建城市Map
                cityNameCodeMap.put(cinfo.getMallName(), cinfo.getMallCode());
                cityCodeNameMap.put(cinfo.getMallCode(), cinfo.getMallName());

                cityList.add(cdto);
            }
            pdto.setChildren(cityList);
            procityList.add(pdto);
        }

        List<RegionInfo> regionInfoList = regionMapper.selectByExample(new RegionInfoExample());
        regionInfoList.forEach(regionInfo -> {
            regionCodeNameMap.put(regionInfo.getRegionCode(), regionInfo.getRegionName());
            //区县可能名称不唯一，添加地市编码作为前缀构造唯一
            regionNameCodeMap.put(regionInfo.getLocation()+ regionInfo.getRegionName(), regionInfo.getRegionCode());
        });

        //添加白名单
//        whiteList = Arrays.asList("18523046327","18053846686","13648047960","13594253053","13896987253","17383062157","18223126515"
//                , "13752954000","13340260645","15823524720","13658312520","15213091924","13668497025","13996250880","13896119290"
//                , "18716643042","13594176527"
//                ,"17896205585","18741060066","17847909190");
//        log.info("whiteListString = {}",whiteListString);
//        whiteList = Arrays.asList(whiteListString.trim().replaceAll(" ", "").split(","));

//        log.info("ProvinceCityConfig run whiteList = {}",whiteList);
//        log.info("ProvinceCityConfig run whiteList contains phone = {}",whiteList.contains("13594176527"));
    }

    @Override
    public int getOrder() {
        return 1;
    }
}
