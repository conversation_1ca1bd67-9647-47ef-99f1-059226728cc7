package com.chinamobile.retail.aspect;


import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.retail.exception.IOTException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@Aspect
@Component
@Slf4j
public class AuthIotAspect {

    @Value("${iot.secretKey:E4FE7FE78FE2933D}")
    private String secretKey;

    @Pointcut("execution(public * com.chinamobile.retail.controller.UserController.getLink(..))")
    public void point() {
    }

    @Before(value = "point()")
    public void authBefore(JoinPoint joinPoint) {
        final ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        final HttpServletRequest request = requestAttr.getRequest();
        Object[] bodies = joinPoint.getArgs();
        for (Object body : bodies) {
            if (body instanceof IOTRequest) {
                IOTRequest iotRequest = (IOTRequest) body;
                log.info("Retail 请求接口路径:{},iotRequest:{}", request.getRequestURI(), JSON.toJSONString(iotRequest));
                if (iotRequest.getMessageSeq() != null) {
                    MDC.put("msgSeq", iotRequest.getMessageSeq());
                } else {
                    MDC.put("msgSeq", BaseServiceUtils.getId());
                }

                //对请求内容鉴权
                String content = iotRequest.getContent();
                if (DigestUtils.md5Hex("content=" + content + "&" + secretKey).equals(iotRequest.getSign())) {
                    return;
                } else {
                    IOTAnswer<Void> iotAnswer = new IOTAnswer<>();
                    iotAnswer.setMessageSeq(iotRequest.getMessageSeq());
                    log.info("请求内容加密为：{}，实际获取签名为：{}", DigestUtils.md5Hex("content=" + content + "&" + secretKey), iotRequest.getSign());
                    throw new IOTException(iotAnswer, "-2", "鉴权失败，请重新发起请求");
                }
            }
        }
    }

    /**
     * 统计和IOT商城交互时长
     *
     * @param pjp
     * @return
     * @throws Throwable
     */
    @Around("point()")
    public Object Around(ProceedingJoinPoint pjp) throws Throwable {
        long start = System.currentTimeMillis();
        Object obj = pjp.proceed();
        long end = System.currentTimeMillis();
        log.info(pjp.getSignature().getName() + ";执行了" + (end - start) + "ms");
        return obj;
    }


    @AfterReturning(returning = "result", value = "point()")
    public void after(Object result) {
        try {
            log.info("MsgSeq:{},返回结果:{}", MDC.get("msgSeq"), JSON.toJSONString(result));
        } finally {
            MDC.clear();
        }
    }

    @AfterThrowing(throwing = "ex", value = "point()")
    public void doAfterThrowing(Throwable ex) {
        try {
            if (ex instanceof IOTException) {
                //这里记录错误信息，统一管理
                log.error("MsgSeq:{},错误描述:{}", MDC.get("msgSeq"), ex);
            } else {
                IOTAnswer<Void> voidIOTAnswer = new IOTAnswer<>();
                voidIOTAnswer.setMessageSeq(MDC.get("msgSeq"));
                log.error("MsgSeq:{},错误描述:{}", MDC.get("msgSeq"), ex);
                throw new IOTException(voidIOTAnswer, "-1", "未知错误");
            }
        } finally {
            MDC.clear();
        }
    }

}
