package com.chinamobile.retail.aspect;

import com.chinamobile.retail.annotation.CodeValidMark;
import com.chinamobile.retail.service.impl.CaptchaServiceImpl;
import com.chinamobile.retail.util.RSAUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

@Aspect
@Component
@Slf4j
@Order(99)
public class CodeValidAspect {

    @Resource
    private HttpServletRequest request;

    @Resource
    private CaptchaServiceImpl captchaService;

    @Value(("${private.key}"))
    private String privateKeyStr;

    @Pointcut("@annotation(com.chinamobile.retail.annotation.CodeValidMark)")
    public void pointcut() {

    }

    @Around(value="pointcut()")
    public Object around(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        //获取对应方法
        MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
        Method method = methodSignature.getMethod();
        CodeValidMark codeValidMark = method.getAnnotation(CodeValidMark.class);
        String  account = request.getParameter("userName")==null?request.getParameter("phone") :request.getParameter("userName") ;
        if(codeValidMark.validCode()){
            String session = request.getParameter("sessionId");
            String validCode = request.getParameter("validCode");
            if (codeValidMark.encrypted()) {
                validCode = RSAUtils.decryptByPrivateKey(validCode, privateKeyStr);
            }
            captchaService.valid(validCode,session,account);
        }
//        else if(codeValidMark.validSlideCode()){
//            String sessionId = request.getParameter("sessionId");
//            Integer x = Integer.parseInt(request.getParameter("x"));
//            Integer y = Integer.parseInt(request.getParameter("y"));
//            captchaService.validSlideCode(x,y,sessionId);
//        }
        else if(codeValidMark.validSmsCode()){
            String phone = request.getParameter("phone");
            String codeStr = request.getParameter("code");
            log.info("解密前 phone = {}\n codeStr = {}", phone, codeStr);
            if (codeValidMark.encrypted()) {
                phone = RSAUtils.decryptByPrivateKey(phone, privateKeyStr);
                codeStr = RSAUtils.decryptByPrivateKey(codeStr, privateKeyStr);
                log.info("解密后 phone = {}\n codeStr = {}", phone, codeStr);
            }
            Integer code = Integer.parseInt(codeStr);
            captchaService.validSmsCode(account,phone,code);
        }else if(codeValidMark.validNewSmsCode()){
            String newPhone = request.getParameter("newPhone");
            String newCodeStr = request.getParameter("newCode");
            if (codeValidMark.encrypted()) {
                newPhone = RSAUtils.decryptByPrivateKey(newPhone, privateKeyStr);
                newCodeStr = RSAUtils.decryptByPrivateKey(newCodeStr, privateKeyStr);
            }
            Integer newCode = Integer.parseInt(newCodeStr);
            captchaService.validEditCode(newPhone,newCode,account);
        }
        Object result = proceedingJoinPoint.proceed();
        if(codeValidMark.validSmsCode()){
            String phone = request.getParameter("phone");
            if (codeValidMark.encrypted()) {
                phone = RSAUtils.decryptByPrivateKey(phone, privateKeyStr);
            }
            captchaService.deleteSmsCodeInRedis(phone);
        }else if(codeValidMark.validNewSmsCode()){
            String newPhone = request.getParameter("newPhone");
            if (codeValidMark.encrypted()) {
                newPhone = RSAUtils.decryptByPrivateKey(newPhone, privateKeyStr);
            }
            captchaService.deleteEditSmsCodeInRedis(newPhone);
        }
        return result;
    }


}
