package com.chinamobile.retail.enums;

/**
 * @Author: YSC
 * @Date: 2021/11/9 15:54
 * @Description: 前端展示枚举类 前端展示status即可不需要描述信息故未使用
 */
public enum OrderStatusInnerEnum {
    // 订单状态
    WAIT_SEND(0, "待发货", "用户下单", "创建订单"),
    WAIT_RECEIVE(1, "待收货", "商家发货", "完成发货"),
    COMPLETE(2, "已收货", "用户收货", "确认收货"),
    //    CREATE_INVOICE(3, "开票"),
    ORDER_REFUND(4, "退款中", "退款中", "退款中"),
    ORDER_RETURN(5, "退货退款中", "退货退款中", "退货退款中"),
    ORDER_CHANGE(6, "换货中", "换货中", "换货中"),
    ORDER_SUCCESS(7, "交易成功", "订单结果", "交易成功"),
    ORDER_FAIL(8, "交易失败", "订单结果", "交易失败"),
    PART_SUCCESS(9, "订单部分退款成功", "订单部分退款成功", "部分退款成功"),
    VALET_ORDER_TAKING(10, "待接单", "待接单", "创建订单"),
    VALET_ORDER_APPROVE(11, "待省侧审批（代客下单）", "待省侧审批（代客下单）", "待省侧审批（代客下单）"),
    VALET_ORDER_COMPLETE(12, "待出账（仅代客下单）", "待出账（仅代客下单）", "确认收货"),
    VALET_DRAFT(13, "草稿单", "草稿单", "草稿单"),
    VALET_DRAFT_DELETE(14, "草稿单删除", "草稿单删除", "草稿单删除"),
    VALET_CARD_MAKING(15, "制卡中", "制卡中", "制卡中"),
    VALET_WAIT_DELIVER(16, "待交付", "待交付", "待交付"),
    ;

    /**
     * 订单状态码
     */
    private final Integer status;
    /**
     * 订单状态信息
     */
    private final String message;
    private final String process;
    /**
     * 订单历史描述
     */
    private final String history;

    OrderStatusInnerEnum(int status, String message, String process, String history) {
        this.status = status;
        this.process = process;
        this.message = message;
        this.history = history;
    }

    public Integer getStatus() {
        return status;
    }

    public String getMessage() {
        return message;
    }

    public static String getDescribe(Integer status) {
        for (OrderStatusInnerEnum value : OrderStatusInnerEnum.values()) {
            if (value.status.equals(status)) {
                return value.message;
            }
        }
        return null;
    }

    public static String getHistoryDescribe(Integer status) {
        for (OrderStatusInnerEnum value : OrderStatusInnerEnum.values()) {
            if (value.status.equals(status)) {
                return value.history;
            }
        }
        return null;
    }

    public static String getProcessDescribe(Integer status) {
        for (OrderStatusInnerEnum value : OrderStatusInnerEnum.values()) {
            if (value.status.equals(status)) {
                return value.process;
            }
        }
        return null;
    }

    public String getHistory() {
        return history;
    }
}
