package com.chinamobile.retail.enums;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2022/10/18 14:46
 * 积分收支明细，不含兑换
 */
public enum PointOperateTypeEnum {

    ORDER_POINT(1,"订单激励"),
    INCREASE(2,"补发"),
    DECREASE(3,"扣减"),
    ACTIVITY_POINT(4,"活动积分下发")
    ;

    public Integer code;

    public String desc;

    PointOperateTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PointOperateTypeEnum fromCode(Integer code){
        PointOperateTypeEnum[] values = PointOperateTypeEnum.values();
        for (PointOperateTypeEnum value : values) {
            if(value.code.equals(code)){
                return value;
            }
        }
        return null;
    }
}
