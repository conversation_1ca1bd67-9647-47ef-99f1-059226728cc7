package com.chinamobile.retail.enums;

/**
 * 积分兑换状态枚举类
 */
public enum PointExchangeStatusEnum {

    PAYING(1,"兑换中"),
    SUCCESS(2,"已兑换"),
    FAIL(3,"兑换失败")
    ;

    public Integer code;
    public String desc;

    PointExchangeStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PointExchangeStatusEnum fromCode(Integer code){
        PointExchangeStatusEnum[] values = PointExchangeStatusEnum.values();
        for (PointExchangeStatusEnum value : values) {
            if(value.code.equals(code)){
                return value;
            }
        }
        return null;
    }
}
