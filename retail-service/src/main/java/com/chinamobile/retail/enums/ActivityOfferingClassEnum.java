package com.chinamobile.retail.enums;

import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import lombok.Getter;

public enum ActivityOfferingClassEnum {
    A04("A04", "DICT产品增值"),
    A06("A06", "联合销售"),
    A07("A07", "合同履约"),
    A08("A08", "OneNET独立服务"),
    A09("A09", "OnePark独立服务"),
    A10("A10", "OneTraffic独立服务"),
    A11("A11", "卡+X"),
    A12("A12", "行车卫士标准产品"),
    A13("A13", "软件服务");
    /**
     * 一级销售目录编号
     */
    private final String spuOfferingClass;

    /**
     * 前端显示内容
     */
    @Getter
    private final String display;

    ActivityOfferingClassEnum(String spuOfferingClass, String display) {
        this.spuOfferingClass = spuOfferingClass;
        this.display = display;
    }

    public String getSpuOfferingClass() {
        return spuOfferingClass;
    }

    /**
     * 前端显示内容
     *
     * @return
     */
    public static String getDisplay(String spuOfferingClass) {
        for (ActivityOfferingClassEnum value : ActivityOfferingClassEnum.values()) {
            if (value.spuOfferingClass.equals(spuOfferingClass)) {
                return value.display;
            }
        }
        return null;
    }
}
