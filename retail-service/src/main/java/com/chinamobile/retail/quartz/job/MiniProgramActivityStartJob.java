package com.chinamobile.retail.quartz.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.chinamobile.retail.constant.ActivityAuditStatusEnum;
import com.chinamobile.retail.constant.ActivityStatusEnum;
import com.chinamobile.retail.dao.MiniProgramActivityMapper;
import com.chinamobile.retail.pojo.entity.MiniProgramActivity;
import com.chinamobile.retail.quartz.GeneralJobData;
import com.chinamobile.retail.quartz.GeneralJobManager;
import com.chinamobile.retail.service.impl.MiniActivityServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/17 15:43
 * @description 小程序活动开始任务，将活动状态变为进行中
 */
@Slf4j
@Component
public class MiniProgramActivityStartJob extends QuartzJobBean {

    @Resource
    private MiniProgramActivityMapper miniProgramActivityMapper;

    @Resource
    private GeneralJobManager jobManager;
    @Resource
    private MiniActivityServiceImpl miniActivityService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        JobDataMap jobDataMap = jobExecutionContext.getJobDetail().getJobDataMap();
        String data = jobDataMap.getString("data");
        GeneralJobData<MiniProgramActivity> jobData = JSON.parseObject(data, new TypeReference<GeneralJobData<MiniProgramActivity>>(){});
        String activityId = jobData.getData().getId();
        MiniProgramActivity miniProgramActivity = miniProgramActivityMapper.selectByPrimaryKey(activityId);
        if (miniProgramActivity.getIsDelete() == 0 &&  ActivityAuditStatusEnum.PASSED.getStatus().equals(miniProgramActivity.getAuditStatus())) {
            Date now = new Date();
            if (now.before(miniProgramActivity.getStopTime())) {
                miniProgramActivity.setStatus(ActivityStatusEnum.IN_PROGRESS.getStatus());
                miniProgramActivity.setUpdateTime(now);
                miniProgramActivityMapper.updateByPrimaryKeySelective(miniProgramActivity);
                //审核通过后，立即进行排名
                log.info("执行小程序活动开始任务，活动id：{}，活动名称：{}", miniProgramActivity.getId(), miniProgramActivity.getName());
                miniActivityService.saveRankActivityUsers(miniProgramActivity);
                miniActivityService.saveRankActivityUserAwards(miniProgramActivity);
//                miniActivityService.syncLoadActivity2Redis(miniProgramActivity.getId());
            }

        }
        jobManager.deleteQuartzJob(jobData.getName(), jobData.getGroup());
    }
}
