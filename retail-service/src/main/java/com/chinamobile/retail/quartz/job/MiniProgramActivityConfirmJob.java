package com.chinamobile.retail.quartz.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.chinamobile.retail.constant.ActivityAuditStatusEnum;
import com.chinamobile.retail.constant.ActivityStatusEnum;
import com.chinamobile.retail.dao.MiniProgramActivityMapper;
import com.chinamobile.retail.pojo.entity.MiniProgramActivity;
import com.chinamobile.retail.quartz.GeneralJobData;
import com.chinamobile.retail.quartz.GeneralJobManager;
import com.chinamobile.retail.service.IMiniActivityService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/17 15:43
 * @description 小程序活动奖品确认任务，在该时间之后，开始发放奖品，活动状态不变，将排名和奖品保存到数据库
 */
@Slf4j
@Component
public class MiniProgramActivityConfirmJob extends QuartzJobBean {

    @Resource
    private MiniProgramActivityMapper miniProgramActivityMapper;

    @Resource
    private IMiniActivityService miniActivityService;

    @Resource
    private GeneralJobManager jobManager;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        JobDataMap jobDataMap = jobExecutionContext.getJobDetail().getJobDataMap();
        String data = jobDataMap.getString("data");
        GeneralJobData<MiniProgramActivity> jobData = JSON.parseObject(data, new TypeReference<GeneralJobData<MiniProgramActivity>>() {
        });
        String activityId = jobData.getData().getId();
        MiniProgramActivity miniProgramActivity = miniProgramActivityMapper.selectByPrimaryKey(activityId);
        Date now = new Date();
        if (miniProgramActivity.getIsDelete() == 0 && ActivityAuditStatusEnum.PASSED.getStatus().equals(miniProgramActivity.getAuditStatus())) {
            log.info("执行小程序活动奖品确认任务，活动id：{}，活动名称：{}", miniProgramActivity.getId(), miniProgramActivity.getName());
            miniProgramActivity.setStatus(ActivityStatusEnum.CONFIRMING.getStatus());
            miniProgramActivity.setUpdateTime(now);
            miniProgramActivityMapper.updateByPrimaryKeySelective(miniProgramActivity);
//                miniActivityService.syncLoadActivity2Redis(miniProgramActivity.getId());
            miniActivityService.saveRankActivityUserAwards(miniProgramActivity);
        }
        jobManager.deleteQuartzJob(jobData.getName(), jobData.getGroup());
    }
}
