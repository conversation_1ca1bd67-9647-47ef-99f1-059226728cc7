package com.chinamobile.retail.quartz;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/18 09:25
 * @description TODO
 */
@Slf4j
@Component
public class GeneralJobManager {

    @Resource
    private Scheduler scheduler;

    /**
     * 保存定时任务
     */
    public <T> void saveQuartzJob(GeneralJobData<T> data) {
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put("data", JSON.toJSONString(data));
        JobDetail jobDetail = JobBuilder.newJob(data.getJob())
            .withIdentity(data.getName(), data.getGroup())
            .usingJobData(jobDataMap)
            .storeDurably()
            .build();
        Trigger trigger = TriggerBuilder.newTrigger()
            .withIdentity(data.getName(), data.getGroup())
            .withSchedule(CronScheduleBuilder.cronSchedule(data.getCronExpression())
                    .withMisfireHandlingInstructionFireAndProceed())
            .forJob(jobDetail)
            .build();
        try {
            Date date = scheduler.scheduleJob(jobDetail, trigger);
            log.info("保存定时任务结果：{}", date);
            if (!scheduler.isShutdown()) {
                scheduler.start();
            }
            log.info("保存定时任务成功，jobData:{}", JSON.toJSONString(data));
        } catch (SchedulerException e) {
            log.error("定时任务保存失败，jobData:{}", JSON.toJSONString(data), e);
        }
    }

    /**
     * 删除定时任务
     */
    public void deleteQuartzJob(String name, String group) {
        try {
            TriggerKey triggerKey = new TriggerKey(name, group);
            scheduler.pauseTrigger(triggerKey);// 停止触发器
            scheduler.unscheduleJob(triggerKey);// 移除触发器
            JobKey jobKey = new JobKey(name, group);
            scheduler.deleteJob(jobKey);// 删除任务
            log.info("定时任务删除成功，name:{}，group:{}", name, group);
        } catch (Exception e) {
            log.error("定时任务删除失败，name:{}，group:{}", name, group, e);
        }
    }
}
