package com.chinamobile.retail.util.sm4.tool;


import com.chinamobile.retail.util.sm4.tool.utils.NumberTool;
import com.chinamobile.retail.util.sm4.tool.utils.SM4Base;
import com.chinamobile.retail.util.sm4.tool.utils.SM4_Context;
import com.chinamobile.retail.util.sm4.tool.utils.StreamTool;

import java.io.*;
import java.util.Base64;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SM4 {
    private String secretKey = "";
    private String iv = "";

    private String charset = "UTF-8";
    private boolean hexString = false;

    /**
     * SM4加密，默认ECB模式
     * @param key 16位秘钥
     * @param charset 字符集
     */
    public SM4(String key,  String charset) {
        this.secretKey = key;
        this.charset = charset;

    }

    /**
     * ECB模式加密
     * @param cipherText
     * @return
     */
    public String decryptDataToString_CBC(String cipherText) {
        return decryptDataToString_CBC(cipherText, charset);
    }

    /**
     *  ECB模式加密
     * @param cipherText 密文
     * @param charset 字符集
     * @return
     */
    public String decryptDataToString_CBC(String cipherText, String charset) {
        try {
            SM4_Context ctx = new SM4_Context();
            ctx.isPadding = true;
            ctx.mode = SM4Base.SM4_DECRYPT;

            byte[] keyBytes;
            byte[] ivBytes;
            if (hexString) {
                keyBytes = NumberTool.hexStringToBytes(secretKey);
                ivBytes = NumberTool.hexStringToBytes(iv);
            } else {
                keyBytes = secretKey.getBytes();
                ivBytes = iv.getBytes();
            }

            SM4Base sm4 = new SM4Base();
            sm4.sm4_setkey_dec(ctx, keyBytes);
            byte[] decrypted = sm4.sm4_crypt_cbc(ctx, ivBytes, Base64.getDecoder().decode(cipherText));
            return new String(decrypted, charset);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 使用CBC模式解密密文并返回字节数组结果。
     * @param cipherText
     * @return
     */
    public byte[] decryptData_CBC(String cipherText) {
        try {
            SM4_Context ctx = new SM4_Context();
            ctx.isPadding = true;
            ctx.mode = SM4Base.SM4_DECRYPT;

            byte[] keyBytes;
            byte[] ivBytes;
            if (hexString) {
                keyBytes = NumberTool.hexStringToBytes(secretKey);
                ivBytes = NumberTool.hexStringToBytes(iv);
            } else {
                keyBytes = secretKey.getBytes();
                ivBytes = iv.getBytes();
            }

            SM4Base sm4 = new SM4Base();
            sm4.sm4_setkey_dec(ctx, keyBytes);
            return sm4.sm4_crypt_cbc(ctx, ivBytes, Base64.getDecoder().decode(cipherText));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     *使用CBC模式解密字节数组并返回明文结果。
     * @param bytes
     * @return
     */
    public String decryptDataToString_CBC(byte[] bytes) {
        try {
            SM4_Context ctx = new SM4_Context();
            ctx.isPadding = true;
            ctx.mode = SM4Base.SM4_DECRYPT;

            byte[] keyBytes;
            byte[] ivBytes;
            if (hexString) {
                keyBytes = NumberTool.hexStringToBytes(secretKey);
                ivBytes = NumberTool.hexStringToBytes(iv);
            } else {
                keyBytes = secretKey.getBytes();
                ivBytes = iv.getBytes();
            }

            SM4Base sm4 = new SM4Base();
            sm4.sm4_setkey_dec(ctx, keyBytes);
            byte[] decrypted = sm4.sm4_crypt_cbc(ctx, ivBytes, bytes);
            return new String(decrypted, charset);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 使用ECB模式解密字节数组并返回结果。
     * @param bytes
     * @return
     */
    public byte[] decryptData_ECB(byte[] bytes) {
        try {
            SM4_Context ctx = new SM4_Context();
            ctx.isPadding = true;
            ctx.mode = SM4Base.SM4_DECRYPT;

            byte[] keyBytes;
            if (hexString) {
                keyBytes = NumberTool.hexStringToBytes(secretKey);
            } else {
                keyBytes = secretKey.getBytes();
            }

            SM4Base sm4 = new SM4Base();
            sm4.sm4_setkey_dec(ctx, keyBytes);
            return sm4.sm4_crypt_ecb(ctx, bytes);

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 使用ECB模式解密密文并返回字节数组结果。
     * @param cipherText
     * @return
     */
    public byte[] decryptData_ECB(String cipherText) {
        try {
            SM4_Context ctx = new SM4_Context();
            ctx.isPadding = true;
            ctx.mode = SM4Base.SM4_DECRYPT;

            byte[] keyBytes;
            if (hexString) {
                keyBytes = NumberTool.hexStringToBytes(secretKey);
            } else {
                keyBytes = secretKey.getBytes();
            }

            SM4Base sm4 = new SM4Base();
            sm4.sm4_setkey_dec(ctx, keyBytes);
            return sm4.sm4_crypt_ecb(ctx, Base64.getDecoder().decode(cipherText));

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 使用ECB模式解密密文并返回明文结果。
     * @param cipherText
     * @return
     */
    public String decryptDataToString_ECB(String cipherText) {
        return decryptDataToString_ECB(cipherText, charset);
    }

    /**
     * 使用ECB模式解密密文并返回明文结果，可以指定字符集。
     * @param cipherText
     * @param charset
     * @return
     */
    public String decryptDataToString_ECB(String cipherText, String charset) {
        try {
            SM4_Context ctx = new SM4_Context();
            ctx.isPadding = true;
            ctx.mode = SM4Base.SM4_DECRYPT;

            byte[] keyBytes;
            if (hexString) {
                keyBytes = NumberTool.hexStringToBytes(secretKey);
            } else {
                keyBytes = secretKey.getBytes();
            }

            SM4Base sm4 = new SM4Base();
            sm4.sm4_setkey_dec(ctx, keyBytes);
            byte[] decrypted = sm4.sm4_crypt_ecb(ctx, Base64.getDecoder().decode(cipherText));
            return new String(decrypted, charset);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 使用CBC模式加密字节数组并返回Base64编码的密文结果。
     * @param bytes
     * @return
     */
    public String encryptDataToString_CBC(byte[] bytes) {
        try {
            SM4_Context ctx = new SM4_Context();
            ctx.isPadding = true;
            ctx.mode = SM4Base.SM4_ENCRYPT;

            byte[] keyBytes;
            byte[] ivBytes;
            if (hexString) {
                keyBytes = NumberTool.hexStringToBytes(secretKey);
                ivBytes = NumberTool.hexStringToBytes(iv);
            } else {
                keyBytes = secretKey.getBytes();
                ivBytes = iv.getBytes();
            }

            SM4Base sm4 = new SM4Base();
            sm4.sm4_setkey_enc(ctx, keyBytes);
            byte[] encrypted = sm4.sm4_crypt_cbc(ctx, ivBytes, bytes);
            String cipherText = Base64.getEncoder().encodeToString(encrypted);
            if (cipherText != null && cipherText.trim().length() > 0) {
                Pattern p = Pattern.compile("\\s*|\t|\r|\n");
                Matcher m = p.matcher(cipherText);
                cipherText = m.replaceAll("");
            }
            return cipherText;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 使用CBC模式加密明文字符串并返回Base64编码的密文结果。
     * @param plainText
     * @return
     */
    public String encryptDataToString_CBC(String plainText) {
        return encryptDataToString_CBC(plainText, charset);
    }

    /**
     * 使用CBC模式加密明文字符串并返回Base64编码的密文结果，可以指定字符集。
     * @param plainText
     * @param charset
     * @return
     */
    public String encryptDataToString_CBC(String plainText, String charset) {
        try {
            SM4_Context ctx = new SM4_Context();
            ctx.isPadding = true;
            ctx.mode = SM4Base.SM4_ENCRYPT;

            byte[] keyBytes;
            byte[] ivBytes;
            if (hexString) {
                keyBytes = NumberTool.hexStringToBytes(secretKey);
                ivBytes = NumberTool.hexStringToBytes(iv);
            } else {
                keyBytes = secretKey.getBytes();
                ivBytes = iv.getBytes();
            }

            SM4Base sm4 = new SM4Base();
            sm4.sm4_setkey_enc(ctx, keyBytes);
            byte[] encrypted = sm4.sm4_crypt_cbc(ctx, ivBytes, plainText.getBytes(charset));
            String cipherText = Base64.getEncoder().encodeToString(encrypted);
            if (cipherText != null && cipherText.trim().length() > 0) {
                Pattern p = Pattern.compile("\\s*|\t|\r|\n");
                Matcher m = p.matcher(cipherText);
                cipherText = m.replaceAll("");
            }
            return cipherText;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 使用CBC模式加密明文字符串并返回字节数组结果，可以指定字符集。
     * @param plainText
     * @param charset
     * @return
     */
    public byte[] encryptData_CBC(String plainText, String charset) {
        try {
            return encryptData_CBC(plainText.getBytes(charset));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 使用CBC模式加密字节数组并返回结果。
     * @param bytes
     * @return
     */
    public byte[] encryptData_CBC(byte[] bytes) {
        try {
            SM4_Context ctx = new SM4_Context();
            ctx.isPadding = true;
            ctx.mode = SM4Base.SM4_ENCRYPT;

            byte[] keyBytes;
            byte[] ivBytes;
            if (hexString) {
                keyBytes = NumberTool.hexStringToBytes(secretKey);
                ivBytes = NumberTool.hexStringToBytes(iv);
            } else {
                keyBytes = secretKey.getBytes();
                ivBytes = iv.getBytes();
            }

            SM4Base sm4 = new SM4Base();
            sm4.sm4_setkey_enc(ctx, keyBytes);
            return sm4.sm4_crypt_cbc(ctx, ivBytes, bytes);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 使用ECB模式加密字节数组并返回结果。
     * @param bytes
     * @return
     */
    public byte[] encryptData_ECB(byte[] bytes) {
//		SM4_Context ctx = new SM4_Context();
//		SM4Base sm4 = new SM4Base();
//		try {
//			return sm4.sm4_crypt_ecb(ctx, bytes);
//		} catch (Exception e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//			return null;
//		}

        try {
            SM4_Context ctx = new SM4_Context();
            ctx.isPadding = true;
            ctx.mode = SM4Base.SM4_ENCRYPT;

            byte[] keyBytes;
            if (hexString) {
                keyBytes = NumberTool.hexStringToBytes(secretKey);
            } else {
                keyBytes = secretKey.getBytes();
            }

            SM4Base sm4 = new SM4Base();
            sm4.sm4_setkey_enc(ctx, keyBytes);
            return sm4.sm4_crypt_ecb(ctx, bytes);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 使用ECB模式加密文件并返回结果
     * @param f
     * @return
     * @throws IOException
     */
    public byte[] encryptData_ECB(File f) throws IOException {
        if(f.exists()) {
            InputStream inStream = new FileInputStream(f);
            return encryptData_ECB(StreamTool.readInputStream2ByteArray(inStream));
        } else {
            throw new IOException("File not Found");
        }

    }

    /**
     * 使用ECB模式加密明文字符串并返回字节数组结果。
     * @param plainText
     * @return
     */
    public byte[] encryptData_ECB(String plainText) {
        return encryptData_ECB(plainText, charset);
    }

    /**
     * 使用ECB模式加密明文字符串并返回字节数组结果，可以指定字符集。
     * @param plainText
     * @param charset
     * @return
     */
    public byte[] encryptData_ECB(String plainText, String charset) {
        try {
            return encryptData_ECB(plainText.getBytes(charset));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 使用ECB模式加密明文字符串并返回Base64编码的密文结果。
     * @param plainText 明文
     * @return
     */
    public String encryptDataToString_ECB(String plainText) {
        return encryptDataToString_ECB(plainText, charset);
    }

    /**
     * 使用ECB模式加密明文字符串并返回Base64编码的密文结果，可以指定字符集。
     * @param plainText 明文
     * @param charset 字符集
     * @return
     */
    public String encryptDataToString_ECB(String plainText, String charset) {
        try {
            SM4_Context ctx = new SM4_Context();
            ctx.isPadding = true;
            ctx.mode = SM4Base.SM4_ENCRYPT;

            byte[] keyBytes;
            if (hexString) {
                keyBytes = NumberTool.hexStringToBytes(secretKey);
            } else {
                keyBytes = secretKey.getBytes();
            }

            SM4Base sm4 = new SM4Base();
            sm4.sm4_setkey_enc(ctx, keyBytes);
            byte[] encrypted = sm4.sm4_crypt_ecb(ctx, plainText.getBytes(charset));
            String cipherText = Base64.getEncoder().encodeToString(encrypted);
            if (cipherText != null && cipherText.trim().length() > 0) {
                Pattern p = Pattern.compile("\\s*|\t|\r|\n");
                Matcher m = p.matcher(cipherText);
                cipherText = m.replaceAll("");
            }
            return cipherText;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    /**
     * @return the iv
     */
    public String getIv() {
        return iv;
    }

    /**
     * @return the secretKey
     */
    public String getSecretKey() {
        return secretKey;
    }

    /**
     * @return the hexString
     */
    public boolean isHexString() {
        return hexString;
    }

    /**
     * @param hexString the hexString to set
     */
    public void setHexString(boolean hexString) {
        this.hexString = hexString;
    }

    /**
     * @param iv the iv to set
     */
    public void setIv(String iv) {
        this.iv = iv;
    }

    /**
     * @param secretKey the secretKey to set
     */
    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }


}
