package com.chinamobile.retail.util.cache;

import com.chinamobile.iot.sc.common.redis.Processor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class RedisCacheUtil {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RedisTemplate redisTemplate;

    private final ExecutorService executorService = new ThreadPoolExecutor(
            50,
            50,
            1L,
            TimeUnit.MINUTES,
            new LinkedBlockingDeque<>(10000),
            new ThreadPoolExecutor.CallerRunsPolicy()
    );

    public interface DataLoader<V> {
        V load();
    }

    public <K, V> V get(K key) {
        return (V) redisTemplate.opsForValue().get(key);
    }

    /**
     * 加载数据，如果没有命中 Redis缓存 则从 DataLoader 加载
     *
     * @param key
     * @param lockKey
     * @param loader
     * @return
     */
    public <K, V> V loadAndCache(K key, String lockKey, DataLoader<V> loader) {
        // 第一次检查缓存
        log.info("第一次检查缓存，key：{}", key);
        V value = (V) redisTemplate.opsForValue().get(key);
        if (value != null) {
            log.info("第一次检查缓存命中，key：{}", key);
            return value; // 缓存命中，直接返回
        }
        log.info("第一次检查缓存未命中，key：{}", key);

        // 缓存未命中，获取写锁加载数据
        RLock rl = redissonClient.getLock(lockKey);
        try {
            rl.lock();
            // 再次检查缓存，防止其他线程已加载完成
            log.info("第二次检查缓存，key：{}", key);
            value = (V) redisTemplate.opsForValue().get(key);
            if (value == null) {
                log.info("第二次检查缓存未命中，重新加载，key：{}", key);
                value = loader.load();
                if (!ObjectUtils.isEmpty(value)) {
                    log.info("重新加载成功，key：{}", key);
                    redisTemplate.opsForValue().set(key, value); // 缓存新值
                } else {
                    log.info("重新加载失败，没有数据，key：{}", key);
                }
            } else {
                log.info("第二次检查缓存命中，key：{}", key);
            }
            return value;
        } finally {
            if (rl.isHeldByCurrentThread()) {
                rl.unlock(); // 释放写锁
            }
        }
    }


    /**
     * 加载数据，如果没有命中 Redis缓存 则从 DataLoader 加载
     *
     * @param key
     * @param lockKey
     * @param timeout
     * @param unit
     * @param loader
     * @return
     */
    public <K, V> V loadAndCache(K key, String lockKey, long timeout, TimeUnit unit, DataLoader<V> loader) {
        // 第一次检查缓存
        log.info("第一次检查缓存，key：{}", key);
        V value = (V) redisTemplate.opsForValue().get(key);
        if (value != null) {
            log.info("第一次检查缓存命中，key：{}", key);
            return value; // 缓存命中，直接返回
        }
        log.info("第一次检查缓存未命中，key：{}", key);

        // 缓存未命中，获取写锁加载数据
        RLock rl = redissonClient.getLock(lockKey);
        try {
            rl.lock();
            // 再次检查缓存，防止其他线程已加载完成
            log.info("第二次检查缓存，key：{}", key);
            value = (V) redisTemplate.opsForValue().get(key);
            if (value == null) {
                log.info("第二次检查缓存未命中，重新加载，key：{}", key);
                value = loader.load();
                if (!ObjectUtils.isEmpty(value)) {
                    log.info("重新加载成功，key：{}", key);
                    redisTemplate.opsForValue().set(key, value, timeout, unit); // 缓存新值
                } else {
                    log.info("重新加载失败，没有数据，key：{}", key);
                }
            } else {
                log.info("第二次检查缓存命中，key：{}", key);
            }
            return value;
        } finally {
            if (rl.isHeldByCurrentThread()) {
                rl.unlock(); // 释放写锁
            }
        }
    }

    /**
     * 缓存数据到 Redis
     *
     * @param key
     * @param value
     * @param lockKey
     */
    public <K, V> void cache(K key, V value, String lockKey) {
        smartStringLock(lockKey, () -> {
            redisTemplate.opsForValue().set(key, value);
            return null;
        });
    }

    /**
     * 缓存数据到 Redis
     *
     * @param key
     * @param value
     * @param lockKey
     * @param timeout
     * @param unit
     */
    public <K, V> void cache(K key, V value, String lockKey, long timeout, TimeUnit unit) {
        smartStringLock(lockKey, () -> {
            redisTemplate.opsForValue().set(key, value, timeout, unit);
            return null;
        });
    }

    /**
     * 缓存数据到 Redis，并发情况下，只允许一个线程执行成功，避免写入重复内容
     *
     * @param key
     * @param value
     * @param lockKey
     */
    public <K, V> void cacheOnce(K key, V value, String lockKey) {
        tryExecuteWithStringLock(lockKey, () -> {
            redisTemplate.opsForValue().set(key, value);
            return null;
        });
    }

    /**
     * 缓存数据到 Redis，并发情况下，只允许一个线程执行成功，避免写入重复内容
     *
     * @param key
     * @param value
     * @param lockKey
     * @param timeout
     * @param unit
     */
    public <K, V> void cacheOnce(K key, V value, String lockKey, long timeout, TimeUnit unit) {
        tryExecuteWithStringLock(lockKey, () -> {
            redisTemplate.opsForValue().set(key, value, timeout, unit);
            return null;
        });
    }

    /**
     * 异步更新缓存
     *
     * @param key
     * @param lockKey
     * @param loader
     */
    public <K, V> void asyncUpdateCache(K key, String lockKey, DataLoader<V> loader) {
        executorService.execute(() -> {
            V value = loader.load();
            if (!ObjectUtils.isEmpty(value)) {
                cache(key, value, lockKey);
            }
        });
    }

    /**
     * 异步更新缓存
     *
     * @param key
     * @param lockKey
     * @param timeout
     * @param unit
     * @param loader
     */
    public <K, V> void asyncUpdateCache(K key, String lockKey, long timeout, TimeUnit unit, DataLoader<V> loader) {
        executorService.execute(() -> {
            V value = loader.load();
            if (!ObjectUtils.isEmpty(value)) {
                cache(key, value, lockKey, timeout, unit);
            }
        });
    }

    public <K> void delete(K key) {
        redisTemplate.delete(key);
    }

    public <K> void deleteAll(K key) {
        Set<String> keys = redisTemplate.keys(key + "*");
        List<String> batchDeleteKeys = new ArrayList<>(keys);
        if (!batchDeleteKeys.isEmpty()) {
            redisTemplate.delete(batchDeleteKeys);
        }
    }

    private <T> void smartStringLock(String lockKey, Processor<T> proc) {
        RLock rl = redissonClient.getLock(lockKey);
        try {
            rl.lock();
            proc.doProcess();
        } finally {
            if (rl.isHeldByCurrentThread()) {
                rl.unlock();
            }
        }
    }

    private <T> void tryExecuteWithStringLock(String lockKey, Processor<T> proc) {
        RLock rl = redissonClient.getLock(lockKey);
        try {
            boolean lockAcquired = rl.tryLock();
            if (lockAcquired) {
                proc.doProcess();
            } else {
                log.warn("无法获取锁，放弃执行，lockKey: {}", lockKey);
            }
        } finally {
            if (rl.isHeldByCurrentThread()) {
                rl.unlock();
            }
        }
    }


}
