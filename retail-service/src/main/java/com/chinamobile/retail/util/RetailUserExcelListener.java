package com.chinamobile.retail.util;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.chinamobile.retail.pojo.dto.RetailUserImportDTO;
import lombok.extern.slf4j.Slf4j;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
public class RetailUserExcelListener extends AnalysisEventListener<RetailUserImportDTO> {
//public class RetailUserExcelListener extends PageReadListener<RetailUserImportDTO> {

    private List<RetailUserImportDTO> cachedDataList = new ArrayList<>();

    public List<RetailUserImportDTO> getCachedDataList(){
        return cachedDataList;
    }

    @Override
    public void invoke(RetailUserImportDTO data, AnalysisContext analysisContext) {
        log.info("invoke Enter");
        int currentRow = analysisContext.readRowHolder().getRowIndex()+1;
        log.info("当前行：{}; 解析到一条数据：{},",currentRow, data);
        boolean exist = test(data);
        if(!exist){
            //该行被忽略
            log.info("该行被忽略...");
            return;
        }
        cachedDataList.add(data);
        log.info("listener list size = {}",cachedDataList.size());
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        if(CollectionUtils.isEmpty(cachedDataList)){
            return;
        }
    }


//    private boolean isLineNullValue(T data){
//        if(data.instanceof String){
//            return ObjectsUtil.isNull(data);
//        }
//    }


    private boolean test(RetailUserImportDTO data){
        boolean exist = false;
        String[] fieldName = getFieldName(data);
        for(String string : fieldName){
            Object fieldValue = getFieldValue(data, string);
            if(fieldValue instanceof String){
                if(StringUtils.isNotBlank((String)fieldValue)){
                    exist = true;
                }
            }
            if(!Objects.isNull(fieldValue)){
                exist = true;
            }
        }
        return exist;
    }


    public static String[] getFieldName(Object o){
        Field[] fields=o.getClass().getDeclaredFields();
        String[] fieldNames=new String[fields.length];
        for(int i=0; i<fields.length; i++){
            if(fields[i].isAnnotationPresent(ExcelProperty.class)){
                fieldNames[i]=fields[i].getName();
            }
        }
        return fieldNames;
    }


    public static Object getFieldValue(Object o, String name){
        try {
            Field[] fields = o.getClass().getDeclaredFields();
            Object object = null;
            for (Field field : fields) {
                // 可以获取到私有属性
                field.setAccessible(true);
                if (field.getName().toUpperCase().equals(name.toUpperCase())) {
                    object = field.get(o);
                    break;
                }
            }
            return object;
        }catch (Exception e) {
            log.warn("获取值异常,field={}", o, e);
            return false;
        }
    }


}
