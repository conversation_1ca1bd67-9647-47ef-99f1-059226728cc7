package com.chinamobile.retail.util;

import cn.hutool.core.util.HexUtil;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * Md5Util:
 *
 * @date 2021/3/5 11:46
 */
public class Md5Util {

    /**
     * md5 加密 （如 获取token接口 加密phone）
     *
     * @param data
     * @return
     * @throws NoSuchAlgorithmException
     */
    public static String md5DigestAsHex(String data) throws NoSuchAlgorithmException {
        MessageDigest messageDigest = MessageDigest.getInstance("MD5");
        byte[] digest = messageDigest.digest(data.getBytes(StandardCharsets.UTF_8));
        return HexUtil.encodeHexStr(digest, false);
    }

    public static String md5DigestAsHexToLowerCase(String data) throws NoSuchAlgorithmException {
        MessageDigest messageDigest = MessageDigest.getInstance("MD5");
        byte[] digest = messageDigest.digest(data.getBytes(StandardCharsets.UTF_8));
        return HexUtil.encodeHexStr(digest, true);
    }

    public static void main(String[] args) throws NoSuchAlgorithmException {
        System.out.println(md5DigestAsHex("18664805491"));
    }
}
