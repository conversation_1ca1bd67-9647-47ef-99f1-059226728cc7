package com.chinamobile.retail.util.qiniu;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Arrays;

/**
 * 枚举工具
 * <AUTHOR>
 *
 */
@Slf4j
public class Utils {

    //MD5计算大小控制
    private static final  int limit = 5*1024;//（5KB内的信息 md5计算 基本上在小于 1毫秒范围内）
    private static final  int mid_interval = 100; // 大于5Kb，则进行两头+中间取值（一定范围）+长度 来进行md5计算，基本上差10倍以上数量级

    public static String getKey(MultipartFile file) {
        byte[] buffer = new byte[limit];
        if(file.getSize()<limit)
            buffer = new byte[(int)file.getSize()];

        try(InputStream inputStream = file.getInputStream()) {
            inputStream.read(buffer);
            return mergeFile(DigestUtils.md5Hex(buffer),file.getOriginalFilename());
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return file.getOriginalFilename();
    }

    public static String getKey(File file) {
        byte[] buffer = new byte[limit];
        if(file.length()<limit)
            buffer = new byte[(int)file.length()];

        try(InputStream inputStream = new FileInputStream(file)) {
            inputStream.read(buffer);
            return mergeFile(DigestUtils.md5Hex(buffer),file.getName());
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return file.getName();
    }

    private static  String mergeFile(String md5,String fname) {
        try {
            StringBuilder sb = new StringBuilder(md5);
            String extention = StringUtils.substringAfterLast(fname, ".");
            if(StringUtils.isBlank(extention)){
                return sb.toString();
            }
            return sb.append(".").append(extention).toString();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return fname;
    }


   public static String getKey(String str,String name){
        int l = str.length();
        if(l>limit){
            //去中间文件
            int mid = l >>1;
            int low = mid-mid_interval;
            int higth = mid+mid_interval;
            //长度+截取的内容
            byte[] bytes = (str.charAt(0) + l + str.substring(low, higth) + str.charAt(l - 1)).getBytes();
            return merge(bytes,name);
        }else{
            return merge(str.getBytes(),name);
        }
    }

    /**
     * 文件字节
     * @param b 字节数据t
     * @param name 文件名称
     * @return String
     */
    public static String getKey(byte[] bt,String name ){
        return merge(bt,name);
    }

    private static  String merge(byte[] b,String fname) {
        try {
            StringBuilder sb = new StringBuilder(getKey(b));
            String extention = StringUtils.substringAfterLast(fname, ".");
            if(StringUtils.isBlank(extention)){
                return sb.toString();
            }
            return sb.append(".").append(extention).toString();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return fname;
    }

    public static  String getKey(byte[] bt) {
        try {
            int l = bt.length;
            if(l>limit){
                //取中间信息
                int mid = l >>1;
                int low = mid-mid_interval;
                int higth = mid+mid_interval;
                //长度+截取的内容
                byte[] b = Arrays.copyOfRange(bt, low, higth);
                String lengStr = l + "";
                for (int i = 0; i < lengStr.length(); i++) {
                    b[i+1] = (byte) lengStr.charAt(i);
                }
                b[0] = bt[0];
                b[b.length-1] = bt[l-1];
                return DigestUtils.md5Hex(b);
            }
            return DigestUtils.md5Hex(bt);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }
}
