package com.chinamobile.retail.util;

import com.chinamobile.iot.sc.common.utils.BaseUtils;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.retail.exception.StatusContant;
import com.chinamobile.retail.pojo.dto.lakala.LakalaPublicRequestDTO;
import com.chinamobile.retail.pojo.dto.lakala.LakalaPublicResponseDTO;
import lombok.extern.slf4j.Slf4j;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.Date;
import java.util.Map;

/**
 * created by liuxia<PERSON> on 2022/10/28 09:43
 * 拉卡拉请求和响应的加密，解密，签名类
 */
@Slf4j
public class LakalaUtil {

    /**
     * 使用拉卡拉的秘钥，对公共响应验签和解密，获取业务参数的jsonString
     */
    public static String getDecryptedBizContentFromPublicResponse(LakalaPublicResponseDTO publicResponse, String publicKey) throws Exception {
        //验签
        Map<String, String> stringStringMap = BaseUtils.objectToStringMap(publicResponse);
        String prepareCheck = RSAUtils.getEncodeSignContent(stringStringMap);
        boolean a = RSAUtils.verifyByPublicKey(prepareCheck.getBytes(),publicKey, publicResponse.getSign());
        if(!a){
            throw new BusinessException(StatusContant.LAKALA_SIGN_ERROR);
        }
        //解密
        String bizContent = RSAUtils.decryptByPublicKey(URLDecoder.decode(publicResponse.getData(),"UTF-8"), publicKey);
        return bizContent;
    }

    /**
     * 获取lakala公共请求
     */
    public static LakalaPublicRequestDTO getPublicRequestDTO(String supplierUniqueId, String privateKey, String jsonStr, String method) throws Exception {
        if(jsonStr == null){
            jsonStr = "{}";
        }
        log.info("业务参数转码前:{}",jsonStr);
        String bizContent = URLEncoder.encode(jsonStr);
//        log.info("业务参数加密前:{}",bizContent);
        //对业务参数加密
        bizContent = RSAUtils.encryptByPrivateKey(bizContent, privateKey);
//        log.info("业务参数加密后:{}",bizContent);
        LakalaPublicRequestDTO publicRequestDTO = new LakalaPublicRequestDTO();
        publicRequestDTO.setChannel(supplierUniqueId);
        publicRequestDTO.setMethod(method);
        publicRequestDTO.setTimestamp(DateTimeUtil.formatDate(new Date(), DateTimeUtil.DEFAULT_DATE_DEFAULT));
        //将业务参数密文放入请求参数中
        publicRequestDTO.setBizContent(bizContent);
        return publicRequestDTO;
    }

    /**
     * 获取拉卡拉请求签名
     */
    public static String getSign(String privateKey, Map<String, String> stringStringMap) throws Exception {
        //请求参数排序
        String prepareSign = RSAUtils.getEncodeSignContent(stringStringMap);
//        log.info("待签名内容:{}",prepareSign);
        //加签
        return RSAUtils.signByPrivateKey(prepareSign.getBytes(),privateKey);
    }

}
