package com.chinamobile.retail.request.order2c;

import lombok.Data;

@Data
public class OrderOrgBizInfoDTO {
    /**
     * 订单收入归属省公司组织机构标识	省公司的组织机构标识，如果客户没有组织机构，则设置默认值：0。
     * 若组织机构已补充省公司组织机构标识则该字段为省公司组织机构标识；若组织机构未补充省公司组织机构标识则该字段为空。
     * 如果本字段不为空，则表示该笔收入所属的省公司组织机构。
     */
    private String orderOrgBizCode;
    /**
     * 组织级别	对应订单收入归属省公司组织机构级别
     * 1：集团
     * 2：省
     * 3：地市
     * 4：区县
     * 5：营业厅
     */
    private String orgLevel;
    /**
     * 全组织机构名称
     * 全组织机构名称为拼接字段，
     * 按订单收入归属省公司组织机构的父组织机构拼接，
     * 最多取订单收入归属组织机构及以上共5层父组织机构，
     * 各级别间以“-”连接；
     */
    private String orgName;

    /**
     * 归属省内组织机构全称
     * 当ordertype=00且offeringClass=A11:卡+X硬件时，取省BOSS回传的省内集团客户上的“归属省内组织机构全称”
     */
    private String provinceOrgName;
}
