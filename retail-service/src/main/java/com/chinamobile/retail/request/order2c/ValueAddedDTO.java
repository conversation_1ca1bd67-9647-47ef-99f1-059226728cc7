package com.chinamobile.retail.request.order2c;

import lombok.Data;

/**
 * 订单卡增值服务包信息
 */
@Data
public class ValueAddedDTO {

    /**
     * 商品名称
     */
    private String offeringName;

    /**
     * 商品类型
     */
    private String offeringType;

    /**
     * 主商品
     * 01：和对讲个人
     * 02：物联卡个人
     * 03：窄带网个人
     * 16：行车卫士个人
     */
    private String mainOffering;

    /**
     * 资费单价,取商城卡增值服务包配置的资费单价（元）
     */
    private String expensesPrice;

    /**
     * 资费有效期
     */
    private String expensesTerm;

    /**
     * 资费有效期单位
     */
    private String validityPeriodUnit;

    /**
     * 订购数量
     */
    private String orderQuantity;
}
