package com.chinamobile.retail.request.order2c;

import lombok.Data;

/**
 * @Author: YSC
 * @Date: 2021/11/3 17:42
 * @Description:
 */
@Data
public class AtomOfferingInfoDTO {
    private String offeringCode;
    private Long quantity;
    private Long price;
    private String actionType;
    private String deductPrice;
    /**
     * orderType为00时必传(代客下单)
     * 单位：厘，客户经理录入的含税合同单价
     */
    private String taxSettlePrice;

    private String atomOfferingVersion;

}
