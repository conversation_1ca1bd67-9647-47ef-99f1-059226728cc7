package com.chinamobile.retail.request.order2c;

import lombok.Data;

import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/11/3 17:34
 * @Description:
 */
@Data
public class SpuOfferingInfoDTO {
    /**
     * 一级管理目录
     * A01-基础产品
     * A02-行业应用
     * A03-硬件终端
     * A06-软件功能费+（代销类）硬件
     * A07-软件功能费+（合同履约类）硬件
     * A04：（DICT）产品增值服务包
     * A08： OneNET独立服务
     * A09：OnePark独立服务
     * A10：OneTraffic独立服务
     * A11:卡+X硬件
     * A12：行车卫士标准产品
     * A13：软件服务
     * A14: OneCyber标准产品
     * A15：千里眼独立服务
     * A16：和对讲独立服务
     * A17: 云视讯独立服务
     */
    private String offeringClass;
    /**
     * 商品组编码/销售商品编码
     */
    private String offeringCode;
    /**
     * 服务商唯一标识
     */
    private String supplierCode;
    /**
     * 订单项属性动作
     */
    private String actionType;
    /**
     * 规格商品
     */
    private List<SkuOfferingInfoDTO> skuOfferingInfo;

    /**
     * spu版本号
     */
    private String spuOfferingVersion;
    /**
     * 上架平台  0：物联网商城1：视联网商城
     */
    private String listPlatform;
}
