package com.chinamobile.retail.request;

import com.chinamobile.retail.request.order2c.OrderInfoDTO;
import lombok.Data;

import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/11/3 16:59
 * @Description: 订单同步请求体
 */
@Data
public class Order2CInfoAtomRequest {
    /**
     * 订单基本信息
     */
    OrderInfoDTO orderInfo;
    /**
     * 原子订单基本信息
     */
    List<Order2cKfkAtomInfo> order2cAtomInfos;
    @Data
    public static class Order2cKfkAtomInfo {
        private String orderId;
        private String id;
        private Long skuPrice;
        private Long atomQuantity;
        private String skuOfferingCode;
    }
}
