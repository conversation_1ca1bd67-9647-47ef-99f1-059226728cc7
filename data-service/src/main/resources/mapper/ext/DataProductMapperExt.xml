<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.data.dao.ext.DataProductMapperExt">
    <select id="listDataProductUserOrder" resultType="com.chinamobile.data.pojo.mapper.DataProductUserOrderDO">
        select
          o2i.business_code businessCode ,
          count(o2i.order_id) orderCount
        from
          order_2c_info o2i
        where
        ((o2i.order_type ='01' and o2i.status != 4) or((o2i.order_type = '00' or o2i.order_type = '02' or o2i.order_type = '03') and o2i.order_status in(12,7)))
        and o2i.business_code in ('SyncIndividualOrderInfo','SyncGrpOrderInfo','SyncValetOrderInfo')
        <if test="timeParam.startTime != null">
            and
            CASE
            when o2i.order_type ='01' then o2i.create_time <![CDATA[ >= ]]> #{timeParam.startTime}
            when (o2i.order_type = '00' or o2i.order_type = '02' or o2i.order_type = '03') then o2i.valet_order_complete_time <![CDATA[ >= ]]> #{timeParam.startTime}
            ELSE o2i.create_time <![CDATA[ >= ]]> #{timeParam.startTime}
            END
        </if>
        <if test="timeParam.endTime != null">
            and
            CASE
            when o2i.order_type ='01' then o2i.create_time <![CDATA[ <= ]]> #{timeParam.endTime}
            when (o2i.order_type = '00' or o2i.order_type = '02' or o2i.order_type = '03') then o2i.valet_order_complete_time <![CDATA[ <= ]]> #{timeParam.endTime}
            ELSE o2i.create_time <![CDATA[ <= ]]> #{timeParam.endTime}
            END
        </if>
        group by o2i.business_code
    </select>

    <select id="listDataProductSale" resultType="com.chinamobile.data.pojo.mapper.DataProductSaleDO">
        SELECT
        occ.spu_offering_class spuOfferingClass,
        CASE
            WHEN occ.spu_offering_class = 'A04' THEN 'DICT产品增值'
            WHEN occ.spu_offering_class = 'A06' THEN '联合销售'
            WHEN occ.spu_offering_class = 'A07' THEN '合同履约'
            WHEN occ.spu_offering_class = 'A08' THEN 'OneNET独立'
            WHEN occ.spu_offering_class = 'A09' THEN 'OnePark独立服务'
            WHEN occ.spu_offering_class = 'A12' THEN '行车卫士标准产品'
            WHEN occ.spu_offering_class = 'A13' THEN '软件服务'
            WHEN occ.spu_offering_class = 'A14' THEN 'OneCyber标准产品'
            ELSE '其他'
        END offeringClassName,
        SUM( occ.atomOrderAmount ) AS offeringClassSell,
        count( occ.order_id ) AS orderCount,
        occ.productType,
        CASE
            WHEN occ.productType = 1 THEN '自研'
            WHEN occ.productType = 2 THEN '生态'
            ELSE '其他'
        END productTypeName
        FROM
            (
            SELECT
            oc.order_id AS order_id,
           (oc.sku_quantity * oc.sku_price)  AS atomOrderAmount,
            oi.spu_offering_class spu_offering_class,
            pp.type productType
            FROM
            product_property pp,
            standard_service ss,
            atom_std_service ass,
            atom_offering_info aoi,
            order_2c_atom_info oc,
            order_2c_info oi
            WHERE
				pp.id = ss.product_property_id
            and ss.id = ass.std_service_id
            and ass.atom_id = aoi.id
            and aoi.spu_code = oc.spu_offering_code
            and aoi.sku_code = oc.sku_offering_code
            and aoi.offering_code = oc.atom_offering_code
            and aoi.offering_class = oc.atom_offering_class
            and oc.order_id = oi.order_id
            and (((oc.order_type = '01' and oc.order_status != 8)  or ((oc.order_type = '00' or oc.order_type = '02' or oc.order_type = '03') and oc.order_status in (12,7)))  AND ( oc.atom_offering_class != 'S' OR oc.atom_offering_class is null ))
            <if test="timeParam.startTime != null">
                and
                CASE
                when oi.order_type ='01' then oi.create_time <![CDATA[ >= ]]> #{timeParam.startTime}
                when (oi.order_type = '00' or oi.order_type = '02' or oi.order_type = '03') then oi.valet_order_complete_time <![CDATA[ >= ]]> #{timeParam.startTime}
                ELSE oi.create_time <![CDATA[ >= ]]> #{timeParam.startTime}
                END
            </if>
            <if test="timeParam.endTime != null">
                and
                CASE
                when oi.order_type ='01' then oi.create_time <![CDATA[ <= ]]> #{timeParam.endTime}
                when (oi.order_type = '00' or oi.order_type = '02'  or oi.order_type = '03') then oi.valet_order_complete_time <![CDATA[ <= ]]> #{timeParam.endTime}
                ELSE oi.create_time <![CDATA[ <= ]]> #{timeParam.endTime}
                END
            </if>
            GROUP BY
            oc.order_id ) occ
        GROUP BY occ.productType,occ.spu_offering_class
    </select>

    <select id="listDataProductUserTotalPrice" resultType="com.chinamobile.data.pojo.mapper.DataProductUserTotalPriceDO">
        select
         o2i.business_code businessCode,
          o2i.total_price totalPrice
        from
          order_2c_info o2i
        where
        ((o2i.order_type ='01' and o2i.status != 4) or((o2i.order_type = '00' or o2i.order_type = '02' or o2i.order_type = '03') and o2i.order_status in(12,7)))
        and o2i.business_code in ('SyncIndividualOrderInfo','SyncGrpOrderInfo','SyncValetOrderInfo')
        <if test="timeParam.startTime != null">
            and
            CASE
            when o2i.order_type ='01' then o2i.create_time <![CDATA[ >= ]]> #{timeParam.startTime}
            when (o2i.order_type = '00' or o2i.order_type = '02' or o2i.order_type = '03') then o2i.valet_order_complete_time <![CDATA[ >= ]]> #{timeParam.startTime}
            ELSE o2i.create_time <![CDATA[ >= ]]> #{timeParam.startTime}
            END
        </if>
        <if test="timeParam.endTime != null">
            and
            CASE
            when o2i.order_type ='01' then o2i.create_time <![CDATA[ <= ]]> #{timeParam.endTime}
            when (o2i.order_type = '00' or o2i.order_type = '02' or o2i.order_type = '03') then o2i.valet_order_complete_time <![CDATA[ <= ]]> #{timeParam.endTime}
            ELSE o2i.create_time <![CDATA[ <= ]]> #{timeParam.endTime}
            END
        </if>
    </select>

    <select id="getProductOrderHotSell" resultType="com.chinamobile.data.pojo.vo.ProductOrderHotDataVO">
        SELECT
            occ.realProductName,
            count(occ.order_id) atomOrderCount,
            SUM(occ.atomOrderAmount) atomOrderAmount
        FROM
            (SELECT
                 oai.order_id,
                 (oai.sku_quantity * oai.sku_price) atomOrderAmount,
                 ss.real_product_name realProductName,
                 ss.id product_id
             FROM
                 order_2c_atom_info oai,
                 atom_offering_info aoi,
                 atom_std_service ass,
                 standard_service ss
             WHERE
        (((oai.order_type = '01' and oai.order_status != 8)  or ((oai.order_type = '00' or oai.order_type = '02' or oai.order_type = '03') and oai.order_status in (12,7)))  AND ( oai.atom_offering_class != 'S' OR oai.atom_offering_class is null ))
               AND ( oai.atom_offering_class != 'S' OR oai.atom_offering_class IS NULL )
               AND oai.spu_offering_code = aoi.spu_code
               AND oai.sku_offering_code = aoi.sku_code
               AND oai.atom_offering_code = aoi.offering_code
               AND aoi.id = ass.atom_id
               AND ass.std_service_id = ss.id
                <if test="startTime != null">
                    and
                    CASE
                    when oai.order_type ='01' then oai.create_time <![CDATA[ >= ]]> #{startTime}
                    when (oai.order_type = '00' or oai.order_type = '02' or oai.order_type = '03') then oai.valet_order_complete_time <![CDATA[ >= ]]> #{startTime}
                    ELSE oai.create_time <![CDATA[ >= ]]> #{startTime}
                    END
                </if>
                <if test="endTime != null">
                    and
                    CASE
                    when oai.order_type ='01' then oai.create_time <![CDATA[ <= ]]> #{endTime}
                    when (oai.order_type = '00' or oai.order_type = '02' or oai.order_type = '03') then oai.valet_order_complete_time <![CDATA[ <= ]]> #{endTime}
                    ELSE oai.create_time <![CDATA[ <= ]]> #{endTime}
                    END
                </if>

             GROUP BY order_id
            )occ
        GROUP BY occ.product_id
        ORDER BY atomOrderAmount DESC
        limit 0,15
    </select>

    <select id="getTodayProductOrderData" resultType="com.chinamobile.data.pojo.vo.ProductOrderRealTimeVO">
        SELECT
            (oai.sku_quantity * oai.sku_price)/1000 amountSale,
            oai.sku_quantity sellQuantity,
            oai.create_time createTime,
            spi.offering_name realProductName
        FROM
            order_2c_atom_info oai,
            sku_offering_info ski,
            spu_offering_info spi
        WHERE
        ((oai.order_type = '01' and oai.order_status != 8)  or ((oai.order_type = '00' or oai.order_type = '02' or oai.order_type = '03') and oai.order_status in (12,7)))
             AND ( oai.atom_offering_class != 'S' OR oai.atom_offering_class is null )
        AND oai.sku_offering_code = ski.offering_code
        AND oai.spu_offering_code = spi.offering_code
        <if test="startTime != null">
            and
            CASE
            when oai.order_type ='01' then oai.create_time <![CDATA[ >= ]]> #{startTime}
            when (oai.order_type = '00' or oai.order_type = '02' or oai.order_type = '03') then oai.valet_order_complete_time <![CDATA[ >= ]]> #{startTime}
            ELSE oai.create_time <![CDATA[ >= ]]> #{startTime}
            END
        </if>
        GROUP BY order_id
        ORDER BY oai.create_time DESC
    </select>

    <select id="departmentStatistics" parameterType="java.lang.String" resultType="com.chinamobile.data.pojo.mapper.OrderStatisticsDO">
    SELECT
        d.short_name name,
        sum(oa.amount) amount,
        count(order_id) orderCount
    FROM
        (
            SELECT
                order_id,
                atom_offering_code,
                spu_offering_code,
                sku_offering_code,
                sku_quantity * sku_price amount
            FROM
                order_2c_atom_info
            WHERE
                (((order_type = '01' and order_status != 8)  or ((order_type = '00' or order_type = '02' or order_type = '03') and order_status in (12,7)))  AND ( atom_offering_class != 'S' OR atom_offering_class is null ))
            <if test="startTime != null">
                and
                CASE
                when order_type ='01' then create_time <![CDATA[ >= ]]> #{startTime}
                when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ >= ]]> #{startTime}
                ELSE create_time <![CDATA[ >= ]]> #{startTime}
                END
            </if>
            <if test="endTime != null">
                and
                CASE
                when order_type ='01' then create_time <![CDATA[ <= ]]> #{endTime}
                when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ <= ]]> #{endTime}
                ELSE create_time <![CDATA[ <= ]]> #{endTime}
                END
            </if>
            GROUP BY
                order_id
         )oa
    LEFT JOIN atom_offering_info aoi ON aoi.offering_code = oa.atom_offering_code and oa.spu_offering_code = aoi.spu_code AND oa.sku_offering_code = aoi.sku_code
    LEFT JOIN atom_std_service ass ON ass.atom_id = aoi.id
    LEFT JOIN standard_service ss ON ass.std_service_id = ss.id
    JOIN department d ON d.id = ss.product_department_id
    GROUP BY ss.product_department_id
    ORDER BY amount DESC
    </select>

    <select id="totalStatistics" parameterType="java.lang.String" resultType="com.chinamobile.data.pojo.mapper.OrderStatisticsDO">
    SELECT
        sum(oa.amount) amount,
        count(order_id) orderCount
    FROM
        (
            SELECT
                order_id,
                atom_offering_code,
                spu_offering_code,
                sku_offering_code,
                sku_quantity * sku_price amount
            FROM
                order_2c_atom_info
            WHERE
                (((order_type = '01' and order_status != 8)  or ((order_type = '00' or order_type = '02' or order_type = '03') and order_status in (12,7)))  AND ( atom_offering_class != 'S' OR atom_offering_class is null ))
            <if test="startTime != null">
                and
                CASE
                when order_type ='01' then create_time <![CDATA[ >= ]]> #{startTime}
                when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ >= ]]> #{startTime}
                ELSE create_time <![CDATA[ >= ]]> #{startTime}
                END
            </if>
            <if test="endTime != null">
                and
                CASE
                when order_type ='01' then create_time <![CDATA[ <= ]]> #{endTime}
                when (order_type = '00' or order_type = '02' or order_type = '03') then valet_order_complete_time <![CDATA[ <= ]]> #{endTime}
                ELSE create_time <![CDATA[ <= ]]> #{endTime}
                END
            </if>
            GROUP BY
                order_id
         )oa
    LEFT JOIN atom_offering_info aoi ON aoi.offering_code = oa.atom_offering_code and oa.spu_offering_code = aoi.spu_code AND oa.sku_offering_code = aoi.sku_code
    LEFT JOIN atom_std_service ass ON ass.atom_id = aoi.id
    LEFT JOIN standard_service ss ON ass.std_service_id = ss.id
    JOIN department d ON d.id = ss.product_department_id
    </select>

    <select id="shelfStatistics" resultType="com.chinamobile.data.pojo.mapper.ShelfStatisticsDO">
    SELECT
        pp.name propertyName,
        ca.offering_class spuOfferingClass,
        count(*) shelfCount
    FROM
        atom_offering_info atom
        JOIN spu_offering_info spu ON spu.offering_code = atom.spu_code AND spu.delete_time is NULL AND spu.offering_status = '1'
        JOIN category_info ca ON ca.spu_id = spu.id
        JOIN atom_std_service ass ON ass.atom_id = atom.id
        JOIN standard_service ss ON ass.std_service_id = ss.id
        JOIN product_property pp ON pp.id = ss.product_property_id
        WHERE 1=1
        <if test="startTime != null">
            and atom.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            and atom.create_time  <![CDATA[ <= ]]> #{endTime}
        </if>
        GROUP BY pp.name,ca.offering_class
    </select>
</mapper>