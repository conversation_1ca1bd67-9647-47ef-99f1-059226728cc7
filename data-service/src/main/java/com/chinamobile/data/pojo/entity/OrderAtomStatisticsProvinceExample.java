package com.chinamobile.data.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OrderAtomStatisticsProvinceExample {
    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    protected List<Criteria> oredCriteria;

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    public OrderAtomStatisticsProvinceExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    public OrderAtomStatisticsProvinceExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    public OrderAtomStatisticsProvinceExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    public static Criteria newAndCreateCriteria() {
        OrderAtomStatisticsProvinceExample example = new OrderAtomStatisticsProvinceExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    public OrderAtomStatisticsProvinceExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    public OrderAtomStatisticsProvinceExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNull() {
            addCriterion("province is null");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNotNull() {
            addCriterion("province is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceEqualTo(String value) {
            addCriterion("province =", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("province = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNotEqualTo(String value) {
            addCriterion("province <>", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("province <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThan(String value) {
            addCriterion("province >", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("province > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("province >=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("province >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceLessThan(String value) {
            addCriterion("province <", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("province < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceLessThanOrEqualTo(String value) {
            addCriterion("province <=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("province <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceLike(String value) {
            addCriterion("province like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotLike(String value) {
            addCriterion("province not like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceIn(List<String> values) {
            addCriterion("province in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotIn(List<String> values) {
            addCriterion("province not in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceBetween(String value1, String value2) {
            addCriterion("province between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotBetween(String value1, String value2) {
            addCriterion("province not between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andOrderCountIsNull() {
            addCriterion("order_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderCountIsNotNull() {
            addCriterion("order_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCountEqualTo(Integer value) {
            addCriterion("order_count =", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("order_count = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCountNotEqualTo(Integer value) {
            addCriterion("order_count <>", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountNotEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("order_count <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCountGreaterThan(Integer value) {
            addCriterion("order_count >", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountGreaterThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("order_count > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_count >=", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountGreaterThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("order_count >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCountLessThan(Integer value) {
            addCriterion("order_count <", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountLessThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("order_count < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_count <=", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountLessThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("order_count <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCountIn(List<Integer> values) {
            addCriterion("order_count in", values, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountNotIn(List<Integer> values) {
            addCriterion("order_count not in", values, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountBetween(Integer value1, Integer value2) {
            addCriterion("order_count between", value1, value2, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_count not between", value1, value2, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderAmountIsNull() {
            addCriterion("order_amount is null");
            return (Criteria) this;
        }

        public Criteria andOrderAmountIsNotNull() {
            addCriterion("order_amount is not null");
            return (Criteria) this;
        }

        public Criteria andOrderAmountEqualTo(Long value) {
            addCriterion("order_amount =", value, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andOrderAmountEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("order_amount = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderAmountNotEqualTo(Long value) {
            addCriterion("order_amount <>", value, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andOrderAmountNotEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("order_amount <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderAmountGreaterThan(Long value) {
            addCriterion("order_amount >", value, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andOrderAmountGreaterThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("order_amount > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("order_amount >=", value, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andOrderAmountGreaterThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("order_amount >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderAmountLessThan(Long value) {
            addCriterion("order_amount <", value, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andOrderAmountLessThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("order_amount < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderAmountLessThanOrEqualTo(Long value) {
            addCriterion("order_amount <=", value, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andOrderAmountLessThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("order_amount <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderAmountIn(List<Long> values) {
            addCriterion("order_amount in", values, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andOrderAmountNotIn(List<Long> values) {
            addCriterion("order_amount not in", values, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andOrderAmountBetween(Long value1, Long value2) {
            addCriterion("order_amount between", value1, value2, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andOrderAmountNotBetween(Long value1, Long value2) {
            addCriterion("order_amount not between", value1, value2, "orderAmount");
            return (Criteria) this;
        }

        public Criteria andAtomOrderAmountIsNull() {
            addCriterion("atom_order_amount is null");
            return (Criteria) this;
        }

        public Criteria andAtomOrderAmountIsNotNull() {
            addCriterion("atom_order_amount is not null");
            return (Criteria) this;
        }

        public Criteria andAtomOrderAmountEqualTo(Long value) {
            addCriterion("atom_order_amount =", value, "atomOrderAmount");
            return (Criteria) this;
        }

        public Criteria andAtomOrderAmountEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("atom_order_amount = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderAmountNotEqualTo(Long value) {
            addCriterion("atom_order_amount <>", value, "atomOrderAmount");
            return (Criteria) this;
        }

        public Criteria andAtomOrderAmountNotEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("atom_order_amount <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderAmountGreaterThan(Long value) {
            addCriterion("atom_order_amount >", value, "atomOrderAmount");
            return (Criteria) this;
        }

        public Criteria andAtomOrderAmountGreaterThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("atom_order_amount > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("atom_order_amount >=", value, "atomOrderAmount");
            return (Criteria) this;
        }

        public Criteria andAtomOrderAmountGreaterThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("atom_order_amount >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderAmountLessThan(Long value) {
            addCriterion("atom_order_amount <", value, "atomOrderAmount");
            return (Criteria) this;
        }

        public Criteria andAtomOrderAmountLessThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("atom_order_amount < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderAmountLessThanOrEqualTo(Long value) {
            addCriterion("atom_order_amount <=", value, "atomOrderAmount");
            return (Criteria) this;
        }

        public Criteria andAtomOrderAmountLessThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("atom_order_amount <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderAmountIn(List<Long> values) {
            addCriterion("atom_order_amount in", values, "atomOrderAmount");
            return (Criteria) this;
        }

        public Criteria andAtomOrderAmountNotIn(List<Long> values) {
            addCriterion("atom_order_amount not in", values, "atomOrderAmount");
            return (Criteria) this;
        }

        public Criteria andAtomOrderAmountBetween(Long value1, Long value2) {
            addCriterion("atom_order_amount between", value1, value2, "atomOrderAmount");
            return (Criteria) this;
        }

        public Criteria andAtomOrderAmountNotBetween(Long value1, Long value2) {
            addCriterion("atom_order_amount not between", value1, value2, "atomOrderAmount");
            return (Criteria) this;
        }

        public Criteria andAtomOrderQuantityIsNull() {
            addCriterion("atom_order_quantity is null");
            return (Criteria) this;
        }

        public Criteria andAtomOrderQuantityIsNotNull() {
            addCriterion("atom_order_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andAtomOrderQuantityEqualTo(Integer value) {
            addCriterion("atom_order_quantity =", value, "atomOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomOrderQuantityEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("atom_order_quantity = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderQuantityNotEqualTo(Integer value) {
            addCriterion("atom_order_quantity <>", value, "atomOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomOrderQuantityNotEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("atom_order_quantity <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderQuantityGreaterThan(Integer value) {
            addCriterion("atom_order_quantity >", value, "atomOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomOrderQuantityGreaterThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("atom_order_quantity > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("atom_order_quantity >=", value, "atomOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomOrderQuantityGreaterThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("atom_order_quantity >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderQuantityLessThan(Integer value) {
            addCriterion("atom_order_quantity <", value, "atomOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomOrderQuantityLessThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("atom_order_quantity < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("atom_order_quantity <=", value, "atomOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomOrderQuantityLessThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("atom_order_quantity <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderQuantityIn(List<Integer> values) {
            addCriterion("atom_order_quantity in", values, "atomOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomOrderQuantityNotIn(List<Integer> values) {
            addCriterion("atom_order_quantity not in", values, "atomOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomOrderQuantityBetween(Integer value1, Integer value2) {
            addCriterion("atom_order_quantity between", value1, value2, "atomOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomOrderQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("atom_order_quantity not between", value1, value2, "atomOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuOrderQuantityIsNull() {
            addCriterion("sku_order_quantity is null");
            return (Criteria) this;
        }

        public Criteria andSkuOrderQuantityIsNotNull() {
            addCriterion("sku_order_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andSkuOrderQuantityEqualTo(Integer value) {
            addCriterion("sku_order_quantity =", value, "skuOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuOrderQuantityEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("sku_order_quantity = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOrderQuantityNotEqualTo(Integer value) {
            addCriterion("sku_order_quantity <>", value, "skuOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuOrderQuantityNotEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("sku_order_quantity <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOrderQuantityGreaterThan(Integer value) {
            addCriterion("sku_order_quantity >", value, "skuOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuOrderQuantityGreaterThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("sku_order_quantity > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOrderQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("sku_order_quantity >=", value, "skuOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuOrderQuantityGreaterThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("sku_order_quantity >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOrderQuantityLessThan(Integer value) {
            addCriterion("sku_order_quantity <", value, "skuOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuOrderQuantityLessThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("sku_order_quantity < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOrderQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("sku_order_quantity <=", value, "skuOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuOrderQuantityLessThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("sku_order_quantity <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOrderQuantityIn(List<Integer> values) {
            addCriterion("sku_order_quantity in", values, "skuOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuOrderQuantityNotIn(List<Integer> values) {
            addCriterion("sku_order_quantity not in", values, "skuOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuOrderQuantityBetween(Integer value1, Integer value2) {
            addCriterion("sku_order_quantity between", value1, value2, "skuOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuOrderQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("sku_order_quantity not between", value1, value2, "skuOrderQuantity");
            return (Criteria) this;
        }

        public Criteria andYearIsNull() {
            addCriterion("year is null");
            return (Criteria) this;
        }

        public Criteria andYearIsNotNull() {
            addCriterion("year is not null");
            return (Criteria) this;
        }

        public Criteria andYearEqualTo(Integer value) {
            addCriterion("year =", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("year = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearNotEqualTo(Integer value) {
            addCriterion("year <>", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("year <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearGreaterThan(Integer value) {
            addCriterion("year >", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearGreaterThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("year > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearGreaterThanOrEqualTo(Integer value) {
            addCriterion("year >=", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearGreaterThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("year >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearLessThan(Integer value) {
            addCriterion("year <", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearLessThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("year < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearLessThanOrEqualTo(Integer value) {
            addCriterion("year <=", value, "year");
            return (Criteria) this;
        }

        public Criteria andYearLessThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("year <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andYearIn(List<Integer> values) {
            addCriterion("year in", values, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotIn(List<Integer> values) {
            addCriterion("year not in", values, "year");
            return (Criteria) this;
        }

        public Criteria andYearBetween(Integer value1, Integer value2) {
            addCriterion("year between", value1, value2, "year");
            return (Criteria) this;
        }

        public Criteria andYearNotBetween(Integer value1, Integer value2) {
            addCriterion("year not between", value1, value2, "year");
            return (Criteria) this;
        }

        public Criteria andMonthIsNull() {
            addCriterion("month is null");
            return (Criteria) this;
        }

        public Criteria andMonthIsNotNull() {
            addCriterion("month is not null");
            return (Criteria) this;
        }

        public Criteria andMonthEqualTo(Integer value) {
            addCriterion("month =", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("month = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMonthNotEqualTo(Integer value) {
            addCriterion("month <>", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthNotEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("month <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMonthGreaterThan(Integer value) {
            addCriterion("month >", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthGreaterThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("month > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMonthGreaterThanOrEqualTo(Integer value) {
            addCriterion("month >=", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthGreaterThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("month >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMonthLessThan(Integer value) {
            addCriterion("month <", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthLessThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("month < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMonthLessThanOrEqualTo(Integer value) {
            addCriterion("month <=", value, "month");
            return (Criteria) this;
        }

        public Criteria andMonthLessThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("month <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMonthIn(List<Integer> values) {
            addCriterion("month in", values, "month");
            return (Criteria) this;
        }

        public Criteria andMonthNotIn(List<Integer> values) {
            addCriterion("month not in", values, "month");
            return (Criteria) this;
        }

        public Criteria andMonthBetween(Integer value1, Integer value2) {
            addCriterion("month between", value1, value2, "month");
            return (Criteria) this;
        }

        public Criteria andMonthNotBetween(Integer value1, Integer value2) {
            addCriterion("month not between", value1, value2, "month");
            return (Criteria) this;
        }

        public Criteria andDayIsNull() {
            addCriterion("day is null");
            return (Criteria) this;
        }

        public Criteria andDayIsNotNull() {
            addCriterion("day is not null");
            return (Criteria) this;
        }

        public Criteria andDayEqualTo(Integer value) {
            addCriterion("day =", value, "day");
            return (Criteria) this;
        }

        public Criteria andDayEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("day = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDayNotEqualTo(Integer value) {
            addCriterion("day <>", value, "day");
            return (Criteria) this;
        }

        public Criteria andDayNotEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("day <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDayGreaterThan(Integer value) {
            addCriterion("day >", value, "day");
            return (Criteria) this;
        }

        public Criteria andDayGreaterThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("day > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDayGreaterThanOrEqualTo(Integer value) {
            addCriterion("day >=", value, "day");
            return (Criteria) this;
        }

        public Criteria andDayGreaterThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("day >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDayLessThan(Integer value) {
            addCriterion("day <", value, "day");
            return (Criteria) this;
        }

        public Criteria andDayLessThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("day < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDayLessThanOrEqualTo(Integer value) {
            addCriterion("day <=", value, "day");
            return (Criteria) this;
        }

        public Criteria andDayLessThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("day <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDayIn(List<Integer> values) {
            addCriterion("day in", values, "day");
            return (Criteria) this;
        }

        public Criteria andDayNotIn(List<Integer> values) {
            addCriterion("day not in", values, "day");
            return (Criteria) this;
        }

        public Criteria andDayBetween(Integer value1, Integer value2) {
            addCriterion("day between", value1, value2, "day");
            return (Criteria) this;
        }

        public Criteria andDayNotBetween(Integer value1, Integer value2) {
            addCriterion("day not between", value1, value2, "day");
            return (Criteria) this;
        }

        public Criteria andDataTimeIsNull() {
            addCriterion("data_time is null");
            return (Criteria) this;
        }

        public Criteria andDataTimeIsNotNull() {
            addCriterion("data_time is not null");
            return (Criteria) this;
        }

        public Criteria andDataTimeEqualTo(Date value) {
            addCriterion("data_time =", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("data_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDataTimeNotEqualTo(Date value) {
            addCriterion("data_time <>", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeNotEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("data_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDataTimeGreaterThan(Date value) {
            addCriterion("data_time >", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeGreaterThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("data_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDataTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("data_time >=", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeGreaterThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("data_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDataTimeLessThan(Date value) {
            addCriterion("data_time <", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeLessThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("data_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDataTimeLessThanOrEqualTo(Date value) {
            addCriterion("data_time <=", value, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeLessThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("data_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDataTimeIn(List<Date> values) {
            addCriterion("data_time in", values, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeNotIn(List<Date> values) {
            addCriterion("data_time not in", values, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeBetween(Date value1, Date value2) {
            addCriterion("data_time between", value1, value2, "dataTime");
            return (Criteria) this;
        }

        public Criteria andDataTimeNotBetween(Date value1, Date value2) {
            addCriterion("data_time not between", value1, value2, "dataTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(OrderAtomStatisticsProvince.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andProvinceLikeInsensitive(String value) {
            addCriterion("upper(province) like", value.toUpperCase(), "province");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Fri Apr 22 15:26:30 CST 2022
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Fri Apr 22 15:26:30 CST 2022
         */
        private OrderAtomStatisticsProvinceExample example;

        /**
         *
         * @mbg.generated Fri Apr 22 15:26:30 CST 2022
         */
        protected Criteria(OrderAtomStatisticsProvinceExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Fri Apr 22 15:26:30 CST 2022
         */
        public OrderAtomStatisticsProvinceExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Fri Apr 22 15:26:30 CST 2022
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Fri Apr 22 15:26:30 CST 2022
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Fri Apr 22 15:26:30 CST 2022
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Fri Apr 22 15:26:30 CST 2022
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Fri Apr 22 15:26:30 CST 2022
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Fri Apr 22 15:26:30 CST 2022
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Fri Apr 22 15:26:30 CST 2022
         */
        void example(com.chinamobile.data.pojo.entity.OrderAtomStatisticsProvinceExample example);
    }
}