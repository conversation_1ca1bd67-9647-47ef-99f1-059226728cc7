package com.chinamobile.data.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SkuOfferingInfoExample {
    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    public SkuOfferingInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    public SkuOfferingInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    public SkuOfferingInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    public static Criteria newAndCreateCriteria() {
        SkuOfferingInfoExample example = new SkuOfferingInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    public SkuOfferingInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    public SkuOfferingInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSpuIdIsNull() {
            addCriterion("spu_id is null");
            return (Criteria) this;
        }

        public Criteria andSpuIdIsNotNull() {
            addCriterion("spu_id is not null");
            return (Criteria) this;
        }

        public Criteria andSpuIdEqualTo(String value) {
            addCriterion("spu_id =", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuIdNotEqualTo(String value) {
            addCriterion("spu_id <>", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThan(String value) {
            addCriterion("spu_id >", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThanOrEqualTo(String value) {
            addCriterion("spu_id >=", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThan(String value) {
            addCriterion("spu_id <", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThanOrEqualTo(String value) {
            addCriterion("spu_id <=", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuIdLike(String value) {
            addCriterion("spu_id like", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotLike(String value) {
            addCriterion("spu_id not like", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdIn(List<String> values) {
            addCriterion("spu_id in", values, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotIn(List<String> values) {
            addCriterion("spu_id not in", values, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdBetween(String value1, String value2) {
            addCriterion("spu_id between", value1, value2, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotBetween(String value1, String value2) {
            addCriterion("spu_id not between", value1, value2, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNull() {
            addCriterion("spu_code is null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNotNull() {
            addCriterion("spu_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualTo(String value) {
            addCriterion("spu_code =", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualTo(String value) {
            addCriterion("spu_code <>", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThan(String value) {
            addCriterion("spu_code >", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("spu_code >=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThan(String value) {
            addCriterion("spu_code <", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualTo(String value) {
            addCriterion("spu_code <=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLike(String value) {
            addCriterion("spu_code like", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotLike(String value) {
            addCriterion("spu_code not like", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIn(List<String> values) {
            addCriterion("spu_code in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotIn(List<String> values) {
            addCriterion("spu_code not in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeBetween(String value1, String value2) {
            addCriterion("spu_code between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotBetween(String value1, String value2) {
            addCriterion("spu_code not between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeIsNull() {
            addCriterion("offering_code is null");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeIsNotNull() {
            addCriterion("offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeEqualTo(String value) {
            addCriterion("offering_code =", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotEqualTo(String value) {
            addCriterion("offering_code <>", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThan(String value) {
            addCriterion("offering_code >", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("offering_code >=", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThan(String value) {
            addCriterion("offering_code <", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("offering_code <=", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLike(String value) {
            addCriterion("offering_code like", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotLike(String value) {
            addCriterion("offering_code not like", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeIn(List<String> values) {
            addCriterion("offering_code in", values, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotIn(List<String> values) {
            addCriterion("offering_code not in", values, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeBetween(String value1, String value2) {
            addCriterion("offering_code between", value1, value2, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("offering_code not between", value1, value2, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingNameIsNull() {
            addCriterion("offering_name is null");
            return (Criteria) this;
        }

        public Criteria andOfferingNameIsNotNull() {
            addCriterion("offering_name is not null");
            return (Criteria) this;
        }

        public Criteria andOfferingNameEqualTo(String value) {
            addCriterion("offering_name =", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotEqualTo(String value) {
            addCriterion("offering_name <>", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThan(String value) {
            addCriterion("offering_name >", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThanOrEqualTo(String value) {
            addCriterion("offering_name >=", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThan(String value) {
            addCriterion("offering_name <", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThanOrEqualTo(String value) {
            addCriterion("offering_name <=", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameLike(String value) {
            addCriterion("offering_name like", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotLike(String value) {
            addCriterion("offering_name not like", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameIn(List<String> values) {
            addCriterion("offering_name in", values, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotIn(List<String> values) {
            addCriterion("offering_name not in", values, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameBetween(String value1, String value2) {
            addCriterion("offering_name between", value1, value2, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotBetween(String value1, String value2) {
            addCriterion("offering_name not between", value1, value2, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusIsNull() {
            addCriterion("offering_status is null");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusIsNotNull() {
            addCriterion("offering_status is not null");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusEqualTo(String value) {
            addCriterion("offering_status =", value, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingStatusNotEqualTo(String value) {
            addCriterion("offering_status <>", value, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingStatusGreaterThan(String value) {
            addCriterion("offering_status >", value, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingStatusGreaterThanOrEqualTo(String value) {
            addCriterion("offering_status >=", value, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingStatusLessThan(String value) {
            addCriterion("offering_status <", value, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingStatusLessThanOrEqualTo(String value) {
            addCriterion("offering_status <=", value, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingStatusLike(String value) {
            addCriterion("offering_status like", value, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusNotLike(String value) {
            addCriterion("offering_status not like", value, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusIn(List<String> values) {
            addCriterion("offering_status in", values, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusNotIn(List<String> values) {
            addCriterion("offering_status not in", values, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusBetween(String value1, String value2) {
            addCriterion("offering_status between", value1, value2, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusNotBetween(String value1, String value2) {
            addCriterion("offering_status not between", value1, value2, "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusTimeIsNull() {
            addCriterion("offering_status_time is null");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusTimeIsNotNull() {
            addCriterion("offering_status_time is not null");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusTimeEqualTo(Date value) {
            addCriterion("offering_status_time =", value, "offeringStatusTime");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusTimeEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_status_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingStatusTimeNotEqualTo(Date value) {
            addCriterion("offering_status_time <>", value, "offeringStatusTime");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusTimeNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_status_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingStatusTimeGreaterThan(Date value) {
            addCriterion("offering_status_time >", value, "offeringStatusTime");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusTimeGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_status_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingStatusTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("offering_status_time >=", value, "offeringStatusTime");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusTimeGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_status_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingStatusTimeLessThan(Date value) {
            addCriterion("offering_status_time <", value, "offeringStatusTime");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusTimeLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_status_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingStatusTimeLessThanOrEqualTo(Date value) {
            addCriterion("offering_status_time <=", value, "offeringStatusTime");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusTimeLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("offering_status_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingStatusTimeIn(List<Date> values) {
            addCriterion("offering_status_time in", values, "offeringStatusTime");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusTimeNotIn(List<Date> values) {
            addCriterion("offering_status_time not in", values, "offeringStatusTime");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusTimeBetween(Date value1, Date value2) {
            addCriterion("offering_status_time between", value1, value2, "offeringStatusTime");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusTimeNotBetween(Date value1, Date value2) {
            addCriterion("offering_status_time not between", value1, value2, "offeringStatusTime");
            return (Criteria) this;
        }

        public Criteria andCompositionIsNull() {
            addCriterion("composition is null");
            return (Criteria) this;
        }

        public Criteria andCompositionIsNotNull() {
            addCriterion("composition is not null");
            return (Criteria) this;
        }

        public Criteria andCompositionEqualTo(String value) {
            addCriterion("composition =", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("composition = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCompositionNotEqualTo(String value) {
            addCriterion("composition <>", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("composition <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCompositionGreaterThan(String value) {
            addCriterion("composition >", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("composition > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCompositionGreaterThanOrEqualTo(String value) {
            addCriterion("composition >=", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("composition >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCompositionLessThan(String value) {
            addCriterion("composition <", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("composition < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCompositionLessThanOrEqualTo(String value) {
            addCriterion("composition <=", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("composition <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCompositionLike(String value) {
            addCriterion("composition like", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionNotLike(String value) {
            addCriterion("composition not like", value, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionIn(List<String> values) {
            addCriterion("composition in", values, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionNotIn(List<String> values) {
            addCriterion("composition not in", values, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionBetween(String value1, String value2) {
            addCriterion("composition between", value1, value2, "composition");
            return (Criteria) this;
        }

        public Criteria andCompositionNotBetween(String value1, String value2) {
            addCriterion("composition not between", value1, value2, "composition");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("model = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("model <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("model > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("model >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("model < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("model <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNull() {
            addCriterion("quantity is null");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNotNull() {
            addCriterion("quantity is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualTo(Long value) {
            addCriterion("quantity =", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("quantity = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualTo(Long value) {
            addCriterion("quantity <>", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("quantity <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThan(Long value) {
            addCriterion("quantity >", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("quantity > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualTo(Long value) {
            addCriterion("quantity >=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("quantity >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityLessThan(Long value) {
            addCriterion("quantity <", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("quantity < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualTo(Long value) {
            addCriterion("quantity <=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("quantity <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityIn(List<Long> values) {
            addCriterion("quantity in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotIn(List<Long> values) {
            addCriterion("quantity not in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityBetween(Long value1, Long value2) {
            addCriterion("quantity between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotBetween(Long value1, Long value2) {
            addCriterion("quantity not between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andSizeIsNull() {
            addCriterion("size is null");
            return (Criteria) this;
        }

        public Criteria andSizeIsNotNull() {
            addCriterion("size is not null");
            return (Criteria) this;
        }

        public Criteria andSizeEqualTo(String value) {
            addCriterion("size =", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("size = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSizeNotEqualTo(String value) {
            addCriterion("size <>", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("size <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSizeGreaterThan(String value) {
            addCriterion("size >", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("size > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSizeGreaterThanOrEqualTo(String value) {
            addCriterion("size >=", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("size >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSizeLessThan(String value) {
            addCriterion("size <", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("size < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSizeLessThanOrEqualTo(String value) {
            addCriterion("size <=", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("size <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSizeLike(String value) {
            addCriterion("size like", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotLike(String value) {
            addCriterion("size not like", value, "size");
            return (Criteria) this;
        }

        public Criteria andSizeIn(List<String> values) {
            addCriterion("size in", values, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotIn(List<String> values) {
            addCriterion("size not in", values, "size");
            return (Criteria) this;
        }

        public Criteria andSizeBetween(String value1, String value2) {
            addCriterion("size between", value1, value2, "size");
            return (Criteria) this;
        }

        public Criteria andSizeNotBetween(String value1, String value2) {
            addCriterion("size not between", value1, value2, "size");
            return (Criteria) this;
        }

        public Criteria andOperTypeIsNull() {
            addCriterion("oper_type is null");
            return (Criteria) this;
        }

        public Criteria andOperTypeIsNotNull() {
            addCriterion("oper_type is not null");
            return (Criteria) this;
        }

        public Criteria andOperTypeEqualTo(String value) {
            addCriterion("oper_type =", value, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("oper_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperTypeNotEqualTo(String value) {
            addCriterion("oper_type <>", value, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("oper_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperTypeGreaterThan(String value) {
            addCriterion("oper_type >", value, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("oper_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperTypeGreaterThanOrEqualTo(String value) {
            addCriterion("oper_type >=", value, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("oper_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperTypeLessThan(String value) {
            addCriterion("oper_type <", value, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("oper_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperTypeLessThanOrEqualTo(String value) {
            addCriterion("oper_type <=", value, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("oper_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOperTypeLike(String value) {
            addCriterion("oper_type like", value, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeNotLike(String value) {
            addCriterion("oper_type not like", value, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeIn(List<String> values) {
            addCriterion("oper_type in", values, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeNotIn(List<String> values) {
            addCriterion("oper_type not in", values, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeBetween(String value1, String value2) {
            addCriterion("oper_type between", value1, value2, "operType");
            return (Criteria) this;
        }

        public Criteria andOperTypeNotBetween(String value1, String value2) {
            addCriterion("oper_type not between", value1, value2, "operType");
            return (Criteria) this;
        }

        public Criteria andRecommendPriceIsNull() {
            addCriterion("recommend_price is null");
            return (Criteria) this;
        }

        public Criteria andRecommendPriceIsNotNull() {
            addCriterion("recommend_price is not null");
            return (Criteria) this;
        }

        public Criteria andRecommendPriceEqualTo(Long value) {
            addCriterion("recommend_price =", value, "recommendPrice");
            return (Criteria) this;
        }

        public Criteria andRecommendPriceEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("recommend_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRecommendPriceNotEqualTo(Long value) {
            addCriterion("recommend_price <>", value, "recommendPrice");
            return (Criteria) this;
        }

        public Criteria andRecommendPriceNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("recommend_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRecommendPriceGreaterThan(Long value) {
            addCriterion("recommend_price >", value, "recommendPrice");
            return (Criteria) this;
        }

        public Criteria andRecommendPriceGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("recommend_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRecommendPriceGreaterThanOrEqualTo(Long value) {
            addCriterion("recommend_price >=", value, "recommendPrice");
            return (Criteria) this;
        }

        public Criteria andRecommendPriceGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("recommend_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRecommendPriceLessThan(Long value) {
            addCriterion("recommend_price <", value, "recommendPrice");
            return (Criteria) this;
        }

        public Criteria andRecommendPriceLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("recommend_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRecommendPriceLessThanOrEqualTo(Long value) {
            addCriterion("recommend_price <=", value, "recommendPrice");
            return (Criteria) this;
        }

        public Criteria andRecommendPriceLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("recommend_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRecommendPriceIn(List<Long> values) {
            addCriterion("recommend_price in", values, "recommendPrice");
            return (Criteria) this;
        }

        public Criteria andRecommendPriceNotIn(List<Long> values) {
            addCriterion("recommend_price not in", values, "recommendPrice");
            return (Criteria) this;
        }

        public Criteria andRecommendPriceBetween(Long value1, Long value2) {
            addCriterion("recommend_price between", value1, value2, "recommendPrice");
            return (Criteria) this;
        }

        public Criteria andRecommendPriceNotBetween(Long value1, Long value2) {
            addCriterion("recommend_price not between", value1, value2, "recommendPrice");
            return (Criteria) this;
        }

        public Criteria andSaleObjectIsNull() {
            addCriterion("sale_object is null");
            return (Criteria) this;
        }

        public Criteria andSaleObjectIsNotNull() {
            addCriterion("sale_object is not null");
            return (Criteria) this;
        }

        public Criteria andSaleObjectEqualTo(String value) {
            addCriterion("sale_object =", value, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sale_object = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleObjectNotEqualTo(String value) {
            addCriterion("sale_object <>", value, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sale_object <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleObjectGreaterThan(String value) {
            addCriterion("sale_object >", value, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sale_object > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleObjectGreaterThanOrEqualTo(String value) {
            addCriterion("sale_object >=", value, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sale_object >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleObjectLessThan(String value) {
            addCriterion("sale_object <", value, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sale_object < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleObjectLessThanOrEqualTo(String value) {
            addCriterion("sale_object <=", value, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sale_object <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleObjectLike(String value) {
            addCriterion("sale_object like", value, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectNotLike(String value) {
            addCriterion("sale_object not like", value, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectIn(List<String> values) {
            addCriterion("sale_object in", values, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectNotIn(List<String> values) {
            addCriterion("sale_object not in", values, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectBetween(String value1, String value2) {
            addCriterion("sale_object between", value1, value2, "saleObject");
            return (Criteria) this;
        }

        public Criteria andSaleObjectNotBetween(String value1, String value2) {
            addCriterion("sale_object not between", value1, value2, "saleObject");
            return (Criteria) this;
        }

        public Criteria andPriceIsNull() {
            addCriterion("price is null");
            return (Criteria) this;
        }

        public Criteria andPriceIsNotNull() {
            addCriterion("price is not null");
            return (Criteria) this;
        }

        public Criteria andPriceEqualTo(Long value) {
            addCriterion("price =", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPriceNotEqualTo(Long value) {
            addCriterion("price <>", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThan(Long value) {
            addCriterion("price >", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThanOrEqualTo(Long value) {
            addCriterion("price >=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPriceLessThan(Long value) {
            addCriterion("price <", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPriceLessThanOrEqualTo(Long value) {
            addCriterion("price <=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPriceIn(List<Long> values) {
            addCriterion("price in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotIn(List<Long> values) {
            addCriterion("price not in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceBetween(Long value1, Long value2) {
            addCriterion("price between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotBetween(Long value1, Long value2) {
            addCriterion("price not between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("unit = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("unit <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("unit > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("unit >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("unit < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("unit <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andMarketNameIsNull() {
            addCriterion("market_name is null");
            return (Criteria) this;
        }

        public Criteria andMarketNameIsNotNull() {
            addCriterion("market_name is not null");
            return (Criteria) this;
        }

        public Criteria andMarketNameEqualTo(String value) {
            addCriterion("market_name =", value, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("market_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketNameNotEqualTo(String value) {
            addCriterion("market_name <>", value, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("market_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketNameGreaterThan(String value) {
            addCriterion("market_name >", value, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("market_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketNameGreaterThanOrEqualTo(String value) {
            addCriterion("market_name >=", value, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("market_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketNameLessThan(String value) {
            addCriterion("market_name <", value, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("market_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketNameLessThanOrEqualTo(String value) {
            addCriterion("market_name <=", value, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("market_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketNameLike(String value) {
            addCriterion("market_name like", value, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameNotLike(String value) {
            addCriterion("market_name not like", value, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameIn(List<String> values) {
            addCriterion("market_name in", values, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameNotIn(List<String> values) {
            addCriterion("market_name not in", values, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameBetween(String value1, String value2) {
            addCriterion("market_name between", value1, value2, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameNotBetween(String value1, String value2) {
            addCriterion("market_name not between", value1, value2, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketCodeIsNull() {
            addCriterion("market_code is null");
            return (Criteria) this;
        }

        public Criteria andMarketCodeIsNotNull() {
            addCriterion("market_code is not null");
            return (Criteria) this;
        }

        public Criteria andMarketCodeEqualTo(String value) {
            addCriterion("market_code =", value, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("market_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketCodeNotEqualTo(String value) {
            addCriterion("market_code <>", value, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("market_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketCodeGreaterThan(String value) {
            addCriterion("market_code >", value, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("market_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketCodeGreaterThanOrEqualTo(String value) {
            addCriterion("market_code >=", value, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("market_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketCodeLessThan(String value) {
            addCriterion("market_code <", value, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("market_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketCodeLessThanOrEqualTo(String value) {
            addCriterion("market_code <=", value, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("market_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketCodeLike(String value) {
            addCriterion("market_code like", value, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeNotLike(String value) {
            addCriterion("market_code not like", value, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeIn(List<String> values) {
            addCriterion("market_code in", values, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeNotIn(List<String> values) {
            addCriterion("market_code not in", values, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeBetween(String value1, String value2) {
            addCriterion("market_code between", value1, value2, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeNotBetween(String value1, String value2) {
            addCriterion("market_code not between", value1, value2, "marketCode");
            return (Criteria) this;
        }

        public Criteria andSupplierNameIsNull() {
            addCriterion("supplier_name is null");
            return (Criteria) this;
        }

        public Criteria andSupplierNameIsNotNull() {
            addCriterion("supplier_name is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierNameEqualTo(String value) {
            addCriterion("supplier_name =", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("supplier_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotEqualTo(String value) {
            addCriterion("supplier_name <>", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("supplier_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierNameGreaterThan(String value) {
            addCriterion("supplier_name >", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("supplier_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierNameGreaterThanOrEqualTo(String value) {
            addCriterion("supplier_name >=", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("supplier_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierNameLessThan(String value) {
            addCriterion("supplier_name <", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("supplier_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierNameLessThanOrEqualTo(String value) {
            addCriterion("supplier_name <=", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("supplier_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierNameLike(String value) {
            addCriterion("supplier_name like", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotLike(String value) {
            addCriterion("supplier_name not like", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameIn(List<String> values) {
            addCriterion("supplier_name in", values, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotIn(List<String> values) {
            addCriterion("supplier_name not in", values, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameBetween(String value1, String value2) {
            addCriterion("supplier_name between", value1, value2, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotBetween(String value1, String value2) {
            addCriterion("supplier_name not between", value1, value2, "supplierName");
            return (Criteria) this;
        }

        public Criteria andProductTypeIsNull() {
            addCriterion("product_type is null");
            return (Criteria) this;
        }

        public Criteria andProductTypeIsNotNull() {
            addCriterion("product_type is not null");
            return (Criteria) this;
        }

        public Criteria andProductTypeEqualTo(String value) {
            addCriterion("product_type =", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeNotEqualTo(String value) {
            addCriterion("product_type <>", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThan(String value) {
            addCriterion("product_type >", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThanOrEqualTo(String value) {
            addCriterion("product_type >=", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThan(String value) {
            addCriterion("product_type <", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThanOrEqualTo(String value) {
            addCriterion("product_type <=", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("product_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeLike(String value) {
            addCriterion("product_type like", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotLike(String value) {
            addCriterion("product_type not like", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeIn(List<String> values) {
            addCriterion("product_type in", values, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotIn(List<String> values) {
            addCriterion("product_type not in", values, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeBetween(String value1, String value2) {
            addCriterion("product_type between", value1, value2, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotBetween(String value1, String value2) {
            addCriterion("product_type not between", value1, value2, "productType");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderIsNull() {
            addCriterion("receive_order is null");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderIsNotNull() {
            addCriterion("receive_order is not null");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderEqualTo(String value) {
            addCriterion("receive_order =", value, "receiveOrder");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("receive_order = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNotEqualTo(String value) {
            addCriterion("receive_order <>", value, "receiveOrder");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("receive_order <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiveOrderGreaterThan(String value) {
            addCriterion("receive_order >", value, "receiveOrder");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("receive_order > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiveOrderGreaterThanOrEqualTo(String value) {
            addCriterion("receive_order >=", value, "receiveOrder");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("receive_order >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiveOrderLessThan(String value) {
            addCriterion("receive_order <", value, "receiveOrder");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("receive_order < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiveOrderLessThanOrEqualTo(String value) {
            addCriterion("receive_order <=", value, "receiveOrder");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("receive_order <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiveOrderLike(String value) {
            addCriterion("receive_order like", value, "receiveOrder");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNotLike(String value) {
            addCriterion("receive_order not like", value, "receiveOrder");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderIn(List<String> values) {
            addCriterion("receive_order in", values, "receiveOrder");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNotIn(List<String> values) {
            addCriterion("receive_order not in", values, "receiveOrder");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderBetween(String value1, String value2) {
            addCriterion("receive_order between", value1, value2, "receiveOrder");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNotBetween(String value1, String value2) {
            addCriterion("receive_order not between", value1, value2, "receiveOrder");
            return (Criteria) this;
        }

        public Criteria andCustCodeIsNull() {
            addCriterion("cust_code is null");
            return (Criteria) this;
        }

        public Criteria andCustCodeIsNotNull() {
            addCriterion("cust_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustCodeEqualTo(String value) {
            addCriterion("cust_code =", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cust_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeNotEqualTo(String value) {
            addCriterion("cust_code <>", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cust_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThan(String value) {
            addCriterion("cust_code >", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cust_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanOrEqualTo(String value) {
            addCriterion("cust_code >=", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cust_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThan(String value) {
            addCriterion("cust_code <", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cust_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanOrEqualTo(String value) {
            addCriterion("cust_code <=", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cust_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLike(String value) {
            addCriterion("cust_code like", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotLike(String value) {
            addCriterion("cust_code not like", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeIn(List<String> values) {
            addCriterion("cust_code in", values, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotIn(List<String> values) {
            addCriterion("cust_code not in", values, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeBetween(String value1, String value2) {
            addCriterion("cust_code between", value1, value2, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotBetween(String value1, String value2) {
            addCriterion("cust_code not between", value1, value2, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustNameIsNull() {
            addCriterion("cust_name is null");
            return (Criteria) this;
        }

        public Criteria andCustNameIsNotNull() {
            addCriterion("cust_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustNameEqualTo(String value) {
            addCriterion("cust_name =", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cust_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameNotEqualTo(String value) {
            addCriterion("cust_name <>", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cust_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThan(String value) {
            addCriterion("cust_name >", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cust_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThanOrEqualTo(String value) {
            addCriterion("cust_name >=", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cust_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameLessThan(String value) {
            addCriterion("cust_name <", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cust_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameLessThanOrEqualTo(String value) {
            addCriterion("cust_name <=", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cust_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameLike(String value) {
            addCriterion("cust_name like", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotLike(String value) {
            addCriterion("cust_name not like", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameIn(List<String> values) {
            addCriterion("cust_name in", values, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotIn(List<String> values) {
            addCriterion("cust_name not in", values, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameBetween(String value1, String value2) {
            addCriterion("cust_name between", value1, value2, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotBetween(String value1, String value2) {
            addCriterion("cust_name not between", value1, value2, "custName");
            return (Criteria) this;
        }

        public Criteria andCardTypeIsNull() {
            addCriterion("card_type is null");
            return (Criteria) this;
        }

        public Criteria andCardTypeIsNotNull() {
            addCriterion("card_type is not null");
            return (Criteria) this;
        }

        public Criteria andCardTypeEqualTo(String value) {
            addCriterion("card_type =", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("card_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardTypeNotEqualTo(String value) {
            addCriterion("card_type <>", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("card_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardTypeGreaterThan(String value) {
            addCriterion("card_type >", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("card_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardTypeGreaterThanOrEqualTo(String value) {
            addCriterion("card_type >=", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("card_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardTypeLessThan(String value) {
            addCriterion("card_type <", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("card_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardTypeLessThanOrEqualTo(String value) {
            addCriterion("card_type <=", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("card_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardTypeLike(String value) {
            addCriterion("card_type like", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotLike(String value) {
            addCriterion("card_type not like", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeIn(List<String> values) {
            addCriterion("card_type in", values, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotIn(List<String> values) {
            addCriterion("card_type not in", values, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeBetween(String value1, String value2) {
            addCriterion("card_type between", value1, value2, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotBetween(String value1, String value2) {
            addCriterion("card_type not between", value1, value2, "cardType");
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeIsNull() {
            addCriterion("main_offering_code is null");
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeIsNotNull() {
            addCriterion("main_offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeEqualTo(String value) {
            addCriterion("main_offering_code =", value, "mainOfferingCode");
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("main_offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeNotEqualTo(String value) {
            addCriterion("main_offering_code <>", value, "mainOfferingCode");
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("main_offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeGreaterThan(String value) {
            addCriterion("main_offering_code >", value, "mainOfferingCode");
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("main_offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("main_offering_code >=", value, "mainOfferingCode");
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("main_offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeLessThan(String value) {
            addCriterion("main_offering_code <", value, "mainOfferingCode");
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("main_offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("main_offering_code <=", value, "mainOfferingCode");
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("main_offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeLike(String value) {
            addCriterion("main_offering_code like", value, "mainOfferingCode");
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeNotLike(String value) {
            addCriterion("main_offering_code not like", value, "mainOfferingCode");
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeIn(List<String> values) {
            addCriterion("main_offering_code in", values, "mainOfferingCode");
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeNotIn(List<String> values) {
            addCriterion("main_offering_code not in", values, "mainOfferingCode");
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeBetween(String value1, String value2) {
            addCriterion("main_offering_code between", value1, value2, "mainOfferingCode");
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("main_offering_code not between", value1, value2, "mainOfferingCode");
            return (Criteria) this;
        }

        public Criteria andTemplateNameIsNull() {
            addCriterion("template_name is null");
            return (Criteria) this;
        }

        public Criteria andTemplateNameIsNotNull() {
            addCriterion("template_name is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateNameEqualTo(String value) {
            addCriterion("template_name =", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("template_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotEqualTo(String value) {
            addCriterion("template_name <>", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("template_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateNameGreaterThan(String value) {
            addCriterion("template_name >", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("template_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateNameGreaterThanOrEqualTo(String value) {
            addCriterion("template_name >=", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("template_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateNameLessThan(String value) {
            addCriterion("template_name <", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("template_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateNameLessThanOrEqualTo(String value) {
            addCriterion("template_name <=", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("template_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateNameLike(String value) {
            addCriterion("template_name like", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotLike(String value) {
            addCriterion("template_name not like", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameIn(List<String> values) {
            addCriterion("template_name in", values, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotIn(List<String> values) {
            addCriterion("template_name not in", values, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameBetween(String value1, String value2) {
            addCriterion("template_name between", value1, value2, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotBetween(String value1, String value2) {
            addCriterion("template_name not between", value1, value2, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNull() {
            addCriterion("template_id is null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNotNull() {
            addCriterion("template_id is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdEqualTo(String value) {
            addCriterion("template_id =", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("template_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotEqualTo(String value) {
            addCriterion("template_id <>", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("template_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThan(String value) {
            addCriterion("template_id >", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("template_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanOrEqualTo(String value) {
            addCriterion("template_id >=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("template_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThan(String value) {
            addCriterion("template_id <", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("template_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanOrEqualTo(String value) {
            addCriterion("template_id <=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("template_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdLike(String value) {
            addCriterion("template_id like", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotLike(String value) {
            addCriterion("template_id not like", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIn(List<String> values) {
            addCriterion("template_id in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotIn(List<String> values) {
            addCriterion("template_id not in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdBetween(String value1, String value2) {
            addCriterion("template_id between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotBetween(String value1, String value2) {
            addCriterion("template_id not between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andSaleModelIsNull() {
            addCriterion("sale_model is null");
            return (Criteria) this;
        }

        public Criteria andSaleModelIsNotNull() {
            addCriterion("sale_model is not null");
            return (Criteria) this;
        }

        public Criteria andSaleModelEqualTo(String value) {
            addCriterion("sale_model =", value, "saleModel");
            return (Criteria) this;
        }

        public Criteria andSaleModelEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sale_model = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleModelNotEqualTo(String value) {
            addCriterion("sale_model <>", value, "saleModel");
            return (Criteria) this;
        }

        public Criteria andSaleModelNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sale_model <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleModelGreaterThan(String value) {
            addCriterion("sale_model >", value, "saleModel");
            return (Criteria) this;
        }

        public Criteria andSaleModelGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sale_model > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleModelGreaterThanOrEqualTo(String value) {
            addCriterion("sale_model >=", value, "saleModel");
            return (Criteria) this;
        }

        public Criteria andSaleModelGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sale_model >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleModelLessThan(String value) {
            addCriterion("sale_model <", value, "saleModel");
            return (Criteria) this;
        }

        public Criteria andSaleModelLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sale_model < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleModelLessThanOrEqualTo(String value) {
            addCriterion("sale_model <=", value, "saleModel");
            return (Criteria) this;
        }

        public Criteria andSaleModelLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sale_model <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleModelLike(String value) {
            addCriterion("sale_model like", value, "saleModel");
            return (Criteria) this;
        }

        public Criteria andSaleModelNotLike(String value) {
            addCriterion("sale_model not like", value, "saleModel");
            return (Criteria) this;
        }

        public Criteria andSaleModelIn(List<String> values) {
            addCriterion("sale_model in", values, "saleModel");
            return (Criteria) this;
        }

        public Criteria andSaleModelNotIn(List<String> values) {
            addCriterion("sale_model not in", values, "saleModel");
            return (Criteria) this;
        }

        public Criteria andSaleModelBetween(String value1, String value2) {
            addCriterion("sale_model between", value1, value2, "saleModel");
            return (Criteria) this;
        }

        public Criteria andSaleModelNotBetween(String value1, String value2) {
            addCriterion("sale_model not between", value1, value2, "saleModel");
            return (Criteria) this;
        }

        public Criteria andProjectIsNull() {
            addCriterion("project is null");
            return (Criteria) this;
        }

        public Criteria andProjectIsNotNull() {
            addCriterion("project is not null");
            return (Criteria) this;
        }

        public Criteria andProjectEqualTo(String value) {
            addCriterion("project =", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("project = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectNotEqualTo(String value) {
            addCriterion("project <>", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("project <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThan(String value) {
            addCriterion("project >", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("project > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThanOrEqualTo(String value) {
            addCriterion("project >=", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("project >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectLessThan(String value) {
            addCriterion("project <", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("project < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectLessThanOrEqualTo(String value) {
            addCriterion("project <=", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("project <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectLike(String value) {
            addCriterion("project like", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotLike(String value) {
            addCriterion("project not like", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectIn(List<String> values) {
            addCriterion("project in", values, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotIn(List<String> values) {
            addCriterion("project not in", values, "project");
            return (Criteria) this;
        }

        public Criteria andProjectBetween(String value1, String value2) {
            addCriterion("project between", value1, value2, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotBetween(String value1, String value2) {
            addCriterion("project not between", value1, value2, "project");
            return (Criteria) this;
        }

        public Criteria andPointStatusIsNull() {
            addCriterion("point_status is null");
            return (Criteria) this;
        }

        public Criteria andPointStatusIsNotNull() {
            addCriterion("point_status is not null");
            return (Criteria) this;
        }

        public Criteria andPointStatusEqualTo(Integer value) {
            addCriterion("point_status =", value, "pointStatus");
            return (Criteria) this;
        }

        public Criteria andPointStatusEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("point_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPointStatusNotEqualTo(Integer value) {
            addCriterion("point_status <>", value, "pointStatus");
            return (Criteria) this;
        }

        public Criteria andPointStatusNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("point_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPointStatusGreaterThan(Integer value) {
            addCriterion("point_status >", value, "pointStatus");
            return (Criteria) this;
        }

        public Criteria andPointStatusGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("point_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPointStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("point_status >=", value, "pointStatus");
            return (Criteria) this;
        }

        public Criteria andPointStatusGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("point_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPointStatusLessThan(Integer value) {
            addCriterion("point_status <", value, "pointStatus");
            return (Criteria) this;
        }

        public Criteria andPointStatusLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("point_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPointStatusLessThanOrEqualTo(Integer value) {
            addCriterion("point_status <=", value, "pointStatus");
            return (Criteria) this;
        }

        public Criteria andPointStatusLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("point_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPointStatusIn(List<Integer> values) {
            addCriterion("point_status in", values, "pointStatus");
            return (Criteria) this;
        }

        public Criteria andPointStatusNotIn(List<Integer> values) {
            addCriterion("point_status not in", values, "pointStatus");
            return (Criteria) this;
        }

        public Criteria andPointStatusBetween(Integer value1, Integer value2) {
            addCriterion("point_status between", value1, value2, "pointStatus");
            return (Criteria) this;
        }

        public Criteria andPointStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("point_status not between", value1, value2, "pointStatus");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIsNull() {
            addCriterion("cooperator_id is null");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIsNotNull() {
            addCriterion("cooperator_id is not null");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdEqualTo(String value) {
            addCriterion("cooperator_id =", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotEqualTo(String value) {
            addCriterion("cooperator_id <>", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThan(String value) {
            addCriterion("cooperator_id >", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanOrEqualTo(String value) {
            addCriterion("cooperator_id >=", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThan(String value) {
            addCriterion("cooperator_id <", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanOrEqualTo(String value) {
            addCriterion("cooperator_id <=", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLike(String value) {
            addCriterion("cooperator_id like", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotLike(String value) {
            addCriterion("cooperator_id not like", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIn(List<String> values) {
            addCriterion("cooperator_id in", values, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotIn(List<String> values) {
            addCriterion("cooperator_id not in", values, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdBetween(String value1, String value2) {
            addCriterion("cooperator_id between", value1, value2, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotBetween(String value1, String value2) {
            addCriterion("cooperator_id not between", value1, value2, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeIsNull() {
            addCriterion("delete_time is null");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeIsNotNull() {
            addCriterion("delete_time is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeEqualTo(Date value) {
            addCriterion("delete_time =", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("delete_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotEqualTo(Date value) {
            addCriterion("delete_time <>", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("delete_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThan(Date value) {
            addCriterion("delete_time >", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("delete_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("delete_time >=", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("delete_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThan(Date value) {
            addCriterion("delete_time <", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("delete_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThanOrEqualTo(Date value) {
            addCriterion("delete_time <=", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("delete_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeIn(List<Date> values) {
            addCriterion("delete_time in", values, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotIn(List<Date> values) {
            addCriterion("delete_time not in", values, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeBetween(Date value1, Date value2) {
            addCriterion("delete_time between", value1, value2, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotBetween(Date value1, Date value2) {
            addCriterion("delete_time not between", value1, value2, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationIsNull() {
            addCriterion("sku_abbreviation is null");
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationIsNotNull() {
            addCriterion("sku_abbreviation is not null");
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationEqualTo(String value) {
            addCriterion("sku_abbreviation =", value, "skuAbbreviation");
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_abbreviation = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationNotEqualTo(String value) {
            addCriterion("sku_abbreviation <>", value, "skuAbbreviation");
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_abbreviation <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationGreaterThan(String value) {
            addCriterion("sku_abbreviation >", value, "skuAbbreviation");
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_abbreviation > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationGreaterThanOrEqualTo(String value) {
            addCriterion("sku_abbreviation >=", value, "skuAbbreviation");
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_abbreviation >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationLessThan(String value) {
            addCriterion("sku_abbreviation <", value, "skuAbbreviation");
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_abbreviation < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationLessThanOrEqualTo(String value) {
            addCriterion("sku_abbreviation <=", value, "skuAbbreviation");
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_abbreviation <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationLike(String value) {
            addCriterion("sku_abbreviation like", value, "skuAbbreviation");
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationNotLike(String value) {
            addCriterion("sku_abbreviation not like", value, "skuAbbreviation");
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationIn(List<String> values) {
            addCriterion("sku_abbreviation in", values, "skuAbbreviation");
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationNotIn(List<String> values) {
            addCriterion("sku_abbreviation not in", values, "skuAbbreviation");
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationBetween(String value1, String value2) {
            addCriterion("sku_abbreviation between", value1, value2, "skuAbbreviation");
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationNotBetween(String value1, String value2) {
            addCriterion("sku_abbreviation not between", value1, value2, "skuAbbreviation");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameIsNull() {
            addCriterion("receive_order_name is null");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameIsNotNull() {
            addCriterion("receive_order_name is not null");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameEqualTo(String value) {
            addCriterion("receive_order_name =", value, "receiveOrderName");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("receive_order_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameNotEqualTo(String value) {
            addCriterion("receive_order_name <>", value, "receiveOrderName");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("receive_order_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameGreaterThan(String value) {
            addCriterion("receive_order_name >", value, "receiveOrderName");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("receive_order_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameGreaterThanOrEqualTo(String value) {
            addCriterion("receive_order_name >=", value, "receiveOrderName");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("receive_order_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameLessThan(String value) {
            addCriterion("receive_order_name <", value, "receiveOrderName");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("receive_order_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameLessThanOrEqualTo(String value) {
            addCriterion("receive_order_name <=", value, "receiveOrderName");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("receive_order_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameLike(String value) {
            addCriterion("receive_order_name like", value, "receiveOrderName");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameNotLike(String value) {
            addCriterion("receive_order_name not like", value, "receiveOrderName");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameIn(List<String> values) {
            addCriterion("receive_order_name in", values, "receiveOrderName");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameNotIn(List<String> values) {
            addCriterion("receive_order_name not in", values, "receiveOrderName");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameBetween(String value1, String value2) {
            addCriterion("receive_order_name between", value1, value2, "receiveOrderName");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameNotBetween(String value1, String value2) {
            addCriterion("receive_order_name not between", value1, value2, "receiveOrderName");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneIsNull() {
            addCriterion("receive_order_phone is null");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneIsNotNull() {
            addCriterion("receive_order_phone is not null");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneEqualTo(String value) {
            addCriterion("receive_order_phone =", value, "receiveOrderPhone");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("receive_order_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneNotEqualTo(String value) {
            addCriterion("receive_order_phone <>", value, "receiveOrderPhone");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("receive_order_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneGreaterThan(String value) {
            addCriterion("receive_order_phone >", value, "receiveOrderPhone");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("receive_order_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("receive_order_phone >=", value, "receiveOrderPhone");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("receive_order_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneLessThan(String value) {
            addCriterion("receive_order_phone <", value, "receiveOrderPhone");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("receive_order_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneLessThanOrEqualTo(String value) {
            addCriterion("receive_order_phone <=", value, "receiveOrderPhone");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("receive_order_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneLike(String value) {
            addCriterion("receive_order_phone like", value, "receiveOrderPhone");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneNotLike(String value) {
            addCriterion("receive_order_phone not like", value, "receiveOrderPhone");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneIn(List<String> values) {
            addCriterion("receive_order_phone in", values, "receiveOrderPhone");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneNotIn(List<String> values) {
            addCriterion("receive_order_phone not in", values, "receiveOrderPhone");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneBetween(String value1, String value2) {
            addCriterion("receive_order_phone between", value1, value2, "receiveOrderPhone");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneNotBetween(String value1, String value2) {
            addCriterion("receive_order_phone not between", value1, value2, "receiveOrderPhone");
            return (Criteria) this;
        }

        public Criteria andDeliverNameIsNull() {
            addCriterion("deliver_name is null");
            return (Criteria) this;
        }

        public Criteria andDeliverNameIsNotNull() {
            addCriterion("deliver_name is not null");
            return (Criteria) this;
        }

        public Criteria andDeliverNameEqualTo(String value) {
            addCriterion("deliver_name =", value, "deliverName");
            return (Criteria) this;
        }

        public Criteria andDeliverNameEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverNameNotEqualTo(String value) {
            addCriterion("deliver_name <>", value, "deliverName");
            return (Criteria) this;
        }

        public Criteria andDeliverNameNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverNameGreaterThan(String value) {
            addCriterion("deliver_name >", value, "deliverName");
            return (Criteria) this;
        }

        public Criteria andDeliverNameGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverNameGreaterThanOrEqualTo(String value) {
            addCriterion("deliver_name >=", value, "deliverName");
            return (Criteria) this;
        }

        public Criteria andDeliverNameGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverNameLessThan(String value) {
            addCriterion("deliver_name <", value, "deliverName");
            return (Criteria) this;
        }

        public Criteria andDeliverNameLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverNameLessThanOrEqualTo(String value) {
            addCriterion("deliver_name <=", value, "deliverName");
            return (Criteria) this;
        }

        public Criteria andDeliverNameLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverNameLike(String value) {
            addCriterion("deliver_name like", value, "deliverName");
            return (Criteria) this;
        }

        public Criteria andDeliverNameNotLike(String value) {
            addCriterion("deliver_name not like", value, "deliverName");
            return (Criteria) this;
        }

        public Criteria andDeliverNameIn(List<String> values) {
            addCriterion("deliver_name in", values, "deliverName");
            return (Criteria) this;
        }

        public Criteria andDeliverNameNotIn(List<String> values) {
            addCriterion("deliver_name not in", values, "deliverName");
            return (Criteria) this;
        }

        public Criteria andDeliverNameBetween(String value1, String value2) {
            addCriterion("deliver_name between", value1, value2, "deliverName");
            return (Criteria) this;
        }

        public Criteria andDeliverNameNotBetween(String value1, String value2) {
            addCriterion("deliver_name not between", value1, value2, "deliverName");
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneIsNull() {
            addCriterion("deliver_phone is null");
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneIsNotNull() {
            addCriterion("deliver_phone is not null");
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneEqualTo(String value) {
            addCriterion("deliver_phone =", value, "deliverPhone");
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneNotEqualTo(String value) {
            addCriterion("deliver_phone <>", value, "deliverPhone");
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneGreaterThan(String value) {
            addCriterion("deliver_phone >", value, "deliverPhone");
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("deliver_phone >=", value, "deliverPhone");
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneLessThan(String value) {
            addCriterion("deliver_phone <", value, "deliverPhone");
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneLessThanOrEqualTo(String value) {
            addCriterion("deliver_phone <=", value, "deliverPhone");
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneLike(String value) {
            addCriterion("deliver_phone like", value, "deliverPhone");
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneNotLike(String value) {
            addCriterion("deliver_phone not like", value, "deliverPhone");
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneIn(List<String> values) {
            addCriterion("deliver_phone in", values, "deliverPhone");
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneNotIn(List<String> values) {
            addCriterion("deliver_phone not in", values, "deliverPhone");
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneBetween(String value1, String value2) {
            addCriterion("deliver_phone between", value1, value2, "deliverPhone");
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneNotBetween(String value1, String value2) {
            addCriterion("deliver_phone not between", value1, value2, "deliverPhone");
            return (Criteria) this;
        }

        public Criteria andAftermarketNameIsNull() {
            addCriterion("aftermarket_name is null");
            return (Criteria) this;
        }

        public Criteria andAftermarketNameIsNotNull() {
            addCriterion("aftermarket_name is not null");
            return (Criteria) this;
        }

        public Criteria andAftermarketNameEqualTo(String value) {
            addCriterion("aftermarket_name =", value, "aftermarketName");
            return (Criteria) this;
        }

        public Criteria andAftermarketNameEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("aftermarket_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAftermarketNameNotEqualTo(String value) {
            addCriterion("aftermarket_name <>", value, "aftermarketName");
            return (Criteria) this;
        }

        public Criteria andAftermarketNameNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("aftermarket_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAftermarketNameGreaterThan(String value) {
            addCriterion("aftermarket_name >", value, "aftermarketName");
            return (Criteria) this;
        }

        public Criteria andAftermarketNameGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("aftermarket_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAftermarketNameGreaterThanOrEqualTo(String value) {
            addCriterion("aftermarket_name >=", value, "aftermarketName");
            return (Criteria) this;
        }

        public Criteria andAftermarketNameGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("aftermarket_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAftermarketNameLessThan(String value) {
            addCriterion("aftermarket_name <", value, "aftermarketName");
            return (Criteria) this;
        }

        public Criteria andAftermarketNameLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("aftermarket_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAftermarketNameLessThanOrEqualTo(String value) {
            addCriterion("aftermarket_name <=", value, "aftermarketName");
            return (Criteria) this;
        }

        public Criteria andAftermarketNameLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("aftermarket_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAftermarketNameLike(String value) {
            addCriterion("aftermarket_name like", value, "aftermarketName");
            return (Criteria) this;
        }

        public Criteria andAftermarketNameNotLike(String value) {
            addCriterion("aftermarket_name not like", value, "aftermarketName");
            return (Criteria) this;
        }

        public Criteria andAftermarketNameIn(List<String> values) {
            addCriterion("aftermarket_name in", values, "aftermarketName");
            return (Criteria) this;
        }

        public Criteria andAftermarketNameNotIn(List<String> values) {
            addCriterion("aftermarket_name not in", values, "aftermarketName");
            return (Criteria) this;
        }

        public Criteria andAftermarketNameBetween(String value1, String value2) {
            addCriterion("aftermarket_name between", value1, value2, "aftermarketName");
            return (Criteria) this;
        }

        public Criteria andAftermarketNameNotBetween(String value1, String value2) {
            addCriterion("aftermarket_name not between", value1, value2, "aftermarketName");
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneIsNull() {
            addCriterion("aftermarket_phone is null");
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneIsNotNull() {
            addCriterion("aftermarket_phone is not null");
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneEqualTo(String value) {
            addCriterion("aftermarket_phone =", value, "aftermarketPhone");
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("aftermarket_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneNotEqualTo(String value) {
            addCriterion("aftermarket_phone <>", value, "aftermarketPhone");
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneNotEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("aftermarket_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneGreaterThan(String value) {
            addCriterion("aftermarket_phone >", value, "aftermarketPhone");
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneGreaterThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("aftermarket_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("aftermarket_phone >=", value, "aftermarketPhone");
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneGreaterThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("aftermarket_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneLessThan(String value) {
            addCriterion("aftermarket_phone <", value, "aftermarketPhone");
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneLessThanColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("aftermarket_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneLessThanOrEqualTo(String value) {
            addCriterion("aftermarket_phone <=", value, "aftermarketPhone");
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneLessThanOrEqualToColumn(SkuOfferingInfo.Column column) {
            addCriterion(new StringBuilder("aftermarket_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneLike(String value) {
            addCriterion("aftermarket_phone like", value, "aftermarketPhone");
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneNotLike(String value) {
            addCriterion("aftermarket_phone not like", value, "aftermarketPhone");
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneIn(List<String> values) {
            addCriterion("aftermarket_phone in", values, "aftermarketPhone");
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneNotIn(List<String> values) {
            addCriterion("aftermarket_phone not in", values, "aftermarketPhone");
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneBetween(String value1, String value2) {
            addCriterion("aftermarket_phone between", value1, value2, "aftermarketPhone");
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneNotBetween(String value1, String value2) {
            addCriterion("aftermarket_phone not between", value1, value2, "aftermarketPhone");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andSpuIdLikeInsensitive(String value) {
            addCriterion("upper(spu_id) like", value.toUpperCase(), "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLikeInsensitive(String value) {
            addCriterion("upper(spu_code) like", value.toUpperCase(), "spuCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(offering_code) like", value.toUpperCase(), "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingNameLikeInsensitive(String value) {
            addCriterion("upper(offering_name) like", value.toUpperCase(), "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingStatusLikeInsensitive(String value) {
            addCriterion("upper(offering_status) like", value.toUpperCase(), "offeringStatus");
            return (Criteria) this;
        }

        public Criteria andCompositionLikeInsensitive(String value) {
            addCriterion("upper(composition) like", value.toUpperCase(), "composition");
            return (Criteria) this;
        }

        public Criteria andModelLikeInsensitive(String value) {
            addCriterion("upper(model) like", value.toUpperCase(), "model");
            return (Criteria) this;
        }

        public Criteria andSizeLikeInsensitive(String value) {
            addCriterion("upper(size) like", value.toUpperCase(), "size");
            return (Criteria) this;
        }

        public Criteria andOperTypeLikeInsensitive(String value) {
            addCriterion("upper(oper_type) like", value.toUpperCase(), "operType");
            return (Criteria) this;
        }

        public Criteria andSaleObjectLikeInsensitive(String value) {
            addCriterion("upper(sale_object) like", value.toUpperCase(), "saleObject");
            return (Criteria) this;
        }

        public Criteria andUnitLikeInsensitive(String value) {
            addCriterion("upper(unit) like", value.toUpperCase(), "unit");
            return (Criteria) this;
        }

        public Criteria andMarketNameLikeInsensitive(String value) {
            addCriterion("upper(market_name) like", value.toUpperCase(), "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketCodeLikeInsensitive(String value) {
            addCriterion("upper(market_code) like", value.toUpperCase(), "marketCode");
            return (Criteria) this;
        }

        public Criteria andSupplierNameLikeInsensitive(String value) {
            addCriterion("upper(supplier_name) like", value.toUpperCase(), "supplierName");
            return (Criteria) this;
        }

        public Criteria andProductTypeLikeInsensitive(String value) {
            addCriterion("upper(product_type) like", value.toUpperCase(), "productType");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderLikeInsensitive(String value) {
            addCriterion("upper(receive_order) like", value.toUpperCase(), "receiveOrder");
            return (Criteria) this;
        }

        public Criteria andCustCodeLikeInsensitive(String value) {
            addCriterion("upper(cust_code) like", value.toUpperCase(), "custCode");
            return (Criteria) this;
        }

        public Criteria andCustNameLikeInsensitive(String value) {
            addCriterion("upper(cust_name) like", value.toUpperCase(), "custName");
            return (Criteria) this;
        }

        public Criteria andCardTypeLikeInsensitive(String value) {
            addCriterion("upper(card_type) like", value.toUpperCase(), "cardType");
            return (Criteria) this;
        }

        public Criteria andMainOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(main_offering_code) like", value.toUpperCase(), "mainOfferingCode");
            return (Criteria) this;
        }

        public Criteria andTemplateNameLikeInsensitive(String value) {
            addCriterion("upper(template_name) like", value.toUpperCase(), "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLikeInsensitive(String value) {
            addCriterion("upper(template_id) like", value.toUpperCase(), "templateId");
            return (Criteria) this;
        }

        public Criteria andSaleModelLikeInsensitive(String value) {
            addCriterion("upper(sale_model) like", value.toUpperCase(), "saleModel");
            return (Criteria) this;
        }

        public Criteria andProjectLikeInsensitive(String value) {
            addCriterion("upper(project) like", value.toUpperCase(), "project");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLikeInsensitive(String value) {
            addCriterion("upper(cooperator_id) like", value.toUpperCase(), "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andSkuAbbreviationLikeInsensitive(String value) {
            addCriterion("upper(sku_abbreviation) like", value.toUpperCase(), "skuAbbreviation");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderNameLikeInsensitive(String value) {
            addCriterion("upper(receive_order_name) like", value.toUpperCase(), "receiveOrderName");
            return (Criteria) this;
        }

        public Criteria andReceiveOrderPhoneLikeInsensitive(String value) {
            addCriterion("upper(receive_order_phone) like", value.toUpperCase(), "receiveOrderPhone");
            return (Criteria) this;
        }

        public Criteria andDeliverNameLikeInsensitive(String value) {
            addCriterion("upper(deliver_name) like", value.toUpperCase(), "deliverName");
            return (Criteria) this;
        }

        public Criteria andDeliverPhoneLikeInsensitive(String value) {
            addCriterion("upper(deliver_phone) like", value.toUpperCase(), "deliverPhone");
            return (Criteria) this;
        }

        public Criteria andAftermarketNameLikeInsensitive(String value) {
            addCriterion("upper(aftermarket_name) like", value.toUpperCase(), "aftermarketName");
            return (Criteria) this;
        }

        public Criteria andAftermarketPhoneLikeInsensitive(String value) {
            addCriterion("upper(aftermarket_phone) like", value.toUpperCase(), "aftermarketPhone");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Thu Feb 13 15:08:27 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Thu Feb 13 15:08:27 CST 2025
         */
        private SkuOfferingInfoExample example;

        /**
         *
         * @mbg.generated Thu Feb 13 15:08:27 CST 2025
         */
        protected Criteria(SkuOfferingInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Thu Feb 13 15:08:27 CST 2025
         */
        public SkuOfferingInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Thu Feb 13 15:08:27 CST 2025
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Feb 13 15:08:27 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Feb 13 15:08:27 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Thu Feb 13 15:08:27 CST 2025
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Thu Feb 13 15:08:27 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Thu Feb 13 15:08:27 CST 2025
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Thu Feb 13 15:08:27 CST 2025
         */
        void example(com.chinamobile.data.pojo.entity.SkuOfferingInfoExample example);
    }
}