package com.chinamobile.data.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 2C类商品订单信息
 *
 * <AUTHOR>
public class Order2cInfo implements Serializable {
    /**
     * 业务订单流水号
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String orderId;

    /**
     * 订单类型 00：代客下单
     * 01：自主下单
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String orderType;

    /**
     * 业务编码
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String businessCode;

    /**
     * 操作员编码
取个人客户所属的客户经理的操作员编码;
分享订购场景，取分享链接中的客户经理编码。（本场景暂不支持）
自主注册个人客户，本字段为空。
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String createOperCode;

    /**
     * 操作员省工号 与操作员编码（ createOperCode）搭配使用，对应操作员的省员工工号；
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String employeeNum;

    /**
     * 操作员姓名
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String custMgName;

    /**
     * 操作员电话,解密
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String custMgPhone;

    /**
     * 订单状态变更时间
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private Date orderStatusTime;

    /**
     * 客户编码
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String custCode;

    /**
     * 客户名称
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String custName;

    /**
     * 个人客户所属省份。
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String beId;

    /**
     * 个人客户所属归属地市编码
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String location;

    /**
     * 个人客户所属归属区县
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String regionId;

    /**
     * 订单收入归属省公司组织机构标识,省公司的组织机构标识，如果客户没有组织机构，则设置默认值：0。若组织机构已补充省公司组织机构标识则该字段为省公司组织机构标识；若组织机构未补充省公司组织机构标识则该字段为空。
     * 如果本字段不为空，则表示该笔收入所属的省公司组织机构。
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String orderOrgBizCode;

    /**
     * 组织级别	对应订单收入归属省公司组织机构级别
     * 1：集团
     * 2：省
     * 3：地市
     * 4：区县
     * 5：营业厅
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String orgLevel;

    /**
     * 全组织机构名称	全组织机构名称为拼接字段，按订单收入归属省公司组织机构的父组织机构拼接，最多取订单收入归属组织机构及以上共5层父组织机构，各级别间以“-”连接；
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String orgName;

    /**
     * 备注
个人客户非标准化的要求在备注中说明。
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String remarks;

    /**
     * 预占流水号
物联网服务类业务以及软件功能费+
（代销类）硬件业务，本字段必传;
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String bookid;

    /**
     * 订单状态
0:订单创建
1:订单验收（个人客户确认收货时，同步本状态）
3:订单计收;（订单同步至CMIoT成功后，同步本状态）4.订单退款完成
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private Integer status;

    /**
     * 订单总金额
单位：厘;
订单创建场景必填，其他同步场景非必填。
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String totalPrice;

    /**
     * 订单创建时间	
格式：yyyyMMddHHmmss
订单创建场景必填，其他同步场景非必填。
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String createTime;

    /**
     * 收货人姓名
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String contactPersonName;

    /**
     * 收货人手机号
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String contactPhone;

    /**
     * 省份;
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String addr1;

    /**
     * 地市
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String addr2;

    /**
     * 区县
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String addr3;

    /**
     * 乡镇
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String addr4;

    /**
     * 非结构地址
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String usaddr;

    /**
     * 一级管理目录
A01-基础产品
A02-行业应用
A03-硬件终端
A06-软件功能费+（代销类）硬件
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String spuOfferingClass;

    /**
     * 商品组编码/销售商品编码
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String spuOfferingCode;

    /**
     * 服务商唯一标识
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String supplierCode;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private Date updateTime;

    /**
     * 订单状态
0 待发货
1 待收货
2 已完成
暂时不用，订单中只看原子订单商品状态
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private Integer orderStatus;

    /**
     * 订单抵扣金额，单位：厘
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String deductPrice;

    /**
     * 订购渠道来源
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String orderingChannelSource;

    /**
     * 是否同步到k3   0--未同步 1--同步
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private Integer toK3;

    /**
     * 是否经过特殊的售后处理标识  0--未经过  1--经过
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private Integer specialAfterMarketHandle;

    /**
     * 特殊的售后状态：1-待退款 2-退款中 3-退款成功 4-退款取消
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String specialAfterStatus;

    /**
     * 特殊的售后状态变更时间
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String specialAfterStatusTime;

    /**
     * 特殊的售后截止时间
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String specialAfterLatestTime;

    /**
     * K3同步数据的id号
     *
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private String syncK3Id;

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.order_id
     *
     * @return the value of supply_chain..order_2c_info.order_id
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.order_id
     *
     * @param orderId the value for supply_chain..order_2c_info.order_id
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.order_type
     *
     * @return the value of supply_chain..order_2c_info.order_type
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getOrderType() {
        return orderType;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withOrderType(String orderType) {
        this.setOrderType(orderType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.order_type
     *
     * @param orderType the value for supply_chain..order_2c_info.order_type
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.business_code
     *
     * @return the value of supply_chain..order_2c_info.business_code
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getBusinessCode() {
        return businessCode;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withBusinessCode(String businessCode) {
        this.setBusinessCode(businessCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.business_code
     *
     * @param businessCode the value for supply_chain..order_2c_info.business_code
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.create_oper_code
     *
     * @return the value of supply_chain..order_2c_info.create_oper_code
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getCreateOperCode() {
        return createOperCode;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withCreateOperCode(String createOperCode) {
        this.setCreateOperCode(createOperCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.create_oper_code
     *
     * @param createOperCode the value for supply_chain..order_2c_info.create_oper_code
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setCreateOperCode(String createOperCode) {
        this.createOperCode = createOperCode;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.employee_num
     *
     * @return the value of supply_chain..order_2c_info.employee_num
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getEmployeeNum() {
        return employeeNum;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withEmployeeNum(String employeeNum) {
        this.setEmployeeNum(employeeNum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.employee_num
     *
     * @param employeeNum the value for supply_chain..order_2c_info.employee_num
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setEmployeeNum(String employeeNum) {
        this.employeeNum = employeeNum;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.cust_mg_name
     *
     * @return the value of supply_chain..order_2c_info.cust_mg_name
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getCustMgName() {
        return custMgName;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withCustMgName(String custMgName) {
        this.setCustMgName(custMgName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.cust_mg_name
     *
     * @param custMgName the value for supply_chain..order_2c_info.cust_mg_name
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setCustMgName(String custMgName) {
        this.custMgName = custMgName;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.cust_mg_phone
     *
     * @return the value of supply_chain..order_2c_info.cust_mg_phone
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getCustMgPhone() {
        return custMgPhone;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withCustMgPhone(String custMgPhone) {
        this.setCustMgPhone(custMgPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.cust_mg_phone
     *
     * @param custMgPhone the value for supply_chain..order_2c_info.cust_mg_phone
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setCustMgPhone(String custMgPhone) {
        this.custMgPhone = custMgPhone;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.order_status_time
     *
     * @return the value of supply_chain..order_2c_info.order_status_time
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Date getOrderStatusTime() {
        return orderStatusTime;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withOrderStatusTime(Date orderStatusTime) {
        this.setOrderStatusTime(orderStatusTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.order_status_time
     *
     * @param orderStatusTime the value for supply_chain..order_2c_info.order_status_time
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setOrderStatusTime(Date orderStatusTime) {
        this.orderStatusTime = orderStatusTime;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.cust_code
     *
     * @return the value of supply_chain..order_2c_info.cust_code
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getCustCode() {
        return custCode;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withCustCode(String custCode) {
        this.setCustCode(custCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.cust_code
     *
     * @param custCode the value for supply_chain..order_2c_info.cust_code
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setCustCode(String custCode) {
        this.custCode = custCode;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.cust_name
     *
     * @return the value of supply_chain..order_2c_info.cust_name
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getCustName() {
        return custName;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withCustName(String custName) {
        this.setCustName(custName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.cust_name
     *
     * @param custName the value for supply_chain..order_2c_info.cust_name
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setCustName(String custName) {
        this.custName = custName;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.be_id
     *
     * @return the value of supply_chain..order_2c_info.be_id
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getBeId() {
        return beId;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withBeId(String beId) {
        this.setBeId(beId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.be_id
     *
     * @param beId the value for supply_chain..order_2c_info.be_id
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setBeId(String beId) {
        this.beId = beId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.location
     *
     * @return the value of supply_chain..order_2c_info.location
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getLocation() {
        return location;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withLocation(String location) {
        this.setLocation(location);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.location
     *
     * @param location the value for supply_chain..order_2c_info.location
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setLocation(String location) {
        this.location = location;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.region_ID
     *
     * @return the value of supply_chain..order_2c_info.region_ID
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getRegionId() {
        return regionId;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withRegionId(String regionId) {
        this.setRegionId(regionId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.region_ID
     *
     * @param regionId the value for supply_chain..order_2c_info.region_ID
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.order_org_biz_code
     *
     * @return the value of supply_chain..order_2c_info.order_org_biz_code
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getOrderOrgBizCode() {
        return orderOrgBizCode;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withOrderOrgBizCode(String orderOrgBizCode) {
        this.setOrderOrgBizCode(orderOrgBizCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.order_org_biz_code
     *
     * @param orderOrgBizCode the value for supply_chain..order_2c_info.order_org_biz_code
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setOrderOrgBizCode(String orderOrgBizCode) {
        this.orderOrgBizCode = orderOrgBizCode;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.org_level
     *
     * @return the value of supply_chain..order_2c_info.org_level
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getOrgLevel() {
        return orgLevel;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withOrgLevel(String orgLevel) {
        this.setOrgLevel(orgLevel);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.org_level
     *
     * @param orgLevel the value for supply_chain..order_2c_info.org_level
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setOrgLevel(String orgLevel) {
        this.orgLevel = orgLevel;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.org_name
     *
     * @return the value of supply_chain..order_2c_info.org_name
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getOrgName() {
        return orgName;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withOrgName(String orgName) {
        this.setOrgName(orgName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.org_name
     *
     * @param orgName the value for supply_chain..order_2c_info.org_name
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.remarks
     *
     * @return the value of supply_chain..order_2c_info.remarks
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getRemarks() {
        return remarks;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withRemarks(String remarks) {
        this.setRemarks(remarks);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.remarks
     *
     * @param remarks the value for supply_chain..order_2c_info.remarks
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.bookId
     *
     * @return the value of supply_chain..order_2c_info.bookId
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getBookid() {
        return bookid;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withBookid(String bookid) {
        this.setBookid(bookid);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.bookId
     *
     * @param bookid the value for supply_chain..order_2c_info.bookId
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setBookid(String bookid) {
        this.bookid = bookid;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.status
     *
     * @return the value of supply_chain..order_2c_info.status
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Integer getStatus() {
        return status;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withStatus(Integer status) {
        this.setStatus(status);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.status
     *
     * @param status the value for supply_chain..order_2c_info.status
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.total_price
     *
     * @return the value of supply_chain..order_2c_info.total_price
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getTotalPrice() {
        return totalPrice;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withTotalPrice(String totalPrice) {
        this.setTotalPrice(totalPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.total_price
     *
     * @param totalPrice the value for supply_chain..order_2c_info.total_price
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setTotalPrice(String totalPrice) {
        this.totalPrice = totalPrice;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.create_time
     *
     * @return the value of supply_chain..order_2c_info.create_time
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withCreateTime(String createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.create_time
     *
     * @param createTime the value for supply_chain..order_2c_info.create_time
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.contact_person_name
     *
     * @return the value of supply_chain..order_2c_info.contact_person_name
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getContactPersonName() {
        return contactPersonName;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withContactPersonName(String contactPersonName) {
        this.setContactPersonName(contactPersonName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.contact_person_name
     *
     * @param contactPersonName the value for supply_chain..order_2c_info.contact_person_name
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setContactPersonName(String contactPersonName) {
        this.contactPersonName = contactPersonName;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.contact_phone
     *
     * @return the value of supply_chain..order_2c_info.contact_phone
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getContactPhone() {
        return contactPhone;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withContactPhone(String contactPhone) {
        this.setContactPhone(contactPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.contact_phone
     *
     * @param contactPhone the value for supply_chain..order_2c_info.contact_phone
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.addr1
     *
     * @return the value of supply_chain..order_2c_info.addr1
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getAddr1() {
        return addr1;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withAddr1(String addr1) {
        this.setAddr1(addr1);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.addr1
     *
     * @param addr1 the value for supply_chain..order_2c_info.addr1
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setAddr1(String addr1) {
        this.addr1 = addr1;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.addr2
     *
     * @return the value of supply_chain..order_2c_info.addr2
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getAddr2() {
        return addr2;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withAddr2(String addr2) {
        this.setAddr2(addr2);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.addr2
     *
     * @param addr2 the value for supply_chain..order_2c_info.addr2
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setAddr2(String addr2) {
        this.addr2 = addr2;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.addr3
     *
     * @return the value of supply_chain..order_2c_info.addr3
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getAddr3() {
        return addr3;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withAddr3(String addr3) {
        this.setAddr3(addr3);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.addr3
     *
     * @param addr3 the value for supply_chain..order_2c_info.addr3
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setAddr3(String addr3) {
        this.addr3 = addr3;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.addr4
     *
     * @return the value of supply_chain..order_2c_info.addr4
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getAddr4() {
        return addr4;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withAddr4(String addr4) {
        this.setAddr4(addr4);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.addr4
     *
     * @param addr4 the value for supply_chain..order_2c_info.addr4
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setAddr4(String addr4) {
        this.addr4 = addr4;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.usaddr
     *
     * @return the value of supply_chain..order_2c_info.usaddr
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getUsaddr() {
        return usaddr;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withUsaddr(String usaddr) {
        this.setUsaddr(usaddr);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.usaddr
     *
     * @param usaddr the value for supply_chain..order_2c_info.usaddr
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setUsaddr(String usaddr) {
        this.usaddr = usaddr;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.spu_offering_class
     *
     * @return the value of supply_chain..order_2c_info.spu_offering_class
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getSpuOfferingClass() {
        return spuOfferingClass;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withSpuOfferingClass(String spuOfferingClass) {
        this.setSpuOfferingClass(spuOfferingClass);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.spu_offering_class
     *
     * @param spuOfferingClass the value for supply_chain..order_2c_info.spu_offering_class
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setSpuOfferingClass(String spuOfferingClass) {
        this.spuOfferingClass = spuOfferingClass;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.spu_offering_code
     *
     * @return the value of supply_chain..order_2c_info.spu_offering_code
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getSpuOfferingCode() {
        return spuOfferingCode;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withSpuOfferingCode(String spuOfferingCode) {
        this.setSpuOfferingCode(spuOfferingCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.spu_offering_code
     *
     * @param spuOfferingCode the value for supply_chain..order_2c_info.spu_offering_code
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setSpuOfferingCode(String spuOfferingCode) {
        this.spuOfferingCode = spuOfferingCode;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.supplier_code
     *
     * @return the value of supply_chain..order_2c_info.supplier_code
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getSupplierCode() {
        return supplierCode;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withSupplierCode(String supplierCode) {
        this.setSupplierCode(supplierCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.supplier_code
     *
     * @param supplierCode the value for supply_chain..order_2c_info.supplier_code
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.update_time
     *
     * @return the value of supply_chain..order_2c_info.update_time
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.update_time
     *
     * @param updateTime the value for supply_chain..order_2c_info.update_time
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.order_status
     *
     * @return the value of supply_chain..order_2c_info.order_status
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Integer getOrderStatus() {
        return orderStatus;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withOrderStatus(Integer orderStatus) {
        this.setOrderStatus(orderStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.order_status
     *
     * @param orderStatus the value for supply_chain..order_2c_info.order_status
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.deduct_price
     *
     * @return the value of supply_chain..order_2c_info.deduct_price
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getDeductPrice() {
        return deductPrice;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withDeductPrice(String deductPrice) {
        this.setDeductPrice(deductPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.deduct_price
     *
     * @param deductPrice the value for supply_chain..order_2c_info.deduct_price
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setDeductPrice(String deductPrice) {
        this.deductPrice = deductPrice;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.ordering_channel_source
     *
     * @return the value of supply_chain..order_2c_info.ordering_channel_source
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getOrderingChannelSource() {
        return orderingChannelSource;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withOrderingChannelSource(String orderingChannelSource) {
        this.setOrderingChannelSource(orderingChannelSource);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.ordering_channel_source
     *
     * @param orderingChannelSource the value for supply_chain..order_2c_info.ordering_channel_source
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setOrderingChannelSource(String orderingChannelSource) {
        this.orderingChannelSource = orderingChannelSource;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.to_k3
     *
     * @return the value of supply_chain..order_2c_info.to_k3
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Integer getToK3() {
        return toK3;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withToK3(Integer toK3) {
        this.setToK3(toK3);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.to_k3
     *
     * @param toK3 the value for supply_chain..order_2c_info.to_k3
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setToK3(Integer toK3) {
        this.toK3 = toK3;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.special_after_market_handle
     *
     * @return the value of supply_chain..order_2c_info.special_after_market_handle
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Integer getSpecialAfterMarketHandle() {
        return specialAfterMarketHandle;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withSpecialAfterMarketHandle(Integer specialAfterMarketHandle) {
        this.setSpecialAfterMarketHandle(specialAfterMarketHandle);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.special_after_market_handle
     *
     * @param specialAfterMarketHandle the value for supply_chain..order_2c_info.special_after_market_handle
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setSpecialAfterMarketHandle(Integer specialAfterMarketHandle) {
        this.specialAfterMarketHandle = specialAfterMarketHandle;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.special_after_status
     *
     * @return the value of supply_chain..order_2c_info.special_after_status
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getSpecialAfterStatus() {
        return specialAfterStatus;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withSpecialAfterStatus(String specialAfterStatus) {
        this.setSpecialAfterStatus(specialAfterStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.special_after_status
     *
     * @param specialAfterStatus the value for supply_chain..order_2c_info.special_after_status
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setSpecialAfterStatus(String specialAfterStatus) {
        this.specialAfterStatus = specialAfterStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.special_after_status_time
     *
     * @return the value of supply_chain..order_2c_info.special_after_status_time
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getSpecialAfterStatusTime() {
        return specialAfterStatusTime;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withSpecialAfterStatusTime(String specialAfterStatusTime) {
        this.setSpecialAfterStatusTime(specialAfterStatusTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.special_after_status_time
     *
     * @param specialAfterStatusTime the value for supply_chain..order_2c_info.special_after_status_time
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setSpecialAfterStatusTime(String specialAfterStatusTime) {
        this.specialAfterStatusTime = specialAfterStatusTime;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.special_after_latest_time
     *
     * @return the value of supply_chain..order_2c_info.special_after_latest_time
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getSpecialAfterLatestTime() {
        return specialAfterLatestTime;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withSpecialAfterLatestTime(String specialAfterLatestTime) {
        this.setSpecialAfterLatestTime(specialAfterLatestTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.special_after_latest_time
     *
     * @param specialAfterLatestTime the value for supply_chain..order_2c_info.special_after_latest_time
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setSpecialAfterLatestTime(String specialAfterLatestTime) {
        this.specialAfterLatestTime = specialAfterLatestTime;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.sync_k3_id
     *
     * @return the value of supply_chain..order_2c_info.sync_k3_id
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public String getSyncK3Id() {
        return syncK3Id;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public Order2cInfo withSyncK3Id(String syncK3Id) {
        this.setSyncK3Id(syncK3Id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.sync_k3_id
     *
     * @param syncK3Id the value for supply_chain..order_2c_info.sync_k3_id
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public void setSyncK3Id(String syncK3Id) {
        this.syncK3Id = syncK3Id;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", orderId=").append(orderId);
        sb.append(", orderType=").append(orderType);
        sb.append(", businessCode=").append(businessCode);
        sb.append(", createOperCode=").append(createOperCode);
        sb.append(", employeeNum=").append(employeeNum);
        sb.append(", custMgName=").append(custMgName);
        sb.append(", custMgPhone=").append(custMgPhone);
        sb.append(", orderStatusTime=").append(orderStatusTime);
        sb.append(", custCode=").append(custCode);
        sb.append(", custName=").append(custName);
        sb.append(", beId=").append(beId);
        sb.append(", location=").append(location);
        sb.append(", regionId=").append(regionId);
        sb.append(", orderOrgBizCode=").append(orderOrgBizCode);
        sb.append(", orgLevel=").append(orgLevel);
        sb.append(", orgName=").append(orgName);
        sb.append(", remarks=").append(remarks);
        sb.append(", bookid=").append(bookid);
        sb.append(", status=").append(status);
        sb.append(", totalPrice=").append(totalPrice);
        sb.append(", createTime=").append(createTime);
        sb.append(", contactPersonName=").append(contactPersonName);
        sb.append(", contactPhone=").append(contactPhone);
        sb.append(", addr1=").append(addr1);
        sb.append(", addr2=").append(addr2);
        sb.append(", addr3=").append(addr3);
        sb.append(", addr4=").append(addr4);
        sb.append(", usaddr=").append(usaddr);
        sb.append(", spuOfferingClass=").append(spuOfferingClass);
        sb.append(", spuOfferingCode=").append(spuOfferingCode);
        sb.append(", supplierCode=").append(supplierCode);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", orderStatus=").append(orderStatus);
        sb.append(", deductPrice=").append(deductPrice);
        sb.append(", orderingChannelSource=").append(orderingChannelSource);
        sb.append(", toK3=").append(toK3);
        sb.append(", specialAfterMarketHandle=").append(specialAfterMarketHandle);
        sb.append(", specialAfterStatus=").append(specialAfterStatus);
        sb.append(", specialAfterStatusTime=").append(specialAfterStatusTime);
        sb.append(", specialAfterLatestTime=").append(specialAfterLatestTime);
        sb.append(", syncK3Id=").append(syncK3Id);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Order2cInfo other = (Order2cInfo) that;
        return (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getOrderType() == null ? other.getOrderType() == null : this.getOrderType().equals(other.getOrderType()))
            && (this.getBusinessCode() == null ? other.getBusinessCode() == null : this.getBusinessCode().equals(other.getBusinessCode()))
            && (this.getCreateOperCode() == null ? other.getCreateOperCode() == null : this.getCreateOperCode().equals(other.getCreateOperCode()))
            && (this.getEmployeeNum() == null ? other.getEmployeeNum() == null : this.getEmployeeNum().equals(other.getEmployeeNum()))
            && (this.getCustMgName() == null ? other.getCustMgName() == null : this.getCustMgName().equals(other.getCustMgName()))
            && (this.getCustMgPhone() == null ? other.getCustMgPhone() == null : this.getCustMgPhone().equals(other.getCustMgPhone()))
            && (this.getOrderStatusTime() == null ? other.getOrderStatusTime() == null : this.getOrderStatusTime().equals(other.getOrderStatusTime()))
            && (this.getCustCode() == null ? other.getCustCode() == null : this.getCustCode().equals(other.getCustCode()))
            && (this.getCustName() == null ? other.getCustName() == null : this.getCustName().equals(other.getCustName()))
            && (this.getBeId() == null ? other.getBeId() == null : this.getBeId().equals(other.getBeId()))
            && (this.getLocation() == null ? other.getLocation() == null : this.getLocation().equals(other.getLocation()))
            && (this.getRegionId() == null ? other.getRegionId() == null : this.getRegionId().equals(other.getRegionId()))
            && (this.getOrderOrgBizCode() == null ? other.getOrderOrgBizCode() == null : this.getOrderOrgBizCode().equals(other.getOrderOrgBizCode()))
            && (this.getOrgLevel() == null ? other.getOrgLevel() == null : this.getOrgLevel().equals(other.getOrgLevel()))
            && (this.getOrgName() == null ? other.getOrgName() == null : this.getOrgName().equals(other.getOrgName()))
            && (this.getRemarks() == null ? other.getRemarks() == null : this.getRemarks().equals(other.getRemarks()))
            && (this.getBookid() == null ? other.getBookid() == null : this.getBookid().equals(other.getBookid()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getTotalPrice() == null ? other.getTotalPrice() == null : this.getTotalPrice().equals(other.getTotalPrice()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getContactPersonName() == null ? other.getContactPersonName() == null : this.getContactPersonName().equals(other.getContactPersonName()))
            && (this.getContactPhone() == null ? other.getContactPhone() == null : this.getContactPhone().equals(other.getContactPhone()))
            && (this.getAddr1() == null ? other.getAddr1() == null : this.getAddr1().equals(other.getAddr1()))
            && (this.getAddr2() == null ? other.getAddr2() == null : this.getAddr2().equals(other.getAddr2()))
            && (this.getAddr3() == null ? other.getAddr3() == null : this.getAddr3().equals(other.getAddr3()))
            && (this.getAddr4() == null ? other.getAddr4() == null : this.getAddr4().equals(other.getAddr4()))
            && (this.getUsaddr() == null ? other.getUsaddr() == null : this.getUsaddr().equals(other.getUsaddr()))
            && (this.getSpuOfferingClass() == null ? other.getSpuOfferingClass() == null : this.getSpuOfferingClass().equals(other.getSpuOfferingClass()))
            && (this.getSpuOfferingCode() == null ? other.getSpuOfferingCode() == null : this.getSpuOfferingCode().equals(other.getSpuOfferingCode()))
            && (this.getSupplierCode() == null ? other.getSupplierCode() == null : this.getSupplierCode().equals(other.getSupplierCode()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getOrderStatus() == null ? other.getOrderStatus() == null : this.getOrderStatus().equals(other.getOrderStatus()))
            && (this.getDeductPrice() == null ? other.getDeductPrice() == null : this.getDeductPrice().equals(other.getDeductPrice()))
            && (this.getOrderingChannelSource() == null ? other.getOrderingChannelSource() == null : this.getOrderingChannelSource().equals(other.getOrderingChannelSource()))
            && (this.getToK3() == null ? other.getToK3() == null : this.getToK3().equals(other.getToK3()))
            && (this.getSpecialAfterMarketHandle() == null ? other.getSpecialAfterMarketHandle() == null : this.getSpecialAfterMarketHandle().equals(other.getSpecialAfterMarketHandle()))
            && (this.getSpecialAfterStatus() == null ? other.getSpecialAfterStatus() == null : this.getSpecialAfterStatus().equals(other.getSpecialAfterStatus()))
            && (this.getSpecialAfterStatusTime() == null ? other.getSpecialAfterStatusTime() == null : this.getSpecialAfterStatusTime().equals(other.getSpecialAfterStatusTime()))
            && (this.getSpecialAfterLatestTime() == null ? other.getSpecialAfterLatestTime() == null : this.getSpecialAfterLatestTime().equals(other.getSpecialAfterLatestTime()))
            && (this.getSyncK3Id() == null ? other.getSyncK3Id() == null : this.getSyncK3Id().equals(other.getSyncK3Id()));
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getOrderType() == null) ? 0 : getOrderType().hashCode());
        result = prime * result + ((getBusinessCode() == null) ? 0 : getBusinessCode().hashCode());
        result = prime * result + ((getCreateOperCode() == null) ? 0 : getCreateOperCode().hashCode());
        result = prime * result + ((getEmployeeNum() == null) ? 0 : getEmployeeNum().hashCode());
        result = prime * result + ((getCustMgName() == null) ? 0 : getCustMgName().hashCode());
        result = prime * result + ((getCustMgPhone() == null) ? 0 : getCustMgPhone().hashCode());
        result = prime * result + ((getOrderStatusTime() == null) ? 0 : getOrderStatusTime().hashCode());
        result = prime * result + ((getCustCode() == null) ? 0 : getCustCode().hashCode());
        result = prime * result + ((getCustName() == null) ? 0 : getCustName().hashCode());
        result = prime * result + ((getBeId() == null) ? 0 : getBeId().hashCode());
        result = prime * result + ((getLocation() == null) ? 0 : getLocation().hashCode());
        result = prime * result + ((getRegionId() == null) ? 0 : getRegionId().hashCode());
        result = prime * result + ((getOrderOrgBizCode() == null) ? 0 : getOrderOrgBizCode().hashCode());
        result = prime * result + ((getOrgLevel() == null) ? 0 : getOrgLevel().hashCode());
        result = prime * result + ((getOrgName() == null) ? 0 : getOrgName().hashCode());
        result = prime * result + ((getRemarks() == null) ? 0 : getRemarks().hashCode());
        result = prime * result + ((getBookid() == null) ? 0 : getBookid().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getTotalPrice() == null) ? 0 : getTotalPrice().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getContactPersonName() == null) ? 0 : getContactPersonName().hashCode());
        result = prime * result + ((getContactPhone() == null) ? 0 : getContactPhone().hashCode());
        result = prime * result + ((getAddr1() == null) ? 0 : getAddr1().hashCode());
        result = prime * result + ((getAddr2() == null) ? 0 : getAddr2().hashCode());
        result = prime * result + ((getAddr3() == null) ? 0 : getAddr3().hashCode());
        result = prime * result + ((getAddr4() == null) ? 0 : getAddr4().hashCode());
        result = prime * result + ((getUsaddr() == null) ? 0 : getUsaddr().hashCode());
        result = prime * result + ((getSpuOfferingClass() == null) ? 0 : getSpuOfferingClass().hashCode());
        result = prime * result + ((getSpuOfferingCode() == null) ? 0 : getSpuOfferingCode().hashCode());
        result = prime * result + ((getSupplierCode() == null) ? 0 : getSupplierCode().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getOrderStatus() == null) ? 0 : getOrderStatus().hashCode());
        result = prime * result + ((getDeductPrice() == null) ? 0 : getDeductPrice().hashCode());
        result = prime * result + ((getOrderingChannelSource() == null) ? 0 : getOrderingChannelSource().hashCode());
        result = prime * result + ((getToK3() == null) ? 0 : getToK3().hashCode());
        result = prime * result + ((getSpecialAfterMarketHandle() == null) ? 0 : getSpecialAfterMarketHandle().hashCode());
        result = prime * result + ((getSpecialAfterStatus() == null) ? 0 : getSpecialAfterStatus().hashCode());
        result = prime * result + ((getSpecialAfterStatusTime() == null) ? 0 : getSpecialAfterStatusTime().hashCode());
        result = prime * result + ((getSpecialAfterLatestTime() == null) ? 0 : getSpecialAfterLatestTime().hashCode());
        result = prime * result + ((getSyncK3Id() == null) ? 0 : getSyncK3Id().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Apr 03 11:13:23 CST 2023
     */
    public enum Column {
        orderId("order_id", "orderId", "VARCHAR", false),
        orderType("order_type", "orderType", "VARCHAR", false),
        businessCode("business_code", "businessCode", "VARCHAR", false),
        createOperCode("create_oper_code", "createOperCode", "VARCHAR", false),
        employeeNum("employee_num", "employeeNum", "VARCHAR", false),
        custMgName("cust_mg_name", "custMgName", "VARCHAR", false),
        custMgPhone("cust_mg_phone", "custMgPhone", "VARCHAR", false),
        orderStatusTime("order_status_time", "orderStatusTime", "TIMESTAMP", false),
        custCode("cust_code", "custCode", "VARCHAR", false),
        custName("cust_name", "custName", "VARCHAR", false),
        beId("be_id", "beId", "VARCHAR", false),
        location("location", "location", "VARCHAR", false),
        regionId("region_ID", "regionId", "VARCHAR", false),
        orderOrgBizCode("order_org_biz_code", "orderOrgBizCode", "VARCHAR", false),
        orgLevel("org_level", "orgLevel", "VARCHAR", false),
        orgName("org_name", "orgName", "VARCHAR", false),
        remarks("remarks", "remarks", "VARCHAR", false),
        bookid("bookId", "bookid", "VARCHAR", false),
        status("status", "status", "INTEGER", false),
        totalPrice("total_price", "totalPrice", "VARCHAR", false),
        createTime("create_time", "createTime", "VARCHAR", false),
        contactPersonName("contact_person_name", "contactPersonName", "VARCHAR", false),
        contactPhone("contact_phone", "contactPhone", "VARCHAR", false),
        addr1("addr1", "addr1", "VARCHAR", false),
        addr2("addr2", "addr2", "VARCHAR", false),
        addr3("addr3", "addr3", "VARCHAR", false),
        addr4("addr4", "addr4", "VARCHAR", false),
        usaddr("usaddr", "usaddr", "VARCHAR", false),
        spuOfferingClass("spu_offering_class", "spuOfferingClass", "VARCHAR", false),
        spuOfferingCode("spu_offering_code", "spuOfferingCode", "VARCHAR", false),
        supplierCode("supplier_code", "supplierCode", "VARCHAR", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        orderStatus("order_status", "orderStatus", "INTEGER", false),
        deductPrice("deduct_price", "deductPrice", "VARCHAR", false),
        orderingChannelSource("ordering_channel_source", "orderingChannelSource", "VARCHAR", false),
        toK3("to_k3", "toK3", "INTEGER", false),
        specialAfterMarketHandle("special_after_market_handle", "specialAfterMarketHandle", "INTEGER", false),
        specialAfterStatus("special_after_status", "specialAfterStatus", "VARCHAR", false),
        specialAfterStatusTime("special_after_status_time", "specialAfterStatusTime", "VARCHAR", false),
        specialAfterLatestTime("special_after_latest_time", "specialAfterLatestTime", "VARCHAR", false),
        syncK3Id("sync_k3_id", "syncK3Id", "VARCHAR", false);

        /**
         *
         * @mbg.generated Mon Apr 03 11:13:23 CST 2023
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Apr 03 11:13:23 CST 2023
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Apr 03 11:13:23 CST 2023
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Apr 03 11:13:23 CST 2023
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Apr 03 11:13:23 CST 2023
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Apr 03 11:13:23 CST 2023
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Apr 03 11:13:23 CST 2023
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Apr 03 11:13:23 CST 2023
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Apr 03 11:13:23 CST 2023
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Apr 03 11:13:23 CST 2023
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Apr 03 11:13:23 CST 2023
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Apr 03 11:13:23 CST 2023
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Apr 03 11:13:23 CST 2023
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Apr 03 11:13:23 CST 2023
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Apr 03 11:13:23 CST 2023
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Apr 03 11:13:23 CST 2023
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Apr 03 11:13:23 CST 2023
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}