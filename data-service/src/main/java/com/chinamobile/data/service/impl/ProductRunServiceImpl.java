package com.chinamobile.data.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.data.dao.ext.Order2cAtomInfoMapperExt;
import com.chinamobile.data.pojo.mapper.DepartmentSellDO;
import com.chinamobile.data.pojo.mapper.ProductRealNameSellDO;
import com.chinamobile.data.pojo.mapper.ProvinceOfferingClassTotalSellDO;
import com.chinamobile.data.pojo.mapper.ProvinceSpuCodeUseDO;
import com.chinamobile.data.pojo.param.ProductRealNameSellParamProduct;
import com.chinamobile.data.pojo.param.ProductRunParam;
import com.chinamobile.data.pojo.vo.*;
import com.chinamobile.data.service.ProductRunService;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ResponseCode;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.util.CommonResponseUtils;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

import static com.chinamobile.data.config.CommonConstant.REDIS_PROVINCE_KEY;
import static com.chinamobile.iot.sc.common.BaseConstant.ADMIN_ROLE;
import static com.chinamobile.iot.sc.common.BaseConstant.OPERATOR_ROLE;
import static com.chinamobile.iot.sc.enums.SPUOfferingClassEnum.*;
import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.DATE_PARSE_ERROR;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/19
 * @description 运营周报service实现类
 */
@Service
@Slf4j
public class ProductRunServiceImpl implements ProductRunService {

    @Resource
    private Order2cAtomInfoMapperExt order2cAtomInfoMapperExt;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public List<ProvinceOfferingClassTotalSellVO> listOfferingClassTotalSellByProvince(ProductRunParam productRunParam, LoginIfo4Redis loginIfo4Redis) {
        String roleType = loginIfo4Redis.getRoleType();
        if (!ADMIN_ROLE.equals(roleType) && !OPERATOR_ROLE.equals(roleType)){
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
        }
        validAndSetTime(productRunParam);

        List<ProvinceOfferingClassTotalSellDO> totalSellDOList = order2cAtomInfoMapperExt.listOfferingClassTotalSellByProvince(productRunParam);

        List<ProvinceOfferingClassTotalSellVO> totalSellVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(totalSellDOList)) {
            String beId = "";
            ProvinceOfferingClassTotalSellVO totalSellVO = null;
            // 用于计算省份的销售总额
            BigDecimal provinceTotalSell = null;
            // 用于计算产品增值服务包销售总额
            List<BigDecimal> productAddedValueTotalSell = new ArrayList<>();
            productAddedValueTotalSell.add(new BigDecimal(0));
            // 用于计算合同履约销售总额
            List<BigDecimal> contractComplianceTotalSell = new ArrayList<>();
            contractComplianceTotalSell.add(new BigDecimal(0));
            // 用于计算onenet独立服务销售总额
            List<BigDecimal> onenetServiceTotalSell = new ArrayList<>();
            onenetServiceTotalSell.add(new BigDecimal(0));
            // 用于计算联合销售总额
            List<BigDecimal> unionTotalSell = new ArrayList<>();
            unionTotalSell.add(new BigDecimal(0));
            // 用于计算OnePark独立销售总额
            List<BigDecimal> oneParkTotalSell = new ArrayList<>();
            oneParkTotalSell.add(new BigDecimal(0));
            // 用于计算行车卫士标准产品销售总额
            List<BigDecimal> carTVStandardProductsTotalSell = new ArrayList<>();
            carTVStandardProductsTotalSell.add(new BigDecimal(0));
            // 用于计算软件服务销售总额
            List<BigDecimal> softwareServiceTotalSell = new ArrayList<>();
            softwareServiceTotalSell.add(new BigDecimal(0));
            // 用于计算OneCyber标准产品销售总额
            List<BigDecimal> oneCyberTotalSell = new ArrayList<>();
            oneCyberTotalSell.add(new BigDecimal(0));

            // 用于计算所有销售总额
            List<BigDecimal> totalSell = new ArrayList<>();

            totalSell.add(new BigDecimal(0));

            BigDecimal thousand = new BigDecimal(1000);

            int totalSellDOSize = totalSellDOList.size();
            for (int i = 0; i < totalSellDOSize; i++) {
                ProvinceOfferingClassTotalSellDO totalSellDO = totalSellDOList.get(i);
                // 初始化数据
                if (i == 0) {
                    totalSellVO = new ProvinceOfferingClassTotalSellVO();
                    beId = initTotalSellVO(totalSellVO, totalSellDO,
                            productAddedValueTotalSell, contractComplianceTotalSell,
                            onenetServiceTotalSell, unionTotalSell,oneParkTotalSell,carTVStandardProductsTotalSell,softwareServiceTotalSell,oneCyberTotalSell, totalSell);
                    provinceTotalSell = new BigDecimal(0);
                    provinceTotalSell = provinceTotalSell.add(totalSellDO.getOfferingClassSell());
                    if (totalSellDOSize == 1){
                        lastValueSetTotalSellVO(totalSellVO,provinceTotalSell,thousand,totalSellVOList);
                    }
                } else {
                    // 如果省份id继续相等，那么说明是一个省份的数据，否则重新初始化beId及totalSellDO，省份的销售总额
                    if (beId.equals(totalSellDO.getBeId())) {
                        String spuOfferingClass = totalSellDO.getSpuOfferingClass();
                        BigDecimal offeringClassSell = totalSellDO.getOfferingClassSell();
                        setSell(spuOfferingClass, offeringClassSell, totalSellVO,
                                productAddedValueTotalSell, contractComplianceTotalSell,
                                onenetServiceTotalSell, unionTotalSell,oneParkTotalSell,carTVStandardProductsTotalSell,softwareServiceTotalSell,oneCyberTotalSell, totalSell);
                        provinceTotalSell = provinceTotalSell.add(offeringClassSell);
                        if (i==totalSellDOSize-1){
                            lastValueSetTotalSellVO(totalSellVO,provinceTotalSell,thousand,totalSellVOList);
                        }
                    } else {
                        totalSellVO.setProvinceTotalSell(divideUnit(provinceTotalSell,thousand));
                        totalSellVOList.add(totalSellVO);
                        // 重新初始化数据
                        totalSellVO = new ProvinceOfferingClassTotalSellVO();
                        beId = initTotalSellVO(totalSellVO, totalSellDO,
                                productAddedValueTotalSell, contractComplianceTotalSell,
                                onenetServiceTotalSell, unionTotalSell,oneParkTotalSell, carTVStandardProductsTotalSell,softwareServiceTotalSell,oneCyberTotalSell,totalSell);
                        provinceTotalSell = new BigDecimal(0);
                        provinceTotalSell = provinceTotalSell.add(totalSellDO.getOfferingClassSell());
                        if (i==totalSellDOSize-1){
                            lastValueSetTotalSellVO(totalSellVO,provinceTotalSell,thousand,totalSellVOList);
                        }
                    }
                }

            }

            // 进行单位换算
            if (CollectionUtils.isNotEmpty(totalSellVOList)){
                totalSellVOList.stream().forEach(totalSellVO1 -> {
                    totalSellVO1.setProductAddedValueSell(divideUnit(totalSellVO1.getProductAddedValueSell(), thousand));
                    totalSellVO1.setUnionSell(divideUnit(totalSellVO1.getUnionSell(),thousand));
                    totalSellVO1.setContractComplianceSell(divideUnit(totalSellVO1.getContractComplianceSell(),thousand));
                    totalSellVO1.setOnenetServiceSell(divideUnit(totalSellVO1.getOnenetServiceSell(),thousand));
                    totalSellVO1.setOneParkServiceSell(divideUnit(totalSellVO1.getOneParkServiceSell(),thousand));
                    totalSellVO1.setCarTVStandardProductsSell(divideUnit(totalSellVO1.getCarTVStandardProductsSell(),thousand));
                });
            }

            // 销售额(元)
            totalSellVO = new ProvinceOfferingClassTotalSellVO();
            totalSellVO.setProvinceName("销售额(元)");
            setTotalSellVO(totalSellVO, productAddedValueTotalSell, contractComplianceTotalSell,
                    onenetServiceTotalSell, unionTotalSell,oneParkTotalSell, carTVStandardProductsTotalSell,totalSell, thousand);
            totalSellVOList.add(totalSellVO);
            // 销售额（万元）
            totalSellVO = new ProvinceOfferingClassTotalSellVO();
            totalSellVO.setProvinceName("销售额(万元)");
            BigDecimal tenMillion = new BigDecimal(10000000);
            setTotalSellVO(totalSellVO, productAddedValueTotalSell, contractComplianceTotalSell,
                    onenetServiceTotalSell, unionTotalSell,oneParkTotalSell,carTVStandardProductsTotalSell, totalSell, tenMillion);
            totalSellVOList.add(totalSellVO);
        }
        return totalSellVOList;
    }

    @Override
    public void exportOfferingClassTotalSellByProvince(ProductRunParam productRunParam,LoginIfo4Redis loginIfo4Redis) throws IOException {
        validAndSetTime(productRunParam);

        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

        List<ProvinceOfferingClassTotalSellVO> totalSellVOList = this.listOfferingClassTotalSellByProvince(productRunParam, loginIfo4Redis);

        String startTime = productRunParam.getStartTime();
        String endTime = productRunParam.getEndTime();
        String fileName = "商城销售数据(省份-范式)".concat(startTime).concat("--").concat(endTime);

        CommonResponseUtils commonResponseUtils = ExcelUtils.exportExcel(totalSellVOList, fileName, fileName, ProvinceOfferingClassTotalSellVO.class, fileName, response);
        if (!commonResponseUtils.get("stateCode").equals(ResponseCode.SUCCESS)) {
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR);
        }
    }

    @Override
    public List<ProvinceSpuCodeUseVO> listSpuCodeUseByProvince(ProductRunParam productRunParam,LoginIfo4Redis loginIfo4Redis) {
        String roleType = loginIfo4Redis.getRoleType();
        if (!ADMIN_ROLE.equals(roleType) && !OPERATOR_ROLE.equals(roleType)){
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
        }
        validAndSetTime(productRunParam);

        List<ProvinceSpuCodeUseDO> provinceSpuCodeUseDOList = order2cAtomInfoMapperExt.listSpuCodeUseByProvince(productRunParam);

        List<ProvinceSpuCodeUseVO> spuCodeUseVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(provinceSpuCodeUseDOList)) {
            String beId = "";
            ProvinceSpuCodeUseVO spuCodeUseVO = null;
            // 用于计算省份的销售总额
            Integer provinceTotalUse = null;
            // 用于计算产品增值服务包破零数量
            List<Integer> productAddedValueTotalUse = new ArrayList<>();
            productAddedValueTotalUse.add(0);
            // 用于计算合同履约破零数量
            List<Integer> contractComplianceTotalUse = new ArrayList<>();
            contractComplianceTotalUse.add(0);
            // 用于计算onenet独立服务破零数量
            List<Integer> onenetServiceTotalUse = new ArrayList<>();
            onenetServiceTotalUse.add(0);
            // 用于计算联合破零数量
            List<Integer> unionTotalUse = new ArrayList<>();
            unionTotalUse.add(0);
            // 用于计算onepark独立服务破零数量
            List<Integer> oneParkServiceTotalUse = new ArrayList<>();
            oneParkServiceTotalUse.add(0);
            // 用于计算行车卫士标准产品破零数量
            List<Integer>  carTVStandardProductsTotalUse = new ArrayList<>();
            carTVStandardProductsTotalUse.add(0);
            // 用于计算软件服务破零数量
            List<Integer>  softwareServiceTotalUse = new ArrayList<>();
            softwareServiceTotalUse.add(0);
            // 用于计算OneCyber标准产品破零数量
            List<Integer>  oneCyberTotalUse = new ArrayList<>();
            oneCyberTotalUse.add(0);
            // 用于计算所有破零数量
            List<Integer> totalUse = new ArrayList<>();
            totalUse.add(0);

            int totalUseDOSize = provinceSpuCodeUseDOList.size();
            for (int i = 0; i < totalUseDOSize; i++) {
                ProvinceSpuCodeUseDO spuCodeUseDO = provinceSpuCodeUseDOList.get(i);
                // 初始化数据
                Integer spuOfferingCode = spuCodeUseDO.getSpuOfferingCode();
                if (i == 0) {
                    spuCodeUseVO = new ProvinceSpuCodeUseVO();
                    beId = initSpuUseVO(spuCodeUseVO, spuCodeUseDO,
                            productAddedValueTotalUse, contractComplianceTotalUse,
                            onenetServiceTotalUse, unionTotalUse,oneParkServiceTotalUse,carTVStandardProductsTotalUse,softwareServiceTotalUse,oneCyberTotalUse, totalUse);
                    provinceTotalUse = 0;
                    provinceTotalUse = provinceTotalUse + spuOfferingCode;
                    if (totalUseDOSize == 1){
                        lastValueSetProvinceSpuCodeUse(spuCodeUseVO,provinceTotalUse,spuCodeUseVOList);
                    }
                } else {
                    // 如果省份id继续相等，那么说明是一个省份的数据，否则重新初始化beId及totalSellDO，省份的销售总额
                    if (beId.equals(spuCodeUseDO.getBeId())) {
                        String spuOfferingClass = spuCodeUseDO.getSpuOfferingClass();
                        setSpuUse(spuOfferingClass, spuOfferingCode, spuCodeUseVO,
                                productAddedValueTotalUse, contractComplianceTotalUse,
                                onenetServiceTotalUse, unionTotalUse,oneParkServiceTotalUse,carTVStandardProductsTotalUse,softwareServiceTotalUse,oneCyberTotalUse, totalUse);
                        provinceTotalUse = provinceTotalUse + spuOfferingCode;
                        if (i==totalUseDOSize-1){
                            lastValueSetProvinceSpuCodeUse(spuCodeUseVO,provinceTotalUse,spuCodeUseVOList);
                        }
                    } else {
                        spuCodeUseVO.setProvinceTotalUse(provinceTotalUse);
                        spuCodeUseVOList.add(spuCodeUseVO);
                        // 重新初始化数据
                        spuCodeUseVO = new ProvinceSpuCodeUseVO();
                        beId = initSpuUseVO(spuCodeUseVO, spuCodeUseDO,
                                productAddedValueTotalUse, contractComplianceTotalUse,
                                onenetServiceTotalUse, unionTotalUse,oneParkServiceTotalUse,carTVStandardProductsTotalUse, softwareServiceTotalUse,oneCyberTotalUse,totalUse);
                        provinceTotalUse = 0;
                        provinceTotalUse = provinceTotalUse + spuOfferingCode;
                        if (i==totalUseDOSize-1){
                            lastValueSetProvinceSpuCodeUse(spuCodeUseVO,provinceTotalUse,spuCodeUseVOList);
                        }
                    }
                }

            }
            // 销售额(元)
            spuCodeUseVO = new ProvinceSpuCodeUseVO();
            spuCodeUseVO.setProvinceName("合计");
            spuCodeUseVO.setProductAddedValueUse(productAddedValueTotalUse.get(0));
            spuCodeUseVO.setContractComplianceUse(contractComplianceTotalUse.get(0));
            spuCodeUseVO.setOnenetServiceUse(onenetServiceTotalUse.get(0));
            spuCodeUseVO.setUnionUse(unionTotalUse.get(0));
            spuCodeUseVO.setOneParkServiceUse(oneParkServiceTotalUse.get(0));
            spuCodeUseVO.setCarTVStandardProductsUse(carTVStandardProductsTotalUse.get(0));
            spuCodeUseVO.setProvinceTotalUse(totalUse.get(0));
            spuCodeUseVOList.add(spuCodeUseVO);
        }
        return spuCodeUseVOList;
    }

    @Override
    public void exportSpuCodeUseByProvince(ProductRunParam productRunParam,LoginIfo4Redis loginIfo4Redis) throws IOException {
        validAndSetTime(productRunParam);

        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

        List<ProvinceSpuCodeUseVO> spuCodeUseVOList = this.listSpuCodeUseByProvince(productRunParam,loginIfo4Redis);

        String startTime = productRunParam.getStartTime();
        String endTime = productRunParam.getEndTime();
        String fileName = "商品范式销售破零数据数据".concat(startTime).concat("--").concat(endTime);

        CommonResponseUtils commonResponseUtils = ExcelUtils.exportExcel(spuCodeUseVOList, fileName, fileName, ProvinceSpuCodeUseVO.class, fileName, response);
        if (!commonResponseUtils.get("stateCode").equals(ResponseCode.SUCCESS)) {
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR);
        }
    }

    @Override
    public PageVo<ProductRealNameSellVO> pageProductRealNameSell(ProductRealNameSellParamProduct productRealNameSellParam,LoginIfo4Redis loginIfo4Redis) {
        String roleType = loginIfo4Redis.getRoleType();
        if (!ADMIN_ROLE.equals(roleType) && !OPERATOR_ROLE.equals(roleType)){
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
        }
        // 时间验证
        validAndSetTime(productRealNameSellParam);

        BigDecimal thousand = new BigDecimal(1000);
        BigDecimal tenMillion = new BigDecimal(10000000);

        // 销售范围值校验
        compareSell(productRealNameSellParam, thousand);

        PageVo<ProductRealNameSellVO> pageRespVo = new PageVo<>();
        Integer pageIndex = productRealNameSellParam.getPage();
        Integer num = productRealNameSellParam.getNum();
        if (pageIndex == null || pageIndex == 0) {
            pageIndex = 1;
        }
        if (num == null || num == 0) {
            num = 5;
        }
        Page<ProductRealNameSellVO> page = new Page<>(pageIndex, num);
        List<ProductRealNameSellDO> realNameSellDOList
                = order2cAtomInfoMapperExt.listProductRealNameSell(page, productRealNameSellParam);

        pageRespVo.setCurrentPage(pageIndex);
        pageRespVo.setPageCount(num);
        pageRespVo.setTotalCount(page.getTotal());
        List<ProductRealNameSellVO> realNameSellVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(realNameSellDOList)) {
            for (int i = 0; i < realNameSellDOList.size(); i++) {
                ProductRealNameSellDO productRealNameSellDO = realNameSellDOList.get(i);
                ProductRealNameSellVO productRealNameSellVO = new ProductRealNameSellVO();
                setProductRealNameSellVO(productRealNameSellDO, productRealNameSellVO, thousand, tenMillion);
                // 销售排名计算，如第6名，当前页大小为5，第2页，i=0,计算结果就是0+1(2-1)*5=6
                productRealNameSellVO.setSellOrder(i + 1 + (pageIndex - 1) * num);
                realNameSellVOList.add(productRealNameSellVO);
            }
        }

        pageRespVo.setList(realNameSellVOList);
        return pageRespVo;
    }

    @Override
    public void exportProductRealNameSell(ProductRealNameSellParamProduct productRealNameSellParam) throws IOException {
        // 时间验证
        validAndSetTime(productRealNameSellParam);

        BigDecimal thousand = new BigDecimal(1000);
        BigDecimal tenMillion = new BigDecimal(10000000);

        // 销售范围值校验
        compareSell(productRealNameSellParam, thousand);

        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

        List<ProductRealNameSellDO> realNameSellDOList
                = order2cAtomInfoMapperExt.listProductRealNameSell(productRealNameSellParam);

        List<ProductRealNameSellVO> realNameSellVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(realNameSellDOList)) {
            int realNameSellSize = realNameSellDOList.size();
            for (int i = 0; i < realNameSellSize; i++) {
                ProductRealNameSellDO productRealNameSellDO = realNameSellDOList.get(i);
                ProductRealNameSellVO productRealNameSellVO = new ProductRealNameSellVO();
                setProductRealNameSellVO(productRealNameSellDO, productRealNameSellVO, thousand, tenMillion);
                // 销售排名计算
                productRealNameSellVO.setSellOrder(i + 1);
                realNameSellVOList.add(productRealNameSellVO);
            }
        }

        String startTime = productRealNameSellParam.getStartTime();
        String endTime = productRealNameSellParam.getEndTime();
        String fileName = "商品销售排名数据(实际销售产品名称&商品名称spu)".concat(startTime).concat("--").concat(endTime);

        CommonResponseUtils commonResponseUtils = ExcelUtils.exportExcel(realNameSellVOList, fileName, fileName, ProductRealNameSellVO.class, fileName, response);
        if (!commonResponseUtils.get("stateCode").equals(ResponseCode.SUCCESS)) {
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR);
        }
    }

    @Override
    public List<DepartmentSellVO> listDepartmentSell(ProductRunParam productRunParam,LoginIfo4Redis loginIfo4Redis) {
        String roleType = loginIfo4Redis.getRoleType();
        if (!ADMIN_ROLE.equals(roleType) && !OPERATOR_ROLE.equals(roleType)){
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
        }
        validAndSetTime(productRunParam);

        List<DepartmentSellDO> departmentSellDOList = order2cAtomInfoMapperExt.listDepartmentSell(productRunParam);

        List<DepartmentSellVO> departmentSellVOList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(departmentSellDOList)) {
            BigDecimal thousand = new BigDecimal(1000);
            BigDecimal tenMillion = new BigDecimal(10000000);

            // 所有部门的省公司销售额汇总
            BigDecimal provinceSellTotal = new BigDecimal(0);
            // 所有部门的物联网公司销售额汇总
            BigDecimal networkSellTotal = new BigDecimal(0);

            for (int i = 0; i < departmentSellDOList.size(); i++) {
                DepartmentSellDO departmentSellDO = departmentSellDOList.get(i);
                DepartmentSellVO departmentSellVO = new DepartmentSellVO();
                departmentSellVO.setDepartmentName(departmentSellDO.getDepartmentName());
                BigDecimal provinceSell = departmentSellDO.getProvinceSell();
                BigDecimal networkSell = departmentSellDO.getNetworkSell();
                setDepartmentSellVO(departmentSellVO, networkSell, provinceSell, thousand, tenMillion);

                provinceSellTotal = provinceSellTotal.add(provinceSell);
                networkSellTotal = networkSellTotal.add(networkSell);

                departmentSellVOList.add(departmentSellVO);
            }

            DepartmentSellVO departmentSellVO = new DepartmentSellVO();
            departmentSellVO.setDepartmentName("合计");
            setDepartmentSellVO(departmentSellVO, networkSellTotal, provinceSellTotal, thousand, tenMillion);
            departmentSellVOList.add(departmentSellVO);
        }

        return departmentSellVOList;
    }

    @Override
    public void exportDepartmentSell(ProductRunParam productRunParam,LoginIfo4Redis loginIfo4Redis) throws IOException {
        validAndSetTime(productRunParam);

        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();

        List<DepartmentSellVO> departmentSellVOList = this.listDepartmentSell(productRunParam,loginIfo4Redis);

        String startTime = productRunParam.getStartTime();
        String endTime = productRunParam.getEndTime();
        String fileName = "产品部门销售数据".concat(startTime).concat("--").concat(endTime);

        CommonResponseUtils commonResponseUtils = ExcelUtils.exportExcel(departmentSellVOList, fileName, fileName, DepartmentSellVO.class, fileName, response);
        if (!commonResponseUtils.get("stateCode").equals(ResponseCode.SUCCESS)) {
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR);
        }
    }

    /**
     * 商城范式销售额最后一个值时设置数据
     * @param totalSellVO
     * @param provinceTotalSell
     * @param unit
     * @param totalSellVOList
     */
    private void lastValueSetTotalSellVO(ProvinceOfferingClassTotalSellVO totalSellVO,
                                         BigDecimal provinceTotalSell,
                                         BigDecimal unit,
                                         List<ProvinceOfferingClassTotalSellVO> totalSellVOList){
        totalSellVO.setProvinceTotalSell(divideUnit(provinceTotalSell,unit));
        totalSellVOList.add(totalSellVO);
    }

    /**
     * 范式销售额破零数据最后一个值时设置数据
     * @param spuCodeUseVO
     * @param provinceTotalUse
     * @param spuCodeUseVOList
     */
    private void lastValueSetProvinceSpuCodeUse(ProvinceSpuCodeUseVO spuCodeUseVO,
                                                Integer provinceTotalUse,
                                                List<ProvinceSpuCodeUseVO> spuCodeUseVOList){
        spuCodeUseVO.setProvinceTotalUse(provinceTotalUse);
        spuCodeUseVOList.add(spuCodeUseVO);
    }

    /**
     * 设置部门销售额参数
     *
     * @param departmentSellVO
     * @param networkSell
     * @param provinceSell
     * @param thousand
     * @param tenMillion
     */
    private void setDepartmentSellVO(DepartmentSellVO departmentSellVO,
                                     BigDecimal networkSell,
                                     BigDecimal provinceSell,
                                     BigDecimal thousand,
                                     BigDecimal tenMillion) {
        departmentSellVO.setNetworkSellY(divideUnit(networkSell, thousand));
        departmentSellVO.setNetworkSellW(divideUnit(networkSell, tenMillion));
        departmentSellVO.setProvinceSellY(divideUnit(provinceSell, thousand));
        departmentSellVO.setProvinceSellW(divideUnit(provinceSell, tenMillion));
    }

    /**
     * 设置实际销售产品信息
     *
     * @param productRealNameSellDO
     * @param productRealNameSellVO
     * @param thousand
     * @param tenMillion
     */
    private void setProductRealNameSellVO(ProductRealNameSellDO productRealNameSellDO,
                                          ProductRealNameSellVO productRealNameSellVO,
                                          BigDecimal thousand, BigDecimal tenMillion) {
        BigDecimal realNameSell = productRealNameSellDO.getRealNameSell();
        BeanUtils.copyProperties(productRealNameSellDO, productRealNameSellVO);
        productRealNameSellVO.setRealNameSellY(divideUnit(realNameSell, thousand));
        productRealNameSellVO.setRealNameSellW(divideUnit(realNameSell, tenMillion));

    }

    private BigDecimal divideUnit(BigDecimal divide, BigDecimal unit) {
        return divide.divide(unit, 2, RoundingMode.HALF_UP);
    }

    /**
     * 进行销售额范围验证
     *
     * @param productRealNameSellParam
     * @param thousand
     */
    private void compareSell(ProductRealNameSellParamProduct productRealNameSellParam,
                             BigDecimal thousand) {
        BigDecimal startSell = productRealNameSellParam.getStartSell();
        BigDecimal endSell = productRealNameSellParam.getEndSell();
        BigDecimal zero = new BigDecimal(0);
        if (startSell != null) {
            int startSellCompare = startSell.compareTo(zero);
            if (startSellCompare > 0) {
                productRealNameSellParam.setStartSell(startSell.multiply(thousand));
            } else if (startSellCompare < 0) {
                productRealNameSellParam.setStartSell(zero);
            }
        }

        if (endSell != null) {
            int endSellCompare = endSell.compareTo(zero);
            if (endSellCompare > 0) {
                productRealNameSellParam.setEndSell(endSell.multiply(thousand));
            } else if (endSellCompare < 0) {
                productRealNameSellParam.setEndSell(zero);
            }
        }
    }

    /**
     * 验证和设置时间
     *
     * @param productRunParam
     */
    private void validAndSetTime(ProductRunParam productRunParam) {
        String startTime = productRunParam.getStartTime();
        String endTime = productRunParam.getEndTime();
        validDate(startTime, endTime);
        if (StringUtils.isEmpty(startTime)) {
            productRunParam.setStartTime(DateUtils.getCurrentYearBeginNoSymbol());
        }

        if (StringUtils.isEmpty(endTime)) {
            productRunParam.setEndTime(DateUtils.getCurrentDate(DateUtils.DATE_FORMAT_NO_SYMBOL));
        }
    }


    /**
     * 设置销售额总量数据
     *
     * @param totalSellVO
     * @param productAddedValueTotalSell
     * @param contractComplianceTotalSell
     * @param onenetServiceTotalSell
     * @param unionTotalSell
     * @param totalSell
     * @param unit
     */
    private void setTotalSellVO(ProvinceOfferingClassTotalSellVO totalSellVO,
                                List<BigDecimal> productAddedValueTotalSell,
                                List<BigDecimal> contractComplianceTotalSell,
                                List<BigDecimal> onenetServiceTotalSell,
                                List<BigDecimal> unionTotalSell,
                                List<BigDecimal> oneParkTotalSell,
                                List<BigDecimal> carTVStandardProductsTotalSell,
                                List<BigDecimal> totalSell,
                                BigDecimal unit) {
        totalSellVO.setProductAddedValueSell(divideUnit(productAddedValueTotalSell.get(0), unit));
        totalSellVO.setContractComplianceSell(divideUnit(contractComplianceTotalSell.get(0), unit));
        totalSellVO.setOnenetServiceSell(divideUnit(onenetServiceTotalSell.get(0), unit));
        totalSellVO.setUnionSell(divideUnit(unionTotalSell.get(0), unit));
        totalSellVO.setOneParkServiceSell(divideUnit(oneParkTotalSell.get(0),unit));
        totalSellVO.setCarTVStandardProductsSell(divideUnit(carTVStandardProductsTotalSell.get(0),unit));
        totalSellVO.setProvinceTotalSell(divideUnit(totalSell.get(0), unit));
    }

    /**
     * 初始化信息
     *
     * @param totalSellVO
     * @param totalSellDO
     * @param productAddedValueTotalSell
     * @param contractComplianceTotalSell
     * @param onenetServiceTotalSell
     * @param unionTotalSell
     * @param totalSell
     * @return
     */
    private String initTotalSellVO(ProvinceOfferingClassTotalSellVO totalSellVO,
                                   ProvinceOfferingClassTotalSellDO totalSellDO,
                                   List<BigDecimal> productAddedValueTotalSell,
                                   List<BigDecimal> contractComplianceTotalSell,
                                   List<BigDecimal> onenetServiceTotalSell,
                                   List<BigDecimal> unionTotalSell,
                                   List<BigDecimal> oneParkTotalSell,
                                   List<BigDecimal> carTVStandardProductsTotalSell,
                                   List<BigDecimal> softwareServiceTotalSell,
                                   List<BigDecimal> oneCyberTotalSell,
                                   List<BigDecimal> totalSell) {
        String beId = totalSellDO.getBeId();
        String provinceName = (String) stringRedisTemplate.opsForHash().get(REDIS_PROVINCE_KEY, beId);
        totalSellVO.setProvinceName(provinceName);
        String spuOfferingClass = totalSellDO.getSpuOfferingClass();
        BigDecimal offeringClassSell = totalSellDO.getOfferingClassSell();
        setSell(spuOfferingClass, offeringClassSell, totalSellVO,
                productAddedValueTotalSell, contractComplianceTotalSell,
                onenetServiceTotalSell, unionTotalSell,oneParkTotalSell,carTVStandardProductsTotalSell,softwareServiceTotalSell,oneCyberTotalSell, totalSell);
        return beId;
    }

    /**
     * 组装销售数据
     *
     * @param spuOfferingClass
     * @param offeringClassSell
     * @param totalSellVO
     * @param productAddedValueTotalSell
     * @param contractComplianceTotalSell
     * @param onenetServiceTotalSell
     * @param unionTotalSell
     * @param totalSell
     * @param carTVStandardProductsTotalSell
     */
    private void setSell(String spuOfferingClass,
                         BigDecimal offeringClassSell,
                         ProvinceOfferingClassTotalSellVO totalSellVO,
                         List<BigDecimal> productAddedValueTotalSell,
                         List<BigDecimal> contractComplianceTotalSell,
                         List<BigDecimal> onenetServiceTotalSell,
                         List<BigDecimal> unionTotalSell,
                         List<BigDecimal> oneParkTotalSell,
                         List<BigDecimal> carTVStandardProductsTotalSell,
                         List<BigDecimal> softwareServiceTotalSell,
                         List<BigDecimal> oneCyberTotalSell,
                         List<BigDecimal> totalSell) {
        BigDecimal zero = new BigDecimal(0);
        if (A04.getSpuOfferingClass().equals(spuOfferingClass)) {
            // DICT产品增值
            BigDecimal productAddedValueSell = totalSellVO.getProductAddedValueSell()==null? zero :totalSellVO.getProductAddedValueSell();
            totalSellVO.setProductAddedValueSell(productAddedValueSell.add(offeringClassSell));
            productAddedValueTotalSell.set(0, productAddedValueTotalSell.get(0).add(offeringClassSell));
        } else if (A06.getSpuOfferingClass().equals(spuOfferingClass)) {
            // 联合销售
            BigDecimal unionSell = totalSellVO.getUnionSell()==null?zero:totalSellVO.getUnionSell();
            totalSellVO.setUnionSell(unionSell.add(offeringClassSell));
            unionTotalSell.set(0, unionTotalSell.get(0).add(offeringClassSell));
        } else if (A07.getSpuOfferingClass().equals(spuOfferingClass)) {
            // 合同履约
            BigDecimal contractComplianceSell = totalSellVO.getContractComplianceSell()==null?zero:totalSellVO.getContractComplianceSell();
            totalSellVO.setContractComplianceSell(contractComplianceSell.add(offeringClassSell));
            contractComplianceTotalSell.set(0, contractComplianceTotalSell.get(0).add(offeringClassSell));
        } else if (A08.getSpuOfferingClass().equals(spuOfferingClass)) {
            // OneNET独立
            BigDecimal onenetServiceSell = totalSellVO.getOnenetServiceSell()==null?zero:totalSellVO.getOnenetServiceSell();
            totalSellVO.setOnenetServiceSell(onenetServiceSell.add(offeringClassSell));
            onenetServiceTotalSell.set(0, onenetServiceTotalSell.get(0).add(offeringClassSell));
        } else if (A09.getSpuOfferingClass().equals(spuOfferingClass)) {
            // OnePark独立
            BigDecimal oneParkServiceSell = totalSellVO.getOneParkServiceSell()==null?zero:totalSellVO.getOneParkServiceSell();
            totalSellVO.setOneParkServiceSell(oneParkServiceSell.add(offeringClassSell));
            oneParkTotalSell.set(0, oneParkTotalSell.get(0).add(offeringClassSell));
        } else if (A12.getSpuOfferingClass().equals(spuOfferingClass)) {
            // 标准产品（行车卫士）
            BigDecimal carTVStandardProductsSell  = totalSellVO.getCarTVStandardProductsSell()==null?zero:totalSellVO.getCarTVStandardProductsSell();
            totalSellVO.setCarTVStandardProductsSell(carTVStandardProductsSell.add(offeringClassSell));
            carTVStandardProductsTotalSell.set(0, carTVStandardProductsTotalSell.get(0).add(offeringClassSell));
        }else if (A13.getSpuOfferingClass().equals(spuOfferingClass)) {
            // 软件服务
            BigDecimal softwareServiceSell  = totalSellVO.getSoftwareService()==null?zero:totalSellVO.getSoftwareService();
            totalSellVO.setSoftwareService(softwareServiceSell.add(offeringClassSell));
            softwareServiceTotalSell.set(0, softwareServiceTotalSell.get(0).add(offeringClassSell));
        }else if (A14.getSpuOfferingClass().equals(spuOfferingClass)) {
            // OneCyber标准产品
            BigDecimal oneCyberSell  = totalSellVO.getOneCyberSell()==null?zero:totalSellVO.getOneCyberSell();
            totalSellVO.setOneCyberSell(oneCyberSell.add(offeringClassSell));
            oneCyberTotalSell.set(0, oneCyberTotalSell.get(0).add(offeringClassSell));
        }else{

        }
        totalSell.set(0, totalSell.get(0).add(offeringClassSell));
    }

    private void validDate(String startTime, String endTime) {
        try {
            if (StringUtils.isNotEmpty(startTime)) {
                DateUtils.strToDate(startTime, DateUtils.DATE_FORMAT_NO_SYMBOL);
            }
            if (StringUtils.isNotEmpty(endTime)) {
                DateUtils.strToDate(endTime, DateUtils.DATE_FORMAT_NO_SYMBOL);
            }
        } catch (ParseException e) {
            log.error("日期格式解析错误{}", e);
            throw new BusinessException(DATE_PARSE_ERROR);
        }
    }

    /**
     * 初始化破零数据
     *
     * @param spuCodeUseVO
     * @param spuCodeUseDO
     * @param productAddedValueTotalUse
     * @param contractComplianceTotalUse
     * @param onenetServiceTotalUse
     * @param unionTotalUse
     * @param totalUse
     * @return
     */
    private String initSpuUseVO(ProvinceSpuCodeUseVO spuCodeUseVO,
                                ProvinceSpuCodeUseDO spuCodeUseDO,
                                List<Integer> productAddedValueTotalUse,
                                List<Integer> contractComplianceTotalUse,
                                List<Integer> onenetServiceTotalUse,
                                List<Integer> unionTotalUse,
                                List<Integer> oneParkServiceTotalUse,
                                List<Integer> carTVStandardProductsTotalUse,
                                List<Integer> softwareServiceTotalUse,
                                List<Integer> oneCyberseTotalUse,
                                List<Integer> totalUse) {
        String beId = spuCodeUseDO.getBeId();
        String provinceName = (String) stringRedisTemplate.opsForHash().get(REDIS_PROVINCE_KEY, beId);
        spuCodeUseVO.setProvinceName(provinceName);
        String spuOfferingClass = spuCodeUseDO.getSpuOfferingClass();
        Integer spuOfferingCode = spuCodeUseDO.getSpuOfferingCode();
        setSpuUse(spuOfferingClass, spuOfferingCode, spuCodeUseVO,
                productAddedValueTotalUse, contractComplianceTotalUse,
                onenetServiceTotalUse, unionTotalUse,oneParkServiceTotalUse,carTVStandardProductsTotalUse, softwareServiceTotalUse,oneCyberseTotalUse,totalUse);
        return beId;
    }

    /**
     * 范式破零数据组装
     *
     * @param spuOfferingClass
     * @param spuUse
     * @param spuCodeUseVO
     * @param productAddedValueTotalUse
     * @param contractComplianceTotalUse
     * @param onenetServiceTotalUse
     * @param unionTotalUse
     * @param totalUse
     */
    private void setSpuUse(String spuOfferingClass,
                           Integer spuUse,
                           ProvinceSpuCodeUseVO spuCodeUseVO,
                           List<Integer> productAddedValueTotalUse,
                           List<Integer> contractComplianceTotalUse,
                           List<Integer> onenetServiceTotalUse,
                           List<Integer> unionTotalUse,
                           List<Integer> oneParkServiceTotalUse,
                           List<Integer> carTVStandardProductsTotalUse,
                           List<Integer> softwareServiceTotalUse,
                           List<Integer> oneCyberTotalUse,
                           List<Integer> totalUse) {
        if (A04.getSpuOfferingClass().equals(spuOfferingClass)) {
            // DICT产品增值
            Integer productAddedValueUse = spuCodeUseVO.getProductAddedValueUse()==null?0:spuCodeUseVO.getProductAddedValueUse();
            spuCodeUseVO.setProductAddedValueUse(productAddedValueUse+spuUse);
            productAddedValueTotalUse.set(0, productAddedValueTotalUse.get(0) + spuUse);
        } else if (A06.getSpuOfferingClass().equals(spuOfferingClass)) {
            // 联合销售
            Integer unionUse = spuCodeUseVO.getUnionUse()==null?0:spuCodeUseVO.getUnionUse();
            spuCodeUseVO.setUnionUse(unionUse+spuUse);
            unionTotalUse.set(0, unionTotalUse.get(0) + spuUse);
        } else if (A07.getSpuOfferingClass().equals(spuOfferingClass)) {
            // 合同履约
            Integer contractComplianceUse = spuCodeUseVO.getContractComplianceUse()==null?0:spuCodeUseVO.getContractComplianceUse();
            spuCodeUseVO.setContractComplianceUse(contractComplianceUse+spuUse);
            contractComplianceTotalUse.set(0, contractComplianceTotalUse.get(0) + spuUse);
        } else if (A08.getSpuOfferingClass().equals(spuOfferingClass)) {
            // OneNET独立
            Integer onenetServiceUse = spuCodeUseVO.getOnenetServiceUse()==null?0:spuCodeUseVO.getOnenetServiceUse();
            spuCodeUseVO.setOnenetServiceUse(onenetServiceUse+spuUse);
            onenetServiceTotalUse.set(0, onenetServiceTotalUse.get(0) + spuUse);
        } else if (A09.getSpuOfferingClass().equals(spuOfferingClass)) {
            // OnePark独立
            Integer oneParkServiceUse = spuCodeUseVO.getOneParkServiceUse()==null?0:spuCodeUseVO.getOneParkServiceUse();
            spuCodeUseVO.setOneParkServiceUse(oneParkServiceUse+spuUse);
            oneParkServiceTotalUse.set(0, oneParkServiceTotalUse.get(0) + spuUse);
        } if (A12.getSpuOfferingClass().equals(spuOfferingClass)) {
            // 行车卫士标准产品
            Integer carTVStandardProductsUse = spuCodeUseVO.getCarTVStandardProductsUse()==null?0:spuCodeUseVO.getCarTVStandardProductsUse();
            spuCodeUseVO.setCarTVStandardProductsUse(carTVStandardProductsUse+spuUse);
            carTVStandardProductsTotalUse.set(0, carTVStandardProductsTotalUse.get(0) + spuUse);
        }
        if (A13.getSpuOfferingClass().equals(spuOfferingClass)) {
            // 软件服务
            Integer softwareService = spuCodeUseVO.getSoftwareService()==null?0:spuCodeUseVO.getSoftwareService();
            spuCodeUseVO.setSoftwareService(softwareService+spuUse);
            softwareServiceTotalUse.set(0, softwareServiceTotalUse.get(0) + spuUse);
        }
        if (A14.getSpuOfferingClass().equals(spuOfferingClass)) {
            // OneCyber标准产品
            Integer oneCyberseUse = spuCodeUseVO.getOneCyberUse()==null?0:spuCodeUseVO.getOneCyberUse();
            spuCodeUseVO.setOneCyberUse(oneCyberseUse+spuUse);
            oneCyberTotalUse.set(0,oneCyberTotalUse.get(0) + spuUse);
        }
        totalUse.set(0, totalUse.get(0) + spuUse);
    }
}
