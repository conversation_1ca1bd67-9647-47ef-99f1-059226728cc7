package com.chinamobile.iot.sc;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.dao.ext.BRMMapperExt;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.dto.BRMUploadDTO;
import com.chinamobile.iot.sc.pojo.param.IOPUploadParam;
import com.chinamobile.iot.sc.request.Order2CInfoRequest;
import com.chinamobile.iot.sc.request.order2c.*;
import com.chinamobile.iot.sc.service.BRMService;
import com.chinamobile.iot.sc.service.CardInfoService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.SFTPUtil;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import com.chinamobile.iot.sc.util.Sm4Util;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: YSC
 * @Date: 2021/11/10 19:19
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {IOTApplication.class})
@Slf4j
public class OrderTest {

    @Resource
    private BRMMapperExt brmMapperExt;

    @Resource
    private BRMService brmService;

    @Resource
    private CardInfoService cardInfoService;


    @Value("${brm.sms.templateId:108279}")
    private String iopMessageId;
    @Value("${brm.ftp.workPath}")
    private String sftpWorkPath;
    @Value("${softService.sm4Key}")
    private String sm4Key;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;

    private SimpleDateFormat orderStatusSDF = new SimpleDateFormat("yyyyMMddHHmmss");
    @Test
    public void getOrder() {
        Order2CInfoRequest content = new Order2CInfoRequest();
        OrderInfoDTO orderInfoDTO = new OrderInfoDTO();
        orderInfoDTO.setBusinessCode("SyncIndividualOrderInfo");
        orderInfoDTO.setCreateOperCode("ysc operator");
        CustInfoDTO custInfoDTO = new CustInfoDTO();
        custInfoDTO.setCustCode("客户编码");
        custInfoDTO.setCustName("客户名称");
        //物联网公司
        custInfoDTO.setBeId("002");
        //重庆
        custInfoDTO.setLocation("2300");
        //巴南区
        custInfoDTO.setRegionID("10001721");
        orderInfoDTO.setCustInfo(custInfoDTO);
        orderInfoDTO.setRemarks("备注信息");
        orderInfoDTO.setOrderId("order Id:123123");
        orderInfoDTO.setBookId("1");
        //todo 这里的订单状态需要商量下
        orderInfoDTO.setStatus(0);
        orderInfoDTO.setTotalPrice("10000");
        orderInfoDTO.setCreateTime("20211110200010");
        ContactInfoDTO contactInfoDTO = new ContactInfoDTO();
        contactInfoDTO.setContactPhone("***********");
        contactInfoDTO.setContactPersonName("收货人杨思陈");
        AddressInfoDTO addressInfoDTO = new AddressInfoDTO();
        addressInfoDTO.setAddr1("addr1");
        addressInfoDTO.setAddr2("addr2");
        addressInfoDTO.setAddr3("addr3");
        addressInfoDTO.setAddr4("addr4");
        addressInfoDTO.setUsaddr("非结构化数据");
        contactInfoDTO.setAddresstInfo(addressInfoDTO);
        SpuOfferingInfoDTO spuOfferingInfoDTO = new SpuOfferingInfoDTO();
        spuOfferingInfoDTO.setOfferingClass("A01");
        spuOfferingInfoDTO.setOfferingCode("1000009002");
        spuOfferingInfoDTO.setSupplierCode("华为");
        spuOfferingInfoDTO.setActionType("A");
        SkuOfferingInfoDTO skuOfferingInfoDTO = new SkuOfferingInfoDTO();
        skuOfferingInfoDTO.setOfferingCode("1000009003");
        skuOfferingInfoDTO.setQuantity(10L);
        skuOfferingInfoDTO.setActionType("A");
        skuOfferingInfoDTO.setPrice(1000L);

        AtomOfferingInfoDTO atomOfferingInfoDTO = new AtomOfferingInfoDTO();
        atomOfferingInfoDTO.setOfferingCode("1000009004");
        atomOfferingInfoDTO.setPrice(100L);
        atomOfferingInfoDTO.setQuantity(10L);
        atomOfferingInfoDTO.setActionType("A");
        List<AtomOfferingInfoDTO> atomList = new ArrayList<>();
        atomList.add(atomOfferingInfoDTO);
        skuOfferingInfoDTO.setAtomOfferingInfo(atomList);
        List<SkuOfferingInfoDTO> skuList = new ArrayList<>();
        skuList.add(skuOfferingInfoDTO);
        spuOfferingInfoDTO.setSkuOfferingInfo(skuList);
        orderInfoDTO.setSpuOfferingInfo(spuOfferingInfoDTO);
        orderInfoDTO.setContactInfo(contactInfoDTO);
        content.setOrderInfo(orderInfoDTO);


        IOTRequest iotRequest = new IOTRequest();
        iotRequest.setMessageSeq("0001000009591");
        iotRequest.setRouteType("1");
        iotRequest.setRouteValue("000");
        iotRequest.setLoginSystemCode("");
        iotRequest.setPassword("");
        iotRequest.setRemoteIP("");
        iotRequest.setOperatorId("1617871748813121112");
        iotRequest.setBeId("000");
        iotRequest.setSign("1fa8cda8b94eb4cc87199082b706f6a3");
        iotRequest.setChannelId("30");
        iotRequest.setContent(JSON.toJSONString(content));
        System.out.println(JSON.toJSONString(iotRequest));
    }

    @Test
    public void encodeTest() {
        String str = "13648047960";
        System.out.println("加密前：" + str);
        try {
            System.out.println("加密后：" + Sm4Util.encrypt(str, "7F480B3D813CEE2478F99E7AA65159F7"));
        } catch (Exception e) {
            e.printStackTrace();
        }

        String str1 = "9eCH/D2EOY4hW1ZJbQilpw==";
        System.out.println("解密前：" + str1);
        try {
            System.out.println("解密后：" + IOTEncodeUtils.decryptSM4(str1, "iotmalltoos24122", "iotmalltoos24122"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    @Test
    public void timeTest() throws ParseException {
        String str = "20250121163841";
        String.valueOf(orderStatusSDF.parse(str).getTime());
        System.out.println("加密前：" +String.valueOf(orderStatusSDF.parse(str)));
        System.out.println("加密前1：" + DateTimeUtil.formatDate(orderStatusSDF.parse(str), DateTimeUtil.STANDARD_DAY));

    }
    @Test
    public void brmTest() throws ParseException {
        IOPUploadParam iopUploadParam = new IOPUploadParam();
        // type为null时为非首次同步，定时任务都是非首次，手动调用接口传首次数据
        Date now = new Date();
        Date todayBegin = DateTimeUtil.getDayBeginDate(now);
        Date todayEnd = DateTimeUtil.getDayEndDate(now);
        Date yesterdayBegin = DateTimeUtil.addDay(todayBegin, -1);
        Date yesterdayEnd = DateTimeUtil.addDay(todayEnd, -1);
        iopUploadParam.setType("a");
        iopUploadParam.setRetry("00");
        iopUploadParam.setStartTime(yesterdayBegin);
        iopUploadParam.setEndTime(yesterdayEnd);
        brmService.sftpUploadBRM(iopUploadParam);



    }

    @Test
    public void setStatusCanSell(){
        cardInfoService.setStatusCanSell("1281312015510687744");
    }

    @Test
    public void testEnDecrypt(){
        String decryptStr = IOTEncodeUtils.decryptSM4("UrckUP7PTt6s7EOHxkwPEKiCXywkfdFEY/Xlx3qWxL4=", iotSm4Key, iotSm4Iv);
        System.out.println(decryptStr);

        String encryptStr = IOTEncodeUtils.encryptSM4(decryptStr, iotSm4Key, iotSm4Iv);
        System.out.println(encryptStr);
    }

}
