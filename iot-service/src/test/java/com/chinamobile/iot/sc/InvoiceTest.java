package com.chinamobile.iot.sc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.chinamobile.iot.sc.IOTApplication;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.config.CommonConstant;
import com.chinamobile.iot.sc.config.FTPConfig;
import com.chinamobile.iot.sc.constant.InvoiceConstant;
import com.chinamobile.iot.sc.constant.NoBookOrderConstant;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.entity.ByteArrayUpload;
import com.chinamobile.iot.sc.entity.UpResult;
import com.chinamobile.iot.sc.exception.IOTException;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.Order2cAtomInfo;
import com.chinamobile.iot.sc.pojo.Order2cAtomInfoExample;
import com.chinamobile.iot.sc.pojo.Order2cInfo;
import com.chinamobile.iot.sc.pojo.Order2cInfoExample;
import com.chinamobile.iot.sc.pojo.invoice.ApplyInvoiceInfo;
import com.chinamobile.iot.sc.pojo.invoice.ApplyInvoiceRec;
import com.chinamobile.iot.sc.pojo.invoice.InvoiceReverseInfo;
import com.chinamobile.iot.sc.pojo.invoice.InvoiceReverseRec;
import com.chinamobile.iot.sc.request.invoice.InvoiceApplyDTO;
import com.chinamobile.iot.sc.request.invoice.TaxpayerInfoDTO;
import com.chinamobile.iot.sc.response.web.Order2CInfoDetailDTO;
import com.chinamobile.iot.sc.service.IOrder2CService;
import com.chinamobile.iot.sc.service.IStorageService;
import com.chinamobile.iot.sc.util.FileUtils;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import com.chinamobile.iot.sc.util.SFTPUtil;
import com.chinamobile.iot.sc.util.ShareCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.*;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * @package: com.chinamobile.sc
 * @ClassName: InvoiceTest
 * @description: 发票管理单元测试类
 * @author: zyj
 * @create: 2021/11/30 19:58
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {IOTApplication.class})
public class InvoiceTest {
    @Value("${iot.encodeKey}")
    private String encodeKey;
    @Resource
    private FTPConfig ftpConfig;
    @Resource
    private ApplyInvoiceRecMapper invoiceRecMapper;
    @Resource
    private ApplyInvoiceInfoMapper invoiceInfoMapper;
    @Resource
    private InvoiceReverseRecMapper reverseRecMapper;
    @Resource
    private InvoiceReverseInfoMapper reverseInfoMapper;
    @Resource
    private IStorageService storageService;
    @Resource
    private Order2cInfoMapper order2cInfoMapper;
    @Resource
    private Order2cAtomInfoMapper atomOrderInfoMapper;
    @Resource
    private UserFeignClient userFeignClient;
    @Resource
    private IOrder2CService order2CService;

    @Value("${iot.ftp.name}")
    private String sftpUserName;
    @Value("${iot.ftp.password}")
    private String sftpPassword;
    @Value("${iot.ftp.host}")
    private String sftpHost;
    @Value("${iot.ftp.port}")
    private Integer sftpPort;
    @Value("${iot.ftp.refundPath}")
    private String sftpRefundPath;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Test
//    @Transactional(rollbackFor = Throwable.class)
    public void testInvoiceRecMapper(){
        /*List<ApplyInvoiceRec> recsPage = invoiceRecMapper.findPage(1,0, 10);
        System.out.println(JSONObject.toJSONString(recsPage));*/
        String cust_code = "MTA3MkFEMjVBOEEzNDU2MDczOUIyMkQ4OTAyMTIxRjUyNTRCNjcyQzUxNDk2Rjk1";
        for(int i=0; i<3; i++){
            String orderId = BaseServiceUtils.getId();
            String atomOrderId = BaseServiceUtils.getId();
            ApplyInvoiceRec applyInvoiceRec = new ApplyInvoiceRec().setId(BaseServiceUtils.getId())
                    .setOrderSeq(i+"").setOrderId(orderId)
                    .setCustCode(cust_code).setBeId(i + "")
                    .setAtomOrderId(atomOrderId).setBeId("00"+i)
                    .setCreateTime(new Date()).setUpdateTime(new Date())
                    .setPrintDate("********").setFrank(InvoiceConstant.FRANK_NORMAL)
                    .setPName("纳税人名称"+i).setIdentifyNum("纳税人识别号"+i)
                    .setAddressInfo("纳税人地址信息"+i).setPhoneNumber("纳税人电话"+i)
                    .setBankName("纳税人开户行").setBankId("纳税人开户行账号").setOrderPrice(Long.valueOf(i*3+""))
                    .setStatus(InvoiceConstant.STATUS_INVOICE_APPLY)
                    .setCooperatorId("912739745732288512")
                    .setRemark("测试数据").setCreateTime(new Date())
                    .setUpdateTime(new Date());
            invoiceRecMapper.insert(applyInvoiceRec);
            for(int j=0; j<3; j++){
                String voucherFile = String.format("%s_Voucher_2_%s_%s_%s.pdf", "00" + i, cust_code, i + "", ShareCodeUtil.getRandomNum(6));
                ApplyInvoiceInfo applyInvoiceInfo = new ApplyInvoiceInfo().setId(BaseServiceUtils.getId())
                        .setOrderId(orderId).setVoucherId("000000")
                        .setAtomOrderId(atomOrderId).setVoucherType(0).setCustCode(cust_code)
                        .setOrderSeq(i+"").setVoucherFile(voucherFile).setBeId(applyInvoiceRec.getBeId())
                        .setVoucherNum(i + "" + j).setVoucherSum(Long.valueOf(i + ""))
                        .setBillingDate("********")
                        .setVoucherInnerUrl("http://www.innerVoucher" + i + j + ".com")
                        .setVoucherOuterUrl("http://www.outerVoucher" + i + j + ".com")
                        .setSort(j + 1);
                invoiceInfoMapper.insert(applyInvoiceInfo);
            }
        }

    }

    @Test
    public void ftpTest() throws FileNotFoundException {
        /*String filePath = "C:\\Users\\<USER>\\Desktop\\logback-spring.xml";
        File file = new File(filePath);
        System.out.println(file.exists());
        System.out.println(file.getName());
        FileInputStream fileInputStream = new FileInputStream(file);
        System.out.println(ftpConfig.getHost());
        System.out.println(ftpConfig.getPort() + "    " + ftpConfig.getPort().getClass());
        System.out.println(ftpConfig.getPassword());
        System.out.println(ftpConfig.getWorkPath());
        System.out.println(ftpConfig.getName());*/

        String custCode = IOTEncodeUtils.decryptIOTMessage("MTA3MkFEMjVBOEEzNDU2MDczOUIyMkQ4OTAyMTIxRjUyNTRCNjcyQzUxNDk2Rjk1", encodeKey);
        System.out.println(custCode);


        /*FTPUtil.uploadFile("*************", 21, "app", "NSWpshLq8M!Ct"
                , "/home/<USER>", "", file.getName(), fileInputStream);*/
    }

    @Test
    public void objectStorage() throws Exception {
//        File file = new File("C:\\Users\\<USER>\\Downloads\\智慧加油站架构&&流程图.pdf");
        File file = new File("F:\\yidongbangong\\测试用0607.pdf");
        BaseAnswer<UpResult> answer = storageService.uploadFile(file, "test0607/测试用06071.pdf", true, -1);
        System.out.println(JSONObject.toJSONString(answer.getData()));

    }


    @Test
    public void byteStorage() throws Exception{

        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        if (sftpUtil.login()) {
            log.info("sftp连接成功！");
            sftpUtil.listFiles("/copftp/ftpcmiot/iotMall/os/refund");
        }

//        File file = new File("F:\\yidongbangong\\测试用0607.pdf");
        byte[] buffer = null;
        try {
            File file = new File("F:\\yidongbangong\\测试用0607.pdf");
            FileInputStream fis = new FileInputStream(file);
            ByteArrayOutputStream bos = new ByteArrayOutputStream(1000);
            byte[] b = new byte[1000];
            int n;
            while ((n = fis.read(b)) != -1) {
                bos.write(b, 0, n);
            }
            fis.close();
            bos.close();
            buffer = bos.toByteArray();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
        byteArrayUpload.setBytes(buffer);
        byteArrayUpload.setCover(true);
        byteArrayUpload.setFileName("testByte1111");
        BaseAnswer<UpResult> upResultBaseAnswer = storageService.uploadByte(byteArrayUpload);
        System.out.println(JSONObject.toJSONString(upResultBaseAnswer.getData()));
    }


    @Test
    public void InvoiceRequest2OS(){
        String baseRequestJSON = "{\"beId\":\"100\",\"channelId\":\"30\",\"content\":\"{\\\"orderSeq\\\":\\\"****************\\\",\\\"orderId\\\":\\\"610100000000221002\\\",\\\"printDate\\\":\\\"**************\\\",\\\"frank\\\":\\\"1\\\",\\\"taxpayerInfo\\\":{\\\"pName\\\":\\\"自主开票测试Os\\\",\\\"identifyNum\\\":\\\"FGH123456789000\\\",\\\"addressInfo\\\":\\\"狸猫\\\",\\\"phoneNumber\\\":\\\"***********\\\",\\\"bankName\\\":\\\"狸猫\\\",\\\"bankID\\\":\\\"62178548854785478577\\\"}}\",\"loginSystemCode\":\"\",\"messageSeq\":\"1100100000000221004\",\"operatorId\":\"1634092862690611061\",\"password\":\"\",\"remoteIP\":\"\",\"routeType\":\"1\",\"routeValue\":\"100\",\"sign\":\"0a74dccd3e7b789d7b801c7c61b09e16\"}";
        IOTRequest iotRequest = (IOTRequest) JSON.parseObject(baseRequestJSON, IOTRequest.class);
        InvoiceApplyDTO invoiceApply = new InvoiceApplyDTO();
        IOTAnswer<Void> iotAnswer = new IOTAnswer<>();
        //获取beid
        String beId = iotRequest.getBeId();
        try {
            invoiceApply = JSON.parseObject(iotRequest.getContent(), InvoiceApplyDTO.class);
        } catch (Exception e) {
            log.error("IOT商城同步开票申请至OS系统-InvoiceApplyRequest，JSON解析失败",e.toString());
            throw new IOTException(iotAnswer, "数据解析异常");
        }
        if(ObjectUtils.isNotEmpty(invoiceApply) && ObjectUtils.isNotEmpty(invoiceApply.getOrderId())){
            //校验发票业务唯一标识
            if(ObjectUtils.isNotEmpty(invoiceApply) && ObjectUtils.isNotEmpty(invoiceApply.getOrderSeq())){
                List<ApplyInvoiceRec> applyInvoiceRecs = invoiceRecMapper.selectList(new QueryWrapper<ApplyInvoiceRec>().lambda()
                        .eq(ApplyInvoiceRec::getOrderSeq, invoiceApply.getOrderSeq()));
                if(ObjectUtils.isNotEmpty(applyInvoiceRecs)){
                    throw new IOTException(iotAnswer, "发票业务唯一标识重复！");
                }
            }
            // 获取业务订单信息-获取客户编码
            Order2cInfoExample order2cInfoExample = new Order2cInfoExample();
            Order2cInfoExample.Criteria infoExampleCriteria = order2cInfoExample.createCriteria();
            infoExampleCriteria.andOrderIdEqualTo(invoiceApply.getOrderId());
            List<Order2cInfo> order2cInfos = order2cInfoMapper.selectByExample(order2cInfoExample);
            Order2cInfo order2cInfo = order2cInfos.get(0);
            //获取客户编码(加密)
            String custCode = order2cInfo.getCustCode();
            // 获取原子商品订单信息(目前只有硬件订单)
            Order2cAtomInfoExample atomInfoExample = new Order2cAtomInfoExample();
            Order2cAtomInfoExample.Criteria criteria = atomInfoExample.createCriteria();
            criteria.andOrderIdEqualTo(invoiceApply.getOrderId());
            //只查询硬件订单
            criteria.andAtomOfferingClassEqualTo("H");
            List<Order2cAtomInfo> order2cAtomInfos = atomOrderInfoMapper.selectByExample(atomInfoExample);
            if(ObjectUtils.isNotEmpty(order2cAtomInfos)){
                for(Order2cAtomInfo order2cAtomInfo : order2cAtomInfos){
                    //获取原子订单总价，单位：厘
                    Long totalPrice = null;
                    //获取合作伙伴手机号
                    String partnerPhone = null;
                    Long atomPrice = order2cAtomInfo.getAtomPrice() != null ? order2cAtomInfo.getAtomPrice() : 0L;
                    Long atomQuantity = order2cAtomInfo.getAtomQuantity() != null ? order2cAtomInfo.getAtomQuantity() : 0L;
                    totalPrice = atomQuantity * atomPrice;
                    //获取 合作伙伴id
                    String cooperatorId = order2cAtomInfo.getCooperatorId();
                    BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfo(cooperatorId);
                    log.info("查询合作伙伴id：{}，用户信息为：{}", cooperatorId, JSONObject.toJSONString(data4UserBaseAnswer));
                    partnerPhone = data4UserBaseAnswer.getData().getPhone();
                    // 根据合作伙伴-原子商品订单，初始化开票申请记录（按照不同合作伙伴-cooperator_id）
                    TaxpayerInfoDTO taxpayerInfo = invoiceApply.getTaxpayerInfo();
                    ApplyInvoiceRec applyInvoiceRec = new ApplyInvoiceRec().setId(BaseServiceUtils.getId())
                            .setOrderId(invoiceApply.getOrderId()).setOrderSeq(invoiceApply.getOrderSeq())
                            .setAtomOrderId(order2cAtomInfo.getId()).setBeId(beId).setCustCode(custCode)
                            .setPrintDate(invoiceApply.getPrintDate()).setFrank(invoiceApply.getFrank()).setPName(taxpayerInfo.getPName())
                            .setIdentifyNum(taxpayerInfo.getIdentifyNum()).setAddressInfo(taxpayerInfo.getAddressInfo())
                            .setPhoneNumber(taxpayerInfo.getPhoneNumber()).setBankName(taxpayerInfo.getBankName())
                            .setBankId(taxpayerInfo.getBankID()).setOrderPrice(totalPrice).setStatus(InvoiceConstant.STATUS_INVOICE_APPLY_ENTRY)
                            .setCooperatorId(cooperatorId)
                            .setCreateTime(new Date()).setUpdateTime(new Date());
                    //保存入库
                    invoiceRecMapper.insert(applyInvoiceRec);
                    //TODO 发送提示短信 - 原子商品订单信息（发送订单id or 原子订单id？） 应该是原子订单id，原子商品订单信息
//                    sendSmsInvoiceApply(partnerPhone, order2cAtomInfo.getId());
                }
            }
        }
    }

    @Test
    public void getOrderDetail(){
        BaseAnswer<Order2CInfoDetailDTO> orderDetail = order2CService.getOrderDetailInternal("918160832150749184");
        System.out.println(JSON.toJSONString(orderDetail.getData()));
    }

    @Test
    public void testRevInvoice(){
        String recId = BaseServiceUtils.getId();
        String orderId = BaseServiceUtils.getId();
        String atomOrderId = BaseServiceUtils.getId();
        String orderSeq = BaseServiceUtils.getId();
        Long orderPrice = 1L;
        String coOperatorId = "911200191170740224";
        String custCode = "MTA3MkFEMjVBOEEzNDU2MDczOUIyMkQ4OTAyMTIxRjUyNTRCNjcyQzUxNDk2Rjk1";
        InvoiceReverseRec invoiceReverseRec = new InvoiceReverseRec();
        invoiceReverseRec.setId(recId).setOrderId(orderId).setAtomOrderId(atomOrderId).setOrderSeq(orderSeq).setOperType("01")
                .setCustomerType("0").setCustomerNumber(custCode).setOrderPrice(orderPrice).setStatus(4)
                .setCooperatorId(coOperatorId).setCreateTime(new Date()).setUpdateTime(new Date());
        reverseRecMapper.insert(invoiceReverseRec);

        String infoId = BaseServiceUtils.getId();
        InvoiceReverseInfo invoiceReverseInfo = new InvoiceReverseInfo();
        invoiceReverseInfo.setId(infoId).setOrderSeq(orderSeq).setOrderId(orderId).setAtomOrderId(atomOrderId).setFrank("1")
                .setVoucherType("2").setBillingDate("20220104").setCreditNoteId("123456789012")
                .setCreditNoteNum("12345678").setCreditNoteSum(1L).setVoucherSum(1L).setDes("冲红").setBeId("471")
                .setHomeCity("1234").setCooperatorId(coOperatorId).setCreateTime(new Date()).setUpdateTime(new Date());
        reverseInfoMapper.insert(invoiceReverseInfo);
    }

    @Test
    public void testNobook(){
        //String content = "{\"bookId\":\"240100000000405004\",\"offeringCode\":\"8000000331\",\"quantity\":\"1\"}";
        String content = "{\"bookId\":\"280100000003070029\",\"spuOfferingInfo\":[{\"offeringClass\":\"A08\",\"skuOfferingInfo\":[{\"atomOfferingInfo\":[{\"offeringCode\":\"1000077414\",\"quantity\":2}],\"offeringCode\":\"1000080108\",\"quantity\":1}],\"spuOfferingCode\":\"1000080107\"}]}";
        JSONObject jsonObject = JSON.parseObject(content);
        System.out.println(JSON.toJSONString(ObjectUtils.isEmpty(jsonObject.getJSONArray("spuOfferingInfo"))));
    }

    @Test
    public void testUpload() throws Exception {
        String fileName = "C:\\Users\\<USER>\\Desktop\\产品信息割接版本.txt";
        File file = new File(fileName);
        FileInputStream fileInputStream = new FileInputStream(file);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        int len;
        byte[] bytes = new byte[1024];
        while ((len = fileInputStream.read(bytes)) != -1){
            byteArrayOutputStream.write(bytes,0,len);
            byteArrayOutputStream.flush();
        }


        ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
        byteArrayUpload.setBytes(byteArrayOutputStream.toByteArray());
        byteArrayUpload.setCover(true);
        byteArrayUpload.setFileName(CommonConstant.PRODUCT_FLOW_ATTACHMENT_PATH+"1111"+"/"+"123456.txt");
        BaseAnswer<UpResult> upResultBaseAnswer = storageService.uploadByte(byteArrayUpload);
        System.out.println(JSON.toJSONString(upResultBaseAnswer));

    }

   /* @Test
    public void clearRedis(){
        stringRedisTemplate.delete(DEFAULT_ATTACHMENT_KEY);
        System.out.println("ok");
    }*/

}
