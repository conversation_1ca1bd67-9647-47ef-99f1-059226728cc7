package com.chinamobile.iot.sc.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.services.dynamodbv2.xspec.S;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.request.express.QueryTrackReq;
import com.chinamobile.iot.sc.request.sms.Msg4Request;
import com.chinamobile.iot.sc.response.iot.IOTSmsResponse;
import com.chinamobile.iot.sc.response.iot.express.QueryTrackParam;

import com.chinamobile.iot.sc.response.web.logistics.LogisticsVO;
import com.chinamobile.iot.sc.util.express.SignUtils;
import com.google.gson.Gson;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;

import static com.chinamobile.iot.sc.constant.RedisLockConstant.AFTER_ORDER_DISPATCH_INSTALL_REDIS_KEY;

/**
 * <AUTHOR> xiemaohua
 * @date : 2022/4/14 9:54
 * @description: 快递100查询测试
 **/
@SpringBootTest()
//@RunWith(SpringRunner.class)
public class Express100Test {

    @Resource
    private IOT100ExpressService iot100ExpressService;

    @Resource
    private IOTSmsService iotSmsService;

    @Autowired
    private SmsFeignClient smsFeignClient;

    @Resource
    private IOrder2CService order2CService;

    @Resource
    private RedisTemplate redisTemplate;


    @Test
    public void testQueryTrack(){
        QueryTrackReq queryTrackReq = new QueryTrackReq();
        QueryTrackParam queryTrackParam = new QueryTrackParam();
        queryTrackParam.setCom("shunfeng");
        queryTrackParam.setNum("SF1981734023485");
        //queryTrackParam.setFrom("广东省深圳市南山区");
        //queryTrackParam.setTo("北京市朝阳区");
        //queryTrackParam.setResultv2("2");
        //queryTrackParam.setPhone("13864155868");
        String param = new Gson().toJson(queryTrackParam);

        queryTrackReq.setParam(param);
        queryTrackReq.setCustomer("7FBC3DEE91BFA7ACF8F19E423114DC81");
        String sign = SignUtils.querySign(param, "PKxKllGX650", "7FBC3DEE91BFA7ACF8F19E423114DC81");
        queryTrackReq.setSign(sign);

        String content = JSON.toJSONString(queryTrackReq);
        System.out.println("content:"+content);
        IOTRequest iotRequest=new IOTRequest();
        iotRequest.setMessageSeq("122010025810");
        iotRequest.setRouteType("1");
        iotRequest.setRouteValue("220");
        iotRequest.setLoginSystemCode("1650611485141759283");
        iotRequest.setPassword("");
        iotRequest.setRemoteIP("");
        iotRequest.setOperatorId("1617871748813121112");
        iotRequest.setBeId("000");
        iotRequest.setSign("4649b83ebe7767428293563d9430301b");
        iotRequest.setChannelId("13");
        iotRequest.setContent(content);
        System.out.println("iot;{}"+iotRequest);
        System.out.println("iotRequest:{}"+JSON.toJSONString(iotRequest));
        IOTAnswer<JSONObject> answer = iot100ExpressService.queryRealTimeExpress(iotRequest);
        System.out.println(answer);

    }


    @Test
    public void iotSms() throws UnsupportedEncodingException {
        Msg4Request msg4Request =new Msg4Request();


        msg4Request.setMobiles("18716342176");
        msg4Request.setSicode("7127c72900ef4c429480069daf3410db");
        msg4Request.setSignId("101399");
        msg4Request.setTempid("106114");
        msg4Request.setPARAM1("北京市客户经理");
        //msg4Request.setPARAM2("18716342176");
      // String param3 = URLEncoder.encode("https://*************:12900/mall/#/re", "UTF-8");
      /*  msg4Request.setPARAM3("你好");
        String param4 = URLEncoder.encode("gistration?&sf=1657613310985&mp=mH+SE", "UTF-8");
        msg4Request.setPARAM4(param4);
       // String param5 = URLEncoder.encode("lOhJ4LuulZZqFg79GO/FxhtBzax2aVkJb4HbpM=", "UTF-8");
        msg4Request.setPARAM5("123");*/
        String content = JSON.toJSONString(msg4Request);
        IOTRequest iotRequest=new IOTRequest();
        iotRequest.setMessageSeq("100010025721");
        iotRequest.setRouteType("1");
        iotRequest.setRouteValue("000");
        iotRequest.setLoginSystemCode("");
        iotRequest.setPassword("");
        iotRequest.setRemoteIP("");
        iotRequest.setOperatorId("1617871748813121112");
        iotRequest.setBeId("000");
        iotRequest.setSign("f19e7e29569d3a13ca3603a627704591");
        iotRequest.setChannelId("13");
        iotRequest.setContent(content);

        IOTAnswer<JSONObject> jsonObjectIOTAnswer = iotSmsService.sendSms(iotRequest);
        System.out.println(jsonObjectIOTAnswer);

    }

    @Test
    public void md5Hex(){
        String key = "82E4FE7FE78FE293";

        String a ="{\"inventoryInfo\":{\"spuOfferingInfo\":[{\"spuOfferingCode\":\"8000010989\"}]}}";

        String md5Hex = DigestUtils.md5Hex("content=" + a + "&" + key);
        System.out.println(md5Hex);


    }

    @Test
    public void smsTest() {
        BaseAnswer<LogisticsVO> logisticsDetails = order2CService.getLogisticsDetails("SF1981734023485", "shunfeng","15025737199");
        System.out.println(logisticsDetails);
    }


    @Test
    public void redisKeyTest() {
   /* Integer code = (int) ((Math.random() * 9 + 1) * 100000);
    System.out.println("生成的服务验证码"+code);
     redisTemplate.opsForValue().set(AFTER_ORDER_DISPATCH_INSTALL_REDIS_KEY.concat("720100000000464038").concat("15159592661"),String.valueOf(code));*/
        String redisContractValue = AFTER_ORDER_DISPATCH_INSTALL_REDIS_KEY.concat("570100000000465025")
                .concat("13648047960");
        Object redisServiceCode = redisTemplate.opsForValue().get(redisContractValue);
        System.out.println("获取的服务验证码："+redisServiceCode);
    }
}
