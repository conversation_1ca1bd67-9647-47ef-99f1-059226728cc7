package com.chinamobile.iot.sc;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.WriteDirectionEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.IOTApplication;
import com.chinamobile.iot.sc.pojo.dto.CardXBatchDeliverResultDTO;
import com.chinamobile.iot.sc.util.DateTimeUtil;
//import com.chinamobile.iot.sc.util.Excel2PDFUtil;
import com.chinamobile.iot.sc.util.ExcelUtils;
import com.chinamobile.iot.sc.util.FileUtils;
import com.chinamobile.iot.sc.util.PdfUtils;
import com.chinamobile.iot.sc.util.excel.CustomSheetWriteHandler;
import com.chinamobile.iot.sc.util.excel.EasyExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @package: com.chinamobile.sc
 * @ClassName: EasyExcelTest
 * @description: easyexcel单元测试类
 * @author: zyj
 * @create: 2022/1/29 9:45
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {IOTApplication.class})
public class EasyExcelTest {

    @Autowired
    ResourceLoader resourceLoader;

    @Test
    public void easyExcelTest(){
        ClassPathResource classPathResource = new ClassPathResource("template/goods_list.xlsx");
        File templateFile = null;
        String excelPath = "test.xlsx";
        String pdfPath = "goods.pdf";
        /*File outFile = new File(excelPath);
        File outPdfFile = new File(pdfPath);
        try {
            templateFile = classPathResource.getFile();
        } catch (IOException e) {
            e.printStackTrace();
        }
        System.out.println(templateFile.getName());
        ExcelWriter excelWriter = EasyExcel.write(outFile).withTemplate(templateFile).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("orderId", "888899996666");
        map.put("createTime", DateTimeUtil.getCurrentMin("yyyy-MM-dd HH:mm:ss"));
        map.put("custName", "张三");
        map.put("quantity", "10");
        excelWriter.fill(map, writeSheet);
        // 这里注意 入参用了forceNewRow 代表在写入list的时候不管list下面有没有空行 都会创建一行，然后下面的数据往后移动。默认 是false，会直接使用下一行，如果没有则创建。
        // forceNewRow 如果设置了true,有个缺点 就是他会把所有的数据都放到内存了，所以慎用
        // 简单的说 如果你的模板有list,且list不是最后一行，下面还有数据需要填充 就必须设置 forceNewRow=true 但是这个就会把所有数据放到内存 会很耗内存
        // 如果数据量大 list不是最后一行 参照下一个
        FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
        //造数据
        List<TestData> dataList = new ArrayList<>();
        TestData data = new TestData();
        data.setSkuOfferingName("千里眼-平安乡村");
        data.setSkuOfferingCode("千里眼-平安乡村");
        data.setAtomOfferingName("摄像头-平安乡村");
        data.setSaQuantity("2*5");
        data.setAtomPrice(10.50);
        data.setSumPrice(new BigDecimal(2*5*10.5));
        for(int i=10;i>0; i--){
            dataList.add(data);
        }
//        excelWriter.write(dataList, writeSheet);
        excelWriter.fill(dataList, fillConfig, writeSheet);

        excelWriter.finish();

        //转excel为pdf
        Excel2PDFUtil.excel2pdf(excelPath, pdfPath);

        ByteArrayOutputStream arrayOutputStream = new ByteArrayOutputStream();
        try {
            FileInputStream fileInputStream = new FileInputStream(pdfPath);

        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }*/
        /*byte[] bytes = Excel2PDFUtil.loadFile(pdfPath);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentDispositionFormData("attachment", "goods_new.pdf");
        httpHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        ResponseEntity<byte[]> responseEntity = new ResponseEntity<>(bytes, httpHeaders, HttpStatus.CREATED);
        File excelFile = new File(excelPath);
        File pdfFile = new File(pdfPath);
        try {
            System.out.println(JSON.toJSONString(responseEntity));
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            excelFile.deleteOnExit();
            pdfFile.deleteOnExit();
        }*/
        String pdfNewPath = "D:\\file\\goods_new.pdf";
        String jsonByte = "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";
        byte[] bytes1 = Base64.decodeBase64(jsonByte);
//        Excel2PDFUtil.byteToFile(bytes1, pdfNewPath);
    }

    /**
     * 测试有列表，也有其他单独属性的excel导出()，然后excel转pdf，再进行pdf合并
     * @param args
     */
    /*public static void main(String[] args) {
        FileOutputStream tempOutputStream = null;
        FileOutputStream mergedOutputStream = null;
        FileInputStream templateFileInputstream = null;
        String tempExcelName = "testExcel.xlsx";
        String mergedExcelName = "result.xlsx";
        try {
            templateFileInputstream = new FileInputStream("C:\\Users\\<USER>\\Desktop\\测试excel转PDF.xlsx");
            tempOutputStream = new FileOutputStream(tempExcelName);

            //列表之前的其他单个数据
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("orderId","123");
            dataMap.put("receiverName","刘祥");

            //列表之后的其他单个数据
            dataMap.put("date","2020-01-01");

            List<TestExcelDTO> dataList = new ArrayList<>();
            TestExcelDTO dto1 = new TestExcelDTO();
            dto1.setSkuName("sku1");
            dto1.setAtomName("atom1-1");
            dto1.setSkuQuantity("1");
            dto1.setRemark("备注");
            dataList.add(dto1);

            TestExcelDTO dto2 = new TestExcelDTO();
            dto2.setSkuName("sku1");
            dto2.setAtomName("atom1-2");
            dto2.setSkuQuantity("1");
            dto2.setRemark("备注");
            dataList.add(dto2);

            TestExcelDTO dto3 = new TestExcelDTO();
            dto3.setSkuName("sku2");
            dto3.setAtomName("atom2-1");
            dto3.setSkuQuantity("2");
            dto3.setRemark("备注");
            dataList.add(dto3);

            TestExcelDTO dto4 = new TestExcelDTO();
            dto4.setSkuName("sku3");
            dto4.setAtomName("atom3-1");
            dto4.setSkuQuantity("2");
            dto4.setRemark("备注");
            dataList.add(dto4);

            TestExcelDTO dto5 = new TestExcelDTO();
            dto5.setSkuName("sku3");
            dto5.setAtomName("atom3-2");
            dto5.setSkuQuantity("2");
            dto5.setRemark("备注");
            dataList.add(dto5);

            TestExcelDTO dto6 = new TestExcelDTO();
            dto6.setSkuName("sku3");
            dto6.setAtomName("atom3-3");
            dto6.setSkuQuantity("33");
            dto6.setRemark("备注");
            dataList.add(dto6);

            TestExcelDTO dto7 = new TestExcelDTO();
            dto7.setSkuName("sku4");
            dto7.setAtomName("atom4-1");
            dto7.setSkuQuantity("33");
            dto7.setRemark("备注");
            dataList.add(dto7);

            TestExcelDTO dto8 = new TestExcelDTO();
            dto8.setSkuName("sku4");
            dto8.setAtomName("atom4-2");
            dto8.setSkuQuantity("33");
            dto8.setRemark("备注");
            dataList.add(dto8);

            //确认需要合并的行
            //key - sku名称,valueList[0]--合并开始行（从第一行数据开始的数字），valueList[0]--合并结束行（从第一行数据开始的数字），
            Map<String,List<Integer>> mergeRowMap = new HashMap<>();

            for(int i=0;i<dataList.size();i++){
                TestExcelDTO excelDTO = dataList.get(i);
                String skuName = excelDTO.getSkuName();
                if(i<dataList.size()-1){
                    //如果是最后一行就不需要判断是否需要和下一行合并
                    TestExcelDTO nextExcelDTO = dataList.get(i + 1);
                    if(skuName.equals(nextExcelDTO.getSkuName())){
                        //需要合并
                        List<Integer> mergeRowList = mergeRowMap.get(skuName);
                        if(CollectionUtils.isEmpty(mergeRowList)){
                            mergeRowList = new ArrayList<>();
                            mergeRowList.add(i);
                            mergeRowList.add(i+1);
                            mergeRowMap.put(skuName,mergeRowList);
                        }else {
                            //将合并的结束行，向后增加一行
                            mergeRowList.set(1,i+1);
                        }
                    }
                }

            }

            String sheetName = "测试sheet";
            EasyExcelUtils.exportExcel2OutputStream(tempOutputStream,"list",dataList,dataMap,templateFileInputstream,0,sheetName,new ArrayList<>(),new ArrayList<>(),null,true);
            XSSFWorkbook workbook = new XSSFWorkbook(new File(tempExcelName));
            XSSFSheet sheet = workbook.getSheet(sheetName);

            //合并sku相同的行,必须加上偏移量，因为不是从第一行开始合并
            Integer rowStart = 7;
            if(!mergeRowMap.isEmpty()){
                Collection<List<Integer>> values = mergeRowMap.values();
                for (List<Integer> mergeRow : values) {
                    Integer startRow = mergeRow.get(0)+rowStart;
                    Integer endRow = mergeRow.get(1)+rowStart;
                    sheet.addMergedRegion(new CellRangeAddress(startRow,endRow,0,0));
                    sheet.addMergedRegion(new CellRangeAddress(startRow,endRow,2,2));
                    sheet.addMergedRegion(new CellRangeAddress(startRow,endRow,3,3));
                }
            }
            mergedOutputStream = new FileOutputStream(mergedExcelName);
            workbook.write(mergedOutputStream);
            workbook.close();

            //excel转pdf
            PdfUtils.excel2Pdf(mergedExcelName,"转化后的pdf.pdf");


        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            if(tempOutputStream != null){
                try {
                    tempOutputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if(mergedOutputStream != null){
                try {
                    mergedOutputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if(templateFileInputstream != null){
                try {
                    templateFileInputstream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            File tempExcel = new File(tempExcelName);
            File mergedExcel = new File(mergedExcelName);
            *//*if(tempExcel.exists()){
                tempExcel.delete();
            }
            if(mergedExcel.exists()){
                mergedExcel.delete();
            }*//*
        }
    }*/
    public static void main(String[] args) {
        PdfUtils.excel2Pdf("D:\\Projects\\supply-chain\\result.xlsx","结果.pdf");
        System.out.println("完毕");
    }

}
