package com.chinamobile.iot.sc;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @package: com.chinamobile.sc
 * @ClassName: TestData
 * @description: 测试数据对象
 * @author: zyj
 * @create: 2022/1/29 10:38
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
@Accessors(chain = true)
public class TestData {

    private String skuOfferingName;
    private String skuOfferingCode;
    private String atomOfferingName;
    private String saQuantity;
    private Double atomPrice;
    private BigDecimal sumPrice;

}
