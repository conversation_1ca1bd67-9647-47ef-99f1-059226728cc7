package com.chinamobile.iot.sc;

import com.chinamobile.iot.sc.config.CarSecurityConfig;
import com.chinamobile.iot.sc.util.CarSecurityUtil;
import org.apache.commons.lang.ArrayUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/2
 * @description
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {IOTApplication.class})
public class CarSecurityUtilTest {

    @Resource
    private CarSecurityUtil carSecurityUtil;

    @Resource
    private CarSecurityConfig carSecurityConfig;

    @Test
    public void testCar() throws Exception {
        String targetUri = "/openApi/v1/vehicle/queryVehicleInfo";
        String body = "{\"vin\": \"xxx\"}";
        String response = carSecurityUtil.doPostCar(targetUri, body);
        System.out.println(response);
    }

    @Test
    public void testSoftCode(){
        String softCode = carSecurityConfig.getSoftCode();
        String[] softCodeSplit = softCode.split(",");
        boolean contains = ArrayUtils.contains(softCodeSplit, "xcws-01-0003");
        if (contains){
            System.out.println("1111111111");
        }
    }

}
