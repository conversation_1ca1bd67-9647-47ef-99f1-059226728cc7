package com.chinamobile.iot.sc;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.controller.web.ProductFlowController;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.ProductFlowListParam;
import com.chinamobile.iot.sc.pojo.vo.ProductFlowDetailVO;
import com.chinamobile.iot.sc.pojo.vo.ProductFlowListVO;
import com.chinamobile.iot.sc.service.ProductFlowService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

/**
 * created by liuxiang on 2024/3/8 15:27
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {IOTApplication.class})
public class ProductFlowTest {

    @Autowired
    private ProductFlowService productFlowService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Test
    public void testInit(){
        BaseAnswer init = productFlowService.init();
        System.out.println(init);
    }

    @Test
    public void test2(){
        stringRedisTemplate.delete("lcTaskCount");
        stringRedisTemplate.delete("lcTaskDay");
    }

    @Test
    public void testGetFlowList(){
        BaseAnswer<PageData<ProductFlowListVO>> flowList = productFlowService.getFlowList(new ProductFlowListParam(), null);
        System.out.println(JSON.toJSONString(flowList));
    }

    @Test
    public void testGetFlowDetail(){
        BaseAnswer<ProductFlowDetailVO> flowDetail = productFlowService.getFlowDetail("1230174480899371008", null,true);
        System.out.println(flowDetail);
    }

    @Test
    public void testGetFlowTypeList(){
        System.out.println(productFlowService.getFlowTypeList());
    }

    @Test
    public void testGetFlowRoleList(){
        System.out.println(productFlowService.getFlowRoleList());
    }


}
