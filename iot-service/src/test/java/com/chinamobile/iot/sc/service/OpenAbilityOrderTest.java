package com.chinamobile.iot.sc.service;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.OpenAbilityConstant;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.common.utils.BaseUtils;
import com.chinamobile.iot.sc.constant.OrderStatusInnerEnum;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.dao.ext.UserRefundKxMapperExt;
import com.chinamobile.iot.sc.dao.handle.InventoryHandlerMapper;
import com.chinamobile.iot.sc.dao.handle.OrderHandleMapper;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.mode.*;
import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.pojo.entity.OperateRecord;
import com.chinamobile.iot.sc.pojo.handle.OrderInfoHandle;
import com.chinamobile.iot.sc.pojo.param.KxCanChooseUserParam;
import com.chinamobile.iot.sc.pojo.vo.KxCanChooseUserVO;
import com.chinamobile.iot.sc.response.iot.ServiceResultInfoResponse;
import com.chinamobile.iot.sc.response.web.Order2CInfoDetailDTO;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.HttpUtil;
import com.chinamobile.iot.sc.util.OpenAbilityUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpStatus;
import org.springframework.test.annotation.Commit;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/17 11:01
 * @description TODO
 */
@SpringBootTest
public class OpenAbilityOrderTest {


    @Resource
    private OpenAbilityAppMapper openAbilityAppMapper;

    @Resource
    private OpenAbilityOrganizationMapper openAbilityOrganizationMapper;

    @Resource
    private OpenAbilityThemeMapper openAbilityThemeMapper;

    @Resource
    private OpenAbilityThemeContentMapper openAbilityThemeContentMapper;

    @Resource
    private OpenAbilityThemeFieldRuleMapper openAbilityThemeFieldRuleMapper;

    @Resource
    private OpenAbilityThemeContentConfigMapper openAbilityThemeContentConfigMapper;

    @Resource
    private OpenAbilityThemeFieldConfigMapper openAbilityThemeFieldConfigMapper;

    @Resource
    private IOrder2CService iOrder2CService;

    @Resource
    private OpenAbilityUtils openAbilityUtils;

    @Resource
    private OrderHandleMapper orderHandleMapper;


    @Test
    public void createTestOrganization() {
        OpenAbilityOrganization organization = new OpenAbilityOrganization();
        organization.setId(BaseServiceUtils.getId());
        organization.setApiKey(BaseUtils.generateRandomString(32));
        organization.setSecret(BaseUtils.generateRandomString(32));
        organization.setName("开放平台部");
        organization.setDescription("开放平台部");
        organization.setContactName("测试");
        organization.setContactPhone("17823323244");
        organization.setEnable(true);
        Date now = new Date();
        organization.setCreateTime(now);
        organization.setUpdateTime(now);

        openAbilityOrganizationMapper.insertSelective(organization);

        OpenAbilityApp app = new OpenAbilityApp();
        app.setId(BaseServiceUtils.getId());
        app.setOrganizationId(organization.getId());
        app.setName("默认应用");
        app.setCreateTime(now);
        app.setUpdateTime(now);

        openAbilityAppMapper.insertSelective(app);
    }

    @Test
    public void createMarketOrganization() {
        OpenAbilityOrganization organization = new OpenAbilityOrganization();
        organization.setId(BaseServiceUtils.getId());
        organization.setApiKey(BaseUtils.generateRandomString(32));
        organization.setSecret(BaseUtils.generateRandomString(32));
        organization.setName("市场部");
        organization.setDescription("市场部");
        organization.setContactName("谢世鑫");
        organization.setContactPhone("15723095199");
        organization.setEnable(true);
        Date now = new Date();
        organization.setCreateTime(now);
        organization.setUpdateTime(now);

        openAbilityOrganizationMapper.insertSelective(organization);

        OpenAbilityApp app = new OpenAbilityApp();
        app.setId(BaseServiceUtils.getId());
        app.setOrganizationId(organization.getId());
        app.setName("默认应用");
        app.setCreateTime(now);
        app.setUpdateTime(now);

        openAbilityAppMapper.insertSelective(app);
    }

    @Test
    public void createSKOrganization() {
        OpenAbilityOrganization organization = new OpenAbilityOrganization();
        organization.setId(BaseServiceUtils.getId());
        organization.setApiKey(BaseUtils.generateRandomString(32));
        organization.setSecret(BaseUtils.generateRandomString(32));
        organization.setName("商客部");
        organization.setDescription("商客部");
        organization.setContactName("尹栩沆");
        organization.setContactPhone("18883031305");
        organization.setEnable(true);
        Date now = new Date();
        organization.setCreateTime(now);
        organization.setUpdateTime(now);

        openAbilityOrganizationMapper.insertSelective(organization);

        OpenAbilityApp app = new OpenAbilityApp();
        app.setId(BaseServiceUtils.getId());
        app.setOrganizationId(organization.getId());
        app.setName("默认应用");
        app.setCreateTime(now);
        app.setUpdateTime(now);

        openAbilityAppMapper.insertSelective(app);
    }


    @Test
    public void createCYZQOrganization(){
        OpenAbilityOrganization organization = new OpenAbilityOrganization();
        organization.setId(BaseServiceUtils.getId());
        organization.setApiKey(BaseUtils.generateRandomString(32));
        organization.setSecret(BaseUtils.generateRandomString(32));
        organization.setName("云南彩云智企");
        organization.setDescription("云南彩云智企");
        organization.setContactName("骆杨");
        organization.setContactPhone("15823524720");
        organization.setEnable(true);
        Date now = new Date();
        organization.setCreateTime(now);
        organization.setUpdateTime(now);

        openAbilityOrganizationMapper.insertSelective(organization);

        OpenAbilityApp app = new OpenAbilityApp();
        app.setId(BaseServiceUtils.getId());
        app.setOrganizationId(organization.getId());
        app.setName("默认应用");
        app.setCreateTime(now);
        app.setUpdateTime(now);
        openAbilityAppMapper.insertSelective(app);

    }

    @Test
    public void createEstewardOrganization(){
        Date now = new Date();
        OpenAbilityApp app = new OpenAbilityApp();
        app.setId(BaseServiceUtils.getId());
        app.setOrganizationId("1282630899866054656");
        app.setName("湖南移智商企");
        app.setCreateTime(now);
        app.setUpdateTime(now);
        openAbilityAppMapper.insertSelective(app);
    }



    @Test
    public void createTestTheme() {
        OpenAbilityTheme theme = new OpenAbilityTheme();
        theme.setId(BaseServiceUtils.getId());
        theme.setName("订单");
        theme.setCode("order");
        Date now = new Date();
        theme.setCreateTime(now);
        theme.setUpdateTime(now);

        openAbilityThemeMapper.insertSelective(theme);

        OpenAbilityThemeContent apiContent = new OpenAbilityThemeContent();
        apiContent.setId(BaseServiceUtils.getId());
        apiContent.setType(BaseConstant.OPEN_ABILITY_CONTENT_TPE_API);
        apiContent.setName("订单列表");
        apiContent.setPathCode("/open/order/list");
        apiContent.setMethod("get");
        apiContent.setIntroUrl("https://www.baidu.com");
        apiContent.setThemeId(theme.getId());

        openAbilityThemeContentMapper.insertSelective(apiContent);

//        createTestApiField(apiContent.getId());

        OpenAbilityThemeContent subscribeContent = new OpenAbilityThemeContent();
        subscribeContent.setId(BaseServiceUtils.getId());
        subscribeContent.setType(BaseConstant.OPEN_ABILITY_CONTENT_TPE_SUBSCRIBE);
        subscribeContent.setName("订单数据订阅");
        subscribeContent.setPathCode("order");
        subscribeContent.setIntroUrl("https://www.baidu.com");
        subscribeContent.setThemeId(theme.getId());

        openAbilityThemeContentMapper.insertSelective(subscribeContent);

        createTestSubscribeField(theme.getId(), subscribeContent.getId());
    }


    @Test
    public void createCYZQTheme() {
        OpenAbilityThemeContent apiContent = new OpenAbilityThemeContent();
        apiContent.setId(BaseServiceUtils.getId());
        apiContent.setType(BaseConstant.OPEN_ABILITY_CONTENT_TPE_API);
        apiContent.setName("彩云智企订单列表");
        apiContent.setPathCode("/open/order/cyzq/list");
        apiContent.setMethod("get");
        apiContent.setIntroUrl("https://www.baidu.com");
        apiContent.setThemeId("1250149552393957376");

        openAbilityThemeContentMapper.insertSelective(apiContent);
    }


    @Test
    public void createDataTheme() {
        OpenAbilityTheme theme = new OpenAbilityTheme();
        theme.setId(BaseServiceUtils.getId());
        theme.setName("数据");
        theme.setCode("data");
        Date now = new Date();
        theme.setCreateTime(now);
        theme.setUpdateTime(now);

        openAbilityThemeMapper.insertSelective(theme);

        List<OpenAbilityThemeContent> contents = new ArrayList<>();
        OpenAbilityThemeContent apiContent = new OpenAbilityThemeContent();
        apiContent.setId(BaseServiceUtils.getId());
        apiContent.setType(BaseConstant.OPEN_ABILITY_CONTENT_TPE_API);
        apiContent.setName("销售额接口");
        apiContent.setPathCode("/data/market/sales");
        apiContent.setMethod("get");
        apiContent.setIntroUrl("https://www.baidu.com");
        apiContent.setThemeId(theme.getId());
        contents.add(apiContent);

        OpenAbilityThemeContent apiContent1 = new OpenAbilityThemeContent();
        apiContent1.setId(BaseServiceUtils.getId());
        apiContent1.setType(BaseConstant.OPEN_ABILITY_CONTENT_TPE_API);
        apiContent1.setName("订单数接口");
        apiContent1.setPathCode("/data/market/orders");
        apiContent1.setMethod("get");
        apiContent1.setIntroUrl("https://www.baidu.com");
        apiContent1.setThemeId(theme.getId());
        contents.add(apiContent1);

        OpenAbilityThemeContent apiContent2 = new OpenAbilityThemeContent();
        apiContent2.setId(BaseServiceUtils.getId());
        apiContent2.setType(BaseConstant.OPEN_ABILITY_CONTENT_TPE_API);
        apiContent2.setName("产品销量top10");
        apiContent2.setPathCode("/data/market/top10pro");
        apiContent2.setMethod("post");
        apiContent2.setIntroUrl("https://www.baidu.com");
        apiContent2.setThemeId(theme.getId());
        contents.add(apiContent2);

        OpenAbilityThemeContent apiContent3 = new OpenAbilityThemeContent();
        apiContent3.setId(BaseServiceUtils.getId());
        apiContent3.setType(BaseConstant.OPEN_ABILITY_CONTENT_TPE_API);
        apiContent3.setName("用户数接口");
        apiContent3.setPathCode("/data/market/users");
        apiContent3.setMethod("get");
        apiContent3.setIntroUrl("https://www.baidu.com");
        apiContent3.setThemeId(theme.getId());
        contents.add(apiContent3);

        openAbilityThemeContentMapper.batchInsert(contents);
    }

    @Test
    public void testConfigMarketOrganization() {
        List<OpenAbilityOrganization> organizations = openAbilityOrganizationMapper.selectByExample(
                new OpenAbilityOrganizationExample().createCriteria().andNameEqualTo("市场部").example());
        if (CollectionUtils.isEmpty(organizations)) {
            return;
        }
        OpenAbilityOrganization organization = organizations.get(0);

        List<OpenAbilityTheme> themes = openAbilityThemeMapper.selectByExample(
                new OpenAbilityThemeExample().createCriteria().andCodeEqualTo("data").example());
        if (CollectionUtils.isEmpty(themes)) {
            return;
        }
        OpenAbilityTheme theme = themes.get(0);
        List<OpenAbilityThemeContent> contents = openAbilityThemeContentMapper.selectByExample(
                new OpenAbilityThemeContentExample().createCriteria().andThemeIdEqualTo(theme.getId()).example());
        if (CollectionUtils.isEmpty(contents)) {
            return;
        }
        Date now = new Date();
        //配置机构
        List<OpenAbilityThemeContentConfig> organizationContentConfigs = contents.stream().map(item -> {
            OpenAbilityThemeContentConfig config = new OpenAbilityThemeContentConfig();
            config.setContentId(item.getId());
            config.setId(BaseServiceUtils.getId());
            config.setOrganizationId(organization.getId());
            config.setCreateTime(now);
            config.setUpdateTime(now);
            return config;
        }).collect(Collectors.toList());

        List<OpenAbilityApp> apps = openAbilityAppMapper.selectByExample(
                new OpenAbilityAppExample().createCriteria().andOrganizationIdEqualTo(organization.getId()).example());
        if (CollectionUtils.isEmpty(apps)) {
            return;
        }
        OpenAbilityApp app = apps.get(0);
        //配置应用
        organizationContentConfigs.addAll(
                contents.stream().map(item -> {
                    OpenAbilityThemeContentConfig config = new OpenAbilityThemeContentConfig();
                    config.setContentId(item.getId());
                    config.setId(BaseServiceUtils.getId());
                    config.setAppId(app.getId());
                    config.setCreateTime(now);
                    config.setUpdateTime(now);
                    return config;
                }).collect(Collectors.toList()));

        openAbilityThemeContentConfigMapper.batchInsert(organizationContentConfigs);
    }

    @Test
    public void testConfigSKOrganization() {
        List<OpenAbilityOrganization> organizations = openAbilityOrganizationMapper.selectByExample(
                new OpenAbilityOrganizationExample().createCriteria().andNameEqualTo("商客部").example());
        if (CollectionUtils.isEmpty(organizations)) {
            return;
        }
        OpenAbilityOrganization organization = organizations.get(0);

        List<OpenAbilityTheme> themes = openAbilityThemeMapper.selectByExample(
                new OpenAbilityThemeExample().createCriteria().andCodeEqualTo("order").example());
        if (CollectionUtils.isEmpty(themes)) {
            return;
        }
        OpenAbilityTheme theme = themes.get(0);
        List<OpenAbilityThemeContent> contents = openAbilityThemeContentMapper.selectByExample(
                new OpenAbilityThemeContentExample().createCriteria().andThemeIdEqualTo(theme.getId()).andTypeEqualTo("api").example());
        if (CollectionUtils.isEmpty(contents)) {
            return;
        }
        Date now = new Date();
        //配置机构
        List<OpenAbilityThemeContentConfig> organizationContentConfigs = contents.stream().map(item -> {
            OpenAbilityThemeContentConfig config = new OpenAbilityThemeContentConfig();
            config.setContentId(item.getId());
            config.setId(BaseServiceUtils.getId());
            config.setOrganizationId(organization.getId());
            config.setCreateTime(now);
            config.setUpdateTime(now);
            return config;
        }).collect(Collectors.toList());

        List<OpenAbilityApp> apps = openAbilityAppMapper.selectByExample(
                new OpenAbilityAppExample().createCriteria().andOrganizationIdEqualTo(organization.getId()).example());
        if (CollectionUtils.isEmpty(apps)) {
            return;
        }
        OpenAbilityApp app = apps.get(0);
        //配置应用
        organizationContentConfigs.addAll(
                contents.stream().map(item -> {
                    OpenAbilityThemeContentConfig config = new OpenAbilityThemeContentConfig();
                    config.setContentId(item.getId());
                    config.setId(BaseServiceUtils.getId());
                    config.setAppId(app.getId());
                    config.setCreateTime(now);
                    config.setUpdateTime(now);
                    return config;
                }).collect(Collectors.toList()));

        openAbilityThemeContentConfigMapper.batchInsert(organizationContentConfigs);
    }

    @Test
    public void testConfigCYZQOrganization() {
        List<OpenAbilityOrganization> organizations = openAbilityOrganizationMapper.selectByExample(
                new OpenAbilityOrganizationExample().createCriteria().andNameEqualTo("云南彩云智企").example());
        if (CollectionUtils.isEmpty(organizations)) {
            return;
        }
        OpenAbilityOrganization organization = organizations.get(0);

        List<OpenAbilityTheme> themes = openAbilityThemeMapper.selectByExample(
                new OpenAbilityThemeExample().createCriteria().andCodeEqualTo("order").example());
        if (CollectionUtils.isEmpty(themes)) {
            return;
        }
//        OpenAbilityTheme theme = themes.get(0);
//        List<OpenAbilityThemeContent> contents = openAbilityThemeContentMapper.selectByExample(
//                new OpenAbilityThemeContentExample().createCriteria().andThemeIdEqualTo(theme.getId()).andTypeEqualTo("api").example());
//        if (CollectionUtils.isEmpty(contents)) {
//            return;
//        }

        List<OpenAbilityThemeContent> contents = openAbilityThemeContentMapper.selectByExample(
                new OpenAbilityThemeContentExample().createCriteria().andTypeEqualTo("api").andNameEqualTo("彩云智企订单列表").example());

        Date now = new Date();
        //配置机构
        List<OpenAbilityThemeContentConfig> organizationContentConfigs = contents.stream().map(item -> {
            OpenAbilityThemeContentConfig config = new OpenAbilityThemeContentConfig();
            config.setContentId(item.getId());
            config.setId(BaseServiceUtils.getId());
            config.setOrganizationId(organization.getId());
            config.setCreateTime(now);
            config.setUpdateTime(now);
            return config;
        }).collect(Collectors.toList());

        List<OpenAbilityApp> apps = openAbilityAppMapper.selectByExample(
                new OpenAbilityAppExample().createCriteria().andOrganizationIdEqualTo(organization.getId()).example());
        if (CollectionUtils.isEmpty(apps)) {
            return;
        }
        OpenAbilityApp app = apps.get(0);
        //配置应用
        organizationContentConfigs.addAll(
                contents.stream().map(item -> {
                    OpenAbilityThemeContentConfig config = new OpenAbilityThemeContentConfig();
                    config.setContentId(item.getId());
                    config.setId(BaseServiceUtils.getId());
                    config.setAppId(app.getId());
                    config.setCreateTime(now);
                    config.setUpdateTime(now);
                    return config;
                }).collect(Collectors.toList()));

        openAbilityThemeContentConfigMapper.batchInsert(organizationContentConfigs);
    }


    @Test
    public void testConfigEstewardOrganization(){
        List<OpenAbilityOrganization> organizations = openAbilityOrganizationMapper.selectByExample(
                new OpenAbilityOrganizationExample().createCriteria().andNameEqualTo("商客部").example());
        if (CollectionUtils.isEmpty(organizations)) {
            return;
        }
        OpenAbilityOrganization organization = organizations.get(0);

        List<OpenAbilityTheme> themes = openAbilityThemeMapper.selectByExample(
                new OpenAbilityThemeExample().createCriteria().andCodeEqualTo("order").example());
        if (CollectionUtils.isEmpty(themes)) {
            return;
        }
        OpenAbilityTheme theme = themes.get(0);
        List<OpenAbilityThemeContent> contents = openAbilityThemeContentMapper.selectByExample(
                new OpenAbilityThemeContentExample().createCriteria().andThemeIdEqualTo(theme.getId()).andTypeEqualTo("api")
                        .andNameEqualTo("订单列表").example());
        if (CollectionUtils.isEmpty(contents)) {
            return;
        }
        Date now = new Date();
        //配置机构
//        List<OpenAbilityThemeContentConfig> organizationContentConfigs = contents.stream().map(item -> {
//            OpenAbilityThemeContentConfig config = new OpenAbilityThemeContentConfig();
//            config.setContentId(item.getId());
//            config.setId(BaseServiceUtils.getId());
//            config.setOrganizationId(organization.getId());
//            config.setCreateTime(now);
//            config.setUpdateTime(now);
//            return config;
//        }).collect(Collectors.toList());

        List<OpenAbilityApp> apps = openAbilityAppMapper.selectByExample(
                new OpenAbilityAppExample().createCriteria().andOrganizationIdEqualTo(organization.getId()).andNameEqualTo("湖南移智商企").example());
        if (CollectionUtils.isEmpty(apps)) {
            return;
        }
        OpenAbilityApp app = apps.get(0);
        //配置应用
        List<OpenAbilityThemeContentConfig> organizationContentConfigs =
                contents.stream().map(item -> {
                    OpenAbilityThemeContentConfig config = new OpenAbilityThemeContentConfig();
                    config.setContentId(item.getId());
                    config.setId(BaseServiceUtils.getId());
                    config.setAppId(app.getId());
                    config.setCreateTime(now);
                    config.setUpdateTime(now);
                    return config;
                }).collect(Collectors.toList());

        openAbilityThemeContentConfigMapper.batchInsert(organizationContentConfigs);
    }



    @Test
    public void createMarketConfig() {
        createMarketOrganization();
        createDataTheme();
        testConfigMarketOrganization();
        testLoad2Redis();
    }

    @Test
    public void createSKConfig() {
        createSKOrganization();
        testConfigSKOrganization();
        testLoad2Redis();
    }


    /**
     * 云南彩云智企数据开放
     */
    @Test
    public void createCYZQConfig() {
        createCYZQTheme();
        createCYZQOrganization();
        testConfigCYZQOrganization();
    }

    @Test
    public void createEstewardConfig() {
        createEstewardOrganization();
        testConfigEstewardOrganization();
    }




    private void createTestApiField(String contentId) {

    }

    private void createTestSubscribeField(String themeId, String contentId) {
        List<OpenAbilityThemeFieldRule> rules = new ArrayList<>();
        OpenAbilityThemeFieldRule orderStatue = new OpenAbilityThemeFieldRule();
        orderStatue.setId(BaseServiceUtils.getId());
        orderStatue.setName("orderStatus");
        orderStatue.setDescription("订单状态");
        orderStatue.setThemeId(themeId);
        orderStatue.setContentId(contentId);
        orderStatue.setType(OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_ENUM);
        List<OpenAbilityRangeRO> rangeROS = new ArrayList<>();
        Map<Object, String> enumMap = new LinkedHashMap<>();
        enumMap.put(0, "待发货");
        enumMap.put(1, "待收货");
        enumMap.put(2, "已收货");
        enumMap.put(3, "开票");
        enumMap.put(4, "退款中");
        enumMap.put(5, "退货退款中");
        enumMap.put(6, "换货中");
        enumMap.put(7, "交易完成");
        enumMap.put(8, "交易失败");
        enumMap.put(10, "待接单（代客下单）");
        enumMap.put(11, "待省侧审批（代客下单）");
        enumMap.put(12, "待出账（仅代客下单）");
        enumMap.forEach((status, desc) -> rangeROS.add(new OpenAbilityRangeRO(desc, status)));

        orderStatue.setRangeJson(JSON.toJSONString(rangeROS));
        rules.add(orderStatue);

        OpenAbilityThemeFieldRule createTime = new OpenAbilityThemeFieldRule();
        createTime.setId(BaseServiceUtils.getId());
        createTime.setName("createTime");
        createTime.setDescription("订单创建时间");
        createTime.setThemeId(themeId);
        createTime.setContentId(contentId);
        createTime.setType(OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_TIME);
        rules.add(createTime);

        OpenAbilityThemeFieldRule spuOfferingName = new OpenAbilityThemeFieldRule();
        spuOfferingName.setId(BaseServiceUtils.getId());
        spuOfferingName.setName("spuOfferingName");
        spuOfferingName.setDescription("商品组名称");
        spuOfferingName.setThemeId(themeId);
        spuOfferingName.setContentId(contentId);
        spuOfferingName.setType(OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_STRING);
        rules.add(spuOfferingName);

        OpenAbilityThemeFieldRule spuOfferingCode = new OpenAbilityThemeFieldRule();
        spuOfferingCode.setId(BaseServiceUtils.getId());
        spuOfferingCode.setName("spuOfferingCode");
        spuOfferingCode.setDescription("商品组编码");
        spuOfferingCode.setThemeId(themeId);
        spuOfferingCode.setContentId(contentId);
        spuOfferingCode.setType(OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_STRING);
        rules.add(spuOfferingCode);

        OpenAbilityThemeFieldRule skuOfferingName = new OpenAbilityThemeFieldRule();
        skuOfferingName.setId(BaseServiceUtils.getId());
        skuOfferingName.setName("skuOfferingName");
        skuOfferingName.setDescription("商品规格名称");
        skuOfferingName.setThemeId(themeId);
        skuOfferingName.setContentId(contentId);
        skuOfferingName.setType(OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_STRING);
        rules.add(skuOfferingName);

        OpenAbilityThemeFieldRule skuOfferingCode = new OpenAbilityThemeFieldRule();
        skuOfferingCode.setId(BaseServiceUtils.getId());
        skuOfferingCode.setName("skuOfferingCode");
        skuOfferingCode.setDescription("商品规格编码");
        skuOfferingCode.setThemeId(themeId);
        skuOfferingCode.setContentId(contentId);
        skuOfferingCode.setType(OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_STRING);
        rules.add(skuOfferingCode);

        OpenAbilityThemeFieldRule atomOfferingCode = new OpenAbilityThemeFieldRule();
        atomOfferingCode.setId(BaseServiceUtils.getId());
        atomOfferingCode.setName("atomOfferingCode");
        atomOfferingCode.setDescription("原子商品编码");
        atomOfferingCode.setThemeId(themeId);
        atomOfferingCode.setContentId(contentId);
        atomOfferingCode.setType(OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_STRING);
        rules.add(atomOfferingCode);

        OpenAbilityThemeFieldRule atomOfferingName = new OpenAbilityThemeFieldRule();
        atomOfferingName.setId(BaseServiceUtils.getId());
        atomOfferingName.setName("atomOfferingName");
        atomOfferingName.setDescription("原子商品名称");
        atomOfferingName.setThemeId(themeId);
        atomOfferingName.setContentId(contentId);
        atomOfferingName.setType(OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_STRING);
        rules.add(atomOfferingName);

        OpenAbilityThemeFieldRule partnerName = new OpenAbilityThemeFieldRule();
        partnerName.setId(BaseServiceUtils.getId());
        partnerName.setName("partnerName");
        partnerName.setDescription("合作伙伴名称");
        partnerName.setThemeId(themeId);
        partnerName.setContentId(contentId);
        partnerName.setType(OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_STRING);
        rules.add(partnerName);

        OpenAbilityThemeFieldRule cooperatorName = new OpenAbilityThemeFieldRule();
        cooperatorName.setId(BaseServiceUtils.getId());
        cooperatorName.setName("cooperatorName");
        cooperatorName.setDescription("合作伙伴联系人姓名");
        cooperatorName.setThemeId(themeId);
        cooperatorName.setContentId(contentId);
        cooperatorName.setType(OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_STRING);
        rules.add(cooperatorName);

        OpenAbilityThemeFieldRule specialAfterMarketHandle = new OpenAbilityThemeFieldRule();
        specialAfterMarketHandle.setId(BaseServiceUtils.getId());
        specialAfterMarketHandle.setName("specialAfterMarketHandle");
        specialAfterMarketHandle.setDescription("是否开启了特殊退货退款查询");
        specialAfterMarketHandle.setThemeId(themeId);
        specialAfterMarketHandle.setContentId(contentId);
        specialAfterMarketHandle.setType(OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_BOOL);
        rules.add(specialAfterMarketHandle);

        OpenAbilityThemeFieldRule specialAfterStatus = new OpenAbilityThemeFieldRule();
        specialAfterStatus.setId(BaseServiceUtils.getId());
        specialAfterStatus.setName("specialAfterStatus");
        specialAfterStatus.setDescription("特殊的售后状态");
        specialAfterStatus.setThemeId(themeId);
        specialAfterStatus.setContentId(contentId);
        specialAfterStatus.setType(OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_ENUM);
        enumMap.clear();
        rangeROS.clear();
        enumMap.put("1", "待退款");
        enumMap.put("2", "退款中");
        enumMap.put("3", "退款成功");
        enumMap.put("4", "退款取消");
        enumMap.put("5", "部分退款取消");
        enumMap.put("6", "部分退款成功");
        enumMap.forEach((status, desc) -> rangeROS.add(new OpenAbilityRangeRO(desc, status)));

        specialAfterStatus.setRangeJson(JSON.toJSONString(rangeROS));
        rules.add(specialAfterStatus);

        OpenAbilityThemeFieldRule orderType = new OpenAbilityThemeFieldRule();
        orderType.setId(BaseServiceUtils.getId());
        orderType.setName("orderType");
        orderType.setDescription("订单类型");
        orderType.setThemeId(themeId);
        orderType.setContentId(contentId);
        orderType.setType(OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_ENUM);
        enumMap.clear();
        rangeROS.clear();
        enumMap.put("01", "自主下单");
        enumMap.put("00", "代客下单");
        enumMap.put("02", "代客下单(省内融合集团客户订单)");
        enumMap.put("03", "代客下单(省内融合个人客户订单)");
        enumMap.forEach((status, desc) -> rangeROS.add(new OpenAbilityRangeRO(desc, status)));

        orderType.setRangeJson(JSON.toJSONString(rangeROS));
        rules.add(orderType);

        OpenAbilityThemeFieldRule qlyStatus = new OpenAbilityThemeFieldRule();
        qlyStatus.setId(BaseServiceUtils.getId());
        qlyStatus.setName("qlyStatus");
        qlyStatus.setDescription("千里眼服务开通状态");
        qlyStatus.setThemeId(themeId);
        qlyStatus.setContentId(contentId);
        qlyStatus.setType(OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_ENUM);
        enumMap.clear();
        rangeROS.clear();
        enumMap.put(0, "未校验");
        enumMap.put(1, "校验成功");
        enumMap.put(2, "开通成功");
        enumMap.put(3, "开通失败");
        enumMap.put(4, "部分开通成功");
        enumMap.put(5, "退订成功");
        enumMap.put(6, "退订失败");
        enumMap.forEach((status, desc) -> rangeROS.add(new OpenAbilityRangeRO(desc, status)));

        qlyStatus.setRangeJson(JSON.toJSONString(rangeROS));
        rules.add(qlyStatus);

        OpenAbilityThemeFieldRule ysxStatus = new OpenAbilityThemeFieldRule();
        ysxStatus.setId(BaseServiceUtils.getId());
        ysxStatus.setName("qlyStatus");
        ysxStatus.setDescription("云视讯平台服务开通状态");
        ysxStatus.setThemeId(themeId);
        ysxStatus.setContentId(contentId);
        ysxStatus.setType(OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_ENUM);
        enumMap.clear();
        rangeROS.clear();
        enumMap.put(1, "已校验");
        enumMap.put(2, "开通成功");
        enumMap.put(3, "开通失败");
        enumMap.put(4, "退订成功");
        enumMap.put(5, "退订失败");
        enumMap.forEach((status, desc) -> rangeROS.add(new OpenAbilityRangeRO(desc, status)));

        ysxStatus.setRangeJson(JSON.toJSONString(rangeROS));
        rules.add(ysxStatus);

        System.out.println(JSON.toJSONString(rules));
        openAbilityThemeFieldRuleMapper.batchInsert(rules);
    }

    @Test
    public void testThemeField() {
        List<OpenAbilityThemeFieldRule> rules = new ArrayList<>();
        List<OpenAbilityRangeRO> rangeROS = new ArrayList<>();
        Map<Object, String> enumMap = new LinkedHashMap<>();

        OpenAbilityThemeFieldRule specialAfterMarketHandle = new OpenAbilityThemeFieldRule();
        specialAfterMarketHandle.setId(BaseServiceUtils.getId());
        specialAfterMarketHandle.setName("specialAfterMarketHandle");
        specialAfterMarketHandle.setDescription("是否开启了特殊退货退款查询");
        specialAfterMarketHandle.setThemeId("1000000231314");
        specialAfterMarketHandle.setContentId("2000013413455");
        specialAfterMarketHandle.setType(OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_BOOL);
        rules.add(specialAfterMarketHandle);

        OpenAbilityThemeFieldRule ysxStatus = new OpenAbilityThemeFieldRule();
        ysxStatus.setId(BaseServiceUtils.getId());
        ysxStatus.setName("qlyStatus");
        ysxStatus.setDescription("云视讯平台服务开通状态");
        ysxStatus.setThemeId("1000000231314");
        ysxStatus.setContentId("2000013413455");
        ysxStatus.setType(OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_ENUM);
        enumMap.clear();
        rangeROS.clear();
        enumMap.put(1, "已校验");
        enumMap.put(2, "开通成功");
        enumMap.put(3, "开通失败");
        enumMap.put(4, "退订成功");
        enumMap.put(5, "退订失败");
        enumMap.forEach((status, desc) -> rangeROS.add(new OpenAbilityRangeRO(desc, status)));

        ysxStatus.setRangeJson(JSON.toJSONString(rangeROS));
        rules.add(ysxStatus);

        openAbilityThemeFieldRuleMapper.batchInsert(rules);
    }

    @Test
    public void testConfigOrganization() {
        List<OpenAbilityOrganization> organizations = openAbilityOrganizationMapper.selectByExample(
                new OpenAbilityOrganizationExample().createCriteria().andNameEqualTo("开放平台部").example());
        if (CollectionUtils.isEmpty(organizations)) {
            return;
        }
        OpenAbilityOrganization organization = organizations.get(0);

        List<OpenAbilityTheme> themes = openAbilityThemeMapper.selectByExample(
                new OpenAbilityThemeExample().createCriteria().andCodeEqualTo("order").example());
        if (CollectionUtils.isEmpty(themes)) {
            return;
        }
        OpenAbilityTheme theme = themes.get(0);
        List<OpenAbilityThemeContent> contents = openAbilityThemeContentMapper.selectByExample(
                new OpenAbilityThemeContentExample().createCriteria().andThemeIdEqualTo(theme.getId()).example());
        if (CollectionUtils.isEmpty(contents)) {
            return;
        }
        Date now = new Date();
        List<OpenAbilityThemeContentConfig> organizationContentConfigs = contents.stream().map(item -> {
            OpenAbilityThemeContentConfig config = new OpenAbilityThemeContentConfig();
            config.setContentId(item.getId());
            config.setId(BaseServiceUtils.getId());
            config.setOrganizationId(organization.getId());
            config.setCreateTime(now);
            config.setUpdateTime(now);
            return config;
        }).collect(Collectors.toList());

        List<OpenAbilityApp> apps = openAbilityAppMapper.selectByExample(
                new OpenAbilityAppExample().createCriteria().andOrganizationIdEqualTo(organization.getId()).example());
        if (CollectionUtils.isEmpty(apps)) {
            return;
        }
        OpenAbilityApp app = apps.get(0);
        contents.stream().filter(x -> StringUtils.equals(x.getType(),
                BaseConstant.OPEN_ABILITY_CONTENT_TPE_SUBSCRIBE)).findFirst().ifPresent(value -> {
            OpenAbilityThemeContentConfig config = new OpenAbilityThemeContentConfig();
            config.setId(BaseServiceUtils.getId());
            config.setContentId(value.getId());
            config.setAppId(app.getId());
            config.setWebHook("https://www.test.com/order/subscribe");
            config.setCreateTime(now);
            config.setUpdateTime(now);
            organizationContentConfigs.add(config);

            openAbilityThemeFieldRuleMapper.selectByExample(new OpenAbilityThemeFieldRuleExample().createCriteria()
                            .andContentIdEqualTo(value.getId()).andNameEqualTo("orderStatus").example())
                    .stream().findFirst().ifPresent(filed -> {
                        OpenAbilityThemeFieldConfig fieldConfig = new OpenAbilityThemeFieldConfig();
                        fieldConfig.setId(BaseServiceUtils.getId());
                        fieldConfig.setAppId(app.getId());
                        fieldConfig.setFieldId(filed.getId());
                        fieldConfig.setRuleType(OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_IN);
                        List<Integer> rangeROS = new ArrayList<>();
                        rangeROS.add(7);
                        rangeROS.add(8);
                        fieldConfig.setRuleRangeList(JSON.toJSONString(rangeROS));
                        fieldConfig.setCreateTime(now);
                        fieldConfig.setUpdateTime(now);
                        openAbilityThemeFieldConfigMapper.insertSelective(fieldConfig);
                    });

        });

        openAbilityThemeContentConfigMapper.batchInsert(organizationContentConfigs);
    }

    @Test
    public void testLoad2Redis() {
        openAbilityUtils.load2redis();
    }

    @Test
    public void testOpenApiHeaderCheck() {
        long timestamp = Long.parseLong("1719213031057");
        long current = System.currentTimeMillis();
        if (timestamp > current || current - timestamp > 5000) {
            System.out.println("时间过期，timestamp:" + timestamp + ",current:" + current);
        }

    }

    @Test
    public void testGetOrder(){
        System.out.println("before selectOrderListByHandle:"+System.currentTimeMillis());
//        System.out.println(JSON.toJSONString(orderHandleMapper.selectOrderListByHandle(1,10,null,"110190000000653049",
//                null,null,null,null,null,
//                null,null,null,null,null,null,
//                null,null,null,null,null,null,null,
//                null,null,null,null,null,null,null)));
        System.out.println("after selectOrderListByHandle:"+System.currentTimeMillis());

        List<Integer> orderStatus = new ArrayList<>();
        orderStatus.add(7);

        System.out.println("before selectOrderDetailForOpen:" + System.currentTimeMillis());
        System.out.println("count:" + orderHandleMapper.countOrderForOpen(null,null,null,
                null,orderStatus,null,null,null,null,
                null,null,null,null,null,
                null,null,null,"admin",null,null,null,
                null,null,null,null,null,null));
        System.out.println(JSON.toJSONString(iOrder2CService.getOrderListForOpen(null, null,
                null, orderStatus, null, null, null,
                null, null, null, null, null, null,
                null, null, null, null, null, null, null,
                null, null, null, null,null,10, 1000)));
        System.out.println("after selectOrderDetailForOpen:" + System.currentTimeMillis());

    }

    @Test
    public void testAPI() {
        String url = "http://10.12.6.24:30707/mallos/api/open/order/list";
        String rand = BaseUtils.generateRandomString(16);
        String timestamp = String.valueOf(System.currentTimeMillis());
        String apiKey = "RGYBj6pIxQIV6CQzHtjMVEFv9xilGxG7";
        String appId = "1250135206490095616";
        String sign = BaseUtils.getOpenSign(appId,rand,timestamp,apiKey);
        Map<String,String> headerMap = new LinkedHashMap<>();
        headerMap.put("rand",rand);
        headerMap.put("timestamp",timestamp);
        headerMap.put("appId",appId);
        headerMap.put("sign",sign);

        Map<String,String> urlMap = new LinkedHashMap<>();
        urlMap.put("page","2");
        urlMap.put("num","100");
        urlMap.put("custPhone","12003156701");
        try {
            String response = HttpUtil.get(url,headerMap,urlMap,60000,600000);
            System.out.println("response:" + response);
        } catch (Exception e) {
            System.out.println("Exception:" + e.getMessage());
        }
    }
    @Test
    public void testSyncOrder() {
        String atomOrderId = "1100388448664489984";
        System.out.println("time start:" + System.currentTimeMillis());
        List<OpenAbilityAppRO> appROS = openAbilityUtils.getAppBySubscribe(OpenAbilityConstant.OPEN_ABILITY_THEME_ORDER,
                OpenAbilityConstant.OPEN_ABILITY_THEME_ORDER_SUBSCRIBE_ORDER);
        System.out.println("time getAppBySubscribe:" + System.currentTimeMillis());
        System.out.println("apps:" + JSON.toJSONString(appROS));
        Order2CInfoDetailDTO detailDTO = iOrder2CService.getOrderDetailInternal(atomOrderId).getData();
        System.out.println("time getOrderDetail:" + System.currentTimeMillis());
        System.out.println("atom:" + JSON.toJSONString(detailDTO));
        appROS.forEach(app -> {
            boolean checked = openAbilityUtils.checkDataMatched(detailDTO, app);
            System.out.println("time checkDataMatched:" + System.currentTimeMillis());
            System.out.println("check result:" + checked + "\napp:" + JSON.toJSONString(app));
        });
        System.out.println("time end:" + System.currentTimeMillis());
    }

    @Test
    @Transactional
    @Commit
    public void testTransactionCommit() throws Exception {
        //事务执行完成
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    super.afterCommit();
                    System.out.println("afterCommit");
                }

                @Override
                public void beforeCommit(boolean readOnly) {

                    super.beforeCommit(readOnly);
                    System.out.println("beforeCommit");
                }

                @Override
                public void beforeCompletion() {
                    super.beforeCompletion();
                    System.out.println("beforeCompletion");
                }
            });
        }
    }

}
