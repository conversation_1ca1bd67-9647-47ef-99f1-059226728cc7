package com.chinamobile.iot.sc;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.ProductFlowListParam;
import com.chinamobile.iot.sc.pojo.vo.ProductFlowListVO;
import com.chinamobile.iot.sc.service.MessageCenterService;
import com.chinamobile.iot.sc.service.ProductFlowService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * created by liuxiang on 2024/3/8 15:27
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {IOTApplication.class})
public class MessageTest {

    @Autowired
    private MessageCenterService messageCenterService;


    @Test
    public void testInit(){
        BaseAnswer init = messageCenterService.initModule();
        System.out.println(init);
    }

}
