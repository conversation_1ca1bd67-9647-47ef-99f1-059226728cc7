package com.chinamobile.iot.sc;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.service.IOrder2CService;
import com.chinamobile.iot.sc.service.impl.Order2CRocInfoServiceImpl;
import org.checkerframework.checker.units.qual.A;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * created by liuxiang on 2024/11/26 10:48
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {IOTApplication.class})
public class OrderRocTest {

    @Autowired
    private Order2CRocInfoServiceImpl order2CRocInfoService;

    @Autowired
    private IOrder2CService iOrder2CService;

    @Test
    public void initRocId(){
        BaseAnswer baseAnswer = order2CRocInfoService.initRocId();
        System.out.println(baseAnswer);
    }

    @Test
    public void rocList(){
        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setRoleId("907921766251245568");
//        iOrder2CService.getOrderRocList("990190000001881016",null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,1,5,null,loginIfo4Redis);
    }
}
