package com.chinamobile.iot.sc;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseUtils;
import com.chinamobile.iot.sc.enums.log.ProductManageOperateEnum;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.pojo.param.FlowInstanceListParam;
import com.chinamobile.iot.sc.pojo.param.PlanProductSheflParam;
import com.chinamobile.iot.sc.pojo.param.ShelfSpuSimpleListParam;
import com.chinamobile.iot.sc.pojo.vo.ProductFlowAuditStepListVO;
import com.chinamobile.iot.sc.pojo.vo.ProductFlowInstanceDetailVO;
import com.chinamobile.iot.sc.pojo.vo.ProductStandardListVO;
import com.chinamobile.iot.sc.pojo.vo.ProductTypeListVO;
import com.chinamobile.iot.sc.pojo.vo.productFlowInfo.ShelfSpuDetailVO;
import com.chinamobile.iot.sc.service.ProductFlowInstanceService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.List;

/**
 * created by liuxiang on 2024/3/8 15:27
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {IOTApplication.class})
public class ProductFlowInstanceTest {

    @Autowired
    private ProductFlowInstanceService productFlowInstanceService;

    @Test
    public void testGetProductStandardList(){
        BaseAnswer<List<ProductStandardListVO>> productStandardList = productFlowInstanceService.getProductStandardList();
        System.out.println(JSON.toJSONString(productStandardList));
    }

    @Test
    public void testGetProductTypeList(){
        BaseAnswer<List<ProductTypeListVO>> productTypeList = productFlowInstanceService.getProductTypeList(1);
        System.out.println(JSON.toJSONString(productTypeList));
    }

    @Test
    public void testGetProductOperateList(){
        System.out.println(productFlowInstanceService.getProductOperateList());
    }

    @Test
    public void testGetFlowInstanceList(){
        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setIsAdmin(false);
        loginIfo4Redis.setUserId("1217760567668441088");
        FlowInstanceListParam flowInstanceListParam = new FlowInstanceListParam();
        List<Integer> flowTypeList = new ArrayList<>();
        flowTypeList.add(1);
        flowTypeList.add(2);
        flowInstanceListParam.setFlowTypeList(flowTypeList);
        System.out.println(JSON.toJSONString(productFlowInstanceService.getFlowInstanceList(loginIfo4Redis,flowInstanceListParam)));
    }

    @Test
    public void testGetInstanceDetail(){
        String instanceId = "1347252561910665216";
        BaseAnswer<ProductFlowInstanceDetailVO> instanceDetail = productFlowInstanceService.getInstanceDetail(instanceId,new LoginIfo4Redis(), ProductManageOperateEnum.UPDATE.code);
        System.out.println(JSON.toJSONString(instanceDetail));
    }
    @Test
    public void testGetShelfCategoryList(){
        System.out.println(productFlowInstanceService.getShelfCategoryList());
    }

     @Test
    public void testGetCmiotCostListByCategoryId(){
        System.out.println(productFlowInstanceService.getCmiotCostListByCategoryId("500"));
    }

     @Test
    public void testGetFirstNavigationList(){
        System.out.println(productFlowInstanceService.getFirstNavigationList());
    }

     @Test
    public void testGetSecondNavigationList(){
//        System.out.println(productFlowInstanceService.getSecondNavigationList("300"));
    }

    @Test
    public void navigationData(){
        BaseAnswer<List<ProductFlowInstanceDetailVO.NavigationDirectory>> listBaseAnswer = productFlowInstanceService.navigationData();
        System.out.println(JSON.toJSONString(listBaseAnswer.getData()));
    }

     @Test
    public void testGetAttachmentList(){
        System.out.println(productFlowInstanceService.getAttachmentList("1217780773766012928"));
    }

     @Test
    public void testHasRunningFlowInstance(){
        System.out.println(productFlowInstanceService.hasRunningFlowInstance("1"));
    }

    @Test
    public void testCancelFlow() throws FileNotFoundException {
        String instanceId = "1217886904089960448";
        System.out.println(productFlowInstanceService.cancelFlow(instanceId,new LoginIfo4Redis(),ProductManageOperateEnum.SHELF.code));
    }

    @Test
    public void testGetShelfSpuSimpleList(){
        System.out.println(productFlowInstanceService.getShelfSpuSimpleList(new LoginIfo4Redis(),new ShelfSpuSimpleListParam()));
    }

    @Test
    public void testGetShelfSkuListBySpu() {
        String spuCode = "1234";
        System.out.println(productFlowInstanceService.getShelfSkuListBySpu(spuCode));
    }

    /**
     * 测试实体类是否至少有一个属性有值（空字符串也是无值）
     */
    @Test
    public void testAnyFiledHasValue() {
        PlanProductSheflParam planProductSheflParam = new PlanProductSheflParam();
        planProductSheflParam.setApplicationArea(" ");
        boolean hasValue = BaseUtils.anyFiledHasValue(planProductSheflParam);
        System.out.println(hasValue);
    }

    /*@Test
    public void testDeleteAttachment() {
        BaseAnswer baseAnswer = productFlowInstanceService.deleteAttachment("1227677350935539712");
    }*/

    @Test
    public void  getAuditStepList(){
        BaseAnswer<List<ProductFlowAuditStepListVO>> auditStepList = productFlowInstanceService.getAuditStepList("1255184715386134528");
        System.out.println(JSON.toJSONString(auditStepList.getData()));
    }

    @Test
    public void getShelfSpuDeatail(){
        BaseAnswer<ShelfSpuDetailVO> shelfSpuDeatail = productFlowInstanceService.getShelfSpuDeatail("1359577344945455104", "789", "456");
        System.out.println(JSON.toJSONString(shelfSpuDeatail.getData()));
    }

    @Test
    public void skuExport(){
        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        productFlowInstanceService.skuExport(loginIfo4Redis,"1359577344945455104","789","456","111");
    }



}
