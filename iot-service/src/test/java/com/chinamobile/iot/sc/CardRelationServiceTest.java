package com.chinamobile.iot.sc;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.pojo.CardInfo;
import com.chinamobile.iot.sc.pojo.param.DeliverCardParam;
import com.chinamobile.iot.sc.pojo.param.OrderProductCardRelationListParam;
import com.chinamobile.iot.sc.pojo.vo.CardRalationCountByAreaVO;
import com.chinamobile.iot.sc.pojo.vo.OrderProductCardRelationListVO;
import com.chinamobile.iot.sc.service.CardInfoService;
import com.chinamobile.iot.sc.service.CardRelationService;
import com.chinamobile.iot.sc.service.OsMallSyncService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import javax.smartcardio.Card;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {IOTApplication.class})
public class CardRelationServiceTest {

    @Resource
    private CardRelationService cardRelationService;

    @Resource
    private OsMallSyncService osMallSyncService;

    @Test
    public void orderProductCardRelationList(){
        OrderProductCardRelationListParam param = new OrderProductCardRelationListParam();
//        param.setAtomOrderId("1275887633086451712");
        BaseAnswer<List<OrderProductCardRelationListVO>> listBaseAnswer = cardRelationService.orderProductCardRelationList(param);
        System.out.println(JSON.toJSONString(listBaseAnswer));
    }

    @Test
    public void cardRalationCountByArea(){
        String atomOrderId = "1275887633086451712";
        BaseAnswer<List<CardRalationCountByAreaVO>> listBaseAnswer = cardRelationService.cardRalationCountByArea(atomOrderId);
        System.out.println(JSON.toJSONString(listBaseAnswer.getData()));
    }


    @Test
    public void handleCardInfoFileTest(){
        CardInfo cardInfo = new CardInfo();
        Date now = new Date();
        cardInfo.setId(BaseServiceUtils.getId());
        cardInfo.setCreateTime(now);
        cardInfo.setUpdateTime(now);
        cardInfo.setCustCode("200A9992001802387");
        cardInfo.setCustName("广东揭阳商城集团客户测试用-国防");
        cardInfo.setTemplateId("10000004367123");
        cardInfo.setTemplateName("测试测试");
        cardInfo.setBeId("200");
        cardInfo.setRegionId("6630");

        osMallSyncService.handleCardInfoFile(cardInfo);
    }

}
