package com.chinamobile.iot.sc;

import org.junit.Test;

/**
 * @Author: YSC
 * @Date: 2021/11/15 16:48
 * @Description:
 */
public class InventoryTest {
    @Test
    public void getInventory(){
        /*InventoryInfoRequest inventoryInfoRequest=new InventoryInfoRequest();
        InventoryInfoRequest.InventoryInfo inventoryInfo=new InventoryInfoRequest.InventoryInfo();
        inventoryInfoRequest.setInventoryInfo(inventoryInfo);
        inventoryInfo.setOfferingCode("1000009003");
        IOTRequest iotRequest=new IOTRequest();
        iotRequest.setMessageSeq("0001000009591");
        iotRequest.setRouteType("1");
        iotRequest.setRouteValue("000");
        iotRequest.setLoginSystemCode("");
        iotRequest.setPassword("");
        iotRequest.setRemoteIP("");
        iotRequest.setOperatorId("1617871748813121112");
        iotRequest.setBeId("000");
        iotRequest.setSign("1fa8cda8b94eb4cc87199082b706f6a3");
        iotRequest.setChannelId("30");
        iotRequest.setContent(JSON.toJSONString(inventoryInfoRequest));
        System.out.println(JSON.toJSONString(iotRequest));*/
    }

    @Test
    public void getReserveInventory() {
//        ReserveInventoryRequest inventoryInfoRequest = new ReserveInventoryRequest();
//        ReserveInventoryRequest.InventoryInfo inventoryInfo = new ReserveInventoryRequest.InventoryInfo();
//        inventoryInfoRequest.setInventoryInfo(inventoryInfo);
////        inventoryInfo.setOfferingCode("1000009003");
//        inventoryInfo.setBookId("123456");
////        inventoryInfo.setQuantity("10");
//        IOTRequest iotRequest = new IOTRequest();
//        iotRequest.setMessageSeq("0001000009591");
//        iotRequest.setRouteType("1");
//        iotRequest.setRouteValue("000");
//        iotRequest.setLoginSystemCode("");
//        iotRequest.setPassword("");
//        iotRequest.setRemoteIP("");
//        iotRequest.setOperatorId("1617871748813121112");
//        iotRequest.setBeId("000");
//        iotRequest.setSign("1fa8cda8b94eb4cc87199082b706f6a3");
//        iotRequest.setChannelId("30");
//        iotRequest.setContent(JSON.toJSONString(inventoryInfoRequest));
//        System.out.println(JSON.toJSONString(iotRequest));
    }
}
