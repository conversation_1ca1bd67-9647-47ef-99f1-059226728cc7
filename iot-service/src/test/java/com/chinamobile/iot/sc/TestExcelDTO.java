package com.chinamobile.iot.sc;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import com.chinamobile.iot.sc.pojo.dto.CardXBatchDeliverResultDTO;
import com.chinamobile.iot.sc.pojo.param.DeliverCardParam;
import lombok.Data;

import java.util.List;

/**
 * created by liuxiang on 2024/9/10 16:00
 */
@Data
public class TestExcelDTO {


    @Excel(name = "sku名称",width = 50,needMerge = true)
    private String skuName;


    @Excel(name = "原子名称",width = 50,needMerge = true)
    private String atomName;


    @Excel(name = "数量",width = 50,needMerge = true)
    private String skuQuantity;

    @Excel(name = "备注",width = 50,needMerge = true)
    private String remark;


}
