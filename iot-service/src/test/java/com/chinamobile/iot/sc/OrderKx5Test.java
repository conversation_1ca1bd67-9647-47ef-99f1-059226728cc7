package com.chinamobile.iot.sc;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.service.IOrder2CService;
import com.chinamobile.iot.sc.service.OrderKxH5Service;
import com.chinamobile.iot.sc.service.impl.Order2CRocInfoServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * created by liuxiang on 2024/11/26 10:48
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {IOTApplication.class})
public class OrderKx5Test {

    @Autowired
    private OrderKxH5Service orderKxH5Service;

    @Autowired
    private IOrder2CService iOrder2CService;



    @Test
    public void getNotHandleKxOrderCount(){
        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setRoleId("907998143042379779");
        loginIfo4Redis.setUserId("1085860281685286912");
        loginIfo4Redis.setRoleType("manager");
        orderKxH5Service.getNotHandleKxOrderCount(loginIfo4Redis);
    }
}
