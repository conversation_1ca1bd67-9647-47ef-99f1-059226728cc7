package com.chinamobile.iot.sc.service;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.constant.OrderStatusInnerEnum;
import com.chinamobile.iot.sc.dao.ContractMaterialMapper;
import com.chinamobile.iot.sc.dao.ext.UserRefundKxMapperExt;
import com.chinamobile.iot.sc.dao.handle.InventoryHandlerMapper;
import com.chinamobile.iot.sc.dao.handle.OrderHandleMapper;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.pojo.ContractMaterialExample;
import com.chinamobile.iot.sc.pojo.handle.OrderInfoHandle;
import com.chinamobile.iot.sc.pojo.param.KxCanChooseUserParam;
import com.chinamobile.iot.sc.pojo.vo.KxCanChooseUserVO;
import com.chinamobile.iot.sc.response.iot.ReserveInventoryResponse;
import com.chinamobile.iot.sc.response.iot.ServiceResultInfoResponse;
import com.chinamobile.iot.sc.util.DateUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/17 11:01
 * @description TODO
 */
@SpringBootTest
public class Order2CServiceTest {

    @Resource
    private IOrder2CService order2CService;

    @Resource
    private OrderHandleMapper orderHandleMapper;

    @Resource
    private IProductService productService;

    @Resource
    private UserRefundKxMapperExt userRefundKxMapperExt;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private IInventoryService inventoryService;

    @Resource
    private InventoryHandlerMapper inventoryHandlerMapper;

    @Resource
    private ContractMaterialMapper contractMaterialMapper;
    @Test
    public void testDownloadDictOrderInfo() {
        order2CService.downloadDictOrderInfo();
    }



 /*   @Test
    public void testSpecialAfter(){
        List<OrderInfoHandle> orderInfoHandles = orderHandleMapper.selectOrderListByHandle(1, 10, null, "380100000000518013",
                "", "", null, "",
                "", "", "",
                "", "", "",
                "", "", null,
                "", "", "", null,null,null,null,null,null);
        orderInfoHandles.forEach(x -> {
            // OS上订单状态为交易成功，且商品范式不为合同履约且没有同步K3的订单,尚未进行特殊售后处理,
            // 进行特殊售后处理且取消退款或售后处理到期的订单才允许进行特殊售后
            Integer specialAfterMarketHandleNormal = x.getSpecialAfterMarketHandle();
            String specialAfterStatusNormal = x.getSpecialAfterStatus();
            String syncK3Id = x.getSyncK3Id();
            String specialAfterLatestTimeNormal = x.getSpecialAfterLatestTime();
            boolean allowSpecialAfter = false;
            try {
                allowSpecialAfter = (int) OrderStatusInnerEnum.ORDER_SUCCESS.getStatus() == x.getOrderStatus()
                        && checkAllowSpecialAfter("A07", syncK3Id,
                        specialAfterMarketHandleNormal, specialAfterStatusNormal,specialAfterLatestTimeNormal);
            } catch (ParseException e) {
                System.out.println(e);
            }
            if (allowSpecialAfter) {
                System.out.println(1);
            } else {
                System.out.println(0);
            }
        });
    }*/
    private Boolean checkAllowSpecialAfter(String spuOfferingClass,
                                           String syncK3Id,
                                           Integer specialAfterMarketHandle,
                                           String specialAfterStatus,
                                           String specialAfterLatestTime) throws ParseException {

        boolean dateAfter=false;
        if (StringUtils.isNotEmpty(specialAfterLatestTime)){
            Date specialAfterLatestTimeDate = DateUtils.strToDate(specialAfterLatestTime,DateUtils.DATETIME_FORMAT_NO_SYMBOL);
            Date now = DateUtils.formatDate(new Date(), DateUtils.DATETIME_FORMAT_NO_SYMBOL);
            dateAfter = now.after(specialAfterLatestTimeDate);
        }
        return !SPUOfferingClassEnum.A07.getSpuOfferingClass().equals(spuOfferingClass)
                && StringUtils.isEmpty(syncK3Id)
                && (specialAfterMarketHandle == null || specialAfterMarketHandle == 0
                || (specialAfterMarketHandle == 1 && ("4".equals(specialAfterStatus) || dateAfter)));
    }

    @Test
    public void testSync2COrderInfo(){
//        String txt = "{\"beId\":\"371\",\"channelId\":\"13\",\"content\":\"{\\\"orderInfo\\\":{\\\"remarks\\\":\\\"\\\",\\\"orderId\\\":\\\"40190000000623018\\\",\\\"orderingChannelSource\\\":\\\"019030\\\",\\\"bookId\\\":\\\"\\\",\\\"status\\\":\\\"0\\\",\\\"totalPrice\\\":\\\"M0JGRkYzQjYxNzNFRjA5NA==\\\",\\\"orderStatusTime\\\":\\\"20230616090523\\\",\\\"createTime\\\":\\\"20230616090440\\\",\\\"contactInfo\\\":{\\\"contactPersonName\\\":\\\"QzE3MkY5RTdEQzg0Njg5MjRGOEIxNkQyRjgwRTBEOTI=\\\",\\\"contactPhone\\\":\\\"Qzc3MURENzkxMjgxMDIyOTYwQjk4M0REMUE1MTNCMDg=\\\",\\\"addresstInfo\\\":{\\\"addr1\\\":\\\"MDYyRjgwNkNFNjRENEE1NzRBOEVFQTU3QjEzMTcxQjM=\\\",\\\"addr2\\\":\\\"OTY1MURCRTUzQjE2RkNGNTFENjE5QzU3QzVDNTVGNzU=\\\",\\\"addr3\\\":\\\"QUU0QjdEMDczRTg0NjhEQTk4RTQ5OUE1OUVFMUZFMzQ=\\\",\\\"addr4\\\":\\\"\\\",\\\"usaddr\\\":\\\"QjFCOUE4MDJEOTU2QTUyRTIwMTJGQUUyMEEzQ0Q5RTI=\\\"}},\\\"spuOfferingInfo\\\":{\\\"offeringClass\\\":\\\"A11\\\",\\\"offeringCode\\\":\\\"1000014770\\\",\\\"supplierCode\\\":\\\"96541000111231\\\",\\\"actionType\\\":\\\"A\\\",\\\"skuOfferingInfo\\\":[{\\\"offeringCode\\\":\\\"1000014774\\\",\\\"quantity\\\":1,\\\"cardName\\\":\\\"汪集团洛阳洛龙分团\\\",\\\"msisdn\\\":[\\\"***********\\\"],\\\"actionType\\\":\\\"A\\\",\\\"price\\\":2586000,\\\"atomOfferingInfo\\\":[]}]},\\\"orderType\\\":\\\"01\\\",\\\"couponInfo\\\":[],\\\"custInfo\\\":{\\\"custCode\\\":\\\"MTA3MkFEMjVBOEEzNDU2MEVDNjAxNzE3RTY4MkQ2RTRDNUNGMkI4QzFFQTE3NUYw\\\",\\\"custName\\\":\\\"NDc5QUQxNTFDRkVFQkVCQg==\\\",\\\"beId\\\":\\\"371\\\",\\\"location\\\":\\\"3790\\\",\\\"regionID\\\":\\\"10000553\\\"},\\\"businessCode\\\":\\\"SyncIndividualOrderInfo\\\",\\\"createOperCode\\\":\\\"\\\",\\\"employeeNum\\\":\\\"\\\",\\\"orderOrgBizInfo\\\":{\\\"orderOrgBizCode\\\":\\\"\\\",\\\"orgLevel\\\":\\\"3\\\",\\\"orgName\\\":\\\"河南移动-洛阳-洛阳物联网营销中心\\\"}}}\",\"messageSeq\":\"137110040512\",\"pageSize\":\"\",\"remoteIP\":\"\",\"routeType\":\"1\",\"routeValue\":\"371\",\"sign\":\"767ad261d51e0bbef5c050b107a0ca82\",\"startNum\":\"\",\"totalNum\":\"\"}";
//        String txt = "{\"beId\":\"000\",\"channelId\":\"13\",\"content\":\"{\\\"spuOfferingInfo\\\":{\\\"categoryInfo\\\":{\\\"offeringClass\\\":\\\"A08\\\"},\\\"offeringCode\\\":\\\"111111\\\",\\\"spuOfferingVersion\\\":\\\"V2\\\",\\\"offeringName\\\":\\\"spu版本号V2\\\",\\\"offeringStatus\\\":\\\"0\\\",\\\"operType\\\":\\\"M\\\",\\\"spucreationTime\\\":\\\"20240229154934\\\",\\\"productDescription\\\":\\\"该服务由江苏移动信息系统集成有限公司提供，核心部件为5G无线企业网关L70MB，核心部件质保期1年。\\\",\\\"productLinks\\\":\\\"https://mall.iot.10086.cn/mall/#/resource/offering/1000105089?jump=y\\\",\\\"productImage\\\":\\\"IMAGE_1709192774524984167_20240304094019.jpg\\\",\\\"productLabelInfo\\\":[{\\\"productLabel\\\":\\\"自营\\\"}],\\\"skuOfferingInfo\\\":[{\\\"atomOfferingInfo\\\":[{\\\"offeringCode\\\":\\\"3111111\\\",\\\"atomOfferingVersion\\\":\\\"V2\\\",\\\"atomSalePrice\\\":998000,\\\"quantity\\\":1,\\\"offeringClass\\\":\\\"O\\\",\\\"settlePrice\\\":925000,\\\"unit\\\":\\\"元/人天\\\",\\\"offeringName\\\":\\\"原子商品版本号V2\\\",\\\"offeringSaleRegion\\\":\\\"\\\",\\\"chargeCode\\\":\\\"C_IOT_121048_OneNet平台集成定制服务费\\\",\\\"settleServiceName\\\":\\\"\\\"}],\\\"offeringCode\\\":\\\"2111111\\\",\\\"skuOfferingVersion\\\":\\\"V2\\\",\\\"offeringName\\\":\\\"sku版本号测试V2\\\",\\\"skuOfferingStatus\\\":\\\"0\\\",\\\"skuOfferingStatusTime\\\":\\\"20240304094018\\\",\\\"releaseTargetList\\\":[{\\\"province\\\":\\\"250\\\",\\\"cityList\\\":[\\\"5150\\\",\\\"5120\\\",\\\"5180\\\",\\\"5110\\\",\\\"5270\\\",\\\"5170\\\",\\\"5140\\\",\\\"5230\\\",\\\"5130\\\",\\\"5100\\\",\\\"5190\\\",\\\"5160\\\",\\\"2500\\\",\\\"10000007\\\"]}],\\\"operType\\\":\\\"M\\\",\\\"price\\\":998000,\\\"marketName\\\":\\\"\\\",\\\"marketCode\\\":\\\"\\\",\\\"supplierName\\\":\\\"江苏移动信息系统集成有限公司\\\"}]},\\\"managerInfo\\\":{\\\"operId\\\":\\\"jiangwenhao2\\\"}}\",\"loginSystemCode\":\"\",\"messageSeq\":\"0001000105526\",\"operatorId\":\"1676509931731980169\",\"pageSize\":\"\",\"password\":\"\",\"remoteIP\":\"\",\"routeType\":\"1\",\"routeValue\":\"000\",\"sign\":\"0151245617841faed372a055d6d89648\",\"totalNum\":\"\"}";
//        IOTRequest baseRequest = JSON.parseObject(txt, IOTRequest.class);
//        productService.syncOfferingInfo(baseRequest);

//        String txt = "{\"beId\":\"000\",\"channelId\":\"13\",\"content\":\"{\\\"skuOfferingInfo\\\":[{\\\"atomOfferingInfo\\\":[{\\\"offeringCode\\\":\\\"1000009586\\\",\\\"atomSalePrice\\\":10,\\\"quantity\\\":1,\\\"offeringClass\\\":\\\"S\\\",\\\"extSoftOfferingCode\\\":\\\"905999999999999\\\",\\\"settlePrice\\\":10,\\\"chargeCode\\\":\\\"C_IOT_411011_千里眼软件功能费\\\",\\\"unit\\\":\\\"元/月\\\",\\\"offeringName\\\":\\\"河南专用软件\\\",\\\"offeringSaleRegion\\\":\\\"\\\",\\\"settleServiceName\\\":\\\"\\\"},{\\\"offeringCode\\\":\\\"1000009587\\\",\\\"atomSalePrice\\\":0,\\\"quantity\\\":1,\\\"offeringClass\\\":\\\"H\\\",\\\"extHardOfferingCode\\\":\\\"905999999999999\\\",\\\"settlePrice\\\":10,\\\"color\\\":\\\"白色\\\",\\\"model\\\":\\\"巨大型号\\\",\\\"unit\\\":\\\"元\\\",\\\"offeringName\\\":\\\"河南专用硬件\\\",\\\"offeringSaleRegion\\\":\\\"\\\",\\\"settleServiceName\\\":\\\"\\\"}],\\\"spuOfferingCode\\\":\\\"1000040041\\\",\\\"offeringCode\\\":\\\"1000040067\\\",\\\"offeringClass\\\":\\\"A07\\\",\\\"offeringName\\\":\\\"存量上架未审批\\\",\\\"skuOfferingStatus\\\":\\\"2\\\",\\\"skuOfferingStatusTime\\\":\\\"20230821105245\\\",\\\"releaseTargetList\\\":[{\\\"province\\\":\\\"000\\\"}],\\\"composition\\\":\\\"1\\\",\\\"operType\\\":\\\"M\\\",\\\"price\\\":10,\\\"marketName\\\":\\\"\\\",\\\"marketCode\\\":\\\"\\\",\\\"supplierName\\\":\\\"\\\"}],\\\"managerInfo\\\":{\\\"operId\\\":\\\"cpjlyyl\\\"}}\",\"loginSystemCode\":\"\",\"messageSeq\":\"0001000040221\",\"operatorId\":\"1651741312483320839\",\"pageSize\":\"\",\"password\":\"\",\"remoteIP\":\"\",\"routeType\":\"1\",\"routeValue\":\"000\",\"sign\":\"c53a0704d83e301d9e3f8e603e3bfde2\",\"totalNum\":\"\"}";
//        String txt ="{\"beId\":\"100\",\"channelId\":\"13\",\"content\":\"{\\\"orderInfo\\\":{\\\"remarks\\\":\\\"\\\",\\\"orderId\\\":\\\"880570000002819034\\\",\\\"orderingChannelSource\\\":\\\"019030\\\",\\\"bookId\\\":\\\"\\\",\\\"status\\\":\\\"0\\\",\\\"totalPrice\\\":\\\"Q0Y3NjA2RDMzNTI5MTZFQw==\\\",\\\"orderingChannelName\\\":\\\"移动物联网商城\\\",\\\"orderStatusTime\\\":\\\"20240326104250\\\",\\\"createTime\\\":\\\"20240326104143\\\",\\\"contactInfo\\\":{\\\"contactPersonName\\\":\\\"MkNEMEZCNUYyRkYyRTgwMjg3ODMyMzQyQzc1MDY1ODQ=\\\",\\\"contactPhone\\\":\\\"NEVDOTQzMDlEREJGQkNGMDBDQ0RERTYxNzFFMjc4Rjk=\\\",\\\"addresstInfo\\\":{\\\"addr1\\\":\\\"NTQzODkyNUU0ODk5NzlDNQ==\\\",\\\"addr2\\\":\\\"RjVGNUE4RThGOEU0OTg5MTFENjE5QzU3QzVDNTVGNzU=\\\",\\\"addr3\\\":\\\"NUE0OTVFMkQ1Rjc5OTZBQTk4RTQ5OUE1OUVFMUZFMzQ=\\\",\\\"addr4\\\":\\\"\\\",\\\"usaddr\\\":\\\"OTUzNzExMzlDREIyMEREMTI0MkE2RkVDNTkzQkY4MEQ=\\\"}},\\\"spuOfferingInfo\\\":{\\\"offeringClass\\\":\\\"A11\\\",\\\"offeringCode\\\":\\\"3000131478\\\",\\\"spuOfferingVersion\\\":\\\"V1\\\",\\\"supplierCode\\\":\\\"CMIOT\\\",\\\"actionType\\\":\\\"A\\\",\\\"skuOfferingInfo\\\":[{\\\"offeringCode\\\":\\\"3000131479\\\",\\\"skuOfferingVersion\\\":\\\"V1\\\",\\\"quantity\\\":1,\\\"cardName\\\":\\\"测试测试\\\",\\\"msisdn\\\":[\\\"1440133344368\\\"],\\\"actionType\\\":\\\"A\\\",\\\"price\\\":598000,\\\"atomOfferingInfo\\\":[]}]},\\\"orderType\\\":\\\"01\\\",\\\"couponInfo\\\":[],\\\"custInfo\\\":{\\\"custCode\\\":\\\"MkQxQzJDMzcwMkJGQ0JDODhBMEI4MjQwMTM5NTUyQzNBQ0YyNEY1REM0RUI1NDlD\\\",\\\"custName\\\":\\\"NTQzODkyNUU0ODk5NzlDNQ==\\\",\\\"beId\\\":\\\"100\\\",\\\"location\\\":\\\"1000\\\",\\\"regionID\\\":\\\"\\\"},\\\"businessCode\\\":\\\"SyncIndividualOrderInfo\\\",\\\"createOperCode\\\":\\\"\\\",\\\"employeeNum\\\":\\\"\\\",\\\"orderOrgBizInfo\\\":{\\\"orderOrgBizCode\\\":\\\"6705\\\",\\\"orgLevel\\\":\\\"2\\\",\\\"orgName\\\":\\\"北京移动-城区二分公司\\\"}}}\",\"messageSeq\":\"110030241723\",\"pageSize\":\"\",\"remoteIP\":\"\",\"routeType\":\"1\",\"routeValue\":\"100\",\"sign\":\"1dfd67bea6dbc4c0348ddcf803d4efa9\",\"startNum\":\"\",\"totalNum\":\"\"}";
//        IOTRequest baseRequest = JSON.parseObject(txt, IOTRequest.class);
//        order2CService.sync2COrderInfo(baseRequest);
//        String txt = "{\"beId\":\"000\",\"channelId\":\"13\",\"content\":\"{\\\"atomOfferingInfo\\\":[{\\\"offeringCode\\\":\\\"311111111111\\\",\\\"operType\\\":\\\"M\\\",\\\"atomOfferingVersion\\\":\\\"V5\\\",\\\"atomSalePrice\\\":998000,\\\"quantity\\\":1,\\\"offeringClass\\\":\\\"O\\\",\\\"settlePrice\\\":925000,\\\"unit\\\":\\\"元/人天\\\",\\\"offeringName\\\":\\\"原子商品版本号V4\\\",\\\"offeringSaleRegion\\\":\\\"\\\",\\\"chargeCode\\\":\\\"C_IOT_121048_OneNet平台集成定制服务费\\\",\\\"settleServiceName\\\":\\\"\\\"}],\\\"managerInfo\\\":{\\\"operId\\\":\\\"jiangwenhao2\\\"}}\",\"loginSystemCode\":\"\",\"messageSeq\":\"0001000105526\",\"operatorId\":\"1676509931731980169\",\"pageSize\":\"\",\"password\":\"\",\"remoteIP\":\"\",\"routeType\":\"1\",\"routeValue\":\"000\",\"sign\":\"0151245617841faed372a055d6d89648\",\"totalNum\":\"\"}";
//        IOTRequest baseRequest = JSON.parseObject(txt, IOTRequest.class);
//        productService.syncAtomOfferingInfos(baseRequest);
//        String txt = "{\"beId\":\"000\",\"channelId\":\"13\",\"content\":\"{\\\"atomOfferingInfo\\\":[{\\\"offeringCode\\\":\\\"3111111\\\",\\\"operType\\\":\\\"M\\\",\\\"atomOfferingVersion\\\":\\\"V5\\\",\\\"atomSalePrice\\\":998000,\\\"quantity\\\":1,\\\"offeringClass\\\":\\\"O\\\",\\\"settlePrice\\\":925000,\\\"unit\\\":\\\"元/人天\\\",\\\"offeringName\\\":\\\"原子商品版本号V4\\\",\\\"offeringSaleRegion\\\":\\\"\\\",\\\"chargeCode\\\":\\\"C_IOT_121048_OneNet平台集成定制服务费\\\",\\\"settleServiceName\\\":\\\"\\\"}],\\\"managerInfo\\\":{\\\"operId\\\":\\\"jiangwenhao2\\\"}}\",\"loginSystemCode\":\"\",\"messageSeq\":\"0001000105526\",\"operatorId\":\"1676509931731980169\",\"pageSize\":\"\",\"password\":\"\",\"remoteIP\":\"\",\"routeType\":\"1\",\"routeValue\":\"000\",\"sign\":\"0151245617841faed372a055d6d89648\",\"totalNum\":\"\"}";
        // sku {\"beId\":\"000\",\"channelId\":\"13\",\"content\":\"{\\\"operType\\\":\\\"A\\\",\\\"afterMarketInternalName\\\":\\\"河南千里眼枪机安装服务\\\",\\\"afterMarketExternalName\\\":\\\"河南千里眼枪机安装服务\\\",\\\"afterMarketCode\\\":\\\"AS1000089000\\\",\\\"sellPrice\\\":\\\"0\\\",\\\"settlePrice\\\":\\\"0\\\",\\\"mandatory\\\":\\\"1\\\",\\\"afterMarketType\\\":\\\"3\\\",\\\"offeringinfo\\\":[{\\\"skuOfferingCode\\\":\\\"1000088541\\\"}]}\",\"messageSeq\":\"100014767972\",\"routeType\":\"1\",\"routeValue\":\"000\",\"sign\":\"462a140177d6e5720a185136f45c753b\"}
        // atom       {\"beId\":\"000\",\"channelId\":\"13\",\"content\":\"{\\\"operType\\\":\\\"A\\\",\\\"afterMarketInternalName\\\":\\\"OnePark售后服务包非必选\\\",\\\"afterMarketExternalName\\\":\\\"OnePark售后服务包非必选\\\",\\\"afterMarketCode\\\":\\\"AS1000047936\\\",\\\"sellPrice\\\":\\\"10\\\",\\\"settlePrice\\\":\\\"10\\\",\\\"mandatory\\\":\\\"0\\\",\\\"afterMarketType\\\":\\\"1\\\",\\\"offeringinfo\\\":[{\\\"offeringCode\\\":\\\"1000085860\\\"},{\\\"offeringCode\\\":\\\"1000047734\\\"}]}\",\"messageSeq\":\"100014533471\",\"routeType\":\"1\",\"routeValue\":\"000\",\"sign\":\"caa0257bbacfa627aca4c7dd0a7e6589\"}

        // 订单 {\"beId\":\"371\",\"channelId\":\"13\",\"content\":\"{\\\"serviceOrderId\\\":\\\"910100000003819088\\\",\\\"offeringsOrderId\\\":\\\"910100000003819087\\\",\\\"status\\\":\\\"1\\\",\\\"afterMarketOfferingInfo\\\":[{\\\"afterMarketType\\\":\\\"2\\\",\\\"afterMarketCode\\\":\\\"AS1000061304\\\",\\\"quantity\\\":\\\"1\\\",\\\"offeringinfo\\\":{\\\"spuOfferingCode\\\":\\\"1000061097\\\",\\\"skuOfferingCode\\\":\\\"1000080600\\\"}}]}\",\"messageSeq\":\"137115887652\",\"operatorId\":\"1638155836513310291\",\"regionId\":\"3980\",\"routeType\":\"1\",\"routeValue\":\"371\",\"sign\":\"2e1b87a83a63c2ea533818ffee8776ce\"}
        /*String txt = "{\"beId\":\"000\",\"channelId\":\"13\",\"content\":\"{\\\"operType\\\":\\\"A\\\",\\\"afterMarketInternalName\\\":\\\"1121关键业务售后\\\",\\\"afterMarketExternalName\\\":\\\"1121关键业务售后\\\",\\\"afterMarketCode\\\":\\\"AS1000042362\\\",\\\"afterMarketVersion\\\":\\\"V1\\\",\\\"sellPrice\\\":\\\"0\\\",\\\"settlePrice\\\":\\\"0\\\",\\\"mandatory\\\":\\\"1\\\",\\\"afterMarketType\\\":\\\"1\\\",\\\"offeringinfo\\\":[{\\\"offeringCode\\\":\\\"1000040743\\\",\\\"atomOfferingVersion\\\":\\\"V1\\\"}]}\",\"messageSeq\":\"100030239935\",\"routeType\":\"1\",\"routeValue\":\"000\",\"sign\":\"ed7f5181d9932a765e2136ad831d4e1d\"}";
        IOTRequest baseRequest = JSON.parseObject(txt, IOTRequest.class);
        productService.syncAftermarketOfferingInfos(baseRequest);*/
//        syncAftermarketOfferingInfos
//        String txt = "{\"beId\":\"000\",\"channelId\":\"13\",\"content\":\"{\\\"skuOfferingInfo\\\":[{\\\"atomOfferingInfo\\\":[{\\\"offeringCode\\\":\\\"1000046175\\\",\\\"atomOfferingVersion\\\":\\\"V2\\\",\\\"atomSalePrice\\\":100,\\\"quantity\\\":1,\\\"offeringClass\\\":\\\"F\\\",\\\"settlePrice\\\":100,\\\"chargeCode\\\":\\\"C_IOT_121108_行车卫士平台技术支撑服务费\\\",\\\"unit\\\":\\\"元+\\\",\\\"offeringName\\\":\\\"0311行车卫士原子+\\\",\\\"offeringSaleRegion\\\":\\\"\\\",\\\"settlePricePartner\\\":200,\\\"settleServiceName\\\":\\\"这是结算明细服务名称\\\"}],\\\"spuOfferingCode\\\":\\\"1000046177\\\",\\\"spuOfferingVersion\\\":\\\"V2\\\",\\\"offeringCode\\\":\\\"1000046200\\\",\\\"skuOfferingVersion\\\":\\\"V4\\\",\\\"offeringClass\\\":\\\"A12\\\",\\\"offeringName\\\":\\\"0312行车卫士规格\\\",\\\"skuOfferingStatus\\\":\\\"0\\\",\\\"skuOfferingStatusTime\\\":\\\"20240313110422\\\",\\\"releaseTargetList\\\":[{\\\"province\\\":\\\"000\\\"}],\\\"model\\\":\\\"这是部件型号\\\",\\\"composition\\\":\\\"1\\\",\\\"size\\\":\\\"这是部件尺寸\\\",\\\"operType\\\":\\\"M\\\",\\\"price\\\":100,\\\"unit\\\":\\\"这是计量单位\\\",\\\"marketName\\\":\\\"\\\",\\\"marketCode\\\":\\\"\\\",\\\"supplierName\\\":\\\"yyl勿动\\\"}],\\\"managerInfo\\\":{\\\"operId\\\":\\\"cpjlyyl\\\"}}\",\"loginSystemCode\":\"\",\"messageSeq\":\"0001000046267\",\"operatorId\":\"1651741312483320839\",\"pageSize\":\"\",\"password\":\"\",\"remoteIP\":\"\",\"routeType\":\"1\",\"routeValue\":\"000\",\"sign\":\"a428979b1e6eb0c83ba9820816aac3d4\",\"totalNum\":\"\"}";
//        IOTRequest baseRequest = JSON.parseObject(txt, IOTRequest.class);
//        productService.syncSkuOfferingInfos(baseRequest);
        String txt = "{\"beId\":\"200\",\"channelId\":\"13\",\"content\":\"{\\\"orderInfo\\\":{\\\"orderId\\\":\\\"990190000001892042\\\",\\\"orderingChannelSource\\\":\\\"019030\\\",\\\"status\\\":\\\"4\\\",\\\"orderingChannelName\\\":\\\"商城\\\",\\\"orderStatusTime\\\":\\\"20241211164906\\\",\\\"spuOfferingInfo\\\":{},\\\"orderType\\\":\\\"01\\\",\\\"couponInfo\\\":[],\\\"businessCode\\\":\\\"SyncIndividualOrderInfo\\\"}}\",\"messageSeq\":\"120010143711\",\"operatorId\":\"\",\"pageSize\":\"\",\"remoteIP\":\"\",\"routeType\":\"1\",\"routeValue\":\"200\",\"sign\":\"53eb80df27db6f873ed07326472d12b3\",\"startNum\":\"\",\"totalNum\":\"\"}";
        IOTRequest iotRequest = JSON.parseObject(txt, IOTRequest.class);
        order2CService.sync2COrderInfo(iotRequest);
    }

    @Test
    public void testQueryNoticeUser(){
        KxCanChooseUserParam param = new KxCanChooseUserParam();
        param.setNoticeType(1);
        List<KxCanChooseUserVO> lst = userRefundKxMapperExt.listKxCanChooseUser(param);
        System.out.println(JSON.toJSONString(lst));
    }

    @Test
    public void testReserveInventory(){
        String text = "{\"beId\":\"100\",\"channelId\":\"13\",\"content\":\"{\\\"inventoryInfo\\\":[{\\\"bookId\\\":\\\"190570000002908043\\\",\\\"spuOfferingInfo\\\":[{\\\"spuOfferingCode\\\":\\\"3000133662\\\",\\\"offeringClass\\\":\\\"A04\\\",\\\"skuOfferingInfo\\\":[{\\\"offeringCode\\\":\\\"3000133663\\\",\\\"quantity\\\":1,\\\"atomOfferingInfo\\\":[{\\\"offeringCode\\\":\\\"3000133658\\\",\\\"quantity\\\":1},{\\\"offeringCode\\\":\\\"3000133659\\\",\\\"quantity\\\":1}]}]}]},{\\\"bookId\\\":\\\"190570000002908044\\\",\\\"spuOfferingInfo\\\":[{\\\"spuOfferingCode\\\":\\\"3000133666\\\",\\\"offeringClass\\\":\\\"A09\\\",\\\"skuOfferingInfo\\\":[{\\\"offeringCode\\\":\\\"3000133667\\\",\\\"quantity\\\":1,\\\"atomOfferingInfo\\\":[{\\\"offeringCode\\\":\\\"3000133664\\\",\\\"quantity\\\":1}]}]}]},{\\\"bookId\\\":\\\"190570000002908045\\\",\\\"spuOfferingInfo\\\":[{\\\"spuOfferingCode\\\":\\\"3000133666\\\",\\\"offeringClass\\\":\\\"A09\\\",\\\"skuOfferingInfo\\\":[{\\\"offeringCode\\\":\\\"3000133667\\\",\\\"quantity\\\":1,\\\"atomOfferingInfo\\\":[{\\\"offeringCode\\\":\\\"3000133664\\\",\\\"quantity\\\":1}]}]}]}]}\",\"messageSeq\":\"110030358812\",\"routeType\":\"1\",\"routeValue\":\"100\",\"sign\":\"7db699f69cb5e206470627c7aa6d15e5\"}";
        IOTRequest baseRequest = JSON.parseObject(text, IOTRequest.class);
        IOTAnswer<ReserveInventoryResponse> answer = inventoryService.reserveInventory(baseRequest);
        System.out.println(JSON.toJSONString(answer));
    }

    @Test
    public void testYsxCheck(){
        String text = "{\"beId\":\"591\",\"channelId\":\"13\",\"content\":\"{\\\"serviceNumberInfo\\\":[{\\\"serviceNumber\\\":\\\"NzdGMEFERUU2QUE2MTkwMDYwQjk4M0REMUE1MTNCMDg=\\\"}]}\",\"messageSeq\":\"159110056754\",\"routeType\":\"1\",\"routeValue\":\"591\",\"sign\":\"374391a73608320b7c51641275f4be30\"}";
        IOTRequest baseRequest = JSON.parseObject(text, IOTRequest.class);
        IOTAnswer<ServiceResultInfoResponse> answer = order2CService.syncServiceNumberInfo(baseRequest);
        System.out.println(JSON.toJSONString(answer));
    }

    @Test
    public void testSmartLock(){
        List<String> locks = Arrays.asList("test111","test2222");
        redisUtil.smartLock(locks,()->{
            System.out.println(JSON.toJSONString(locks));
            return null;
        });
    }

    @Test
    public void testUpdateInventory() {
        System.out.println(JSON.toJSONString(contractMaterialMapper.selectByExample(new ContractMaterialExample().createCriteria()
                .andContractNumberEqualTo("64641635416541")
                .andServicePackIdEqualTo("1173576662181236736").andMaterialNumberEqualTo("10508105-0004").example())));

    }

    @Test
    public void testExcelOut(){
        LoginIfo4Redis redis = new LoginIfo4Redis();
        redis.setUserId("123456");
        order2CService.testExcel("1282788249440677888",redis,false,"127.0.0.1");
    }

}
