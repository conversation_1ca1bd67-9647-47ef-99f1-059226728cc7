#服务配置
server:
  port: 9495
  # 开启优雅下线
  shutdown: graceful
spring:
  profiles:
    active: test
  application:
    name: supply-chain-iot-svc77
#apollo apollo.bootstrap.enabled = true
apollo:
  meta: http://10.12.57.1:8080
  autoUpdateInjectedSpringProperties: true
  bootstrap:
    enabled: true
    namespaces: application.yml
#apollo app.id
app:
  id: supply-chain-iot
mybatis:
  mapperLocations: classpath*:mapper/*.xml,classpath*:/mapper/handle/*.xml
  configuration:
    mapUnderscoreToCamelCase: true
# 暴露 shutdown和prometheus 接口
management:
  server:
    port: 8091
  endpoint:
    shutdown:
      enabled: true
    prometheus:
      enabled: true
  endpoints:
    web:
      exposure:
        include: '*'
  metrics:
    export:
      prometheus:
        enabled: true