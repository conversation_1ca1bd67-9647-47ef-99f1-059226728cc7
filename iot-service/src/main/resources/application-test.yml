#feign:
#  client:
#    config:
#      default:
#        connectTimeout: 10000 #单位毫秒
#        readTimeout: 10000 #单位毫秒
#        loggerLevel: FULL
#    url:
#      # 物料系统接口地址
#      # materialSystemUrl: http://172.18.0.29 #正式环境
#      materialSystemUrl: http://10.12.3.28
#      # 统一用户管理平台接口地址
#      ldapManagerUrl: http://10.12.3.28:8080
#      qlyOpenUrl: http://**********
#spring:
#  datasource:
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: ************************************************************************************************************************************
#    username: supply
#    password: app_!QAZxsw2
#    hikari:
#      idle-timeout: 180000
#      minimum-idle: 4
#      max-lifetime: 30000
#      maximum-pool-size: 8
#  redis:
#    cluster:
#      nodes: 10.12.57.3:6381,10.12.57.4:6381,10.12.57.5:6381
#    password: app_!QAZxsw2
#    pool:
#      max-active: 5
#      max-idle: 5
#      max-wait: -1
#      min-idle: 0
#    timeout: 10000
#  jackson:
#    date-format: yyyy-MM-dd HH:mm:ss
#    time-zone: GMT+8
#  kafka:
#    bootstrap-servers: **********:9092,**********:9092,**********0:9092
#    consumer:
#      auto-offset-reset: latest
#      group-id: orderChangeGroups
#      value-deserializer: org.apache.kafka.common.serialization.ByteArrayDeserializer
#    listener:
#      concurrency: 5
#    producer:
#      retries: 3
#      value-serializer: org.apache.kafka.common.serialization.ByteArraySerializer
#  #上传文件配置
#  servlet:
#    multipart:
#      max-file-size: 100MB #文件最大设置
#      max-request-size: 100MB #请求最大设置
#  #注册中心
#  cloud:
#    nacos:
#      discovery:
#        server-addr: **********:8848
#        namespace: test
##日志打印
#logging:
#  level:
#    org.mybatis: debug
#    org.apache.ibatis: debug
#    com.chinamobile.iot.sc: debug
##apollo app.id
#app:
#  id: supply-chain-iot
#
#  # 加密
#supply:
#  des:
#    key: 3D88F1C1AAE7 #用于敏感信息加密
#  sign:
#    secret_key: b244ff421031fde652f9bb66d1486576 # md5用于计算sign
#
##sms 发送短信templateId
#sms:
#  OrderTemplateId: 106067
#  InventoryTemplateId: 106040
#  OrderTemplateId24: 106039
#  OrderTemplateId48: 106038
#  OrderCancelHasInvoice: 106041
#  OrderCancelHasInvoice24: 106042
#  OrderCancelHasInvoice48: 106043
#  OrderCancelNoInvoice: 106044
#  OrderCancelNoInvoice24: 106045
#  OrderCancelNoInvoice48: 106046
#  OrderCancelCardInvoice48: 107392
#  orderCancelBeforeKxCardInvoice48: 107490
#  orderCancelBeforeKxCardInvoice: 107489
#  OrderConfirmId: 106165
#  OrderConfirmId24: 106048
#  OrderConfirmId48: 106049
#  OrderReminderShipmentsId: 106050
#  OrderReminderRefundId: 106051
#  OrderReminderExamineId: 106164
#  CancelOrderTemplateId: 106053
#  ApplyMakeInvoiceTempId: 106054
#  OrderMakeInvoiceId24: 106055
#  OrderMakeInvoiceId48: 106056
#  OrderMakeInvoiceId72: 106057
#  ApplyInvoiceRushTempId: 106058
#  OrderRushInvoiceId24: 106059
#  OrderRushInvoiceId48: 106060
#  OrderRushInvoiceId72: 106061
#  OrderExportTempId: 106437
#  productInitiateTemplateId: 106258
#  productNotPassTemplateId: 106259
#  productPassTemplateId: 106260
#  newProductOnlineInitiateTemplateId: 106261
#  newProductOnlineNotPassTemplateId: 106262
#  newProductOnlinePassTemplateId: 106263
#  newProductOfflineInitiateTemplateId: 106264
#  newProductOfflineNotPassTemplateId: 106265
#  newProductOfflinePassTemplateId: 106266
#  AfterMarketOrderAppointmentTemplateId: 106381
#  AfterMarketOrderDispatchTemplateId: 106382
#  AfterMarketOrderTerminationTemplateId: 106383
#  getOrderFailureTempId: 106847
#  OrderCancel24Time: 1 # 剩余24小时通知 1440
#  OrderCancel48Time: 3 #剩余48小时通知 2880
#  OrderCancel72Time: 5 # 72小时自动处理 4320
#  OrderCheck30Day: 6  #30天自动处理 43200
#  startFinancingTempId: 106966
#  provinceRejectedTempId: 106962
#  planConfirmingTempId: 106964
#  bankPayTempId: 106965
#  authFinancingPassTempId: 106958
#  authFinancingBackTempId: 106959
#  authFinancingRejectTempId: 106960
#  turnOnYsxServiceFailed: 107178
#  inventoryAlarmTempId: 107402
#  productFlowAuditSms: 107473
#  productFlowPassSms: 107474
#  productFlowRejectSms: 107475
#  productFlowKnownSms: 107476
#  smsInventoryKxDeficiencyTemplateId: 107590
#  smsInInventoryKxAlarmTemplateId: 107591
#  smsSoftServiceTemplateId: 107683
#  smsInventoryDeficiencyServiceTemplateId: 107751
#  importKxFail: 108065
#  importKxSucc: 108064
#  importKxErr: 108069
#  importKxDevlierFail: 108071
#  importKxDevlierSucc: 108070
#  importKxDevlierErr: 108069
#  importKxOrderDevlierFail: 108110
#  importKxOrderDevlierSucc: 108109
#  importKxOrderDevlierErr: 108069
#  productFlowShelfCompleteSms: 108147
#  cardInventoryMainTemplateId: 108185
#iot:
#  secretKey: 82E4FE7FE78FE293
#  syncOrdersLogisInfoUrl: https://************:8601/apiaccess/os/v1/orderservice/syncOrdersLogisInfo
#  invoicingResult2IoTMallUrl: https://************:8601/apiaccess/os/v1/invoiceservice/InvoicingResult2IoTMall
#  synOrderRefundResultUrl: https://************:8601/apiaccess/os/v1/orderservice/refundResult
#  invoiceVoidCallbackUrl: https://************:8601/apiaccess/os/v1/invoiceservice/InvoiceVoidCallback
#  confirmReturnOrderUrl: https://************:8601/apiaccess/os/v1/orderservice/confirmReturnOrder
#  serviceOrderResultUrl: https://************:8601/apiaccess/os/v1/orderservice/serviceOrderResult
#  encodeKey: 3D88F1C1AAE7
#  searchMallLinkUrl: https://************:8601/apiaccess/os/v1/linkredirectabilities/operation/searchMallLink
#  loginstatusqueryUrl: https://************:8601/apiaccess/os/v1/thirdpartycapabilities/operation/loginstatusquery
#  openSpecialRefundUrl: https://************:8601/apiaccess/os/v1/orderservice/openSpecialRefund
#  syncOrderCardQueryResultUrl: https://************:8601/apiaccess/os/v1/orderservice/SyncOrderCardQueryResult
#  realNameResultUrl: https:///apiaccess/os/v1/thirdpartycapabilities/operation/realNameResult
#  checkDictInventory: false
#  serviceNumberOrderResult: https://************:8601/apiaccess/os/v1/orderservice/serviceNumberOrderResult
#  allocateCardRequestResultUrl: https://************:8601/apiaccess/os/v1/orderservice/AllocateCardRequestResult
#  qrySubscribersUrl: https://************:8601/apiaccess/os/v1/orderservice/qrySubscribers
#  querySubscriberStatusUrl: https://************:8601/apiaccess/os/v1/orderservice/querySubscriberStatus
#  sm4Key: iotmalltoos24122
#  sm4Iv: iotmalltoos24122
#
#  #iot商城-sftp配置
#  ftp:
#    host: ************  #************* ************** ************* *************
#    port: 19999
#    name: ftpcmiot
#    password: '!QAZ6yhn'
#    workPath: /home/<USER>/iotMall/os/voucher
#    refundPath: /home/<USER>/iotMall/os/refund
#    spuImagePath: /home/<USER>/iotMall/os/Product
#    sftpOrderStatus: /home/<USER>/iotMall/os/orderStatus
#    sftpAccountManagerData: /copftp/ftpiot2os/iotMall/os/operAccountManagerData
#    sftpCustomerData: /copftp/ftpiot2os/iotMall/os/operCustomerData
#    sftpCardInfoPath: /copftp/ftpiot2os/iotMall/os/SyncNumberCardInfos
#    productFlowAttachmentPath: /copftp/ftpiot2os/iotMall/os/Temporarily
#    orderAttachmentPath: /copftp/ftpiot2os/iotMall/os/orderfile
##对象存储
#storage:
#  platform: OneNet #目前支持 Qiniu、OneNet平台
#
##iop商城-sftp配置
#iop:
#  ftp:
#    host: **********
#    port: 22
#    name: sftp_os
#    password: 'Iot@10086'
#    report: /os/report
#    upload: /os/
##oneNET 对象存储
#onenet-storage:
#  queryHttpInner: http://*************:9092/mallos/oss/ #访问代理地址
#  queryHttpOuter: http://**********/mallos/oss/
#  endpoint: http://s3-qos.iot-st-armtest.qiniu-solutions.com
#  bucketName: mallos-test
#  accessKey: W1aowUWsredwHbsuCeLUbI_wXI8_eNJtSWelhbxD
#  secretKey: zht2qc8vrIdCrL50PB5EdFrZSNAApdlOxQ7wZIFD
#
##物流配置
#logistics:
#  customer: 7FBC3DEE91BFA7ACF8F19E423114DC81
#  key: PKxKllGX650
#  queryUrl: https://poll.kuaidi100.com/poll/query.do
#  subscribeUrl: https://poll.kuaidi100.com/poll
#  callbackUrl: https://iotbase.cmcconenet.com:1005/mallos/api/os/express/syncByKD100  #快递100回调地址
#
#h5:
#  loginAddress: http://**********:31330/mallos/orderProcess/login
#
#protype:
#  contract: Hunan,Anhui,Neimenggu,Shanxi1,Shanxi2,Shanghai,Guangdong,Jiangsu,Beijing,Hebei,Guangxi,Tianjin,Jilin,Guizhou,Liaoning,Xizang,Jiangxi,Chongqing,Heilongjiang,Fujian,Shandong,Hainan,Hubei,Xinjiang,Gansu,Sichuan,Zhejang,Ningxia
#  b2b: Henan,Yunnan,Qinghai
#
#sso:
#  loginUrl: http://***************/yzapi_51/api/authenticationExemptLogin
#
#oneKey:
#  loginUrl: http://***************/h5/onekeylogin/testTokenValidate
#  originUrl: http://***************
#financing:
#  privateKey: meecaqaMeMyhkEziP0QcaqyikEziP0Qdaq3ejPaBa7ebbcdkzDlZp0wmampVQz5CM5u29GYrrp3173X5zuYLL/r0/7==
#  financingPublicKey: mfAMeMyhkEziP0QcaqyikEziP0Qdaq3dq7aeFPQrg21YqBO9rcS3VDSZVb0tkHr9gkeBkQbEmzIHkY+5ZH2eugLGEZZU0vRcePxmjnc3Sdg8UHFGVo+65wCGoq==
#  cancelFinancingUrl: http://**********/request-web/iotmall/revokeOrder
#  startFinancingUrl: http://**********/request-web/iotmall/receiveLoanInfo
#  jumpUrl: http://**********/request-web/iotmall/getUrl
#  ftpUserName: iotmall
#  ftpPassword: Iotmall-uat@230717
#  ftpHost: *************
#  ftpPort: 8443
#  ftpWorkPath: /iotmall/files
#  roleId: 1109180984878157830
#scm:
#  importPoDraftUrl: http://**************:8800/scm/api/iot/importPoDraft
#  marketSystem:
#    orderSyncUrl: http://***********:8082/saleCenter-app/externalAuthApi/osMallReceivableInfoSync
#    key: IOTMALL-OS
#    secret: FKhRHW5GR9YYktlYwAsCEcKeRsUHCZ
#    orderSyncUrlB2B: http://***********:8082/saleCenter-app/externalAuthApi/b2bOrderSync
#    marketSystemSecretB2B: FKhRHW5GR9YYktlYwAsCEcKeRsUHCZ
#org:
#  quartz:
#    dataSource:
#      sgs_quartz:
#        URL: *****************************************************************************************************************************************
#        driver: com.mysql.cj.jdbc.Driver
#        maxConnections: 30
#        password: app_!QAZxsw2
#        user: supply
#        validateOnCheckout: true
#        validationQuery: select 1
#    jobStore:
#      class: org.quartz.impl.jdbcjobstore.JobStoreTX
#      clusterCheckinInterval: 20000
#      dataSource: sgs_quartz
#      driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
#      isClustered: true
#      maxMisfiresToHandleAtATime: 1
#      misfireThreshold: 120000
#      selectWithLockSQL: SELECT * FROM {0}LOCKS WHERE LOCK_NAME = ? FOR UPDATE
#      tablePrefix: QRTZ_
#      txIsolationLevelSerializable: true
#      useProperties: true
#    plugin:
#      shutdownhook:
#        class: org.quartz.plugins.management.ShutdownHookPlugin
#        cleanShutdown: true
#      triggHistory:
#        class: org.quartz.plugins.history.LoggingJobHistoryPlugin
#    scheduler:
#      instanceId: AUTO
#      instanceName: smartGasScheduler
#      skipUpdateCheck: true
#    threadPool:
#      class: org.quartz.simpl.SimpleThreadPool
#      threadCount: 10
#      threadPriority: 5
#      threadsInheritContextClassLoaderOfInitializingThread: true
#onelink:
#  appid: 999036test
#  password: Tb2u2I9upzG
#  getTokenUrl: http://************:30016/v5/ec/get/token
#  uploadSimInfoUrl: http://************:30016/v5/inner/upload/sim-info
#  realNameUrl: http://************:30016/v5/inner/secure/sim-real-name-reg
#
##千里眼平台配置
#qly:
#  # 千里眼平台地址
#  qlyOpenUrl: http://**********
#  #千里眼账目项ID
#  chargeCodes: C_IOT_411011
#
##云视讯平台配置
#ysx:
#  # 云视讯平台地址
#  ysxOpenUrl: https://contacts.dashipin.cn
#  #云视讯RSA加密公钥
#  rsa: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjqC5p7s8C/2fPCv3yHd83iKyXT1+8F4FayUGK6mTmP3tlqltQBXvy7xIghItMcjJ+iAwqTMFmLB8VL7IvEN92T0L+lM3lq0+77njxkY1oOz9NfOyUzJtiqbk8TrIoGVbafRrEnyJk9tO58P49okBF7qYO8LnWte+q76XV6sh9aPRtjibjiGNewHAfpm8WNMfypZKF3ByynvxzjETxUxI7x5Dqy17IKaRu6rL+EVmBm4dEWfnhkbA0CZ4usUwA/wOoVx9cJAbOWfEZx7wPyqjb4tpCBhbETrLNlphmulDSVFhMcDCSRMUs31Z8EAuhwddx40MCn2M1bigtnr81NMtWQIDAQAB
#  chargeCodes: C_IOT_211098,C_IOT_211099
#
#jszw:
#  skuOfferingCodes: 3000130952,1000045523,1000045396,1000045441,1000045292,1000009721
#  openUrl: http://***********:9312/orderReceiveCo/receive
#  cancelUrl: http://***********:9312/orderCancelCo/cancel
#qlyNvr:
#  skuOfferingCodes: 1000052490,1000052441
#  newSkuOfferingCodes: 1000052490
#  openUrl: http://**********
#supplier:
#  list:
#    - supplierName: yuantong
#      name: 圆通速递
#    - supplierName: yunda
#      name: 韵达快递
#    - supplierName: zhongtong
#      name: 中通快递
#    - supplierName: shentong
#      name: 申通快递
#    - supplierName: huitongkuaidi
#      name: 百世快递
#    - supplierName: shunfeng
#      name: 顺丰速运
#    - supplierName: youzhengguonei
#      name: 邮政快递包裹
#    - supplierName: jd
#      name: 京东物流
#    - supplierName: debangwuliu
#      name: 德邦
#    - supplierName: zhaijisong
#      name: 宅急送
#    - supplierName: youshuwuliu
#      name: 优速快递
#    - supplierName: kuayue
#      name: 跨越速运
#    - supplierName: suer
#      name: 速尔快递
#    - supplierName: tiantian
#      name: 天天快递
#    - supplierName: guotongkuaidi
#      name: 国通快递
#    - supplierName: kuaijiesudi
#      name: 快捷速递
#    - supplierName: quanyikuaidi
#      name: 全一快递
#    - supplierName: ganzhongnengda
#      name: 能达速递
#    - supplierName: quanfengkuaidi
#      name: 全峰快递
#    - supplierName: rufengda
#      name: 如风达
#    - supplierName: hre
#      name: 高铁速递
#    - supplierName: 99
#      name: 其他
#
##代客下单订单类型
#tocustomer:
#  orderType: 00,02,03
#
#
##行车卫士配置
#carsecurity:
#  baseUrl: https://test.cmobd.com
#  appKey: iot_xcws
#  appKeySecret: 949d3dde-ffee-442e-a20f-0855202b741e
#  softCode: xcws-01-0001,xcws-01-0002,xcws-01-0003,xcws-02-0001,xcws-02-0002,xcws-02-0003,xcws-02-0004
##软件服务配置
#softService:
#  osSecretKey: da729707db7cde6fed5df07166c96cf7
#  sm4Key: 7F480B3D813CEE2478F99E7AA65159F7
#  secretKey: c3d9e7a0e2434e5b84a7a7b5b6a0d8f3
#  qly:
#    openUrl: http://10.12.38.39:9977
#  onenet:
#    openUrl: http://**********:32037
#  xcws:
#    #    openUrl: http://10.12.7.50
#    openUrl: https://test.cmobd.com/lushang/open-api
#  hemu:
#    openUrl: http://111.10.38.128:8181
#  oneCyber:
#    openUrl: https://10.12.6.50:8091
##开票系统配置
#revenue:
#  token:
#    url: http://10.12.3.19:8081/revenue/Sync/token/getToken
#    appKey: es2iz7Dj
#    appSecret: 1e8535db8e80d675cdddb8a421762e17116972ca
#  invoiceApply:
#    url: http://10.12.3.19:8081/revenue/Sync/salesInvoice/invoiceApply
#    isTest: Y
#  getInvoice:
#    url: http://10.12.3.19:8081/revenue/Sync/salesInvoice/getSalesInvoiceData
##省代码配置
#province:
#  code:
#    provinceCodes:
#      - provinceName: 北京
#        provinceCode: Beijing
#      - provinceName: 上海
#        provinceCode: Shanghai
#      - provinceName: 天津
#        provinceCode: Tianjin
#      - provinceName: 重庆
#        provinceCode: Chongqing
#      - provinceName: 河南
#        provinceCode: Henan
#      - provinceName: 河北
#        provinceCode: Hebei
#      - provinceName: 青海
#        provinceCode: Qinghai
#      - provinceName: 安徽
#        provinceCode: Anhui
#      - provinceName: 福建
#        provinceCode: Fujian
#      - provinceName: 广东
#        provinceCode: Guangdong
#      - provinceName: 广西
#        provinceCode: Guangxi
#      - provinceName: 贵州
#        provinceCode: Guizhou
#      - provinceName: 甘肃
#        provinceCode: Gansu
#      - provinceName: 海南
#        provinceCode: Hainan
#      - provinceName: 黑龙江
#        provinceCode: Heilongjiang
#      - provinceName: 湖北
#        provinceCode: Hubei
#      - provinceName: 湖南
#        provinceCode: Hunan
#      - provinceName: 吉林
#        provinceCode: Jilin
#      - provinceName: 江苏
#        provinceCode: Jiangsu
#      - provinceName: 江西
#        provinceCode: Jiangxi
#      - provinceName: 辽宁
#        provinceCode: Liaoning
#      - provinceName: 内蒙古
#        provinceCode: Neimenggu
#      - provinceName: 宁夏
#        provinceCode: Ningxia
#      - provinceName: 陕西
#        provinceCode: Shanxi1
#      - provinceName: 山西
#        provinceCode: Shanxi2
#      - provinceName: 山东
#        provinceCode: Shandong
#      - provinceName: 四川
#        provinceCode: Sichuan
#      - provinceName: 西藏
#        provinceCode: Xizang
#      - provinceName: 新疆
#        provinceCode: Xinjiang
#      - provinceName: 云南
#        provinceCode: Yunnan
#      - provinceName: 浙江
#        provinceCode: Zhejang
#