<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.CouponInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.CouponInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 15:49:28 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="coupon_code" jdbcType="VARCHAR" property="couponCode" />
    <result column="coupon_amount" jdbcType="VARCHAR" property="couponAmount" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="salesman_code" jdbcType="VARCHAR" property="salesmanCode" />
    <result column="salesman_phone" jdbcType="VARCHAR" property="salesmanPhone" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="salesman_name" jdbcType="VARCHAR" property="salesmanName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 15:49:28 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 15:49:28 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 15:49:28 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, coupon_code, coupon_amount, order_id, salesman_code, salesman_phone, employee_id, 
    salesman_name
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.CouponInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 15:49:28 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from coupon_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 15:49:28 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from coupon_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 15:49:28 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from coupon_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.CouponInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 15:49:28 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from coupon_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.CouponInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 15:49:28 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into coupon_info (id, coupon_code, coupon_amount, 
      order_id, salesman_code, salesman_phone, 
      employee_id, salesman_name)
    values (#{id,jdbcType=VARCHAR}, #{couponCode,jdbcType=VARCHAR}, #{couponAmount,jdbcType=VARCHAR}, 
      #{orderId,jdbcType=VARCHAR}, #{salesmanCode,jdbcType=VARCHAR}, #{salesmanPhone,jdbcType=VARCHAR}, 
      #{employeeId,jdbcType=VARCHAR}, #{salesmanName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.CouponInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 15:49:28 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into coupon_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="couponCode != null">
        coupon_code,
      </if>
      <if test="couponAmount != null">
        coupon_amount,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="salesmanCode != null">
        salesman_code,
      </if>
      <if test="salesmanPhone != null">
        salesman_phone,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="salesmanName != null">
        salesman_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="couponCode != null">
        #{couponCode,jdbcType=VARCHAR},
      </if>
      <if test="couponAmount != null">
        #{couponAmount,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="salesmanCode != null">
        #{salesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="salesmanPhone != null">
        #{salesmanPhone,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="salesmanName != null">
        #{salesmanName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.CouponInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 15:49:28 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from coupon_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 15:49:28 CST 2024. by MyBatis Generator, do not modify.
    -->
    update coupon_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.couponCode != null">
        coupon_code = #{record.couponCode,jdbcType=VARCHAR},
      </if>
      <if test="record.couponAmount != null">
        coupon_amount = #{record.couponAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.salesmanCode != null">
        salesman_code = #{record.salesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="record.salesmanPhone != null">
        salesman_phone = #{record.salesmanPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeId != null">
        employee_id = #{record.employeeId,jdbcType=VARCHAR},
      </if>
      <if test="record.salesmanName != null">
        salesman_name = #{record.salesmanName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 15:49:28 CST 2024. by MyBatis Generator, do not modify.
    -->
    update coupon_info
    set id = #{record.id,jdbcType=VARCHAR},
      coupon_code = #{record.couponCode,jdbcType=VARCHAR},
      coupon_amount = #{record.couponAmount,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      salesman_code = #{record.salesmanCode,jdbcType=VARCHAR},
      salesman_phone = #{record.salesmanPhone,jdbcType=VARCHAR},
      employee_id = #{record.employeeId,jdbcType=VARCHAR},
      salesman_name = #{record.salesmanName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.CouponInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 15:49:28 CST 2024. by MyBatis Generator, do not modify.
    -->
    update coupon_info
    <set>
      <if test="couponCode != null">
        coupon_code = #{couponCode,jdbcType=VARCHAR},
      </if>
      <if test="couponAmount != null">
        coupon_amount = #{couponAmount,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="salesmanCode != null">
        salesman_code = #{salesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="salesmanPhone != null">
        salesman_phone = #{salesmanPhone,jdbcType=VARCHAR},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="salesmanName != null">
        salesman_name = #{salesmanName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.CouponInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 15:49:28 CST 2024. by MyBatis Generator, do not modify.
    -->
    update coupon_info
    set coupon_code = #{couponCode,jdbcType=VARCHAR},
      coupon_amount = #{couponAmount,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      salesman_code = #{salesmanCode,jdbcType=VARCHAR},
      salesman_phone = #{salesmanPhone,jdbcType=VARCHAR},
      employee_id = #{employeeId,jdbcType=VARCHAR},
      salesman_name = #{salesmanName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 15:49:28 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into coupon_info
    (id, coupon_code, coupon_amount, order_id, salesman_code, salesman_phone, employee_id, 
      salesman_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.couponCode,jdbcType=VARCHAR}, #{item.couponAmount,jdbcType=VARCHAR}, 
        #{item.orderId,jdbcType=VARCHAR}, #{item.salesmanCode,jdbcType=VARCHAR}, #{item.salesmanPhone,jdbcType=VARCHAR}, 
        #{item.employeeId,jdbcType=VARCHAR}, #{item.salesmanName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 15:49:28 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into coupon_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'coupon_code'.toString() == column.value">
          #{item.couponCode,jdbcType=VARCHAR}
        </if>
        <if test="'coupon_amount'.toString() == column.value">
          #{item.couponAmount,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'salesman_code'.toString() == column.value">
          #{item.salesmanCode,jdbcType=VARCHAR}
        </if>
        <if test="'salesman_phone'.toString() == column.value">
          #{item.salesmanPhone,jdbcType=VARCHAR}
        </if>
        <if test="'employee_id'.toString() == column.value">
          #{item.employeeId,jdbcType=VARCHAR}
        </if>
        <if test="'salesman_name'.toString() == column.value">
          #{item.salesmanName,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>