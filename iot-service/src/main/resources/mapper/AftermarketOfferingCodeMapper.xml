<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.AftermarketOfferingCodeMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.AftermarketOfferingCode">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="after_market_code" jdbcType="VARCHAR" property="afterMarketCode" />
    <result column="sku_offering_code" jdbcType="VARCHAR" property="skuOfferingCode" />
    <result column="offering_code" jdbcType="VARCHAR" property="offeringCode" />
    <result column="spu_offering_code" jdbcType="VARCHAR" property="spuOfferingCode" />
    <result column="spu_offering_class" jdbcType="VARCHAR" property="spuOfferingClass" />
    <result column="hardware_name" jdbcType="VARCHAR" property="hardwareName" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="color" jdbcType="VARCHAR" property="color" />
    <result column="remark1" jdbcType="VARCHAR" property="remark1" />
    <result column="remark2" jdbcType="VARCHAR" property="remark2" />
    <result column="remark3" jdbcType="VARCHAR" property="remark3" />
    <result column="order_take_type" jdbcType="INTEGER" property="orderTakeType" />
    <result column="admin_cooperator_id" jdbcType="VARCHAR" property="adminCooperatorId" />
    <result column="install_manager_id" jdbcType="VARCHAR" property="installManagerId" />
    <result column="province_install_platform" jdbcType="VARCHAR" property="provinceInstallPlatform" />
    <result column="config_time" jdbcType="TIMESTAMP" property="configTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="spu_offering_version" jdbcType="VARCHAR" property="spuOfferingVersion" />
    <result column="sku_offering_version" jdbcType="VARCHAR" property="skuOfferingVersion" />
    <result column="atom_offering_version" jdbcType="VARCHAR" property="atomOfferingVersion" />
    <result column="after_market_version" jdbcType="VARCHAR" property="afterMarketVersion" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, after_market_code, sku_offering_code, offering_code, spu_offering_code, spu_offering_class, 
    hardware_name, model, color, remark1, remark2, remark3, order_take_type, admin_cooperator_id, 
    install_manager_id, province_install_platform, config_time, create_time, update_time, 
    spu_offering_version, sku_offering_version, atom_offering_version, after_market_version
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.AftermarketOfferingCodeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from aftermarket_offering_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from aftermarket_offering_code
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from aftermarket_offering_code
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.AftermarketOfferingCodeExample">
    delete from aftermarket_offering_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.AftermarketOfferingCode">
    insert into aftermarket_offering_code (id, after_market_code, sku_offering_code, 
      offering_code, spu_offering_code, spu_offering_class, 
      hardware_name, model, color, 
      remark1, remark2, remark3, 
      order_take_type, admin_cooperator_id, install_manager_id, 
      province_install_platform, config_time, 
      create_time, update_time, spu_offering_version, 
      sku_offering_version, atom_offering_version, 
      after_market_version)
    values (#{id,jdbcType=VARCHAR}, #{afterMarketCode,jdbcType=VARCHAR}, #{skuOfferingCode,jdbcType=VARCHAR}, 
      #{offeringCode,jdbcType=VARCHAR}, #{spuOfferingCode,jdbcType=VARCHAR}, #{spuOfferingClass,jdbcType=VARCHAR}, 
      #{hardwareName,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, #{color,jdbcType=VARCHAR}, 
      #{remark1,jdbcType=VARCHAR}, #{remark2,jdbcType=VARCHAR}, #{remark3,jdbcType=VARCHAR}, 
      #{orderTakeType,jdbcType=INTEGER}, #{adminCooperatorId,jdbcType=VARCHAR}, #{installManagerId,jdbcType=VARCHAR}, 
      #{provinceInstallPlatform,jdbcType=VARCHAR}, #{configTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{spuOfferingVersion,jdbcType=VARCHAR}, 
      #{skuOfferingVersion,jdbcType=VARCHAR}, #{atomOfferingVersion,jdbcType=VARCHAR}, 
      #{afterMarketVersion,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.AftermarketOfferingCode">
    insert into aftermarket_offering_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="afterMarketCode != null">
        after_market_code,
      </if>
      <if test="skuOfferingCode != null">
        sku_offering_code,
      </if>
      <if test="offeringCode != null">
        offering_code,
      </if>
      <if test="spuOfferingCode != null">
        spu_offering_code,
      </if>
      <if test="spuOfferingClass != null">
        spu_offering_class,
      </if>
      <if test="hardwareName != null">
        hardware_name,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="color != null">
        color,
      </if>
      <if test="remark1 != null">
        remark1,
      </if>
      <if test="remark2 != null">
        remark2,
      </if>
      <if test="remark3 != null">
        remark3,
      </if>
      <if test="orderTakeType != null">
        order_take_type,
      </if>
      <if test="adminCooperatorId != null">
        admin_cooperator_id,
      </if>
      <if test="installManagerId != null">
        install_manager_id,
      </if>
      <if test="provinceInstallPlatform != null">
        province_install_platform,
      </if>
      <if test="configTime != null">
        config_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="spuOfferingVersion != null">
        spu_offering_version,
      </if>
      <if test="skuOfferingVersion != null">
        sku_offering_version,
      </if>
      <if test="atomOfferingVersion != null">
        atom_offering_version,
      </if>
      <if test="afterMarketVersion != null">
        after_market_version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="afterMarketCode != null">
        #{afterMarketCode,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingCode != null">
        #{skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="offeringCode != null">
        #{offeringCode,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingCode != null">
        #{spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingClass != null">
        #{spuOfferingClass,jdbcType=VARCHAR},
      </if>
      <if test="hardwareName != null">
        #{hardwareName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        #{color,jdbcType=VARCHAR},
      </if>
      <if test="remark1 != null">
        #{remark1,jdbcType=VARCHAR},
      </if>
      <if test="remark2 != null">
        #{remark2,jdbcType=VARCHAR},
      </if>
      <if test="remark3 != null">
        #{remark3,jdbcType=VARCHAR},
      </if>
      <if test="orderTakeType != null">
        #{orderTakeType,jdbcType=INTEGER},
      </if>
      <if test="adminCooperatorId != null">
        #{adminCooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="installManagerId != null">
        #{installManagerId,jdbcType=VARCHAR},
      </if>
      <if test="provinceInstallPlatform != null">
        #{provinceInstallPlatform,jdbcType=VARCHAR},
      </if>
      <if test="configTime != null">
        #{configTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="spuOfferingVersion != null">
        #{spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingVersion != null">
        #{skuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingVersion != null">
        #{atomOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="afterMarketVersion != null">
        #{afterMarketVersion,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.AftermarketOfferingCodeExample" resultType="java.lang.Long">
    select count(*) from aftermarket_offering_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update aftermarket_offering_code
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.afterMarketCode != null">
        after_market_code = #{record.afterMarketCode,jdbcType=VARCHAR},
      </if>
      <if test="record.skuOfferingCode != null">
        sku_offering_code = #{record.skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.offeringCode != null">
        offering_code = #{record.offeringCode,jdbcType=VARCHAR},
      </if>
      <if test="record.spuOfferingCode != null">
        spu_offering_code = #{record.spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.spuOfferingClass != null">
        spu_offering_class = #{record.spuOfferingClass,jdbcType=VARCHAR},
      </if>
      <if test="record.hardwareName != null">
        hardware_name = #{record.hardwareName,jdbcType=VARCHAR},
      </if>
      <if test="record.model != null">
        model = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.color != null">
        color = #{record.color,jdbcType=VARCHAR},
      </if>
      <if test="record.remark1 != null">
        remark1 = #{record.remark1,jdbcType=VARCHAR},
      </if>
      <if test="record.remark2 != null">
        remark2 = #{record.remark2,jdbcType=VARCHAR},
      </if>
      <if test="record.remark3 != null">
        remark3 = #{record.remark3,jdbcType=VARCHAR},
      </if>
      <if test="record.orderTakeType != null">
        order_take_type = #{record.orderTakeType,jdbcType=INTEGER},
      </if>
      <if test="record.adminCooperatorId != null">
        admin_cooperator_id = #{record.adminCooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.installManagerId != null">
        install_manager_id = #{record.installManagerId,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceInstallPlatform != null">
        province_install_platform = #{record.provinceInstallPlatform,jdbcType=VARCHAR},
      </if>
      <if test="record.configTime != null">
        config_time = #{record.configTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.spuOfferingVersion != null">
        spu_offering_version = #{record.spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.skuOfferingVersion != null">
        sku_offering_version = #{record.skuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOfferingVersion != null">
        atom_offering_version = #{record.atomOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.afterMarketVersion != null">
        after_market_version = #{record.afterMarketVersion,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update aftermarket_offering_code
    set id = #{record.id,jdbcType=VARCHAR},
      after_market_code = #{record.afterMarketCode,jdbcType=VARCHAR},
      sku_offering_code = #{record.skuOfferingCode,jdbcType=VARCHAR},
      offering_code = #{record.offeringCode,jdbcType=VARCHAR},
      spu_offering_code = #{record.spuOfferingCode,jdbcType=VARCHAR},
      spu_offering_class = #{record.spuOfferingClass,jdbcType=VARCHAR},
      hardware_name = #{record.hardwareName,jdbcType=VARCHAR},
      model = #{record.model,jdbcType=VARCHAR},
      color = #{record.color,jdbcType=VARCHAR},
      remark1 = #{record.remark1,jdbcType=VARCHAR},
      remark2 = #{record.remark2,jdbcType=VARCHAR},
      remark3 = #{record.remark3,jdbcType=VARCHAR},
      order_take_type = #{record.orderTakeType,jdbcType=INTEGER},
      admin_cooperator_id = #{record.adminCooperatorId,jdbcType=VARCHAR},
      install_manager_id = #{record.installManagerId,jdbcType=VARCHAR},
      province_install_platform = #{record.provinceInstallPlatform,jdbcType=VARCHAR},
      config_time = #{record.configTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      spu_offering_version = #{record.spuOfferingVersion,jdbcType=VARCHAR},
      sku_offering_version = #{record.skuOfferingVersion,jdbcType=VARCHAR},
      atom_offering_version = #{record.atomOfferingVersion,jdbcType=VARCHAR},
      after_market_version = #{record.afterMarketVersion,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.AftermarketOfferingCode">
    update aftermarket_offering_code
    <set>
      <if test="afterMarketCode != null">
        after_market_code = #{afterMarketCode,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingCode != null">
        sku_offering_code = #{skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="offeringCode != null">
        offering_code = #{offeringCode,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingCode != null">
        spu_offering_code = #{spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingClass != null">
        spu_offering_class = #{spuOfferingClass,jdbcType=VARCHAR},
      </if>
      <if test="hardwareName != null">
        hardware_name = #{hardwareName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        color = #{color,jdbcType=VARCHAR},
      </if>
      <if test="remark1 != null">
        remark1 = #{remark1,jdbcType=VARCHAR},
      </if>
      <if test="remark2 != null">
        remark2 = #{remark2,jdbcType=VARCHAR},
      </if>
      <if test="remark3 != null">
        remark3 = #{remark3,jdbcType=VARCHAR},
      </if>
      <if test="orderTakeType != null">
        order_take_type = #{orderTakeType,jdbcType=INTEGER},
      </if>
      <if test="adminCooperatorId != null">
        admin_cooperator_id = #{adminCooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="installManagerId != null">
        install_manager_id = #{installManagerId,jdbcType=VARCHAR},
      </if>
      <if test="provinceInstallPlatform != null">
        province_install_platform = #{provinceInstallPlatform,jdbcType=VARCHAR},
      </if>
      <if test="configTime != null">
        config_time = #{configTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="spuOfferingVersion != null">
        spu_offering_version = #{spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingVersion != null">
        sku_offering_version = #{skuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingVersion != null">
        atom_offering_version = #{atomOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="afterMarketVersion != null">
        after_market_version = #{afterMarketVersion,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.AftermarketOfferingCode">
    update aftermarket_offering_code
    set after_market_code = #{afterMarketCode,jdbcType=VARCHAR},
      sku_offering_code = #{skuOfferingCode,jdbcType=VARCHAR},
      offering_code = #{offeringCode,jdbcType=VARCHAR},
      spu_offering_code = #{spuOfferingCode,jdbcType=VARCHAR},
      spu_offering_class = #{spuOfferingClass,jdbcType=VARCHAR},
      hardware_name = #{hardwareName,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      color = #{color,jdbcType=VARCHAR},
      remark1 = #{remark1,jdbcType=VARCHAR},
      remark2 = #{remark2,jdbcType=VARCHAR},
      remark3 = #{remark3,jdbcType=VARCHAR},
      order_take_type = #{orderTakeType,jdbcType=INTEGER},
      admin_cooperator_id = #{adminCooperatorId,jdbcType=VARCHAR},
      install_manager_id = #{installManagerId,jdbcType=VARCHAR},
      province_install_platform = #{provinceInstallPlatform,jdbcType=VARCHAR},
      config_time = #{configTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      spu_offering_version = #{spuOfferingVersion,jdbcType=VARCHAR},
      sku_offering_version = #{skuOfferingVersion,jdbcType=VARCHAR},
      atom_offering_version = #{atomOfferingVersion,jdbcType=VARCHAR},
      after_market_version = #{afterMarketVersion,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into aftermarket_offering_code
    (id, after_market_code, sku_offering_code, offering_code, spu_offering_code, spu_offering_class, 
      hardware_name, model, color, remark1, remark2, remark3, order_take_type, admin_cooperator_id, 
      install_manager_id, province_install_platform, config_time, create_time, update_time, 
      spu_offering_version, sku_offering_version, atom_offering_version, after_market_version
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.afterMarketCode,jdbcType=VARCHAR}, #{item.skuOfferingCode,jdbcType=VARCHAR}, 
        #{item.offeringCode,jdbcType=VARCHAR}, #{item.spuOfferingCode,jdbcType=VARCHAR}, 
        #{item.spuOfferingClass,jdbcType=VARCHAR}, #{item.hardwareName,jdbcType=VARCHAR}, 
        #{item.model,jdbcType=VARCHAR}, #{item.color,jdbcType=VARCHAR}, #{item.remark1,jdbcType=VARCHAR}, 
        #{item.remark2,jdbcType=VARCHAR}, #{item.remark3,jdbcType=VARCHAR}, #{item.orderTakeType,jdbcType=INTEGER}, 
        #{item.adminCooperatorId,jdbcType=VARCHAR}, #{item.installManagerId,jdbcType=VARCHAR}, 
        #{item.provinceInstallPlatform,jdbcType=VARCHAR}, #{item.configTime,jdbcType=TIMESTAMP}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.spuOfferingVersion,jdbcType=VARCHAR}, 
        #{item.skuOfferingVersion,jdbcType=VARCHAR}, #{item.atomOfferingVersion,jdbcType=VARCHAR}, 
        #{item.afterMarketVersion,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into aftermarket_offering_code (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'after_market_code'.toString() == column.value">
          #{item.afterMarketCode,jdbcType=VARCHAR}
        </if>
        <if test="'sku_offering_code'.toString() == column.value">
          #{item.skuOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'offering_code'.toString() == column.value">
          #{item.offeringCode,jdbcType=VARCHAR}
        </if>
        <if test="'spu_offering_code'.toString() == column.value">
          #{item.spuOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'spu_offering_class'.toString() == column.value">
          #{item.spuOfferingClass,jdbcType=VARCHAR}
        </if>
        <if test="'hardware_name'.toString() == column.value">
          #{item.hardwareName,jdbcType=VARCHAR}
        </if>
        <if test="'model'.toString() == column.value">
          #{item.model,jdbcType=VARCHAR}
        </if>
        <if test="'color'.toString() == column.value">
          #{item.color,jdbcType=VARCHAR}
        </if>
        <if test="'remark1'.toString() == column.value">
          #{item.remark1,jdbcType=VARCHAR}
        </if>
        <if test="'remark2'.toString() == column.value">
          #{item.remark2,jdbcType=VARCHAR}
        </if>
        <if test="'remark3'.toString() == column.value">
          #{item.remark3,jdbcType=VARCHAR}
        </if>
        <if test="'order_take_type'.toString() == column.value">
          #{item.orderTakeType,jdbcType=INTEGER}
        </if>
        <if test="'admin_cooperator_id'.toString() == column.value">
          #{item.adminCooperatorId,jdbcType=VARCHAR}
        </if>
        <if test="'install_manager_id'.toString() == column.value">
          #{item.installManagerId,jdbcType=VARCHAR}
        </if>
        <if test="'province_install_platform'.toString() == column.value">
          #{item.provinceInstallPlatform,jdbcType=VARCHAR}
        </if>
        <if test="'config_time'.toString() == column.value">
          #{item.configTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'spu_offering_version'.toString() == column.value">
          #{item.spuOfferingVersion,jdbcType=VARCHAR}
        </if>
        <if test="'sku_offering_version'.toString() == column.value">
          #{item.skuOfferingVersion,jdbcType=VARCHAR}
        </if>
        <if test="'atom_offering_version'.toString() == column.value">
          #{item.atomOfferingVersion,jdbcType=VARCHAR}
        </if>
        <if test="'after_market_version'.toString() == column.value">
          #{item.afterMarketVersion,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>