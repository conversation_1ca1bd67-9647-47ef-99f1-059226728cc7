<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.NewProductRequestManageMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.NewProductRequestManage">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="request_no" jdbcType="VARCHAR" property="requestNo" />
    <result column="spu_offering_name" jdbcType="VARCHAR" property="spuOfferingName" />
    <result column="spu_offering_code" jdbcType="VARCHAR" property="spuOfferingCode" />
    <result column="sku_offering_name" jdbcType="VARCHAR" property="skuOfferingName" />
    <result column="sku_offering_code" jdbcType="VARCHAR" property="skuOfferingCode" />
    <result column="request_status" jdbcType="INTEGER" property="requestStatus" />
    <result column="online_status" jdbcType="VARCHAR" property="onlineStatus" />
    <result column="online_offline_request_status" jdbcType="INTEGER" property="onlineOfflineRequestStatus" />
    <result column="cooperator_id" jdbcType="VARCHAR" property="cooperatorId" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="color" jdbcType="VARCHAR" property="color" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="network_property" jdbcType="VARCHAR" property="networkProperty" />
    <result column="application_domain" jdbcType="VARCHAR" property="applicationDomain" />
    <result column="product_introduction" jdbcType="VARCHAR" property="productIntroduction" />
    <result column="product_sale_content" jdbcType="VARCHAR" property="productSaleContent" />
    <result column="product_sale_area_code" jdbcType="VARCHAR" property="productSaleAreaCode" />
    <result column="product_sale_area" jdbcType="VARCHAR" property="productSaleArea" />
    <result column="supply_price" jdbcType="DECIMAL" property="supplyPrice" />
    <result column="market_price" jdbcType="DECIMAL" property="marketPrice" />
    <result column="sale_price" jdbcType="DECIMAL" property="salePrice" />
    <result column="product_manager_name" jdbcType="VARCHAR" property="productManagerName" />
    <result column="product_manager_phone" jdbcType="VARCHAR" property="productManagerPhone" />
    <result column="product_manager_email" jdbcType="VARCHAR" property="productManagerEmail" />
    <result column="request_current_handler_user_id" jdbcType="VARCHAR" property="requestCurrentHandlerUserId" />
    <result column="online_offline_current_handler_user_id" jdbcType="VARCHAR" property="onlineOfflineCurrentHandlerUserId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, request_no, spu_offering_name, spu_offering_code, sku_offering_name, sku_offering_code, 
    request_status, online_status, online_offline_request_status, cooperator_id, brand, 
    model, color, material_code, network_property, application_domain, product_introduction, 
    product_sale_content, product_sale_area_code, product_sale_area, supply_price, market_price, 
    sale_price, product_manager_name, product_manager_phone, product_manager_email, request_current_handler_user_id, 
    online_offline_current_handler_user_id, creator, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestManageExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from new_product_request_manage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from new_product_request_manage
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from new_product_request_manage
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestManageExample">
    delete from new_product_request_manage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestManage">
    insert into new_product_request_manage (id, request_no, spu_offering_name, 
      spu_offering_code, sku_offering_name, sku_offering_code, 
      request_status, online_status, online_offline_request_status, 
      cooperator_id, brand, model, 
      color, material_code, network_property, 
      application_domain, product_introduction, 
      product_sale_content, product_sale_area_code, 
      product_sale_area, supply_price, market_price, 
      sale_price, product_manager_name, product_manager_phone, 
      product_manager_email, request_current_handler_user_id, 
      online_offline_current_handler_user_id, creator, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{requestNo,jdbcType=VARCHAR}, #{spuOfferingName,jdbcType=VARCHAR}, 
      #{spuOfferingCode,jdbcType=VARCHAR}, #{skuOfferingName,jdbcType=VARCHAR}, #{skuOfferingCode,jdbcType=VARCHAR}, 
      #{requestStatus,jdbcType=INTEGER}, #{onlineStatus,jdbcType=VARCHAR}, #{onlineOfflineRequestStatus,jdbcType=INTEGER}, 
      #{cooperatorId,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, 
      #{color,jdbcType=VARCHAR}, #{materialCode,jdbcType=VARCHAR}, #{networkProperty,jdbcType=VARCHAR}, 
      #{applicationDomain,jdbcType=VARCHAR}, #{productIntroduction,jdbcType=VARCHAR}, 
      #{productSaleContent,jdbcType=VARCHAR}, #{productSaleAreaCode,jdbcType=VARCHAR}, 
      #{productSaleArea,jdbcType=VARCHAR}, #{supplyPrice,jdbcType=DECIMAL}, #{marketPrice,jdbcType=DECIMAL}, 
      #{salePrice,jdbcType=DECIMAL}, #{productManagerName,jdbcType=VARCHAR}, #{productManagerPhone,jdbcType=VARCHAR}, 
      #{productManagerEmail,jdbcType=VARCHAR}, #{requestCurrentHandlerUserId,jdbcType=VARCHAR}, 
      #{onlineOfflineCurrentHandlerUserId,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestManage">
    insert into new_product_request_manage
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="requestNo != null">
        request_no,
      </if>
      <if test="spuOfferingName != null">
        spu_offering_name,
      </if>
      <if test="spuOfferingCode != null">
        spu_offering_code,
      </if>
      <if test="skuOfferingName != null">
        sku_offering_name,
      </if>
      <if test="skuOfferingCode != null">
        sku_offering_code,
      </if>
      <if test="requestStatus != null">
        request_status,
      </if>
      <if test="onlineStatus != null">
        online_status,
      </if>
      <if test="onlineOfflineRequestStatus != null">
        online_offline_request_status,
      </if>
      <if test="cooperatorId != null">
        cooperator_id,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="color != null">
        color,
      </if>
      <if test="materialCode != null">
        material_code,
      </if>
      <if test="networkProperty != null">
        network_property,
      </if>
      <if test="applicationDomain != null">
        application_domain,
      </if>
      <if test="productIntroduction != null">
        product_introduction,
      </if>
      <if test="productSaleContent != null">
        product_sale_content,
      </if>
      <if test="productSaleAreaCode != null">
        product_sale_area_code,
      </if>
      <if test="productSaleArea != null">
        product_sale_area,
      </if>
      <if test="supplyPrice != null">
        supply_price,
      </if>
      <if test="marketPrice != null">
        market_price,
      </if>
      <if test="salePrice != null">
        sale_price,
      </if>
      <if test="productManagerName != null">
        product_manager_name,
      </if>
      <if test="productManagerPhone != null">
        product_manager_phone,
      </if>
      <if test="productManagerEmail != null">
        product_manager_email,
      </if>
      <if test="requestCurrentHandlerUserId != null">
        request_current_handler_user_id,
      </if>
      <if test="onlineOfflineCurrentHandlerUserId != null">
        online_offline_current_handler_user_id,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="requestNo != null">
        #{requestNo,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingName != null">
        #{spuOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingCode != null">
        #{spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingName != null">
        #{skuOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingCode != null">
        #{skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="requestStatus != null">
        #{requestStatus,jdbcType=INTEGER},
      </if>
      <if test="onlineStatus != null">
        #{onlineStatus,jdbcType=VARCHAR},
      </if>
      <if test="onlineOfflineRequestStatus != null">
        #{onlineOfflineRequestStatus,jdbcType=INTEGER},
      </if>
      <if test="cooperatorId != null">
        #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        #{color,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="networkProperty != null">
        #{networkProperty,jdbcType=VARCHAR},
      </if>
      <if test="applicationDomain != null">
        #{applicationDomain,jdbcType=VARCHAR},
      </if>
      <if test="productIntroduction != null">
        #{productIntroduction,jdbcType=VARCHAR},
      </if>
      <if test="productSaleContent != null">
        #{productSaleContent,jdbcType=VARCHAR},
      </if>
      <if test="productSaleAreaCode != null">
        #{productSaleAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="productSaleArea != null">
        #{productSaleArea,jdbcType=VARCHAR},
      </if>
      <if test="supplyPrice != null">
        #{supplyPrice,jdbcType=DECIMAL},
      </if>
      <if test="marketPrice != null">
        #{marketPrice,jdbcType=DECIMAL},
      </if>
      <if test="salePrice != null">
        #{salePrice,jdbcType=DECIMAL},
      </if>
      <if test="productManagerName != null">
        #{productManagerName,jdbcType=VARCHAR},
      </if>
      <if test="productManagerPhone != null">
        #{productManagerPhone,jdbcType=VARCHAR},
      </if>
      <if test="productManagerEmail != null">
        #{productManagerEmail,jdbcType=VARCHAR},
      </if>
      <if test="requestCurrentHandlerUserId != null">
        #{requestCurrentHandlerUserId,jdbcType=VARCHAR},
      </if>
      <if test="onlineOfflineCurrentHandlerUserId != null">
        #{onlineOfflineCurrentHandlerUserId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestManageExample" resultType="java.lang.Long">
    select count(*) from new_product_request_manage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update new_product_request_manage
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.requestNo != null">
        request_no = #{record.requestNo,jdbcType=VARCHAR},
      </if>
      <if test="record.spuOfferingName != null">
        spu_offering_name = #{record.spuOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="record.spuOfferingCode != null">
        spu_offering_code = #{record.spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.skuOfferingName != null">
        sku_offering_name = #{record.skuOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="record.skuOfferingCode != null">
        sku_offering_code = #{record.skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.requestStatus != null">
        request_status = #{record.requestStatus,jdbcType=INTEGER},
      </if>
      <if test="record.onlineStatus != null">
        online_status = #{record.onlineStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.onlineOfflineRequestStatus != null">
        online_offline_request_status = #{record.onlineOfflineRequestStatus,jdbcType=INTEGER},
      </if>
      <if test="record.cooperatorId != null">
        cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.brand != null">
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.model != null">
        model = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.color != null">
        color = #{record.color,jdbcType=VARCHAR},
      </if>
      <if test="record.materialCode != null">
        material_code = #{record.materialCode,jdbcType=VARCHAR},
      </if>
      <if test="record.networkProperty != null">
        network_property = #{record.networkProperty,jdbcType=VARCHAR},
      </if>
      <if test="record.applicationDomain != null">
        application_domain = #{record.applicationDomain,jdbcType=VARCHAR},
      </if>
      <if test="record.productIntroduction != null">
        product_introduction = #{record.productIntroduction,jdbcType=VARCHAR},
      </if>
      <if test="record.productSaleContent != null">
        product_sale_content = #{record.productSaleContent,jdbcType=VARCHAR},
      </if>
      <if test="record.productSaleAreaCode != null">
        product_sale_area_code = #{record.productSaleAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="record.productSaleArea != null">
        product_sale_area = #{record.productSaleArea,jdbcType=VARCHAR},
      </if>
      <if test="record.supplyPrice != null">
        supply_price = #{record.supplyPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.marketPrice != null">
        market_price = #{record.marketPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.salePrice != null">
        sale_price = #{record.salePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.productManagerName != null">
        product_manager_name = #{record.productManagerName,jdbcType=VARCHAR},
      </if>
      <if test="record.productManagerPhone != null">
        product_manager_phone = #{record.productManagerPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.productManagerEmail != null">
        product_manager_email = #{record.productManagerEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.requestCurrentHandlerUserId != null">
        request_current_handler_user_id = #{record.requestCurrentHandlerUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.onlineOfflineCurrentHandlerUserId != null">
        online_offline_current_handler_user_id = #{record.onlineOfflineCurrentHandlerUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update new_product_request_manage
    set id = #{record.id,jdbcType=VARCHAR},
      request_no = #{record.requestNo,jdbcType=VARCHAR},
      spu_offering_name = #{record.spuOfferingName,jdbcType=VARCHAR},
      spu_offering_code = #{record.spuOfferingCode,jdbcType=VARCHAR},
      sku_offering_name = #{record.skuOfferingName,jdbcType=VARCHAR},
      sku_offering_code = #{record.skuOfferingCode,jdbcType=VARCHAR},
      request_status = #{record.requestStatus,jdbcType=INTEGER},
      online_status = #{record.onlineStatus,jdbcType=VARCHAR},
      online_offline_request_status = #{record.onlineOfflineRequestStatus,jdbcType=INTEGER},
      cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      brand = #{record.brand,jdbcType=VARCHAR},
      model = #{record.model,jdbcType=VARCHAR},
      color = #{record.color,jdbcType=VARCHAR},
      material_code = #{record.materialCode,jdbcType=VARCHAR},
      network_property = #{record.networkProperty,jdbcType=VARCHAR},
      application_domain = #{record.applicationDomain,jdbcType=VARCHAR},
      product_introduction = #{record.productIntroduction,jdbcType=VARCHAR},
      product_sale_content = #{record.productSaleContent,jdbcType=VARCHAR},
      product_sale_area_code = #{record.productSaleAreaCode,jdbcType=VARCHAR},
      product_sale_area = #{record.productSaleArea,jdbcType=VARCHAR},
      supply_price = #{record.supplyPrice,jdbcType=DECIMAL},
      market_price = #{record.marketPrice,jdbcType=DECIMAL},
      sale_price = #{record.salePrice,jdbcType=DECIMAL},
      product_manager_name = #{record.productManagerName,jdbcType=VARCHAR},
      product_manager_phone = #{record.productManagerPhone,jdbcType=VARCHAR},
      product_manager_email = #{record.productManagerEmail,jdbcType=VARCHAR},
      request_current_handler_user_id = #{record.requestCurrentHandlerUserId,jdbcType=VARCHAR},
      online_offline_current_handler_user_id = #{record.onlineOfflineCurrentHandlerUserId,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestManage">
    update new_product_request_manage
    <set>
      <if test="requestNo != null">
        request_no = #{requestNo,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingName != null">
        spu_offering_name = #{spuOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingCode != null">
        spu_offering_code = #{spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingName != null">
        sku_offering_name = #{skuOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingCode != null">
        sku_offering_code = #{skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="requestStatus != null">
        request_status = #{requestStatus,jdbcType=INTEGER},
      </if>
      <if test="onlineStatus != null">
        online_status = #{onlineStatus,jdbcType=VARCHAR},
      </if>
      <if test="onlineOfflineRequestStatus != null">
        online_offline_request_status = #{onlineOfflineRequestStatus,jdbcType=INTEGER},
      </if>
      <if test="cooperatorId != null">
        cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        color = #{color,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        material_code = #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="networkProperty != null">
        network_property = #{networkProperty,jdbcType=VARCHAR},
      </if>
      <if test="applicationDomain != null">
        application_domain = #{applicationDomain,jdbcType=VARCHAR},
      </if>
      <if test="productIntroduction != null">
        product_introduction = #{productIntroduction,jdbcType=VARCHAR},
      </if>
      <if test="productSaleContent != null">
        product_sale_content = #{productSaleContent,jdbcType=VARCHAR},
      </if>
      <if test="productSaleAreaCode != null">
        product_sale_area_code = #{productSaleAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="productSaleArea != null">
        product_sale_area = #{productSaleArea,jdbcType=VARCHAR},
      </if>
      <if test="supplyPrice != null">
        supply_price = #{supplyPrice,jdbcType=DECIMAL},
      </if>
      <if test="marketPrice != null">
        market_price = #{marketPrice,jdbcType=DECIMAL},
      </if>
      <if test="salePrice != null">
        sale_price = #{salePrice,jdbcType=DECIMAL},
      </if>
      <if test="productManagerName != null">
        product_manager_name = #{productManagerName,jdbcType=VARCHAR},
      </if>
      <if test="productManagerPhone != null">
        product_manager_phone = #{productManagerPhone,jdbcType=VARCHAR},
      </if>
      <if test="productManagerEmail != null">
        product_manager_email = #{productManagerEmail,jdbcType=VARCHAR},
      </if>
      <if test="requestCurrentHandlerUserId != null">
        request_current_handler_user_id = #{requestCurrentHandlerUserId,jdbcType=VARCHAR},
      </if>
      <if test="onlineOfflineCurrentHandlerUserId != null">
        online_offline_current_handler_user_id = #{onlineOfflineCurrentHandlerUserId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestManage">
    update new_product_request_manage
    set request_no = #{requestNo,jdbcType=VARCHAR},
      spu_offering_name = #{spuOfferingName,jdbcType=VARCHAR},
      spu_offering_code = #{spuOfferingCode,jdbcType=VARCHAR},
      sku_offering_name = #{skuOfferingName,jdbcType=VARCHAR},
      sku_offering_code = #{skuOfferingCode,jdbcType=VARCHAR},
      request_status = #{requestStatus,jdbcType=INTEGER},
      online_status = #{onlineStatus,jdbcType=VARCHAR},
      online_offline_request_status = #{onlineOfflineRequestStatus,jdbcType=INTEGER},
      cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      color = #{color,jdbcType=VARCHAR},
      material_code = #{materialCode,jdbcType=VARCHAR},
      network_property = #{networkProperty,jdbcType=VARCHAR},
      application_domain = #{applicationDomain,jdbcType=VARCHAR},
      product_introduction = #{productIntroduction,jdbcType=VARCHAR},
      product_sale_content = #{productSaleContent,jdbcType=VARCHAR},
      product_sale_area_code = #{productSaleAreaCode,jdbcType=VARCHAR},
      product_sale_area = #{productSaleArea,jdbcType=VARCHAR},
      supply_price = #{supplyPrice,jdbcType=DECIMAL},
      market_price = #{marketPrice,jdbcType=DECIMAL},
      sale_price = #{salePrice,jdbcType=DECIMAL},
      product_manager_name = #{productManagerName,jdbcType=VARCHAR},
      product_manager_phone = #{productManagerPhone,jdbcType=VARCHAR},
      product_manager_email = #{productManagerEmail,jdbcType=VARCHAR},
      request_current_handler_user_id = #{requestCurrentHandlerUserId,jdbcType=VARCHAR},
      online_offline_current_handler_user_id = #{onlineOfflineCurrentHandlerUserId,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into new_product_request_manage
    (id, request_no, spu_offering_name, spu_offering_code, sku_offering_name, sku_offering_code, 
      request_status, online_status, online_offline_request_status, cooperator_id, brand, 
      model, color, material_code, network_property, application_domain, product_introduction, 
      product_sale_content, product_sale_area_code, product_sale_area, supply_price, 
      market_price, sale_price, product_manager_name, product_manager_phone, product_manager_email, 
      request_current_handler_user_id, online_offline_current_handler_user_id, creator, 
      create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.requestNo,jdbcType=VARCHAR}, #{item.spuOfferingName,jdbcType=VARCHAR}, 
        #{item.spuOfferingCode,jdbcType=VARCHAR}, #{item.skuOfferingName,jdbcType=VARCHAR}, 
        #{item.skuOfferingCode,jdbcType=VARCHAR}, #{item.requestStatus,jdbcType=INTEGER}, 
        #{item.onlineStatus,jdbcType=VARCHAR}, #{item.onlineOfflineRequestStatus,jdbcType=INTEGER}, 
        #{item.cooperatorId,jdbcType=VARCHAR}, #{item.brand,jdbcType=VARCHAR}, #{item.model,jdbcType=VARCHAR}, 
        #{item.color,jdbcType=VARCHAR}, #{item.materialCode,jdbcType=VARCHAR}, #{item.networkProperty,jdbcType=VARCHAR}, 
        #{item.applicationDomain,jdbcType=VARCHAR}, #{item.productIntroduction,jdbcType=VARCHAR}, 
        #{item.productSaleContent,jdbcType=VARCHAR}, #{item.productSaleAreaCode,jdbcType=VARCHAR}, 
        #{item.productSaleArea,jdbcType=VARCHAR}, #{item.supplyPrice,jdbcType=DECIMAL}, 
        #{item.marketPrice,jdbcType=DECIMAL}, #{item.salePrice,jdbcType=DECIMAL}, #{item.productManagerName,jdbcType=VARCHAR}, 
        #{item.productManagerPhone,jdbcType=VARCHAR}, #{item.productManagerEmail,jdbcType=VARCHAR}, 
        #{item.requestCurrentHandlerUserId,jdbcType=VARCHAR}, #{item.onlineOfflineCurrentHandlerUserId,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into new_product_request_manage (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'request_no'.toString() == column.value">
          #{item.requestNo,jdbcType=VARCHAR}
        </if>
        <if test="'spu_offering_name'.toString() == column.value">
          #{item.spuOfferingName,jdbcType=VARCHAR}
        </if>
        <if test="'spu_offering_code'.toString() == column.value">
          #{item.spuOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'sku_offering_name'.toString() == column.value">
          #{item.skuOfferingName,jdbcType=VARCHAR}
        </if>
        <if test="'sku_offering_code'.toString() == column.value">
          #{item.skuOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'request_status'.toString() == column.value">
          #{item.requestStatus,jdbcType=INTEGER}
        </if>
        <if test="'online_status'.toString() == column.value">
          #{item.onlineStatus,jdbcType=VARCHAR}
        </if>
        <if test="'online_offline_request_status'.toString() == column.value">
          #{item.onlineOfflineRequestStatus,jdbcType=INTEGER}
        </if>
        <if test="'cooperator_id'.toString() == column.value">
          #{item.cooperatorId,jdbcType=VARCHAR}
        </if>
        <if test="'brand'.toString() == column.value">
          #{item.brand,jdbcType=VARCHAR}
        </if>
        <if test="'model'.toString() == column.value">
          #{item.model,jdbcType=VARCHAR}
        </if>
        <if test="'color'.toString() == column.value">
          #{item.color,jdbcType=VARCHAR}
        </if>
        <if test="'material_code'.toString() == column.value">
          #{item.materialCode,jdbcType=VARCHAR}
        </if>
        <if test="'network_property'.toString() == column.value">
          #{item.networkProperty,jdbcType=VARCHAR}
        </if>
        <if test="'application_domain'.toString() == column.value">
          #{item.applicationDomain,jdbcType=VARCHAR}
        </if>
        <if test="'product_introduction'.toString() == column.value">
          #{item.productIntroduction,jdbcType=VARCHAR}
        </if>
        <if test="'product_sale_content'.toString() == column.value">
          #{item.productSaleContent,jdbcType=VARCHAR}
        </if>
        <if test="'product_sale_area_code'.toString() == column.value">
          #{item.productSaleAreaCode,jdbcType=VARCHAR}
        </if>
        <if test="'product_sale_area'.toString() == column.value">
          #{item.productSaleArea,jdbcType=VARCHAR}
        </if>
        <if test="'supply_price'.toString() == column.value">
          #{item.supplyPrice,jdbcType=DECIMAL}
        </if>
        <if test="'market_price'.toString() == column.value">
          #{item.marketPrice,jdbcType=DECIMAL}
        </if>
        <if test="'sale_price'.toString() == column.value">
          #{item.salePrice,jdbcType=DECIMAL}
        </if>
        <if test="'product_manager_name'.toString() == column.value">
          #{item.productManagerName,jdbcType=VARCHAR}
        </if>
        <if test="'product_manager_phone'.toString() == column.value">
          #{item.productManagerPhone,jdbcType=VARCHAR}
        </if>
        <if test="'product_manager_email'.toString() == column.value">
          #{item.productManagerEmail,jdbcType=VARCHAR}
        </if>
        <if test="'request_current_handler_user_id'.toString() == column.value">
          #{item.requestCurrentHandlerUserId,jdbcType=VARCHAR}
        </if>
        <if test="'online_offline_current_handler_user_id'.toString() == column.value">
          #{item.onlineOfflineCurrentHandlerUserId,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>