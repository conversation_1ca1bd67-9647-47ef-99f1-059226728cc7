<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ProductFlowMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.ProductFlow">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 17:22:39 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="number" jdbcType="VARCHAR" property="number" />
    <result column="flow_type" jdbcType="INTEGER" property="flowType" />
    <result column="operate_type" jdbcType="INTEGER" property="operateType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 17:22:39 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 17:22:39 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 17:22:39 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, number, flow_type, operate_type, status, creator_id, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 17:22:39 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from product_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 17:22:39 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from product_flow
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 17:22:39 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from product_flow
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 17:22:39 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from product_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.ProductFlow">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 17:22:39 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow (id, number, flow_type, 
      operate_type, status, creator_id, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{number,jdbcType=VARCHAR}, #{flowType,jdbcType=INTEGER}, 
      #{operateType,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{creatorId,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlow">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 17:22:39 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="number != null">
        number,
      </if>
      <if test="flowType != null">
        flow_type,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        #{number,jdbcType=VARCHAR},
      </if>
      <if test="flowType != null">
        #{flowType,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 17:22:39 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from product_flow
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 17:22:39 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.number != null">
        number = #{record.number,jdbcType=VARCHAR},
      </if>
      <if test="record.flowType != null">
        flow_type = #{record.flowType,jdbcType=INTEGER},
      </if>
      <if test="record.operateType != null">
        operate_type = #{record.operateType,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 17:22:39 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow
    set id = #{record.id,jdbcType=VARCHAR},
      number = #{record.number,jdbcType=VARCHAR},
      flow_type = #{record.flowType,jdbcType=INTEGER},
      operate_type = #{record.operateType,jdbcType=INTEGER},
      status = #{record.status,jdbcType=INTEGER},
      creator_id = #{record.creatorId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlow">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 17:22:39 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow
    <set>
      <if test="number != null">
        number = #{number,jdbcType=VARCHAR},
      </if>
      <if test="flowType != null">
        flow_type = #{flowType,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.ProductFlow">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 17:22:39 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow
    set number = #{number,jdbcType=VARCHAR},
      flow_type = #{flowType,jdbcType=INTEGER},
      operate_type = #{operateType,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      creator_id = #{creatorId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 17:22:39 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow
    (id, number, flow_type, operate_type, status, creator_id, create_time, update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.number,jdbcType=VARCHAR}, #{item.flowType,jdbcType=INTEGER}, 
        #{item.operateType,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER}, #{item.creatorId,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 18 17:22:39 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'number'.toString() == column.value">
          #{item.number,jdbcType=VARCHAR}
        </if>
        <if test="'flow_type'.toString() == column.value">
          #{item.flowType,jdbcType=INTEGER}
        </if>
        <if test="'operate_type'.toString() == column.value">
          #{item.operateType,jdbcType=INTEGER}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'creator_id'.toString() == column.value">
          #{item.creatorId,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>