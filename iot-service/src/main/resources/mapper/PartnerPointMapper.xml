<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.PartnerPointMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.PartnerPoint">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 07 16:47:52 CST 2022. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="partner_id" jdbcType="VARCHAR" property="partnerId" />
    <result column="supplier_id" jdbcType="VARCHAR" property="supplierId" />
    <result column="total" jdbcType="BIGINT" property="total" />
    <result column="available" jdbcType="BIGINT" property="available" />
    <result column="redeemed" jdbcType="BIGINT" property="redeemed" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 07 16:47:52 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 07 16:47:52 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 07 16:47:52 CST 2022. by MyBatis Generator, do not modify.
    -->
    id, partner_id, supplier_id, total, available, redeemed, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.PartnerPointExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 07 16:47:52 CST 2022. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from partner_point
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 07 16:47:52 CST 2022. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from partner_point
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 07 16:47:52 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from partner_point
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.PartnerPointExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 07 16:47:52 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from partner_point
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.PartnerPoint">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 07 16:47:52 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into partner_point (id, partner_id, supplier_id, 
      total, available, redeemed, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{partnerId,jdbcType=VARCHAR}, #{supplierId,jdbcType=VARCHAR}, 
      #{total,jdbcType=BIGINT}, #{available,jdbcType=BIGINT}, #{redeemed,jdbcType=BIGINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.PartnerPoint">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 07 16:47:52 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into partner_point
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="partnerId != null">
        partner_id,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="total != null">
        total,
      </if>
      <if test="available != null">
        available,
      </if>
      <if test="redeemed != null">
        redeemed,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="partnerId != null">
        #{partnerId,jdbcType=VARCHAR},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=VARCHAR},
      </if>
      <if test="total != null">
        #{total,jdbcType=BIGINT},
      </if>
      <if test="available != null">
        #{available,jdbcType=BIGINT},
      </if>
      <if test="redeemed != null">
        #{redeemed,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.PartnerPointExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 07 16:47:52 CST 2022. by MyBatis Generator, do not modify.
    -->
    select count(*) from partner_point
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 07 16:47:52 CST 2022. by MyBatis Generator, do not modify.
    -->
    update partner_point
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.partnerId != null">
        partner_id = #{record.partnerId,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierId != null">
        supplier_id = #{record.supplierId,jdbcType=VARCHAR},
      </if>
      <if test="record.total != null">
        total = #{record.total,jdbcType=BIGINT},
      </if>
      <if test="record.available != null">
        available = #{record.available,jdbcType=BIGINT},
      </if>
      <if test="record.redeemed != null">
        redeemed = #{record.redeemed,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 07 16:47:52 CST 2022. by MyBatis Generator, do not modify.
    -->
    update partner_point
    set id = #{record.id,jdbcType=VARCHAR},
      partner_id = #{record.partnerId,jdbcType=VARCHAR},
      supplier_id = #{record.supplierId,jdbcType=VARCHAR},
      total = #{record.total,jdbcType=BIGINT},
      available = #{record.available,jdbcType=BIGINT},
      redeemed = #{record.redeemed,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.PartnerPoint">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 07 16:47:52 CST 2022. by MyBatis Generator, do not modify.
    -->
    update partner_point
    <set>
      <if test="partnerId != null">
        partner_id = #{partnerId,jdbcType=VARCHAR},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=VARCHAR},
      </if>
      <if test="total != null">
        total = #{total,jdbcType=BIGINT},
      </if>
      <if test="available != null">
        available = #{available,jdbcType=BIGINT},
      </if>
      <if test="redeemed != null">
        redeemed = #{redeemed,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.PartnerPoint">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 07 16:47:52 CST 2022. by MyBatis Generator, do not modify.
    -->
    update partner_point
    set partner_id = #{partnerId,jdbcType=VARCHAR},
      supplier_id = #{supplierId,jdbcType=VARCHAR},
      total = #{total,jdbcType=BIGINT},
      available = #{available,jdbcType=BIGINT},
      redeemed = #{redeemed,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>