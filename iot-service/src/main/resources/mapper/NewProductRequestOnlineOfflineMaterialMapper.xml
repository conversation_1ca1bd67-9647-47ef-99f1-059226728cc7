<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.NewProductRequestOnlineOfflineMaterialMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineMaterial">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="new_product_request_id" jdbcType="VARCHAR" property="newProductRequestId" />
    <result column="combo_info_id" jdbcType="VARCHAR" property="comboInfoId" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_inner_url" jdbcType="VARCHAR" property="fileInnerUrl" />
    <result column="file_outer_url" jdbcType="VARCHAR" property="fileOuterUrl" />
    <result column="file_type" jdbcType="VARCHAR" property="fileType" />
    <result column="file_key" jdbcType="VARCHAR" property="fileKey" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, new_product_request_id, combo_info_id, file_name, file_inner_url, file_outer_url, 
    file_type, file_key, create_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineMaterialExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from new_product_request_online_offline_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from new_product_request_online_offline_material
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from new_product_request_online_offline_material
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineMaterialExample">
    delete from new_product_request_online_offline_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineMaterial">
    insert into new_product_request_online_offline_material (id, new_product_request_id, combo_info_id, 
      file_name, file_inner_url, file_outer_url, 
      file_type, file_key, create_time
      )
    values (#{id,jdbcType=VARCHAR}, #{newProductRequestId,jdbcType=VARCHAR}, #{comboInfoId,jdbcType=VARCHAR}, 
      #{fileName,jdbcType=VARCHAR}, #{fileInnerUrl,jdbcType=VARCHAR}, #{fileOuterUrl,jdbcType=VARCHAR}, 
      #{fileType,jdbcType=VARCHAR}, #{fileKey,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineMaterial">
    insert into new_product_request_online_offline_material
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="newProductRequestId != null">
        new_product_request_id,
      </if>
      <if test="comboInfoId != null">
        combo_info_id,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="fileInnerUrl != null">
        file_inner_url,
      </if>
      <if test="fileOuterUrl != null">
        file_outer_url,
      </if>
      <if test="fileType != null">
        file_type,
      </if>
      <if test="fileKey != null">
        file_key,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="newProductRequestId != null">
        #{newProductRequestId,jdbcType=VARCHAR},
      </if>
      <if test="comboInfoId != null">
        #{comboInfoId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileInnerUrl != null">
        #{fileInnerUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileOuterUrl != null">
        #{fileOuterUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="fileKey != null">
        #{fileKey,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineMaterialExample" resultType="java.lang.Long">
    select count(*) from new_product_request_online_offline_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update new_product_request_online_offline_material
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.newProductRequestId != null">
        new_product_request_id = #{record.newProductRequestId,jdbcType=VARCHAR},
      </if>
      <if test="record.comboInfoId != null">
        combo_info_id = #{record.comboInfoId,jdbcType=VARCHAR},
      </if>
      <if test="record.fileName != null">
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.fileInnerUrl != null">
        file_inner_url = #{record.fileInnerUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.fileOuterUrl != null">
        file_outer_url = #{record.fileOuterUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.fileType != null">
        file_type = #{record.fileType,jdbcType=VARCHAR},
      </if>
      <if test="record.fileKey != null">
        file_key = #{record.fileKey,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update new_product_request_online_offline_material
    set id = #{record.id,jdbcType=VARCHAR},
      new_product_request_id = #{record.newProductRequestId,jdbcType=VARCHAR},
      combo_info_id = #{record.comboInfoId,jdbcType=VARCHAR},
      file_name = #{record.fileName,jdbcType=VARCHAR},
      file_inner_url = #{record.fileInnerUrl,jdbcType=VARCHAR},
      file_outer_url = #{record.fileOuterUrl,jdbcType=VARCHAR},
      file_type = #{record.fileType,jdbcType=VARCHAR},
      file_key = #{record.fileKey,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineMaterial">
    update new_product_request_online_offline_material
    <set>
      <if test="newProductRequestId != null">
        new_product_request_id = #{newProductRequestId,jdbcType=VARCHAR},
      </if>
      <if test="comboInfoId != null">
        combo_info_id = #{comboInfoId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileInnerUrl != null">
        file_inner_url = #{fileInnerUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileOuterUrl != null">
        file_outer_url = #{fileOuterUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        file_type = #{fileType,jdbcType=VARCHAR},
      </if>
      <if test="fileKey != null">
        file_key = #{fileKey,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineMaterial">
    update new_product_request_online_offline_material
    set new_product_request_id = #{newProductRequestId,jdbcType=VARCHAR},
      combo_info_id = #{comboInfoId,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_inner_url = #{fileInnerUrl,jdbcType=VARCHAR},
      file_outer_url = #{fileOuterUrl,jdbcType=VARCHAR},
      file_type = #{fileType,jdbcType=VARCHAR},
      file_key = #{fileKey,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into new_product_request_online_offline_material
    (id, new_product_request_id, combo_info_id, file_name, file_inner_url, file_outer_url, 
      file_type, file_key, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.newProductRequestId,jdbcType=VARCHAR}, #{item.comboInfoId,jdbcType=VARCHAR}, 
        #{item.fileName,jdbcType=VARCHAR}, #{item.fileInnerUrl,jdbcType=VARCHAR}, #{item.fileOuterUrl,jdbcType=VARCHAR}, 
        #{item.fileType,jdbcType=VARCHAR}, #{item.fileKey,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into new_product_request_online_offline_material (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'new_product_request_id'.toString() == column.value">
          #{item.newProductRequestId,jdbcType=VARCHAR}
        </if>
        <if test="'combo_info_id'.toString() == column.value">
          #{item.comboInfoId,jdbcType=VARCHAR}
        </if>
        <if test="'file_name'.toString() == column.value">
          #{item.fileName,jdbcType=VARCHAR}
        </if>
        <if test="'file_inner_url'.toString() == column.value">
          #{item.fileInnerUrl,jdbcType=VARCHAR}
        </if>
        <if test="'file_outer_url'.toString() == column.value">
          #{item.fileOuterUrl,jdbcType=VARCHAR}
        </if>
        <if test="'file_type'.toString() == column.value">
          #{item.fileType,jdbcType=VARCHAR}
        </if>
        <if test="'file_key'.toString() == column.value">
          #{item.fileKey,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>