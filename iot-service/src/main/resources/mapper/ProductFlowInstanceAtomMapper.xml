<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ProductFlowInstanceAtomMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.ProductFlowInstanceAtom">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 14:51:40 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="flow_instance_id" jdbcType="VARCHAR" property="flowInstanceId" />
    <result column="flow_id" jdbcType="VARCHAR" property="flowId" />
    <result column="spu_code" jdbcType="VARCHAR" property="spuCode" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="atom_name" jdbcType="VARCHAR" property="atomName" />
    <result column="settle_price" jdbcType="BIGINT" property="settlePrice" />
    <result column="settle_price_check" jdbcType="BIGINT" property="settlePriceCheck" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="service_content" jdbcType="VARCHAR" property="serviceContent" />
    <result column="cmiot_cost_project_id" jdbcType="VARCHAR" property="cmiotCostProjectId" />
    <result column="min_purchase_num" jdbcType="INTEGER" property="minPurchaseNum" />
    <result column="province_purchase_contract" jdbcType="VARCHAR" property="provincePurchaseContract" />
    <result column="iot_purchase_contract" jdbcType="VARCHAR" property="iotPurchaseContract" />
    <result column="material_num" jdbcType="VARCHAR" property="materialNum" />
    <result column="atom_remark" jdbcType="VARCHAR" property="atomRemark" />
    <result column="atom_quantity" jdbcType="INTEGER" property="atomQuantity" />
    <result column="color" jdbcType="VARCHAR" property="color" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="hardware_price" jdbcType="BIGINT" property="hardwarePrice" />
    <result column="soft_atom_name" jdbcType="VARCHAR" property="softAtomName" />
    <result column="soft_settle_price" jdbcType="BIGINT" property="softSettlePrice" />
    <result column="soft_unit" jdbcType="VARCHAR" property="softUnit" />
    <result column="soft_quantity" jdbcType="INTEGER" property="softQuantity" />
    <result column="soft_product_code" jdbcType="VARCHAR" property="softProductCode" />
    <result column="no_settlement" jdbcType="VARCHAR" property="noSettlement" />
    <result column="settlement_detail_name" jdbcType="VARCHAR" property="settlementDetailName" />
    <result column="deliver_period" jdbcType="VARCHAR" property="deliverPeriod" />
    <result column="soft_service_content" jdbcType="VARCHAR" property="softServiceContent" />
    <result column="soft_price" jdbcType="BIGINT" property="softPrice" />
    <result column="soft_total_price" jdbcType="BIGINT" property="softTotalPrice" />
    <result column="zhuanhe_settle_price" jdbcType="BIGINT" property="zhuanheSettlePrice" />
    <result column="service_package_name" jdbcType="VARCHAR" property="servicePackageName" />
    <result column="service_contract" jdbcType="VARCHAR" property="serviceContract" />
    <result column="sale_price" jdbcType="BIGINT" property="salePrice" />
    <result column="sale_min_price" jdbcType="BIGINT" property="saleMinPrice" />
    <result column="sale_max_price" jdbcType="BIGINT" property="saleMaxPrice" />
    <result column="sale_out_of_price_range" jdbcType="VARCHAR" property="saleOutOfPriceRange" />
    <result column="inventory" jdbcType="INTEGER" property="inventory" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 14:51:40 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 14:51:40 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 14:51:40 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, flow_instance_id, flow_id, spu_code, sku_code, atom_name, settle_price, settle_price_check, 
    unit, service_content, cmiot_cost_project_id, min_purchase_num, province_purchase_contract, 
    iot_purchase_contract, material_num, atom_remark, atom_quantity, color, model, hardware_price, 
    soft_atom_name, soft_settle_price, soft_unit, soft_quantity, soft_product_code, no_settlement, 
    settlement_detail_name, deliver_period, soft_service_content, soft_price, soft_total_price, 
    zhuanhe_settle_price, service_package_name, service_contract, sale_price, sale_min_price, 
    sale_max_price, sale_out_of_price_range, inventory, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceAtomExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 14:51:40 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from product_flow_instance_atom
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 14:51:40 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from product_flow_instance_atom
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 14:51:40 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from product_flow_instance_atom
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceAtomExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 14:51:40 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from product_flow_instance_atom
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceAtom">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 14:51:40 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_atom (id, flow_instance_id, flow_id, 
      spu_code, sku_code, atom_name, 
      settle_price, settle_price_check, unit, 
      service_content, cmiot_cost_project_id, min_purchase_num, 
      province_purchase_contract, iot_purchase_contract, 
      material_num, atom_remark, atom_quantity, 
      color, model, hardware_price, 
      soft_atom_name, soft_settle_price, soft_unit, 
      soft_quantity, soft_product_code, no_settlement, 
      settlement_detail_name, deliver_period, soft_service_content, 
      soft_price, soft_total_price, zhuanhe_settle_price, 
      service_package_name, service_contract, sale_price, 
      sale_min_price, sale_max_price, sale_out_of_price_range, 
      inventory, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{flowInstanceId,jdbcType=VARCHAR}, #{flowId,jdbcType=VARCHAR}, 
      #{spuCode,jdbcType=VARCHAR}, #{skuCode,jdbcType=VARCHAR}, #{atomName,jdbcType=VARCHAR}, 
      #{settlePrice,jdbcType=BIGINT}, #{settlePriceCheck,jdbcType=BIGINT}, #{unit,jdbcType=VARCHAR}, 
      #{serviceContent,jdbcType=VARCHAR}, #{cmiotCostProjectId,jdbcType=VARCHAR}, #{minPurchaseNum,jdbcType=INTEGER}, 
      #{provincePurchaseContract,jdbcType=VARCHAR}, #{iotPurchaseContract,jdbcType=VARCHAR}, 
      #{materialNum,jdbcType=VARCHAR}, #{atomRemark,jdbcType=VARCHAR}, #{atomQuantity,jdbcType=INTEGER}, 
      #{color,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, #{hardwarePrice,jdbcType=BIGINT}, 
      #{softAtomName,jdbcType=VARCHAR}, #{softSettlePrice,jdbcType=BIGINT}, #{softUnit,jdbcType=VARCHAR}, 
      #{softQuantity,jdbcType=INTEGER}, #{softProductCode,jdbcType=VARCHAR}, #{noSettlement,jdbcType=VARCHAR}, 
      #{settlementDetailName,jdbcType=VARCHAR}, #{deliverPeriod,jdbcType=VARCHAR}, #{softServiceContent,jdbcType=VARCHAR}, 
      #{softPrice,jdbcType=BIGINT}, #{softTotalPrice,jdbcType=BIGINT}, #{zhuanheSettlePrice,jdbcType=BIGINT}, 
      #{servicePackageName,jdbcType=VARCHAR}, #{serviceContract,jdbcType=VARCHAR}, #{salePrice,jdbcType=BIGINT}, 
      #{saleMinPrice,jdbcType=BIGINT}, #{saleMaxPrice,jdbcType=BIGINT}, #{saleOutOfPriceRange,jdbcType=VARCHAR}, 
      #{inventory,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceAtom">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 14:51:40 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_atom
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="flowInstanceId != null">
        flow_instance_id,
      </if>
      <if test="flowId != null">
        flow_id,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="skuCode != null">
        sku_code,
      </if>
      <if test="atomName != null">
        atom_name,
      </if>
      <if test="settlePrice != null">
        settle_price,
      </if>
      <if test="settlePriceCheck != null">
        settle_price_check,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="serviceContent != null">
        service_content,
      </if>
      <if test="cmiotCostProjectId != null">
        cmiot_cost_project_id,
      </if>
      <if test="minPurchaseNum != null">
        min_purchase_num,
      </if>
      <if test="provincePurchaseContract != null">
        province_purchase_contract,
      </if>
      <if test="iotPurchaseContract != null">
        iot_purchase_contract,
      </if>
      <if test="materialNum != null">
        material_num,
      </if>
      <if test="atomRemark != null">
        atom_remark,
      </if>
      <if test="atomQuantity != null">
        atom_quantity,
      </if>
      <if test="color != null">
        color,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="hardwarePrice != null">
        hardware_price,
      </if>
      <if test="softAtomName != null">
        soft_atom_name,
      </if>
      <if test="softSettlePrice != null">
        soft_settle_price,
      </if>
      <if test="softUnit != null">
        soft_unit,
      </if>
      <if test="softQuantity != null">
        soft_quantity,
      </if>
      <if test="softProductCode != null">
        soft_product_code,
      </if>
      <if test="noSettlement != null">
        no_settlement,
      </if>
      <if test="settlementDetailName != null">
        settlement_detail_name,
      </if>
      <if test="deliverPeriod != null">
        deliver_period,
      </if>
      <if test="softServiceContent != null">
        soft_service_content,
      </if>
      <if test="softPrice != null">
        soft_price,
      </if>
      <if test="softTotalPrice != null">
        soft_total_price,
      </if>
      <if test="zhuanheSettlePrice != null">
        zhuanhe_settle_price,
      </if>
      <if test="servicePackageName != null">
        service_package_name,
      </if>
      <if test="serviceContract != null">
        service_contract,
      </if>
      <if test="salePrice != null">
        sale_price,
      </if>
      <if test="saleMinPrice != null">
        sale_min_price,
      </if>
      <if test="saleMaxPrice != null">
        sale_max_price,
      </if>
      <if test="saleOutOfPriceRange != null">
        sale_out_of_price_range,
      </if>
      <if test="inventory != null">
        inventory,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="flowInstanceId != null">
        #{flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="flowId != null">
        #{flowId,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="atomName != null">
        #{atomName,jdbcType=VARCHAR},
      </if>
      <if test="settlePrice != null">
        #{settlePrice,jdbcType=BIGINT},
      </if>
      <if test="settlePriceCheck != null">
        #{settlePriceCheck,jdbcType=BIGINT},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="serviceContent != null">
        #{serviceContent,jdbcType=VARCHAR},
      </if>
      <if test="cmiotCostProjectId != null">
        #{cmiotCostProjectId,jdbcType=VARCHAR},
      </if>
      <if test="minPurchaseNum != null">
        #{minPurchaseNum,jdbcType=INTEGER},
      </if>
      <if test="provincePurchaseContract != null">
        #{provincePurchaseContract,jdbcType=VARCHAR},
      </if>
      <if test="iotPurchaseContract != null">
        #{iotPurchaseContract,jdbcType=VARCHAR},
      </if>
      <if test="materialNum != null">
        #{materialNum,jdbcType=VARCHAR},
      </if>
      <if test="atomRemark != null">
        #{atomRemark,jdbcType=VARCHAR},
      </if>
      <if test="atomQuantity != null">
        #{atomQuantity,jdbcType=INTEGER},
      </if>
      <if test="color != null">
        #{color,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="hardwarePrice != null">
        #{hardwarePrice,jdbcType=BIGINT},
      </if>
      <if test="softAtomName != null">
        #{softAtomName,jdbcType=VARCHAR},
      </if>
      <if test="softSettlePrice != null">
        #{softSettlePrice,jdbcType=BIGINT},
      </if>
      <if test="softUnit != null">
        #{softUnit,jdbcType=VARCHAR},
      </if>
      <if test="softQuantity != null">
        #{softQuantity,jdbcType=INTEGER},
      </if>
      <if test="softProductCode != null">
        #{softProductCode,jdbcType=VARCHAR},
      </if>
      <if test="noSettlement != null">
        #{noSettlement,jdbcType=VARCHAR},
      </if>
      <if test="settlementDetailName != null">
        #{settlementDetailName,jdbcType=VARCHAR},
      </if>
      <if test="deliverPeriod != null">
        #{deliverPeriod,jdbcType=VARCHAR},
      </if>
      <if test="softServiceContent != null">
        #{softServiceContent,jdbcType=VARCHAR},
      </if>
      <if test="softPrice != null">
        #{softPrice,jdbcType=BIGINT},
      </if>
      <if test="softTotalPrice != null">
        #{softTotalPrice,jdbcType=BIGINT},
      </if>
      <if test="zhuanheSettlePrice != null">
        #{zhuanheSettlePrice,jdbcType=BIGINT},
      </if>
      <if test="servicePackageName != null">
        #{servicePackageName,jdbcType=VARCHAR},
      </if>
      <if test="serviceContract != null">
        #{serviceContract,jdbcType=VARCHAR},
      </if>
      <if test="salePrice != null">
        #{salePrice,jdbcType=BIGINT},
      </if>
      <if test="saleMinPrice != null">
        #{saleMinPrice,jdbcType=BIGINT},
      </if>
      <if test="saleMaxPrice != null">
        #{saleMaxPrice,jdbcType=BIGINT},
      </if>
      <if test="saleOutOfPriceRange != null">
        #{saleOutOfPriceRange,jdbcType=VARCHAR},
      </if>
      <if test="inventory != null">
        #{inventory,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceAtomExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 14:51:40 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from product_flow_instance_atom
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 14:51:40 CST 2025. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_atom
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.flowInstanceId != null">
        flow_instance_id = #{record.flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.flowId != null">
        flow_id = #{record.flowId,jdbcType=VARCHAR},
      </if>
      <if test="record.spuCode != null">
        spu_code = #{record.spuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCode != null">
        sku_code = #{record.skuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.atomName != null">
        atom_name = #{record.atomName,jdbcType=VARCHAR},
      </if>
      <if test="record.settlePrice != null">
        settle_price = #{record.settlePrice,jdbcType=BIGINT},
      </if>
      <if test="record.settlePriceCheck != null">
        settle_price_check = #{record.settlePriceCheck,jdbcType=BIGINT},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceContent != null">
        service_content = #{record.serviceContent,jdbcType=VARCHAR},
      </if>
      <if test="record.cmiotCostProjectId != null">
        cmiot_cost_project_id = #{record.cmiotCostProjectId,jdbcType=VARCHAR},
      </if>
      <if test="record.minPurchaseNum != null">
        min_purchase_num = #{record.minPurchaseNum,jdbcType=INTEGER},
      </if>
      <if test="record.provincePurchaseContract != null">
        province_purchase_contract = #{record.provincePurchaseContract,jdbcType=VARCHAR},
      </if>
      <if test="record.iotPurchaseContract != null">
        iot_purchase_contract = #{record.iotPurchaseContract,jdbcType=VARCHAR},
      </if>
      <if test="record.materialNum != null">
        material_num = #{record.materialNum,jdbcType=VARCHAR},
      </if>
      <if test="record.atomRemark != null">
        atom_remark = #{record.atomRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.atomQuantity != null">
        atom_quantity = #{record.atomQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.color != null">
        color = #{record.color,jdbcType=VARCHAR},
      </if>
      <if test="record.model != null">
        model = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.hardwarePrice != null">
        hardware_price = #{record.hardwarePrice,jdbcType=BIGINT},
      </if>
      <if test="record.softAtomName != null">
        soft_atom_name = #{record.softAtomName,jdbcType=VARCHAR},
      </if>
      <if test="record.softSettlePrice != null">
        soft_settle_price = #{record.softSettlePrice,jdbcType=BIGINT},
      </if>
      <if test="record.softUnit != null">
        soft_unit = #{record.softUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.softQuantity != null">
        soft_quantity = #{record.softQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.softProductCode != null">
        soft_product_code = #{record.softProductCode,jdbcType=VARCHAR},
      </if>
      <if test="record.noSettlement != null">
        no_settlement = #{record.noSettlement,jdbcType=VARCHAR},
      </if>
      <if test="record.settlementDetailName != null">
        settlement_detail_name = #{record.settlementDetailName,jdbcType=VARCHAR},
      </if>
      <if test="record.deliverPeriod != null">
        deliver_period = #{record.deliverPeriod,jdbcType=VARCHAR},
      </if>
      <if test="record.softServiceContent != null">
        soft_service_content = #{record.softServiceContent,jdbcType=VARCHAR},
      </if>
      <if test="record.softPrice != null">
        soft_price = #{record.softPrice,jdbcType=BIGINT},
      </if>
      <if test="record.softTotalPrice != null">
        soft_total_price = #{record.softTotalPrice,jdbcType=BIGINT},
      </if>
      <if test="record.zhuanheSettlePrice != null">
        zhuanhe_settle_price = #{record.zhuanheSettlePrice,jdbcType=BIGINT},
      </if>
      <if test="record.servicePackageName != null">
        service_package_name = #{record.servicePackageName,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceContract != null">
        service_contract = #{record.serviceContract,jdbcType=VARCHAR},
      </if>
      <if test="record.salePrice != null">
        sale_price = #{record.salePrice,jdbcType=BIGINT},
      </if>
      <if test="record.saleMinPrice != null">
        sale_min_price = #{record.saleMinPrice,jdbcType=BIGINT},
      </if>
      <if test="record.saleMaxPrice != null">
        sale_max_price = #{record.saleMaxPrice,jdbcType=BIGINT},
      </if>
      <if test="record.saleOutOfPriceRange != null">
        sale_out_of_price_range = #{record.saleOutOfPriceRange,jdbcType=VARCHAR},
      </if>
      <if test="record.inventory != null">
        inventory = #{record.inventory,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 14:51:40 CST 2025. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_atom
    set id = #{record.id,jdbcType=VARCHAR},
      flow_instance_id = #{record.flowInstanceId,jdbcType=VARCHAR},
      flow_id = #{record.flowId,jdbcType=VARCHAR},
      spu_code = #{record.spuCode,jdbcType=VARCHAR},
      sku_code = #{record.skuCode,jdbcType=VARCHAR},
      atom_name = #{record.atomName,jdbcType=VARCHAR},
      settle_price = #{record.settlePrice,jdbcType=BIGINT},
      settle_price_check = #{record.settlePriceCheck,jdbcType=BIGINT},
      unit = #{record.unit,jdbcType=VARCHAR},
      service_content = #{record.serviceContent,jdbcType=VARCHAR},
      cmiot_cost_project_id = #{record.cmiotCostProjectId,jdbcType=VARCHAR},
      min_purchase_num = #{record.minPurchaseNum,jdbcType=INTEGER},
      province_purchase_contract = #{record.provincePurchaseContract,jdbcType=VARCHAR},
      iot_purchase_contract = #{record.iotPurchaseContract,jdbcType=VARCHAR},
      material_num = #{record.materialNum,jdbcType=VARCHAR},
      atom_remark = #{record.atomRemark,jdbcType=VARCHAR},
      atom_quantity = #{record.atomQuantity,jdbcType=INTEGER},
      color = #{record.color,jdbcType=VARCHAR},
      model = #{record.model,jdbcType=VARCHAR},
      hardware_price = #{record.hardwarePrice,jdbcType=BIGINT},
      soft_atom_name = #{record.softAtomName,jdbcType=VARCHAR},
      soft_settle_price = #{record.softSettlePrice,jdbcType=BIGINT},
      soft_unit = #{record.softUnit,jdbcType=VARCHAR},
      soft_quantity = #{record.softQuantity,jdbcType=INTEGER},
      soft_product_code = #{record.softProductCode,jdbcType=VARCHAR},
      no_settlement = #{record.noSettlement,jdbcType=VARCHAR},
      settlement_detail_name = #{record.settlementDetailName,jdbcType=VARCHAR},
      deliver_period = #{record.deliverPeriod,jdbcType=VARCHAR},
      soft_service_content = #{record.softServiceContent,jdbcType=VARCHAR},
      soft_price = #{record.softPrice,jdbcType=BIGINT},
      soft_total_price = #{record.softTotalPrice,jdbcType=BIGINT},
      zhuanhe_settle_price = #{record.zhuanheSettlePrice,jdbcType=BIGINT},
      service_package_name = #{record.servicePackageName,jdbcType=VARCHAR},
      service_contract = #{record.serviceContract,jdbcType=VARCHAR},
      sale_price = #{record.salePrice,jdbcType=BIGINT},
      sale_min_price = #{record.saleMinPrice,jdbcType=BIGINT},
      sale_max_price = #{record.saleMaxPrice,jdbcType=BIGINT},
      sale_out_of_price_range = #{record.saleOutOfPriceRange,jdbcType=VARCHAR},
      inventory = #{record.inventory,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceAtom">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 14:51:40 CST 2025. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_atom
    <set>
      <if test="flowInstanceId != null">
        flow_instance_id = #{flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="flowId != null">
        flow_id = #{flowId,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        sku_code = #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="atomName != null">
        atom_name = #{atomName,jdbcType=VARCHAR},
      </if>
      <if test="settlePrice != null">
        settle_price = #{settlePrice,jdbcType=BIGINT},
      </if>
      <if test="settlePriceCheck != null">
        settle_price_check = #{settlePriceCheck,jdbcType=BIGINT},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="serviceContent != null">
        service_content = #{serviceContent,jdbcType=VARCHAR},
      </if>
      <if test="cmiotCostProjectId != null">
        cmiot_cost_project_id = #{cmiotCostProjectId,jdbcType=VARCHAR},
      </if>
      <if test="minPurchaseNum != null">
        min_purchase_num = #{minPurchaseNum,jdbcType=INTEGER},
      </if>
      <if test="provincePurchaseContract != null">
        province_purchase_contract = #{provincePurchaseContract,jdbcType=VARCHAR},
      </if>
      <if test="iotPurchaseContract != null">
        iot_purchase_contract = #{iotPurchaseContract,jdbcType=VARCHAR},
      </if>
      <if test="materialNum != null">
        material_num = #{materialNum,jdbcType=VARCHAR},
      </if>
      <if test="atomRemark != null">
        atom_remark = #{atomRemark,jdbcType=VARCHAR},
      </if>
      <if test="atomQuantity != null">
        atom_quantity = #{atomQuantity,jdbcType=INTEGER},
      </if>
      <if test="color != null">
        color = #{color,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="hardwarePrice != null">
        hardware_price = #{hardwarePrice,jdbcType=BIGINT},
      </if>
      <if test="softAtomName != null">
        soft_atom_name = #{softAtomName,jdbcType=VARCHAR},
      </if>
      <if test="softSettlePrice != null">
        soft_settle_price = #{softSettlePrice,jdbcType=BIGINT},
      </if>
      <if test="softUnit != null">
        soft_unit = #{softUnit,jdbcType=VARCHAR},
      </if>
      <if test="softQuantity != null">
        soft_quantity = #{softQuantity,jdbcType=INTEGER},
      </if>
      <if test="softProductCode != null">
        soft_product_code = #{softProductCode,jdbcType=VARCHAR},
      </if>
      <if test="noSettlement != null">
        no_settlement = #{noSettlement,jdbcType=VARCHAR},
      </if>
      <if test="settlementDetailName != null">
        settlement_detail_name = #{settlementDetailName,jdbcType=VARCHAR},
      </if>
      <if test="deliverPeriod != null">
        deliver_period = #{deliverPeriod,jdbcType=VARCHAR},
      </if>
      <if test="softServiceContent != null">
        soft_service_content = #{softServiceContent,jdbcType=VARCHAR},
      </if>
      <if test="softPrice != null">
        soft_price = #{softPrice,jdbcType=BIGINT},
      </if>
      <if test="softTotalPrice != null">
        soft_total_price = #{softTotalPrice,jdbcType=BIGINT},
      </if>
      <if test="zhuanheSettlePrice != null">
        zhuanhe_settle_price = #{zhuanheSettlePrice,jdbcType=BIGINT},
      </if>
      <if test="servicePackageName != null">
        service_package_name = #{servicePackageName,jdbcType=VARCHAR},
      </if>
      <if test="serviceContract != null">
        service_contract = #{serviceContract,jdbcType=VARCHAR},
      </if>
      <if test="salePrice != null">
        sale_price = #{salePrice,jdbcType=BIGINT},
      </if>
      <if test="saleMinPrice != null">
        sale_min_price = #{saleMinPrice,jdbcType=BIGINT},
      </if>
      <if test="saleMaxPrice != null">
        sale_max_price = #{saleMaxPrice,jdbcType=BIGINT},
      </if>
      <if test="saleOutOfPriceRange != null">
        sale_out_of_price_range = #{saleOutOfPriceRange,jdbcType=VARCHAR},
      </if>
      <if test="inventory != null">
        inventory = #{inventory,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceAtom">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 14:51:40 CST 2025. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_atom
    set flow_instance_id = #{flowInstanceId,jdbcType=VARCHAR},
      flow_id = #{flowId,jdbcType=VARCHAR},
      spu_code = #{spuCode,jdbcType=VARCHAR},
      sku_code = #{skuCode,jdbcType=VARCHAR},
      atom_name = #{atomName,jdbcType=VARCHAR},
      settle_price = #{settlePrice,jdbcType=BIGINT},
      settle_price_check = #{settlePriceCheck,jdbcType=BIGINT},
      unit = #{unit,jdbcType=VARCHAR},
      service_content = #{serviceContent,jdbcType=VARCHAR},
      cmiot_cost_project_id = #{cmiotCostProjectId,jdbcType=VARCHAR},
      min_purchase_num = #{minPurchaseNum,jdbcType=INTEGER},
      province_purchase_contract = #{provincePurchaseContract,jdbcType=VARCHAR},
      iot_purchase_contract = #{iotPurchaseContract,jdbcType=VARCHAR},
      material_num = #{materialNum,jdbcType=VARCHAR},
      atom_remark = #{atomRemark,jdbcType=VARCHAR},
      atom_quantity = #{atomQuantity,jdbcType=INTEGER},
      color = #{color,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      hardware_price = #{hardwarePrice,jdbcType=BIGINT},
      soft_atom_name = #{softAtomName,jdbcType=VARCHAR},
      soft_settle_price = #{softSettlePrice,jdbcType=BIGINT},
      soft_unit = #{softUnit,jdbcType=VARCHAR},
      soft_quantity = #{softQuantity,jdbcType=INTEGER},
      soft_product_code = #{softProductCode,jdbcType=VARCHAR},
      no_settlement = #{noSettlement,jdbcType=VARCHAR},
      settlement_detail_name = #{settlementDetailName,jdbcType=VARCHAR},
      deliver_period = #{deliverPeriod,jdbcType=VARCHAR},
      soft_service_content = #{softServiceContent,jdbcType=VARCHAR},
      soft_price = #{softPrice,jdbcType=BIGINT},
      soft_total_price = #{softTotalPrice,jdbcType=BIGINT},
      zhuanhe_settle_price = #{zhuanheSettlePrice,jdbcType=BIGINT},
      service_package_name = #{servicePackageName,jdbcType=VARCHAR},
      service_contract = #{serviceContract,jdbcType=VARCHAR},
      sale_price = #{salePrice,jdbcType=BIGINT},
      sale_min_price = #{saleMinPrice,jdbcType=BIGINT},
      sale_max_price = #{saleMaxPrice,jdbcType=BIGINT},
      sale_out_of_price_range = #{saleOutOfPriceRange,jdbcType=VARCHAR},
      inventory = #{inventory,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 14:51:40 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_atom
    (id, flow_instance_id, flow_id, spu_code, sku_code, atom_name, settle_price, settle_price_check, 
      unit, service_content, cmiot_cost_project_id, min_purchase_num, province_purchase_contract, 
      iot_purchase_contract, material_num, atom_remark, atom_quantity, color, model, 
      hardware_price, soft_atom_name, soft_settle_price, soft_unit, soft_quantity, soft_product_code, 
      no_settlement, settlement_detail_name, deliver_period, soft_service_content, soft_price, 
      soft_total_price, zhuanhe_settle_price, service_package_name, service_contract, 
      sale_price, sale_min_price, sale_max_price, sale_out_of_price_range, inventory, 
      create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.flowInstanceId,jdbcType=VARCHAR}, #{item.flowId,jdbcType=VARCHAR}, 
        #{item.spuCode,jdbcType=VARCHAR}, #{item.skuCode,jdbcType=VARCHAR}, #{item.atomName,jdbcType=VARCHAR}, 
        #{item.settlePrice,jdbcType=BIGINT}, #{item.settlePriceCheck,jdbcType=BIGINT}, 
        #{item.unit,jdbcType=VARCHAR}, #{item.serviceContent,jdbcType=VARCHAR}, #{item.cmiotCostProjectId,jdbcType=VARCHAR}, 
        #{item.minPurchaseNum,jdbcType=INTEGER}, #{item.provincePurchaseContract,jdbcType=VARCHAR}, 
        #{item.iotPurchaseContract,jdbcType=VARCHAR}, #{item.materialNum,jdbcType=VARCHAR}, 
        #{item.atomRemark,jdbcType=VARCHAR}, #{item.atomQuantity,jdbcType=INTEGER}, #{item.color,jdbcType=VARCHAR}, 
        #{item.model,jdbcType=VARCHAR}, #{item.hardwarePrice,jdbcType=BIGINT}, #{item.softAtomName,jdbcType=VARCHAR}, 
        #{item.softSettlePrice,jdbcType=BIGINT}, #{item.softUnit,jdbcType=VARCHAR}, #{item.softQuantity,jdbcType=INTEGER}, 
        #{item.softProductCode,jdbcType=VARCHAR}, #{item.noSettlement,jdbcType=VARCHAR}, 
        #{item.settlementDetailName,jdbcType=VARCHAR}, #{item.deliverPeriod,jdbcType=VARCHAR}, 
        #{item.softServiceContent,jdbcType=VARCHAR}, #{item.softPrice,jdbcType=BIGINT}, 
        #{item.softTotalPrice,jdbcType=BIGINT}, #{item.zhuanheSettlePrice,jdbcType=BIGINT}, 
        #{item.servicePackageName,jdbcType=VARCHAR}, #{item.serviceContract,jdbcType=VARCHAR}, 
        #{item.salePrice,jdbcType=BIGINT}, #{item.saleMinPrice,jdbcType=BIGINT}, #{item.saleMaxPrice,jdbcType=BIGINT}, 
        #{item.saleOutOfPriceRange,jdbcType=VARCHAR}, #{item.inventory,jdbcType=INTEGER}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 14:51:40 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_atom (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'flow_instance_id'.toString() == column.value">
          #{item.flowInstanceId,jdbcType=VARCHAR}
        </if>
        <if test="'flow_id'.toString() == column.value">
          #{item.flowId,jdbcType=VARCHAR}
        </if>
        <if test="'spu_code'.toString() == column.value">
          #{item.spuCode,jdbcType=VARCHAR}
        </if>
        <if test="'sku_code'.toString() == column.value">
          #{item.skuCode,jdbcType=VARCHAR}
        </if>
        <if test="'atom_name'.toString() == column.value">
          #{item.atomName,jdbcType=VARCHAR}
        </if>
        <if test="'settle_price'.toString() == column.value">
          #{item.settlePrice,jdbcType=BIGINT}
        </if>
        <if test="'settle_price_check'.toString() == column.value">
          #{item.settlePriceCheck,jdbcType=BIGINT}
        </if>
        <if test="'unit'.toString() == column.value">
          #{item.unit,jdbcType=VARCHAR}
        </if>
        <if test="'service_content'.toString() == column.value">
          #{item.serviceContent,jdbcType=VARCHAR}
        </if>
        <if test="'cmiot_cost_project_id'.toString() == column.value">
          #{item.cmiotCostProjectId,jdbcType=VARCHAR}
        </if>
        <if test="'min_purchase_num'.toString() == column.value">
          #{item.minPurchaseNum,jdbcType=INTEGER}
        </if>
        <if test="'province_purchase_contract'.toString() == column.value">
          #{item.provincePurchaseContract,jdbcType=VARCHAR}
        </if>
        <if test="'iot_purchase_contract'.toString() == column.value">
          #{item.iotPurchaseContract,jdbcType=VARCHAR}
        </if>
        <if test="'material_num'.toString() == column.value">
          #{item.materialNum,jdbcType=VARCHAR}
        </if>
        <if test="'atom_remark'.toString() == column.value">
          #{item.atomRemark,jdbcType=VARCHAR}
        </if>
        <if test="'atom_quantity'.toString() == column.value">
          #{item.atomQuantity,jdbcType=INTEGER}
        </if>
        <if test="'color'.toString() == column.value">
          #{item.color,jdbcType=VARCHAR}
        </if>
        <if test="'model'.toString() == column.value">
          #{item.model,jdbcType=VARCHAR}
        </if>
        <if test="'hardware_price'.toString() == column.value">
          #{item.hardwarePrice,jdbcType=BIGINT}
        </if>
        <if test="'soft_atom_name'.toString() == column.value">
          #{item.softAtomName,jdbcType=VARCHAR}
        </if>
        <if test="'soft_settle_price'.toString() == column.value">
          #{item.softSettlePrice,jdbcType=BIGINT}
        </if>
        <if test="'soft_unit'.toString() == column.value">
          #{item.softUnit,jdbcType=VARCHAR}
        </if>
        <if test="'soft_quantity'.toString() == column.value">
          #{item.softQuantity,jdbcType=INTEGER}
        </if>
        <if test="'soft_product_code'.toString() == column.value">
          #{item.softProductCode,jdbcType=VARCHAR}
        </if>
        <if test="'no_settlement'.toString() == column.value">
          #{item.noSettlement,jdbcType=VARCHAR}
        </if>
        <if test="'settlement_detail_name'.toString() == column.value">
          #{item.settlementDetailName,jdbcType=VARCHAR}
        </if>
        <if test="'deliver_period'.toString() == column.value">
          #{item.deliverPeriod,jdbcType=VARCHAR}
        </if>
        <if test="'soft_service_content'.toString() == column.value">
          #{item.softServiceContent,jdbcType=VARCHAR}
        </if>
        <if test="'soft_price'.toString() == column.value">
          #{item.softPrice,jdbcType=BIGINT}
        </if>
        <if test="'soft_total_price'.toString() == column.value">
          #{item.softTotalPrice,jdbcType=BIGINT}
        </if>
        <if test="'zhuanhe_settle_price'.toString() == column.value">
          #{item.zhuanheSettlePrice,jdbcType=BIGINT}
        </if>
        <if test="'service_package_name'.toString() == column.value">
          #{item.servicePackageName,jdbcType=VARCHAR}
        </if>
        <if test="'service_contract'.toString() == column.value">
          #{item.serviceContract,jdbcType=VARCHAR}
        </if>
        <if test="'sale_price'.toString() == column.value">
          #{item.salePrice,jdbcType=BIGINT}
        </if>
        <if test="'sale_min_price'.toString() == column.value">
          #{item.saleMinPrice,jdbcType=BIGINT}
        </if>
        <if test="'sale_max_price'.toString() == column.value">
          #{item.saleMaxPrice,jdbcType=BIGINT}
        </if>
        <if test="'sale_out_of_price_range'.toString() == column.value">
          #{item.saleOutOfPriceRange,jdbcType=VARCHAR}
        </if>
        <if test="'inventory'.toString() == column.value">
          #{item.inventory,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>