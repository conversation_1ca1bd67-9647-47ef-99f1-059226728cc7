<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.OpenAbilityThemeContentMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.OpenAbilityThemeContent">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 06 16:42:10 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="theme_id" jdbcType="VARCHAR" property="themeId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="path_code" jdbcType="VARCHAR" property="pathCode" />
    <result column="intro_url" jdbcType="VARCHAR" property="introUrl" />
    <result column="method" jdbcType="VARCHAR" property="method" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 06 16:42:10 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 06 16:42:10 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 06 16:42:10 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, theme_id, type, name, path_code, intro_url, method
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeContentExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 06 16:42:10 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from open_ability_theme_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 06 16:42:10 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from open_ability_theme_content
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 06 16:42:10 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from open_ability_theme_content
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeContentExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 06 16:42:10 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from open_ability_theme_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeContent">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 06 16:42:10 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into open_ability_theme_content (id, theme_id, type, 
      name, path_code, intro_url, 
      method)
    values (#{id,jdbcType=VARCHAR}, #{themeId,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{pathCode,jdbcType=VARCHAR}, #{introUrl,jdbcType=VARCHAR}, 
      #{method,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeContent">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 06 16:42:10 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into open_ability_theme_content
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="themeId != null">
        theme_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="pathCode != null">
        path_code,
      </if>
      <if test="introUrl != null">
        intro_url,
      </if>
      <if test="method != null">
        method,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="themeId != null">
        #{themeId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="pathCode != null">
        #{pathCode,jdbcType=VARCHAR},
      </if>
      <if test="introUrl != null">
        #{introUrl,jdbcType=VARCHAR},
      </if>
      <if test="method != null">
        #{method,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeContentExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 06 16:42:10 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from open_ability_theme_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 06 16:42:10 CST 2024. by MyBatis Generator, do not modify.
    -->
    update open_ability_theme_content
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.themeId != null">
        theme_id = #{record.themeId,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.pathCode != null">
        path_code = #{record.pathCode,jdbcType=VARCHAR},
      </if>
      <if test="record.introUrl != null">
        intro_url = #{record.introUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.method != null">
        method = #{record.method,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 06 16:42:10 CST 2024. by MyBatis Generator, do not modify.
    -->
    update open_ability_theme_content
    set id = #{record.id,jdbcType=VARCHAR},
      theme_id = #{record.themeId,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      path_code = #{record.pathCode,jdbcType=VARCHAR},
      intro_url = #{record.introUrl,jdbcType=VARCHAR},
      method = #{record.method,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeContent">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 06 16:42:10 CST 2024. by MyBatis Generator, do not modify.
    -->
    update open_ability_theme_content
    <set>
      <if test="themeId != null">
        theme_id = #{themeId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="pathCode != null">
        path_code = #{pathCode,jdbcType=VARCHAR},
      </if>
      <if test="introUrl != null">
        intro_url = #{introUrl,jdbcType=VARCHAR},
      </if>
      <if test="method != null">
        method = #{method,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeContent">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 06 16:42:10 CST 2024. by MyBatis Generator, do not modify.
    -->
    update open_ability_theme_content
    set theme_id = #{themeId,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      path_code = #{pathCode,jdbcType=VARCHAR},
      intro_url = #{introUrl,jdbcType=VARCHAR},
      method = #{method,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 06 16:42:10 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into open_ability_theme_content
    (id, theme_id, type, name, path_code, intro_url, method)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.themeId,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR}, 
        #{item.name,jdbcType=VARCHAR}, #{item.pathCode,jdbcType=VARCHAR}, #{item.introUrl,jdbcType=VARCHAR}, 
        #{item.method,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jun 06 16:42:10 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into open_ability_theme_content (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'theme_id'.toString() == column.value">
          #{item.themeId,jdbcType=VARCHAR}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'path_code'.toString() == column.value">
          #{item.pathCode,jdbcType=VARCHAR}
        </if>
        <if test="'intro_url'.toString() == column.value">
          #{item.introUrl,jdbcType=VARCHAR}
        </if>
        <if test="'method'.toString() == column.value">
          #{item.method,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>