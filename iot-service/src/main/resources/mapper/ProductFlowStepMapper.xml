<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ProductFlowStepMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.ProductFlowStep">
    <!--
<<<<<<< HEAD
      WARNING - @mbg.generated  This element was generated on Fri Mar 22 16:35:05 CST 2024. by MyBatis Generator, do not modify.
=======
      WARNING - @mbg.generated  This element was generated on Thu Mar 21 15:45:46 CST 2024. by MyBatis Generator, do not modify.
>>>>>>> feature-product-flow
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="flow_id" jdbcType="VARCHAR" property="flowId" />
    <result column="step_index" jdbcType="INTEGER" property="stepIndex" />
    <result column="step_name" jdbcType="VARCHAR" property="stepName" />
    <result column="tip" jdbcType="VARCHAR" property="tip" />
    <result column="assignee_role_id" jdbcType="VARCHAR" property="assigneeRoleId" />
    <result column="reject_next_step_id" jdbcType="VARCHAR" property="rejectNextStepId" />
    <result column="allow_redirect" jdbcType="BIT" property="allowRedirect" />
    <result column="redirect_role_id" jdbcType="VARCHAR" property="redirectRoleId" />
    <result column="allow_known" jdbcType="BIT" property="allowKnown" />
    <result column="known_role_id" jdbcType="VARCHAR" property="knownRoleId" />
    <result column="allow_limit" jdbcType="BIT" property="allowLimit" />
    <result column="limit_id" jdbcType="INTEGER" property="limitId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
<<<<<<< HEAD
      WARNING - @mbg.generated  This element was generated on Fri Mar 22 16:35:05 CST 2024. by MyBatis Generator, do not modify.
=======
      WARNING - @mbg.generated  This element was generated on Thu Mar 21 15:45:46 CST 2024. by MyBatis Generator, do not modify.
>>>>>>> feature-product-flow
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
<<<<<<< HEAD
      WARNING - @mbg.generated  This element was generated on Fri Mar 22 16:35:05 CST 2024. by MyBatis Generator, do not modify.
=======
      WARNING - @mbg.generated  This element was generated on Thu Mar 21 15:45:46 CST 2024. by MyBatis Generator, do not modify.
>>>>>>> feature-product-flow
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
<<<<<<< HEAD
      WARNING - @mbg.generated  This element was generated on Fri Mar 22 16:35:05 CST 2024. by MyBatis Generator, do not modify.
=======
      WARNING - @mbg.generated  This element was generated on Thu Mar 21 15:45:46 CST 2024. by MyBatis Generator, do not modify.
>>>>>>> feature-product-flow
    -->
    id, flow_id, step_index, step_name, tip, assignee_role_id, reject_next_step_id, allow_redirect, 
    redirect_role_id, allow_known, known_role_id, allow_limit, limit_id, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowStepExample" resultMap="BaseResultMap">
    <!--
<<<<<<< HEAD
      WARNING - @mbg.generated  This element was generated on Fri Mar 22 16:35:05 CST 2024. by MyBatis Generator, do not modify.
=======
      WARNING - @mbg.generated  This element was generated on Thu Mar 21 15:45:46 CST 2024. by MyBatis Generator, do not modify.
>>>>>>> feature-product-flow
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from product_flow_step
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
<<<<<<< HEAD
      WARNING - @mbg.generated  This element was generated on Fri Mar 22 16:35:05 CST 2024. by MyBatis Generator, do not modify.
=======
      WARNING - @mbg.generated  This element was generated on Thu Mar 21 15:45:46 CST 2024. by MyBatis Generator, do not modify.
>>>>>>> feature-product-flow
    -->
    select 
    <include refid="Base_Column_List" />
    from product_flow_step
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
<<<<<<< HEAD
      WARNING - @mbg.generated  This element was generated on Fri Mar 22 16:35:05 CST 2024. by MyBatis Generator, do not modify.
=======
      WARNING - @mbg.generated  This element was generated on Thu Mar 21 15:45:46 CST 2024. by MyBatis Generator, do not modify.
>>>>>>> feature-product-flow
    -->
    delete from product_flow_step
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowStepExample">
    <!--
<<<<<<< HEAD
      WARNING - @mbg.generated  This element was generated on Fri Mar 22 16:35:05 CST 2024. by MyBatis Generator, do not modify.
=======
      WARNING - @mbg.generated  This element was generated on Thu Mar 21 15:45:46 CST 2024. by MyBatis Generator, do not modify.
>>>>>>> feature-product-flow
    -->
    delete from product_flow_step
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowStep">
    <!--
<<<<<<< HEAD
      WARNING - @mbg.generated  This element was generated on Fri Mar 22 16:35:05 CST 2024. by MyBatis Generator, do not modify.
=======
      WARNING - @mbg.generated  This element was generated on Thu Mar 21 15:45:46 CST 2024. by MyBatis Generator, do not modify.
>>>>>>> feature-product-flow
    -->
    insert into product_flow_step (id, flow_id, step_index, 
      step_name, tip, assignee_role_id, 
      reject_next_step_id, allow_redirect, redirect_role_id, 
      allow_known, known_role_id, allow_limit, 
      limit_id, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{flowId,jdbcType=VARCHAR}, #{stepIndex,jdbcType=INTEGER}, 
      #{stepName,jdbcType=VARCHAR}, #{tip,jdbcType=VARCHAR}, #{assigneeRoleId,jdbcType=VARCHAR}, 
      #{rejectNextStepId,jdbcType=VARCHAR}, #{allowRedirect,jdbcType=BIT}, #{redirectRoleId,jdbcType=VARCHAR}, 
      #{allowKnown,jdbcType=BIT}, #{knownRoleId,jdbcType=VARCHAR}, #{allowLimit,jdbcType=BIT}, 
      #{limitId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowStep">
    <!--
<<<<<<< HEAD
      WARNING - @mbg.generated  This element was generated on Fri Mar 22 16:35:05 CST 2024. by MyBatis Generator, do not modify.
=======
      WARNING - @mbg.generated  This element was generated on Thu Mar 21 15:45:46 CST 2024. by MyBatis Generator, do not modify.
>>>>>>> feature-product-flow
    -->
    insert into product_flow_step
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="flowId != null">
        flow_id,
      </if>
      <if test="stepIndex != null">
        step_index,
      </if>
      <if test="stepName != null">
        step_name,
      </if>
      <if test="tip != null">
        tip,
      </if>
      <if test="assigneeRoleId != null">
        assignee_role_id,
      </if>
      <if test="rejectNextStepId != null">
        reject_next_step_id,
      </if>
      <if test="allowRedirect != null">
        allow_redirect,
      </if>
      <if test="redirectRoleId != null">
        redirect_role_id,
      </if>
      <if test="allowKnown != null">
        allow_known,
      </if>
      <if test="knownRoleId != null">
        known_role_id,
      </if>
      <if test="allowLimit != null">
        allow_limit,
      </if>
      <if test="limitId != null">
        limit_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="flowId != null">
        #{flowId,jdbcType=VARCHAR},
      </if>
      <if test="stepIndex != null">
        #{stepIndex,jdbcType=INTEGER},
      </if>
      <if test="stepName != null">
        #{stepName,jdbcType=VARCHAR},
      </if>
      <if test="tip != null">
        #{tip,jdbcType=VARCHAR},
      </if>
      <if test="assigneeRoleId != null">
        #{assigneeRoleId,jdbcType=VARCHAR},
      </if>
      <if test="rejectNextStepId != null">
        #{rejectNextStepId,jdbcType=VARCHAR},
      </if>
      <if test="allowRedirect != null">
        #{allowRedirect,jdbcType=BIT},
      </if>
      <if test="redirectRoleId != null">
        #{redirectRoleId,jdbcType=VARCHAR},
      </if>
      <if test="allowKnown != null">
        #{allowKnown,jdbcType=BIT},
      </if>
      <if test="knownRoleId != null">
        #{knownRoleId,jdbcType=VARCHAR},
      </if>
      <if test="allowLimit != null">
        #{allowLimit,jdbcType=BIT},
      </if>
      <if test="limitId != null">
        #{limitId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowStepExample" resultType="java.lang.Long">
    <!--
<<<<<<< HEAD
      WARNING - @mbg.generated  This element was generated on Fri Mar 22 16:35:05 CST 2024. by MyBatis Generator, do not modify.
=======
      WARNING - @mbg.generated  This element was generated on Thu Mar 21 15:45:46 CST 2024. by MyBatis Generator, do not modify.
>>>>>>> feature-product-flow
    -->
    select count(*) from product_flow_step
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
<<<<<<< HEAD
      WARNING - @mbg.generated  This element was generated on Fri Mar 22 16:35:05 CST 2024. by MyBatis Generator, do not modify.
=======
      WARNING - @mbg.generated  This element was generated on Thu Mar 21 15:45:46 CST 2024. by MyBatis Generator, do not modify.
>>>>>>> feature-product-flow
    -->
    update product_flow_step
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.flowId != null">
        flow_id = #{record.flowId,jdbcType=VARCHAR},
      </if>
      <if test="record.stepIndex != null">
        step_index = #{record.stepIndex,jdbcType=INTEGER},
      </if>
      <if test="record.stepName != null">
        step_name = #{record.stepName,jdbcType=VARCHAR},
      </if>
      <if test="record.tip != null">
        tip = #{record.tip,jdbcType=VARCHAR},
      </if>
      <if test="record.assigneeRoleId != null">
        assignee_role_id = #{record.assigneeRoleId,jdbcType=VARCHAR},
      </if>
      <if test="record.rejectNextStepId != null">
        reject_next_step_id = #{record.rejectNextStepId,jdbcType=VARCHAR},
      </if>
      <if test="record.allowRedirect != null">
        allow_redirect = #{record.allowRedirect,jdbcType=BIT},
      </if>
      <if test="record.redirectRoleId != null">
        redirect_role_id = #{record.redirectRoleId,jdbcType=VARCHAR},
      </if>
      <if test="record.allowKnown != null">
        allow_known = #{record.allowKnown,jdbcType=BIT},
      </if>
      <if test="record.knownRoleId != null">
        known_role_id = #{record.knownRoleId,jdbcType=VARCHAR},
      </if>
      <if test="record.allowLimit != null">
        allow_limit = #{record.allowLimit,jdbcType=BIT},
      </if>
      <if test="record.limitId != null">
        limit_id = #{record.limitId,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
<<<<<<< HEAD
      WARNING - @mbg.generated  This element was generated on Fri Mar 22 16:35:05 CST 2024. by MyBatis Generator, do not modify.
=======
      WARNING - @mbg.generated  This element was generated on Thu Mar 21 15:45:46 CST 2024. by MyBatis Generator, do not modify.
>>>>>>> feature-product-flow
    -->
    update product_flow_step
    set id = #{record.id,jdbcType=VARCHAR},
      flow_id = #{record.flowId,jdbcType=VARCHAR},
      step_index = #{record.stepIndex,jdbcType=INTEGER},
      step_name = #{record.stepName,jdbcType=VARCHAR},
      tip = #{record.tip,jdbcType=VARCHAR},
      assignee_role_id = #{record.assigneeRoleId,jdbcType=VARCHAR},
      reject_next_step_id = #{record.rejectNextStepId,jdbcType=VARCHAR},
      allow_redirect = #{record.allowRedirect,jdbcType=BIT},
      redirect_role_id = #{record.redirectRoleId,jdbcType=VARCHAR},
      allow_known = #{record.allowKnown,jdbcType=BIT},
      known_role_id = #{record.knownRoleId,jdbcType=VARCHAR},
      allow_limit = #{record.allowLimit,jdbcType=BIT},
      limit_id = #{record.limitId,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowStep">
    <!--
<<<<<<< HEAD
      WARNING - @mbg.generated  This element was generated on Fri Mar 22 16:35:05 CST 2024. by MyBatis Generator, do not modify.
=======
      WARNING - @mbg.generated  This element was generated on Thu Mar 21 15:45:46 CST 2024. by MyBatis Generator, do not modify.
>>>>>>> feature-product-flow
    -->
    update product_flow_step
    <set>
      <if test="flowId != null">
        flow_id = #{flowId,jdbcType=VARCHAR},
      </if>
      <if test="stepIndex != null">
        step_index = #{stepIndex,jdbcType=INTEGER},
      </if>
      <if test="stepName != null">
        step_name = #{stepName,jdbcType=VARCHAR},
      </if>
      <if test="tip != null">
        tip = #{tip,jdbcType=VARCHAR},
      </if>
      <if test="assigneeRoleId != null">
        assignee_role_id = #{assigneeRoleId,jdbcType=VARCHAR},
      </if>
      <if test="rejectNextStepId != null">
        reject_next_step_id = #{rejectNextStepId,jdbcType=VARCHAR},
      </if>
      <if test="allowRedirect != null">
        allow_redirect = #{allowRedirect,jdbcType=BIT},
      </if>
      <if test="redirectRoleId != null">
        redirect_role_id = #{redirectRoleId,jdbcType=VARCHAR},
      </if>
      <if test="allowKnown != null">
        allow_known = #{allowKnown,jdbcType=BIT},
      </if>
      <if test="knownRoleId != null">
        known_role_id = #{knownRoleId,jdbcType=VARCHAR},
      </if>
      <if test="allowLimit != null">
        allow_limit = #{allowLimit,jdbcType=BIT},
      </if>
      <if test="limitId != null">
        limit_id = #{limitId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowStep">
    <!--
<<<<<<< HEAD
      WARNING - @mbg.generated  This element was generated on Fri Mar 22 16:35:05 CST 2024. by MyBatis Generator, do not modify.
=======
      WARNING - @mbg.generated  This element was generated on Thu Mar 21 15:45:46 CST 2024. by MyBatis Generator, do not modify.
>>>>>>> feature-product-flow
    -->
    update product_flow_step
    set flow_id = #{flowId,jdbcType=VARCHAR},
      step_index = #{stepIndex,jdbcType=INTEGER},
      step_name = #{stepName,jdbcType=VARCHAR},
      tip = #{tip,jdbcType=VARCHAR},
      assignee_role_id = #{assigneeRoleId,jdbcType=VARCHAR},
      reject_next_step_id = #{rejectNextStepId,jdbcType=VARCHAR},
      allow_redirect = #{allowRedirect,jdbcType=BIT},
      redirect_role_id = #{redirectRoleId,jdbcType=VARCHAR},
      allow_known = #{allowKnown,jdbcType=BIT},
      known_role_id = #{knownRoleId,jdbcType=VARCHAR},
      allow_limit = #{allowLimit,jdbcType=BIT},
      limit_id = #{limitId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
<<<<<<< HEAD
      WARNING - @mbg.generated  This element was generated on Fri Mar 22 16:35:05 CST 2024. by MyBatis Generator, do not modify.
=======
      WARNING - @mbg.generated  This element was generated on Thu Mar 21 15:45:46 CST 2024. by MyBatis Generator, do not modify.
>>>>>>> feature-product-flow
    -->
    insert into product_flow_step
    (id, flow_id, step_index, step_name, tip, assignee_role_id, reject_next_step_id, 
      allow_redirect, redirect_role_id, allow_known, known_role_id, allow_limit, limit_id, 
      create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.flowId,jdbcType=VARCHAR}, #{item.stepIndex,jdbcType=INTEGER}, 
        #{item.stepName,jdbcType=VARCHAR}, #{item.tip,jdbcType=VARCHAR}, #{item.assigneeRoleId,jdbcType=VARCHAR}, 
        #{item.rejectNextStepId,jdbcType=VARCHAR}, #{item.allowRedirect,jdbcType=BIT}, 
        #{item.redirectRoleId,jdbcType=VARCHAR}, #{item.allowKnown,jdbcType=BIT}, #{item.knownRoleId,jdbcType=VARCHAR}, 
        #{item.allowLimit,jdbcType=BIT}, #{item.limitId,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
<<<<<<< HEAD
      WARNING - @mbg.generated  This element was generated on Fri Mar 22 16:35:05 CST 2024. by MyBatis Generator, do not modify.
=======
      WARNING - @mbg.generated  This element was generated on Thu Mar 21 15:45:46 CST 2024. by MyBatis Generator, do not modify.
>>>>>>> feature-product-flow
    -->
    insert into product_flow_step (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'flow_id'.toString() == column.value">
          #{item.flowId,jdbcType=VARCHAR}
        </if>
        <if test="'step_index'.toString() == column.value">
          #{item.stepIndex,jdbcType=INTEGER}
        </if>
        <if test="'step_name'.toString() == column.value">
          #{item.stepName,jdbcType=VARCHAR}
        </if>
        <if test="'tip'.toString() == column.value">
          #{item.tip,jdbcType=VARCHAR}
        </if>
        <if test="'assignee_role_id'.toString() == column.value">
          #{item.assigneeRoleId,jdbcType=VARCHAR}
        </if>
        <if test="'reject_next_step_id'.toString() == column.value">
          #{item.rejectNextStepId,jdbcType=VARCHAR}
        </if>
        <if test="'allow_redirect'.toString() == column.value">
          #{item.allowRedirect,jdbcType=BIT}
        </if>
        <if test="'redirect_role_id'.toString() == column.value">
          #{item.redirectRoleId,jdbcType=VARCHAR}
        </if>
        <if test="'allow_known'.toString() == column.value">
          #{item.allowKnown,jdbcType=BIT}
        </if>
        <if test="'known_role_id'.toString() == column.value">
          #{item.knownRoleId,jdbcType=VARCHAR}
        </if>
        <if test="'allow_limit'.toString() == column.value">
          #{item.allowLimit,jdbcType=BIT}
        </if>
        <if test="'limit_id'.toString() == column.value">
          #{item.limitId,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>