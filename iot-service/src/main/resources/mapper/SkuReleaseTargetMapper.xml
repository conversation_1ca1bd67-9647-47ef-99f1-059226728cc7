<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.SkuReleaseTargetMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.SkuReleaseTarget">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 13 15:39:00 CST 2022. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="sku_offering_code" jdbcType="VARCHAR" property="skuOfferingCode" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 13 15:39:00 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 13 15:39:00 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 13 15:39:00 CST 2022. by MyBatis Generator, do not modify.
    -->
    id, sku_offering_code, province_code, city_code
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.SkuReleaseTargetExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 13 15:39:00 CST 2022. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sku_release_target
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 13 15:39:00 CST 2022. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from sku_release_target
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 13 15:39:00 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from sku_release_target
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.SkuReleaseTargetExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 13 15:39:00 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from sku_release_target
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.SkuReleaseTarget">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 13 15:39:00 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into sku_release_target (id, sku_offering_code, province_code, 
      city_code)
    values (#{id,jdbcType=VARCHAR}, #{skuOfferingCode,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, 
      #{cityCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.SkuReleaseTarget">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 13 15:39:00 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into sku_release_target
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="skuOfferingCode != null">
        sku_offering_code,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingCode != null">
        #{skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.SkuReleaseTargetExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 13 15:39:00 CST 2022. by MyBatis Generator, do not modify.
    -->
    select count(*) from sku_release_target
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 13 15:39:00 CST 2022. by MyBatis Generator, do not modify.
    -->
    update sku_release_target
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.skuOfferingCode != null">
        sku_offering_code = #{record.skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 13 15:39:00 CST 2022. by MyBatis Generator, do not modify.
    -->
    update sku_release_target
    set id = #{record.id,jdbcType=VARCHAR},
      sku_offering_code = #{record.skuOfferingCode,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.SkuReleaseTarget">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 13 15:39:00 CST 2022. by MyBatis Generator, do not modify.
    -->
    update sku_release_target
    <set>
      <if test="skuOfferingCode != null">
        sku_offering_code = #{skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        province_code = #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.SkuReleaseTarget">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 13 15:39:00 CST 2022. by MyBatis Generator, do not modify.
    -->
    update sku_release_target
    set sku_offering_code = #{skuOfferingCode,jdbcType=VARCHAR},
      province_code = #{provinceCode,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 13 15:39:00 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into sku_release_target
    (id, sku_offering_code, province_code, city_code)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.skuOfferingCode,jdbcType=VARCHAR}, #{item.provinceCode,jdbcType=VARCHAR}, 
        #{item.cityCode,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Sep 13 15:39:00 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into sku_release_target (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'sku_offering_code'.toString() == column.value">
          #{item.skuOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'province_code'.toString() == column.value">
          #{item.provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="'city_code'.toString() == column.value">
          #{item.cityCode,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>