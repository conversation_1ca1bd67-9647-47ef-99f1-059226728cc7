<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.OrderRemarksInfoMapper">
    <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.OrderRemarksInfo">
      <!--
        WARNING - @mbg.generated
      -->
      <id column="order_id" jdbcType="VARCHAR" property="orderId" />
      <result column="order_remark" jdbcType="VARCHAR" property="orderRemark" />
      <result column="create_user" jdbcType="VARCHAR" property="createUser" />
      <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    </resultMap>
  </mapper>
