<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.SkuMsisdnRelationMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.SkuMsisdnRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 06 17:18:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="order_atom_info_id" jdbcType="VARCHAR" property="orderAtomInfoId" />
    <result column="msisdn" jdbcType="VARCHAR" property="msisdn" />
    <result column="imei" jdbcType="VARCHAR" property="imei" />
    <result column="order_result" jdbcType="INTEGER" property="orderResult" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="temp_iccid" jdbcType="VARCHAR" property="tempIccid" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 06 17:18:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 06 17:18:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 06 17:18:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    id, order_id, order_atom_info_id, msisdn, imei, order_result, create_time, update_time, 
    temp_iccid
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.SkuMsisdnRelationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 06 17:18:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sku_msisdn_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 06 17:18:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from sku_msisdn_relation
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 06 17:18:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from sku_msisdn_relation
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.SkuMsisdnRelationExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 06 17:18:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from sku_msisdn_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.SkuMsisdnRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 06 17:18:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into sku_msisdn_relation (id, order_id, order_atom_info_id, 
      msisdn, imei, order_result, 
      create_time, update_time, temp_iccid
      )
    values (#{id,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{orderAtomInfoId,jdbcType=VARCHAR}, 
      #{msisdn,jdbcType=VARCHAR}, #{imei,jdbcType=VARCHAR}, #{orderResult,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{tempIccid,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.SkuMsisdnRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 06 17:18:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into sku_msisdn_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderAtomInfoId != null">
        order_atom_info_id,
      </if>
      <if test="msisdn != null">
        msisdn,
      </if>
      <if test="imei != null">
        imei,
      </if>
      <if test="orderResult != null">
        order_result,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tempIccid != null">
        temp_iccid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderAtomInfoId != null">
        #{orderAtomInfoId,jdbcType=VARCHAR},
      </if>
      <if test="msisdn != null">
        #{msisdn,jdbcType=VARCHAR},
      </if>
      <if test="imei != null">
        #{imei,jdbcType=VARCHAR},
      </if>
      <if test="orderResult != null">
        #{orderResult,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tempIccid != null">
        #{tempIccid,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.SkuMsisdnRelationExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 06 17:18:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    select count(*) from sku_msisdn_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 06 17:18:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    update sku_msisdn_relation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderAtomInfoId != null">
        order_atom_info_id = #{record.orderAtomInfoId,jdbcType=VARCHAR},
      </if>
      <if test="record.msisdn != null">
        msisdn = #{record.msisdn,jdbcType=VARCHAR},
      </if>
      <if test="record.imei != null">
        imei = #{record.imei,jdbcType=VARCHAR},
      </if>
      <if test="record.orderResult != null">
        order_result = #{record.orderResult,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tempIccid != null">
        temp_iccid = #{record.tempIccid,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 06 17:18:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    update sku_msisdn_relation
    set id = #{record.id,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      order_atom_info_id = #{record.orderAtomInfoId,jdbcType=VARCHAR},
      msisdn = #{record.msisdn,jdbcType=VARCHAR},
      imei = #{record.imei,jdbcType=VARCHAR},
      order_result = #{record.orderResult,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      temp_iccid = #{record.tempIccid,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.SkuMsisdnRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 06 17:18:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    update sku_msisdn_relation
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderAtomInfoId != null">
        order_atom_info_id = #{orderAtomInfoId,jdbcType=VARCHAR},
      </if>
      <if test="msisdn != null">
        msisdn = #{msisdn,jdbcType=VARCHAR},
      </if>
      <if test="imei != null">
        imei = #{imei,jdbcType=VARCHAR},
      </if>
      <if test="orderResult != null">
        order_result = #{orderResult,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tempIccid != null">
        temp_iccid = #{tempIccid,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.SkuMsisdnRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 06 17:18:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    update sku_msisdn_relation
    set order_id = #{orderId,jdbcType=VARCHAR},
      order_atom_info_id = #{orderAtomInfoId,jdbcType=VARCHAR},
      msisdn = #{msisdn,jdbcType=VARCHAR},
      imei = #{imei,jdbcType=VARCHAR},
      order_result = #{orderResult,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      temp_iccid = #{tempIccid,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 06 17:18:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into sku_msisdn_relation
    (id, order_id, order_atom_info_id, msisdn, imei, order_result, create_time, update_time, 
      temp_iccid)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR}, #{item.orderAtomInfoId,jdbcType=VARCHAR}, 
        #{item.msisdn,jdbcType=VARCHAR}, #{item.imei,jdbcType=VARCHAR}, #{item.orderResult,jdbcType=INTEGER}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.tempIccid,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 06 17:18:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into sku_msisdn_relation (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'order_atom_info_id'.toString() == column.value">
          #{item.orderAtomInfoId,jdbcType=VARCHAR}
        </if>
        <if test="'msisdn'.toString() == column.value">
          #{item.msisdn,jdbcType=VARCHAR}
        </if>
        <if test="'imei'.toString() == column.value">
          #{item.imei,jdbcType=VARCHAR}
        </if>
        <if test="'order_result'.toString() == column.value">
          #{item.orderResult,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'temp_iccid'.toString() == column.value">
          #{item.tempIccid,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>