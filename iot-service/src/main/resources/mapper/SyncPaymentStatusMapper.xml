<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.SyncPaymentStatusMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.SyncPaymentStatus">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Mar 23 10:27:27 CST 2023. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="segment1" jdbcType="VARCHAR" property="segment1" />
    <result column="iot_mall_number" jdbcType="VARCHAR" property="iotMallNumber" />
    <result column="po_segment1" jdbcType="VARCHAR" property="poSegment1" />
    <result column="reim_no" jdbcType="VARCHAR" property="reimNo" />
    <result column="payment_amount" jdbcType="DECIMAL" property="paymentAmount" />
    <result column="status_code" jdbcType="VARCHAR" property="statusCode" />
    <result column="status_name" jdbcType="VARCHAR" property="statusName" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="subscriber" jdbcType="VARCHAR" property="subscriber" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Mar 23 10:27:27 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Mar 23 10:27:27 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Mar 23 10:27:27 CST 2023. by MyBatis Generator, do not modify.
    -->
    id, segment1, iot_mall_number, po_segment1, reim_no, payment_amount, status_code, 
    status_name, province, subscriber, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.SyncPaymentStatusExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Mar 23 10:27:27 CST 2023. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sync_payment_status
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Mar 23 10:27:27 CST 2023. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from sync_payment_status
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Mar 23 10:27:27 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from sync_payment_status
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.SyncPaymentStatusExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Mar 23 10:27:27 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from sync_payment_status
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.SyncPaymentStatus">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Mar 23 10:27:27 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into sync_payment_status (id, segment1, iot_mall_number, 
      po_segment1, reim_no, payment_amount, 
      status_code, status_name, province, 
      subscriber, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{segment1,jdbcType=VARCHAR}, #{iotMallNumber,jdbcType=VARCHAR}, 
      #{poSegment1,jdbcType=VARCHAR}, #{reimNo,jdbcType=VARCHAR}, #{paymentAmount,jdbcType=DECIMAL}, 
      #{statusCode,jdbcType=VARCHAR}, #{statusName,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, 
      #{subscriber,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.SyncPaymentStatus">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Mar 23 10:27:27 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into sync_payment_status
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="segment1 != null">
        segment1,
      </if>
      <if test="iotMallNumber != null">
        iot_mall_number,
      </if>
      <if test="poSegment1 != null">
        po_segment1,
      </if>
      <if test="reimNo != null">
        reim_no,
      </if>
      <if test="paymentAmount != null">
        payment_amount,
      </if>
      <if test="statusCode != null">
        status_code,
      </if>
      <if test="statusName != null">
        status_name,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="subscriber != null">
        subscriber,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="segment1 != null">
        #{segment1,jdbcType=VARCHAR},
      </if>
      <if test="iotMallNumber != null">
        #{iotMallNumber,jdbcType=VARCHAR},
      </if>
      <if test="poSegment1 != null">
        #{poSegment1,jdbcType=VARCHAR},
      </if>
      <if test="reimNo != null">
        #{reimNo,jdbcType=VARCHAR},
      </if>
      <if test="paymentAmount != null">
        #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="statusCode != null">
        #{statusCode,jdbcType=VARCHAR},
      </if>
      <if test="statusName != null">
        #{statusName,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="subscriber != null">
        #{subscriber,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.SyncPaymentStatusExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Mar 23 10:27:27 CST 2023. by MyBatis Generator, do not modify.
    -->
    select count(*) from sync_payment_status
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Mar 23 10:27:27 CST 2023. by MyBatis Generator, do not modify.
    -->
    update sync_payment_status
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.segment1 != null">
        segment1 = #{record.segment1,jdbcType=VARCHAR},
      </if>
      <if test="record.iotMallNumber != null">
        iot_mall_number = #{record.iotMallNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.poSegment1 != null">
        po_segment1 = #{record.poSegment1,jdbcType=VARCHAR},
      </if>
      <if test="record.reimNo != null">
        reim_no = #{record.reimNo,jdbcType=VARCHAR},
      </if>
      <if test="record.paymentAmount != null">
        payment_amount = #{record.paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.statusCode != null">
        status_code = #{record.statusCode,jdbcType=VARCHAR},
      </if>
      <if test="record.statusName != null">
        status_name = #{record.statusName,jdbcType=VARCHAR},
      </if>
      <if test="record.province != null">
        province = #{record.province,jdbcType=VARCHAR},
      </if>
      <if test="record.subscriber != null">
        subscriber = #{record.subscriber,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Mar 23 10:27:27 CST 2023. by MyBatis Generator, do not modify.
    -->
    update sync_payment_status
    set id = #{record.id,jdbcType=VARCHAR},
      segment1 = #{record.segment1,jdbcType=VARCHAR},
      iot_mall_number = #{record.iotMallNumber,jdbcType=VARCHAR},
      po_segment1 = #{record.poSegment1,jdbcType=VARCHAR},
      reim_no = #{record.reimNo,jdbcType=VARCHAR},
      payment_amount = #{record.paymentAmount,jdbcType=DECIMAL},
      status_code = #{record.statusCode,jdbcType=VARCHAR},
      status_name = #{record.statusName,jdbcType=VARCHAR},
      province = #{record.province,jdbcType=VARCHAR},
      subscriber = #{record.subscriber,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.SyncPaymentStatus">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Mar 23 10:27:27 CST 2023. by MyBatis Generator, do not modify.
    -->
    update sync_payment_status
    <set>
      <if test="segment1 != null">
        segment1 = #{segment1,jdbcType=VARCHAR},
      </if>
      <if test="iotMallNumber != null">
        iot_mall_number = #{iotMallNumber,jdbcType=VARCHAR},
      </if>
      <if test="poSegment1 != null">
        po_segment1 = #{poSegment1,jdbcType=VARCHAR},
      </if>
      <if test="reimNo != null">
        reim_no = #{reimNo,jdbcType=VARCHAR},
      </if>
      <if test="paymentAmount != null">
        payment_amount = #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="statusCode != null">
        status_code = #{statusCode,jdbcType=VARCHAR},
      </if>
      <if test="statusName != null">
        status_name = #{statusName,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="subscriber != null">
        subscriber = #{subscriber,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.SyncPaymentStatus">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Mar 23 10:27:27 CST 2023. by MyBatis Generator, do not modify.
    -->
    update sync_payment_status
    set segment1 = #{segment1,jdbcType=VARCHAR},
      iot_mall_number = #{iotMallNumber,jdbcType=VARCHAR},
      po_segment1 = #{poSegment1,jdbcType=VARCHAR},
      reim_no = #{reimNo,jdbcType=VARCHAR},
      payment_amount = #{paymentAmount,jdbcType=DECIMAL},
      status_code = #{statusCode,jdbcType=VARCHAR},
      status_name = #{statusName,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      subscriber = #{subscriber,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Mar 23 10:27:27 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into sync_payment_status
    (id, segment1, iot_mall_number, po_segment1, reim_no, payment_amount, status_code, 
      status_name, province, subscriber, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.segment1,jdbcType=VARCHAR}, #{item.iotMallNumber,jdbcType=VARCHAR}, 
        #{item.poSegment1,jdbcType=VARCHAR}, #{item.reimNo,jdbcType=VARCHAR}, #{item.paymentAmount,jdbcType=DECIMAL}, 
        #{item.statusCode,jdbcType=VARCHAR}, #{item.statusName,jdbcType=VARCHAR}, #{item.province,jdbcType=VARCHAR}, 
        #{item.subscriber,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Mar 23 10:27:27 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into sync_payment_status (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'segment1'.toString() == column.value">
          #{item.segment1,jdbcType=VARCHAR}
        </if>
        <if test="'iot_mall_number'.toString() == column.value">
          #{item.iotMallNumber,jdbcType=VARCHAR}
        </if>
        <if test="'po_segment1'.toString() == column.value">
          #{item.poSegment1,jdbcType=VARCHAR}
        </if>
        <if test="'reim_no'.toString() == column.value">
          #{item.reimNo,jdbcType=VARCHAR}
        </if>
        <if test="'payment_amount'.toString() == column.value">
          #{item.paymentAmount,jdbcType=DECIMAL}
        </if>
        <if test="'status_code'.toString() == column.value">
          #{item.statusCode,jdbcType=VARCHAR}
        </if>
        <if test="'status_name'.toString() == column.value">
          #{item.statusName,jdbcType=VARCHAR}
        </if>
        <if test="'province'.toString() == column.value">
          #{item.province,jdbcType=VARCHAR}
        </if>
        <if test="'subscriber'.toString() == column.value">
          #{item.subscriber,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>