<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.OrderRenewalInformationMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.OrderRenewalInformation">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 25 16:31:56 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="operation" jdbcType="VARCHAR" property="operation" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 25 16:31:56 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 25 16:31:56 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 25 16:31:56 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, order_id, operation, reason, create_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderRenewalInformationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 25 16:31:56 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_renewal_information
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 25 16:31:56 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from order_renewal_information
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 25 16:31:56 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from order_renewal_information
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderRenewalInformationExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 25 16:31:56 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from order_renewal_information
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderRenewalInformation">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 25 16:31:56 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_renewal_information (id, order_id, operation, 
      reason, create_time)
    values (#{id,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{operation,jdbcType=VARCHAR}, 
      #{reason,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderRenewalInformation">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 25 16:31:56 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_renewal_information
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="operation != null">
        operation,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderRenewalInformationExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 25 16:31:56 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from order_renewal_information
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 25 16:31:56 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_renewal_information
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.operation != null">
        operation = #{record.operation,jdbcType=VARCHAR},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 25 16:31:56 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_renewal_information
    set id = #{record.id,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      operation = #{record.operation,jdbcType=VARCHAR},
      reason = #{record.reason,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderRenewalInformation">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 25 16:31:56 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_renewal_information
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="operation != null">
        operation = #{operation,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderRenewalInformation">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 25 16:31:56 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_renewal_information
    set order_id = #{orderId,jdbcType=VARCHAR},
      operation = #{operation,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 25 16:31:56 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_renewal_information
    (id, order_id, operation, reason, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR}, #{item.operation,jdbcType=VARCHAR}, 
        #{item.reason,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Apr 25 16:31:56 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_renewal_information (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'operation'.toString() == column.value">
          #{item.operation,jdbcType=VARCHAR}
        </if>
        <if test="'reason'.toString() == column.value">
          #{item.reason,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>