<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.CardInventoryMainInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.CardInventoryMainInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Dec 24 09:55:53 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="cust_code" jdbcType="VARCHAR" property="custCode" />
    <result column="cust_name" jdbcType="VARCHAR" property="custName" />
    <result column="template_id" jdbcType="VARCHAR" property="templateId" />
    <result column="template_name" jdbcType="VARCHAR" property="templateName" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="provice_name" jdbcType="VARCHAR" property="proviceName" />
    <result column="region_id" jdbcType="VARCHAR" property="regionId" />
    <result column="region_name" jdbcType="VARCHAR" property="regionName" />
    <result column="card_type" jdbcType="VARCHAR" property="cardType" />
    <result column="reserve_quatity" jdbcType="INTEGER" property="reserveQuatity" />
    <result column="current_inventory" jdbcType="INTEGER" property="currentInventory" />
    <result column="total_inventory" jdbcType="INTEGER" property="totalInventory" />
    <result column="inventory_threshold" jdbcType="INTEGER" property="inventoryThreshold" />
    <result column="is_notice" jdbcType="BIT" property="isNotice" />
    <result column="inventory_status" jdbcType="VARCHAR" property="inventoryStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Dec 24 09:55:53 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Dec 24 09:55:53 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Dec 24 09:55:53 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, cust_code, cust_name, template_id, template_name, be_id, provice_name, region_id, 
    region_name, card_type, reserve_quatity, current_inventory, total_inventory, inventory_threshold, 
    is_notice, inventory_status, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.CardInventoryMainInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Dec 24 09:55:53 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from card_inventory_main_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Dec 24 09:55:53 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from card_inventory_main_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Dec 24 09:55:53 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from card_inventory_main_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.CardInventoryMainInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Dec 24 09:55:53 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from card_inventory_main_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.CardInventoryMainInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Dec 24 09:55:53 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into card_inventory_main_info (id, cust_code, cust_name, 
      template_id, template_name, be_id, 
      provice_name, region_id, region_name, 
      card_type, reserve_quatity, current_inventory, 
      total_inventory, inventory_threshold, is_notice, 
      inventory_status, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{custCode,jdbcType=VARCHAR}, #{custName,jdbcType=VARCHAR}, 
      #{templateId,jdbcType=VARCHAR}, #{templateName,jdbcType=VARCHAR}, #{beId,jdbcType=VARCHAR}, 
      #{proviceName,jdbcType=VARCHAR}, #{regionId,jdbcType=VARCHAR}, #{regionName,jdbcType=VARCHAR}, 
      #{cardType,jdbcType=VARCHAR}, #{reserveQuatity,jdbcType=INTEGER}, #{currentInventory,jdbcType=INTEGER}, 
      #{totalInventory,jdbcType=INTEGER}, #{inventoryThreshold,jdbcType=INTEGER}, #{isNotice,jdbcType=BIT}, 
      #{inventoryStatus,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.CardInventoryMainInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Dec 24 09:55:53 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into card_inventory_main_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="custCode != null">
        cust_code,
      </if>
      <if test="custName != null">
        cust_name,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="templateName != null">
        template_name,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="proviceName != null">
        provice_name,
      </if>
      <if test="regionId != null">
        region_id,
      </if>
      <if test="regionName != null">
        region_name,
      </if>
      <if test="cardType != null">
        card_type,
      </if>
      <if test="reserveQuatity != null">
        reserve_quatity,
      </if>
      <if test="currentInventory != null">
        current_inventory,
      </if>
      <if test="totalInventory != null">
        total_inventory,
      </if>
      <if test="inventoryThreshold != null">
        inventory_threshold,
      </if>
      <if test="isNotice != null">
        is_notice,
      </if>
      <if test="inventoryStatus != null">
        inventory_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="custCode != null">
        #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="custName != null">
        #{custName,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="templateName != null">
        #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="proviceName != null">
        #{proviceName,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null">
        #{regionId,jdbcType=VARCHAR},
      </if>
      <if test="regionName != null">
        #{regionName,jdbcType=VARCHAR},
      </if>
      <if test="cardType != null">
        #{cardType,jdbcType=VARCHAR},
      </if>
      <if test="reserveQuatity != null">
        #{reserveQuatity,jdbcType=INTEGER},
      </if>
      <if test="currentInventory != null">
        #{currentInventory,jdbcType=INTEGER},
      </if>
      <if test="totalInventory != null">
        #{totalInventory,jdbcType=INTEGER},
      </if>
      <if test="inventoryThreshold != null">
        #{inventoryThreshold,jdbcType=INTEGER},
      </if>
      <if test="isNotice != null">
        #{isNotice,jdbcType=BIT},
      </if>
      <if test="inventoryStatus != null">
        #{inventoryStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.CardInventoryMainInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Dec 24 09:55:53 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from card_inventory_main_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Dec 24 09:55:53 CST 2024. by MyBatis Generator, do not modify.
    -->
    update card_inventory_main_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.custCode != null">
        cust_code = #{record.custCode,jdbcType=VARCHAR},
      </if>
      <if test="record.custName != null">
        cust_name = #{record.custName,jdbcType=VARCHAR},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=VARCHAR},
      </if>
      <if test="record.templateName != null">
        template_name = #{record.templateName,jdbcType=VARCHAR},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.proviceName != null">
        provice_name = #{record.proviceName,jdbcType=VARCHAR},
      </if>
      <if test="record.regionId != null">
        region_id = #{record.regionId,jdbcType=VARCHAR},
      </if>
      <if test="record.regionName != null">
        region_name = #{record.regionName,jdbcType=VARCHAR},
      </if>
      <if test="record.cardType != null">
        card_type = #{record.cardType,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveQuatity != null">
        reserve_quatity = #{record.reserveQuatity,jdbcType=INTEGER},
      </if>
      <if test="record.currentInventory != null">
        current_inventory = #{record.currentInventory,jdbcType=INTEGER},
      </if>
      <if test="record.totalInventory != null">
        total_inventory = #{record.totalInventory,jdbcType=INTEGER},
      </if>
      <if test="record.inventoryThreshold != null">
        inventory_threshold = #{record.inventoryThreshold,jdbcType=INTEGER},
      </if>
      <if test="record.isNotice != null">
        is_notice = #{record.isNotice,jdbcType=BIT},
      </if>
      <if test="record.inventoryStatus != null">
        inventory_status = #{record.inventoryStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Dec 24 09:55:53 CST 2024. by MyBatis Generator, do not modify.
    -->
    update card_inventory_main_info
    set id = #{record.id,jdbcType=VARCHAR},
      cust_code = #{record.custCode,jdbcType=VARCHAR},
      cust_name = #{record.custName,jdbcType=VARCHAR},
      template_id = #{record.templateId,jdbcType=VARCHAR},
      template_name = #{record.templateName,jdbcType=VARCHAR},
      be_id = #{record.beId,jdbcType=VARCHAR},
      provice_name = #{record.proviceName,jdbcType=VARCHAR},
      region_id = #{record.regionId,jdbcType=VARCHAR},
      region_name = #{record.regionName,jdbcType=VARCHAR},
      card_type = #{record.cardType,jdbcType=VARCHAR},
      reserve_quatity = #{record.reserveQuatity,jdbcType=INTEGER},
      current_inventory = #{record.currentInventory,jdbcType=INTEGER},
      total_inventory = #{record.totalInventory,jdbcType=INTEGER},
      inventory_threshold = #{record.inventoryThreshold,jdbcType=INTEGER},
      is_notice = #{record.isNotice,jdbcType=BIT},
      inventory_status = #{record.inventoryStatus,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.CardInventoryMainInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Dec 24 09:55:53 CST 2024. by MyBatis Generator, do not modify.
    -->
    update card_inventory_main_info
    <set>
      <if test="custCode != null">
        cust_code = #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="custName != null">
        cust_name = #{custName,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="templateName != null">
        template_name = #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="proviceName != null">
        provice_name = #{proviceName,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null">
        region_id = #{regionId,jdbcType=VARCHAR},
      </if>
      <if test="regionName != null">
        region_name = #{regionName,jdbcType=VARCHAR},
      </if>
      <if test="cardType != null">
        card_type = #{cardType,jdbcType=VARCHAR},
      </if>
      <if test="reserveQuatity != null">
        reserve_quatity = #{reserveQuatity,jdbcType=INTEGER},
      </if>
      <if test="currentInventory != null">
        current_inventory = #{currentInventory,jdbcType=INTEGER},
      </if>
      <if test="totalInventory != null">
        total_inventory = #{totalInventory,jdbcType=INTEGER},
      </if>
      <if test="inventoryThreshold != null">
        inventory_threshold = #{inventoryThreshold,jdbcType=INTEGER},
      </if>
      <if test="isNotice != null">
        is_notice = #{isNotice,jdbcType=BIT},
      </if>
      <if test="inventoryStatus != null">
        inventory_status = #{inventoryStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.CardInventoryMainInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Dec 24 09:55:53 CST 2024. by MyBatis Generator, do not modify.
    -->
    update card_inventory_main_info
    set cust_code = #{custCode,jdbcType=VARCHAR},
      cust_name = #{custName,jdbcType=VARCHAR},
      template_id = #{templateId,jdbcType=VARCHAR},
      template_name = #{templateName,jdbcType=VARCHAR},
      be_id = #{beId,jdbcType=VARCHAR},
      provice_name = #{proviceName,jdbcType=VARCHAR},
      region_id = #{regionId,jdbcType=VARCHAR},
      region_name = #{regionName,jdbcType=VARCHAR},
      card_type = #{cardType,jdbcType=VARCHAR},
      reserve_quatity = #{reserveQuatity,jdbcType=INTEGER},
      current_inventory = #{currentInventory,jdbcType=INTEGER},
      total_inventory = #{totalInventory,jdbcType=INTEGER},
      inventory_threshold = #{inventoryThreshold,jdbcType=INTEGER},
      is_notice = #{isNotice,jdbcType=BIT},
      inventory_status = #{inventoryStatus,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Dec 24 09:55:53 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into card_inventory_main_info
    (id, cust_code, cust_name, template_id, template_name, be_id, provice_name, region_id, 
      region_name, card_type, reserve_quatity, current_inventory, total_inventory, inventory_threshold, 
      is_notice, inventory_status, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.custCode,jdbcType=VARCHAR}, #{item.custName,jdbcType=VARCHAR}, 
        #{item.templateId,jdbcType=VARCHAR}, #{item.templateName,jdbcType=VARCHAR}, #{item.beId,jdbcType=VARCHAR}, 
        #{item.proviceName,jdbcType=VARCHAR}, #{item.regionId,jdbcType=VARCHAR}, #{item.regionName,jdbcType=VARCHAR}, 
        #{item.cardType,jdbcType=VARCHAR}, #{item.reserveQuatity,jdbcType=INTEGER}, #{item.currentInventory,jdbcType=INTEGER}, 
        #{item.totalInventory,jdbcType=INTEGER}, #{item.inventoryThreshold,jdbcType=INTEGER}, 
        #{item.isNotice,jdbcType=BIT}, #{item.inventoryStatus,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Dec 24 09:55:53 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into card_inventory_main_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'cust_code'.toString() == column.value">
          #{item.custCode,jdbcType=VARCHAR}
        </if>
        <if test="'cust_name'.toString() == column.value">
          #{item.custName,jdbcType=VARCHAR}
        </if>
        <if test="'template_id'.toString() == column.value">
          #{item.templateId,jdbcType=VARCHAR}
        </if>
        <if test="'template_name'.toString() == column.value">
          #{item.templateName,jdbcType=VARCHAR}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'provice_name'.toString() == column.value">
          #{item.proviceName,jdbcType=VARCHAR}
        </if>
        <if test="'region_id'.toString() == column.value">
          #{item.regionId,jdbcType=VARCHAR}
        </if>
        <if test="'region_name'.toString() == column.value">
          #{item.regionName,jdbcType=VARCHAR}
        </if>
        <if test="'card_type'.toString() == column.value">
          #{item.cardType,jdbcType=VARCHAR}
        </if>
        <if test="'reserve_quatity'.toString() == column.value">
          #{item.reserveQuatity,jdbcType=INTEGER}
        </if>
        <if test="'current_inventory'.toString() == column.value">
          #{item.currentInventory,jdbcType=INTEGER}
        </if>
        <if test="'total_inventory'.toString() == column.value">
          #{item.totalInventory,jdbcType=INTEGER}
        </if>
        <if test="'inventory_threshold'.toString() == column.value">
          #{item.inventoryThreshold,jdbcType=INTEGER}
        </if>
        <if test="'is_notice'.toString() == column.value">
          #{item.isNotice,jdbcType=BIT}
        </if>
        <if test="'inventory_status'.toString() == column.value">
          #{item.inventoryStatus,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>