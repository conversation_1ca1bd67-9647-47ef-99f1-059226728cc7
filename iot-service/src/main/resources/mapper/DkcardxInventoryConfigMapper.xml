<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.DkcardxInventoryConfigMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat May 11 09:34:11 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="inventory_id" jdbcType="VARCHAR" property="inventoryId" />
    <result column="device_version" jdbcType="VARCHAR" property="deviceVersion" />
    <result column="imei" jdbcType="VARCHAR" property="imei" />
    <result column="msisdn" jdbcType="VARCHAR" property="msisdn" />
    <result column="iccid" jdbcType="VARCHAR" property="iccid" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="sale_status" jdbcType="VARCHAR" property="saleStatus" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat May 11 09:34:11 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat May 11 09:34:11 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat May 11 09:34:11 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, inventory_id, device_version, imei, msisdn, iccid, be_id, province_name, location, 
    city_name, sale_status, update_time, create_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryConfigExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat May 11 09:34:11 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from dkcardx_inventory_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat May 11 09:34:11 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from dkcardx_inventory_config
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat May 11 09:34:11 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from dkcardx_inventory_config
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryConfigExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat May 11 09:34:11 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from dkcardx_inventory_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat May 11 09:34:11 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into dkcardx_inventory_config (id, inventory_id, device_version, 
      imei, msisdn, iccid, 
      be_id, province_name, location, 
      city_name, sale_status, update_time, 
      create_time)
    values (#{id,jdbcType=VARCHAR}, #{inventoryId,jdbcType=VARCHAR}, #{deviceVersion,jdbcType=VARCHAR}, 
      #{imei,jdbcType=VARCHAR}, #{msisdn,jdbcType=VARCHAR}, #{iccid,jdbcType=VARCHAR}, 
      #{beId,jdbcType=VARCHAR}, #{provinceName,jdbcType=VARCHAR}, #{location,jdbcType=VARCHAR}, 
      #{cityName,jdbcType=VARCHAR}, #{saleStatus,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat May 11 09:34:11 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into dkcardx_inventory_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="inventoryId != null">
        inventory_id,
      </if>
      <if test="deviceVersion != null">
        device_version,
      </if>
      <if test="imei != null">
        imei,
      </if>
      <if test="msisdn != null">
        msisdn,
      </if>
      <if test="iccid != null">
        iccid,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="saleStatus != null">
        sale_status,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="inventoryId != null">
        #{inventoryId,jdbcType=VARCHAR},
      </if>
      <if test="deviceVersion != null">
        #{deviceVersion,jdbcType=VARCHAR},
      </if>
      <if test="imei != null">
        #{imei,jdbcType=VARCHAR},
      </if>
      <if test="msisdn != null">
        #{msisdn,jdbcType=VARCHAR},
      </if>
      <if test="iccid != null">
        #{iccid,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="saleStatus != null">
        #{saleStatus,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryConfigExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat May 11 09:34:11 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from dkcardx_inventory_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat May 11 09:34:11 CST 2024. by MyBatis Generator, do not modify.
    -->
    update dkcardx_inventory_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.inventoryId != null">
        inventory_id = #{record.inventoryId,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceVersion != null">
        device_version = #{record.deviceVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.imei != null">
        imei = #{record.imei,jdbcType=VARCHAR},
      </if>
      <if test="record.msisdn != null">
        msisdn = #{record.msisdn,jdbcType=VARCHAR},
      </if>
      <if test="record.iccid != null">
        iccid = #{record.iccid,jdbcType=VARCHAR},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceName != null">
        province_name = #{record.provinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.location != null">
        location = #{record.location,jdbcType=VARCHAR},
      </if>
      <if test="record.cityName != null">
        city_name = #{record.cityName,jdbcType=VARCHAR},
      </if>
      <if test="record.saleStatus != null">
        sale_status = #{record.saleStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat May 11 09:34:11 CST 2024. by MyBatis Generator, do not modify.
    -->
    update dkcardx_inventory_config
    set id = #{record.id,jdbcType=VARCHAR},
      inventory_id = #{record.inventoryId,jdbcType=VARCHAR},
      device_version = #{record.deviceVersion,jdbcType=VARCHAR},
      imei = #{record.imei,jdbcType=VARCHAR},
      msisdn = #{record.msisdn,jdbcType=VARCHAR},
      iccid = #{record.iccid,jdbcType=VARCHAR},
      be_id = #{record.beId,jdbcType=VARCHAR},
      province_name = #{record.provinceName,jdbcType=VARCHAR},
      location = #{record.location,jdbcType=VARCHAR},
      city_name = #{record.cityName,jdbcType=VARCHAR},
      sale_status = #{record.saleStatus,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat May 11 09:34:11 CST 2024. by MyBatis Generator, do not modify.
    -->
    update dkcardx_inventory_config
    <set>
      <if test="inventoryId != null">
        inventory_id = #{inventoryId,jdbcType=VARCHAR},
      </if>
      <if test="deviceVersion != null">
        device_version = #{deviceVersion,jdbcType=VARCHAR},
      </if>
      <if test="imei != null">
        imei = #{imei,jdbcType=VARCHAR},
      </if>
      <if test="msisdn != null">
        msisdn = #{msisdn,jdbcType=VARCHAR},
      </if>
      <if test="iccid != null">
        iccid = #{iccid,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="saleStatus != null">
        sale_status = #{saleStatus,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat May 11 09:34:11 CST 2024. by MyBatis Generator, do not modify.
    -->
    update dkcardx_inventory_config
    set inventory_id = #{inventoryId,jdbcType=VARCHAR},
      device_version = #{deviceVersion,jdbcType=VARCHAR},
      imei = #{imei,jdbcType=VARCHAR},
      msisdn = #{msisdn,jdbcType=VARCHAR},
      iccid = #{iccid,jdbcType=VARCHAR},
      be_id = #{beId,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR},
      location = #{location,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      sale_status = #{saleStatus,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat May 11 09:34:11 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into dkcardx_inventory_config
    (id, inventory_id, device_version, imei, msisdn, iccid, be_id, province_name, location, 
      city_name, sale_status, update_time, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.inventoryId,jdbcType=VARCHAR}, #{item.deviceVersion,jdbcType=VARCHAR}, 
        #{item.imei,jdbcType=VARCHAR}, #{item.msisdn,jdbcType=VARCHAR}, #{item.iccid,jdbcType=VARCHAR}, 
        #{item.beId,jdbcType=VARCHAR}, #{item.provinceName,jdbcType=VARCHAR}, #{item.location,jdbcType=VARCHAR}, 
        #{item.cityName,jdbcType=VARCHAR}, #{item.saleStatus,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat May 11 09:34:11 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into dkcardx_inventory_config (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'inventory_id'.toString() == column.value">
          #{item.inventoryId,jdbcType=VARCHAR}
        </if>
        <if test="'device_version'.toString() == column.value">
          #{item.deviceVersion,jdbcType=VARCHAR}
        </if>
        <if test="'imei'.toString() == column.value">
          #{item.imei,jdbcType=VARCHAR}
        </if>
        <if test="'msisdn'.toString() == column.value">
          #{item.msisdn,jdbcType=VARCHAR}
        </if>
        <if test="'iccid'.toString() == column.value">
          #{item.iccid,jdbcType=VARCHAR}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'province_name'.toString() == column.value">
          #{item.provinceName,jdbcType=VARCHAR}
        </if>
        <if test="'location'.toString() == column.value">
          #{item.location,jdbcType=VARCHAR}
        </if>
        <if test="'city_name'.toString() == column.value">
          #{item.cityName,jdbcType=VARCHAR}
        </if>
        <if test="'sale_status'.toString() == column.value">
          #{item.saleStatus,jdbcType=VARCHAR}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>