<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ServicePackLimitAmountMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.ServicePackLimitAmount">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="service_code" jdbcType="VARCHAR" property="serviceCode" />
    <result column="service_name" jdbcType="VARCHAR" property="serviceName" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="iot_limit" jdbcType="DOUBLE" property="iotLimit" />
    <result column="reserve_quatity" jdbcType="DOUBLE" property="reserveQuatity" />
    <result column="current_inventory" jdbcType="DOUBLE" property="currentInventory" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="efftime" jdbcType="VARCHAR" property="efftime" />
    <result column="exptime" jdbcType="VARCHAR" property="exptime" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="use_inventory" jdbcType="DOUBLE" property="useInventory" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, product_name, service_code, service_name, company_id, iot_limit, reserve_quatity, 
    current_inventory, create_time, update_time, efftime, exptime, company_name, status, 
    use_inventory
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ServicePackLimitAmountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from service_pack_limit_amount
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from service_pack_limit_amount
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from service_pack_limit_amount
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ServicePackLimitAmountExample">
    delete from service_pack_limit_amount
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.ServicePackLimitAmount">
    insert into service_pack_limit_amount (id, product_name, service_code, 
      service_name, company_id, iot_limit, 
      reserve_quatity, current_inventory, create_time, 
      update_time, efftime, exptime, 
      company_name, status, use_inventory
      )
    values (#{id,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, #{serviceCode,jdbcType=VARCHAR}, 
      #{serviceName,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, #{iotLimit,jdbcType=DOUBLE}, 
      #{reserveQuatity,jdbcType=DOUBLE}, #{currentInventory,jdbcType=DOUBLE}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{efftime,jdbcType=VARCHAR}, #{exptime,jdbcType=VARCHAR}, 
      #{companyName,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{useInventory,jdbcType=DOUBLE}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ServicePackLimitAmount">
    insert into service_pack_limit_amount
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="serviceCode != null">
        service_code,
      </if>
      <if test="serviceName != null">
        service_name,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="iotLimit != null">
        iot_limit,
      </if>
      <if test="reserveQuatity != null">
        reserve_quatity,
      </if>
      <if test="currentInventory != null">
        current_inventory,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="efftime != null">
        efftime,
      </if>
      <if test="exptime != null">
        exptime,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="useInventory != null">
        use_inventory,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="serviceCode != null">
        #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="serviceName != null">
        #{serviceName,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="iotLimit != null">
        #{iotLimit,jdbcType=DOUBLE},
      </if>
      <if test="reserveQuatity != null">
        #{reserveQuatity,jdbcType=DOUBLE},
      </if>
      <if test="currentInventory != null">
        #{currentInventory,jdbcType=DOUBLE},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="efftime != null">
        #{efftime,jdbcType=VARCHAR},
      </if>
      <if test="exptime != null">
        #{exptime,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="useInventory != null">
        #{useInventory,jdbcType=DOUBLE},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ServicePackLimitAmountExample" resultType="java.lang.Long">
    select count(*) from service_pack_limit_amount
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update service_pack_limit_amount
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceCode != null">
        service_code = #{record.serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceName != null">
        service_name = #{record.serviceName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.iotLimit != null">
        iot_limit = #{record.iotLimit,jdbcType=DOUBLE},
      </if>
      <if test="record.reserveQuatity != null">
        reserve_quatity = #{record.reserveQuatity,jdbcType=DOUBLE},
      </if>
      <if test="record.currentInventory != null">
        current_inventory = #{record.currentInventory,jdbcType=DOUBLE},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.efftime != null">
        efftime = #{record.efftime,jdbcType=VARCHAR},
      </if>
      <if test="record.exptime != null">
        exptime = #{record.exptime,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.useInventory != null">
        use_inventory = #{record.useInventory,jdbcType=DOUBLE},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update service_pack_limit_amount
    set id = #{record.id,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      service_code = #{record.serviceCode,jdbcType=VARCHAR},
      service_name = #{record.serviceName,jdbcType=VARCHAR},
      company_id = #{record.companyId,jdbcType=VARCHAR},
      iot_limit = #{record.iotLimit,jdbcType=DOUBLE},
      reserve_quatity = #{record.reserveQuatity,jdbcType=DOUBLE},
      current_inventory = #{record.currentInventory,jdbcType=DOUBLE},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      efftime = #{record.efftime,jdbcType=VARCHAR},
      exptime = #{record.exptime,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      use_inventory = #{record.useInventory,jdbcType=DOUBLE}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ServicePackLimitAmount">
    update service_pack_limit_amount
    <set>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="serviceCode != null">
        service_code = #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="serviceName != null">
        service_name = #{serviceName,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="iotLimit != null">
        iot_limit = #{iotLimit,jdbcType=DOUBLE},
      </if>
      <if test="reserveQuatity != null">
        reserve_quatity = #{reserveQuatity,jdbcType=DOUBLE},
      </if>
      <if test="currentInventory != null">
        current_inventory = #{currentInventory,jdbcType=DOUBLE},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="efftime != null">
        efftime = #{efftime,jdbcType=VARCHAR},
      </if>
      <if test="exptime != null">
        exptime = #{exptime,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="useInventory != null">
        use_inventory = #{useInventory,jdbcType=DOUBLE},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.ServicePackLimitAmount">
    update service_pack_limit_amount
    set product_name = #{productName,jdbcType=VARCHAR},
      service_code = #{serviceCode,jdbcType=VARCHAR},
      service_name = #{serviceName,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=VARCHAR},
      iot_limit = #{iotLimit,jdbcType=DOUBLE},
      reserve_quatity = #{reserveQuatity,jdbcType=DOUBLE},
      current_inventory = #{currentInventory,jdbcType=DOUBLE},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      efftime = #{efftime,jdbcType=VARCHAR},
      exptime = #{exptime,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      use_inventory = #{useInventory,jdbcType=DOUBLE}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into service_pack_limit_amount
    (id, product_name, service_code, service_name, company_id, iot_limit, reserve_quatity, 
      current_inventory, create_time, update_time, efftime, exptime, company_name, status, 
      use_inventory)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.productName,jdbcType=VARCHAR}, #{item.serviceCode,jdbcType=VARCHAR}, 
        #{item.serviceName,jdbcType=VARCHAR}, #{item.companyId,jdbcType=VARCHAR}, #{item.iotLimit,jdbcType=DOUBLE}, 
        #{item.reserveQuatity,jdbcType=DOUBLE}, #{item.currentInventory,jdbcType=DOUBLE}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.efftime,jdbcType=VARCHAR}, 
        #{item.exptime,jdbcType=VARCHAR}, #{item.companyName,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR}, 
        #{item.useInventory,jdbcType=DOUBLE})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into service_pack_limit_amount (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'service_code'.toString() == column.value">
          #{item.serviceCode,jdbcType=VARCHAR}
        </if>
        <if test="'service_name'.toString() == column.value">
          #{item.serviceName,jdbcType=VARCHAR}
        </if>
        <if test="'company_id'.toString() == column.value">
          #{item.companyId,jdbcType=VARCHAR}
        </if>
        <if test="'iot_limit'.toString() == column.value">
          #{item.iotLimit,jdbcType=DOUBLE}
        </if>
        <if test="'reserve_quatity'.toString() == column.value">
          #{item.reserveQuatity,jdbcType=DOUBLE}
        </if>
        <if test="'current_inventory'.toString() == column.value">
          #{item.currentInventory,jdbcType=DOUBLE}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'efftime'.toString() == column.value">
          #{item.efftime,jdbcType=VARCHAR}
        </if>
        <if test="'exptime'.toString() == column.value">
          #{item.exptime,jdbcType=VARCHAR}
        </if>
        <if test="'company_name'.toString() == column.value">
          #{item.companyName,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=VARCHAR}
        </if>
        <if test="'use_inventory'.toString() == column.value">
          #{item.useInventory,jdbcType=DOUBLE}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>