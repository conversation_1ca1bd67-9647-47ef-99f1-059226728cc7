<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ProvinceMaterialInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.ProvinceMaterialInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 27 14:42:50 CST 2023. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="province_contract_id" jdbcType="VARCHAR" property="provinceContractId" />
    <result column="product_code" jdbcType="VARCHAR" property="productCode" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="material_describe" jdbcType="VARCHAR" property="materialDescribe" />
    <result column="part_code" jdbcType="VARCHAR" property="partCode" />
    <result column="part_name" jdbcType="VARCHAR" property="partName" />
    <result column="nested_code" jdbcType="VARCHAR" property="nestedCode" />
    <result column="internet_material_code" jdbcType="VARCHAR" property="internetMaterialCode" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="tax_exclusive_univalence" jdbcType="DECIMAL" property="taxExclusiveUnivalence" />
    <result column="tax_rate" jdbcType="VARCHAR" property="taxRate" />
    <result column="tax_inclusive_univalence" jdbcType="DECIMAL" property="taxInclusiveUnivalence" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 27 14:42:50 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 27 14:42:50 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 27 14:42:50 CST 2023. by MyBatis Generator, do not modify.
    -->
    id, province_contract_id, product_code, product_name, material_code, material_describe, 
    part_code, part_name, nested_code, internet_material_code, unit, tax_exclusive_univalence, 
    tax_rate, tax_inclusive_univalence
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.ProvinceMaterialInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 27 14:42:50 CST 2023. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from province_material_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 27 14:42:50 CST 2023. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from province_material_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 27 14:42:50 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from province_material_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.ProvinceMaterialInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 27 14:42:50 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from province_material_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.ProvinceMaterialInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 27 14:42:50 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into province_material_info (id, province_contract_id, product_code, 
      product_name, material_code, material_describe, 
      part_code, part_name, nested_code, 
      internet_material_code, unit, tax_exclusive_univalence, 
      tax_rate, tax_inclusive_univalence)
    values (#{id,jdbcType=VARCHAR}, #{provinceContractId,jdbcType=VARCHAR}, #{productCode,jdbcType=VARCHAR}, 
      #{productName,jdbcType=VARCHAR}, #{materialCode,jdbcType=VARCHAR}, #{materialDescribe,jdbcType=VARCHAR}, 
      #{partCode,jdbcType=VARCHAR}, #{partName,jdbcType=VARCHAR}, #{nestedCode,jdbcType=VARCHAR}, 
      #{internetMaterialCode,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, #{taxExclusiveUnivalence,jdbcType=DECIMAL}, 
      #{taxRate,jdbcType=VARCHAR}, #{taxInclusiveUnivalence,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.ProvinceMaterialInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 27 14:42:50 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into province_material_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="provinceContractId != null">
        province_contract_id,
      </if>
      <if test="productCode != null">
        product_code,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="materialCode != null">
        material_code,
      </if>
      <if test="materialDescribe != null">
        material_describe,
      </if>
      <if test="partCode != null">
        part_code,
      </if>
      <if test="partName != null">
        part_name,
      </if>
      <if test="nestedCode != null">
        nested_code,
      </if>
      <if test="internetMaterialCode != null">
        internet_material_code,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="taxExclusiveUnivalence != null">
        tax_exclusive_univalence,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="taxInclusiveUnivalence != null">
        tax_inclusive_univalence,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="provinceContractId != null">
        #{provinceContractId,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="materialDescribe != null">
        #{materialDescribe,jdbcType=VARCHAR},
      </if>
      <if test="partCode != null">
        #{partCode,jdbcType=VARCHAR},
      </if>
      <if test="partName != null">
        #{partName,jdbcType=VARCHAR},
      </if>
      <if test="nestedCode != null">
        #{nestedCode,jdbcType=VARCHAR},
      </if>
      <if test="internetMaterialCode != null">
        #{internetMaterialCode,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="taxExclusiveUnivalence != null">
        #{taxExclusiveUnivalence,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=VARCHAR},
      </if>
      <if test="taxInclusiveUnivalence != null">
        #{taxInclusiveUnivalence,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.ProvinceMaterialInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 27 14:42:50 CST 2023. by MyBatis Generator, do not modify.
    -->
    select count(*) from province_material_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 27 14:42:50 CST 2023. by MyBatis Generator, do not modify.
    -->
    update province_material_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceContractId != null">
        province_contract_id = #{record.provinceContractId,jdbcType=VARCHAR},
      </if>
      <if test="record.productCode != null">
        product_code = #{record.productCode,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.materialCode != null">
        material_code = #{record.materialCode,jdbcType=VARCHAR},
      </if>
      <if test="record.materialDescribe != null">
        material_describe = #{record.materialDescribe,jdbcType=VARCHAR},
      </if>
      <if test="record.partCode != null">
        part_code = #{record.partCode,jdbcType=VARCHAR},
      </if>
      <if test="record.partName != null">
        part_name = #{record.partName,jdbcType=VARCHAR},
      </if>
      <if test="record.nestedCode != null">
        nested_code = #{record.nestedCode,jdbcType=VARCHAR},
      </if>
      <if test="record.internetMaterialCode != null">
        internet_material_code = #{record.internetMaterialCode,jdbcType=VARCHAR},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.taxExclusiveUnivalence != null">
        tax_exclusive_univalence = #{record.taxExclusiveUnivalence,jdbcType=DECIMAL},
      </if>
      <if test="record.taxRate != null">
        tax_rate = #{record.taxRate,jdbcType=VARCHAR},
      </if>
      <if test="record.taxInclusiveUnivalence != null">
        tax_inclusive_univalence = #{record.taxInclusiveUnivalence,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 27 14:42:50 CST 2023. by MyBatis Generator, do not modify.
    -->
    update province_material_info
    set id = #{record.id,jdbcType=VARCHAR},
      province_contract_id = #{record.provinceContractId,jdbcType=VARCHAR},
      product_code = #{record.productCode,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      material_code = #{record.materialCode,jdbcType=VARCHAR},
      material_describe = #{record.materialDescribe,jdbcType=VARCHAR},
      part_code = #{record.partCode,jdbcType=VARCHAR},
      part_name = #{record.partName,jdbcType=VARCHAR},
      nested_code = #{record.nestedCode,jdbcType=VARCHAR},
      internet_material_code = #{record.internetMaterialCode,jdbcType=VARCHAR},
      unit = #{record.unit,jdbcType=VARCHAR},
      tax_exclusive_univalence = #{record.taxExclusiveUnivalence,jdbcType=DECIMAL},
      tax_rate = #{record.taxRate,jdbcType=VARCHAR},
      tax_inclusive_univalence = #{record.taxInclusiveUnivalence,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.ProvinceMaterialInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 27 14:42:50 CST 2023. by MyBatis Generator, do not modify.
    -->
    update province_material_info
    <set>
      <if test="provinceContractId != null">
        province_contract_id = #{provinceContractId,jdbcType=VARCHAR},
      </if>
      <if test="productCode != null">
        product_code = #{productCode,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        material_code = #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="materialDescribe != null">
        material_describe = #{materialDescribe,jdbcType=VARCHAR},
      </if>
      <if test="partCode != null">
        part_code = #{partCode,jdbcType=VARCHAR},
      </if>
      <if test="partName != null">
        part_name = #{partName,jdbcType=VARCHAR},
      </if>
      <if test="nestedCode != null">
        nested_code = #{nestedCode,jdbcType=VARCHAR},
      </if>
      <if test="internetMaterialCode != null">
        internet_material_code = #{internetMaterialCode,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="taxExclusiveUnivalence != null">
        tax_exclusive_univalence = #{taxExclusiveUnivalence,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=VARCHAR},
      </if>
      <if test="taxInclusiveUnivalence != null">
        tax_inclusive_univalence = #{taxInclusiveUnivalence,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.ProvinceMaterialInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 27 14:42:50 CST 2023. by MyBatis Generator, do not modify.
    -->
    update province_material_info
    set province_contract_id = #{provinceContractId,jdbcType=VARCHAR},
      product_code = #{productCode,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      material_code = #{materialCode,jdbcType=VARCHAR},
      material_describe = #{materialDescribe,jdbcType=VARCHAR},
      part_code = #{partCode,jdbcType=VARCHAR},
      part_name = #{partName,jdbcType=VARCHAR},
      nested_code = #{nestedCode,jdbcType=VARCHAR},
      internet_material_code = #{internetMaterialCode,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      tax_exclusive_univalence = #{taxExclusiveUnivalence,jdbcType=DECIMAL},
      tax_rate = #{taxRate,jdbcType=VARCHAR},
      tax_inclusive_univalence = #{taxInclusiveUnivalence,jdbcType=DECIMAL}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 27 14:42:50 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into province_material_info
    (id, province_contract_id, product_code, product_name, material_code, material_describe, 
      part_code, part_name, nested_code, internet_material_code, unit, tax_exclusive_univalence, 
      tax_rate, tax_inclusive_univalence)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.provinceContractId,jdbcType=VARCHAR}, #{item.productCode,jdbcType=VARCHAR}, 
        #{item.productName,jdbcType=VARCHAR}, #{item.materialCode,jdbcType=VARCHAR}, #{item.materialDescribe,jdbcType=VARCHAR}, 
        #{item.partCode,jdbcType=VARCHAR}, #{item.partName,jdbcType=VARCHAR}, #{item.nestedCode,jdbcType=VARCHAR}, 
        #{item.internetMaterialCode,jdbcType=VARCHAR}, #{item.unit,jdbcType=VARCHAR}, #{item.taxExclusiveUnivalence,jdbcType=DECIMAL}, 
        #{item.taxRate,jdbcType=VARCHAR}, #{item.taxInclusiveUnivalence,jdbcType=DECIMAL}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 27 14:42:50 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into province_material_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'province_contract_id'.toString() == column.value">
          #{item.provinceContractId,jdbcType=VARCHAR}
        </if>
        <if test="'product_code'.toString() == column.value">
          #{item.productCode,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'material_code'.toString() == column.value">
          #{item.materialCode,jdbcType=VARCHAR}
        </if>
        <if test="'material_describe'.toString() == column.value">
          #{item.materialDescribe,jdbcType=VARCHAR}
        </if>
        <if test="'part_code'.toString() == column.value">
          #{item.partCode,jdbcType=VARCHAR}
        </if>
        <if test="'part_name'.toString() == column.value">
          #{item.partName,jdbcType=VARCHAR}
        </if>
        <if test="'nested_code'.toString() == column.value">
          #{item.nestedCode,jdbcType=VARCHAR}
        </if>
        <if test="'internet_material_code'.toString() == column.value">
          #{item.internetMaterialCode,jdbcType=VARCHAR}
        </if>
        <if test="'unit'.toString() == column.value">
          #{item.unit,jdbcType=VARCHAR}
        </if>
        <if test="'tax_exclusive_univalence'.toString() == column.value">
          #{item.taxExclusiveUnivalence,jdbcType=DECIMAL}
        </if>
        <if test="'tax_rate'.toString() == column.value">
          #{item.taxRate,jdbcType=VARCHAR}
        </if>
        <if test="'tax_inclusive_univalence'.toString() == column.value">
          #{item.taxInclusiveUnivalence,jdbcType=DECIMAL}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>