<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.AftermarketOfferingInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.AftermarketOfferingInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 21 15:06:20 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="after_market_code" jdbcType="VARCHAR" property="afterMarketCode" />
    <result column="oper_type" jdbcType="VARCHAR" property="operType" />
    <result column="after_market_internal_name" jdbcType="VARCHAR" property="afterMarketInternalName" />
    <result column="after_market_external_name" jdbcType="VARCHAR" property="afterMarketExternalName" />
    <result column="sell_price" jdbcType="VARCHAR" property="sellPrice" />
    <result column="settle_price" jdbcType="VARCHAR" property="settlePrice" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="mandatory" jdbcType="VARCHAR" property="mandatory" />
    <result column="aftermarket_type" jdbcType="VARCHAR" property="aftermarketType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 21 15:06:20 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 21 15:06:20 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 21 15:06:20 CST 2024. by MyBatis Generator, do not modify.
    -->
    after_market_code, oper_type, after_market_internal_name, after_market_external_name, 
    sell_price, settle_price, unit, mandatory, aftermarket_type, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.AftermarketOfferingInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 21 15:06:20 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from aftermarket_offering_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 21 15:06:20 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from aftermarket_offering_info
    where after_market_code = #{afterMarketCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 21 15:06:20 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from aftermarket_offering_info
    where after_market_code = #{afterMarketCode,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.AftermarketOfferingInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 21 15:06:20 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from aftermarket_offering_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.AftermarketOfferingInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 21 15:06:20 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into aftermarket_offering_info (after_market_code, oper_type, after_market_internal_name, 
      after_market_external_name, sell_price, settle_price, 
      unit, mandatory, aftermarket_type, 
      create_time, update_time)
    values (#{afterMarketCode,jdbcType=VARCHAR}, #{operType,jdbcType=VARCHAR}, #{afterMarketInternalName,jdbcType=VARCHAR}, 
      #{afterMarketExternalName,jdbcType=VARCHAR}, #{sellPrice,jdbcType=VARCHAR}, #{settlePrice,jdbcType=VARCHAR}, 
      #{unit,jdbcType=VARCHAR}, #{mandatory,jdbcType=VARCHAR}, #{aftermarketType,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.AftermarketOfferingInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 21 15:06:20 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into aftermarket_offering_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterMarketCode != null">
        after_market_code,
      </if>
      <if test="operType != null">
        oper_type,
      </if>
      <if test="afterMarketInternalName != null">
        after_market_internal_name,
      </if>
      <if test="afterMarketExternalName != null">
        after_market_external_name,
      </if>
      <if test="sellPrice != null">
        sell_price,
      </if>
      <if test="settlePrice != null">
        settle_price,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="mandatory != null">
        mandatory,
      </if>
      <if test="aftermarketType != null">
        aftermarket_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="afterMarketCode != null">
        #{afterMarketCode,jdbcType=VARCHAR},
      </if>
      <if test="operType != null">
        #{operType,jdbcType=VARCHAR},
      </if>
      <if test="afterMarketInternalName != null">
        #{afterMarketInternalName,jdbcType=VARCHAR},
      </if>
      <if test="afterMarketExternalName != null">
        #{afterMarketExternalName,jdbcType=VARCHAR},
      </if>
      <if test="sellPrice != null">
        #{sellPrice,jdbcType=VARCHAR},
      </if>
      <if test="settlePrice != null">
        #{settlePrice,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="mandatory != null">
        #{mandatory,jdbcType=VARCHAR},
      </if>
      <if test="aftermarketType != null">
        #{aftermarketType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.AftermarketOfferingInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 21 15:06:20 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from aftermarket_offering_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 21 15:06:20 CST 2024. by MyBatis Generator, do not modify.
    -->
    update aftermarket_offering_info
    <set>
      <if test="record.afterMarketCode != null">
        after_market_code = #{record.afterMarketCode,jdbcType=VARCHAR},
      </if>
      <if test="record.operType != null">
        oper_type = #{record.operType,jdbcType=VARCHAR},
      </if>
      <if test="record.afterMarketInternalName != null">
        after_market_internal_name = #{record.afterMarketInternalName,jdbcType=VARCHAR},
      </if>
      <if test="record.afterMarketExternalName != null">
        after_market_external_name = #{record.afterMarketExternalName,jdbcType=VARCHAR},
      </if>
      <if test="record.sellPrice != null">
        sell_price = #{record.sellPrice,jdbcType=VARCHAR},
      </if>
      <if test="record.settlePrice != null">
        settle_price = #{record.settlePrice,jdbcType=VARCHAR},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.mandatory != null">
        mandatory = #{record.mandatory,jdbcType=VARCHAR},
      </if>
      <if test="record.aftermarketType != null">
        aftermarket_type = #{record.aftermarketType,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 21 15:06:20 CST 2024. by MyBatis Generator, do not modify.
    -->
    update aftermarket_offering_info
    set after_market_code = #{record.afterMarketCode,jdbcType=VARCHAR},
      oper_type = #{record.operType,jdbcType=VARCHAR},
      after_market_internal_name = #{record.afterMarketInternalName,jdbcType=VARCHAR},
      after_market_external_name = #{record.afterMarketExternalName,jdbcType=VARCHAR},
      sell_price = #{record.sellPrice,jdbcType=VARCHAR},
      settle_price = #{record.settlePrice,jdbcType=VARCHAR},
      unit = #{record.unit,jdbcType=VARCHAR},
      mandatory = #{record.mandatory,jdbcType=VARCHAR},
      aftermarket_type = #{record.aftermarketType,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.AftermarketOfferingInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 21 15:06:20 CST 2024. by MyBatis Generator, do not modify.
    -->
    update aftermarket_offering_info
    <set>
      <if test="operType != null">
        oper_type = #{operType,jdbcType=VARCHAR},
      </if>
      <if test="afterMarketInternalName != null">
        after_market_internal_name = #{afterMarketInternalName,jdbcType=VARCHAR},
      </if>
      <if test="afterMarketExternalName != null">
        after_market_external_name = #{afterMarketExternalName,jdbcType=VARCHAR},
      </if>
      <if test="sellPrice != null">
        sell_price = #{sellPrice,jdbcType=VARCHAR},
      </if>
      <if test="settlePrice != null">
        settle_price = #{settlePrice,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="mandatory != null">
        mandatory = #{mandatory,jdbcType=VARCHAR},
      </if>
      <if test="aftermarketType != null">
        aftermarket_type = #{aftermarketType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where after_market_code = #{afterMarketCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.AftermarketOfferingInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 21 15:06:20 CST 2024. by MyBatis Generator, do not modify.
    -->
    update aftermarket_offering_info
    set oper_type = #{operType,jdbcType=VARCHAR},
      after_market_internal_name = #{afterMarketInternalName,jdbcType=VARCHAR},
      after_market_external_name = #{afterMarketExternalName,jdbcType=VARCHAR},
      sell_price = #{sellPrice,jdbcType=VARCHAR},
      settle_price = #{settlePrice,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      mandatory = #{mandatory,jdbcType=VARCHAR},
      aftermarket_type = #{aftermarketType,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where after_market_code = #{afterMarketCode,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 21 15:06:20 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into aftermarket_offering_info
    (after_market_code, oper_type, after_market_internal_name, after_market_external_name, 
      sell_price, settle_price, unit, mandatory, aftermarket_type, create_time, update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.afterMarketCode,jdbcType=VARCHAR}, #{item.operType,jdbcType=VARCHAR}, #{item.afterMarketInternalName,jdbcType=VARCHAR}, 
        #{item.afterMarketExternalName,jdbcType=VARCHAR}, #{item.sellPrice,jdbcType=VARCHAR}, 
        #{item.settlePrice,jdbcType=VARCHAR}, #{item.unit,jdbcType=VARCHAR}, #{item.mandatory,jdbcType=VARCHAR}, 
        #{item.aftermarketType,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 21 15:06:20 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into aftermarket_offering_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'after_market_code'.toString() == column.value">
          #{item.afterMarketCode,jdbcType=VARCHAR}
        </if>
        <if test="'oper_type'.toString() == column.value">
          #{item.operType,jdbcType=VARCHAR}
        </if>
        <if test="'after_market_internal_name'.toString() == column.value">
          #{item.afterMarketInternalName,jdbcType=VARCHAR}
        </if>
        <if test="'after_market_external_name'.toString() == column.value">
          #{item.afterMarketExternalName,jdbcType=VARCHAR}
        </if>
        <if test="'sell_price'.toString() == column.value">
          #{item.sellPrice,jdbcType=VARCHAR}
        </if>
        <if test="'settle_price'.toString() == column.value">
          #{item.settlePrice,jdbcType=VARCHAR}
        </if>
        <if test="'unit'.toString() == column.value">
          #{item.unit,jdbcType=VARCHAR}
        </if>
        <if test="'mandatory'.toString() == column.value">
          #{item.mandatory,jdbcType=VARCHAR}
        </if>
        <if test="'aftermarket_type'.toString() == column.value">
          #{item.aftermarketType,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>