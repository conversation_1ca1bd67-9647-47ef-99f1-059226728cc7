<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ProductFlowInstanceSkuMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.ProductFlowInstanceSku">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 11 15:02:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="flow_instance_id" jdbcType="VARCHAR" property="flowInstanceId" />
    <result column="flow_id" jdbcType="VARCHAR" property="flowId" />
    <result column="spu_code" jdbcType="VARCHAR" property="spuCode" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="sku_short_name" jdbcType="VARCHAR" property="skuShortName" />
    <result column="key_compoment_name" jdbcType="VARCHAR" property="keyCompomentName" />
    <result column="key_component_service_info" jdbcType="VARCHAR" property="keyComponentServiceInfo" />
    <result column="sale_price" jdbcType="BIGINT" property="salePrice" />
    <result column="sale_min_price" jdbcType="BIGINT" property="saleMinPrice" />
    <result column="sale_max_price" jdbcType="BIGINT" property="saleMaxPrice" />
    <result column="sale_out_of_price_range" jdbcType="VARCHAR" property="saleOutOfPriceRange" />
    <result column="sale_province_city" jdbcType="VARCHAR" property="saleProvinceCity" />
    <result column="delivery_range" jdbcType="VARCHAR" property="deliveryRange" />
    <result column="tourist_partner_visible" jdbcType="VARCHAR" property="touristPartnerVisible" />
    <result column="standard_product_name" jdbcType="VARCHAR" property="standardProductName" />
    <result column="standard_product_attribute" jdbcType="VARCHAR" property="standardProductAttribute" />
    <result column="sku_service_provider" jdbcType="VARCHAR" property="skuServiceProvider" />
    <result column="manage_department" jdbcType="VARCHAR" property="manageDepartment" />
    <result column="standard_product_manager" jdbcType="VARCHAR" property="standardProductManager" />
    <result column="receive_order_account" jdbcType="VARCHAR" property="receiveOrderAccount" />
    <result column="deliver_account" jdbcType="VARCHAR" property="deliverAccount" />
    <result column="aftermarket_account" jdbcType="VARCHAR" property="aftermarketAccount" />
    <result column="sku_remark" jdbcType="VARCHAR" property="skuRemark" />
    <result column="province_price" jdbcType="BIGINT" property="provincePrice" />
    <result column="product_package_sale_name" jdbcType="VARCHAR" property="productPackageSaleName" />
    <result column="product_package_service_content" jdbcType="VARCHAR" property="productPackageServiceContent" />
    <result column="send_contact_person" jdbcType="VARCHAR" property="sendContactPerson" />
    <result column="has_remuneration" jdbcType="VARCHAR" property="hasRemuneration" />
    <result column="remuneration_percent" jdbcType="VARCHAR" property="remunerationPercent" />
    <result column="cooperate_company" jdbcType="VARCHAR" property="cooperateCompany" />
    <result column="order_master_handler" jdbcType="VARCHAR" property="orderMasterHandler" />
    <result column="order_slave_handler" jdbcType="VARCHAR" property="orderSlaveHandler" />
    <result column="min_purchase_num" jdbcType="INTEGER" property="minPurchaseNum" />
    <result column="shelf_status" jdbcType="INTEGER" property="shelfStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 11 15:02:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 11 15:02:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 11 15:02:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, flow_instance_id, flow_id, spu_code, sku_code, sku_name, sku_short_name, key_compoment_name, 
    key_component_service_info, sale_price, sale_min_price, sale_max_price, sale_out_of_price_range, 
    sale_province_city, delivery_range, tourist_partner_visible, standard_product_name, 
    standard_product_attribute, sku_service_provider, manage_department, standard_product_manager, 
    receive_order_account, deliver_account, aftermarket_account, sku_remark, province_price, 
    product_package_sale_name, product_package_service_content, send_contact_person, 
    has_remuneration, remuneration_percent, cooperate_company, order_master_handler, 
    order_slave_handler, min_purchase_num, shelf_status, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceSkuExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 11 15:02:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from product_flow_instance_sku
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 11 15:02:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from product_flow_instance_sku
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 11 15:02:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from product_flow_instance_sku
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceSkuExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 11 15:02:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from product_flow_instance_sku
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceSku">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 11 15:02:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_sku (id, flow_instance_id, flow_id, 
      spu_code, sku_code, sku_name, 
      sku_short_name, key_compoment_name, key_component_service_info, 
      sale_price, sale_min_price, sale_max_price, 
      sale_out_of_price_range, sale_province_city, 
      delivery_range, tourist_partner_visible, standard_product_name, 
      standard_product_attribute, sku_service_provider, 
      manage_department, standard_product_manager, 
      receive_order_account, deliver_account, aftermarket_account, 
      sku_remark, province_price, product_package_sale_name, 
      product_package_service_content, send_contact_person, 
      has_remuneration, remuneration_percent, cooperate_company, 
      order_master_handler, order_slave_handler, 
      min_purchase_num, shelf_status, create_time, 
      update_time)
    values (#{id,jdbcType=VARCHAR}, #{flowInstanceId,jdbcType=VARCHAR}, #{flowId,jdbcType=VARCHAR}, 
      #{spuCode,jdbcType=VARCHAR}, #{skuCode,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR}, 
      #{skuShortName,jdbcType=VARCHAR}, #{keyCompomentName,jdbcType=VARCHAR}, #{keyComponentServiceInfo,jdbcType=VARCHAR}, 
      #{salePrice,jdbcType=BIGINT}, #{saleMinPrice,jdbcType=BIGINT}, #{saleMaxPrice,jdbcType=BIGINT}, 
      #{saleOutOfPriceRange,jdbcType=VARCHAR}, #{saleProvinceCity,jdbcType=VARCHAR}, 
      #{deliveryRange,jdbcType=VARCHAR}, #{touristPartnerVisible,jdbcType=VARCHAR}, #{standardProductName,jdbcType=VARCHAR}, 
      #{standardProductAttribute,jdbcType=VARCHAR}, #{skuServiceProvider,jdbcType=VARCHAR}, 
      #{manageDepartment,jdbcType=VARCHAR}, #{standardProductManager,jdbcType=VARCHAR}, 
      #{receiveOrderAccount,jdbcType=VARCHAR}, #{deliverAccount,jdbcType=VARCHAR}, #{aftermarketAccount,jdbcType=VARCHAR}, 
      #{skuRemark,jdbcType=VARCHAR}, #{provincePrice,jdbcType=BIGINT}, #{productPackageSaleName,jdbcType=VARCHAR}, 
      #{productPackageServiceContent,jdbcType=VARCHAR}, #{sendContactPerson,jdbcType=VARCHAR}, 
      #{hasRemuneration,jdbcType=VARCHAR}, #{remunerationPercent,jdbcType=VARCHAR}, #{cooperateCompany,jdbcType=VARCHAR}, 
      #{orderMasterHandler,jdbcType=VARCHAR}, #{orderSlaveHandler,jdbcType=VARCHAR}, 
      #{minPurchaseNum,jdbcType=INTEGER}, #{shelfStatus,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceSku">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 11 15:02:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_sku
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="flowInstanceId != null">
        flow_instance_id,
      </if>
      <if test="flowId != null">
        flow_id,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="skuCode != null">
        sku_code,
      </if>
      <if test="skuName != null">
        sku_name,
      </if>
      <if test="skuShortName != null">
        sku_short_name,
      </if>
      <if test="keyCompomentName != null">
        key_compoment_name,
      </if>
      <if test="keyComponentServiceInfo != null">
        key_component_service_info,
      </if>
      <if test="salePrice != null">
        sale_price,
      </if>
      <if test="saleMinPrice != null">
        sale_min_price,
      </if>
      <if test="saleMaxPrice != null">
        sale_max_price,
      </if>
      <if test="saleOutOfPriceRange != null">
        sale_out_of_price_range,
      </if>
      <if test="saleProvinceCity != null">
        sale_province_city,
      </if>
      <if test="deliveryRange != null">
        delivery_range,
      </if>
      <if test="touristPartnerVisible != null">
        tourist_partner_visible,
      </if>
      <if test="standardProductName != null">
        standard_product_name,
      </if>
      <if test="standardProductAttribute != null">
        standard_product_attribute,
      </if>
      <if test="skuServiceProvider != null">
        sku_service_provider,
      </if>
      <if test="manageDepartment != null">
        manage_department,
      </if>
      <if test="standardProductManager != null">
        standard_product_manager,
      </if>
      <if test="receiveOrderAccount != null">
        receive_order_account,
      </if>
      <if test="deliverAccount != null">
        deliver_account,
      </if>
      <if test="aftermarketAccount != null">
        aftermarket_account,
      </if>
      <if test="skuRemark != null">
        sku_remark,
      </if>
      <if test="provincePrice != null">
        province_price,
      </if>
      <if test="productPackageSaleName != null">
        product_package_sale_name,
      </if>
      <if test="productPackageServiceContent != null">
        product_package_service_content,
      </if>
      <if test="sendContactPerson != null">
        send_contact_person,
      </if>
      <if test="hasRemuneration != null">
        has_remuneration,
      </if>
      <if test="remunerationPercent != null">
        remuneration_percent,
      </if>
      <if test="cooperateCompany != null">
        cooperate_company,
      </if>
      <if test="orderMasterHandler != null">
        order_master_handler,
      </if>
      <if test="orderSlaveHandler != null">
        order_slave_handler,
      </if>
      <if test="minPurchaseNum != null">
        min_purchase_num,
      </if>
      <if test="shelfStatus != null">
        shelf_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="flowInstanceId != null">
        #{flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="flowId != null">
        #{flowId,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="skuShortName != null">
        #{skuShortName,jdbcType=VARCHAR},
      </if>
      <if test="keyCompomentName != null">
        #{keyCompomentName,jdbcType=VARCHAR},
      </if>
      <if test="keyComponentServiceInfo != null">
        #{keyComponentServiceInfo,jdbcType=VARCHAR},
      </if>
      <if test="salePrice != null">
        #{salePrice,jdbcType=BIGINT},
      </if>
      <if test="saleMinPrice != null">
        #{saleMinPrice,jdbcType=BIGINT},
      </if>
      <if test="saleMaxPrice != null">
        #{saleMaxPrice,jdbcType=BIGINT},
      </if>
      <if test="saleOutOfPriceRange != null">
        #{saleOutOfPriceRange,jdbcType=VARCHAR},
      </if>
      <if test="saleProvinceCity != null">
        #{saleProvinceCity,jdbcType=VARCHAR},
      </if>
      <if test="deliveryRange != null">
        #{deliveryRange,jdbcType=VARCHAR},
      </if>
      <if test="touristPartnerVisible != null">
        #{touristPartnerVisible,jdbcType=VARCHAR},
      </if>
      <if test="standardProductName != null">
        #{standardProductName,jdbcType=VARCHAR},
      </if>
      <if test="standardProductAttribute != null">
        #{standardProductAttribute,jdbcType=VARCHAR},
      </if>
      <if test="skuServiceProvider != null">
        #{skuServiceProvider,jdbcType=VARCHAR},
      </if>
      <if test="manageDepartment != null">
        #{manageDepartment,jdbcType=VARCHAR},
      </if>
      <if test="standardProductManager != null">
        #{standardProductManager,jdbcType=VARCHAR},
      </if>
      <if test="receiveOrderAccount != null">
        #{receiveOrderAccount,jdbcType=VARCHAR},
      </if>
      <if test="deliverAccount != null">
        #{deliverAccount,jdbcType=VARCHAR},
      </if>
      <if test="aftermarketAccount != null">
        #{aftermarketAccount,jdbcType=VARCHAR},
      </if>
      <if test="skuRemark != null">
        #{skuRemark,jdbcType=VARCHAR},
      </if>
      <if test="provincePrice != null">
        #{provincePrice,jdbcType=BIGINT},
      </if>
      <if test="productPackageSaleName != null">
        #{productPackageSaleName,jdbcType=VARCHAR},
      </if>
      <if test="productPackageServiceContent != null">
        #{productPackageServiceContent,jdbcType=VARCHAR},
      </if>
      <if test="sendContactPerson != null">
        #{sendContactPerson,jdbcType=VARCHAR},
      </if>
      <if test="hasRemuneration != null">
        #{hasRemuneration,jdbcType=VARCHAR},
      </if>
      <if test="remunerationPercent != null">
        #{remunerationPercent,jdbcType=VARCHAR},
      </if>
      <if test="cooperateCompany != null">
        #{cooperateCompany,jdbcType=VARCHAR},
      </if>
      <if test="orderMasterHandler != null">
        #{orderMasterHandler,jdbcType=VARCHAR},
      </if>
      <if test="orderSlaveHandler != null">
        #{orderSlaveHandler,jdbcType=VARCHAR},
      </if>
      <if test="minPurchaseNum != null">
        #{minPurchaseNum,jdbcType=INTEGER},
      </if>
      <if test="shelfStatus != null">
        #{shelfStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceSkuExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 11 15:02:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from product_flow_instance_sku
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 11 15:02:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_sku
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.flowInstanceId != null">
        flow_instance_id = #{record.flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.flowId != null">
        flow_id = #{record.flowId,jdbcType=VARCHAR},
      </if>
      <if test="record.spuCode != null">
        spu_code = #{record.spuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCode != null">
        sku_code = #{record.skuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.skuName != null">
        sku_name = #{record.skuName,jdbcType=VARCHAR},
      </if>
      <if test="record.skuShortName != null">
        sku_short_name = #{record.skuShortName,jdbcType=VARCHAR},
      </if>
      <if test="record.keyCompomentName != null">
        key_compoment_name = #{record.keyCompomentName,jdbcType=VARCHAR},
      </if>
      <if test="record.keyComponentServiceInfo != null">
        key_component_service_info = #{record.keyComponentServiceInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.salePrice != null">
        sale_price = #{record.salePrice,jdbcType=BIGINT},
      </if>
      <if test="record.saleMinPrice != null">
        sale_min_price = #{record.saleMinPrice,jdbcType=BIGINT},
      </if>
      <if test="record.saleMaxPrice != null">
        sale_max_price = #{record.saleMaxPrice,jdbcType=BIGINT},
      </if>
      <if test="record.saleOutOfPriceRange != null">
        sale_out_of_price_range = #{record.saleOutOfPriceRange,jdbcType=VARCHAR},
      </if>
      <if test="record.saleProvinceCity != null">
        sale_province_city = #{record.saleProvinceCity,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryRange != null">
        delivery_range = #{record.deliveryRange,jdbcType=VARCHAR},
      </if>
      <if test="record.touristPartnerVisible != null">
        tourist_partner_visible = #{record.touristPartnerVisible,jdbcType=VARCHAR},
      </if>
      <if test="record.standardProductName != null">
        standard_product_name = #{record.standardProductName,jdbcType=VARCHAR},
      </if>
      <if test="record.standardProductAttribute != null">
        standard_product_attribute = #{record.standardProductAttribute,jdbcType=VARCHAR},
      </if>
      <if test="record.skuServiceProvider != null">
        sku_service_provider = #{record.skuServiceProvider,jdbcType=VARCHAR},
      </if>
      <if test="record.manageDepartment != null">
        manage_department = #{record.manageDepartment,jdbcType=VARCHAR},
      </if>
      <if test="record.standardProductManager != null">
        standard_product_manager = #{record.standardProductManager,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveOrderAccount != null">
        receive_order_account = #{record.receiveOrderAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.deliverAccount != null">
        deliver_account = #{record.deliverAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.aftermarketAccount != null">
        aftermarket_account = #{record.aftermarketAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.skuRemark != null">
        sku_remark = #{record.skuRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.provincePrice != null">
        province_price = #{record.provincePrice,jdbcType=BIGINT},
      </if>
      <if test="record.productPackageSaleName != null">
        product_package_sale_name = #{record.productPackageSaleName,jdbcType=VARCHAR},
      </if>
      <if test="record.productPackageServiceContent != null">
        product_package_service_content = #{record.productPackageServiceContent,jdbcType=VARCHAR},
      </if>
      <if test="record.sendContactPerson != null">
        send_contact_person = #{record.sendContactPerson,jdbcType=VARCHAR},
      </if>
      <if test="record.hasRemuneration != null">
        has_remuneration = #{record.hasRemuneration,jdbcType=VARCHAR},
      </if>
      <if test="record.remunerationPercent != null">
        remuneration_percent = #{record.remunerationPercent,jdbcType=VARCHAR},
      </if>
      <if test="record.cooperateCompany != null">
        cooperate_company = #{record.cooperateCompany,jdbcType=VARCHAR},
      </if>
      <if test="record.orderMasterHandler != null">
        order_master_handler = #{record.orderMasterHandler,jdbcType=VARCHAR},
      </if>
      <if test="record.orderSlaveHandler != null">
        order_slave_handler = #{record.orderSlaveHandler,jdbcType=VARCHAR},
      </if>
      <if test="record.minPurchaseNum != null">
        min_purchase_num = #{record.minPurchaseNum,jdbcType=INTEGER},
      </if>
      <if test="record.shelfStatus != null">
        shelf_status = #{record.shelfStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 11 15:02:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_sku
    set id = #{record.id,jdbcType=VARCHAR},
      flow_instance_id = #{record.flowInstanceId,jdbcType=VARCHAR},
      flow_id = #{record.flowId,jdbcType=VARCHAR},
      spu_code = #{record.spuCode,jdbcType=VARCHAR},
      sku_code = #{record.skuCode,jdbcType=VARCHAR},
      sku_name = #{record.skuName,jdbcType=VARCHAR},
      sku_short_name = #{record.skuShortName,jdbcType=VARCHAR},
      key_compoment_name = #{record.keyCompomentName,jdbcType=VARCHAR},
      key_component_service_info = #{record.keyComponentServiceInfo,jdbcType=VARCHAR},
      sale_price = #{record.salePrice,jdbcType=BIGINT},
      sale_min_price = #{record.saleMinPrice,jdbcType=BIGINT},
      sale_max_price = #{record.saleMaxPrice,jdbcType=BIGINT},
      sale_out_of_price_range = #{record.saleOutOfPriceRange,jdbcType=VARCHAR},
      sale_province_city = #{record.saleProvinceCity,jdbcType=VARCHAR},
      delivery_range = #{record.deliveryRange,jdbcType=VARCHAR},
      tourist_partner_visible = #{record.touristPartnerVisible,jdbcType=VARCHAR},
      standard_product_name = #{record.standardProductName,jdbcType=VARCHAR},
      standard_product_attribute = #{record.standardProductAttribute,jdbcType=VARCHAR},
      sku_service_provider = #{record.skuServiceProvider,jdbcType=VARCHAR},
      manage_department = #{record.manageDepartment,jdbcType=VARCHAR},
      standard_product_manager = #{record.standardProductManager,jdbcType=VARCHAR},
      receive_order_account = #{record.receiveOrderAccount,jdbcType=VARCHAR},
      deliver_account = #{record.deliverAccount,jdbcType=VARCHAR},
      aftermarket_account = #{record.aftermarketAccount,jdbcType=VARCHAR},
      sku_remark = #{record.skuRemark,jdbcType=VARCHAR},
      province_price = #{record.provincePrice,jdbcType=BIGINT},
      product_package_sale_name = #{record.productPackageSaleName,jdbcType=VARCHAR},
      product_package_service_content = #{record.productPackageServiceContent,jdbcType=VARCHAR},
      send_contact_person = #{record.sendContactPerson,jdbcType=VARCHAR},
      has_remuneration = #{record.hasRemuneration,jdbcType=VARCHAR},
      remuneration_percent = #{record.remunerationPercent,jdbcType=VARCHAR},
      cooperate_company = #{record.cooperateCompany,jdbcType=VARCHAR},
      order_master_handler = #{record.orderMasterHandler,jdbcType=VARCHAR},
      order_slave_handler = #{record.orderSlaveHandler,jdbcType=VARCHAR},
      min_purchase_num = #{record.minPurchaseNum,jdbcType=INTEGER},
      shelf_status = #{record.shelfStatus,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceSku">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 11 15:02:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_sku
    <set>
      <if test="flowInstanceId != null">
        flow_instance_id = #{flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="flowId != null">
        flow_id = #{flowId,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        sku_code = #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        sku_name = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="skuShortName != null">
        sku_short_name = #{skuShortName,jdbcType=VARCHAR},
      </if>
      <if test="keyCompomentName != null">
        key_compoment_name = #{keyCompomentName,jdbcType=VARCHAR},
      </if>
      <if test="keyComponentServiceInfo != null">
        key_component_service_info = #{keyComponentServiceInfo,jdbcType=VARCHAR},
      </if>
      <if test="salePrice != null">
        sale_price = #{salePrice,jdbcType=BIGINT},
      </if>
      <if test="saleMinPrice != null">
        sale_min_price = #{saleMinPrice,jdbcType=BIGINT},
      </if>
      <if test="saleMaxPrice != null">
        sale_max_price = #{saleMaxPrice,jdbcType=BIGINT},
      </if>
      <if test="saleOutOfPriceRange != null">
        sale_out_of_price_range = #{saleOutOfPriceRange,jdbcType=VARCHAR},
      </if>
      <if test="saleProvinceCity != null">
        sale_province_city = #{saleProvinceCity,jdbcType=VARCHAR},
      </if>
      <if test="deliveryRange != null">
        delivery_range = #{deliveryRange,jdbcType=VARCHAR},
      </if>
      <if test="touristPartnerVisible != null">
        tourist_partner_visible = #{touristPartnerVisible,jdbcType=VARCHAR},
      </if>
      <if test="standardProductName != null">
        standard_product_name = #{standardProductName,jdbcType=VARCHAR},
      </if>
      <if test="standardProductAttribute != null">
        standard_product_attribute = #{standardProductAttribute,jdbcType=VARCHAR},
      </if>
      <if test="skuServiceProvider != null">
        sku_service_provider = #{skuServiceProvider,jdbcType=VARCHAR},
      </if>
      <if test="manageDepartment != null">
        manage_department = #{manageDepartment,jdbcType=VARCHAR},
      </if>
      <if test="standardProductManager != null">
        standard_product_manager = #{standardProductManager,jdbcType=VARCHAR},
      </if>
      <if test="receiveOrderAccount != null">
        receive_order_account = #{receiveOrderAccount,jdbcType=VARCHAR},
      </if>
      <if test="deliverAccount != null">
        deliver_account = #{deliverAccount,jdbcType=VARCHAR},
      </if>
      <if test="aftermarketAccount != null">
        aftermarket_account = #{aftermarketAccount,jdbcType=VARCHAR},
      </if>
      <if test="skuRemark != null">
        sku_remark = #{skuRemark,jdbcType=VARCHAR},
      </if>
      <if test="provincePrice != null">
        province_price = #{provincePrice,jdbcType=BIGINT},
      </if>
      <if test="productPackageSaleName != null">
        product_package_sale_name = #{productPackageSaleName,jdbcType=VARCHAR},
      </if>
      <if test="productPackageServiceContent != null">
        product_package_service_content = #{productPackageServiceContent,jdbcType=VARCHAR},
      </if>
      <if test="sendContactPerson != null">
        send_contact_person = #{sendContactPerson,jdbcType=VARCHAR},
      </if>
      <if test="hasRemuneration != null">
        has_remuneration = #{hasRemuneration,jdbcType=VARCHAR},
      </if>
      <if test="remunerationPercent != null">
        remuneration_percent = #{remunerationPercent,jdbcType=VARCHAR},
      </if>
      <if test="cooperateCompany != null">
        cooperate_company = #{cooperateCompany,jdbcType=VARCHAR},
      </if>
      <if test="orderMasterHandler != null">
        order_master_handler = #{orderMasterHandler,jdbcType=VARCHAR},
      </if>
      <if test="orderSlaveHandler != null">
        order_slave_handler = #{orderSlaveHandler,jdbcType=VARCHAR},
      </if>
      <if test="minPurchaseNum != null">
        min_purchase_num = #{minPurchaseNum,jdbcType=INTEGER},
      </if>
      <if test="shelfStatus != null">
        shelf_status = #{shelfStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceSku">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 11 15:02:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_sku
    set flow_instance_id = #{flowInstanceId,jdbcType=VARCHAR},
      flow_id = #{flowId,jdbcType=VARCHAR},
      spu_code = #{spuCode,jdbcType=VARCHAR},
      sku_code = #{skuCode,jdbcType=VARCHAR},
      sku_name = #{skuName,jdbcType=VARCHAR},
      sku_short_name = #{skuShortName,jdbcType=VARCHAR},
      key_compoment_name = #{keyCompomentName,jdbcType=VARCHAR},
      key_component_service_info = #{keyComponentServiceInfo,jdbcType=VARCHAR},
      sale_price = #{salePrice,jdbcType=BIGINT},
      sale_min_price = #{saleMinPrice,jdbcType=BIGINT},
      sale_max_price = #{saleMaxPrice,jdbcType=BIGINT},
      sale_out_of_price_range = #{saleOutOfPriceRange,jdbcType=VARCHAR},
      sale_province_city = #{saleProvinceCity,jdbcType=VARCHAR},
      delivery_range = #{deliveryRange,jdbcType=VARCHAR},
      tourist_partner_visible = #{touristPartnerVisible,jdbcType=VARCHAR},
      standard_product_name = #{standardProductName,jdbcType=VARCHAR},
      standard_product_attribute = #{standardProductAttribute,jdbcType=VARCHAR},
      sku_service_provider = #{skuServiceProvider,jdbcType=VARCHAR},
      manage_department = #{manageDepartment,jdbcType=VARCHAR},
      standard_product_manager = #{standardProductManager,jdbcType=VARCHAR},
      receive_order_account = #{receiveOrderAccount,jdbcType=VARCHAR},
      deliver_account = #{deliverAccount,jdbcType=VARCHAR},
      aftermarket_account = #{aftermarketAccount,jdbcType=VARCHAR},
      sku_remark = #{skuRemark,jdbcType=VARCHAR},
      province_price = #{provincePrice,jdbcType=BIGINT},
      product_package_sale_name = #{productPackageSaleName,jdbcType=VARCHAR},
      product_package_service_content = #{productPackageServiceContent,jdbcType=VARCHAR},
      send_contact_person = #{sendContactPerson,jdbcType=VARCHAR},
      has_remuneration = #{hasRemuneration,jdbcType=VARCHAR},
      remuneration_percent = #{remunerationPercent,jdbcType=VARCHAR},
      cooperate_company = #{cooperateCompany,jdbcType=VARCHAR},
      order_master_handler = #{orderMasterHandler,jdbcType=VARCHAR},
      order_slave_handler = #{orderSlaveHandler,jdbcType=VARCHAR},
      min_purchase_num = #{minPurchaseNum,jdbcType=INTEGER},
      shelf_status = #{shelfStatus,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 11 15:02:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_sku
    (id, flow_instance_id, flow_id, spu_code, sku_code, sku_name, sku_short_name, key_compoment_name, 
      key_component_service_info, sale_price, sale_min_price, sale_max_price, sale_out_of_price_range, 
      sale_province_city, delivery_range, tourist_partner_visible, standard_product_name, 
      standard_product_attribute, sku_service_provider, manage_department, standard_product_manager, 
      receive_order_account, deliver_account, aftermarket_account, sku_remark, province_price, 
      product_package_sale_name, product_package_service_content, send_contact_person, 
      has_remuneration, remuneration_percent, cooperate_company, order_master_handler, 
      order_slave_handler, min_purchase_num, shelf_status, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.flowInstanceId,jdbcType=VARCHAR}, #{item.flowId,jdbcType=VARCHAR}, 
        #{item.spuCode,jdbcType=VARCHAR}, #{item.skuCode,jdbcType=VARCHAR}, #{item.skuName,jdbcType=VARCHAR}, 
        #{item.skuShortName,jdbcType=VARCHAR}, #{item.keyCompomentName,jdbcType=VARCHAR}, 
        #{item.keyComponentServiceInfo,jdbcType=VARCHAR}, #{item.salePrice,jdbcType=BIGINT}, 
        #{item.saleMinPrice,jdbcType=BIGINT}, #{item.saleMaxPrice,jdbcType=BIGINT}, #{item.saleOutOfPriceRange,jdbcType=VARCHAR}, 
        #{item.saleProvinceCity,jdbcType=VARCHAR}, #{item.deliveryRange,jdbcType=VARCHAR}, 
        #{item.touristPartnerVisible,jdbcType=VARCHAR}, #{item.standardProductName,jdbcType=VARCHAR}, 
        #{item.standardProductAttribute,jdbcType=VARCHAR}, #{item.skuServiceProvider,jdbcType=VARCHAR}, 
        #{item.manageDepartment,jdbcType=VARCHAR}, #{item.standardProductManager,jdbcType=VARCHAR}, 
        #{item.receiveOrderAccount,jdbcType=VARCHAR}, #{item.deliverAccount,jdbcType=VARCHAR}, 
        #{item.aftermarketAccount,jdbcType=VARCHAR}, #{item.skuRemark,jdbcType=VARCHAR}, 
        #{item.provincePrice,jdbcType=BIGINT}, #{item.productPackageSaleName,jdbcType=VARCHAR}, 
        #{item.productPackageServiceContent,jdbcType=VARCHAR}, #{item.sendContactPerson,jdbcType=VARCHAR}, 
        #{item.hasRemuneration,jdbcType=VARCHAR}, #{item.remunerationPercent,jdbcType=VARCHAR}, 
        #{item.cooperateCompany,jdbcType=VARCHAR}, #{item.orderMasterHandler,jdbcType=VARCHAR}, 
        #{item.orderSlaveHandler,jdbcType=VARCHAR}, #{item.minPurchaseNum,jdbcType=INTEGER}, 
        #{item.shelfStatus,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 11 15:02:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_sku (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'flow_instance_id'.toString() == column.value">
          #{item.flowInstanceId,jdbcType=VARCHAR}
        </if>
        <if test="'flow_id'.toString() == column.value">
          #{item.flowId,jdbcType=VARCHAR}
        </if>
        <if test="'spu_code'.toString() == column.value">
          #{item.spuCode,jdbcType=VARCHAR}
        </if>
        <if test="'sku_code'.toString() == column.value">
          #{item.skuCode,jdbcType=VARCHAR}
        </if>
        <if test="'sku_name'.toString() == column.value">
          #{item.skuName,jdbcType=VARCHAR}
        </if>
        <if test="'sku_short_name'.toString() == column.value">
          #{item.skuShortName,jdbcType=VARCHAR}
        </if>
        <if test="'key_compoment_name'.toString() == column.value">
          #{item.keyCompomentName,jdbcType=VARCHAR}
        </if>
        <if test="'key_component_service_info'.toString() == column.value">
          #{item.keyComponentServiceInfo,jdbcType=VARCHAR}
        </if>
        <if test="'sale_price'.toString() == column.value">
          #{item.salePrice,jdbcType=BIGINT}
        </if>
        <if test="'sale_min_price'.toString() == column.value">
          #{item.saleMinPrice,jdbcType=BIGINT}
        </if>
        <if test="'sale_max_price'.toString() == column.value">
          #{item.saleMaxPrice,jdbcType=BIGINT}
        </if>
        <if test="'sale_out_of_price_range'.toString() == column.value">
          #{item.saleOutOfPriceRange,jdbcType=VARCHAR}
        </if>
        <if test="'sale_province_city'.toString() == column.value">
          #{item.saleProvinceCity,jdbcType=VARCHAR}
        </if>
        <if test="'delivery_range'.toString() == column.value">
          #{item.deliveryRange,jdbcType=VARCHAR}
        </if>
        <if test="'tourist_partner_visible'.toString() == column.value">
          #{item.touristPartnerVisible,jdbcType=VARCHAR}
        </if>
        <if test="'standard_product_name'.toString() == column.value">
          #{item.standardProductName,jdbcType=VARCHAR}
        </if>
        <if test="'standard_product_attribute'.toString() == column.value">
          #{item.standardProductAttribute,jdbcType=VARCHAR}
        </if>
        <if test="'sku_service_provider'.toString() == column.value">
          #{item.skuServiceProvider,jdbcType=VARCHAR}
        </if>
        <if test="'manage_department'.toString() == column.value">
          #{item.manageDepartment,jdbcType=VARCHAR}
        </if>
        <if test="'standard_product_manager'.toString() == column.value">
          #{item.standardProductManager,jdbcType=VARCHAR}
        </if>
        <if test="'receive_order_account'.toString() == column.value">
          #{item.receiveOrderAccount,jdbcType=VARCHAR}
        </if>
        <if test="'deliver_account'.toString() == column.value">
          #{item.deliverAccount,jdbcType=VARCHAR}
        </if>
        <if test="'aftermarket_account'.toString() == column.value">
          #{item.aftermarketAccount,jdbcType=VARCHAR}
        </if>
        <if test="'sku_remark'.toString() == column.value">
          #{item.skuRemark,jdbcType=VARCHAR}
        </if>
        <if test="'province_price'.toString() == column.value">
          #{item.provincePrice,jdbcType=BIGINT}
        </if>
        <if test="'product_package_sale_name'.toString() == column.value">
          #{item.productPackageSaleName,jdbcType=VARCHAR}
        </if>
        <if test="'product_package_service_content'.toString() == column.value">
          #{item.productPackageServiceContent,jdbcType=VARCHAR}
        </if>
        <if test="'send_contact_person'.toString() == column.value">
          #{item.sendContactPerson,jdbcType=VARCHAR}
        </if>
        <if test="'has_remuneration'.toString() == column.value">
          #{item.hasRemuneration,jdbcType=VARCHAR}
        </if>
        <if test="'remuneration_percent'.toString() == column.value">
          #{item.remunerationPercent,jdbcType=VARCHAR}
        </if>
        <if test="'cooperate_company'.toString() == column.value">
          #{item.cooperateCompany,jdbcType=VARCHAR}
        </if>
        <if test="'order_master_handler'.toString() == column.value">
          #{item.orderMasterHandler,jdbcType=VARCHAR}
        </if>
        <if test="'order_slave_handler'.toString() == column.value">
          #{item.orderSlaveHandler,jdbcType=VARCHAR}
        </if>
        <if test="'min_purchase_num'.toString() == column.value">
          #{item.minPurchaseNum,jdbcType=INTEGER}
        </if>
        <if test="'shelf_status'.toString() == column.value">
          #{item.shelfStatus,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>