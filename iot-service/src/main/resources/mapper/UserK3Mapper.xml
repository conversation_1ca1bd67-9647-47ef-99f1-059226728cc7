<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.UserK3Mapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.UserK3">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 21 23:30:29 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="account" jdbcType="VARCHAR" property="account" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="department" jdbcType="VARCHAR" property="department" />
    <result column="team" jdbcType="VARCHAR" property="team" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="usercode" jdbcType="VARCHAR" property="usercode" />
    <result column="ihrusercode" jdbcType="VARCHAR" property="ihrusercode" />
    <result column="createor" jdbcType="VARCHAR" property="createor" />
    <result column="project" jdbcType="VARCHAR" property="project" />
    <result column="costcenter" jdbcType="VARCHAR" property="costcenter" />
    <result column="costcenterName" jdbcType="VARCHAR" property="costcentername" />
    <result column="sellerdept" jdbcType="VARCHAR" property="sellerdept" />
    <result column="sellerteam" jdbcType="VARCHAR" property="sellerteam" />
    <result column="createtime" jdbcType="TIMESTAMP" property="createtime" />
    <result column="updatetime" jdbcType="TIMESTAMP" property="updatetime" />
    <result column="provincename" jdbcType="VARCHAR" property="provincename" />
    <result column="provincecode" jdbcType="VARCHAR" property="provincecode" />
    <result column="usertype" jdbcType="VARCHAR" property="usertype" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 21 23:30:29 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 21 23:30:29 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 21 23:30:29 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, account, name, department, team, phone, email, usercode, ihrusercode, createor, 
    project, costcenter, costcenterName, sellerdept, sellerteam, createtime, updatetime, 
    provincename, provincecode, usertype
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.UserK3Example" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 21 23:30:29 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_k3
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 21 23:30:29 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from user_k3
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 21 23:30:29 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from user_k3
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.UserK3Example">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 21 23:30:29 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from user_k3
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.UserK3">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 21 23:30:29 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into user_k3 (id, account, name, 
      department, team, phone, 
      email, usercode, ihrusercode, 
      createor, project, costcenter, 
      costcenterName, sellerdept, sellerteam, 
      createtime, updatetime, provincename, 
      provincecode, usertype)
    values (#{id,jdbcType=VARCHAR}, #{account,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{department,jdbcType=VARCHAR}, #{team,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, 
      #{email,jdbcType=VARCHAR}, #{usercode,jdbcType=VARCHAR}, #{ihrusercode,jdbcType=VARCHAR}, 
      #{createor,jdbcType=VARCHAR}, #{project,jdbcType=VARCHAR}, #{costcenter,jdbcType=VARCHAR}, 
      #{costcentername,jdbcType=VARCHAR}, #{sellerdept,jdbcType=VARCHAR}, #{sellerteam,jdbcType=VARCHAR}, 
      #{createtime,jdbcType=TIMESTAMP}, #{updatetime,jdbcType=TIMESTAMP}, #{provincename,jdbcType=VARCHAR}, 
      #{provincecode,jdbcType=VARCHAR}, #{usertype,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.UserK3">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 21 23:30:29 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into user_k3
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="account != null">
        account,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="department != null">
        department,
      </if>
      <if test="team != null">
        team,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="usercode != null">
        usercode,
      </if>
      <if test="ihrusercode != null">
        ihrusercode,
      </if>
      <if test="createor != null">
        createor,
      </if>
      <if test="project != null">
        project,
      </if>
      <if test="costcenter != null">
        costcenter,
      </if>
      <if test="costcentername != null">
        costcenterName,
      </if>
      <if test="sellerdept != null">
        sellerdept,
      </if>
      <if test="sellerteam != null">
        sellerteam,
      </if>
      <if test="createtime != null">
        createtime,
      </if>
      <if test="updatetime != null">
        updatetime,
      </if>
      <if test="provincename != null">
        provincename,
      </if>
      <if test="provincecode != null">
        provincecode,
      </if>
      <if test="usertype != null">
        usertype,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="account != null">
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="department != null">
        #{department,jdbcType=VARCHAR},
      </if>
      <if test="team != null">
        #{team,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="usercode != null">
        #{usercode,jdbcType=VARCHAR},
      </if>
      <if test="ihrusercode != null">
        #{ihrusercode,jdbcType=VARCHAR},
      </if>
      <if test="createor != null">
        #{createor,jdbcType=VARCHAR},
      </if>
      <if test="project != null">
        #{project,jdbcType=VARCHAR},
      </if>
      <if test="costcenter != null">
        #{costcenter,jdbcType=VARCHAR},
      </if>
      <if test="costcentername != null">
        #{costcentername,jdbcType=VARCHAR},
      </if>
      <if test="sellerdept != null">
        #{sellerdept,jdbcType=VARCHAR},
      </if>
      <if test="sellerteam != null">
        #{sellerteam,jdbcType=VARCHAR},
      </if>
      <if test="createtime != null">
        #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatetime != null">
        #{updatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="provincename != null">
        #{provincename,jdbcType=VARCHAR},
      </if>
      <if test="provincecode != null">
        #{provincecode,jdbcType=VARCHAR},
      </if>
      <if test="usertype != null">
        #{usertype,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.UserK3Example" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 21 23:30:29 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from user_k3
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 21 23:30:29 CST 2024. by MyBatis Generator, do not modify.
    -->
    update user_k3
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.account != null">
        account = #{record.account,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.department != null">
        department = #{record.department,jdbcType=VARCHAR},
      </if>
      <if test="record.team != null">
        team = #{record.team,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.email != null">
        email = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.usercode != null">
        usercode = #{record.usercode,jdbcType=VARCHAR},
      </if>
      <if test="record.ihrusercode != null">
        ihrusercode = #{record.ihrusercode,jdbcType=VARCHAR},
      </if>
      <if test="record.createor != null">
        createor = #{record.createor,jdbcType=VARCHAR},
      </if>
      <if test="record.project != null">
        project = #{record.project,jdbcType=VARCHAR},
      </if>
      <if test="record.costcenter != null">
        costcenter = #{record.costcenter,jdbcType=VARCHAR},
      </if>
      <if test="record.costcentername != null">
        costcenterName = #{record.costcentername,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerdept != null">
        sellerdept = #{record.sellerdept,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerteam != null">
        sellerteam = #{record.sellerteam,jdbcType=VARCHAR},
      </if>
      <if test="record.createtime != null">
        createtime = #{record.createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatetime != null">
        updatetime = #{record.updatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.provincename != null">
        provincename = #{record.provincename,jdbcType=VARCHAR},
      </if>
      <if test="record.provincecode != null">
        provincecode = #{record.provincecode,jdbcType=VARCHAR},
      </if>
      <if test="record.usertype != null">
        usertype = #{record.usertype,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 21 23:30:29 CST 2024. by MyBatis Generator, do not modify.
    -->
    update user_k3
    set id = #{record.id,jdbcType=VARCHAR},
      account = #{record.account,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      department = #{record.department,jdbcType=VARCHAR},
      team = #{record.team,jdbcType=VARCHAR},
      phone = #{record.phone,jdbcType=VARCHAR},
      email = #{record.email,jdbcType=VARCHAR},
      usercode = #{record.usercode,jdbcType=VARCHAR},
      ihrusercode = #{record.ihrusercode,jdbcType=VARCHAR},
      createor = #{record.createor,jdbcType=VARCHAR},
      project = #{record.project,jdbcType=VARCHAR},
      costcenter = #{record.costcenter,jdbcType=VARCHAR},
      costcenterName = #{record.costcentername,jdbcType=VARCHAR},
      sellerdept = #{record.sellerdept,jdbcType=VARCHAR},
      sellerteam = #{record.sellerteam,jdbcType=VARCHAR},
      createtime = #{record.createtime,jdbcType=TIMESTAMP},
      updatetime = #{record.updatetime,jdbcType=TIMESTAMP},
      provincename = #{record.provincename,jdbcType=VARCHAR},
      provincecode = #{record.provincecode,jdbcType=VARCHAR},
      usertype = #{record.usertype,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.UserK3">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 21 23:30:29 CST 2024. by MyBatis Generator, do not modify.
    -->
    update user_k3
    <set>
      <if test="account != null">
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="department != null">
        department = #{department,jdbcType=VARCHAR},
      </if>
      <if test="team != null">
        team = #{team,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="usercode != null">
        usercode = #{usercode,jdbcType=VARCHAR},
      </if>
      <if test="ihrusercode != null">
        ihrusercode = #{ihrusercode,jdbcType=VARCHAR},
      </if>
      <if test="createor != null">
        createor = #{createor,jdbcType=VARCHAR},
      </if>
      <if test="project != null">
        project = #{project,jdbcType=VARCHAR},
      </if>
      <if test="costcenter != null">
        costcenter = #{costcenter,jdbcType=VARCHAR},
      </if>
      <if test="costcentername != null">
        costcenterName = #{costcentername,jdbcType=VARCHAR},
      </if>
      <if test="sellerdept != null">
        sellerdept = #{sellerdept,jdbcType=VARCHAR},
      </if>
      <if test="sellerteam != null">
        sellerteam = #{sellerteam,jdbcType=VARCHAR},
      </if>
      <if test="createtime != null">
        createtime = #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatetime != null">
        updatetime = #{updatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="provincename != null">
        provincename = #{provincename,jdbcType=VARCHAR},
      </if>
      <if test="provincecode != null">
        provincecode = #{provincecode,jdbcType=VARCHAR},
      </if>
      <if test="usertype != null">
        usertype = #{usertype,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.UserK3">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 21 23:30:29 CST 2024. by MyBatis Generator, do not modify.
    -->
    update user_k3
    set account = #{account,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      department = #{department,jdbcType=VARCHAR},
      team = #{team,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      usercode = #{usercode,jdbcType=VARCHAR},
      ihrusercode = #{ihrusercode,jdbcType=VARCHAR},
      createor = #{createor,jdbcType=VARCHAR},
      project = #{project,jdbcType=VARCHAR},
      costcenter = #{costcenter,jdbcType=VARCHAR},
      costcenterName = #{costcentername,jdbcType=VARCHAR},
      sellerdept = #{sellerdept,jdbcType=VARCHAR},
      sellerteam = #{sellerteam,jdbcType=VARCHAR},
      createtime = #{createtime,jdbcType=TIMESTAMP},
      updatetime = #{updatetime,jdbcType=TIMESTAMP},
      provincename = #{provincename,jdbcType=VARCHAR},
      provincecode = #{provincecode,jdbcType=VARCHAR},
      usertype = #{usertype,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 21 23:30:29 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into user_k3
    (id, account, name, department, team, phone, email, usercode, ihrusercode, createor, 
      project, costcenter, costcenterName, sellerdept, sellerteam, createtime, updatetime, 
      provincename, provincecode, usertype)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.account,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, 
        #{item.department,jdbcType=VARCHAR}, #{item.team,jdbcType=VARCHAR}, #{item.phone,jdbcType=VARCHAR}, 
        #{item.email,jdbcType=VARCHAR}, #{item.usercode,jdbcType=VARCHAR}, #{item.ihrusercode,jdbcType=VARCHAR}, 
        #{item.createor,jdbcType=VARCHAR}, #{item.project,jdbcType=VARCHAR}, #{item.costcenter,jdbcType=VARCHAR}, 
        #{item.costcentername,jdbcType=VARCHAR}, #{item.sellerdept,jdbcType=VARCHAR}, #{item.sellerteam,jdbcType=VARCHAR}, 
        #{item.createtime,jdbcType=TIMESTAMP}, #{item.updatetime,jdbcType=TIMESTAMP}, #{item.provincename,jdbcType=VARCHAR}, 
        #{item.provincecode,jdbcType=VARCHAR}, #{item.usertype,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Nov 21 23:30:29 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into user_k3 (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'account'.toString() == column.value">
          #{item.account,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'department'.toString() == column.value">
          #{item.department,jdbcType=VARCHAR}
        </if>
        <if test="'team'.toString() == column.value">
          #{item.team,jdbcType=VARCHAR}
        </if>
        <if test="'phone'.toString() == column.value">
          #{item.phone,jdbcType=VARCHAR}
        </if>
        <if test="'email'.toString() == column.value">
          #{item.email,jdbcType=VARCHAR}
        </if>
        <if test="'usercode'.toString() == column.value">
          #{item.usercode,jdbcType=VARCHAR}
        </if>
        <if test="'ihrusercode'.toString() == column.value">
          #{item.ihrusercode,jdbcType=VARCHAR}
        </if>
        <if test="'createor'.toString() == column.value">
          #{item.createor,jdbcType=VARCHAR}
        </if>
        <if test="'project'.toString() == column.value">
          #{item.project,jdbcType=VARCHAR}
        </if>
        <if test="'costcenter'.toString() == column.value">
          #{item.costcenter,jdbcType=VARCHAR}
        </if>
        <if test="'costcenterName'.toString() == column.value">
          #{item.costcentername,jdbcType=VARCHAR}
        </if>
        <if test="'sellerdept'.toString() == column.value">
          #{item.sellerdept,jdbcType=VARCHAR}
        </if>
        <if test="'sellerteam'.toString() == column.value">
          #{item.sellerteam,jdbcType=VARCHAR}
        </if>
        <if test="'createtime'.toString() == column.value">
          #{item.createtime,jdbcType=TIMESTAMP}
        </if>
        <if test="'updatetime'.toString() == column.value">
          #{item.updatetime,jdbcType=TIMESTAMP}
        </if>
        <if test="'provincename'.toString() == column.value">
          #{item.provincename,jdbcType=VARCHAR}
        </if>
        <if test="'provincecode'.toString() == column.value">
          #{item.provincecode,jdbcType=VARCHAR}
        </if>
        <if test="'usertype'.toString() == column.value">
          #{item.usertype,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>