<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.InventoryCutInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.InventoryCutInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Feb 02 14:35:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="inventory_now_name" jdbcType="VARCHAR" property="inventoryNowName" />
    <result column="inventory_pattern_name" jdbcType="VARCHAR" property="inventoryPatternName" />
    <result column="cut_time" jdbcType="TIMESTAMP" property="cutTime" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="is_cancel" jdbcType="BIT" property="isCancel" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Feb 02 14:35:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Feb 02 14:35:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Feb 02 14:35:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, inventory_now_name, inventory_pattern_name, cut_time, operator, is_cancel, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.InventoryCutInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Feb 02 14:35:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from inventory_cut_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Feb 02 14:35:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from inventory_cut_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Feb 02 14:35:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from inventory_cut_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.InventoryCutInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Feb 02 14:35:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from inventory_cut_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.InventoryCutInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Feb 02 14:35:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into inventory_cut_info (id, inventory_now_name, inventory_pattern_name, 
      cut_time, operator, is_cancel, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{inventoryNowName,jdbcType=VARCHAR}, #{inventoryPatternName,jdbcType=VARCHAR}, 
      #{cutTime,jdbcType=TIMESTAMP}, #{operator,jdbcType=VARCHAR}, #{isCancel,jdbcType=BIT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.InventoryCutInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Feb 02 14:35:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into inventory_cut_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="inventoryNowName != null">
        inventory_now_name,
      </if>
      <if test="inventoryPatternName != null">
        inventory_pattern_name,
      </if>
      <if test="cutTime != null">
        cut_time,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="isCancel != null">
        is_cancel,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="inventoryNowName != null">
        #{inventoryNowName,jdbcType=VARCHAR},
      </if>
      <if test="inventoryPatternName != null">
        #{inventoryPatternName,jdbcType=VARCHAR},
      </if>
      <if test="cutTime != null">
        #{cutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="isCancel != null">
        #{isCancel,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.InventoryCutInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Feb 02 14:35:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from inventory_cut_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Feb 02 14:35:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    update inventory_cut_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.inventoryNowName != null">
        inventory_now_name = #{record.inventoryNowName,jdbcType=VARCHAR},
      </if>
      <if test="record.inventoryPatternName != null">
        inventory_pattern_name = #{record.inventoryPatternName,jdbcType=VARCHAR},
      </if>
      <if test="record.cutTime != null">
        cut_time = #{record.cutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.isCancel != null">
        is_cancel = #{record.isCancel,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Feb 02 14:35:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    update inventory_cut_info
    set id = #{record.id,jdbcType=VARCHAR},
      inventory_now_name = #{record.inventoryNowName,jdbcType=VARCHAR},
      inventory_pattern_name = #{record.inventoryPatternName,jdbcType=VARCHAR},
      cut_time = #{record.cutTime,jdbcType=TIMESTAMP},
      operator = #{record.operator,jdbcType=VARCHAR},
      is_cancel = #{record.isCancel,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.InventoryCutInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Feb 02 14:35:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    update inventory_cut_info
    <set>
      <if test="inventoryNowName != null">
        inventory_now_name = #{inventoryNowName,jdbcType=VARCHAR},
      </if>
      <if test="inventoryPatternName != null">
        inventory_pattern_name = #{inventoryPatternName,jdbcType=VARCHAR},
      </if>
      <if test="cutTime != null">
        cut_time = #{cutTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="isCancel != null">
        is_cancel = #{isCancel,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.InventoryCutInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Feb 02 14:35:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    update inventory_cut_info
    set inventory_now_name = #{inventoryNowName,jdbcType=VARCHAR},
      inventory_pattern_name = #{inventoryPatternName,jdbcType=VARCHAR},
      cut_time = #{cutTime,jdbcType=TIMESTAMP},
      operator = #{operator,jdbcType=VARCHAR},
      is_cancel = #{isCancel,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Feb 02 14:35:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into inventory_cut_info
    (id, inventory_now_name, inventory_pattern_name, cut_time, operator, is_cancel, create_time, 
      update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.inventoryNowName,jdbcType=VARCHAR}, #{item.inventoryPatternName,jdbcType=VARCHAR}, 
        #{item.cutTime,jdbcType=TIMESTAMP}, #{item.operator,jdbcType=VARCHAR}, #{item.isCancel,jdbcType=BIT}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Feb 02 14:35:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into inventory_cut_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'inventory_now_name'.toString() == column.value">
          #{item.inventoryNowName,jdbcType=VARCHAR}
        </if>
        <if test="'inventory_pattern_name'.toString() == column.value">
          #{item.inventoryPatternName,jdbcType=VARCHAR}
        </if>
        <if test="'cut_time'.toString() == column.value">
          #{item.cutTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'operator'.toString() == column.value">
          #{item.operator,jdbcType=VARCHAR}
        </if>
        <if test="'is_cancel'.toString() == column.value">
          #{item.isCancel,jdbcType=BIT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>