<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.SpuOfferingInfoHistoryMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.SpuOfferingInfoHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 14:50:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="oper_id" jdbcType="VARCHAR" property="operId" />
    <result column="offering_code" jdbcType="VARCHAR" property="offeringCode" />
    <result column="offering_name" jdbcType="VARCHAR" property="offeringName" />
    <result column="offering_status" jdbcType="VARCHAR" property="offeringStatus" />
    <result column="oper_type" jdbcType="VARCHAR" property="operType" />
    <result column="sale_object" jdbcType="VARCHAR" property="saleObject" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="img_url" jdbcType="VARCHAR" property="imgUrl" />
    <result column="tag" jdbcType="VARCHAR" property="tag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime" />
    <result column="spu_offering_version" jdbcType="VARCHAR" property="spuOfferingVersion" />
    <result column="spu_id" jdbcType="VARCHAR" property="spuId" />
    <result column="list_platform" jdbcType="VARCHAR" property="listPlatform" />
    <result column="product_keywords" jdbcType="VARCHAR" property="productKeywords" />
    <result column="secretly_listed" jdbcType="VARCHAR" property="secretlyListed" />
    <result column="product_description" jdbcType="VARCHAR" property="productDescription" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 14:50:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 14:50:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 14:50:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, oper_id, offering_code, offering_name, offering_status, oper_type, sale_object, 
    url, img_url, tag, create_time, update_time, delete_time, spu_offering_version, spu_id, 
    list_platform, product_keywords, secretly_listed, product_description
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.SpuOfferingInfoHistoryExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 14:50:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from spu_offering_info_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 14:50:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from spu_offering_info_history
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 14:50:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from spu_offering_info_history
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.SpuOfferingInfoHistoryExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 14:50:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from spu_offering_info_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.SpuOfferingInfoHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 14:50:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into spu_offering_info_history (id, oper_id, offering_code, 
      offering_name, offering_status, oper_type, 
      sale_object, url, img_url, 
      tag, create_time, update_time, 
      delete_time, spu_offering_version, spu_id, 
      list_platform, product_keywords, secretly_listed, 
      product_description)
    values (#{id,jdbcType=VARCHAR}, #{operId,jdbcType=VARCHAR}, #{offeringCode,jdbcType=VARCHAR}, 
      #{offeringName,jdbcType=VARCHAR}, #{offeringStatus,jdbcType=VARCHAR}, #{operType,jdbcType=VARCHAR}, 
      #{saleObject,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, #{imgUrl,jdbcType=VARCHAR}, 
      #{tag,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deleteTime,jdbcType=TIMESTAMP}, #{spuOfferingVersion,jdbcType=VARCHAR}, #{spuId,jdbcType=VARCHAR}, 
      #{listPlatform,jdbcType=VARCHAR}, #{productKeywords,jdbcType=VARCHAR}, #{secretlyListed,jdbcType=VARCHAR}, 
      #{productDescription,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.SpuOfferingInfoHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 14:50:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into spu_offering_info_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="operId != null">
        oper_id,
      </if>
      <if test="offeringCode != null">
        offering_code,
      </if>
      <if test="offeringName != null">
        offering_name,
      </if>
      <if test="offeringStatus != null">
        offering_status,
      </if>
      <if test="operType != null">
        oper_type,
      </if>
      <if test="saleObject != null">
        sale_object,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="imgUrl != null">
        img_url,
      </if>
      <if test="tag != null">
        tag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleteTime != null">
        delete_time,
      </if>
      <if test="spuOfferingVersion != null">
        spu_offering_version,
      </if>
      <if test="spuId != null">
        spu_id,
      </if>
      <if test="listPlatform != null">
        list_platform,
      </if>
      <if test="productKeywords != null">
        product_keywords,
      </if>
      <if test="secretlyListed != null">
        secretly_listed,
      </if>
      <if test="productDescription != null">
        product_description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="operId != null">
        #{operId,jdbcType=VARCHAR},
      </if>
      <if test="offeringCode != null">
        #{offeringCode,jdbcType=VARCHAR},
      </if>
      <if test="offeringName != null">
        #{offeringName,jdbcType=VARCHAR},
      </if>
      <if test="offeringStatus != null">
        #{offeringStatus,jdbcType=VARCHAR},
      </if>
      <if test="operType != null">
        #{operType,jdbcType=VARCHAR},
      </if>
      <if test="saleObject != null">
        #{saleObject,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="imgUrl != null">
        #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="tag != null">
        #{tag,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteTime != null">
        #{deleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="spuOfferingVersion != null">
        #{spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="spuId != null">
        #{spuId,jdbcType=VARCHAR},
      </if>
      <if test="listPlatform != null">
        #{listPlatform,jdbcType=VARCHAR},
      </if>
      <if test="productKeywords != null">
        #{productKeywords,jdbcType=VARCHAR},
      </if>
      <if test="secretlyListed != null">
        #{secretlyListed,jdbcType=VARCHAR},
      </if>
      <if test="productDescription != null">
        #{productDescription,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.SpuOfferingInfoHistoryExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 14:50:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from spu_offering_info_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 14:50:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    update spu_offering_info_history
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.operId != null">
        oper_id = #{record.operId,jdbcType=VARCHAR},
      </if>
      <if test="record.offeringCode != null">
        offering_code = #{record.offeringCode,jdbcType=VARCHAR},
      </if>
      <if test="record.offeringName != null">
        offering_name = #{record.offeringName,jdbcType=VARCHAR},
      </if>
      <if test="record.offeringStatus != null">
        offering_status = #{record.offeringStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.operType != null">
        oper_type = #{record.operType,jdbcType=VARCHAR},
      </if>
      <if test="record.saleObject != null">
        sale_object = #{record.saleObject,jdbcType=VARCHAR},
      </if>
      <if test="record.url != null">
        url = #{record.url,jdbcType=VARCHAR},
      </if>
      <if test="record.imgUrl != null">
        img_url = #{record.imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.tag != null">
        tag = #{record.tag,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleteTime != null">
        delete_time = #{record.deleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.spuOfferingVersion != null">
        spu_offering_version = #{record.spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.spuId != null">
        spu_id = #{record.spuId,jdbcType=VARCHAR},
      </if>
      <if test="record.listPlatform != null">
        list_platform = #{record.listPlatform,jdbcType=VARCHAR},
      </if>
      <if test="record.productKeywords != null">
        product_keywords = #{record.productKeywords,jdbcType=VARCHAR},
      </if>
      <if test="record.secretlyListed != null">
        secretly_listed = #{record.secretlyListed,jdbcType=VARCHAR},
      </if>
      <if test="record.productDescription != null">
        product_description = #{record.productDescription,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 14:50:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    update spu_offering_info_history
    set id = #{record.id,jdbcType=VARCHAR},
      oper_id = #{record.operId,jdbcType=VARCHAR},
      offering_code = #{record.offeringCode,jdbcType=VARCHAR},
      offering_name = #{record.offeringName,jdbcType=VARCHAR},
      offering_status = #{record.offeringStatus,jdbcType=VARCHAR},
      oper_type = #{record.operType,jdbcType=VARCHAR},
      sale_object = #{record.saleObject,jdbcType=VARCHAR},
      url = #{record.url,jdbcType=VARCHAR},
      img_url = #{record.imgUrl,jdbcType=VARCHAR},
      tag = #{record.tag,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      delete_time = #{record.deleteTime,jdbcType=TIMESTAMP},
      spu_offering_version = #{record.spuOfferingVersion,jdbcType=VARCHAR},
      spu_id = #{record.spuId,jdbcType=VARCHAR},
      list_platform = #{record.listPlatform,jdbcType=VARCHAR},
      product_keywords = #{record.productKeywords,jdbcType=VARCHAR},
      secretly_listed = #{record.secretlyListed,jdbcType=VARCHAR},
      product_description = #{record.productDescription,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.SpuOfferingInfoHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 14:50:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    update spu_offering_info_history
    <set>
      <if test="operId != null">
        oper_id = #{operId,jdbcType=VARCHAR},
      </if>
      <if test="offeringCode != null">
        offering_code = #{offeringCode,jdbcType=VARCHAR},
      </if>
      <if test="offeringName != null">
        offering_name = #{offeringName,jdbcType=VARCHAR},
      </if>
      <if test="offeringStatus != null">
        offering_status = #{offeringStatus,jdbcType=VARCHAR},
      </if>
      <if test="operType != null">
        oper_type = #{operType,jdbcType=VARCHAR},
      </if>
      <if test="saleObject != null">
        sale_object = #{saleObject,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="imgUrl != null">
        img_url = #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="tag != null">
        tag = #{tag,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteTime != null">
        delete_time = #{deleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="spuOfferingVersion != null">
        spu_offering_version = #{spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="spuId != null">
        spu_id = #{spuId,jdbcType=VARCHAR},
      </if>
      <if test="listPlatform != null">
        list_platform = #{listPlatform,jdbcType=VARCHAR},
      </if>
      <if test="productKeywords != null">
        product_keywords = #{productKeywords,jdbcType=VARCHAR},
      </if>
      <if test="secretlyListed != null">
        secretly_listed = #{secretlyListed,jdbcType=VARCHAR},
      </if>
      <if test="productDescription != null">
        product_description = #{productDescription,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.SpuOfferingInfoHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 14:50:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    update spu_offering_info_history
    set oper_id = #{operId,jdbcType=VARCHAR},
      offering_code = #{offeringCode,jdbcType=VARCHAR},
      offering_name = #{offeringName,jdbcType=VARCHAR},
      offering_status = #{offeringStatus,jdbcType=VARCHAR},
      oper_type = #{operType,jdbcType=VARCHAR},
      sale_object = #{saleObject,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      img_url = #{imgUrl,jdbcType=VARCHAR},
      tag = #{tag,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      delete_time = #{deleteTime,jdbcType=TIMESTAMP},
      spu_offering_version = #{spuOfferingVersion,jdbcType=VARCHAR},
      spu_id = #{spuId,jdbcType=VARCHAR},
      list_platform = #{listPlatform,jdbcType=VARCHAR},
      product_keywords = #{productKeywords,jdbcType=VARCHAR},
      secretly_listed = #{secretlyListed,jdbcType=VARCHAR},
      product_description = #{productDescription,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 14:50:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into spu_offering_info_history
    (id, oper_id, offering_code, offering_name, offering_status, oper_type, sale_object, 
      url, img_url, tag, create_time, update_time, delete_time, spu_offering_version, 
      spu_id, list_platform, product_keywords, secretly_listed, product_description)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.operId,jdbcType=VARCHAR}, #{item.offeringCode,jdbcType=VARCHAR}, 
        #{item.offeringName,jdbcType=VARCHAR}, #{item.offeringStatus,jdbcType=VARCHAR}, 
        #{item.operType,jdbcType=VARCHAR}, #{item.saleObject,jdbcType=VARCHAR}, #{item.url,jdbcType=VARCHAR}, 
        #{item.imgUrl,jdbcType=VARCHAR}, #{item.tag,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.deleteTime,jdbcType=TIMESTAMP}, #{item.spuOfferingVersion,jdbcType=VARCHAR}, 
        #{item.spuId,jdbcType=VARCHAR}, #{item.listPlatform,jdbcType=VARCHAR}, #{item.productKeywords,jdbcType=VARCHAR}, 
        #{item.secretlyListed,jdbcType=VARCHAR}, #{item.productDescription,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Feb 11 14:50:32 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into spu_offering_info_history (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'oper_id'.toString() == column.value">
          #{item.operId,jdbcType=VARCHAR}
        </if>
        <if test="'offering_code'.toString() == column.value">
          #{item.offeringCode,jdbcType=VARCHAR}
        </if>
        <if test="'offering_name'.toString() == column.value">
          #{item.offeringName,jdbcType=VARCHAR}
        </if>
        <if test="'offering_status'.toString() == column.value">
          #{item.offeringStatus,jdbcType=VARCHAR}
        </if>
        <if test="'oper_type'.toString() == column.value">
          #{item.operType,jdbcType=VARCHAR}
        </if>
        <if test="'sale_object'.toString() == column.value">
          #{item.saleObject,jdbcType=VARCHAR}
        </if>
        <if test="'url'.toString() == column.value">
          #{item.url,jdbcType=VARCHAR}
        </if>
        <if test="'img_url'.toString() == column.value">
          #{item.imgUrl,jdbcType=VARCHAR}
        </if>
        <if test="'tag'.toString() == column.value">
          #{item.tag,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'delete_time'.toString() == column.value">
          #{item.deleteTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'spu_offering_version'.toString() == column.value">
          #{item.spuOfferingVersion,jdbcType=VARCHAR}
        </if>
        <if test="'spu_id'.toString() == column.value">
          #{item.spuId,jdbcType=VARCHAR}
        </if>
        <if test="'list_platform'.toString() == column.value">
          #{item.listPlatform,jdbcType=VARCHAR}
        </if>
        <if test="'product_keywords'.toString() == column.value">
          #{item.productKeywords,jdbcType=VARCHAR}
        </if>
        <if test="'secretly_listed'.toString() == column.value">
          #{item.secretlyListed,jdbcType=VARCHAR}
        </if>
        <if test="'product_description'.toString() == column.value">
          #{item.productDescription,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>