<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.ProductExistDataFlowMapperExt">

    <resultMap id="spuSkuAtomInfoMap" type="com.chinamobile.iot.sc.pojo.dto.SpuSkuAtomInfoDTO">
        <result column="spuOfferingName" jdbcType="VARCHAR" property="spuOfferingName" />
        <result column="spuCode" jdbcType="VARCHAR" property="spuCode" />
        <result column="spuUrl" jdbcType="VARCHAR" property="spuUrl" />
        <result column="skuOfferingName" jdbcType="VARCHAR" property="skuOfferingName" />
        <result column="skuCode" jdbcType="VARCHAR" property="skuCode" />
        <result column="skuReleaseProvinceCity" jdbcType="VARCHAR" property="skuReleaseProvinceCity" />
        <result column="skuPrice" jdbcType="BIGINT" property="skuPrice" />
        <result column="supplierName" jdbcType="VARCHAR" property="supplierName" />
        <collection property="atomInfoList" ofType="com.chinamobile.iot.sc.pojo.dto.AtomInfoDTO">
            <id column="atomId" />
            <result column="atomOfferingName" jdbcType="VARCHAR" property="atomOfferingName" />
            <result column="atomOfferingCode" jdbcType="VARCHAR" property="atomOfferingCode" />
            <result column="atomOfferingCode" jdbcType="VARCHAR" property="atomOfferingCode" />
            <result column="atomQuantity" jdbcType="BIGINT" property="atomQuantity" />
            <result column="extHardOfferingCode" jdbcType="VARCHAR" property="extHardOfferingCode" />
            <result column="extSoftOfferingCode" jdbcType="VARCHAR" property="extSoftOfferingCode" />
            <result column="atomSalePrice" jdbcType="BIGINT" property="salePrice" />
            <result column="settlePrice" jdbcType="BIGINT" property="settlePrice" />
            <result column="settlePricePartner" jdbcType="VARCHAR" property="settlePricePartner" />
            <result column="chargeId" jdbcType="VARCHAR" property="chargeId" />
        </collection>

    </resultMap>

    <select id="listUsedProductExistData" resultType="com.chinamobile.iot.sc.pojo.dto.UsedProductExistDataDTO">
        select
            spu.offering_code spuCode,
            spu.offering_name spuOfferingName,
            sku.offering_code skuCode,
            sku.offering_name skuOfferingName
        from
            spu_offering_info spu,
            sku_offering_info sku
        where
            spu.offering_code = sku.spu_code
        and spu.offering_status = 1
        and spu.delete_time is null
        and sku.offering_status = 1
        and sku.delete_time is null
        order by spu.offering_code
    </select>

    <select id="getSpuSkuAtomInfo" resultMap="spuSkuAtomInfoMap">
        select
            spu.offering_name spuOfferingName,
            spu.offering_code spuCode,
            spu.url spuUrl,
            sku.offering_name skuOfferingName,
            sku.offering_code skuCode,
            (SELECT GROUP_CONCAT(CONCAT(province_code,IF(city_code is null or city_code = '','',CONCAT('_',city_code)))) FROM sku_release_target WHERE sku_offering_code = sku.offering_code) skuReleaseProvinceCity,
            sku.price skuPrice,
            sku.supplier_name supplierName,
            aoi.offering_name atomOfferingName,
            aoi.offering_code atomOfferingCode,
            aoi.quantity atomQuantity,
            aoi.ext_hard_offering_code extHardOfferingCode,
            aoi.ext_soft_offering_code extSoftOfferingCode,
            aoi.settle_price settlePrice,
            aoi.settlePricePartner settlePricePartner,
            aoi.charge_id chargeId,
            aoi.atom_sale_price atomSalePrice,
            aoi.id atomId
        from
            spu_offering_info spu
            join sku_offering_info sku on spu.offering_code = sku.spu_code and sku.offering_status = '1' and sku.delete_time is null
            join atom_offering_info aoi on aoi.spu_code = spu.offering_code and aoi.sku_code = sku.offering_code and aoi.delete_time is null
        where
            spu.offering_code = #{spuSkuAtomParam.spuCode} and spu.delete_time is null and spu.offering_status = '1'
            and sku.offering_code = #{spuSkuAtomParam.skuCode}
    </select>

</mapper>