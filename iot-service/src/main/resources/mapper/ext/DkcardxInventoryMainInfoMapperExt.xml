<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.DkcardxInventoryMainInfoMapperExt">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryMainInfo">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="device_version" jdbcType="VARCHAR" property="deviceVersion" />
    <result column="terminal_type" jdbcType="VARCHAR" property="terminalType" />
    <result column="cust_code" jdbcType="VARCHAR" property="custCode" />
    <result column="cust_name" jdbcType="VARCHAR" property="custName" />
    <result column="template_id" jdbcType="VARCHAR" property="templateId" />
    <result column="template_name" jdbcType="VARCHAR" property="templateName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="listDkcardxInventoryMainInfo"  resultMap="BaseResultMap">
    select
    id, be_id, province_name, device_version, terminal_type, cust_code, cust_name, template_id,
    template_name, create_time, update_time
    from dkcardx_inventory_main_info
    where 1=1
    <if test="dkcardxInventoryMainInfoParam.terminalType != null and dkcardxInventoryMainInfoParam.terminalType != ''">
      and terminal_type = #{dkcardxInventoryMainInfoParam.terminalType}
    </if>
    <if test="dkcardxInventoryMainInfoParam.beId != null and dkcardxInventoryMainInfoParam.beId != ''">
      and be_id = #{dkcardxInventoryMainInfoParam.beId}
    </if>
      <if test="dkcardxInventoryMainInfoParam.beIdList != null and dkcardxInventoryMainInfoParam.beIdList.size() != 0">
          and be_id in
          <foreach collection="dkcardxInventoryMainInfoParam.beIdList" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
    <if test="dkcardxInventoryMainInfoParam.deviceVersion != null and dkcardxInventoryMainInfoParam.deviceVersion != ''">
      and device_version like '%${dkcardxInventoryMainInfoParam.deviceVersion}%'
    </if>
    <if test="dkcardxInventoryMainInfoParam.pageTemplateName != null and dkcardxInventoryMainInfoParam.pageTemplateName != ''">
      and template_name like '%${dkcardxInventoryMainInfoParam.pageTemplateName}%'
    </if>
    <if test="dkcardxInventoryMainInfoParam.custName != null and dkcardxInventoryMainInfoParam.custName != ''">
      and cust_name like '%${dkcardxInventoryMainInfoParam.custName}%'
    </if>
      <if test="dkcardxInventoryMainInfoParam.terminalTypeList != null and dkcardxInventoryMainInfoParam.terminalTypeList.size() != 0">
          and terminal_type in
          <foreach collection="dkcardxInventoryMainInfoParam.terminalTypeList" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
  </select>
  
  
  <select id="exportDxInventoryMainAndDetail" resultType="com.chinamobile.iot.sc.pojo.dto.DxInventoryMainAndDetailExportDTO">
      select beId,provinceName,deviceVersion,
      terminalType,custName,templateName,
      detailCityName,reserveQuatity,
      currentInventory,totalInventory
      from
      (
      select dimi.id inventoryMainId,
      dimi.be_id beId,
      dimi.province_name provinceName ,
      dimi.device_version deviceVersion,
      dimi.terminal_type terminalType,
      dimi.cust_name custName,
      dimi.template_name templateName,
      case
      when didi.location is not null and didi.location != '' then didi.city_name
      else didi.province_alias_name
      end detailCityName,
      didi.reserve_quatity reserveQuatity,
      didi.current_inventory currentInventory,
      didi.total_inventory totalInventory
      from
      dkcardx_inventory_main_info dimi
      inner join dkcardx_inventory_detail_info didi on dimi.id = didi.inventory_main_id and (didi.location is null or didi.location = '')
      union
      select dimi.id inventoryMainId,
      dimi.be_id beId,
      dimi.province_name provinceName ,
      dimi.device_version deviceVersion,
      dimi.terminal_type terminalType,
      dimi.cust_name custName,
      dimi.template_name templateName,
      case
      when didi.location is not null and didi.location != '' then didi.city_name
      else didi.province_alias_name
      end detailCityName,
      didi.reserve_quatity reserveQuatity,
      didi.current_inventory currentInventory,
      didi.total_inventory totalInventory
      from
      dkcardx_inventory_main_info dimi
      inner join dkcardx_inventory_detail_info didi on dimi.id = didi.inventory_main_id
      and didi.location is not null and didi.location != ''
      where 1=1
      <if test="dkcardxInventoryMainInfoParam.location != null and dkcardxInventoryMainInfoParam.location != ''">
          and didi.location = #{dkcardxInventoryMainInfoParam.location}
      </if>
      ) dm
      where 1=1
    <if test="dkcardxInventoryMainInfoParam.terminalType != null and dkcardxInventoryMainInfoParam.terminalType != ''">
      and terminalType = #{dkcardxInventoryMainInfoParam.terminalType}
    </if>
    <if test="dkcardxInventoryMainInfoParam.beId != null and dkcardxInventoryMainInfoParam.beId != ''">
      and beId = #{dkcardxInventoryMainInfoParam.beId}
    </if>
      <if test="dkcardxInventoryMainInfoParam.beIdList != null and dkcardxInventoryMainInfoParam.beIdList.size() != 0">
          and beId in
          <foreach collection="dkcardxInventoryMainInfoParam.beIdList" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
    <if test="dkcardxInventoryMainInfoParam.deviceVersion != null and dkcardxInventoryMainInfoParam.deviceVersion != ''">
      and deviceVersion like '%${dkcardxInventoryMainInfoParam.deviceVersion}%'
    </if>
    <if test="dkcardxInventoryMainInfoParam.pageTemplateName != null and dkcardxInventoryMainInfoParam.pageTemplateName != ''">
      and templateName like '%${dkcardxInventoryMainInfoParam.pageTemplateName}%'
    </if>
    <if test="dkcardxInventoryMainInfoParam.custName != null and dkcardxInventoryMainInfoParam.custName != ''">
      and custName like '%${dkcardxInventoryMainInfoParam.custName}%'
    </if>
    order by beId desc,inventoryMainId desc
  </select>

    <select id="listCardRelationByInventory" resultType="com.chinamobile.iot.sc.pojo.dto.DkcardxInventoryCardDetailInfoDTO">
        select * from (
        select dimi.id inventoryMainId,
        cr.id cardRelationId,
        cr.imei,
        cr.sell_status sellStatus,
        cr.location
        from
        dkcardx_inventory_main_info dimi,
        card_relation cr
        where
        dimi.be_id = cr.be_id
        and dimi.device_version = cr.device_version
        and dimi.terminal_type = cr.terminal_type
        and ifnull(dimi.cust_code,'') = ifnull(cr.cust_code,'')
        and ifnull(dimi.template_id,'') = ifnull(cr.template_id,'')
        and cr.sell_status in (1,2)
        and (cr.location is null or cr.location = '')
        and cr.delete_time is null
        union
        select dimi.id inventoryMainId,
        cr.id cardRelationId,
        cr.imei,
        cr.sell_status sellStatus,
        cr.location
        from
        dkcardx_inventory_main_info dimi,
        card_relation cr
        where
        dimi.be_id = cr.be_id
        and dimi.device_version = cr.device_version
        and dimi.terminal_type = cr.terminal_type
        and ifnull(dimi.cust_code,'') = ifnull(cr.cust_code,'')
        and ifnull(dimi.template_id,'') = ifnull(cr.template_id,'')
        and cr.sell_status in (1,2)
        and cr.location is not null and cr.location != ''
        and cr.delete_time is null
        <if test="cardDetailParam.currentLocation != null and cardDetailParam.currentLocation != ''">
            and cr.location = #{cardDetailParam.currentLocation}
        </if>
        ) cm
        where 1=1
        <if test="cardDetailParam.location != null and cardDetailParam.location != ''">
            <if test="cardDetailParam.location == -1">
                and (location is null or location = '')
            </if>
            <if test="cardDetailParam.location != -1">
                and location = #{cardDetailParam.location}
            </if>

        </if>
        <if test="cardDetailParam.inventoryMainId != null and cardDetailParam.inventoryMainId != ''">
            and inventoryMainId = #{cardDetailParam.inventoryMainId}
        </if>
        <if test="cardDetailParam.sellStatus != null and cardDetailParam.sellStatus != ''">
            and sellStatus = #{cardDetailParam.sellStatus}
        </if>
        <if test="cardDetailParam.imei != null and cardDetailParam.imei != ''">
            and imei like '%${cardDetailParam.imei}%'
        </if>

    </select>

    <select id="listXDetailLocation" resultType="com.chinamobile.iot.sc.pojo.dto.DkcardxInventoryCardDetailLocationDTO">
        select
         distinct dimi.id inventoryMainId,cr.location
        from
            dkcardx_inventory_main_info dimi,
            card_relation cr
        where
            dimi.be_id = cr.be_id
        and dimi.device_version = cr.device_version
        and dimi.terminal_type = cr.terminal_type
        and ifnull(dimi.cust_code,'') = ifnull(cr.cust_code,'')
        and ifnull(dimi.template_id,'') = ifnull(cr.template_id,'')
        and cr.sell_status in (1,2)
        and cr.delete_time is null
        and dimi.id = #{inventoryMainId}
        order by cr.location asc;
    </select>

    <select id="findAtomOrderId" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT oa.id FROM order_2c_atom_info oa
    JOIN atom_offering_info atom ON oa.spu_offering_code = atom.spu_code and oa.sku_offering_code = atom.sku_code AND oa.atom_offering_code = atom.offering_code
    JOIN dkcardx_inventory_main_info main ON atom.inventory_main_id = main.id
    WHERE
    oa.order_id = #{orderId}
    and main.device_version = #{deviceVersion}
    </select>

    <select id="getDeviceVersionConfigedAtomCount" parameterType="java.lang.String" resultType="java.lang.Integer">
    SELECT count(*) FROM atom_offering_info atom
    JOIN dkcardx_inventory_main_info main ON atom.inventory_main_id = main.id
    WHERE atom.spu_code = #{spuCode} and atom.sku_code = #{skuCode} and atom.delete_time is null  and atom.id != #{atomId} and main.device_version = #{deviceVersion}
    </select>

</mapper>