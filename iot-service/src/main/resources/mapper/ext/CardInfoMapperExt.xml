<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.CardInfoMapperExt">

  <select id="listCardInfo" resultType="com.chinamobile.iot.sc.pojo.vo.CardInfoVO">
      select
          cms.id cardRelationId,
          ci.id cardInfoId,
          cms.msisdn,
          ci.open_card_time openCardTime,
          ci.template_id templateId,
          ci.template_name templateName,
          ci.be_id beId,
          ci.region_id regionId,
          cms.card_type cardType,
          ci.cust_name custName,
          ci.cust_code custCode,
          ci.project_id projectId,
          cms.card_status cardStatus
      from
          card_mall_sync cms,
          card_info ci
      where
        cms.card_info_id = ci.id
    <if test="cardInfoParam.openCardStartTime != null and cardInfoParam.openCardStartTime != ''">
        and ci.open_card_time <![CDATA[ >= ]]> #{cardInfoParam.openCardStartTime}
    </if>
    <if test="cardInfoParam.openCardEndTime != null and cardInfoParam.openCardEndTime != ''">
      and ci.open_card_time <![CDATA[ <= ]]> #{cardInfoParam.openCardEndTime}
    </if>
    <if test="cardInfoParam.cardStatus != null and cardInfoParam.cardStatus != ''">
      and cms.card_status = #{cardInfoParam.cardStatus}
    </if>
    <if test="cardInfoParam.beId != null and cardInfoParam.beId != ''">
      and ci.be_id = #{cardInfoParam.beId}
    </if>
    <if test="cardInfoParam.regionId != null and cardInfoParam.regionId != ''">
      and ci.region_id = #{cardInfoParam.regionId}
    </if>
    <if test="cardInfoParam.cardType != null and cardInfoParam.cardType != ''">
      and cms.card_type = #{cardInfoParam.cardType}
    </if>
      <if test="cardInfoParam.msisdn != null and cardInfoParam.msisdn != ''">
          and cms.msisdn = #{cardInfoParam.msisdn}
      </if>
      <if test="cardInfoParam.projectId != null and cardInfoParam.projectId != ''">
          and ci.project_id like '%${cardInfoParam.projectId}%'
      </if>
      <if test="cardInfoParam.iccid != null and cardInfoParam.iccid != ''">
          and cms.iccid like '%${cardInfoParam.iccid}%'
      </if>

      <if test="cardInfoParam.pageTemplateName != null and cardInfoParam.pageTemplateName != ''">
          and ci.template_name like '%${cardInfoParam.pageTemplateName}%'
      </if>

      <if test="cardInfoParam.templateName != null and cardInfoParam.templateName != ''">
          and ci.template_name = #{cardInfoParam.templateName}
      </if>
      <if test="cardInfoParam.cardStatusList != null and cardInfoParam.cardStatusList.size() !=0 ">
          and cms.card_status in
          <foreach collection="cardInfoParam.cardStatusList" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="cardInfoParam.beIdList != null and cardInfoParam.beIdList.size() != 0">
          and ci.be_id in
          <foreach collection="cardInfoParam.beIdList" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="cardInfoParam.cardTypeList != null and cardInfoParam.cardTypeList.size() != 0">
          and cms.card_type in
          <foreach collection="cardInfoParam.cardTypeList" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="cardInfoParam.custCode != null and cardInfoParam.custCode != ''">
          and ci.cust_code = #{cardInfoParam.custCode}
      </if>
      <if test="cardInfoParam.templateId != null and cardInfoParam.templateId != ''">
          and ci.template_id = #{cardInfoParam.templateId}
      </if>
    order by ci.open_card_time asc
  </select>

  <select id="countCardInfoCheckImport" resultType="java.lang.Long">
      select
      count(*)
      from
      card_mall_sync cms,
      card_info ci
      where
      cms.card_info_id = ci.id
      <if test="cardInfoParam.openCardStartTime != null and cardInfoParam.openCardStartTime != ''">
          and ci.open_card_time <![CDATA[ >= ]]> #{cardInfoParam.openCardStartTime}
      </if>
      <if test="cardInfoParam.openCardEndTime != null and cardInfoParam.openCardEndTime != ''">
          and ci.open_card_time <![CDATA[ <= ]]> #{cardInfoParam.openCardEndTime}
      </if>
      <if test="cardInfoParam.cardStatus != null and cardInfoParam.cardStatus != ''">
          and cms.card_status = #{cardInfoParam.cardStatus}
      </if>
      <if test="cardInfoParam.beId != null and cardInfoParam.beId != ''">
          and ci.be_id = #{cardInfoParam.beId}
      </if>
      <if test="cardInfoParam.regionId != null and cardInfoParam.regionId != ''">
          and ci.region_id = #{cardInfoParam.regionId}
      </if>
      <if test="cardInfoParam.cardType != null and cardInfoParam.cardType != ''">
          and cms.card_type = #{cardInfoParam.cardType}
      </if>
      <if test="cardInfoParam.msisdn != null and cardInfoParam.msisdn != ''">
          and cms.msisdn = #{cardInfoParam.msisdn}
      </if>
      <if test="cardInfoParam.projectId != null and cardInfoParam.projectId != ''">
          and ci.project_id = #{cardInfoParam.projectId}
      </if>
      <if test="cardInfoParam.iccid != null and cardInfoParam.iccid != ''">
          and cms.iccid = #{cardInfoParam.iccid}
      </if>

      <if test="cardInfoParam.pageTemplateName != null and cardInfoParam.pageTemplateName != ''">
          and ci.template_name = #{cardInfoParam.pageTemplateName}
      </if>

      <if test="cardInfoParam.templateName != null and cardInfoParam.templateName != ''">
          and ci.template_name = #{cardInfoParam.templateName}
      </if>
      <if test="cardInfoParam.cardStatusList != null and cardInfoParam.cardStatusList.size() !=0 ">
          and cms.card_status in
          <foreach collection="cardInfoParam.cardStatusList" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="cardInfoParam.beIdList != null and cardInfoParam.beIdList.size() != 0">
          and ci.be_id in
          <foreach collection="cardInfoParam.beIdList" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="cardInfoParam.cardTypeList != null and cardInfoParam.cardTypeList.size() != 0">
          and cms.card_type in
          <foreach collection="cardInfoParam.cardTypeList" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      <if test="cardInfoParam.custCode != null and cardInfoParam.custCode != ''">
          and ci.cust_code = #{cardInfoParam.custCode}
      </if>
      <if test="cardInfoParam.templateId != null and cardInfoParam.templateId != ''">
          and ci.template_id = #{cardInfoParam.templateId}
      </if>
  </select>


    <select id="listCardInfoCheckImport" resultType="com.chinamobile.iot.sc.pojo.vo.CardInfoVO">
        select
        cms.id cardRelationId,
        ci.id cardInfoId,
        cms.msisdn,
        ci.open_card_time openCardTime,
        ci.template_id templateId,
        ci.template_name templateName,
        ci.be_id beId,
        ci.region_id regionId,
        cms.card_type cardType,
        ci.cust_name custName,
        ci.cust_code custCode,
        ci.project_id projectId,
        cms.card_status cardStatus
        from
        card_mall_sync cms,
        card_info ci
        where
        cms.card_info_id = ci.id
        <if test="cardInfoParam.openCardStartTime != null and cardInfoParam.openCardStartTime != ''">
            and ci.open_card_time <![CDATA[ >= ]]> #{cardInfoParam.openCardStartTime}
        </if>
        <if test="cardInfoParam.openCardEndTime != null and cardInfoParam.openCardEndTime != ''">
            and ci.open_card_time <![CDATA[ <= ]]> #{cardInfoParam.openCardEndTime}
        </if>
        <if test="cardInfoParam.cardStatus != null and cardInfoParam.cardStatus != ''">
            and cms.card_status = #{cardInfoParam.cardStatus}
        </if>
        <if test="cardInfoParam.beId != null and cardInfoParam.beId != ''">
            and ci.be_id = #{cardInfoParam.beId}
        </if>
        <if test="cardInfoParam.regionId != null and cardInfoParam.regionId != ''">
            and ci.region_id = #{cardInfoParam.regionId}
        </if>
        <if test="cardInfoParam.cardType != null and cardInfoParam.cardType != ''">
            and cms.card_type = #{cardInfoParam.cardType}
        </if>
        <if test="cardInfoParam.msisdn != null and cardInfoParam.msisdn != ''">
            and cms.msisdn = #{cardInfoParam.msisdn}
        </if>
        <if test="cardInfoParam.projectId != null and cardInfoParam.projectId != ''">
            and ci.project_id = #{cardInfoParam.projectId}
        </if>
        <if test="cardInfoParam.iccid != null and cardInfoParam.iccid != ''">
            and cms.iccid = #{cardInfoParam.iccid}
        </if>

        <if test="cardInfoParam.pageTemplateName != null and cardInfoParam.pageTemplateName != ''">
            and ci.template_name = #{cardInfoParam.pageTemplateName}
        </if>

        <if test="cardInfoParam.templateName != null and cardInfoParam.templateName != ''">
            and ci.template_name = #{cardInfoParam.templateName}
        </if>
        <if test="cardInfoParam.cardStatusList != null and cardInfoParam.cardStatusList.size() !=0 ">
            and cms.card_status in
            <foreach collection="cardInfoParam.cardStatusList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cardInfoParam.beIdList != null and cardInfoParam.beIdList.size() != 0">
            and ci.be_id in
            <foreach collection="cardInfoParam.beIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cardInfoParam.cardTypeList != null and cardInfoParam.cardTypeList.size() != 0">
            and cms.card_type in
            <foreach collection="cardInfoParam.cardTypeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="cardInfoParam.custCode != null and cardInfoParam.custCode != ''">
            and ci.cust_code = #{cardInfoParam.custCode}
        </if>
        <if test="cardInfoParam.templateId != null and cardInfoParam.templateId != ''">
            and ci.template_id = #{cardInfoParam.templateId}
        </if>
        order by ci.open_card_time asc
    </select>
</mapper>