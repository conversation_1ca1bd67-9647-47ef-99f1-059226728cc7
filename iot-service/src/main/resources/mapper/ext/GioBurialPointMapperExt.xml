<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.GioBurialPointMapperExt">
  <select id="getUserList"  resultType="com.chinamobile.iot.sc.pojo.dto.gio.GioUserOriginDTO">
      SELECT ump.user_id      AS userId,
             ump.role_type    AS roleType,
             ump.code,
             ump.phone,
             ump.name,
             ump.create_time      AS createTime,
             status,
             ump.province_name    AS provinceName,
             ump.city_name        AS cityName,
             ump.region_name      AS regionName,
             ump.be_id as beId,
             ump.location,
             ump.region_id        AS regionId,
             case
                 when sci.role_type = '4' then smi.city_name
                 when sci.role_type != '4' then sci.city_name
                 else ''
                 end shopCityName,
             case
                 when sci.role_type = '4' then smi.region_name
                 when sci.role_type != '4' then sci.region_name
                 else ''
                 end shopRegionName,
             case
                 when sci.role_type = '4' then smi.gridding_name
                 when sci.role_type != '4' then sci.gridding_name
                 else ''
                 end griddingName
      from user_mini_program ump
               left join shop_customer_info sci on ump.phone = sci.cust_id and ump.role_type != '4'
         left join shop_manager_info smi on smi.create_oper_phone = ump.phone and sci.role_type = '4'
      where ump.user_id is not null
      GROUP BY ump.id;

  </select>
    <resultMap id="logistics_map" type="com.chinamobile.iot.sc.pojo.LogisticsInfo">
        <result property="supplierName" column="logisticsName"/>
        <result property="logisCode" column="logisticsCode"/>
    </resultMap>
    <resultMap id="distributorInfo_map" type="com.chinamobile.iot.sc.pojo.Order2cDistributorInfo">
        <result property="distributorUserId" column="distributorUserID"/>
        <result property="distributorPhone" column="distributorPhone"/>
        <result property="distributorShareCode" column="distributorShareCode"/>
        <result property="distributorLevel" column="distributorShareCode"/>
    </resultMap>
    <resultMap id="agentInfo_map" type="com.chinamobile.iot.sc.pojo.Order2cAgentInfo">
        <result property="agentName" column="agentName"/>
        <result property="agentPhone" column="agentPhone"/>
        <result property="agentNumber" column="agentNumber"/>
        <result property="agentUserId" column="agentUserId"/>
        <result property="agentNameWash" column="agentNameWash"/>
        <result property="agentNumberWash" column="agentNumberWash"/>
        <result property="agentLabelWash" column="agentLabelWash"/>
        <result property="agentCategoryWash" column="agentCategoryWash"/>
    </resultMap>

    <resultMap id="couponInfo_map" type="com.chinamobile.iot.sc.request.order2c.CouponInfoDTO">
        <result property="couponCode" column="couponCode"/>
        <result property="couponAmount" column="couponAmount"/>
        <result property="employeeId" column="employeeId"/>
        <result property="salesmanPhone" column="salesmanPhone"/>
        <result property="salesmanCode" column="salesmanCode"/>
        <result property="salesmanName" column="salesmanName"/>
    </resultMap>
    <resultMap id="snInfo_map" type="String">
        <result column="sn" jdbcType="VARCHAR"  />
    </resultMap>
    <resultMap id="atomOrderInfo_map" type="com.chinamobile.iot.sc.pojo.Order2cAtomInfo">
        <result property="skuQuantity" column="skuQuantity"/>
        <result property="atomQuantity" column="atomQuantity"/>
        <result property="atomPrice" column="atomPrice"/>
        <result property="atomOfferingCode" column="atomOfferingCode"/>
    </resultMap>

    <resultMap id="prudctOrderPndingOrigin_map" type="com.chinamobile.iot.sc.pojo.dto.gio.GioPrudctOrderPndingOriginDTO">
        <result property="orderId" column="orderId"/>
        <result property="custMgPhone" column="custMgPhone"/>
        <result property="provinceOrgName" column="provinceOrgName"/>
        <result property="orderStatus" column="orderStatus"/>
        <result property="spuOfferingClass" column="spuOfferingClass"/>
        <result property="saleOrderType" column="saleOrderType"/>
        <result property="orderStatusTime" column="orderStatusTime"/>
        <result property="createTime" column="createTime"/>
        <result property="billNoTime" column="billNoTime"/>
        <result property="sendGoodsTime" column="sendGoodsTime"/>
        <result property="receiveOrderTime" column="receiveOrderTime"/>
        <result property="valetOrderCompleteTime" column="valetOrderCompleteTime"/>
        <result property="atomQuantity" column="atomQuantity"/>
        <result property="quantity" column="quantity"/>
        <result property="orderType" column="orderType"/>
        <result property="createOperUserId" column="createOperUserId"/>
        <result property="createOperCode" column="createOperCode"/>
        <result property="custName" column="custName"/>
        <result property="atomPrice" column="atomPrice"/>
        <result property="orderingChannelName" column="orderingChannelName"/>
        <result property="addr1" column="addr1"/>
        <result property="addr2" column="addr2"/>
        <result property="addr3" column="addr3"/>
        <result property="custCode" column="custCode"/>
        <result property="totalPrice" column="totalPrice"/>
        <result property="businessCode" column="businessCode"/>
        <result property="beId" column="beId"/>
        <result property="location" column="location"/>
        <result property="regionID" column="regionID"/>
        <result property="skuCode" column="skuCode"/>
        <result property="spuVersion" column="spuVersion"/>
        <result property="skuVersion" column="skuVersion"/>
        <result property="offeringVersion" column="offeringVersion"/>
        <result property="offeringId" column="offeringId"/>
        <result property="skuPrice" column="skuPrice"/>
        <result property="atomOfferingQuantity" column="atomOfferingQuantity"/>
        <result property="atomSettlePrice" column="atomSettlePrice"/>
        <result property="atomSalePrice" column="atomSalePrice"/>
        <result property="deductPrice" column="deductPrice"/>
        <result property="chargeCode" column="chargeCode"/>
        <result property="chargeId" column="chargeId"/>
        <result property="offeringCode" column="offeringCode"/>
        <result property="atomOfferingClass" column="atomOfferingClass"/>

        <collection property="agentInfoList" ofType="com.chinamobile.iot.sc.pojo.Order2cAgentInfo" resultMap="agentInfo_map"/>
        <collection property="couponInfoList" ofType="com.chinamobile.iot.sc.request.order2c.CouponInfoDTO">
            <result property="employeeId" column="employeeId"/>
            <result property="salesmanPhone" column="salesmanPhone"/>
            <result property="couponCode" column="couponCode"/>
            <result property="couponAmount" column="couponAmount"/>
        </collection>
    </resultMap>

    <resultMap id="order_map" type="com.chinamobile.iot.sc.pojo.dto.gio.GioOrderOriginDTO">
        <id property="orderId" column="orderId"/>
        <result property="custMgPhone" column="custMgPhone"/>
        <result property="provinceOrgName" column="provinceOrgName"/>
        <result property="orderStatus" column="orderStatus"/>
        <result property="orderStatusTime" column="orderStatusTime"/>
        <result property="createTime" column="createTime"/>
        <result property="sendGoodsTime" column="sendGoodsTime"/>
        <result property="receiveOrderTime" column="receiveOrderTime"/>
        <result property="valetOrderCompleteTime" column="valetOrderCompleteTime"/>
        <result property="arrivalTime" column="arrivalTime"/>
        <result property="createOperUserId" column="createOperUserId"/>
        <result property="createOperCode" column="createOperCode"/>
        <result property="custName" column="custName"/>
        <result property="orderingChannelName" column="orderingChannelName"/>
        <result property="addr1" column="addr1"/>
        <result property="addr2" column="addr2"/>
        <result property="addr3" column="addr3"/>
        <result property="orderGrid" column="orderGrid"/>
        <result property="custCode" column="custCode"/>
        <result property="billNoTime" column="billNoTime"/>
        <result property="saleOrderType" column="saleOrderType"/>
        <result property="businessCode" column="businessCode"/>
        <result property="spuOfferingClass" column="spuOfferingClass"/>
        <!-- 网格信息字段 -->
        <result property="gridProvince" column="gridProvince"/>
        <result property="gridCity" column="gridCity"/>
        <result property="gridDistrict" column="gridDistrict"/>
        <result property="gridName" column="gridName"/>
        <collection property="order2cAtomSnList" ofType="java.lang.String" resultMap="snInfo_map"/>
        <collection property="couponInfoList" ofType="com.chinamobile.iot.sc.request.order2c.CouponInfoDTO" resultMap="couponInfo_map"/>
        <collection property="order2cAgentInfoList" ofType="com.chinamobile.iot.sc.pojo.Order2cAgentInfo" resultMap="agentInfo_map"/>
        <collection property="logisticsInfoList" ofType="com.chinamobile.iot.sc.pojo.dto.gio.GioUserOriginDTO" resultMap="logistics_map"/>
        <collection property="order2cDistributorInfoList" ofType="com.chinamobile.iot.sc.pojo.Order2cDistributorInfo" resultMap="distributorInfo_map"/>
    </resultMap>
    <resultMap id="order_orderPndingInvoice_map" type="com.chinamobile.iot.sc.pojo.dto.gio.GioOrderPndingInvoiceOriginDTO">
        <id property="orderId" column="orderId"/>
        <result property="custMgPhone" column="custMgPhone"/>
        <result property="provinceOrgName" column="provinceOrgName"/>
        <result property="orderStatus" column="orderStatus"/>
        <result property="orderStatusTime" column="orderStatusTime"/>
        <result property="createTime" column="createTime"/>
        <result property="sendGoodsTime" column="sendGoodsTime"/>
        <result property="receiveOrderTime" column="receiveOrderTime"/>
        <result property="valetOrderCompleteTime" column="valetOrderCompleteTime"/>
        <result property="arrivalTime" column="arrivalTime"/>
        <result property="createOperUserId" column="createOperUserId"/>
        <result property="createOperCode" column="createOperCode"/>
        <result property="custName" column="custName"/>
        <result property="orderingChannelName" column="orderingChannelName"/>
        <result property="addr1" column="addr1"/>
        <result property="addr2" column="addr2"/>
        <result property="addr3" column="addr3"/>
        <result property="orderGrid" column="orderGrid"/>
        <result property="custCode" column="custCode"/>
        <result property="billNoTime" column="billNoTime"/>
        <result property="saleOrderType" column="saleOrderType"/>

        <result property="spuOfferingClass" column="spuOfferingClass"/>
        <collection property="order2cAtomInfoList" ofType="com.chinamobile.iot.sc.pojo.Order2cAtomInfo" resultMap="atomOrderInfo_map"/>
        <collection property="couponInfoList" ofType="com.chinamobile.iot.sc.request.order2c.CouponInfoDTO" resultMap="couponInfo_map"/>

    </resultMap>
    <resultMap id="order_GioOrderH5OrderCreatedHD_map" type="com.chinamobile.iot.sc.pojo.dto.gio.GioOrderH5OrderCreatedHDOriginDTO">
        <id property="orderId" column="orderId"/>
        <result property="custMgPhone" column="custMgPhone"/>
        <result property="provinceOrgName" column="provinceOrgName"/>
        <result property="orderStatus" column="orderStatus"/>
        <result property="orderStatusTime" column="orderStatusTime"/>
        <result property="createTime" column="createTime"/>
        <result property="sendGoodsTime" column="sendGoodsTime"/>
        <result property="receiveOrderTime" column="receiveOrderTime"/>
        <result property="valetOrderCompleteTime" column="valetOrderCompleteTime"/>
        <result property="arrivalTime" column="arrivalTime"/>
        <result property="createOperUserId" column="createOperUserId"/>
        <result property="createOperCode" column="createOperCode"/>
        <result property="custName" column="custName"/>
        <result property="orderingChannelName" column="orderingChannelName"/>
        <result property="addr1" column="addr1"/>
        <result property="addr2" column="addr2"/>
        <result property="addr3" column="addr3"/>
        <result property="orderGrid" column="orderGrid"/>
        <result property="custCode" column="custCode"/>
        <result property="billNoTime" column="billNoTime"/>
        <result property="saleOrderType" column="saleOrderType"/>
        <result property="totalPrice" column="totalPrice"/>
        <result property="spuOfferingClass" column="spuOfferingClass"/>
        <result property="expensesPrice" column="expensesPrice"/>
        <result property="orderQuantity" column="orderQuantity"/>
        <result property="expensesTerm" column="expensesTerm"/>
        <result property="businessCode" column="businessCode"/>
        <result property="beId" column="beId"/>
        <result property="location" column="location"/>
        <result property="regionID" column="regionID"/>
        <collection property="order2cAtomInfoList" ofType="com.chinamobile.iot.sc.pojo.Order2cAtomInfo" resultMap="atomOrderInfo_map"/>

        <collection property="order2cAtomSnList" ofType="java.lang.String" resultMap="snInfo_map"/>
        <collection property="couponInfoList" ofType="com.chinamobile.iot.sc.request.order2c.CouponInfoDTO" resultMap="couponInfo_map"/>
        <collection property="order2cAgentInfoList" ofType="com.chinamobile.iot.sc.pojo.Order2cAgentInfo" resultMap="agentInfo_map"/>
        <collection property="logisticsInfoList" ofType="com.chinamobile.iot.sc.pojo.dto.gio.GioUserOriginDTO" resultMap="logistics_map"/>
        <collection property="order2cDistributorInfoList" ofType="com.chinamobile.iot.sc.pojo.Order2cDistributorInfo" resultMap="distributorInfo_map"/>
    </resultMap>
    <resultMap id="order_orderCreatedHD_map" type="com.chinamobile.iot.sc.pojo.dto.gio.GioOrderCreatedOriginDTO">
        <id property="orderId" column="orderId"/>
        <result property="custMgPhone" column="custMgPhone"/>
        <result property="provinceOrgName" column="provinceOrgName"/>
        <result property="orderStatus" column="orderStatus"/>
        <result property="orderType" column="orderType"/>
        <result property="orderStatusTime" column="orderStatusTime"/>
        <result property="createTime" column="createTime"/>
        <result property="sendGoodsTime" column="sendGoodsTime"/>
        <result property="receiveOrderTime" column="receiveOrderTime"/>
        <result property="valetOrderCompleteTime" column="valetOrderCompleteTime"/>
        <result property="arrivalTime" column="arrivalTime"/>
        <result property="createOperUserId" column="createOperUserId"/>
        <result property="createOperCode" column="createOperCode"/>
        <result property="custName" column="custName"/>
        <result property="atomOfferingClass" column="atomOfferingClass"/>
        <result property="orderingChannelName" column="orderingChannelName"/>
        <result property="addr1" column="addr1"/>
        <result property="addr2" column="addr2"/>
        <result property="addr3" column="addr3"/>
        <result property="orderGrid" column="orderGrid"/>
        <result property="custCode" column="custCode"/>
        <result property="billNoTime" column="billNoTime"/>
        <result property="saleOrderType" column="saleOrderType"/>
        <result property="totalPrice" column="totalPrice"/>
        <result property="spuOfferingClass" column="spuOfferingClass"/>
        <result property="expensesPrice" column="expensesPrice"/>
        <result property="orderQuantity" column="orderQuantity"/>
        <result property="expensesTerm" column="expensesTerm"/>
        <result property="businessCode" column="businessCode"/>
        <result property="beId" column="beId"/>
        <result property="location" column="location"/>
        <result property="regionID" column="regionID"/>
        <result property="deductPrice" column="deductPrice"/>

        <collection property="agentInfoList" ofType="com.chinamobile.iot.sc.pojo.Order2cAgentInfo" resultMap="agentInfo_map"/>
        <collection property="order2cAtomInfoList" ofType="com.chinamobile.iot.sc.pojo.Order2cAtomInfo" resultMap="atomOrderInfo_map"/>
        <collection property="couponInfoList" ofType="com.chinamobile.iot.sc.request.order2c.CouponInfoDTO" resultMap="couponInfo_map"/>

    </resultMap>

    <select id="getOrderList"  resultMap="order_map">
        SELECT oa.id                                                    as                       id,
               oa.order_id                                              AS                       orderId,
               cust_mg_phone                                            AS                       custMgPhone,
               IF((oi.org_name is null or oi.org_name=''), oi.province_org_name, oi.org_name) provinceOrgName,
               oa.order_status                                          AS                       orderStatus,
               oi.order_status_time                                     AS                       orderStatusTime,
               oa.create_time                                           AS                       createTime,
               sendGoodsTime,
               IF(oi.pay_time is null, oi.create_time, DATE_FORMAT(oi.pay_time, '%Y%m%d%H%i%s')) receiveOrderTime,
               oi.valet_order_complete_time                             AS                       valetOrderCompleteTime,
               oi.create_oper_user_id                                   AS                       createOperUserId,
               oi.employee_num                                      AS                       createOperCode,
               oi.cust_name                                             AS                       custName,
               oi.ordering_channel_name                                 AS                       orderingChannelName,
               oi.addr1,
               oi.addr2,
               oi.addr3,
               IF(smi.id is null, sci.gridding_name, smi.gridding_name) AS                       orderGrid,
               oi.cust_code                                             AS                       custCode,
               li.supplier_name                                         as                       logisticsName,
               li.logis_code                                            as                       logisticsCode,
               o2di.distributor_user_id                                 as                       distributorUserId,
               o2di.distributor_phone                                   as                       distributorPhone,
               o2di.distributor_share_code                              as                       distributorShareCode,
               o2di.distributor_level                                   as                       distributorLevel,
               o2ai.agent_name as agentName,
               o2ai.agent_phone as agentPhone,
               o2ai.agent_number as agentNumber,
               o2ai.agent_user_id as  agentUserId,
               o2ai.agent_name_wash as agentNameWash,
               o2ai.agent_number_wash as agentNumberWash,
               o2ai.agent_label_wash as agentLabelWash,
               o2ai.agent_category_wash as agentCategoryWash,
               oa.bill_no_time as billNoTime,
               ci.coupon_code as couponCode,
               ci.coupon_amount as couponAmount,
               ci.salesman_name as salesmanName,
               ci.salesman_code as salesmanCode,
               oi.total_price as totalPrice,
               oi.spu_offering_class as  spuOfferingClass,
               oi.business_code as businessCode,
               (SELECT
                    case
                        when sale_order_type = 0 then '标准产品省框'
                        when sale_order_type = 1 then 'DICT服务包'
                        when sale_order_type = 2 then '统结标准产品'
                        else ''
                        end saleOrderType
                FROM contract WHERE number = kpm.contract_num limit 1
            ) saleOrderType,
               o2as.sn,
               -- 网格信息字段
               roc_grid.province as gridProvince,
               roc_grid.city as gridCity,
               roc_grid.district as gridDistrict,
               roc_grid.grid_name as gridName
        FROM order_2c_atom_info oa force index (idx_create_time)
            LEFT JOIN order_2c_info oi ON oa.order_id = oi.order_id
            LEFT JOIN spu_offering_info_history si ON si.offering_code = oa.spu_offering_code
            AND si.spu_offering_version = oa.spu_offering_version
            LEFT JOIN sku_offering_info_history sku ON sku.offering_code = oa.sku_offering_code
            AND sku.spu_code = oa.spu_offering_code
            AND sku.spu_offering_version = oa.spu_offering_version
            AND sku.sku_offering_version = oa.sku_offering_version
            LEFT JOIN province p ON oi.be_id = p.province_code
            LEFT JOIN atom_offering_info atom ON atom.offering_code = oa.atom_offering_code AND atom.spu_code =
            oa.spu_offering_code AND
            atom.sku_code = oa.sku_offering_code
            LEFT JOIN atom_std_service ass ON ass.atom_id = atom.id
            LEFT JOIN standard_service std ON std.id = ass.std_service_id
            LEFT JOIN (select o2ah.atom_order_id,
            o2ah.order_id,
            date_format(max(o2ah.create_time), '%Y-%m-%d %H:%i:%s') sendGoodsTime
            from order_2c_atom_history o2ah,
            order_2c_info oi2
            where o2ah.operate_type = 1
            and o2ah.inner_status = 1
            and oi2.order_id = o2ah.order_id
            and oi2.spu_offering_class not in
            ('A01', 'A02', 'A03', 'A04', 'A08', 'A09', 'A10', 'A12', 'A13', 'A14', 'A15', 'A16', 'A17')
            group by atom_order_id, order_id
            union
            select o2ah.atom_order_id, o2ah.order_id, o2ah.create_time sendGoodsTime
            from order_2c_atom_history o2ah,
            order_2c_info oi2
            where o2ah.operate_type = 1
            and o2ah.inner_status = 61
            and oi2.order_id = o2ah.order_id
            and oi2.spu_offering_class in
            ('A01', 'A02', 'A03', 'A04', 'A08', 'A09', 'A10', 'A12', 'A13', 'A14', 'A15', 'A16',
            'A17')) o2ah on o2ah.atom_order_id = oa.id and o2ah.order_id = oa.order_id
            LEFT JOIN order_2c_agent_info agent ON agent.order_id = oa.order_id
            left join k3_product_material kpm on kpm.id = (select id
            from k3_product_material
            where atom_id = atom.id
            limit 1)
            left join charge_item_config cic on cic.charge_id = atom.charge_id or cic.product_type_id = atom.product_type
            left join shop_manager_info smi on oi.cust_mg_phone is not null and smi.create_oper_phone = oi.cust_mg_phone
            left join order_2c_agent_info o2ai on oa.order_id = o2ai.order_id
            left join shop_customer_info sci on oi.cust_mg_phone is null and sci.cust_id = o2ai.agent_phone and sci.agent_number = o2AI.agent_number
            left join logistics_info li on li.order_id = oi.order_id
            left join order_2c_distributor_info o2di on o2di.order_id = oi.order_id
            left join coupon_info ci on ci.order_id=oi.order_id
            left join order_2c_atom_sn o2as on o2as.atom_order_id = oa.id
            -- 添加网格信息关联
            left join rise_order_2c_grid roc_grid on roc_grid.order_id = oi.order_id
            where 1=1
            <if test="startTime != null">
                and oa.create_time <![CDATA[ >= ]]>#{startTime}
            </if>
            <if test="endTime != null">
                and oa.create_time <![CDATA[ < ]]>#{endTime}
            </if>
    </select>
    <select id="getOrderGioOrderH5OrderCreatedHDList"  resultMap="order_GioOrderH5OrderCreatedHD_map">
        SELECT oa.id                                                    as                       id,
               oa.order_id                                              AS                       orderId,
               cust_mg_phone                                            AS                       custMgPhone,
               IF((oi.org_name is null or oi.org_name=''), oi.province_org_name, oi.org_name) provinceOrgName,
               oa.order_status                                          AS                       orderStatus,
               oi.order_status_time                                     AS                       orderStatusTime,
               oa.create_time                                           AS                       createTime,
               sendGoodsTime,
               IF(oi.pay_time is null, oi.create_time, DATE_FORMAT(oi.pay_time, '%Y%m%d%H%i%s')) receiveOrderTime,
               oi.valet_order_complete_time                             AS                       valetOrderCompleteTime,
               oi.create_oper_user_id                                   AS                       createOperUserId,
               oi.employee_num                                      AS                       createOperCode,
               oi.cust_name                                             AS                       custName,
               oi.ordering_channel_name                                 AS                       orderingChannelName,
               oi.addr1,
               oi.addr2,
               oi.addr3,
               IF(smi.id is null, sci.gridding_name, smi.gridding_name) AS                       orderGrid,
               oi.cust_code                                             AS                       custCode,
               li.supplier_name                                         as                       logisticsName,
               li.logis_code                                            as                       logisticsCode,
               o2di.distributor_user_id                                 as                       distributorUserId,
               o2di.distributor_phone                                   as                       distributorPhone,
               o2di.distributor_share_code                              as                       distributorShareCode,
               o2di.distributor_level                                   as                       distributorLevel,
               o2ai.agent_name as agentName,
               o2ai.agent_phone as agentPhone,
               o2ai.agent_number as agentNumber,
               o2ai.agent_user_id as  agentUserId,
               oa.bill_no_time as billNoTime,
               ci.coupon_code as couponCode,
               ci.coupon_amount as couponAmount,
               oi.total_price as totalPrice,
               oa.sku_quantity as skuQuantity,
               oa.atom_quantity as atomQuantity,
               oa.atom_price as atomPrice,
               oi.spu_offering_class as  spuOfferingClass,
               oi.be_id as beId,
               oi.location,
               oi.region_ID as regionID,
               oi.business_code as businessCode,
               (SELECT
                    case
                        when sale_order_type = 0 then '标准产品省框'
                        when sale_order_type = 1 then 'DICT服务包'
                        when sale_order_type = 2 then '统结标准产品'
                        else ''
                        end saleOrderType
                FROM contract WHERE number = kpm.contract_num limit 1
            ) saleOrderType,
               o2as.sn
        FROM order_2c_atom_info oa force index (idx_create_time)
            LEFT JOIN order_2c_info oi ON oa.order_id = oi.order_id
            LEFT JOIN spu_offering_info_history si ON si.offering_code = oa.spu_offering_code
            AND si.spu_offering_version = oa.spu_offering_version
            LEFT JOIN sku_offering_info_history sku ON sku.offering_code = oa.sku_offering_code
            AND sku.spu_code = oa.spu_offering_code
            AND sku.spu_offering_version = oa.spu_offering_version
            AND sku.sku_offering_version = oa.sku_offering_version
            LEFT JOIN province p ON oi.be_id = p.province_code
            LEFT JOIN atom_offering_info atom ON atom.offering_code = oa.atom_offering_code AND atom.spu_code =
            oa.spu_offering_code AND
            atom.sku_code = oa.sku_offering_code
            LEFT JOIN atom_std_service ass ON ass.atom_id = atom.id
            LEFT JOIN standard_service std ON std.id = ass.std_service_id
            LEFT JOIN (select o2ah.atom_order_id,
            o2ah.order_id,
            date_format(max(o2ah.create_time), '%Y-%m-%d %H:%i:%s') sendGoodsTime
            from order_2c_atom_history o2ah,
            order_2c_info oi2
            where o2ah.operate_type = 1
            and o2ah.inner_status = 1
            and oi2.order_id = o2ah.order_id
            and oi2.spu_offering_class not in
            ('A01', 'A02', 'A03', 'A04', 'A08', 'A09', 'A10', 'A12', 'A13', 'A14', 'A15', 'A16', 'A17')
            group by atom_order_id, order_id
            union
            select o2ah.atom_order_id, o2ah.order_id, o2ah.create_time sendGoodsTime
            from order_2c_atom_history o2ah,
            order_2c_info oi2
            where o2ah.operate_type = 1
            and o2ah.inner_status = 61
            and oi2.order_id = o2ah.order_id
            and oi2.spu_offering_class in
            ('A01', 'A02', 'A03', 'A04', 'A08', 'A09', 'A10', 'A12', 'A13', 'A14', 'A15', 'A16',
            'A17')) o2ah on o2ah.atom_order_id = oa.id and o2ah.order_id = oa.order_id
            LEFT JOIN order_2c_agent_info agent ON agent.order_id = oa.order_id
            left join k3_product_material kpm on kpm.id = (select id
            from k3_product_material
            where atom_id = atom.id
            limit 1)
            left join charge_item_config cic on cic.charge_id = atom.charge_id or cic.product_type_id = atom.product_type
            left join shop_manager_info smi on oi.cust_mg_phone is not null and smi.create_oper_phone = oi.cust_mg_phone
            left join order_2c_agent_info o2ai on oa.order_id = o2ai.order_id
            left join shop_customer_info sci on oi.cust_mg_phone is null and sci.cust_id = o2ai.agent_phone and sci.agent_number = o2AI.agent_number
            left join logistics_info li on li.order_id = oi.order_id
            left join order_2c_distributor_info o2di on o2di.order_id = oi.order_id
            left join coupon_info ci on ci.order_id=oi.order_id
            left join order_2c_atom_sn o2as on o2as.atom_order_id = oa.id
    </select>
    <!-- 优化后的查询：拆分复杂子查询，减少JOIN，添加索引提示 -->
    <select id="getPrudctOrderPndingOriginList"  resultMap="prudctOrderPndingOrigin_map">
        SELECT DISTINCT
        oa.id                                                    as                       id,
        oa.order_id                                              AS                       orderId,
        oi.cust_mg_phone                                         AS                       custMgPhone,
        COALESCE(NULLIF(oi.org_name, ''), oi.province_org_name)  AS                       provinceOrgName,
        oa.order_status                                          AS                       orderStatus,
        oi.order_status_time                                     AS                       orderStatusTime,
        oa.create_time                                           AS                       createTime,
        o2ah_temp.sendGoodsTime,
        COALESCE(DATE_FORMAT(oi.pay_time, '%Y%m%d%H%i%s'), DATE_FORMAT(oi.create_time, '%Y%m%d%H%i%s')) AS receiveOrderTime,
        oi.valet_order_complete_time                             AS                       valetOrderCompleteTime,
        oi.create_oper_user_id                                   AS                       createOperUserId,
        oi.employee_num                                          AS                       createOperCode,
        oi.cust_name                                             AS                       custName,
        oi.ordering_channel_name                                 AS                       orderingChannelName,
        oi.addr1,
        oi.addr2,
        oi.addr3,
        oi.cust_code                                             AS                       custCode,
        oa.bill_no_time                                          AS                       billNoTime,
        oi.total_price                                           AS                       totalPrice,
        oa.atom_quantity                                         AS                       atomQuantity,
        oa.atom_price                                            AS                       atomPrice,
        oi.spu_offering_class                                    AS                       spuOfferingClass,
        oi.be_id                                                 AS                       beId,
        oi.location,
        oa.order_type                                            AS                       orderType,
        oi.region_ID                                             AS                       regionID,
        oa.sku_offering_code                                     AS                       skuCode,
        oa.spu_offering_version                                  AS                       spuVersion,
        oa.sku_offering_version                                  AS                       skuVersion,
        oa.atom_offering_version                                 AS                       offeringVersion,
        atom.id                                                  AS                       offeringId,
        oa.sku_price                                             AS                       skuPrice,
        oa.sku_quantity                                          AS                       quantity,
        atom.quantity                                            AS                       atomOfferingQuantity,
        oa.atom_settle_price                                     AS                       atomSettlePrice,
        oa.atom_price                                            AS                       atomSalePrice,
        oi.business_code                                         AS                       businessCode,
        oa.valet_order_complete_time                             AS                       valetOrderCompleteTime,
        agent.agent_phone                                        AS                       agentPhone,
        agent.agent_number                                       AS                       agentNumber,
        oi.deduct_price                                          AS                       deductPrice,
        ci.employee_id                                           AS                       employeeId,
        ci.salesman_phone                                        AS                       salesmanPhone,
        ci.coupon_code as couponCode,
        ci.coupon_amount as couponAmount,
        atom.charge_code                                         AS                       chargeCode,
        atom.charge_id                                           AS                       chargeId,
        atom.offering_code                                       AS                       offeringCode,
        oa.atom_offering_class                                   AS                       atomOfferingClass,
        contract_temp.saleOrderType
        FROM order_2c_atom_info oa FORCE INDEX (idx_create_time)
        INNER JOIN order_2c_info oi ON oa.order_id = oi.order_id
        INNER JOIN atom_offering_info atom ON atom.offering_code = oa.atom_offering_code
            AND atom.spu_code = oa.spu_offering_code
            AND atom.sku_code = oa.sku_offering_code
        LEFT JOIN order_2c_agent_info agent ON agent.order_id = oa.order_id
        LEFT JOIN coupon_info ci ON ci.order_id = oa.order_id
        -- 优化：将复杂的UNION子查询提取为临时表
        LEFT JOIN (
            SELECT
                atom_order_id,
                order_id,
                MAX(sendGoodsTime) as sendGoodsTime
            FROM (
                SELECT
                    o2ah.atom_order_id,
                    o2ah.order_id,
                    DATE_FORMAT(o2ah.create_time, '%Y-%m-%d %H:%i:%s') as sendGoodsTime
                FROM order_2c_atom_history o2ah
                WHERE o2ah.operate_type = 1
                    AND ((o2ah.inner_status = 1) OR (o2ah.inner_status = 61))
            ) temp_history
            GROUP BY atom_order_id, order_id
        ) o2ah_temp ON o2ah_temp.atom_order_id = oa.id AND o2ah_temp.order_id = oa.order_id
        -- 优化：将子查询提取为LEFT JOIN
        LEFT JOIN (
            SELECT
                kpm.atom_id,
                CASE
                    WHEN c.sale_order_type = 0 THEN '标准产品省框'
                    WHEN c.sale_order_type = 1 THEN 'DICT服务包'
                    WHEN c.sale_order_type = 2 THEN '统结标准产品'
                    ELSE ''
                END as saleOrderType
            FROM k3_product_material kpm
            INNER JOIN contract c ON c.number = kpm.contract_num
        ) contract_temp ON contract_temp.atom_id = atom.id
        WHERE oi.spu_offering_class IS NOT NULL
        <if test="isReceiveGuests != null and isReceiveGuests==1 ">
            AND oa.valet_order_complete_time IS NOT NULL
            AND oa.order_type IN ('00','02','03')
        </if>
        <if test="startTime != null">
            AND oa.create_time <![CDATA[ >= ]]>#{startTime}
        </if>
        <if test="endTime != null">
            AND oa.create_time <![CDATA[ < ]]>#{endTime}
        </if>
    </select>
    <select id="getGioOrderCreatedOriginDTOList"  resultMap="order_orderCreatedHD_map">
        SELECT oa.id                                                    as                       id,
        oa.order_id                                              AS                       orderId,
        cust_mg_phone                                            AS                       custMgPhone,
        IF((oi.org_name is null or oi.org_name=''), oi.province_org_name, oi.org_name) provinceOrgName,
        oa.order_status                                          AS                       orderStatus,
        oi.order_status_time                                     AS                       orderStatusTime,
        oa.create_time                                           AS                       createTime,
        sendGoodsTime,
        IF(oi.pay_time is null, oi.create_time, DATE_FORMAT(oi.pay_time, '%Y%m%d%H%i%s')) receiveOrderTime,
        oi.valet_order_complete_time                             AS                       valetOrderCompleteTime,
        oi.create_oper_user_id                                   AS                       createOperUserId,
        oi.employee_num                                      AS                       createOperCode,
        oi.cust_name                                             AS                       custName,
        oi.ordering_channel_name                                 AS                       orderingChannelName,
        oi.addr1,
        oi.addr2,
        oi.addr3,
        oi.cust_code                                             AS                       custCode,
        oa.bill_no_time as billNoTime,
        oi.total_price as totalPrice,
        oa.sku_quantity as skuQuantity,
        oa.atom_quantity as atomQuantity,
        oa.order_type as orderType,
        oa.atom_price as atomPrice,
        oa.atom_offering_code as atomOfferingCode,
        oi.spu_offering_class as  spuOfferingClass,
        oi.be_id as beId,
        oi.location,
        oi.region_ID as regionID,
        oa.sku_offering_code as skuCode,
        oa.spu_offering_version as spuOfferingVersion,
        oa.sku_offering_version as skuOfferingVersion,
        oa.atom_offering_version as offeringVersion,
        atom.id as offeringId,
        sku.price as skuPrice,
        atom.quantity as atomOfferingQuantity ,
        atom.settle_price as atomSettlePrice,
        atom.offering_class as atomOfferingClass,
        ci.coupon_code as couponCode,
        ci.coupon_amount as couponAmount,
        ci.employee_id as employeeId,
        ci.salesman_phone as salesmanPhone,
        atom.atom_sale_price as atomSalePrice,
        cvai.expenses_price as expensesPrice,
        cvai.order_quantity as orderQuantity,
        cvai.expenses_term as expensesTerm,
        oi.business_code as businessCode,
        agent.agent_phone as agentPhone,
        agent.agent_number as agentNumber,
        oi.deduct_price as deductPrice,
        (SELECT
        case
        when sale_order_type = 0 then '标准产品省框'
        when sale_order_type = 1 then 'DICT服务包'
        when sale_order_type = 2 then '统结标准产品'
        else ''
        end saleOrderType
        FROM contract WHERE number = kpm.contract_num limit 1
        ) saleOrderType

        FROM order_2c_atom_info oa force index (idx_create_time)

        LEFT JOIN order_2c_info oi ON oa.order_id = oi.order_id
        LEFT JOIN spu_offering_info_history si ON si.offering_code = oa.spu_offering_code
        AND si.spu_offering_version = oa.spu_offering_version
        LEFT JOIN sku_offering_info_history sku ON sku.offering_code = oa.sku_offering_code
        AND sku.spu_code = oa.spu_offering_code
        AND sku.spu_offering_version = oa.spu_offering_version
        AND sku.sku_offering_version = oa.sku_offering_version
        LEFT JOIN province p ON oi.be_id = p.province_code
        JOIN atom_offering_info atom ON atom.offering_code = oa.atom_offering_code AND atom.spu_code =
        oa.spu_offering_code AND
        atom.sku_code = oa.sku_offering_code
        LEFT JOIN atom_std_service ass ON ass.atom_id = atom.id
        LEFT JOIN standard_service std ON std.id = ass.std_service_id
        LEFT JOIN (select o2ah.atom_order_id,
        o2ah.order_id,
        date_format(max(o2ah.create_time), '%Y-%m-%d %H:%i:%s') sendGoodsTime
        from order_2c_atom_history o2ah,
        order_2c_info oi2
        where o2ah.operate_type = 1
        and o2ah.inner_status = 1
        and oi2.order_id = o2ah.order_id
        and oi2.spu_offering_class not in
        ('A01', 'A02', 'A03', 'A04', 'A08', 'A09', 'A10', 'A12', 'A13', 'A14', 'A15', 'A16', 'A17')
        group by atom_order_id, order_id
        union
        select o2ah.atom_order_id, o2ah.order_id, o2ah.create_time sendGoodsTime
        from order_2c_atom_history o2ah,
        order_2c_info oi2
        where o2ah.operate_type = 1
        and o2ah.inner_status = 61
        and oi2.order_id = o2ah.order_id
        and oi2.spu_offering_class in
        ('A01', 'A02', 'A03', 'A04', 'A08', 'A09', 'A10', 'A12', 'A13', 'A14', 'A15', 'A16',
        'A17')) o2ah on o2ah.atom_order_id = oa.id and o2ah.order_id = oa.order_id
        LEFT JOIN order_2c_agent_info agent ON agent.order_id = oa.order_id
        left join k3_product_material kpm on kpm.id = (select id
        from k3_product_material
        where atom_id = atom.id
        limit 1)
        left join card_value_added_info cvai on cvai.order_id = oa.order_id
        left join coupon_info ci on ci.order_id=oi.order_id
            where 1=1
        <if test="isReceiveGuests != null and isReceiveGuests==1 ">
            and oa.valet_order_complete_time is not null and oa.order_type in ('00','02','03')
        </if>
        <if test="startTime != null">
            and oa.create_time <![CDATA[ >= ]]>#{startTime}
        </if>
        <if test="endTime != null">
        and oa.create_time <![CDATA[ < ]]>#{endTime}
        </if>




    </select>

    <resultMap id="sku_release_map" type="com.chinamobile.iot.sc.pojo.SkuReleaseTarget">
        <result property="provinceCode" column="provinceCode"/>
        <result property="cityCode" column="cityCode"/>
    </resultMap>
    <resultMap id="sku_map" type="com.chinamobile.iot.sc.pojo.dto.gio.GioSkuOriginDTO">
        <id property="id" column="id"/>
        <result property="offeringCode" column="offeringCode"/>
        <result property="spuId" column="spuId"/>
        <result property="offeringName" column="offeringName"/>
        <result property="price" column="price"/>
        <result property="offeringStatus" column="offeringStatus"/>
<!--        <result property="spuOfferingVersion" column="spuOfferingVersion"/>-->
        <result property="spuOfferingName" column="spuOfferingName"/>
        <result property="spuOfferingCode" column="spuOfferingCode"/>
        <result property="spuCreateTime" column="spuCreateTime"/>
        <result property="spuOfferingStatus" column="spuOfferingStatus"/>
        <result property="spuOfferingClass" column="spuOfferingClass"/>
        <result property="supplierName" column="supplierName"/>
        <result property="pointPercent" column="pointPercent"/>
        <result property="deleteTime" column="deleteTime"/>
        <!-- 新增字段映射 -->
        <result property="firstLevelNavCatalog" column="firstLevelNavCatalog"/>
        <result property="secondLevelNavCatalog" column="secondLevelNavCatalog"/>
        <result property="thirdLevelNavCatalog" column="thirdLevelNavCatalog"/>
        <result property="productKeyword" column="productKeyword"/>
        <result property="mainSaleTag" column="mainSaleTag"/>
        <result property="subSaleTag" column="subSaleTag"/>

        <collection property="skuReleaseTargetList" ofType="com.chinamobile.iot.sc.pojo.SkuReleaseTarget" resultMap="sku_release_map"/>

    </resultMap>
    <select id="getSkuList"  resultMap="sku_map">
        SELECT sku.id,
               sku.offering_code         as offeringCode,
               sku.spu_id                as spuId,
               sku.offering_name         as offeringName,
               sku.price,
               sku.offering_status       as offeringStatus,
               srt.province_code         as provinceCode,
               srt.city_code             as cityCode,
               spu.offering_name         as spuOfferingName,
               spu.offering_code         as spuOfferingCode,
               spu.create_time           as spuCreateTime,
               spu.offering_status       as spuOfferingStatus,
               ci.offering_class         as spuOfferingClass,
               supplier_name             as supplierName,
               srr.point_percent         as pointPercent,
               sku.delete_time           as deleteTime,
               -- 新增字段：导航目录
               (SELECT GROUP_CONCAT(DISTINCT d.`NAME`) FROM product_navigation_directory d JOIN navigation_info n ON d.id = n.level1_navigation_code WHERE n.spu_offering_code = spu.offering_code ) as firstLevelNavCatalog,
               (SELECT GROUP_CONCAT(DISTINCT d.`NAME`) FROM product_navigation_directory d JOIN navigation_info n ON d.id = n.level2_navigation_code WHERE n.spu_offering_code = spu.offering_code ) as secondLevelNavCatalog,
               (SELECT GROUP_CONCAT(DISTINCT d.`NAME`) FROM product_navigation_directory d JOIN navigation_info n ON d.id = n.level3_navigation_code WHERE n.spu_offering_code = spu.offering_code ) as thirdLevelNavCatalog,
               -- 新增字段：商品关键字
               spu.product_keywords as productKeyword,
               -- 新增字段：销售标签
               (SELECT GROUP_CONCAT(label) FROM spu_sale_label where (type = 0 or type is null) and spu_code = spu.offering_code) as mainSaleTag,
               (SELECT GROUP_CONCAT(label) FROM spu_sale_label where type = 1 and spu_code = spu.offering_code) as subSaleTag
--                soih.spu_offering_version as spuOfferingVersion
        from sku_offering_info sku
                 left join sku_release_target srt on srt.sku_offering_code = sku.offering_code
                 left join spu_offering_info spu on sku.spu_id = spu.id
                 left join category_info ci on ci.spu_id = spu.id
                 left join sku_role_relation srr on srr.sku_id = sku.id
--                  left join spu_offering_info_history soih
--                            on soih.offering_code = spu.offering_code and soih.id = (select id
--                                                                                     from spu_offering_info_history
--                                                                                     where spu_offering_info_history.offering_code = spu.offering_code
--                                                                                     ORDER BY CAST(SUBSTRING(soih.spu_offering_version, 2) AS UNSIGNED) DESC
--             LIMIT 1
--             );

    </select>

    <select id="getSkuListBySpuCode" resultMap="sku_map">
        SELECT sku.id,
               sku.offering_code         as offeringCode,
               sku.spu_id                as spuId,
               sku.offering_name         as offeringName,
               sku.price,
               sku.offering_status       as offeringStatus,
               srt.province_code         as provinceCode,
               srt.city_code             as cityCode,
               spu.offering_name         as spuOfferingName,
               spu.offering_code         as spuOfferingCode,
               spu.create_time           as spuCreateTime,
               spu.offering_status       as spuOfferingStatus,
               ci.offering_class         as spuOfferingClass,
               supplier_name             as supplierName,
               srr.point_percent         as pointPercent,
               sku.delete_time           as deleteTime,
               -- 新增字段：导航目录
               (SELECT pnd1.name FROM navigation_info ni LEFT JOIN product_navigation_directory pnd1 ON ni.level1_navigation_code = pnd1.id WHERE ni.spu_offering_code = spu.offering_code LIMIT 1) as firstLevelNavCatalog,
               (SELECT pnd2.name FROM navigation_info ni LEFT JOIN product_navigation_directory pnd2 ON ni.level2_navigation_code = pnd2.id WHERE ni.spu_offering_code = spu.offering_code LIMIT 1) as secondLevelNavCatalog,
               (SELECT pnd3.name FROM navigation_info ni LEFT JOIN product_navigation_directory pnd3 ON ni.level3_navigation_code = pnd3.id WHERE ni.spu_offering_code = spu.offering_code LIMIT 1) as thirdLevelNavCatalog,
               -- 新增字段：商品关键字
               spu.product_keywords      as productKeyword,
               -- 新增字段：销售标签
               (SELECT GROUP_CONCAT(label) FROM spu_sale_label where (type = 0 or type is null) and spu_code = spu.offering_code) as mainSaleTag,
               (SELECT GROUP_CONCAT(label) FROM spu_sale_label where type = 1 and spu_code = spu.offering_code) as subSaleTag
        from sku_offering_info sku
                 left join sku_release_target srt on srt.sku_offering_code = sku.offering_code
                 left join spu_offering_info spu on sku.spu_id = spu.id
                 left join category_info ci on ci.spu_id = spu.id
                 left join sku_role_relation srr on srr.sku_id = sku.id
        where spu.offering_code = #{spuCode}
          and sku.delete_time is null
    </select>

    <select id="getAtomList"  resultType="com.chinamobile.iot.sc.pojo.dto.gio.GioAtomOriginDTO">
        SELECT
            aoi.id,
            offering_code AS offeringCode,
            offering_name AS offeringName,

            offering_class AS offeringClass,

            charge_code AS chargeCode,
            ext_soft_offering_code AS extSoftOfferingCode,
            ext_hard_offering_code AS extHardOfferingCode,
            quantity AS offeringQuantity,
            unit AS offeringUnit,
            model AS offeringModel,
            color AS offeringColor,
            atom_sale_price AS offeringPrice,
            settle_price AS offeringSettlementPrice,
            ss.id AS serviceCode,
            ss.name AS serviceName,
            ss.real_product_name AS productName,
            dept.short_name AS productDepartment,
            pp.name AS productAttributes,
            quantity,
            unit,
            model,
            color,
            atom_sale_price AS atomSalePrice,
            settle_price AS settlePrice,
            delete_time AS deleteTime
        FROM
            atom_offering_info aoi
                left join atom_std_service ass on ass.atom_id = aoi.id
                left join standard_service ss on ss.id = ass.std_service_id
                left join department dept on dept.id = ss.product_department_id
                left join product_property pp on ss.product_property_id = pp.id

    </select>
    <select id="getAtomAllList"  resultType="com.chinamobile.iot.sc.pojo.dto.gio.GioAtomOriginAllDTO">
        SELECT
            aoi.id as offeringId,
            sku.offering_code as skuCode,
            skt.province_code as skuProvinceCode,
            skt.city_code as skuCityCode,
            aoi.offering_code as atomOfferingCode,
            aoi.create_time as createTime
        FROM
            atom_offering_info aoi
                left join sku_offering_info sku on sku.id = aoi.sku_id
                left join sku_release_target skt on skt.sku_offering_code = sku.offering_code;

    </select>
    <select id="getUserIdList"  resultType="com.chinamobile.iot.sc.pojo.entity.UserMiniProgram">
        select user_id as userId,
               create_time as createTime
        from user_mini_program
        where user_id is not null;

    </select>

</mapper>