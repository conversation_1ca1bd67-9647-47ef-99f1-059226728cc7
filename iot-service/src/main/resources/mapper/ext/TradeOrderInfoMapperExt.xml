<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.TradeOrderInfoMapperExt">

  <select id="listTradeOrderInfo"
          resultType="com.chinamobile.iot.sc.pojo.dto.TradeOrderInfoDTO">
      select
      toi.id,
      toi.create_time createTime,
      toi.trade_no tradeNo,
      toi.buyer_name buyerName,
      up.partner_name partnerName,
      toi.contract_num contractNum,
      toi.seller_name sellerName,
      toi.trade_price tradePrice,
      toi.max_financing_price maxFinancingPrice,
      toi.request_financing_price requestFinancingPrice,
      toi.erwartetes_wareneingangsdatum erwartetesWareneingangsdatum,
      toi.invoice_num invoiceNum,
      toi.invoice_price invoicePrice,
      toi.can_use_bank canUseBank,
      toi.baoli_status baoliStatus
      from
      trade_order_info toi
      <if test="tradeOrderInfoParam.downUserIdList != null and tradeOrderInfoParam.downUserIdList.size() != 0">
          inner join (
          select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
          ob.trade_no
          from
          order_baoli ob,
          order_cooperator_relation ocr,
          user_partner up
          where
          ob.order_atom_info_id = ocr.atom_order_id
          and ob.order_id = ocr.order_id
          and ocr.cooperator_id = up.user_id
          and ocr.cooperator_id in
          <foreach collection="tradeOrderInfoParam.downUserIdList" item="downUserId" index="index" open="(" close=")" separator=",">
              #{downUserId}
          </foreach>
          group by ob.trade_no
          ) op on op.trade_no = toi.trade_no
      </if>,
      user_partner up,
      user_partner up2
      where
      toi.cooperator_id = up.user_id
      and up.partner_name = up2.partner_name
      and up2.cj_status = 'activated'
    <if test="tradeOrderInfoParam.contractNum != null and tradeOrderInfoParam.contractNum != ''">
      and toi.contract_num like '%${tradeOrderInfoParam.contractNum}%'
    </if>
    <if test="tradeOrderInfoParam.buyerName != null and tradeOrderInfoParam.buyerName != ''">
      and toi.buyer_name like '%${tradeOrderInfoParam.buyerName}%'
    </if>
    <if test="tradeOrderInfoParam.partnerName != null and tradeOrderInfoParam.partnerName != ''">
      and up.partner_name like '%${tradeOrderInfoParam.partnerName}%'
    </if>
    <if test="tradeOrderInfoParam.sellerName != null and tradeOrderInfoParam.sellerName != ''">
      and toi.seller_name like '%${tradeOrderInfoParam.sellerName}%'
    </if>
    <if test="tradeOrderInfoParam.tradeNo != null and tradeOrderInfoParam.tradeNo != ''">
      and toi.trade_no like '%${tradeOrderInfoParam.tradeNo}%'
    </if>
    <if test="tradeOrderInfoParam.baoliStatus != null">
      and toi.baoli_status = #{tradeOrderInfoParam.baoliStatus}
    </if>
    <if test="tradeOrderInfoParam.userId != null and tradeOrderInfoParam.userId != ''">
      and up2.user_id = #{tradeOrderInfoParam.userId}
    </if>
    <!--<if test="tradeOrderInfoParam.downUserIdList != null and tradeOrderInfoParam.downUserIdList.size() != 0">
      and toi.cooperator_id in
      <foreach collection="tradeOrderInfoParam.downUserIdList" item="downUserId" index="index" open="(" close=")" separator=",">
          #{downUserId}
      </foreach>
    </if>-->
    order by toi.update_time desc
  </select>

    <select id="listTradeOrderDetail" parameterType="java.lang.String"
            resultType="com.chinamobile.iot.sc.pojo.dto.TradeOrderDetailDTO">
         select
			oi.cust_code custCode,
            p.province_company province,
            oi.org_name orgName,
            o2ai.create_time createTime,
            o2ai.order_id orderId,
			oi.business_code businessCode,
            o2ai.order_status orderStatus,
            ob.order_finish_time orderFinishTime,
            oi.remarks,
            oi.deduct_price orderDeductPrice,
            (SELECT GROUP_CONCAT(CONCAT('(',ci.coupon_code,',',ci.coupon_amount),')') FROM coupon_info ci WHERE ci.order_id = o2ai.order_id GROUP BY ci.order_id) couponInfo,
            oi.addr1 deliveryArea,
            ( SELECT GROUP_CONCAT( li.logis_code ) FROM logistics_info li WHERE li.order_id = o2ai.order_id AND li.order_atom_info_id = o2ai.id AND li.logistics_type = 0 ) logisCode,
            ( SELECT GROUP_CONCAT( o2as.sn ) FROM order_2c_atom_sn o2as WHERE o2as.atom_order_id = o2ai.id ) sn,
            soi.offering_name spuOfferingName,
            soi.offering_code spuOfferingCode,
			oi.spu_offering_class spuOfferingClass,
			o2ai.sku_offering_name skuOfferingName,
			o2ai.sku_offering_code skuOfferingCode,
            o2ai.sku_quantity  skuQuantity,
			o2ai.atom_offering_name atomOfferingName,
			o2ai.atom_offering_code atomOfferingCode,
            o2ai.atom_offering_class atomOfferingClass,
			o2ai.atom_quantity * o2ai.sku_quantity atomQuantity,
            toi.seller_name sellerName,
            toi.contract_num contractNum,
            c.name contractName,
            toi.buyer_name buyerName,
            up.partner_name partnerName,
            sum(ob.total_price) baoliSettlePrice
        from
			trade_order_info toi,
            user_partner up,
            contract c,
            order_baoli ob,
            order_2c_atom_info o2ai,
            spu_offering_info_history soi,
            order_2c_info oi
            LEFT JOIN province p ON oi.be_id = p.province_code
		where
			toi.contract_num = c.number
		and	toi.trade_no = ob.trade_no
        and ob.order_id = o2ai.order_id
        and o2ai.order_id = oi.order_id
        and soi.offering_code = o2ai.spu_offering_code and soi.spu_offering_version = o2ai.spu_offering_version
        and toi.cooperator_id = up.user_id
        and toi.trade_no = #{tradeNo}
        group by oi.order_id
    </select>

    <select id="listFinancingBillDetail"
            resultType="com.chinamobile.iot.sc.pojo.dto.FinancingBillDetailDTO">
        select
            oi.create_oper_code createOperCode,
            oi.employee_num employeeNum,
            oi.cust_mg_name custMgName,
            oi.cust_mg_phone custMgPhone,
			oi.cust_code custCode,
            oi.cust_name custName,
            oi.be_id beId,
            oi.location,
            oi.region_ID regionID,
            oi.org_name orgName,
            o2ai.create_time createTime,
            o2ai.order_id orderId,
			oi.business_code businessCode,
            o2ai.order_status orderStatus,
            case
				when (oi.order_type = '00' or oi.order_type = '02' or oi.order_type = '03') then '代客下单'
                when oi.order_type = '01' then '自主下单'
			else '未知订单类型'
            end orderType,
            ob.order_finish_time orderFinishTime,
            oi.remarks,
            oi.total_price totalPrice,
            oi.deduct_price orderDeductPrice,
            (SELECT GROUP_CONCAT(CONCAT('(',ci.coupon_code,',',ci.coupon_amount),')') FROM coupon_info ci WHERE ci.order_id = o2ai.order_id GROUP BY ci.order_id) couponInfo,
            oi.addr1 deliveryArea,
            oi.contact_person_name contactPersonName,
            oi.addr1,
            oi.addr2,
            oi.addr3,
            oi.addr4,
            oi.contact_phone contactPhone,
            ( SELECT GROUP_CONCAT( li.logis_code ) FROM logistics_info li WHERE li.order_id = o2ai.order_id AND li.order_atom_info_id = o2ai.id AND li.logistics_type = 0 ) logisCode,
            ( SELECT GROUP_CONCAT( o2as.sn ) FROM order_2c_atom_sn o2as WHERE o2as.atom_order_id = o2ai.id ) sn,
            soi.offering_name spuOfferingName,
            soi.offering_code spuOfferingCode,
			oi.spu_offering_class spuOfferingClass,
			o2ai.sku_offering_name skuOfferingName,
			o2ai.sku_offering_code skuOfferingCode,
            o2ai.sku_quantity  skuQuantity,
            o2ai.sku_price skuPrice,
			o2ai.atom_offering_name atomOfferingName,
			o2ai.atom_offering_code atomOfferingCode,
            o2ai.atom_offering_class atomOfferingClass,
			o2ai.atom_quantity * o2ai.sku_quantity atomQuantity,
            o2ai.atom_quantity * o2ai.sku_quantity * o2ai.atom_price atomPrice,
            o2ai.atom_settle_price atomSettlePrice,
            o2ai.atom_quantity * o2ai.sku_quantity * o2ai.atom_settle_price atomTotalSettlePrice,
            up.partner_name partnerName,
            up2.name partnerUserName,
            kpm2.seller_name supplierName,
            kpm2.contract_num supplierContractNum,
            kpm2.contract_name supplierContractName,
            sum(ob.total_price) baoliSettlePrice,
            toi.baoli_status baoliStatus,
            '' billsReceivable,
            '' clientName,
            kpm.contract_num sellContractNum,
            kpm.contract_name sellContractName,
            kpm.material_num materialNum,
            d.short_name productDepartmentName,
            '' purchaseOrder,
            '' collectionNumber,
            '' companyInvoiceNumber,
            '' supplierInvoiceNumber,
            '' reimbursemenNumber
        from
			trade_order_info toi,
            user_partner up,
            user_partner up2,
            contract c,
            order_baoli ob,
            order_2c_atom_info o2ai,
            spu_offering_info_history soi,
            atom_offering_info aoi
            left join k3_product_material kpm on aoi.id = kpm.atom_id
				and kpm.contract_type = 1
            left join k3_product_material kpm2 on aoi.id = kpm2.atom_id
				and kpm2.contract_type = 2,
            order_2c_info oi,
            atom_std_service ass,
            standard_service ss,
            department d
		where
			toi.contract_num = c.number
		and	toi.trade_no = ob.trade_no
        and ob.order_id = o2ai.order_id
        and o2ai.order_id = oi.order_id
        and soi.offering_code = o2ai.spu_offering_code and soi.spu_offering_version = o2ai.spu_offering_version
        and o2ai.atom_offering_code = aoi.offering_code
        and o2ai.spu_offering_code = aoi.spu_code
        and o2ai.sku_offering_code = aoi.sku_code
        and toi.cooperator_id = up.user_id
        and up.partner_name = up2.partner_name
        and up2.cj_status = 'activated'
        and aoi.id = ass.atom_id
        and ass.std_service_id = ss.id
        and ss.product_department_id = d.id
        and toi.trade_no = #{tradeNo}
        group by ob.order_id
    </select>

</mapper>