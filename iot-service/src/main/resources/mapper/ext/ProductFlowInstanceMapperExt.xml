<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.ProductFlowInstanceMapperExt">
    <resultMap id="FlowInstanceSpuMap" type="com.chinamobile.iot.sc.pojo.ProductFlowInstanceSpu">
        <!--
          WARNING - @mbg.generated  This element was generated on Tue Mar 05 17:28:51 CST 2024. by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="flow_instance_id" jdbcType="VARCHAR" property="flowInstanceId" />
        <result column="flow_id" jdbcType="VARCHAR" property="flowId" />
        <result column="shelf_catagory_id" jdbcType="VARCHAR" property="shelfCatagoryId" />
        <result column="first_directory_id" jdbcType="VARCHAR" property="firstDirectoryId" />
        <result column="second_directory_id" jdbcType="VARCHAR" property="secondDirectoryId" />
        <result column="spu_code" jdbcType="VARCHAR" property="spuCode" />
        <result column="spu_name" jdbcType="VARCHAR" property="spuName" />
        <result column="product_standard" jdbcType="INTEGER" property="productStandard" />
        <result column="product_type" jdbcType="INTEGER" property="productType" />
        <result column="url" jdbcType="VARCHAR" property="url" />
        <result column="manager" jdbcType="VARCHAR" property="manager" />
        <result column="product_desc" jdbcType="VARCHAR" property="productDesc" />
        <result column="application_area" jdbcType="VARCHAR" property="applicationArea" />
        <result column="is_hidden_shelf" jdbcType="VARCHAR" property="isHiddenShelf" />
        <result column="spu_service_provider" jdbcType="VARCHAR" property="spuServiceProvider" />
        <result column="sale_tag" jdbcType="VARCHAR" property="saleTag" />
        <result column="search_word" jdbcType="VARCHAR" property="searchWord" />
        <result column="spu_remark" jdbcType="VARCHAR" property="spuRemark" />
        <result column="aftermarket_admin_info" jdbcType="VARCHAR" property="aftermarketAdminInfo" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>


    <select id="getFlowInstanceList" resultType="com.chinamobile.iot.sc.pojo.mapper.FlowInstanceListDO">
    SELECT
        i.id,
        i.flow_instance_number flowNumber,
        spu.spu_name spuName,
        GROUP_CONCAT(sku.sku_name) skuName,
        spu.product_type productType,
        spu.product_standard productStandard,
        f.flow_type flowType,
        f.operate_type operateType,
        i.creator_name creatorName,
        i.create_time createTime,
        task.step_name currentStepName,
        if((#{isAdmin} or task.assignee_id = #{userId}) AND i.can_edit,true,false) canEdit,
        if((#{isAdmin} or task.assignee_id = #{userId}) AND i.can_cancel,true,false) canCancel,
        if((#{isAdmin} or task.assignee_id = #{userId}) AND i.can_audit,true,false) canAudit,
        if((#{isAdmin} or task.assignee_id = #{userId}) AND i.can_config,true,false) canConfig
    FROM
        product_flow_instance i
        LEFT JOIN product_flow f ON i.flow_id = f.id
        LEFT JOIN product_flow_instance_spu spu ON spu.flow_instance_id = i.id
        LEFT JOIN product_flow_instance_sku sku ON sku.flow_instance_id = i.id
        LEFT JOIN product_flow_instance_task task ON task.flow_instance_id = i.id AND task.step_id = i.current_step_id AND task.handle_status = 0
    where 1=1
        <if test="spuName != null and spuName != ''">
            and spu.spu_name like concat ('%',#{spuName},'%')
        </if>
        <if test="skuName != null and skuName != ''">
            and sku.sku_name like concat ('%',#{skuName},'%')
        </if>
        <if test="productType != null">
            and spu.product_type = #{productType}
        </if>
        <if test="list != null and list.size() > 0">
            and f.flow_type in
            <foreach collection = "list" item = "item" open = '(' close = ')' separator = ','>
                #{item}
            </foreach>
        </if>
        <if test="productStandard != null">
            and spu.product_standard = #{productStandard}
        </if>
        <if test="operatorType != null">
            and f.operate_type = #{operatorType}
        </if>
        <if test="flowNumber != null and flowNumber != ''">
            and i.flow_instance_number like concat ('%',#{flowNumber},'%')
        </if>
        <if test="creatorName != null and creatorName != ''">
            and i.creator_name like concat ('%',#{creatorName},'%')
        </if>
        <if test="startTime != null">
            and i.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            and i.create_time <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="userId != null and userId != '' and personData == true">
            and  (i.creator_id = #{userId} or task.assignee_id = #{userId} or exists (select * from product_flow_instance_task where flow_instance_id = i.id and assignee_id = #{userId}))
        </if>
    GROUP BY i.id
    ORDER BY i.update_time DESC
    </select>

    <select id="getFlowInstanceListArea" resultType="com.chinamobile.iot.sc.pojo.mapper.FlowInstanceListDO">
    SELECT
        temp.*,
        (SELECT GROUP_CONCAT(sku_name) FROM product_flow_instance_sku WHERE flow_instance_id = temp.id) skuName
    FROM
        (SELECT
        i.id,
        i.flow_instance_number flowNumber,
        spu.spu_name spuName,
        spu.product_type productType,
        spu.product_standard productStandard,
        f.flow_type flowType,
        f.operate_type operateType,
        i.creator_name creatorName,
        i.create_time createTime,
        task.step_name currentStepName,
        if((#{isAdmin} or task.assignee_id = #{userId}) AND i.can_edit,true,false) canEdit,
        if((#{isAdmin} or task.assignee_id = #{userId}) AND i.can_cancel,true,false) canCancel,
        if((#{isAdmin} or task.assignee_id = #{userId}) AND i.can_audit,true,false) canAudit,
        if((#{isAdmin} or task.assignee_id = #{userId}) AND i.can_config,true,false) canConfig
        FROM
        product_flow_instance i
        LEFT JOIN product_flow f ON i.flow_id = f.id
        LEFT JOIN product_flow_instance_spu spu ON spu.flow_instance_id = i.id
        LEFT JOIN product_flow_instance_sku sku ON sku.flow_instance_id = i.id
        LEFT JOIN product_flow_instance_task task ON task.flow_instance_id = i.id AND task.step_id = i.current_step_id AND task.handle_status = 0
        where 1=1
        <if test="spuName != null and spuName != ''">
            and spu.spu_name like concat ('%',#{spuName},'%')
        </if>
        <if test="skuName != null and skuName != ''">
            and sku.sku_name like concat ('%',#{skuName},'%')
        </if>
        <if test="productType != null">
            and spu.product_type = #{productType}
        </if>
        <if test="list != null and list.size() > 0">
            and f.flow_type in
            <foreach collection = "list" item = "item" open = '(' close = ')' separator = ','>
                #{item}
            </foreach>
        </if>
        <if test="productStandard != null">
            and spu.product_standard = #{productStandard}
        </if>
        <if test="operatorType != null">
            and f.operate_type = #{operatorType}
        </if>
        <if test="flowNumber != null and flowNumber != ''">
            and i.flow_instance_number like concat ('%',#{flowNumber},'%')
        </if>
        <if test="creatorName != null and creatorName != ''">
            and i.creator_name like concat ('%',#{creatorName},'%')
        </if>
        <if test="startTime != null">
            and i.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            and i.create_time <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="userId != null and userId != ''">
            and (
            (i.creator_id = #{userId} or task.assignee_id = #{userId} or exists (select * from product_flow_instance_task where flow_instance_id = i.id and assignee_id = #{userId}))
            or
            (sku.sale_province_city like '%全国%' or sku.sale_province_city like concat ('%',#{area},'%') ))
        </if>
        GROUP BY i.id
        ORDER BY i.update_time DESC
        )temp
    </select>


    <select id="getShelfSpu" parameterType="java.lang.String" resultMap="FlowInstanceSpuMap">
    SELECT
        spu.*
    FROM
        product_flow_instance_spu spu
        JOIN product_flow flow ON flow.id = spu.flow_id AND flow.flow_type in (1,3,4,5,6)
        JOIN product_flow_instance instance ON spu.flow_instance_id = instance.id  AND instance.`status` = 1
    WHERE spu.spu_code = #{spuCode}
    order by instance.update_time desc
    LIMIT 1
    </select>

    <select id="getTaskInfo" parameterType="java.lang.String" resultType="com.chinamobile.iot.sc.pojo.mapper.ProductFlowInstanceTaskDO">
    SELECT
        s.step_index stepIndex,
        (SELECT step_name FROM product_flow_instance_task WHERE step_id = i.current_step_id AND flow_instance_id = i.id AND handle_status = 0 ORDER BY create_time DESC LIMIT 1 )stepName,
        s.tip,
        s.redirect_role_id redirectRoleId,
        s.known_role_id knownRoleId,
        s.limit_id limitId,
        passNextS.step_name passNextStepName,
        passNextS.assignee_role_id passNextStepRoleId,
        rejectNextS.step_name rejectNextStepName,
        rejectNextS.assignee_role_id rejectNextStepRoleId,
        passNextNextS.step_name passNextNextStepName,
        passNextNextS.assignee_role_id passNextNextStepRoleId
    FROM
        product_flow_instance i
        JOIN product_flow_step s ON i.flow_id = s.flow_id AND i.current_step_id = s.id
        LEFT JOIN product_flow_step rejectNextS ON s.flow_id = rejectNextS.flow_id AND s.reject_next_step_id = rejectNextS.id
        LEFT JOIN product_flow_step passNextS ON s.flow_id = passNextS.flow_id AND passNextS.step_index = s.step_index + 1
        LEFT JOIN product_flow_step passNextNextS ON s.flow_id = passNextNextS.flow_id AND passNextNextS.step_index = s.step_index + 2
    WHERE i.id = #{flowInstanceId}
    </select>

    <select id="getShelfSpuSimpleList" resultType="com.chinamobile.iot.sc.pojo.mapper.ShelfSpuSimpleListDO" parameterType="java.lang.String">
    SELECT
        spu.spu_code spuCode,
        spu.spu_name spuName,
        spu.product_standard productStandard,
        spu.product_type productType
    FROM
        product_flow_instance_spu spu
        JOIN product_flow_instance i ON i.id = spu.flow_instance_id AND i.`status` = 1
        JOIN product_flow f ON f.id = i.flow_id AND f.flow_type = 1
    where 1=1
        <if test="spuNameCode != null and spuNameCode != ''">
            and (spu.spu_name like concat ('%',#{spuNameCode},'%') or spu.spu_code like concat ('%',#{spuNameCode},'%') )
        </if>
    group by spuCode
    </select>

    <select id="getUnhandldeTask" parameterType="java.lang.String" resultType="java.lang.Integer">
    SELECT
        count(*)
    FROM
        product_flow_instance_task task
        JOIN product_flow_instance i ON task.flow_instance_id = i.id
        AND i.`status` = 0
    WHERE (task.assignee_id = #{userId} or task.next_assignee_id = #{userId}) and task.handle_status = 0
    </select>

    <select id="findNeedDealProductFlowInstanceSpu" resultType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceSpu">
    SELECT * FROM product_flow_instance_spu spu
    WHERE NOT EXISTS (SELECT * FROM product_flow_instance_directory WHERE flow_instance_id = spu.flow_instance_id)
    </select>

    <select id="getNavigationDirectoryList" parameterType="java.lang.String" resultType="com.chinamobile.iot.sc.pojo.mapper.ProductFlowNavigationDirectoryDO">
    SELECT
        id.first_directory_id firstDirectoryId,
        id.first_directory_name firstDirectoryName,
        id.second_directory_id secondDirectoryId,
        id.second_directory_name secondDirectoryName,
        id.third_directory_id thirdDirectoryId,
        id.third_directory_name thirdDirectoryName
    FROM
        product_flow_instance_directory id
    WHERE id.flow_instance_id = #{flowInstanceId}
    </select>

</mapper>