<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.AfterMarkOrder2cOfferingInfoMapperExt">

    <select id="getAfterMarketOrderOfferingInfos"
            parameterType="com.chinamobile.iot.sc.pojo.param.AfterMarketOrderOfferingQueryParam"
            resultType="com.chinamobile.iot.sc.pojo.mapper.AfterMarketOrderOfferingInfoDO">
        select
        aoi.after_market_code afterMarketCode,
        aoi.aftermarket_type afterMarketType,
        aoi.after_market_internal_name afterMarketName,
        aoi.settle_price afterMarketSettlePrice,
        aoc.order_take_type orderTakeType,
        aoc.sku_offering_code skuOfferingCode,
        aoc.offering_code atomOfferingCode,
        aoc.spu_offering_code spuOfferingCode,
        aoc.spu_offering_class spuOfferingClass,
        sku.offering_name skuOfferingName,
        atom.offering_name atomOfferingName,
        aoc.admin_cooperator_id adminCooperatorId,
        aoc.install_manager_id installManagerId,
        aoc.province_install_platform provinceInstallPlatform,
        u.partner_name adminCooperatorName,
        aoc.spu_offering_version spuOfferingVersion,
        aoc.after_market_version afterMarketVersion,
        aoc.sku_offering_version skuOfferingVersion,
        aoc.atom_offering_version atomOfferingVersion
        from
        (
        select * from aftermarket_offering_code where aftermarket_offering_code.after_market_code=#{param.afterMarketCode}
                                                and aftermarket_offering_code.after_market_version=#{param.afterMarketVersion}
        <if test=" param.atomOfferingCode != null and param.atomOfferingCode !=''">
            and aftermarket_offering_code.offering_code=#{param.atomOfferingCode}
            and aftermarket_offering_code.atom_offering_version=#{param.atomOfferingVersion}
        </if>
        <if test=" param.atomOfferingCode == null or param.atomOfferingCode ==''">
            and aftermarket_offering_code.sku_offering_code=#{param.skuOfferingCode}
            and aftermarket_offering_code.sku_offering_version=#{param.skuOfferingVersion}
            and aftermarket_offering_code.spu_offering_code=#{param.spuOfferingCode}
            and aftermarket_offering_code.spu_offering_version=#{param.spuOfferingVersion}
        </if>
        ) aoc
        left join (select * from atom_offering_info_history where atom_offering_info_history.sku_code=#{param.skuOfferingCode}
                                                    and atom_offering_info_history.sku_offering_version=#{param.skuOfferingVersion}
                                                    and atom_offering_info_history.spu_code=#{param.spuOfferingCode}
                                                    and atom_offering_info_history.spu_offering_version=#{param.spuOfferingVersion}
                                                    ) atom
            on atom.offering_code = aoc.offering_code
            and atom.atom_offering_version = aoc.atom_offering_version
        left join sku_offering_info_history sku on sku.offering_code = aoc.sku_offering_code
                                        and sku.sku_offering_version = aoc.sku_offering_version
                                        and sku.spu_code = aoc.spu_offering_code
                                        and sku.spu_offering_version = aoc.spu_offering_version
        left join `user_partner` u on u.user_id = aoc.admin_cooperator_id
        left join aftermarket_offering_info_history aoi on aoi.after_market_code = aoc.after_market_code
                                                        and aoi.after_market_version = aoc.after_market_version
        order by aoc.update_time desc
    </select>


</mapper>
