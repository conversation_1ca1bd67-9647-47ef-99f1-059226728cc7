<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.OrderCooperatorRelationMapperExt">

  <select id="listCooperatorInfoByGroup" resultType="com.chinamobile.iot.sc.entity.iot.OrderCooperatorInfoByGroupDTO">
    select
      GROUP_CONCAT(distinct up.partner_name) partnerName,
      group_concat(distinct up.name) userName,
      group_concat(distinct ocr.cooperator_id) cooperatorId,
      ocr.atom_order_id atomOrderId,
      ocr.order_id orderId
    from order_cooperator_relation ocr
    inner JOIN user_partner up on up.user_id = ocr.cooperator_id
    where 1=1
    <if test="orderCooperatorInfoByGroupParam.atomOrderId != null and orderCooperatorInfoByGroupParam.atomOrderId != ''">
      and ocr.atom_order_id =  #{orderCooperatorInfoByGroupParam.atomOrderId}
    </if>
    <if test="orderCooperatorInfoByGroupParam.orderId != null and orderCooperatorInfoByGroupParam.orderId != ''">
      and ocr.order_id =  #{orderCooperatorInfoByGroupParam.orderId}
    </if>
    <if test="orderCooperatorInfoByGroupParam.userName != null and orderCooperatorInfoByGroupParam.userName != ''">
      and up.name like '%${orderCooperatorInfoByGroupParam.userName}%'
    </if>
    <if test="orderCooperatorInfoByGroupParam.partnerName != null and orderCooperatorInfoByGroupParam.partnerName != ''">
      and up.partner_name like '%${orderCooperatorInfoByGroupParam.partnerName}%'
    </if>
    <if test="orderCooperatorInfoByGroupParam.cooperatorId != null and orderCooperatorInfoByGroupParam.cooperatorId != ''">
      and aocr.cooperator_id = #{orderCooperatorInfoByGroupParam.cooperatorId}
    </if>
    
    group by ocr.atom_order_id
  </select>

  <select id="listCooperatorInfo" resultType="com.chinamobile.iot.sc.pojo.dto.OrderCooperatorInfoDTO">
    select distinct
      up.partner_name partnerName,
      up.name userName,
      ocr.cooperator_id cooperatorId,
      ocr.atom_order_id atomOrderId,
      ocr.order_id orderId
    from order_cooperator_relation ocr
    inner JOIN user_partner up on up.user_id = ocr.cooperator_id
    where 1=1
    <if test="orderCooperatorInfoParam.atomOrderId != null and orderCooperatorInfoParam.atomOrderId != ''">
      and ocr.atom_order_id =  #{orderCooperatorInfoParam.atomOrderId}
    </if>
    <if test="orderCooperatorInfoParam.orderId != null and orderCooperatorInfoParam.orderId != ''">
      and ocr.order_id =  #{orderCooperatorInfoParam.orderId}
    </if>
    <if test="orderCooperatorInfoParam.userName != null and orderCooperatorInfoParam.userName != ''">
      and up.name like '%${orderCooperatorInfoParam.userName}%'
    </if>
    <if test="orderCooperatorInfoParam.partnerName != null and orderCooperatorInfoParam.partnerName != ''">
      and up.partner_name like '%${orderCooperatorInfoParam.partnerName}%'
    </if>
    <if test="orderCooperatorInfoParam.cooperatorId != null and orderCooperatorInfoParam.cooperatorId != ''">
      and aocr.cooperator_id = #{orderCooperatorInfoParam.cooperatorId}
    </if>
    <if test="orderCooperatorInfoParam.atomOrderIdList != null and orderCooperatorInfoParam.atomOrderIdList.size() != 0">
      and ocr.atom_order_id in
      <foreach collection="orderCooperatorInfoParam.atomOrderIdList" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
  </select>

</mapper>