<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.Order2cAtomInfoMapperExt">
    <select id="getHandleHistoryOrderCard" resultType="com.chinamobile.iot.sc.pojo.mapper.Order2cAtomInfoHistoryDO">
        SELECT
            oc.order_id orderId ,
            oc.order_type orderType,
            oc.order_status orderStatus,
            oc.atom_offering_code atomOfferingCode,
            oc.spu_offering_code spuOfferingCode,
            oc.sku_offering_code skuOfferingCode,
            aoi.inventory_main_id inventoryMainId,
            aoi.id atomId,
            dimi.terminal_type terminalType
        FROM
            order_2c_atom_info oc
                left join atom_offering_info aoi ON aoi.offering_code = oc.atom_offering_code and oc.spu_offering_code = aoi.spu_code AND oc.sku_offering_code = aoi.sku_code
                left join dkcardx_inventory_main_info dimi on dimi.id =aoi.inventory_main_id
        where ((oc.order_type = '01' and oc.order_status in(10,15,16) )  or ((oc.order_type = '00' or oc.order_type = '02' or oc.order_type = '03') and oc.order_status in (10,11,15,16)))
          and oc.atom_offering_class ='X'
          and (aoi.inventory_main_id is not null or aoi.inventory_main_id !='')
        GROUP BY
            oc.order_id
    </select>
    <select id="selectOrderExportList" resultType="com.chinamobile.iot.sc.pojo.mapper.OrderExportDO">
        SELECT
            oa.id as id,
            oa.create_time AS createTime,
            oa.order_id AS orderId,
            case
                when status in (3,4) then oi.order_status_time
                else null
                end finishTime,
            IF(oi.pay_time is null, oi.create_time,DATE_FORMAT(oi.pay_time,'%Y%m%d%H%i%s')) receiveOrderTime,
            oi.order_status_time orderStatusTime,
            sendGoodsTime,
            case
                when (oa.order_type = '00' or oa.order_type = '02' or oa.order_type = '03') then oa.valet_order_complete_time
                when oa.order_type = '01' then oa.create_time
                end valetOrderCompleteTime,
            oa.bill_no_time billNoTime,
            (SELECT
                 case
                     when sale_order_type = 0 then '标准产品省框'
                     when sale_order_type = 1 then 'DICT服务包'
                     when sale_order_type = 2 then '统结标准产品'
                     else ''
                     end saleOrderType
             FROM contract WHERE number = kpm.contract_num limit 1
            ) saleOrderType
        FROM
            order_2c_atom_info oa force index (idx_create_time)
            LEFT JOIN order_2c_info oi ON oa.order_id = oi.order_id
            LEFT JOIN atom_offering_info atom ON atom.offering_code = oa.atom_offering_code AND atom.spu_code =
            oa.spu_offering_code AND atom.sku_code = oa.sku_offering_code

            LEFT JOIN
            (
            select o2ah.atom_order_id ,o2ah.order_id, date_format(max(o2ah.create_time),'%Y-%m-%d %H:%i:%s') sendGoodsTime
            from order_2c_atom_history o2ah,
            order_2c_info oi2
            where o2ah.operate_type = 1 and o2ah.inner_status = 1
            and oi2.order_id = o2ah.order_id
            and oi2.order_id =#{orderId}
            and oi2.spu_offering_class not in
            ('A01','A02','A03','A04','A08','A09','A10','A12','A13','A14','A15','A16','A17')
            group by atom_order_id ,order_id
            union
            select o2ah.atom_order_id ,o2ah.order_id, o2ah.create_time sendGoodsTime
            from order_2c_atom_history o2ah,
            order_2c_info oi2
            where o2ah.operate_type = 1 and o2ah.inner_status = 61
            and oi2.order_id = o2ah.order_id
            and oi2.order_id =#{orderId}
            and oi2.spu_offering_class in ('A01','A02','A03','A04','A08','A09','A10','A12','A13','A14','A15','A16','A17'))
            o2ah on o2ah.atom_order_id = oa.id and o2ah.order_id = oa.order_id
            LEFT JOIN order_2c_agent_info agent ON agent.order_id = oa.order_id
            left join k3_product_material kpm on kpm.id = (select id from k3_product_material where
            atom_id = atom.id limit 1)
        where
            oa.order_status not in (13,14)
          and oa.order_id =#{orderId};
    </select>
</mapper>