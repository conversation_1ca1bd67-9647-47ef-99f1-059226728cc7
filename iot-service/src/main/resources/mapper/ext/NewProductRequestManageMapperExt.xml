<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.NewProductRequestManageMapperExt">

  <select id="listNewProductOnlineOffline" resultType="com.chinamobile.iot.sc.pojo.vo.NewProductOnlineOfflineVO">
    SELECT
        nprm.id newProductRequestId,
        nprooci.id comboInfoId,
        nprooci.request_no requestNo,
        nprm.spu_offering_name spuOfferingName,
        nprm.sku_offering_name skuOfferingName,
        u.partner_name cooperatorName,
        nprm.online_status onlineStatus,
        CASE
            WHEN nprm.online_status IN ('01' , '03', '04','06') THEN '下架'
            WHEN nprm.online_status IN ('02' , '05') THEN '上架'
            ELSE '状态错误'
        END onlineStatusName,
        nprm.online_offline_request_status onlineOfflineRequestStatus,
        CASE
          WHEN nprm.online_offline_request_status = 0 THEN '新增'
          WHEN nprm.online_offline_request_status = 1 THEN '待审核(上架初审)'
          WHEN nprm.online_offline_request_status = 2 THEN '待审核(上架复审)'
          WHEN nprm.online_offline_request_status = 3 THEN '待审核(上架终审)'
          WHEN nprm.online_offline_request_status = 4 THEN '待审核(上架运管审核)'
          WHEN nprm.online_offline_request_status = 5 THEN '已上架'
          WHEN nprm.online_offline_request_status = 6 THEN '审核不通过(上架)'
          WHEN nprm.online_offline_request_status = 7 THEN '待审核(下架初审)'
          WHEN nprm.online_offline_request_status = 8 THEN '待审核(下架复审)'
          WHEN nprm.online_offline_request_status = 9 THEN '待审核(下架终审)'
          WHEN nprm.online_offline_request_status = 10 THEN '待审核(下架运管审核)'
          WHEN nprm.online_offline_request_status = 11 THEN '已下架'
          WHEN nprm.online_offline_request_status = 12 THEN '审核不通过(下架)'
          ELSE '状态错误'
        END onlineOfflineRequestStatusName,
        nprm.online_offline_current_handler_user_id onlineOfflineCurrentHandlerUserId
    FROM
        new_product_request_manage nprm
    inner join new_product_request_online_offline_combo_info nprooci on nprm.id = nprooci.new_product_request_id,
      user_partner u
    WHERE
        nprm.cooperator_id = u.user_id
    AND nprm.request_status = 4
    AND nprm.cooperator_id = #{onlineOfflineParam.currentUserId}
    <if test="onlineOfflineParam.requestNo != null and onlineOfflineParam.requestNo !=  ''">
      and nprooci.request_no like '%${onlineOfflineParam.requestNo}%'
    </if>
    <if test="onlineOfflineParam.spuOfferingName != null and onlineOfflineParam.spuOfferingName !=  ''">
      and nprm.spu_offering_name like '%${onlineOfflineParam.spuOfferingName}%'
    </if>
    <if test="onlineOfflineParam.skuOfferingName != null and onlineOfflineParam.skuOfferingName !=  ''">
      and nprm.sku_offering_name like '%${onlineOfflineParam.skuOfferingName}%'
    </if>
    <if test="onlineOfflineParam.onlineStatus == '01'">
      and  nprm.online_status  in ('02','05')
    </if>
    <if test="onlineOfflineParam.onlineStatus == '02'">
      and  nprm.online_status  in ('01','03','04','06')
    </if>
    <if test="onlineOfflineParam.labelStatus == 0">
        <if test="onlineOfflineParam.onlineOfflineRequestStatus != null and onlineOfflineParam.onlineOfflineRequestStatus != ''">
            and  nprm.online_offline_request_status  = #{onlineOfflineParam.onlineOfflineRequestStatus}
        </if>
    </if>
    <if test="onlineOfflineParam.labelStatus == 1">
        <if test="onlineOfflineParam.onlineOfflineRequestStatus == null or onlineOfflineParam.onlineOfflineRequestStatus != ''">
            and  nprm.online_offline_request_status  in (1,2,3,4,7,8,9,10)
        </if>
        <if test="onlineOfflineParam.onlineOfflineRequestStatus != null and onlineOfflineParam.onlineOfflineRequestStatus != ''">
            and  nprm.online_offline_request_status  = #{onlineOfflineParam.onlineOfflineRequestStatus}
        </if>
    </if>
    <if test="onlineOfflineParam.labelStatus == 2">
      <if test="onlineOfflineParam.onlineOfflineRequestStatus == null or onlineOfflineParam.onlineOfflineRequestStatus != ''">
          and  nprm.online_offline_request_status  in (5,12)
      </if>
      <if test="onlineOfflineParam.onlineOfflineRequestStatus != null and onlineOfflineParam.onlineOfflineRequestStatus != ''">
          and  nprm.online_offline_request_status  = #{onlineOfflineParam.onlineOfflineRequestStatus}
      </if>
    </if>
    <if test="onlineOfflineParam.labelStatus == 3">
      <if test="onlineOfflineParam.onlineOfflineRequestStatus == null or onlineOfflineParam.onlineOfflineRequestStatus != ''">
          and  nprm.online_offline_request_status  in (0,6,11)
      </if>
      <if test="onlineOfflineParam.onlineOfflineRequestStatus != null and onlineOfflineParam.onlineOfflineRequestStatus != ''">
          and  nprm.online_offline_request_status  = #{onlineOfflineParam.onlineOfflineRequestStatus}
      </if>
    </if>
    ORDER BY nprooci.create_time DESC
  </select>

    <select id="listReadyOnline" resultType="com.chinamobile.iot.sc.pojo.vo.NewProductOnlineRequestVO">
        SELECT nprm.id                    newProductRequestId,
               nprm.request_no            requestNo,
               nprm.spu_offering_name     spuOfferingName,
               nprm.sku_offering_name     skuOfferingName,
               u.partner_name             cooperatorName,
               u.user_id                  cooperatorId,
               u.phone                    cooperatorPhone,
               u.email                    cooperatorEmail,
               nprm.application_domain    applicationDomain,
               nprm.product_introduction  productIntroduction,
               nprm.product_sale_area     productSaleArea,
               nprm.supply_price          supplyPrice,
               nprm.product_manager_name  productManagerName,
               nprm.product_manager_phone productManagerPhone,
               nprm.product_manager_email productManagerEmail
        FROM new_product_request_manage nprm,
             user_partner u
        WHERE nprm.cooperator_id = u.user_id
          AND nprm.online_status = '01'
          AND request_status = 4
          AND (nprm.cooperator_id = #{currentUserId}
              or nprm.creator = #{currentUserId}
            )
    </select>

    <select id="listReadyOnlineAdmin" resultType="com.chinamobile.iot.sc.pojo.vo.NewProductOnlineRequestVO">
        SELECT
            nprm.id newProductRequestId,
            nprm.request_no requestNo,
            nprm.spu_offering_name spuOfferingName,
            nprm.sku_offering_name skuOfferingName,
            u.partner_name cooperatorName,
            u.user_id cooperatorId,
            u.phone cooperatorPhone,
            u.email cooperatorEmail,
            nprm.application_domain applicationDomain,
            nprm.product_introduction productIntroduction,
            nprm.product_sale_area productSaleArea,
            nprm.supply_price supplyPrice,
            nprm.product_manager_name productManagerName,
            nprm.product_manager_phone productManagerPhone,
            nprm.product_manager_email productManagerEmail
        FROM
            new_product_request_manage nprm,
            user_partner u
        WHERE
            nprm.cooperator_id = u.user_id
          AND nprm.online_status = '01'
          AND request_status = 4
    </select>

  <select id="getPageRequestManageList" resultType="com.chinamobile.iot.sc.pojo.mapper.NewProductRequestManageListDO">
     select
      occ.id,
      occ.requestNo,
      occ.spuOfferingName,
      occ.skuOfferingName ,
      occ.requestStatus ,
      occ.cooperatorId ,
      occ.brand,
      occ.model,
      occ.color,
      occ.materialCode ,
      occ.networkProperty ,
      occ.applicationDomain ,
      occ.productIntroduction ,
      occ.productSaleContent ,
      occ.productSaleAreaCode ,
      occ.productSaleArea ,
      occ.supplyPrice ,
      occ.marketPrice ,
      occ.salePrice ,
      occ.productManagerName ,
      occ.productManagerPhone ,
      occ.productManagerEmail ,
      occ.requestCurrentHandlerUserId ,
      occ.createTime ,
      occ.cooperatorName
      from
      (
          SELECT
      nprm.id,
      nprm.request_no requestNo,
      nprm.spu_offering_name spuOfferingName,
      nprm.sku_offering_name skuOfferingName,
      nprm.request_status requestStatus,
      nprm.cooperator_id cooperatorId,
      nprm.brand brand,
      nprm.model model,
      nprm.color color,
      nprm.material_code materialCode,
      nprm.network_property networkProperty,
      nprm.application_domain applicationDomain,
      nprm.product_introduction productIntroduction,
      nprm.product_sale_content productSaleContent,
      nprm.product_sale_area_code productSaleAreaCode,
      nprm.product_sale_area productSaleArea,
      nprm.supply_price supplyPrice,
      nprm.market_price marketPrice,
      nprm.sale_price salePrice,
      nprm.product_manager_name productManagerName,
      nprm.product_manager_phone productManagerPhone,
      nprm.product_manager_email productManagerEmail,
      nprm.request_current_handler_user_id requestCurrentHandlerUserId,
      nprm.create_time createTime,
      u.partner_name cooperatorName
      FROM
      new_product_request_manage nprm,
      user_partner u
      WHERE
      nprm.cooperator_id = u.user_id
      <if test="managePageParam.userId != null and managePageParam.userId !=  ''">
          AND(nprm.creator = #{managePageParam.userId}
          or nprm.cooperator_id = #{managePageParam.userId}
          )
      </if>
      <if test="managePageParam.requestNo != null and managePageParam.requestNo !=  ''">
          and nprm.request_no like '%${managePageParam.requestNo}%'
      </if>
      <if test="managePageParam.spuOfferingName != null and managePageParam.spuOfferingName !=  ''">
          and nprm.spu_offering_name like '%${managePageParam.spuOfferingName}%'
      </if>
      <if test="managePageParam.skuOfferingName != null and managePageParam.skuOfferingName !=  ''">
          and nprm.sku_offering_name like '%${managePageParam.skuOfferingName}%'
      </if>
      <if test="managePageParam.cooperatorName != null and managePageParam.cooperatorName !=  ''">
          and u.partner_name like '%${managePageParam.cooperatorName}%'
      </if>
      <if test="managePageParam.requestStatusList != null ">
          and nprm.request_status in
          <foreach item="item" collection="managePageParam.requestStatusList" open="(" separator="," close=")">
              #{item}
          </foreach>
      </if>
      <if test="managePageParam.startTime != null">
          and nprm.create_time <![CDATA[ >= ]]> #{managePageParam.startTime}
      </if>
      <if test="managePageParam.endTime != null">
          and nprm.create_time <![CDATA[ <= ]]> #{managePageParam.endTime}
      </if>
      UNION
      SELECT
      nprm.id,
      nprm.request_no requestNo,
      nprm.spu_offering_name spuOfferingName,
      nprm.sku_offering_name skuOfferingName,
      nprm.request_status requestStatus,
      nprm.cooperator_id cooperatorId,
      nprm.brand brand,
      nprm.model model,
      nprm.color color,
      nprm.material_code materialCode,
      nprm.network_property networkProperty,
      nprm.application_domain applicationDomain,
      nprm.product_introduction productIntroduction,
      nprm.product_sale_content productSaleContent,
      nprm.product_sale_area_code productSaleAreaCode,
      nprm.product_sale_area productSaleArea,
      nprm.supply_price supplyPrice,
      nprm.market_price marketPrice,
      nprm.sale_price salePrice,
      nprm.product_manager_name productManagerName,
      nprm.product_manager_phone productManagerPhone,
      nprm.product_manager_email productManagerEmail,
      nprm.request_current_handler_user_id requestCurrentHandlerUserId,
      nprm.create_time createTime,
      u.partner_name cooperatorName
      FROM
      new_product_request_manage nprm,
      new_product_request_handler_info nprhi,
      user_partner u
      WHERE
      nprm.id = nprhi.flow_source_id
      AND nprm.cooperator_id = u.user_id
      and nprhi.flow_type = '01'
      and nprhi.handler_status !='03'
      <if test="managePageParam.userId != null and managePageParam.userId !=  ''">
          AND (nprhi.next_handler_user_id = #{managePageParam.userId}
          or nprm.cooperator_id = #{managePageParam.userId}
          )
      </if>
      <if test="managePageParam.requestNo != null and managePageParam.requestNo !=  ''">
          and nprm.request_no like '%${managePageParam.requestNo}%'
      </if>
      <if test="managePageParam.spuOfferingName != null and managePageParam.spuOfferingName !=  ''">
          and nprm.spu_offering_name like '%${managePageParam.spuOfferingName}%'
      </if>
      <if test="managePageParam.skuOfferingName != null and managePageParam.skuOfferingName !=  ''">
          and nprm.sku_offering_name like '%${managePageParam.skuOfferingName}%'
      </if>
      <if test="managePageParam.cooperatorName != null and managePageParam.cooperatorName !=  ''">
          and u.partner_name like '%${managePageParam.cooperatorName}%'
      </if>
      <if test="managePageParam.requestStatusList != null ">
          and nprm.request_status in
          <foreach item="item" collection="managePageParam.requestStatusList" open="(" separator="," close=")">
              #{item}
          </foreach>
      </if>
      <if test="managePageParam.startTime != null">
          and nprm.create_time <![CDATA[ >= ]]> #{managePageParam.startTime}
      </if>
      <if test="managePageParam.endTime != null">
          and nprm.create_time <![CDATA[ <= ]]> #{managePageParam.endTime}
      </if>) occ
      ORDER BY occ.createTime DESC
  </select>
    <select id="getNewProductManageDetailsById" parameterType="java.lang.String" resultType="com.chinamobile.iot.sc.pojo.vo.NewProductManageDetailsVO">
        select
            nprm.spu_offering_name spuOfferingName,
            nprm.sku_offering_name skuOfferingName,
            nprm.application_domain applicationDomain,
            nprm.product_introduction productIntroduction,
            nprm.product_sale_area productSaleArea,
            nprm.supply_price supplyPrice,
            nprm.product_manager_name productManagerName,
            nprm.product_manager_phone productManagerPhone,
            nprm.product_manager_email productManagerEmail,
            u.partner_name cooperatorName
         from
        new_product_request_manage nprm,
        user_partner u
       where
        nprm.cooperator_id = u.user_id
        <if test="id != null and id !=  ''">
            and nprm.id =#{id}
        </if>
    </select>
</mapper>