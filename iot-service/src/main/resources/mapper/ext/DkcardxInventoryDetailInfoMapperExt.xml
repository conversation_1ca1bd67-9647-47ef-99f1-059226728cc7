<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.DkcardxInventoryDetailInfoMapperExt">
    <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryDetailInfo">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="inventory_main_id" jdbcType="VARCHAR" property="inventoryMainId" />
        <result column="be_id" jdbcType="VARCHAR" property="beId" />
        <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
        <result column="province_alias_name" jdbcType="VARCHAR" property="provinceAliasName" />
        <result column="location" jdbcType="VARCHAR" property="location" />
        <result column="city_name" jdbcType="VARCHAR" property="cityName" />
        <result column="reserve_quatity" jdbcType="INTEGER" property="reserveQuatity" />
        <result column="current_inventory" jdbcType="INTEGER" property="currentInventory" />
        <result column="total_inventory" jdbcType="INTEGER" property="totalInventory" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <select id="listDkcardxInventoryDetailInfo" resultMap="BaseResultMap">
        select *
        from(
        select *
        from
        dkcardx_inventory_detail_info
        where
        (location is null or location = '')
        union
        select *
        from
        dkcardx_inventory_detail_info
        where
        1=1
        <if test="dkcardxInventoryDetailInfoParam.location != null and dkcardxInventoryDetailInfoParam.location != ''">
            and location = #{dkcardxInventoryDetailInfoParam.location}
        </if>
        ) dm
        where
          1=1
        <if test="dkcardxInventoryDetailInfoParam.inventoryMainId != null and dkcardxInventoryDetailInfoParam.inventoryMainId != ''">
            and inventory_main_id = #{dkcardxInventoryDetailInfoParam.inventoryMainId}
        </if>
    </select>

    <select id="getInventoryDetailCount" resultType="com.chinamobile.iot.sc.pojo.dto.InventoryDetailCountDTO">
        select
            inventory_main_id inventoryMainId,
            count(inventory_main_id) detailCount
        from
            dkcardx_inventory_detail_info
        where inventory_main_id = #{inventoryMainId}
        group by inventory_main_id
    </select>

    <select id="listNotBindIdToAtom" resultType="java.lang.String">
        select
            id
        from
            dkcardx_inventory_detail_info
        where id not in (
            select inventory_detail_id from dkcardx_inventory_atom_info
            where atom_id = #{atomId} and inventory_main_id = #{inventoryMainId}
        )
        and inventory_main_id = #{inventoryMainId}
    </select>

</mapper>