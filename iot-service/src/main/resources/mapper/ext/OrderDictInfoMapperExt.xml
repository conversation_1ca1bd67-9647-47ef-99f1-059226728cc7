<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.OrderDictInfoMapperExt">
  <select id="findOrderStatusByTime" parameterType="java.util.Date" resultType="com.chinamobile.iot.sc.pojo.mapper.ValetOrderStatusDO">
    SELECT
        odi.order_id orderId,
        GROUP_CONCAT(CONCAT(odi.valet_order_status,'_',odi.order_status_time)) orderStatusAndTime
    FROM order_dict_info odi
    WHERE odi.order_type = 'valetOrder'
    AND odi.order_status_time <![CDATA[ >= ]]> #{startTime} AND  odi.order_status_time <![CDATA[ <= ]]> #{endTime}
    GROUP BY odi.order_id
  </select>
</mapper>