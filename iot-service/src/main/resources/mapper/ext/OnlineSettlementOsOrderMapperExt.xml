<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.OnlineSettlementOsOrderMapperExt">
  <select id="listOsOrderToOnlineOrder" resultType="com.chinamobile.iot.sc.pojo.dto.OsOrderToOnlineOrderDTO">
    select
      o2ai.order_type orderType,
      o2ai.order_id orderId,
      o2ai.spu_offering_code spuOfferingCode,
      o2ai.sku_offering_code skuOfferingCode,
      o2ai.sku_offering_name skuOfferingName,
      sum(o2ai.sku_quantity) skuQuantity,
      o2i.spu_offering_class spuOfferingClass,
      sum(o2ai.sku_quantity*o2ai.sku_price) totalPrice,
      o2i.be_id beId,
      o2i.location,
      DATE_FORMAT(o2ai.create_time, '%Y-%m-%d %H:%i:%s') orderCreateTime,
      case
      when o2ai.order_status = 7 then (select create_time from order_2c_atom_history where operate_type = 1 and inner_status = 7 and o2ai.id = atom_order_id)
      else null
      end orderSuccessTime,
      0 purchaseStatus,
      ospm.settle_status onlinePurchaseStatus,
      now() createTime,
      now() updateTime
    from
      order_2c_info o2i,
      order_2c_atom_info o2ai,
      sku_offering_info sku,
      online_settlement_province_manage ospm
    where
    o2i.order_id = o2ai.order_id
    and	o2ai.sku_offering_code = sku.offering_code
    and sku.supplier_name = '中移物联网有限公司'
    and o2i.spu_offering_class in ('A08','A09','A10','A15','A16','A17')
    and o2i.special_after_market_handle = 0
    and o2i.be_id = ospm.be_id
    and ((o2ai.order_type = '01' and o2ai.order_status = 7) or (o2ai.order_type in ('00','02','03') and o2ai.order_status in (0,1,2,7)))
    and  not exists (
      select * from online_settlement_os_order where order_id = o2i.order_id
    )
    <if test="orderId != null and orderId !=''">
      and o2ai.order_id = #{orderId}
    </if>
    group by o2ai.order_id
  </select>

  <select id="getOnlineSettlementInfo" resultType="com.chinamobile.iot.sc.pojo.dto.OnlineSettlementInfoDTO">
    select
    ospo.scm_order_num scmOrderNum,
    ospo.settle_status settleStatus
    from
    online_settlement_os_order osoo
    inner join online_settlement_purchase_order ospo on osoo.online_settlement_purchase_order_id = ospo.id
    where
    osoo.order_id = #{orderId}
  </select>
</mapper>