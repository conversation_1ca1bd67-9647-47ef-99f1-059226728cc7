<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.NewProductRequestHandlerInfoMapperExt">

  <select id="listNewProductRequestHandlerInfo" resultType="com.chinamobile.iot.sc.pojo.vo.NewProductRequestHandlerInfoVO">
    SELECT
        request_link requestLink,
        current_handler_user_id currentHandlerUserId,
        current_handler_user_name currentHandlerUserName,
        next_handler_user_id nextHandlerUserId,
        next_handler_user_name nextHandlerUserName,
        CASE
            WHEN handler_status = '01' THEN '发起'
            WHEN handler_status = '02' THEN '通过'
            ELSE '不通过'
        END handlerStatusName,
        handler_remark handlerRemark,
        CASE
            WHEN online_status = '01' THEN '上架'
            WHEN online_status = '02' THEN '下架'
            ELSE ''
        END onlineStatusName,
        create_time createTime,
        update_time updateTime
    FROM
        new_product_request_handler_info
    where 1 = 1
    <if test="flowSourceId != null and flowSourceId != ''">
      and  flow_source_id  = #{flowSourceId}
    </if>
    <if test="flowType != null and flowType != ''">
      and  flow_type  = #{flowType}
    </if>
    order by create_time asc
  </select>
</mapper>