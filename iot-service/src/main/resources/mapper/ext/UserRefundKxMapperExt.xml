<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.UserRefundKxMapperExt">

    <select id="listKxCanChooseUser" resultType="com.chinamobile.iot.sc.pojo.vo.KxCanChooseUserVO">
        select
            u.user_id userId,
            u.name userName,
            u.phone,
            u.role_id roleId,
            r.name roleName
        from
            user u,
            role_info r
        where
            u.role_id = r.id
        and u.is_cancel = 0
        and (u.user_type = 'normal' or u.user_type is null)
        and r.system = 'os'
        and u.role_id != '907921766251245569'
        <if test="kxCanChooseUserParam.userName != null and kxCanChooseUserParam.userName != ''">
            and u.name like '%${kxCanChooseUserParam.userName}%'
        </if>
        <if test="kxCanChooseUserParam.phone != null and kxCanChooseUserParam.phone != ''">
            and u.phone = #{kxCanChooseUserParam.phone}
        </if>
        <if test="kxCanChooseUserParam.noticeType != null ">
            and u.user_id not in (select user_id from user_refund_kx where notice_type = #{kxCanChooseUserParam.noticeType})
        </if>
    </select>

    <select id="listUserRefundKx" resultType="com.chinamobile.iot.sc.pojo.vo.UserRefundKxVO">
        select urk.id,
               urk.notice_type noticeType,
               u.name          userName,
               u.phone,
               r.name          roleName,
               urk.create_time   createTime,
               urk.creator
        from user_refund_kx urk,
             user u,
             role_info r
        where urk.user_id = u.user_id
          and urk.role_id = r.id
        <if test="param.noticeType != null ">
            and urk.notice_type = #{param.noticeType}
        </if>
        <if test="param.userName != null and param.userName != ''">
            and u.name like '%${param.userName}%'
        </if>
        <if test="param.phone != null and param.phone != ''">
            and u.phone = #{param.phone}
        </if>
    </select>

</mapper>