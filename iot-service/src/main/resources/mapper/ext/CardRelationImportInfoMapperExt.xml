<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.CardRelationImportInfoMapperExt">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.vo.CardRelationImportInfoVO">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="import_num" jdbcType="VARCHAR" property="importNum" />
    <result column="device_version" jdbcType="VARCHAR" property="deviceVersion" />
    <result column="import_count" jdbcType="INTEGER" property="importCount" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="created_user" jdbcType="VARCHAR" property="createdUser" />
    <result column="created_user_name" jdbcType="VARCHAR" property="createdUserName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <select id="listCardRelationImport" resultMap="BaseResultMap">
    SELECT
        id,
        import_num,
        device_version,
        import_count,
        be_id,
        created_user,
        created_user_name,
        create_time,
        DATE_FORMAT(create_time,'%Y-%m-%d') createTimeStr
    FROM
        card_relation_import_info
    where 1=1
    <if test="cardRelationImportInfoParam.beginCreateTime != null and cardRelationImportInfoParam.beginCreateTime !=  ''">
      and DATE_FORMAT(create_time,'%Y-%m-%d') <![CDATA[ >= ]]> #{cardRelationImportInfoParam.beginCreateTime}
    </if>
    <if test="cardRelationImportInfoParam.endCreateTime != null and cardRelationImportInfoParam.endCreateTime !=  ''">
      and DATE_FORMAT(create_time,'%Y-%m-%d') <![CDATA[ <= ]]> #{cardRelationImportInfoParam.endCreateTime}
    </if>
    <if test="cardRelationImportInfoParam.createdUserName != null and cardRelationImportInfoParam.createdUserName != ''">
      and created_user_name like '%${cardRelationImportInfoParam.createdUserName}%'
    </if>
    <if test="cardRelationImportInfoParam.importNum != null and cardRelationImportInfoParam.importNum != ''">
      and import_num like '%${cardRelationImportInfoParam.importNum}%'
    </if>
    <if test="cardRelationImportInfoParam.deviceVersion != null and cardRelationImportInfoParam.deviceVersion != ''">
      and device_version like '%${cardRelationImportInfoParam.deviceVersion}%'
    </if>
    <if test="cardRelationImportInfoParam.beId != null and cardRelationImportInfoParam.beId != ''">
      and be_id = #{cardRelationImportInfoParam.beId}
    </if>
      <if test="cardRelationImportInfoParam.beIdList != null and cardRelationImportInfoParam.beIdList.size() != 0">
          and be_id in
          <foreach collection="cardRelationImportInfoParam.beIdList" item="item" index="index" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
    order by create_time desc
  </select>
</mapper>