<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.StandardServiceMapperExt">

    <select id="findByAfterMarketCode" parameterType="java.lang.String" resultType="com.chinamobile.iot.sc.pojo.mapper.StandardServiceDO">
    SELECT
        ss.id code,
        ss.name,
        d.short_name department,
        ss.real_product_name realProductName,
        pp.`name` property
    FROM
        after_market_std_service amss
        JOIN standard_service ss ON ss.id = amss.std_service_id
        JOIN department d ON d.id = ss.product_department_id
        JOIN product_property pp ON pp.id = ss.product_property_id
    WHERE
        amss.after_market_code = #{afterMarketCode}
        and amss.after_market_version = #{afterMarketVersion}
        limit 1
    </select>

    <select id="findByAtomCodeList" parameterType="java.lang.String" resultType="com.chinamobile.iot.sc.pojo.mapper.StandardServiceDO">
     SELECT
        1 as code,
        (ss.name) name,
        (d.short_name) department,
        (ss.real_product_name) realProductName,
        (pp.`name`) property
    FROM
        atom_std_service ass
        join atom_offering_info atom on atom.id = ass.atom_id
        JOIN standard_service ss ON ss.id = ass.std_service_id
        JOIN department d ON d.id = ss.product_department_id
        JOIN product_property pp ON pp.id = ss.product_property_id
    WHERE
        atom.sku_code = #{skuCode}
        and atom.offering_code in
        <foreach close=")" collection="atomCodeList" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>