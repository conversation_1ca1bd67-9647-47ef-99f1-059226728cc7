<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.OrderKxH5MapperExt">
    <select id="getNotHandleKxOrderCount" resultType="java.lang.Integer">
        select
        count(distinct o2i.order_id)
        from
        order_2c_atom_info o2ai
        join order_2c_info o2i on o2ai.order_id = o2i.order_id
        <if test="notHandleKxOrderH5Param.cooperatorIdList != null and notHandleKxOrderH5Param.cooperatorIdList.size() != 0">
            inner join (
            select group_concat(distinct ocr.cooperator_id) cooperator_id,ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            where 1=1
            and ocr.cooperator_id in
            <foreach collection="notHandleKxOrderH5Param.cooperatorIdList" item="cooperatorId" index="index" open="(" close=")" separator=",">
                #{cooperatorId}
            </foreach>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = o2ai.id and cp.order_id = o2ai.order_id
        </if>
        left join sku_offering_info_history skuh on skuh.spu_code = o2ai.spu_offering_code and skuh.offering_code = o2ai.sku_offering_code
        and skuh.spu_offering_version = o2ai.spu_offering_version and skuh.sku_offering_version = o2ai.sku_offering_version and skuh.delete_time is null
        left join service_open_info soi on o2ai.id = soi.atom_order_id and o2i.spu_offering_class = 'A13'
        where o2ai.order_status not in (13,14)
        and o2i.spu_offering_class in ('A06','A07','A11','A13')
        and (
        (o2ai.order_status = 0 and o2i.spu_offering_class in ('A06','A07')) or
        (o2ai.order_status in (0,10,16) and o2i.spu_offering_class = 'A11' and skuh.product_type in
        ('4','5','6','7','8','9','10','11','12')) or
        ((o2ai.soft_service_status = 1 or soi.sync_iot_fail_status in (1,2) ) and o2i.spu_offering_class = 'A13'
        and o2ai.order_status not in (7,8,9))
        )
        <!--<if test="notHandleKxOrderH5Param.cooperatorIdList != null and notHandleKxOrderH5Param.cooperatorIdList.size() != 0">
            and o2ai.cooperator_id in
            <foreach collection="notHandleKxOrderH5Param.cooperatorIdList" item="cooperatorId" index="index" open="(" close=")" separator=",">
                #{cooperatorId}
            </foreach>
        </if>-->
        <if test="notHandleKxOrderH5Param.beId != null and notHandleKxOrderH5Param.beId != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = o2ai.sku_offering_code and (srt.province_code = #{notHandleKxOrderH5Param.beId} or srt.province_code = '000'))
        </if>
        <if test="notHandleKxOrderH5Param.location != null and notHandleKxOrderH5Param.location != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = o2ai.sku_offering_code and srt.city_code =#{notHandleKxOrderH5Param.location})
        </if>
    </select>

    <select id="getNotHandleKxOrderRocCount" resultType="java.lang.Integer">
        select
        count(o2ri.refund_order_id)
        from
        order_2c_roc_info o2ri,
        order_2c_info o2i,
        order_2c_atom_info o2ai
        <if test="notHandleKxOrderH5Param.cooperatorIdList != null and notHandleKxOrderH5Param.cooperatorIdList.size() != 0">
            inner join (
            select group_concat(distinct ocr.cooperator_id) cooperator_id,ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            where 1=1
            and ocr.cooperator_id in
            <foreach collection="notHandleKxOrderH5Param.cooperatorIdList" item="cooperatorId" index="index" open="(" close=")" separator=",">
                #{cooperatorId}
            </foreach>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = o2ai.id and cp.order_id = o2ai.order_id
        </if>
        where
        o2ri.inner_status in (1,7,10)
        and o2ri.order_id = o2ai.order_id
        and o2ri.atom_order_id = o2ai.id
        and o2ai.order_id = o2i.order_id
        and o2i.spu_offering_class in ('A06','A07','A11','A13')
        and o2ai.atom_offering_class in ('X','H','A')
        <!--<if test="notHandleKxOrderH5Param.cooperatorIdList != null and notHandleKxOrderH5Param.cooperatorIdList.size() != 0">
            and o2ri.cooperator_id in
            <foreach collection="notHandleKxOrderH5Param.cooperatorIdList" item="cooperatorId" index="index" open="(" close=")" separator=",">
                #{cooperatorId}
            </foreach>
        </if>-->
        <if test="notHandleKxOrderH5Param.beId != null and notHandleKxOrderH5Param.beId != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = o2ai.sku_offering_code and (srt.province_code = #{notHandleKxOrderH5Param.beId} or srt.province_code = '000'))
        </if>
        <if test="notHandleKxOrderH5Param.location != null and notHandleKxOrderH5Param.location != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = o2ai.sku_offering_code and srt.city_code =#{notHandleKxOrderH5Param.location})
        </if>
    </select>


    <select id="getNotHandleInvoiceCount" resultType="java.lang.Integer">
        select count(1) from (
        select id,cooperator_id,order_id,atom_order_id from apply_invoice_record where status = 1
        union all
        select id,cooperator_id,order_id,atom_order_id from invoice_reverse_record where status = 4
        ) r,
        order_2c_info o2i,
        order_2c_atom_info o2ai
        <if test="notHandleKxOrderH5Param.cooperatorIdList != null and notHandleKxOrderH5Param.cooperatorIdList.size() != 0">
            inner join (
            select group_concat(distinct ocr.cooperator_id) cooperator_id,ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            where 1=1
            and ocr.cooperator_id in
            <foreach collection="notHandleKxOrderH5Param.cooperatorIdList" item="cooperatorId" index="index" open="(" close=")" separator=",">
                #{cooperatorId}
            </foreach>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = o2ai.id and cp.order_id = o2ai.order_id
        </if>
        where 1=1
        and r.order_id = o2i.order_id
        and r.atom_order_id = o2ai.id
        and o2i.order_id = o2ai.order_id
        and o2i.spu_offering_class in ('A06','A07','A11','A13')
        and o2ai.atom_offering_class in ('X','H','A')
        <!--<if test="notHandleKxOrderH5Param.cooperatorIdList != null and notHandleKxOrderH5Param.cooperatorIdList.size() != 0">
            and o2ai.cooperator_id in
            <foreach collection="notHandleKxOrderH5Param.cooperatorIdList" item="cooperatorId" index="index" open="(" close=")" separator=",">
                #{cooperatorId}
            </foreach>
        </if>-->
        <if test="notHandleKxOrderH5Param.beId != null and notHandleKxOrderH5Param.beId != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = o2ai.sku_offering_code and (srt.province_code = #{notHandleKxOrderH5Param.beId} or srt.province_code = '000'))
        </if>
        <if test="notHandleKxOrderH5Param.location != null and notHandleKxOrderH5Param.location != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = o2ai.sku_offering_code and srt.city_code =#{notHandleKxOrderH5Param.location})
        </if>
    </select>

    <select id="listInvoiceInfoH5" resultType="com.chinamobile.iot.sc.pojo.vo.InvoiceInfoH5VO">
        select distinct
        dataFrom,
        id,
        orderSeq,
        cooperatorId,
        url,
        imgUrl,
        status,
        statusName,
        orderId,
        atomOrderId,
        skuOfferingName,
        skuQuantity,
        price,
        createTime,
        frank,
        pName,
        identifyNum,
        addressInfo,
        phoneNumber,
        bankName,
        bankID,
        orderPrice
        from (
        select
        1 dataFrom,
        air.id,
        air.order_seq orderSeq,
        air.cooperator_id cooperatorId,
        spu.url,
        spu.img_url imgUrl,
        air.status,
        case
        when air.status = -1 then '失败'
        when air.status = 0 then '申请开发票'
        when air.status = 1 then '待录入开票'
        when air.status = 2 then '开票成功'
        when air.status = 3 then '冲红申请'
        when air.status = 4 then '待录入冲红信息'
        when air.status = 5 then '完成冲红'
        else '未知状态'
        end statusName,
        air.order_id orderId,
        air.atom_order_id atomOrderId,
        o2ai.sku_offering_name skuOfferingName,
        o2ai.sku_quantity*o2ai.atom_quantity skuQuantity,
        air.order_price price ,
        air.create_time createTime,
        air.frank,
        air.p_name pName,
        air.identify_num identifyNum,
        air.address_info addressInfo,
        air.phone_number phoneNumber,
        air.bank_name bankName,
        air.bank_iD bankID,
        air.order_price  orderPrice
        from
        apply_invoice_record air
        <if test="invoiceInfoH5Param.cooperatorIdList != null and invoiceInfoH5Param.cooperatorIdList.size() != 0">
            inner join (
            select  group_concat(distinct ocr.cooperator_id) cooperator_id,airi.id
            from
            apply_invoice_record airi,
            order_cooperator_relation ocr
            where
            airi.atom_order_id = ocr.atom_order_id
            and airi.order_id = ocr.order_id
            and ocr.cooperator_id in
            <foreach collection="invoiceInfoH5Param.cooperatorIdList" item="cooperatorId" index="index" open="("
                     close=")" separator=",">
                #{cooperatorId}
            </foreach>
            group by airi.id
            ) aip on aip.id = air.id
        </if>,
        order_2c_atom_info o2ai,
        spu_offering_info spu,
        order_2c_info o2i
        where 1=1
        and air.atom_order_id = o2ai.id
        and air.order_id = o2ai.order_id
        and o2ai.spu_offering_code = spu.offering_code
        and air.order_id = o2i.order_id
        and (
        (o2i.spu_offering_class = 'A06' and o2ai.atom_offering_class in ('H')) or
        o2i.spu_offering_class = 'A13'
        )
        <!--<if test="invoiceInfoH5Param.cooperatorIdList != null and invoiceInfoH5Param.cooperatorIdList.size() != 0">
            and air.cooperator_id in
            <foreach collection="invoiceInfoH5Param.cooperatorIdList" item="cooperatorId" index="index" open="("
                     close=")" separator=",">
                #{cooperatorId}
            </foreach>
        </if>-->
        <if test="invoiceInfoH5Param.orderTabStatus != null">
            <if test="invoiceInfoH5Param.orderTabStatus == 1">
                and air.status in (1,4)
            </if>
            <if test="invoiceInfoH5Param.orderTabStatus == 2">
                and air.status in (2,5)
            </if>
        </if>
        union all
        select
        2 dataFrom,
        irr.id,
        irr.order_seq orderSeq,
        irr.cooperator_id cooperatorId,
        spu.url,
        spu.img_url imgUrl,
        irr.status,
        case
        when irr.status = -1 then '失败'
        when irr.status = 0 then '申请开发票'
        when irr.status = 1 then '待录入开票'
        when irr.status = 2 then '开票成功'
        when irr.status = 3 then '冲红申请'
        when irr.status = 4 then '待冲红'
        when irr.status = 5 then '完成冲红'
        else '未知状态'
        end statusName,
        irr.order_id orderId,
        irr.atom_order_id atomOrderId,
        o2ai.sku_offering_name skuOfferingName,
        o2ai.sku_quantity*o2ai.atom_quantity skuQuantity,
        irr.order_price price,
        irr.create_time createTime,
        air.frank,
        air.p_name pName,
        air.identify_num identifyNum,
        air.address_info addressInfo,
        air.phone_number phoneNumber,
        air.bank_name bankName,
        air.bank_iD bankID,
        air.order_price  orderPrice
        from
        invoice_reverse_record irr
        <if test="invoiceInfoH5Param.cooperatorIdList != null and invoiceInfoH5Param.cooperatorIdList.size() != 0">
            inner join (
            select  group_concat(distinct ocr.cooperator_id) cooperator_id,irri.id
            from
            invoice_reverse_record irri,
            order_cooperator_relation ocr
            where
            irri.atom_order_id = ocr.atom_order_id
            and irri.order_id = ocr.order_id
            and ocr.cooperator_id in
            <foreach collection="invoiceInfoH5Param.cooperatorIdList" item="cooperatorId" index="index" open="("
                     close=")" separator=",">
                #{cooperatorId}
            </foreach>
            group by irri.id
            ) ipi on ipi.id = irr.id
        </if>,
        order_2c_atom_info o2ai,
        spu_offering_info spu,
        order_2c_info o2i,
        apply_invoice_record air
        where 1=1
        and irr.atom_order_id = o2ai.id
        and irr.order_id = o2ai.order_id
        and o2ai.spu_offering_code = spu.offering_code
        and irr.order_id = o2i.order_id
        and (
        (o2i.spu_offering_class = 'A06' and o2ai.atom_offering_class in ('H')) or
        o2i.spu_offering_class = 'A13'
        )
        and irr.atom_order_id = air.atom_order_id
        and irr.order_id = air.order_id
        <!--<if test="invoiceInfoH5Param.cooperatorIdList != null and invoiceInfoH5Param.cooperatorIdList.size() != 0">
            and irr.cooperator_id in
            <foreach collection="invoiceInfoH5Param.cooperatorIdList" item="cooperatorId" index="index" open="("
                     close=")" separator=",">
                #{cooperatorId}
            </foreach>
        </if>-->
        <if test="invoiceInfoH5Param.orderTabStatus != null">
            <if test="invoiceInfoH5Param.orderTabStatus == 1">
                and irr.status in (1,4)
            </if>
            <if test="invoiceInfoH5Param.orderTabStatus == 2">
                and irr.status in (2,5)
            </if>
        </if>
        ) rr
        where 1=1
        <if test="invoiceInfoH5Param.orderIdSeqNo != null and invoiceInfoH5Param.orderIdSeqNo !=  ''">
            and (orderSeq like '%${invoiceInfoH5Param.orderIdSeqNo}%' or orderId like
            '%${invoiceInfoH5Param.orderIdSeqNo}%')
        </if>
        order by createTime desc
    </select>

    <select id="listOrderKxH5" resultType="com.chinamobile.iot.sc.pojo.vo.OrderKxH5VO">
        select
        o2ai.id,
        o2ai.order_id orderId,
        o2ai.create_time createTime,
        o2ai.order_status orderStatus,
        spu.url,
        spu.img_url imgUrl,
        spu.offering_name spuOfferingName,
        o2ai.atom_quantity*o2ai.sku_quantity quantity,
        -- o2i.total_price totalPrice
        o2ai.atom_quantity*o2ai.sku_quantity*o2ai.atom_price totalPrice
        from
        order_2c_atom_info o2ai
        <if test="orderKxH5Param.cooperatorIdList != null and orderKxH5Param.cooperatorIdList.size() != 0">
            inner join (
            select group_concat(distinct ocr.cooperator_id) cooperator_id,ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            where 1=1
            and ocr.cooperator_id in
            <foreach collection="orderKxH5Param.cooperatorIdList" item="cooperatorId" index="index" open="(" close=")" separator=",">
                #{cooperatorId}
            </foreach>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = o2ai.id and cp.order_id = o2ai.order_id
        </if>,
        order_2c_info o2i,
        spu_offering_info spu
        where
        o2ai.order_id = o2i.order_id
        and	o2ai.spu_offering_code = spu.offering_code
        and case
        when o2i.spu_offering_class in ('A06','A07') then o2ai.atom_offering_class = 'H'
        when o2i.spu_offering_class = 'A11' then o2ai.atom_offering_class = 'X'
        when o2i.spu_offering_class = 'A13' then o2ai.atom_offering_class in ('A','S')
        else false
        end
        <!--<if test="orderKxH5Param.cooperatorIdList != null and orderKxH5Param.cooperatorIdList.size() != 0">
            and o2ai.cooperator_id in
            <foreach collection="orderKxH5Param.cooperatorIdList" item="cooperatorId" index="index" open="(" close=")" separator=",">
                #{cooperatorId}
            </foreach>
        </if>-->

        <if test="orderKxH5Param.orderText != null and orderKxH5Param.orderText !=  ''">
            and (o2ai.order_id like '%${orderKxH5Param.orderText}%' || spu.offering_name like '%${orderKxH5Param.orderText}%')
        </if>

        <if test="orderKxH5Param.orderTabStatus != null">
            <if test="orderKxH5Param.orderTabStatus == 1">
                and o2ai.order_status = 0
            </if>
            <if test="orderKxH5Param.orderTabStatus == 2">
                and o2ai.order_status != 0
            </if>
        </if>
        <if test="orderKxH5Param.beId != null and orderKxH5Param.beId != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = o2ai.sku_offering_code and (srt.province_code = #{beId} or srt.province_code = '000'))
        </if>
        <if test="orderKxH5Param.location != null and orderKxH5Param.location != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = o2ai.sku_offering_code and srt.city_code =#{location})
        </if>
        order by o2i.update_time desc
    </select>

    <select id="listOrderRocKxH5" resultType="com.chinamobile.iot.sc.pojo.vo.OrderRocKxH5VO">
        select
        o2ri.refund_order_id refundOrderId,
        o2ri.order_id orderId,
        date_format(o2ri.create_time,'%Y-%m-%d %H:%i:%s') createTime,
        o2ri.inner_status rocStatus,
        spu.url,
        spu.img_url imgUrl,
        spu.offering_name spuOfferingName,
        case
        when o2ai.part_return=1 then o2ri.refunds_number
        else
        o2ri.refunds_number*o2ai.atom_quantity
        end refundsNumber,
        case
        when o2ai.part_return=1 then o2ri.refunds_number *o2ai.atom_price
        else
        o2ri.refunds_number*o2ai.atom_quantity*o2ai.atom_price
        end returnPrice
        from
        order_2c_roc_info o2ri,
        order_2c_atom_info o2ai
        <if test="orderRocKxH5Param.cooperatorIdList != null and orderRocKxH5Param.cooperatorIdList.size() != 0">
            inner join (
            select group_concat(distinct ocr.cooperator_id) cooperator_id,ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            where 1=1
            and ocr.cooperator_id in
            <foreach collection="orderRocKxH5Param.cooperatorIdList" item="cooperatorId" index="index" open="(" close=")" separator=",">
                #{cooperatorId}
            </foreach>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = o2ai.id and cp.order_id = o2ai.order_id
        </if>,
        spu_offering_info spu,
        order_2c_atom_info o2aiC
        where
        o2ri.order_id = o2ai.order_id
        and o2ri.atom_order_id = o2ai.id
        and	o2ai.spu_offering_code = spu.offering_code
        and o2ai.atom_offering_class = 'X'
        and o2ai.order_id = o2aiC.order_id
        and o2ai.spu_offering_code = o2aiC.spu_offering_code
        and o2ai.sku_offering_code = o2aiC.sku_offering_code
        and o2aiC.atom_offering_class = 'C'
        <!--<if test="orderRocKxH5Param.cooperatorIdList != null and orderRocKxH5Param.cooperatorIdList.size() != 0">
            and o2ri.cooperator_id in
            <foreach collection="orderRocKxH5Param.cooperatorIdList" item="cooperatorId" index="index" open="(" close=")" separator=",">
                #{cooperatorId}
            </foreach>
        </if>-->

        <if test="orderRocKxH5Param.orderText != null and orderRocKxH5Param.orderText !=  ''">
            and (o2ri.order_id like '%${orderRocKxH5Param.orderText}%' || spu.offering_name like '%${orderRocKxH5Param.orderText}%')
        </if>

        <if test="orderRocKxH5Param.orderRocTabStatus != null">
            <if test="orderRocKxH5Param.orderRocTabStatus == 1">
                and o2ri.inner_status = 1
            </if>
            <if test="orderRocKxH5Param.orderRocTabStatus == 2">
                and o2ri.inner_status != 1
            </if>
        </if>
        order by o2ri.update_time desc
    </select>

</mapper>