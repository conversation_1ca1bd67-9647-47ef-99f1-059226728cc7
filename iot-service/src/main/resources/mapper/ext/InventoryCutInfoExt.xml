<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.InventoryCutInfoMapperExt">

    <select id="getInventoryCutInfoPage" resultType="com.chinamobile.iot.sc.pojo.vo.InventoryCutListVO">
       select ici.inventory_pattern_name inventoryPatternName,
              ici.cut_time cutTime,
              ici.operator operator
              from
           inventory_cut_info ici
        where 1=1
        <if test="inventoryPatternName != null and inventoryPatternName != ''">
            and ici.inventory_pattern_name = #{inventoryPatternName}
        </if>
        <if test="startTime != null ">
            and ici.cut_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null ">
            and ici.cut_time <![CDATA[ <= ]]> #{endTime}
        </if>
        order by cut_time desc
    </select>

</mapper>