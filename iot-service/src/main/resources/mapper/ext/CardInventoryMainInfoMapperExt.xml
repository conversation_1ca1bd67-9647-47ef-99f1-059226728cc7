<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.CardInventoryMainInfoMapperExt">
 <select id="listCardInventoryMainInfo" resultType="com.chinamobile.iot.sc.pojo.vo.CardInventoryInfoVO">
  SELECT
  cimi.id,
  cimi.cust_code custCode,
  cimi.cust_name custName,
  cimi.template_id templateId,
  cimi.template_name templateName,
  cimi.be_id beId,
  cimi.provice_name proviceName,
  cimi.region_id regionId,
  cimi.region_name regionName,
  cimi.card_type cardType,
     case
     when cimi.card_type = '0'  then '插拔卡'
     when cimi.card_type = '1'  then '贴片卡'
     when cimi.card_type = '3'  then 'M2M芯片空写卡'
     end cardTypeName,
  cimi.reserve_quatity reserveQuatity,
  cimi.current_inventory currentInventory,
  cimi.total_inventory totalInventory,
  cimi.inventory_threshold inventoryThreshold,
     case
     when cimi.inventory_status = '0'  then '短缺'
     when cimi.inventory_status = '1'  then '充足'
     end inventoryStatus
  FROM
   card_inventory_main_info cimi
    WHERE 1=1
    <if test="cardInfoParam.cardStatus != null and cardInfoParam.cardStatus != ''">
     and cimi.card_status = #{cardInfoParam.cardStatus}
    </if>
    <if test="cardInfoParam.beId != null and cardInfoParam.beId != ''">
     and cimi.be_id = #{cardInfoParam.beId}
    </if>
    <if test="cardInfoParam.regionId != null and cardInfoParam.regionId != ''">
     and cimi.region_id = #{cardInfoParam.regionId}
    </if>
    <if test="cardInfoParam.cardType != null and cardInfoParam.cardType != ''">
     and cimi.card_type = #{cardInfoParam.cardType}
    </if>

    <if test="cardInfoParam.pageTemplateName != null and cardInfoParam.pageTemplateName != ''">
     and cimi.template_name like '%${cardInfoParam.pageTemplateName}%'
    </if>
    <if test="cardInfoParam.templateName != null and cardInfoParam.templateName != ''">
     and cimi.template_name like '%${cardInfoParam.templateName}%'
    </if>
     <if test="cardInfoParam.templateId != null and cardInfoParam.templateId != ''">
         and cimi.template_id = #{cardInfoParam.templateId}
     </if>
    <if test="cardInfoParam.cardStatusList != null and cardInfoParam.cardStatusList.size() !=0 ">
     and cimi.card_status in
     <foreach collection="cardInfoParam.cardStatusList" item="item" index="index" open="(" close=")" separator=",">
      #{item}
     </foreach>
    </if>
    <if test="cardInfoParam.beIdList != null and cardInfoParam.beIdList.size() != 0">
     and cimi.be_id in
     <foreach collection="cardInfoParam.beIdList" item="item" index="index" open="(" close=")" separator=",">
      #{item}
     </foreach>
    </if>
    <if test="cardInfoParam.cardTypeList != null and cardInfoParam.cardTypeList.size() != 0">
     and cimi.card_type in
     <foreach collection="cardInfoParam.cardTypeList" item="item" index="index" open="(" close=")" separator=",">
      #{item}
     </foreach>
    </if>
    <if test="cardInfoParam.custCode != null and cardInfoParam.custCode != ''">
     and cimi.cust_code = #{cardInfoParam.custCode}
    </if>
     <if test="cardInfoParam.custName != null and cardInfoParam.custName != ''">
         and cimi.cust_name like '%${cardInfoParam.custName}%'
     </if>
    order by cimi.create_time desc
 </select>
 <select id="queryCardInfoByInventoryId" resultType="com.chinamobile.iot.sc.pojo.vo.CardInfoByInventoryVO">
  select
   cms.msisdn,
     case
     when cms.card_status = '1'  then '未销售'
     when cms.card_status = '2'  then '销售中'
     when cms.card_status = '3'  then '已销售'
     when cms.card_status = '4'  then '销售失败'
     when cms.card_status = '9'  then '不可销售'
     end cardStatus
  from
   card_mall_sync cms where 1=1
   and cms.card_status in (1,2)
  and cms.card_inventory_main_id =#{param.cardInventoryMainId}
  <if test="param.cardStatus != null and param.cardStatus != ''">
   and cms.card_status = #{param.cardStatus}
  </if>
  <if test="param.msisdn != null and param.msisdn != ''">
   and cms.msisdn = #{param.msisdn}
  </if>
  order by cms.create_time desc
 </select>
    <select id="listAtomCardInventoryMainInfo" resultType="com.chinamobile.iot.sc.pojo.vo.CardInventoryInfoVO">
        SELECT
        cimi.id,
        cimi.cust_code custCode,
        cimi.cust_name custName,
        cimi.template_id templateId,
        cimi.template_name templateName,
        cimi.be_id beId,
        cimi.provice_name proviceName,
        cimi.region_id regionId,
        cimi.region_name regionName,
        cimi.card_type cardType,
        case
        when cimi.card_type = '0'  then '插拔卡'
        when cimi.card_type = '1'  then '贴片卡'
        when cimi.card_type = '3'  then 'M2M芯片空写卡'
        end cardTypeName,
        cimi.reserve_quatity reserveQuatity,
        cimi.current_inventory currentInventory,
        cimi.total_inventory totalInventory,
        cimi.inventory_threshold inventoryThreshold,
        case
        when cimi.inventory_status = '0'  then '短缺'
        when cimi.inventory_status = '1'  then '充足'
        end inventoryStatus,
        ciai.atom_inventory atomInventory
        FROM
        card_inventory_main_info cimi
        left join card_inventory_atom_info ciai on ciai.card_inventory_main_id =cimi.id
        WHERE 1=1
        <if test="atomId != null and atomId != ''">
            and ciai.atom_id = #{atomId}
        </if>
        <if test="cardInventoryMainId != null and cardInventoryMainId != ''">
            and cimi.id = #{cardInventoryMainId}
        </if>
        order by cimi.create_time desc
    </select>
</mapper>