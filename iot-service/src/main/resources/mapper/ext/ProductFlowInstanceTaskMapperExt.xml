<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.ProductFlowInstanceTaskMapperExt">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.ProductFlowInstanceTask">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Mar 15 14:24:01 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="flow_instance_id" jdbcType="VARCHAR" property="flowInstanceId" />
    <result column="flow_id" jdbcType="VARCHAR" property="flowId" />
    <result column="step_id" jdbcType="VARCHAR" property="stepId" />
    <result column="step_name" jdbcType="VARCHAR" property="stepName" />
    <result column="assignee_id" jdbcType="VARCHAR" property="assigneeId" />
    <result column="assignee_name" jdbcType="VARCHAR" property="assigneeName" />
    <result column="options" jdbcType="VARCHAR" property="options" />
    <result column="next_assignee_id" jdbcType="VARCHAR" property="nextAssigneeId" />
    <result column="handle_status" jdbcType="INTEGER" property="handleStatus" />
    <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="getTaskListByStepIndexDesc" parameterType="java.lang.String" resultMap="BaseResultMap">
  SELECT task.*
  FROM product_flow_instance_task task
  LEFT JOIN product_flow_step step ON task.step_id = step.id
  WHERE task.flow_instance_id = #{flowInstanceId}
  order by create_time desc,step.step_index desc
  </select>

</mapper>