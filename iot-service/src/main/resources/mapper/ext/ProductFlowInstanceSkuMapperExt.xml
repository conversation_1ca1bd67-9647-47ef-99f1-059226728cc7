<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.ProductFlowInstanceSkuMapperExt">
    <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.dto.ProductFlowInstanceSkuDTO">
        <!--
          WARNING - @mbg.generated  This element was generated on Mon Mar 11 15:02:25 CST 2024. by MyBatis Generator, do not modify.
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="flow_instance_id" jdbcType="VARCHAR" property="flowInstanceId"/>
        <result column="flow_id" jdbcType="VARCHAR" property="flowId"/>
        <result column="spu_code" jdbcType="VARCHAR" property="spuCode"/>
        <result column="sku_code" jdbcType="VARCHAR" property="skuCode"/>
        <result column="sku_name" jdbcType="VARCHAR" property="skuName"/>
        <result column="sku_short_name" jdbcType="VARCHAR" property="skuShortName"/>
        <result column="key_compoment_name" jdbcType="VARCHAR" property="keyCompomentName"/>
        <result column="key_component_service_info" jdbcType="VARCHAR" property="keyComponentServiceInfo"/>
        <result column="sale_price" jdbcType="BIGINT" property="salePrice"/>
        <result column="sale_min_price" jdbcType="BIGINT" property="saleMinPrice"/>
        <result column="sale_max_price" jdbcType="BIGINT" property="saleMaxPrice"/>
        <result column="sale_out_of_price_range" jdbcType="VARCHAR" property="saleOutOfPriceRange"/>
        <result column="sale_province_city" jdbcType="VARCHAR" property="saleProvinceCity"/>
        <result column="delivery_range" jdbcType="VARCHAR" property="deliveryRange"/>
        <result column="tourist_partner_visible" jdbcType="VARCHAR" property="touristPartnerVisible"/>
        <result column="standard_product_name" jdbcType="VARCHAR" property="standardProductName"/>
        <result column="standard_product_attribute" jdbcType="VARCHAR" property="standardProductAttribute"/>
        <result column="sku_service_provider" jdbcType="VARCHAR" property="skuServiceProvider"/>
        <result column="manage_department" jdbcType="VARCHAR" property="manageDepartment"/>
        <result column="standard_product_manager" jdbcType="VARCHAR" property="standardProductManager"/>
        <result column="receive_order_account" jdbcType="VARCHAR" property="receiveOrderAccount"/>
        <result column="deliver_account" jdbcType="VARCHAR" property="deliverAccount"/>
        <result column="aftermarket_account" jdbcType="VARCHAR" property="aftermarketAccount"/>
        <result column="sku_remark" jdbcType="VARCHAR" property="skuRemark"/>
        <result column="province_price" jdbcType="BIGINT" property="provincePrice"/>
        <result column="product_package_sale_name" jdbcType="VARCHAR" property="productPackageSaleName"/>
        <result column="product_package_service_content" jdbcType="VARCHAR" property="productPackageServiceContent"/>
        <result column="send_contact_person" jdbcType="VARCHAR" property="sendContactPerson"/>
        <result column="has_remuneration" jdbcType="VARCHAR" property="hasRemuneration"/>
        <result column="remuneration_percent" jdbcType="VARCHAR" property="remunerationPercent"/>
        <result column="cooperate_company" jdbcType="VARCHAR" property="cooperateCompany"/>
        <result column="order_master_handler" jdbcType="VARCHAR" property="orderMasterHandler"/>
        <result column="order_slave_handler" jdbcType="VARCHAR" property="orderSlaveHandler"/>
        <result column="min_purchase_num" jdbcType="INTEGER" property="minPurchaseNum"/>
        <result column="shelf_status" jdbcType="INTEGER" property="shelfStatus"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="operate_type" jdbcType="INTEGER" property="operateType"/>
    </resultMap>

    <select id="listProductFlowInstanceSkuOnLine" resultMap="BaseResultMap">
       select pfik.id, pfik.flow_instance_id, pfik.flow_id, pfik.spu_code, pfik.sku_code,
            pfik.sku_name, pfik.sku_short_name, pfik.key_compoment_name,
            pfik.key_component_service_info, pfik.sale_price, pfik.sale_min_price, pfik.sale_max_price,
            pfik.sale_out_of_price_range,pfik.sale_province_city, pfik.delivery_range, pfik.tourist_partner_visible,
            pfik.standard_product_name,
            pfik.standard_product_attribute, pfik.sku_service_provider, pfik.manage_department, pfik.standard_product_manager,
            pfik.receive_order_account, pfik.deliver_account, pfik.aftermarket_account, pfik.sku_remark,
            pfik.province_price,
            pfik.product_package_sale_name, pfik.product_package_service_content, pfik.send_contact_person,
            pfik.has_remuneration, pfik.remuneration_percent, pfik.cooperate_company, pfik.order_master_handler,
            pfik.order_slave_handler, pfik.min_purchase_num, pfik.shelf_status, pfik.create_time, pfik.update_time,
            pf.operate_type operateType
        from
          product_flow_instance_sku pfik,
          product_flow pf,
          product_flow_instance pfi
        where 1=1
        and pfik.flow_id = pf.id
        and pfik.flow_instance_id = pfi.id
        <if test="productFlowSkuParam.spuCode != null and productFlowSkuParam.spuCode != ''">
            and pfik.spu_code = #{productFlowSkuParam.spuCode}
        </if>

        <if test="productFlowSkuParam.skuCode != null and productFlowSkuParam.skuCode != ''">
            and pfik.sku_code = #{productFlowSkuParam.skuCode}
        </if>

        <if test="productFlowSkuParam.spuSkuCodeOrName != null and productFlowSkuParam.spuSkuCodeOrName != ''">
            and (pfik.spu_code like '%${productFlowSkuParam.spuSkuCodeOrName}%' or pfik.sku_code like '%${productFlowSkuParam.spuSkuCodeOrName}%' or pfik.sku_name like '%${productFlowSkuParam.spuSkuCodeOrName}%')
        </if>

        <if test="productFlowSkuParam.operateType != null and productFlowSkuParam.operateType != ''">
            and pf.operate_type = #{productFlowSkuParam.operateType}
        </if>

        <if test="productFlowSkuParam.flowInstanceStatus != null">
            and pfi.status = #{productFlowSkuParam.flowInstanceStatus}
        </if>

        <if test="productFlowSkuParam.shelfStatusList != null and productFlowSkuParam.shelfStatusList.size() != 0">
            and pfik.shelf_status in
            <foreach collection="productFlowSkuParam.shelfStatusList" item="shelfStatus" index="index" open="(" close=")" separator=",">
                #{shelfStatus}
            </foreach>
        </if>

        <if test="productFlowSkuParam.flowTypeList != null and productFlowSkuParam.flowTypeList.size() != 0">
            and pf.flow_type in
            <foreach collection="productFlowSkuParam.flowTypeList" item="flowType" index="index" open="(" close=")" separator=",">
                #{flowType}
            </foreach>
        </if>
    </select>

</mapper>