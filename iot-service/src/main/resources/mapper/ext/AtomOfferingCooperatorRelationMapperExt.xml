<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.AtomOfferingCooperatorRelationMapperExt">

  <select id="listCooperatorInfoByGroup" resultType="com.chinamobile.iot.sc.pojo.dto.AtomCooperatorInfoByGroupDTO">
    select
      GROUP_CONCAT(distinct up.partner_name) partnerName,
      group_concat(distinct up.name) userName,
      group_concat(distinct aocr.cooperator_id) cooperatorId,
      aocr.atom_offering_id atomOfferingId
    from
      atom_offering_cooperator_relation aocr,
      user_partner up
    where
        aocr.cooperator_id = up.user_id

    <if test="atomCooperatorInfoByGroupParam.atomOfferingId != null and atomCooperatorInfoByGroupParam.atomOfferingId != ''">
      and aocr.atom_offering_id =  #{atomCooperatorInfoByGroupParam.atomOfferingId}
    </if>
    <if test="atomCooperatorInfoByGroupParam.userName != null and atomCooperatorInfoByGroupParam.userName != ''">
      and up.name like '%${atomCooperatorInfoByGroupParam.userName}%'
    </if>
    <if test="atomCooperatorInfoByGroupParam.partnerName != null and atomCooperatorInfoByGroupParam.partnerName != ''">
      and up.partner_name like '%${atomCooperatorInfoByGroupParam.partnerName}%'
    </if>
    <if test="atomCooperatorInfoByGroupParam.cooperatorId != null and atomCooperatorInfoByGroupParam.cooperatorId != ''">
      and aocr.cooperator_id = #{atomCooperatorInfoByGroupParam.cooperatorId}
    </if>

      group by aocr.atom_offering_id
  </select>

  <select id="listCooperatorInfo" resultType="com.chinamobile.iot.sc.pojo.dto.AtomCooperatorInfoDTO">
    select distinct
      up.partner_name partnerName,
      up.name userName,
      aocr.cooperator_id cooperatorId,
      aocr.atom_offering_id atomOfferingId
    from
      atom_offering_cooperator_relation aocr,
      user_partner up
    where
      aocr.cooperator_id = up.user_id
    <if test="atomCooperatorInfoParam.atomOfferingId != null and atomCooperatorInfoParam.atomOfferingId != ''">
      and aocr.atom_offering_id =  #{atomCooperatorInfoParam.atomOfferingId}
    </if>
    <if test="atomCooperatorInfoParam.userName != null and atomCooperatorInfoParam.userName != ''">
      and up.name like '%${atomCooperatorInfoParam.userName}%'
    </if>
    <if test="atomCooperatorInfoParam.partnerName != null and atomCooperatorInfoParam.partnerName != ''">
      and up.partner_name like '%${atomCooperatorInfoParam.partnerName}%'
    </if>
    <if test="atomCooperatorInfoParam.cooperatorId != null and atomCooperatorInfoParam.cooperatorId != ''">
      and aocr.cooperator_id = #{atomCooperatorInfoParam.cooperatorId}
    </if>
  </select>

</mapper>