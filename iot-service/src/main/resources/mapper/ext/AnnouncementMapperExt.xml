<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.AnnouncementMapperExt">
  <select id="announcementList" parameterType="com.chinamobile.iot.sc.pojo.param.AnnouncementListParam" resultType="com.chinamobile.iot.sc.pojo.mapper.AnnouncementDO">
    SELECT
      a.id,
      a.title,
      a.content,
      a.`status`,
      u.`name` creatorName,
      a.popup,
      a.end_time endTime,
      a.create_time createTime
    FROM
      announcement a
    left JOIN `user` u ON a.creator_id = u.user_id
    WHERE 1=1
    <if test="param.keyWord != null and param.keyWord != ''">
      and (a.title like concat('%',#{param.keyWord},'%') or a.content like concat('%',#{param.keyWord},'%'))
    </if>
    <if test="param.id != null and param.id != ''">
      and a.id = #{param.id}
    </if>
    <if test="param.userId != null and param.userId != ''">
      and a.creator_id = #{param.userId}
    </if>
    <if test="param.createStartTime != null">
     and a.create_time <![CDATA[ >= ]]> STR_TO_DATE(#{param.createStartTime}, '%Y-%m-%d %H:%i:%s')
    </if>
    <if test="param.createEndTime != null">
      and a.create_time <![CDATA[ <= ]]> STR_TO_DATE(#{param.createEndTime}, '%Y-%m-%d %H:%i:%s')
    </if>
    <if test="param.status != null">
      and a.`status` = #{param.status}
    </if>
    order by a.update_time DESC
  </select>
</mapper>