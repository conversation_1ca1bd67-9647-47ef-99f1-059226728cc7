<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.OrderCooperatorRelationHistoryMapperExt">

  <select id="listCooperatorInfoHistoryByGroup" resultType="com.chinamobile.iot.sc.pojo.dto.OrderCooperatorInfoHistoryByGroupDTO">
    select
      GROUP_CONCAT(distinct up.partner_name) partnerName,
      group_concat(distinct up.name) userName,
      group_concat(distinct ocrh.cooperator_id) cooperatorId,
      ocrh.atom_order_id atomOrderId,
      ocrh.order_id orderId
    from order_cooperator_relation_history ocrh
    inner JOIN user_partner up on up.user_id = ocrh.cooperator_id
    where 1=1
    <if test="orderCooperatorInfoHistoryByGroupParam.atomOrderId != null and orderCooperatorInfoHistoryByGroupParam.atomOrderId != ''">
      and ocrh.atom_order_id =  #{orderCooperatorInfoHistoryByGroupParam.atomOrderId}
    </if>
    <if test="orderCooperatorInfoHistoryByGroupParam.orderId != null and orderCooperatorInfoHistoryByGroupParam.orderId != ''">
      and ocrh.order_id =  #{orderCooperatorInfoHistoryByGroupParam.orderId}
    </if>
    <if test="orderCooperatorInfoHistoryByGroupParam.userName != null and orderCooperatorInfoHistoryByGroupParam.userName != ''">
      and up.name like '%${orderCooperatorInfoHistoryByGroupParam.userName}%'
    </if>
    <if test="orderCooperatorInfoHistoryByGroupParam.partnerName != null and orderCooperatorInfoHistoryByGroupParam.partnerName != ''">
      and up.partner_name like '%${orderCooperatorInfoHistoryByGroupParam.partnerName}%'
    </if>
    <if test="orderCooperatorInfoHistoryByGroupParam.cooperatorId != null and orderCooperatorInfoHistoryByGroupParam.cooperatorId != ''">
      and aocr.cooperator_id = #{orderCooperatorInfoHistoryByGroupParam.cooperatorId}
    </if>
    
    group by ocrh.atom_order_id
  </select>

  <select id="listCooperatorInfoHistory" resultType="com.chinamobile.iot.sc.pojo.dto.OrderCooperatorInfoHistoryDTO">
    select distinct
      up.partner_name partnerName,
      up.name userName,
      ocrh.cooperator_id cooperatorId,
      ocrh.atom_order_id atomOrderId,
      ocrh.order_id orderId
    from order_cooperator_relation_history ocrh
    inner JOIN user_partner up on up.user_id = ocrh.cooperator_id
    where 1=1
    <if test="orderCooperatorInfoHistoryParam.atomOrderId != null and orderCooperatorInfoHistoryParam.atomOrderId != ''">
      and ocrh.atom_order_id =  #{orderCooperatorInfoHistoryParam.atomOrderId}
    </if>
    <if test="orderCooperatorInfoHistoryParam.orderId != null and orderCooperatorInfoHistoryParam.orderId != ''">
      and ocrh.order_id =  #{orderCooperatorInfoHistoryParam.orderId}
    </if>
    <if test="orderCooperatorInfoHistoryParam.userName != null and orderCooperatorInfoHistoryParam.userName != ''">
      and up.name like '%${orderCooperatorInfoHistoryParam.userName}%'
    </if>
    <if test="orderCooperatorInfoHistoryParam.partnerName != null and orderCooperatorInfoHistoryParam.partnerName != ''">
      and up.partner_name like '%${orderCooperatorInfoHistoryParam.partnerName}%'
    </if>
    <if test="orderCooperatorInfoHistoryParam.cooperatorId != null and orderCooperatorInfoHistoryParam.cooperatorId != ''">
      and aocr.cooperator_id = #{orderCooperatorInfoHistoryParam.cooperatorId}
    </if>
    <if test="orderCooperatorInfoHistoryParam.atomOrderIdList != null and orderCooperatorInfoHistoryParam.atomOrderIdList.size() != 0">
      and ocrh.atom_order_id in
      <foreach collection="orderCooperatorInfoHistoryParam.atomOrderIdList" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
  </select>

</mapper>