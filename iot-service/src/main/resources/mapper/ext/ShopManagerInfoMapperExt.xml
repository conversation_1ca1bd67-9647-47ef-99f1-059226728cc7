<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.ShopManagerInfoMapperExt">
  <select id="getShopUserRepetitionHistoryList" resultType="com.chinamobile.iot.sc.response.web.ShopUserRepetitionDTO">
    select id, create_oper_code createOperCode,max(id) maxId
    from shop_manager_info_history
    where 1=1 and opr_type ='1'
    group by BINARY create_oper_code having count(*) >1 order by cust_status_time desc;
  </select>


  <select id="getShopUserRepetitionList" resultType="com.chinamobile.iot.sc.response.web.ShopUserRepetitionDTO">
    select id, create_oper_code createOperCode,max(id) maxId
    from shop_manager_info
    where 1=1
    group by BINARY create_oper_code having count(*) >1 order by register_date desc;
  </select>


  <select id="getShopUserCustomerHistoryList" resultType="com.chinamobile.iot.sc.response.web.ShopUserCustomerDTO">
    select id, cust_code custCode,min(id) maxId
    from shop_customer_info_history
    where 1=1 and opr_type ='1'
    group by BINARY cust_code,cust_id having count(*) >1 order by cust_status_time asc;
  </select>


  <select id="getShopUserCustomerList" resultType="com.chinamobile.iot.sc.response.web.ShopUserCustomerDTO">
    select id, cust_code custCode,min(id) maxId
    from shop_customer_info
    where 1=1
    group by BINARY cust_code,cust_id having count(*) >1 order by register_date asc;
  </select>

</mapper>