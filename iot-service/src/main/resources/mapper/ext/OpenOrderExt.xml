<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.OpenOrderMapperExt">

    <select id="listCyzq" resultType="com.chinamobile.iot.sc.pojo.dto.CyzqListDTO">
        SELECT
            oa.order_id AS orderId,
            oa.create_time AS createTime,
            oa.order_status AS orderStatus,
            oi.order_type orderType,
            si.offering_name AS spuOfferingName,
            oa.sku_offering_name AS skuOfferingName,
            si.offering_code AS spuOfferingCode,
            oa.sku_offering_code AS skuOfferingCode,
            oi.spu_offering_class AS spuOfferingClass,
            oa.sku_quantity  skuQuantity,
            oa.atom_offering_class AS atomOfferingClass,
            oa.atom_offering_name AS atomOfferingName,
            oa.atom_offering_code AS atomOfferingCode,
            oa.atom_quantity * oa.sku_quantity AS atomQuantity,
            atom.unit atomUnit,
            oi.contact_person_name contactPersonName,
            oi.contact_phone contactPhone,
            oi.addr1,
            oi.addr2,
            oi.addr3,
            oi.addr4,
            oi.usaddr,
            oi.cust_code custCode
        FROM
            order_2c_atom_info oa
            inner JOIN order_2c_info oi ON oa.order_id = oi.order_id
            inner JOIN atom_offering_info atom ON atom.offering_code = oa.atom_offering_code AND atom.spu_code = oa.spu_offering_code AND atom.sku_code = oa.sku_offering_code
            LEFT JOIN spu_offering_info_history si ON si.offering_code = oa.spu_offering_code
            AND si.spu_offering_version = oa.spu_offering_version
        where 1=1
           and oa.sku_offering_code in ('1000143654','1000152737','1000155242','1000148483')
        <if test="cyzqListParam.startTime != null and cyzqListParam.startTime != ''">
            and oa.create_time <![CDATA[ >= ]]> #{cyzqListParam.startTime}
        </if>
        <if test="cyzqListParam.endTime != null and cyzqListParam.endTime != ''">
            and oa.create_time <![CDATA[ <= ]]> #{cyzqListParam.endTime}
        </if>
        ORDER BY oa.create_time DESC
    </select>

</mapper>
