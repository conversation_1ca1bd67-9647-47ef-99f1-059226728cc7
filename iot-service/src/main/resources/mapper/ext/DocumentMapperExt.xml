<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.DocumentMapperExt">
 <select id="getDocumentList" parameterType="com.chinamobile.iot.sc.pojo.param.DocumentListParam" resultType="com.chinamobile.iot.sc.pojo.vo.DocumentListVO">
  select
      d.id,
      d.name,
      group_concat(distinct dvr.visible_range) visibleRange,
      d.update_user_id updateUserName,
      d.update_time updateTime
      from document d
        inner join document_visible_range dvr on dvr.document_id = d.id
  where 1=1
   <if test="param.name != null and param.name != ''">
     and d.name like concat('%',#{param.name},'%')
   </if>
   <if test="param.roleId != null and param.roleId != ''">
     and dvr.visible_range = #{param.roleId}
   </if>
   <if test="param.createStartTime != null">
     and d.create_time <![CDATA[ >= ]]> STR_TO_DATE(#{param.createStartTime}, '%Y-%m-%d %H:%i:%s')
   </if>
   <if test="param.createEndTime != null">
     and d.create_time <![CDATA[ <= ]]>  STR_TO_DATE(#{param.createEndTime}, '%Y-%m-%d %H:%i:%s')
   </if>
     group by d.id
   order by d.create_time DESC
 </select>
</mapper>