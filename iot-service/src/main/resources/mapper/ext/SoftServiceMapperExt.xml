<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.SoftServiceMapperExt">
    <update id="updateSoftServiceIotStatus">
        UPDATE service_open_info
        SET sync_iot_fail_status = #{iotStatus}, sync_iot_fail_reson = #{failReason}
        WHERE order_id = #{orderId}
    </update>
    <update id="updateSoftServiceOpenStatusByOrderId">
        UPDATE service_open_info
        set soft_service_open_time=now(), soft_service_open_status=0,soft_service_use_time=now()
        where order_id=#{orderId}
    </update>
</mapper>