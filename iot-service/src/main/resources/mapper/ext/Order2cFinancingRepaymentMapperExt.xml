<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.Order2cFinancingRepaymentMapperExt">
  <select id="repaymentList" parameterType="java.lang.String" resultType="com.chinamobile.iot.sc.pojo.mapper.FinancingRepaymentListDO">
      SELECT
      t.id,
      t.trade_no orderCode,
      t.seller_name provinceCompanyName,
      t.contract_num provinceCompanyContract,
      oc.partner_name partnerName,
      t.pay_amount payAmount,
      IFNULL(fr.repay_amount,0) repayAmount,
      IF(fr.outstanding_amount is null,t.pay_amount,fr.outstanding_amount) outstandingAmount
      FROM
      trade_order_info t
      JOIN (
      select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
      ob.trade_no
      from
      order_baoli ob,order_cooperator_relation ocr,user_partner up
      where
      ob.order_atom_info_id = ocr.atom_order_id
      and ob.order_id = ocr.order_id
      and ocr.cooperator_id = up.user_id
      <if test="partnerIdList != null">
          and ocr.cooperator_id in
          <foreach collection="partnerIdList" item="userId" index="index" open="(" close=")" separator=",">
              #{userId}
          </foreach>
      </if>
      <if test="partnerName != null and partnerName != ''">
          and up.partner_name like concat ('%',#{partnerName},'%')
      </if>
      group by ob.trade_no
      ) oc on oc.trade_no = t.trade_no
      LEFT JOIN order_2c_financing_repayment fr ON fr.order_code = t.trade_no
      WHERE t.baoli_status = 11
      <if test="orderCode != null and orderCode != ''">
        and t.trade_no like concat ('%',#{orderCode},'%')
      </if>
      <if test="provinceCompanyName != null and provinceCompanyName != ''">
        and t.seller_name like concat ('%',#{provinceCompanyName},'%')
      </if>
      <if test="provinceCompanyContract != null and provinceCompanyContract != ''">
        and t.contract_num like concat ('%',#{provinceCompanyContract},'%')
      </if>
      <!--<if test="partnerName != null and partnerName != ''">
        and u.partner_name like concat ('%',#{partnerName},'%')
      </if>-->
      ORDER BY fr.update_time DESC
  </select>
</mapper>