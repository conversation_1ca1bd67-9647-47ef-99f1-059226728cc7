<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.NewProductRequestOnlineOfflineComboInfoMapperExt">
    <select id="listNewProductOnlineOfflineToSuperAdmin"
            resultType="com.chinamobile.iot.sc.pojo.vo.NewProductOnlineOfflineVO">
        SELECT distinct
        nprm.id newProductRequestId,
        nprooci.id comboInfoId,
        nprooci.request_no requestNo,
        nprm.spu_offering_name spuOfferingName,
        nprm.sku_offering_name skuOfferingName,
        u.partner_name cooperatorName,
        nprm.online_status onlineStatus,
        CASE
        WHEN nprm.online_status IN ('01' , '03', '04','06') THEN '下架'
        WHEN nprm.online_status IN ('02' , '05') THEN '上架'
        ELSE '状态错误'
        END onlineStatusName,
        nprm.online_offline_request_status onlineOfflineRequestStatus,
        CASE
        WHEN nprm.online_offline_request_status = 0 THEN '新增'
        WHEN nprm.online_offline_request_status = 1 THEN '待审核(上架初审)'
        WHEN nprm.online_offline_request_status = 2 THEN '待审核(上架复审)'
        WHEN nprm.online_offline_request_status = 3 THEN '待审核(上架终审)'
        WHEN nprm.online_offline_request_status = 4 THEN '待审核(上架运管审核)'
        WHEN nprm.online_offline_request_status = 5 THEN '已上架'
        WHEN nprm.online_offline_request_status = 6 THEN '审核不通过(上架)'
        WHEN nprm.online_offline_request_status = 7 THEN '待审核(下架初审)'
        WHEN nprm.online_offline_request_status = 8 THEN '待审核(下架复审)'
        WHEN nprm.online_offline_request_status = 9 THEN '待审核(下架终审)'
        WHEN nprm.online_offline_request_status = 10 THEN '待审核(下架运管审核)'
        WHEN nprm.online_offline_request_status = 11 THEN '已下架'
        WHEN nprm.online_offline_request_status = 12 THEN '审核不通过(下架)'
        ELSE '状态错误'
        END onlineOfflineRequestStatusName,
        uh.name currentHandlerUserName,
        nprm.online_offline_current_handler_user_id onlineOfflineCurrentHandlerUserId
        FROM
        new_product_request_handler_info nprhi,
        new_product_request_online_offline_combo_info nprooci,
        new_product_request_manage nprm
        left join user uh on nprm.online_offline_current_handler_user_id = uh.user_id,
        user_partner u
        WHERE
        nprhi.flow_type = '02'
        AND nprhi.flow_source_id = nprooci.id
        and nprm.id = nprooci.new_product_request_id
        AND nprm.cooperator_id = u.user_id
        AND nprm.request_status = 4
        <if test="onlineOfflineParam.requestNo != null and onlineOfflineParam.requestNo !=  ''">
            and nprooci.request_no like '%${onlineOfflineParam.requestNo}%'
        </if>
        <if test="onlineOfflineParam.spuOfferingName != null and onlineOfflineParam.spuOfferingName !=  ''">
            and nprm.spu_offering_name like '%${onlineOfflineParam.spuOfferingName}%'
        </if>
        <if test="onlineOfflineParam.skuOfferingName != null and onlineOfflineParam.skuOfferingName !=  ''">
            and nprm.sku_offering_name like '%${onlineOfflineParam.skuOfferingName}%'
        </if>
        <if test="onlineOfflineParam.onlineStatus == '01'">
            and nprm.online_status in ('02','05')
        </if>
        <if test="onlineOfflineParam.onlineStatus == '02'">
            and nprm.online_status in ('01','03','04','06')
        </if>
        <if test="onlineOfflineParam.labelStatus == 0">
            <if test="onlineOfflineParam.onlineOfflineRequestStatus != null and onlineOfflineParam.onlineOfflineRequestStatus != ''">
                and nprm.online_offline_request_status = #{onlineOfflineParam.onlineOfflineRequestStatus}
            </if>
        </if>
        <if test="onlineOfflineParam.labelStatus == 1">
            <if test="onlineOfflineParam.onlineOfflineRequestStatus == null or onlineOfflineParam.onlineOfflineRequestStatus != ''">
                and nprm.online_offline_request_status in (1,2,3,4,7,8,9,10)
            </if>
            <if test="onlineOfflineParam.onlineOfflineRequestStatus != null and onlineOfflineParam.onlineOfflineRequestStatus != ''">
                and nprm.online_offline_request_status = #{onlineOfflineParam.onlineOfflineRequestStatus}
            </if>
        </if>
        <if test="onlineOfflineParam.labelStatus == 2">
            <if test="onlineOfflineParam.onlineOfflineRequestStatus == null or onlineOfflineParam.onlineOfflineRequestStatus != ''">
                and nprm.online_offline_request_status in (5,12)
            </if>
            <if test="onlineOfflineParam.onlineOfflineRequestStatus != null and onlineOfflineParam.onlineOfflineRequestStatus != ''">
                and nprm.online_offline_request_status = #{onlineOfflineParam.onlineOfflineRequestStatus}
            </if>
        </if>
        <if test="onlineOfflineParam.labelStatus == 3">
            <if test="onlineOfflineParam.onlineOfflineRequestStatus == null or onlineOfflineParam.onlineOfflineRequestStatus != ''">
                and nprm.online_offline_request_status in (0,6,11)
            </if>
            <if test="onlineOfflineParam.onlineOfflineRequestStatus != null and onlineOfflineParam.onlineOfflineRequestStatus != ''">
                and nprm.online_offline_request_status = #{onlineOfflineParam.onlineOfflineRequestStatus}
            </if>
        </if>
        ORDER BY nprooci.create_time DESC
    </select>

    <select id="listNewProductOnlineOfflineToJudgeUser"
            resultType="com.chinamobile.iot.sc.pojo.vo.NewProductOnlineOfflineVO">
        SELECT
            newProductRequestId,
            comboInfoId,
            requestNo,
            spuOfferingName,
            skuOfferingName,
            cooperatorName,
            onlineStatus,
            onlineStatusName,
            onlineOfflineRequestStatus,
            onlineOfflineRequestStatusName,
            currentHandlerUserName,
            onlineOfflineCurrentHandlerUserId,
            create_time
        FROM(
        SELECT
        nprm.id newProductRequestId,
        nprooci.id comboInfoId,
        nprooci.request_no requestNo,
        nprm.spu_offering_name spuOfferingName,
        nprm.sku_offering_name skuOfferingName,
        u.partner_name cooperatorName,
        nprm.online_status onlineStatus,
        CASE
        WHEN nprm.online_status IN ('01' , '03', '04','06') THEN '下架'
        WHEN nprm.online_status IN ('02' , '05') THEN '上架'
        ELSE '状态错误'
        END onlineStatusName,
        nprm.online_offline_request_status onlineOfflineRequestStatus,
        CASE
        WHEN nprm.online_offline_request_status = 0 THEN '新增'
        WHEN nprm.online_offline_request_status = 1 THEN '待审核(上架初审)'
        WHEN nprm.online_offline_request_status = 2 THEN '待审核(上架复审)'
        WHEN nprm.online_offline_request_status = 3 THEN '待审核(上架终审)'
        WHEN nprm.online_offline_request_status = 4 THEN '待审核(上架运管审核)'
        WHEN nprm.online_offline_request_status = 5 THEN '已上架'
        WHEN nprm.online_offline_request_status = 6 THEN '审核不通过(上架)'
        WHEN nprm.online_offline_request_status = 7 THEN '待审核(下架初审)'
        WHEN nprm.online_offline_request_status = 8 THEN '待审核(下架复审)'
        WHEN nprm.online_offline_request_status = 9 THEN '待审核(下架终审)'
        WHEN nprm.online_offline_request_status = 10 THEN '待审核(下架运管审核)'
        WHEN nprm.online_offline_request_status = 11 THEN '已下架'
        WHEN nprm.online_offline_request_status = 12 THEN '审核不通过(下架)'
        ELSE '状态错误'
        END onlineOfflineRequestStatusName,
        uh.name currentHandlerUserName,
        nprm.online_offline_current_handler_user_id onlineOfflineCurrentHandlerUserId,
        nprooci.create_time
        FROM
        new_product_request_handler_info nprhi,
        new_product_request_online_offline_combo_info nprooci,
        new_product_request_manage nprm,
        user_partner u,
        user uh
        WHERE
        nprhi.flow_type = '02'
        and nprhi.flow_source_id = nprooci.id
        and nprm.id = nprooci.new_product_request_id
        and nprm.cooperator_id = u.user_id
        and nprm.online_offline_current_handler_user_id = uh.user_id
        AND nprm.request_status = 4
        and nprm.online_offline_current_handler_user_id = #{onlineOfflineParam.currentUserId}
        <if test="onlineOfflineParam.requestNo != null and onlineOfflineParam.requestNo !=  ''">
            and nprooci.request_no like '%${onlineOfflineParam.requestNo}%'
        </if>
        <if test="onlineOfflineParam.spuOfferingName != null and onlineOfflineParam.spuOfferingName !=  ''">
          and nprm.spu_offering_name like '%${onlineOfflineParam.spuOfferingName}%'
        </if>
        <if test="onlineOfflineParam.skuOfferingName != null and onlineOfflineParam.skuOfferingName !=  ''">
            and nprm.sku_offering_name like '%${onlineOfflineParam.skuOfferingName}%'
        </if>
        <if test="onlineOfflineParam.onlineStatus == '01'">
            and nprm.online_status in ('02','05')
        </if>
        <if test="onlineOfflineParam.onlineStatus == '02'">
            and nprm.online_status in ('01','03','04','06')
        </if>
        <if test="onlineOfflineParam.labelStatus == 0">
            <if test="onlineOfflineParam.onlineOfflineRequestStatus != null and onlineOfflineParam.onlineOfflineRequestStatus != ''">
                and nprm.online_offline_request_status = #{onlineOfflineParam.onlineOfflineRequestStatus}
            </if>
        </if>
        <if test="onlineOfflineParam.labelStatus == 1">
            <if test="onlineOfflineParam.onlineOfflineRequestStatus == null or onlineOfflineParam.onlineOfflineRequestStatus != ''">
                and nprm.online_offline_request_status in (1,2,3,4,7,8,9,10)
            </if>
            <if test="onlineOfflineParam.onlineOfflineRequestStatus != null and onlineOfflineParam.onlineOfflineRequestStatus != ''">
                and nprm.online_offline_request_status = #{onlineOfflineParam.onlineOfflineRequestStatus}
            </if>
        </if>
        <if test="onlineOfflineParam.labelStatus == 2">
            <if test="onlineOfflineParam.onlineOfflineRequestStatus == null or onlineOfflineParam.onlineOfflineRequestStatus != ''">
                and nprm.online_offline_request_status in (5,12)
            </if>
            <if test="onlineOfflineParam.onlineOfflineRequestStatus != null and onlineOfflineParam.onlineOfflineRequestStatus != ''">
                and nprm.online_offline_request_status = #{onlineOfflineParam.onlineOfflineRequestStatus}
            </if>
        </if>
        <if test="onlineOfflineParam.labelStatus == 3">
            <if test="onlineOfflineParam.onlineOfflineRequestStatus == null or onlineOfflineParam.onlineOfflineRequestStatus != ''">
                and nprm.online_offline_request_status in (0,6,11)
            </if>
            <if test="onlineOfflineParam.onlineOfflineRequestStatus != null and onlineOfflineParam.onlineOfflineRequestStatus != ''">
                and nprm.online_offline_request_status = #{onlineOfflineParam.onlineOfflineRequestStatus}
            </if>
        </if>
            union
            SELECT
                nprm.id newProductRequestId,
                nprooci.id comboInfoId,
                nprooci.request_no requestNo,
                nprm.spu_offering_name spuOfferingName,
                nprm.sku_offering_name skuOfferingName,
                u.partner_name cooperatorName,
                nprm.online_status onlineStatus,
                CASE
                WHEN nprm.online_status IN ('01' , '03', '04','06') THEN '下架'
                WHEN nprm.online_status IN ('02' , '05') THEN '上架'
                ELSE '状态错误'
                END onlineStatusName,
                nprm.online_offline_request_status onlineOfflineRequestStatus,
                CASE
                WHEN nprm.online_offline_request_status = 0 THEN '新增'
                WHEN nprm.online_offline_request_status = 1 THEN '待审核(上架初审)'
                WHEN nprm.online_offline_request_status = 2 THEN '待审核(上架复审)'
                WHEN nprm.online_offline_request_status = 3 THEN '待审核(上架终审)'
                WHEN nprm.online_offline_request_status = 4 THEN '待审核(上架运管审核)'
                WHEN nprm.online_offline_request_status = 5 THEN '已上架'
                WHEN nprm.online_offline_request_status = 6 THEN '审核不通过(上架)'
                WHEN nprm.online_offline_request_status = 7 THEN '待审核(下架初审)'
                WHEN nprm.online_offline_request_status = 8 THEN '待审核(下架复审)'
                WHEN nprm.online_offline_request_status = 9 THEN '待审核(下架终审)'
                WHEN nprm.online_offline_request_status = 10 THEN '待审核(下架运管审核)'
                WHEN nprm.online_offline_request_status = 11 THEN '已下架'
                WHEN nprm.online_offline_request_status = 12 THEN '审核不通过(下架)'
                ELSE '状态错误'
                END onlineOfflineRequestStatusName,
                uh.name currentHandlerUserName,
                nprm.online_offline_current_handler_user_id onlineOfflineCurrentHandlerUserId,
                nprooci.create_time
            FROM
                new_product_request_handler_info nprhi,
                new_product_request_online_offline_combo_info nprooci,
                new_product_request_manage nprm
                left join user uh on nprm.online_offline_current_handler_user_id = uh.user_id,
                user_partner u
            WHERE
              nprhi.flow_type = '02'
            and nprhi.flow_source_id = nprooci.id
            and nprm.id = nprooci.new_product_request_id
            and nprm.cooperator_id = u.user_id
            AND nprm.request_status = 4
            and nprhi.current_handler_user_id = #{onlineOfflineParam.currentUserId}
            <if test="onlineOfflineParam.requestNo != null and onlineOfflineParam.requestNo !=  ''">
                and nprooci.request_no like '%${onlineOfflineParam.requestNo}%'
            </if>
            <if test="onlineOfflineParam.spuOfferingName != null and onlineOfflineParam.spuOfferingName !=  ''">
                and nprm.spu_offering_name like '%${onlineOfflineParam.spuOfferingName}%'
            </if>
            <if test="onlineOfflineParam.skuOfferingName != null and onlineOfflineParam.skuOfferingName !=  ''">
                and nprm.sku_offering_name like '%${onlineOfflineParam.skuOfferingName}%'
            </if>
            <if test="onlineOfflineParam.onlineStatus == '01'">
                and nprm.online_status in ('02','05')
            </if>
            <if test="onlineOfflineParam.onlineStatus == '02'">
                and nprm.online_status in ('01','03','04','06')
            </if>
            <if test="onlineOfflineParam.labelStatus == 0">
                <if test="onlineOfflineParam.onlineOfflineRequestStatus != null and onlineOfflineParam.onlineOfflineRequestStatus != ''">
                    and nprm.online_offline_request_status = #{onlineOfflineParam.onlineOfflineRequestStatus}
                </if>
            </if>
            <if test="onlineOfflineParam.labelStatus == 1">
                <if test="onlineOfflineParam.onlineOfflineRequestStatus == null or onlineOfflineParam.onlineOfflineRequestStatus != ''">
                    and nprm.online_offline_request_status in (1,2,3,4,7,8,9,10)
                </if>
                <if test="onlineOfflineParam.onlineOfflineRequestStatus != null and onlineOfflineParam.onlineOfflineRequestStatus != ''">
                    and nprm.online_offline_request_status = #{onlineOfflineParam.onlineOfflineRequestStatus}
                </if>
            </if>
            <if test="onlineOfflineParam.labelStatus == 2">
                <if test="onlineOfflineParam.onlineOfflineRequestStatus == null or onlineOfflineParam.onlineOfflineRequestStatus != ''">
                    and nprm.online_offline_request_status in (5,12)
                </if>
                <if test="onlineOfflineParam.onlineOfflineRequestStatus != null and onlineOfflineParam.onlineOfflineRequestStatus != ''">
                    and nprm.online_offline_request_status = #{onlineOfflineParam.onlineOfflineRequestStatus}
                </if>
            </if>
            <if test="onlineOfflineParam.labelStatus == 3">
                <if test="onlineOfflineParam.onlineOfflineRequestStatus == null or onlineOfflineParam.onlineOfflineRequestStatus != ''">
                    and nprm.online_offline_request_status in (0,6,11)
                </if>
                <if test="onlineOfflineParam.onlineOfflineRequestStatus != null and onlineOfflineParam.onlineOfflineRequestStatus != ''">
                    and nprm.online_offline_request_status = #{onlineOfflineParam.onlineOfflineRequestStatus}
                </if>
            </if>
        ) ON_OFF_LINE
        ORDER BY create_time DESC
    </select>
</mapper>