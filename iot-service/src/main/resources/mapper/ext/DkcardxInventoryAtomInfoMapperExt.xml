<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.DkcardxInventoryAtomInfoMapperExt">
    <select id="listAtomDetailInventory" resultType="com.chinamobile.iot.sc.pojo.dto.AtomDetailInventoryDTO">
        select
            atom_id atomId,
            inventory_main_id inventoryMainId,
            count(inventory_detail_id) detailCount
        from
            dkcardx_inventory_atom_info
        group by atom_id,inventory_main_id;
    </select>
</mapper>