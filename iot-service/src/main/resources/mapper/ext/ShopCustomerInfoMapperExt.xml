<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.ShopCustomerInfoMapperExt">
    <select id="getShopManagerInfoList" resultType="com.chinamobile.iot.sc.pojo.mapper.ShopUserUpdateDO">
        select id, create_oper_code userCode
        from shop_manager_info smi
        where 1 = 1
          and BINARY smi.create_oper_code = #{userCode}
    </select>
    <select id="getShopManagerInfoHistoryList" resultType="com.chinamobile.iot.sc.pojo.mapper.ShopUserUpdateDO">
        select id, create_oper_code userCode
        from shop_manager_info_history smih
        where 1 = 1
          and BINARY smih.create_oper_code = #{userCode}
    </select>

    <select id="getShopCustomerInfoList" resultType="com.chinamobile.iot.sc.pojo.mapper.ShopUserUpdateDO">
        select id, cust_code userCode
        from shop_customer_info sci
        where 1 = 1
          and BINARY sci.cust_code = #{userCode} and sci.cust_id = #{custId}
    </select>

    <select id="getShopCustomerInfoHistoryList" resultType="com.chinamobile.iot.sc.pojo.mapper.ShopUserUpdateDO">
        select id, cust_code userCode
        from shop_customer_info_history scih
        where 1 = 1
          and BINARY scih.cust_code = #{userCode} and scih.cust_id = #{custId}
    </select>

    <select id="custCodeCustIdList" parameterType="java.lang.String"
            resultType="com.chinamobile.iot.sc.pojo.dto.CustCodeCustIdDTO">
        SELECT cust_code custCode,GROUP_CONCAT(cust_id) custId FROM shop_customer_info WHERE 1=1
        <if test="custCodeList != null and custCodeList.size() != 0">
            and cust_code in
            <foreach collection="custCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY cust_code
    </select>

</mapper>