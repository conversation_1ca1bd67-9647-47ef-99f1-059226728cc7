<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.ProductFlowInstanceSpuMapperExt">
    <resultMap type="com.chinamobile.iot.sc.pojo.mapper.productFlowInfo.ShelfSpuDetailDO" id="ShelfSpuDetail">
        <association property="spuItem"
                     javaType="com.chinamobile.iot.sc.pojo.mapper.productFlowInfo.ShelfSpuDetailSpuDO">
            <result column="spuName" jdbcType="VARCHAR" property="spuName"></result>
            <result column="spuCode" jdbcType="VARCHAR" property="spuCode"></result>
            <result column="productStandard" jdbcType="INTEGER" property="productStandard"></result>
            <result column="productType" jdbcType="INTEGER" property="productType"></result>
            <result column="shelfCatagoryName" jdbcType="VARCHAR" property="shelfCatagoryName"></result>
            <result column="manager" jdbcType="VARCHAR" property="manager"></result>
            <result column="aftermarketAdminInfo" jdbcType="VARCHAR" property="aftermarketAdminInfo"></result>
            <result column="productDesc" jdbcType="VARCHAR" property="productDesc"></result>
            <result column="applicationArea" jdbcType="VARCHAR" property="applicationArea"></result>
            <result column="isHiddenShelf" jdbcType="VARCHAR" property="isHiddenShelf"></result>
            <result column="spuServiceProvider" jdbcType="VARCHAR" property="spuServiceProvider"></result>
            <result column="saleTag" jdbcType="VARCHAR" property="saleTag"></result>
            <result column="searchWord" jdbcType="VARCHAR" property="searchWord"></result>
            <result column="url" jdbcType="VARCHAR" property="url"></result>
            <result column="configRemark" jdbcType="VARCHAR" property="configRemark"></result>
            <result column="reMarker" jdbcType="VARCHAR" property="reMarker"></result>
        </association>
        <association property="skuItem"
                     javaType="com.chinamobile.iot.sc.pojo.mapper.productFlowInfo.ShelfSpuDetailSkuDO">
            <result column="flowId" jdbcType="VARCHAR" property="flowId"></result>
            <result column="flowInstanceId" jdbcType="VARCHAR" property="flowInstanceId"></result>
            <result column="skuName" jdbcType="VARCHAR" property="skuName"></result>
            <result column="skuCode" jdbcType="VARCHAR" property="skuCode"></result>
            <result column="skuShortName" jdbcType="VARCHAR" property="skuShortName"></result>
            <result column="keyCompomentName" jdbcType="VARCHAR" property="keyCompomentName"></result>
            <result column="keyComponentServiceInfo" jdbcType="VARCHAR" property="keyComponentServiceInfo"></result>
            <result column="salePrice" jdbcType="INTEGER" property="salePrice"></result>
            <result column="provincePrice" jdbcType="INTEGER" property="provincePrice"></result>
            <result column="saleMinPrice" jdbcType="INTEGER" property="saleMinPrice"></result>
            <result column="saleMaxPrice" jdbcType="INTEGER" property="saleMaxPrice"></result>
            <result column="saleOutOfPriceRange" jdbcType="VARCHAR" property="saleOutOfPriceRange"></result>
            <result column="saleProvinceCity" jdbcType="VARCHAR" property="saleProvinceCity"></result>
            <result column="deliveryRange" jdbcType="VARCHAR" property="deliveryRange"></result>
            <result column="touristPartnerVisible" jdbcType="VARCHAR" property="touristPartnerVisible"></result>
            <result column="standardProductName" jdbcType="VARCHAR" property="standardProductName"></result>
            <result column="standardProductAttribute" jdbcType="VARCHAR" property="standardProductAttribute"></result>
            <result column="skuServiceProvider" jdbcType="VARCHAR" property="skuServiceProvider"></result>
            <result column="manageDepartment" jdbcType="VARCHAR" property="manageDepartment"></result>
            <result column="standardProductManager" jdbcType="VARCHAR" property="standardProductManager"></result>
            <result column="receiveOrderAccount" jdbcType="VARCHAR" property="receiveOrderAccount"></result>
            <result column="deliverAccount" jdbcType="VARCHAR" property="deliverAccount"></result>
            <result column="aftermarketAccount" jdbcType="VARCHAR" property="aftermarketAccount"></result>
            <result column="skuRemark" jdbcType="VARCHAR" property="skuRemark"></result>

        </association>

    </resultMap>

    <select id="getFlowInstanceSpuDetail" parameterType="java.lang.String" resultMap="ShelfSpuDetail">
        SELECT
        sku.id as id,
        spu.spu_name as spuName,
        sku.flow_id as flowId,
        sku.flow_instance_id as flowInstanceId,
        spu.spu_code as spuCode,
        spu.product_standard as productStandard,
        spu.product_type as productType,
        pscs.name as shelfCatagoryName,
        spu.manager as manager,
        spu.aftermarket_admin_info as aftermarketAdminInfo,
        spu.product_desc as productDesc,
        spu.application_area as applicationArea,
        spu.is_hidden_shelf as isHiddenShelf,
        spu.spu_service_provider as spuServiceProvider,
        spu.sale_tag as saleTag,
        spu.search_word as searchWord,
        spu.spu_remark as reMarker,
        spu.url as url,

        sku.sku_name as skuName,
        sku.sku_code as skuCode,
        sku.sku_short_name as skuShortName,
        sku.key_compoment_name as keyCompomentName,
        sku.key_component_service_info as keyComponentServiceInfo,
        sku.sale_price as salePrice,
        sku.province_price as provincePrice,
        sku.sale_min_price as saleMinPrice,
        sku.sale_max_price as saleMaxPrice,
        sku.sale_out_of_price_range as saleOutOfPriceRange,
        sku.sale_province_city as saleProvinceCity,
        sku.delivery_range as deliveryRange,
        sku.tourist_partner_visible as touristPartnerVisible,
        sku.standard_product_name as standardProductName,
        sku.standard_product_attribute as standardProductAttribute,
        sku.sku_service_provider as skuServiceProvider,
        sku.manage_department as manageDepartment,
        sku.standard_product_manager as standardProductManager,
        sku.receive_order_account as receiveOrderAccount,
        sku.deliver_account as deliverAccount,
        sku.aftermarket_account as aftermarketAccount,
        sku.sku_remark as skuRemark,
        config.config_remark as configRemark
        FROM
        product_flow_instance_sku sku
        LEFT JOIN product_flow_instance_spu spu ON sku.flow_instance_id = spu.flow_instance_id
        AND (sku.spu_code = spu.spu_code OR sku.spu_code IS NULL)
        LEFT JOIN product_shelf_category_cost pscs ON pscs.id = spu.shelf_catagory_id
        LEFT JOIN product_flow_instance_config config ON config.flow_instance_id = sku.flow_instance_id
        where 1 = 1
        <if test="spuCode != null">
            and sku.spu_code = #{spuCode}
        </if>
        <if test="skuCode != null">
          and sku.sku_code = #{skuCode}
        </if>
        <if test="flowInstanceId!=null">
            and sku.flow_instance_id = #{flowInstanceId}
        </if>

    </select>
    <select id="getFlowInstanceSpuDetailList" parameterType="java.lang.String"
            resultType="com.chinamobile.iot.sc.pojo.mapper.productFlowInfo.ProductFlowAllExportDO">
        SELECT spu.spu_name           as spuName,
               spu.spu_code           as spuCode,
               spu.product_standard   as productStandard,
               spu.product_type       as productType,
               sku.sku_name           as skuName,
               sku.sku_code           as skuCode,
               sku.sale_price/1000         as salePrice,
               pscs.name              as shelfCatagoryName,
               sku.sale_province_city as saleProvinceCity
        FROM
            (
                SELECT *
                FROM (  SELECT product_flow_instance_sku.*,product_flow_instance.status as status
                        FROM product_flow_instance_sku
                                 JOIN product_flow_instance ON product_flow_instance_sku.flow_instance_id = product_flow_instance.id
                        WHERE ((product_flow_instance_sku.sku_code IS NOT NULL AND product_flow_instance.status = 1)
                            OR product_flow_instance_sku.sku_code IS null)) p1
                WHERE ((p1.sku_code IS NOT NULL AND (p1.spu_code, p1.sku_code) IN (
                    SELECT spu_code, sku_code
                    FROM (  SELECT product_flow_instance_sku.*,product_flow_instance.status as status
                            FROM product_flow_instance_sku
                                     JOIN product_flow_instance ON product_flow_instance_sku.flow_instance_id = product_flow_instance.id
                            WHERE ((product_flow_instance_sku.sku_code IS NOT NULL AND product_flow_instance.status = 1)
                                OR product_flow_instance_sku.sku_code IS null)) p2
                    WHERE p2.sku_code IS NOT NULL
                    GROUP BY spu_code, sku_code
                ))
                    OR (p1.sku_code IS NULL AND p1.flow_instance_id IN (
                        SELECT flow_instance_id
                        FROM (  SELECT product_flow_instance_sku.*,product_flow_instance.status as status
                                FROM product_flow_instance_sku
                                         JOIN product_flow_instance ON product_flow_instance_sku.flow_instance_id = product_flow_instance.id
                                WHERE ((product_flow_instance_sku.sku_code IS NOT NULL AND product_flow_instance.status = 1)
                                    OR product_flow_instance_sku.sku_code IS null)) p3
                        WHERE p3.sku_code IS NULL
                        GROUP BY flow_instance_id
                    )))
                  AND p1.create_time = (
                    SELECT MAX(p4.create_time)
                    FROM (  SELECT product_flow_instance_sku.*,product_flow_instance.status as status
                            FROM product_flow_instance_sku
                                     JOIN product_flow_instance ON product_flow_instance_sku.flow_instance_id = product_flow_instance.id
                            WHERE ((product_flow_instance_sku.sku_code IS NOT NULL AND product_flow_instance.status = 1)
                                OR product_flow_instance_sku.sku_code IS null)) p4
                    WHERE IF(p1.sku_code IS NOT NULL, CONCAT(p1.spu_code, p1.sku_code), p1.flow_instance_id) = IF(p4.sku_code IS NOT NULL, CONCAT(p4.spu_code, p4.sku_code), p4.flow_instance_id)
                )

            )sku
                LEFT JOIN product_flow_instance_spu spu  ON sku.flow_instance_id = spu.flow_instance_id
                AND (sku.spu_code = spu.spu_code OR sku.spu_code IS NULL)
                LEFT JOIN product_flow_instance_config config ON config.flow_instance_id = sku.flow_instance_id
                LEFT JOIN product_shelf_category_cost pscs ON pscs.id= spu.shelf_catagory_id
    </select>

    <select id="getFlowInstanceSpuList" parameterType="com.chinamobile.iot.sc.pojo.param.FlowInstanceSpuListParam"
            resultType="com.chinamobile.iot.sc.pojo.mapper.ShelfSpuInfoDO">
        SELECT
        sku.flow_instance_id AS flowInstanceId,
        sku.sku_name AS skuName,
        sku.sku_code AS skuCode,
        sku.key_compoment_name AS keyCompomentName,
        spu.spu_name AS spuName,
        spu.spu_code AS spuCode,
        spu.product_type AS productType,
        spu.product_standard AS productStandard,
        sku.shelf_status AS shelfStatus,
        config.product_department AS productDepartment,
        pscs.name AS shelfCatagoryName,
        sku.sale_province_city AS saleProvinceCity,
        sku.create_time AS createTime
        FROM
        (
        SELECT
        sku.*,
        pfi.status,
        pfi.creator_id,
        IF(sku.sku_code IS NOT NULL, CONCAT(sku.spu_code, sku.sku_code), sku.flow_instance_id) AS sku_key
        FROM
        product_flow_instance_sku sku
        JOIN product_flow_instance pfi ON sku.flow_instance_id = pfi.id
        WHERE
        (sku.sku_code IS NOT NULL AND pfi.status = 1)
        OR sku.sku_code IS NULL
        ) AS sku
        LEFT JOIN (
        SELECT
        IF(sku_code IS NOT NULL, CONCAT(spu_code, sku_code), flow_instance_id) AS sku_key,
        MAX(create_time) AS max_create_time
        FROM
        product_flow_instance_sku
        GROUP BY
        sku_key
        ) AS mct ON sku.sku_key = mct.sku_key
        LEFT JOIN product_flow_instance_spu spu ON sku.flow_instance_id = spu.flow_instance_id AND (sku.spu_code = spu.spu_code OR sku.spu_code IS NULL)
        LEFT JOIN product_flow_instance_config config ON config.flow_instance_id = sku.flow_instance_id
        LEFT JOIN product_shelf_category_cost pscs ON pscs.id = spu.shelf_catagory_id
        WHERE
        sku.create_time = mct.max_create_time
        <if test="param.spuOfferingName != null and param.spuOfferingName != ''">
            and spu.spu_name like concat ('%',#{param.spuOfferingName},'%')
        </if>
        <if test="param.skuOfferingName != null and param.skuOfferingName != ''">
            and sku.sku_name like concat ('%',#{param.skuOfferingName},'%')
        </if>
        <if test="param.productType != null">
            and spu.product_type = #{param.productType}
        </if>
        <if test="param.saleProvinceCity != null">
            and sku.sale_province_city like concat ('%',#{param.saleProvinceCity},'%')
        </if>

        <if test="param.productDepartment != null">
            and sku.manage_department = #{param.productDepartment}
        </if>
        <if test="param.shelfCatagoryName != null">
            and spu.shelf_catagory_name = #{param.shelfCatagoryName}
        </if>
        <if test="userIdList != null and userIdList.size() != 0">
            and sku.creator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        ORDER BY
        sku.create_time DESC
    </select>
    <select id="getNewSku" parameterType="java.lang.String"
            resultType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceSku">
        select *
        from product_flow_instance_sku sku
        where
            spu_code= #{spuCode} and sku_code=#{skuCode}
        ORDER BY sku.create_time DESC
            limit 1
    </select>

</mapper>