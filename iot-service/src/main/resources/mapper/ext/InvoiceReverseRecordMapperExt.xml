<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.InvoiceReverseRecordMapperExt">

    <resultMap id="invoiceReverseRecMap" type="com.chinamobile.iot.sc.pojo.mapper.InvoiceReverseRecDO">
        <result column="id" property="id"/>
        <result column="atom_order_id" property="atomOrderId"/>
        <result column="order_seq" property="orderSeq"/>
        <result column="order_id" property="orderId"/>
        <result column="oper_type" property="operType"/>
        <result column="customer_type" property="customerType"/>
        <result column="customer_number" property="customerNumber"/>
        <result column="order_price" property="orderPrice"/>
        <result column="status" property="status"/>
        <result column="error_desc" property="errorDesc"/>
        <result column="cooperator_id" property="cooperatorId"/>
        <result column="finish_cooperator_id" property="finishCooperatorId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="partner_name" property="partnerName"/>
        <result column="cooperatorName" property="cooperatorName"/>
        <result column="finishCooperatorName" property="finishCooperatorName"/>
        <result column="reminderCount" property="reminderCount"/>
        <result column="spuOfferingClass" jdbcType="VARCHAR" property="spuOfferingClass"/>
    </resultMap>

    <resultMap id="invoiceReverseRecDetailMap" type="com.chinamobile.iot.sc.pojo.mapper.InvoiceReverseRecDetailDO">
        <result column="id" property="id"/>
        <result column="atom_order_id" property="atomOrderId"/>
        <result column="order_seq" property="orderSeq"/>
        <result column="order_id" property="orderId"/>
        <result column="oper_type" property="operType"/>
        <result column="customer_type" property="customerType"/>
        <result column="customer_number" property="customerNumber"/>
        <result column="order_price" property="orderPrice"/>
        <result column="status" property="status"/>
        <result column="error_desc" property="errorDesc"/>
        <result column="primary_cooperator_id" property="primaryCooperatorId"/>
        <result column="cooperator_id" property="cooperatorId"/>
        <result column="finish_cooperator_id" property="finishCooperatorId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="partner_name" property="partnerName"/>
        <result column="cooperatorName" property="cooperatorName"/>
        <result column="finishCooperatorName" property="finishCooperatorName"/>
        <result column="reminder_count" property="reminderCount"/>
    </resultMap>

    <select id="page4InvoRevRec" resultMap="invoiceReverseRecMap">
        SELECT a.id,
        a.atom_order_id,
        a.order_seq,
        a.order_id,
        a.oper_type,
        a.customer_type,
        a.customer_number,
        a.order_price,
        a.status,
        a.error_desc,
        ipi.cooperator_id,
        ipih.cooperator_id finish_cooperator_id,
        a.create_time,
        a.update_time,
        ipi.partner_name,
        ipi.user_name AS cooperatorName,
        ipih.user_name AS finishCooperatorName,
        a.reminder_count reminderCount,
        o2i.spu_offering_class spuOfferingClass
        FROM invoice_reverse_record a
        <if test="userIdList==null or userIdList.size() == 0">
            left join (
            select  GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            irri.id
            from
            invoice_reverse_record irri,
            order_cooperator_relation ocr,
            user_partner up
            where
            irri.atom_order_id = ocr.atom_order_id
            and irri.order_id = ocr.order_id
            and ocr.cooperator_id = up.user_id
            group by irri.id
            ) ipi on ipi.id = a.id
        </if>
        <if test="userIdList!=null and userIdList.size != 0">
            inner join (
            select  GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            irri.id
            from
            invoice_reverse_record irri,
            order_cooperator_relation ocr,
            user_partner up
            where
            irri.atom_order_id = ocr.atom_order_id
            and irri.order_id = ocr.order_id
            and ocr.cooperator_id = up.user_id
            and ocr.cooperator_id in
            <foreach item="item" collection="userIdList" open="(" separator="," close=")">#{item}</foreach>
            group by irri.id
            ) ipi on ipi.id = a.id
        </if>
        left join (
        select  GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocrh.cooperator_id) cooperator_id,
        irri.id
        from
        invoice_reverse_record irri,
        order_cooperator_relation_history ocrh,
        user_partner up
        where
        irri.atom_order_id = ocrh.atom_order_id
        and irri.order_id = ocrh.order_id
        and ocrh.cooperator_id = up.user_id
        group by irri.id
        ) ipih on ipih.id = a.id
        --         left join order_2c_atom_info o on a.atom_order_id = o.id
        inner join order_2c_info o2i on o2i.order_id = a.order_id
        where 1=1
            <if test="status!=null">and a.status = #{status}</if>
            <!--<if test="userIdList!=null">and a.cooperator_id in
                <foreach item="item" collection="userIdList" open="(" separator="," close=")">#{item}</foreach>
            </if>-->
            <if test="invoiceReverseId != null and invoiceReverseId !=''">
                and  a.id like concat('%',#{invoiceReverseId},'%')
            </if>
            <if test="orderId!=null and orderId!=''">
                and  a.order_id like concat('%',#{orderId},'%')
            </if>
            <if test="orderSeq!=null and orderSeq!=''">
                and  a.order_seq like concat('%',#{orderSeq},'%')
            </if>
        order by update_time desc
        limit #{pageIndex},#{num}
    </select>

    <select id="pageCount4InvoRevRec" resultType="java.lang.Long">
        SELECT count(*)
        FROM invoice_reverse_record a
        <if test="userIdList!=null and userIdList.size != 0">
            inner join (
            select  GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            irri.id
            from
            invoice_reverse_record irri,
            order_cooperator_relation ocr,
            user_partner up
            where
            irri.atom_order_id = ocr.atom_order_id
            and irri.order_id = ocr.order_id
            and ocr.cooperator_id = up.user_id
            and ocr.cooperator_id in
            <foreach item="item" collection="userIdList" open="(" separator="," close=")">#{item}</foreach>
            group by irri.id
            ) ipi on ipi.id = a.id
        </if>
        --         left join order_2c_atom_info o on a.atom_order_id = o.id
        inner join order_2c_info o2i on o2i.order_id = a.order_id
        where 1=1
        <if test="status!=null">and a.status = #{status}</if>
        <!--<if test="userIdList!=null">and a.cooperator_id in
            <foreach item="item" collection="userIdList" open="(" separator="," close=")">#{item}</foreach>
        </if>-->
        <if test="invoiceReverseId != null and invoiceReverseId !=''">
            and  a.id like concat('%',#{invoiceReverseId},'%')
        </if>
        <if test="orderId!=null and orderId!=''">
            and  a.order_id like concat('%',#{orderId},'%')
        </if>
    </select>

    <select id="getUnRedFlushRec" resultType="com.chinamobile.iot.sc.response.web.invoice.Data4UnRedFlush">
        SELECT a.id,
        a.order_id,
        a.customer_type,
        a.order_price,
        a.status,
        ipi.cooperator_id,
        a.create_time,
        ipi.partner_name,
        ipi.user_name AS cooperatorName
        FROM invoice_reverse_record a
        <if test="userIdList!=null and userIdList.size() != 0">
            inner join (
            select  GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            irri.id
            from
            invoice_reverse_record irri,
            order_cooperator_relation ocr,
            user_partner up
            where
            irri.atom_order_id = ocr.atom_order_id
            and irri.order_id = ocr.order_id
            and ocr.cooperator_id = up.user_id
            and ocr.cooperator_id in
            <foreach item="item" collection="userIdList" open="(" separator="," close=")">#{item}</foreach>
            group by irri.id
            ) ipi on ipi.id = a.id
        </if>
        <where>
            <if test="status!=null">and status = #{status}</if>
            <!--<if test="userIdList!=null">and a.cooperator_id in
                <foreach item="item" collection="userIdList" open="(" separator="," close=")">#{item}</foreach>
            </if>-->
        </where>
        order by a.update_time desc
    </select>

    <select id="getInvoiceReverseRecordByOrderId" resultMap="invoiceReverseRecDetailMap">
        SELECT irr.id,
        irr.atom_order_id,
        irr.order_seq,
        irr.order_id,
        irr.oper_type,
        irr.customer_type,
        irr.customer_number,
        irr.order_price,
        irr.status,
        irr.error_desc,
        o2ai.cooperator_id primary_cooperator_id,
        <!--ipi.cooperator_id,
        ipih.cooperator_id finish_cooperator_id,-->
        irr.create_time,
        irr.update_time,
        <!--ipi.partner_name,
        ipi.user_name AS cooperatorName,
        ipih.user_name AS finishCooperatorName,-->
        irr.reminder_count
        FROM invoice_reverse_record irr
        <!--<if test="userIdList==null or userIdList.size() == 0">
            left join (
            select  GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            irri.id
            from
            invoice_reverse_record irri,
            order_cooperator_relation ocr,
            user_partner up
            where
            irri.atom_order_id = ocr.atom_order_id
            and irri.order_id = ocr.order_id
            and ocr.cooperator_id = up.user_id
            group by irri.id
            ) ipi on ipi.id = a.id
        </if>
        <if test="userIdList!=null and userIdList.size != 0">
            inner join (
            select  GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            irri.id
            from
            invoice_reverse_record irri,
            order_cooperator_relation ocr,
            user_partner up
            where
            irri.atom_order_id = ocr.atom_order_id
            and irri.order_id = ocr.order_id
            and ocr.cooperator_id = up.user_id
            and ocr.cooperator_id in
            <foreach item="item" collection="userIdList" open="(" separator="," close=")">#{item}</foreach>
            group by irri.id
            ) ipi on ipi.id = a.id
        </if>-->
        <!--left join (
        select  GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocrh.cooperator_id) cooperator_id,
        irri.id
        from
        invoice_reverse_record irri,
        order_cooperator_relation_history ocrh,
        user_partner up
        where
        irri.atom_order_id = ocrh.atom_order_id
        and irri.order_id = ocrh.order_id
        and ocrh.cooperator_id = up.user_id
        group by irri.id
        ) ipih on ipih.id = irr.id-->
        inner join order_2c_atom_info o2ai on irr.atom_order_id = o2ai.id
        where irr.order_id = #{orderId}
        order by irr.create_time desc
        limit 1
    </select>

</mapper>
