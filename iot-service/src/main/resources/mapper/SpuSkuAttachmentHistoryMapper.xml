<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.SpuSkuAttachmentHistoryMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.SpuSkuAttachmentHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 13 11:31:29 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="spu_code" jdbcType="VARCHAR" property="spuCode" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_key" jdbcType="VARCHAR" property="fileKey" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="sku_offering_version" jdbcType="VARCHAR" property="skuOfferingVersion" />
    <result column="spu_offering_version" jdbcType="VARCHAR" property="spuOfferingVersion" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="spu_id" jdbcType="VARCHAR" property="spuId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 13 11:31:29 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 13 11:31:29 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 13 11:31:29 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, spu_code, sku_code, file_name, file_key, file_url, type, create_time, update_time, 
    sku_offering_version, spu_offering_version, sku_id, spu_id
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.SpuSkuAttachmentHistoryExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 13 11:31:29 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from spu_sku_attachment_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 13 11:31:29 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from spu_sku_attachment_history
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 13 11:31:29 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from spu_sku_attachment_history
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.SpuSkuAttachmentHistoryExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 13 11:31:29 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from spu_sku_attachment_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.SpuSkuAttachmentHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 13 11:31:29 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into spu_sku_attachment_history (id, spu_code, sku_code, 
      file_name, file_key, file_url, 
      type, create_time, update_time, 
      sku_offering_version, spu_offering_version, 
      sku_id, spu_id)
    values (#{id,jdbcType=VARCHAR}, #{spuCode,jdbcType=VARCHAR}, #{skuCode,jdbcType=VARCHAR}, 
      #{fileName,jdbcType=VARCHAR}, #{fileKey,jdbcType=VARCHAR}, #{fileUrl,jdbcType=VARCHAR}, 
      #{type,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{skuOfferingVersion,jdbcType=VARCHAR}, #{spuOfferingVersion,jdbcType=VARCHAR}, 
      #{skuId,jdbcType=VARCHAR}, #{spuId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.SpuSkuAttachmentHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 13 11:31:29 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into spu_sku_attachment_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="skuCode != null">
        sku_code,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="fileKey != null">
        file_key,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="skuOfferingVersion != null">
        sku_offering_version,
      </if>
      <if test="spuOfferingVersion != null">
        spu_offering_version,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="spuId != null">
        spu_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileKey != null">
        #{fileKey,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="skuOfferingVersion != null">
        #{skuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingVersion != null">
        #{spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="spuId != null">
        #{spuId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.SpuSkuAttachmentHistoryExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 13 11:31:29 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from spu_sku_attachment_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 13 11:31:29 CST 2025. by MyBatis Generator, do not modify.
    -->
    update spu_sku_attachment_history
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.spuCode != null">
        spu_code = #{record.spuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCode != null">
        sku_code = #{record.skuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.fileName != null">
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.fileKey != null">
        file_key = #{record.fileKey,jdbcType=VARCHAR},
      </if>
      <if test="record.fileUrl != null">
        file_url = #{record.fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.skuOfferingVersion != null">
        sku_offering_version = #{record.skuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.spuOfferingVersion != null">
        spu_offering_version = #{record.spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.skuId != null">
        sku_id = #{record.skuId,jdbcType=VARCHAR},
      </if>
      <if test="record.spuId != null">
        spu_id = #{record.spuId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 13 11:31:29 CST 2025. by MyBatis Generator, do not modify.
    -->
    update spu_sku_attachment_history
    set id = #{record.id,jdbcType=VARCHAR},
      spu_code = #{record.spuCode,jdbcType=VARCHAR},
      sku_code = #{record.skuCode,jdbcType=VARCHAR},
      file_name = #{record.fileName,jdbcType=VARCHAR},
      file_key = #{record.fileKey,jdbcType=VARCHAR},
      file_url = #{record.fileUrl,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      sku_offering_version = #{record.skuOfferingVersion,jdbcType=VARCHAR},
      spu_offering_version = #{record.spuOfferingVersion,jdbcType=VARCHAR},
      sku_id = #{record.skuId,jdbcType=VARCHAR},
      spu_id = #{record.spuId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.SpuSkuAttachmentHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 13 11:31:29 CST 2025. by MyBatis Generator, do not modify.
    -->
    update spu_sku_attachment_history
    <set>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        sku_code = #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileKey != null">
        file_key = #{fileKey,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="skuOfferingVersion != null">
        sku_offering_version = #{skuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingVersion != null">
        spu_offering_version = #{spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="spuId != null">
        spu_id = #{spuId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.SpuSkuAttachmentHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 13 11:31:29 CST 2025. by MyBatis Generator, do not modify.
    -->
    update spu_sku_attachment_history
    set spu_code = #{spuCode,jdbcType=VARCHAR},
      sku_code = #{skuCode,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_key = #{fileKey,jdbcType=VARCHAR},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      sku_offering_version = #{skuOfferingVersion,jdbcType=VARCHAR},
      spu_offering_version = #{spuOfferingVersion,jdbcType=VARCHAR},
      sku_id = #{skuId,jdbcType=VARCHAR},
      spu_id = #{spuId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 13 11:31:29 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into spu_sku_attachment_history
    (id, spu_code, sku_code, file_name, file_key, file_url, type, create_time, update_time, 
      sku_offering_version, spu_offering_version, sku_id, spu_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.spuCode,jdbcType=VARCHAR}, #{item.skuCode,jdbcType=VARCHAR}, 
        #{item.fileName,jdbcType=VARCHAR}, #{item.fileKey,jdbcType=VARCHAR}, #{item.fileUrl,jdbcType=VARCHAR}, 
        #{item.type,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.skuOfferingVersion,jdbcType=VARCHAR}, #{item.spuOfferingVersion,jdbcType=VARCHAR}, 
        #{item.skuId,jdbcType=VARCHAR}, #{item.spuId,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 13 11:31:29 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into spu_sku_attachment_history (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'spu_code'.toString() == column.value">
          #{item.spuCode,jdbcType=VARCHAR}
        </if>
        <if test="'sku_code'.toString() == column.value">
          #{item.skuCode,jdbcType=VARCHAR}
        </if>
        <if test="'file_name'.toString() == column.value">
          #{item.fileName,jdbcType=VARCHAR}
        </if>
        <if test="'file_key'.toString() == column.value">
          #{item.fileKey,jdbcType=VARCHAR}
        </if>
        <if test="'file_url'.toString() == column.value">
          #{item.fileUrl,jdbcType=VARCHAR}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'sku_offering_version'.toString() == column.value">
          #{item.skuOfferingVersion,jdbcType=VARCHAR}
        </if>
        <if test="'spu_offering_version'.toString() == column.value">
          #{item.spuOfferingVersion,jdbcType=VARCHAR}
        </if>
        <if test="'sku_id'.toString() == column.value">
          #{item.skuId,jdbcType=VARCHAR}
        </if>
        <if test="'spu_id'.toString() == column.value">
          #{item.spuId,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>