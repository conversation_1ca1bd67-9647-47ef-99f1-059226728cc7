<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.DkcardxInventoryMainInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryMainInfo">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="device_version" jdbcType="VARCHAR" property="deviceVersion" />
    <result column="terminal_type" jdbcType="VARCHAR" property="terminalType" />
    <result column="cust_code" jdbcType="VARCHAR" property="custCode" />
    <result column="cust_name" jdbcType="VARCHAR" property="custName" />
    <result column="template_id" jdbcType="VARCHAR" property="templateId" />
    <result column="template_name" jdbcType="VARCHAR" property="templateName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, be_id, province_name, device_version, terminal_type, cust_code, cust_name, template_id, 
    template_name, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryMainInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from dkcardx_inventory_main_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from dkcardx_inventory_main_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from dkcardx_inventory_main_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryMainInfoExample">
    delete from dkcardx_inventory_main_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryMainInfo">
    insert into dkcardx_inventory_main_info (id, be_id, province_name, 
      device_version, terminal_type, cust_code, 
      cust_name, template_id, template_name, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{beId,jdbcType=VARCHAR}, #{provinceName,jdbcType=VARCHAR}, 
      #{deviceVersion,jdbcType=VARCHAR}, #{terminalType,jdbcType=VARCHAR}, #{custCode,jdbcType=VARCHAR}, 
      #{custName,jdbcType=VARCHAR}, #{templateId,jdbcType=VARCHAR}, #{templateName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryMainInfo">
    insert into dkcardx_inventory_main_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="deviceVersion != null">
        device_version,
      </if>
      <if test="terminalType != null">
        terminal_type,
      </if>
      <if test="custCode != null">
        cust_code,
      </if>
      <if test="custName != null">
        cust_name,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="templateName != null">
        template_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="deviceVersion != null">
        #{deviceVersion,jdbcType=VARCHAR},
      </if>
      <if test="terminalType != null">
        #{terminalType,jdbcType=VARCHAR},
      </if>
      <if test="custCode != null">
        #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="custName != null">
        #{custName,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="templateName != null">
        #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryMainInfoExample" resultType="java.lang.Long">
    select count(*) from dkcardx_inventory_main_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update dkcardx_inventory_main_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceName != null">
        province_name = #{record.provinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceVersion != null">
        device_version = #{record.deviceVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.terminalType != null">
        terminal_type = #{record.terminalType,jdbcType=VARCHAR},
      </if>
      <if test="record.custCode != null">
        cust_code = #{record.custCode,jdbcType=VARCHAR},
      </if>
      <if test="record.custName != null">
        cust_name = #{record.custName,jdbcType=VARCHAR},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=VARCHAR},
      </if>
      <if test="record.templateName != null">
        template_name = #{record.templateName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update dkcardx_inventory_main_info
    set id = #{record.id,jdbcType=VARCHAR},
      be_id = #{record.beId,jdbcType=VARCHAR},
      province_name = #{record.provinceName,jdbcType=VARCHAR},
      device_version = #{record.deviceVersion,jdbcType=VARCHAR},
      terminal_type = #{record.terminalType,jdbcType=VARCHAR},
      cust_code = #{record.custCode,jdbcType=VARCHAR},
      cust_name = #{record.custName,jdbcType=VARCHAR},
      template_id = #{record.templateId,jdbcType=VARCHAR},
      template_name = #{record.templateName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryMainInfo">
    update dkcardx_inventory_main_info
    <set>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="deviceVersion != null">
        device_version = #{deviceVersion,jdbcType=VARCHAR},
      </if>
      <if test="terminalType != null">
        terminal_type = #{terminalType,jdbcType=VARCHAR},
      </if>
      <if test="custCode != null">
        cust_code = #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="custName != null">
        cust_name = #{custName,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="templateName != null">
        template_name = #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryMainInfo">
    update dkcardx_inventory_main_info
    set be_id = #{beId,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR},
      device_version = #{deviceVersion,jdbcType=VARCHAR},
      terminal_type = #{terminalType,jdbcType=VARCHAR},
      cust_code = #{custCode,jdbcType=VARCHAR},
      cust_name = #{custName,jdbcType=VARCHAR},
      template_id = #{templateId,jdbcType=VARCHAR},
      template_name = #{templateName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into dkcardx_inventory_main_info
    (id, be_id, province_name, device_version, terminal_type, cust_code, cust_name, template_id, 
      template_name, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.beId,jdbcType=VARCHAR}, #{item.provinceName,jdbcType=VARCHAR}, 
        #{item.deviceVersion,jdbcType=VARCHAR}, #{item.terminalType,jdbcType=VARCHAR}, 
        #{item.custCode,jdbcType=VARCHAR}, #{item.custName,jdbcType=VARCHAR}, #{item.templateId,jdbcType=VARCHAR}, 
        #{item.templateName,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into dkcardx_inventory_main_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'province_name'.toString() == column.value">
          #{item.provinceName,jdbcType=VARCHAR}
        </if>
        <if test="'device_version'.toString() == column.value">
          #{item.deviceVersion,jdbcType=VARCHAR}
        </if>
        <if test="'terminal_type'.toString() == column.value">
          #{item.terminalType,jdbcType=VARCHAR}
        </if>
        <if test="'cust_code'.toString() == column.value">
          #{item.custCode,jdbcType=VARCHAR}
        </if>
        <if test="'cust_name'.toString() == column.value">
          #{item.custName,jdbcType=VARCHAR}
        </if>
        <if test="'template_id'.toString() == column.value">
          #{item.templateId,jdbcType=VARCHAR}
        </if>
        <if test="'template_name'.toString() == column.value">
          #{item.templateName,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>