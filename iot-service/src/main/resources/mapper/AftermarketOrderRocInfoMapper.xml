<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.AftermarketOrderRocInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.AftermarketOrderRocInfo">
    <id column="refund_serv_order_id" jdbcType="VARCHAR" property="refundServOrderId" />
    <result column="serv_order_id" jdbcType="VARCHAR" property="servOrderId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="refunds_type" jdbcType="VARCHAR" property="refundsType" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="picture" jdbcType="VARCHAR" property="picture" />
    <result column="picture_outer_url" jdbcType="VARCHAR" property="pictureOuterUrl" />
    <result column="original_status" jdbcType="INTEGER" property="originalStatus" />
    <result column="inner_status" jdbcType="INTEGER" property="innerStatus" />
    <result column="audit_id" jdbcType="VARCHAR" property="auditId" />
    <result column="audit_result" jdbcType="VARCHAR" property="auditResult" />
    <result column="audit_result_reason" jdbcType="VARCHAR" property="auditResultReason" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    refund_serv_order_id, serv_order_id, order_id, refunds_type, reason, remark, picture, 
    picture_outer_url, original_status, inner_status, audit_id, audit_result, audit_result_reason, 
    create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.AftermarketOrderRocInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from aftermarket_order_roc_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from aftermarket_order_roc_info
    where refund_serv_order_id = #{refundServOrderId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from aftermarket_order_roc_info
    where refund_serv_order_id = #{refundServOrderId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.AftermarketOrderRocInfoExample">
    delete from aftermarket_order_roc_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.AftermarketOrderRocInfo">
    insert into aftermarket_order_roc_info (refund_serv_order_id, serv_order_id, order_id, 
      refunds_type, reason, remark, 
      picture, picture_outer_url, original_status, 
      inner_status, audit_id, audit_result, 
      audit_result_reason, create_time, update_time
      )
    values (#{refundServOrderId,jdbcType=VARCHAR}, #{servOrderId,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, 
      #{refundsType,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{picture,jdbcType=VARCHAR}, #{pictureOuterUrl,jdbcType=VARCHAR}, #{originalStatus,jdbcType=INTEGER}, 
      #{innerStatus,jdbcType=INTEGER}, #{auditId,jdbcType=VARCHAR}, #{auditResult,jdbcType=VARCHAR}, 
      #{auditResultReason,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.AftermarketOrderRocInfo">
    insert into aftermarket_order_roc_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="refundServOrderId != null">
        refund_serv_order_id,
      </if>
      <if test="servOrderId != null">
        serv_order_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="refundsType != null">
        refunds_type,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="picture != null">
        picture,
      </if>
      <if test="pictureOuterUrl != null">
        picture_outer_url,
      </if>
      <if test="originalStatus != null">
        original_status,
      </if>
      <if test="innerStatus != null">
        inner_status,
      </if>
      <if test="auditId != null">
        audit_id,
      </if>
      <if test="auditResult != null">
        audit_result,
      </if>
      <if test="auditResultReason != null">
        audit_result_reason,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="refundServOrderId != null">
        #{refundServOrderId,jdbcType=VARCHAR},
      </if>
      <if test="servOrderId != null">
        #{servOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="refundsType != null">
        #{refundsType,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="picture != null">
        #{picture,jdbcType=VARCHAR},
      </if>
      <if test="pictureOuterUrl != null">
        #{pictureOuterUrl,jdbcType=VARCHAR},
      </if>
      <if test="originalStatus != null">
        #{originalStatus,jdbcType=INTEGER},
      </if>
      <if test="innerStatus != null">
        #{innerStatus,jdbcType=INTEGER},
      </if>
      <if test="auditId != null">
        #{auditId,jdbcType=VARCHAR},
      </if>
      <if test="auditResult != null">
        #{auditResult,jdbcType=VARCHAR},
      </if>
      <if test="auditResultReason != null">
        #{auditResultReason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.AftermarketOrderRocInfoExample" resultType="java.lang.Long">
    select count(*) from aftermarket_order_roc_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update aftermarket_order_roc_info
    <set>
      <if test="record.refundServOrderId != null">
        refund_serv_order_id = #{record.refundServOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.servOrderId != null">
        serv_order_id = #{record.servOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.refundsType != null">
        refunds_type = #{record.refundsType,jdbcType=VARCHAR},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.picture != null">
        picture = #{record.picture,jdbcType=VARCHAR},
      </if>
      <if test="record.pictureOuterUrl != null">
        picture_outer_url = #{record.pictureOuterUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.originalStatus != null">
        original_status = #{record.originalStatus,jdbcType=INTEGER},
      </if>
      <if test="record.innerStatus != null">
        inner_status = #{record.innerStatus,jdbcType=INTEGER},
      </if>
      <if test="record.auditId != null">
        audit_id = #{record.auditId,jdbcType=VARCHAR},
      </if>
      <if test="record.auditResult != null">
        audit_result = #{record.auditResult,jdbcType=VARCHAR},
      </if>
      <if test="record.auditResultReason != null">
        audit_result_reason = #{record.auditResultReason,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update aftermarket_order_roc_info
    set refund_serv_order_id = #{record.refundServOrderId,jdbcType=VARCHAR},
      serv_order_id = #{record.servOrderId,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      refunds_type = #{record.refundsType,jdbcType=VARCHAR},
      reason = #{record.reason,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      picture = #{record.picture,jdbcType=VARCHAR},
      picture_outer_url = #{record.pictureOuterUrl,jdbcType=VARCHAR},
      original_status = #{record.originalStatus,jdbcType=INTEGER},
      inner_status = #{record.innerStatus,jdbcType=INTEGER},
      audit_id = #{record.auditId,jdbcType=VARCHAR},
      audit_result = #{record.auditResult,jdbcType=VARCHAR},
      audit_result_reason = #{record.auditResultReason,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.AftermarketOrderRocInfo">
    update aftermarket_order_roc_info
    <set>
      <if test="servOrderId != null">
        serv_order_id = #{servOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="refundsType != null">
        refunds_type = #{refundsType,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="picture != null">
        picture = #{picture,jdbcType=VARCHAR},
      </if>
      <if test="pictureOuterUrl != null">
        picture_outer_url = #{pictureOuterUrl,jdbcType=VARCHAR},
      </if>
      <if test="originalStatus != null">
        original_status = #{originalStatus,jdbcType=INTEGER},
      </if>
      <if test="innerStatus != null">
        inner_status = #{innerStatus,jdbcType=INTEGER},
      </if>
      <if test="auditId != null">
        audit_id = #{auditId,jdbcType=VARCHAR},
      </if>
      <if test="auditResult != null">
        audit_result = #{auditResult,jdbcType=VARCHAR},
      </if>
      <if test="auditResultReason != null">
        audit_result_reason = #{auditResultReason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where refund_serv_order_id = #{refundServOrderId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.AftermarketOrderRocInfo">
    update aftermarket_order_roc_info
    set serv_order_id = #{servOrderId,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      refunds_type = #{refundsType,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      picture = #{picture,jdbcType=VARCHAR},
      picture_outer_url = #{pictureOuterUrl,jdbcType=VARCHAR},
      original_status = #{originalStatus,jdbcType=INTEGER},
      inner_status = #{innerStatus,jdbcType=INTEGER},
      audit_id = #{auditId,jdbcType=VARCHAR},
      audit_result = #{auditResult,jdbcType=VARCHAR},
      audit_result_reason = #{auditResultReason,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where refund_serv_order_id = #{refundServOrderId,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into aftermarket_order_roc_info
    (refund_serv_order_id, serv_order_id, order_id, refunds_type, reason, remark, picture, 
      picture_outer_url, original_status, inner_status, audit_id, audit_result, audit_result_reason, 
      create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.refundServOrderId,jdbcType=VARCHAR}, #{item.servOrderId,jdbcType=VARCHAR}, 
        #{item.orderId,jdbcType=VARCHAR}, #{item.refundsType,jdbcType=VARCHAR}, #{item.reason,jdbcType=VARCHAR}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.picture,jdbcType=VARCHAR}, #{item.pictureOuterUrl,jdbcType=VARCHAR}, 
        #{item.originalStatus,jdbcType=INTEGER}, #{item.innerStatus,jdbcType=INTEGER}, 
        #{item.auditId,jdbcType=VARCHAR}, #{item.auditResult,jdbcType=VARCHAR}, #{item.auditResultReason,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into aftermarket_order_roc_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'refund_serv_order_id'.toString() == column.value">
          #{item.refundServOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'serv_order_id'.toString() == column.value">
          #{item.servOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'refunds_type'.toString() == column.value">
          #{item.refundsType,jdbcType=VARCHAR}
        </if>
        <if test="'reason'.toString() == column.value">
          #{item.reason,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'picture'.toString() == column.value">
          #{item.picture,jdbcType=VARCHAR}
        </if>
        <if test="'picture_outer_url'.toString() == column.value">
          #{item.pictureOuterUrl,jdbcType=VARCHAR}
        </if>
        <if test="'original_status'.toString() == column.value">
          #{item.originalStatus,jdbcType=INTEGER}
        </if>
        <if test="'inner_status'.toString() == column.value">
          #{item.innerStatus,jdbcType=INTEGER}
        </if>
        <if test="'audit_id'.toString() == column.value">
          #{item.auditId,jdbcType=VARCHAR}
        </if>
        <if test="'audit_result'.toString() == column.value">
          #{item.auditResult,jdbcType=VARCHAR}
        </if>
        <if test="'audit_result_reason'.toString() == column.value">
          #{item.auditResultReason,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>