<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ProductFlowInstanceAttachmentMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.ProductFlowInstanceAttachment">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 10 10:13:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="flow_instance_id" jdbcType="VARCHAR" property="flowInstanceId" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_key" jdbcType="VARCHAR" property="fileKey" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="bytes" jdbcType="BIGINT" property="bytes" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 10 10:13:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 10 10:13:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 10 10:13:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, flow_instance_id, file_name, file_key, file_url, bytes, type, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceAttachmentExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 10 10:13:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from product_flow_instance_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 10 10:13:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from product_flow_instance_attachment
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 10 10:13:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from product_flow_instance_attachment
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceAttachmentExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 10 10:13:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from product_flow_instance_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceAttachment">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 10 10:13:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_attachment (id, flow_instance_id, file_name, 
      file_key, file_url, bytes, 
      type, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{flowInstanceId,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, 
      #{fileKey,jdbcType=VARCHAR}, #{fileUrl,jdbcType=VARCHAR}, #{bytes,jdbcType=BIGINT}, 
      #{type,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceAttachment">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 10 10:13:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_attachment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="flowInstanceId != null">
        flow_instance_id,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="fileKey != null">
        file_key,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="bytes != null">
        bytes,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="flowInstanceId != null">
        #{flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileKey != null">
        #{fileKey,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="bytes != null">
        #{bytes,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceAttachmentExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 10 10:13:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from product_flow_instance_attachment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 10 10:13:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_attachment
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.flowInstanceId != null">
        flow_instance_id = #{record.flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.fileName != null">
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.fileKey != null">
        file_key = #{record.fileKey,jdbcType=VARCHAR},
      </if>
      <if test="record.fileUrl != null">
        file_url = #{record.fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.bytes != null">
        bytes = #{record.bytes,jdbcType=BIGINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 10 10:13:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_attachment
    set id = #{record.id,jdbcType=VARCHAR},
      flow_instance_id = #{record.flowInstanceId,jdbcType=VARCHAR},
      file_name = #{record.fileName,jdbcType=VARCHAR},
      file_key = #{record.fileKey,jdbcType=VARCHAR},
      file_url = #{record.fileUrl,jdbcType=VARCHAR},
      bytes = #{record.bytes,jdbcType=BIGINT},
      type = #{record.type,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceAttachment">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 10 10:13:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_attachment
    <set>
      <if test="flowInstanceId != null">
        flow_instance_id = #{flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileKey != null">
        file_key = #{fileKey,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="bytes != null">
        bytes = #{bytes,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceAttachment">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 10 10:13:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_attachment
    set flow_instance_id = #{flowInstanceId,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_key = #{fileKey,jdbcType=VARCHAR},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      bytes = #{bytes,jdbcType=BIGINT},
      type = #{type,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 10 10:13:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_attachment
    (id, flow_instance_id, file_name, file_key, file_url, bytes, type, create_time, update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.flowInstanceId,jdbcType=VARCHAR}, #{item.fileName,jdbcType=VARCHAR}, 
        #{item.fileKey,jdbcType=VARCHAR}, #{item.fileUrl,jdbcType=VARCHAR}, #{item.bytes,jdbcType=BIGINT}, 
        #{item.type,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Apr 10 10:13:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_attachment (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'flow_instance_id'.toString() == column.value">
          #{item.flowInstanceId,jdbcType=VARCHAR}
        </if>
        <if test="'file_name'.toString() == column.value">
          #{item.fileName,jdbcType=VARCHAR}
        </if>
        <if test="'file_key'.toString() == column.value">
          #{item.fileKey,jdbcType=VARCHAR}
        </if>
        <if test="'file_url'.toString() == column.value">
          #{item.fileUrl,jdbcType=VARCHAR}
        </if>
        <if test="'bytes'.toString() == column.value">
          #{item.bytes,jdbcType=BIGINT}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>