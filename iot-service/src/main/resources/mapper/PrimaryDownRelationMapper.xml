<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.PrimaryDownRelationMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.PrimaryDownRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 02 16:27:05 CST 2023. by MyBatis Generator, do not modify.
    -->
    <id column="primary_down_id" jdbcType="VARCHAR" property="primaryDownId" />
    <result column="primary_user_id" jdbcType="VARCHAR" property="primaryUserId" />
    <result column="down_user_id" jdbcType="VARCHAR" property="downUserId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 02 16:27:05 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 02 16:27:05 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 02 16:27:05 CST 2023. by MyBatis Generator, do not modify.
    -->
    primary_down_id, primary_user_id, down_user_id
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.PrimaryDownRelationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 02 16:27:05 CST 2023. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from primary_down_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 02 16:27:05 CST 2023. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from primary_down_relation
    where primary_down_id = #{primaryDownId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 02 16:27:05 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from primary_down_relation
    where primary_down_id = #{primaryDownId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.PrimaryDownRelationExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 02 16:27:05 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from primary_down_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.PrimaryDownRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 02 16:27:05 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into primary_down_relation (primary_down_id, primary_user_id, down_user_id
      )
    values (#{primaryDownId,jdbcType=VARCHAR}, #{primaryUserId,jdbcType=VARCHAR}, #{downUserId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.PrimaryDownRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 02 16:27:05 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into primary_down_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="primaryDownId != null">
        primary_down_id,
      </if>
      <if test="primaryUserId != null">
        primary_user_id,
      </if>
      <if test="downUserId != null">
        down_user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="primaryDownId != null">
        #{primaryDownId,jdbcType=VARCHAR},
      </if>
      <if test="primaryUserId != null">
        #{primaryUserId,jdbcType=VARCHAR},
      </if>
      <if test="downUserId != null">
        #{downUserId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.PrimaryDownRelationExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 02 16:27:05 CST 2023. by MyBatis Generator, do not modify.
    -->
    select count(*) from primary_down_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 02 16:27:05 CST 2023. by MyBatis Generator, do not modify.
    -->
    update primary_down_relation
    <set>
      <if test="record.primaryDownId != null">
        primary_down_id = #{record.primaryDownId,jdbcType=VARCHAR},
      </if>
      <if test="record.primaryUserId != null">
        primary_user_id = #{record.primaryUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.downUserId != null">
        down_user_id = #{record.downUserId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 02 16:27:05 CST 2023. by MyBatis Generator, do not modify.
    -->
    update primary_down_relation
    set primary_down_id = #{record.primaryDownId,jdbcType=VARCHAR},
      primary_user_id = #{record.primaryUserId,jdbcType=VARCHAR},
      down_user_id = #{record.downUserId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.PrimaryDownRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 02 16:27:05 CST 2023. by MyBatis Generator, do not modify.
    -->
    update primary_down_relation
    <set>
      <if test="primaryUserId != null">
        primary_user_id = #{primaryUserId,jdbcType=VARCHAR},
      </if>
      <if test="downUserId != null">
        down_user_id = #{downUserId,jdbcType=VARCHAR},
      </if>
    </set>
    where primary_down_id = #{primaryDownId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.PrimaryDownRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 02 16:27:05 CST 2023. by MyBatis Generator, do not modify.
    -->
    update primary_down_relation
    set primary_user_id = #{primaryUserId,jdbcType=VARCHAR},
      down_user_id = #{downUserId,jdbcType=VARCHAR}
    where primary_down_id = #{primaryDownId,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 02 16:27:05 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into primary_down_relation
    (primary_down_id, primary_user_id, down_user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.primaryDownId,jdbcType=VARCHAR}, #{item.primaryUserId,jdbcType=VARCHAR}, 
        #{item.downUserId,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Feb 02 16:27:05 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into primary_down_relation (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'primary_down_id'.toString() == column.value">
          #{item.primaryDownId,jdbcType=VARCHAR}
        </if>
        <if test="'primary_user_id'.toString() == column.value">
          #{item.primaryUserId,jdbcType=VARCHAR}
        </if>
        <if test="'down_user_id'.toString() == column.value">
          #{item.downUserId,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>