<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ServicePackLimitSyncMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.ServicePackLimitSync">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="trans_iD" jdbcType="VARCHAR" property="transId" />
    <result column="company_iD" jdbcType="VARCHAR" property="companyId" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="service_code" jdbcType="VARCHAR" property="serviceCode" />
    <result column="service_name" jdbcType="VARCHAR" property="serviceName" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="efftime" jdbcType="VARCHAR" property="efftime" />
    <result column="exptime" jdbcType="VARCHAR" property="exptime" />
    <result column="iot_limit" jdbcType="DOUBLE" property="iotLimit" />
    <result column="oper_type" jdbcType="VARCHAR" property="operType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, trans_iD, company_iD, product_name, service_code, service_name, status, efftime, 
    exptime, iot_limit, oper_type, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ServicePackLimitSyncExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from service_pack_limit_sync
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from service_pack_limit_sync
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from service_pack_limit_sync
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ServicePackLimitSyncExample">
    delete from service_pack_limit_sync
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.ServicePackLimitSync">
    insert into service_pack_limit_sync (id, trans_iD, company_iD, 
      product_name, service_code, service_name, 
      status, efftime, exptime, 
      iot_limit, oper_type, create_time, 
      update_time)
    values (#{id,jdbcType=VARCHAR}, #{transId,jdbcType=VARCHAR}, #{companyId,jdbcType=VARCHAR}, 
      #{productName,jdbcType=VARCHAR}, #{serviceCode,jdbcType=VARCHAR}, #{serviceName,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{efftime,jdbcType=VARCHAR}, #{exptime,jdbcType=VARCHAR}, 
      #{iotLimit,jdbcType=DOUBLE}, #{operType,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ServicePackLimitSync">
    insert into service_pack_limit_sync
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="transId != null">
        trans_iD,
      </if>
      <if test="companyId != null">
        company_iD,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="serviceCode != null">
        service_code,
      </if>
      <if test="serviceName != null">
        service_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="efftime != null">
        efftime,
      </if>
      <if test="exptime != null">
        exptime,
      </if>
      <if test="iotLimit != null">
        iot_limit,
      </if>
      <if test="operType != null">
        oper_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="transId != null">
        #{transId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="serviceCode != null">
        #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="serviceName != null">
        #{serviceName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="efftime != null">
        #{efftime,jdbcType=VARCHAR},
      </if>
      <if test="exptime != null">
        #{exptime,jdbcType=VARCHAR},
      </if>
      <if test="iotLimit != null">
        #{iotLimit,jdbcType=DOUBLE},
      </if>
      <if test="operType != null">
        #{operType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ServicePackLimitSyncExample" resultType="java.lang.Long">
    select count(*) from service_pack_limit_sync
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update service_pack_limit_sync
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.transId != null">
        trans_iD = #{record.transId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyId != null">
        company_iD = #{record.companyId,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceCode != null">
        service_code = #{record.serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceName != null">
        service_name = #{record.serviceName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.efftime != null">
        efftime = #{record.efftime,jdbcType=VARCHAR},
      </if>
      <if test="record.exptime != null">
        exptime = #{record.exptime,jdbcType=VARCHAR},
      </if>
      <if test="record.iotLimit != null">
        iot_limit = #{record.iotLimit,jdbcType=DOUBLE},
      </if>
      <if test="record.operType != null">
        oper_type = #{record.operType,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update service_pack_limit_sync
    set id = #{record.id,jdbcType=VARCHAR},
      trans_iD = #{record.transId,jdbcType=VARCHAR},
      company_iD = #{record.companyId,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      service_code = #{record.serviceCode,jdbcType=VARCHAR},
      service_name = #{record.serviceName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      efftime = #{record.efftime,jdbcType=VARCHAR},
      exptime = #{record.exptime,jdbcType=VARCHAR},
      iot_limit = #{record.iotLimit,jdbcType=DOUBLE},
      oper_type = #{record.operType,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ServicePackLimitSync">
    update service_pack_limit_sync
    <set>
      <if test="transId != null">
        trans_iD = #{transId,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_iD = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="serviceCode != null">
        service_code = #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="serviceName != null">
        service_name = #{serviceName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="efftime != null">
        efftime = #{efftime,jdbcType=VARCHAR},
      </if>
      <if test="exptime != null">
        exptime = #{exptime,jdbcType=VARCHAR},
      </if>
      <if test="iotLimit != null">
        iot_limit = #{iotLimit,jdbcType=DOUBLE},
      </if>
      <if test="operType != null">
        oper_type = #{operType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.ServicePackLimitSync">
    update service_pack_limit_sync
    set trans_iD = #{transId,jdbcType=VARCHAR},
      company_iD = #{companyId,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      service_code = #{serviceCode,jdbcType=VARCHAR},
      service_name = #{serviceName,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      efftime = #{efftime,jdbcType=VARCHAR},
      exptime = #{exptime,jdbcType=VARCHAR},
      iot_limit = #{iotLimit,jdbcType=DOUBLE},
      oper_type = #{operType,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into service_pack_limit_sync
    (id, trans_iD, company_iD, product_name, service_code, service_name, status, efftime, 
      exptime, iot_limit, oper_type, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.transId,jdbcType=VARCHAR}, #{item.companyId,jdbcType=VARCHAR}, 
        #{item.productName,jdbcType=VARCHAR}, #{item.serviceCode,jdbcType=VARCHAR}, #{item.serviceName,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=VARCHAR}, #{item.efftime,jdbcType=VARCHAR}, #{item.exptime,jdbcType=VARCHAR}, 
        #{item.iotLimit,jdbcType=DOUBLE}, #{item.operType,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into service_pack_limit_sync (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'trans_iD'.toString() == column.value">
          #{item.transId,jdbcType=VARCHAR}
        </if>
        <if test="'company_iD'.toString() == column.value">
          #{item.companyId,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'service_code'.toString() == column.value">
          #{item.serviceCode,jdbcType=VARCHAR}
        </if>
        <if test="'service_name'.toString() == column.value">
          #{item.serviceName,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=VARCHAR}
        </if>
        <if test="'efftime'.toString() == column.value">
          #{item.efftime,jdbcType=VARCHAR}
        </if>
        <if test="'exptime'.toString() == column.value">
          #{item.exptime,jdbcType=VARCHAR}
        </if>
        <if test="'iot_limit'.toString() == column.value">
          #{item.iotLimit,jdbcType=DOUBLE}
        </if>
        <if test="'oper_type'.toString() == column.value">
          #{item.operType,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>