<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.Order2cAtomHistoryMapper">
    <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.Order2cAtomHistory">
        <result column="atom_order_id" jdbcType="VARCHAR" property="atomOrderId"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="refund_order_id" jdbcType="VARCHAR" property="refundOrderId"/>
        <result column="operate_type" jdbcType="INTEGER" property="operateType"/>
        <result column="operator_id" jdbcType="VARCHAR" property="operatorId"/>
        <result column="inner_status" jdbcType="INTEGER" property="innerStatus"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        atom_order_id
        , order_id, refund_order_id, operate_type, operator_id, inner_status,
    create_time, update_time
    </sql>
    <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomHistoryExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from order_2c_atom_history
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomHistoryExample">
        delete from order_2c_atom_history
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomHistory">
        insert into order_2c_atom_history (atom_order_id, order_id, refund_order_id,
                                           operate_type, operator_id, inner_status,
                                           create_time, update_time)
        values (#{atomOrderId,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{refundOrderId,jdbcType=VARCHAR},
                #{operateType,jdbcType=INTEGER}, #{operatorId,jdbcType=VARCHAR}, #{innerStatus,jdbcType=INTEGER},
                #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomHistory">
        insert into order_2c_atom_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="atomOrderId != null">
                atom_order_id,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
            <if test="refundOrderId != null">
                refund_order_id,
            </if>
            <if test="operateType != null">
                operate_type,
            </if>
            <if test="operatorId != null">
                operator_id,
            </if>
            <if test="innerStatus != null">
                inner_status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="atomOrderId != null">
                #{atomOrderId,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=VARCHAR},
            </if>
            <if test="refundOrderId != null">
                #{refundOrderId,jdbcType=VARCHAR},
            </if>
            <if test="operateType != null">
                #{operateType,jdbcType=INTEGER},
            </if>
            <if test="operatorId != null">
                #{operatorId,jdbcType=VARCHAR},
            </if>
            <if test="innerStatus != null">
                #{innerStatus,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomHistoryExample"
            resultType="java.lang.Long">
        select count(*) from order_2c_atom_history
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update order_2c_atom_history
        <set>
            <if test="record.atomOrderId != null">
                atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
            </if>
            <if test="record.orderId != null">
                order_id = #{record.orderId,jdbcType=VARCHAR},
            </if>
            <if test="record.refundOrderId != null">
                refund_order_id = #{record.refundOrderId,jdbcType=VARCHAR},
            </if>
            <if test="record.operateType != null">
                operate_type = #{record.operateType,jdbcType=INTEGER},
            </if>
            <if test="record.operatorId != null">
                operator_id = #{record.operatorId,jdbcType=VARCHAR},
            </if>
            <if test="record.innerStatus != null">
                inner_status = #{record.innerStatus,jdbcType=INTEGER},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updateTime != null">
                update_time = #{record.updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update order_2c_atom_history
        set atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
        order_id = #{record.orderId,jdbcType=VARCHAR},
        refund_order_id = #{record.refundOrderId,jdbcType=VARCHAR},
        operate_type = #{record.operateType,jdbcType=INTEGER},
        operator_id = #{record.operatorId,jdbcType=VARCHAR},
        inner_status = #{record.innerStatus,jdbcType=INTEGER},
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
        update_time = #{record.updateTime,jdbcType=TIMESTAMP}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <insert id="batchInsert" parameterType="map">
        insert into order_2c_atom_history
        (atom_order_id, order_id, refund_order_id, operate_type, operator_id, inner_status,
        create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.atomOrderId,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR},
            #{item.refundOrderId,jdbcType=VARCHAR},
            #{item.operateType,jdbcType=INTEGER}, #{item.operatorId,jdbcType=VARCHAR},
            #{item.innerStatus,jdbcType=INTEGER},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="batchInsertSelective" parameterType="map">
        insert into order_2c_atom_history (
        <foreach collection="selective" item="column" separator=",">
            ${column.escapedColumnName}
        </foreach>
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            <foreach collection="selective" item="column" separator=",">
                <if test="'atom_order_id'.toString() == column.value">
                    #{item.atomOrderId,jdbcType=VARCHAR}
                </if>
                <if test="'order_id'.toString() == column.value">
                    #{item.orderId,jdbcType=VARCHAR}
                </if>
                <if test="'refund_order_id'.toString() == column.value">
                    #{item.refundOrderId,jdbcType=VARCHAR}
                </if>
                <if test="'operate_type'.toString() == column.value">
                    #{item.operateType,jdbcType=INTEGER}
                </if>
                <if test="'operator_id'.toString() == column.value">
                    #{item.operatorId,jdbcType=VARCHAR}
                </if>
                <if test="'inner_status'.toString() == column.value">
                    #{item.innerStatus,jdbcType=INTEGER}
                </if>
                <if test="'create_time'.toString() == column.value">
                    #{item.createTime,jdbcType=TIMESTAMP}
                </if>
                <if test="'update_time'.toString() == column.value">
                    #{item.updateTime,jdbcType=TIMESTAMP}
                </if>
            </foreach>
            )
        </foreach>
    </insert>
</mapper>