<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.DkcardxInventoryInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 10 14:33:56 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="inventory_id" jdbcType="VARCHAR" property="inventoryId" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="reserve_quatity" jdbcType="INTEGER" property="reserveQuatity" />
    <result column="current_inventory" jdbcType="INTEGER" property="currentInventory" />
    <result column="total_inventory" jdbcType="INTEGER" property="totalInventory" />
    <result column="inventory_status" jdbcType="INTEGER" property="inventoryStatus" />
    <result column="inventory_warn" jdbcType="INTEGER" property="inventoryWarn" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 10 14:33:56 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 10 14:33:56 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 10 14:33:56 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, inventory_id, be_id, location, province_name, city_name, reserve_quatity, current_inventory, 
    total_inventory, inventory_status, inventory_warn, update_time, create_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 10 14:33:56 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from dkcardx_inventory_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 10 14:33:56 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from dkcardx_inventory_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 10 14:33:56 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from dkcardx_inventory_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 10 14:33:56 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from dkcardx_inventory_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 10 14:33:56 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into dkcardx_inventory_info (id, inventory_id, be_id, 
      location, province_name, city_name, 
      reserve_quatity, current_inventory, total_inventory, 
      inventory_status, inventory_warn, update_time, 
      create_time)
    values (#{id,jdbcType=VARCHAR}, #{inventoryId,jdbcType=VARCHAR}, #{beId,jdbcType=VARCHAR}, 
      #{location,jdbcType=VARCHAR}, #{provinceName,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR}, 
      #{reserveQuatity,jdbcType=INTEGER}, #{currentInventory,jdbcType=INTEGER}, #{totalInventory,jdbcType=INTEGER}, 
      #{inventoryStatus,jdbcType=INTEGER}, #{inventoryWarn,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 10 14:33:56 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into dkcardx_inventory_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="inventoryId != null">
        inventory_id,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="reserveQuatity != null">
        reserve_quatity,
      </if>
      <if test="currentInventory != null">
        current_inventory,
      </if>
      <if test="totalInventory != null">
        total_inventory,
      </if>
      <if test="inventoryStatus != null">
        inventory_status,
      </if>
      <if test="inventoryWarn != null">
        inventory_warn,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="inventoryId != null">
        #{inventoryId,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="reserveQuatity != null">
        #{reserveQuatity,jdbcType=INTEGER},
      </if>
      <if test="currentInventory != null">
        #{currentInventory,jdbcType=INTEGER},
      </if>
      <if test="totalInventory != null">
        #{totalInventory,jdbcType=INTEGER},
      </if>
      <if test="inventoryStatus != null">
        #{inventoryStatus,jdbcType=INTEGER},
      </if>
      <if test="inventoryWarn != null">
        #{inventoryWarn,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 10 14:33:56 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from dkcardx_inventory_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 10 14:33:56 CST 2024. by MyBatis Generator, do not modify.
    -->
    update dkcardx_inventory_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.inventoryId != null">
        inventory_id = #{record.inventoryId,jdbcType=VARCHAR},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.location != null">
        location = #{record.location,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceName != null">
        province_name = #{record.provinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.cityName != null">
        city_name = #{record.cityName,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveQuatity != null">
        reserve_quatity = #{record.reserveQuatity,jdbcType=INTEGER},
      </if>
      <if test="record.currentInventory != null">
        current_inventory = #{record.currentInventory,jdbcType=INTEGER},
      </if>
      <if test="record.totalInventory != null">
        total_inventory = #{record.totalInventory,jdbcType=INTEGER},
      </if>
      <if test="record.inventoryStatus != null">
        inventory_status = #{record.inventoryStatus,jdbcType=INTEGER},
      </if>
      <if test="record.inventoryWarn != null">
        inventory_warn = #{record.inventoryWarn,jdbcType=INTEGER},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 10 14:33:56 CST 2024. by MyBatis Generator, do not modify.
    -->
    update dkcardx_inventory_info
    set id = #{record.id,jdbcType=VARCHAR},
      inventory_id = #{record.inventoryId,jdbcType=VARCHAR},
      be_id = #{record.beId,jdbcType=VARCHAR},
      location = #{record.location,jdbcType=VARCHAR},
      province_name = #{record.provinceName,jdbcType=VARCHAR},
      city_name = #{record.cityName,jdbcType=VARCHAR},
      reserve_quatity = #{record.reserveQuatity,jdbcType=INTEGER},
      current_inventory = #{record.currentInventory,jdbcType=INTEGER},
      total_inventory = #{record.totalInventory,jdbcType=INTEGER},
      inventory_status = #{record.inventoryStatus,jdbcType=INTEGER},
      inventory_warn = #{record.inventoryWarn,jdbcType=INTEGER},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 10 14:33:56 CST 2024. by MyBatis Generator, do not modify.
    -->
    update dkcardx_inventory_info
    <set>
      <if test="inventoryId != null">
        inventory_id = #{inventoryId,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="reserveQuatity != null">
        reserve_quatity = #{reserveQuatity,jdbcType=INTEGER},
      </if>
      <if test="currentInventory != null">
        current_inventory = #{currentInventory,jdbcType=INTEGER},
      </if>
      <if test="totalInventory != null">
        total_inventory = #{totalInventory,jdbcType=INTEGER},
      </if>
      <if test="inventoryStatus != null">
        inventory_status = #{inventoryStatus,jdbcType=INTEGER},
      </if>
      <if test="inventoryWarn != null">
        inventory_warn = #{inventoryWarn,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 10 14:33:56 CST 2024. by MyBatis Generator, do not modify.
    -->
    update dkcardx_inventory_info
    set inventory_id = #{inventoryId,jdbcType=VARCHAR},
      be_id = #{beId,jdbcType=VARCHAR},
      location = #{location,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      reserve_quatity = #{reserveQuatity,jdbcType=INTEGER},
      current_inventory = #{currentInventory,jdbcType=INTEGER},
      total_inventory = #{totalInventory,jdbcType=INTEGER},
      inventory_status = #{inventoryStatus,jdbcType=INTEGER},
      inventory_warn = #{inventoryWarn,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 10 14:33:56 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into dkcardx_inventory_info
    (id, inventory_id, be_id, location, province_name, city_name, reserve_quatity, current_inventory, 
      total_inventory, inventory_status, inventory_warn, update_time, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.inventoryId,jdbcType=VARCHAR}, #{item.beId,jdbcType=VARCHAR}, 
        #{item.location,jdbcType=VARCHAR}, #{item.provinceName,jdbcType=VARCHAR}, #{item.cityName,jdbcType=VARCHAR}, 
        #{item.reserveQuatity,jdbcType=INTEGER}, #{item.currentInventory,jdbcType=INTEGER}, 
        #{item.totalInventory,jdbcType=INTEGER}, #{item.inventoryStatus,jdbcType=INTEGER}, 
        #{item.inventoryWarn,jdbcType=INTEGER}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 10 14:33:56 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into dkcardx_inventory_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'inventory_id'.toString() == column.value">
          #{item.inventoryId,jdbcType=VARCHAR}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'location'.toString() == column.value">
          #{item.location,jdbcType=VARCHAR}
        </if>
        <if test="'province_name'.toString() == column.value">
          #{item.provinceName,jdbcType=VARCHAR}
        </if>
        <if test="'city_name'.toString() == column.value">
          #{item.cityName,jdbcType=VARCHAR}
        </if>
        <if test="'reserve_quatity'.toString() == column.value">
          #{item.reserveQuatity,jdbcType=INTEGER}
        </if>
        <if test="'current_inventory'.toString() == column.value">
          #{item.currentInventory,jdbcType=INTEGER}
        </if>
        <if test="'total_inventory'.toString() == column.value">
          #{item.totalInventory,jdbcType=INTEGER}
        </if>
        <if test="'inventory_status'.toString() == column.value">
          #{item.inventoryStatus,jdbcType=INTEGER}
        </if>
        <if test="'inventory_warn'.toString() == column.value">
          #{item.inventoryWarn,jdbcType=INTEGER}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>