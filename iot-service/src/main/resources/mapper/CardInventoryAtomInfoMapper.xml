<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.CardInventoryAtomInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.CardInventoryAtomInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Dec 19 10:21:32 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="card_inventory_main_id" jdbcType="VARCHAR" property="cardInventoryMainId" />
    <result column="atom_id" jdbcType="VARCHAR" property="atomId" />
    <result column="atom_inventory" jdbcType="INTEGER" property="atomInventory" />
    <result column="spu_code" jdbcType="VARCHAR" property="spuCode" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="offering_code" jdbcType="VARCHAR" property="offeringCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Dec 19 10:21:32 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Dec 19 10:21:32 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Dec 19 10:21:32 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, card_inventory_main_id, atom_id, atom_inventory, spu_code, sku_code, offering_code, 
    create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.CardInventoryAtomInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Dec 19 10:21:32 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from card_inventory_atom_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Dec 19 10:21:32 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from card_inventory_atom_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Dec 19 10:21:32 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from card_inventory_atom_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.CardInventoryAtomInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Dec 19 10:21:32 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from card_inventory_atom_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.CardInventoryAtomInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Dec 19 10:21:32 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into card_inventory_atom_info (id, card_inventory_main_id, atom_id, 
      atom_inventory, spu_code, sku_code, 
      offering_code, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{cardInventoryMainId,jdbcType=VARCHAR}, #{atomId,jdbcType=VARCHAR}, 
      #{atomInventory,jdbcType=INTEGER}, #{spuCode,jdbcType=VARCHAR}, #{skuCode,jdbcType=VARCHAR}, 
      #{offeringCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.CardInventoryAtomInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Dec 19 10:21:32 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into card_inventory_atom_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="cardInventoryMainId != null">
        card_inventory_main_id,
      </if>
      <if test="atomId != null">
        atom_id,
      </if>
      <if test="atomInventory != null">
        atom_inventory,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="skuCode != null">
        sku_code,
      </if>
      <if test="offeringCode != null">
        offering_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="cardInventoryMainId != null">
        #{cardInventoryMainId,jdbcType=VARCHAR},
      </if>
      <if test="atomId != null">
        #{atomId,jdbcType=VARCHAR},
      </if>
      <if test="atomInventory != null">
        #{atomInventory,jdbcType=INTEGER},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="offeringCode != null">
        #{offeringCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.CardInventoryAtomInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Dec 19 10:21:32 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from card_inventory_atom_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Dec 19 10:21:32 CST 2024. by MyBatis Generator, do not modify.
    -->
    update card_inventory_atom_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.cardInventoryMainId != null">
        card_inventory_main_id = #{record.cardInventoryMainId,jdbcType=VARCHAR},
      </if>
      <if test="record.atomId != null">
        atom_id = #{record.atomId,jdbcType=VARCHAR},
      </if>
      <if test="record.atomInventory != null">
        atom_inventory = #{record.atomInventory,jdbcType=INTEGER},
      </if>
      <if test="record.spuCode != null">
        spu_code = #{record.spuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCode != null">
        sku_code = #{record.skuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.offeringCode != null">
        offering_code = #{record.offeringCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Dec 19 10:21:32 CST 2024. by MyBatis Generator, do not modify.
    -->
    update card_inventory_atom_info
    set id = #{record.id,jdbcType=VARCHAR},
      card_inventory_main_id = #{record.cardInventoryMainId,jdbcType=VARCHAR},
      atom_id = #{record.atomId,jdbcType=VARCHAR},
      atom_inventory = #{record.atomInventory,jdbcType=INTEGER},
      spu_code = #{record.spuCode,jdbcType=VARCHAR},
      sku_code = #{record.skuCode,jdbcType=VARCHAR},
      offering_code = #{record.offeringCode,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.CardInventoryAtomInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Dec 19 10:21:32 CST 2024. by MyBatis Generator, do not modify.
    -->
    update card_inventory_atom_info
    <set>
      <if test="cardInventoryMainId != null">
        card_inventory_main_id = #{cardInventoryMainId,jdbcType=VARCHAR},
      </if>
      <if test="atomId != null">
        atom_id = #{atomId,jdbcType=VARCHAR},
      </if>
      <if test="atomInventory != null">
        atom_inventory = #{atomInventory,jdbcType=INTEGER},
      </if>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        sku_code = #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="offeringCode != null">
        offering_code = #{offeringCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.CardInventoryAtomInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Dec 19 10:21:32 CST 2024. by MyBatis Generator, do not modify.
    -->
    update card_inventory_atom_info
    set card_inventory_main_id = #{cardInventoryMainId,jdbcType=VARCHAR},
      atom_id = #{atomId,jdbcType=VARCHAR},
      atom_inventory = #{atomInventory,jdbcType=INTEGER},
      spu_code = #{spuCode,jdbcType=VARCHAR},
      sku_code = #{skuCode,jdbcType=VARCHAR},
      offering_code = #{offeringCode,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Dec 19 10:21:32 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into card_inventory_atom_info
    (id, card_inventory_main_id, atom_id, atom_inventory, spu_code, sku_code, offering_code, 
      create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.cardInventoryMainId,jdbcType=VARCHAR}, #{item.atomId,jdbcType=VARCHAR}, 
        #{item.atomInventory,jdbcType=INTEGER}, #{item.spuCode,jdbcType=VARCHAR}, #{item.skuCode,jdbcType=VARCHAR}, 
        #{item.offeringCode,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Dec 19 10:21:32 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into card_inventory_atom_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'card_inventory_main_id'.toString() == column.value">
          #{item.cardInventoryMainId,jdbcType=VARCHAR}
        </if>
        <if test="'atom_id'.toString() == column.value">
          #{item.atomId,jdbcType=VARCHAR}
        </if>
        <if test="'atom_inventory'.toString() == column.value">
          #{item.atomInventory,jdbcType=INTEGER}
        </if>
        <if test="'spu_code'.toString() == column.value">
          #{item.spuCode,jdbcType=VARCHAR}
        </if>
        <if test="'sku_code'.toString() == column.value">
          #{item.skuCode,jdbcType=VARCHAR}
        </if>
        <if test="'offering_code'.toString() == column.value">
          #{item.offeringCode,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>