<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.CardMallSyncMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.CardMallSync">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Dec 06 09:49:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="card_info_id" jdbcType="VARCHAR" property="cardInfoId" />
    <result column="card_inventory_main_id" jdbcType="VARCHAR" property="cardInventoryMainId" />
    <result column="card_type" jdbcType="VARCHAR" property="cardType" />
    <result column="msisdn" jdbcType="VARCHAR" property="msisdn" />
    <result column="iccid" jdbcType="VARCHAR" property="iccid" />
    <result column="card_status" jdbcType="VARCHAR" property="cardStatus" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="atom_order_id" jdbcType="VARCHAR" property="atomOrderId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Dec 06 09:49:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Dec 06 09:49:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Dec 06 09:49:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, card_info_id, card_inventory_main_id, card_type, msisdn, iccid, card_status, 
    order_id, atom_order_id, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.CardMallSyncExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Dec 06 09:49:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from card_mall_sync
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Dec 06 09:49:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from card_mall_sync
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Dec 06 09:49:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from card_mall_sync
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.CardMallSyncExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Dec 06 09:49:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from card_mall_sync
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.CardMallSync">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Dec 06 09:49:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into card_mall_sync (id, card_info_id, card_inventory_main_id, 
      card_type, msisdn, iccid, 
      card_status, order_id, atom_order_id, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{cardInfoId,jdbcType=VARCHAR}, #{cardInventoryMainId,jdbcType=VARCHAR}, 
      #{cardType,jdbcType=VARCHAR}, #{msisdn,jdbcType=VARCHAR}, #{iccid,jdbcType=VARCHAR}, 
      #{cardStatus,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{atomOrderId,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.CardMallSync">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Dec 06 09:49:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into card_mall_sync
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="cardInfoId != null">
        card_info_id,
      </if>
      <if test="cardInventoryMainId != null">
        card_inventory_main_id,
      </if>
      <if test="cardType != null">
        card_type,
      </if>
      <if test="msisdn != null">
        msisdn,
      </if>
      <if test="iccid != null">
        iccid,
      </if>
      <if test="cardStatus != null">
        card_status,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="atomOrderId != null">
        atom_order_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="cardInfoId != null">
        #{cardInfoId,jdbcType=VARCHAR},
      </if>
      <if test="cardInventoryMainId != null">
        #{cardInventoryMainId,jdbcType=VARCHAR},
      </if>
      <if test="cardType != null">
        #{cardType,jdbcType=VARCHAR},
      </if>
      <if test="msisdn != null">
        #{msisdn,jdbcType=VARCHAR},
      </if>
      <if test="iccid != null">
        #{iccid,jdbcType=VARCHAR},
      </if>
      <if test="cardStatus != null">
        #{cardStatus,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="atomOrderId != null">
        #{atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.CardMallSyncExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Dec 06 09:49:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from card_mall_sync
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Dec 06 09:49:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    update card_mall_sync
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.cardInfoId != null">
        card_info_id = #{record.cardInfoId,jdbcType=VARCHAR},
      </if>
      <if test="record.cardInventoryMainId != null">
        card_inventory_main_id = #{record.cardInventoryMainId,jdbcType=VARCHAR},
      </if>
      <if test="record.cardType != null">
        card_type = #{record.cardType,jdbcType=VARCHAR},
      </if>
      <if test="record.msisdn != null">
        msisdn = #{record.msisdn,jdbcType=VARCHAR},
      </if>
      <if test="record.iccid != null">
        iccid = #{record.iccid,jdbcType=VARCHAR},
      </if>
      <if test="record.cardStatus != null">
        card_status = #{record.cardStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOrderId != null">
        atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Dec 06 09:49:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    update card_mall_sync
    set id = #{record.id,jdbcType=VARCHAR},
      card_info_id = #{record.cardInfoId,jdbcType=VARCHAR},
      card_inventory_main_id = #{record.cardInventoryMainId,jdbcType=VARCHAR},
      card_type = #{record.cardType,jdbcType=VARCHAR},
      msisdn = #{record.msisdn,jdbcType=VARCHAR},
      iccid = #{record.iccid,jdbcType=VARCHAR},
      card_status = #{record.cardStatus,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.CardMallSync">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Dec 06 09:49:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    update card_mall_sync
    <set>
      <if test="cardInfoId != null">
        card_info_id = #{cardInfoId,jdbcType=VARCHAR},
      </if>
      <if test="cardInventoryMainId != null">
        card_inventory_main_id = #{cardInventoryMainId,jdbcType=VARCHAR},
      </if>
      <if test="cardType != null">
        card_type = #{cardType,jdbcType=VARCHAR},
      </if>
      <if test="msisdn != null">
        msisdn = #{msisdn,jdbcType=VARCHAR},
      </if>
      <if test="iccid != null">
        iccid = #{iccid,jdbcType=VARCHAR},
      </if>
      <if test="cardStatus != null">
        card_status = #{cardStatus,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="atomOrderId != null">
        atom_order_id = #{atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.CardMallSync">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Dec 06 09:49:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    update card_mall_sync
    set card_info_id = #{cardInfoId,jdbcType=VARCHAR},
      card_inventory_main_id = #{cardInventoryMainId,jdbcType=VARCHAR},
      card_type = #{cardType,jdbcType=VARCHAR},
      msisdn = #{msisdn,jdbcType=VARCHAR},
      iccid = #{iccid,jdbcType=VARCHAR},
      card_status = #{cardStatus,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      atom_order_id = #{atomOrderId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Dec 06 09:49:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into card_mall_sync
    (id, card_info_id, card_inventory_main_id, card_type, msisdn, iccid, card_status, 
      order_id, atom_order_id, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.cardInfoId,jdbcType=VARCHAR}, #{item.cardInventoryMainId,jdbcType=VARCHAR}, 
        #{item.cardType,jdbcType=VARCHAR}, #{item.msisdn,jdbcType=VARCHAR}, #{item.iccid,jdbcType=VARCHAR}, 
        #{item.cardStatus,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR}, #{item.atomOrderId,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Dec 06 09:49:55 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into card_mall_sync (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'card_info_id'.toString() == column.value">
          #{item.cardInfoId,jdbcType=VARCHAR}
        </if>
        <if test="'card_inventory_main_id'.toString() == column.value">
          #{item.cardInventoryMainId,jdbcType=VARCHAR}
        </if>
        <if test="'card_type'.toString() == column.value">
          #{item.cardType,jdbcType=VARCHAR}
        </if>
        <if test="'msisdn'.toString() == column.value">
          #{item.msisdn,jdbcType=VARCHAR}
        </if>
        <if test="'iccid'.toString() == column.value">
          #{item.iccid,jdbcType=VARCHAR}
        </if>
        <if test="'card_status'.toString() == column.value">
          #{item.cardStatus,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'atom_order_id'.toString() == column.value">
          #{item.atomOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>