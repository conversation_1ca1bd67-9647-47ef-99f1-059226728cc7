<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.K3syncStatisCityMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.K3syncStatisCity">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 25 14:19:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="contract_num" jdbcType="VARCHAR" property="contractNum" />
    <result column="contract_name" jdbcType="VARCHAR" property="contractName" />
    <result column="contract_dept" jdbcType="VARCHAR" property="contractDept" />
    <result column="contract_statis_type" jdbcType="VARCHAR" property="contractStatisType" />
    <result column="contract_seller" jdbcType="VARCHAR" property="contractSeller" />
    <result column="contract_type" jdbcType="VARCHAR" property="contractType" />
    <result column="money_unit" jdbcType="VARCHAR" property="moneyUnit" />
    <result column="sell_unit" jdbcType="VARCHAR" property="sellUnit" />
    <result column="product_type" jdbcType="VARCHAR" property="productType" />
    <result column="order_province_name" jdbcType="VARCHAR" property="orderProvinceName" />
    <result column="order_province_code" jdbcType="VARCHAR" property="orderProvinceCode" />
    <result column="order_city_name" jdbcType="VARCHAR" property="orderCityName" />
    <result column="order_city_code" jdbcType="VARCHAR" property="orderCityCode" />
    <result column="order_count" jdbcType="VARCHAR" property="orderCount" />
    <result column="total_price" jdbcType="BIGINT" property="totalPrice" />
    <result column="k3_ret_num" jdbcType="VARCHAR" property="k3RetNum" />
    <result column="k3_sync_status" jdbcType="VARCHAR" property="k3SyncStatus" />
    <result column="k3_commit_status" jdbcType="VARCHAR" property="k3CommitStatus" />
    <result column="seller_org_id" jdbcType="VARCHAR" property="sellerOrgId" />
    <result column="seller_team_id" jdbcType="VARCHAR" property="sellerTeamId" />
    <result column="seller_dept_id" jdbcType="VARCHAR" property="sellerDeptId" />
    <result column="seller_phone" jdbcType="VARCHAR" property="sellerPhone" />
    <result column="cost_center" jdbcType="VARCHAR" property="costCenter" />
    <result column="project" jdbcType="VARCHAR" property="project" />
    <result column="sub_project" jdbcType="VARCHAR" property="subProject" />
    <result column="sync_suc_time" jdbcType="TIMESTAMP" property="syncSucTime" />
    <result column="commit_suc_time" jdbcType="TIMESTAMP" property="commitSucTime" />
    <result column="custom_code" jdbcType="VARCHAR" property="customCode" />
    <result column="buyer_province" jdbcType="VARCHAR" property="buyerProvince" />
    <result column="buyer_province_code" jdbcType="VARCHAR" property="buyerProvinceCode" />
    <result column="buyer_city_code" jdbcType="VARCHAR" property="buyerCityCode" />
    <result column="buyer_city" jdbcType="VARCHAR" property="buyerCity" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="related_order_ids" jdbcType="VARCHAR" property="relatedOrderIds" />
    <result column="related_k3_order_ids" jdbcType="VARCHAR" property="relatedK3OrderIds" />
    <result column="pro_ret_num" jdbcType="VARCHAR" property="proRetNum" />
    <result column="pro_sync_status" jdbcType="VARCHAR" property="proSyncStatus" />
    <result column="pro_submit_account_status" jdbcType="VARCHAR" property="proSubmitAccountStatus" />
    <result column="sync_k3_user_name" jdbcType="VARCHAR" property="syncK3UserName" />
    <result column="commit_k3_user_name" jdbcType="VARCHAR" property="commitK3UserName" />
    <result column="material_dept" jdbcType="VARCHAR" property="materialDept" />
    <result column="pro_data_code" jdbcType="VARCHAR" property="proDataCode" />
    <result column="k3_status" jdbcType="INTEGER" property="k3Status" />
    <result column="pro_material_status" jdbcType="INTEGER" property="proMaterialStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 25 14:19:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 25 14:19:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 25 14:19:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    id, contract_num, contract_name, contract_dept, contract_statis_type, contract_seller, 
    contract_type, money_unit, sell_unit, product_type, order_province_name, order_province_code, 
    order_city_name, order_city_code, order_count, total_price, k3_ret_num, k3_sync_status, 
    k3_commit_status, seller_org_id, seller_team_id, seller_dept_id, seller_phone, cost_center, 
    project, sub_project, sync_suc_time, commit_suc_time, custom_code, buyer_province, 
    buyer_province_code, buyer_city_code, buyer_city, create_time, related_order_ids, 
    related_k3_order_ids, pro_ret_num, pro_sync_status, pro_submit_account_status, sync_k3_user_name, 
    commit_k3_user_name, material_dept, pro_data_code, k3_status, pro_material_status
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.K3syncStatisCityExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 25 14:19:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from k3sync_statis_city
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 25 14:19:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from k3sync_statis_city
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 25 14:19:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from k3sync_statis_city
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.K3syncStatisCityExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 25 14:19:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from k3sync_statis_city
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.K3syncStatisCity">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 25 14:19:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into k3sync_statis_city (id, contract_num, contract_name, 
      contract_dept, contract_statis_type, contract_seller, 
      contract_type, money_unit, sell_unit, 
      product_type, order_province_name, order_province_code, 
      order_city_name, order_city_code, order_count, 
      total_price, k3_ret_num, k3_sync_status, 
      k3_commit_status, seller_org_id, seller_team_id, 
      seller_dept_id, seller_phone, cost_center, 
      project, sub_project, sync_suc_time, 
      commit_suc_time, custom_code, buyer_province, 
      buyer_province_code, buyer_city_code, buyer_city, 
      create_time, related_order_ids, related_k3_order_ids, 
      pro_ret_num, pro_sync_status, pro_submit_account_status, 
      sync_k3_user_name, commit_k3_user_name, material_dept, 
      pro_data_code, k3_status, pro_material_status
      )
    values (#{id,jdbcType=VARCHAR}, #{contractNum,jdbcType=VARCHAR}, #{contractName,jdbcType=VARCHAR}, 
      #{contractDept,jdbcType=VARCHAR}, #{contractStatisType,jdbcType=VARCHAR}, #{contractSeller,jdbcType=VARCHAR}, 
      #{contractType,jdbcType=VARCHAR}, #{moneyUnit,jdbcType=VARCHAR}, #{sellUnit,jdbcType=VARCHAR}, 
      #{productType,jdbcType=VARCHAR}, #{orderProvinceName,jdbcType=VARCHAR}, #{orderProvinceCode,jdbcType=VARCHAR}, 
      #{orderCityName,jdbcType=VARCHAR}, #{orderCityCode,jdbcType=VARCHAR}, #{orderCount,jdbcType=VARCHAR}, 
      #{totalPrice,jdbcType=BIGINT}, #{k3RetNum,jdbcType=VARCHAR}, #{k3SyncStatus,jdbcType=VARCHAR}, 
      #{k3CommitStatus,jdbcType=VARCHAR}, #{sellerOrgId,jdbcType=VARCHAR}, #{sellerTeamId,jdbcType=VARCHAR}, 
      #{sellerDeptId,jdbcType=VARCHAR}, #{sellerPhone,jdbcType=VARCHAR}, #{costCenter,jdbcType=VARCHAR}, 
      #{project,jdbcType=VARCHAR}, #{subProject,jdbcType=VARCHAR}, #{syncSucTime,jdbcType=TIMESTAMP}, 
      #{commitSucTime,jdbcType=TIMESTAMP}, #{customCode,jdbcType=VARCHAR}, #{buyerProvince,jdbcType=VARCHAR}, 
      #{buyerProvinceCode,jdbcType=VARCHAR}, #{buyerCityCode,jdbcType=VARCHAR}, #{buyerCity,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{relatedOrderIds,jdbcType=VARCHAR}, #{relatedK3OrderIds,jdbcType=VARCHAR}, 
      #{proRetNum,jdbcType=VARCHAR}, #{proSyncStatus,jdbcType=VARCHAR}, #{proSubmitAccountStatus,jdbcType=VARCHAR}, 
      #{syncK3UserName,jdbcType=VARCHAR}, #{commitK3UserName,jdbcType=VARCHAR}, #{materialDept,jdbcType=VARCHAR}, 
      #{proDataCode,jdbcType=VARCHAR}, #{k3Status,jdbcType=INTEGER}, #{proMaterialStatus,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.K3syncStatisCity">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 25 14:19:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into k3sync_statis_city
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="contractNum != null">
        contract_num,
      </if>
      <if test="contractName != null">
        contract_name,
      </if>
      <if test="contractDept != null">
        contract_dept,
      </if>
      <if test="contractStatisType != null">
        contract_statis_type,
      </if>
      <if test="contractSeller != null">
        contract_seller,
      </if>
      <if test="contractType != null">
        contract_type,
      </if>
      <if test="moneyUnit != null">
        money_unit,
      </if>
      <if test="sellUnit != null">
        sell_unit,
      </if>
      <if test="productType != null">
        product_type,
      </if>
      <if test="orderProvinceName != null">
        order_province_name,
      </if>
      <if test="orderProvinceCode != null">
        order_province_code,
      </if>
      <if test="orderCityName != null">
        order_city_name,
      </if>
      <if test="orderCityCode != null">
        order_city_code,
      </if>
      <if test="orderCount != null">
        order_count,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="k3RetNum != null">
        k3_ret_num,
      </if>
      <if test="k3SyncStatus != null">
        k3_sync_status,
      </if>
      <if test="k3CommitStatus != null">
        k3_commit_status,
      </if>
      <if test="sellerOrgId != null">
        seller_org_id,
      </if>
      <if test="sellerTeamId != null">
        seller_team_id,
      </if>
      <if test="sellerDeptId != null">
        seller_dept_id,
      </if>
      <if test="sellerPhone != null">
        seller_phone,
      </if>
      <if test="costCenter != null">
        cost_center,
      </if>
      <if test="project != null">
        project,
      </if>
      <if test="subProject != null">
        sub_project,
      </if>
      <if test="syncSucTime != null">
        sync_suc_time,
      </if>
      <if test="commitSucTime != null">
        commit_suc_time,
      </if>
      <if test="customCode != null">
        custom_code,
      </if>
      <if test="buyerProvince != null">
        buyer_province,
      </if>
      <if test="buyerProvinceCode != null">
        buyer_province_code,
      </if>
      <if test="buyerCityCode != null">
        buyer_city_code,
      </if>
      <if test="buyerCity != null">
        buyer_city,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="relatedOrderIds != null">
        related_order_ids,
      </if>
      <if test="relatedK3OrderIds != null">
        related_k3_order_ids,
      </if>
      <if test="proRetNum != null">
        pro_ret_num,
      </if>
      <if test="proSyncStatus != null">
        pro_sync_status,
      </if>
      <if test="proSubmitAccountStatus != null">
        pro_submit_account_status,
      </if>
      <if test="syncK3UserName != null">
        sync_k3_user_name,
      </if>
      <if test="commitK3UserName != null">
        commit_k3_user_name,
      </if>
      <if test="materialDept != null">
        material_dept,
      </if>
      <if test="proDataCode != null">
        pro_data_code,
      </if>
      <if test="k3Status != null">
        k3_status,
      </if>
      <if test="proMaterialStatus != null">
        pro_material_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="contractNum != null">
        #{contractNum,jdbcType=VARCHAR},
      </if>
      <if test="contractName != null">
        #{contractName,jdbcType=VARCHAR},
      </if>
      <if test="contractDept != null">
        #{contractDept,jdbcType=VARCHAR},
      </if>
      <if test="contractStatisType != null">
        #{contractStatisType,jdbcType=VARCHAR},
      </if>
      <if test="contractSeller != null">
        #{contractSeller,jdbcType=VARCHAR},
      </if>
      <if test="contractType != null">
        #{contractType,jdbcType=VARCHAR},
      </if>
      <if test="moneyUnit != null">
        #{moneyUnit,jdbcType=VARCHAR},
      </if>
      <if test="sellUnit != null">
        #{sellUnit,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        #{productType,jdbcType=VARCHAR},
      </if>
      <if test="orderProvinceName != null">
        #{orderProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="orderProvinceCode != null">
        #{orderProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="orderCityName != null">
        #{orderCityName,jdbcType=VARCHAR},
      </if>
      <if test="orderCityCode != null">
        #{orderCityCode,jdbcType=VARCHAR},
      </if>
      <if test="orderCount != null">
        #{orderCount,jdbcType=VARCHAR},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=BIGINT},
      </if>
      <if test="k3RetNum != null">
        #{k3RetNum,jdbcType=VARCHAR},
      </if>
      <if test="k3SyncStatus != null">
        #{k3SyncStatus,jdbcType=VARCHAR},
      </if>
      <if test="k3CommitStatus != null">
        #{k3CommitStatus,jdbcType=VARCHAR},
      </if>
      <if test="sellerOrgId != null">
        #{sellerOrgId,jdbcType=VARCHAR},
      </if>
      <if test="sellerTeamId != null">
        #{sellerTeamId,jdbcType=VARCHAR},
      </if>
      <if test="sellerDeptId != null">
        #{sellerDeptId,jdbcType=VARCHAR},
      </if>
      <if test="sellerPhone != null">
        #{sellerPhone,jdbcType=VARCHAR},
      </if>
      <if test="costCenter != null">
        #{costCenter,jdbcType=VARCHAR},
      </if>
      <if test="project != null">
        #{project,jdbcType=VARCHAR},
      </if>
      <if test="subProject != null">
        #{subProject,jdbcType=VARCHAR},
      </if>
      <if test="syncSucTime != null">
        #{syncSucTime,jdbcType=TIMESTAMP},
      </if>
      <if test="commitSucTime != null">
        #{commitSucTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customCode != null">
        #{customCode,jdbcType=VARCHAR},
      </if>
      <if test="buyerProvince != null">
        #{buyerProvince,jdbcType=VARCHAR},
      </if>
      <if test="buyerProvinceCode != null">
        #{buyerProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="buyerCityCode != null">
        #{buyerCityCode,jdbcType=VARCHAR},
      </if>
      <if test="buyerCity != null">
        #{buyerCity,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="relatedOrderIds != null">
        #{relatedOrderIds,jdbcType=VARCHAR},
      </if>
      <if test="relatedK3OrderIds != null">
        #{relatedK3OrderIds,jdbcType=VARCHAR},
      </if>
      <if test="proRetNum != null">
        #{proRetNum,jdbcType=VARCHAR},
      </if>
      <if test="proSyncStatus != null">
        #{proSyncStatus,jdbcType=VARCHAR},
      </if>
      <if test="proSubmitAccountStatus != null">
        #{proSubmitAccountStatus,jdbcType=VARCHAR},
      </if>
      <if test="syncK3UserName != null">
        #{syncK3UserName,jdbcType=VARCHAR},
      </if>
      <if test="commitK3UserName != null">
        #{commitK3UserName,jdbcType=VARCHAR},
      </if>
      <if test="materialDept != null">
        #{materialDept,jdbcType=VARCHAR},
      </if>
      <if test="proDataCode != null">
        #{proDataCode,jdbcType=VARCHAR},
      </if>
      <if test="k3Status != null">
        #{k3Status,jdbcType=INTEGER},
      </if>
      <if test="proMaterialStatus != null">
        #{proMaterialStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.K3syncStatisCityExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 25 14:19:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    select count(*) from k3sync_statis_city
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 25 14:19:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    update k3sync_statis_city
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.contractNum != null">
        contract_num = #{record.contractNum,jdbcType=VARCHAR},
      </if>
      <if test="record.contractName != null">
        contract_name = #{record.contractName,jdbcType=VARCHAR},
      </if>
      <if test="record.contractDept != null">
        contract_dept = #{record.contractDept,jdbcType=VARCHAR},
      </if>
      <if test="record.contractStatisType != null">
        contract_statis_type = #{record.contractStatisType,jdbcType=VARCHAR},
      </if>
      <if test="record.contractSeller != null">
        contract_seller = #{record.contractSeller,jdbcType=VARCHAR},
      </if>
      <if test="record.contractType != null">
        contract_type = #{record.contractType,jdbcType=VARCHAR},
      </if>
      <if test="record.moneyUnit != null">
        money_unit = #{record.moneyUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.sellUnit != null">
        sell_unit = #{record.sellUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.productType != null">
        product_type = #{record.productType,jdbcType=VARCHAR},
      </if>
      <if test="record.orderProvinceName != null">
        order_province_name = #{record.orderProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.orderProvinceCode != null">
        order_province_code = #{record.orderProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orderCityName != null">
        order_city_name = #{record.orderCityName,jdbcType=VARCHAR},
      </if>
      <if test="record.orderCityCode != null">
        order_city_code = #{record.orderCityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orderCount != null">
        order_count = #{record.orderCount,jdbcType=VARCHAR},
      </if>
      <if test="record.totalPrice != null">
        total_price = #{record.totalPrice,jdbcType=BIGINT},
      </if>
      <if test="record.k3RetNum != null">
        k3_ret_num = #{record.k3RetNum,jdbcType=VARCHAR},
      </if>
      <if test="record.k3SyncStatus != null">
        k3_sync_status = #{record.k3SyncStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.k3CommitStatus != null">
        k3_commit_status = #{record.k3CommitStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerOrgId != null">
        seller_org_id = #{record.sellerOrgId,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerTeamId != null">
        seller_team_id = #{record.sellerTeamId,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerDeptId != null">
        seller_dept_id = #{record.sellerDeptId,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerPhone != null">
        seller_phone = #{record.sellerPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.costCenter != null">
        cost_center = #{record.costCenter,jdbcType=VARCHAR},
      </if>
      <if test="record.project != null">
        project = #{record.project,jdbcType=VARCHAR},
      </if>
      <if test="record.subProject != null">
        sub_project = #{record.subProject,jdbcType=VARCHAR},
      </if>
      <if test="record.syncSucTime != null">
        sync_suc_time = #{record.syncSucTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.commitSucTime != null">
        commit_suc_time = #{record.commitSucTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.customCode != null">
        custom_code = #{record.customCode,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerProvince != null">
        buyer_province = #{record.buyerProvince,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerProvinceCode != null">
        buyer_province_code = #{record.buyerProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerCityCode != null">
        buyer_city_code = #{record.buyerCityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerCity != null">
        buyer_city = #{record.buyerCity,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.relatedOrderIds != null">
        related_order_ids = #{record.relatedOrderIds,jdbcType=VARCHAR},
      </if>
      <if test="record.relatedK3OrderIds != null">
        related_k3_order_ids = #{record.relatedK3OrderIds,jdbcType=VARCHAR},
      </if>
      <if test="record.proRetNum != null">
        pro_ret_num = #{record.proRetNum,jdbcType=VARCHAR},
      </if>
      <if test="record.proSyncStatus != null">
        pro_sync_status = #{record.proSyncStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.proSubmitAccountStatus != null">
        pro_submit_account_status = #{record.proSubmitAccountStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.syncK3UserName != null">
        sync_k3_user_name = #{record.syncK3UserName,jdbcType=VARCHAR},
      </if>
      <if test="record.commitK3UserName != null">
        commit_k3_user_name = #{record.commitK3UserName,jdbcType=VARCHAR},
      </if>
      <if test="record.materialDept != null">
        material_dept = #{record.materialDept,jdbcType=VARCHAR},
      </if>
      <if test="record.proDataCode != null">
        pro_data_code = #{record.proDataCode,jdbcType=VARCHAR},
      </if>
      <if test="record.k3Status != null">
        k3_status = #{record.k3Status,jdbcType=INTEGER},
      </if>
      <if test="record.proMaterialStatus != null">
        pro_material_status = #{record.proMaterialStatus,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 25 14:19:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    update k3sync_statis_city
    set id = #{record.id,jdbcType=VARCHAR},
      contract_num = #{record.contractNum,jdbcType=VARCHAR},
      contract_name = #{record.contractName,jdbcType=VARCHAR},
      contract_dept = #{record.contractDept,jdbcType=VARCHAR},
      contract_statis_type = #{record.contractStatisType,jdbcType=VARCHAR},
      contract_seller = #{record.contractSeller,jdbcType=VARCHAR},
      contract_type = #{record.contractType,jdbcType=VARCHAR},
      money_unit = #{record.moneyUnit,jdbcType=VARCHAR},
      sell_unit = #{record.sellUnit,jdbcType=VARCHAR},
      product_type = #{record.productType,jdbcType=VARCHAR},
      order_province_name = #{record.orderProvinceName,jdbcType=VARCHAR},
      order_province_code = #{record.orderProvinceCode,jdbcType=VARCHAR},
      order_city_name = #{record.orderCityName,jdbcType=VARCHAR},
      order_city_code = #{record.orderCityCode,jdbcType=VARCHAR},
      order_count = #{record.orderCount,jdbcType=VARCHAR},
      total_price = #{record.totalPrice,jdbcType=BIGINT},
      k3_ret_num = #{record.k3RetNum,jdbcType=VARCHAR},
      k3_sync_status = #{record.k3SyncStatus,jdbcType=VARCHAR},
      k3_commit_status = #{record.k3CommitStatus,jdbcType=VARCHAR},
      seller_org_id = #{record.sellerOrgId,jdbcType=VARCHAR},
      seller_team_id = #{record.sellerTeamId,jdbcType=VARCHAR},
      seller_dept_id = #{record.sellerDeptId,jdbcType=VARCHAR},
      seller_phone = #{record.sellerPhone,jdbcType=VARCHAR},
      cost_center = #{record.costCenter,jdbcType=VARCHAR},
      project = #{record.project,jdbcType=VARCHAR},
      sub_project = #{record.subProject,jdbcType=VARCHAR},
      sync_suc_time = #{record.syncSucTime,jdbcType=TIMESTAMP},
      commit_suc_time = #{record.commitSucTime,jdbcType=TIMESTAMP},
      custom_code = #{record.customCode,jdbcType=VARCHAR},
      buyer_province = #{record.buyerProvince,jdbcType=VARCHAR},
      buyer_province_code = #{record.buyerProvinceCode,jdbcType=VARCHAR},
      buyer_city_code = #{record.buyerCityCode,jdbcType=VARCHAR},
      buyer_city = #{record.buyerCity,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      related_order_ids = #{record.relatedOrderIds,jdbcType=VARCHAR},
      related_k3_order_ids = #{record.relatedK3OrderIds,jdbcType=VARCHAR},
      pro_ret_num = #{record.proRetNum,jdbcType=VARCHAR},
      pro_sync_status = #{record.proSyncStatus,jdbcType=VARCHAR},
      pro_submit_account_status = #{record.proSubmitAccountStatus,jdbcType=VARCHAR},
      sync_k3_user_name = #{record.syncK3UserName,jdbcType=VARCHAR},
      commit_k3_user_name = #{record.commitK3UserName,jdbcType=VARCHAR},
      material_dept = #{record.materialDept,jdbcType=VARCHAR},
      pro_data_code = #{record.proDataCode,jdbcType=VARCHAR},
      k3_status = #{record.k3Status,jdbcType=INTEGER},
      pro_material_status = #{record.proMaterialStatus,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.K3syncStatisCity">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 25 14:19:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    update k3sync_statis_city
    <set>
      <if test="contractNum != null">
        contract_num = #{contractNum,jdbcType=VARCHAR},
      </if>
      <if test="contractName != null">
        contract_name = #{contractName,jdbcType=VARCHAR},
      </if>
      <if test="contractDept != null">
        contract_dept = #{contractDept,jdbcType=VARCHAR},
      </if>
      <if test="contractStatisType != null">
        contract_statis_type = #{contractStatisType,jdbcType=VARCHAR},
      </if>
      <if test="contractSeller != null">
        contract_seller = #{contractSeller,jdbcType=VARCHAR},
      </if>
      <if test="contractType != null">
        contract_type = #{contractType,jdbcType=VARCHAR},
      </if>
      <if test="moneyUnit != null">
        money_unit = #{moneyUnit,jdbcType=VARCHAR},
      </if>
      <if test="sellUnit != null">
        sell_unit = #{sellUnit,jdbcType=VARCHAR},
      </if>
      <if test="productType != null">
        product_type = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="orderProvinceName != null">
        order_province_name = #{orderProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="orderProvinceCode != null">
        order_province_code = #{orderProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="orderCityName != null">
        order_city_name = #{orderCityName,jdbcType=VARCHAR},
      </if>
      <if test="orderCityCode != null">
        order_city_code = #{orderCityCode,jdbcType=VARCHAR},
      </if>
      <if test="orderCount != null">
        order_count = #{orderCount,jdbcType=VARCHAR},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=BIGINT},
      </if>
      <if test="k3RetNum != null">
        k3_ret_num = #{k3RetNum,jdbcType=VARCHAR},
      </if>
      <if test="k3SyncStatus != null">
        k3_sync_status = #{k3SyncStatus,jdbcType=VARCHAR},
      </if>
      <if test="k3CommitStatus != null">
        k3_commit_status = #{k3CommitStatus,jdbcType=VARCHAR},
      </if>
      <if test="sellerOrgId != null">
        seller_org_id = #{sellerOrgId,jdbcType=VARCHAR},
      </if>
      <if test="sellerTeamId != null">
        seller_team_id = #{sellerTeamId,jdbcType=VARCHAR},
      </if>
      <if test="sellerDeptId != null">
        seller_dept_id = #{sellerDeptId,jdbcType=VARCHAR},
      </if>
      <if test="sellerPhone != null">
        seller_phone = #{sellerPhone,jdbcType=VARCHAR},
      </if>
      <if test="costCenter != null">
        cost_center = #{costCenter,jdbcType=VARCHAR},
      </if>
      <if test="project != null">
        project = #{project,jdbcType=VARCHAR},
      </if>
      <if test="subProject != null">
        sub_project = #{subProject,jdbcType=VARCHAR},
      </if>
      <if test="syncSucTime != null">
        sync_suc_time = #{syncSucTime,jdbcType=TIMESTAMP},
      </if>
      <if test="commitSucTime != null">
        commit_suc_time = #{commitSucTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customCode != null">
        custom_code = #{customCode,jdbcType=VARCHAR},
      </if>
      <if test="buyerProvince != null">
        buyer_province = #{buyerProvince,jdbcType=VARCHAR},
      </if>
      <if test="buyerProvinceCode != null">
        buyer_province_code = #{buyerProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="buyerCityCode != null">
        buyer_city_code = #{buyerCityCode,jdbcType=VARCHAR},
      </if>
      <if test="buyerCity != null">
        buyer_city = #{buyerCity,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="relatedOrderIds != null">
        related_order_ids = #{relatedOrderIds,jdbcType=VARCHAR},
      </if>
      <if test="relatedK3OrderIds != null">
        related_k3_order_ids = #{relatedK3OrderIds,jdbcType=VARCHAR},
      </if>
      <if test="proRetNum != null">
        pro_ret_num = #{proRetNum,jdbcType=VARCHAR},
      </if>
      <if test="proSyncStatus != null">
        pro_sync_status = #{proSyncStatus,jdbcType=VARCHAR},
      </if>
      <if test="proSubmitAccountStatus != null">
        pro_submit_account_status = #{proSubmitAccountStatus,jdbcType=VARCHAR},
      </if>
      <if test="syncK3UserName != null">
        sync_k3_user_name = #{syncK3UserName,jdbcType=VARCHAR},
      </if>
      <if test="commitK3UserName != null">
        commit_k3_user_name = #{commitK3UserName,jdbcType=VARCHAR},
      </if>
      <if test="materialDept != null">
        material_dept = #{materialDept,jdbcType=VARCHAR},
      </if>
      <if test="proDataCode != null">
        pro_data_code = #{proDataCode,jdbcType=VARCHAR},
      </if>
      <if test="k3Status != null">
        k3_status = #{k3Status,jdbcType=INTEGER},
      </if>
      <if test="proMaterialStatus != null">
        pro_material_status = #{proMaterialStatus,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.K3syncStatisCity">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 25 14:19:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    update k3sync_statis_city
    set contract_num = #{contractNum,jdbcType=VARCHAR},
      contract_name = #{contractName,jdbcType=VARCHAR},
      contract_dept = #{contractDept,jdbcType=VARCHAR},
      contract_statis_type = #{contractStatisType,jdbcType=VARCHAR},
      contract_seller = #{contractSeller,jdbcType=VARCHAR},
      contract_type = #{contractType,jdbcType=VARCHAR},
      money_unit = #{moneyUnit,jdbcType=VARCHAR},
      sell_unit = #{sellUnit,jdbcType=VARCHAR},
      product_type = #{productType,jdbcType=VARCHAR},
      order_province_name = #{orderProvinceName,jdbcType=VARCHAR},
      order_province_code = #{orderProvinceCode,jdbcType=VARCHAR},
      order_city_name = #{orderCityName,jdbcType=VARCHAR},
      order_city_code = #{orderCityCode,jdbcType=VARCHAR},
      order_count = #{orderCount,jdbcType=VARCHAR},
      total_price = #{totalPrice,jdbcType=BIGINT},
      k3_ret_num = #{k3RetNum,jdbcType=VARCHAR},
      k3_sync_status = #{k3SyncStatus,jdbcType=VARCHAR},
      k3_commit_status = #{k3CommitStatus,jdbcType=VARCHAR},
      seller_org_id = #{sellerOrgId,jdbcType=VARCHAR},
      seller_team_id = #{sellerTeamId,jdbcType=VARCHAR},
      seller_dept_id = #{sellerDeptId,jdbcType=VARCHAR},
      seller_phone = #{sellerPhone,jdbcType=VARCHAR},
      cost_center = #{costCenter,jdbcType=VARCHAR},
      project = #{project,jdbcType=VARCHAR},
      sub_project = #{subProject,jdbcType=VARCHAR},
      sync_suc_time = #{syncSucTime,jdbcType=TIMESTAMP},
      commit_suc_time = #{commitSucTime,jdbcType=TIMESTAMP},
      custom_code = #{customCode,jdbcType=VARCHAR},
      buyer_province = #{buyerProvince,jdbcType=VARCHAR},
      buyer_province_code = #{buyerProvinceCode,jdbcType=VARCHAR},
      buyer_city_code = #{buyerCityCode,jdbcType=VARCHAR},
      buyer_city = #{buyerCity,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      related_order_ids = #{relatedOrderIds,jdbcType=VARCHAR},
      related_k3_order_ids = #{relatedK3OrderIds,jdbcType=VARCHAR},
      pro_ret_num = #{proRetNum,jdbcType=VARCHAR},
      pro_sync_status = #{proSyncStatus,jdbcType=VARCHAR},
      pro_submit_account_status = #{proSubmitAccountStatus,jdbcType=VARCHAR},
      sync_k3_user_name = #{syncK3UserName,jdbcType=VARCHAR},
      commit_k3_user_name = #{commitK3UserName,jdbcType=VARCHAR},
      material_dept = #{materialDept,jdbcType=VARCHAR},
      pro_data_code = #{proDataCode,jdbcType=VARCHAR},
      k3_status = #{k3Status,jdbcType=INTEGER},
      pro_material_status = #{proMaterialStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 25 14:19:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into k3sync_statis_city
    (id, contract_num, contract_name, contract_dept, contract_statis_type, contract_seller, 
      contract_type, money_unit, sell_unit, product_type, order_province_name, order_province_code, 
      order_city_name, order_city_code, order_count, total_price, k3_ret_num, k3_sync_status, 
      k3_commit_status, seller_org_id, seller_team_id, seller_dept_id, seller_phone, 
      cost_center, project, sub_project, sync_suc_time, commit_suc_time, custom_code, 
      buyer_province, buyer_province_code, buyer_city_code, buyer_city, create_time, 
      related_order_ids, related_k3_order_ids, pro_ret_num, pro_sync_status, pro_submit_account_status, 
      sync_k3_user_name, commit_k3_user_name, material_dept, pro_data_code, k3_status, 
      pro_material_status)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.contractNum,jdbcType=VARCHAR}, #{item.contractName,jdbcType=VARCHAR}, 
        #{item.contractDept,jdbcType=VARCHAR}, #{item.contractStatisType,jdbcType=VARCHAR}, 
        #{item.contractSeller,jdbcType=VARCHAR}, #{item.contractType,jdbcType=VARCHAR}, 
        #{item.moneyUnit,jdbcType=VARCHAR}, #{item.sellUnit,jdbcType=VARCHAR}, #{item.productType,jdbcType=VARCHAR}, 
        #{item.orderProvinceName,jdbcType=VARCHAR}, #{item.orderProvinceCode,jdbcType=VARCHAR}, 
        #{item.orderCityName,jdbcType=VARCHAR}, #{item.orderCityCode,jdbcType=VARCHAR}, 
        #{item.orderCount,jdbcType=VARCHAR}, #{item.totalPrice,jdbcType=BIGINT}, #{item.k3RetNum,jdbcType=VARCHAR}, 
        #{item.k3SyncStatus,jdbcType=VARCHAR}, #{item.k3CommitStatus,jdbcType=VARCHAR}, 
        #{item.sellerOrgId,jdbcType=VARCHAR}, #{item.sellerTeamId,jdbcType=VARCHAR}, #{item.sellerDeptId,jdbcType=VARCHAR}, 
        #{item.sellerPhone,jdbcType=VARCHAR}, #{item.costCenter,jdbcType=VARCHAR}, #{item.project,jdbcType=VARCHAR}, 
        #{item.subProject,jdbcType=VARCHAR}, #{item.syncSucTime,jdbcType=TIMESTAMP}, #{item.commitSucTime,jdbcType=TIMESTAMP}, 
        #{item.customCode,jdbcType=VARCHAR}, #{item.buyerProvince,jdbcType=VARCHAR}, #{item.buyerProvinceCode,jdbcType=VARCHAR}, 
        #{item.buyerCityCode,jdbcType=VARCHAR}, #{item.buyerCity,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.relatedOrderIds,jdbcType=VARCHAR}, #{item.relatedK3OrderIds,jdbcType=VARCHAR}, 
        #{item.proRetNum,jdbcType=VARCHAR}, #{item.proSyncStatus,jdbcType=VARCHAR}, #{item.proSubmitAccountStatus,jdbcType=VARCHAR}, 
        #{item.syncK3UserName,jdbcType=VARCHAR}, #{item.commitK3UserName,jdbcType=VARCHAR}, 
        #{item.materialDept,jdbcType=VARCHAR}, #{item.proDataCode,jdbcType=VARCHAR}, #{item.k3Status,jdbcType=INTEGER}, 
        #{item.proMaterialStatus,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 25 14:19:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into k3sync_statis_city (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'contract_num'.toString() == column.value">
          #{item.contractNum,jdbcType=VARCHAR}
        </if>
        <if test="'contract_name'.toString() == column.value">
          #{item.contractName,jdbcType=VARCHAR}
        </if>
        <if test="'contract_dept'.toString() == column.value">
          #{item.contractDept,jdbcType=VARCHAR}
        </if>
        <if test="'contract_statis_type'.toString() == column.value">
          #{item.contractStatisType,jdbcType=VARCHAR}
        </if>
        <if test="'contract_seller'.toString() == column.value">
          #{item.contractSeller,jdbcType=VARCHAR}
        </if>
        <if test="'contract_type'.toString() == column.value">
          #{item.contractType,jdbcType=VARCHAR}
        </if>
        <if test="'money_unit'.toString() == column.value">
          #{item.moneyUnit,jdbcType=VARCHAR}
        </if>
        <if test="'sell_unit'.toString() == column.value">
          #{item.sellUnit,jdbcType=VARCHAR}
        </if>
        <if test="'product_type'.toString() == column.value">
          #{item.productType,jdbcType=VARCHAR}
        </if>
        <if test="'order_province_name'.toString() == column.value">
          #{item.orderProvinceName,jdbcType=VARCHAR}
        </if>
        <if test="'order_province_code'.toString() == column.value">
          #{item.orderProvinceCode,jdbcType=VARCHAR}
        </if>
        <if test="'order_city_name'.toString() == column.value">
          #{item.orderCityName,jdbcType=VARCHAR}
        </if>
        <if test="'order_city_code'.toString() == column.value">
          #{item.orderCityCode,jdbcType=VARCHAR}
        </if>
        <if test="'order_count'.toString() == column.value">
          #{item.orderCount,jdbcType=VARCHAR}
        </if>
        <if test="'total_price'.toString() == column.value">
          #{item.totalPrice,jdbcType=BIGINT}
        </if>
        <if test="'k3_ret_num'.toString() == column.value">
          #{item.k3RetNum,jdbcType=VARCHAR}
        </if>
        <if test="'k3_sync_status'.toString() == column.value">
          #{item.k3SyncStatus,jdbcType=VARCHAR}
        </if>
        <if test="'k3_commit_status'.toString() == column.value">
          #{item.k3CommitStatus,jdbcType=VARCHAR}
        </if>
        <if test="'seller_org_id'.toString() == column.value">
          #{item.sellerOrgId,jdbcType=VARCHAR}
        </if>
        <if test="'seller_team_id'.toString() == column.value">
          #{item.sellerTeamId,jdbcType=VARCHAR}
        </if>
        <if test="'seller_dept_id'.toString() == column.value">
          #{item.sellerDeptId,jdbcType=VARCHAR}
        </if>
        <if test="'seller_phone'.toString() == column.value">
          #{item.sellerPhone,jdbcType=VARCHAR}
        </if>
        <if test="'cost_center'.toString() == column.value">
          #{item.costCenter,jdbcType=VARCHAR}
        </if>
        <if test="'project'.toString() == column.value">
          #{item.project,jdbcType=VARCHAR}
        </if>
        <if test="'sub_project'.toString() == column.value">
          #{item.subProject,jdbcType=VARCHAR}
        </if>
        <if test="'sync_suc_time'.toString() == column.value">
          #{item.syncSucTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'commit_suc_time'.toString() == column.value">
          #{item.commitSucTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'custom_code'.toString() == column.value">
          #{item.customCode,jdbcType=VARCHAR}
        </if>
        <if test="'buyer_province'.toString() == column.value">
          #{item.buyerProvince,jdbcType=VARCHAR}
        </if>
        <if test="'buyer_province_code'.toString() == column.value">
          #{item.buyerProvinceCode,jdbcType=VARCHAR}
        </if>
        <if test="'buyer_city_code'.toString() == column.value">
          #{item.buyerCityCode,jdbcType=VARCHAR}
        </if>
        <if test="'buyer_city'.toString() == column.value">
          #{item.buyerCity,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'related_order_ids'.toString() == column.value">
          #{item.relatedOrderIds,jdbcType=VARCHAR}
        </if>
        <if test="'related_k3_order_ids'.toString() == column.value">
          #{item.relatedK3OrderIds,jdbcType=VARCHAR}
        </if>
        <if test="'pro_ret_num'.toString() == column.value">
          #{item.proRetNum,jdbcType=VARCHAR}
        </if>
        <if test="'pro_sync_status'.toString() == column.value">
          #{item.proSyncStatus,jdbcType=VARCHAR}
        </if>
        <if test="'pro_submit_account_status'.toString() == column.value">
          #{item.proSubmitAccountStatus,jdbcType=VARCHAR}
        </if>
        <if test="'sync_k3_user_name'.toString() == column.value">
          #{item.syncK3UserName,jdbcType=VARCHAR}
        </if>
        <if test="'commit_k3_user_name'.toString() == column.value">
          #{item.commitK3UserName,jdbcType=VARCHAR}
        </if>
        <if test="'material_dept'.toString() == column.value">
          #{item.materialDept,jdbcType=VARCHAR}
        </if>
        <if test="'pro_data_code'.toString() == column.value">
          #{item.proDataCode,jdbcType=VARCHAR}
        </if>
        <if test="'k3_status'.toString() == column.value">
          #{item.k3Status,jdbcType=INTEGER}
        </if>
        <if test="'pro_material_status'.toString() == column.value">
          #{item.proMaterialStatus,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>