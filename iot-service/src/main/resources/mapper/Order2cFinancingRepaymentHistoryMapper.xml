<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.Order2cFinancingRepaymentHistoryMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.Order2cFinancingRepaymentHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 11 16:20:45 CST 2023. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="repay_date" jdbcType="VARCHAR" property="repayDate" />
    <result column="repay_amount" jdbcType="BIGINT" property="repayAmount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 11 16:20:45 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 11 16:20:45 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 11 16:20:45 CST 2023. by MyBatis Generator, do not modify.
    -->
    id, order_code, repay_date, repay_amount, create_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cFinancingRepaymentHistoryExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 11 16:20:45 CST 2023. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_2c_financing_repayment_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 11 16:20:45 CST 2023. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from order_2c_financing_repayment_history
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 11 16:20:45 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from order_2c_financing_repayment_history
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cFinancingRepaymentHistoryExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 11 16:20:45 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from order_2c_financing_repayment_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.Order2cFinancingRepaymentHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 11 16:20:45 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_financing_repayment_history (id, order_code, repay_date, 
      repay_amount, create_time)
    values (#{id,jdbcType=VARCHAR}, #{orderCode,jdbcType=VARCHAR}, #{repayDate,jdbcType=VARCHAR}, 
      #{repayAmount,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.Order2cFinancingRepaymentHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 11 16:20:45 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_financing_repayment_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderCode != null">
        order_code,
      </if>
      <if test="repayDate != null">
        repay_date,
      </if>
      <if test="repayAmount != null">
        repay_amount,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderCode != null">
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="repayDate != null">
        #{repayDate,jdbcType=VARCHAR},
      </if>
      <if test="repayAmount != null">
        #{repayAmount,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cFinancingRepaymentHistoryExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 11 16:20:45 CST 2023. by MyBatis Generator, do not modify.
    -->
    select count(*) from order_2c_financing_repayment_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 11 16:20:45 CST 2023. by MyBatis Generator, do not modify.
    -->
    update order_2c_financing_repayment_history
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderCode != null">
        order_code = #{record.orderCode,jdbcType=VARCHAR},
      </if>
      <if test="record.repayDate != null">
        repay_date = #{record.repayDate,jdbcType=VARCHAR},
      </if>
      <if test="record.repayAmount != null">
        repay_amount = #{record.repayAmount,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 11 16:20:45 CST 2023. by MyBatis Generator, do not modify.
    -->
    update order_2c_financing_repayment_history
    set id = #{record.id,jdbcType=VARCHAR},
      order_code = #{record.orderCode,jdbcType=VARCHAR},
      repay_date = #{record.repayDate,jdbcType=VARCHAR},
      repay_amount = #{record.repayAmount,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.Order2cFinancingRepaymentHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 11 16:20:45 CST 2023. by MyBatis Generator, do not modify.
    -->
    update order_2c_financing_repayment_history
    <set>
      <if test="orderCode != null">
        order_code = #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="repayDate != null">
        repay_date = #{repayDate,jdbcType=VARCHAR},
      </if>
      <if test="repayAmount != null">
        repay_amount = #{repayAmount,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.Order2cFinancingRepaymentHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 11 16:20:45 CST 2023. by MyBatis Generator, do not modify.
    -->
    update order_2c_financing_repayment_history
    set order_code = #{orderCode,jdbcType=VARCHAR},
      repay_date = #{repayDate,jdbcType=VARCHAR},
      repay_amount = #{repayAmount,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 11 16:20:45 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_financing_repayment_history
    (id, order_code, repay_date, repay_amount, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.orderCode,jdbcType=VARCHAR}, #{item.repayDate,jdbcType=VARCHAR}, 
        #{item.repayAmount,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jul 11 16:20:45 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_financing_repayment_history (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'order_code'.toString() == column.value">
          #{item.orderCode,jdbcType=VARCHAR}
        </if>
        <if test="'repay_date'.toString() == column.value">
          #{item.repayDate,jdbcType=VARCHAR}
        </if>
        <if test="'repay_amount'.toString() == column.value">
          #{item.repayAmount,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>