<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.OperateRecordMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.OperateRecord">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="time" jdbcType="TIMESTAMP" property="time" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="operator_account" jdbcType="VARCHAR" property="operatorAccount" />
    <result column="role" jdbcType="INTEGER" property="role" />
    <result column="module" jdbcType="INTEGER" property="module" />
    <result column="sub_module" jdbcType="INTEGER" property="subModule" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="role_name" jdbcType="VARCHAR" property="roleName" />
    <result column="data_from" jdbcType="INTEGER" property="dataFrom" />
    <result column="result" jdbcType="INTEGER" property="result" />
    <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
    <result column="main_acct_id" jdbcType="VARCHAR" property="mainAcctId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.chinamobile.iot.sc.pojo.entity.OperateRecord">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, time, ip, operator_name, operator_account, role, module, sub_module, deleted, 
    role_name, data_from, result, fail_reason, main_acct_id
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.chinamobile.iot.sc.pojo.entity.OperateRecordExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from operate_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OperateRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from operate_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from operate_record
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from operate_record
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OperateRecordExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from operate_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.OperateRecord">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into operate_record (id, time, ip, 
      operator_name, operator_account, role, 
      module, sub_module, deleted, 
      role_name, data_from, result, 
      fail_reason, main_acct_id, content
      )
    values (#{id,jdbcType=VARCHAR}, #{time,jdbcType=TIMESTAMP}, #{ip,jdbcType=VARCHAR}, 
      #{operatorName,jdbcType=VARCHAR}, #{operatorAccount,jdbcType=VARCHAR}, #{role,jdbcType=INTEGER}, 
      #{module,jdbcType=INTEGER}, #{subModule,jdbcType=INTEGER}, #{deleted,jdbcType=INTEGER}, 
      #{roleName,jdbcType=VARCHAR}, #{dataFrom,jdbcType=INTEGER}, #{result,jdbcType=INTEGER}, 
      #{failReason,jdbcType=VARCHAR}, #{mainAcctId,jdbcType=VARCHAR}, #{content,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.OperateRecord">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into operate_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="time != null">
        time,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="operatorAccount != null">
        operator_account,
      </if>
      <if test="role != null">
        role,
      </if>
      <if test="module != null">
        module,
      </if>
      <if test="subModule != null">
        sub_module,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="roleName != null">
        role_name,
      </if>
      <if test="dataFrom != null">
        data_from,
      </if>
      <if test="result != null">
        result,
      </if>
      <if test="failReason != null">
        fail_reason,
      </if>
      <if test="mainAcctId != null">
        main_acct_id,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="time != null">
        #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operatorAccount != null">
        #{operatorAccount,jdbcType=VARCHAR},
      </if>
      <if test="role != null">
        #{role,jdbcType=INTEGER},
      </if>
      <if test="module != null">
        #{module,jdbcType=INTEGER},
      </if>
      <if test="subModule != null">
        #{subModule,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="roleName != null">
        #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="dataFrom != null">
        #{dataFrom,jdbcType=INTEGER},
      </if>
      <if test="result != null">
        #{result,jdbcType=INTEGER},
      </if>
      <if test="failReason != null">
        #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="mainAcctId != null">
        #{mainAcctId,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OperateRecordExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from operate_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    update operate_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.time != null">
        time = #{record.time,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ip != null">
        ip = #{record.ip,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorName != null">
        operator_name = #{record.operatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorAccount != null">
        operator_account = #{record.operatorAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.role != null">
        role = #{record.role,jdbcType=INTEGER},
      </if>
      <if test="record.module != null">
        module = #{record.module,jdbcType=INTEGER},
      </if>
      <if test="record.subModule != null">
        sub_module = #{record.subModule,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=INTEGER},
      </if>
      <if test="record.roleName != null">
        role_name = #{record.roleName,jdbcType=VARCHAR},
      </if>
      <if test="record.dataFrom != null">
        data_from = #{record.dataFrom,jdbcType=INTEGER},
      </if>
      <if test="record.result != null">
        result = #{record.result,jdbcType=INTEGER},
      </if>
      <if test="record.failReason != null">
        fail_reason = #{record.failReason,jdbcType=VARCHAR},
      </if>
      <if test="record.mainAcctId != null">
        main_acct_id = #{record.mainAcctId,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    update operate_record
    set id = #{record.id,jdbcType=VARCHAR},
      time = #{record.time,jdbcType=TIMESTAMP},
      ip = #{record.ip,jdbcType=VARCHAR},
      operator_name = #{record.operatorName,jdbcType=VARCHAR},
      operator_account = #{record.operatorAccount,jdbcType=VARCHAR},
      role = #{record.role,jdbcType=INTEGER},
      module = #{record.module,jdbcType=INTEGER},
      sub_module = #{record.subModule,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=INTEGER},
      role_name = #{record.roleName,jdbcType=VARCHAR},
      data_from = #{record.dataFrom,jdbcType=INTEGER},
      result = #{record.result,jdbcType=INTEGER},
      fail_reason = #{record.failReason,jdbcType=VARCHAR},
      main_acct_id = #{record.mainAcctId,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    update operate_record
    set id = #{record.id,jdbcType=VARCHAR},
      time = #{record.time,jdbcType=TIMESTAMP},
      ip = #{record.ip,jdbcType=VARCHAR},
      operator_name = #{record.operatorName,jdbcType=VARCHAR},
      operator_account = #{record.operatorAccount,jdbcType=VARCHAR},
      role = #{record.role,jdbcType=INTEGER},
      module = #{record.module,jdbcType=INTEGER},
      sub_module = #{record.subModule,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=INTEGER},
      role_name = #{record.roleName,jdbcType=VARCHAR},
      data_from = #{record.dataFrom,jdbcType=INTEGER},
      result = #{record.result,jdbcType=INTEGER},
      fail_reason = #{record.failReason,jdbcType=VARCHAR},
      main_acct_id = #{record.mainAcctId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.OperateRecord">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    update operate_record
    <set>
      <if test="time != null">
        time = #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="ip != null">
        ip = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operatorAccount != null">
        operator_account = #{operatorAccount,jdbcType=VARCHAR},
      </if>
      <if test="role != null">
        role = #{role,jdbcType=INTEGER},
      </if>
      <if test="module != null">
        module = #{module,jdbcType=INTEGER},
      </if>
      <if test="subModule != null">
        sub_module = #{subModule,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="roleName != null">
        role_name = #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="dataFrom != null">
        data_from = #{dataFrom,jdbcType=INTEGER},
      </if>
      <if test="result != null">
        result = #{result,jdbcType=INTEGER},
      </if>
      <if test="failReason != null">
        fail_reason = #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="mainAcctId != null">
        main_acct_id = #{mainAcctId,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.chinamobile.iot.sc.pojo.entity.OperateRecord">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    update operate_record
    set time = #{time,jdbcType=TIMESTAMP},
      ip = #{ip,jdbcType=VARCHAR},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      operator_account = #{operatorAccount,jdbcType=VARCHAR},
      role = #{role,jdbcType=INTEGER},
      module = #{module,jdbcType=INTEGER},
      sub_module = #{subModule,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=INTEGER},
      role_name = #{roleName,jdbcType=VARCHAR},
      data_from = #{dataFrom,jdbcType=INTEGER},
      result = #{result,jdbcType=INTEGER},
      fail_reason = #{failReason,jdbcType=VARCHAR},
      main_acct_id = #{mainAcctId,jdbcType=VARCHAR},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.OperateRecord">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    update operate_record
    set time = #{time,jdbcType=TIMESTAMP},
      ip = #{ip,jdbcType=VARCHAR},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      operator_account = #{operatorAccount,jdbcType=VARCHAR},
      role = #{role,jdbcType=INTEGER},
      module = #{module,jdbcType=INTEGER},
      sub_module = #{subModule,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=INTEGER},
      role_name = #{roleName,jdbcType=VARCHAR},
      data_from = #{dataFrom,jdbcType=INTEGER},
      result = #{result,jdbcType=INTEGER},
      fail_reason = #{failReason,jdbcType=VARCHAR},
      main_acct_id = #{mainAcctId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into operate_record
    (id, time, ip, operator_name, operator_account, role, module, sub_module, deleted, 
      role_name, data_from, result, fail_reason, main_acct_id, content)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.time,jdbcType=TIMESTAMP}, #{item.ip,jdbcType=VARCHAR}, 
        #{item.operatorName,jdbcType=VARCHAR}, #{item.operatorAccount,jdbcType=VARCHAR}, 
        #{item.role,jdbcType=INTEGER}, #{item.module,jdbcType=INTEGER}, #{item.subModule,jdbcType=INTEGER}, 
        #{item.deleted,jdbcType=INTEGER}, #{item.roleName,jdbcType=VARCHAR}, #{item.dataFrom,jdbcType=INTEGER}, 
        #{item.result,jdbcType=INTEGER}, #{item.failReason,jdbcType=VARCHAR}, #{item.mainAcctId,jdbcType=VARCHAR}, 
        #{item.content,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 15 11:06:57 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into operate_record (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'time'.toString() == column.value">
          #{item.time,jdbcType=TIMESTAMP}
        </if>
        <if test="'ip'.toString() == column.value">
          #{item.ip,jdbcType=VARCHAR}
        </if>
        <if test="'operator_name'.toString() == column.value">
          #{item.operatorName,jdbcType=VARCHAR}
        </if>
        <if test="'operator_account'.toString() == column.value">
          #{item.operatorAccount,jdbcType=VARCHAR}
        </if>
        <if test="'role'.toString() == column.value">
          #{item.role,jdbcType=INTEGER}
        </if>
        <if test="'module'.toString() == column.value">
          #{item.module,jdbcType=INTEGER}
        </if>
        <if test="'sub_module'.toString() == column.value">
          #{item.subModule,jdbcType=INTEGER}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=INTEGER}
        </if>
        <if test="'role_name'.toString() == column.value">
          #{item.roleName,jdbcType=VARCHAR}
        </if>
        <if test="'data_from'.toString() == column.value">
          #{item.dataFrom,jdbcType=INTEGER}
        </if>
        <if test="'result'.toString() == column.value">
          #{item.result,jdbcType=INTEGER}
        </if>
        <if test="'fail_reason'.toString() == column.value">
          #{item.failReason,jdbcType=VARCHAR}
        </if>
        <if test="'main_acct_id'.toString() == column.value">
          #{item.mainAcctId,jdbcType=VARCHAR}
        </if>
        <if test="'content'.toString() == column.value">
          #{item.content,jdbcType=LONGVARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>