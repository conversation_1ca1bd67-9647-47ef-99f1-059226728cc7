<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.OrderReturnInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.OrderReturnInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 15 17:28:39 CST 2022. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="associated_order" jdbcType="VARCHAR" property="associatedOrder" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="order_status_time" jdbcType="TIMESTAMP" property="orderStatusTime" />
    <result column="refund_type" jdbcType="INTEGER" property="refundType" />
    <result column="refuse_reason" jdbcType="VARCHAR" property="refuseReason" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 15 17:28:39 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 15 17:28:39 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 15 17:28:39 CST 2022. by MyBatis Generator, do not modify.
    -->
    id, order_id, associated_order, status, order_status_time, refund_type, refuse_reason
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.OrderReturnInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 15 17:28:39 CST 2022. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_return_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 15 17:28:39 CST 2022. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from order_return_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 15 17:28:39 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from order_return_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.OrderReturnInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 15 17:28:39 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from order_return_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.OrderReturnInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 15 17:28:39 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_return_info (id, order_id, associated_order, 
      status, order_status_time, refund_type, 
      refuse_reason)
    values (#{id,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{associatedOrder,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{orderStatusTime,jdbcType=TIMESTAMP}, #{refundType,jdbcType=INTEGER}, 
      #{refuseReason,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.OrderReturnInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 15 17:28:39 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_return_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="associatedOrder != null">
        associated_order,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="orderStatusTime != null">
        order_status_time,
      </if>
      <if test="refundType != null">
        refund_type,
      </if>
      <if test="refuseReason != null">
        refuse_reason,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="associatedOrder != null">
        #{associatedOrder,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="orderStatusTime != null">
        #{orderStatusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundType != null">
        #{refundType,jdbcType=INTEGER},
      </if>
      <if test="refuseReason != null">
        #{refuseReason,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.OrderReturnInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 15 17:28:39 CST 2022. by MyBatis Generator, do not modify.
    -->
    select count(*) from order_return_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 15 17:28:39 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_return_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.associatedOrder != null">
        associated_order = #{record.associatedOrder,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.orderStatusTime != null">
        order_status_time = #{record.orderStatusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.refundType != null">
        refund_type = #{record.refundType,jdbcType=INTEGER},
      </if>
      <if test="record.refuseReason != null">
        refuse_reason = #{record.refuseReason,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 15 17:28:39 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_return_info
    set id = #{record.id,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      associated_order = #{record.associatedOrder,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      order_status_time = #{record.orderStatusTime,jdbcType=TIMESTAMP},
      refund_type = #{record.refundType,jdbcType=INTEGER},
      refuse_reason = #{record.refuseReason,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.OrderReturnInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 15 17:28:39 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_return_info
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="associatedOrder != null">
        associated_order = #{associatedOrder,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="orderStatusTime != null">
        order_status_time = #{orderStatusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundType != null">
        refund_type = #{refundType,jdbcType=INTEGER},
      </if>
      <if test="refuseReason != null">
        refuse_reason = #{refuseReason,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.OrderReturnInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 15 17:28:39 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_return_info
    set order_id = #{orderId,jdbcType=VARCHAR},
      associated_order = #{associatedOrder,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      order_status_time = #{orderStatusTime,jdbcType=TIMESTAMP},
      refund_type = #{refundType,jdbcType=INTEGER},
      refuse_reason = #{refuseReason,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 15 17:28:39 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_return_info
    (id, order_id, associated_order, status, order_status_time, refund_type, refuse_reason
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR}, #{item.associatedOrder,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=INTEGER}, #{item.orderStatusTime,jdbcType=TIMESTAMP}, #{item.refundType,jdbcType=INTEGER}, 
        #{item.refuseReason,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 15 17:28:39 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_return_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'associated_order'.toString() == column.value">
          #{item.associatedOrder,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'order_status_time'.toString() == column.value">
          #{item.orderStatusTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'refund_type'.toString() == column.value">
          #{item.refundType,jdbcType=INTEGER}
        </if>
        <if test="'refuse_reason'.toString() == column.value">
          #{item.refuseReason,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>