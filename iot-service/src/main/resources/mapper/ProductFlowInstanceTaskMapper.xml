<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ProductFlowInstanceTaskMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.ProductFlowInstanceTask">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Mar 15 14:24:01 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="flow_instance_id" jdbcType="VARCHAR" property="flowInstanceId" />
    <result column="flow_id" jdbcType="VARCHAR" property="flowId" />
    <result column="step_id" jdbcType="VARCHAR" property="stepId" />
    <result column="step_name" jdbcType="VARCHAR" property="stepName" />
    <result column="assignee_id" jdbcType="VARCHAR" property="assigneeId" />
    <result column="assignee_name" jdbcType="VARCHAR" property="assigneeName" />
    <result column="options" jdbcType="VARCHAR" property="options" />
    <result column="next_assignee_id" jdbcType="VARCHAR" property="nextAssigneeId" />
    <result column="handle_status" jdbcType="INTEGER" property="handleStatus" />
    <result column="handle_time" jdbcType="TIMESTAMP" property="handleTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Mar 15 14:24:01 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Mar 15 14:24:01 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Mar 15 14:24:01 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, flow_instance_id, flow_id, step_id, step_name, assignee_id, assignee_name, options, 
    next_assignee_id, handle_status, handle_time, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceTaskExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Mar 15 14:24:01 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from product_flow_instance_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Mar 15 14:24:01 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from product_flow_instance_task
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Mar 15 14:24:01 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from product_flow_instance_task
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceTaskExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Mar 15 14:24:01 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from product_flow_instance_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceTask">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Mar 15 14:24:01 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_task (id, flow_instance_id, flow_id, 
      step_id, step_name, assignee_id, 
      assignee_name, options, next_assignee_id, 
      handle_status, handle_time, create_time, 
      update_time)
    values (#{id,jdbcType=VARCHAR}, #{flowInstanceId,jdbcType=VARCHAR}, #{flowId,jdbcType=VARCHAR}, 
      #{stepId,jdbcType=VARCHAR}, #{stepName,jdbcType=VARCHAR}, #{assigneeId,jdbcType=VARCHAR}, 
      #{assigneeName,jdbcType=VARCHAR}, #{options,jdbcType=VARCHAR}, #{nextAssigneeId,jdbcType=VARCHAR}, 
      #{handleStatus,jdbcType=INTEGER}, #{handleTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceTask">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Mar 15 14:24:01 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="flowInstanceId != null">
        flow_instance_id,
      </if>
      <if test="flowId != null">
        flow_id,
      </if>
      <if test="stepId != null">
        step_id,
      </if>
      <if test="stepName != null">
        step_name,
      </if>
      <if test="assigneeId != null">
        assignee_id,
      </if>
      <if test="assigneeName != null">
        assignee_name,
      </if>
      <if test="options != null">
        options,
      </if>
      <if test="nextAssigneeId != null">
        next_assignee_id,
      </if>
      <if test="handleStatus != null">
        handle_status,
      </if>
      <if test="handleTime != null">
        handle_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="flowInstanceId != null">
        #{flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="flowId != null">
        #{flowId,jdbcType=VARCHAR},
      </if>
      <if test="stepId != null">
        #{stepId,jdbcType=VARCHAR},
      </if>
      <if test="stepName != null">
        #{stepName,jdbcType=VARCHAR},
      </if>
      <if test="assigneeId != null">
        #{assigneeId,jdbcType=VARCHAR},
      </if>
      <if test="assigneeName != null">
        #{assigneeName,jdbcType=VARCHAR},
      </if>
      <if test="options != null">
        #{options,jdbcType=VARCHAR},
      </if>
      <if test="nextAssigneeId != null">
        #{nextAssigneeId,jdbcType=VARCHAR},
      </if>
      <if test="handleStatus != null">
        #{handleStatus,jdbcType=INTEGER},
      </if>
      <if test="handleTime != null">
        #{handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceTaskExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Mar 15 14:24:01 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from product_flow_instance_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Mar 15 14:24:01 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.flowInstanceId != null">
        flow_instance_id = #{record.flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.flowId != null">
        flow_id = #{record.flowId,jdbcType=VARCHAR},
      </if>
      <if test="record.stepId != null">
        step_id = #{record.stepId,jdbcType=VARCHAR},
      </if>
      <if test="record.stepName != null">
        step_name = #{record.stepName,jdbcType=VARCHAR},
      </if>
      <if test="record.assigneeId != null">
        assignee_id = #{record.assigneeId,jdbcType=VARCHAR},
      </if>
      <if test="record.assigneeName != null">
        assignee_name = #{record.assigneeName,jdbcType=VARCHAR},
      </if>
      <if test="record.options != null">
        options = #{record.options,jdbcType=VARCHAR},
      </if>
      <if test="record.nextAssigneeId != null">
        next_assignee_id = #{record.nextAssigneeId,jdbcType=VARCHAR},
      </if>
      <if test="record.handleStatus != null">
        handle_status = #{record.handleStatus,jdbcType=INTEGER},
      </if>
      <if test="record.handleTime != null">
        handle_time = #{record.handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Mar 15 14:24:01 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_task
    set id = #{record.id,jdbcType=VARCHAR},
      flow_instance_id = #{record.flowInstanceId,jdbcType=VARCHAR},
      flow_id = #{record.flowId,jdbcType=VARCHAR},
      step_id = #{record.stepId,jdbcType=VARCHAR},
      step_name = #{record.stepName,jdbcType=VARCHAR},
      assignee_id = #{record.assigneeId,jdbcType=VARCHAR},
      assignee_name = #{record.assigneeName,jdbcType=VARCHAR},
      options = #{record.options,jdbcType=VARCHAR},
      next_assignee_id = #{record.nextAssigneeId,jdbcType=VARCHAR},
      handle_status = #{record.handleStatus,jdbcType=INTEGER},
      handle_time = #{record.handleTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceTask">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Mar 15 14:24:01 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_task
    <set>
      <if test="flowInstanceId != null">
        flow_instance_id = #{flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="flowId != null">
        flow_id = #{flowId,jdbcType=VARCHAR},
      </if>
      <if test="stepId != null">
        step_id = #{stepId,jdbcType=VARCHAR},
      </if>
      <if test="stepName != null">
        step_name = #{stepName,jdbcType=VARCHAR},
      </if>
      <if test="assigneeId != null">
        assignee_id = #{assigneeId,jdbcType=VARCHAR},
      </if>
      <if test="assigneeName != null">
        assignee_name = #{assigneeName,jdbcType=VARCHAR},
      </if>
      <if test="options != null">
        options = #{options,jdbcType=VARCHAR},
      </if>
      <if test="nextAssigneeId != null">
        next_assignee_id = #{nextAssigneeId,jdbcType=VARCHAR},
      </if>
      <if test="handleStatus != null">
        handle_status = #{handleStatus,jdbcType=INTEGER},
      </if>
      <if test="handleTime != null">
        handle_time = #{handleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceTask">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Mar 15 14:24:01 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_task
    set flow_instance_id = #{flowInstanceId,jdbcType=VARCHAR},
      flow_id = #{flowId,jdbcType=VARCHAR},
      step_id = #{stepId,jdbcType=VARCHAR},
      step_name = #{stepName,jdbcType=VARCHAR},
      assignee_id = #{assigneeId,jdbcType=VARCHAR},
      assignee_name = #{assigneeName,jdbcType=VARCHAR},
      options = #{options,jdbcType=VARCHAR},
      next_assignee_id = #{nextAssigneeId,jdbcType=VARCHAR},
      handle_status = #{handleStatus,jdbcType=INTEGER},
      handle_time = #{handleTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Mar 15 14:24:01 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_task
    (id, flow_instance_id, flow_id, step_id, step_name, assignee_id, assignee_name, options, 
      next_assignee_id, handle_status, handle_time, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.flowInstanceId,jdbcType=VARCHAR}, #{item.flowId,jdbcType=VARCHAR}, 
        #{item.stepId,jdbcType=VARCHAR}, #{item.stepName,jdbcType=VARCHAR}, #{item.assigneeId,jdbcType=VARCHAR}, 
        #{item.assigneeName,jdbcType=VARCHAR}, #{item.options,jdbcType=VARCHAR}, #{item.nextAssigneeId,jdbcType=VARCHAR}, 
        #{item.handleStatus,jdbcType=INTEGER}, #{item.handleTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Mar 15 14:24:01 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_task (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'flow_instance_id'.toString() == column.value">
          #{item.flowInstanceId,jdbcType=VARCHAR}
        </if>
        <if test="'flow_id'.toString() == column.value">
          #{item.flowId,jdbcType=VARCHAR}
        </if>
        <if test="'step_id'.toString() == column.value">
          #{item.stepId,jdbcType=VARCHAR}
        </if>
        <if test="'step_name'.toString() == column.value">
          #{item.stepName,jdbcType=VARCHAR}
        </if>
        <if test="'assignee_id'.toString() == column.value">
          #{item.assigneeId,jdbcType=VARCHAR}
        </if>
        <if test="'assignee_name'.toString() == column.value">
          #{item.assigneeName,jdbcType=VARCHAR}
        </if>
        <if test="'options'.toString() == column.value">
          #{item.options,jdbcType=VARCHAR}
        </if>
        <if test="'next_assignee_id'.toString() == column.value">
          #{item.nextAssigneeId,jdbcType=VARCHAR}
        </if>
        <if test="'handle_status'.toString() == column.value">
          #{item.handleStatus,jdbcType=INTEGER}
        </if>
        <if test="'handle_time'.toString() == column.value">
          #{item.handleTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>