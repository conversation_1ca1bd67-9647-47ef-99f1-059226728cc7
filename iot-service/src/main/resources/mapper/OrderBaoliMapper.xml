<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.OrderBaoliMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.OrderBaoli">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jul 20 09:07:10 CST 2023. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_atom_info_id" jdbcType="VARCHAR" property="orderAtomInfoId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="sku_offering_name" jdbcType="VARCHAR" property="skuOfferingName" />
    <result column="total_price" jdbcType="BIGINT" property="totalPrice" />
    <result column="contract_num" jdbcType="VARCHAR" property="contractNum" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="baoli_status" jdbcType="INTEGER" property="baoliStatus" />
    <result column="cooperator_id" jdbcType="VARCHAR" property="cooperatorId" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
    <result column="return_price" jdbcType="BIGINT" property="returnPrice" />
    <result column="order_finish_time" jdbcType="TIMESTAMP" property="orderFinishTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jul 20 09:07:10 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jul 20 09:07:10 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jul 20 09:07:10 CST 2023. by MyBatis Generator, do not modify.
    -->
    id, order_atom_info_id, order_id, sku_offering_name, total_price, contract_num, buyer_name, 
    seller_name, baoli_status, cooperator_id, trade_no, return_price, order_finish_time, 
    create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderBaoliExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jul 20 09:07:10 CST 2023. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_baoli
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jul 20 09:07:10 CST 2023. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from order_baoli
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jul 20 09:07:10 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from order_baoli
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderBaoliExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jul 20 09:07:10 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from order_baoli
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderBaoli">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jul 20 09:07:10 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into order_baoli (id, order_atom_info_id, order_id, 
      sku_offering_name, total_price, contract_num, 
      buyer_name, seller_name, baoli_status, 
      cooperator_id, trade_no, return_price, 
      order_finish_time, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{orderAtomInfoId,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, 
      #{skuOfferingName,jdbcType=VARCHAR}, #{totalPrice,jdbcType=BIGINT}, #{contractNum,jdbcType=VARCHAR}, 
      #{buyerName,jdbcType=VARCHAR}, #{sellerName,jdbcType=VARCHAR}, #{baoliStatus,jdbcType=INTEGER}, 
      #{cooperatorId,jdbcType=VARCHAR}, #{tradeNo,jdbcType=VARCHAR}, #{returnPrice,jdbcType=BIGINT}, 
      #{orderFinishTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderBaoli">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jul 20 09:07:10 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into order_baoli
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderAtomInfoId != null">
        order_atom_info_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="skuOfferingName != null">
        sku_offering_name,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="contractNum != null">
        contract_num,
      </if>
      <if test="buyerName != null">
        buyer_name,
      </if>
      <if test="sellerName != null">
        seller_name,
      </if>
      <if test="baoliStatus != null">
        baoli_status,
      </if>
      <if test="cooperatorId != null">
        cooperator_id,
      </if>
      <if test="tradeNo != null">
        trade_no,
      </if>
      <if test="returnPrice != null">
        return_price,
      </if>
      <if test="orderFinishTime != null">
        order_finish_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderAtomInfoId != null">
        #{orderAtomInfoId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingName != null">
        #{skuOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=BIGINT},
      </if>
      <if test="contractNum != null">
        #{contractNum,jdbcType=VARCHAR},
      </if>
      <if test="buyerName != null">
        #{buyerName,jdbcType=VARCHAR},
      </if>
      <if test="sellerName != null">
        #{sellerName,jdbcType=VARCHAR},
      </if>
      <if test="baoliStatus != null">
        #{baoliStatus,jdbcType=INTEGER},
      </if>
      <if test="cooperatorId != null">
        #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="tradeNo != null">
        #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="returnPrice != null">
        #{returnPrice,jdbcType=BIGINT},
      </if>
      <if test="orderFinishTime != null">
        #{orderFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderBaoliExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jul 20 09:07:10 CST 2023. by MyBatis Generator, do not modify.
    -->
    select count(*) from order_baoli
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jul 20 09:07:10 CST 2023. by MyBatis Generator, do not modify.
    -->
    update order_baoli
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderAtomInfoId != null">
        order_atom_info_id = #{record.orderAtomInfoId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.skuOfferingName != null">
        sku_offering_name = #{record.skuOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="record.totalPrice != null">
        total_price = #{record.totalPrice,jdbcType=BIGINT},
      </if>
      <if test="record.contractNum != null">
        contract_num = #{record.contractNum,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerName != null">
        buyer_name = #{record.buyerName,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerName != null">
        seller_name = #{record.sellerName,jdbcType=VARCHAR},
      </if>
      <if test="record.baoliStatus != null">
        baoli_status = #{record.baoliStatus,jdbcType=INTEGER},
      </if>
      <if test="record.cooperatorId != null">
        cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.tradeNo != null">
        trade_no = #{record.tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="record.returnPrice != null">
        return_price = #{record.returnPrice,jdbcType=BIGINT},
      </if>
      <if test="record.orderFinishTime != null">
        order_finish_time = #{record.orderFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jul 20 09:07:10 CST 2023. by MyBatis Generator, do not modify.
    -->
    update order_baoli
    set id = #{record.id,jdbcType=VARCHAR},
      order_atom_info_id = #{record.orderAtomInfoId,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      sku_offering_name = #{record.skuOfferingName,jdbcType=VARCHAR},
      total_price = #{record.totalPrice,jdbcType=BIGINT},
      contract_num = #{record.contractNum,jdbcType=VARCHAR},
      buyer_name = #{record.buyerName,jdbcType=VARCHAR},
      seller_name = #{record.sellerName,jdbcType=VARCHAR},
      baoli_status = #{record.baoliStatus,jdbcType=INTEGER},
      cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      trade_no = #{record.tradeNo,jdbcType=VARCHAR},
      return_price = #{record.returnPrice,jdbcType=BIGINT},
      order_finish_time = #{record.orderFinishTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderBaoli">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jul 20 09:07:10 CST 2023. by MyBatis Generator, do not modify.
    -->
    update order_baoli
    <set>
      <if test="orderAtomInfoId != null">
        order_atom_info_id = #{orderAtomInfoId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingName != null">
        sku_offering_name = #{skuOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=BIGINT},
      </if>
      <if test="contractNum != null">
        contract_num = #{contractNum,jdbcType=VARCHAR},
      </if>
      <if test="buyerName != null">
        buyer_name = #{buyerName,jdbcType=VARCHAR},
      </if>
      <if test="sellerName != null">
        seller_name = #{sellerName,jdbcType=VARCHAR},
      </if>
      <if test="baoliStatus != null">
        baoli_status = #{baoliStatus,jdbcType=INTEGER},
      </if>
      <if test="cooperatorId != null">
        cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="tradeNo != null">
        trade_no = #{tradeNo,jdbcType=VARCHAR},
      </if>
      <if test="returnPrice != null">
        return_price = #{returnPrice,jdbcType=BIGINT},
      </if>
      <if test="orderFinishTime != null">
        order_finish_time = #{orderFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderBaoli">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jul 20 09:07:10 CST 2023. by MyBatis Generator, do not modify.
    -->
    update order_baoli
    set order_atom_info_id = #{orderAtomInfoId,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      sku_offering_name = #{skuOfferingName,jdbcType=VARCHAR},
      total_price = #{totalPrice,jdbcType=BIGINT},
      contract_num = #{contractNum,jdbcType=VARCHAR},
      buyer_name = #{buyerName,jdbcType=VARCHAR},
      seller_name = #{sellerName,jdbcType=VARCHAR},
      baoli_status = #{baoliStatus,jdbcType=INTEGER},
      cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      trade_no = #{tradeNo,jdbcType=VARCHAR},
      return_price = #{returnPrice,jdbcType=BIGINT},
      order_finish_time = #{orderFinishTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jul 20 09:07:10 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into order_baoli
    (id, order_atom_info_id, order_id, sku_offering_name, total_price, contract_num, 
      buyer_name, seller_name, baoli_status, cooperator_id, trade_no, return_price, order_finish_time, 
      create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.orderAtomInfoId,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR}, 
        #{item.skuOfferingName,jdbcType=VARCHAR}, #{item.totalPrice,jdbcType=BIGINT}, #{item.contractNum,jdbcType=VARCHAR}, 
        #{item.buyerName,jdbcType=VARCHAR}, #{item.sellerName,jdbcType=VARCHAR}, #{item.baoliStatus,jdbcType=INTEGER}, 
        #{item.cooperatorId,jdbcType=VARCHAR}, #{item.tradeNo,jdbcType=VARCHAR}, #{item.returnPrice,jdbcType=BIGINT}, 
        #{item.orderFinishTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Jul 20 09:07:10 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into order_baoli (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'order_atom_info_id'.toString() == column.value">
          #{item.orderAtomInfoId,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'sku_offering_name'.toString() == column.value">
          #{item.skuOfferingName,jdbcType=VARCHAR}
        </if>
        <if test="'total_price'.toString() == column.value">
          #{item.totalPrice,jdbcType=BIGINT}
        </if>
        <if test="'contract_num'.toString() == column.value">
          #{item.contractNum,jdbcType=VARCHAR}
        </if>
        <if test="'buyer_name'.toString() == column.value">
          #{item.buyerName,jdbcType=VARCHAR}
        </if>
        <if test="'seller_name'.toString() == column.value">
          #{item.sellerName,jdbcType=VARCHAR}
        </if>
        <if test="'baoli_status'.toString() == column.value">
          #{item.baoliStatus,jdbcType=INTEGER}
        </if>
        <if test="'cooperator_id'.toString() == column.value">
          #{item.cooperatorId,jdbcType=VARCHAR}
        </if>
        <if test="'trade_no'.toString() == column.value">
          #{item.tradeNo,jdbcType=VARCHAR}
        </if>
        <if test="'return_price'.toString() == column.value">
          #{item.returnPrice,jdbcType=BIGINT}
        </if>
        <if test="'order_finish_time'.toString() == column.value">
          #{item.orderFinishTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>