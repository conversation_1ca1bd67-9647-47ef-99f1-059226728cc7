<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ServiceOpenInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.ServiceOpenInfo">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="atom_offering_code" jdbcType="VARCHAR" property="atomOfferingCode" />
    <result column="soft_service_open_status" jdbcType="INTEGER" property="softServiceOpenStatus" />
    <result column="soft_service_retail_status" jdbcType="INTEGER" property="softServiceRetailStatus" />
    <result column="soft_service_phone" jdbcType="VARCHAR" property="softServicePhone" />
    <result column="soft_service_open_time" jdbcType="TIMESTAMP" property="softServiceOpenTime" />
    <result column="soft_service_retail_time" jdbcType="TIMESTAMP" property="softServiceRetailTime" />
    <result column="soft_service_use_time" jdbcType="TIMESTAMP" property="softServiceUseTime" />
    <result column="soft_service_remark" jdbcType="VARCHAR" property="softServiceRemark" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="ext_soft_offering_code" jdbcType="VARCHAR" property="extSoftOfferingCode" />
    <result column="open_fail_reason" jdbcType="VARCHAR" property="openFailReason" />
    <result column="retail_fail_reason" jdbcType="VARCHAR" property="retailFailReason" />
    <result column="spu_offering_code" jdbcType="VARCHAR" property="spuOfferingCode" />
    <result column="sku_offering_code" jdbcType="VARCHAR" property="skuOfferingCode" />
    <result column="atom_order_id" jdbcType="VARCHAR" property="atomOrderId" />
    <result column="sync_iot_fail_status" jdbcType="INTEGER" property="syncIotFailStatus" />
    <result column="sync_iot_fail_reson" jdbcType="VARCHAR" property="syncIotFailReson" />
    <result column="equipment_code" jdbcType="VARCHAR" property="equipmentCode" />
    <result column="open_sucess_num" jdbcType="BIGINT" property="openSucessNum" />
    <result column="retail_sucess_num" jdbcType="BIGINT" property="retailSucessNum" />
    <result column="open_num" jdbcType="BIGINT" property="openNum" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, atom_offering_code, soft_service_open_status, soft_service_retail_status, soft_service_phone, 
    soft_service_open_time, soft_service_retail_time, soft_service_use_time, soft_service_remark, 
    order_id, ext_soft_offering_code, open_fail_reason, retail_fail_reason, spu_offering_code, 
    sku_offering_code, atom_order_id, sync_iot_fail_status, sync_iot_fail_reson, equipment_code, 
    open_sucess_num, retail_sucess_num, open_num
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ServiceOpenInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from service_open_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from service_open_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from service_open_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ServiceOpenInfoExample">
    delete from service_open_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.ServiceOpenInfo">
    insert into service_open_info (id, atom_offering_code, soft_service_open_status, 
      soft_service_retail_status, soft_service_phone, 
      soft_service_open_time, soft_service_retail_time, 
      soft_service_use_time, soft_service_remark, 
      order_id, ext_soft_offering_code, open_fail_reason, 
      retail_fail_reason, spu_offering_code, sku_offering_code, 
      atom_order_id, sync_iot_fail_status, sync_iot_fail_reson, 
      equipment_code, open_sucess_num, retail_sucess_num, 
      open_num)
    values (#{id,jdbcType=VARCHAR}, #{atomOfferingCode,jdbcType=VARCHAR}, #{softServiceOpenStatus,jdbcType=INTEGER}, 
      #{softServiceRetailStatus,jdbcType=INTEGER}, #{softServicePhone,jdbcType=VARCHAR}, 
      #{softServiceOpenTime,jdbcType=TIMESTAMP}, #{softServiceRetailTime,jdbcType=TIMESTAMP}, 
      #{softServiceUseTime,jdbcType=TIMESTAMP}, #{softServiceRemark,jdbcType=VARCHAR}, 
      #{orderId,jdbcType=VARCHAR}, #{extSoftOfferingCode,jdbcType=VARCHAR}, #{openFailReason,jdbcType=VARCHAR}, 
      #{retailFailReason,jdbcType=VARCHAR}, #{spuOfferingCode,jdbcType=VARCHAR}, #{skuOfferingCode,jdbcType=VARCHAR}, 
      #{atomOrderId,jdbcType=VARCHAR}, #{syncIotFailStatus,jdbcType=INTEGER}, #{syncIotFailReson,jdbcType=VARCHAR}, 
      #{equipmentCode,jdbcType=VARCHAR}, #{openSucessNum,jdbcType=BIGINT}, #{retailSucessNum,jdbcType=BIGINT}, 
      #{openNum,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ServiceOpenInfo">
    insert into service_open_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="atomOfferingCode != null">
        atom_offering_code,
      </if>
      <if test="softServiceOpenStatus != null">
        soft_service_open_status,
      </if>
      <if test="softServiceRetailStatus != null">
        soft_service_retail_status,
      </if>
      <if test="softServicePhone != null">
        soft_service_phone,
      </if>
      <if test="softServiceOpenTime != null">
        soft_service_open_time,
      </if>
      <if test="softServiceRetailTime != null">
        soft_service_retail_time,
      </if>
      <if test="softServiceUseTime != null">
        soft_service_use_time,
      </if>
      <if test="softServiceRemark != null">
        soft_service_remark,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="extSoftOfferingCode != null">
        ext_soft_offering_code,
      </if>
      <if test="openFailReason != null">
        open_fail_reason,
      </if>
      <if test="retailFailReason != null">
        retail_fail_reason,
      </if>
      <if test="spuOfferingCode != null">
        spu_offering_code,
      </if>
      <if test="skuOfferingCode != null">
        sku_offering_code,
      </if>
      <if test="atomOrderId != null">
        atom_order_id,
      </if>
      <if test="syncIotFailStatus != null">
        sync_iot_fail_status,
      </if>
      <if test="syncIotFailReson != null">
        sync_iot_fail_reson,
      </if>
      <if test="equipmentCode != null">
        equipment_code,
      </if>
      <if test="openSucessNum != null">
        open_sucess_num,
      </if>
      <if test="retailSucessNum != null">
        retail_sucess_num,
      </if>
      <if test="openNum != null">
        open_num,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingCode != null">
        #{atomOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="softServiceOpenStatus != null">
        #{softServiceOpenStatus,jdbcType=INTEGER},
      </if>
      <if test="softServiceRetailStatus != null">
        #{softServiceRetailStatus,jdbcType=INTEGER},
      </if>
      <if test="softServicePhone != null">
        #{softServicePhone,jdbcType=VARCHAR},
      </if>
      <if test="softServiceOpenTime != null">
        #{softServiceOpenTime,jdbcType=TIMESTAMP},
      </if>
      <if test="softServiceRetailTime != null">
        #{softServiceRetailTime,jdbcType=TIMESTAMP},
      </if>
      <if test="softServiceUseTime != null">
        #{softServiceUseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="softServiceRemark != null">
        #{softServiceRemark,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="extSoftOfferingCode != null">
        #{extSoftOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="openFailReason != null">
        #{openFailReason,jdbcType=VARCHAR},
      </if>
      <if test="retailFailReason != null">
        #{retailFailReason,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingCode != null">
        #{spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingCode != null">
        #{skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="atomOrderId != null">
        #{atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="syncIotFailStatus != null">
        #{syncIotFailStatus,jdbcType=INTEGER},
      </if>
      <if test="syncIotFailReson != null">
        #{syncIotFailReson,jdbcType=VARCHAR},
      </if>
      <if test="equipmentCode != null">
        #{equipmentCode,jdbcType=VARCHAR},
      </if>
      <if test="openSucessNum != null">
        #{openSucessNum,jdbcType=BIGINT},
      </if>
      <if test="retailSucessNum != null">
        #{retailSucessNum,jdbcType=BIGINT},
      </if>
      <if test="openNum != null">
        #{openNum,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ServiceOpenInfoExample" resultType="java.lang.Long">
    select count(*) from service_open_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update service_open_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOfferingCode != null">
        atom_offering_code = #{record.atomOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.softServiceOpenStatus != null">
        soft_service_open_status = #{record.softServiceOpenStatus,jdbcType=INTEGER},
      </if>
      <if test="record.softServiceRetailStatus != null">
        soft_service_retail_status = #{record.softServiceRetailStatus,jdbcType=INTEGER},
      </if>
      <if test="record.softServicePhone != null">
        soft_service_phone = #{record.softServicePhone,jdbcType=VARCHAR},
      </if>
      <if test="record.softServiceOpenTime != null">
        soft_service_open_time = #{record.softServiceOpenTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.softServiceRetailTime != null">
        soft_service_retail_time = #{record.softServiceRetailTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.softServiceUseTime != null">
        soft_service_use_time = #{record.softServiceUseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.softServiceRemark != null">
        soft_service_remark = #{record.softServiceRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.extSoftOfferingCode != null">
        ext_soft_offering_code = #{record.extSoftOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.openFailReason != null">
        open_fail_reason = #{record.openFailReason,jdbcType=VARCHAR},
      </if>
      <if test="record.retailFailReason != null">
        retail_fail_reason = #{record.retailFailReason,jdbcType=VARCHAR},
      </if>
      <if test="record.spuOfferingCode != null">
        spu_offering_code = #{record.spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.skuOfferingCode != null">
        sku_offering_code = #{record.skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOrderId != null">
        atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.syncIotFailStatus != null">
        sync_iot_fail_status = #{record.syncIotFailStatus,jdbcType=INTEGER},
      </if>
      <if test="record.syncIotFailReson != null">
        sync_iot_fail_reson = #{record.syncIotFailReson,jdbcType=VARCHAR},
      </if>
      <if test="record.equipmentCode != null">
        equipment_code = #{record.equipmentCode,jdbcType=VARCHAR},
      </if>
      <if test="record.openSucessNum != null">
        open_sucess_num = #{record.openSucessNum,jdbcType=BIGINT},
      </if>
      <if test="record.retailSucessNum != null">
        retail_sucess_num = #{record.retailSucessNum,jdbcType=BIGINT},
      </if>
      <if test="record.openNum != null">
        open_num = #{record.openNum,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update service_open_info
    set id = #{record.id,jdbcType=VARCHAR},
      atom_offering_code = #{record.atomOfferingCode,jdbcType=VARCHAR},
      soft_service_open_status = #{record.softServiceOpenStatus,jdbcType=INTEGER},
      soft_service_retail_status = #{record.softServiceRetailStatus,jdbcType=INTEGER},
      soft_service_phone = #{record.softServicePhone,jdbcType=VARCHAR},
      soft_service_open_time = #{record.softServiceOpenTime,jdbcType=TIMESTAMP},
      soft_service_retail_time = #{record.softServiceRetailTime,jdbcType=TIMESTAMP},
      soft_service_use_time = #{record.softServiceUseTime,jdbcType=TIMESTAMP},
      soft_service_remark = #{record.softServiceRemark,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      ext_soft_offering_code = #{record.extSoftOfferingCode,jdbcType=VARCHAR},
      open_fail_reason = #{record.openFailReason,jdbcType=VARCHAR},
      retail_fail_reason = #{record.retailFailReason,jdbcType=VARCHAR},
      spu_offering_code = #{record.spuOfferingCode,jdbcType=VARCHAR},
      sku_offering_code = #{record.skuOfferingCode,jdbcType=VARCHAR},
      atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      sync_iot_fail_status = #{record.syncIotFailStatus,jdbcType=INTEGER},
      sync_iot_fail_reson = #{record.syncIotFailReson,jdbcType=VARCHAR},
      equipment_code = #{record.equipmentCode,jdbcType=VARCHAR},
      open_sucess_num = #{record.openSucessNum,jdbcType=BIGINT},
      retail_sucess_num = #{record.retailSucessNum,jdbcType=BIGINT},
      open_num = #{record.openNum,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ServiceOpenInfo">
    update service_open_info
    <set>
      <if test="atomOfferingCode != null">
        atom_offering_code = #{atomOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="softServiceOpenStatus != null">
        soft_service_open_status = #{softServiceOpenStatus,jdbcType=INTEGER},
      </if>
      <if test="softServiceRetailStatus != null">
        soft_service_retail_status = #{softServiceRetailStatus,jdbcType=INTEGER},
      </if>
      <if test="softServicePhone != null">
        soft_service_phone = #{softServicePhone,jdbcType=VARCHAR},
      </if>
      <if test="softServiceOpenTime != null">
        soft_service_open_time = #{softServiceOpenTime,jdbcType=TIMESTAMP},
      </if>
      <if test="softServiceRetailTime != null">
        soft_service_retail_time = #{softServiceRetailTime,jdbcType=TIMESTAMP},
      </if>
      <if test="softServiceUseTime != null">
        soft_service_use_time = #{softServiceUseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="softServiceRemark != null">
        soft_service_remark = #{softServiceRemark,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="extSoftOfferingCode != null">
        ext_soft_offering_code = #{extSoftOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="openFailReason != null">
        open_fail_reason = #{openFailReason,jdbcType=VARCHAR},
      </if>
      <if test="retailFailReason != null">
        retail_fail_reason = #{retailFailReason,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingCode != null">
        spu_offering_code = #{spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingCode != null">
        sku_offering_code = #{skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="atomOrderId != null">
        atom_order_id = #{atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="syncIotFailStatus != null">
        sync_iot_fail_status = #{syncIotFailStatus,jdbcType=INTEGER},
      </if>
      <if test="syncIotFailReson != null">
        sync_iot_fail_reson = #{syncIotFailReson,jdbcType=VARCHAR},
      </if>
      <if test="equipmentCode != null">
        equipment_code = #{equipmentCode,jdbcType=VARCHAR},
      </if>
      <if test="openSucessNum != null">
        open_sucess_num = #{openSucessNum,jdbcType=BIGINT},
      </if>
      <if test="retailSucessNum != null">
        retail_sucess_num = #{retailSucessNum,jdbcType=BIGINT},
      </if>
      <if test="openNum != null">
        open_num = #{openNum,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.ServiceOpenInfo">
    update service_open_info
    set atom_offering_code = #{atomOfferingCode,jdbcType=VARCHAR},
      soft_service_open_status = #{softServiceOpenStatus,jdbcType=INTEGER},
      soft_service_retail_status = #{softServiceRetailStatus,jdbcType=INTEGER},
      soft_service_phone = #{softServicePhone,jdbcType=VARCHAR},
      soft_service_open_time = #{softServiceOpenTime,jdbcType=TIMESTAMP},
      soft_service_retail_time = #{softServiceRetailTime,jdbcType=TIMESTAMP},
      soft_service_use_time = #{softServiceUseTime,jdbcType=TIMESTAMP},
      soft_service_remark = #{softServiceRemark,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      ext_soft_offering_code = #{extSoftOfferingCode,jdbcType=VARCHAR},
      open_fail_reason = #{openFailReason,jdbcType=VARCHAR},
      retail_fail_reason = #{retailFailReason,jdbcType=VARCHAR},
      spu_offering_code = #{spuOfferingCode,jdbcType=VARCHAR},
      sku_offering_code = #{skuOfferingCode,jdbcType=VARCHAR},
      atom_order_id = #{atomOrderId,jdbcType=VARCHAR},
      sync_iot_fail_status = #{syncIotFailStatus,jdbcType=INTEGER},
      sync_iot_fail_reson = #{syncIotFailReson,jdbcType=VARCHAR},
      equipment_code = #{equipmentCode,jdbcType=VARCHAR},
      open_sucess_num = #{openSucessNum,jdbcType=BIGINT},
      retail_sucess_num = #{retailSucessNum,jdbcType=BIGINT},
      open_num = #{openNum,jdbcType=BIGINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into service_open_info
    (id, atom_offering_code, soft_service_open_status, soft_service_retail_status, soft_service_phone, 
      soft_service_open_time, soft_service_retail_time, soft_service_use_time, soft_service_remark, 
      order_id, ext_soft_offering_code, open_fail_reason, retail_fail_reason, spu_offering_code, 
      sku_offering_code, atom_order_id, sync_iot_fail_status, sync_iot_fail_reson, equipment_code, 
      open_sucess_num, retail_sucess_num, open_num)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.atomOfferingCode,jdbcType=VARCHAR}, #{item.softServiceOpenStatus,jdbcType=INTEGER}, 
        #{item.softServiceRetailStatus,jdbcType=INTEGER}, #{item.softServicePhone,jdbcType=VARCHAR}, 
        #{item.softServiceOpenTime,jdbcType=TIMESTAMP}, #{item.softServiceRetailTime,jdbcType=TIMESTAMP}, 
        #{item.softServiceUseTime,jdbcType=TIMESTAMP}, #{item.softServiceRemark,jdbcType=VARCHAR}, 
        #{item.orderId,jdbcType=VARCHAR}, #{item.extSoftOfferingCode,jdbcType=VARCHAR}, 
        #{item.openFailReason,jdbcType=VARCHAR}, #{item.retailFailReason,jdbcType=VARCHAR}, 
        #{item.spuOfferingCode,jdbcType=VARCHAR}, #{item.skuOfferingCode,jdbcType=VARCHAR}, 
        #{item.atomOrderId,jdbcType=VARCHAR}, #{item.syncIotFailStatus,jdbcType=INTEGER}, 
        #{item.syncIotFailReson,jdbcType=VARCHAR}, #{item.equipmentCode,jdbcType=VARCHAR}, 
        #{item.openSucessNum,jdbcType=BIGINT}, #{item.retailSucessNum,jdbcType=BIGINT}, 
        #{item.openNum,jdbcType=BIGINT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into service_open_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'atom_offering_code'.toString() == column.value">
          #{item.atomOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'soft_service_open_status'.toString() == column.value">
          #{item.softServiceOpenStatus,jdbcType=INTEGER}
        </if>
        <if test="'soft_service_retail_status'.toString() == column.value">
          #{item.softServiceRetailStatus,jdbcType=INTEGER}
        </if>
        <if test="'soft_service_phone'.toString() == column.value">
          #{item.softServicePhone,jdbcType=VARCHAR}
        </if>
        <if test="'soft_service_open_time'.toString() == column.value">
          #{item.softServiceOpenTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'soft_service_retail_time'.toString() == column.value">
          #{item.softServiceRetailTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'soft_service_use_time'.toString() == column.value">
          #{item.softServiceUseTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'soft_service_remark'.toString() == column.value">
          #{item.softServiceRemark,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'ext_soft_offering_code'.toString() == column.value">
          #{item.extSoftOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'open_fail_reason'.toString() == column.value">
          #{item.openFailReason,jdbcType=VARCHAR}
        </if>
        <if test="'retail_fail_reason'.toString() == column.value">
          #{item.retailFailReason,jdbcType=VARCHAR}
        </if>
        <if test="'spu_offering_code'.toString() == column.value">
          #{item.spuOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'sku_offering_code'.toString() == column.value">
          #{item.skuOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'atom_order_id'.toString() == column.value">
          #{item.atomOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'sync_iot_fail_status'.toString() == column.value">
          #{item.syncIotFailStatus,jdbcType=INTEGER}
        </if>
        <if test="'sync_iot_fail_reson'.toString() == column.value">
          #{item.syncIotFailReson,jdbcType=VARCHAR}
        </if>
        <if test="'equipment_code'.toString() == column.value">
          #{item.equipmentCode,jdbcType=VARCHAR}
        </if>
        <if test="'open_sucess_num'.toString() == column.value">
          #{item.openSucessNum,jdbcType=BIGINT}
        </if>
        <if test="'retail_sucess_num'.toString() == column.value">
          #{item.retailSucessNum,jdbcType=BIGINT}
        </if>
        <if test="'open_num'.toString() == column.value">
          #{item.openNum,jdbcType=BIGINT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>