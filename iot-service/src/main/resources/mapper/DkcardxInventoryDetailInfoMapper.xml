<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.DkcardxInventoryDetailInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryDetailInfo">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="inventory_main_id" jdbcType="VARCHAR" property="inventoryMainId" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="province_alias_name" jdbcType="VARCHAR" property="provinceAliasName" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="reserve_quatity" jdbcType="INTEGER" property="reserveQuatity" />
    <result column="current_inventory" jdbcType="INTEGER" property="currentInventory" />
    <result column="total_inventory" jdbcType="INTEGER" property="totalInventory" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, inventory_main_id, be_id, province_name, province_alias_name, location, city_name, 
    reserve_quatity, current_inventory, total_inventory, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryDetailInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from dkcardx_inventory_detail_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from dkcardx_inventory_detail_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from dkcardx_inventory_detail_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryDetailInfoExample">
    delete from dkcardx_inventory_detail_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryDetailInfo">
    insert into dkcardx_inventory_detail_info (id, inventory_main_id, be_id, 
      province_name, province_alias_name, location, 
      city_name, reserve_quatity, current_inventory, 
      total_inventory, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{inventoryMainId,jdbcType=VARCHAR}, #{beId,jdbcType=VARCHAR}, 
      #{provinceName,jdbcType=VARCHAR}, #{provinceAliasName,jdbcType=VARCHAR}, #{location,jdbcType=VARCHAR}, 
      #{cityName,jdbcType=VARCHAR}, #{reserveQuatity,jdbcType=INTEGER}, #{currentInventory,jdbcType=INTEGER}, 
      #{totalInventory,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryDetailInfo">
    insert into dkcardx_inventory_detail_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="inventoryMainId != null">
        inventory_main_id,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="provinceAliasName != null">
        province_alias_name,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="reserveQuatity != null">
        reserve_quatity,
      </if>
      <if test="currentInventory != null">
        current_inventory,
      </if>
      <if test="totalInventory != null">
        total_inventory,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="inventoryMainId != null">
        #{inventoryMainId,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="provinceAliasName != null">
        #{provinceAliasName,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="reserveQuatity != null">
        #{reserveQuatity,jdbcType=INTEGER},
      </if>
      <if test="currentInventory != null">
        #{currentInventory,jdbcType=INTEGER},
      </if>
      <if test="totalInventory != null">
        #{totalInventory,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryDetailInfoExample" resultType="java.lang.Long">
    select count(*) from dkcardx_inventory_detail_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update dkcardx_inventory_detail_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.inventoryMainId != null">
        inventory_main_id = #{record.inventoryMainId,jdbcType=VARCHAR},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceName != null">
        province_name = #{record.provinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceAliasName != null">
        province_alias_name = #{record.provinceAliasName,jdbcType=VARCHAR},
      </if>
      <if test="record.location != null">
        location = #{record.location,jdbcType=VARCHAR},
      </if>
      <if test="record.cityName != null">
        city_name = #{record.cityName,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveQuatity != null">
        reserve_quatity = #{record.reserveQuatity,jdbcType=INTEGER},
      </if>
      <if test="record.currentInventory != null">
        current_inventory = #{record.currentInventory,jdbcType=INTEGER},
      </if>
      <if test="record.totalInventory != null">
        total_inventory = #{record.totalInventory,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update dkcardx_inventory_detail_info
    set id = #{record.id,jdbcType=VARCHAR},
      inventory_main_id = #{record.inventoryMainId,jdbcType=VARCHAR},
      be_id = #{record.beId,jdbcType=VARCHAR},
      province_name = #{record.provinceName,jdbcType=VARCHAR},
      province_alias_name = #{record.provinceAliasName,jdbcType=VARCHAR},
      location = #{record.location,jdbcType=VARCHAR},
      city_name = #{record.cityName,jdbcType=VARCHAR},
      reserve_quatity = #{record.reserveQuatity,jdbcType=INTEGER},
      current_inventory = #{record.currentInventory,jdbcType=INTEGER},
      total_inventory = #{record.totalInventory,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryDetailInfo">
    update dkcardx_inventory_detail_info
    <set>
      <if test="inventoryMainId != null">
        inventory_main_id = #{inventoryMainId,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="provinceAliasName != null">
        province_alias_name = #{provinceAliasName,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="reserveQuatity != null">
        reserve_quatity = #{reserveQuatity,jdbcType=INTEGER},
      </if>
      <if test="currentInventory != null">
        current_inventory = #{currentInventory,jdbcType=INTEGER},
      </if>
      <if test="totalInventory != null">
        total_inventory = #{totalInventory,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryDetailInfo">
    update dkcardx_inventory_detail_info
    set inventory_main_id = #{inventoryMainId,jdbcType=VARCHAR},
      be_id = #{beId,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR},
      province_alias_name = #{provinceAliasName,jdbcType=VARCHAR},
      location = #{location,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      reserve_quatity = #{reserveQuatity,jdbcType=INTEGER},
      current_inventory = #{currentInventory,jdbcType=INTEGER},
      total_inventory = #{totalInventory,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into dkcardx_inventory_detail_info
    (id, inventory_main_id, be_id, province_name, province_alias_name, location, city_name, 
      reserve_quatity, current_inventory, total_inventory, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.inventoryMainId,jdbcType=VARCHAR}, #{item.beId,jdbcType=VARCHAR}, 
        #{item.provinceName,jdbcType=VARCHAR}, #{item.provinceAliasName,jdbcType=VARCHAR}, 
        #{item.location,jdbcType=VARCHAR}, #{item.cityName,jdbcType=VARCHAR}, #{item.reserveQuatity,jdbcType=INTEGER}, 
        #{item.currentInventory,jdbcType=INTEGER}, #{item.totalInventory,jdbcType=INTEGER}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into dkcardx_inventory_detail_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'inventory_main_id'.toString() == column.value">
          #{item.inventoryMainId,jdbcType=VARCHAR}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'province_name'.toString() == column.value">
          #{item.provinceName,jdbcType=VARCHAR}
        </if>
        <if test="'province_alias_name'.toString() == column.value">
          #{item.provinceAliasName,jdbcType=VARCHAR}
        </if>
        <if test="'location'.toString() == column.value">
          #{item.location,jdbcType=VARCHAR}
        </if>
        <if test="'city_name'.toString() == column.value">
          #{item.cityName,jdbcType=VARCHAR}
        </if>
        <if test="'reserve_quatity'.toString() == column.value">
          #{item.reserveQuatity,jdbcType=INTEGER}
        </if>
        <if test="'current_inventory'.toString() == column.value">
          #{item.currentInventory,jdbcType=INTEGER}
        </if>
        <if test="'total_inventory'.toString() == column.value">
          #{item.totalInventory,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>