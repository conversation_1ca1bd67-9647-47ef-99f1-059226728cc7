<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.UserRetailMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.UserRetail">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 12 11:01:44 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="cust_code" jdbcType="VARCHAR" property="custCode" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="work_num" jdbcType="VARCHAR" property="workNum" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="role_type" jdbcType="INTEGER" property="roleType" />
    <result column="header_img_url" jdbcType="VARCHAR" property="headerImgUrl" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="audit_reason" jdbcType="VARCHAR" property="auditReason" />
    <result column="file_key" jdbcType="VARCHAR" property="fileKey" />
    <result column="audit_header_notice" jdbcType="INTEGER" property="auditHeaderNotice" />
    <result column="recommend_code" jdbcType="VARCHAR" property="recommendCode" />
    <result column="reg_time" jdbcType="TIMESTAMP" property="regTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="latest_login_time" jdbcType="TIMESTAMP" property="latestLoginTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 12 11:01:44 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 12 11:01:44 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 12 11:01:44 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, user_id, be_id, province, province_code, city, city_code, cust_code, phone, work_num, 
    name, role_type, header_img_url, audit_status, audit_reason, file_key, audit_header_notice, 
    recommend_code, reg_time, update_time, latest_login_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.UserRetailExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 12 11:01:44 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_retail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 12 11:01:44 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from user_retail
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 12 11:01:44 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from user_retail
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.UserRetailExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 12 11:01:44 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from user_retail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.UserRetail">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 12 11:01:44 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into user_retail (id, user_id, be_id, 
      province, province_code, city, 
      city_code, cust_code, phone, 
      work_num, name, role_type, 
      header_img_url, audit_status, audit_reason, 
      file_key, audit_header_notice, recommend_code, 
      reg_time, update_time, latest_login_time
      )
    values (#{id,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{beId,jdbcType=VARCHAR}, 
      #{province,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, 
      #{cityCode,jdbcType=VARCHAR}, #{custCode,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, 
      #{workNum,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{roleType,jdbcType=INTEGER}, 
      #{headerImgUrl,jdbcType=VARCHAR}, #{auditStatus,jdbcType=INTEGER}, #{auditReason,jdbcType=VARCHAR}, 
      #{fileKey,jdbcType=VARCHAR}, #{auditHeaderNotice,jdbcType=INTEGER}, #{recommendCode,jdbcType=VARCHAR}, 
      #{regTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{latestLoginTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.UserRetail">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 12 11:01:44 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into user_retail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="custCode != null">
        cust_code,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="workNum != null">
        work_num,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="roleType != null">
        role_type,
      </if>
      <if test="headerImgUrl != null">
        header_img_url,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="auditReason != null">
        audit_reason,
      </if>
      <if test="fileKey != null">
        file_key,
      </if>
      <if test="auditHeaderNotice != null">
        audit_header_notice,
      </if>
      <if test="recommendCode != null">
        recommend_code,
      </if>
      <if test="regTime != null">
        reg_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="latestLoginTime != null">
        latest_login_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="custCode != null">
        #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="workNum != null">
        #{workNum,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="roleType != null">
        #{roleType,jdbcType=INTEGER},
      </if>
      <if test="headerImgUrl != null">
        #{headerImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditReason != null">
        #{auditReason,jdbcType=VARCHAR},
      </if>
      <if test="fileKey != null">
        #{fileKey,jdbcType=VARCHAR},
      </if>
      <if test="auditHeaderNotice != null">
        #{auditHeaderNotice,jdbcType=INTEGER},
      </if>
      <if test="recommendCode != null">
        #{recommendCode,jdbcType=VARCHAR},
      </if>
      <if test="regTime != null">
        #{regTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="latestLoginTime != null">
        #{latestLoginTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.UserRetailExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 12 11:01:44 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from user_retail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 12 11:01:44 CST 2024. by MyBatis Generator, do not modify.
    -->
    update user_retail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.province != null">
        province = #{record.province,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.custCode != null">
        cust_code = #{record.custCode,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.workNum != null">
        work_num = #{record.workNum,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.roleType != null">
        role_type = #{record.roleType,jdbcType=INTEGER},
      </if>
      <if test="record.headerImgUrl != null">
        header_img_url = #{record.headerImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=INTEGER},
      </if>
      <if test="record.auditReason != null">
        audit_reason = #{record.auditReason,jdbcType=VARCHAR},
      </if>
      <if test="record.fileKey != null">
        file_key = #{record.fileKey,jdbcType=VARCHAR},
      </if>
      <if test="record.auditHeaderNotice != null">
        audit_header_notice = #{record.auditHeaderNotice,jdbcType=INTEGER},
      </if>
      <if test="record.recommendCode != null">
        recommend_code = #{record.recommendCode,jdbcType=VARCHAR},
      </if>
      <if test="record.regTime != null">
        reg_time = #{record.regTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.latestLoginTime != null">
        latest_login_time = #{record.latestLoginTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 12 11:01:44 CST 2024. by MyBatis Generator, do not modify.
    -->
    update user_retail
    set id = #{record.id,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      be_id = #{record.beId,jdbcType=VARCHAR},
      province = #{record.province,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      city = #{record.city,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      cust_code = #{record.custCode,jdbcType=VARCHAR},
      phone = #{record.phone,jdbcType=VARCHAR},
      work_num = #{record.workNum,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      role_type = #{record.roleType,jdbcType=INTEGER},
      header_img_url = #{record.headerImgUrl,jdbcType=VARCHAR},
      audit_status = #{record.auditStatus,jdbcType=INTEGER},
      audit_reason = #{record.auditReason,jdbcType=VARCHAR},
      file_key = #{record.fileKey,jdbcType=VARCHAR},
      audit_header_notice = #{record.auditHeaderNotice,jdbcType=INTEGER},
      recommend_code = #{record.recommendCode,jdbcType=VARCHAR},
      reg_time = #{record.regTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      latest_login_time = #{record.latestLoginTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.UserRetail">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 12 11:01:44 CST 2024. by MyBatis Generator, do not modify.
    -->
    update user_retail
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        province_code = #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="custCode != null">
        cust_code = #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="workNum != null">
        work_num = #{workNum,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="roleType != null">
        role_type = #{roleType,jdbcType=INTEGER},
      </if>
      <if test="headerImgUrl != null">
        header_img_url = #{headerImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditReason != null">
        audit_reason = #{auditReason,jdbcType=VARCHAR},
      </if>
      <if test="fileKey != null">
        file_key = #{fileKey,jdbcType=VARCHAR},
      </if>
      <if test="auditHeaderNotice != null">
        audit_header_notice = #{auditHeaderNotice,jdbcType=INTEGER},
      </if>
      <if test="recommendCode != null">
        recommend_code = #{recommendCode,jdbcType=VARCHAR},
      </if>
      <if test="regTime != null">
        reg_time = #{regTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="latestLoginTime != null">
        latest_login_time = #{latestLoginTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.UserRetail">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 12 11:01:44 CST 2024. by MyBatis Generator, do not modify.
    -->
    update user_retail
    set user_id = #{userId,jdbcType=VARCHAR},
      be_id = #{beId,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      province_code = #{provinceCode,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      cust_code = #{custCode,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      work_num = #{workNum,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      role_type = #{roleType,jdbcType=INTEGER},
      header_img_url = #{headerImgUrl,jdbcType=VARCHAR},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      audit_reason = #{auditReason,jdbcType=VARCHAR},
      file_key = #{fileKey,jdbcType=VARCHAR},
      audit_header_notice = #{auditHeaderNotice,jdbcType=INTEGER},
      recommend_code = #{recommendCode,jdbcType=VARCHAR},
      reg_time = #{regTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      latest_login_time = #{latestLoginTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 12 11:01:44 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into user_retail
    (id, user_id, be_id, province, province_code, city, city_code, cust_code, phone, 
      work_num, name, role_type, header_img_url, audit_status, audit_reason, file_key, 
      audit_header_notice, recommend_code, reg_time, update_time, latest_login_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, #{item.beId,jdbcType=VARCHAR}, 
        #{item.province,jdbcType=VARCHAR}, #{item.provinceCode,jdbcType=VARCHAR}, #{item.city,jdbcType=VARCHAR}, 
        #{item.cityCode,jdbcType=VARCHAR}, #{item.custCode,jdbcType=VARCHAR}, #{item.phone,jdbcType=VARCHAR}, 
        #{item.workNum,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.roleType,jdbcType=INTEGER}, 
        #{item.headerImgUrl,jdbcType=VARCHAR}, #{item.auditStatus,jdbcType=INTEGER}, #{item.auditReason,jdbcType=VARCHAR}, 
        #{item.fileKey,jdbcType=VARCHAR}, #{item.auditHeaderNotice,jdbcType=INTEGER}, #{item.recommendCode,jdbcType=VARCHAR}, 
        #{item.regTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.latestLoginTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 12 11:01:44 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into user_retail (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'province'.toString() == column.value">
          #{item.province,jdbcType=VARCHAR}
        </if>
        <if test="'province_code'.toString() == column.value">
          #{item.provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="'city'.toString() == column.value">
          #{item.city,jdbcType=VARCHAR}
        </if>
        <if test="'city_code'.toString() == column.value">
          #{item.cityCode,jdbcType=VARCHAR}
        </if>
        <if test="'cust_code'.toString() == column.value">
          #{item.custCode,jdbcType=VARCHAR}
        </if>
        <if test="'phone'.toString() == column.value">
          #{item.phone,jdbcType=VARCHAR}
        </if>
        <if test="'work_num'.toString() == column.value">
          #{item.workNum,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'role_type'.toString() == column.value">
          #{item.roleType,jdbcType=INTEGER}
        </if>
        <if test="'header_img_url'.toString() == column.value">
          #{item.headerImgUrl,jdbcType=VARCHAR}
        </if>
        <if test="'audit_status'.toString() == column.value">
          #{item.auditStatus,jdbcType=INTEGER}
        </if>
        <if test="'audit_reason'.toString() == column.value">
          #{item.auditReason,jdbcType=VARCHAR}
        </if>
        <if test="'file_key'.toString() == column.value">
          #{item.fileKey,jdbcType=VARCHAR}
        </if>
        <if test="'audit_header_notice'.toString() == column.value">
          #{item.auditHeaderNotice,jdbcType=INTEGER}
        </if>
        <if test="'recommend_code'.toString() == column.value">
          #{item.recommendCode,jdbcType=VARCHAR}
        </if>
        <if test="'reg_time'.toString() == column.value">
          #{item.regTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'latest_login_time'.toString() == column.value">
          #{item.latestLoginTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>