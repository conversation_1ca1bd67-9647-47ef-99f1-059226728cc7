<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.NewProductRequestOnlineOfflineComboInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineComboInfo">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="new_product_request_id" jdbcType="VARCHAR" property="newProductRequestId" />
    <result column="request_no" jdbcType="VARCHAR" property="requestNo" />
    <result column="request_pass" jdbcType="INTEGER" property="requestPass" />
    <result column="things_card_combo" jdbcType="VARCHAR" property="thingsCardCombo" />
    <result column="hardware_shipping_list" jdbcType="VARCHAR" property="hardwareShippingList" />
    <result column="product_param" jdbcType="VARCHAR" property="productParam" />
    <result column="product_ship_province" jdbcType="VARCHAR" property="productShipProvince" />
    <result column="product_ship_province_name" jdbcType="VARCHAR" property="productShipProvinceName" />
    <result column="product_ship_city" jdbcType="VARCHAR" property="productShipCity" />
    <result column="product_ship_city_name" jdbcType="VARCHAR" property="productShipCityName" />
    <result column="product_ship_address" jdbcType="VARCHAR" property="productShipAddress" />
    <result column="hardware_expressage_simple_name" jdbcType="VARCHAR" property="hardwareExpressageSimpleName" />
    <result column="hardware_expressage_name" jdbcType="VARCHAR" property="hardwareExpressageName" />
    <result column="product_ship_time" jdbcType="VARCHAR" property="productShipTime" />
    <result column="product_working_condition" jdbcType="VARCHAR" property="productWorkingCondition" />
    <result column="software_info" jdbcType="VARCHAR" property="softwareInfo" />
    <result column="software_get_way" jdbcType="VARCHAR" property="softwareGetWay" />
    <result column="app_mini_program_info" jdbcType="VARCHAR" property="appMiniProgramInfo" />
    <result column="app_mini_program_get_way" jdbcType="VARCHAR" property="appMiniProgramGetWay" />
    <result column="installation_services" jdbcType="VARCHAR" property="installationServices" />
    <result column="offline_reason" jdbcType="VARCHAR" property="offlineReason" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="request_offline_user_id" jdbcType="VARCHAR" property="requestOfflineUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, new_product_request_id, request_no, request_pass, things_card_combo, hardware_shipping_list, 
    product_param, product_ship_province, product_ship_province_name, product_ship_city, 
    product_ship_city_name, product_ship_address, hardware_expressage_simple_name, hardware_expressage_name, 
    product_ship_time, product_working_condition, software_info, software_get_way, app_mini_program_info, 
    app_mini_program_get_way, installation_services, offline_reason, creator, request_offline_user_id, 
    create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineComboInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from new_product_request_online_offline_combo_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from new_product_request_online_offline_combo_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from new_product_request_online_offline_combo_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineComboInfoExample">
    delete from new_product_request_online_offline_combo_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineComboInfo">
    insert into new_product_request_online_offline_combo_info (id, new_product_request_id, request_no, 
      request_pass, things_card_combo, hardware_shipping_list, 
      product_param, product_ship_province, product_ship_province_name, 
      product_ship_city, product_ship_city_name, product_ship_address, 
      hardware_expressage_simple_name, hardware_expressage_name, 
      product_ship_time, product_working_condition, 
      software_info, software_get_way, app_mini_program_info, 
      app_mini_program_get_way, installation_services, 
      offline_reason, creator, request_offline_user_id, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{newProductRequestId,jdbcType=VARCHAR}, #{requestNo,jdbcType=VARCHAR}, 
      #{requestPass,jdbcType=INTEGER}, #{thingsCardCombo,jdbcType=VARCHAR}, #{hardwareShippingList,jdbcType=VARCHAR}, 
      #{productParam,jdbcType=VARCHAR}, #{productShipProvince,jdbcType=VARCHAR}, #{productShipProvinceName,jdbcType=VARCHAR}, 
      #{productShipCity,jdbcType=VARCHAR}, #{productShipCityName,jdbcType=VARCHAR}, #{productShipAddress,jdbcType=VARCHAR}, 
      #{hardwareExpressageSimpleName,jdbcType=VARCHAR}, #{hardwareExpressageName,jdbcType=VARCHAR}, 
      #{productShipTime,jdbcType=VARCHAR}, #{productWorkingCondition,jdbcType=VARCHAR}, 
      #{softwareInfo,jdbcType=VARCHAR}, #{softwareGetWay,jdbcType=VARCHAR}, #{appMiniProgramInfo,jdbcType=VARCHAR}, 
      #{appMiniProgramGetWay,jdbcType=VARCHAR}, #{installationServices,jdbcType=VARCHAR}, 
      #{offlineReason,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{requestOfflineUserId,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineComboInfo">
    insert into new_product_request_online_offline_combo_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="newProductRequestId != null">
        new_product_request_id,
      </if>
      <if test="requestNo != null">
        request_no,
      </if>
      <if test="requestPass != null">
        request_pass,
      </if>
      <if test="thingsCardCombo != null">
        things_card_combo,
      </if>
      <if test="hardwareShippingList != null">
        hardware_shipping_list,
      </if>
      <if test="productParam != null">
        product_param,
      </if>
      <if test="productShipProvince != null">
        product_ship_province,
      </if>
      <if test="productShipProvinceName != null">
        product_ship_province_name,
      </if>
      <if test="productShipCity != null">
        product_ship_city,
      </if>
      <if test="productShipCityName != null">
        product_ship_city_name,
      </if>
      <if test="productShipAddress != null">
        product_ship_address,
      </if>
      <if test="hardwareExpressageSimpleName != null">
        hardware_expressage_simple_name,
      </if>
      <if test="hardwareExpressageName != null">
        hardware_expressage_name,
      </if>
      <if test="productShipTime != null">
        product_ship_time,
      </if>
      <if test="productWorkingCondition != null">
        product_working_condition,
      </if>
      <if test="softwareInfo != null">
        software_info,
      </if>
      <if test="softwareGetWay != null">
        software_get_way,
      </if>
      <if test="appMiniProgramInfo != null">
        app_mini_program_info,
      </if>
      <if test="appMiniProgramGetWay != null">
        app_mini_program_get_way,
      </if>
      <if test="installationServices != null">
        installation_services,
      </if>
      <if test="offlineReason != null">
        offline_reason,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="requestOfflineUserId != null">
        request_offline_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="newProductRequestId != null">
        #{newProductRequestId,jdbcType=VARCHAR},
      </if>
      <if test="requestNo != null">
        #{requestNo,jdbcType=VARCHAR},
      </if>
      <if test="requestPass != null">
        #{requestPass,jdbcType=INTEGER},
      </if>
      <if test="thingsCardCombo != null">
        #{thingsCardCombo,jdbcType=VARCHAR},
      </if>
      <if test="hardwareShippingList != null">
        #{hardwareShippingList,jdbcType=VARCHAR},
      </if>
      <if test="productParam != null">
        #{productParam,jdbcType=VARCHAR},
      </if>
      <if test="productShipProvince != null">
        #{productShipProvince,jdbcType=VARCHAR},
      </if>
      <if test="productShipProvinceName != null">
        #{productShipProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="productShipCity != null">
        #{productShipCity,jdbcType=VARCHAR},
      </if>
      <if test="productShipCityName != null">
        #{productShipCityName,jdbcType=VARCHAR},
      </if>
      <if test="productShipAddress != null">
        #{productShipAddress,jdbcType=VARCHAR},
      </if>
      <if test="hardwareExpressageSimpleName != null">
        #{hardwareExpressageSimpleName,jdbcType=VARCHAR},
      </if>
      <if test="hardwareExpressageName != null">
        #{hardwareExpressageName,jdbcType=VARCHAR},
      </if>
      <if test="productShipTime != null">
        #{productShipTime,jdbcType=VARCHAR},
      </if>
      <if test="productWorkingCondition != null">
        #{productWorkingCondition,jdbcType=VARCHAR},
      </if>
      <if test="softwareInfo != null">
        #{softwareInfo,jdbcType=VARCHAR},
      </if>
      <if test="softwareGetWay != null">
        #{softwareGetWay,jdbcType=VARCHAR},
      </if>
      <if test="appMiniProgramInfo != null">
        #{appMiniProgramInfo,jdbcType=VARCHAR},
      </if>
      <if test="appMiniProgramGetWay != null">
        #{appMiniProgramGetWay,jdbcType=VARCHAR},
      </if>
      <if test="installationServices != null">
        #{installationServices,jdbcType=VARCHAR},
      </if>
      <if test="offlineReason != null">
        #{offlineReason,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="requestOfflineUserId != null">
        #{requestOfflineUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineComboInfoExample" resultType="java.lang.Long">
    select count(*) from new_product_request_online_offline_combo_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update new_product_request_online_offline_combo_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.newProductRequestId != null">
        new_product_request_id = #{record.newProductRequestId,jdbcType=VARCHAR},
      </if>
      <if test="record.requestNo != null">
        request_no = #{record.requestNo,jdbcType=VARCHAR},
      </if>
      <if test="record.requestPass != null">
        request_pass = #{record.requestPass,jdbcType=INTEGER},
      </if>
      <if test="record.thingsCardCombo != null">
        things_card_combo = #{record.thingsCardCombo,jdbcType=VARCHAR},
      </if>
      <if test="record.hardwareShippingList != null">
        hardware_shipping_list = #{record.hardwareShippingList,jdbcType=VARCHAR},
      </if>
      <if test="record.productParam != null">
        product_param = #{record.productParam,jdbcType=VARCHAR},
      </if>
      <if test="record.productShipProvince != null">
        product_ship_province = #{record.productShipProvince,jdbcType=VARCHAR},
      </if>
      <if test="record.productShipProvinceName != null">
        product_ship_province_name = #{record.productShipProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.productShipCity != null">
        product_ship_city = #{record.productShipCity,jdbcType=VARCHAR},
      </if>
      <if test="record.productShipCityName != null">
        product_ship_city_name = #{record.productShipCityName,jdbcType=VARCHAR},
      </if>
      <if test="record.productShipAddress != null">
        product_ship_address = #{record.productShipAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.hardwareExpressageSimpleName != null">
        hardware_expressage_simple_name = #{record.hardwareExpressageSimpleName,jdbcType=VARCHAR},
      </if>
      <if test="record.hardwareExpressageName != null">
        hardware_expressage_name = #{record.hardwareExpressageName,jdbcType=VARCHAR},
      </if>
      <if test="record.productShipTime != null">
        product_ship_time = #{record.productShipTime,jdbcType=VARCHAR},
      </if>
      <if test="record.productWorkingCondition != null">
        product_working_condition = #{record.productWorkingCondition,jdbcType=VARCHAR},
      </if>
      <if test="record.softwareInfo != null">
        software_info = #{record.softwareInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.softwareGetWay != null">
        software_get_way = #{record.softwareGetWay,jdbcType=VARCHAR},
      </if>
      <if test="record.appMiniProgramInfo != null">
        app_mini_program_info = #{record.appMiniProgramInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.appMiniProgramGetWay != null">
        app_mini_program_get_way = #{record.appMiniProgramGetWay,jdbcType=VARCHAR},
      </if>
      <if test="record.installationServices != null">
        installation_services = #{record.installationServices,jdbcType=VARCHAR},
      </if>
      <if test="record.offlineReason != null">
        offline_reason = #{record.offlineReason,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.requestOfflineUserId != null">
        request_offline_user_id = #{record.requestOfflineUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update new_product_request_online_offline_combo_info
    set id = #{record.id,jdbcType=VARCHAR},
      new_product_request_id = #{record.newProductRequestId,jdbcType=VARCHAR},
      request_no = #{record.requestNo,jdbcType=VARCHAR},
      request_pass = #{record.requestPass,jdbcType=INTEGER},
      things_card_combo = #{record.thingsCardCombo,jdbcType=VARCHAR},
      hardware_shipping_list = #{record.hardwareShippingList,jdbcType=VARCHAR},
      product_param = #{record.productParam,jdbcType=VARCHAR},
      product_ship_province = #{record.productShipProvince,jdbcType=VARCHAR},
      product_ship_province_name = #{record.productShipProvinceName,jdbcType=VARCHAR},
      product_ship_city = #{record.productShipCity,jdbcType=VARCHAR},
      product_ship_city_name = #{record.productShipCityName,jdbcType=VARCHAR},
      product_ship_address = #{record.productShipAddress,jdbcType=VARCHAR},
      hardware_expressage_simple_name = #{record.hardwareExpressageSimpleName,jdbcType=VARCHAR},
      hardware_expressage_name = #{record.hardwareExpressageName,jdbcType=VARCHAR},
      product_ship_time = #{record.productShipTime,jdbcType=VARCHAR},
      product_working_condition = #{record.productWorkingCondition,jdbcType=VARCHAR},
      software_info = #{record.softwareInfo,jdbcType=VARCHAR},
      software_get_way = #{record.softwareGetWay,jdbcType=VARCHAR},
      app_mini_program_info = #{record.appMiniProgramInfo,jdbcType=VARCHAR},
      app_mini_program_get_way = #{record.appMiniProgramGetWay,jdbcType=VARCHAR},
      installation_services = #{record.installationServices,jdbcType=VARCHAR},
      offline_reason = #{record.offlineReason,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      request_offline_user_id = #{record.requestOfflineUserId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineComboInfo">
    update new_product_request_online_offline_combo_info
    <set>
      <if test="newProductRequestId != null">
        new_product_request_id = #{newProductRequestId,jdbcType=VARCHAR},
      </if>
      <if test="requestNo != null">
        request_no = #{requestNo,jdbcType=VARCHAR},
      </if>
      <if test="requestPass != null">
        request_pass = #{requestPass,jdbcType=INTEGER},
      </if>
      <if test="thingsCardCombo != null">
        things_card_combo = #{thingsCardCombo,jdbcType=VARCHAR},
      </if>
      <if test="hardwareShippingList != null">
        hardware_shipping_list = #{hardwareShippingList,jdbcType=VARCHAR},
      </if>
      <if test="productParam != null">
        product_param = #{productParam,jdbcType=VARCHAR},
      </if>
      <if test="productShipProvince != null">
        product_ship_province = #{productShipProvince,jdbcType=VARCHAR},
      </if>
      <if test="productShipProvinceName != null">
        product_ship_province_name = #{productShipProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="productShipCity != null">
        product_ship_city = #{productShipCity,jdbcType=VARCHAR},
      </if>
      <if test="productShipCityName != null">
        product_ship_city_name = #{productShipCityName,jdbcType=VARCHAR},
      </if>
      <if test="productShipAddress != null">
        product_ship_address = #{productShipAddress,jdbcType=VARCHAR},
      </if>
      <if test="hardwareExpressageSimpleName != null">
        hardware_expressage_simple_name = #{hardwareExpressageSimpleName,jdbcType=VARCHAR},
      </if>
      <if test="hardwareExpressageName != null">
        hardware_expressage_name = #{hardwareExpressageName,jdbcType=VARCHAR},
      </if>
      <if test="productShipTime != null">
        product_ship_time = #{productShipTime,jdbcType=VARCHAR},
      </if>
      <if test="productWorkingCondition != null">
        product_working_condition = #{productWorkingCondition,jdbcType=VARCHAR},
      </if>
      <if test="softwareInfo != null">
        software_info = #{softwareInfo,jdbcType=VARCHAR},
      </if>
      <if test="softwareGetWay != null">
        software_get_way = #{softwareGetWay,jdbcType=VARCHAR},
      </if>
      <if test="appMiniProgramInfo != null">
        app_mini_program_info = #{appMiniProgramInfo,jdbcType=VARCHAR},
      </if>
      <if test="appMiniProgramGetWay != null">
        app_mini_program_get_way = #{appMiniProgramGetWay,jdbcType=VARCHAR},
      </if>
      <if test="installationServices != null">
        installation_services = #{installationServices,jdbcType=VARCHAR},
      </if>
      <if test="offlineReason != null">
        offline_reason = #{offlineReason,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="requestOfflineUserId != null">
        request_offline_user_id = #{requestOfflineUserId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineComboInfo">
    update new_product_request_online_offline_combo_info
    set new_product_request_id = #{newProductRequestId,jdbcType=VARCHAR},
      request_no = #{requestNo,jdbcType=VARCHAR},
      request_pass = #{requestPass,jdbcType=INTEGER},
      things_card_combo = #{thingsCardCombo,jdbcType=VARCHAR},
      hardware_shipping_list = #{hardwareShippingList,jdbcType=VARCHAR},
      product_param = #{productParam,jdbcType=VARCHAR},
      product_ship_province = #{productShipProvince,jdbcType=VARCHAR},
      product_ship_province_name = #{productShipProvinceName,jdbcType=VARCHAR},
      product_ship_city = #{productShipCity,jdbcType=VARCHAR},
      product_ship_city_name = #{productShipCityName,jdbcType=VARCHAR},
      product_ship_address = #{productShipAddress,jdbcType=VARCHAR},
      hardware_expressage_simple_name = #{hardwareExpressageSimpleName,jdbcType=VARCHAR},
      hardware_expressage_name = #{hardwareExpressageName,jdbcType=VARCHAR},
      product_ship_time = #{productShipTime,jdbcType=VARCHAR},
      product_working_condition = #{productWorkingCondition,jdbcType=VARCHAR},
      software_info = #{softwareInfo,jdbcType=VARCHAR},
      software_get_way = #{softwareGetWay,jdbcType=VARCHAR},
      app_mini_program_info = #{appMiniProgramInfo,jdbcType=VARCHAR},
      app_mini_program_get_way = #{appMiniProgramGetWay,jdbcType=VARCHAR},
      installation_services = #{installationServices,jdbcType=VARCHAR},
      offline_reason = #{offlineReason,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      request_offline_user_id = #{requestOfflineUserId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into new_product_request_online_offline_combo_info
    (id, new_product_request_id, request_no, request_pass, things_card_combo, hardware_shipping_list, 
      product_param, product_ship_province, product_ship_province_name, product_ship_city, 
      product_ship_city_name, product_ship_address, hardware_expressage_simple_name, 
      hardware_expressage_name, product_ship_time, product_working_condition, software_info, 
      software_get_way, app_mini_program_info, app_mini_program_get_way, installation_services, 
      offline_reason, creator, request_offline_user_id, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.newProductRequestId,jdbcType=VARCHAR}, #{item.requestNo,jdbcType=VARCHAR}, 
        #{item.requestPass,jdbcType=INTEGER}, #{item.thingsCardCombo,jdbcType=VARCHAR}, 
        #{item.hardwareShippingList,jdbcType=VARCHAR}, #{item.productParam,jdbcType=VARCHAR}, 
        #{item.productShipProvince,jdbcType=VARCHAR}, #{item.productShipProvinceName,jdbcType=VARCHAR}, 
        #{item.productShipCity,jdbcType=VARCHAR}, #{item.productShipCityName,jdbcType=VARCHAR}, 
        #{item.productShipAddress,jdbcType=VARCHAR}, #{item.hardwareExpressageSimpleName,jdbcType=VARCHAR}, 
        #{item.hardwareExpressageName,jdbcType=VARCHAR}, #{item.productShipTime,jdbcType=VARCHAR}, 
        #{item.productWorkingCondition,jdbcType=VARCHAR}, #{item.softwareInfo,jdbcType=VARCHAR}, 
        #{item.softwareGetWay,jdbcType=VARCHAR}, #{item.appMiniProgramInfo,jdbcType=VARCHAR}, 
        #{item.appMiniProgramGetWay,jdbcType=VARCHAR}, #{item.installationServices,jdbcType=VARCHAR}, 
        #{item.offlineReason,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.requestOfflineUserId,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into new_product_request_online_offline_combo_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'new_product_request_id'.toString() == column.value">
          #{item.newProductRequestId,jdbcType=VARCHAR}
        </if>
        <if test="'request_no'.toString() == column.value">
          #{item.requestNo,jdbcType=VARCHAR}
        </if>
        <if test="'request_pass'.toString() == column.value">
          #{item.requestPass,jdbcType=INTEGER}
        </if>
        <if test="'things_card_combo'.toString() == column.value">
          #{item.thingsCardCombo,jdbcType=VARCHAR}
        </if>
        <if test="'hardware_shipping_list'.toString() == column.value">
          #{item.hardwareShippingList,jdbcType=VARCHAR}
        </if>
        <if test="'product_param'.toString() == column.value">
          #{item.productParam,jdbcType=VARCHAR}
        </if>
        <if test="'product_ship_province'.toString() == column.value">
          #{item.productShipProvince,jdbcType=VARCHAR}
        </if>
        <if test="'product_ship_province_name'.toString() == column.value">
          #{item.productShipProvinceName,jdbcType=VARCHAR}
        </if>
        <if test="'product_ship_city'.toString() == column.value">
          #{item.productShipCity,jdbcType=VARCHAR}
        </if>
        <if test="'product_ship_city_name'.toString() == column.value">
          #{item.productShipCityName,jdbcType=VARCHAR}
        </if>
        <if test="'product_ship_address'.toString() == column.value">
          #{item.productShipAddress,jdbcType=VARCHAR}
        </if>
        <if test="'hardware_expressage_simple_name'.toString() == column.value">
          #{item.hardwareExpressageSimpleName,jdbcType=VARCHAR}
        </if>
        <if test="'hardware_expressage_name'.toString() == column.value">
          #{item.hardwareExpressageName,jdbcType=VARCHAR}
        </if>
        <if test="'product_ship_time'.toString() == column.value">
          #{item.productShipTime,jdbcType=VARCHAR}
        </if>
        <if test="'product_working_condition'.toString() == column.value">
          #{item.productWorkingCondition,jdbcType=VARCHAR}
        </if>
        <if test="'software_info'.toString() == column.value">
          #{item.softwareInfo,jdbcType=VARCHAR}
        </if>
        <if test="'software_get_way'.toString() == column.value">
          #{item.softwareGetWay,jdbcType=VARCHAR}
        </if>
        <if test="'app_mini_program_info'.toString() == column.value">
          #{item.appMiniProgramInfo,jdbcType=VARCHAR}
        </if>
        <if test="'app_mini_program_get_way'.toString() == column.value">
          #{item.appMiniProgramGetWay,jdbcType=VARCHAR}
        </if>
        <if test="'installation_services'.toString() == column.value">
          #{item.installationServices,jdbcType=VARCHAR}
        </if>
        <if test="'offline_reason'.toString() == column.value">
          #{item.offlineReason,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'request_offline_user_id'.toString() == column.value">
          #{item.requestOfflineUserId,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>