<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.InvoiceReverseRecordMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.InvoiceReverseRecord">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 11 09:28:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="atom_order_id" jdbcType="VARCHAR" property="atomOrderId" />
    <result column="order_seq" jdbcType="VARCHAR" property="orderSeq" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="oper_type" jdbcType="VARCHAR" property="operType" />
    <result column="customer_type" jdbcType="VARCHAR" property="customerType" />
    <result column="customer_number" jdbcType="VARCHAR" property="customerNumber" />
    <result column="order_price" jdbcType="BIGINT" property="orderPrice" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="error_desc" jdbcType="VARCHAR" property="errorDesc" />
    <result column="cooperator_id" jdbcType="VARCHAR" property="cooperatorId" />
    <result column="finish_cooperator_id" jdbcType="VARCHAR" property="finishCooperatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="reminder_count" jdbcType="INTEGER" property="reminderCount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 11 09:28:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 11 09:28:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 11 09:28:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, atom_order_id, order_seq, order_id, oper_type, customer_type, customer_number, 
    order_price, status, error_desc, cooperator_id, finish_cooperator_id, create_time, 
    update_time, reminder_count
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.InvoiceReverseRecordExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 11 09:28:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from invoice_reverse_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 11 09:28:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from invoice_reverse_record
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 11 09:28:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from invoice_reverse_record
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.InvoiceReverseRecordExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 11 09:28:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from invoice_reverse_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.InvoiceReverseRecord">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 11 09:28:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into invoice_reverse_record (id, atom_order_id, order_seq, 
      order_id, oper_type, customer_type, 
      customer_number, order_price, status, 
      error_desc, cooperator_id, finish_cooperator_id, 
      create_time, update_time, reminder_count
      )
    values (#{id,jdbcType=VARCHAR}, #{atomOrderId,jdbcType=VARCHAR}, #{orderSeq,jdbcType=VARCHAR}, 
      #{orderId,jdbcType=VARCHAR}, #{operType,jdbcType=VARCHAR}, #{customerType,jdbcType=VARCHAR}, 
      #{customerNumber,jdbcType=VARCHAR}, #{orderPrice,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, 
      #{errorDesc,jdbcType=VARCHAR}, #{cooperatorId,jdbcType=VARCHAR}, #{finishCooperatorId,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{reminderCount,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.InvoiceReverseRecord">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 11 09:28:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into invoice_reverse_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="atomOrderId != null">
        atom_order_id,
      </if>
      <if test="orderSeq != null">
        order_seq,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="operType != null">
        oper_type,
      </if>
      <if test="customerType != null">
        customer_type,
      </if>
      <if test="customerNumber != null">
        customer_number,
      </if>
      <if test="orderPrice != null">
        order_price,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="errorDesc != null">
        error_desc,
      </if>
      <if test="cooperatorId != null">
        cooperator_id,
      </if>
      <if test="finishCooperatorId != null">
        finish_cooperator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="reminderCount != null">
        reminder_count,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="atomOrderId != null">
        #{atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderSeq != null">
        #{orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="operType != null">
        #{operType,jdbcType=VARCHAR},
      </if>
      <if test="customerType != null">
        #{customerType,jdbcType=VARCHAR},
      </if>
      <if test="customerNumber != null">
        #{customerNumber,jdbcType=VARCHAR},
      </if>
      <if test="orderPrice != null">
        #{orderPrice,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="errorDesc != null">
        #{errorDesc,jdbcType=VARCHAR},
      </if>
      <if test="cooperatorId != null">
        #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="finishCooperatorId != null">
        #{finishCooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reminderCount != null">
        #{reminderCount,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.InvoiceReverseRecordExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 11 09:28:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from invoice_reverse_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 11 09:28:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    update invoice_reverse_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOrderId != null">
        atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderSeq != null">
        order_seq = #{record.orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.operType != null">
        oper_type = #{record.operType,jdbcType=VARCHAR},
      </if>
      <if test="record.customerType != null">
        customer_type = #{record.customerType,jdbcType=VARCHAR},
      </if>
      <if test="record.customerNumber != null">
        customer_number = #{record.customerNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.orderPrice != null">
        order_price = #{record.orderPrice,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.errorDesc != null">
        error_desc = #{record.errorDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.cooperatorId != null">
        cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.finishCooperatorId != null">
        finish_cooperator_id = #{record.finishCooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reminderCount != null">
        reminder_count = #{record.reminderCount,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 11 09:28:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    update invoice_reverse_record
    set id = #{record.id,jdbcType=VARCHAR},
      atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      order_seq = #{record.orderSeq,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      oper_type = #{record.operType,jdbcType=VARCHAR},
      customer_type = #{record.customerType,jdbcType=VARCHAR},
      customer_number = #{record.customerNumber,jdbcType=VARCHAR},
      order_price = #{record.orderPrice,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER},
      error_desc = #{record.errorDesc,jdbcType=VARCHAR},
      cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      finish_cooperator_id = #{record.finishCooperatorId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      reminder_count = #{record.reminderCount,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.InvoiceReverseRecord">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 11 09:28:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    update invoice_reverse_record
    <set>
      <if test="atomOrderId != null">
        atom_order_id = #{atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderSeq != null">
        order_seq = #{orderSeq,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="operType != null">
        oper_type = #{operType,jdbcType=VARCHAR},
      </if>
      <if test="customerType != null">
        customer_type = #{customerType,jdbcType=VARCHAR},
      </if>
      <if test="customerNumber != null">
        customer_number = #{customerNumber,jdbcType=VARCHAR},
      </if>
      <if test="orderPrice != null">
        order_price = #{orderPrice,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="errorDesc != null">
        error_desc = #{errorDesc,jdbcType=VARCHAR},
      </if>
      <if test="cooperatorId != null">
        cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="finishCooperatorId != null">
        finish_cooperator_id = #{finishCooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reminderCount != null">
        reminder_count = #{reminderCount,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.InvoiceReverseRecord">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 11 09:28:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    update invoice_reverse_record
    set atom_order_id = #{atomOrderId,jdbcType=VARCHAR},
      order_seq = #{orderSeq,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      oper_type = #{operType,jdbcType=VARCHAR},
      customer_type = #{customerType,jdbcType=VARCHAR},
      customer_number = #{customerNumber,jdbcType=VARCHAR},
      order_price = #{orderPrice,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      error_desc = #{errorDesc,jdbcType=VARCHAR},
      cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      finish_cooperator_id = #{finishCooperatorId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      reminder_count = #{reminderCount,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 11 09:28:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into invoice_reverse_record
    (id, atom_order_id, order_seq, order_id, oper_type, customer_type, customer_number, 
      order_price, status, error_desc, cooperator_id, finish_cooperator_id, create_time, 
      update_time, reminder_count)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.atomOrderId,jdbcType=VARCHAR}, #{item.orderSeq,jdbcType=VARCHAR}, 
        #{item.orderId,jdbcType=VARCHAR}, #{item.operType,jdbcType=VARCHAR}, #{item.customerType,jdbcType=VARCHAR}, 
        #{item.customerNumber,jdbcType=VARCHAR}, #{item.orderPrice,jdbcType=BIGINT}, #{item.status,jdbcType=INTEGER}, 
        #{item.errorDesc,jdbcType=VARCHAR}, #{item.cooperatorId,jdbcType=VARCHAR}, #{item.finishCooperatorId,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.reminderCount,jdbcType=INTEGER}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 11 09:28:18 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into invoice_reverse_record (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'atom_order_id'.toString() == column.value">
          #{item.atomOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'order_seq'.toString() == column.value">
          #{item.orderSeq,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'oper_type'.toString() == column.value">
          #{item.operType,jdbcType=VARCHAR}
        </if>
        <if test="'customer_type'.toString() == column.value">
          #{item.customerType,jdbcType=VARCHAR}
        </if>
        <if test="'customer_number'.toString() == column.value">
          #{item.customerNumber,jdbcType=VARCHAR}
        </if>
        <if test="'order_price'.toString() == column.value">
          #{item.orderPrice,jdbcType=BIGINT}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'error_desc'.toString() == column.value">
          #{item.errorDesc,jdbcType=VARCHAR}
        </if>
        <if test="'cooperator_id'.toString() == column.value">
          #{item.cooperatorId,jdbcType=VARCHAR}
        </if>
        <if test="'finish_cooperator_id'.toString() == column.value">
          #{item.finishCooperatorId,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'reminder_count'.toString() == column.value">
          #{item.reminderCount,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>