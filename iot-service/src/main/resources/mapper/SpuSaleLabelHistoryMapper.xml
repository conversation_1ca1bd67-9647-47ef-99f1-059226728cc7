<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.SpuSaleLabelHistoryMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.SpuSaleLabelHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 26 17:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="spu_code" jdbcType="VARCHAR" property="spuCode" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="spu_offering_version" jdbcType="VARCHAR" property="spuOfferingVersion" />
    <result column="spu_id" jdbcType="VARCHAR" property="spuId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 26 17:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 26 17:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 26 17:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, spu_code, label, type, create_time, update_time, spu_offering_version, spu_id
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.SpuSaleLabelHistoryExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 26 17:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from spu_sale_label_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 26 17:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from spu_sale_label_history
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 26 17:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from spu_sale_label_history
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.SpuSaleLabelHistoryExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 26 17:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from spu_sale_label_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.SpuSaleLabelHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 26 17:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into spu_sale_label_history (id, spu_code, label, 
      type, create_time, update_time, 
      spu_offering_version, spu_id)
    values (#{id,jdbcType=VARCHAR}, #{spuCode,jdbcType=VARCHAR}, #{label,jdbcType=VARCHAR}, 
      #{type,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{spuOfferingVersion,jdbcType=VARCHAR}, #{spuId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.SpuSaleLabelHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 26 17:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into spu_sale_label_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="label != null">
        label,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="spuOfferingVersion != null">
        spu_offering_version,
      </if>
      <if test="spuId != null">
        spu_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="spuOfferingVersion != null">
        #{spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="spuId != null">
        #{spuId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.SpuSaleLabelHistoryExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 26 17:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from spu_sale_label_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 26 17:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    update spu_sale_label_history
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.spuCode != null">
        spu_code = #{record.spuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.label != null">
        label = #{record.label,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.spuOfferingVersion != null">
        spu_offering_version = #{record.spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.spuId != null">
        spu_id = #{record.spuId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 26 17:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    update spu_sale_label_history
    set id = #{record.id,jdbcType=VARCHAR},
      spu_code = #{record.spuCode,jdbcType=VARCHAR},
      label = #{record.label,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      spu_offering_version = #{record.spuOfferingVersion,jdbcType=VARCHAR},
      spu_id = #{record.spuId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.SpuSaleLabelHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 26 17:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    update spu_sale_label_history
    <set>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="label != null">
        label = #{label,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="spuOfferingVersion != null">
        spu_offering_version = #{spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="spuId != null">
        spu_id = #{spuId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.SpuSaleLabelHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 26 17:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    update spu_sale_label_history
    set spu_code = #{spuCode,jdbcType=VARCHAR},
      label = #{label,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      spu_offering_version = #{spuOfferingVersion,jdbcType=VARCHAR},
      spu_id = #{spuId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 26 17:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into spu_sale_label_history
    (id, spu_code, label, type, create_time, update_time, spu_offering_version, spu_id
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.spuCode,jdbcType=VARCHAR}, #{item.label,jdbcType=VARCHAR}, 
        #{item.type,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.spuOfferingVersion,jdbcType=VARCHAR}, #{item.spuId,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Feb 26 17:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into spu_sale_label_history (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'spu_code'.toString() == column.value">
          #{item.spuCode,jdbcType=VARCHAR}
        </if>
        <if test="'label'.toString() == column.value">
          #{item.label,jdbcType=VARCHAR}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'spu_offering_version'.toString() == column.value">
          #{item.spuOfferingVersion,jdbcType=VARCHAR}
        </if>
        <if test="'spu_id'.toString() == column.value">
          #{item.spuId,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>