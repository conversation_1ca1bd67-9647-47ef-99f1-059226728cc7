<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.YuntongPackageMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.YuntongPackage">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jan 14 16:56:07 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="atom_code" jdbcType="VARCHAR" property="atomCode" />
    <result column="atom_name" jdbcType="VARCHAR" property="atomName" />
    <result column="package_name" jdbcType="VARCHAR" property="packageName" />
    <result column="package_number" jdbcType="VARCHAR" property="packageNumber" />
    <result column="storage_type" jdbcType="INTEGER" property="storageType" />
    <result column="storage_days" jdbcType="INTEGER" property="storageDays" />
    <result column="package_property" jdbcType="INTEGER" property="packageProperty" />
    <result column="package_cost" jdbcType="INTEGER" property="packageCost" />
    <result column="service_months" jdbcType="INTEGER" property="serviceMonths" />
    <result column="package_type" jdbcType="INTEGER" property="packageType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jan 14 16:56:07 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jan 14 16:56:07 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jan 14 16:56:07 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, sku_code, atom_code, atom_name, package_name, package_number, storage_type, storage_days, 
    package_property, package_cost, service_months, package_type, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.YuntongPackageExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jan 14 16:56:07 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from yuntong_package
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jan 14 16:56:07 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from yuntong_package
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jan 14 16:56:07 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from yuntong_package
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.YuntongPackageExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jan 14 16:56:07 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from yuntong_package
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.YuntongPackage">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jan 14 16:56:07 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into yuntong_package (id, sku_code, atom_code, 
      atom_name, package_name, package_number, 
      storage_type, storage_days, package_property, 
      package_cost, service_months, package_type, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{skuCode,jdbcType=VARCHAR}, #{atomCode,jdbcType=VARCHAR}, 
      #{atomName,jdbcType=VARCHAR}, #{packageName,jdbcType=VARCHAR}, #{packageNumber,jdbcType=VARCHAR}, 
      #{storageType,jdbcType=INTEGER}, #{storageDays,jdbcType=INTEGER}, #{packageProperty,jdbcType=INTEGER}, 
      #{packageCost,jdbcType=INTEGER}, #{serviceMonths,jdbcType=INTEGER}, #{packageType,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.YuntongPackage">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jan 14 16:56:07 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into yuntong_package
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="skuCode != null">
        sku_code,
      </if>
      <if test="atomCode != null">
        atom_code,
      </if>
      <if test="atomName != null">
        atom_name,
      </if>
      <if test="packageName != null">
        package_name,
      </if>
      <if test="packageNumber != null">
        package_number,
      </if>
      <if test="storageType != null">
        storage_type,
      </if>
      <if test="storageDays != null">
        storage_days,
      </if>
      <if test="packageProperty != null">
        package_property,
      </if>
      <if test="packageCost != null">
        package_cost,
      </if>
      <if test="serviceMonths != null">
        service_months,
      </if>
      <if test="packageType != null">
        package_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="atomCode != null">
        #{atomCode,jdbcType=VARCHAR},
      </if>
      <if test="atomName != null">
        #{atomName,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null">
        #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="packageNumber != null">
        #{packageNumber,jdbcType=VARCHAR},
      </if>
      <if test="storageType != null">
        #{storageType,jdbcType=INTEGER},
      </if>
      <if test="storageDays != null">
        #{storageDays,jdbcType=INTEGER},
      </if>
      <if test="packageProperty != null">
        #{packageProperty,jdbcType=INTEGER},
      </if>
      <if test="packageCost != null">
        #{packageCost,jdbcType=INTEGER},
      </if>
      <if test="serviceMonths != null">
        #{serviceMonths,jdbcType=INTEGER},
      </if>
      <if test="packageType != null">
        #{packageType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.YuntongPackageExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jan 14 16:56:07 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from yuntong_package
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jan 14 16:56:07 CST 2025. by MyBatis Generator, do not modify.
    -->
    update yuntong_package
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCode != null">
        sku_code = #{record.skuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.atomCode != null">
        atom_code = #{record.atomCode,jdbcType=VARCHAR},
      </if>
      <if test="record.atomName != null">
        atom_name = #{record.atomName,jdbcType=VARCHAR},
      </if>
      <if test="record.packageName != null">
        package_name = #{record.packageName,jdbcType=VARCHAR},
      </if>
      <if test="record.packageNumber != null">
        package_number = #{record.packageNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.storageType != null">
        storage_type = #{record.storageType,jdbcType=INTEGER},
      </if>
      <if test="record.storageDays != null">
        storage_days = #{record.storageDays,jdbcType=INTEGER},
      </if>
      <if test="record.packageProperty != null">
        package_property = #{record.packageProperty,jdbcType=INTEGER},
      </if>
      <if test="record.packageCost != null">
        package_cost = #{record.packageCost,jdbcType=INTEGER},
      </if>
      <if test="record.serviceMonths != null">
        service_months = #{record.serviceMonths,jdbcType=INTEGER},
      </if>
      <if test="record.packageType != null">
        package_type = #{record.packageType,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jan 14 16:56:07 CST 2025. by MyBatis Generator, do not modify.
    -->
    update yuntong_package
    set id = #{record.id,jdbcType=VARCHAR},
      sku_code = #{record.skuCode,jdbcType=VARCHAR},
      atom_code = #{record.atomCode,jdbcType=VARCHAR},
      atom_name = #{record.atomName,jdbcType=VARCHAR},
      package_name = #{record.packageName,jdbcType=VARCHAR},
      package_number = #{record.packageNumber,jdbcType=VARCHAR},
      storage_type = #{record.storageType,jdbcType=INTEGER},
      storage_days = #{record.storageDays,jdbcType=INTEGER},
      package_property = #{record.packageProperty,jdbcType=INTEGER},
      package_cost = #{record.packageCost,jdbcType=INTEGER},
      service_months = #{record.serviceMonths,jdbcType=INTEGER},
      package_type = #{record.packageType,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.YuntongPackage">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jan 14 16:56:07 CST 2025. by MyBatis Generator, do not modify.
    -->
    update yuntong_package
    <set>
      <if test="skuCode != null">
        sku_code = #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="atomCode != null">
        atom_code = #{atomCode,jdbcType=VARCHAR},
      </if>
      <if test="atomName != null">
        atom_name = #{atomName,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null">
        package_name = #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="packageNumber != null">
        package_number = #{packageNumber,jdbcType=VARCHAR},
      </if>
      <if test="storageType != null">
        storage_type = #{storageType,jdbcType=INTEGER},
      </if>
      <if test="storageDays != null">
        storage_days = #{storageDays,jdbcType=INTEGER},
      </if>
      <if test="packageProperty != null">
        package_property = #{packageProperty,jdbcType=INTEGER},
      </if>
      <if test="packageCost != null">
        package_cost = #{packageCost,jdbcType=INTEGER},
      </if>
      <if test="serviceMonths != null">
        service_months = #{serviceMonths,jdbcType=INTEGER},
      </if>
      <if test="packageType != null">
        package_type = #{packageType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.YuntongPackage">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jan 14 16:56:07 CST 2025. by MyBatis Generator, do not modify.
    -->
    update yuntong_package
    set sku_code = #{skuCode,jdbcType=VARCHAR},
      atom_code = #{atomCode,jdbcType=VARCHAR},
      atom_name = #{atomName,jdbcType=VARCHAR},
      package_name = #{packageName,jdbcType=VARCHAR},
      package_number = #{packageNumber,jdbcType=VARCHAR},
      storage_type = #{storageType,jdbcType=INTEGER},
      storage_days = #{storageDays,jdbcType=INTEGER},
      package_property = #{packageProperty,jdbcType=INTEGER},
      package_cost = #{packageCost,jdbcType=INTEGER},
      service_months = #{serviceMonths,jdbcType=INTEGER},
      package_type = #{packageType,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jan 14 16:56:07 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into yuntong_package
    (id, sku_code, atom_code, atom_name, package_name, package_number, storage_type, 
      storage_days, package_property, package_cost, service_months, package_type, create_time, 
      update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.skuCode,jdbcType=VARCHAR}, #{item.atomCode,jdbcType=VARCHAR}, 
        #{item.atomName,jdbcType=VARCHAR}, #{item.packageName,jdbcType=VARCHAR}, #{item.packageNumber,jdbcType=VARCHAR}, 
        #{item.storageType,jdbcType=INTEGER}, #{item.storageDays,jdbcType=INTEGER}, #{item.packageProperty,jdbcType=INTEGER}, 
        #{item.packageCost,jdbcType=INTEGER}, #{item.serviceMonths,jdbcType=INTEGER}, #{item.packageType,jdbcType=INTEGER}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jan 14 16:56:07 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into yuntong_package (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'sku_code'.toString() == column.value">
          #{item.skuCode,jdbcType=VARCHAR}
        </if>
        <if test="'atom_code'.toString() == column.value">
          #{item.atomCode,jdbcType=VARCHAR}
        </if>
        <if test="'atom_name'.toString() == column.value">
          #{item.atomName,jdbcType=VARCHAR}
        </if>
        <if test="'package_name'.toString() == column.value">
          #{item.packageName,jdbcType=VARCHAR}
        </if>
        <if test="'package_number'.toString() == column.value">
          #{item.packageNumber,jdbcType=VARCHAR}
        </if>
        <if test="'storage_type'.toString() == column.value">
          #{item.storageType,jdbcType=INTEGER}
        </if>
        <if test="'storage_days'.toString() == column.value">
          #{item.storageDays,jdbcType=INTEGER}
        </if>
        <if test="'package_property'.toString() == column.value">
          #{item.packageProperty,jdbcType=INTEGER}
        </if>
        <if test="'package_cost'.toString() == column.value">
          #{item.packageCost,jdbcType=INTEGER}
        </if>
        <if test="'service_months'.toString() == column.value">
          #{item.serviceMonths,jdbcType=INTEGER}
        </if>
        <if test="'package_type'.toString() == column.value">
          #{item.packageType,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>