<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.MessageMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.Message">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Apr 08 18:00:23 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="module" jdbcType="VARCHAR" property="module" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="file_key" jdbcType="VARCHAR" property="fileKey" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="isRead" jdbcType="BIT" property="isread" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Apr 08 18:00:23 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Apr 08 18:00:23 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Apr 08 18:00:23 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, module, type, content, url, file_key, user_id, isRead, source, create_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.MessageExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Apr 08 18:00:23 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Apr 08 18:00:23 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from message
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Apr 08 18:00:23 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from message
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.MessageExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Apr 08 18:00:23 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.Message">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Apr 08 18:00:23 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into message (id, module, type, 
      content, url, file_key, 
      user_id, isRead, source, 
      create_time)
    values (#{id,jdbcType=VARCHAR}, #{module,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{content,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, #{fileKey,jdbcType=VARCHAR}, 
      #{userId,jdbcType=VARCHAR}, #{isread,jdbcType=BIT}, #{source,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.Message">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Apr 08 18:00:23 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into message
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="module != null">
        module,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="fileKey != null">
        file_key,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="isread != null">
        isRead,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="module != null">
        #{module,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="fileKey != null">
        #{fileKey,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="isread != null">
        #{isread,jdbcType=BIT},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.MessageExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Apr 08 18:00:23 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Apr 08 18:00:23 CST 2024. by MyBatis Generator, do not modify.
    -->
    update message
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.module != null">
        module = #{record.module,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.url != null">
        url = #{record.url,jdbcType=VARCHAR},
      </if>
      <if test="record.fileKey != null">
        file_key = #{record.fileKey,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.isread != null">
        isRead = #{record.isread,jdbcType=BIT},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Apr 08 18:00:23 CST 2024. by MyBatis Generator, do not modify.
    -->
    update message
    set id = #{record.id,jdbcType=VARCHAR},
      module = #{record.module,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      url = #{record.url,jdbcType=VARCHAR},
      file_key = #{record.fileKey,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      isRead = #{record.isread,jdbcType=BIT},
      source = #{record.source,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.Message">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Apr 08 18:00:23 CST 2024. by MyBatis Generator, do not modify.
    -->
    update message
    <set>
      <if test="module != null">
        module = #{module,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="fileKey != null">
        file_key = #{fileKey,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="isread != null">
        isRead = #{isread,jdbcType=BIT},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.Message">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Apr 08 18:00:23 CST 2024. by MyBatis Generator, do not modify.
    -->
    update message
    set module = #{module,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      file_key = #{fileKey,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      isRead = #{isread,jdbcType=BIT},
      source = #{source,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Apr 08 18:00:23 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into message
    (id, module, type, content, url, file_key, user_id, isRead, source, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.module,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR}, 
        #{item.content,jdbcType=VARCHAR}, #{item.url,jdbcType=VARCHAR}, #{item.fileKey,jdbcType=VARCHAR}, 
        #{item.userId,jdbcType=VARCHAR}, #{item.isread,jdbcType=BIT}, #{item.source,jdbcType=INTEGER}, 
        #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Apr 08 18:00:23 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into message (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'module'.toString() == column.value">
          #{item.module,jdbcType=VARCHAR}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=VARCHAR}
        </if>
        <if test="'content'.toString() == column.value">
          #{item.content,jdbcType=VARCHAR}
        </if>
        <if test="'url'.toString() == column.value">
          #{item.url,jdbcType=VARCHAR}
        </if>
        <if test="'file_key'.toString() == column.value">
          #{item.fileKey,jdbcType=VARCHAR}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
        <if test="'isRead'.toString() == column.value">
          #{item.isread,jdbcType=BIT}
        </if>
        <if test="'source'.toString() == column.value">
          #{item.source,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>