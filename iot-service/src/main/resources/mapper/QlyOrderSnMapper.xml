<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.QlyOrderSnMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.QlyOrderSn">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 29 15:54:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="qly_status" jdbcType="INTEGER" property="qlyStatus" />
    <result column="turn_on_code" jdbcType="VARCHAR" property="turnOnCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 29 15:54:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 29 15:54:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 29 15:54:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    order_id, sn, qly_status, turn_on_code
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.QlyOrderSnExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 29 15:54:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from qly_order_sn
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.QlyOrderSnExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 29 15:54:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from qly_order_sn
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.QlyOrderSn">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 29 15:54:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into qly_order_sn (order_id, sn, qly_status, 
      turn_on_code)
    values (#{orderId,jdbcType=VARCHAR}, #{sn,jdbcType=VARCHAR}, #{qlyStatus,jdbcType=INTEGER}, 
      #{turnOnCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.QlyOrderSn">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 29 15:54:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into qly_order_sn
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="sn != null">
        sn,
      </if>
      <if test="qlyStatus != null">
        qly_status,
      </if>
      <if test="turnOnCode != null">
        turn_on_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
      <if test="qlyStatus != null">
        #{qlyStatus,jdbcType=INTEGER},
      </if>
      <if test="turnOnCode != null">
        #{turnOnCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.QlyOrderSnExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 29 15:54:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    select count(*) from qly_order_sn
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 29 15:54:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    update qly_order_sn
    <set>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.sn != null">
        sn = #{record.sn,jdbcType=VARCHAR},
      </if>
      <if test="record.qlyStatus != null">
        qly_status = #{record.qlyStatus,jdbcType=INTEGER},
      </if>
      <if test="record.turnOnCode != null">
        turn_on_code = #{record.turnOnCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 29 15:54:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    update qly_order_sn
    set order_id = #{record.orderId,jdbcType=VARCHAR},
      sn = #{record.sn,jdbcType=VARCHAR},
      qly_status = #{record.qlyStatus,jdbcType=INTEGER},
      turn_on_code = #{record.turnOnCode,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 29 15:54:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into qly_order_sn
    (order_id, sn, qly_status, turn_on_code)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=VARCHAR}, #{item.sn,jdbcType=VARCHAR}, #{item.qlyStatus,jdbcType=INTEGER}, 
        #{item.turnOnCode,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Aug 29 15:54:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into qly_order_sn (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'sn'.toString() == column.value">
          #{item.sn,jdbcType=VARCHAR}
        </if>
        <if test="'qly_status'.toString() == column.value">
          #{item.qlyStatus,jdbcType=INTEGER}
        </if>
        <if test="'turn_on_code'.toString() == column.value">
          #{item.turnOnCode,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>