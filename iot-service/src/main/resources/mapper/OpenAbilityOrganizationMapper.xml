<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.OpenAbilityOrganizationMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.OpenAbilityOrganization">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 05 16:58:33 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="api_key" jdbcType="VARCHAR" property="apiKey" />
    <result column="secret" jdbcType="VARCHAR" property="secret" />
    <result column="enable" jdbcType="BIT" property="enable" />
    <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 05 16:58:33 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 05 16:58:33 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 05 16:58:33 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, name, description, contact_phone, contact_name, api_key, secret, enable, delete_time, 
    create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityOrganizationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 05 16:58:33 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from open_ability_organization
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 05 16:58:33 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from open_ability_organization
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 05 16:58:33 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from open_ability_organization
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityOrganizationExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 05 16:58:33 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from open_ability_organization
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityOrganization">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 05 16:58:33 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into open_ability_organization (id, name, description, 
      contact_phone, contact_name, api_key, 
      secret, enable, delete_time, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{contactPhone,jdbcType=VARCHAR}, #{contactName,jdbcType=VARCHAR}, #{apiKey,jdbcType=VARCHAR}, 
      #{secret,jdbcType=VARCHAR}, #{enable,jdbcType=BIT}, #{deleteTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityOrganization">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 05 16:58:33 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into open_ability_organization
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="contactPhone != null">
        contact_phone,
      </if>
      <if test="contactName != null">
        contact_name,
      </if>
      <if test="apiKey != null">
        api_key,
      </if>
      <if test="secret != null">
        secret,
      </if>
      <if test="enable != null">
        enable,
      </if>
      <if test="deleteTime != null">
        delete_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null">
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="apiKey != null">
        #{apiKey,jdbcType=VARCHAR},
      </if>
      <if test="secret != null">
        #{secret,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        #{enable,jdbcType=BIT},
      </if>
      <if test="deleteTime != null">
        #{deleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityOrganizationExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 05 16:58:33 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from open_ability_organization
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 05 16:58:33 CST 2024. by MyBatis Generator, do not modify.
    -->
    update open_ability_organization
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPhone != null">
        contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.contactName != null">
        contact_name = #{record.contactName,jdbcType=VARCHAR},
      </if>
      <if test="record.apiKey != null">
        api_key = #{record.apiKey,jdbcType=VARCHAR},
      </if>
      <if test="record.secret != null">
        secret = #{record.secret,jdbcType=VARCHAR},
      </if>
      <if test="record.enable != null">
        enable = #{record.enable,jdbcType=BIT},
      </if>
      <if test="record.deleteTime != null">
        delete_time = #{record.deleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 05 16:58:33 CST 2024. by MyBatis Generator, do not modify.
    -->
    update open_ability_organization
    set id = #{record.id,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      contact_name = #{record.contactName,jdbcType=VARCHAR},
      api_key = #{record.apiKey,jdbcType=VARCHAR},
      secret = #{record.secret,jdbcType=VARCHAR},
      enable = #{record.enable,jdbcType=BIT},
      delete_time = #{record.deleteTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityOrganization">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 05 16:58:33 CST 2024. by MyBatis Generator, do not modify.
    -->
    update open_ability_organization
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null">
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="apiKey != null">
        api_key = #{apiKey,jdbcType=VARCHAR},
      </if>
      <if test="secret != null">
        secret = #{secret,jdbcType=VARCHAR},
      </if>
      <if test="enable != null">
        enable = #{enable,jdbcType=BIT},
      </if>
      <if test="deleteTime != null">
        delete_time = #{deleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityOrganization">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 05 16:58:33 CST 2024. by MyBatis Generator, do not modify.
    -->
    update open_ability_organization
    set name = #{name,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      contact_phone = #{contactPhone,jdbcType=VARCHAR},
      contact_name = #{contactName,jdbcType=VARCHAR},
      api_key = #{apiKey,jdbcType=VARCHAR},
      secret = #{secret,jdbcType=VARCHAR},
      enable = #{enable,jdbcType=BIT},
      delete_time = #{deleteTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 05 16:58:33 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into open_ability_organization
    (id, name, description, contact_phone, contact_name, api_key, secret, enable, delete_time, 
      create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR}, 
        #{item.contactPhone,jdbcType=VARCHAR}, #{item.contactName,jdbcType=VARCHAR}, #{item.apiKey,jdbcType=VARCHAR}, 
        #{item.secret,jdbcType=VARCHAR}, #{item.enable,jdbcType=BIT}, #{item.deleteTime,jdbcType=TIMESTAMP}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jun 05 16:58:33 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into open_ability_organization (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'description'.toString() == column.value">
          #{item.description,jdbcType=VARCHAR}
        </if>
        <if test="'contact_phone'.toString() == column.value">
          #{item.contactPhone,jdbcType=VARCHAR}
        </if>
        <if test="'contact_name'.toString() == column.value">
          #{item.contactName,jdbcType=VARCHAR}
        </if>
        <if test="'api_key'.toString() == column.value">
          #{item.apiKey,jdbcType=VARCHAR}
        </if>
        <if test="'secret'.toString() == column.value">
          #{item.secret,jdbcType=VARCHAR}
        </if>
        <if test="'enable'.toString() == column.value">
          #{item.enable,jdbcType=BIT}
        </if>
        <if test="'delete_time'.toString() == column.value">
          #{item.deleteTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>