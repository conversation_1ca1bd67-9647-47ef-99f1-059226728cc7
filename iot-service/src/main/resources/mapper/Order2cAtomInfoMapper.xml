<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.Order2cAtomInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.Order2cAtomInfo">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="spu_offering_code" jdbcType="VARCHAR" property="spuOfferingCode" />
    <result column="sku_offering_code" jdbcType="VARCHAR" property="skuOfferingCode" />
    <result column="sku_offering_name" jdbcType="VARCHAR" property="skuOfferingName" />
    <result column="sku_quantity" jdbcType="BIGINT" property="skuQuantity" />
    <result column="sku_price" jdbcType="BIGINT" property="skuPrice" />
    <result column="market_name" jdbcType="VARCHAR" property="marketName" />
    <result column="market_code" jdbcType="VARCHAR" property="marketCode" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="color" jdbcType="VARCHAR" property="color" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="atom_offering_class" jdbcType="VARCHAR" property="atomOfferingClass" />
    <result column="atom_offering_code" jdbcType="VARCHAR" property="atomOfferingCode" />
    <result column="atom_offering_name" jdbcType="VARCHAR" property="atomOfferingName" />
    <result column="deduct_price" jdbcType="VARCHAR" property="deductPrice" />
    <result column="atom_price" jdbcType="BIGINT" property="atomPrice" />
    <result column="atom_settle_price" jdbcType="BIGINT" property="atomSettlePrice" />
    <result column="atom_quantity" jdbcType="BIGINT" property="atomQuantity" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="cooperator_id" jdbcType="VARCHAR" property="cooperatorId" />
    <result column="finish_cooperator_id" jdbcType="VARCHAR" property="finishCooperatorId" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="region_id" jdbcType="VARCHAR" property="regionId" />
    <result column="ex_handle_id" jdbcType="VARCHAR" property="exHandleId" />
    <result column="sku_card_name" jdbcType="VARCHAR" property="skuCardName" />
    <result column="sku_msisdn" jdbcType="VARCHAR" property="skuMsisdn" />
    <result column="allow_order_status" jdbcType="INTEGER" property="allowOrderStatus" />
    <result column="allow_order_failure_reason" jdbcType="VARCHAR" property="allowOrderFailureReason" />
    <result column="part_return" jdbcType="INTEGER" property="partReturn" />
    <result column="baoli_status" jdbcType="INTEGER" property="baoliStatus" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="valet_order_complete_time" jdbcType="VARCHAR" property="valetOrderCompleteTime" />
    <result column="car_open_status" jdbcType="INTEGER" property="carOpenStatus" />
    <result column="spu_offering_version" jdbcType="VARCHAR" property="spuOfferingVersion" />
    <result column="sku_offering_version" jdbcType="VARCHAR" property="skuOfferingVersion" />
    <result column="atom_offering_version" jdbcType="VARCHAR" property="atomOfferingVersion" />
    <result column="soft_service_status" jdbcType="INTEGER" property="softServiceStatus" />
    <result column="scm_order_num" jdbcType="VARCHAR" property="scmOrderNum" />
    <result column="settle_status" jdbcType="INTEGER" property="settleStatus" />
    <result column="online_settle_status" jdbcType="INTEGER" property="onlineSettleStatus" />
    <result column="bill_no_time" jdbcType="TIMESTAMP" property="billNoTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, order_type, spu_offering_code, sku_offering_code, sku_offering_name, 
    sku_quantity, sku_price, market_name, market_code, supplier_name, color, model, atom_offering_class, 
    atom_offering_code, atom_offering_name, deduct_price, atom_price, atom_settle_price, 
    atom_quantity, order_status, cooperator_id, finish_cooperator_id, be_id, region_id, 
    ex_handle_id, sku_card_name, sku_msisdn, allow_order_status, allow_order_failure_reason, 
    part_return, baoli_status, create_time, update_time, valet_order_complete_time, car_open_status, 
    spu_offering_version, sku_offering_version, atom_offering_version, soft_service_status, 
    scm_order_num, settle_status, online_settle_status, bill_no_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_2c_atom_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_2c_atom_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from order_2c_atom_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomInfoExample">
    delete from order_2c_atom_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomInfo">
    insert into order_2c_atom_info (id, order_id, order_type, 
      spu_offering_code, sku_offering_code, sku_offering_name, 
      sku_quantity, sku_price, market_name, 
      market_code, supplier_name, color, 
      model, atom_offering_class, atom_offering_code, 
      atom_offering_name, deduct_price, atom_price, 
      atom_settle_price, atom_quantity, order_status, 
      cooperator_id, finish_cooperator_id, be_id, 
      region_id, ex_handle_id, sku_card_name, 
      sku_msisdn, allow_order_status, allow_order_failure_reason, 
      part_return, baoli_status, create_time, 
      update_time, valet_order_complete_time, car_open_status, 
      spu_offering_version, sku_offering_version, 
      atom_offering_version, soft_service_status, 
      scm_order_num, settle_status, online_settle_status, 
      bill_no_time)
    values (#{id,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{orderType,jdbcType=VARCHAR}, 
      #{spuOfferingCode,jdbcType=VARCHAR}, #{skuOfferingCode,jdbcType=VARCHAR}, #{skuOfferingName,jdbcType=VARCHAR}, 
      #{skuQuantity,jdbcType=BIGINT}, #{skuPrice,jdbcType=BIGINT}, #{marketName,jdbcType=VARCHAR}, 
      #{marketCode,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR}, #{color,jdbcType=VARCHAR}, 
      #{model,jdbcType=VARCHAR}, #{atomOfferingClass,jdbcType=VARCHAR}, #{atomOfferingCode,jdbcType=VARCHAR}, 
      #{atomOfferingName,jdbcType=VARCHAR}, #{deductPrice,jdbcType=VARCHAR}, #{atomPrice,jdbcType=BIGINT}, 
      #{atomSettlePrice,jdbcType=BIGINT}, #{atomQuantity,jdbcType=BIGINT}, #{orderStatus,jdbcType=INTEGER}, 
      #{cooperatorId,jdbcType=VARCHAR}, #{finishCooperatorId,jdbcType=VARCHAR}, #{beId,jdbcType=VARCHAR}, 
      #{regionId,jdbcType=VARCHAR}, #{exHandleId,jdbcType=VARCHAR}, #{skuCardName,jdbcType=VARCHAR}, 
      #{skuMsisdn,jdbcType=VARCHAR}, #{allowOrderStatus,jdbcType=INTEGER}, #{allowOrderFailureReason,jdbcType=VARCHAR}, 
      #{partReturn,jdbcType=INTEGER}, #{baoliStatus,jdbcType=INTEGER}, #{createTime,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{valetOrderCompleteTime,jdbcType=VARCHAR}, #{carOpenStatus,jdbcType=INTEGER}, 
      #{spuOfferingVersion,jdbcType=VARCHAR}, #{skuOfferingVersion,jdbcType=VARCHAR}, 
      #{atomOfferingVersion,jdbcType=VARCHAR}, #{softServiceStatus,jdbcType=INTEGER}, 
      #{scmOrderNum,jdbcType=VARCHAR}, #{settleStatus,jdbcType=INTEGER}, #{onlineSettleStatus,jdbcType=INTEGER}, 
      #{billNoTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomInfo">
    insert into order_2c_atom_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="spuOfferingCode != null">
        spu_offering_code,
      </if>
      <if test="skuOfferingCode != null">
        sku_offering_code,
      </if>
      <if test="skuOfferingName != null">
        sku_offering_name,
      </if>
      <if test="skuQuantity != null">
        sku_quantity,
      </if>
      <if test="skuPrice != null">
        sku_price,
      </if>
      <if test="marketName != null">
        market_name,
      </if>
      <if test="marketCode != null">
        market_code,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="color != null">
        color,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="atomOfferingClass != null">
        atom_offering_class,
      </if>
      <if test="atomOfferingCode != null">
        atom_offering_code,
      </if>
      <if test="atomOfferingName != null">
        atom_offering_name,
      </if>
      <if test="deductPrice != null">
        deduct_price,
      </if>
      <if test="atomPrice != null">
        atom_price,
      </if>
      <if test="atomSettlePrice != null">
        atom_settle_price,
      </if>
      <if test="atomQuantity != null">
        atom_quantity,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="cooperatorId != null">
        cooperator_id,
      </if>
      <if test="finishCooperatorId != null">
        finish_cooperator_id,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="regionId != null">
        region_id,
      </if>
      <if test="exHandleId != null">
        ex_handle_id,
      </if>
      <if test="skuCardName != null">
        sku_card_name,
      </if>
      <if test="skuMsisdn != null">
        sku_msisdn,
      </if>
      <if test="allowOrderStatus != null">
        allow_order_status,
      </if>
      <if test="allowOrderFailureReason != null">
        allow_order_failure_reason,
      </if>
      <if test="partReturn != null">
        part_return,
      </if>
      <if test="baoliStatus != null">
        baoli_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="valetOrderCompleteTime != null">
        valet_order_complete_time,
      </if>
      <if test="carOpenStatus != null">
        car_open_status,
      </if>
      <if test="spuOfferingVersion != null">
        spu_offering_version,
      </if>
      <if test="skuOfferingVersion != null">
        sku_offering_version,
      </if>
      <if test="atomOfferingVersion != null">
        atom_offering_version,
      </if>
      <if test="softServiceStatus != null">
        soft_service_status,
      </if>
      <if test="scmOrderNum != null">
        scm_order_num,
      </if>
      <if test="settleStatus != null">
        settle_status,
      </if>
      <if test="onlineSettleStatus != null">
        online_settle_status,
      </if>
      <if test="billNoTime != null">
        bill_no_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingCode != null">
        #{spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingCode != null">
        #{skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingName != null">
        #{skuOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="skuQuantity != null">
        #{skuQuantity,jdbcType=BIGINT},
      </if>
      <if test="skuPrice != null">
        #{skuPrice,jdbcType=BIGINT},
      </if>
      <if test="marketName != null">
        #{marketName,jdbcType=VARCHAR},
      </if>
      <if test="marketCode != null">
        #{marketCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        #{color,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingClass != null">
        #{atomOfferingClass,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingCode != null">
        #{atomOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingName != null">
        #{atomOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="deductPrice != null">
        #{deductPrice,jdbcType=VARCHAR},
      </if>
      <if test="atomPrice != null">
        #{atomPrice,jdbcType=BIGINT},
      </if>
      <if test="atomSettlePrice != null">
        #{atomSettlePrice,jdbcType=BIGINT},
      </if>
      <if test="atomQuantity != null">
        #{atomQuantity,jdbcType=BIGINT},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="cooperatorId != null">
        #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="finishCooperatorId != null">
        #{finishCooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null">
        #{regionId,jdbcType=VARCHAR},
      </if>
      <if test="exHandleId != null">
        #{exHandleId,jdbcType=VARCHAR},
      </if>
      <if test="skuCardName != null">
        #{skuCardName,jdbcType=VARCHAR},
      </if>
      <if test="skuMsisdn != null">
        #{skuMsisdn,jdbcType=VARCHAR},
      </if>
      <if test="allowOrderStatus != null">
        #{allowOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="allowOrderFailureReason != null">
        #{allowOrderFailureReason,jdbcType=VARCHAR},
      </if>
      <if test="partReturn != null">
        #{partReturn,jdbcType=INTEGER},
      </if>
      <if test="baoliStatus != null">
        #{baoliStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valetOrderCompleteTime != null">
        #{valetOrderCompleteTime,jdbcType=VARCHAR},
      </if>
      <if test="carOpenStatus != null">
        #{carOpenStatus,jdbcType=INTEGER},
      </if>
      <if test="spuOfferingVersion != null">
        #{spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingVersion != null">
        #{skuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingVersion != null">
        #{atomOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="softServiceStatus != null">
        #{softServiceStatus,jdbcType=INTEGER},
      </if>
      <if test="scmOrderNum != null">
        #{scmOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="settleStatus != null">
        #{settleStatus,jdbcType=INTEGER},
      </if>
      <if test="onlineSettleStatus != null">
        #{onlineSettleStatus,jdbcType=INTEGER},
      </if>
      <if test="billNoTime != null">
        #{billNoTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomInfoExample" resultType="java.lang.Long">
    select count(*) from order_2c_atom_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_2c_atom_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=VARCHAR},
      </if>
      <if test="record.spuOfferingCode != null">
        spu_offering_code = #{record.spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.skuOfferingCode != null">
        sku_offering_code = #{record.skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.skuOfferingName != null">
        sku_offering_name = #{record.skuOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="record.skuQuantity != null">
        sku_quantity = #{record.skuQuantity,jdbcType=BIGINT},
      </if>
      <if test="record.skuPrice != null">
        sku_price = #{record.skuPrice,jdbcType=BIGINT},
      </if>
      <if test="record.marketName != null">
        market_name = #{record.marketName,jdbcType=VARCHAR},
      </if>
      <if test="record.marketCode != null">
        market_code = #{record.marketCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierName != null">
        supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      </if>
      <if test="record.color != null">
        color = #{record.color,jdbcType=VARCHAR},
      </if>
      <if test="record.model != null">
        model = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOfferingClass != null">
        atom_offering_class = #{record.atomOfferingClass,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOfferingCode != null">
        atom_offering_code = #{record.atomOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOfferingName != null">
        atom_offering_name = #{record.atomOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="record.deductPrice != null">
        deduct_price = #{record.deductPrice,jdbcType=VARCHAR},
      </if>
      <if test="record.atomPrice != null">
        atom_price = #{record.atomPrice,jdbcType=BIGINT},
      </if>
      <if test="record.atomSettlePrice != null">
        atom_settle_price = #{record.atomSettlePrice,jdbcType=BIGINT},
      </if>
      <if test="record.atomQuantity != null">
        atom_quantity = #{record.atomQuantity,jdbcType=BIGINT},
      </if>
      <if test="record.orderStatus != null">
        order_status = #{record.orderStatus,jdbcType=INTEGER},
      </if>
      <if test="record.cooperatorId != null">
        cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.finishCooperatorId != null">
        finish_cooperator_id = #{record.finishCooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.regionId != null">
        region_id = #{record.regionId,jdbcType=VARCHAR},
      </if>
      <if test="record.exHandleId != null">
        ex_handle_id = #{record.exHandleId,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCardName != null">
        sku_card_name = #{record.skuCardName,jdbcType=VARCHAR},
      </if>
      <if test="record.skuMsisdn != null">
        sku_msisdn = #{record.skuMsisdn,jdbcType=VARCHAR},
      </if>
      <if test="record.allowOrderStatus != null">
        allow_order_status = #{record.allowOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="record.allowOrderFailureReason != null">
        allow_order_failure_reason = #{record.allowOrderFailureReason,jdbcType=VARCHAR},
      </if>
      <if test="record.partReturn != null">
        part_return = #{record.partReturn,jdbcType=INTEGER},
      </if>
      <if test="record.baoliStatus != null">
        baoli_status = #{record.baoliStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.valetOrderCompleteTime != null">
        valet_order_complete_time = #{record.valetOrderCompleteTime,jdbcType=VARCHAR},
      </if>
      <if test="record.carOpenStatus != null">
        car_open_status = #{record.carOpenStatus,jdbcType=INTEGER},
      </if>
      <if test="record.spuOfferingVersion != null">
        spu_offering_version = #{record.spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.skuOfferingVersion != null">
        sku_offering_version = #{record.skuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOfferingVersion != null">
        atom_offering_version = #{record.atomOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.softServiceStatus != null">
        soft_service_status = #{record.softServiceStatus,jdbcType=INTEGER},
      </if>
      <if test="record.scmOrderNum != null">
        scm_order_num = #{record.scmOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="record.settleStatus != null">
        settle_status = #{record.settleStatus,jdbcType=INTEGER},
      </if>
      <if test="record.onlineSettleStatus != null">
        online_settle_status = #{record.onlineSettleStatus,jdbcType=INTEGER},
      </if>
      <if test="record.billNoTime != null">
        bill_no_time = #{record.billNoTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_2c_atom_info
    set id = #{record.id,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      order_type = #{record.orderType,jdbcType=VARCHAR},
      spu_offering_code = #{record.spuOfferingCode,jdbcType=VARCHAR},
      sku_offering_code = #{record.skuOfferingCode,jdbcType=VARCHAR},
      sku_offering_name = #{record.skuOfferingName,jdbcType=VARCHAR},
      sku_quantity = #{record.skuQuantity,jdbcType=BIGINT},
      sku_price = #{record.skuPrice,jdbcType=BIGINT},
      market_name = #{record.marketName,jdbcType=VARCHAR},
      market_code = #{record.marketCode,jdbcType=VARCHAR},
      supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      color = #{record.color,jdbcType=VARCHAR},
      model = #{record.model,jdbcType=VARCHAR},
      atom_offering_class = #{record.atomOfferingClass,jdbcType=VARCHAR},
      atom_offering_code = #{record.atomOfferingCode,jdbcType=VARCHAR},
      atom_offering_name = #{record.atomOfferingName,jdbcType=VARCHAR},
      deduct_price = #{record.deductPrice,jdbcType=VARCHAR},
      atom_price = #{record.atomPrice,jdbcType=BIGINT},
      atom_settle_price = #{record.atomSettlePrice,jdbcType=BIGINT},
      atom_quantity = #{record.atomQuantity,jdbcType=BIGINT},
      order_status = #{record.orderStatus,jdbcType=INTEGER},
      cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      finish_cooperator_id = #{record.finishCooperatorId,jdbcType=VARCHAR},
      be_id = #{record.beId,jdbcType=VARCHAR},
      region_id = #{record.regionId,jdbcType=VARCHAR},
      ex_handle_id = #{record.exHandleId,jdbcType=VARCHAR},
      sku_card_name = #{record.skuCardName,jdbcType=VARCHAR},
      sku_msisdn = #{record.skuMsisdn,jdbcType=VARCHAR},
      allow_order_status = #{record.allowOrderStatus,jdbcType=INTEGER},
      allow_order_failure_reason = #{record.allowOrderFailureReason,jdbcType=VARCHAR},
      part_return = #{record.partReturn,jdbcType=INTEGER},
      baoli_status = #{record.baoliStatus,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      valet_order_complete_time = #{record.valetOrderCompleteTime,jdbcType=VARCHAR},
      car_open_status = #{record.carOpenStatus,jdbcType=INTEGER},
      spu_offering_version = #{record.spuOfferingVersion,jdbcType=VARCHAR},
      sku_offering_version = #{record.skuOfferingVersion,jdbcType=VARCHAR},
      atom_offering_version = #{record.atomOfferingVersion,jdbcType=VARCHAR},
      soft_service_status = #{record.softServiceStatus,jdbcType=INTEGER},
      scm_order_num = #{record.scmOrderNum,jdbcType=VARCHAR},
      settle_status = #{record.settleStatus,jdbcType=INTEGER},
      online_settle_status = #{record.onlineSettleStatus,jdbcType=INTEGER},
      bill_no_time = #{record.billNoTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomInfo">
    update order_2c_atom_info
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingCode != null">
        spu_offering_code = #{spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingCode != null">
        sku_offering_code = #{skuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingName != null">
        sku_offering_name = #{skuOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="skuQuantity != null">
        sku_quantity = #{skuQuantity,jdbcType=BIGINT},
      </if>
      <if test="skuPrice != null">
        sku_price = #{skuPrice,jdbcType=BIGINT},
      </if>
      <if test="marketName != null">
        market_name = #{marketName,jdbcType=VARCHAR},
      </if>
      <if test="marketCode != null">
        market_code = #{marketCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        color = #{color,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingClass != null">
        atom_offering_class = #{atomOfferingClass,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingCode != null">
        atom_offering_code = #{atomOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingName != null">
        atom_offering_name = #{atomOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="deductPrice != null">
        deduct_price = #{deductPrice,jdbcType=VARCHAR},
      </if>
      <if test="atomPrice != null">
        atom_price = #{atomPrice,jdbcType=BIGINT},
      </if>
      <if test="atomSettlePrice != null">
        atom_settle_price = #{atomSettlePrice,jdbcType=BIGINT},
      </if>
      <if test="atomQuantity != null">
        atom_quantity = #{atomQuantity,jdbcType=BIGINT},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="cooperatorId != null">
        cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="finishCooperatorId != null">
        finish_cooperator_id = #{finishCooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null">
        region_id = #{regionId,jdbcType=VARCHAR},
      </if>
      <if test="exHandleId != null">
        ex_handle_id = #{exHandleId,jdbcType=VARCHAR},
      </if>
      <if test="skuCardName != null">
        sku_card_name = #{skuCardName,jdbcType=VARCHAR},
      </if>
      <if test="skuMsisdn != null">
        sku_msisdn = #{skuMsisdn,jdbcType=VARCHAR},
      </if>
      <if test="allowOrderStatus != null">
        allow_order_status = #{allowOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="allowOrderFailureReason != null">
        allow_order_failure_reason = #{allowOrderFailureReason,jdbcType=VARCHAR},
      </if>
      <if test="partReturn != null">
        part_return = #{partReturn,jdbcType=INTEGER},
      </if>
      <if test="baoliStatus != null">
        baoli_status = #{baoliStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valetOrderCompleteTime != null">
        valet_order_complete_time = #{valetOrderCompleteTime,jdbcType=VARCHAR},
      </if>
      <if test="carOpenStatus != null">
        car_open_status = #{carOpenStatus,jdbcType=INTEGER},
      </if>
      <if test="spuOfferingVersion != null">
        spu_offering_version = #{spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="skuOfferingVersion != null">
        sku_offering_version = #{skuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingVersion != null">
        atom_offering_version = #{atomOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="softServiceStatus != null">
        soft_service_status = #{softServiceStatus,jdbcType=INTEGER},
      </if>
      <if test="scmOrderNum != null">
        scm_order_num = #{scmOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="settleStatus != null">
        settle_status = #{settleStatus,jdbcType=INTEGER},
      </if>
      <if test="onlineSettleStatus != null">
        online_settle_status = #{onlineSettleStatus,jdbcType=INTEGER},
      </if>
      <if test="billNoTime != null">
        bill_no_time = #{billNoTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomInfo">
    update order_2c_atom_info
    set order_id = #{orderId,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=VARCHAR},
      spu_offering_code = #{spuOfferingCode,jdbcType=VARCHAR},
      sku_offering_code = #{skuOfferingCode,jdbcType=VARCHAR},
      sku_offering_name = #{skuOfferingName,jdbcType=VARCHAR},
      sku_quantity = #{skuQuantity,jdbcType=BIGINT},
      sku_price = #{skuPrice,jdbcType=BIGINT},
      market_name = #{marketName,jdbcType=VARCHAR},
      market_code = #{marketCode,jdbcType=VARCHAR},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      color = #{color,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      atom_offering_class = #{atomOfferingClass,jdbcType=VARCHAR},
      atom_offering_code = #{atomOfferingCode,jdbcType=VARCHAR},
      atom_offering_name = #{atomOfferingName,jdbcType=VARCHAR},
      deduct_price = #{deductPrice,jdbcType=VARCHAR},
      atom_price = #{atomPrice,jdbcType=BIGINT},
      atom_settle_price = #{atomSettlePrice,jdbcType=BIGINT},
      atom_quantity = #{atomQuantity,jdbcType=BIGINT},
      order_status = #{orderStatus,jdbcType=INTEGER},
      cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      finish_cooperator_id = #{finishCooperatorId,jdbcType=VARCHAR},
      be_id = #{beId,jdbcType=VARCHAR},
      region_id = #{regionId,jdbcType=VARCHAR},
      ex_handle_id = #{exHandleId,jdbcType=VARCHAR},
      sku_card_name = #{skuCardName,jdbcType=VARCHAR},
      sku_msisdn = #{skuMsisdn,jdbcType=VARCHAR},
      allow_order_status = #{allowOrderStatus,jdbcType=INTEGER},
      allow_order_failure_reason = #{allowOrderFailureReason,jdbcType=VARCHAR},
      part_return = #{partReturn,jdbcType=INTEGER},
      baoli_status = #{baoliStatus,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      valet_order_complete_time = #{valetOrderCompleteTime,jdbcType=VARCHAR},
      car_open_status = #{carOpenStatus,jdbcType=INTEGER},
      spu_offering_version = #{spuOfferingVersion,jdbcType=VARCHAR},
      sku_offering_version = #{skuOfferingVersion,jdbcType=VARCHAR},
      atom_offering_version = #{atomOfferingVersion,jdbcType=VARCHAR},
      soft_service_status = #{softServiceStatus,jdbcType=INTEGER},
      scm_order_num = #{scmOrderNum,jdbcType=VARCHAR},
      settle_status = #{settleStatus,jdbcType=INTEGER},
      online_settle_status = #{onlineSettleStatus,jdbcType=INTEGER},
      bill_no_time = #{billNoTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into order_2c_atom_info
    (id, order_id, order_type, spu_offering_code, sku_offering_code, sku_offering_name, 
      sku_quantity, sku_price, market_name, market_code, supplier_name, color, model, 
      atom_offering_class, atom_offering_code, atom_offering_name, deduct_price, atom_price, 
      atom_settle_price, atom_quantity, order_status, cooperator_id, finish_cooperator_id, 
      be_id, region_id, ex_handle_id, sku_card_name, sku_msisdn, allow_order_status, 
      allow_order_failure_reason, part_return, baoli_status, create_time, update_time, 
      valet_order_complete_time, car_open_status, spu_offering_version, sku_offering_version, 
      atom_offering_version, soft_service_status, scm_order_num, settle_status, online_settle_status, 
      bill_no_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR}, #{item.orderType,jdbcType=VARCHAR}, 
        #{item.spuOfferingCode,jdbcType=VARCHAR}, #{item.skuOfferingCode,jdbcType=VARCHAR}, 
        #{item.skuOfferingName,jdbcType=VARCHAR}, #{item.skuQuantity,jdbcType=BIGINT}, 
        #{item.skuPrice,jdbcType=BIGINT}, #{item.marketName,jdbcType=VARCHAR}, #{item.marketCode,jdbcType=VARCHAR}, 
        #{item.supplierName,jdbcType=VARCHAR}, #{item.color,jdbcType=VARCHAR}, #{item.model,jdbcType=VARCHAR}, 
        #{item.atomOfferingClass,jdbcType=VARCHAR}, #{item.atomOfferingCode,jdbcType=VARCHAR}, 
        #{item.atomOfferingName,jdbcType=VARCHAR}, #{item.deductPrice,jdbcType=VARCHAR}, 
        #{item.atomPrice,jdbcType=BIGINT}, #{item.atomSettlePrice,jdbcType=BIGINT}, #{item.atomQuantity,jdbcType=BIGINT}, 
        #{item.orderStatus,jdbcType=INTEGER}, #{item.cooperatorId,jdbcType=VARCHAR}, #{item.finishCooperatorId,jdbcType=VARCHAR}, 
        #{item.beId,jdbcType=VARCHAR}, #{item.regionId,jdbcType=VARCHAR}, #{item.exHandleId,jdbcType=VARCHAR}, 
        #{item.skuCardName,jdbcType=VARCHAR}, #{item.skuMsisdn,jdbcType=VARCHAR}, #{item.allowOrderStatus,jdbcType=INTEGER}, 
        #{item.allowOrderFailureReason,jdbcType=VARCHAR}, #{item.partReturn,jdbcType=INTEGER}, 
        #{item.baoliStatus,jdbcType=INTEGER}, #{item.createTime,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.valetOrderCompleteTime,jdbcType=VARCHAR}, #{item.carOpenStatus,jdbcType=INTEGER}, 
        #{item.spuOfferingVersion,jdbcType=VARCHAR}, #{item.skuOfferingVersion,jdbcType=VARCHAR}, 
        #{item.atomOfferingVersion,jdbcType=VARCHAR}, #{item.softServiceStatus,jdbcType=INTEGER}, 
        #{item.scmOrderNum,jdbcType=VARCHAR}, #{item.settleStatus,jdbcType=INTEGER}, #{item.onlineSettleStatus,jdbcType=INTEGER}, 
        #{item.billNoTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into order_2c_atom_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'order_type'.toString() == column.value">
          #{item.orderType,jdbcType=VARCHAR}
        </if>
        <if test="'spu_offering_code'.toString() == column.value">
          #{item.spuOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'sku_offering_code'.toString() == column.value">
          #{item.skuOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'sku_offering_name'.toString() == column.value">
          #{item.skuOfferingName,jdbcType=VARCHAR}
        </if>
        <if test="'sku_quantity'.toString() == column.value">
          #{item.skuQuantity,jdbcType=BIGINT}
        </if>
        <if test="'sku_price'.toString() == column.value">
          #{item.skuPrice,jdbcType=BIGINT}
        </if>
        <if test="'market_name'.toString() == column.value">
          #{item.marketName,jdbcType=VARCHAR}
        </if>
        <if test="'market_code'.toString() == column.value">
          #{item.marketCode,jdbcType=VARCHAR}
        </if>
        <if test="'supplier_name'.toString() == column.value">
          #{item.supplierName,jdbcType=VARCHAR}
        </if>
        <if test="'color'.toString() == column.value">
          #{item.color,jdbcType=VARCHAR}
        </if>
        <if test="'model'.toString() == column.value">
          #{item.model,jdbcType=VARCHAR}
        </if>
        <if test="'atom_offering_class'.toString() == column.value">
          #{item.atomOfferingClass,jdbcType=VARCHAR}
        </if>
        <if test="'atom_offering_code'.toString() == column.value">
          #{item.atomOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'atom_offering_name'.toString() == column.value">
          #{item.atomOfferingName,jdbcType=VARCHAR}
        </if>
        <if test="'deduct_price'.toString() == column.value">
          #{item.deductPrice,jdbcType=VARCHAR}
        </if>
        <if test="'atom_price'.toString() == column.value">
          #{item.atomPrice,jdbcType=BIGINT}
        </if>
        <if test="'atom_settle_price'.toString() == column.value">
          #{item.atomSettlePrice,jdbcType=BIGINT}
        </if>
        <if test="'atom_quantity'.toString() == column.value">
          #{item.atomQuantity,jdbcType=BIGINT}
        </if>
        <if test="'order_status'.toString() == column.value">
          #{item.orderStatus,jdbcType=INTEGER}
        </if>
        <if test="'cooperator_id'.toString() == column.value">
          #{item.cooperatorId,jdbcType=VARCHAR}
        </if>
        <if test="'finish_cooperator_id'.toString() == column.value">
          #{item.finishCooperatorId,jdbcType=VARCHAR}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'region_id'.toString() == column.value">
          #{item.regionId,jdbcType=VARCHAR}
        </if>
        <if test="'ex_handle_id'.toString() == column.value">
          #{item.exHandleId,jdbcType=VARCHAR}
        </if>
        <if test="'sku_card_name'.toString() == column.value">
          #{item.skuCardName,jdbcType=VARCHAR}
        </if>
        <if test="'sku_msisdn'.toString() == column.value">
          #{item.skuMsisdn,jdbcType=VARCHAR}
        </if>
        <if test="'allow_order_status'.toString() == column.value">
          #{item.allowOrderStatus,jdbcType=INTEGER}
        </if>
        <if test="'allow_order_failure_reason'.toString() == column.value">
          #{item.allowOrderFailureReason,jdbcType=VARCHAR}
        </if>
        <if test="'part_return'.toString() == column.value">
          #{item.partReturn,jdbcType=INTEGER}
        </if>
        <if test="'baoli_status'.toString() == column.value">
          #{item.baoliStatus,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=VARCHAR}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'valet_order_complete_time'.toString() == column.value">
          #{item.valetOrderCompleteTime,jdbcType=VARCHAR}
        </if>
        <if test="'car_open_status'.toString() == column.value">
          #{item.carOpenStatus,jdbcType=INTEGER}
        </if>
        <if test="'spu_offering_version'.toString() == column.value">
          #{item.spuOfferingVersion,jdbcType=VARCHAR}
        </if>
        <if test="'sku_offering_version'.toString() == column.value">
          #{item.skuOfferingVersion,jdbcType=VARCHAR}
        </if>
        <if test="'atom_offering_version'.toString() == column.value">
          #{item.atomOfferingVersion,jdbcType=VARCHAR}
        </if>
        <if test="'soft_service_status'.toString() == column.value">
          #{item.softServiceStatus,jdbcType=INTEGER}
        </if>
        <if test="'scm_order_num'.toString() == column.value">
          #{item.scmOrderNum,jdbcType=VARCHAR}
        </if>
        <if test="'settle_status'.toString() == column.value">
          #{item.settleStatus,jdbcType=INTEGER}
        </if>
        <if test="'online_settle_status'.toString() == column.value">
          #{item.onlineSettleStatus,jdbcType=INTEGER}
        </if>
        <if test="'bill_no_time'.toString() == column.value">
          #{item.billNoTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>