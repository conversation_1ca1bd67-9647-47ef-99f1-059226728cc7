<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.NewProductRequestHandlerInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.NewProductRequestHandlerInfo">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="flow_source_id" jdbcType="VARCHAR" property="flowSourceId" />
    <result column="flow_type" jdbcType="VARCHAR" property="flowType" />
    <result column="request_link" jdbcType="VARCHAR" property="requestLink" />
    <result column="current_handler_user_id" jdbcType="VARCHAR" property="currentHandlerUserId" />
    <result column="current_handler_user_name" jdbcType="VARCHAR" property="currentHandlerUserName" />
    <result column="next_handler_user_id" jdbcType="VARCHAR" property="nextHandlerUserId" />
    <result column="next_handler_user_name" jdbcType="VARCHAR" property="nextHandlerUserName" />
    <result column="handler_status" jdbcType="VARCHAR" property="handlerStatus" />
    <result column="handler_remark" jdbcType="VARCHAR" property="handlerRemark" />
    <result column="online_status" jdbcType="VARCHAR" property="onlineStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, flow_source_id, flow_type, request_link, current_handler_user_id, current_handler_user_name, 
    next_handler_user_id, next_handler_user_name, handler_status, handler_remark, online_status, 
    create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestHandlerInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from new_product_request_handler_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from new_product_request_handler_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from new_product_request_handler_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestHandlerInfoExample">
    delete from new_product_request_handler_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestHandlerInfo">
    insert into new_product_request_handler_info (id, flow_source_id, flow_type, 
      request_link, current_handler_user_id, current_handler_user_name, 
      next_handler_user_id, next_handler_user_name, 
      handler_status, handler_remark, online_status, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{flowSourceId,jdbcType=VARCHAR}, #{flowType,jdbcType=VARCHAR}, 
      #{requestLink,jdbcType=VARCHAR}, #{currentHandlerUserId,jdbcType=VARCHAR}, #{currentHandlerUserName,jdbcType=VARCHAR}, 
      #{nextHandlerUserId,jdbcType=VARCHAR}, #{nextHandlerUserName,jdbcType=VARCHAR}, 
      #{handlerStatus,jdbcType=VARCHAR}, #{handlerRemark,jdbcType=VARCHAR}, #{onlineStatus,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestHandlerInfo">
    insert into new_product_request_handler_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="flowSourceId != null">
        flow_source_id,
      </if>
      <if test="flowType != null">
        flow_type,
      </if>
      <if test="requestLink != null">
        request_link,
      </if>
      <if test="currentHandlerUserId != null">
        current_handler_user_id,
      </if>
      <if test="currentHandlerUserName != null">
        current_handler_user_name,
      </if>
      <if test="nextHandlerUserId != null">
        next_handler_user_id,
      </if>
      <if test="nextHandlerUserName != null">
        next_handler_user_name,
      </if>
      <if test="handlerStatus != null">
        handler_status,
      </if>
      <if test="handlerRemark != null">
        handler_remark,
      </if>
      <if test="onlineStatus != null">
        online_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="flowSourceId != null">
        #{flowSourceId,jdbcType=VARCHAR},
      </if>
      <if test="flowType != null">
        #{flowType,jdbcType=VARCHAR},
      </if>
      <if test="requestLink != null">
        #{requestLink,jdbcType=VARCHAR},
      </if>
      <if test="currentHandlerUserId != null">
        #{currentHandlerUserId,jdbcType=VARCHAR},
      </if>
      <if test="currentHandlerUserName != null">
        #{currentHandlerUserName,jdbcType=VARCHAR},
      </if>
      <if test="nextHandlerUserId != null">
        #{nextHandlerUserId,jdbcType=VARCHAR},
      </if>
      <if test="nextHandlerUserName != null">
        #{nextHandlerUserName,jdbcType=VARCHAR},
      </if>
      <if test="handlerStatus != null">
        #{handlerStatus,jdbcType=VARCHAR},
      </if>
      <if test="handlerRemark != null">
        #{handlerRemark,jdbcType=VARCHAR},
      </if>
      <if test="onlineStatus != null">
        #{onlineStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestHandlerInfoExample" resultType="java.lang.Long">
    select count(*) from new_product_request_handler_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update new_product_request_handler_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.flowSourceId != null">
        flow_source_id = #{record.flowSourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.flowType != null">
        flow_type = #{record.flowType,jdbcType=VARCHAR},
      </if>
      <if test="record.requestLink != null">
        request_link = #{record.requestLink,jdbcType=VARCHAR},
      </if>
      <if test="record.currentHandlerUserId != null">
        current_handler_user_id = #{record.currentHandlerUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.currentHandlerUserName != null">
        current_handler_user_name = #{record.currentHandlerUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.nextHandlerUserId != null">
        next_handler_user_id = #{record.nextHandlerUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.nextHandlerUserName != null">
        next_handler_user_name = #{record.nextHandlerUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.handlerStatus != null">
        handler_status = #{record.handlerStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.handlerRemark != null">
        handler_remark = #{record.handlerRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.onlineStatus != null">
        online_status = #{record.onlineStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update new_product_request_handler_info
    set id = #{record.id,jdbcType=VARCHAR},
      flow_source_id = #{record.flowSourceId,jdbcType=VARCHAR},
      flow_type = #{record.flowType,jdbcType=VARCHAR},
      request_link = #{record.requestLink,jdbcType=VARCHAR},
      current_handler_user_id = #{record.currentHandlerUserId,jdbcType=VARCHAR},
      current_handler_user_name = #{record.currentHandlerUserName,jdbcType=VARCHAR},
      next_handler_user_id = #{record.nextHandlerUserId,jdbcType=VARCHAR},
      next_handler_user_name = #{record.nextHandlerUserName,jdbcType=VARCHAR},
      handler_status = #{record.handlerStatus,jdbcType=VARCHAR},
      handler_remark = #{record.handlerRemark,jdbcType=VARCHAR},
      online_status = #{record.onlineStatus,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestHandlerInfo">
    update new_product_request_handler_info
    <set>
      <if test="flowSourceId != null">
        flow_source_id = #{flowSourceId,jdbcType=VARCHAR},
      </if>
      <if test="flowType != null">
        flow_type = #{flowType,jdbcType=VARCHAR},
      </if>
      <if test="requestLink != null">
        request_link = #{requestLink,jdbcType=VARCHAR},
      </if>
      <if test="currentHandlerUserId != null">
        current_handler_user_id = #{currentHandlerUserId,jdbcType=VARCHAR},
      </if>
      <if test="currentHandlerUserName != null">
        current_handler_user_name = #{currentHandlerUserName,jdbcType=VARCHAR},
      </if>
      <if test="nextHandlerUserId != null">
        next_handler_user_id = #{nextHandlerUserId,jdbcType=VARCHAR},
      </if>
      <if test="nextHandlerUserName != null">
        next_handler_user_name = #{nextHandlerUserName,jdbcType=VARCHAR},
      </if>
      <if test="handlerStatus != null">
        handler_status = #{handlerStatus,jdbcType=VARCHAR},
      </if>
      <if test="handlerRemark != null">
        handler_remark = #{handlerRemark,jdbcType=VARCHAR},
      </if>
      <if test="onlineStatus != null">
        online_status = #{onlineStatus,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestHandlerInfo">
    update new_product_request_handler_info
    set flow_source_id = #{flowSourceId,jdbcType=VARCHAR},
      flow_type = #{flowType,jdbcType=VARCHAR},
      request_link = #{requestLink,jdbcType=VARCHAR},
      current_handler_user_id = #{currentHandlerUserId,jdbcType=VARCHAR},
      current_handler_user_name = #{currentHandlerUserName,jdbcType=VARCHAR},
      next_handler_user_id = #{nextHandlerUserId,jdbcType=VARCHAR},
      next_handler_user_name = #{nextHandlerUserName,jdbcType=VARCHAR},
      handler_status = #{handlerStatus,jdbcType=VARCHAR},
      handler_remark = #{handlerRemark,jdbcType=VARCHAR},
      online_status = #{onlineStatus,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into new_product_request_handler_info
    (id, flow_source_id, flow_type, request_link, current_handler_user_id, current_handler_user_name, 
      next_handler_user_id, next_handler_user_name, handler_status, handler_remark, online_status, 
      create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.flowSourceId,jdbcType=VARCHAR}, #{item.flowType,jdbcType=VARCHAR}, 
        #{item.requestLink,jdbcType=VARCHAR}, #{item.currentHandlerUserId,jdbcType=VARCHAR}, 
        #{item.currentHandlerUserName,jdbcType=VARCHAR}, #{item.nextHandlerUserId,jdbcType=VARCHAR}, 
        #{item.nextHandlerUserName,jdbcType=VARCHAR}, #{item.handlerStatus,jdbcType=VARCHAR}, 
        #{item.handlerRemark,jdbcType=VARCHAR}, #{item.onlineStatus,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into new_product_request_handler_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'flow_source_id'.toString() == column.value">
          #{item.flowSourceId,jdbcType=VARCHAR}
        </if>
        <if test="'flow_type'.toString() == column.value">
          #{item.flowType,jdbcType=VARCHAR}
        </if>
        <if test="'request_link'.toString() == column.value">
          #{item.requestLink,jdbcType=VARCHAR}
        </if>
        <if test="'current_handler_user_id'.toString() == column.value">
          #{item.currentHandlerUserId,jdbcType=VARCHAR}
        </if>
        <if test="'current_handler_user_name'.toString() == column.value">
          #{item.currentHandlerUserName,jdbcType=VARCHAR}
        </if>
        <if test="'next_handler_user_id'.toString() == column.value">
          #{item.nextHandlerUserId,jdbcType=VARCHAR}
        </if>
        <if test="'next_handler_user_name'.toString() == column.value">
          #{item.nextHandlerUserName,jdbcType=VARCHAR}
        </if>
        <if test="'handler_status'.toString() == column.value">
          #{item.handlerStatus,jdbcType=VARCHAR}
        </if>
        <if test="'handler_remark'.toString() == column.value">
          #{item.handlerRemark,jdbcType=VARCHAR}
        </if>
        <if test="'online_status'.toString() == column.value">
          #{item.onlineStatus,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>