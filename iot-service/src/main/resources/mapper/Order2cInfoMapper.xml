<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.Order2cInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.Order2cInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Jun 09 09:29:34 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
    <result column="create_oper_code" jdbcType="VARCHAR" property="createOperCode" />
    <result column="create_oper_user_id" jdbcType="VARCHAR" property="createOperUserId" />
    <result column="employee_num" jdbcType="VARCHAR" property="employeeNum" />
    <result column="cust_mg_name" jdbcType="VARCHAR" property="custMgName" />
    <result column="cust_mg_phone" jdbcType="VARCHAR" property="custMgPhone" />
    <result column="order_status_time" jdbcType="TIMESTAMP" property="orderStatusTime" />
    <result column="cust_code" jdbcType="VARCHAR" property="custCode" />
    <result column="cust_user_id" jdbcType="VARCHAR" property="custUserId" />
    <result column="cust_name" jdbcType="VARCHAR" property="custName" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="region_ID" jdbcType="VARCHAR" property="regionId" />
    <result column="order_org_biz_code" jdbcType="VARCHAR" property="orderOrgBizCode" />
    <result column="org_level" jdbcType="VARCHAR" property="orgLevel" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="province_org_name" jdbcType="VARCHAR" property="provinceOrgName" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="bookId" jdbcType="VARCHAR" property="bookid" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="total_price" jdbcType="VARCHAR" property="totalPrice" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="contact_person_name" jdbcType="VARCHAR" property="contactPersonName" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="addr1" jdbcType="VARCHAR" property="addr1" />
    <result column="addr2" jdbcType="VARCHAR" property="addr2" />
    <result column="addr3" jdbcType="VARCHAR" property="addr3" />
    <result column="addr4" jdbcType="VARCHAR" property="addr4" />
    <result column="usaddr" jdbcType="VARCHAR" property="usaddr" />
    <result column="henan_real_name" jdbcType="VARCHAR" property="henanRealName" />
    <result column="henan_real_phone" jdbcType="VARCHAR" property="henanRealPhone" />
    <result column="henan_real_address" jdbcType="VARCHAR" property="henanRealAddress" />
    <result column="spu_offering_class" jdbcType="VARCHAR" property="spuOfferingClass" />
    <result column="spu_offering_code" jdbcType="VARCHAR" property="spuOfferingCode" />
    <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="deduct_price" jdbcType="VARCHAR" property="deductPrice" />
    <result column="ordering_channel_source" jdbcType="VARCHAR" property="orderingChannelSource" />
    <result column="ordering_channel_name" jdbcType="VARCHAR" property="orderingChannelName" />
    <result column="sso_terminal_type" jdbcType="VARCHAR" property="ssoTerminalType" />
    <result column="to_k3" jdbcType="INTEGER" property="toK3" />
    <result column="special_after_market_handle" jdbcType="INTEGER" property="specialAfterMarketHandle" />
    <result column="special_after_status" jdbcType="VARCHAR" property="specialAfterStatus" />
    <result column="special_after_status_time" jdbcType="VARCHAR" property="specialAfterStatusTime" />
    <result column="special_after_latest_time" jdbcType="VARCHAR" property="specialAfterLatestTime" />
    <result column="effective_rules" jdbcType="VARCHAR" property="effectiveRules" />
    <result column="effective_time" jdbcType="VARCHAR" property="effectiveTime" />
    <result column="sync_k3_id" jdbcType="VARCHAR" property="syncK3Id" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="refund_time" jdbcType="TIMESTAMP" property="refundTime" />
    <result column="valet_order_complete_time" jdbcType="VARCHAR" property="valetOrderCompleteTime" />
    <result column="bill_ladder_type" jdbcType="VARCHAR" property="billLadderType" />
    <result column="qly_status" jdbcType="INTEGER" property="qlyStatus" />
    <result column="ysx_status" jdbcType="INTEGER" property="ysxStatus" />
    <result column="kx_refund_status" jdbcType="INTEGER" property="kxRefundStatus" />
    <result column="kx_refund_reason" jdbcType="VARCHAR" property="kxRefundReason" />
    <result column="spu_offering_version" jdbcType="VARCHAR" property="spuOfferingVersion" />
    <result column="reserve_be_id" jdbcType="VARCHAR" property="reserveBeId" />
    <result column="reserve_location" jdbcType="VARCHAR" property="reserveLocation" />
    <result column="software_order_type" jdbcType="VARCHAR" property="softwareOrderType" />
    <result column="associated_order" jdbcType="VARCHAR" property="associatedOrder" />
    <result column="special_after_refunds_number" jdbcType="VARCHAR" property="specialAfterRefundsNumber" />
    <result column="spu_list_platform" jdbcType="VARCHAR" property="spuListPlatform" />
    <result column="bill_no_number" jdbcType="VARCHAR" property="billNoNumber" />
    <result column="reminder_wait_send" jdbcType="INTEGER" property="reminderWaitSend" />
    <result column="reminder_valet_taking" jdbcType="INTEGER" property="reminderValetTaking" />
    <result column="reminder_wait_deliver" jdbcType="INTEGER" property="reminderWaitDeliver" />
    <result column="customer_type" jdbcType="VARCHAR" property="customerType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Jun 09 09:29:34 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Jun 09 09:29:34 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Jun 09 09:29:34 CST 2025. by MyBatis Generator, do not modify.
    -->
    order_id, order_type, business_code, create_oper_code, create_oper_user_id, employee_num, 
    cust_mg_name, cust_mg_phone, order_status_time, cust_code, cust_user_id, cust_name, 
    be_id, location, region_ID, order_org_biz_code, org_level, org_name, province_org_name, 
    remarks, bookId, status, total_price, create_time, contact_person_name, contact_phone, 
    addr1, addr2, addr3, addr4, usaddr, henan_real_name, henan_real_phone, henan_real_address, 
    spu_offering_class, spu_offering_code, supplier_code, update_time, order_status, 
    deduct_price, ordering_channel_source, ordering_channel_name, sso_terminal_type, 
    to_k3, special_after_market_handle, special_after_status, special_after_status_time, 
    special_after_latest_time, effective_rules, effective_time, sync_k3_id, pay_time, 
    refund_time, valet_order_complete_time, bill_ladder_type, qly_status, ysx_status, 
    kx_refund_status, kx_refund_reason, spu_offering_version, reserve_be_id, reserve_location, 
    software_order_type, associated_order, special_after_refunds_number, spu_list_platform, 
    bill_no_number, reminder_wait_send, reminder_valet_taking, reminder_wait_deliver, 
    customer_type
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Jun 09 09:29:34 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_2c_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Jun 09 09:29:34 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from order_2c_info
    where order_id = #{orderId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Jun 09 09:29:34 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from order_2c_info
    where order_id = #{orderId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Jun 09 09:29:34 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from order_2c_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.Order2cInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Jun 09 09:29:34 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_info (order_id, order_type, business_code, 
      create_oper_code, create_oper_user_id, employee_num, 
      cust_mg_name, cust_mg_phone, order_status_time, 
      cust_code, cust_user_id, cust_name, 
      be_id, location, region_ID, 
      order_org_biz_code, org_level, org_name, 
      province_org_name, remarks, bookId, 
      status, total_price, create_time, 
      contact_person_name, contact_phone, addr1, 
      addr2, addr3, addr4, 
      usaddr, henan_real_name, henan_real_phone, 
      henan_real_address, spu_offering_class, spu_offering_code, 
      supplier_code, update_time, order_status, 
      deduct_price, ordering_channel_source, ordering_channel_name, 
      sso_terminal_type, to_k3, special_after_market_handle, 
      special_after_status, special_after_status_time, 
      special_after_latest_time, effective_rules, 
      effective_time, sync_k3_id, pay_time, 
      refund_time, valet_order_complete_time, bill_ladder_type, 
      qly_status, ysx_status, kx_refund_status, 
      kx_refund_reason, spu_offering_version, reserve_be_id, 
      reserve_location, software_order_type, associated_order, 
      special_after_refunds_number, spu_list_platform, 
      bill_no_number, reminder_wait_send, reminder_valet_taking, 
      reminder_wait_deliver, customer_type)
    values (#{orderId,jdbcType=VARCHAR}, #{orderType,jdbcType=VARCHAR}, #{businessCode,jdbcType=VARCHAR}, 
      #{createOperCode,jdbcType=VARCHAR}, #{createOperUserId,jdbcType=VARCHAR}, #{employeeNum,jdbcType=VARCHAR}, 
      #{custMgName,jdbcType=VARCHAR}, #{custMgPhone,jdbcType=VARCHAR}, #{orderStatusTime,jdbcType=TIMESTAMP}, 
      #{custCode,jdbcType=VARCHAR}, #{custUserId,jdbcType=VARCHAR}, #{custName,jdbcType=VARCHAR}, 
      #{beId,jdbcType=VARCHAR}, #{location,jdbcType=VARCHAR}, #{regionId,jdbcType=VARCHAR}, 
      #{orderOrgBizCode,jdbcType=VARCHAR}, #{orgLevel,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, 
      #{provinceOrgName,jdbcType=VARCHAR}, #{remarks,jdbcType=VARCHAR}, #{bookid,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{totalPrice,jdbcType=VARCHAR}, #{createTime,jdbcType=VARCHAR}, 
      #{contactPersonName,jdbcType=VARCHAR}, #{contactPhone,jdbcType=VARCHAR}, #{addr1,jdbcType=VARCHAR}, 
      #{addr2,jdbcType=VARCHAR}, #{addr3,jdbcType=VARCHAR}, #{addr4,jdbcType=VARCHAR}, 
      #{usaddr,jdbcType=VARCHAR}, #{henanRealName,jdbcType=VARCHAR}, #{henanRealPhone,jdbcType=VARCHAR}, 
      #{henanRealAddress,jdbcType=VARCHAR}, #{spuOfferingClass,jdbcType=VARCHAR}, #{spuOfferingCode,jdbcType=VARCHAR}, 
      #{supplierCode,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{orderStatus,jdbcType=INTEGER}, 
      #{deductPrice,jdbcType=VARCHAR}, #{orderingChannelSource,jdbcType=VARCHAR}, #{orderingChannelName,jdbcType=VARCHAR}, 
      #{ssoTerminalType,jdbcType=VARCHAR}, #{toK3,jdbcType=INTEGER}, #{specialAfterMarketHandle,jdbcType=INTEGER}, 
      #{specialAfterStatus,jdbcType=VARCHAR}, #{specialAfterStatusTime,jdbcType=VARCHAR}, 
      #{specialAfterLatestTime,jdbcType=VARCHAR}, #{effectiveRules,jdbcType=VARCHAR}, 
      #{effectiveTime,jdbcType=VARCHAR}, #{syncK3Id,jdbcType=VARCHAR}, #{payTime,jdbcType=TIMESTAMP}, 
      #{refundTime,jdbcType=TIMESTAMP}, #{valetOrderCompleteTime,jdbcType=VARCHAR}, #{billLadderType,jdbcType=VARCHAR}, 
      #{qlyStatus,jdbcType=INTEGER}, #{ysxStatus,jdbcType=INTEGER}, #{kxRefundStatus,jdbcType=INTEGER}, 
      #{kxRefundReason,jdbcType=VARCHAR}, #{spuOfferingVersion,jdbcType=VARCHAR}, #{reserveBeId,jdbcType=VARCHAR}, 
      #{reserveLocation,jdbcType=VARCHAR}, #{softwareOrderType,jdbcType=VARCHAR}, #{associatedOrder,jdbcType=VARCHAR}, 
      #{specialAfterRefundsNumber,jdbcType=VARCHAR}, #{spuListPlatform,jdbcType=VARCHAR}, 
      #{billNoNumber,jdbcType=VARCHAR}, #{reminderWaitSend,jdbcType=INTEGER}, #{reminderValetTaking,jdbcType=INTEGER}, 
      #{reminderWaitDeliver,jdbcType=INTEGER}, #{customerType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.Order2cInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Jun 09 09:29:34 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="businessCode != null">
        business_code,
      </if>
      <if test="createOperCode != null">
        create_oper_code,
      </if>
      <if test="createOperUserId != null">
        create_oper_user_id,
      </if>
      <if test="employeeNum != null">
        employee_num,
      </if>
      <if test="custMgName != null">
        cust_mg_name,
      </if>
      <if test="custMgPhone != null">
        cust_mg_phone,
      </if>
      <if test="orderStatusTime != null">
        order_status_time,
      </if>
      <if test="custCode != null">
        cust_code,
      </if>
      <if test="custUserId != null">
        cust_user_id,
      </if>
      <if test="custName != null">
        cust_name,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="regionId != null">
        region_ID,
      </if>
      <if test="orderOrgBizCode != null">
        order_org_biz_code,
      </if>
      <if test="orgLevel != null">
        org_level,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="provinceOrgName != null">
        province_org_name,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
      <if test="bookid != null">
        bookId,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="totalPrice != null">
        total_price,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="contactPersonName != null">
        contact_person_name,
      </if>
      <if test="contactPhone != null">
        contact_phone,
      </if>
      <if test="addr1 != null">
        addr1,
      </if>
      <if test="addr2 != null">
        addr2,
      </if>
      <if test="addr3 != null">
        addr3,
      </if>
      <if test="addr4 != null">
        addr4,
      </if>
      <if test="usaddr != null">
        usaddr,
      </if>
      <if test="henanRealName != null">
        henan_real_name,
      </if>
      <if test="henanRealPhone != null">
        henan_real_phone,
      </if>
      <if test="henanRealAddress != null">
        henan_real_address,
      </if>
      <if test="spuOfferingClass != null">
        spu_offering_class,
      </if>
      <if test="spuOfferingCode != null">
        spu_offering_code,
      </if>
      <if test="supplierCode != null">
        supplier_code,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="deductPrice != null">
        deduct_price,
      </if>
      <if test="orderingChannelSource != null">
        ordering_channel_source,
      </if>
      <if test="orderingChannelName != null">
        ordering_channel_name,
      </if>
      <if test="ssoTerminalType != null">
        sso_terminal_type,
      </if>
      <if test="toK3 != null">
        to_k3,
      </if>
      <if test="specialAfterMarketHandle != null">
        special_after_market_handle,
      </if>
      <if test="specialAfterStatus != null">
        special_after_status,
      </if>
      <if test="specialAfterStatusTime != null">
        special_after_status_time,
      </if>
      <if test="specialAfterLatestTime != null">
        special_after_latest_time,
      </if>
      <if test="effectiveRules != null">
        effective_rules,
      </if>
      <if test="effectiveTime != null">
        effective_time,
      </if>
      <if test="syncK3Id != null">
        sync_k3_id,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="refundTime != null">
        refund_time,
      </if>
      <if test="valetOrderCompleteTime != null">
        valet_order_complete_time,
      </if>
      <if test="billLadderType != null">
        bill_ladder_type,
      </if>
      <if test="qlyStatus != null">
        qly_status,
      </if>
      <if test="ysxStatus != null">
        ysx_status,
      </if>
      <if test="kxRefundStatus != null">
        kx_refund_status,
      </if>
      <if test="kxRefundReason != null">
        kx_refund_reason,
      </if>
      <if test="spuOfferingVersion != null">
        spu_offering_version,
      </if>
      <if test="reserveBeId != null">
        reserve_be_id,
      </if>
      <if test="reserveLocation != null">
        reserve_location,
      </if>
      <if test="softwareOrderType != null">
        software_order_type,
      </if>
      <if test="associatedOrder != null">
        associated_order,
      </if>
      <if test="specialAfterRefundsNumber != null">
        special_after_refunds_number,
      </if>
      <if test="spuListPlatform != null">
        spu_list_platform,
      </if>
      <if test="billNoNumber != null">
        bill_no_number,
      </if>
      <if test="reminderWaitSend != null">
        reminder_wait_send,
      </if>
      <if test="reminderValetTaking != null">
        reminder_valet_taking,
      </if>
      <if test="reminderWaitDeliver != null">
        reminder_wait_deliver,
      </if>
      <if test="customerType != null">
        customer_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null">
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="createOperCode != null">
        #{createOperCode,jdbcType=VARCHAR},
      </if>
      <if test="createOperUserId != null">
        #{createOperUserId,jdbcType=VARCHAR},
      </if>
      <if test="employeeNum != null">
        #{employeeNum,jdbcType=VARCHAR},
      </if>
      <if test="custMgName != null">
        #{custMgName,jdbcType=VARCHAR},
      </if>
      <if test="custMgPhone != null">
        #{custMgPhone,jdbcType=VARCHAR},
      </if>
      <if test="orderStatusTime != null">
        #{orderStatusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="custCode != null">
        #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="custUserId != null">
        #{custUserId,jdbcType=VARCHAR},
      </if>
      <if test="custName != null">
        #{custName,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null">
        #{regionId,jdbcType=VARCHAR},
      </if>
      <if test="orderOrgBizCode != null">
        #{orderOrgBizCode,jdbcType=VARCHAR},
      </if>
      <if test="orgLevel != null">
        #{orgLevel,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="provinceOrgName != null">
        #{provinceOrgName,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="bookid != null">
        #{bookid,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null">
        #{totalPrice,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="contactPersonName != null">
        #{contactPersonName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="addr1 != null">
        #{addr1,jdbcType=VARCHAR},
      </if>
      <if test="addr2 != null">
        #{addr2,jdbcType=VARCHAR},
      </if>
      <if test="addr3 != null">
        #{addr3,jdbcType=VARCHAR},
      </if>
      <if test="addr4 != null">
        #{addr4,jdbcType=VARCHAR},
      </if>
      <if test="usaddr != null">
        #{usaddr,jdbcType=VARCHAR},
      </if>
      <if test="henanRealName != null">
        #{henanRealName,jdbcType=VARCHAR},
      </if>
      <if test="henanRealPhone != null">
        #{henanRealPhone,jdbcType=VARCHAR},
      </if>
      <if test="henanRealAddress != null">
        #{henanRealAddress,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingClass != null">
        #{spuOfferingClass,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingCode != null">
        #{spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="deductPrice != null">
        #{deductPrice,jdbcType=VARCHAR},
      </if>
      <if test="orderingChannelSource != null">
        #{orderingChannelSource,jdbcType=VARCHAR},
      </if>
      <if test="orderingChannelName != null">
        #{orderingChannelName,jdbcType=VARCHAR},
      </if>
      <if test="ssoTerminalType != null">
        #{ssoTerminalType,jdbcType=VARCHAR},
      </if>
      <if test="toK3 != null">
        #{toK3,jdbcType=INTEGER},
      </if>
      <if test="specialAfterMarketHandle != null">
        #{specialAfterMarketHandle,jdbcType=INTEGER},
      </if>
      <if test="specialAfterStatus != null">
        #{specialAfterStatus,jdbcType=VARCHAR},
      </if>
      <if test="specialAfterStatusTime != null">
        #{specialAfterStatusTime,jdbcType=VARCHAR},
      </if>
      <if test="specialAfterLatestTime != null">
        #{specialAfterLatestTime,jdbcType=VARCHAR},
      </if>
      <if test="effectiveRules != null">
        #{effectiveRules,jdbcType=VARCHAR},
      </if>
      <if test="effectiveTime != null">
        #{effectiveTime,jdbcType=VARCHAR},
      </if>
      <if test="syncK3Id != null">
        #{syncK3Id,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundTime != null">
        #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valetOrderCompleteTime != null">
        #{valetOrderCompleteTime,jdbcType=VARCHAR},
      </if>
      <if test="billLadderType != null">
        #{billLadderType,jdbcType=VARCHAR},
      </if>
      <if test="qlyStatus != null">
        #{qlyStatus,jdbcType=INTEGER},
      </if>
      <if test="ysxStatus != null">
        #{ysxStatus,jdbcType=INTEGER},
      </if>
      <if test="kxRefundStatus != null">
        #{kxRefundStatus,jdbcType=INTEGER},
      </if>
      <if test="kxRefundReason != null">
        #{kxRefundReason,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingVersion != null">
        #{spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="reserveBeId != null">
        #{reserveBeId,jdbcType=VARCHAR},
      </if>
      <if test="reserveLocation != null">
        #{reserveLocation,jdbcType=VARCHAR},
      </if>
      <if test="softwareOrderType != null">
        #{softwareOrderType,jdbcType=VARCHAR},
      </if>
      <if test="associatedOrder != null">
        #{associatedOrder,jdbcType=VARCHAR},
      </if>
      <if test="specialAfterRefundsNumber != null">
        #{specialAfterRefundsNumber,jdbcType=VARCHAR},
      </if>
      <if test="spuListPlatform != null">
        #{spuListPlatform,jdbcType=VARCHAR},
      </if>
      <if test="billNoNumber != null">
        #{billNoNumber,jdbcType=VARCHAR},
      </if>
      <if test="reminderWaitSend != null">
        #{reminderWaitSend,jdbcType=INTEGER},
      </if>
      <if test="reminderValetTaking != null">
        #{reminderValetTaking,jdbcType=INTEGER},
      </if>
      <if test="reminderWaitDeliver != null">
        #{reminderWaitDeliver,jdbcType=INTEGER},
      </if>
      <if test="customerType != null">
        #{customerType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Jun 09 09:29:34 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from order_2c_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Jun 09 09:29:34 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_2c_info
    <set>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=VARCHAR},
      </if>
      <if test="record.businessCode != null">
        business_code = #{record.businessCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createOperCode != null">
        create_oper_code = #{record.createOperCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createOperUserId != null">
        create_oper_user_id = #{record.createOperUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.employeeNum != null">
        employee_num = #{record.employeeNum,jdbcType=VARCHAR},
      </if>
      <if test="record.custMgName != null">
        cust_mg_name = #{record.custMgName,jdbcType=VARCHAR},
      </if>
      <if test="record.custMgPhone != null">
        cust_mg_phone = #{record.custMgPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.orderStatusTime != null">
        order_status_time = #{record.orderStatusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.custCode != null">
        cust_code = #{record.custCode,jdbcType=VARCHAR},
      </if>
      <if test="record.custUserId != null">
        cust_user_id = #{record.custUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.custName != null">
        cust_name = #{record.custName,jdbcType=VARCHAR},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.location != null">
        location = #{record.location,jdbcType=VARCHAR},
      </if>
      <if test="record.regionId != null">
        region_ID = #{record.regionId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderOrgBizCode != null">
        order_org_biz_code = #{record.orderOrgBizCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgLevel != null">
        org_level = #{record.orgLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceOrgName != null">
        province_org_name = #{record.provinceOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.remarks != null">
        remarks = #{record.remarks,jdbcType=VARCHAR},
      </if>
      <if test="record.bookid != null">
        bookId = #{record.bookid,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.totalPrice != null">
        total_price = #{record.totalPrice,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPersonName != null">
        contact_person_name = #{record.contactPersonName,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPhone != null">
        contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.addr1 != null">
        addr1 = #{record.addr1,jdbcType=VARCHAR},
      </if>
      <if test="record.addr2 != null">
        addr2 = #{record.addr2,jdbcType=VARCHAR},
      </if>
      <if test="record.addr3 != null">
        addr3 = #{record.addr3,jdbcType=VARCHAR},
      </if>
      <if test="record.addr4 != null">
        addr4 = #{record.addr4,jdbcType=VARCHAR},
      </if>
      <if test="record.usaddr != null">
        usaddr = #{record.usaddr,jdbcType=VARCHAR},
      </if>
      <if test="record.henanRealName != null">
        henan_real_name = #{record.henanRealName,jdbcType=VARCHAR},
      </if>
      <if test="record.henanRealPhone != null">
        henan_real_phone = #{record.henanRealPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.henanRealAddress != null">
        henan_real_address = #{record.henanRealAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.spuOfferingClass != null">
        spu_offering_class = #{record.spuOfferingClass,jdbcType=VARCHAR},
      </if>
      <if test="record.spuOfferingCode != null">
        spu_offering_code = #{record.spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierCode != null">
        supplier_code = #{record.supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderStatus != null">
        order_status = #{record.orderStatus,jdbcType=INTEGER},
      </if>
      <if test="record.deductPrice != null">
        deduct_price = #{record.deductPrice,jdbcType=VARCHAR},
      </if>
      <if test="record.orderingChannelSource != null">
        ordering_channel_source = #{record.orderingChannelSource,jdbcType=VARCHAR},
      </if>
      <if test="record.orderingChannelName != null">
        ordering_channel_name = #{record.orderingChannelName,jdbcType=VARCHAR},
      </if>
      <if test="record.ssoTerminalType != null">
        sso_terminal_type = #{record.ssoTerminalType,jdbcType=VARCHAR},
      </if>
      <if test="record.toK3 != null">
        to_k3 = #{record.toK3,jdbcType=INTEGER},
      </if>
      <if test="record.specialAfterMarketHandle != null">
        special_after_market_handle = #{record.specialAfterMarketHandle,jdbcType=INTEGER},
      </if>
      <if test="record.specialAfterStatus != null">
        special_after_status = #{record.specialAfterStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.specialAfterStatusTime != null">
        special_after_status_time = #{record.specialAfterStatusTime,jdbcType=VARCHAR},
      </if>
      <if test="record.specialAfterLatestTime != null">
        special_after_latest_time = #{record.specialAfterLatestTime,jdbcType=VARCHAR},
      </if>
      <if test="record.effectiveRules != null">
        effective_rules = #{record.effectiveRules,jdbcType=VARCHAR},
      </if>
      <if test="record.effectiveTime != null">
        effective_time = #{record.effectiveTime,jdbcType=VARCHAR},
      </if>
      <if test="record.syncK3Id != null">
        sync_k3_id = #{record.syncK3Id,jdbcType=VARCHAR},
      </if>
      <if test="record.payTime != null">
        pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.refundTime != null">
        refund_time = #{record.refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.valetOrderCompleteTime != null">
        valet_order_complete_time = #{record.valetOrderCompleteTime,jdbcType=VARCHAR},
      </if>
      <if test="record.billLadderType != null">
        bill_ladder_type = #{record.billLadderType,jdbcType=VARCHAR},
      </if>
      <if test="record.qlyStatus != null">
        qly_status = #{record.qlyStatus,jdbcType=INTEGER},
      </if>
      <if test="record.ysxStatus != null">
        ysx_status = #{record.ysxStatus,jdbcType=INTEGER},
      </if>
      <if test="record.kxRefundStatus != null">
        kx_refund_status = #{record.kxRefundStatus,jdbcType=INTEGER},
      </if>
      <if test="record.kxRefundReason != null">
        kx_refund_reason = #{record.kxRefundReason,jdbcType=VARCHAR},
      </if>
      <if test="record.spuOfferingVersion != null">
        spu_offering_version = #{record.spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveBeId != null">
        reserve_be_id = #{record.reserveBeId,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveLocation != null">
        reserve_location = #{record.reserveLocation,jdbcType=VARCHAR},
      </if>
      <if test="record.softwareOrderType != null">
        software_order_type = #{record.softwareOrderType,jdbcType=VARCHAR},
      </if>
      <if test="record.associatedOrder != null">
        associated_order = #{record.associatedOrder,jdbcType=VARCHAR},
      </if>
      <if test="record.specialAfterRefundsNumber != null">
        special_after_refunds_number = #{record.specialAfterRefundsNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.spuListPlatform != null">
        spu_list_platform = #{record.spuListPlatform,jdbcType=VARCHAR},
      </if>
      <if test="record.billNoNumber != null">
        bill_no_number = #{record.billNoNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.reminderWaitSend != null">
        reminder_wait_send = #{record.reminderWaitSend,jdbcType=INTEGER},
      </if>
      <if test="record.reminderValetTaking != null">
        reminder_valet_taking = #{record.reminderValetTaking,jdbcType=INTEGER},
      </if>
      <if test="record.reminderWaitDeliver != null">
        reminder_wait_deliver = #{record.reminderWaitDeliver,jdbcType=INTEGER},
      </if>
      <if test="record.customerType != null">
        customer_type = #{record.customerType,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Jun 09 09:29:34 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_2c_info
    set order_id = #{record.orderId,jdbcType=VARCHAR},
      order_type = #{record.orderType,jdbcType=VARCHAR},
      business_code = #{record.businessCode,jdbcType=VARCHAR},
      create_oper_code = #{record.createOperCode,jdbcType=VARCHAR},
      create_oper_user_id = #{record.createOperUserId,jdbcType=VARCHAR},
      employee_num = #{record.employeeNum,jdbcType=VARCHAR},
      cust_mg_name = #{record.custMgName,jdbcType=VARCHAR},
      cust_mg_phone = #{record.custMgPhone,jdbcType=VARCHAR},
      order_status_time = #{record.orderStatusTime,jdbcType=TIMESTAMP},
      cust_code = #{record.custCode,jdbcType=VARCHAR},
      cust_user_id = #{record.custUserId,jdbcType=VARCHAR},
      cust_name = #{record.custName,jdbcType=VARCHAR},
      be_id = #{record.beId,jdbcType=VARCHAR},
      location = #{record.location,jdbcType=VARCHAR},
      region_ID = #{record.regionId,jdbcType=VARCHAR},
      order_org_biz_code = #{record.orderOrgBizCode,jdbcType=VARCHAR},
      org_level = #{record.orgLevel,jdbcType=VARCHAR},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      province_org_name = #{record.provinceOrgName,jdbcType=VARCHAR},
      remarks = #{record.remarks,jdbcType=VARCHAR},
      bookId = #{record.bookid,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      total_price = #{record.totalPrice,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=VARCHAR},
      contact_person_name = #{record.contactPersonName,jdbcType=VARCHAR},
      contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      addr1 = #{record.addr1,jdbcType=VARCHAR},
      addr2 = #{record.addr2,jdbcType=VARCHAR},
      addr3 = #{record.addr3,jdbcType=VARCHAR},
      addr4 = #{record.addr4,jdbcType=VARCHAR},
      usaddr = #{record.usaddr,jdbcType=VARCHAR},
      henan_real_name = #{record.henanRealName,jdbcType=VARCHAR},
      henan_real_phone = #{record.henanRealPhone,jdbcType=VARCHAR},
      henan_real_address = #{record.henanRealAddress,jdbcType=VARCHAR},
      spu_offering_class = #{record.spuOfferingClass,jdbcType=VARCHAR},
      spu_offering_code = #{record.spuOfferingCode,jdbcType=VARCHAR},
      supplier_code = #{record.supplierCode,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      order_status = #{record.orderStatus,jdbcType=INTEGER},
      deduct_price = #{record.deductPrice,jdbcType=VARCHAR},
      ordering_channel_source = #{record.orderingChannelSource,jdbcType=VARCHAR},
      ordering_channel_name = #{record.orderingChannelName,jdbcType=VARCHAR},
      sso_terminal_type = #{record.ssoTerminalType,jdbcType=VARCHAR},
      to_k3 = #{record.toK3,jdbcType=INTEGER},
      special_after_market_handle = #{record.specialAfterMarketHandle,jdbcType=INTEGER},
      special_after_status = #{record.specialAfterStatus,jdbcType=VARCHAR},
      special_after_status_time = #{record.specialAfterStatusTime,jdbcType=VARCHAR},
      special_after_latest_time = #{record.specialAfterLatestTime,jdbcType=VARCHAR},
      effective_rules = #{record.effectiveRules,jdbcType=VARCHAR},
      effective_time = #{record.effectiveTime,jdbcType=VARCHAR},
      sync_k3_id = #{record.syncK3Id,jdbcType=VARCHAR},
      pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      refund_time = #{record.refundTime,jdbcType=TIMESTAMP},
      valet_order_complete_time = #{record.valetOrderCompleteTime,jdbcType=VARCHAR},
      bill_ladder_type = #{record.billLadderType,jdbcType=VARCHAR},
      qly_status = #{record.qlyStatus,jdbcType=INTEGER},
      ysx_status = #{record.ysxStatus,jdbcType=INTEGER},
      kx_refund_status = #{record.kxRefundStatus,jdbcType=INTEGER},
      kx_refund_reason = #{record.kxRefundReason,jdbcType=VARCHAR},
      spu_offering_version = #{record.spuOfferingVersion,jdbcType=VARCHAR},
      reserve_be_id = #{record.reserveBeId,jdbcType=VARCHAR},
      reserve_location = #{record.reserveLocation,jdbcType=VARCHAR},
      software_order_type = #{record.softwareOrderType,jdbcType=VARCHAR},
      associated_order = #{record.associatedOrder,jdbcType=VARCHAR},
      special_after_refunds_number = #{record.specialAfterRefundsNumber,jdbcType=VARCHAR},
      spu_list_platform = #{record.spuListPlatform,jdbcType=VARCHAR},
      bill_no_number = #{record.billNoNumber,jdbcType=VARCHAR},
      reminder_wait_send = #{record.reminderWaitSend,jdbcType=INTEGER},
      reminder_valet_taking = #{record.reminderValetTaking,jdbcType=INTEGER},
      reminder_wait_deliver = #{record.reminderWaitDeliver,jdbcType=INTEGER},
      customer_type = #{record.customerType,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.Order2cInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Jun 09 09:29:34 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_2c_info
    <set>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="businessCode != null">
        business_code = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="createOperCode != null">
        create_oper_code = #{createOperCode,jdbcType=VARCHAR},
      </if>
      <if test="createOperUserId != null">
        create_oper_user_id = #{createOperUserId,jdbcType=VARCHAR},
      </if>
      <if test="employeeNum != null">
        employee_num = #{employeeNum,jdbcType=VARCHAR},
      </if>
      <if test="custMgName != null">
        cust_mg_name = #{custMgName,jdbcType=VARCHAR},
      </if>
      <if test="custMgPhone != null">
        cust_mg_phone = #{custMgPhone,jdbcType=VARCHAR},
      </if>
      <if test="orderStatusTime != null">
        order_status_time = #{orderStatusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="custCode != null">
        cust_code = #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="custUserId != null">
        cust_user_id = #{custUserId,jdbcType=VARCHAR},
      </if>
      <if test="custName != null">
        cust_name = #{custName,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null">
        region_ID = #{regionId,jdbcType=VARCHAR},
      </if>
      <if test="orderOrgBizCode != null">
        order_org_biz_code = #{orderOrgBizCode,jdbcType=VARCHAR},
      </if>
      <if test="orgLevel != null">
        org_level = #{orgLevel,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="provinceOrgName != null">
        province_org_name = #{provinceOrgName,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="bookid != null">
        bookId = #{bookid,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="totalPrice != null">
        total_price = #{totalPrice,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="contactPersonName != null">
        contact_person_name = #{contactPersonName,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="addr1 != null">
        addr1 = #{addr1,jdbcType=VARCHAR},
      </if>
      <if test="addr2 != null">
        addr2 = #{addr2,jdbcType=VARCHAR},
      </if>
      <if test="addr3 != null">
        addr3 = #{addr3,jdbcType=VARCHAR},
      </if>
      <if test="addr4 != null">
        addr4 = #{addr4,jdbcType=VARCHAR},
      </if>
      <if test="usaddr != null">
        usaddr = #{usaddr,jdbcType=VARCHAR},
      </if>
      <if test="henanRealName != null">
        henan_real_name = #{henanRealName,jdbcType=VARCHAR},
      </if>
      <if test="henanRealPhone != null">
        henan_real_phone = #{henanRealPhone,jdbcType=VARCHAR},
      </if>
      <if test="henanRealAddress != null">
        henan_real_address = #{henanRealAddress,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingClass != null">
        spu_offering_class = #{spuOfferingClass,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingCode != null">
        spu_offering_code = #{spuOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        supplier_code = #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="deductPrice != null">
        deduct_price = #{deductPrice,jdbcType=VARCHAR},
      </if>
      <if test="orderingChannelSource != null">
        ordering_channel_source = #{orderingChannelSource,jdbcType=VARCHAR},
      </if>
      <if test="orderingChannelName != null">
        ordering_channel_name = #{orderingChannelName,jdbcType=VARCHAR},
      </if>
      <if test="ssoTerminalType != null">
        sso_terminal_type = #{ssoTerminalType,jdbcType=VARCHAR},
      </if>
      <if test="toK3 != null">
        to_k3 = #{toK3,jdbcType=INTEGER},
      </if>
      <if test="specialAfterMarketHandle != null">
        special_after_market_handle = #{specialAfterMarketHandle,jdbcType=INTEGER},
      </if>
      <if test="specialAfterStatus != null">
        special_after_status = #{specialAfterStatus,jdbcType=VARCHAR},
      </if>
      <if test="specialAfterStatusTime != null">
        special_after_status_time = #{specialAfterStatusTime,jdbcType=VARCHAR},
      </if>
      <if test="specialAfterLatestTime != null">
        special_after_latest_time = #{specialAfterLatestTime,jdbcType=VARCHAR},
      </if>
      <if test="effectiveRules != null">
        effective_rules = #{effectiveRules,jdbcType=VARCHAR},
      </if>
      <if test="effectiveTime != null">
        effective_time = #{effectiveTime,jdbcType=VARCHAR},
      </if>
      <if test="syncK3Id != null">
        sync_k3_id = #{syncK3Id,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundTime != null">
        refund_time = #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valetOrderCompleteTime != null">
        valet_order_complete_time = #{valetOrderCompleteTime,jdbcType=VARCHAR},
      </if>
      <if test="billLadderType != null">
        bill_ladder_type = #{billLadderType,jdbcType=VARCHAR},
      </if>
      <if test="qlyStatus != null">
        qly_status = #{qlyStatus,jdbcType=INTEGER},
      </if>
      <if test="ysxStatus != null">
        ysx_status = #{ysxStatus,jdbcType=INTEGER},
      </if>
      <if test="kxRefundStatus != null">
        kx_refund_status = #{kxRefundStatus,jdbcType=INTEGER},
      </if>
      <if test="kxRefundReason != null">
        kx_refund_reason = #{kxRefundReason,jdbcType=VARCHAR},
      </if>
      <if test="spuOfferingVersion != null">
        spu_offering_version = #{spuOfferingVersion,jdbcType=VARCHAR},
      </if>
      <if test="reserveBeId != null">
        reserve_be_id = #{reserveBeId,jdbcType=VARCHAR},
      </if>
      <if test="reserveLocation != null">
        reserve_location = #{reserveLocation,jdbcType=VARCHAR},
      </if>
      <if test="softwareOrderType != null">
        software_order_type = #{softwareOrderType,jdbcType=VARCHAR},
      </if>
      <if test="associatedOrder != null">
        associated_order = #{associatedOrder,jdbcType=VARCHAR},
      </if>
      <if test="specialAfterRefundsNumber != null">
        special_after_refunds_number = #{specialAfterRefundsNumber,jdbcType=VARCHAR},
      </if>
      <if test="spuListPlatform != null">
        spu_list_platform = #{spuListPlatform,jdbcType=VARCHAR},
      </if>
      <if test="billNoNumber != null">
        bill_no_number = #{billNoNumber,jdbcType=VARCHAR},
      </if>
      <if test="reminderWaitSend != null">
        reminder_wait_send = #{reminderWaitSend,jdbcType=INTEGER},
      </if>
      <if test="reminderValetTaking != null">
        reminder_valet_taking = #{reminderValetTaking,jdbcType=INTEGER},
      </if>
      <if test="reminderWaitDeliver != null">
        reminder_wait_deliver = #{reminderWaitDeliver,jdbcType=INTEGER},
      </if>
      <if test="customerType != null">
        customer_type = #{customerType,jdbcType=VARCHAR},
      </if>
    </set>
    where order_id = #{orderId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.Order2cInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Jun 09 09:29:34 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_2c_info
    set order_type = #{orderType,jdbcType=VARCHAR},
      business_code = #{businessCode,jdbcType=VARCHAR},
      create_oper_code = #{createOperCode,jdbcType=VARCHAR},
      create_oper_user_id = #{createOperUserId,jdbcType=VARCHAR},
      employee_num = #{employeeNum,jdbcType=VARCHAR},
      cust_mg_name = #{custMgName,jdbcType=VARCHAR},
      cust_mg_phone = #{custMgPhone,jdbcType=VARCHAR},
      order_status_time = #{orderStatusTime,jdbcType=TIMESTAMP},
      cust_code = #{custCode,jdbcType=VARCHAR},
      cust_user_id = #{custUserId,jdbcType=VARCHAR},
      cust_name = #{custName,jdbcType=VARCHAR},
      be_id = #{beId,jdbcType=VARCHAR},
      location = #{location,jdbcType=VARCHAR},
      region_ID = #{regionId,jdbcType=VARCHAR},
      order_org_biz_code = #{orderOrgBizCode,jdbcType=VARCHAR},
      org_level = #{orgLevel,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      province_org_name = #{provinceOrgName,jdbcType=VARCHAR},
      remarks = #{remarks,jdbcType=VARCHAR},
      bookId = #{bookid,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      total_price = #{totalPrice,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=VARCHAR},
      contact_person_name = #{contactPersonName,jdbcType=VARCHAR},
      contact_phone = #{contactPhone,jdbcType=VARCHAR},
      addr1 = #{addr1,jdbcType=VARCHAR},
      addr2 = #{addr2,jdbcType=VARCHAR},
      addr3 = #{addr3,jdbcType=VARCHAR},
      addr4 = #{addr4,jdbcType=VARCHAR},
      usaddr = #{usaddr,jdbcType=VARCHAR},
      henan_real_name = #{henanRealName,jdbcType=VARCHAR},
      henan_real_phone = #{henanRealPhone,jdbcType=VARCHAR},
      henan_real_address = #{henanRealAddress,jdbcType=VARCHAR},
      spu_offering_class = #{spuOfferingClass,jdbcType=VARCHAR},
      spu_offering_code = #{spuOfferingCode,jdbcType=VARCHAR},
      supplier_code = #{supplierCode,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_status = #{orderStatus,jdbcType=INTEGER},
      deduct_price = #{deductPrice,jdbcType=VARCHAR},
      ordering_channel_source = #{orderingChannelSource,jdbcType=VARCHAR},
      ordering_channel_name = #{orderingChannelName,jdbcType=VARCHAR},
      sso_terminal_type = #{ssoTerminalType,jdbcType=VARCHAR},
      to_k3 = #{toK3,jdbcType=INTEGER},
      special_after_market_handle = #{specialAfterMarketHandle,jdbcType=INTEGER},
      special_after_status = #{specialAfterStatus,jdbcType=VARCHAR},
      special_after_status_time = #{specialAfterStatusTime,jdbcType=VARCHAR},
      special_after_latest_time = #{specialAfterLatestTime,jdbcType=VARCHAR},
      effective_rules = #{effectiveRules,jdbcType=VARCHAR},
      effective_time = #{effectiveTime,jdbcType=VARCHAR},
      sync_k3_id = #{syncK3Id,jdbcType=VARCHAR},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      refund_time = #{refundTime,jdbcType=TIMESTAMP},
      valet_order_complete_time = #{valetOrderCompleteTime,jdbcType=VARCHAR},
      bill_ladder_type = #{billLadderType,jdbcType=VARCHAR},
      qly_status = #{qlyStatus,jdbcType=INTEGER},
      ysx_status = #{ysxStatus,jdbcType=INTEGER},
      kx_refund_status = #{kxRefundStatus,jdbcType=INTEGER},
      kx_refund_reason = #{kxRefundReason,jdbcType=VARCHAR},
      spu_offering_version = #{spuOfferingVersion,jdbcType=VARCHAR},
      reserve_be_id = #{reserveBeId,jdbcType=VARCHAR},
      reserve_location = #{reserveLocation,jdbcType=VARCHAR},
      software_order_type = #{softwareOrderType,jdbcType=VARCHAR},
      associated_order = #{associatedOrder,jdbcType=VARCHAR},
      special_after_refunds_number = #{specialAfterRefundsNumber,jdbcType=VARCHAR},
      spu_list_platform = #{spuListPlatform,jdbcType=VARCHAR},
      bill_no_number = #{billNoNumber,jdbcType=VARCHAR},
      reminder_wait_send = #{reminderWaitSend,jdbcType=INTEGER},
      reminder_valet_taking = #{reminderValetTaking,jdbcType=INTEGER},
      reminder_wait_deliver = #{reminderWaitDeliver,jdbcType=INTEGER},
      customer_type = #{customerType,jdbcType=VARCHAR}
    where order_id = #{orderId,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Jun 09 09:29:34 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_info
    (order_id, order_type, business_code, create_oper_code, create_oper_user_id, employee_num, 
      cust_mg_name, cust_mg_phone, order_status_time, cust_code, cust_user_id, cust_name, 
      be_id, location, region_ID, order_org_biz_code, org_level, org_name, province_org_name, 
      remarks, bookId, status, total_price, create_time, contact_person_name, contact_phone, 
      addr1, addr2, addr3, addr4, usaddr, henan_real_name, henan_real_phone, henan_real_address, 
      spu_offering_class, spu_offering_code, supplier_code, update_time, order_status, 
      deduct_price, ordering_channel_source, ordering_channel_name, sso_terminal_type, 
      to_k3, special_after_market_handle, special_after_status, special_after_status_time, 
      special_after_latest_time, effective_rules, effective_time, sync_k3_id, pay_time, 
      refund_time, valet_order_complete_time, bill_ladder_type, qly_status, ysx_status, 
      kx_refund_status, kx_refund_reason, spu_offering_version, reserve_be_id, reserve_location, 
      software_order_type, associated_order, special_after_refunds_number, spu_list_platform, 
      bill_no_number, reminder_wait_send, reminder_valet_taking, reminder_wait_deliver, 
      customer_type)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=VARCHAR}, #{item.orderType,jdbcType=VARCHAR}, #{item.businessCode,jdbcType=VARCHAR}, 
        #{item.createOperCode,jdbcType=VARCHAR}, #{item.createOperUserId,jdbcType=VARCHAR}, 
        #{item.employeeNum,jdbcType=VARCHAR}, #{item.custMgName,jdbcType=VARCHAR}, #{item.custMgPhone,jdbcType=VARCHAR}, 
        #{item.orderStatusTime,jdbcType=TIMESTAMP}, #{item.custCode,jdbcType=VARCHAR}, 
        #{item.custUserId,jdbcType=VARCHAR}, #{item.custName,jdbcType=VARCHAR}, #{item.beId,jdbcType=VARCHAR}, 
        #{item.location,jdbcType=VARCHAR}, #{item.regionId,jdbcType=VARCHAR}, #{item.orderOrgBizCode,jdbcType=VARCHAR}, 
        #{item.orgLevel,jdbcType=VARCHAR}, #{item.orgName,jdbcType=VARCHAR}, #{item.provinceOrgName,jdbcType=VARCHAR}, 
        #{item.remarks,jdbcType=VARCHAR}, #{item.bookid,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, 
        #{item.totalPrice,jdbcType=VARCHAR}, #{item.createTime,jdbcType=VARCHAR}, #{item.contactPersonName,jdbcType=VARCHAR}, 
        #{item.contactPhone,jdbcType=VARCHAR}, #{item.addr1,jdbcType=VARCHAR}, #{item.addr2,jdbcType=VARCHAR}, 
        #{item.addr3,jdbcType=VARCHAR}, #{item.addr4,jdbcType=VARCHAR}, #{item.usaddr,jdbcType=VARCHAR}, 
        #{item.henanRealName,jdbcType=VARCHAR}, #{item.henanRealPhone,jdbcType=VARCHAR}, 
        #{item.henanRealAddress,jdbcType=VARCHAR}, #{item.spuOfferingClass,jdbcType=VARCHAR}, 
        #{item.spuOfferingCode,jdbcType=VARCHAR}, #{item.supplierCode,jdbcType=VARCHAR}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.orderStatus,jdbcType=INTEGER}, #{item.deductPrice,jdbcType=VARCHAR}, 
        #{item.orderingChannelSource,jdbcType=VARCHAR}, #{item.orderingChannelName,jdbcType=VARCHAR}, 
        #{item.ssoTerminalType,jdbcType=VARCHAR}, #{item.toK3,jdbcType=INTEGER}, #{item.specialAfterMarketHandle,jdbcType=INTEGER}, 
        #{item.specialAfterStatus,jdbcType=VARCHAR}, #{item.specialAfterStatusTime,jdbcType=VARCHAR}, 
        #{item.specialAfterLatestTime,jdbcType=VARCHAR}, #{item.effectiveRules,jdbcType=VARCHAR}, 
        #{item.effectiveTime,jdbcType=VARCHAR}, #{item.syncK3Id,jdbcType=VARCHAR}, #{item.payTime,jdbcType=TIMESTAMP}, 
        #{item.refundTime,jdbcType=TIMESTAMP}, #{item.valetOrderCompleteTime,jdbcType=VARCHAR}, 
        #{item.billLadderType,jdbcType=VARCHAR}, #{item.qlyStatus,jdbcType=INTEGER}, #{item.ysxStatus,jdbcType=INTEGER}, 
        #{item.kxRefundStatus,jdbcType=INTEGER}, #{item.kxRefundReason,jdbcType=VARCHAR}, 
        #{item.spuOfferingVersion,jdbcType=VARCHAR}, #{item.reserveBeId,jdbcType=VARCHAR}, 
        #{item.reserveLocation,jdbcType=VARCHAR}, #{item.softwareOrderType,jdbcType=VARCHAR}, 
        #{item.associatedOrder,jdbcType=VARCHAR}, #{item.specialAfterRefundsNumber,jdbcType=VARCHAR}, 
        #{item.spuListPlatform,jdbcType=VARCHAR}, #{item.billNoNumber,jdbcType=VARCHAR}, 
        #{item.reminderWaitSend,jdbcType=INTEGER}, #{item.reminderValetTaking,jdbcType=INTEGER}, 
        #{item.reminderWaitDeliver,jdbcType=INTEGER}, #{item.customerType,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Jun 09 09:29:34 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'order_type'.toString() == column.value">
          #{item.orderType,jdbcType=VARCHAR}
        </if>
        <if test="'business_code'.toString() == column.value">
          #{item.businessCode,jdbcType=VARCHAR}
        </if>
        <if test="'create_oper_code'.toString() == column.value">
          #{item.createOperCode,jdbcType=VARCHAR}
        </if>
        <if test="'create_oper_user_id'.toString() == column.value">
          #{item.createOperUserId,jdbcType=VARCHAR}
        </if>
        <if test="'employee_num'.toString() == column.value">
          #{item.employeeNum,jdbcType=VARCHAR}
        </if>
        <if test="'cust_mg_name'.toString() == column.value">
          #{item.custMgName,jdbcType=VARCHAR}
        </if>
        <if test="'cust_mg_phone'.toString() == column.value">
          #{item.custMgPhone,jdbcType=VARCHAR}
        </if>
        <if test="'order_status_time'.toString() == column.value">
          #{item.orderStatusTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'cust_code'.toString() == column.value">
          #{item.custCode,jdbcType=VARCHAR}
        </if>
        <if test="'cust_user_id'.toString() == column.value">
          #{item.custUserId,jdbcType=VARCHAR}
        </if>
        <if test="'cust_name'.toString() == column.value">
          #{item.custName,jdbcType=VARCHAR}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'location'.toString() == column.value">
          #{item.location,jdbcType=VARCHAR}
        </if>
        <if test="'region_ID'.toString() == column.value">
          #{item.regionId,jdbcType=VARCHAR}
        </if>
        <if test="'order_org_biz_code'.toString() == column.value">
          #{item.orderOrgBizCode,jdbcType=VARCHAR}
        </if>
        <if test="'org_level'.toString() == column.value">
          #{item.orgLevel,jdbcType=VARCHAR}
        </if>
        <if test="'org_name'.toString() == column.value">
          #{item.orgName,jdbcType=VARCHAR}
        </if>
        <if test="'province_org_name'.toString() == column.value">
          #{item.provinceOrgName,jdbcType=VARCHAR}
        </if>
        <if test="'remarks'.toString() == column.value">
          #{item.remarks,jdbcType=VARCHAR}
        </if>
        <if test="'bookId'.toString() == column.value">
          #{item.bookid,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'total_price'.toString() == column.value">
          #{item.totalPrice,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=VARCHAR}
        </if>
        <if test="'contact_person_name'.toString() == column.value">
          #{item.contactPersonName,jdbcType=VARCHAR}
        </if>
        <if test="'contact_phone'.toString() == column.value">
          #{item.contactPhone,jdbcType=VARCHAR}
        </if>
        <if test="'addr1'.toString() == column.value">
          #{item.addr1,jdbcType=VARCHAR}
        </if>
        <if test="'addr2'.toString() == column.value">
          #{item.addr2,jdbcType=VARCHAR}
        </if>
        <if test="'addr3'.toString() == column.value">
          #{item.addr3,jdbcType=VARCHAR}
        </if>
        <if test="'addr4'.toString() == column.value">
          #{item.addr4,jdbcType=VARCHAR}
        </if>
        <if test="'usaddr'.toString() == column.value">
          #{item.usaddr,jdbcType=VARCHAR}
        </if>
        <if test="'henan_real_name'.toString() == column.value">
          #{item.henanRealName,jdbcType=VARCHAR}
        </if>
        <if test="'henan_real_phone'.toString() == column.value">
          #{item.henanRealPhone,jdbcType=VARCHAR}
        </if>
        <if test="'henan_real_address'.toString() == column.value">
          #{item.henanRealAddress,jdbcType=VARCHAR}
        </if>
        <if test="'spu_offering_class'.toString() == column.value">
          #{item.spuOfferingClass,jdbcType=VARCHAR}
        </if>
        <if test="'spu_offering_code'.toString() == column.value">
          #{item.spuOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'supplier_code'.toString() == column.value">
          #{item.supplierCode,jdbcType=VARCHAR}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'order_status'.toString() == column.value">
          #{item.orderStatus,jdbcType=INTEGER}
        </if>
        <if test="'deduct_price'.toString() == column.value">
          #{item.deductPrice,jdbcType=VARCHAR}
        </if>
        <if test="'ordering_channel_source'.toString() == column.value">
          #{item.orderingChannelSource,jdbcType=VARCHAR}
        </if>
        <if test="'ordering_channel_name'.toString() == column.value">
          #{item.orderingChannelName,jdbcType=VARCHAR}
        </if>
        <if test="'sso_terminal_type'.toString() == column.value">
          #{item.ssoTerminalType,jdbcType=VARCHAR}
        </if>
        <if test="'to_k3'.toString() == column.value">
          #{item.toK3,jdbcType=INTEGER}
        </if>
        <if test="'special_after_market_handle'.toString() == column.value">
          #{item.specialAfterMarketHandle,jdbcType=INTEGER}
        </if>
        <if test="'special_after_status'.toString() == column.value">
          #{item.specialAfterStatus,jdbcType=VARCHAR}
        </if>
        <if test="'special_after_status_time'.toString() == column.value">
          #{item.specialAfterStatusTime,jdbcType=VARCHAR}
        </if>
        <if test="'special_after_latest_time'.toString() == column.value">
          #{item.specialAfterLatestTime,jdbcType=VARCHAR}
        </if>
        <if test="'effective_rules'.toString() == column.value">
          #{item.effectiveRules,jdbcType=VARCHAR}
        </if>
        <if test="'effective_time'.toString() == column.value">
          #{item.effectiveTime,jdbcType=VARCHAR}
        </if>
        <if test="'sync_k3_id'.toString() == column.value">
          #{item.syncK3Id,jdbcType=VARCHAR}
        </if>
        <if test="'pay_time'.toString() == column.value">
          #{item.payTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'refund_time'.toString() == column.value">
          #{item.refundTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'valet_order_complete_time'.toString() == column.value">
          #{item.valetOrderCompleteTime,jdbcType=VARCHAR}
        </if>
        <if test="'bill_ladder_type'.toString() == column.value">
          #{item.billLadderType,jdbcType=VARCHAR}
        </if>
        <if test="'qly_status'.toString() == column.value">
          #{item.qlyStatus,jdbcType=INTEGER}
        </if>
        <if test="'ysx_status'.toString() == column.value">
          #{item.ysxStatus,jdbcType=INTEGER}
        </if>
        <if test="'kx_refund_status'.toString() == column.value">
          #{item.kxRefundStatus,jdbcType=INTEGER}
        </if>
        <if test="'kx_refund_reason'.toString() == column.value">
          #{item.kxRefundReason,jdbcType=VARCHAR}
        </if>
        <if test="'spu_offering_version'.toString() == column.value">
          #{item.spuOfferingVersion,jdbcType=VARCHAR}
        </if>
        <if test="'reserve_be_id'.toString() == column.value">
          #{item.reserveBeId,jdbcType=VARCHAR}
        </if>
        <if test="'reserve_location'.toString() == column.value">
          #{item.reserveLocation,jdbcType=VARCHAR}
        </if>
        <if test="'software_order_type'.toString() == column.value">
          #{item.softwareOrderType,jdbcType=VARCHAR}
        </if>
        <if test="'associated_order'.toString() == column.value">
          #{item.associatedOrder,jdbcType=VARCHAR}
        </if>
        <if test="'special_after_refunds_number'.toString() == column.value">
          #{item.specialAfterRefundsNumber,jdbcType=VARCHAR}
        </if>
        <if test="'spu_list_platform'.toString() == column.value">
          #{item.spuListPlatform,jdbcType=VARCHAR}
        </if>
        <if test="'bill_no_number'.toString() == column.value">
          #{item.billNoNumber,jdbcType=VARCHAR}
        </if>
        <if test="'reminder_wait_send'.toString() == column.value">
          #{item.reminderWaitSend,jdbcType=INTEGER}
        </if>
        <if test="'reminder_valet_taking'.toString() == column.value">
          #{item.reminderValetTaking,jdbcType=INTEGER}
        </if>
        <if test="'reminder_wait_deliver'.toString() == column.value">
          #{item.reminderWaitDeliver,jdbcType=INTEGER}
        </if>
        <if test="'customer_type'.toString() == column.value">
          #{item.customerType,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>