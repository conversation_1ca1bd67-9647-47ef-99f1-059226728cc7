<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.AtomOfferingInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.AtomOfferingInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 09 09:37:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="spu_id" jdbcType="VARCHAR" property="spuId" />
    <result column="spu_code" jdbcType="VARCHAR" property="spuCode" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="offering_code" jdbcType="VARCHAR" property="offeringCode" />
    <result column="offering_name" jdbcType="VARCHAR" property="offeringName" />
    <result column="offering_class" jdbcType="VARCHAR" property="offeringClass" />
    <result column="quantity" jdbcType="BIGINT" property="quantity" />
    <result column="ext_soft_offering_code" jdbcType="VARCHAR" property="extSoftOfferingCode" />
    <result column="ext_hard_offering_code" jdbcType="VARCHAR" property="extHardOfferingCode" />
    <result column="settle_price" jdbcType="BIGINT" property="settlePrice" />
    <result column="charge_code" jdbcType="VARCHAR" property="chargeCode" />
    <result column="charge_id" jdbcType="VARCHAR" property="chargeId" />
    <result column="color" jdbcType="VARCHAR" property="color" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="atom_sale_price" jdbcType="BIGINT" property="atomSalePrice" />
    <result column="inventory" jdbcType="BIGINT" property="inventory" />
    <result column="reserve_inventory" jdbcType="BIGINT" property="reserveInventory" />
    <result column="is_inventory" jdbcType="BIT" property="isInventory" />
    <result column="cooperator_id" jdbcType="VARCHAR" property="cooperatorId" />
    <result column="inventory_threshold" jdbcType="BIGINT" property="inventoryThreshold" />
    <result column="is_notice" jdbcType="BIT" property="isNotice" />
    <result column="config_all_time" jdbcType="TIMESTAMP" property="configAllTime" />
    <result column="config_time" jdbcType="TIMESTAMP" property="configTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="notified" jdbcType="BIT" property="notified" />
    <result column="offeringSaleRegion" jdbcType="VARCHAR" property="offeringsaleregion" />
    <result column="settlePricePartner" jdbcType="VARCHAR" property="settlepricepartner" />
    <result column="settleServiceName" jdbcType="VARCHAR" property="settleservicename" />
    <result column="associated_offer" jdbcType="VARCHAR" property="associatedOffer" />
    <result column="validity_period" jdbcType="VARCHAR" property="validityPeriod" />
    <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime" />
    <result column="inventory_management_mode_kx" jdbcType="VARCHAR" property="inventoryManagementModeKx" />
    <result column="inventory_id" jdbcType="VARCHAR" property="inventoryId" />
    <result column="get_order_way" jdbcType="INTEGER" property="getOrderWay" />
    <result column="product_type" jdbcType="VARCHAR" property="productType" />
    <result column="special_cooperative_settlement_price" jdbcType="VARCHAR" property="specialCooperativeSettlementPrice" />
    <result column="inventory_main_id" jdbcType="VARCHAR" property="inventoryMainId" />
    <result column="card_info_inventory_main_id" jdbcType="VARCHAR" property="cardInfoInventoryMainId" />
    <result column="card_containing_terminal" jdbcType="VARCHAR" property="cardContainingTerminal" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 09 09:37:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 09 09:37:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 09 09:37:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, spu_id, spu_code, sku_id, sku_code, offering_code, offering_name, offering_class, 
    quantity, ext_soft_offering_code, ext_hard_offering_code, settle_price, charge_code, 
    charge_id, color, model, unit, atom_sale_price, inventory, reserve_inventory, is_inventory, 
    cooperator_id, inventory_threshold, is_notice, config_all_time, config_time, create_time, 
    update_time, notified, offeringSaleRegion, settlePricePartner, settleServiceName, 
    associated_offer, validity_period, delete_time, inventory_management_mode_kx, inventory_id, 
    get_order_way, product_type, special_cooperative_settlement_price, inventory_main_id, 
    card_info_inventory_main_id, card_containing_terminal
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.AtomOfferingInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 09 09:37:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from atom_offering_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 09 09:37:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from atom_offering_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 09 09:37:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from atom_offering_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.AtomOfferingInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 09 09:37:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from atom_offering_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.AtomOfferingInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 09 09:37:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into atom_offering_info (id, spu_id, spu_code, 
      sku_id, sku_code, offering_code, 
      offering_name, offering_class, quantity, 
      ext_soft_offering_code, ext_hard_offering_code, 
      settle_price, charge_code, charge_id, 
      color, model, unit, 
      atom_sale_price, inventory, reserve_inventory, 
      is_inventory, cooperator_id, inventory_threshold, 
      is_notice, config_all_time, config_time, 
      create_time, update_time, notified, 
      offeringSaleRegion, settlePricePartner, 
      settleServiceName, associated_offer, validity_period, 
      delete_time, inventory_management_mode_kx, 
      inventory_id, get_order_way, product_type, 
      special_cooperative_settlement_price, inventory_main_id, 
      card_info_inventory_main_id, card_containing_terminal
      )
    values (#{id,jdbcType=VARCHAR}, #{spuId,jdbcType=VARCHAR}, #{spuCode,jdbcType=VARCHAR}, 
      #{skuId,jdbcType=VARCHAR}, #{skuCode,jdbcType=VARCHAR}, #{offeringCode,jdbcType=VARCHAR}, 
      #{offeringName,jdbcType=VARCHAR}, #{offeringClass,jdbcType=VARCHAR}, #{quantity,jdbcType=BIGINT}, 
      #{extSoftOfferingCode,jdbcType=VARCHAR}, #{extHardOfferingCode,jdbcType=VARCHAR}, 
      #{settlePrice,jdbcType=BIGINT}, #{chargeCode,jdbcType=VARCHAR}, #{chargeId,jdbcType=VARCHAR}, 
      #{color,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, 
      #{atomSalePrice,jdbcType=BIGINT}, #{inventory,jdbcType=BIGINT}, #{reserveInventory,jdbcType=BIGINT}, 
      #{isInventory,jdbcType=BIT}, #{cooperatorId,jdbcType=VARCHAR}, #{inventoryThreshold,jdbcType=BIGINT}, 
      #{isNotice,jdbcType=BIT}, #{configAllTime,jdbcType=TIMESTAMP}, #{configTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{notified,jdbcType=BIT}, 
      #{offeringsaleregion,jdbcType=VARCHAR}, #{settlepricepartner,jdbcType=VARCHAR}, 
      #{settleservicename,jdbcType=VARCHAR}, #{associatedOffer,jdbcType=VARCHAR}, #{validityPeriod,jdbcType=VARCHAR}, 
      #{deleteTime,jdbcType=TIMESTAMP}, #{inventoryManagementModeKx,jdbcType=VARCHAR}, 
      #{inventoryId,jdbcType=VARCHAR}, #{getOrderWay,jdbcType=INTEGER}, #{productType,jdbcType=VARCHAR}, 
      #{specialCooperativeSettlementPrice,jdbcType=VARCHAR}, #{inventoryMainId,jdbcType=VARCHAR}, 
      #{cardInfoInventoryMainId,jdbcType=VARCHAR}, #{cardContainingTerminal,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.AtomOfferingInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 09 09:37:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into atom_offering_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="spuId != null">
        spu_id,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="skuCode != null">
        sku_code,
      </if>
      <if test="offeringCode != null">
        offering_code,
      </if>
      <if test="offeringName != null">
        offering_name,
      </if>
      <if test="offeringClass != null">
        offering_class,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="extSoftOfferingCode != null">
        ext_soft_offering_code,
      </if>
      <if test="extHardOfferingCode != null">
        ext_hard_offering_code,
      </if>
      <if test="settlePrice != null">
        settle_price,
      </if>
      <if test="chargeCode != null">
        charge_code,
      </if>
      <if test="chargeId != null">
        charge_id,
      </if>
      <if test="color != null">
        color,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="atomSalePrice != null">
        atom_sale_price,
      </if>
      <if test="inventory != null">
        inventory,
      </if>
      <if test="reserveInventory != null">
        reserve_inventory,
      </if>
      <if test="isInventory != null">
        is_inventory,
      </if>
      <if test="cooperatorId != null">
        cooperator_id,
      </if>
      <if test="inventoryThreshold != null">
        inventory_threshold,
      </if>
      <if test="isNotice != null">
        is_notice,
      </if>
      <if test="configAllTime != null">
        config_all_time,
      </if>
      <if test="configTime != null">
        config_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="notified != null">
        notified,
      </if>
      <if test="offeringsaleregion != null">
        offeringSaleRegion,
      </if>
      <if test="settlepricepartner != null">
        settlePricePartner,
      </if>
      <if test="settleservicename != null">
        settleServiceName,
      </if>
      <if test="associatedOffer != null">
        associated_offer,
      </if>
      <if test="validityPeriod != null">
        validity_period,
      </if>
      <if test="deleteTime != null">
        delete_time,
      </if>
      <if test="inventoryManagementModeKx != null">
        inventory_management_mode_kx,
      </if>
      <if test="inventoryId != null">
        inventory_id,
      </if>
      <if test="getOrderWay != null">
        get_order_way,
      </if>
      <if test="productType != null">
        product_type,
      </if>
      <if test="specialCooperativeSettlementPrice != null">
        special_cooperative_settlement_price,
      </if>
      <if test="inventoryMainId != null">
        inventory_main_id,
      </if>
      <if test="cardInfoInventoryMainId != null">
        card_info_inventory_main_id,
      </if>
      <if test="cardContainingTerminal != null">
        card_containing_terminal,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="spuId != null">
        #{spuId,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="offeringCode != null">
        #{offeringCode,jdbcType=VARCHAR},
      </if>
      <if test="offeringName != null">
        #{offeringName,jdbcType=VARCHAR},
      </if>
      <if test="offeringClass != null">
        #{offeringClass,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=BIGINT},
      </if>
      <if test="extSoftOfferingCode != null">
        #{extSoftOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="extHardOfferingCode != null">
        #{extHardOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="settlePrice != null">
        #{settlePrice,jdbcType=BIGINT},
      </if>
      <if test="chargeCode != null">
        #{chargeCode,jdbcType=VARCHAR},
      </if>
      <if test="chargeId != null">
        #{chargeId,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        #{color,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="atomSalePrice != null">
        #{atomSalePrice,jdbcType=BIGINT},
      </if>
      <if test="inventory != null">
        #{inventory,jdbcType=BIGINT},
      </if>
      <if test="reserveInventory != null">
        #{reserveInventory,jdbcType=BIGINT},
      </if>
      <if test="isInventory != null">
        #{isInventory,jdbcType=BIT},
      </if>
      <if test="cooperatorId != null">
        #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="inventoryThreshold != null">
        #{inventoryThreshold,jdbcType=BIGINT},
      </if>
      <if test="isNotice != null">
        #{isNotice,jdbcType=BIT},
      </if>
      <if test="configAllTime != null">
        #{configAllTime,jdbcType=TIMESTAMP},
      </if>
      <if test="configTime != null">
        #{configTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="notified != null">
        #{notified,jdbcType=BIT},
      </if>
      <if test="offeringsaleregion != null">
        #{offeringsaleregion,jdbcType=VARCHAR},
      </if>
      <if test="settlepricepartner != null">
        #{settlepricepartner,jdbcType=VARCHAR},
      </if>
      <if test="settleservicename != null">
        #{settleservicename,jdbcType=VARCHAR},
      </if>
      <if test="associatedOffer != null">
        #{associatedOffer,jdbcType=VARCHAR},
      </if>
      <if test="validityPeriod != null">
        #{validityPeriod,jdbcType=VARCHAR},
      </if>
      <if test="deleteTime != null">
        #{deleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inventoryManagementModeKx != null">
        #{inventoryManagementModeKx,jdbcType=VARCHAR},
      </if>
      <if test="inventoryId != null">
        #{inventoryId,jdbcType=VARCHAR},
      </if>
      <if test="getOrderWay != null">
        #{getOrderWay,jdbcType=INTEGER},
      </if>
      <if test="productType != null">
        #{productType,jdbcType=VARCHAR},
      </if>
      <if test="specialCooperativeSettlementPrice != null">
        #{specialCooperativeSettlementPrice,jdbcType=VARCHAR},
      </if>
      <if test="inventoryMainId != null">
        #{inventoryMainId,jdbcType=VARCHAR},
      </if>
      <if test="cardInfoInventoryMainId != null">
        #{cardInfoInventoryMainId,jdbcType=VARCHAR},
      </if>
      <if test="cardContainingTerminal != null">
        #{cardContainingTerminal,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.AtomOfferingInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 09 09:37:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from atom_offering_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 09 09:37:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    update atom_offering_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.spuId != null">
        spu_id = #{record.spuId,jdbcType=VARCHAR},
      </if>
      <if test="record.spuCode != null">
        spu_code = #{record.spuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.skuId != null">
        sku_id = #{record.skuId,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCode != null">
        sku_code = #{record.skuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.offeringCode != null">
        offering_code = #{record.offeringCode,jdbcType=VARCHAR},
      </if>
      <if test="record.offeringName != null">
        offering_name = #{record.offeringName,jdbcType=VARCHAR},
      </if>
      <if test="record.offeringClass != null">
        offering_class = #{record.offeringClass,jdbcType=VARCHAR},
      </if>
      <if test="record.quantity != null">
        quantity = #{record.quantity,jdbcType=BIGINT},
      </if>
      <if test="record.extSoftOfferingCode != null">
        ext_soft_offering_code = #{record.extSoftOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.extHardOfferingCode != null">
        ext_hard_offering_code = #{record.extHardOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.settlePrice != null">
        settle_price = #{record.settlePrice,jdbcType=BIGINT},
      </if>
      <if test="record.chargeCode != null">
        charge_code = #{record.chargeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.chargeId != null">
        charge_id = #{record.chargeId,jdbcType=VARCHAR},
      </if>
      <if test="record.color != null">
        color = #{record.color,jdbcType=VARCHAR},
      </if>
      <if test="record.model != null">
        model = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.atomSalePrice != null">
        atom_sale_price = #{record.atomSalePrice,jdbcType=BIGINT},
      </if>
      <if test="record.inventory != null">
        inventory = #{record.inventory,jdbcType=BIGINT},
      </if>
      <if test="record.reserveInventory != null">
        reserve_inventory = #{record.reserveInventory,jdbcType=BIGINT},
      </if>
      <if test="record.isInventory != null">
        is_inventory = #{record.isInventory,jdbcType=BIT},
      </if>
      <if test="record.cooperatorId != null">
        cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.inventoryThreshold != null">
        inventory_threshold = #{record.inventoryThreshold,jdbcType=BIGINT},
      </if>
      <if test="record.isNotice != null">
        is_notice = #{record.isNotice,jdbcType=BIT},
      </if>
      <if test="record.configAllTime != null">
        config_all_time = #{record.configAllTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.configTime != null">
        config_time = #{record.configTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.notified != null">
        notified = #{record.notified,jdbcType=BIT},
      </if>
      <if test="record.offeringsaleregion != null">
        offeringSaleRegion = #{record.offeringsaleregion,jdbcType=VARCHAR},
      </if>
      <if test="record.settlepricepartner != null">
        settlePricePartner = #{record.settlepricepartner,jdbcType=VARCHAR},
      </if>
      <if test="record.settleservicename != null">
        settleServiceName = #{record.settleservicename,jdbcType=VARCHAR},
      </if>
      <if test="record.associatedOffer != null">
        associated_offer = #{record.associatedOffer,jdbcType=VARCHAR},
      </if>
      <if test="record.validityPeriod != null">
        validity_period = #{record.validityPeriod,jdbcType=VARCHAR},
      </if>
      <if test="record.deleteTime != null">
        delete_time = #{record.deleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.inventoryManagementModeKx != null">
        inventory_management_mode_kx = #{record.inventoryManagementModeKx,jdbcType=VARCHAR},
      </if>
      <if test="record.inventoryId != null">
        inventory_id = #{record.inventoryId,jdbcType=VARCHAR},
      </if>
      <if test="record.getOrderWay != null">
        get_order_way = #{record.getOrderWay,jdbcType=INTEGER},
      </if>
      <if test="record.productType != null">
        product_type = #{record.productType,jdbcType=VARCHAR},
      </if>
      <if test="record.specialCooperativeSettlementPrice != null">
        special_cooperative_settlement_price = #{record.specialCooperativeSettlementPrice,jdbcType=VARCHAR},
      </if>
      <if test="record.inventoryMainId != null">
        inventory_main_id = #{record.inventoryMainId,jdbcType=VARCHAR},
      </if>
      <if test="record.cardInfoInventoryMainId != null">
        card_info_inventory_main_id = #{record.cardInfoInventoryMainId,jdbcType=VARCHAR},
      </if>
      <if test="record.cardContainingTerminal != null">
        card_containing_terminal = #{record.cardContainingTerminal,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 09 09:37:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    update atom_offering_info
    set id = #{record.id,jdbcType=VARCHAR},
      spu_id = #{record.spuId,jdbcType=VARCHAR},
      spu_code = #{record.spuCode,jdbcType=VARCHAR},
      sku_id = #{record.skuId,jdbcType=VARCHAR},
      sku_code = #{record.skuCode,jdbcType=VARCHAR},
      offering_code = #{record.offeringCode,jdbcType=VARCHAR},
      offering_name = #{record.offeringName,jdbcType=VARCHAR},
      offering_class = #{record.offeringClass,jdbcType=VARCHAR},
      quantity = #{record.quantity,jdbcType=BIGINT},
      ext_soft_offering_code = #{record.extSoftOfferingCode,jdbcType=VARCHAR},
      ext_hard_offering_code = #{record.extHardOfferingCode,jdbcType=VARCHAR},
      settle_price = #{record.settlePrice,jdbcType=BIGINT},
      charge_code = #{record.chargeCode,jdbcType=VARCHAR},
      charge_id = #{record.chargeId,jdbcType=VARCHAR},
      color = #{record.color,jdbcType=VARCHAR},
      model = #{record.model,jdbcType=VARCHAR},
      unit = #{record.unit,jdbcType=VARCHAR},
      atom_sale_price = #{record.atomSalePrice,jdbcType=BIGINT},
      inventory = #{record.inventory,jdbcType=BIGINT},
      reserve_inventory = #{record.reserveInventory,jdbcType=BIGINT},
      is_inventory = #{record.isInventory,jdbcType=BIT},
      cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      inventory_threshold = #{record.inventoryThreshold,jdbcType=BIGINT},
      is_notice = #{record.isNotice,jdbcType=BIT},
      config_all_time = #{record.configAllTime,jdbcType=TIMESTAMP},
      config_time = #{record.configTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      notified = #{record.notified,jdbcType=BIT},
      offeringSaleRegion = #{record.offeringsaleregion,jdbcType=VARCHAR},
      settlePricePartner = #{record.settlepricepartner,jdbcType=VARCHAR},
      settleServiceName = #{record.settleservicename,jdbcType=VARCHAR},
      associated_offer = #{record.associatedOffer,jdbcType=VARCHAR},
      validity_period = #{record.validityPeriod,jdbcType=VARCHAR},
      delete_time = #{record.deleteTime,jdbcType=TIMESTAMP},
      inventory_management_mode_kx = #{record.inventoryManagementModeKx,jdbcType=VARCHAR},
      inventory_id = #{record.inventoryId,jdbcType=VARCHAR},
      get_order_way = #{record.getOrderWay,jdbcType=INTEGER},
      product_type = #{record.productType,jdbcType=VARCHAR},
      special_cooperative_settlement_price = #{record.specialCooperativeSettlementPrice,jdbcType=VARCHAR},
      inventory_main_id = #{record.inventoryMainId,jdbcType=VARCHAR},
      card_info_inventory_main_id = #{record.cardInfoInventoryMainId,jdbcType=VARCHAR},
      card_containing_terminal = #{record.cardContainingTerminal,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.AtomOfferingInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 09 09:37:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    update atom_offering_info
    <set>
      <if test="spuId != null">
        spu_id = #{spuId,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        sku_code = #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="offeringCode != null">
        offering_code = #{offeringCode,jdbcType=VARCHAR},
      </if>
      <if test="offeringName != null">
        offering_name = #{offeringName,jdbcType=VARCHAR},
      </if>
      <if test="offeringClass != null">
        offering_class = #{offeringClass,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=BIGINT},
      </if>
      <if test="extSoftOfferingCode != null">
        ext_soft_offering_code = #{extSoftOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="extHardOfferingCode != null">
        ext_hard_offering_code = #{extHardOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="settlePrice != null">
        settle_price = #{settlePrice,jdbcType=BIGINT},
      </if>
      <if test="chargeCode != null">
        charge_code = #{chargeCode,jdbcType=VARCHAR},
      </if>
      <if test="chargeId != null">
        charge_id = #{chargeId,jdbcType=VARCHAR},
      </if>
      <if test="color != null">
        color = #{color,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="atomSalePrice != null">
        atom_sale_price = #{atomSalePrice,jdbcType=BIGINT},
      </if>
      <if test="inventory != null">
        inventory = #{inventory,jdbcType=BIGINT},
      </if>
      <if test="reserveInventory != null">
        reserve_inventory = #{reserveInventory,jdbcType=BIGINT},
      </if>
      <if test="isInventory != null">
        is_inventory = #{isInventory,jdbcType=BIT},
      </if>
      <if test="cooperatorId != null">
        cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="inventoryThreshold != null">
        inventory_threshold = #{inventoryThreshold,jdbcType=BIGINT},
      </if>
      <if test="isNotice != null">
        is_notice = #{isNotice,jdbcType=BIT},
      </if>
      <if test="configAllTime != null">
        config_all_time = #{configAllTime,jdbcType=TIMESTAMP},
      </if>
      <if test="configTime != null">
        config_time = #{configTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="notified != null">
        notified = #{notified,jdbcType=BIT},
      </if>
      <if test="offeringsaleregion != null">
        offeringSaleRegion = #{offeringsaleregion,jdbcType=VARCHAR},
      </if>
      <if test="settlepricepartner != null">
        settlePricePartner = #{settlepricepartner,jdbcType=VARCHAR},
      </if>
      <if test="settleservicename != null">
        settleServiceName = #{settleservicename,jdbcType=VARCHAR},
      </if>
      <if test="associatedOffer != null">
        associated_offer = #{associatedOffer,jdbcType=VARCHAR},
      </if>
      <if test="validityPeriod != null">
        validity_period = #{validityPeriod,jdbcType=VARCHAR},
      </if>
      <if test="deleteTime != null">
        delete_time = #{deleteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inventoryManagementModeKx != null">
        inventory_management_mode_kx = #{inventoryManagementModeKx,jdbcType=VARCHAR},
      </if>
      <if test="inventoryId != null">
        inventory_id = #{inventoryId,jdbcType=VARCHAR},
      </if>
      <if test="getOrderWay != null">
        get_order_way = #{getOrderWay,jdbcType=INTEGER},
      </if>
      <if test="productType != null">
        product_type = #{productType,jdbcType=VARCHAR},
      </if>
      <if test="specialCooperativeSettlementPrice != null">
        special_cooperative_settlement_price = #{specialCooperativeSettlementPrice,jdbcType=VARCHAR},
      </if>
      <if test="inventoryMainId != null">
        inventory_main_id = #{inventoryMainId,jdbcType=VARCHAR},
      </if>
      <if test="cardInfoInventoryMainId != null">
        card_info_inventory_main_id = #{cardInfoInventoryMainId,jdbcType=VARCHAR},
      </if>
      <if test="cardContainingTerminal != null">
        card_containing_terminal = #{cardContainingTerminal,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.AtomOfferingInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 09 09:37:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    update atom_offering_info
    set spu_id = #{spuId,jdbcType=VARCHAR},
      spu_code = #{spuCode,jdbcType=VARCHAR},
      sku_id = #{skuId,jdbcType=VARCHAR},
      sku_code = #{skuCode,jdbcType=VARCHAR},
      offering_code = #{offeringCode,jdbcType=VARCHAR},
      offering_name = #{offeringName,jdbcType=VARCHAR},
      offering_class = #{offeringClass,jdbcType=VARCHAR},
      quantity = #{quantity,jdbcType=BIGINT},
      ext_soft_offering_code = #{extSoftOfferingCode,jdbcType=VARCHAR},
      ext_hard_offering_code = #{extHardOfferingCode,jdbcType=VARCHAR},
      settle_price = #{settlePrice,jdbcType=BIGINT},
      charge_code = #{chargeCode,jdbcType=VARCHAR},
      charge_id = #{chargeId,jdbcType=VARCHAR},
      color = #{color,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      atom_sale_price = #{atomSalePrice,jdbcType=BIGINT},
      inventory = #{inventory,jdbcType=BIGINT},
      reserve_inventory = #{reserveInventory,jdbcType=BIGINT},
      is_inventory = #{isInventory,jdbcType=BIT},
      cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      inventory_threshold = #{inventoryThreshold,jdbcType=BIGINT},
      is_notice = #{isNotice,jdbcType=BIT},
      config_all_time = #{configAllTime,jdbcType=TIMESTAMP},
      config_time = #{configTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      notified = #{notified,jdbcType=BIT},
      offeringSaleRegion = #{offeringsaleregion,jdbcType=VARCHAR},
      settlePricePartner = #{settlepricepartner,jdbcType=VARCHAR},
      settleServiceName = #{settleservicename,jdbcType=VARCHAR},
      associated_offer = #{associatedOffer,jdbcType=VARCHAR},
      validity_period = #{validityPeriod,jdbcType=VARCHAR},
      delete_time = #{deleteTime,jdbcType=TIMESTAMP},
      inventory_management_mode_kx = #{inventoryManagementModeKx,jdbcType=VARCHAR},
      inventory_id = #{inventoryId,jdbcType=VARCHAR},
      get_order_way = #{getOrderWay,jdbcType=INTEGER},
      product_type = #{productType,jdbcType=VARCHAR},
      special_cooperative_settlement_price = #{specialCooperativeSettlementPrice,jdbcType=VARCHAR},
      inventory_main_id = #{inventoryMainId,jdbcType=VARCHAR},
      card_info_inventory_main_id = #{cardInfoInventoryMainId,jdbcType=VARCHAR},
      card_containing_terminal = #{cardContainingTerminal,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 09 09:37:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into atom_offering_info
    (id, spu_id, spu_code, sku_id, sku_code, offering_code, offering_name, offering_class, 
      quantity, ext_soft_offering_code, ext_hard_offering_code, settle_price, charge_code, 
      charge_id, color, model, unit, atom_sale_price, inventory, reserve_inventory, is_inventory, 
      cooperator_id, inventory_threshold, is_notice, config_all_time, config_time, create_time, 
      update_time, notified, offeringSaleRegion, settlePricePartner, settleServiceName, 
      associated_offer, validity_period, delete_time, inventory_management_mode_kx, inventory_id, 
      get_order_way, product_type, special_cooperative_settlement_price, inventory_main_id, 
      card_info_inventory_main_id, card_containing_terminal)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.spuId,jdbcType=VARCHAR}, #{item.spuCode,jdbcType=VARCHAR}, 
        #{item.skuId,jdbcType=VARCHAR}, #{item.skuCode,jdbcType=VARCHAR}, #{item.offeringCode,jdbcType=VARCHAR}, 
        #{item.offeringName,jdbcType=VARCHAR}, #{item.offeringClass,jdbcType=VARCHAR}, 
        #{item.quantity,jdbcType=BIGINT}, #{item.extSoftOfferingCode,jdbcType=VARCHAR}, 
        #{item.extHardOfferingCode,jdbcType=VARCHAR}, #{item.settlePrice,jdbcType=BIGINT}, 
        #{item.chargeCode,jdbcType=VARCHAR}, #{item.chargeId,jdbcType=VARCHAR}, #{item.color,jdbcType=VARCHAR}, 
        #{item.model,jdbcType=VARCHAR}, #{item.unit,jdbcType=VARCHAR}, #{item.atomSalePrice,jdbcType=BIGINT}, 
        #{item.inventory,jdbcType=BIGINT}, #{item.reserveInventory,jdbcType=BIGINT}, #{item.isInventory,jdbcType=BIT}, 
        #{item.cooperatorId,jdbcType=VARCHAR}, #{item.inventoryThreshold,jdbcType=BIGINT}, 
        #{item.isNotice,jdbcType=BIT}, #{item.configAllTime,jdbcType=TIMESTAMP}, #{item.configTime,jdbcType=TIMESTAMP}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.notified,jdbcType=BIT}, 
        #{item.offeringsaleregion,jdbcType=VARCHAR}, #{item.settlepricepartner,jdbcType=VARCHAR}, 
        #{item.settleservicename,jdbcType=VARCHAR}, #{item.associatedOffer,jdbcType=VARCHAR}, 
        #{item.validityPeriod,jdbcType=VARCHAR}, #{item.deleteTime,jdbcType=TIMESTAMP}, 
        #{item.inventoryManagementModeKx,jdbcType=VARCHAR}, #{item.inventoryId,jdbcType=VARCHAR}, 
        #{item.getOrderWay,jdbcType=INTEGER}, #{item.productType,jdbcType=VARCHAR}, #{item.specialCooperativeSettlementPrice,jdbcType=VARCHAR}, 
        #{item.inventoryMainId,jdbcType=VARCHAR}, #{item.cardInfoInventoryMainId,jdbcType=VARCHAR}, 
        #{item.cardContainingTerminal,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 09 09:37:33 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into atom_offering_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'spu_id'.toString() == column.value">
          #{item.spuId,jdbcType=VARCHAR}
        </if>
        <if test="'spu_code'.toString() == column.value">
          #{item.spuCode,jdbcType=VARCHAR}
        </if>
        <if test="'sku_id'.toString() == column.value">
          #{item.skuId,jdbcType=VARCHAR}
        </if>
        <if test="'sku_code'.toString() == column.value">
          #{item.skuCode,jdbcType=VARCHAR}
        </if>
        <if test="'offering_code'.toString() == column.value">
          #{item.offeringCode,jdbcType=VARCHAR}
        </if>
        <if test="'offering_name'.toString() == column.value">
          #{item.offeringName,jdbcType=VARCHAR}
        </if>
        <if test="'offering_class'.toString() == column.value">
          #{item.offeringClass,jdbcType=VARCHAR}
        </if>
        <if test="'quantity'.toString() == column.value">
          #{item.quantity,jdbcType=BIGINT}
        </if>
        <if test="'ext_soft_offering_code'.toString() == column.value">
          #{item.extSoftOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'ext_hard_offering_code'.toString() == column.value">
          #{item.extHardOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'settle_price'.toString() == column.value">
          #{item.settlePrice,jdbcType=BIGINT}
        </if>
        <if test="'charge_code'.toString() == column.value">
          #{item.chargeCode,jdbcType=VARCHAR}
        </if>
        <if test="'charge_id'.toString() == column.value">
          #{item.chargeId,jdbcType=VARCHAR}
        </if>
        <if test="'color'.toString() == column.value">
          #{item.color,jdbcType=VARCHAR}
        </if>
        <if test="'model'.toString() == column.value">
          #{item.model,jdbcType=VARCHAR}
        </if>
        <if test="'unit'.toString() == column.value">
          #{item.unit,jdbcType=VARCHAR}
        </if>
        <if test="'atom_sale_price'.toString() == column.value">
          #{item.atomSalePrice,jdbcType=BIGINT}
        </if>
        <if test="'inventory'.toString() == column.value">
          #{item.inventory,jdbcType=BIGINT}
        </if>
        <if test="'reserve_inventory'.toString() == column.value">
          #{item.reserveInventory,jdbcType=BIGINT}
        </if>
        <if test="'is_inventory'.toString() == column.value">
          #{item.isInventory,jdbcType=BIT}
        </if>
        <if test="'cooperator_id'.toString() == column.value">
          #{item.cooperatorId,jdbcType=VARCHAR}
        </if>
        <if test="'inventory_threshold'.toString() == column.value">
          #{item.inventoryThreshold,jdbcType=BIGINT}
        </if>
        <if test="'is_notice'.toString() == column.value">
          #{item.isNotice,jdbcType=BIT}
        </if>
        <if test="'config_all_time'.toString() == column.value">
          #{item.configAllTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'config_time'.toString() == column.value">
          #{item.configTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'notified'.toString() == column.value">
          #{item.notified,jdbcType=BIT}
        </if>
        <if test="'offeringSaleRegion'.toString() == column.value">
          #{item.offeringsaleregion,jdbcType=VARCHAR}
        </if>
        <if test="'settlePricePartner'.toString() == column.value">
          #{item.settlepricepartner,jdbcType=VARCHAR}
        </if>
        <if test="'settleServiceName'.toString() == column.value">
          #{item.settleservicename,jdbcType=VARCHAR}
        </if>
        <if test="'associated_offer'.toString() == column.value">
          #{item.associatedOffer,jdbcType=VARCHAR}
        </if>
        <if test="'validity_period'.toString() == column.value">
          #{item.validityPeriod,jdbcType=VARCHAR}
        </if>
        <if test="'delete_time'.toString() == column.value">
          #{item.deleteTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'inventory_management_mode_kx'.toString() == column.value">
          #{item.inventoryManagementModeKx,jdbcType=VARCHAR}
        </if>
        <if test="'inventory_id'.toString() == column.value">
          #{item.inventoryId,jdbcType=VARCHAR}
        </if>
        <if test="'get_order_way'.toString() == column.value">
          #{item.getOrderWay,jdbcType=INTEGER}
        </if>
        <if test="'product_type'.toString() == column.value">
          #{item.productType,jdbcType=VARCHAR}
        </if>
        <if test="'special_cooperative_settlement_price'.toString() == column.value">
          #{item.specialCooperativeSettlementPrice,jdbcType=VARCHAR}
        </if>
        <if test="'inventory_main_id'.toString() == column.value">
          #{item.inventoryMainId,jdbcType=VARCHAR}
        </if>
        <if test="'card_info_inventory_main_id'.toString() == column.value">
          #{item.cardInfoInventoryMainId,jdbcType=VARCHAR}
        </if>
        <if test="'card_containing_terminal'.toString() == column.value">
          #{item.cardContainingTerminal,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>