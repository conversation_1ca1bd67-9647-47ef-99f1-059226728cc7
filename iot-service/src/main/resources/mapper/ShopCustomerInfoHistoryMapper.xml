<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ShopCustomerInfoHistoryMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.ShopCustomerInfoHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 11:13:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="cust_code" jdbcType="VARCHAR" property="custCode" />
    <result column="cust_id" jdbcType="VARCHAR" property="custId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="cust_name" jdbcType="VARCHAR" property="custName" />
    <result column="opr_type" jdbcType="VARCHAR" property="oprType" />
    <result column="cust_status_time" jdbcType="TIMESTAMP" property="custStatusTime" />
    <result column="role_type" jdbcType="VARCHAR" property="roleType" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="region_id" jdbcType="VARCHAR" property="regionId" />
    <result column="region_name" jdbcType="VARCHAR" property="regionName" />
    <result column="client_register" jdbcType="TIMESTAMP" property="clientRegister" />
    <result column="client_status" jdbcType="VARCHAR" property="clientStatus" />
    <result column="number_logins" jdbcType="INTEGER" property="numberLogins" />
    <result column="distributor_name" jdbcType="VARCHAR" property="distributorName" />
    <result column="distributor_invitation_register_successful_quantity" jdbcType="INTEGER" property="distributorInvitationRegisterSuccessfulQuantity" />
    <result column="distributor_channel_id" jdbcType="VARCHAR" property="distributorChannelId" />
    <result column="distributor_channel_name" jdbcType="VARCHAR" property="distributorChannelName" />
    <result column="distributor_referral_code" jdbcType="VARCHAR" property="distributorReferralCode" />
    <result column="distributor_mrg_inf" jdbcType="VARCHAR" property="distributorMrgInf" />
    <result column="distributor_mrg_code" jdbcType="VARCHAR" property="distributorMrgCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="agent_number" jdbcType="VARCHAR" property="agentNumber" />
    <result column="agent_name" jdbcType="VARCHAR" property="agentName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 11:13:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 11:13:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 11:13:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, cust_code, cust_id, user_id, cust_name, opr_type, cust_status_time, role_type, 
    be_id, province_name, location, city_name, region_id, region_name, client_register, 
    client_status, number_logins, distributor_name, distributor_invitation_register_successful_quantity, 
    distributor_channel_id, distributor_channel_name, distributor_referral_code, distributor_mrg_inf, 
    distributor_mrg_code, create_time, agent_number, agent_name
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ShopCustomerInfoHistoryExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 11:13:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from shop_customer_info_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 11:13:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from shop_customer_info_history
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 11:13:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from shop_customer_info_history
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ShopCustomerInfoHistoryExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 11:13:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from shop_customer_info_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.ShopCustomerInfoHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 11:13:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into shop_customer_info_history (id, cust_code, cust_id, 
      user_id, cust_name, opr_type, 
      cust_status_time, role_type, be_id, 
      province_name, location, city_name, 
      region_id, region_name, client_register, 
      client_status, number_logins, distributor_name, 
      distributor_invitation_register_successful_quantity, distributor_channel_id, 
      distributor_channel_name, distributor_referral_code, 
      distributor_mrg_inf, distributor_mrg_code, 
      create_time, agent_number, agent_name
      )
    values (#{id,jdbcType=VARCHAR}, #{custCode,jdbcType=VARCHAR}, #{custId,jdbcType=VARCHAR}, 
      #{userId,jdbcType=VARCHAR}, #{custName,jdbcType=VARCHAR}, #{oprType,jdbcType=VARCHAR}, 
      #{custStatusTime,jdbcType=TIMESTAMP}, #{roleType,jdbcType=VARCHAR}, #{beId,jdbcType=VARCHAR}, 
      #{provinceName,jdbcType=VARCHAR}, #{location,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR}, 
      #{regionId,jdbcType=VARCHAR}, #{regionName,jdbcType=VARCHAR}, #{clientRegister,jdbcType=TIMESTAMP}, 
      #{clientStatus,jdbcType=VARCHAR}, #{numberLogins,jdbcType=INTEGER}, #{distributorName,jdbcType=VARCHAR}, 
      #{distributorInvitationRegisterSuccessfulQuantity,jdbcType=INTEGER}, #{distributorChannelId,jdbcType=VARCHAR}, 
      #{distributorChannelName,jdbcType=VARCHAR}, #{distributorReferralCode,jdbcType=VARCHAR}, 
      #{distributorMrgInf,jdbcType=VARCHAR}, #{distributorMrgCode,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{agentNumber,jdbcType=VARCHAR}, #{agentName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ShopCustomerInfoHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 11:13:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into shop_customer_info_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="custCode != null">
        cust_code,
      </if>
      <if test="custId != null">
        cust_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="custName != null">
        cust_name,
      </if>
      <if test="oprType != null">
        opr_type,
      </if>
      <if test="custStatusTime != null">
        cust_status_time,
      </if>
      <if test="roleType != null">
        role_type,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="regionId != null">
        region_id,
      </if>
      <if test="regionName != null">
        region_name,
      </if>
      <if test="clientRegister != null">
        client_register,
      </if>
      <if test="clientStatus != null">
        client_status,
      </if>
      <if test="numberLogins != null">
        number_logins,
      </if>
      <if test="distributorName != null">
        distributor_name,
      </if>
      <if test="distributorInvitationRegisterSuccessfulQuantity != null">
        distributor_invitation_register_successful_quantity,
      </if>
      <if test="distributorChannelId != null">
        distributor_channel_id,
      </if>
      <if test="distributorChannelName != null">
        distributor_channel_name,
      </if>
      <if test="distributorReferralCode != null">
        distributor_referral_code,
      </if>
      <if test="distributorMrgInf != null">
        distributor_mrg_inf,
      </if>
      <if test="distributorMrgCode != null">
        distributor_mrg_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="agentNumber != null">
        agent_number,
      </if>
      <if test="agentName != null">
        agent_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="custCode != null">
        #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="custId != null">
        #{custId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="custName != null">
        #{custName,jdbcType=VARCHAR},
      </if>
      <if test="oprType != null">
        #{oprType,jdbcType=VARCHAR},
      </if>
      <if test="custStatusTime != null">
        #{custStatusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="roleType != null">
        #{roleType,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null">
        #{regionId,jdbcType=VARCHAR},
      </if>
      <if test="regionName != null">
        #{regionName,jdbcType=VARCHAR},
      </if>
      <if test="clientRegister != null">
        #{clientRegister,jdbcType=TIMESTAMP},
      </if>
      <if test="clientStatus != null">
        #{clientStatus,jdbcType=VARCHAR},
      </if>
      <if test="numberLogins != null">
        #{numberLogins,jdbcType=INTEGER},
      </if>
      <if test="distributorName != null">
        #{distributorName,jdbcType=VARCHAR},
      </if>
      <if test="distributorInvitationRegisterSuccessfulQuantity != null">
        #{distributorInvitationRegisterSuccessfulQuantity,jdbcType=INTEGER},
      </if>
      <if test="distributorChannelId != null">
        #{distributorChannelId,jdbcType=VARCHAR},
      </if>
      <if test="distributorChannelName != null">
        #{distributorChannelName,jdbcType=VARCHAR},
      </if>
      <if test="distributorReferralCode != null">
        #{distributorReferralCode,jdbcType=VARCHAR},
      </if>
      <if test="distributorMrgInf != null">
        #{distributorMrgInf,jdbcType=VARCHAR},
      </if>
      <if test="distributorMrgCode != null">
        #{distributorMrgCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="agentNumber != null">
        #{agentNumber,jdbcType=VARCHAR},
      </if>
      <if test="agentName != null">
        #{agentName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ShopCustomerInfoHistoryExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 11:13:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from shop_customer_info_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 11:13:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    update shop_customer_info_history
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.custCode != null">
        cust_code = #{record.custCode,jdbcType=VARCHAR},
      </if>
      <if test="record.custId != null">
        cust_id = #{record.custId,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.custName != null">
        cust_name = #{record.custName,jdbcType=VARCHAR},
      </if>
      <if test="record.oprType != null">
        opr_type = #{record.oprType,jdbcType=VARCHAR},
      </if>
      <if test="record.custStatusTime != null">
        cust_status_time = #{record.custStatusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.roleType != null">
        role_type = #{record.roleType,jdbcType=VARCHAR},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceName != null">
        province_name = #{record.provinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.location != null">
        location = #{record.location,jdbcType=VARCHAR},
      </if>
      <if test="record.cityName != null">
        city_name = #{record.cityName,jdbcType=VARCHAR},
      </if>
      <if test="record.regionId != null">
        region_id = #{record.regionId,jdbcType=VARCHAR},
      </if>
      <if test="record.regionName != null">
        region_name = #{record.regionName,jdbcType=VARCHAR},
      </if>
      <if test="record.clientRegister != null">
        client_register = #{record.clientRegister,jdbcType=TIMESTAMP},
      </if>
      <if test="record.clientStatus != null">
        client_status = #{record.clientStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.numberLogins != null">
        number_logins = #{record.numberLogins,jdbcType=INTEGER},
      </if>
      <if test="record.distributorName != null">
        distributor_name = #{record.distributorName,jdbcType=VARCHAR},
      </if>
      <if test="record.distributorInvitationRegisterSuccessfulQuantity != null">
        distributor_invitation_register_successful_quantity = #{record.distributorInvitationRegisterSuccessfulQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.distributorChannelId != null">
        distributor_channel_id = #{record.distributorChannelId,jdbcType=VARCHAR},
      </if>
      <if test="record.distributorChannelName != null">
        distributor_channel_name = #{record.distributorChannelName,jdbcType=VARCHAR},
      </if>
      <if test="record.distributorReferralCode != null">
        distributor_referral_code = #{record.distributorReferralCode,jdbcType=VARCHAR},
      </if>
      <if test="record.distributorMrgInf != null">
        distributor_mrg_inf = #{record.distributorMrgInf,jdbcType=VARCHAR},
      </if>
      <if test="record.distributorMrgCode != null">
        distributor_mrg_code = #{record.distributorMrgCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.agentNumber != null">
        agent_number = #{record.agentNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.agentName != null">
        agent_name = #{record.agentName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 11:13:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    update shop_customer_info_history
    set id = #{record.id,jdbcType=VARCHAR},
      cust_code = #{record.custCode,jdbcType=VARCHAR},
      cust_id = #{record.custId,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      cust_name = #{record.custName,jdbcType=VARCHAR},
      opr_type = #{record.oprType,jdbcType=VARCHAR},
      cust_status_time = #{record.custStatusTime,jdbcType=TIMESTAMP},
      role_type = #{record.roleType,jdbcType=VARCHAR},
      be_id = #{record.beId,jdbcType=VARCHAR},
      province_name = #{record.provinceName,jdbcType=VARCHAR},
      location = #{record.location,jdbcType=VARCHAR},
      city_name = #{record.cityName,jdbcType=VARCHAR},
      region_id = #{record.regionId,jdbcType=VARCHAR},
      region_name = #{record.regionName,jdbcType=VARCHAR},
      client_register = #{record.clientRegister,jdbcType=TIMESTAMP},
      client_status = #{record.clientStatus,jdbcType=VARCHAR},
      number_logins = #{record.numberLogins,jdbcType=INTEGER},
      distributor_name = #{record.distributorName,jdbcType=VARCHAR},
      distributor_invitation_register_successful_quantity = #{record.distributorInvitationRegisterSuccessfulQuantity,jdbcType=INTEGER},
      distributor_channel_id = #{record.distributorChannelId,jdbcType=VARCHAR},
      distributor_channel_name = #{record.distributorChannelName,jdbcType=VARCHAR},
      distributor_referral_code = #{record.distributorReferralCode,jdbcType=VARCHAR},
      distributor_mrg_inf = #{record.distributorMrgInf,jdbcType=VARCHAR},
      distributor_mrg_code = #{record.distributorMrgCode,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      agent_number = #{record.agentNumber,jdbcType=VARCHAR},
      agent_name = #{record.agentName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ShopCustomerInfoHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 11:13:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    update shop_customer_info_history
    <set>
      <if test="custCode != null">
        cust_code = #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="custId != null">
        cust_id = #{custId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="custName != null">
        cust_name = #{custName,jdbcType=VARCHAR},
      </if>
      <if test="oprType != null">
        opr_type = #{oprType,jdbcType=VARCHAR},
      </if>
      <if test="custStatusTime != null">
        cust_status_time = #{custStatusTime,jdbcType=TIMESTAMP},
      </if>
      <if test="roleType != null">
        role_type = #{roleType,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null">
        region_id = #{regionId,jdbcType=VARCHAR},
      </if>
      <if test="regionName != null">
        region_name = #{regionName,jdbcType=VARCHAR},
      </if>
      <if test="clientRegister != null">
        client_register = #{clientRegister,jdbcType=TIMESTAMP},
      </if>
      <if test="clientStatus != null">
        client_status = #{clientStatus,jdbcType=VARCHAR},
      </if>
      <if test="numberLogins != null">
        number_logins = #{numberLogins,jdbcType=INTEGER},
      </if>
      <if test="distributorName != null">
        distributor_name = #{distributorName,jdbcType=VARCHAR},
      </if>
      <if test="distributorInvitationRegisterSuccessfulQuantity != null">
        distributor_invitation_register_successful_quantity = #{distributorInvitationRegisterSuccessfulQuantity,jdbcType=INTEGER},
      </if>
      <if test="distributorChannelId != null">
        distributor_channel_id = #{distributorChannelId,jdbcType=VARCHAR},
      </if>
      <if test="distributorChannelName != null">
        distributor_channel_name = #{distributorChannelName,jdbcType=VARCHAR},
      </if>
      <if test="distributorReferralCode != null">
        distributor_referral_code = #{distributorReferralCode,jdbcType=VARCHAR},
      </if>
      <if test="distributorMrgInf != null">
        distributor_mrg_inf = #{distributorMrgInf,jdbcType=VARCHAR},
      </if>
      <if test="distributorMrgCode != null">
        distributor_mrg_code = #{distributorMrgCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="agentNumber != null">
        agent_number = #{agentNumber,jdbcType=VARCHAR},
      </if>
      <if test="agentName != null">
        agent_name = #{agentName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.ShopCustomerInfoHistory">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 11:13:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    update shop_customer_info_history
    set cust_code = #{custCode,jdbcType=VARCHAR},
      cust_id = #{custId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      cust_name = #{custName,jdbcType=VARCHAR},
      opr_type = #{oprType,jdbcType=VARCHAR},
      cust_status_time = #{custStatusTime,jdbcType=TIMESTAMP},
      role_type = #{roleType,jdbcType=VARCHAR},
      be_id = #{beId,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR},
      location = #{location,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      region_id = #{regionId,jdbcType=VARCHAR},
      region_name = #{regionName,jdbcType=VARCHAR},
      client_register = #{clientRegister,jdbcType=TIMESTAMP},
      client_status = #{clientStatus,jdbcType=VARCHAR},
      number_logins = #{numberLogins,jdbcType=INTEGER},
      distributor_name = #{distributorName,jdbcType=VARCHAR},
      distributor_invitation_register_successful_quantity = #{distributorInvitationRegisterSuccessfulQuantity,jdbcType=INTEGER},
      distributor_channel_id = #{distributorChannelId,jdbcType=VARCHAR},
      distributor_channel_name = #{distributorChannelName,jdbcType=VARCHAR},
      distributor_referral_code = #{distributorReferralCode,jdbcType=VARCHAR},
      distributor_mrg_inf = #{distributorMrgInf,jdbcType=VARCHAR},
      distributor_mrg_code = #{distributorMrgCode,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      agent_number = #{agentNumber,jdbcType=VARCHAR},
      agent_name = #{agentName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 11:13:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into shop_customer_info_history
    (id, cust_code, cust_id, user_id, cust_name, opr_type, cust_status_time, role_type, 
      be_id, province_name, location, city_name, region_id, region_name, client_register, 
      client_status, number_logins, distributor_name, distributor_invitation_register_successful_quantity, 
      distributor_channel_id, distributor_channel_name, distributor_referral_code, distributor_mrg_inf, 
      distributor_mrg_code, create_time, agent_number, agent_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.custCode,jdbcType=VARCHAR}, #{item.custId,jdbcType=VARCHAR}, 
        #{item.userId,jdbcType=VARCHAR}, #{item.custName,jdbcType=VARCHAR}, #{item.oprType,jdbcType=VARCHAR}, 
        #{item.custStatusTime,jdbcType=TIMESTAMP}, #{item.roleType,jdbcType=VARCHAR}, #{item.beId,jdbcType=VARCHAR}, 
        #{item.provinceName,jdbcType=VARCHAR}, #{item.location,jdbcType=VARCHAR}, #{item.cityName,jdbcType=VARCHAR}, 
        #{item.regionId,jdbcType=VARCHAR}, #{item.regionName,jdbcType=VARCHAR}, #{item.clientRegister,jdbcType=TIMESTAMP}, 
        #{item.clientStatus,jdbcType=VARCHAR}, #{item.numberLogins,jdbcType=INTEGER}, #{item.distributorName,jdbcType=VARCHAR}, 
        #{item.distributorInvitationRegisterSuccessfulQuantity,jdbcType=INTEGER}, #{item.distributorChannelId,jdbcType=VARCHAR}, 
        #{item.distributorChannelName,jdbcType=VARCHAR}, #{item.distributorReferralCode,jdbcType=VARCHAR}, 
        #{item.distributorMrgInf,jdbcType=VARCHAR}, #{item.distributorMrgCode,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.agentNumber,jdbcType=VARCHAR}, #{item.agentName,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 31 11:13:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into shop_customer_info_history (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'cust_code'.toString() == column.value">
          #{item.custCode,jdbcType=VARCHAR}
        </if>
        <if test="'cust_id'.toString() == column.value">
          #{item.custId,jdbcType=VARCHAR}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
        <if test="'cust_name'.toString() == column.value">
          #{item.custName,jdbcType=VARCHAR}
        </if>
        <if test="'opr_type'.toString() == column.value">
          #{item.oprType,jdbcType=VARCHAR}
        </if>
        <if test="'cust_status_time'.toString() == column.value">
          #{item.custStatusTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'role_type'.toString() == column.value">
          #{item.roleType,jdbcType=VARCHAR}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'province_name'.toString() == column.value">
          #{item.provinceName,jdbcType=VARCHAR}
        </if>
        <if test="'location'.toString() == column.value">
          #{item.location,jdbcType=VARCHAR}
        </if>
        <if test="'city_name'.toString() == column.value">
          #{item.cityName,jdbcType=VARCHAR}
        </if>
        <if test="'region_id'.toString() == column.value">
          #{item.regionId,jdbcType=VARCHAR}
        </if>
        <if test="'region_name'.toString() == column.value">
          #{item.regionName,jdbcType=VARCHAR}
        </if>
        <if test="'client_register'.toString() == column.value">
          #{item.clientRegister,jdbcType=TIMESTAMP}
        </if>
        <if test="'client_status'.toString() == column.value">
          #{item.clientStatus,jdbcType=VARCHAR}
        </if>
        <if test="'number_logins'.toString() == column.value">
          #{item.numberLogins,jdbcType=INTEGER}
        </if>
        <if test="'distributor_name'.toString() == column.value">
          #{item.distributorName,jdbcType=VARCHAR}
        </if>
        <if test="'distributor_invitation_register_successful_quantity'.toString() == column.value">
          #{item.distributorInvitationRegisterSuccessfulQuantity,jdbcType=INTEGER}
        </if>
        <if test="'distributor_channel_id'.toString() == column.value">
          #{item.distributorChannelId,jdbcType=VARCHAR}
        </if>
        <if test="'distributor_channel_name'.toString() == column.value">
          #{item.distributorChannelName,jdbcType=VARCHAR}
        </if>
        <if test="'distributor_referral_code'.toString() == column.value">
          #{item.distributorReferralCode,jdbcType=VARCHAR}
        </if>
        <if test="'distributor_mrg_inf'.toString() == column.value">
          #{item.distributorMrgInf,jdbcType=VARCHAR}
        </if>
        <if test="'distributor_mrg_code'.toString() == column.value">
          #{item.distributorMrgCode,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'agent_number'.toString() == column.value">
          #{item.agentNumber,jdbcType=VARCHAR}
        </if>
        <if test="'agent_name'.toString() == column.value">
          #{item.agentName,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>