<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ProductFlowInstanceSpuMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.ProductFlowInstanceSpu">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat Oct 12 01:26:15 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="flow_instance_id" jdbcType="VARCHAR" property="flowInstanceId" />
    <result column="flow_id" jdbcType="VARCHAR" property="flowId" />
    <result column="shelf_catagory_id" jdbcType="VARCHAR" property="shelfCatagoryId" />
    <result column="spu_code" jdbcType="VARCHAR" property="spuCode" />
    <result column="spu_name" jdbcType="VARCHAR" property="spuName" />
    <result column="product_standard" jdbcType="INTEGER" property="productStandard" />
    <result column="product_type" jdbcType="INTEGER" property="productType" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="manager" jdbcType="VARCHAR" property="manager" />
    <result column="product_desc" jdbcType="VARCHAR" property="productDesc" />
    <result column="application_area" jdbcType="VARCHAR" property="applicationArea" />
    <result column="is_hidden_shelf" jdbcType="VARCHAR" property="isHiddenShelf" />
    <result column="spu_service_provider" jdbcType="VARCHAR" property="spuServiceProvider" />
    <result column="sale_tag" jdbcType="VARCHAR" property="saleTag" />
    <result column="search_word" jdbcType="VARCHAR" property="searchWord" />
    <result column="spu_remark" jdbcType="VARCHAR" property="spuRemark" />
    <result column="aftermarket_admin_info" jdbcType="VARCHAR" property="aftermarketAdminInfo" />
    <result column="manage_department" jdbcType="VARCHAR" property="manageDepartment" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat Oct 12 01:26:15 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat Oct 12 01:26:15 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat Oct 12 01:26:15 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, flow_instance_id, flow_id, shelf_catagory_id, spu_code, spu_name, product_standard, 
    product_type, url, manager, product_desc, application_area, is_hidden_shelf, spu_service_provider, 
    sale_tag, search_word, spu_remark, aftermarket_admin_info, manage_department, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceSpuExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat Oct 12 01:26:15 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from product_flow_instance_spu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat Oct 12 01:26:15 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from product_flow_instance_spu
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat Oct 12 01:26:15 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from product_flow_instance_spu
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceSpuExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat Oct 12 01:26:15 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from product_flow_instance_spu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceSpu">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat Oct 12 01:26:15 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_spu (id, flow_instance_id, flow_id, 
      shelf_catagory_id, spu_code, spu_name, 
      product_standard, product_type, url, 
      manager, product_desc, application_area, 
      is_hidden_shelf, spu_service_provider, sale_tag, 
      search_word, spu_remark, aftermarket_admin_info, 
      manage_department, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{flowInstanceId,jdbcType=VARCHAR}, #{flowId,jdbcType=VARCHAR}, 
      #{shelfCatagoryId,jdbcType=VARCHAR}, #{spuCode,jdbcType=VARCHAR}, #{spuName,jdbcType=VARCHAR}, 
      #{productStandard,jdbcType=INTEGER}, #{productType,jdbcType=INTEGER}, #{url,jdbcType=VARCHAR}, 
      #{manager,jdbcType=VARCHAR}, #{productDesc,jdbcType=VARCHAR}, #{applicationArea,jdbcType=VARCHAR}, 
      #{isHiddenShelf,jdbcType=VARCHAR}, #{spuServiceProvider,jdbcType=VARCHAR}, #{saleTag,jdbcType=VARCHAR}, 
      #{searchWord,jdbcType=VARCHAR}, #{spuRemark,jdbcType=VARCHAR}, #{aftermarketAdminInfo,jdbcType=VARCHAR}, 
      #{manageDepartment,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceSpu">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat Oct 12 01:26:15 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_spu
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="flowInstanceId != null">
        flow_instance_id,
      </if>
      <if test="flowId != null">
        flow_id,
      </if>
      <if test="shelfCatagoryId != null">
        shelf_catagory_id,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="spuName != null">
        spu_name,
      </if>
      <if test="productStandard != null">
        product_standard,
      </if>
      <if test="productType != null">
        product_type,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="manager != null">
        manager,
      </if>
      <if test="productDesc != null">
        product_desc,
      </if>
      <if test="applicationArea != null">
        application_area,
      </if>
      <if test="isHiddenShelf != null">
        is_hidden_shelf,
      </if>
      <if test="spuServiceProvider != null">
        spu_service_provider,
      </if>
      <if test="saleTag != null">
        sale_tag,
      </if>
      <if test="searchWord != null">
        search_word,
      </if>
      <if test="spuRemark != null">
        spu_remark,
      </if>
      <if test="aftermarketAdminInfo != null">
        aftermarket_admin_info,
      </if>
      <if test="manageDepartment != null">
        manage_department,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="flowInstanceId != null">
        #{flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="flowId != null">
        #{flowId,jdbcType=VARCHAR},
      </if>
      <if test="shelfCatagoryId != null">
        #{shelfCatagoryId,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="spuName != null">
        #{spuName,jdbcType=VARCHAR},
      </if>
      <if test="productStandard != null">
        #{productStandard,jdbcType=INTEGER},
      </if>
      <if test="productType != null">
        #{productType,jdbcType=INTEGER},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="manager != null">
        #{manager,jdbcType=VARCHAR},
      </if>
      <if test="productDesc != null">
        #{productDesc,jdbcType=VARCHAR},
      </if>
      <if test="applicationArea != null">
        #{applicationArea,jdbcType=VARCHAR},
      </if>
      <if test="isHiddenShelf != null">
        #{isHiddenShelf,jdbcType=VARCHAR},
      </if>
      <if test="spuServiceProvider != null">
        #{spuServiceProvider,jdbcType=VARCHAR},
      </if>
      <if test="saleTag != null">
        #{saleTag,jdbcType=VARCHAR},
      </if>
      <if test="searchWord != null">
        #{searchWord,jdbcType=VARCHAR},
      </if>
      <if test="spuRemark != null">
        #{spuRemark,jdbcType=VARCHAR},
      </if>
      <if test="aftermarketAdminInfo != null">
        #{aftermarketAdminInfo,jdbcType=VARCHAR},
      </if>
      <if test="manageDepartment != null">
        #{manageDepartment,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceSpuExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat Oct 12 01:26:15 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from product_flow_instance_spu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat Oct 12 01:26:15 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_spu
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.flowInstanceId != null">
        flow_instance_id = #{record.flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.flowId != null">
        flow_id = #{record.flowId,jdbcType=VARCHAR},
      </if>
      <if test="record.shelfCatagoryId != null">
        shelf_catagory_id = #{record.shelfCatagoryId,jdbcType=VARCHAR},
      </if>
      <if test="record.spuCode != null">
        spu_code = #{record.spuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.spuName != null">
        spu_name = #{record.spuName,jdbcType=VARCHAR},
      </if>
      <if test="record.productStandard != null">
        product_standard = #{record.productStandard,jdbcType=INTEGER},
      </if>
      <if test="record.productType != null">
        product_type = #{record.productType,jdbcType=INTEGER},
      </if>
      <if test="record.url != null">
        url = #{record.url,jdbcType=VARCHAR},
      </if>
      <if test="record.manager != null">
        manager = #{record.manager,jdbcType=VARCHAR},
      </if>
      <if test="record.productDesc != null">
        product_desc = #{record.productDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.applicationArea != null">
        application_area = #{record.applicationArea,jdbcType=VARCHAR},
      </if>
      <if test="record.isHiddenShelf != null">
        is_hidden_shelf = #{record.isHiddenShelf,jdbcType=VARCHAR},
      </if>
      <if test="record.spuServiceProvider != null">
        spu_service_provider = #{record.spuServiceProvider,jdbcType=VARCHAR},
      </if>
      <if test="record.saleTag != null">
        sale_tag = #{record.saleTag,jdbcType=VARCHAR},
      </if>
      <if test="record.searchWord != null">
        search_word = #{record.searchWord,jdbcType=VARCHAR},
      </if>
      <if test="record.spuRemark != null">
        spu_remark = #{record.spuRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.aftermarketAdminInfo != null">
        aftermarket_admin_info = #{record.aftermarketAdminInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.manageDepartment != null">
        manage_department = #{record.manageDepartment,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat Oct 12 01:26:15 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_spu
    set id = #{record.id,jdbcType=VARCHAR},
      flow_instance_id = #{record.flowInstanceId,jdbcType=VARCHAR},
      flow_id = #{record.flowId,jdbcType=VARCHAR},
      shelf_catagory_id = #{record.shelfCatagoryId,jdbcType=VARCHAR},
      spu_code = #{record.spuCode,jdbcType=VARCHAR},
      spu_name = #{record.spuName,jdbcType=VARCHAR},
      product_standard = #{record.productStandard,jdbcType=INTEGER},
      product_type = #{record.productType,jdbcType=INTEGER},
      url = #{record.url,jdbcType=VARCHAR},
      manager = #{record.manager,jdbcType=VARCHAR},
      product_desc = #{record.productDesc,jdbcType=VARCHAR},
      application_area = #{record.applicationArea,jdbcType=VARCHAR},
      is_hidden_shelf = #{record.isHiddenShelf,jdbcType=VARCHAR},
      spu_service_provider = #{record.spuServiceProvider,jdbcType=VARCHAR},
      sale_tag = #{record.saleTag,jdbcType=VARCHAR},
      search_word = #{record.searchWord,jdbcType=VARCHAR},
      spu_remark = #{record.spuRemark,jdbcType=VARCHAR},
      aftermarket_admin_info = #{record.aftermarketAdminInfo,jdbcType=VARCHAR},
      manage_department = #{record.manageDepartment,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceSpu">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat Oct 12 01:26:15 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_spu
    <set>
      <if test="flowInstanceId != null">
        flow_instance_id = #{flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="flowId != null">
        flow_id = #{flowId,jdbcType=VARCHAR},
      </if>
      <if test="shelfCatagoryId != null">
        shelf_catagory_id = #{shelfCatagoryId,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="spuName != null">
        spu_name = #{spuName,jdbcType=VARCHAR},
      </if>
      <if test="productStandard != null">
        product_standard = #{productStandard,jdbcType=INTEGER},
      </if>
      <if test="productType != null">
        product_type = #{productType,jdbcType=INTEGER},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="manager != null">
        manager = #{manager,jdbcType=VARCHAR},
      </if>
      <if test="productDesc != null">
        product_desc = #{productDesc,jdbcType=VARCHAR},
      </if>
      <if test="applicationArea != null">
        application_area = #{applicationArea,jdbcType=VARCHAR},
      </if>
      <if test="isHiddenShelf != null">
        is_hidden_shelf = #{isHiddenShelf,jdbcType=VARCHAR},
      </if>
      <if test="spuServiceProvider != null">
        spu_service_provider = #{spuServiceProvider,jdbcType=VARCHAR},
      </if>
      <if test="saleTag != null">
        sale_tag = #{saleTag,jdbcType=VARCHAR},
      </if>
      <if test="searchWord != null">
        search_word = #{searchWord,jdbcType=VARCHAR},
      </if>
      <if test="spuRemark != null">
        spu_remark = #{spuRemark,jdbcType=VARCHAR},
      </if>
      <if test="aftermarketAdminInfo != null">
        aftermarket_admin_info = #{aftermarketAdminInfo,jdbcType=VARCHAR},
      </if>
      <if test="manageDepartment != null">
        manage_department = #{manageDepartment,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceSpu">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat Oct 12 01:26:15 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_spu
    set flow_instance_id = #{flowInstanceId,jdbcType=VARCHAR},
      flow_id = #{flowId,jdbcType=VARCHAR},
      shelf_catagory_id = #{shelfCatagoryId,jdbcType=VARCHAR},
      spu_code = #{spuCode,jdbcType=VARCHAR},
      spu_name = #{spuName,jdbcType=VARCHAR},
      product_standard = #{productStandard,jdbcType=INTEGER},
      product_type = #{productType,jdbcType=INTEGER},
      url = #{url,jdbcType=VARCHAR},
      manager = #{manager,jdbcType=VARCHAR},
      product_desc = #{productDesc,jdbcType=VARCHAR},
      application_area = #{applicationArea,jdbcType=VARCHAR},
      is_hidden_shelf = #{isHiddenShelf,jdbcType=VARCHAR},
      spu_service_provider = #{spuServiceProvider,jdbcType=VARCHAR},
      sale_tag = #{saleTag,jdbcType=VARCHAR},
      search_word = #{searchWord,jdbcType=VARCHAR},
      spu_remark = #{spuRemark,jdbcType=VARCHAR},
      aftermarket_admin_info = #{aftermarketAdminInfo,jdbcType=VARCHAR},
      manage_department = #{manageDepartment,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat Oct 12 01:26:15 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_spu
    (id, flow_instance_id, flow_id, shelf_catagory_id, spu_code, spu_name, product_standard, 
      product_type, url, manager, product_desc, application_area, is_hidden_shelf, spu_service_provider, 
      sale_tag, search_word, spu_remark, aftermarket_admin_info, manage_department, create_time, 
      update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.flowInstanceId,jdbcType=VARCHAR}, #{item.flowId,jdbcType=VARCHAR}, 
        #{item.shelfCatagoryId,jdbcType=VARCHAR}, #{item.spuCode,jdbcType=VARCHAR}, #{item.spuName,jdbcType=VARCHAR}, 
        #{item.productStandard,jdbcType=INTEGER}, #{item.productType,jdbcType=INTEGER}, 
        #{item.url,jdbcType=VARCHAR}, #{item.manager,jdbcType=VARCHAR}, #{item.productDesc,jdbcType=VARCHAR}, 
        #{item.applicationArea,jdbcType=VARCHAR}, #{item.isHiddenShelf,jdbcType=VARCHAR}, 
        #{item.spuServiceProvider,jdbcType=VARCHAR}, #{item.saleTag,jdbcType=VARCHAR}, 
        #{item.searchWord,jdbcType=VARCHAR}, #{item.spuRemark,jdbcType=VARCHAR}, #{item.aftermarketAdminInfo,jdbcType=VARCHAR}, 
        #{item.manageDepartment,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Sat Oct 12 01:26:15 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_spu (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'flow_instance_id'.toString() == column.value">
          #{item.flowInstanceId,jdbcType=VARCHAR}
        </if>
        <if test="'flow_id'.toString() == column.value">
          #{item.flowId,jdbcType=VARCHAR}
        </if>
        <if test="'shelf_catagory_id'.toString() == column.value">
          #{item.shelfCatagoryId,jdbcType=VARCHAR}
        </if>
        <if test="'spu_code'.toString() == column.value">
          #{item.spuCode,jdbcType=VARCHAR}
        </if>
        <if test="'spu_name'.toString() == column.value">
          #{item.spuName,jdbcType=VARCHAR}
        </if>
        <if test="'product_standard'.toString() == column.value">
          #{item.productStandard,jdbcType=INTEGER}
        </if>
        <if test="'product_type'.toString() == column.value">
          #{item.productType,jdbcType=INTEGER}
        </if>
        <if test="'url'.toString() == column.value">
          #{item.url,jdbcType=VARCHAR}
        </if>
        <if test="'manager'.toString() == column.value">
          #{item.manager,jdbcType=VARCHAR}
        </if>
        <if test="'product_desc'.toString() == column.value">
          #{item.productDesc,jdbcType=VARCHAR}
        </if>
        <if test="'application_area'.toString() == column.value">
          #{item.applicationArea,jdbcType=VARCHAR}
        </if>
        <if test="'is_hidden_shelf'.toString() == column.value">
          #{item.isHiddenShelf,jdbcType=VARCHAR}
        </if>
        <if test="'spu_service_provider'.toString() == column.value">
          #{item.spuServiceProvider,jdbcType=VARCHAR}
        </if>
        <if test="'sale_tag'.toString() == column.value">
          #{item.saleTag,jdbcType=VARCHAR}
        </if>
        <if test="'search_word'.toString() == column.value">
          #{item.searchWord,jdbcType=VARCHAR}
        </if>
        <if test="'spu_remark'.toString() == column.value">
          #{item.spuRemark,jdbcType=VARCHAR}
        </if>
        <if test="'aftermarket_admin_info'.toString() == column.value">
          #{item.aftermarketAdminInfo,jdbcType=VARCHAR}
        </if>
        <if test="'manage_department'.toString() == column.value">
          #{item.manageDepartment,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>