<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.handle.InventoryHandlerMapper">
    <resultMap id="inventoryInfoHandleMap" type="com.chinamobile.iot.sc.pojo.handle.InventoryInfoHandle">
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="sku_offering_name" jdbcType="VARCHAR" property="skuOfferingName"/>
        <result column="atom_offering_name" jdbcType="VARCHAR" property="atomOfferingName"/>
        <result column="atom_offering_class" jdbcType="VARCHAR" property="atomOfferingClass"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="color" jdbcType="VARCHAR" property="color"/>
        <result column="cooperator_name" jdbcType="VARCHAR" property="cooperatorName"/>
        <result column="partner_name" jdbcType="VARCHAR" property="partnerName"/>
        <result column="inventory" jdbcType="BIGINT" property="inventory"/>
        <result column="reserve_inventory" jdbcType="BIGINT" property="reserveInventory"/>
        <result column="inventory_threshold" jdbcType="BIGINT" property="inventoryThreshold"/>
        <result column="spu_offering_class" jdbcType="VARCHAR" property="spuOfferingClass"/>
        <result column="is_notice" jdbcType="BIT" property="isNotice"/>
        <result column="is_inventory" jdbcType="BIT" property="isInventory"/>
        <result column="spu_offering_code" jdbcType="VARCHAR" property="spuOfferingCode"/>
        <result column="spu_offering_name" jdbcType="VARCHAR" property="spuOfferingName"/>
        <result column="sku_offering_code" jdbcType="VARCHAR" property="skuOfferingCode"/>
        <result column="spu_offering_status" jdbcType="VARCHAR" property="spuOfferingStatus"/>
        <result column="sku_offering_status" jdbcType="VARCHAR" property="skuOfferingStatus"/>
        <result column="img_url" jdbcType="VARCHAR" property="imgUrl"/>
        <result column="atom_offering_code" jdbcType="VARCHAR" property="atomOfferingCode"/>
        <result column="inventory_management_mode_kx" jdbcType="VARCHAR" property="inventoryManagementModeKx"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="card_type" jdbcType="VARCHAR" property="cardType"/>
        <result column="template_name" jdbcType="VARCHAR" property="templateName"/>
        <result column="inventory_id" jdbcType="VARCHAR" property="inventoryId"/>
        <result column="cardInfoInventoryMainId" jdbcType="VARCHAR" property="cardInfoInventoryMainId"/>
        <result column="inventory_type" jdbcType="VARCHAR" property="inventoryType"/>
        <result column="hasCard" jdbcType="TINYINT" property="hasCard"/>
    </resultMap>

    <select id="selectInventoryByHandleQuery" resultMap="inventoryInfoHandleMap">
        select atom.id as id,
        sku.offering_name as sku_offering_name,
        atom.offering_name as atom_offering_name,
        atom.offering_class as atom_offering_class,
        sku.supplier_name as supplier_name,
        atom.model as model,
        atom.color as color,
        ap.user_name cooperator_name,
        ap.partner_name as partner_name,
        ap.is_primary as is_primary,
        atom.inventory as inventory,
        atom.reserve_inventory as reserve_inventory,
        atom.inventory_threshold as inventory_threshold,
        atom.is_notice as is_notice,
        ci.offering_class as spu_offering_class,
        atom.is_inventory as is_inventory,
        atom.offering_code as atom_offering_code,
        sku.offering_code as sku_offering_code,
        spu.offering_code as spu_offering_code,
        spu.offering_name as spu_offering_name,
        spu.img_url as img_url,
        spu.offering_status spu_offering_status,
        sku.offering_status sku_offering_status,
        atom.inventory_management_mode_kx as inventory_management_mode_kx,
        atom.inventory_id as inventory_id,
        sku.product_type product_type,
        sku.card_type card_type,
        sku.template_name template_name,
        sku.template_id templateId,
        sku.cust_name custName,
        sku.cust_code custCode,
        atom.inventory_main_id inventoryMainId,
        atom.card_info_inventory_main_id cardInfoInventoryMainId,
        if(atom.card_containing_terminal is not null and atom.card_containing_terminal = '2',false,true) hasCard
        from atom_offering_info atom
        left join category_info ci on atom.spu_id=ci.spu_id
        left join spu_offering_info spu on spu.offering_code = atom.spu_code and spu.delete_time is null
        left join sku_offering_info sku on atom.sku_code = sku.offering_code and sku.delete_time is null
        <if test="userIdList == null or userIdList.size() == 0">
            left join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct aocr.cooperator_id) cooperator_id,
            aocr.atom_offering_id,GROUP_CONCAT(distinct up.is_primary) is_primary
            from
            atom_offering_cooperator_relation aocr
            inner join user_partner up on aocr.cooperator_id = up.user_id
            where
            1=1
            group by aocr.atom_offering_id
            ) ap on ap.atom_offering_id = atom.id
        </if>
        <if test="(userIdList != null and userIdList.size() != 0) or (partnerName != null and partnerName != '') or (cooperatorName != null and cooperatorName != '')">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct aocr.cooperator_id) cooperator_id,
            aocr.atom_offering_id,GROUP_CONCAT(distinct up.is_primary) is_primary
            from
            atom_offering_cooperator_relation aocr
            inner join user_partner up on aocr.cooperator_id = up.user_id
            where
                1=1
            <if test="userIdList != null and userIdList.size() != 0">
                and aocr.cooperator_id in
                <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="partnerName != null and partnerName != ''">
                and up.partner_name LIKE concat ('%',#{partnerName},'%')
            </if>
            <if test="cooperatorName != null and cooperatorName != ''">
                and up.name like concat ('%',#{cooperatorName},'%')
            </if>
            group by aocr.atom_offering_id
            ) ap on ap.atom_offering_id = atom.id
        </if>
        where atom.delete_time is null and atom.offering_class not in ('S','C','A')
        <if test="inventoryStatus==0">
            and atom.inventory+atom.reserve_inventory <![CDATA[ > ]]> inventory_threshold
        </if>
        <if test="inventoryStatus==1">
            and atom.inventory+atom.reserve_inventory <![CDATA[ <= ]]> inventory_threshold
        </if>
        <!--<if test="userIdList != null and userIdList.size() != 0">
            and atom.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>-->
        <if test="spuOfferingName != null and spuOfferingName != ''">
            and spu.offering_name like concat ('%',#{spuOfferingName},'%')
        </if>
        <if test="skuOfferingName != null and skuOfferingName != ''">
            and sku.offering_name like concat ('%',#{skuOfferingName},'%')
        </if>
        <if test="spuOfferingCode != null and spuOfferingCode != ''">
            and spu.offering_code like concat ('%',#{spuOfferingCode},'%')
        </if>
        <if test="skuOfferingCode != null and skuOfferingCode != ''">
            and sku.offering_code like concat ('%',#{skuOfferingCode},'%')
        </if>
        <if test="atomOfferingName != null and atomOfferingName != ''">
            and atom.offering_name like concat ('%',#{atomOfferingName},'%')
        </if>
        <if test="spuOfferingClass != null and spuOfferingClass != ''">
            and ci.offering_class = #{spuOfferingClass}
        </if>
        <!--<if test="partnerName != null and partnerName != ''">
            and user_partner.partner_name LIKE concat ('%',#{partnerName},'%')
        </if>
        <if test="cooperatorName != null and cooperatorName != ''">
            and user_partner.name like concat ('%',#{cooperatorName},'%')
        </if>-->
        <if test="spuOfferingStatus != null and spuOfferingStatus != ''">
            and spu.offering_status = #{spuOfferingStatus}
        </if>
        <if test="skuOfferingStatus != null and skuOfferingStatus != ''">
            and sku.offering_status = #{skuOfferingStatus}
        </if>
        <if test="h5Key != null and h5Key != ''">
            and (spu.offering_name like concat ('%',#{h5Key},'%')
            or spu.offering_code like concat ('%',#{h5Key},'%')
            or sku.offering_code like concat ('%',#{h5Key},'%')
            or atom.offering_code like concat ('%',#{h5Key},'%'))
        </if>
        <if test="beId != null and beId != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = atom.sku_code and (srt.province_code =#{beId} or srt.province_code = '000'))
        </if>
        <if test="location != null and location != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = atom.sku_code and srt.city_code =#{location})

        </if>
        <if test=" h5SpuOfferingClasses != null and h5SpuOfferingClasses.size() != 0">
            and  ci.offering_class in
            <foreach collection="h5SpuOfferingClasses" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ORDER BY atom.update_time DESC
        limit #{page},#{num}
    </select>

    <select id="countInventoryByHandleQuery" resultType="java.lang.Long">
        select count(*)
        from atom_offering_info atom
        left join category_info ci on atom.spu_id=ci.spu_id
        left join spu_offering_info spu on spu.offering_code = atom.spu_code and spu.delete_time is null
        left join sku_offering_info sku on atom.sku_code = sku.offering_code and sku.delete_time is null
        and spu.offering_code = sku.spu_code
        <if test="userIdList == null or userIdList.size() == 0">
            left join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct aocr.cooperator_id) cooperator_id,
            aocr.atom_offering_id,GROUP_CONCAT(distinct up.is_primary) is_primary
            from
            atom_offering_cooperator_relation aocr
            inner join user_partner up on aocr.cooperator_id = up.user_id
            where
                1=1
            group by aocr.atom_offering_id
            ) ap on ap.atom_offering_id = atom.id
        </if>
        <if test="(userIdList != null and userIdList.size() != 0) or (partnerName != null and partnerName != '') or (cooperatorName != null and cooperatorName != '')">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct aocr.cooperator_id) cooperator_id,
            aocr.atom_offering_id,GROUP_CONCAT(distinct up.is_primary) is_primary
            from
            atom_offering_cooperator_relation aocr
            inner join user_partner up on aocr.cooperator_id = up.user_id
            where
                1=1
            <if test="userIdList != null and userIdList.size() != 0">
                and aocr.cooperator_id in
                <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="partnerName != null and partnerName != ''">
                and up.partner_name LIKE concat ('%',#{partnerName},'%')
            </if>
            <if test="cooperatorName != null and cooperatorName != ''">
                and up.name like concat ('%',#{cooperatorName},'%')
            </if>
            group by aocr.atom_offering_id
            ) ap on ap.atom_offering_id = atom.id
        </if>
        where atom.delete_time is null and atom.offering_class not in ('S','C','A')
        <if test="inventoryStatus==0">
            and atom.inventory+atom.reserve_inventory <![CDATA[ > ]]> inventory_threshold

        </if>
        <if test="inventoryStatus==1">
            and atom.inventory+atom.reserve_inventory <![CDATA[ <= ]]> inventory_threshold
        </if>
        <!--<if test="userIdList != null and userIdList.size() != 0">
            and atom.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>-->
        <if test="spuOfferingName != null and spuOfferingName != ''">
            and spu.offering_name like concat ('%',#{spuOfferingName},'%')
        </if>
        <if test="skuOfferingName != null and skuOfferingName != ''">
            and sku.offering_name like concat ('%',#{skuOfferingName},'%')
        </if>
        <if test="spuOfferingCode != null and spuOfferingCode != ''">
            and spu.offering_code like concat ('%',#{spuOfferingCode},'%')
        </if>
        <if test="skuOfferingCode != null and skuOfferingCode != ''">
            and sku.offering_code like concat ('%',#{skuOfferingCode},'%')
        </if>
        <if test="atomOfferingName != null and atomOfferingName != ''">
            and atom.offering_name like concat ('%',#{atomOfferingName},'%')
        </if>
        <if test="spuOfferingClass != null and spuOfferingClass != ''">
            and ci.offering_class = #{spuOfferingClass}
        </if>
        <!--<if test="partnerName != null and partnerName != ''">
            and user_partner.partner_name LIKE concat ('%',#{partnerName},'%')
        </if>
        <if test="cooperatorName != null and cooperatorName != ''">
            and user_partner.name like concat ('%',#{cooperatorName},'%')
        </if>-->
        <if test="spuOfferingStatus != null and spuOfferingStatus != ''">
            and spu.offering_status = #{spuOfferingStatus}
        </if>
        <if test="skuOfferingStatus != null and skuOfferingStatus != ''">
            and sku.offering_status = #{skuOfferingStatus}
        </if>
        <if test="h5Key != null and h5Key != ''">
            and (spu.offering_name like concat ('%',#{h5Key},'%')
            or spu.offering_code like concat ('%',#{h5Key},'%')
            or sku.offering_code like concat ('%',#{h5Key},'%')
            or atom.offering_code like concat ('%',#{h5Key},'%'))
        </if>
        <if test="beId != null and beId != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = atom.sku_code and (srt.province_code =#{beId} or srt.province_code = '000'))
        </if>
        <if test="location != null and location != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = atom.sku_code and srt.city_code =#{location})
        </if>
        <if test=" h5SpuOfferingClasses != null and h5SpuOfferingClasses.size() != 0">
            and  ci.offering_class in
            <foreach collection="h5SpuOfferingClasses" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listExportInventory" resultType="com.chinamobile.iot.sc.pojo.mapper.InventoryExportDO">
        select
        spu.offering_name spuOfferingName,
        spu.offering_code spuOfferingCode,
        ci.offering_class spuOfferingClass,
        atom.create_time createTime,
        case
        when spu.offering_status = 0 then '测试'
        when spu.offering_status = 1 then '发布'
        when spu.offering_status = 2 then '下架'
        else ''
        end spuOfferingStatusName,
        sku.offering_name skuOfferingName,
        sku.offering_code skuOfferingCode,
        sku.price,
        case
        when sku.offering_status = 0 then '测试'
        when sku.offering_status = 1 then '发布'
        when sku.offering_status = 2 then '下架'
        else ''
        end skuOfferingStatusName,
        srt.province_code provinceCode,
        srt.city_code cityCode,
        atom.offering_name atomOfferingName,
        atom.offering_code offeringCode,
        atom.offering_class atomOfferingClass,
        atom.ext_soft_offering_code extSoftOfferingCode,
        atom.ext_hard_offering_code extHardOfferingCode,
        atom.quantity,
        atom.model,
        atom.color,
        atom.settle_price settlePrice,
        atom.offeringSaleRegion,
        sku.supplier_name supplierName,
        ap.partner_name partnerName,
        ap.user_name cooperatorName,
        atom.inventory_threshold inventoryThreshold,
        atom.inventory,
        atom.reserve_inventory reserveInventory,
        atom.inventory_id inventoryId,
        atom.inventory_main_id inventoryMainId,
        spu.inventory_type inventoryType
        from atom_offering_info atom
        left join category_info ci on atom.spu_id=ci.spu_id
        left join spu_offering_info spu on spu.offering_code = atom.spu_code and spu.delete_time is null
        left join sku_offering_info sku on atom.sku_code = sku.offering_code and sku.delete_time is null
        and spu.offering_code = sku.spu_code
        left join sku_release_target srt on srt.sku_offering_code = atom.sku_code
        <if test="inventoryExportParam.userIdList == null or inventoryExportParam.userIdList.size() == 0">
            left join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct aocr.cooperator_id) cooperator_id,
            aocr.atom_offering_id,GROUP_CONCAT(distinct up.is_primary) is_primary
            from
            atom_offering_cooperator_relation aocr
            inner join user_partner up on aocr.cooperator_id = up.user_id
            where
            1=1
            group by aocr.atom_offering_id
            ) ap on ap.atom_offering_id = atom.id
        </if>
        <if test="(inventoryExportParam.userIdList != null and inventoryExportParam.userIdList.size() != 0) or (inventoryExportParam.partnerName != null and inventoryExportParam.partnerName != '')">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct aocr.cooperator_id) cooperator_id,
            aocr.atom_offering_id,GROUP_CONCAT(distinct up.is_primary) is_primary
            from
            atom_offering_cooperator_relation aocr
            inner join user_partner up on aocr.cooperator_id = up.user_id
            where
            1=1
            <if test="inventoryExportParam.userIdList != null and inventoryExportParam.userIdList.size() != 0">
                and aocr.cooperator_id in
                <foreach collection="inventoryExportParam.userIdList" item="userId" index="index" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>

            <if test="inventoryExportParam.partnerName != null and inventoryExportParam.partnerName != ''">
                and up.partner_name LIKE concat ('%',#{inventoryExportParam.partnerName},'%')
            </if>

            <if test="inventoryExportParam.beId != null and inventoryExportParam.beId != '' ">
                and up.be_id =#{inventoryExportParam.beId}
            </if>
            group by aocr.atom_offering_id
            ) ap on ap.atom_offering_id = atom.id
        </if>
        where atom.delete_time is null and atom.offering_class not in ('S','C','A')
        <if test="inventoryExportParam.startTime != null and inventoryExportParam.startTime != ''">
            and atom.create_time <![CDATA[ >= ]]> #{inventoryExportParam.startTime}
        </if>
        <if test="inventoryExportParam.endTime != null and inventoryExportParam.endTime != ''">
            and atom.create_time  <![CDATA[ <= ]]> #{inventoryExportParam.endTime}
        </if>
        <if test="inventoryExportParam.spuOfferingStatus != null">
            and spu.offering_status = #{inventoryExportParam.spuOfferingStatus}
        </if>
        <if test="inventoryExportParam.skuOfferingStatus != null">
            and sku.offering_status = #{inventoryExportParam.skuOfferingStatus}
        </if>
        <if test="inventoryExportParam.skuReleaseProvince != null and inventoryExportParam.skuReleaseProvince != ''">
            and (srt.province_code = #{inventoryExportParam.skuReleaseProvince} or srt.province_code = '000')
        </if>
        <if test="inventoryExportParam.skuReleaseCity != null and inventoryExportParam.skuReleaseCity != ''">
            and srt.city_code = #{inventoryExportParam.skuReleaseCity}
        </if>
        <!--<if test="inventoryExportParam.partnerName != null and inventoryExportParam.partnerName != ''">
            and user_partner.partner_name LIKE concat ('%',#{inventoryExportParam.partnerName},'%')
        </if>-->
        <if test="inventoryExportParam.inventoryStatus==0">
            and atom.inventory+atom.reserve_inventory <![CDATA[ < ]]> inventory_threshold
        </if>
        <if test="inventoryExportParam.inventoryStatus==1">
            and ((atom.inventory+atom.reserve_inventory <![CDATA[ >= ]]> inventory_threshold) or spu.inventory_type = 1)
        </if>
        <!--<if test="inventoryExportParam.beId != null and inventoryExportParam.beId != '' ">
            and user_partner.be_id =#{inventoryExportParam.beId}
        </if>-->
        <if test="inventoryExportParam.location != null and inventoryExportParam.location != '' ">
            and srt.city_code =#{inventoryExportParam.location}
        </if>
        <if test="inventoryExportParam.userIdList != null and inventoryExportParam.userIdList.size() != 0">
            and ci.offering_class not in ('A04','A08','A09','A12','A14','A15','A16','A17')
        </if>
        ORDER BY spu.offering_name DESC
    </select>

    <select id="getCardXAtomInfo" resultType="com.chinamobile.iot.sc.pojo.dto.CardXAtomInfoDTO">
        select distinct
        atom.id atomId,
        atom.inventory_management_mode_kx inventoryManagementModeKx,
        atom.inventory_id inventoryId,
        sku.card_type cardType,
        sku.template_name templateName,
        srt.province_code beId,
        atom.spu_code spuCode,
        atom.sku_code skuCode,
        atom.offering_code atomOfferingCode
        from
        atom_offering_info atom
        left join spu_offering_info spu on spu.offering_code = atom.spu_code and spu.delete_time is null
        left join sku_offering_info sku on atom.sku_code = sku.offering_code and sku.delete_time is null
        and spu.offering_code = sku.spu_code
        left join sku_release_target srt on atom.sku_code = srt.sku_offering_code
        <if test="cardXAtomInfoParam.userIdList != null and cardXAtomInfoParam.userIdList.size() != 0">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct aocr.cooperator_id) cooperator_id,
            aocr.atom_offering_id,GROUP_CONCAT(distinct up.is_primary) is_primary
            from
            atom_offering_cooperator_relation aocr
            inner join user_partner up on aocr.cooperator_id = up.user_id
            where
                1=1
            and aocr.cooperator_id in
            <foreach collection="cardXAtomInfoParam.userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
            group by aocr.atom_offering_id
            ) ap on ap.atom_offering_id = atom.id
        </if>
        where
        sku.product_type in ('4','5','6','7','8')
        and atom.offering_class = 'X'
        and atom.delete_time is null
        <!--<if test="cardXAtomInfoParam.userIdList != null and cardXAtomInfoParam.userIdList.size() != 0">
            and atom.cooperator_id in
            <foreach collection="cardXAtomInfoParam.userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>-->
        <if test="cardXAtomInfoParam.atomId != null and cardXAtomInfoParam.atomId !=  ''">
            and atom.id = #{cardXAtomInfoParam.atomId}
        </if>
    </select>


    <update id="updateInventoryConfigByPrimaryKey">
        update atom_offering_info atom
        <set>
            <if test="threshold!=null">
                inventory_threshold = #{threshold,jdbcType=BIGINT},
            </if>
            <if test="isNotice != null">
                is_notice = #{isNotice,jdbcType=BIT},
            </if>
            <if test="addAmount!=null">
                atom.inventory= atom.inventory+#{addAmount,jdbcType=BIGINT},
            </if>
        </set>
        <where>
            atom.id=#{id}
            and atom.inventory+atom.reserve_inventory+#{addAmount,jdbcType=BIGINT}  <![CDATA[ >= ]]> 0
--             and atom.offering_class ='H'
        </where>
    </update>

    <update id="updateInventoryConfigOversoldByPrimaryKey">
        update atom_offering_info atom
        <set>
            <if test="threshold!=null">
                inventory_threshold = #{threshold,jdbcType=BIGINT},
            </if>
            <if test="isNotice != null">
                is_notice = #{isNotice,jdbcType=BIT},
            </if>
            <if test="addAmount!=null">
                atom.inventory= atom.inventory+#{addAmount,jdbcType=BIGINT},
            </if>
        </set>
        <where>
            atom.id=#{id}
        </where>
    </update>

    <update id="updateInventoryByPrimaryKey">
        update atom_offering_info atom
        <set>
            atom.inventory=atom.inventory-atom.quantity*#{skuQuantity},
            atom.reserve_inventory=atom.reserve_inventory+atom.quantity*#{skuQuantity}
        </set>
        where atom.id=#{id} and atom.inventory-atom.quantity*#{skuQuantity} <![CDATA[ >= ]]> 0
    </update>

    <update id="updateNoInventoryByPrimaryKey">
        update atom_offering_info atom
        <set>
            atom.reserve_inventory=atom.reserve_inventory+#{atomQuantity}
        </set>
        where atom.id=#{id}
    </update>

    <update id="updateInventoryPaymentByPrimaryKey">
        update atom_offering_info atom
        <set>
            atom.inventory=atom.inventory-atom.quantity*#{skuQuantity},
            atom.reserve_inventory=atom.reserve_inventory+atom.quantity*#{skuQuantity}
        </set>
        where atom.id=#{id}
    </update>

    <update id="updateInventoryServiceByPrimaryKey">
        update atom_offering_info atom
        <set>
            atom.inventory=atom.inventory-#{atomQuantity},
            atom.reserve_inventory=atom.reserve_inventory+#{atomQuantity}
        </set>
        where atom.id=#{id}
    </update>
    <select id="getDICTLimitList"   resultType="com.chinamobile.iot.sc.response.web.LimitInfoDTO">
        select *
        from service_pack_limit_amount spla
        <if test="param.productName != null and param.productName !='' ">
            and spla.product_name like concat('%',#{param.productName},'%')
        </if>
        <if test="param.serviceName != null and param.serviceName !='' ">
            and spla.service_name like concat('%',#{param.serviceName},'%')
        </if>
        <if test="param.status != null and param.status !='' ">
            and spla.status = #{param.status}
        </if>
        <if test="param.companyId != null and param.companyId !='' ">
            and spla.company_id = #{param.companyId}
        </if>
        <if test="param.chargeId != null and param.chargeId !='' ">
            and aoi.charge_id = #{param.chargeId}
        </if>
    </select>

    <update id="updateDICTLimitByPrimaryKey">
        update service_pack_limit_amount spla
        <set>
            spla.reserve_quatity=spla.reserve_quatity+#{limit},
            spla.current_inventory=spla.current_inventory-#{limit},
            spla.use_inventory=if(spla.current_inventory is null,#{limit},spla.use_inventory+#{limit}),
            spla.status = if(#{limit}<![CDATA[ >= ]]>spla.current_inventory,'0','1')
        </set>
        where spla.id=#{id}
    </update>
    <update id="releaseInventoryByPrimaryKey">
        update atom_offering_info atom
        <set>
            atom.inventory=atom.inventory+atom.quantity*#{skuQuantity},
            atom.reserve_inventory=atom.reserve_inventory-atom.quantity*#{skuQuantity}
        </set>
        where atom.id=#{id} and atom.reserve_inventory-atom.quantity*#{skuQuantity} <![CDATA[ >= ]]> 0
    </update>

    <update id="releaseInventoryServiceByPrimaryKey">
        update atom_offering_info atom
        <set>
            atom.inventory=atom.inventory+#{atomQuantity},
            atom.reserve_inventory=atom.reserve_inventory-#{atomQuantity}
        </set>
        where atom.id=#{id} and atom.reserve_inventory-#{atomQuantity} <![CDATA[ >= ]]> 0
    </update>

    <update id="releaseNoInventoryByPrimaryKey">
        update atom_offering_info atom
        <set>
            atom.reserve_inventory=atom.reserve_inventory-#{atomQuantity}
        </set>
        where atom.id=#{id} and atom.reserve_inventory-#{atomQuantity} <![CDATA[ >= ]]> 0
    </update>

    <update id="reduceReserveInventory">
        update atom_offering_info atom
        <set>
            atom.reserve_inventory=atom.reserve_inventory-atom.quantity*#{skuQuantity}
        </set>
        where atom.id=#{id} and atom.reserve_inventory-atom.quantity*#{skuQuantity} <![CDATA[ >= ]]> 0
    </update>

    <update id="reduceReserveInventoryService">
        update atom_offering_info atom
        <set>
            atom.reserve_inventory=atom.reserve_inventory-#{atomQuantity}
        </set>
        where atom.id=#{id} and atom.reserve_inventory-#{atomQuantity} <![CDATA[ >= ]]> 0
    </update>
    <update id="reduceLimitInventoryService">
        update service_pack_limit_amount spla
        <set>
            spla.reserve_quatity=spla.reserve_quatity-#{limit},

        </set>
        where spla.id=#{id} and spla.reserve_quatity-#{limit} <![CDATA[ >= ]]> 0
    </update>
    <update id="recoverLimitInventoryService">
        update service_pack_limit_amount spla
        <set>
            spla.current_inventory= spla.current_inventory +#{limit},
            spla.use_inventory = spla.use_inventory -#{limit},
            spla.status = '1',
        </set>
        where spla.id=#{id}
    </update>
    <update id="releaseLimitInventoryService">
        update service_pack_limit_amount spla
        <set>
            spla.reserve_quatity=spla.reserve_quatity-#{limit},
            spla.current_inventory= spla.current_inventory +#{limit},
            spla.use_inventory = spla.use_inventory -#{limit},
            spla.status = '1',
        </set>
        where spla.id=#{id}
    </update>

    <update id="recoverInventory">
        update atom_offering_info atom
        <set>
            atom.inventory=atom.inventory+atom.quantity*#{skuQuantity}
        </set>
        where atom.id=#{id}
    </update>


    <update id="recoverInventoryService">
        update atom_offering_info atom
        <set>
            atom.inventory=atom.inventory+#{atomQuantity}
        </set>
        where atom.id=#{id}
    </update>

    <update id="updateServicePackInventoryConfigByService">
        update contract_material cm
        <set>
            cm.service_quota_remain = cm.service_quota_remain-#{serviceQuota},
            cm.service_quota_used = if(cm.service_quota_used is null,#{serviceQuota}, cm.service_quota_used +#{serviceQuota} ),
            cm.service_quota_reverse = if(cm.service_quota_reverse is null,#{serviceQuota}, cm.service_quota_reverse +#{serviceQuota} ),
        </set>
        <where>
            cm.contract_number=#{contractNum} and cm.service_pack_id=#{servicePackId}
            and cm.service_quota_remain-#{serviceQuota}  <![CDATA[ >= ]]> 0
        </where>
    </update>

    <update id="updateMaterialInventoryConfigByMaterial">
        update contract_material cm
        <set>
            cm.material_quota_remain = cm.material_quota_remain-#{materialQuota},
            cm.material_quota_used = if(cm.material_quota_used is null,#{materialQuota}, cm.material_quota_used +#{materialQuota} ),
            cm.material_quota_reverse = if(cm.material_quota_reverse is null,#{materialQuota}, cm.material_quota_reverse +#{materialQuota} ),
        </set>
        <where>
            cm.contract_number=#{contractNum} and cm.service_pack_id=#{servicePackId} and cm.material_number=#{materialNum}
            and cm.material_quota_remain-#{materialQuota}  <![CDATA[ >= ]]> 0
        </where>
    </update>

    <update id="releaseServicePackInventoryConfigByService">
        update contract_material cm
        <set>
            cm.service_quota_remain = cm.service_quota_remain+#{serviceQuota},
            cm.service_quota_used = cm.service_quota_used-#{serviceQuota},
            cm.service_quota_reverse = cm.service_quota_reverse-#{serviceQuota},
        </set>
        <where>
            cm.contract_number=#{contractNum} and cm.service_pack_id=#{servicePackId}
            and cm.service_quota_reverse-#{serviceQuota}  <![CDATA[ >= ]]> 0
        </where>
    </update>

    <update id="releaseMaterialInventoryConfigByMaterial">
        update contract_material cm
        <set>
            cm.material_quota_remain = cm.material_quota_remain+#{materialQuota},
            cm.material_quota_used = cm.material_quota_used-#{materialQuota},
            cm.material_quota_reverse = cm.material_quota_reverse-#{materialQuota},
        </set>
        <where>
            cm.contract_number=#{contractNum} and cm.service_pack_id=#{servicePackId} and cm.material_number=#{materialNum}
            and cm.material_quota_reverse-#{materialQuota}  <![CDATA[ >= ]]> 0
        </where>
    </update>

    <update id="reduceServiceReserveInventory">
        update contract_material cm
        <set>
            cm.service_quota_reverse=cm.service_quota_reverse-#{serviceQuota}
        </set>
        where cm.contract_number=#{contractNum} and cm.service_pack_id=#{servicePackId} and cm.service_quota_reverse-#{serviceQuota} <![CDATA[ >= ]]> 0
    </update>

    <update id="reduceMaterialReserveInventory">
        update contract_material cm
        <set>
            cm.material_quota_reverse=cm.material_quota_reverse-#{materialQuota}
        </set>
        where cm.contract_number=#{contractNum} and cm.service_pack_id=#{servicePackId}
            and cm.material_number=#{materialNum} and cm.material_quota_reverse=cm.material_quota_reverse-#{materialQuota} <![CDATA[ >= ]]> 0
    </update>

    <update id="recoverServiceReserveInventory">
        update contract_material cm
        <set>
            cm.service_quota_remain=cm.service_quota_remain+#{serviceQuota}
        </set>
        where cm.contract_number=#{contractNum} and cm.service_pack_id=#{servicePackId}
    </update>

    <update id="recoverMaterialReserveInventory">
        update contract_material cm
        <set>
            cm.material_quota_remain=cm.material_quota_remain+#{materialQuota}
        </set>
        where cm.contract_number=#{contractNum} and cm.service_pack_id=#{servicePackId}
        and cm.material_number=#{materialNum}
    </update>

    <update id="reserveCardXInventoryByInventoryMainId">
        update atom_offering_info atom
        left join dkcardx_inventory_detail_info didi on atom.inventory_main_id = didi.inventory_main_id
        <set>
            didi.current_inventory=didi.current_inventory-#{atomQuantity},
            didi.reserve_quatity=didi.reserve_quatity+#{atomQuantity},
            didi.total_inventory = didi.current_inventory + didi.reserve_quatity,
            atom.reserve_inventory = atom.reserve_inventory + #{atomQuantity}
        </set>
        where didi.inventory_main_id=#{inventoryMainId} and atom.id = #{atomId}
        <if test="regionProvince != null and regionProvince != ''">
            and didi.be_id = #{regionProvince} and didi.province_alias_name is not null and didi.province_alias_name != ''
        </if>
        <if test="regionCity != null and regionCity != ''">
            and didi.location = #{regionCity} and (didi.province_alias_name is null or didi.province_alias_name = '')
        </if>
        and didi.current_inventory-#{atomQuantity} <![CDATA[ >= ]]> 0
    </update>


    <update id="reserveCardXPayInventoryByInventoryMainId">
        update atom_offering_info atom
        left join dkcardx_inventory_detail_info didi on atom.inventory_main_id = didi.inventory_main_id
        <set>
            didi.current_inventory=didi.current_inventory-#{atomQuantity},
            didi.reserve_quatity=didi.reserve_quatity+#{atomQuantity},
            didi.total_inventory = didi.current_inventory + didi.reserve_quatity,
            atom.reserve_inventory = atom.reserve_inventory + #{atomQuantity}
        </set>
        where didi.inventory_main_id=#{inventoryMainId}  and atom.id = #{atomId}
        <if test="regionProvince != null and regionProvince != ''">
            and didi.be_id = #{regionProvince} and didi.province_alias_name is not null and didi.province_alias_name != ''
        </if>
        <if test="regionCity != null and regionCity != ''">
            and didi.location = #{regionCity} and (didi.province_alias_name is null or didi.province_alias_name = '')
        </if>
    </update>


    <update id="reserveCardXInventoryByPrimaryKey">
        update atom_offering_info atom
        left join dkcardx_inventory_info cxinv on atom.inventory_id = cxinv.inventory_id
        <set>
            cxinv.current_inventory=cxinv.current_inventory-atom.quantity*#{skuQuantity},
            cxinv.reserve_quatity=cxinv.reserve_quatity+atom.quantity*#{skuQuantity},
            cxinv.total_inventory = cxinv.current_inventory + cxinv.reserve_quatity
        </set>
        where cxinv.inventory_id=#{id}
        <if test="regionProvince != null and regionProvince != ''">
            and cxinv.be_id = #{regionProvince}
        </if>
        <if test="regionCity != null and regionCity != ''">
            and cxinv.location = #{regionCity}
        </if>
        and cxinv.current_inventory-atom.quantity*#{skuQuantity} <![CDATA[ >= ]]> 0
    </update>


    <update id="reserveCardXPayInventoryByPrimaryKey">
        update atom_offering_info atom
        left join dkcardx_inventory_info cxinv on atom.inventory_id = cxinv.inventory_id
        <set>
            cxinv.current_inventory=cxinv.current_inventory-atom.quantity*#{skuQuantity},
            cxinv.reserve_quatity=cxinv.reserve_quatity+atom.quantity*#{skuQuantity},
            cxinv.total_inventory = cxinv.current_inventory + cxinv.reserve_quatity
        </set>
        where cxinv.inventory_id=#{id}
        <if test="regionProvince != null and regionProvince != ''">
            and cxinv.be_id = #{regionProvince}
        </if>
        <if test="regionCity != null and regionCity != ''">
            and cxinv.location = #{regionCity}
        </if>
    </update>

    <update id="reduceKXReserveInventory">
        update atom_offering_info atom
        left join dkcardx_inventory_detail_info didi on atom.inventory_main_id = didi.inventory_main_id
        <set>
            didi.reserve_quatity=didi.reserve_quatity-#{reserveQuantity},
            didi.total_inventory = didi.total_inventory -#{reserveQuantity},
            atom.reserve_inventory = atom.reserve_inventory -#{reserveQuantity},
            didi.update_time = now(),
            atom.update_time = now()
        </set>
        where didi.inventory_main_id=#{inventoryMainId}  and atom.id = #{atomId}
        <if test="regionProvince != null and regionProvince != ''">
            and didi.be_id = #{regionProvince} and didi.province_alias_name is not null and didi.province_alias_name != ''
        </if>
        <if test="regionCity != null and regionCity != ''">
            and didi.location = #{regionCity} and (didi.province_alias_name is null or didi.province_alias_name = '')
        </if>
        and didi.reserve_quatity-#{reserveQuantity} <![CDATA[ >= ]]> 0
    </update>

    <update id="releaseKxInventoryByPrimaryKey">
        update atom_offering_info atom
        left join dkcardx_inventory_detail_info didi on atom.inventory_main_id = didi.inventory_main_id
        <set>
            didi.current_inventory=didi.current_inventory+#{reserveQuantity},
            didi.reserve_quatity=didi.reserve_quatity-#{reserveQuantity},
            didi.total_inventory = didi.current_inventory + didi.reserve_quatity,
            atom.reserve_inventory = atom.reserve_inventory -#{reserveQuantity}
        </set>
        where didi.inventory_main_id=#{inventoryMainId}  and atom.id = #{atomId}
        <if test="regionProvince != null and regionProvince != ''">
            and didi.be_id = #{regionProvince} and didi.province_alias_name is not null and didi.province_alias_name != ''
        </if>
        <if test="regionCity != null and regionCity != ''">
            and didi.location = #{regionCity} and (didi.province_alias_name is null or didi.province_alias_name = '')
        </if>
        and didi.reserve_quatity-#{reserveQuantity} <![CDATA[ >= ]]> 0

    </update>

    <update id="recoverKxInventoryByPrimaryKey">
        update atom_offering_info atom
        left join dkcardx_inventory_detail_info didi on atom.inventory_main_id = didi.inventory_main_id
        <set>
            didi.current_inventory=didi.current_inventory+#{reserveQuantity},
            didi.total_inventory = didi.current_inventory+#{reserveQuantity}
        </set>
        where didi.inventory_main_id=#{inventoryMainId}  and atom.id = #{atomId}
        <if test="regionProvince != null and regionProvince != ''">
            and didi.be_id = #{regionProvince} and didi.province_alias_name is not null and didi.province_alias_name != ''
        </if>
        <if test="regionCity != null and regionCity != ''">
            and didi.location = #{regionCity} and (didi.province_alias_name is null or didi.province_alias_name = '')
        </if>

    </update>

    <select id="selectInventoryNewByHandleQuery" resultMap="inventoryInfoHandleMap">
        select
        sku.offering_name as sku_offering_name,
        sku.supplier_name as supplier_name,
        ci.offering_class as spu_offering_class,
        sku.offering_code as sku_offering_code,
        spu.offering_code as spu_offering_code,
        spu.offering_name as spu_offering_name,
        spu.img_url as img_url,
        spu.offering_status spu_offering_status,
        sku.offering_status sku_offering_status,
        sku.product_type product_type,
        sku.card_type card_type,
        sku.template_name template_name,
        spu.inventory_type inventoryType,
        <if test="userIdList != null and userIdList.size() != 0">
            ap.user_name as cooperator_name,
        </if>
        <if test="userIdList == null or userIdList.size == 0">
            ap.f_user_name as cooperator_name,
        </if>
        ap.partner_name as partner_name,
        ap.is_primary as is_primary,
        atom.config_all_time as configAllTime,
        if ((SELECT count(*) FROM atom_offering_info WHERE sku_code = sku.offering_code and offering_class not in ('S','C','A') AND is_inventory is true) > 0,1,0) is_inventory,
        <if test="h5SpuOfferingClasses!=null and h5SpuOfferingClasses.contains('A11'.toString())">
            if((SELECT
            count(*)
            FROM atom_offering_info atom
            LEFT JOIN sku_release_target srt ON srt.sku_offering_code = atom.sku_code
            left JOIN dkcardx_inventory_detail_info did ON atom.inventory_main_id = did.inventory_main_id and did.province_alias_name is not null and did.province_alias_name != ''
            left JOIN dkcardx_inventory_detail_info did_city ON atom.inventory_main_id = did_city.inventory_main_id and (did_city.province_alias_name is null or did_city.province_alias_name = '') and did_city.location = srt.city_code
            where atom.sku_code = sku.offering_code and
            atom.offering_class not in ('S','C','A') and
            (COALESCE(did.total_inventory, 0) + COALESCE(did_city.total_inventory, 0))<![CDATA[ < ]]> atom.inventory_threshold) >0,0,1) inventoryStatus
        </if>
        <if test="h5SpuOfferingClasses!=null and !h5SpuOfferingClasses.contains('A11'.toString())">
            if ((SELECT count(*) FROM atom_offering_info WHERE sku_code = sku.offering_code and offering_class not in ('S','C','A') AND inventory+reserve_inventory <![CDATA[ < ]]> inventory_threshold) > 0,0,1) inventoryStatus
        </if>
        from sku_offering_info sku
        left join atom_offering_info atom on atom.id = (select id from atom_offering_info where sku_code = sku.offering_code and offering_class not in ('S','C','A')  limit 1)
        <!--left join user_partner u on atom.cooperator_id = u.user_id
        left join user_partner fuser on fuser.partner_name = u.partner_name and fuser.is_primary = 1 and fuser.is_cancel = 0 and fuser.is_logoff = 0-->
        left join spu_offering_info spu on spu.offering_code = sku.spu_code and spu.delete_time is null
        left join category_info ci on sku.spu_id=ci.spu_id
        <if test="userIdList == null or userIdList.size() == 0">
            left join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct aocr.cooperator_id) cooperator_id,
            aocr.atom_offering_id,GROUP_CONCAT(distinct up.is_primary) is_primary,group_concat(distinct fuser.name)  f_user_name
            from
            atom_offering_cooperator_relation aocr
            inner join user_partner up on aocr.cooperator_id = up.user_id
            left join user_partner fuser on fuser.partner_name = up.partner_name and fuser.is_primary = 1 and fuser.is_cancel = 0 and fuser.is_logoff = 0
            where
                1=1
            group by aocr.atom_offering_id
            ) ap on ap.atom_offering_id = atom.id
        </if>
        <if test="(userIdList != null and userIdList.size() != 0) or (partnerName != null and partnerName != '') or (cooperatorName != null and cooperatorName != '')">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct aocr.cooperator_id) cooperator_id,
            aocr.atom_offering_id,GROUP_CONCAT(distinct up.is_primary) is_primary,group_concat(distinct fuser.name)  f_user_name
            from
            atom_offering_cooperator_relation aocr
            inner join user_partner up on aocr.cooperator_id = up.user_id
            left join user_partner fuser on fuser.partner_name = up.partner_name and fuser.is_primary = 1 and fuser.is_cancel = 0 and fuser.is_logoff = 0
            where
                1=1
            <if test="userIdList != null and userIdList.size() != 0">
                and aocr.cooperator_id in
                <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="partnerName != null and partnerName != ''">
                and up.partner_name like concat ('%',#{partnerName},'%')
            </if>
            <if test="cooperatorName != null and cooperatorName != ''">
                and up.name like concat ('%',#{cooperatorName},'%')
            </if>
            group by aocr.atom_offering_id
            ) ap on ap.atom_offering_id = atom.id
        </if>
        where sku.delete_time is null
        <if test="h5SpuOfferingClasses!=null and !h5SpuOfferingClasses.contains('A11'.toString())">
            <if test="inventoryStatus==1">
                and (SELECT count(*) FROM atom_offering_info WHERE sku_code = sku.offering_code and offering_class not in ('S','C','A') AND inventory+reserve_inventory <![CDATA[ <= ]]> inventory_threshold) <![CDATA[ <= ]]> 0
            </if>
            <if test="inventoryStatus==0">
                and (SELECT count(*) FROM atom_offering_info WHERE sku_code = sku.offering_code and offering_class not in ('S','C','A') AND inventory+reserve_inventory <![CDATA[ <= ]]> inventory_threshold) > 0
            </if>
        </if>
        <if test="h5SpuOfferingClasses!=null and h5SpuOfferingClasses.contains('A11'.toString())">
            <if test="inventoryStatus==1">
                and (SELECT
                count(*)
                FROM atom_offering_info atom
                LEFT JOIN sku_release_target srt ON srt.sku_offering_code = atom.sku_code
                left JOIN dkcardx_inventory_detail_info did ON atom.inventory_main_id = did.inventory_main_id and did.province_alias_name is not null
                left JOIN dkcardx_inventory_detail_info did_city ON atom.inventory_main_id = did_city.inventory_main_id and did_city.province_alias_name is null and did_city.location = srt.city_code
                where atom.sku_code = sku.offering_code and
                atom.offering_class not in ('S','C','A') and
                (COALESCE(did.total_inventory, 0) + COALESCE(did_city.total_inventory, 0))<![CDATA[ <= ]]> atom.inventory_threshold) <![CDATA[ <= ]]> 0
            </if>
            <if test="inventoryStatus==0">
                and (SELECT
                count(*)
                FROM atom_offering_info atom
                LEFT JOIN sku_release_target srt ON srt.sku_offering_code = atom.sku_code
                left JOIN dkcardx_inventory_detail_info did ON atom.inventory_main_id = did.inventory_main_id and did.province_alias_name is not null and did.province_alias_name != ''
                left JOIN dkcardx_inventory_detail_info did_city ON atom.inventory_main_id = did_city.inventory_main_id and (did_city.province_alias_name is null or did_city.province_alias_name = '') and did_city.location = srt.city_code
                where atom.sku_code = sku.offering_code and
                atom.offering_class not in ('S','C','A') and
                (COALESCE(did.total_inventory, 0) + COALESCE(did_city.total_inventory, 0))<![CDATA[ <= ]]> atom.inventory_threshold) > 0
            </if>
            and sku.product_type in ('4','5','6','7','8','9','10','11','12')
        </if>
        <if test="spuOfferingName != null and spuOfferingName != ''">
            and spu.offering_name like concat ('%',#{spuOfferingName},'%')
        </if>
        <if test="skuOfferingName != null and skuOfferingName != ''">
            and sku.offering_name like concat ('%',#{skuOfferingName},'%')
        </if>
        <if test="spuOfferingCode != null and spuOfferingCode != ''">
            and spu.offering_code like concat ('%',#{spuOfferingCode},'%')
        </if>
        <if test="skuOfferingCode != null and skuOfferingCode != ''">
            and sku.offering_code like concat ('%',#{skuOfferingCode},'%')
        </if>
        <if test="spuOfferingClass != null and spuOfferingClass.size() != 0">
            and ci.offering_class in
            <foreach collection="spuOfferingClass" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <!--<if test="partnerName != null and partnerName != ''">
            and
            u.partner_name like concat ('%',#{partnerName},'%')
        </if>
        <if test="cooperatorName != null and cooperatorName != ''">
            and u.name like concat ('%',#{cooperatorName},'%')
        </if>-->
        <if test="spuOfferingStatus != null and spuOfferingStatus.size() != 0">
            and spu.offering_status in
            <foreach collection="spuOfferingStatus" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="skuOfferingStatus != null and skuOfferingStatus.size() != 0">
            and sku.offering_status in
            <foreach collection="skuOfferingStatus" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="h5SpuOfferingClasses != null and h5SpuOfferingClasses.size() != 0">
            and  ci.offering_class in
            <foreach collection="h5SpuOfferingClasses" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="h5Key != null and h5Key != ''">
            and (spu.offering_name like concat ('%',#{h5Key},'%')
            or spu.offering_code like concat ('%',#{h5Key},'%')
            or sku.offering_code like concat ('%',#{h5Key},'%')
            or atom.offering_code like concat ('%',#{h5Key},'%'))
        </if>
        <if test="inventoryType != null and inventoryType != ''">
            and spu.inventory_type = #{inventoryType}
        </if>
        <if test="atomOfferingName != null and atomOfferingName != ''">
            and ((SELECT count(*) FROM atom_offering_info WHERE sku_code = sku.offering_code AND offering_name like concat ('%',#{atomOfferingName},'%'))>0)
        </if>
        <if test="beId != null and beId != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = atom.sku_code and (srt.province_code =#{beId} or srt.province_code = '000'))
        </if>
        <if test="location != null and location != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = atom.sku_code and srt.city_code =#{location})
        </if>

        <!--<if test="userIdList != null and userIdList.size() != 0">
            and atom.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>-->
        ORDER BY sku.update_time DESC
        limit #{page},#{num}
    </select>

    <select id="countInventoryNewByHandleQuery" resultType="java.lang.Long">
        select count(*)
        from sku_offering_info sku
        left join atom_offering_info atom on atom.id = (select id from atom_offering_info where sku_code = sku.offering_code and offering_class not in ('S','C','A')  limit 1)
        left join spu_offering_info spu on spu.offering_code = sku.spu_code and spu.delete_time is null
        left join category_info ci on sku.spu_id=ci.spu_id
        <if test="userIdList == null or userIdList.size() == 0">
            left join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct aocr.cooperator_id) cooperator_id,
            aocr.atom_offering_id,GROUP_CONCAT(distinct up.is_primary) is_primary,group_concat(distinct fuser.name)  f_user_name
            from
            atom_offering_cooperator_relation aocr
            inner join user_partner up on aocr.cooperator_id = up.user_id
            left join user_partner fuser on fuser.partner_name = up.partner_name and fuser.is_primary = 1 and fuser.is_cancel = 0 and fuser.is_logoff = 0
            where
                1=1
            group by aocr.atom_offering_id
            ) ap on ap.atom_offering_id = atom.id
        </if>
        <if test="(userIdList != null and userIdList.size() != 0) or (partnerName != null and partnerName != '') or (cooperatorName != null and cooperatorName != '')">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct aocr.cooperator_id) cooperator_id,
            aocr.atom_offering_id,GROUP_CONCAT(distinct up.is_primary) is_primary,group_concat(distinct fuser.name)  f_user_name
            from
            atom_offering_cooperator_relation aocr
            inner join user_partner up on aocr.cooperator_id = up.user_id
            left join user_partner fuser on fuser.partner_name = up.partner_name and fuser.is_primary = 1 and fuser.is_cancel = 0 and fuser.is_logoff = 0
            where
            1=1
            <if test="userIdList != null and userIdList.size() != 0">
                and aocr.cooperator_id in
                <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="partnerName != null and partnerName != ''">
                and up.partner_name like concat ('%',#{partnerName},'%')
            </if>
            <if test="cooperatorName != null and cooperatorName != ''">
                and up.name like concat ('%',#{cooperatorName},'%')
            </if>
            group by aocr.atom_offering_id
            ) ap on ap.atom_offering_id = atom.id
        </if>
        where sku.delete_time is null
        <if test="h5SpuOfferingClasses!=null and !h5SpuOfferingClasses.contains('A11'.toString())">
            <if test="inventoryStatus==1">
                and (SELECT count(*) FROM atom_offering_info WHERE sku_code = sku.offering_code and offering_class not in ('S','C','A') AND inventory+reserve_inventory <![CDATA[ <= ]]> inventory_threshold) <![CDATA[ <= ]]> 0
            </if>
            <if test="inventoryStatus==0">
                and (SELECT count(*) FROM atom_offering_info WHERE sku_code = sku.offering_code and offering_class not in ('S','C','A') AND inventory+reserve_inventory <![CDATA[ <= ]]> inventory_threshold) > 0
            </if>
        </if>
        <if test="h5SpuOfferingClasses!=null and h5SpuOfferingClasses.contains('A11'.toString())">
            <if test="inventoryStatus==1">
                and (SELECT
                count(*)
                FROM atom_offering_info atom
                LEFT JOIN sku_release_target srt ON srt.sku_offering_code = atom.sku_code
                left JOIN dkcardx_inventory_detail_info did ON atom.inventory_main_id = did.inventory_main_id and did.province_alias_name is not null and did.province_alias_name != ''
                left JOIN dkcardx_inventory_detail_info did_city ON atom.inventory_main_id = did_city.inventory_main_id and (did_city.province_alias_name is null or did_city.province_alias_name = '') and did_city.location = srt.city_code
                where atom.sku_code = sku.offering_code and
                atom.offering_class not in ('S','C','A') and
                (COALESCE(did.total_inventory, 0) + COALESCE(did_city.total_inventory, 0))<![CDATA[ <= ]]> atom.inventory_threshold) <![CDATA[ <= ]]> 0
            </if>
            <if test="inventoryStatus==0">
                and (SELECT
                count(*)
                FROM atom_offering_info atom
                LEFT JOIN sku_release_target srt ON srt.sku_offering_code = atom.sku_code
                left JOIN dkcardx_inventory_detail_info did ON atom.inventory_main_id = did.inventory_main_id and did.province_alias_name is not null and did.province_alias_name != ''
                left JOIN dkcardx_inventory_detail_info did_city ON atom.inventory_main_id = did_city.inventory_main_id and (did_city.province_alias_name is null or did_city.province_alias_name = '') and did_city.location = srt.city_code
                where atom.sku_code = sku.offering_code and
                atom.offering_class not in ('S','C','A') and
                (COALESCE(did.total_inventory, 0) + COALESCE(did_city.total_inventory, 0))<![CDATA[ <= ]]> atom.inventory_threshold) > 0
            </if>
            and sku.product_type in ('4','5','6','7','8','9','10','11','12')
        </if>
        <if test="spuOfferingName != null and spuOfferingName != ''">
            and spu.offering_name like concat ('%',#{spuOfferingName},'%')
        </if>
        <if test="skuOfferingName != null and skuOfferingName != ''">
            and sku.offering_name like concat ('%',#{skuOfferingName},'%')
        </if>
        <if test="spuOfferingCode != null and spuOfferingCode != ''">
            and spu.offering_code like concat ('%',#{spuOfferingCode},'%')
        </if>
        <if test="skuOfferingCode != null and skuOfferingCode != ''">
            and sku.offering_code like concat ('%',#{skuOfferingCode},'%')
        </if>
        <if test="spuOfferingClass != null and spuOfferingClass.size() != 0">
            and ci.offering_class in
            <foreach collection="spuOfferingClass" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <!--<if test="partnerName != null and partnerName != ''">
            and
            u.partner_name like concat ('%',#{partnerName},'%')
        </if>
        <if test="cooperatorName != null and cooperatorName != ''">
            and u.name like concat ('%',#{cooperatorName},'%')
        </if>-->
        <if test="spuOfferingStatus != null and spuOfferingStatus.size() != 0">
            and spu.offering_status in
            <foreach collection="spuOfferingStatus" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="skuOfferingStatus != null and skuOfferingStatus.size() != 0">
            and sku.offering_status in
            <foreach collection="skuOfferingStatus" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="h5SpuOfferingClasses != null and h5SpuOfferingClasses.size() != 0">
            and  ci.offering_class in
            <foreach collection="h5SpuOfferingClasses" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="h5Key != null and h5Key != ''">
            and (spu.offering_name like concat ('%',#{h5Key},'%')
            or spu.offering_code like concat ('%',#{h5Key},'%')
            or sku.offering_code like concat ('%',#{h5Key},'%')
            or atom.offering_code like concat ('%',#{h5Key},'%'))
        </if>
        <if test="inventoryType != null and inventoryType != ''">
            and spu.inventory_type = #{inventoryType}
        </if>
        <if test="atomOfferingName != null and atomOfferingName != ''">
            and ((SELECT count(*) FROM atom_offering_info WHERE sku_code = sku.offering_code AND offering_name like concat ('%',#{atomOfferingName},'%'))>0)
        </if>
        <if test="beId != null and beId != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = atom.sku_code and (srt.province_code =#{beId} or srt.province_code = '000'))
        </if>
        <if test="location != null and location != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = atom.sku_code and srt.city_code =#{location})
        </if>
        <!--<if test="userIdList != null and userIdList.size() != 0">
            and atom.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>-->
    </select>

    <update id="setInventoryPaymentBySpu">
        update  atom_offering_info
        <set>
            inventory = 0 - reserve_inventory
        </set>
        where spu_code=#{spuCode} and delete_time is null
    </update>

    <select id="selectInventoryTypeByHandleQuery" resultMap="inventoryInfoHandleMap">
        select
        ci.offering_class as spu_offering_class,
        spu.offering_code as spu_offering_code,
        spu.offering_name as spu_offering_name,
        spu.offering_status spu_offering_status,
        spu.inventory_type inventory_type
        from spu_offering_info spu
        left join category_info ci on spu.id = ci.spu_id
        where spu.delete_time is null
        <if test="spuOfferingName != null and spuOfferingName != ''">
            and spu.offering_name like concat ('%',#{spuOfferingName},'%')
        </if>
        <if test="spuOfferingCode != null and spuOfferingCode != ''">
            and spu.offering_code like concat ('%',#{spuOfferingCode},'%')
        </if>
        <if test="inventoryType != null and inventoryType != ''">
            and spu.inventory_type = #{inventoryType}
        </if>
        <if test="spuOfferingClass != null and spuOfferingClass != ''">
            and ci.offering_class = #{spuOfferingClass}
        </if>
        <if test="h5SpuOfferingClasses != null and h5SpuOfferingClasses.size() != 0">
            and ci.offering_class in
            <foreach collection="h5SpuOfferingClasses" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        ORDER BY spu.update_time DESC
        limit #{page},#{num}
    </select>

    <select id="countInventoryTypeByHandleQuery" resultType="java.lang.Long">
        select count(*)
        from spu_offering_info spu
        left join category_info ci on spu.id = ci.spu_id
        where spu.delete_time is null
        <if test="spuOfferingName != null and spuOfferingName != ''">
            and spu.offering_name like concat ('%',#{spuOfferingName},'%')
        </if>
        <if test="spuOfferingCode != null and spuOfferingCode != ''">
            and spu.offering_code like concat ('%',#{spuOfferingCode},'%')
        </if>
        <if test="inventoryType != null and inventoryType != ''">
            and spu.inventory_type = #{inventoryType}
        </if>
        <if test="spuOfferingClass != null and spuOfferingClass != ''">
            and ci.offering_class = #{spuOfferingClass}
        </if>
        <if test="h5SpuOfferingClasses != null and h5SpuOfferingClasses.size() != 0">
            and ci.offering_class in
            <foreach collection="h5SpuOfferingClasses" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="listKXInventoryDetailImei" resultType="com.chinamobile.iot.sc.pojo.dto.KXInventoryDetailImeiDTO">
        select imei,sellStatus,location
        from (
         select
            cr.imei,
            cr.sell_status sellStatus,
            cr.location
            from atom_offering_info atom
            inner join dkcardx_inventory_detail_info didi on didi.inventory_main_id = atom.inventory_main_id
            inner join dkcardx_inventory_atom_info diai on diai.inventory_detail_id = didi.id and diai.atom_id = atom.id
            inner join dkcardx_inventory_main_info dimi on dimi.id = didi.inventory_main_id
            inner join    card_relation cr on  dimi.be_id = cr.be_id
                    and dimi.device_version = cr.device_version
                    and dimi.terminal_type = cr.terminal_type
                    and ifnull(dimi.cust_code,'') = ifnull(cr.cust_code,'')
                    and ifnull(dimi.template_id,'') = ifnull(cr.template_id,'')
                    and (cr.location is null or cr.location='')
                    and cr.delete_time is null

            where
                didi.province_alias_name is not null and didi.province_alias_name != ''
            and cr.sell_status in ('1','2')
            and atom.id = #{kxInventoryDetailImeiParam.atomId}
    union
    select
          cr.imei,
          cr.sell_status sellStatus,
          cr.location
        from atom_offering_info atom
        inner join dkcardx_inventory_detail_info didi on didi.inventory_main_id = atom.inventory_main_id
        inner join dkcardx_inventory_atom_info diai on diai.inventory_detail_id = didi.id and diai.atom_id = atom.id
        inner join dkcardx_inventory_main_info dimi on dimi.id = didi.inventory_main_id
        inner join    card_relation cr on dimi.be_id = cr.be_id
                and dimi.device_version = cr.device_version
                and dimi.terminal_type = cr.terminal_type
                and ifnull(dimi.cust_code,'') = ifnull(cr.cust_code,'')
                and ifnull(dimi.template_id,'') = ifnull(cr.template_id,'')
                and cr.location is not null and cr.location != ''
                and cr.delete_time is null
        inner join sku_release_target srt on srt.sku_offering_code = atom.sku_code and (srt.province_code = didi.be_id or srt.province_code = '000')
            and case when
            srt.city_code is not null
            then didi.location = srt.city_code
            else 1 = 1
            end
        where
            (didi.province_alias_name is null or didi.province_alias_name = '')
        and cr.sell_status in ('1','2')
        and atom.id = #{kxInventoryDetailImeiParam.atomId}
        ) invDetail
        where 1=1
        <if test="kxInventoryDetailImeiParam.location != null and kxInventoryDetailImeiParam.location != ''">
            <if test="kxInventoryDetailImeiParam.location == '-1'">
                and (location is null or location = '')
            </if>
            <if test="kxInventoryDetailImeiParam.location != '-1'">
                and location = #{kxInventoryDetailImeiParam.location}
            </if>

        </if>
        <if test="kxInventoryDetailImeiParam.sellStatus != null and kxInventoryDetailImeiParam.sellStatus != ''">
            and sellStatus = #{kxInventoryDetailImeiParam.sellStatus}
        </if>
        <if test="kxInventoryDetailImeiParam.imei != null and kxInventoryDetailImeiParam.imei != ''">
            and imei like '%${kxInventoryDetailImeiParam.imei}%'
        </if>
    </select>

    <select id="listKXInventoryDetailLocation" resultType="com.chinamobile.iot.sc.pojo.dto.KXInventoryDetailLocationDTO">
        select distinct atomId,location
        from (
         select
            atom.id atomId,
            cr.location
            from atom_offering_info atom
            inner join dkcardx_inventory_detail_info didi on didi.inventory_main_id = atom.inventory_main_id
            inner join dkcardx_inventory_atom_info diai on diai.inventory_detail_id = didi.id and diai.atom_id = atom.id
            inner join dkcardx_inventory_main_info dimi on dimi.id = didi.inventory_main_id
            inner join    card_relation cr on  dimi.be_id = cr.be_id
                    and dimi.device_version = cr.device_version
                    and dimi.terminal_type = cr.terminal_type
                    and ifnull(dimi.cust_code,'') = ifnull(cr.cust_code,'')
                    and ifnull(dimi.template_id,'') = ifnull(cr.template_id,'')
                    and (cr.location is null or cr.location='')
                    and cr.delete_time is null
            where
                didi.province_alias_name is not null and didi.province_alias_name != ''
            and atom.id = #{atomId}
    union
    select
          atom.id atomId,
          cr.location
        from atom_offering_info atom
        inner join dkcardx_inventory_detail_info didi on didi.inventory_main_id = atom.inventory_main_id
        inner join dkcardx_inventory_atom_info diai on diai.inventory_detail_id = didi.id and diai.atom_id = atom.id
        inner join dkcardx_inventory_main_info dimi on dimi.id = didi.inventory_main_id
        inner join card_relation cr on dimi.be_id = cr.be_id
                and dimi.device_version = cr.device_version
                and dimi.terminal_type = cr.terminal_type
                and ifnull(dimi.cust_code,'') = ifnull(cr.cust_code,'')
                and ifnull(dimi.template_id,'') = ifnull(cr.template_id,'')
                and cr.location is not null and cr.location != ''
                and cr.delete_time is null
        inner join sku_release_target srt on srt.sku_offering_code = atom.sku_code and (srt.province_code = cr.be_id or srt.province_code = '000') and ifnull(srt.city_code,'-1') = cr.location
         where
            (didi.province_alias_name is null or didi.province_alias_name = '')
        and atom.id = #{atomId}
        ) invDetail
        where 1=1
    </select>

    <select id="getInventoryKxDetailList" resultType="com.chinamobile.iot.sc.response.web.DkCardxInventoryDetailInfoDTO">
        select
          atomId,inventoryThreshold,reserveQuatity,currentInventory,
          totalInventory,provinceCityName,inventoryMainId,atomInventory,
          deviceVersion
        from(
        select
            atom.id atomId,
            atom.inventory_threshold inventoryThreshold,
            didi.reserve_quatity reserveQuatity,
            didi.current_inventory currentInventory,
            didi.total_inventory totalInventory,
            didi.province_alias_name provinceCityName,
            didi.inventory_main_id inventoryMainId,
            diai.atom_inventory atomInventory,
            dimi.device_version deviceVersion
        from atom_offering_info atom
        inner join sku_release_target srt on srt.sku_offering_code = atom.sku_code
        inner join dkcardx_inventory_detail_info didi on didi.inventory_main_id = atom.inventory_main_id
        inner join dkcardx_inventory_main_info dimi on dimi.id = didi.inventory_main_id
        inner join dkcardx_inventory_atom_info diai on diai.inventory_detail_id = didi.id and diai.atom_id = atom.id
        where
			didi.province_alias_name is not null and didi.province_alias_name != ''
        union
        select
            atom.id atomId,
            atom.inventory_threshold inventoryThreshold,
            didi.reserve_quatity reserveQuatity,
            didi.current_inventory currentInventory,
            didi.total_inventory totalInventory,
            didi.city_name provinceCityName,
            didi.inventory_main_id inventoryMainId,
            diai.atom_inventory atomInventory,
            dimi.device_version deviceVersion
        from atom_offering_info atom
        inner join dkcardx_inventory_detail_info didi on didi.inventory_main_id = atom.inventory_main_id
        inner join dkcardx_inventory_main_info dimi on dimi.id = didi.inventory_main_id
        inner join dkcardx_inventory_atom_info diai on diai.inventory_detail_id = didi.id and diai.atom_id = atom.id
        inner join sku_release_target srt on srt.sku_offering_code = atom.sku_code and (srt.province_code = didi.be_id or srt.province_code = '000')
                and case when
                srt.city_code is not null
                then didi.location = srt.city_code
                else 1 = 1
                end
                where
                    (didi.province_alias_name is null or didi.province_alias_name = '')
           )inv
        where inv.atomId = #{atomId}
    </select>

    <select id="updateInventoryAtomInfoFix" resultType="com.chinamobile.iot.sc.pojo.DkcardxInventoryAtomInfo">
        select *
        from dkcardx_inventory_atom_info
        group by (inventory_main_id)
        having  count(*) = 1
    </select>
</mapper>