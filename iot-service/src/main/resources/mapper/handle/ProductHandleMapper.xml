<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.handle.ProductHandlerMapper">

    <resultMap id="productInfoHandleMap" type="com.chinamobile.iot.sc.pojo.handle.ProductInfoHandle">
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="sku_offering_name" jdbcType="VARCHAR" property="skuOfferingName"/>
        <result column="atom_offering_name" jdbcType="VARCHAR" property="atomOfferingName"/>
        <result column="atom_offering_class" jdbcType="VARCHAR" property="atomOfferingClass"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="color" jdbcType="VARCHAR" property="color"/>
        <result column="atom_price" jdbcType="BIGINT" property="atomPrice"/>
        <result column="config_all_status" jdbcType="INTEGER" property="configAllStatus"/>
        <result column="config_time" jdbcType="TIMESTAMP" property="configTime"/>
        <result column="cooperator_name" jdbcType="VARCHAR" property="cooperatorName"/>
        <result column="is_primary" jdbcType="TINYINT" property="isPrimary"/>
        <result column="partner_name" jdbcType="VARCHAR" property="partnerName"/>
        <result column="cooperator_name" jdbcType="VARCHAR" property="cooperatorName"/>
        <result column="cooperator_id" jdbcType="VARCHAR" property="cooperatorId"/>
        <result column="spu_offering_class" jdbcType="VARCHAR" property="spuOfferingClass"/>
        <result column="spu_offering_code" jdbcType="VARCHAR" property="spuOfferingCode"/>
        <result column="spu_offering_name" jdbcType="VARCHAR" property="spuOfferingName"/>
        <result column="sku_offering_code" jdbcType="VARCHAR" property="skuOfferingCode"/>
        <result column="spu_offering_status" jdbcType="VARCHAR" property="spuOfferingStatus"/>
        <result column="sku_offering_status" jdbcType="VARCHAR" property="skuOfferingStatus"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="config_all_time" jdbcType="VARCHAR" property="configAllTime"/>
        <result column="config_status" jdbcType="INTEGER" property="configStatus"/>
        <result column="img_url" jdbcType="VARCHAR" property="imgUrl"/>
        <result column="atom_offering_code" jdbcType="VARCHAR" property="atomOfferingCode"/>
    </resultMap>

    <resultMap id="productConfigMap" type="com.chinamobile.iot.sc.pojo.vo.ProductConfigVO">
        <result column="id" jdbcType="VARCHAR" property="productConfig.id"/>
        <result column="atom_offering_name" jdbcType="VARCHAR" property="productConfig.atomOfferingName"/>
        <result column="atom_offering_class" jdbcType="VARCHAR" property="productConfig.atomOfferingClass"/>
        <result column="atom_offering_code" jdbcType="VARCHAR" property="productConfig.atomOfferingCode"/>
        <result column="ext_soft_offering_code" jdbcType="VARCHAR" property="productConfig.extSoftOfferingCode"/>
        <result column="ext_hard_offering_code" jdbcType="VARCHAR" property="productConfig.extHardOfferingCode"/>
        <result column="model" jdbcType="VARCHAR" property="productConfig.model"/>
        <result column="color" jdbcType="VARCHAR" property="productConfig.color"/>
        <result column="inventory" jdbcType="BIGINT" property="productConfig.inventory"/>
        <result column="atom_quantity" jdbcType="BIGINT" property="productConfig.atomQuantity"/>
        <result column="settle_price" jdbcType="BIGINT" property="productConfig.settlePrice"/>
        <result column="atom_sale_price" jdbcType="BIGINT" property="productConfig.atomSalePrice"/>
        <result column="offering_sale_region" jdbcType="VARCHAR" property="productConfig.offeringSaleRegion"/>
        <result column="cooperator_id" jdbcType="VARCHAR" property="productConfig.cooperatorId"/>

        <result column="spu_offering_class" jdbcType="VARCHAR" property="productConfig.spuOfferingClass"/>
        <result column="spu_offering_code" jdbcType="VARCHAR" property="productConfig.spuOfferingCode"/>
        <result column="spu_offering_name" jdbcType="VARCHAR" property="productConfig.spuOfferingName"/>
        <result column="spu_offering_status" jdbcType="VARCHAR" property="productConfig.spuOfferingStatus"/>
        <result column="sku_offering_name" jdbcType="VARCHAR" property="productConfig.skuOfferingName"/>
        <result column="sku_offering_code" jdbcType="VARCHAR" property="productConfig.skuOfferingCode"/>
        <result column="sku_offering_status" jdbcType="VARCHAR" property="productConfig.skuOfferingStatus"/>
        <result column="sale_object" jdbcType="VARCHAR" property="productConfig.saleObject"/>
        <result column="price" jdbcType="BIGINT" property="productConfig.price"/>
        <result column="spu_img_url" jdbcType="VARCHAR" property="productConfig.spuImgUrl"/>
        <result column="secretly_listed" jdbcType="VARCHAR" property="productConfig.secretlyListed"/>
        <result column="product_description" jdbcType="VARCHAR" property="productConfig.productDescription"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="productConfig.supplierName"/>
        <result column="receive_order_name" jdbcType="VARCHAR" property="productConfig.receiveOrderName"/>
        <result column="receive_order_phone" jdbcType="VARCHAR" property="productConfig.receiveOrderPhone"/>
        <result column="deliver_name" jdbcType="VARCHAR" property="productConfig.deliverName"/>
        <result column="deliver_phone" jdbcType="VARCHAR" property="productConfig.deliverPhone"/>
        <result column="aftermarket_name" jdbcType="VARCHAR" property="productConfig.aftermarketName"/>
        <result column="aftermarket_phone" jdbcType="VARCHAR" property="productConfig.aftermarketPhone"/>
        <result column="firstNavigationName" jdbcType="VARCHAR" property="productConfig.firstNavigationName"/>
        <result column="secondNavigationName" jdbcType="VARCHAR" property="productConfig.secondNavigationName"/>
        <result column="thirdNavigationName" jdbcType="VARCHAR" property="productConfig.thirdNavigationName"/>
        <result column="mainSaleLabel" jdbcType="VARCHAR" property="productConfig.mainSaleLabel"/>
        <result column="subSaleLabel" jdbcType="VARCHAR" property="productConfig.subSaleLabel"/>
        <result column="productKeywords" jdbcType="VARCHAR" property="productConfig.productKeywords"/>

        <result column="get_order_way" jdbcType="TINYINT" property="productConfig.ordertakeType"/>

        <result column="atom_offering_version" jdbcType="VARCHAR" property="productConfig.atomOfferingVersion"/>
        <result column="sku_offering_version" jdbcType="VARCHAR" property="productConfig.skuOfferingVersion"/>
        <result column="spu_offering_version" jdbcType="VARCHAR" property="productConfig.spuOfferingVersion"/>

        <result column="product_type" jdbcType="VARCHAR" property="productConfig.productType" />
        <result column="receive_order" jdbcType="VARCHAR" property="productConfig.receiveOrder" />
        <result column="cust_code" jdbcType="VARCHAR" property="productConfig.custCode" />
        <result column="cust_name" jdbcType="VARCHAR" property="productConfig.custName" />
        <result column="card_type" jdbcType="VARCHAR" property="productConfig.cardType" />
        <result column="main_offering_code" jdbcType="VARCHAR" property="productConfig.mainOfferingCode" />
        <result column="template_name" jdbcType="VARCHAR" property="productConfig.templateName" />
        <result column="project" jdbcType="VARCHAR" property="productConfig.project" />
        <result column="associated_offer" jdbcType="VARCHAR" property="productConfig.associatedOffer" />
        <result column="validity_period" jdbcType="VARCHAR" property="productConfig.validityPeriod" />

    </resultMap>

    <resultMap id="stdServiceConfigMap" type="com.chinamobile.iot.sc.pojo.dto.StdServiceConfigDTO">
        <result column="config_id" jdbcType="VARCHAR" property="configId"/>
        <result column="std_svc_id" jdbcType="VARCHAR" property="stdSvcId"/>
        <result column="std_svc_name" jdbcType="VARCHAR" property="stdSvcName"/>
        <result column="product_dept" jdbcType="VARCHAR" property="productDept"/>
        <result column="real_product_name" jdbcType="VARCHAR" property="realProductName"/>
        <result column="product_property" jdbcType="VARCHAR" property="productProperty"/>
        <result column="remark1" jdbcType="VARCHAR" property="remark1"/>
        <result column="remark2" jdbcType="VARCHAR" property="remark2"/>
        <result column="special_cooperative_settlement_price" jdbcType="VARCHAR" property="specialCooperativeSettlementPrice"/>
    </resultMap>

    <select id="selectProductByQueryInfo" resultMap="productInfoHandleMap">
        SELECT
        sku.id as id,
        sku.offering_name as sku_offering_name,
        sku.offering_code as sku_offering_code,
        ci.offering_class as spu_offering_class,
        sku.offering_status sku_offering_status,
        if ((SELECT count(*) FROM atom_offering_info atom LEFT JOIN spu_offering_info spu ON atom.spu_code = spu.offering_code LEFT JOIN category_info ci on ci.spu_id = spu.id WHERE sku_code = sku.offering_code
        AND
        case
        when ci.offering_class !='A13' then atom.offering_class not in ('S','C')
        else 1=1
        end
        AND config_all_time is null)>0,0,1) config_all_status,
        sp.partner_name as partner_name,
        sp.user_name cooperator_name,
        sp.cooperator_id cooperator_id,
        spu.img_url img_url,
        spu.offering_code spu_offering_code,
        spu.offering_name spu_offering_name,
        spu.offering_status spu_offering_status
        FROM sku_offering_info sku
        join spu_offering_info spu on spu.offering_code = sku.spu_code and spu.delete_time is null
        join category_info ci on spu.id=ci.spu_id
        <if test="userIds==null or userIds.size()==0">
            left join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct scr.cooperator_id) cooperator_id,
            scr.sku_offering_code
            from
            sku_cooperator_relation scr
            inner join user_partner up on scr.cooperator_id = up.user_id
            where
                1=1
            group by scr.sku_offering_code
            ) sp on sp.sku_offering_code = sku.offering_code
        </if>
        <if test="(userIds!=null and userIds.size()!=0) or (partnerName != null and partnerName != '') or (cooperatorName != null and cooperatorName != '')">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct scr.cooperator_id) cooperator_id,
            scr.sku_offering_code
            from
            sku_cooperator_relation scr
            inner join user_partner up on scr.cooperator_id = up.user_id
            where
                1=1
            <if test="userIds!=null and userIds.size()!=0">
                and scr.cooperator_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="partnerName != null and partnerName != ''">
                and up.partner_name LIKE concat ('%',#{partnerName},'%')
            </if>
            <if test="cooperatorName != null and cooperatorName != ''">
                and up.name like concat ('%',#{cooperatorName},'%')
            </if>
            group by scr.sku_offering_code
            ) sp on sp.sku_offering_code = sku.offering_code
        </if>

        <if test="deviceVersion != null and deviceVersion != ''">
            inner join (
            select atom.id atomId,atom.spu_code,atom.sku_code,
            dimi.device_version
            from atom_offering_info atom
            inner join sku_release_target srt on srt.sku_offering_code = atom.sku_code
            inner join dkcardx_inventory_detail_info didi on didi.inventory_main_id = atom.inventory_main_id
            inner join dkcardx_inventory_main_info dimi on dimi.id = didi.inventory_main_id
            inner join dkcardx_inventory_atom_info diai on diai.inventory_detail_id = didi.id and diai.atom_id = atom.id
            where
            didi.province_alias_name is not null and didi.province_alias_name != ''
            and dimi.device_version like concat ('%',#{deviceVersion},'%')
            union
            select atom.id atomId,atom.spu_code,atom.sku_code,
            dimi.device_version
            from atom_offering_info atom
            inner join dkcardx_inventory_detail_info didi on didi.inventory_main_id = atom.inventory_main_id
            inner join dkcardx_inventory_main_info dimi on dimi.id = didi.inventory_main_id
            inner join dkcardx_inventory_atom_info diai on diai.inventory_detail_id = didi.id and diai.atom_id = atom.id
            inner join sku_release_target srt on srt.sku_offering_code = atom.sku_code and (srt.province_code = didi.be_id or srt.province_code = '000')
            and case when
            srt.city_code is not null
            then didi.location = srt.city_code
            else 1 = 1
            end
            where
            (didi.province_alias_name is null or didi.province_alias_name = '')
            and dimi.device_version like concat ('%',#{deviceVersion},'%')
            )inv on inv.spu_code = sku.spu_code and inv.sku_code = sku.offering_code
        </if>
        WHERE sku.delete_time is null and EXISTS (SELECT * FROM atom_offering_info WHERE sku_code = sku.offering_code and delete_time is null )
        <!--<if test="userIds!=null and userIds.size()!=0">
            and sku.cooperator_id in
            <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>-->
        <if test="spuOfferingName != null and spuOfferingName != ''">
            and spu.offering_name like concat ('%',#{spuOfferingName},'%')
        </if>
        <if test="spuOfferingCode != null and spuOfferingCode != ''">
            and spu.offering_code like concat ('%',#{spuOfferingCode},'%')
        </if>
        <if test="skuOfferingName != null and skuOfferingName != ''">
            and sku.offering_name like concat ('%',#{skuOfferingName},'%')
        </if>
        <if test="skuOfferingCode != null and skuOfferingCode != ''">
            and sku.offering_code like concat ('%',#{skuOfferingCode},'%')
        </if>
        <if test="templateId != null and templateId != ''">
            and sku.template_id like concat ('%',#{templateId},'%')
        </if>
        <if test="templateName != null and templateName != ''">
            and sku.template_name like concat ('%',#{templateName},'%')
        </if>
        <if test="atomOfferingName != null and atomOfferingName != ''">
            and EXISTS ( SELECT * FROM atom_offering_info WHERE sku_code = sku.offering_code AND offering_name like concat ('%',#{atomOfferingName},'%'))
        </if>
        <if test="spuOfferingClass != null and spuOfferingClass.size()!=0">
            and ci.offering_class in
            <foreach collection="spuOfferingClass" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--<if test="partnerName != null and partnerName != ''">
            and user.partner_name LIKE concat ('%',#{partnerName},'%')
        </if>
        <if test="cooperatorName != null and cooperatorName != ''">
            and user.name like concat ('%',#{cooperatorName},'%')
        </if>-->
        <if test="spuOfferingStatus != null and spuOfferingStatus.size()!=0">
            and spu.offering_status in
            <foreach collection="spuOfferingStatus" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="skuOfferingStatus != null and skuOfferingStatus.size()!=0">
            and sku.offering_status in
            <foreach collection="skuOfferingStatus" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="h5Key != null and h5Key != ''">
            and (spu.offering_name like concat ('%',#{h5Key},'%')
            or spu.offering_code like concat ('%',#{h5Key},'%')
            or sku.offering_code like concat ('%',#{h5Key},'%')
            or atom.offering_code like concat ('%',#{h5Key},'%'))
        </if>
        <if test=" h5SpuOfferingClasses != null and h5SpuOfferingClasses.size() != 0">
            and ci.offering_class in
            <foreach collection="h5SpuOfferingClasses" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="configAllStatus == 0">
            and (SELECT count(*) FROM atom_offering_info atom LEFT JOIN spu_offering_info spu ON atom.spu_code = spu.offering_code LEFT JOIN category_info ci on ci.spu_id = spu.id WHERE sku_code = sku.offering_code
            AND
            case
            when ci.offering_class !='A13' then atom.offering_class not in ('S','C')
            else 1=1
            end
            AND config_all_time is null)>0
        </if>
        <if test="configAllStatus == 1">
            and (SELECT count(*) FROM atom_offering_info atom LEFT JOIN spu_offering_info spu ON atom.spu_code = spu.offering_code LEFT JOIN category_info ci on ci.spu_id = spu.id WHERE sku_code = sku.offering_code
            AND
            case
            when ci.offering_class !='A13' then atom.offering_class not in ('S','C')
            else 1=1
            end
            AND config_all_time is null)=0
        </if>
        ORDER BY sku.update_time DESC
    </select>
    <select id="countProductByQueryInfo" resultType="java.lang.Long">
        select count(*)
        from atom_offering_info atom
        left join sku_offering_info sku on atom.sku_code = sku.offering_code and sku.delete_time is null AND atom.spu_code = sku.spu_code
        left join category_info ci on atom.spu_id=ci.spu_id
        left join spu_offering_info spu on spu.offering_code = atom.spu_code and spu.delete_time is null
        <if test="userIds==null or userIds.size()==0">
            left join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct aocr.cooperator_id) cooperator_id,
            aocr.atom_offering_id,GROUP_CONCAT(distinct up.is_primary) is_primary
            from
            atom_offering_cooperator_relation aocr
            inner join user_partner up on aocr.cooperator_id = up.user_id
            where
                1=1
            group by aocr.atom_offering_id
            ) ap on ap.atom_offering_id = atom.id
        </if>
        <if test="(userIds!=null and userIds.size()!=0) or (partnerName != null and partnerName != '') or (cooperatorName != null and cooperatorName != '')">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct aocr.cooperator_id) cooperator_id,
            aocr.atom_offering_id,GROUP_CONCAT(distinct up.is_primary) is_primary
            from
            atom_offering_cooperator_relation aocr
            inner join user_partner up on aocr.cooperator_id = up.user_id
            where
                1=1
            <if test="userIds!=null and userIds.size()!=0">
                and aocr.cooperator_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="partnerName != null and partnerName != ''">
                and up.partner_name LIKE concat ('%',#{partnerName},'%')
            </if>
            <if test="cooperatorName != null and cooperatorName != ''">
                and up.name like concat ('%',#{cooperatorName},'%')
            </if>
            group by aocr.atom_offering_id
            ) ap on ap.atom_offering_id = atom.id
        </if>
        where atom.delete_time is null and atom.offering_class not in ('S','C')
        <if test="configStatus == 0">
            and atom.cooperator_id is null and atom.offering_class not in ('O','D')
        </if>
        <if test="configStatus == 1">
            and atom.cooperator_id is not null and user_partner.is_primary = true
        </if>
        <if test="configStatus == 2">
            and  atom.cooperator_id is not null and user_partner.is_primary = false
        </if>
        <!--<if test="userIds!=null and userIds.size()!=0">
            and atom.cooperator_id in
            <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>-->
        <if test="spuOfferingName != null and spuOfferingName != ''">
            and spu.offering_name like concat ('%',#{spuOfferingName},'%')
        </if>
        <if test="spuOfferingCode != null and spuOfferingCode != ''">
            and spu.offering_code like concat ('%',#{spuOfferingCode},'%')
        </if>
        <if test="skuOfferingName != null and skuOfferingName != ''">
            and sku.offering_name like concat ('%',#{skuOfferingName},'%')
        </if>
        <if test="skuOfferingCode != null and skuOfferingCode != ''">
            and sku.offering_code like concat ('%',#{skuOfferingCode},'%')
        </if>
        <if test="atomOfferingName != null and atomOfferingName != ''">
            and atom.offering_name like concat ('%',#{atomOfferingName},'%')
        </if>
        <if test="spuOfferingClass != null and spuOfferingClass != ''">
            and ci.offering_class = #{spuOfferingClass}
        </if>
        <!--<if test="partnerName != null and partnerName != ''">
            and user_partner.partner_name LIKE concat ('%',#{partnerName},'%')
        </if>
        <if test="cooperatorName != null and cooperatorName != ''">
            and user_partner.name like concat ('%',#{cooperatorName},'%')
        </if>-->
        <if test="spuOfferingStatus != null and spuOfferingStatus != ''">
            and spu.offering_status = #{spuOfferingStatus}
        </if>
        <if test="skuOfferingStatus != null and skuOfferingStatus != ''">
            and sku.offering_status = #{skuOfferingStatus}
        </if>
        <if test="configAllStatus == 0">
            and
            CASE
            WHEN ci.offering_class IN ('A06','A07','A11','A13') then atom.config_all_time is null
            WHEN ci.offering_class IN ('A04','A08','A09','A10','A12','A14','A15','A16','A17') then atom.config_all_time is null and not exists (SELECT * FROM atom_std_service WHERE atom_id = atom.id)
            END
        </if>
        <if test="configAllStatus == 1">
            and
            CASE
            WHEN ci.offering_class IN ('A06','A07','A11','A13') then atom.config_all_time is not null
            WHEN ci.offering_class IN ('A04','A08','A09','A10','A12','A14','A15','A16','A17') then atom.config_all_time is not null or exists (SELECT * FROM atom_std_service WHERE atom_id = atom.id)
            END
        </if>

        <if test="h5Key != null and h5Key != ''">
            and (spu.offering_name like concat ('%',#{h5Key},'%')
            or spu.offering_code like concat ('%',#{h5Key},'%')
            or sku.offering_code like concat ('%',#{h5Key},'%')
            or atom.offering_code like concat ('%',#{h5Key},'%'))
        </if>

        <if test=" h5SpuOfferingClasses != null and h5SpuOfferingClasses.size() != 0">
            and  ci.offering_class in
            <foreach collection="h5SpuOfferingClasses" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectProductConfig" resultMap="productConfigMap"  parameterType="java.lang.String">
        select
        sku.id,
        sku.cooperator_id as cooperator_id,
        spu.offering_name as spu_offering_name,
        ci.offering_class as spu_offering_class,
        spu.offering_code as spu_offering_code,
        spu.offering_status as spu_offering_status,
        spu.img_url as spu_img_url,
        spu.secretly_listed as secretly_listed,
        spu.product_description as product_description,
        (SELECT GROUP_CONCAT(DISTINCT d.`NAME`) FROM product_navigation_directory d JOIN navigation_info n ON d.id = n.level1_navigation_code WHERE n.spu_offering_code = spu.offering_code ) firstNavigationName,
        (SELECT GROUP_CONCAT(DISTINCT d.`NAME`) FROM product_navigation_directory d JOIN navigation_info n ON d.id = n.level2_navigation_code WHERE n.spu_offering_code = spu.offering_code ) secondNavigationName,
        (SELECT GROUP_CONCAT(DISTINCT d.`NAME`) FROM product_navigation_directory d JOIN navigation_info n ON d.id = n.level3_navigation_code WHERE n.spu_offering_code = spu.offering_code ) thirdNavigationName,
        spu.product_keywords productKeywords,
        sku.offering_name as sku_offering_name,
        sku.offering_code as sku_offering_code,
        sku.offering_status as sku_offering_status,
        sku.price as price,
        sku.product_type,
        sku.receive_order,
        sku.cust_code,
        sku.cust_name,
        sku.card_type,
        sku.main_offering_code,
        sku.template_name,
        sku.project,
        sku.sale_object,
        sku.supplier_name as supplier_name,
        sku.receive_order_name as receive_order_name,
        sku.receive_order_phone as receive_order_phone,
        sku.deliver_name as deliver_name,
        sku.deliver_phone as deliver_phone,
        sku.aftermarket_name as aftermarket_name,
        sku.aftermarket_phone as aftermarket_phone,
        (SELECT GROUP_CONCAT(label) FROM spu_sale_label where (type = 0 or type is null) and spu_code = spu.offering_code) mainSaleLabel,
        (SELECT GROUP_CONCAT(label) FROM spu_sale_label where type = 1 and spu_code = spu.offering_code) subSaleLabel,

        atom.get_order_way,
        skuh.spu_offering_version,
        skuh.sku_offering_version,
        atom.associated_offer,
        atom.validity_period

        from sku_offering_info sku
        left JOIN sku_offering_info_history skuh ON skuh.sku_id = sku.id
        left join atom_offering_info atom on atom.sku_code = sku.offering_code and sku.delete_time is null
        left join spu_offering_info spu on sku.spu_code = spu.offering_code and spu.delete_time is null
        left join category_info ci on atom.spu_id = ci.spu_id
    where sku.id = #{id}
    and
        case
            when ci.offering_class !='A13' then atom.offering_class not in ('S','C')
            else 1=1
        end
    order by skuh.spu_offering_version desc,skuh.sku_offering_version desc LIMIT 1
    </select>

    <select id="atomListBySkuId" parameterType="java.lang.String" resultType="com.chinamobile.iot.sc.pojo.mapper.ProductAtomInfoDO">
    select
        atom.id as id,
        atom.offering_name as atomOfferingName,
        atom.offering_class as atomOfferingClass,
        atom.offering_code as atom_offering_code,
        atom.ext_soft_offering_code as extSoftOfferingCode,
        atom.ext_hard_offering_code as extHardOfferingCode,
        atom.model as model,
        atom.color as color,
        atom.quantity as atomQuantity,
        atom.settle_price as settlePrice,
        atom.atom_sale_price as atomSalePrice,
        atom.offeringSaleRegion,
       	(SELECT atom_offering_version FROM atom_offering_info_history WHERE atom_id = atom.id ORDER BY atom_offering_version DESC limit 1) atomOfferingVersion,
       	atom.charge_code chargeCode,
       	atom.charge_id chargeId
    from
        atom_offering_info atom
    join sku_offering_info sku on atom.sku_code = sku.offering_code and sku.delete_time is null
    where sku.id = #{skuId} and atom.delete_time is null
    </select>


    <select id="selectStdServiceConfig" resultMap="stdServiceConfigMap"  parameterType="java.lang.String">
        select
               ass.id as config_id,
               ss.id as std_svc_id,
               ss.name as std_svc_name,
               dept.full_name as product_dept,
               ss.real_product_name,
               pp.name as product_property,
               ss.remark1,
               ss.remark2,
               aoi.special_cooperative_settlement_price
        from atom_std_service ass
                 left join standard_service ss on ss.id = ass.std_service_id
                 left join department dept on dept.id = ss.product_department_id
                 left join product_property pp on ss.product_property_id = pp.id
        inner join atom_offering_info aoi on aoi.id = ass.atom_id and aoi.delete_time is null
        where ass.atom_id = #{id}
    </select>
    <select id="selectStdServiceConfigGio" resultMap="stdServiceConfigMap"  parameterType="java.lang.String">
        select
            ass.id as config_id,
            ss.id as std_svc_id,
            ss.name as std_svc_name,
            dept.short_name as product_dept,
            ss.real_product_name,
            pp.name as product_property,
            ss.remark1,
            ss.remark2
        from atom_std_service ass
                 left join standard_service ss on ss.id = ass.std_service_id
                 left join department dept on dept.id = ss.product_department_id
                 left join product_property pp on ss.product_property_id = pp.id
        where 1=1
        <if test=" atomId != null">
            and ass.atom_id = #{atomId}
        </if>

    </select>
    <select id="selectStdServiceConfigGioByStdServiceId" resultMap="stdServiceConfigMap"  parameterType="java.lang.String">
        select
        ss.id as std_svc_id,
        ss.name as std_svc_name,
        dept.short_name as product_dept,
        ss.real_product_name,
        pp.name as product_property,
        ss.remark1,
        ss.remark2
        from standard_service ss
        left join department dept on dept.id = ss.product_department_id
        left join product_property pp on ss.product_property_id = pp.id
        where 1=1
        <if test=" stdId != null">
            and ss.id = #{stdId}
        </if>

    </select>

    <select id="selectCityInfo" resultType="com.chinamobile.iot.sc.pojo.mapper.CityInfoDO"  parameterType="java.lang.String">
        select cpi.mall_code as province,
               cci.mall_code as city
        from sku_release_target srt
                 left join contract_province_info cpi on (cpi.mall_code = srt.province_code or srt.province_code = '000')
                 left join contract_city_info cci on cci.mall_code = srt.city_code
        where srt.sku_offering_code = #{skuCode}
    </select>

    <select id="listAtomCooperatorHistory" resultType="com.chinamobile.iot.sc.pojo.dto.AtomCooperatorHistoryDTO">
        select distinct
            pdr.down_user_id cooperatorId,
            aoi.id atomOfferingId
        from
            primary_down_relation pdr,
            atom_offering_info aoi
        where
            pdr.down_user_id = aoi.cooperator_id
        <if test="atomOfferingId != null and atomOfferingId != ''">
            and aoi.id = #{atomOfferingId}
        </if>
    </select>
    
    <update id="updateAtomCooperator">
        update
            primary_down_relation pdr,
            atom_offering_info aoi
        set
            aoi.cooperator_id = pdr.primary_user_id
        where
            pdr.down_user_id = aoi.cooperator_id
        <if test="atomOfferingId != null and atomOfferingId != ''">
            and aoi.id = #{atomOfferingId}
        </if>
    </update>

    <select id="listSkuCooperatorHistory" resultType="com.chinamobile.iot.sc.pojo.dto.SkuCooperatorHistoryDTO">
        select distinct
            sku.offering_code skuOfferingCode,
            pdr.down_user_id cooperatorId
        from
            sku_offering_info sku,
            atom_offering_info aoi,
            primary_down_relation pdr
        where
            sku.offering_code = aoi.sku_code
        and sku.cooperator_id = pdr.down_user_id
        <if test="atomOfferingId != null and atomOfferingId != ''">
            and aoi.id = #{atomOfferingId}
        </if>
    </select>

    <update id="updateSkuCooperator">
        update
            sku_offering_info sku,
            atom_offering_info aoi,
            primary_down_relation pdr
        set
            sku.cooperator_id =  pdr.primary_user_id
        where
        sku.offering_code = aoi.sku_code
        and sku.cooperator_id = pdr.down_user_id
        <if test="atomOfferingId != null and atomOfferingId != ''">
            and aoi.id = #{atomOfferingId}
        </if>
    </update>

    <select id="listOrderCooperatorHistory" resultType="com.chinamobile.iot.sc.pojo.dto.OrderCooperatorHistoryDTO">
        select distinct
            o2ai.order_id orderId,
            o2ai.id atomOrderId,
            o2ai.cooperator_id cooperatorId
        from
            order_2c_atom_info o2ai,
            primary_down_relation pdr,
            atom_offering_info aoi
        where
            o2ai.cooperator_id = pdr.down_user_id
        and o2ai.atom_offering_code = aoi.offering_code
        and o2ai.spu_offering_code = aoi.spu_code
        and o2ai.sku_offering_code = aoi.sku_code
        <if test="atomOfferingId != null and atomOfferingId != ''">
            and aoi.id = #{atomOfferingId}
        </if>
    </select>

    <update id="updateOrderCooperator">
        update
            order_2c_atom_info o2ai,
            primary_down_relation pdr,
            atom_offering_info aoi
        set
            o2ai.cooperator_id = pdr.primary_user_id
        where
            o2ai.cooperator_id = pdr.down_user_id
        and o2ai.atom_offering_code = aoi.offering_code
        and o2ai.spu_offering_code = aoi.spu_code
        and o2ai.sku_offering_code = aoi.sku_code
        <if test="atomOfferingId != null and atomOfferingId != ''">
            and aoi.id = #{atomOfferingId}
        </if>
    </update>

    <select id="listOrderFinishCooperatorHistory" resultType="com.chinamobile.iot.sc.pojo.dto.OrderFinishCooperatorHistoryDTO">
        select distinct
            o2ai.order_id orderId,
            o2ai.id atomOrderId,
            o2ai.finish_cooperator_id cooperatorId
        from
            order_2c_atom_info o2ai,
            primary_down_relation pdr,
            atom_offering_info aoi
        where
            o2ai.finish_cooperator_id = pdr.down_user_id
        and o2ai.atom_offering_code = aoi.offering_code
        and o2ai.spu_offering_code = aoi.spu_code
        and o2ai.sku_offering_code = aoi.sku_code
        <if test="atomOfferingId != null and atomOfferingId != ''">
            and aoi.id = #{atomOfferingId}
        </if>
    </select>

    <update id="updateOrderFinishCooperator">
        update
            order_2c_atom_info o2ai,
            primary_down_relation pdr,
            atom_offering_info aoi
        set
            o2ai.finish_cooperator_id = pdr.primary_user_id
        where
            o2ai.finish_cooperator_id = pdr.down_user_id
        and o2ai.atom_offering_code = aoi.offering_code
        and o2ai.spu_offering_code = aoi.spu_code
        and o2ai.sku_offering_code = aoi.sku_code
        <if test="atomOfferingId != null and atomOfferingId != ''">
            and aoi.id = #{atomOfferingId}
        </if>
    </update>

    <update id="updateOrderRocCooperator">
        update
            order_2c_roc_info o2ri,
            order_2c_atom_info o2ai,
            primary_down_relation pdr,
            atom_offering_info aoi
        set
            o2ri.cooperator_id = pdr.primary_user_id
        where
            o2ri.atom_order_id = o2ai.id
        and o2ri.order_id = o2ai.order_id
        and	o2ri.cooperator_id = pdr.down_user_id
        and o2ai.atom_offering_code = aoi.offering_code
        and o2ai.spu_offering_code = aoi.spu_code
        and o2ai.sku_offering_code = aoi.sku_code
        <if test="atomOfferingId != null and atomOfferingId != ''">
            and aoi.id = #{atomOfferingId}
        </if>
    </update>

    <update id="updateOrderRocFinishCooperator">
        update
            order_2c_roc_info o2ri,
            order_2c_atom_info o2ai,
            primary_down_relation pdr,
            atom_offering_info aoi
        set
            o2ri.finish_cooperator_id = pdr.primary_user_id
        where
            o2ri.atom_order_id = o2ai.id
        and o2ri.order_id = o2ai.order_id
        and	o2ri.finish_cooperator_id = pdr.down_user_id
        and o2ai.atom_offering_code = aoi.offering_code
        and o2ai.spu_offering_code = aoi.spu_code
        and o2ai.sku_offering_code = aoi.sku_code
        <if test="atomOfferingId != null and atomOfferingId != ''">
            and aoi.id = #{atomOfferingId}
        </if>
    </update>

    <update id="updateInvoiceReverseRecordCooperator">
        update
            invoice_reverse_record irr,
            order_2c_atom_info o2ai,
            primary_down_relation pdr,
            atom_offering_info aoi
        set
            irr.cooperator_id = pdr.primary_user_id
        where
            irr.atom_order_id = o2ai.id
        and irr.order_id = o2ai.order_id
        and	irr.cooperator_id = pdr.down_user_id
        and o2ai.atom_offering_code = aoi.offering_code
        and o2ai.spu_offering_code = aoi.spu_code
        and o2ai.sku_offering_code = aoi.sku_code
        <if test="atomOfferingId != null and atomOfferingId != ''">
            and aoi.id = #{atomOfferingId}
        </if>
    </update>

    <update id="updateInvoiceReverseRecordFinishCooperator">
        update
            invoice_reverse_record irr,
            order_2c_atom_info o2ai,
            primary_down_relation pdr,
            atom_offering_info aoi
        set
            irr.finish_cooperator_id = pdr.primary_user_id
        where
            irr.atom_order_id = o2ai.id
        and irr.order_id = o2ai.order_id
        and	irr.finish_cooperator_id = pdr.down_user_id
        and o2ai.atom_offering_code = aoi.offering_code
        and o2ai.spu_offering_code = aoi.spu_code
        and o2ai.sku_offering_code = aoi.sku_code
        <if test="atomOfferingId != null and atomOfferingId != ''">
            and aoi.id = #{atomOfferingId}
        </if>
    </update>

    <update id="updateApplyInvoiceRecordCooperator">
        update
            apply_invoice_record air,
            order_2c_atom_info o2ai,
            primary_down_relation pdr,
            atom_offering_info aoi
        set
            air.cooperator_id = pdr.primary_user_id
        where
            air.atom_order_id = o2ai.id
        and air.order_id = o2ai.order_id
        and	air.cooperator_id = pdr.down_user_id
        and o2ai.atom_offering_code = aoi.offering_code
        and o2ai.spu_offering_code = aoi.spu_code
        and o2ai.sku_offering_code = aoi.sku_code
        <if test="atomOfferingId != null and atomOfferingId != ''">
            and aoi.id = #{atomOfferingId}
        </if>
    </update>

    <update id="updateApplyInvoiceRecordFinishCooperator">
        update
            apply_invoice_record air,
            order_2c_atom_info o2ai,
            primary_down_relation pdr,
            atom_offering_info aoi
        set
            air.finish_cooperator_id = pdr.primary_user_id
        where
            air.atom_order_id = o2ai.id
        and air.order_id = o2ai.order_id
        and	air.finish_cooperator_id = pdr.down_user_id
        and o2ai.atom_offering_code = aoi.offering_code
        and o2ai.spu_offering_code = aoi.spu_code
        and o2ai.sku_offering_code = aoi.sku_code
        <if test="atomOfferingId != null and atomOfferingId != ''">
            and aoi.id = #{atomOfferingId}
        </if>
    </update>

    <select id="getCooperatorAtomCount" resultType="java.lang.Integer">
        select count(1)
        from
        atom_offering_info aoi,
        atom_offering_cooperator_relation aocr
        where
        aoi.id = aocr.atom_offering_id
        and aoi.delete_time is null
        and aocr.cooperator_id = #{cooperatorId}
    </select>

    <select id="getCooperatorOrderCount" resultType="java.lang.Integer">
        select count(1)
        from
        order_2c_atom_info o2ai,
        order_cooperator_relation ocr
        where
        o2ai.order_id = ocr.order_id
        and o2ai.id = ocr.atom_order_id
        and o2ai.order_status != 2
        and o2ai.atom_offering_class != 'S'
        and ocr.cooperator_id = #{cooperatorId}
    </select>

    <select id="listAtomCooperateIdNotEqualSkuCooperateId" resultType="com.chinamobile.iot.sc.pojo.dto.AtomCooperateIdNotEqualSkuCooperateIdDTO">
        select
            sku.cooperator_id primaryCooperatorId,
            sku.offering_code skuOfferingCode,
            aoi.id atomOfferingId
        from
        atom_offering_info aoi
        inner join sku_offering_info sku on aoi.spu_code = sku.spu_code
        and aoi.sku_code = sku.offering_code
        inner join spu_offering_info spu on spu.offering_code = sku.spu_code
        inner join category_info ci on spu.id = ci.spu_id
        where
        aoi.cooperator_id = '-1'
        and sku.cooperator_id != '-1'
        and case when ci.offering_class != 'A13' then aoi.offering_class not in ('S','C')
        else 1=1
        end
    </select>

    <select id="listOrderCooperateIdNotEqualSkuCooperateId" resultType="com.chinamobile.iot.sc.pojo.dto.OrderCooperateIdNotEqualSkuCooperateIdDTO">
        select
            o2ai.id atomOrderId,
            o2ai.order_id orderId,
            o2ai.sku_offering_code skuOfferingCode,
            sku.cooperator_id primaryCooperatorId
        from
        order_2c_atom_info o2ai
        inner join order_2c_info o2i on o2ai.order_id = o2i.order_id
        inner join sku_offering_info sku on o2ai.spu_offering_code = sku.spu_code and o2ai.sku_offering_code = sku.offering_code
        where
        o2ai.cooperator_id = '-1'
        and sku.cooperator_id != '-1'
        and case when o2i.spu_offering_class != 'A13' then o2ai.atom_offering_class not in ('S','C')
        else 1=1
        end
    </select>

    <select id="selectProductExport" resultType="com.chinamobile.iot.sc.pojo.mapper.ProductExportDO">
        SELECT
        spu.offering_name spuOfferingName,
        spu.offering_code spuOfferingCode,
        ca.offering_class spuOfferingClass,
        DATE_FORMAT( atom.create_time, '%Y/%m/%d %H:%i:%s') createTime,
        spu.offering_status spuOfferingStatus,
        spu.secretly_listed as secretlyListed,
        spu.product_description as productDescription,
        (SELECT GROUP_CONCAT(DISTINCT d.`NAME`) FROM product_navigation_directory d JOIN navigation_info n ON d.id = n.level1_navigation_code WHERE n.spu_offering_code = spu.offering_code ) firstNavigationName,
        (SELECT GROUP_CONCAT(DISTINCT d.`NAME`) FROM product_navigation_directory d JOIN navigation_info n ON d.id = n.level2_navigation_code WHERE n.spu_offering_code = spu.offering_code ) secondNavigationName,
        (SELECT GROUP_CONCAT(DISTINCT d.`NAME`) FROM product_navigation_directory d JOIN navigation_info n ON d.id = n.level3_navigation_code WHERE n.spu_offering_code = spu.offering_code ) thirdNavigationName,
        (SELECT GROUP_CONCAT(label) FROM spu_sale_label where (type = 0 or type is null) and spu_code = spu.offering_code) mainSaleLabel,
        (SELECT GROUP_CONCAT(label) FROM spu_sale_label where type = 1 and spu_code = spu.offering_code) subSaleLabel,
        spu.product_keywords productKeywords,
        sku.offering_name skuOfferingName,
        sku.offering_code skuOfferingCode,
        sku.price/1000 price,
        sku.offering_status skuOfferingStatus,
        sku.receive_order_name as receiveOrderName,
        sku.receive_order_phone as receiveOrderPhone,
        sku.deliver_name as deliverName,
        sku.deliver_phone as deliverPhone,
        sku.aftermarket_name as aftermarketName,
        sku.aftermarket_phone as aftermarketPhone,
        srt.province_code skuReleaseProvince,
        srt.city_code skuReleaseCity,
        atom.offering_name atomOfferingName,
        atom.id atomId,
        atom.offering_code atomOfferingCode,
        atom.offering_class atomOfferingClass,
        atom.ext_soft_offering_code extSoftOfferingCode,
        atom.ext_hard_offering_code extHardOfferingCode,
        atom.quantity atomQuantity,
        atom.model,
        atom.color,
        atom.atom_sale_price/1000 atomSalePrice,
        atom.settle_price/1000 settlePrice,
        atom.offeringSaleRegion,
        atom.charge_code chargeCode,
        atom.charge_id chargeId,
        sku.supplier_name supplierName,
        ap.partner_name partnerName,
        ap.user_name cooperatorName,
        ss.id standardServiceCode,
        ss.`name` standardServiceName,
        ss.real_product_name realProductName,
        d.short_name department,
        pp.`name` property,
        ss.remark1,
        ss.remark2,
        atomh.atom_offering_version         as atomOfferingVersion,
        atomh.spu_offering_version          as spuOfferingVersion,
        atomh.sku_offering_version          as skuOfferingVersion
        FROM
        atom_offering_info atom
        JOIN sku_offering_info sku ON atom.sku_code = sku.offering_code AND atom.spu_code = sku.spu_code AND sku.delete_time is null
        JOIN spu_offering_info spu ON spu.offering_code = atom.spu_code AND spu.delete_time is null
        JOIN category_info ca ON ca.spu_id = spu.id
        LEFT JOIN atom_std_service ass ON ass.atom_id = atom.id
        LEFT JOIN standard_service ss ON ss.id = ass.std_service_id
        LEFT JOIN sku_release_target srt ON srt.sku_offering_code = sku.offering_code
        LEFT JOIN department d ON d.id = ss.product_department_id
        LEFT JOIN product_property pp ON pp.id = ss.product_property_id
        left join atom_offering_info_history atomh on  atomh.id = (
        select id from atom_offering_info_history atomh where atomh.offering_code = atom.offering_code
        and atomh.spu_code = atom.spu_code
        and atomh.sku_code = atom.sku_code
        order by atomh.atom_offering_version desc,atomh.sku_offering_version desc, atomh.spu_offering_version desc
        limit 1)
        <if test=" partnerName == null or partnerName == ''">
            left join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct aocr.cooperator_id) cooperator_id,
            aocr.atom_offering_id,GROUP_CONCAT(distinct up.is_primary) is_primary
            from
            atom_offering_cooperator_relation aocr
            inner join user_partner up on aocr.cooperator_id = up.user_id
            where
                1=1
            group by aocr.atom_offering_id
            ) ap on ap.atom_offering_id = atom.id
        </if>
        <if test=" partnerName != null and partnerName != ''">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct aocr.cooperator_id) cooperator_id,
            aocr.atom_offering_id,GROUP_CONCAT(distinct up.is_primary) is_primary
            from
            atom_offering_cooperator_relation aocr
            inner join user_partner up on aocr.cooperator_id = up.user_id
            where
                1=1
            and up.partner_name LIKE concat ('%',#{partnerName},'%')
            group by aocr.atom_offering_id
            ) ap on ap.atom_offering_id = atom.id
        </if>
        WHERE atom.delete_time is null
    <if test=" startTime != null">
        and atom.create_time <![CDATA[ >= ]]> #{startTime}
    </if>
    <if test=" endTime != null">
        and atom.create_time <![CDATA[ <= ]]> #{endTime}
    </if>
    <if test=" spuOfferingStatus != null and spuOfferingStatus != ''">
        and spu.offering_status = #{spuOfferingStatus}
    </if>
    <if test=" skuOfferingStatus != null and skuOfferingStatus != ''">
        and sku.offering_status = #{skuOfferingStatus}
    </if>
    <if test=" skuReleaseProvince != null and skuReleaseProvince != ''">
        and (srt.province_code = #{skuReleaseProvince} or srt.province_code = '000')
    </if>
    <if test=" skuReleaseCity != null and skuReleaseCity != ''">
        and srt.city_code = #{skuReleaseCity}
    </if>
    <!--<if test=" partnerName != null and partnerName != ''">
        and u.partner_name LIKE concat ('%',#{partnerName},'%')
    </if>-->
    ORDER BY atom.create_time DESC
    </select>

    <select id="getProductExportCount" resultType="java.lang.Integer">
        SELECT count(distinct atom.id)
        FROM
        atom_offering_info atom
        JOIN sku_offering_info sku ON atom.sku_code = sku.offering_code AND atom.spu_code = sku.spu_code AND sku.delete_time is null
        JOIN spu_offering_info spu ON spu.offering_code = atom.spu_code AND spu.delete_time is null
        JOIN category_info ca ON ca.spu_id = spu.id
        <if test=" skuReleaseCity != null and skuReleaseCity != ''">
            LEFT JOIN sku_release_target srt ON srt.sku_offering_code = sku.offering_code
        </if>
        <if test=" partnerName != null and partnerName != ''">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct aocr.cooperator_id) cooperator_id,
            aocr.atom_offering_id,GROUP_CONCAT(distinct up.is_primary) is_primary
            from
            atom_offering_cooperator_relation aocr
            inner join user_partner up on aocr.cooperator_id = up.user_id
            where
                1=1
            and up.partner_name LIKE concat ('%',#{partnerName},'%')
            group by aocr.atom_offering_id
            ) ap on ap.atom_offering_id = atom.id
        </if>
        WHERE atom.delete_time is null
        <if test=" startTime != null">
            and atom.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test=" endTime != null">
            and atom.create_time <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test=" spuOfferingStatus != null and spuOfferingStatus != ''">
            and spu.offering_status = #{spuOfferingStatus}
        </if>
        <if test=" skuOfferingStatus != null and skuOfferingStatus != ''">
            and sku.offering_status = #{skuOfferingStatus}
        </if>
        <if test=" skuReleaseProvince != null and skuReleaseProvince != ''">
            and (srt.province_code = #{skuReleaseProvince} or srt.province_code = '000')
        </if>
        <if test=" skuReleaseCity != null and skuReleaseCity != ''">
            and srt.city_code = #{skuReleaseCity}
        </if>
        <!--<if test=" partnerName != null and partnerName != ''">
            and u.partner_name LIKE concat ('%',#{partnerName},'%')
        </if>-->
    </select>

</mapper>