<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.handle.OrderHandleMapper">

    <resultMap id="orderInfoHandleMap" type="com.chinamobile.iot.sc.pojo.handle.OrderInfoHandle">
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="sku_offering_name" jdbcType="VARCHAR" property="skuOfferingName"/>
        <result column="atom_offering_name" jdbcType="VARCHAR" property="atomOfferingName"/>
        <result column="atom_offering_class" jdbcType="VARCHAR" property="atomOfferingClass"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="color" jdbcType="VARCHAR" property="color"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="atom_price" jdbcType="BIGINT" property="atomPrice"/>
        <result column="skuQuantity" jdbcType="INTEGER" property="skuQuantity"/>
        <result column="skuPrice" jdbcType="VARCHAR" property="skuPrice"/>
        <!--<result column="cooperator_id" jdbcType="VARCHAR" property="cooperatorId"/>
        <result column="cooperator_name" jdbcType="VARCHAR" property="cooperatorName"/>-->
        <result column="partner_name" jdbcType="VARCHAR" property="partnerName"/>
        <result column="order_status" jdbcType="INTEGER" property="orderStatus"/>
        <result column="spu_offering_class" jdbcType="VARCHAR" property="spuOfferingClass"/>
        <result column="spu_offering_code" jdbcType="VARCHAR" property="spuOfferingCode"/>
        <result column="spu_offering_name" jdbcType="VARCHAR" property="spuOfferingName"/>
        <result column="sku_offering_code" jdbcType="VARCHAR" property="skuOfferingCode"/>
        <result column="sync_k3_id" jdbcType="VARCHAR" property="syncK3Id"/>
        <result column="special_after_market_handle" jdbcType="INTEGER" property="specialAfterMarketHandle"/>
        <result column="special_after_status" jdbcType="VARCHAR" property="specialAfterStatus"/>
        <result column="special_after_latest_time" jdbcType="VARCHAR" property="specialAfterLatestTime"/>
        <result column="imgUrl" jdbcType="VARCHAR" property="imgUrl"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="carOpenStatus" jdbcType="VARCHAR" property="carOpenStatus"/>
        <result column="extSoftOfferingCode" jdbcType="VARCHAR" property="extSoftOfferingCode"/>
        <result column="kxRefundStatus" jdbcType="VARCHAR" property="kxRefundStatus"/>
        <result column="allowOrderStatus" jdbcType="INTEGER" property="allowOrderStatus"/>
        <result column="totalPrice" jdbcType="BIGINT" property="totalPrice"/>
        <result column="softServiceOpenStatus" jdbcType="INTEGER" property="softServiceOpenStatus"/>
        <result column="softServiceRetailStatus" jdbcType="INTEGER" property="softServiceRetailStatus"/>
        <result column="softServiceUseTime" jdbcType="TIMESTAMP" property="softServiceUseTime"/>
        <result column="productType" jdbcType="VARCHAR" property="productType"/>
        <result column="cardType" jdbcType="VARCHAR" property="cardType"/>
        <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="card_containing_terminal" jdbcType="VARCHAR" property="cardContainingTerminal"/>
        <result column="settleStatus" jdbcType="INTEGER" property="settleStatus"/>
        <result column="onlineSettleStatus" jdbcType="INTEGER" property="onlineSettleStatus"/>
        <result column="scmOrderNum" jdbcType="VARCHAR" property="scmOrderNum"/>
        <result column="reminderWaitSend" jdbcType="INTEGER" property="reminderWaitSend"/>
        <result column="reminderValetTaking" jdbcType="INTEGER" property="reminderValetTaking"/>
        <result column="reminderWaitDeliver" jdbcType="INTEGER" property="reminderWaitDeliver"/>
        <result column="receiveOrder" jdbcType="VARCHAR" property="receiveOrder"/>
    </resultMap>

    <resultMap id="orderInfoDetailHandleMap" type="com.chinamobile.iot.sc.pojo.handle.OrderInfoDetailHandle">
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="contact_person_name" jdbcType="VARCHAR" property="contactPersonName"/>
        <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone"/>
        <result column="addr1" jdbcType="VARCHAR" property="addr1"/>
        <result column="addr2" jdbcType="VARCHAR" property="addr2"/>
        <result column="addr3" jdbcType="VARCHAR" property="addr3"/>
        <result column="addr4" jdbcType="VARCHAR" property="addr4"/>
        <result column="usaddr" jdbcType="VARCHAR" property="usaddr"/>
        <result column="henan_real_name" jdbcType="VARCHAR" property="henanRealName"/>
        <result column="henan_real_phone" jdbcType="VARCHAR" property="henanRealPhone"/>
        <result column="henan_real_address" jdbcType="VARCHAR" property="henanRealAddress"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="spu_offering_class" jdbcType="VARCHAR" property="spuOfferingClass"/>
        <result column="sku_offering_name" jdbcType="VARCHAR" property="skuOfferingName"/>
        <result column="sku_offering_code" jdbcType="VARCHAR" property="skuOfferingCode"/>
        <result column="composition" jdbcType="VARCHAR" property="composition"/>
        <result column="atom_offering_name" jdbcType="VARCHAR" property="atomOfferingName"/>
        <result column="atom_offering_code" jdbcType="VARCHAR" property="atomOfferingCode"/>
        <result column="offering_class" jdbcType="VARCHAR" property="offeringClass"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="color" jdbcType="VARCHAR" property="color"/>
        <result column="quantity" jdbcType="BIGINT" property="quantity"/>
        <result column="skuQuantity" jdbcType="BIGINT" property="skuQuantity"/>
        <result column="create_time" jdbcType="VARCHAR" property="createTime"/>
        <result column="atom_price" jdbcType="BIGINT" property="atomPrice"/>
        <result column="skuPrice" jdbcType="BIGINT" property="skuPrice"/>
        <result column="order_status" jdbcType="INTEGER" property="orderStatus"/>
        <result column="partner_name" jdbcType="VARCHAR" property="partnerName"/>
        <result column="spu_offering_code" jdbcType="VARCHAR" property="spuOfferingCode"/>
        <result column="spu_offering_name" jdbcType="VARCHAR" property="spuOfferingName"/>
        <!--<result column="cooperator_name" jdbcType="VARCHAR" property="cooperatorName"/>
        <result column="cooperator_id" jdbcType="VARCHAR" property="cooperatorId"/>-->
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="special_after_status_str" jdbcType="VARCHAR" property="specialAfterStatusStr"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="imgUrl" jdbcType="VARCHAR" property="imgUrl"/>
        <result column="carOpenStatus" jdbcType="VARCHAR" property="carOpenStatus"/>
        <result column="custMgName" jdbcType="VARCHAR" property="custMgName"/>
        <result column="custMgPhone" jdbcType="VARCHAR" property="custMgPhone"/>
        <result column="employeeNum" jdbcType="VARCHAR" property="employeeNum"/>
        <result column="createOperCode" jdbcType="VARCHAR" property="createOperCode"/>
        <result column="custName" jdbcType="VARCHAR" property="custName"/>
        <result column="custCode" jdbcType="VARCHAR" property="custCode"/>
        <result column="primary_cooperator_id" jdbcType="VARCHAR" property="primaryCooperatorId"/>
        <result column="reserveBeId" jdbcType="VARCHAR" property="reserveBeId"/>
        <result column="reserveLocation" jdbcType="VARCHAR" property="reserveLocation"/>
        <result column="settleStatus" jdbcType="VARCHAR" property="settleStatus"/>
        <result column="orderDeductPrice" jdbcType="VARCHAR" property="orderDeductPrice"/>
        <result column="customerType" jdbcType="VARCHAR" property="customerType"/>
    </resultMap>

    <!--    <resultMap id="CardxDeliveryNotesInfo" type="com.chinamobile.iot.sc.pojo.mapper.DeliveryNotesDO">-->
    <!--        <result column="orderId" jdbcType="VARCHAR" property="orderId"/>-->
    <!--        <result column="receiverName" jdbcType="VARCHAR" property="receiverName"/>-->
    <!--        <result column="receiverPhone" jdbcType="VARCHAR" property="receiverPhone"/>-->
    <!--        <result column="addr1" jdbcType="VARCHAR" property="addr1"/>-->
    <!--        <result column="addr2" jdbcType="VARCHAR" property="addr2"/>-->
    <!--        <result column="addr3" jdbcType="VARCHAR" property="addr3"/>-->
    <!--        <result column="addr4" jdbcType="VARCHAR" property="addr4"/>-->
    <!--        <result column="usaddr" jdbcType="VARCHAR" property="usaddr"/>-->
    <!--        <result column="logisticsNumber" jdbcType="VARCHAR" property="logisticsNumber"/>-->
    <!--        <result column="skuName" jdbcType="VARCHAR" property="skuName"/>-->
    <!--        <result column="skuQuantity" jdbcType="VARCHAR" property="skuQuantity"/>-->
    <!--        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>-->
    <!--        <collection property="deviceVersionList" ofType="java.lang.String">-->
    <!--            <result column="deviceVersion" jdbcType="VARCHAR" property="deviceVersion" />-->
    <!--        </collection>-->
    <!--    </resultMap>-->


    <select id="selectOrderDetailByHandle" resultMap="orderInfoDetailHandleMap">
        select oa.id                              as id,
        oa.order_id                        as order_id,
        oi.contact_person_name             as contact_person_name,
        oi.contact_phone                   as contact_phone,
        oi.addr1                           as addr1,
        oi.addr2                           as addr2,
        oi.addr3                           as addr3,
        oi.addr4                           as addr4,
        oi.usaddr                          as usaddr,
        oi.henan_real_name                 as henan_real_name,
        oi.henan_real_phone                as henan_real_phone,
        oi.henan_real_address              as henan_real_address,
        oi.spu_offering_class              as spu_offering_class,
        oi.remarks                         as remarks,
        oa.sku_offering_name               as sku_offering_name,
        oa.atom_offering_name              as atom_offering_name,
        oa.atom_offering_code              as atom_offering_code,
        oa.atom_offering_class             as offering_class,
        oa.supplier_name                   as supplier_name,
        oa.model                           as model,
        oa.color                           as color,
        oa.sku_quantity * oa.atom_quantity as quantity,
        oa.sku_quantity skuQuantity,
        oa.create_time                     as create_time,
        oa.atom_price                      as atom_price,
        oa.order_status                    as order_status,
        oa.cooperator_id primary_cooperator_id,
        <!--cp.partner_name,
        cp.user_name cooperator_name,
        cp.cooperator_id,-->
        oa.sku_offering_code               as sku_offering_code,
        si.offering_code                   as spu_offering_code,
        si.offering_name                   as spu_offering_name,
        oi.order_type                      as orderType,
        oi.qly_status                      as qlyStatus,
        oi.ysx_status                      as ysxStatus,
        oi.order_status_time               as orderStatusTime,
        oi.pay_time                        as payTime,
        oa.atom_offering_version           as atomOfferingVersion,
        oa.spu_offering_version            as spuOfferingVersion,
        oa.sku_offering_version            as skuOfferingVersion,
        oi.special_after_refunds_number as specialAfterRefundsNumber,
        oa.soft_service_status             as softServiceStatus,
        case
        when oi.special_after_status is null or oi.special_after_status = '' then ''
        when oi.special_after_status = '1' then '待退款'
        when oi.special_after_status = '2' then '退款中'
        when oi.special_after_status = '3' then '退款成功'
        when oi.special_after_status = '4' then '退款取消'
        when oi.special_after_status = '5' then '部分退款取消'
        when oi.special_after_status = '6' then '部分退款成功'
        else '退款状态错误'
        end special_after_status_str,
        si.url,
        si.img_url imgUrl,
       <!-- ai.ext_soft_offering_code extSoftOfferingCode,-->
        oa.car_open_status carOpenStatus,
        oi.cust_mg_name custMgName,
        oi.cust_mg_phone custMgPhone,
        oi.employee_num  employeeNum,
        oi.create_oper_code createOperCode,
        oi.cust_name custName,
        oi.cust_code custCode,
        oi.reserve_be_id reserveBeId,
        oi.reserve_location reserveLocation,
        oa.settle_status settleStatus,
        oi.deduct_price orderDeductPrice,
        oa.sku_price skuPrice,
        oi.customer_type customerType
        from order_2c_atom_info oa
        left join order_2c_info oi on oa.order_id = oi.order_id
        left join user_partner on oa.cooperator_id = user_partner.user_id
        left join spu_offering_info_history si on si.offering_code = oa.spu_offering_code
        and  si.spu_offering_version = oa.spu_offering_version
        <!--left join atom_offering_info_history ai on ai.spu_code = oa.spu_offering_code and ai.sku_code = oa.sku_offering_code
        and ai.offering_code = oa.atom_offering_code
        and ai.spu_offering_version = oa.spu_offering_version and ai.sku_offering_version = oa.sku_offering_version
        and ai.atom_offering_version = oa.atom_offering_version-->
        <!--left join (
        select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
        ocr.atom_order_id,ocr.order_id
        from order_cooperator_relation ocr
        inner JOIN user_partner up on up.user_id = ocr.cooperator_id
        group by ocr.atom_order_id
        ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id-->

        where oa.id = #{id}

    </select>

    <select id="selectOrderDetailByOrderId" resultMap="orderInfoDetailHandleMap">
        select
        oa.id                              as id,
        oa.order_id                        as order_id,
        oi.contact_person_name             as contact_person_name,
        oi.contact_phone                   as contact_phone,
        oi.addr1                           as addr1,
        oi.addr2                           as addr2,
        oi.addr3                           as addr3,
        oi.addr4                           as addr4,
        oi.usaddr                          as usaddr,
        oi.henan_real_name                 as henan_real_name,
        oi.henan_real_phone                as henan_real_phone,
        oi.henan_real_address              as henan_real_address,
        oi.spu_offering_class              as spu_offering_class,
        oi.remarks                         as remarks,
        oa.sku_offering_name               as sku_offering_name,
        oa.atom_offering_name              as atom_offering_name,
        oa.atom_offering_code              as atom_offering_code,
        oa.atom_offering_class             as offering_class,
        oa.supplier_name                   as supplier_name,
        oa.model                           as model,
        oa.color                           as color,
        oa.sku_quantity * oa.atom_quantity as quantity,
        oa.sku_quantity skuQuantity,
        oa.create_time                     as create_time,
        oa.atom_price                      as atom_price,
        oa.order_status                    as order_status,
        oa.cooperator_id primary_cooperator_id,
        <!--cp.partner_name,
        cp.user_name cooperator_name,
        cp.cooperator_id,-->
        oa.sku_offering_code               as sku_offering_code,
        si.offering_code                   as spu_offering_code,
        si.offering_name                   as spu_offering_name,
        oi.order_type                      as orderType,
        oi.qly_status                      as qlyStatus,
        oi.ysx_status                      as ysxStatus,
        oi.order_status_time               as orderStatusTime,
        oi.pay_time                        as payTime,
        oa.atom_offering_version           as atomOfferingVersion,
        oa.spu_offering_version            as spuOfferingVersion,
        oa.sku_offering_version            as skuOfferingVersion,
        oi.special_after_refunds_number as specialAfterRefundsNumber,
        oa.soft_service_status             as softServiceStatus,
        case
        when oi.special_after_status is null or oi.special_after_status = '' then ''
        when oi.special_after_status = '1' then '待退款'
        when oi.special_after_status = '2' then '退款中'
        when oi.special_after_status = '3' then '退款成功'
        when oi.special_after_status = '4' then '退款取消'
        when oi.special_after_status = '5' then '部分退款取消'
        when oi.special_after_status = '6' then '部分退款成功'
        else '退款状态错误'
        end special_after_status_str,
        si.url,
        si.img_url imgUrl,
        <!-- ai.ext_soft_offering_code extSoftOfferingCode,-->
        oa.car_open_status carOpenStatus,
        oi.cust_mg_name custMgName,
        oi.cust_mg_phone custMgPhone,
        oi.employee_num  employeeNum,
        oi.create_oper_code createOperCode,
        oi.cust_name custName,
        oi.cust_code custCode,
        oi.reserve_be_id reserveBeId,
        oi.reserve_location reserveLocation,
        oa.settle_status settleStatus,
        oi.deduct_price orderDeductPrice,
        oa.sku_price skuPrice,
        oi.customer_type customerType
        from order_2c_atom_info oa
        inner join order_2c_info oi on oa.order_id = oi.order_id
        left join user_partner on oa.cooperator_id = user_partner.user_id
        left join spu_offering_info_history si on si.offering_code = oa.spu_offering_code
        and  si.spu_offering_version = oa.spu_offering_version
        left join atom_offering_info_history ai on ai.spu_code = oa.spu_offering_code and ai.sku_code = oa.sku_offering_code
        and ai.offering_code = oa.atom_offering_code
        and ai.spu_offering_version = oa.spu_offering_version and ai.sku_offering_version = oa.sku_offering_version
        and ai.atom_offering_version = oa.atom_offering_version
        <!--left join (
        select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
        ocr.atom_order_id,ocr.order_id
        from order_cooperator_relation ocr
        inner JOIN user_partner up on up.user_id = ocr.cooperator_id
        group by ocr.atom_order_id
        ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id-->

        where oa.order_id = #{orderId}
        and case when oi.spu_offering_class != 'A13' then oa.atom_offering_class not in ('S','C')
            when oi.spu_offering_class = 'A13' then (oa.soft_service_status is not null and oa.soft_service_status != '')
        else 1=1
        end
        and case
        when oa.atom_offering_class = 'X' then (ai.card_containing_terminal is null or ai.card_containing_terminal = 1)
        else 1=1
        end
        limit 1

    </select>

    <select id="listAtomOfferingInfoByAtomOrderId" resultType="com.chinamobile.iot.sc.pojo.dto.AtomOfferingInfoByAtomOrderIdDTO">
        select
            aoih.offering_name atomOfferingName,
            aoih.offering_class atomOfferingClass,
            o2ai.atom_quantity * o2ai.sku_quantity  quantity,
            o2ai.atom_price atomPrice,
            aoih.offering_code atomOfferingCode,
            aoih.ext_soft_offering_code extSoftOfferingCode
        from
            order_2c_atom_info o2ai
        inner join atom_offering_info_history aoih on o2ai.spu_offering_code = aoih.spu_code
        and o2ai.sku_offering_code = aoih.sku_code and o2ai.atom_offering_code = aoih.offering_code
        and o2ai.atom_offering_version = aoih.atom_offering_version
        and o2ai.spu_offering_version = aoih.spu_offering_version and o2ai.sku_offering_version = aoih.sku_offering_version
        where o2ai.order_id = #{orderId}
    </select>

    <select id="selectOrderListByHandle"
            parameterType="com.chinamobile.iot.sc.pojo.param.OrderListQueryParam"
            resultMap="orderInfoHandleMap">
        select
        <!--oa.id as id,-->
        oa.create_time as create_time,
        oa.order_id as order_id,
        si.offering_code as spu_offering_code,
        si.offering_name as spu_offering_name,
        oa.sku_offering_name as sku_offering_name,
        oa.sku_offering_code as sku_offering_code,
        <!--oa.atom_offering_name as atom_offering_name,
        oa.atom_offering_class as atom_offering_class,-->
        oa.supplier_name as supplier_name,
        <!--oa.model as model,
        oa.color as color,
        oa.atom_quantity*oa.sku_quantity as quantity,
        oa.atom_price as atom_price,-->
        oa.sku_quantity as skuQuantity,
        oa.sku_price as skuPrice,
        oa.order_status as order_status,
        oi.spu_offering_class as spu_offering_class,
        <!--cp.cooperator_id,
        cph.cooperator_id finish_cooperator_id,-->
        oi.sync_k3_id,
        oi.special_after_market_handle,
        oi.special_after_status,
        oi.special_after_latest_time,
        oi.addr1 addr1,
        oi.order_type orderType,
        oi.qly_status qlyStatus,
        si.url,
        si.img_url imgUrl,
        oi.ysx_status ysxStatus,
        oa.car_open_status carOpenStatus,
        <!--aoi.ext_soft_offering_code extSoftOfferingCode,-->
        oi.kx_refund_status kxRefundStatus,
        oa.allow_order_status allowOrderStatus,
        oi.total_price totalPrice,
        oi.special_after_refunds_number specialAfterRefundsNumber,
        <!--oa.soft_service_status softServiceStatus,-->
        skuh.product_type productType,
        skuh.card_type cardType,
        oi.update_time updateTime,
        aoi.card_containing_terminal,
        oa.settle_status settleStatus,
        ospoo.scm_order_num scmOrderNum,
        ospoo.settle_status onlineSettleStatus,
        oi.reminder_wait_send reminderWaitSend,
        oi.reminder_valet_taking reminderValetTaking,
        oi.reminder_wait_deliver reminderWaitDeliver,
        skuh.receive_order receiveOrder
        from order_2c_atom_info oa
        inner join order_2c_info oi on oa.order_id=oi.order_id
        left join spu_offering_info_history si on si.offering_code = oa.spu_offering_code and si.spu_offering_version =
        oa.spu_offering_version
        left join atom_offering_info_history aoi on aoi.spu_code = oa.spu_offering_code
        and aoi.sku_code = oa.sku_offering_code and aoi.offering_code = oa.atom_offering_code
        and aoi.spu_offering_version = oa.spu_offering_version and aoi.sku_offering_version = oa.sku_offering_version
        and aoi.atom_offering_version = oa.atom_offering_version and aoi.delete_time is null
        left join sku_offering_info_history skuh on skuh.spu_code = si.offering_code and skuh.offering_code =
        oa.sku_offering_code
        and skuh.spu_offering_version = si.spu_offering_version and skuh.sku_offering_version = oa.sku_offering_version
        and skuh.delete_time is null
        left join (SELECT ospo.scm_order_num ,osoo.order_id,
            ospo.settle_status FROM online_settlement_os_order osoo
            INNER join online_settlement_purchase_order ospo on ospo.id = osoo.online_settlement_purchase_order_id) ospoo
            on ospoo.order_id = oa.order_id
        <!--<if test="(userIdList == null or userIdList.size() == 0) and (partnerName == null or partnerName == '') and (cooperatorName == null or cooperatorName == '')">
            left join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>-->
        <!--<if test="(userIdList != null and userIdList.size() != 0) or (partnerName != null and partnerName != '') or (cooperatorName != null and cooperatorName != '')">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            where 1=1
            <if test="userIdList != null and userIdList.size() != 0">
                and ocr.cooperator_id in
                <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="partnerName != null and partnerName != ''">
                and up.partner_name LIKE concat ('%',#{partnerName},'%')
            </if>
            <if test="cooperatorName != null and cooperatorName != ''">
                and up.name like concat ('%',#{cooperatorName},'%')
            </if>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>-->
        <if test="(userIdList != null and userIdList.size() != 0) or (partnerName != null and partnerName != '') or (cooperatorName != null and cooperatorName != '')">
            inner join  order_cooperator_relation ocr on ocr.atom_order_id = oa.id and ocr.order_id = oa.order_id
            inner join user_partner up on up.user_id = ocr.cooperator_id
        </if>
        <!--left join (
        select group_concat(distinct ocrh.cooperator_id) cooperator_id,ocrh.atom_order_id,ocrh.order_id
        from order_cooperator_relation_history ocrh
        group by ocrh.atom_order_id
        ) cph on cph.atom_order_id = oa.id and cph.order_id = oa.order_id-->
        <if test="h5Status!=null">
            left join service_open_info soi on oa.id = soi.atom_order_id and oi.spu_offering_class = 'A13'
        </if>

        <if test="msisdn != null and msisdn != ''">
            inner join card_mall_sync cms on cms.order_id = oa.order_id and cms.atom_order_id = oa.id
        </if>
        where oa.order_status not in (13,14)
        and case when oi.spu_offering_class != 'A13' then oa.atom_offering_class not in ('S','C')
                 when oi.spu_offering_class = 'A13' then (oa.soft_service_status is not null and oa.soft_service_status != '')
        else 1=1
        end
        and case
        when oa.atom_offering_class = 'X' then (aoi.card_containing_terminal is null or aoi.card_containing_terminal = 1)
        else 1=1
        end
        <if test="roleType == 'partner' or roleType == 'partnerLord'">
            and ( ( oi.spu_offering_class != 'A13' and (oa.atom_offering_class != 'S' or oa.atom_offering_class is null)
            ) or (oi.spu_offering_class = 'A13') )
        </if>
        <if test="orderId!=null and orderId!='' ">
            and oa.order_id like concat('%',#{orderId},'%')
        </if>
        <!--<if test="userIdList != null and userIdList.size() != 0">
            and oa.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>-->

        <if test="userIdList != null and userIdList.size() != 0">
            and ocr.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="partnerName != null and partnerName != ''">
            and up.partner_name LIKE concat ('%',#{partnerName},'%')
        </if>
        <if test="cooperatorName != null and cooperatorName != ''">
            and up.name like concat ('%',#{cooperatorName},'%')
        </if>
        <if test="orderStatus!=null and orderStatus.size()!=0">
            and oa.order_status in
            <foreach collection="orderStatus" item="status" index="index" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="startTime != null and startTime != ''">
            and oa.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and oa.create_time <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="spuOfferingClass!=null and spuOfferingClass.size()!=0">
            and oi.spu_offering_class in
            <foreach collection="spuOfferingClass" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="spuOfferingName != null and spuOfferingName!= ''">
            and si.offering_name like concat ('%',#{spuOfferingName},'%')
        </if>
        <if test="spuOfferingCode != null and spuOfferingCode != ''">
            and si.offering_code like concat ('%',#{spuOfferingCode},'%')
        </if>
        <if test="skuOfferingName != null and skuOfferingName != ''">
            and oa.sku_offering_name like concat ('%',#{skuOfferingName},'%')
        </if>
        <if test="skuOfferingCode != null and skuOfferingCode != ''">
            and oa.sku_offering_code like concat ('%',#{skuOfferingCode},'%')
        </if>
        <if test="atomOfferingCode != null and atomOfferingCode != ''">
            and oa.atom_offering_code like concat ('%',#{atomOfferingCode},'%')
        </if>
        <if test="atomOfferingName != null and atomOfferingName != ''">
            and oa.atom_offering_name like concat ('%',#{atomOfferingName},'%')
        </if>
        <!--<if test="partnerName != null and partnerName != ''">
            and user_partner.partner_name LIKE concat ('%',#{partnerName},'%')
        </if>-->
        <!--<if test="cooperatorName != null and cooperatorName != ''">
            and user_partner.name like concat ('%',#{cooperatorName},'%')
        </if>-->
        <if test="specialAfterMarketHandle !=null">
            and oi.special_after_market_handle =#{specialAfterMarketHandle}
        </if>
        <if test="specialAfterStatus != null and specialAfterStatus != '' ">
            and oi.special_after_status =#{specialAfterStatus}
        </if>
        <if test="orderType != null and orderType != ''">
            <if test="orderType == '00' or orderType == '02' or orderType == '03'">
                and oi.order_type in ('00','02','03')
            </if>
            <if test="orderType != '00' and orderType != '02' and orderType != '03'">
                and oi.order_type = #{orderType}
            </if>
        </if>
        <if test="qlyStatus != null">
            and oi.qly_status = #{qlyStatus}
        </if>
        <if test="h5Key != null and h5Key != '' ">
            and (oa.order_id like '%${h5Key}%' || si.offering_name like '%${h5Key}%')
        </if>
        <if test="h5SpuOfferingClasses != null and h5SpuOfferingClasses.size() != 0">
            and oi.spu_offering_class in
            <foreach collection="h5SpuOfferingClasses" item="spuClass" index="index" open="(" close=")" separator=",">
                #{spuClass}
            </foreach>
        </if>
        <if test="clusterCode != null and clusterCode != '' ">
            and ( EXISTS (SELECT * FROM order_2c_atom_sn WHERE atom_order_id = oa.id and sn like '%${clusterCode}%')
            or EXISTS (SELECT * FROM card_relation WHERE order_id=oa.order_id and delete_time is null and imei like
            '%${clusterCode}%'))
        </if>

        <if test="h5Status != null  ">
            <if test="h5Status == 1">
                and (
                (oa.order_status = 0 and oi.spu_offering_class in ('A06','A07')) or
                (oa.order_status in (0,10,16) and oi.spu_offering_class = 'A11' and skuh.product_type in
                ('4','5','6','7','8','9','10','11','12')) or
                ((oa.soft_service_status = 1 or soi.sync_iot_fail_status in (1,2) ) and oi.spu_offering_class = 'A13'
                and oa.order_status not in (7,8,9))
                )
            </if>
            <if test="h5Status == 2">
                and (
                (oi.spu_offering_class in ('A06','A07') and oa.order_status != 0 ) or
                ( oi.spu_offering_class = 'A11' and (skuh.product_type is null or skuh.product_type not in
                ('4','5','6','7','8','9','10','11','12') or oa.order_status not in (0,10,16)) ) or
                (oi.spu_offering_class = 'A13' and
                (
                ((oa.soft_service_status != 1 or oa.soft_service_status is null) and (soi.sync_iot_fail_status is null
                or soi.sync_iot_fail_status not in (1,2)) ) or
                (oa.order_status in (7,8,9))
                )
                )
                )
            </if>
        </if>
        <if test="beId != null and beId != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code =
            oa.sku_offering_code and (srt.province_code = #{beId} or srt.province_code = '000'))
        </if>
        <if test="location != null and location != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code =
            oa.sku_offering_code and srt.city_code =#{location})
        </if>
        <if test="softServiceStatus!=null">
            and oa.soft_service_status = #{softServiceStatus}
        </if>

        <if test="settleStatus!=null">
            and (
            (oa.settle_status is null or oa.settle_status = '')
            and oi.spu_offering_class in ('A04','A08','A09','A13','A14','A15','A16','A17')
            and oa.atom_offering_class not in ('C','A')
            )
        </if>

        <if test="onlineSettleStatus!=null">
            and ospoo.settle_status = #{onlineSettleStatus}
        </if>
        <if test="msisdn != null and msisdn != ''">
            and cms.msisdn LIKE concat ('%',#{msisdn},'%')
        </if>
        <if test="custName !=null and custName != '' ">
            and oi.cust_name = #{custName}
        </if>
        <if test="logisticsNum != null and logisticsNum != '' ">
            and exists (select li.logis_code from logistics_info li where li.order_id = oa.order_id and li.logis_code
            like '%${logisticsNum}%')

        </if>
      <!--  group by oa.id-->
        group by oi.order_id
        ORDER BY oi.update_time DESC
        limit ${num} OFFSET ${(page - 1) * num}
    </select>


    <select id="countOrderList"
            parameterType="com.chinamobile.iot.sc.pojo.param.OrderListQueryParam"
            resultType="java.lang.Long">
        select
        count(distinct oa.order_id)
        <!--count(distinct oa.id)-->
        from order_2c_atom_info oa
        inner join order_2c_info oi on oa.order_id=oi.order_id
        left join atom_offering_info_history aoi on aoi.spu_code = oa.spu_offering_code
        and aoi.sku_code = oa.sku_offering_code and aoi.offering_code = oa.atom_offering_code
        and aoi.spu_offering_version = oa.spu_offering_version and aoi.sku_offering_version = oa.sku_offering_version
        and aoi.atom_offering_version = oa.atom_offering_version and aoi.delete_time is null
        left join (SELECT ospo.scm_order_num ,osoo.order_id,
        ospo.settle_status FROM online_settlement_os_order osoo
        INNER join online_settlement_purchase_order ospo on ospo.id = osoo.online_settlement_purchase_order_id) ospoo
        on ospoo.order_id = oa.order_id
        <if test="(spuOfferingName != null and spuOfferingName!= '') or (spuOfferingCode != null and spuOfferingCode != '') or (h5Key != null and h5Key != '') ">
            left join spu_offering_info_history si on si.offering_code = oa.spu_offering_code and
            si.spu_offering_version = oa.spu_offering_version
            and si.delete_time is null
        </if>
        <if test="h5Status != null">
            left join sku_offering_info_history skuh on skuh.spu_code = oa.spu_offering_code and skuh.offering_code =
            oa.sku_offering_code
            and skuh.spu_offering_version = oa.spu_offering_version and skuh.sku_offering_version =
            oa.sku_offering_version and skuh.delete_time is null
            left join service_open_info soi on oa.id = soi.atom_order_id and oi.spu_offering_class = 'A13'
        </if>
        <!--<if test="(userIdList == null or userIdList.size() == 0) and (partnerName == null or partnerName == '')">
            left join (
            select group_concat(distinct ocr.cooperator_id) cooperator_id,ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>-->
        <!--<if test="(userIdList != null and userIdList.size() != 0) or (partnerName != null and partnerName != '') or (cooperatorName != null and cooperatorName != '')">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            where 1=1
            <if test="userIdList != null and userIdList.size() != 0">
                and ocr.cooperator_id in
                <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="partnerName != null and partnerName != ''">
                and up.partner_name LIKE concat ('%',#{partnerName},'%')
            </if>
            <if test="cooperatorName != null and cooperatorName != ''">
                and up.name like concat ('%',#{cooperatorName},'%')
            </if>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>-->

        <if test="(userIdList != null and userIdList.size() != 0) or (partnerName != null and partnerName != '') or (cooperatorName != null and cooperatorName != '')">
            inner join  order_cooperator_relation ocr on ocr.atom_order_id = oa.id and ocr.order_id = oa.order_id
            inner join user_partner up on up.user_id = ocr.cooperator_id
        </if>

        <if test="msisdn != null and msisdn != ''">
            inner join card_mall_sync cms on cms.order_id = oa.order_id and cms.atom_order_id = oa.id
        </if>
        where 1=1 and oa.order_status not in (13,14)
        and case when oi.spu_offering_class != 'A13' then oa.atom_offering_class not in ('S','C')
                 when oi.spu_offering_class = 'A13' then (oa.soft_service_status is not null and oa.soft_service_status != '')
        else 1=1
        end
        and case
        when oa.atom_offering_class = 'X' then (aoi.card_containing_terminal is null or aoi.card_containing_terminal = 1)
        else 1=1
        end
        <if test="roleType == 'partner' or roleType == 'partnerLord'">
            and ( ( oi.spu_offering_class != 'A13' and (oa.atom_offering_class != 'S' or oa.atom_offering_class is null)
            ) or (oi.spu_offering_class = 'A13') )
        </if>
        <if test="orderId!=null and orderId!='' ">
            and oa.order_id like concat('%',#{orderId},'%')
        </if>
        <!--<if test="userIdList != null and userIdList.size() != 0">
            and oa.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>-->
        <if test="userIdList != null and userIdList.size() != 0">
            and ocr.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="partnerName != null and partnerName != ''">
            and up.partner_name LIKE concat ('%',#{partnerName},'%')
        </if>
        <if test="cooperatorName != null and cooperatorName != ''">
            and up.name like concat ('%',#{cooperatorName},'%')
        </if>
        <if test="orderStatus!=null and orderStatus.size()!=0">
            and oa.order_status in
            <foreach collection="orderStatus" item="status" index="index" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="startTime != null and startTime != ''">
            and oa.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and oa.create_time <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="spuOfferingClass!=null and spuOfferingClass.size()!=0">
            and oi.spu_offering_class in
            <foreach collection="spuOfferingClass" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="spuOfferingName != null and spuOfferingName!= ''">
            and si.offering_name like concat ('%',#{spuOfferingName},'%')
        </if>
        <if test="spuOfferingCode != null and spuOfferingCode != ''">
            and si.offering_code like concat ('%',#{spuOfferingCode},'%')
        </if>
        <if test="skuOfferingName != null and skuOfferingName != ''">
            and oa.sku_offering_name like concat ('%',#{skuOfferingName},'%')
        </if>
        <if test="skuOfferingCode != null and skuOfferingCode != ''">
            and oa.sku_offering_code like concat ('%',#{skuOfferingCode},'%')
        </if>
        <if test="atomOfferingCode != null and atomOfferingCode != ''">
            and oa.atom_offering_code like concat ('%',#{atomOfferingCode},'%')
        </if>
        <if test="atomOfferingName != null and atomOfferingName != ''">
            and oa.atom_offering_name like concat ('%',#{atomOfferingName},'%')
        </if>
        <!--<if test="partnerName != null and partnerName != ''">
            and user_partner.partner_name LIKE concat ('%',#{partnerName},'%')
        </if>-->
        <!--<if test="cooperatorName != null and cooperatorName != ''">
            and user_partner.name like concat ('%',#{cooperatorName},'%')
        </if>-->
        <if test="specialAfterMarketHandle !=null">
            and oi.special_after_market_handle =#{specialAfterMarketHandle}
        </if>
        <if test="specialAfterStatus != null and specialAfterStatus != '' ">
            and oi.special_after_status =#{specialAfterStatus}
        </if>
        <if test="orderType != null and orderType != ''">
            <if test="orderType == '00' or orderType == '02' or orderType == '03'">
                and oi.order_type in ('00','02','03')
            </if>
            <if test="orderType != '00' and orderType != '02' and orderType != '03'">
                and oi.order_type = #{orderType}
            </if>
        </if>
        <if test="qlyStatus != null">
            and oi.qly_status = #{qlyStatus}
        </if>
        <if test="h5Key != null and h5Key != '' ">
            and (oa.order_id like '%${h5Key}%' || si.offering_name like '%${h5Key}%')
        </if>
        <if test="clusterCode != null and clusterCode != '' ">
            and ( EXISTS (SELECT * FROM order_2c_atom_sn WHERE atom_order_id = oa.id and sn like '%${clusterCode}%')
            or EXISTS (SELECT * FROM card_relation WHERE order_id=oa.order_id and delete_time is null and imei like
            '%${clusterCode}%'))
        </if>
        <if test="h5SpuOfferingClasses != null and h5SpuOfferingClasses.size() != 0">
            and oi.spu_offering_class in
            <foreach collection="h5SpuOfferingClasses" item="spuClass" index="index" open="(" close=")" separator=",">
                #{spuClass}
            </foreach>
        </if>
        <if test="h5Status != null  ">
            <if test="h5Status == 1">
                and (
                (oa.order_status = 0 and oi.spu_offering_class in ('A06','A07')) or
                (oa.order_status in (0,10,16) and oi.spu_offering_class = 'A11' and skuh.product_type in
                ('4','5','6','7','8','9','10','11','12')) or
                ((oa.soft_service_status = 1 or soi.sync_iot_fail_status in (1,2) ) and oi.spu_offering_class = 'A13'
                and oa.order_status not in (7,8,9))
                )
            </if>
            <if test="h5Status == 2">
                and (
                (oi.spu_offering_class in ('A06','A07') and oa.order_status != 0 ) or
                ( oi.spu_offering_class = 'A11' and (skuh.product_type is null or skuh.product_type not in
                ('4','5','6','7','8','9','10','11','12') or oa.order_status not in (0,10,16)) ) or
                (oi.spu_offering_class = 'A13' and
                (
                ((oa.soft_service_status != 1 or oa.soft_service_status is null) and (soi.sync_iot_fail_status is null
                or soi.sync_iot_fail_status not in (1,2)) ) or
                (oa.order_status in (7,8,9))
                )
                )
                )
            </if>
        </if>
        <if test="beId != null and beId != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code =
            oa.sku_offering_code and (srt.province_code = #{beId} or srt.province_code = '000'))
        </if>
        <if test="location != null and location != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code =
            oa.sku_offering_code and srt.city_code =#{location})
        </if>
        <if test="softServiceStatus!= null">
            and oa.soft_service_status = #{softServiceStatus}
        </if>
        <if test="settleStatus!=null">
            and (
            (oa.settle_status is null or oa.settle_status = '')
            and oi.spu_offering_class in ('A04','A08','A09','A13','A14','A15','A16','A17')
            and oa.atom_offering_class not in ('C','A')
            )
        </if>
        <if test="onlineSettleStatus!=null">
            and ospoo.settle_status = #{onlineSettleStatus}
        </if>
        <if test="msisdn != null and msisdn != ''">
            and cms.msisdn LIKE concat ('%',#{msisdn},'%')
        </if>
        <if test="custName !=null and custName != '' ">
            and oi.cust_name = #{custName}
        </if>
        <if test="logisticsNum != null and logisticsNum != '' ">
            and exists (select li.logis_code from logistics_info li where li.order_id = oa.order_id and li.logis_code
            like '%${logisticsNum}%')
        </if>
    </select>

    <select id="selectGoodList" resultType="com.chinamobile.iot.sc.response.web.good.AtomOrderInfo">
        select oa.id                  as id,
        oa.order_id            as order_id,
        oa.sku_offering_name   as sku_offering_name,
        oa.sku_offering_code   as sku_offering_code,
        oa.atom_offering_name  as atom_offering_name,
        oa.sku_quantity        as sku_quantity,
        oa.atom_quantity       as atom_quantity,
        oa.atom_price          as atom_price,
        oi.contact_person_name as contact_person_name,
        cp.partner_name,
        oa.create_time         as create_time
        from order_2c_info oi
        left join order_2c_atom_info oa on oa.order_id = oi.order_id
        left join (
        select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
        ocr.atom_order_id,ocr.order_id
        inner JOIN user_partner up on up.user_id = ocr.cooperator_id
        group by ocr.atom_order_id
        ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        where oi.order_id = #{orderId}
    </select>

    <select id="selectOrderExportListByHandle" resultType="com.chinamobile.iot.sc.pojo.handle.OrderExportHandle">
        SELECT
        oa.id as id,
        oa.create_time AS createTime,
        oa.order_id AS orderId,
        si.offering_code AS spuOfferingCode,
        si.offering_name AS spuOfferingName,
        oa.sku_offering_name AS skuOfferingName,
        oa.sku_offering_code AS skuOfferingCode,
        oa.atom_offering_name AS atomOfferingName,
        oa.atom_offering_code AS atomOfferingCode,
        oa.atom_offering_class AS atomOfferingClass,
        oa.atom_quantity * oa.sku_quantity AS quantity,
        oa.atom_price as atomPrice,
        oa.order_status AS orderStatus,
        oi.spu_offering_class AS spuOfferingClass,
        cp.partner_name partnerName,
        cp.user_name cooperatorName,
        cp.cooperator_id cooperatorId,
        cph.user_name finishCooperatorName,
        cph.cooperator_id finishCooperatorId,
        oi.contact_phone AS receiverPhone,
        oa.supplier_name as supplier_name,
        oi.addr1 AS deliveryArea,
        oa.atom_settle_price settlePrice,
        oi.create_oper_code createOperCode,
        oi.employee_num employeeNum,
        oi.cust_code custCode,
        oi.cust_name custName,
        p.province_company province,
        oi.location,
        oi.region_ID regionID,
        oi.contact_person_name contactPersonName,
        oi.addr1,
        oi.addr2,
        oi.addr3,
        oi.addr4,
        oi.usaddr,
        ( SELECT GROUP_CONCAT( logis_code ) FROM logistics_info WHERE order_id = oa.order_id AND order_atom_info_id =
        oa.id AND logistics_type = 0 ) logisCode,
        ( SELECT GROUP_CONCAT( sn ) FROM order_2c_atom_sn WHERE atom_order_id = oa.id ) sn,
        oi.org_name orgName,
        oi.deduct_price orderDeductPrice,
        oa.sku_quantity skuQuantity,
        sku.price,
        oi.total_price orderTotalPrice,
        oi.business_code businessCode,
        oi.cust_mg_name custMgName,
        oi.cust_mg_phone custMgPhone,
        oi.remarks,
        oi.order_type orderType,
        case
        when status in (3,4) then oi.order_status_time
        else null
        end finishTime,
        (SELECT GROUP_CONCAT(CONCAT('(',coupon_code,',',coupon_amount),')') FROM coupon_info WHERE order_id =
        oa.order_id GROUP BY order_id) couponInfo
        FROM
        order_2c_atom_info oa
        LEFT JOIN order_2c_info oi ON oa.order_id = oi.order_id
        LEFT JOIN spu_offering_info_history si ON si.offering_code = oa.spu_offering_code
        AND si.spu_offering_version = oa.spu_offering_version AND si.delete_time is null
        LEFT JOIN ( SELECT order_id, inner_status FROM ( SELECT order_id, inner_status FROM order_2c_roc_info ORDER BY
        create_time ) temp GROUP BY order_id ) ori ON ori.order_id = oa.order_id
        LEFT JOIN sku_offering_info_history sku ON sku.offering_code = oa.sku_offering_code
        AND sku.spu_code = oa.spu_offering_code
        AND sku.sku_offering_version = oa.sku_offering_version AND sku.spu_offering_version = oa.spu_offering_version
        AND sku.delete_time is null
        LEFT JOIN province p ON oi.be_id = p.province_code
        <if test="userIdList == null or userIdList.size() == 0">
            left join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner join order_2c_atom_info o2ai on o2ai.id = ocr.atom_order_id and o2ai.order_id = ocr.order_id
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            where 1=1
            group by o2ai.id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>
        <if test="userIdList != null and userIdList.size() != 0">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner join order_2c_atom_info o2ai on o2ai.id = ocr.atom_order_id and o2ai.order_id = ocr.order_id
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            where 1=1
            and ocr.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
            group by o2ai.id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>
        left join (
        select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocrh.cooperator_id) cooperator_id,
        ocrh.atom_order_id,ocrh.order_id
        from order_cooperator_relation_history ocrh
        inner JOIN user_partner up on up.user_id = ocrh.cooperator_id
        group by ocrh.atom_order_id
        ) cph on cph.atom_order_id = oa.id and cph.order_id = oa.order_id
        <where>
            oa.create_time <![CDATA[ >= ]]> #{startTime} and oa.create_time <![CDATA[ <= ]]> #{endTime}
            and oa.order_status not in (13,14)
            <if test="roleType == 'partner' or roleType == 'partnerLord'">
                and (oa.atom_offering_class != 'S' or oa.atom_offering_class is null)
            </if>
            <!--<if test="userIdList != null and userIdList.size() != 0">
                and oa.cooperator_id in
                <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>-->
            <if test="orderId != null">
                and oa.order_id like '%${orderId}%'
            </if>
            <if test="orderStatus!=null">
                and oa.order_status = #{orderStatus}
            </if>
            <if test="spuOfferingClass!=null">
                and oi.spu_offering_class = #{spuOfferingClass}
            </if>
            <if test="skuOfferingName!=null">
                and oa.sku_offering_name like '%${skuOfferingName}%'
            </if>
            <if test="skuOfferingCode!=null">
                and oa.sku_offering_code like '%${skuOfferingCode}%'
            </if>
            <if test="atomOfferingName!=null">
                and oa.atom_offering_name like '%${atomOfferingName}%'
            </if>
            <if test="rocStatus!=null">
                and ori.inner_status = #{rocStatus}
            </if>
            <if test="receiverPhone!=null">
                and user.phone like '%${receiverPhone}%'
            </if>
            <if test="finishStartTime != null">
                and oi.status in (3,4) and oi.order_status_time <![CDATA[ >= ]]> #{finishStartTime}
            </if>
            <if test="finishEndTime != null ">
                and oi.status in (3,4) and oi.order_status_time <![CDATA[ <= ]]> #{finishEndTime}
            </if>
            <if test="businessCode!=null">
                and oi.business_code = #{businessCode}
            </if>
            <if test="specialAfterMarketHandle !=null">
                and oi.special_after_market_handle =#{specialAfterMarketHandle}
            </if>
            <if test="specialAfterStatus != null and specialAfterStatus != ''">
                and oi.special_after_status =#{specialAfterStatus}
            </if>
            <if test="orderType != null and orderType != ''">
                <if test="orderType == '00' or orderType == '02' or orderType == '03'">
                    and oi.order_type in ('00','02','03')
                </if>
                <if test="orderType != '00' and orderType != '02' and orderType != '03'">
                    and oi.order_type = #{orderType}
                </if>
            </if>
        </where>
        ORDER BY oa.create_time DESC
    </select>

    <select id="selectOrderListByHandleWithoutUser"
            parameterType="com.chinamobile.iot.sc.pojo.param.OrderListQueryParam"
            resultMap="orderInfoHandleMap">
        select
        <!--oa.id as id,-->
        oa.create_time as create_time,
        oa.order_id as order_id,
        si.offering_code as spu_offering_code,
        si.offering_name as spu_offering_name,
        oa.sku_offering_name as sku_offering_name,
        oa.sku_offering_code as sku_offering_code,
        <!--oa.atom_offering_name as atom_offering_name,
        oa.atom_offering_class as atom_offering_class,
        oa.atom_offering_code as atom_offering_code,-->
        oa.supplier_name as supplier_name,
        <!--oa.model as model,
        oa.color as color,
        oa.atom_quantity*oa.sku_quantity as quantity,
        oa.atom_price as atom_price,-->
        oa.sku_quantity as skuQuantity,
        oa.sku_price as skuPrice,
        oa.order_status as order_status,
        <!--cp.cooperator_id as cooperator_id,
        cph.cooperator_id as finish_cooperator_id,-->
        oi.spu_offering_class as spu_offering_class,
        oi.sync_k3_id,
        oi.special_after_market_handle,
        oi.special_after_status,
        oi.special_after_latest_time,
        oi.addr1 addr1,
        oi.order_type orderType,
        oi.qly_status qlyStatus,
        si.url,
        si.img_url imgUrl,
        oi.ysx_status ysxStatus,
        oa.car_open_status carOpenStatus,
        <!--aoi.ext_soft_offering_code extSoftOfferingCode,-->
        oi.kx_refund_status kxRefundStatus,
        oa.allow_order_status allowOrderStatus,
        oi.total_price totalPrice,
        oi.special_after_refunds_number specialAfterRefundsNumber,
        <!--oa.soft_service_status softServiceStatus,-->
        skuh.product_type productType,
        skuh.card_type cardType,
        oi.update_time updateTime,
        aoi.card_containing_terminal,
        oa.settle_status settleStatus,
        ospoo.scm_order_num scmOrderNum,
        ospoo.settle_status onlineSettleStatus,
        oi.reminder_wait_send reminderWaitSend,
        oi.reminder_valet_taking reminderValetTaking,
        oi.reminder_wait_deliver reminderWaitDeliver,
        skuh.receive_order receiveOrder
        from order_2c_atom_info oa
        inner join order_2c_info oi on oa.order_id=oi.order_id
        left join spu_offering_info_history si on si.offering_code = oa.spu_offering_code and si.spu_offering_version =
        oa.spu_offering_version
        and si.delete_time is null
        left join atom_offering_info_history aoi on aoi.spu_code = oa.spu_offering_code
        and aoi.sku_code = oa.sku_offering_code and aoi.offering_code = oa.atom_offering_code
        and aoi.spu_offering_version = oa.spu_offering_version and aoi.sku_offering_version = oa.sku_offering_version
        and aoi.atom_offering_version = oa.atom_offering_version
        and aoi.delete_time is null
        left join sku_offering_info_history skuh on skuh.spu_code = si.offering_code and skuh.offering_code =
        oa.sku_offering_code
        and skuh.spu_offering_version = si.spu_offering_version and skuh.sku_offering_version = oa.sku_offering_version
        and skuh.delete_time is null
        left join (SELECT ospo.scm_order_num ,osoo.order_id,
            ospo.settle_status FROM online_settlement_os_order osoo
            INNER join online_settlement_purchase_order ospo on ospo.id = osoo.online_settlement_purchase_order_id) ospoo
                on ospoo.order_id = oa.order_id
        <!--<if test="(userIdList == null or userIdList.size() == 0) and (partnerName == null or partnerName == '')">
            left join (
            select group_concat(distinct ocr.cooperator_id) cooperator_id,ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>-->
        <!--<if test="(userIdList != null and userIdList.size() != 0) or (partnerName != null and partnerName != '')">
            inner join (
            select group_concat(distinct ocr.cooperator_id) cooperator_id,ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            where 1=1
            <if test="userIdList != null and userIdList.size() != 0">
                and ocr.cooperator_id in
                <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="partnerName != null and partnerName != ''">
                and up.partner_name LIKE concat ('%',#{partnerName},'%')
            </if>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>-->

        <if test="(userIdList != null and userIdList.size() != 0) or (partnerName != null and partnerName != '') or (cooperatorName != null and cooperatorName != '')">
            inner join  order_cooperator_relation ocr on ocr.atom_order_id = oa.id and ocr.order_id = oa.order_id
            inner join user_partner up on up.user_id = ocr.cooperator_id
        </if>
        <!--left join (
        select group_concat(distinct ocrh.cooperator_id) cooperator_id, ocrh.atom_order_id,ocrh.order_id
        from order_cooperator_relation_history ocrh
        group by ocrh.atom_order_id
        ) cph on cph.atom_order_id = oa.id and cph.order_id = oa.order_id-->
        <if test="h5Status!=null">
            left join service_open_info soi on oa.id = soi.atom_order_id and oi.spu_offering_class = 'A13'
        </if>

        <if test="msisdn != null and msisdn != ''">
            inner join card_mall_sync cms on cms.order_id = oa.order_id and cms.atom_order_id = oa.id
        </if>
        where oa.order_status not in (13,14)
        and case when oi.spu_offering_class != 'A13' then oa.atom_offering_class not in ('S','C')
                 when oi.spu_offering_class = 'A13' then (oa.soft_service_status is not null and oa.soft_service_status != '')
        else 1=1
        end
        and case
        when oa.atom_offering_class = 'X' then (aoi.card_containing_terminal is null or aoi.card_containing_terminal = 1)
        else 1=1
        end
        <if test="roleType == 'partner' or roleType == 'partnerLord'">
            and ( ( oi.spu_offering_class != 'A13' and (oa.atom_offering_class != 'S' or oa.atom_offering_class is null)
            ) or (oi.spu_offering_class = 'A13') )
        </if>
        <if test="orderId!=null and orderId!='' ">
            and oa.order_id like concat('%',#{orderId},'%')
        </if>
        <!--<if test="userIdList != null and userIdList.size() != 0">
            and oa.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>-->
        <if test="userIdList != null and userIdList.size() != 0">
            and ocr.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="partnerName != null and partnerName != ''">
            and up.partner_name LIKE concat ('%',#{partnerName},'%')
        </if>
        <if test="orderStatus!=null and orderStatus.size()!=0">
            and oa.order_status in
            <foreach collection="orderStatus" item="status" index="index" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="startTime != null and startTime != ''">
            and oa.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and oa.create_time <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="spuOfferingClass!=null and spuOfferingClass.size()!=0">
            and oi.spu_offering_class in
            <foreach collection="spuOfferingClass" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="spuOfferingName != null and spuOfferingName!= ''">
            and si.offering_name like concat ('%',#{spuOfferingName},'%')
        </if>
        <if test="spuOfferingCode != null and spuOfferingCode != ''">
            and si.offering_code like concat ('%',#{spuOfferingCode},'%')
        </if>
        <if test="skuOfferingName != null and skuOfferingName != ''">
            and oa.sku_offering_name like concat ('%',#{skuOfferingName},'%')
        </if>
        <if test="skuOfferingCode != null and skuOfferingCode != ''">
            and oa.sku_offering_code like concat ('%',#{skuOfferingCode},'%')
        </if>
        <if test="atomOfferingCode != null and atomOfferingCode != ''">
            and oa.atom_offering_code like concat ('%',#{atomOfferingCode},'%')
        </if>
        <if test="atomOfferingName != null and atomOfferingName != ''">
            and oa.atom_offering_name like concat ('%',#{atomOfferingName},'%')
        </if>
        <if test="specialAfterMarketHandle !=null">
            and oi.special_after_market_handle =#{specialAfterMarketHandle}
        </if>
        <if test="specialAfterStatus != null and specialAfterStatus != '' ">
            and oi.special_after_status =#{specialAfterStatus}
        </if>
        <if test="orderType != null and orderType != ''">
            <if test="orderType == '00' or orderType == '02' or orderType == '03'">
                and oi.order_type in ('00','02','03')
            </if>
            <if test="orderType != '00' and orderType != '02' and orderType != '03'">
                and oi.order_type = #{orderType}
            </if>
        </if>
        <if test="qlyStatus != null">
            and oi.qly_status = #{qlyStatus}
        </if>
        <if test="h5Key != null and h5Key != '' ">
            and (oa.order_id like '%${h5Key}%' || si.offering_name like '%${h5Key}%')
        </if>
        <if test="clusterCode != null and clusterCode != '' ">
            and ( EXISTS (SELECT * FROM order_2c_atom_sn WHERE atom_order_id = oa.id and sn like '%${clusterCode}%')
            or EXISTS (SELECT * FROM card_relation WHERE order_id=oa.order_id and delete_time is null and imei like
            '%${clusterCode}%'))
        </if>
        <if test="h5Status != null  ">
            <if test="h5Status == 1">
                and (
                (oa.order_status = 0 and oi.spu_offering_class in ('A06','A07')) or
                (oa.order_status in (0,10,16) and oi.spu_offering_class = 'A11' and skuh.product_type in
                ('4','5','6','7','8','9','10','11','12')) or
                ((oa.soft_service_status = 1 or soi.sync_iot_fail_status in (1,2) ) and oi.spu_offering_class = 'A13'
                and oa.order_status not in (7,8,9))
                )
            </if>
            <if test="h5Status == 2">
                and (
                (oi.spu_offering_class in ('A06','A07') and oa.order_status != 0 ) or
                ( oi.spu_offering_class = 'A11' and (skuh.product_type is null or skuh.product_type not in
                ('4','5','6','7','8','9','10','11','12') or oa.order_status not in (0,10,16)) ) or
                (oi.spu_offering_class = 'A13' and
                (
                ((oa.soft_service_status != 1 or oa.soft_service_status is null) and (soi.sync_iot_fail_status is null
                or soi.sync_iot_fail_status not in (1,2)) ) or
                (oa.order_status in (7,8,9))
                )
                )
                )
            </if>
        </if>
        <if test="h5SpuOfferingClasses != null and h5SpuOfferingClasses.size() != 0">
            and oi.spu_offering_class in
            <foreach collection="h5SpuOfferingClasses" item="spuClass" index="index" open="(" close=")" separator=",">
                #{spuClass}
            </foreach>
        </if>
        <if test="beId != null and beId != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code =
            oa.sku_offering_code and (srt.province_code = #{beId} or srt.province_code = '000'))
        </if>
        <if test="location != null and location != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code =
            oa.sku_offering_code and srt.city_code =#{location})
        </if>
        <if test="softServiceStatus!=null">
            and oa.soft_service_status = #{softServiceStatus}
        </if>
        <if test="settleStatus!=null">
            and (
            (oa.settle_status is null or oa.settle_status = '')
            and oi.spu_offering_class in ('A04','A08','A09','A13','A14','A15','A16','A17')
            and oa.atom_offering_class not in ('C','A')
            )
        </if>

        <if test="onlineSettleStatus!=null">
            and ospoo.settle_status = #{onlineSettleStatus}
        </if>
        <if test="msisdn != null and msisdn != ''">
            and cms.msisdn LIKE concat ('%',#{msisdn},'%')
        </if>
        <if test="custName !=null and custName != '' ">
            and oi.cust_name = #{custName}
        </if>
        <if test="logisticsNum != null and logisticsNum != '' ">
            and exists (select li.logis_code from logistics_info li where li.order_id = oa.order_id and li.logis_code
            like '%${logisticsNum}%')

        </if>
       <!-- group by oa.id-->
        group by oi.order_id
        ORDER BY oi.update_time DESC
        limit ${num} OFFSET ${(page - 1) * num}
    </select>


    <select id="countOrderListWithoutUser"
            parameterType="com.chinamobile.iot.sc.pojo.param.OrderListQueryParam"
            resultType="java.lang.Long">
        select
        count(distinct oa.order_id)
        <!--count(distinct oa.id)-->
        from order_2c_atom_info oa
        inner join order_2c_info oi on oa.order_id=oi.order_id
        left join atom_offering_info_history aoi on aoi.spu_code = oa.spu_offering_code
        and aoi.sku_code = oa.sku_offering_code and aoi.offering_code = oa.atom_offering_code
        and aoi.spu_offering_version = oa.spu_offering_version and aoi.sku_offering_version = oa.sku_offering_version
        and aoi.atom_offering_version = oa.atom_offering_version
        and aoi.delete_time is null
        left join (SELECT ospo.scm_order_num ,osoo.order_id,
        ospo.settle_status FROM online_settlement_os_order osoo
        INNER join online_settlement_purchase_order ospo on ospo.id = osoo.online_settlement_purchase_order_id) ospoo
        on ospoo.order_id = oa.order_id
        <if test="(spuOfferingName != null and spuOfferingName!= '') or (spuOfferingCode != null and spuOfferingCode != '') or (h5Key != null and h5Key != '') ">
            left join spu_offering_info_history si on si.offering_code = oa.spu_offering_code and
            si.spu_offering_version = oa.spu_offering_version
            and si.delete_time is null
        </if>
        <if test="h5Status != null">
            left join sku_offering_info_history skuh on skuh.spu_code = oa.spu_offering_code and skuh.offering_code =
            oa.sku_offering_code
            and skuh.spu_offering_version = oa.spu_offering_version and skuh.sku_offering_version =
            oa.sku_offering_version and skuh.delete_time is null
            left join service_open_info soi on oa.id = soi.atom_order_id and oi.spu_offering_class = 'A13'
        </if>
        <!--<if test="(userIdList == null or userIdList.size() == 0) and (partnerName == null or partnerName == '')">
            left join (
            select group_concat(distinct ocr.cooperator_id) cooperator_id,ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>-->
        <!--<if test="(userIdList != null and userIdList.size() != 0) or (partnerName != null and partnerName != '')">
            inner join (
            select group_concat(distinct ocr.cooperator_id) cooperator_id,ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            where 1=1
            <if test="userIdList != null and userIdList.size() != 0">
                and ocr.cooperator_id in
                <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                    #{userId}
                </foreach>
            </if>
            <if test="partnerName != null and partnerName != ''">
                and up.partner_name LIKE concat ('%',#{partnerName},'%')
            </if>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>-->
        <if test="(userIdList != null and userIdList.size() != 0) or (partnerName != null and partnerName != '') or (cooperatorName != null and cooperatorName != '')">
            inner join  order_cooperator_relation ocr on ocr.atom_order_id = oa.id and ocr.order_id = oa.order_id
            inner join user_partner up on up.user_id = ocr.cooperator_id
        </if>

        <if test="msisdn != null and msisdn != ''">
            inner join card_mall_sync cms on cms.order_id = oa.order_id and cms.atom_order_id = oa.id
        </if>
        where oa.order_status not in (13,14)
        and case when oi.spu_offering_class != 'A13' then oa.atom_offering_class not in ('S','C')
                 when oi.spu_offering_class = 'A13' then (oa.soft_service_status is not null and oa.soft_service_status != '')
        else 1=1
        end
        and case
        when oa.atom_offering_class = 'X' then (aoi.card_containing_terminal is null or aoi.card_containing_terminal = 1)
        else 1=1
        end
        <if test="roleType == 'partner' or roleType == 'partnerLord'">
            and ( ( oi.spu_offering_class != 'A13' and (oa.atom_offering_class != 'S' or oa.atom_offering_class is null)
            ) or (oi.spu_offering_class = 'A13') )
        </if>
        <if test="orderId!=null and orderId!='' ">
            and oa.order_id = #{orderId}
        </if>
        <!--<if test="userIdList != null and userIdList.size() != 0">
            and oa.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>-->
        <if test="userIdList != null and userIdList.size() != 0">
            and ocr.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="partnerName != null and partnerName != ''">
            and up.partner_name LIKE concat ('%',#{partnerName},'%')
        </if>
        <if test="orderStatus!=null and orderStatus.size()!=0">
            and oa.order_status in
            <foreach collection="orderStatus" item="status" index="index" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="startTime != null and startTime != ''">
            and oa.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and oa.create_time <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="spuOfferingClass!=null and spuOfferingClass.size()!=0">
            and oi.spu_offering_class in
            <foreach collection="spuOfferingClass" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="spuOfferingName != null and spuOfferingName!= ''">
            and si.offering_name like concat ('%',#{spuOfferingName},'%')
        </if>
        <if test="spuOfferingCode != null and spuOfferingCode != ''">
            and si.offering_code like concat ('%',#{spuOfferingCode},'%')
        </if>
        <if test="skuOfferingName != null and skuOfferingName != ''">
            and oa.sku_offering_name like concat ('%',#{skuOfferingName},'%')
        </if>
        <if test="skuOfferingCode != null and skuOfferingCode != ''">
            and oa.sku_offering_code like concat ('%',#{skuOfferingCode},'%')
        </if>
        <if test="atomOfferingCode != null and atomOfferingCode != ''">
            and oa.atom_offering_code like concat ('%',#{atomOfferingCode},'%')
        </if>
        <if test="atomOfferingName != null and atomOfferingName != ''">
            and oa.atom_offering_name like concat ('%',#{atomOfferingName},'%')
        </if>
        <if test="specialAfterMarketHandle !=null">
            and oi.special_after_market_handle =#{specialAfterMarketHandle}
        </if>
        <if test="specialAfterStatus != null and specialAfterStatus != '' ">
            and oi.special_after_status =#{specialAfterStatus}
        </if>
        <if test="orderType != null and orderType != ''">
            <if test="orderType == '00' or orderType == '02' or orderType == '03'">
                and oi.order_type in ('00','02','03')
            </if>
            <if test="orderType != '00' and orderType != '02' and orderType != '03'">
                and oi.order_type = #{orderType}
            </if>
        </if>
        <if test="qlyStatus != null">
            and oi.qly_status = #{qlyStatus}
        </if>
        <if test="h5Key != null and h5Key != '' ">
            and (oa.order_id like '%${h5Key}%' || si.offering_name like '%${h5Key}%')
        </if>
        <if test="clusterCode != null and clusterCode != '' ">
            and ( EXISTS (SELECT * FROM order_2c_atom_sn WHERE atom_order_id = oa.id and sn like '%${clusterCode}%')
            or EXISTS (SELECT * FROM card_relation WHERE order_id=oa.order_id and delete_time is null and imei like
            '%${clusterCode}%'))
        </if>
        <if test="h5Status != null  ">
            <if test="h5Status == 1">
                and (
                (oa.order_status = 0 and oi.spu_offering_class in ('A06','A07')) or
                (oa.order_status in (0,10,16) and oi.spu_offering_class = 'A11' and skuh.product_type in
                ('4','5','6','7','8','9','10','11','12')) or
                ((oa.soft_service_status = 1 or soi.sync_iot_fail_status in (1,2) ) and oi.spu_offering_class = 'A13'
                and oa.order_status not in (7,8,9))
                )
            </if>
            <if test="h5Status == 2">
                and (
                (oi.spu_offering_class in ('A06','A07') and oa.order_status != 0 ) or
                ( oi.spu_offering_class = 'A11' and (skuh.product_type is null or skuh.product_type not in
                ('4','5','6','7','8','9','10','11','12') or oa.order_status not in (0,10,16)) ) or
                (oi.spu_offering_class = 'A13' and
                (
                ((oa.soft_service_status != 1 or oa.soft_service_status is null) and (soi.sync_iot_fail_status is null
                or soi.sync_iot_fail_status not in (1,2)) ) or
                (oa.order_status in (7,8,9))
                )
                )
                )
            </if>
        </if>
        <if test="h5SpuOfferingClasses != null and h5SpuOfferingClasses.size() != 0">
            and oi.spu_offering_class in
            <foreach collection="h5SpuOfferingClasses" item="spuClass" index="index" open="(" close=")" separator=",">
                #{spuClass}
            </foreach>
        </if>
        <if test="beId != null and beId != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code =
            oa.sku_offering_code and (srt.province_code = #{beId} or srt.province_code = '000'))
        </if>
        <if test="location != null and location != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code =
            oa.sku_offering_code and srt.city_code =#{location})
        </if>
        <if test="softServiceStatus!= null">
            and oa.soft_service_status = #{softServiceStatus}
        </if>

        <if test="settleStatus!=null">
            and (
            (oa.settle_status is null or oa.settle_status = '')
            and oi.spu_offering_class in ('A04','A08','A09','A13','A14','A15','A16','A17')
            and oa.atom_offering_class not in ('C','A')
            )
        </if>
        <if test="onlineSettleStatus!=null">
            and ospoo.settle_status = #{onlineSettleStatus}
        </if>
        <if test="msisdn != null and msisdn != ''">
            and cms.msisdn LIKE concat ('%',#{msisdn},'%')
        </if>
        <if test="custName !=null and custName != '' ">
            and oi.cust_name = #{custName}
        </if>
        <if test="logisticsNum != null and logisticsNum != '' ">
            and exists (select li.logis_code from logistics_info li where li.order_id = oa.order_id and li.logis_code
            like '%${logisticsNum}%')
        </if>
    </select>

    <select id="selectOrderStoreSalesReport" resultType="com.chinamobile.iot.sc.pojo.vo.OrderSalesReportVO">
        SELECT
        oi.order_id AS orderId,
        oi.business_code businessCode,
        oi.addr1 clientProvince,
        oi.addr2 clientCity,
        oi.addr3 clientDistricts,
        oi.addr4 clientVillagesTowns,
        oi.usaddr,
        oi.employee_num employeeNum,
        oi.cust_mg_name custMgName,
        oi.cust_mg_phone custMgPhone,
        oi.total_price totalPrice,
        oi.create_time AS createTime,
        oi.contact_person_name contactPersonName,
        oi.contact_phone contactPhone,
        si.offering_name AS spuOfferingName,
        oa.sku_offering_name AS skuOfferingName,
        oi.status orderStatus,
        odi.distributor_phone distributorPhone,
        odi.distributor_level distributorLevel
        FROM
        order_2c_info oi
        LEFT JOIN order_2c_atom_info oa on oa.order_id = oi.order_id
        LEFT JOIN order_2c_distributor_info odi ON odi.order_id = oi.order_id
        LEFT JOIN spu_offering_info si ON si.offering_code = oi.spu_offering_code
        WHERE
        1 = 1
        AND oa.atom_offering_class != 'S'
        <if test="startTime != null and startTime != ''">
            and oi.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and oi.create_time <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="beId != null and beId != ''">
            and oi.be_id =#{beId}
        </if>
        order by oi.create_time desc
    </select>
    <select id="selectUnionSellOrderExport" resultType="com.chinamobile.iot.sc.pojo.mapper.UnionSellOrderExportDO">
        SELECT
        oa.id  id,
        oa.create_time  createTime,
        oa.order_id  orderId,
        spu.offering_code  spuOfferingCode,
        spu.offering_name  spuOfferingName,
        oa.sku_offering_name  skuOfferingName,
        oa.sku_offering_code  skuOfferingCode,
        oa.atom_offering_name  atomOfferingName,
        oa.atom_offering_code  atomOfferingCode,
        oa.atom_offering_class  atomOfferingClass,
        oa.atom_quantity * oa.sku_quantity  quantity,
        oa.atom_price  atomPrice,
        oa.order_status  orderStatus,
        oi.spu_offering_class  spuOfferingClass,
        cp.partner_name partnerName,
        cp.user_name cooperatorName,
        oa.atom_settle_price settlePrice,
        oi.org_name orgName,
        oa.sku_quantity  skuQuantity,
        sku.price,
        oi.total_price orderTotalPrice,
        d.short_name department,
        oi.pay_time payTime,
        case
        when oi.status = 3 then oi.order_status_time
        else null
        end finishTime,
        case
        when oi.status = 4 then oi.refund_time
        else null
        end refundTime
        FROM
        order_2c_atom_info oa
        LEFT JOIN order_2c_info oi ON oa.order_id = oi.order_id
        LEFT JOIN spu_offering_info_history spu ON spu.offering_code = oa.spu_offering_code
        and spu.spu_offering_version = oa.spu_offering_version and spu.delete_time is null
        LEFT JOIN sku_offering_info_history sku ON sku.offering_code = oa.sku_offering_code AND sku.spu_code = oa.spu_offering_code
        and sku.sku_offering_version = oa.sku_offering_version
        and sku.spu_offering_version = oa.spu_offering_version and sku.delete_time is null
        LEFT JOIN atom_offering_info atom ON atom.offering_code = oa.atom_offering_code AND  atom.offering_class = oa.atom_offering_class
        AND  atom.spu_code = oa.spu_offering_code AND  atom.sku_code = oa.sku_offering_code and atom.delete_time is null
        LEFT JOIN atom_std_service ass ON ass.atom_id = atom.id
        LEFT JOIN standard_service ss ON ss.id = ass.std_service_id
        LEFT JOIN department d ON d.id = ss.product_department_id
        left join (
        select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
        ocr.atom_order_id,ocr.order_id
        from order_cooperator_relation ocr
        inner JOIN user_partner up on up.user_id = ocr.cooperator_id
        group by ocr.atom_order_id
        ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        WHERE
        oa.atom_offering_class = "H"
        AND oi.spu_offering_class = "A06"
        AND  ((oi.pay_time <![CDATA[ >= ]]> #{startTime} AND oi.pay_time <![CDATA[ <= ]]> #{endTime})
        or (oi.status = 4 AND oi.refund_time <![CDATA[ >= ]]> #{startTime} AND oi.refund_time <![CDATA[ <= ]]> #{endTime}) )
        ORDER BY oa.create_time DESC
    </select>

    <select id="selectUnionSellOrderList"
            parameterType="com.chinamobile.iot.sc.pojo.param.UnionSellOrderListQueryParam"
            resultType="com.chinamobile.iot.sc.pojo.UnionSellOrderExcelHistory">
        select
        usoeh.id,
        usoeh.date,
        usoeh.amount,
        usoeh.create_time,
        usoeh.update_time
        from
        union_sell_order_excel_history usoeh
        where 1=1
        <if test="startTime != null">
            and usoeh.date <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null">
            and usoeh.date <![CDATA[ <= ]]> #{endTime}
        </if>
        order by usoeh.date desc
    </select>

    <select id="selectUnionSellOrderInfo"
            resultType="com.chinamobile.iot.sc.pojo.UnionSellOrderExcelHistory">
        select
        usoeh.date,
        usoeh.url,
        usoeh.create_time,
        usoeh.update_time
        from
        union_sell_order_excel_history usoeh
        where
        1=1
        AND `date` IN
        <foreach close=")" collection="dateList" item="listItem" open="(" separator=",">
            #{listItem}
        </foreach>
    </select>

    <select id="findNoStatusTimeOrder" resultType="com.chinamobile.iot.sc.pojo.Order2cInfo">
    SELECT
        order_id orderId,
        STATUS status,
        order_status_time orderStatusTime,
        update_time updateTime
    FROM
        order_2c_info
    WHERE
        STATUS IN ( 3, 4 )
        AND order_status_time IS NULL
        AND order_type = '01'
    ORDER BY
        create_time DESC
    LIMIT #{pageSize}
    </select>

    <update id="initValetOrderCompleteTime">
    UPDATE order_2c_info SET valet_order_complete_time = create_time WHERE order_status in (12,7) AND order_type in ('00','02','03') AND valet_order_complete_time is NULL;
    </update>

    <update id="initValetOrderCompleteTimeAtom">
    UPDATE order_2c_atom_info SET valet_order_complete_time = create_time WHERE order_status in (12,7) AND order_type in ('00','02','03') AND valet_order_complete_time is NULL;
    </update>

    <select id="selectOrderListByHandleToBacklog" resultMap="orderInfoHandleMap">
        select
        <!--oa.id as id,-->
        oa.create_time as create_time,
        oa.order_id as order_id,
        si.offering_code as spu_offering_code,
        si.offering_name as spu_offering_name,
        oa.sku_offering_name as sku_offering_name,
        oa.sku_offering_code as sku_offering_code,
        <!--oa.atom_offering_name as atom_offering_name,
        oa.atom_offering_class as atom_offering_class,-->
        oa.supplier_name as supplier_name,
        <!--oa.model as model,
        oa.color as color,
        oa.atom_quantity*oa.sku_quantity as quantity,
        oa.atom_price as atom_price,-->
        oa.sku_quantity as skuQuantity,
        oa.sku_price as skuPrice,
        oa.order_status as order_status,
        oi.spu_offering_class as spu_offering_class,
        <!--cp.cooperator_id,
        cph.cooperator_id finish_cooperator_id,-->
        oi.sync_k3_id,
        oi.special_after_market_handle,
        oi.special_after_status,
        oi.special_after_latest_time,
        oi.addr1 addr1,
        oi.order_type orderType,
        oi.qly_status qlyStatus,
        si.url,
        si.img_url imgUrl,
        oi.ysx_status ysxStatus,
        oa.car_open_status carOpenStatus,
        <!--aoi.ext_soft_offering_code extSoftOfferingCode,-->
        oi.kx_refund_status kxRefundStatus,
        oa.allow_order_status allowOrderStatus,
        oi.total_price totalPrice,
        oi.special_after_refunds_number specialAfterRefundsNumber,
        <!--oa.soft_service_status softServiceStatus,-->
        skuh.product_type productType,
        skuh.card_type cardType,
        oi.update_time updateTime,
        aoi.card_containing_terminal,
        oa.settle_status settleStatus,
        ospoo.scm_order_num scmOrderNum,
        ospoo.settle_status onlineSettleStatus,
        oi.reminder_wait_send reminderWaitSend,
        oi.reminder_valet_taking reminderValetTaking,
        oi.reminder_wait_deliver reminderWaitDeliver,
        skuh.receive_order receiveOrder
        from order_2c_atom_info oa
        inner join order_2c_info oi on oa.order_id=oi.order_id
        left join spu_offering_info_history si on si.offering_code = oa.spu_offering_code and si.spu_offering_version =
        oa.spu_offering_version
        left join atom_offering_info_history aoi on aoi.spu_code = oa.spu_offering_code
        and aoi.sku_code = oa.sku_offering_code and aoi.offering_code = oa.atom_offering_code
        and aoi.spu_offering_version = oa.spu_offering_version and aoi.sku_offering_version = oa.sku_offering_version
        and aoi.atom_offering_version = oa.atom_offering_version and aoi.delete_time is null
        left join sku_offering_info_history skuh on skuh.spu_code = si.offering_code and skuh.offering_code =
        oa.sku_offering_code
        and skuh.spu_offering_version = si.spu_offering_version and skuh.sku_offering_version = oa.sku_offering_version
        and skuh.delete_time is null
        left join (SELECT ospo.scm_order_num ,osoo.order_id,
        ospo.settle_status FROM online_settlement_os_order osoo
        INNER join online_settlement_purchase_order ospo on ospo.id = osoo.online_settlement_purchase_order_id) ospoo
        on ospoo.order_id = oa.order_id
        <if test="param.userIdList != null and param.userIdList.size() != 0">
            inner join  order_cooperator_relation ocr on ocr.atom_order_id = oa.id and ocr.order_id = oa.order_id
            inner join user_partner up on up.user_id = ocr.cooperator_id
        </if>
        where oa.order_status in (0,10,16)
        and case when oi.spu_offering_class != 'A13' then oa.atom_offering_class not in ('S','C')
        when oi.spu_offering_class = 'A13' then (oa.soft_service_status is not null and oa.soft_service_status != '')
        else 1=1
        end
        and case
        when oa.atom_offering_class = 'X' then (aoi.card_containing_terminal is null or aoi.card_containing_terminal = 1)
        else 1=1
        end
        <!--<if test="userIdList != null and userIdList.size() != 0">
            and oa.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>-->

        <if test="param.userIdList != null and param.userIdList.size() != 0">
            and ocr.cooperator_id in
            <foreach collection="param.userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="param.roleType == 'partner' or param.roleType == 'partnerLord'">
            and ( ( oi.spu_offering_class != 'A13' and (oa.atom_offering_class != 'S' or oa.atom_offering_class is null)
            ) or (oi.spu_offering_class = 'A13') )
        </if>
        <if test="param.beId != null and param.beId != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code =
            oa.sku_offering_code and (srt.province_code = #{param.beId} or srt.province_code = '000'))
        </if>
        <if test="param.location != null and param.location != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code =
            oa.sku_offering_code and srt.city_code =#{param.location})
        </if>
        <if test="param.orderId!=null and param.orderId!='' ">
            and oa.order_id = #{param.orderId}
        </if>
        <!--  group by oa.id-->
        group by oi.order_id
        ORDER BY oi.update_time DESC
    </select>


    <select id="selectOrderDetailForOpen" resultMap="orderInfoDetailHandleMap">
        select oa.id as id,
        oa.order_id as order_id,
        oi.contact_person_name as contact_person_name,
        oi.contact_phone as contact_phone,
        oi.addr1 as addr1,
        oi.addr2 as addr2,
        oi.addr3 as addr3,
        oi.addr4 as addr4,
        oi.usaddr as usaddr,
        oi.spu_offering_class as spu_offering_class,
        oi.remarks as remarks,
        oa.sku_offering_name as sku_offering_name,
        oa.atom_offering_name as atom_offering_name,
        oa.atom_offering_code as atom_offering_code,
        oa.atom_offering_class as offering_class,
        oa.supplier_name as supplier_name,
        oa.model as model,
        oa.color as color,
        oa.sku_quantity * oa.atom_quantity as quantity,
        oa.sku_quantity skuQuantity,
        oa.create_time as create_time,
        oa.atom_price as atom_price,
        oa.order_status as order_status,
        oa.cooperator_id primary_cooperator_id,
       <!-- cp.partner_name,
        cp.user_name cooperator_name,-->
        oa.sku_offering_code as sku_offering_code,
        si.offering_code as spu_offering_code,
        si.offering_name as spu_offering_name,
        oi.order_type as orderType,
        oi.qly_status as qlyStatus,
        oi.ysx_status as ysxStatus,
        oi.order_status_time as orderStatusTime,
        oi.pay_time as payTime,
        oa.atom_offering_version as atomOfferingVersion,
        oa.spu_offering_version as spuOfferingVersion,
        oa.sku_offering_version as skuOfferingVersion,
        oi.special_after_refunds_number as specialAfterRefundsNumber,
        oa.soft_service_status as softServiceStatus,
        case
        when oi.special_after_status is null or oi.special_after_status = '' then ''
        when oi.special_after_status = '1' then '待退款'
        when oi.special_after_status = '2' then '退款中'
        when oi.special_after_status = '3' then '退款成功'
        when oi.special_after_status = '4' then '退款取消'
        else '退款状态错误'
        end special_after_status_str,
        si.url,
        si.img_url imgUrl,
        ai.ext_soft_offering_code extSoftOfferingCode,
        oa.car_open_status carOpenStatus,
        oi.org_name as orgName,
        oi.province_org_name as provinceOrgName,
        oi.org_level as orgLevel,
        case
        when oi.status in (3,4) then oi.order_status_time
        else null
        end finishTime,
        IF(oi.pay_time is null, oi.create_time,DATE_FORMAT(oi.pay_time,'%Y%m%d%H%i%s')) receiveOrderTime,
        sendGoodsTime,
        oi.total_price orderTotalPrice,
        oi.deduct_price orderDeductPrice,
        oa.sku_quantity skuQuantity,
        oi.be_id as province,
        oi.location as location,
        oi.region_id as regionID,
        pr.province_company as provinceCompany,
        ct.city_name as cityName,
        oi.create_oper_code as managerCode,
        oi.ordering_channel_source as orderingChannelSource,
        oi.ordering_channel_name as orderingChannelName,
        oa.sku_price skuPrice,
        oi.customer_type customerType
        from order_2c_atom_info oa
        left join order_2c_info oi on oa.order_id = oi.order_id
        left join province pr on oi.be_id = pr.province_code
        left join city ct on oi.location = ct.city_code
        left join spu_offering_info_history si on si.offering_code = oa.spu_offering_code
        and si.spu_offering_version = oa.spu_offering_version and si.delete_time is null
        left join atom_offering_info_history ai on ai.spu_code = oa.spu_offering_code and ai.sku_code =
        oa.sku_offering_code
        and ai.offering_code = oa.atom_offering_code
        and ai.spu_offering_version = oa.spu_offering_version and ai.sku_offering_version = oa.sku_offering_version
        and ai.atom_offering_version = oa.atom_offering_version and ai.delete_time is null
        LEFT JOIN (select atom_order_id ,order_id, date_format(max(create_time),'%Y-%m-%d %H:%i:%s') sendGoodsTime
        from order_2c_atom_history
        where operate_type = 1 and inner_status = 1
        group by atom_order_id ,order_id) o2ah on o2ah.atom_order_id = oa.id and o2ah.order_id = oa.order_id
        <!--<if test="userIdList == null or userIdList.size() == 0">
            left join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            where 1=1
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>-->
        <if test="userIdList != null and userIdList.size() != 0">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            where 1=1
            and ocr.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>
        where oa.order_status not in (13,14)
        <if test="roleType == 'partner' or roleType == 'partnerLord'">
            and (oa.atom_offering_class != 'S' or oa.atom_offering_class is null)
        </if>
        <if test="orderId!=null and orderId!='' ">
            and oa.order_id = #{orderId}
        </if>
        <!--<if test="userIdList != null and userIdList.size() != 0">
            and oa.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>-->
        <if test="orderStatus!=null and orderStatus.size()!=0">
            and oa.order_status in
            <foreach collection="orderStatus" item="status" index="index" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="startTime != null and startTime != ''">
            and oa.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and oa.create_time <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="spuOfferingClass != null and spuOfferingClass != ''">
            and oi.spu_offering_class like concat ('%',#{spuOfferingClass},'%')
        </if>
        <if test="spuOfferingName != null and spuOfferingName!= ''">
            and si.offering_name like concat ('%',#{spuOfferingName},'%')
        </if>
        <if test="spuOfferingCode != null and spuOfferingCode != ''">
            and si.offering_code like concat ('%',#{spuOfferingCode},'%')
        </if>
        <if test="skuOfferingName != null and skuOfferingName != ''">
            and oa.sku_offering_name like concat ('%',#{skuOfferingName},'%')
        </if>
        <if test="skuOfferingCode != null and skuOfferingCode != ''">
            and oa.sku_offering_code like concat ('%',#{skuOfferingCode},'%')
        </if>
        <if test="atomOfferingCode != null and atomOfferingCode != ''">
            and oa.atom_offering_code like concat ('%',#{atomOfferingCode},'%')
        </if>
        <if test="atomOfferingName != null and atomOfferingName != ''">
            and oa.atom_offering_name like concat ('%',#{atomOfferingName},'%')
        </if>
        <if test="specialAfterMarketHandle !=null">
            and oi.special_after_market_handle =#{specialAfterMarketHandle}
        </if>
        <if test="specialAfterStatus != null and specialAfterStatus != '' ">
            and oi.special_after_status =#{specialAfterStatus}
        </if>
        <if test="orderType != null and orderType != ''">
            <if test="orderType == '00' or orderType == '02' or orderType == '03'">
                and oi.order_type in ('00','02','03')
            </if>
            <if test="orderType != '00' and orderType != '02' and orderType != '03'">
                and oi.order_type = #{orderType}
            </if>
        </if>
        <if test="qlyStatus != null">
            and oi.qly_status = #{qlyStatus}
        </if>
        <if test="h5Key != null and h5Key != '' ">
            and (oa.order_id like '%${h5Key}%' || si.offering_name like '%${h5Key}%')
        </if>
        <if test="clusterCode != null and clusterCode != '' ">
            and (
            oa.id in (select atom_order_id from order_2c_atom_sn where sn like '%${clusterCode}%')
            or
            oa.order_id in (select order_id from card_relation where delete_time is null and imei like
            '%${clusterCode}%')
            )
        </if>
        <if test="h5Status != null  ">
            <if test="h5Status == 1">
                and oa.order_status = 0
            </if>
            <if test="h5Status == 2">
                and oa.order_status != 0
            </if>
        </if>
        <if test="h5SpuOfferingClasses != null and h5SpuOfferingClasses.size() != 0">
            and oi.spu_offering_class in
            <foreach collection="h5SpuOfferingClasses" item="spuClass" index="index" open="(" close=")" separator=",">
                #{spuClass}
            </foreach>
        </if>
        <if test="softServiceStatus!=null">
            and oa.soft_service_status = #{softServiceStatus}
        </if>
        <if test="orderingChannelSource!=null">
            and oi.ordering_channel_source = #{orderingChannelSource}
        </if>
        <if test="encryptCustCode !=null and encryptCustCode != ''">
            and oi.cust_code = #{encryptCustCode}
        </if>
        ORDER BY oa.update_time DESC
        <if test="page != null and num != null">
            limit #{page},#{num}
        </if>

    </select>

    <select id="countOrderForOpen" resultType="java.lang.Long">
        select count(*) from (select oa.id
        from order_2c_atom_info oa
        left join order_2c_info oi on oa.order_id = oi.order_id
        left join spu_offering_info_history si on si.offering_code = oa.spu_offering_code
        and si.spu_offering_version = oa.spu_offering_version and si.delete_time is null
        left join atom_offering_info_history ai on ai.spu_code = oa.spu_offering_code and ai.sku_code =
        oa.sku_offering_code
        and ai.offering_code = oa.atom_offering_code
        and ai.spu_offering_version = oa.spu_offering_version and ai.sku_offering_version = oa.sku_offering_version
        and ai.atom_offering_version = oa.atom_offering_version and ai.delete_time is null
        <if test="userIdList == null or userIdList.size() == 0">
            left join (
            select group_concat(distinct ocr.cooperator_id) cooperator_id,ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            where 1=1
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>
        <if test="userIdList != null and userIdList.size() != 0">
            inner join (
            select group_concat(distinct ocr.cooperator_id) cooperator_id,ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            where 1=1
            and ocr.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>
        where oa.order_status not in (13,14)
        <if test="roleType == 'partner' or roleType == 'partnerLord'">
            and (oa.atom_offering_class != 'S' or oa.atom_offering_class is null)
        </if>
        <if test="orderId!=null and orderId!='' ">
            and oa.order_id like concat('%',#{orderId},'%')
        </if>
        <!--<if test="userIdList != null and userIdList.size() != 0">
            and oa.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>-->
        <if test="orderStatus!=null and orderStatus.size()!=0">
            and oa.order_status in
            <foreach collection="orderStatus" item="status" index="index" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="startTime != null and startTime != ''">
            and oa.create_time <![CDATA[ >= ]]> #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and oa.create_time <![CDATA[ <= ]]> #{endTime}
        </if>
        <if test="spuOfferingClass != null and spuOfferingClass != ''">
            and oi.spu_offering_class like concat ('%',#{spuOfferingClass},'%')
        </if>
        <if test="spuOfferingName != null and spuOfferingName!= ''">
            and si.offering_name like concat ('%',#{spuOfferingName},'%')
        </if>
        <if test="spuOfferingCode != null and spuOfferingCode != ''">
            and si.offering_code like concat ('%',#{spuOfferingCode},'%')
        </if>
        <if test="skuOfferingName != null and skuOfferingName != ''">
            and oa.sku_offering_name like concat ('%',#{skuOfferingName},'%')
        </if>
        <if test="skuOfferingCode != null and skuOfferingCode != ''">
            and oa.sku_offering_code like concat ('%',#{skuOfferingCode},'%')
        </if>
        <if test="atomOfferingCode != null and atomOfferingCode != ''">
            and oa.atom_offering_code like concat ('%',#{atomOfferingCode},'%')
        </if>
        <if test="atomOfferingName != null and atomOfferingName != ''">
            and oa.atom_offering_name like concat ('%',#{atomOfferingName},'%')
        </if>
        <if test="specialAfterMarketHandle !=null">
            and oi.special_after_market_handle =#{specialAfterMarketHandle}
        </if>
        <if test="specialAfterStatus != null and specialAfterStatus != '' ">
            and oi.special_after_status =#{specialAfterStatus}
        </if>
        <if test="orderType != null and orderType != ''">
            <if test="orderType == '00' or orderType == '02' or orderType == '03'">
                and oi.order_type in ('00','02','03')
            </if>
            <if test="orderType != '00' and orderType != '02' and orderType != '03'">
                and oi.order_type = #{orderType}
            </if>
        </if>
        <if test="qlyStatus != null">
            and oi.qly_status = #{qlyStatus}
        </if>
        <if test="h5Key != null and h5Key != '' ">
            and (oa.order_id like '%${h5Key}%' || si.offering_name like '%${h5Key}%')
        </if>
        <if test="clusterCode != null and clusterCode != '' ">
            and (
            oa.id in (select atom_order_id from order_2c_atom_sn where sn like '%${clusterCode}%')
            or
            oa.order_id in (select order_id from card_relation where delete_time is null and imei like
            '%${clusterCode}%')
            )
        </if>
        <if test="h5Status != null  ">
            <if test="h5Status == 1">
                and oa.order_status = 0
            </if>
            <if test="h5Status == 2">
                and oa.order_status != 0
            </if>
        </if>
        <if test="h5SpuOfferingClasses != null and h5SpuOfferingClasses.size() != 0">
            and oi.spu_offering_class in
            <foreach collection="h5SpuOfferingClasses" item="spuClass" index="index" open="(" close=")" separator=",">
                #{spuClass}
            </foreach>
        </if>
        <if test="softServiceStatus!=null">
            and oa.soft_service_status = #{softServiceStatus}
        </if>
        <if test="orderingChannelSource!=null">
            and oi.ordering_channel_source = #{orderingChannelSource}
        </if>
        <if test="encryptCustCode !=null and encryptCustCode != ''">
            and oi.cust_code = #{encryptCustCode}
        </if>) temp

    </select>

    <select id="selectCardxDeliveryNotesInfo" parameterType="java.lang.String"
            resultType="com.chinamobile.iot.sc.pojo.mapper.DeliveryNotesDO">
    SELECT
        oa.order_id orderId,
        oi.cust_name custName,
        oi.contact_person_name receiverName,
        oi.contact_phone receiverPhone,
        oi.addr1,
        oi.addr2,
        oi.addr3,
        oi.addr4,
        oi.usaddr,
        (SELECT GROUP_CONCAT(logis_code) FROM logistics_info WHERE order_atom_info_id = oa.id and logistics_type = 0) logisticsNumber,
        sku.offering_name skuName,
        GROUP_CONCAT(m.device_version) deviceVersion,
        oa.sku_quantity skuQuantity,
        oi.remarks remark
    FROM order_2c_atom_info oa
    LEFT JOIN order_2c_info oi ON oa.order_id = oi.order_id
    LEFT JOIN logistics_info li ON li.order_atom_info_id = oa.id
    left JOIN sku_offering_info sku ON sku.offering_code = oa.sku_offering_code AND sku.spu_code = oa.spu_offering_code
    LEFT JOIN atom_offering_info atom ON atom.sku_code = oa.sku_offering_code and atom.spu_code = oa.spu_offering_code and atom.offering_class = 'X'
    LEFT JOIN dkcardx_inventory_main_info m ON atom.inventory_main_id = m.id
    WHERE oa.id = #{atomOrderId}
    GROUP BY oa.id
    </select>

    <select id="getOrderIdAndAtomOrderIdList" resultType="com.chinamobile.iot.sc.pojo.mapper.OrderIdAtomOrderIdDO">
        SELECT
        oa.id AS atomOrderId,
        oa.order_id AS orderId
        FROM
        order_2c_atom_info oa
        LEFT JOIN order_2c_info oi ON oa.order_id = oi.order_id
        <if test="userIdList != null and userIdList.size() != 0">
            inner join (
            select group_concat(distinct ocr.cooperator_id) cooperator_id,ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            where 1=1
            and ocr.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>
        WHERE
        oa.order_status NOT IN (13,14)
        and oi.spu_offering_class in ('A06','A07','A11')
        and oa.atom_offering_class not in ('S','C')
        <if test="beId != null and beId != '' ">
            and oi.be_id =#{beId}
        </if>
        <if test="location != null and location != '' ">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code =
            oa.sku_offering_code and srt.city_code =#{location})
        </if>
        <if test="orderStatusList != null and orderStatusList.size() != 0">
            and oa.order_status in
            <foreach collection="orderStatusList" item="orderStatus" index="index" open="(" close=")" separator=",">
                #{orderStatus}
            </foreach>
        </if>
        <!--<if test="userIdList != null and userIdList.size() != 0">
            and oa.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>-->
    </select>

</mapper>
