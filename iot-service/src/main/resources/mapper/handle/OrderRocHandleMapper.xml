<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.handle.OrderRocHandleMapper">
    <resultMap id="orderRocInfoHandleMap" type="com.chinamobile.iot.sc.pojo.handle.OrderRocInfoHandle">
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="refund_order_id" jdbcType="VARCHAR" property="refundOrderId"/>
        <result column="refunds_type" jdbcType="VARCHAR" property="refundsType"/>
        <result column="reason" jdbcType="VARCHAR" property="reason"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="order_status" jdbcType="INTEGER" property="orderStatus"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="atom_order_id" jdbcType="VARCHAR" property="atomOrderId"/>
        <result column="sku_offering_name" jdbcType="VARCHAR" property="skuOfferingName"/>
        <result column="atom_offering_name" jdbcType="VARCHAR" property="atomOfferingName"/>
        <result column="atom_offering_class" jdbcType="VARCHAR" property="atomOfferingClass"/>
        <result column="spu_offering_class" jdbcType="VARCHAR" property="spuOfferingClass"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="color" jdbcType="VARCHAR" property="color"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="atom_price" jdbcType="BIGINT" property="atomPrice"/>
        <result column="skuQuantity" jdbcType="INTEGER" property="skuQuantity"/>
        <result column="skuPrice" jdbcType="VARCHAR" property="skuPrice"/>
        <!--<result column="cooperator_id" jdbcType="VARCHAR" property="cooperatorId"/>
        <result column="cooperator_name" jdbcType="VARCHAR" property="cooperatorName"/>
        <result column="partner_name" jdbcType="VARCHAR" property="partnerName"/>-->
        <result column="pictures_outer_url" jdbcType="VARCHAR" property="picturesOuterUrl"/>
        <result column="spu_offering_code" jdbcType="VARCHAR" property="spuOfferingCode"/>
        <result column="spu_offering_name" jdbcType="VARCHAR" property="spuOfferingName"/>
        <result column="sku_offering_code" jdbcType="VARCHAR" property="skuOfferingCode"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="refunds_number" jdbcType="INTEGER" property="refundsNumber"/>
        <result column="return_price" jdbcType="BIGINT" property="returnPrice"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="url" jdbcType="VARCHAR" property="url" />
        <result column="img_url" jdbcType="VARCHAR" property="imgUrl" />
        <result column="card_containing_terminal" jdbcType="VARCHAR" property="cardContainingTerminal"/>
        <result column="reminderCount" jdbcType="INTEGER" property="reminderCount"/>
        <result column="spuOfferingName" jdbcType="VARCHAR" property="spuOfferingName"/>
    </resultMap>

    <resultMap id="orderRocInfoDetailHandleMap" type="com.chinamobile.iot.sc.pojo.handle.OrderRocInfoDetailHandle">
        <result column="refund_order_id" jdbcType="VARCHAR" property="refundOrderId"/>
        <result column="refunds_type" jdbcType="VARCHAR" property="refundsType"/>
        <result column="reason" jdbcType="VARCHAR" property="reason"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="order_status" jdbcType="INTEGER" property="orderStatus"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="atom_order_id" jdbcType="VARCHAR" property="atomOrderId"/>
        <result column="sku_offering_name" jdbcType="VARCHAR" property="skuOfferingName"/>
        <result column="atom_offering_name" jdbcType="VARCHAR" property="atomOfferingName"/>
        <result column="atom_offering_class" jdbcType="VARCHAR" property="atomOfferingClass"/>
        <result column="spu_offering_class" jdbcType="VARCHAR" property="spuOfferingClass"/>
        <result column="model" jdbcType="VARCHAR" property="model"/>
        <result column="color" jdbcType="VARCHAR" property="color"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="atom_price" jdbcType="BIGINT" property="atomPrice"/>
        <result column="skuQuantity" jdbcType="INTEGER" property="skuQuantity"/>
        <result column="skuPrice" jdbcType="VARCHAR" property="skuPrice"/>
        <result column="cooperator_id" jdbcType="VARCHAR" property="cooperatorId"/>
        <result column="cooperator_name" jdbcType="VARCHAR" property="cooperatorName"/>
        <result column="partner_name" jdbcType="VARCHAR" property="partnerName"/>
        <result column="pictures_outer_url" jdbcType="VARCHAR" property="picturesOuterUrl"/>
        <result column="audit_result_reason" jdbcType="VARCHAR" property="auditResultReason"/>
        <result column="receipt_result_reason" jdbcType="VARCHAR" property="receiptResultReason"/>
        <result column="contact_info" jdbcType="VARCHAR" property="contactInfo"/>
        <result column="contact_person_name" jdbcType="VARCHAR" property="contactPersonName"/>
        <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone"/>
        <result column="addr1" jdbcType="VARCHAR" property="addr1"/>
        <result column="addr2" jdbcType="VARCHAR" property="addr2"/>
        <result column="addr3" jdbcType="VARCHAR" property="addr3"/>
        <result column="addr4" jdbcType="VARCHAR" property="addr4"/>
        <result column="usaddr" jdbcType="VARCHAR" property="usaddr"/>
        <result column="spu_offering_code" jdbcType="VARCHAR" property="spuOfferingCode"/>
        <result column="spu_offering_name" jdbcType="VARCHAR" property="spuOfferingName"/>
        <result column="sku_offering_code" jdbcType="VARCHAR" property="skuOfferingCode"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="refunds_number" jdbcType="INTEGER" property="refundsNumber"/>
        <result column="return_price" jdbcType="BIGINT" property="returnPrice"/>
        <result column="orderType" jdbcType="VARCHAR" property="orderType"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="primary_cooperator_id" jdbcType="VARCHAR" property="primaryCooperatorId"/>
        <result column="imgUrl" jdbcType="VARCHAR" property="imgUrl"/>
        <result column="custMgName" jdbcType="VARCHAR" property="custMgName"/>
        <result column="custMgPhone" jdbcType="VARCHAR" property="custMgPhone"/>
        <result column="employeeNum" jdbcType="VARCHAR" property="employeeNum"/>
        <result column="createOperCode" jdbcType="VARCHAR" property="createOperCode"/>
        <result column="custName" jdbcType="VARCHAR" property="custName"/>
        <result column="custCode" jdbcType="VARCHAR" property="custCode"/>
        <result column="reserveBeId" jdbcType="VARCHAR" property="reserveBeId"/>
        <result column="reserveLocation" jdbcType="VARCHAR" property="reserveLocation"/>
        <result column="reminderCount" jdbcType="INTEGER" property="reminderCount"/>
        <result column="customerType" jdbcType="VARCHAR" property="customerType"/>
    </resultMap>

    <!--    <select id="selectOrderRocDetailByHandle" resultMap="orderInfoDetailHandleMap">-->
    <!--    </select>-->
    <select id="selectOrderRocListByHandle" resultMap="orderRocInfoHandleMap">
        select distinct
        (SELECT id FROM order_2c_roc_info WHERE atom_order_id = oa.id and refund_order_id = roc.refund_order_id
        order by create_time desc limit 1 ) as id,
        roc.refund_order_id as refund_order_id,
        roc.refunds_type as refunds_type,
        roc.inner_status as order_status,
        roc.order_id as order_id,
        oa.id as atom_order_id,
        roc.reason as reason,
        roc.remark as remark,
        roc.create_time create_time,
        roc.pictures_outer_url as pictures_outer_url,
        oa.sku_offering_name as sku_offering_name,
        oa.atom_offering_name as atom_offering_name,
        oa.atom_offering_class as atom_offering_class,
        oi.spu_offering_class as spu_offering_class,
        oa.model as model,
        oa.color as color,
        oa.atom_quantity * oa.sku_quantity as quantity,
        oa.atom_price as atom_price,
        oa.sku_quantity as skuQuantity,
        oa.sku_price as skuPrice,
        <!--cp.cooperator_id as cooperator_id,
        cp.partner_name as partner_name,
        cp.user_name cooperator_name,
        cph.cooperator_id as finish_cooperator_id,
        cph.user_name finish_cooperator_name,-->
        si.offering_code as spu_offering_code,
        si.offering_name as spu_offering_name,
        si.url,
        si.img_url img_url,
        oa.sku_offering_code as sku_offering_code,
        oi.addr1 as addr1,
        oa.supplier_name as supplier_name,
        case
        when oa.part_return=1 then roc.refunds_number
        else
        (case when roc.refunds_number is null or roc.refunds_number = '' then oa.atom_quantity * oa.sku_quantity

        else (case when ci.offering_class = 'A04' then roc.refunds_number*oa.sku_quantity
        else roc.refunds_number*oa.atom_quantity
        end) end)
        end refunds_number,
        case
        when oa.part_return=1 then roc.refunds_number *oa.atom_price
        else
        (case when roc.refunds_number is null or roc.refunds_number = '' then oa.atom_quantity * oa.sku_quantity  *oa.atom_price

        else (case when ci.offering_class = 'A04' then roc.refunds_number*oa.sku_quantity  *oa.atom_price
        else roc.refunds_number*oa.atom_quantity  *oa.atom_price
        end) end)
        end return_price,
        aoi.card_containing_terminal,
        roc.reminder_count reminderCount,
        si.offering_name spuOfferingName
        from order_2c_roc_info roc
        left join order_2c_atom_info oa on roc.order_id = oa.order_id and oa.id = roc.atom_order_id
        left join atom_offering_info_history aoi on aoi.spu_code = oa.spu_offering_code
        and aoi.sku_code = oa.sku_offering_code and aoi.offering_code = oa.atom_offering_code
        and aoi.spu_offering_version = oa.spu_offering_version and aoi.sku_offering_version = oa.sku_offering_version and aoi.atom_offering_version = oa.atom_offering_version and aoi.delete_time is null
        left join order_2c_info oi on roc.order_id = oi.order_id
        left join spu_offering_info_history si on si.offering_code = oa.spu_offering_code and si.spu_offering_version = oa.spu_offering_version
        left join category_info ci on si.spu_id = ci.spu_id
        <!--<if test="userIdList == null or userIdList.size() == 0">
            left join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>-->
        <!--<if test="userIdList != null and userIdList.size() != 0">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            where 1=1
            and ocr.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>-->
        <if test="(userIdList != null and userIdList.size() != 0) or (cooperatorName != null and cooperatorName != '') or (partnerName != null and partnerName != '')">
            inner join order_cooperator_relation ocr on ocr.atom_order_id = oa.id and ocr.order_id = oa.order_id
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
        </if>
        <!--left join (
        select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocrh.cooperator_id) cooperator_id,
        ocrh.atom_order_id,ocrh.order_id
        from order_cooperator_relation_history ocrh
        inner JOIN user_partner up on up.user_id = ocrh.cooperator_id
        group by ocrh.atom_order_id
        ) cph on cph.atom_order_id = oa.id and cph.order_id = oa.order_id-->
        where 1=1
        <if test="roleType == 'partner' or roleType == 'partnerLord'">
            and oa.atom_offering_class not in ('S','C')
        </if>
        <if test="rocId != null and rocId != ''">
            and roc.refund_order_id like concat ('%',#{rocId},'%')
        </if>
        <if test="refundType != null and refundType.size() != 0">
            and roc.refunds_type  in
            <foreach collection="refundType" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="orderId != null and orderId != ''">
            and roc.order_id like concat ('%',#{orderId},'%')
        </if>
        <if test="orderType != null and orderType != ''">
            <if test="orderType == '00' or orderType == '02' or orderType == '03'">
                and oa.order_type in ('00','02','03')
            </if>
            <if test="orderType != '00' and orderType != '02' and orderType != '03'">
                and oa.order_type = #{orderType}
            </if>
        </if>
        <!--<if test="userIdList != null and userIdList.size() != 0">
            and oa.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>-->
        <if test="userIdList != null and userIdList.size() != 0">
            and ocr.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="orderStatus!=null and orderStatus.size()!=0">
            and inner_status in
            <foreach collection="orderStatus" item="status" index="index" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="spuOfferingClass != null and spuOfferingClass.size() != 0">
            and oi.spu_offering_class in
            <foreach collection="spuOfferingClass" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="spuOfferingName != null and spuOfferingName!= ''">
            and si.offering_name like concat ('%',#{spuOfferingName},'%')
        </if>
        <if test="spuOfferingCode != null and spuOfferingCode != ''">
            and si.offering_code like concat ('%',#{spuOfferingCode},'%')
        </if>
        <if test="skuOfferingName != null and skuOfferingName != ''">
            and oa.sku_offering_name like concat ('%',#{skuOfferingName},'%')
        </if>
        <if test="skuOfferingCode != null and skuOfferingCode != ''">
            and oa.sku_offering_code like concat ('%',#{skuOfferingCode},'%')
        </if>
        <if test="atomOfferingCode != null and atomOfferingCode != ''">
            and oa.atom_offering_code like concat ('%',#{atomOfferingCode},'%')
        </if>
        <if test="atomOfferingName != null and atomOfferingName != ''">
            and oa.atom_offering_name like concat ('%',#{atomOfferingName},'%')
        </if>
        <if test="partnerName != null and partnerName != ''">
            and
            CASE
            when oa.atom_offering_class = 'D' then  oa.supplier_name like concat ('%',#{partnerName},'%')
            when oa.atom_offering_class = 'P' then  oa.supplier_name like concat ('%',#{partnerName},'%')
            when oa.atom_offering_class = 'O' then  oa.supplier_name like concat ('%',#{partnerName},'%')
            ELSE up.partner_name LIKE concat ('%',#{partnerName},'%')
            END
        </if>
        <if test="cooperatorName != null and cooperatorName != ''">
            <!--and user_partner.name like concat ('%',#{cooperatorName},'%')-->
            and up.user_name like concat ('%',#{cooperatorName},'%')
        </if>
        <if test="orderText != null and orderText !=  ''">
            and (roc.order_id like '%${orderText}%' || si.offering_name like '%${orderText}%')
        </if>
        <if test="orderRocTabStatus != null">
            <if test="orderRocTabStatus == 1">
                and roc.inner_status in (1,7,10)
            </if>
            <if test="orderRocTabStatus == 2">
                and roc.inner_status not in (1,7,10)
            </if>
        </if>
        <if test="beId != null and beId != ''">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = oa.sku_offering_code and (srt.province_code = #{beId} or srt.province_code = '000'))
        </if>
        <if test="location != null and location != ''">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = oa.sku_offering_code and srt.city_code = #{location})
        </if>
        <if test="spuOfferingClassList != null and spuOfferingClassList.size() != 0">
            and oi.spu_offering_class  in
            <foreach collection="spuOfferingClassList" item="spuOfferingClass" index="index" open="(" close=")" separator=",">
                #{spuOfferingClass}
            </foreach>
        </if>
        ORDER BY roc.update_time DESC, roc.refund_order_id,oa.atom_offering_class DESC
        limit #{page},#{num}
    </select>
    <select id="countOrderRocList" resultType="java.lang.Long">
        select count(distinct roc.id)
        from order_2c_roc_info roc
        left join order_2c_atom_info oa on roc.order_id = oa.order_id and oa.id = roc.atom_order_id
        left join atom_offering_info_history aoi on aoi.spu_code = oa.spu_offering_code
        and aoi.sku_code = oa.sku_offering_code and aoi.offering_code = oa.atom_offering_code
        and aoi.spu_offering_version = oa.spu_offering_version and aoi.sku_offering_version = oa.sku_offering_version and aoi.atom_offering_version = oa.atom_offering_version and aoi.delete_time is null
        left join order_2c_info oi on roc.order_id = oi.order_id
        left join spu_offering_info_history si on si.offering_code = oa.spu_offering_code and si.spu_offering_version = oa.spu_offering_version
        left join category_info ci on si.spu_id = ci.spu_id
        <!--<if test="userIdList == null or userIdList.size() == 0">
            left join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>-->
        <!--<if test="userIdList != null and userIdList.size() != 0">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            where 1=1
            and ocr.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>-->
        <if test="userIdList != null and userIdList.size() != 0">
            inner join order_cooperator_relation ocr on ocr.atom_order_id = oa.id and ocr.order_id = oa.order_id
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
        </if>
        where 1=1
        <if test="roleType == 'partner' or roleType == 'partnerLord'">
            and oa.atom_offering_class not in ('S','C')
        </if>
        <if test="rocId != null and rocId != ''">
            and roc.refund_order_id like concat ('%',#{rocId},'%')
        </if>
        <if test="refundType != null and refundType.size() != 0">
            and roc.refunds_type  in
            <foreach collection="refundType" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="orderId != null and orderId != ''">
            and roc.order_id  like concat ('%',#{orderId},'%')
        </if>
        <if test="orderType != null and orderType != ''">
            <if test="orderType == '00' or orderType == '02' or orderType == '03'">
                and oa.order_type in ('00','02','03')
            </if>
            <if test="orderType != '00' and orderType != '02' and orderType != '03'">
                and oa.order_type = #{orderType}
            </if>
        </if>
        <!--<if test="userIdList != null and userIdList.size() != 0">
            and oa.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>-->
        <if test="(userIdList != null and userIdList.size() != 0) or (partnerName != null and partnerName != '') or (cooperatorName != null and cooperatorName != '')">
            and ocr.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="orderStatus!=null and orderStatus.size()!=0">
            and inner_status in
            <foreach collection="orderStatus" item="status" index="index" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="spuOfferingClass != null and spuOfferingClass.size() != 0">
            and oi.spu_offering_class in
            <foreach collection="spuOfferingClass" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="spuOfferingName != null and spuOfferingName!= ''">
            and si.offering_name like concat ('%',#{spuOfferingName},'%')
        </if>
        <if test="spuOfferingCode != null and spuOfferingCode != ''">
            and si.offering_code like concat ('%',#{spuOfferingCode},'%')
        </if>
        <if test="skuOfferingName != null and skuOfferingName != ''">
            and oa.sku_offering_name like concat ('%',#{skuOfferingName},'%')
        </if>
        <if test="skuOfferingCode != null and skuOfferingCode != ''">
            and oa.sku_offering_code like concat ('%',#{skuOfferingCode},'%')
        </if>
        <if test="atomOfferingCode != null and atomOfferingCode != ''">
            and oa.atom_offering_code like concat ('%',#{atomOfferingCode},'%')
        </if>
        <if test="atomOfferingName != null and atomOfferingName != ''">
            and oa.atom_offering_name like concat ('%',#{atomOfferingName},'%')
        </if>
        <if test="partnerName != null and partnerName != ''">
            and
            CASE
            when oa.atom_offering_class = 'D' then  oa.supplier_name like concat ('%',#{partnerName},'%')
            when oa.atom_offering_class = 'P' then  oa.supplier_name like concat ('%',#{partnerName},'%')
            when oa.atom_offering_class = 'O' then  oa.supplier_name like concat ('%',#{partnerName},'%')
            ELSE up.partner_name LIKE concat ('%',#{partnerName},'%')
            END
        </if>
        <if test="cooperatorName != null and cooperatorName != ''">
            <!--and user_partner.name like concat ('%',#{cooperatorName},'%')-->
            and up.user_name like concat ('%',#{cooperatorName},'%')
        </if>
        <if test="orderText != null and orderText !=  ''">
            and (roc.order_id like '%${orderText}%' || si.offering_name like '%${orderText}%')
        </if>
        <if test="orderRocTabStatus != null">
            <if test="orderRocTabStatus == 1">
                and roc.inner_status in (1,7,10)
            </if>
            <if test="orderRocTabStatus == 2">
                and roc.inner_status not in (1,7,10)
            </if>
        </if>
        <if test="beId != null and beId != ''">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = oa.sku_offering_code and (srt.province_code = #{beId} or srt.province_code = '000'))
        </if>
        <if test="location != null and location != ''">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = oa.sku_offering_code and srt.city_code = #{location})
        </if>
        <if test="spuOfferingClassList != null and spuOfferingClassList.size() != 0">
            and oi.spu_offering_class  in
            <foreach collection="spuOfferingClassList" item="spuOfferingClass" index="index" open="(" close=")" separator=",">
                #{spuOfferingClass}
            </foreach>
        </if>
    </select>

    <select id="selectOrderRocListByHandleToBacklog" resultMap="orderRocInfoHandleMap">
        select distinct
        (SELECT id FROM order_2c_roc_info WHERE atom_order_id = oa.id and refund_order_id = roc.refund_order_id
        order by create_time desc limit 1) as id,
        roc.refund_order_id as refund_order_id,
        roc.refunds_type as refunds_type,
        roc.inner_status as order_status,
        roc.order_id as order_id,
        roc.pictures_outer_url as pictures_outer_url,
        oa.atom_offering_class as atom_offering_class,
        oi.spu_offering_class as spu_offering_class,
        si.url,
        si.img_url img_url,
        oi.addr1 as addr1,
        aoi.card_containing_terminal,
        roc.reminder_count reminderCount,
        si.offering_name spuOfferingName
        from order_2c_roc_info roc
        left join order_2c_atom_info oa on roc.order_id = oa.order_id and oa.id = roc.atom_order_id
        left join atom_offering_info_history aoi on aoi.spu_code = oa.spu_offering_code
        and aoi.sku_code = oa.sku_offering_code and aoi.offering_code = oa.atom_offering_code
        and aoi.spu_offering_version = oa.spu_offering_version and aoi.sku_offering_version = oa.sku_offering_version and aoi.atom_offering_version = oa.atom_offering_version and aoi.delete_time is null
        left join order_2c_info oi on roc.order_id = oi.order_id
        left join spu_offering_info_history si on si.offering_code = oa.spu_offering_code and si.spu_offering_version = oa.spu_offering_version
        <!--<if test="userIdList != null and userIdList.size() != 0">
            inner join (
            select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
            ocr.atom_order_id,ocr.order_id
            from order_cooperator_relation ocr
            inner JOIN user_partner up on up.user_id = ocr.cooperator_id
            where 1=1
            and ocr.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
            group by ocr.atom_order_id
            ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        </if>-->
        <if test="userIdList != null and userIdList.size() != 0">
            inner join order_cooperator_relation ocr on ocr.atom_order_id = oa.id and ocr.order_id = oa.order_id
<!--            inner JOIN user_partner up on up.user_id = ocr.cooperator_id-->
        </if>
        where roc.inner_status in (1,7,10)
        <if test="roleType == 'partner' or roleType == 'partnerLord'">
            and oa.atom_offering_class not in ('S','C')
        </if>
        <if test="beId != null and beId != ''">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = oa.sku_offering_code and (srt.province_code = #{beId} or srt.province_code = '000'))
        </if>
        <if test="location != null and location != ''">
            and exists (select srt.sku_offering_code from sku_release_target srt where srt.sku_offering_code = oa.sku_offering_code and srt.city_code = #{location})
        </if>
        <if test="refundOrderId != null and refundOrderId != ''">
            and roc.refund_order_id = #{refundOrderId}
        </if>
        <if test="userIdList != null and userIdList.size() != 0">
            and ocr.cooperator_id in
            <foreach collection="userIdList" item="userId" index="index" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>

    </select>

    <select id="selectOrderRocDetailByHandle" resultMap="orderRocInfoDetailHandleMap">
        select roc.refund_order_id                as refund_order_id,
        roc.refunds_type                   as refunds_type,
        roc.inner_status                   as order_status,
        roc.order_id                       as order_id,
        roc.atom_order_id                  as atom_order_id,
        roc.reason                         as reason,
        roc.remark                         as remark,
        roc.pictures_outer_url             as pictures_outer_url,
        roc.audit_result_reason            as audit_result_reason,
        roc.receipt_result_reason          as receipt_result_reason,
        roc.contactInfo                    as contact_info,
        oa.sku_offering_name               as sku_offering_name,
        oa.atom_offering_name              as atom_offering_name,
        oa.atom_offering_class             as atom_offering_class,
        oa.supplier_name                   as supplier_name,
        oi.spu_offering_class              as spu_offering_class,
        oi.contact_person_name             as contact_person_name,
        oi.contact_phone                   as contact_phone,
        oi.addr1                           as addr1,
        oi.addr2                           as addr2,
        oi.addr3                           as addr3,
        oi.addr4                           as addr4,
        oi.usaddr                          as usaddr,
        oi.remarks                         as remarks,
        oa.model                           as model,
        oa.color                           as color,
        oa.atom_quantity * oa.sku_quantity as quantity,
        oa.atom_price                      as atom_price,
        oa.sku_quantity as skuQuantity,
        oa.sku_price as skuPrice,
        oa.cooperator_id primary_cooperator_id,
        <!--cp.cooperator_id                   as cooperator_id,
        cp.user_name                  as cooperator_name,
        cp.partner_name          as partner_name,-->
        oa.sku_offering_code               as sku_offering_code,
        si.offering_code                   as spu_offering_code,
        si.offering_name                   as spu_offering_name,
        oa.atom_offering_version           as atomOfferingVersion,
        oa.spu_offering_version            as spuOfferingVersion,
        oa.sku_offering_version            as skuOfferingVersion,
        case
        when oa.part_return=1 then roc.refunds_number
        else
        (case when roc.refunds_number is null or roc.refunds_number = '' then oa.atom_quantity * oa.sku_quantity
        else (case when ci.offering_class = 'A04' then roc.refunds_number*oa.sku_quantity
        else roc.refunds_number*oa.atom_quantity
        end) end)
        end refunds_number,
        case
        when oa.part_return=1 then roc.refunds_number *oa.atom_price
        else
        (case when roc.refunds_number is null or roc.refunds_number = '' then oa.atom_quantity * oa.sku_quantity  *oa.atom_price
        else (case when ci.offering_class = 'A04' then roc.refunds_number*oa.sku_quantity*oa.atom_price
        else roc.refunds_number*oa.atom_quantity*oa.atom_price
        end) end)
        end return_price,
        oi.order_type orderType,
        si.url,
        si.img_url imgUrl,
        oi.cust_mg_name custMgName,
        oi.cust_mg_phone custMgPhone,
        oi.employee_num  employeeNum,
        oi.create_oper_code createOperCode,
        oi.cust_name custName,
        oi.cust_code custCode,
        oi.reserve_be_id reserveBeId,
        oi.reserve_location reserveLocation,
        roc.reminder_count reminderCount,
        oi.customer_type customerType
        from order_2c_roc_info roc
        left join order_2c_atom_info oa on roc.atom_order_id = oa.id
        left join order_2c_info oi on roc.order_id = oi.order_id
        left join spu_offering_info_history si on si.offering_code = oa.spu_offering_code and si.spu_offering_version = oa.spu_offering_version
        left join category_info ci on si.spu_id = ci.spu_id
        <!--left join (
        select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
        ocr.atom_order_id,ocr.order_id
        from order_cooperator_relation ocr
        inner JOIN user_partner up on up.user_id = ocr.cooperator_id
        group by ocr.atom_order_id
        ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id-->
        where roc.id = #{id}
    </select>

    <select id="selectOrderRocByOrderId" resultMap="orderRocInfoDetailHandleMap">
        select roc.refund_order_id                as refund_order_id,
        roc.refunds_type                   as refunds_type,
        roc.inner_status                   as order_status,
        roc.order_id                       as order_id,
        roc.atom_order_id                  as atom_order_id,
        roc.reason                         as reason,
        roc.remark                         as remark,
        roc.pictures_outer_url             as pictures_outer_url,
        roc.audit_result_reason            as audit_result_reason,
        roc.receipt_result_reason          as receipt_result_reason,
        roc.contactInfo                    as contact_info,
        oa.sku_offering_name               as sku_offering_name,
        oa.atom_offering_name              as atom_offering_name,
        oa.atom_offering_class             as atom_offering_class,
        oa.supplier_name                   as supplier_name,
        oi.spu_offering_class              as spu_offering_class,
        oi.contact_person_name             as contact_person_name,
        oi.contact_phone                   as contact_phone,
        oi.addr1                           as addr1,
        oi.addr2                           as addr2,
        oi.addr3                           as addr3,
        oi.addr4                           as addr4,
        oi.usaddr                          as usaddr,
        oi.remarks                         as remarks,
        oa.model                           as model,
        oa.color                           as color,
        oa.atom_quantity * oa.sku_quantity as quantity,
        oa.atom_price                      as atom_price,
        oa.sku_quantity as skuQuantity,
        oa.sku_price as skuPrice,
        oa.cooperator_id primary_cooperator_id,
        cp.cooperator_id                   as cooperator_id,
        cp.user_name                  as cooperator_name,
        cp.partner_name          as partner_name,
        oa.sku_offering_code               as sku_offering_code,
        si.offering_code                   as spu_offering_code,
        si.offering_name                   as spu_offering_name,
        oa.atom_offering_version           as atomOfferingVersion,
        oa.spu_offering_version            as spuOfferingVersion,
        oa.sku_offering_version            as skuOfferingVersion,
        case
        when oa.part_return=1 then roc.refunds_number
        else
        (case when roc.refunds_number is null or roc.refunds_number = '' then oa.atom_quantity * oa.sku_quantity
        else (case when ci.offering_class = 'A04' then roc.refunds_number*oa.sku_quantity
        else roc.refunds_number*oa.atom_quantity
        end) end)
        end refunds_number,
        case
        when oa.part_return=1 then roc.refunds_number *oa.atom_price
        else
        (case when roc.refunds_number is null or roc.refunds_number = '' then oa.atom_quantity * oa.sku_quantity  *oa.atom_price
        else (case when ci.offering_class = 'A04' then roc.refunds_number*oa.sku_quantity*oa.atom_price
        else roc.refunds_number*oa.atom_quantity*oa.atom_price
        end) end)
        end return_price,
        oi.order_type orderType,
        si.url,
        si.img_url imgUrl,
        oi.cust_mg_name custMgName,
        oi.cust_mg_phone custMgPhone,
        oi.employee_num  employeeNum,
        oi.create_oper_code createOperCode,
        oi.cust_name custName,
        oi.cust_code custCode,
        oi.reserve_be_id reserveBeId,
        oi.reserve_location reserveLocation,
        roc.reminder_count reminderCount,
        oi.customer_type customerType
        from order_2c_roc_info roc
        left join order_2c_atom_info oa on roc.atom_order_id = oa.id
        left join order_2c_info oi on roc.order_id = oi.order_id
        left join spu_offering_info_history si on si.offering_code = oa.spu_offering_code and si.spu_offering_version = oa.spu_offering_version
        left join category_info ci on si.spu_id = ci.spu_id
        left join (
        select GROUP_CONCAT(distinct up.partner_name) partner_name,group_concat(distinct up.name) user_name,group_concat(distinct ocr.cooperator_id) cooperator_id,
        ocr.atom_order_id,ocr.order_id
        from order_cooperator_relation ocr
        inner JOIN user_partner up on up.user_id = ocr.cooperator_id
        group by ocr.atom_order_id
        ) cp on cp.atom_order_id = oa.id and cp.order_id = oa.order_id
        where roc.order_id = #{orderId}
    </select>

    <select id="getLatestRefundRocByOrderId" resultType="com.chinamobile.iot.sc.pojo.vo.LatestRefundRocVO">
        select
            refund_order_id refundOrderId,
            order_id orderId,
            '仅退款' refundsTypeName,
            reason,
            remark,
            picture,
            pictures_inner_url picturesInnerUrl,
            pictures_outer_url picturesOuterUrl
        from
            order_2c_roc_info
        where
            order_id = #{orderId}
        and refunds_type = 1
        order by create_time desc
        limit 1
    </select>

</mapper>