<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.OrderCooperatorRelationMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 30 11:19:04 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="atom_order_id" jdbcType="VARCHAR" property="atomOrderId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="cooperator_id" jdbcType="VARCHAR" property="cooperatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 30 11:19:04 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 30 11:19:04 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 30 11:19:04 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, atom_order_id, order_id, cooperator_id, create_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 30 11:19:04 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_cooperator_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 30 11:19:04 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from order_cooperator_relation
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 30 11:19:04 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from order_cooperator_relation
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelationExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 30 11:19:04 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from order_cooperator_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 30 11:19:04 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_cooperator_relation (id, atom_order_id, order_id, 
      cooperator_id, create_time)
    values (#{id,jdbcType=VARCHAR}, #{atomOrderId,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, 
      #{cooperatorId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 30 11:19:04 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_cooperator_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="atomOrderId != null">
        atom_order_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="cooperatorId != null">
        cooperator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="atomOrderId != null">
        #{atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="cooperatorId != null">
        #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelationExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 30 11:19:04 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from order_cooperator_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 30 11:19:04 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_cooperator_relation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOrderId != null">
        atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.cooperatorId != null">
        cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 30 11:19:04 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_cooperator_relation
    set id = #{record.id,jdbcType=VARCHAR},
      atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 30 11:19:04 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_cooperator_relation
    <set>
      <if test="atomOrderId != null">
        atom_order_id = #{atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="cooperatorId != null">
        cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.OrderCooperatorRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 30 11:19:04 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_cooperator_relation
    set atom_order_id = #{atomOrderId,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 30 11:19:04 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_cooperator_relation
    (id, atom_order_id, order_id, cooperator_id, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.atomOrderId,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR}, 
        #{item.cooperatorId,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri May 30 11:19:04 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_cooperator_relation (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'atom_order_id'.toString() == column.value">
          #{item.atomOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'cooperator_id'.toString() == column.value">
          #{item.cooperatorId,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>