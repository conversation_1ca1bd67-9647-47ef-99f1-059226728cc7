<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.SkuRoleRelationMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.SkuRoleRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Sep 15 10:29:03 CST 2022. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="partner_role_id" jdbcType="INTEGER" property="partnerRoleId" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="point_percent" jdbcType="DOUBLE" property="pointPercent" />
    <result column="point_limit" jdbcType="BIGINT" property="pointLimit" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Sep 15 10:29:03 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Sep 15 10:29:03 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Sep 15 10:29:03 CST 2022. by MyBatis Generator, do not modify.
    -->
    id, partner_role_id, sku_id, point_percent, point_limit, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.SkuRoleRelationExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Sep 15 10:29:03 CST 2022. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sku_role_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Sep 15 10:29:03 CST 2022. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from sku_role_relation
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Sep 15 10:29:03 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from sku_role_relation
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.SkuRoleRelationExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Sep 15 10:29:03 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from sku_role_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.SkuRoleRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Sep 15 10:29:03 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into sku_role_relation (id, partner_role_id, sku_id, 
      point_percent, point_limit, create_time, 
      update_time)
    values (#{id,jdbcType=VARCHAR}, #{partnerRoleId,jdbcType=INTEGER}, #{skuId,jdbcType=VARCHAR}, 
      #{pointPercent,jdbcType=DOUBLE}, #{pointLimit,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.SkuRoleRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Sep 15 10:29:03 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into sku_role_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="partnerRoleId != null">
        partner_role_id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="pointPercent != null">
        point_percent,
      </if>
      <if test="pointLimit != null">
        point_limit,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="partnerRoleId != null">
        #{partnerRoleId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="pointPercent != null">
        #{pointPercent,jdbcType=DOUBLE},
      </if>
      <if test="pointLimit != null">
        #{pointLimit,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.SkuRoleRelationExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Sep 15 10:29:03 CST 2022. by MyBatis Generator, do not modify.
    -->
    select count(*) from sku_role_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Sep 15 10:29:03 CST 2022. by MyBatis Generator, do not modify.
    -->
    update sku_role_relation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.partnerRoleId != null">
        partner_role_id = #{record.partnerRoleId,jdbcType=INTEGER},
      </if>
      <if test="record.skuId != null">
        sku_id = #{record.skuId,jdbcType=VARCHAR},
      </if>
      <if test="record.pointPercent != null">
        point_percent = #{record.pointPercent,jdbcType=DOUBLE},
      </if>
      <if test="record.pointLimit != null">
        point_limit = #{record.pointLimit,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Sep 15 10:29:03 CST 2022. by MyBatis Generator, do not modify.
    -->
    update sku_role_relation
    set id = #{record.id,jdbcType=VARCHAR},
      partner_role_id = #{record.partnerRoleId,jdbcType=INTEGER},
      sku_id = #{record.skuId,jdbcType=VARCHAR},
      point_percent = #{record.pointPercent,jdbcType=DOUBLE},
      point_limit = #{record.pointLimit,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.SkuRoleRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Sep 15 10:29:03 CST 2022. by MyBatis Generator, do not modify.
    -->
    update sku_role_relation
    <set>
      <if test="partnerRoleId != null">
        partner_role_id = #{partnerRoleId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="pointPercent != null">
        point_percent = #{pointPercent,jdbcType=DOUBLE},
      </if>
      <if test="pointLimit != null">
        point_limit = #{pointLimit,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.SkuRoleRelation">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Sep 15 10:29:03 CST 2022. by MyBatis Generator, do not modify.
    -->
    update sku_role_relation
    set partner_role_id = #{partnerRoleId,jdbcType=INTEGER},
      sku_id = #{skuId,jdbcType=VARCHAR},
      point_percent = #{pointPercent,jdbcType=DOUBLE},
      point_limit = #{pointLimit,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Sep 15 10:29:03 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into sku_role_relation
    (id, partner_role_id, sku_id, point_percent, point_limit, create_time, update_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.partnerRoleId,jdbcType=INTEGER}, #{item.skuId,jdbcType=VARCHAR}, 
        #{item.pointPercent,jdbcType=DOUBLE}, #{item.pointLimit,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu Sep 15 10:29:03 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into sku_role_relation (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'partner_role_id'.toString() == column.value">
          #{item.partnerRoleId,jdbcType=INTEGER}
        </if>
        <if test="'sku_id'.toString() == column.value">
          #{item.skuId,jdbcType=VARCHAR}
        </if>
        <if test="'point_percent'.toString() == column.value">
          #{item.pointPercent,jdbcType=DOUBLE}
        </if>
        <if test="'point_limit'.toString() == column.value">
          #{item.pointLimit,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>