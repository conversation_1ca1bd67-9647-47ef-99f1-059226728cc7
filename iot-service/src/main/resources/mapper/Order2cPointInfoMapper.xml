<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.Order2cPointInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.Order2cPointInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 22 16:35:07 CST 2022. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="atom_order_id" jdbcType="VARCHAR" property="atomOrderId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="supplier_id" jdbcType="VARCHAR" property="supplierId" />
    <result column="distributor_type" jdbcType="VARCHAR" property="distributorType" />
    <result column="distributor_user_id" jdbcType="VARCHAR" property="distributorUserId" />
    <result column="point" jdbcType="BIGINT" property="point" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 22 16:35:07 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 22 16:35:07 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 22 16:35:07 CST 2022. by MyBatis Generator, do not modify.
    -->
    id, atom_order_id, order_id, supplier_id, distributor_type, distributor_user_id, 
    point, create_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cPointInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 22 16:35:07 CST 2022. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_2c_point_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 22 16:35:07 CST 2022. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from order_2c_point_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 22 16:35:07 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from order_2c_point_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cPointInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 22 16:35:07 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from order_2c_point_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.Order2cPointInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 22 16:35:07 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_point_info (id, atom_order_id, order_id, 
      supplier_id, distributor_type, distributor_user_id, 
      point, create_time)
    values (#{id,jdbcType=VARCHAR}, #{atomOrderId,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, 
      #{supplierId,jdbcType=VARCHAR}, #{distributorType,jdbcType=VARCHAR}, #{distributorUserId,jdbcType=VARCHAR}, 
      #{point,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.Order2cPointInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 22 16:35:07 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_point_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="atomOrderId != null">
        atom_order_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="distributorType != null">
        distributor_type,
      </if>
      <if test="distributorUserId != null">
        distributor_user_id,
      </if>
      <if test="point != null">
        point,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="atomOrderId != null">
        #{atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=VARCHAR},
      </if>
      <if test="distributorType != null">
        #{distributorType,jdbcType=VARCHAR},
      </if>
      <if test="distributorUserId != null">
        #{distributorUserId,jdbcType=VARCHAR},
      </if>
      <if test="point != null">
        #{point,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cPointInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 22 16:35:07 CST 2022. by MyBatis Generator, do not modify.
    -->
    select count(*) from order_2c_point_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 22 16:35:07 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_2c_point_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOrderId != null">
        atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierId != null">
        supplier_id = #{record.supplierId,jdbcType=VARCHAR},
      </if>
      <if test="record.distributorType != null">
        distributor_type = #{record.distributorType,jdbcType=VARCHAR},
      </if>
      <if test="record.distributorUserId != null">
        distributor_user_id = #{record.distributorUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.point != null">
        point = #{record.point,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 22 16:35:07 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_2c_point_info
    set id = #{record.id,jdbcType=VARCHAR},
      atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      supplier_id = #{record.supplierId,jdbcType=VARCHAR},
      distributor_type = #{record.distributorType,jdbcType=VARCHAR},
      distributor_user_id = #{record.distributorUserId,jdbcType=VARCHAR},
      point = #{record.point,jdbcType=BIGINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.Order2cPointInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 22 16:35:07 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_2c_point_info
    <set>
      <if test="atomOrderId != null">
        atom_order_id = #{atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=VARCHAR},
      </if>
      <if test="distributorType != null">
        distributor_type = #{distributorType,jdbcType=VARCHAR},
      </if>
      <if test="distributorUserId != null">
        distributor_user_id = #{distributorUserId,jdbcType=VARCHAR},
      </if>
      <if test="point != null">
        point = #{point,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.Order2cPointInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 22 16:35:07 CST 2022. by MyBatis Generator, do not modify.
    -->
    update order_2c_point_info
    set atom_order_id = #{atomOrderId,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      supplier_id = #{supplierId,jdbcType=VARCHAR},
      distributor_type = #{distributorType,jdbcType=VARCHAR},
      distributor_user_id = #{distributorUserId,jdbcType=VARCHAR},
      point = #{point,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 22 16:35:07 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_point_info
    (id, atom_order_id, order_id, supplier_id, distributor_type, distributor_user_id, 
      point, create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.atomOrderId,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR}, 
        #{item.supplierId,jdbcType=VARCHAR}, #{item.distributorType,jdbcType=VARCHAR}, 
        #{item.distributorUserId,jdbcType=VARCHAR}, #{item.point,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Nov 22 16:35:07 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_point_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'atom_order_id'.toString() == column.value">
          #{item.atomOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'supplier_id'.toString() == column.value">
          #{item.supplierId,jdbcType=VARCHAR}
        </if>
        <if test="'distributor_type'.toString() == column.value">
          #{item.distributorType,jdbcType=VARCHAR}
        </if>
        <if test="'distributor_user_id'.toString() == column.value">
          #{item.distributorUserId,jdbcType=VARCHAR}
        </if>
        <if test="'point'.toString() == column.value">
          #{item.point,jdbcType=BIGINT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>