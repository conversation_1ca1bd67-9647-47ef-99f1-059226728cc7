<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.MessageMapperExt">
  <update id="clearUrlAndFile" parameterType="java.lang.String">
    update message set url = null,file_key = null,content = '文件已失效,无法下载', isRead = case when source = 2 then true else isRead end
    where id in
      <foreach item ="item" index ="index" collection ="idList" open ="(" separator ="," close =")" >
      #{item}
      </foreach>
  </update>
</mapper>