<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.CardRelationMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.CardRelation">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="imei" jdbcType="VARCHAR" property="imei" />
    <result column="temp_iccid" jdbcType="VARCHAR" property="tempIccid" />
    <result column="client_name" jdbcType="VARCHAR" property="clientName" />
    <result column="product_num" jdbcType="VARCHAR" property="productNum" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="order_atom_info_id" jdbcType="VARCHAR" property="orderAtomInfoId" />
    <result column="msisdn" jdbcType="VARCHAR" property="msisdn" />
    <result column="sell_status" jdbcType="VARCHAR" property="sellStatus" />
    <result column="terminal_type" jdbcType="VARCHAR" property="terminalType" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="device_version" jdbcType="VARCHAR" property="deviceVersion" />
    <result column="mis_num" jdbcType="VARCHAR" property="misNum" />
    <result column="order_source" jdbcType="VARCHAR" property="orderSource" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="device_type" jdbcType="VARCHAR" property="deviceType" />
    <result column="supply" jdbcType="VARCHAR" property="supply" />
    <result column="contract_term" jdbcType="VARCHAR" property="contractTerm" />
    <result column="termianl_owner" jdbcType="VARCHAR" property="termianlOwner" />
    <result column="device_carton_num" jdbcType="VARCHAR" property="deviceCartonNum" />
    <result column="device_attr" jdbcType="VARCHAR" property="deviceAttr" />
    <result column="terminal_condition" jdbcType="VARCHAR" property="terminalCondition" />
    <result column="inventory_flag" jdbcType="VARCHAR" property="inventoryFlag" />
    <result column="use_mode" jdbcType="VARCHAR" property="useMode" />
    <result column="physics_flag" jdbcType="VARCHAR" property="physicsFlag" />
    <result column="condition_time" jdbcType="VARCHAR" property="conditionTime" />
    <result column="teminal_create_time" jdbcType="VARCHAR" property="teminalCreateTime" />
    <result column="teminal_remark" jdbcType="VARCHAR" property="teminalRemark" />
    <result column="recycle_source" jdbcType="VARCHAR" property="recycleSource" />
    <result column="chaba_msisdn" jdbcType="VARCHAR" property="chabaMsisdn" />
    <result column="template_id" jdbcType="VARCHAR" property="templateId" />
    <result column="template_name" jdbcType="VARCHAR" property="templateName" />
    <result column="cust_code" jdbcType="VARCHAR" property="custCode" />
    <result column="cust_name" jdbcType="VARCHAR" property="custName" />
    <result column="card_deliver_time" jdbcType="TIMESTAMP" property="cardDeliverTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="import_num" jdbcType="VARCHAR" property="importNum" />
    <result column="created_user" jdbcType="VARCHAR" property="createdUser" />
    <result column="created_user_name" jdbcType="VARCHAR" property="createdUserName" />
    <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, imei, temp_iccid, client_name, product_num, sn, order_id, order_atom_info_id, 
    msisdn, sell_status, terminal_type, be_id, location, device_version, mis_num, order_source, 
    channel, device_type, supply, contract_term, termianl_owner, device_carton_num, device_attr, 
    terminal_condition, inventory_flag, use_mode, physics_flag, condition_time, teminal_create_time, 
    teminal_remark, recycle_source, chaba_msisdn, template_id, template_name, cust_code, 
    cust_name, card_deliver_time, create_time, update_time, import_num, created_user, 
    created_user_name, delete_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.CardRelationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from card_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from card_relation
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from card_relation
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.CardRelationExample">
    delete from card_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.CardRelation">
    insert into card_relation (id, imei, temp_iccid, 
      client_name, product_num, sn, 
      order_id, order_atom_info_id, msisdn, 
      sell_status, terminal_type, be_id, 
      location, device_version, mis_num, 
      order_source, channel, device_type, 
      supply, contract_term, termianl_owner, 
      device_carton_num, device_attr, terminal_condition, 
      inventory_flag, use_mode, physics_flag, 
      condition_time, teminal_create_time, teminal_remark, 
      recycle_source, chaba_msisdn, template_id, 
      template_name, cust_code, cust_name, 
      card_deliver_time, create_time, update_time, 
      import_num, created_user, created_user_name, 
      delete_time)
    values (#{id,jdbcType=VARCHAR}, #{imei,jdbcType=VARCHAR}, #{tempIccid,jdbcType=VARCHAR}, 
      #{clientName,jdbcType=VARCHAR}, #{productNum,jdbcType=VARCHAR}, #{sn,jdbcType=VARCHAR}, 
      #{orderId,jdbcType=VARCHAR}, #{orderAtomInfoId,jdbcType=VARCHAR}, #{msisdn,jdbcType=VARCHAR}, 
      #{sellStatus,jdbcType=VARCHAR}, #{terminalType,jdbcType=VARCHAR}, #{beId,jdbcType=VARCHAR}, 
      #{location,jdbcType=VARCHAR}, #{deviceVersion,jdbcType=VARCHAR}, #{misNum,jdbcType=VARCHAR}, 
      #{orderSource,jdbcType=VARCHAR}, #{channel,jdbcType=VARCHAR}, #{deviceType,jdbcType=VARCHAR}, 
      #{supply,jdbcType=VARCHAR}, #{contractTerm,jdbcType=VARCHAR}, #{termianlOwner,jdbcType=VARCHAR}, 
      #{deviceCartonNum,jdbcType=VARCHAR}, #{deviceAttr,jdbcType=VARCHAR}, #{terminalCondition,jdbcType=VARCHAR}, 
      #{inventoryFlag,jdbcType=VARCHAR}, #{useMode,jdbcType=VARCHAR}, #{physicsFlag,jdbcType=VARCHAR}, 
      #{conditionTime,jdbcType=VARCHAR}, #{teminalCreateTime,jdbcType=VARCHAR}, #{teminalRemark,jdbcType=VARCHAR}, 
      #{recycleSource,jdbcType=VARCHAR}, #{chabaMsisdn,jdbcType=VARCHAR}, #{templateId,jdbcType=VARCHAR}, 
      #{templateName,jdbcType=VARCHAR}, #{custCode,jdbcType=VARCHAR}, #{custName,jdbcType=VARCHAR}, 
      #{cardDeliverTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{importNum,jdbcType=VARCHAR}, #{createdUser,jdbcType=VARCHAR}, #{createdUserName,jdbcType=VARCHAR}, 
      #{deleteTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.CardRelation">
    insert into card_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="imei != null">
        imei,
      </if>
      <if test="tempIccid != null">
        temp_iccid,
      </if>
      <if test="clientName != null">
        client_name,
      </if>
      <if test="productNum != null">
        product_num,
      </if>
      <if test="sn != null">
        sn,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderAtomInfoId != null">
        order_atom_info_id,
      </if>
      <if test="msisdn != null">
        msisdn,
      </if>
      <if test="sellStatus != null">
        sell_status,
      </if>
      <if test="terminalType != null">
        terminal_type,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="deviceVersion != null">
        device_version,
      </if>
      <if test="misNum != null">
        mis_num,
      </if>
      <if test="orderSource != null">
        order_source,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="deviceType != null">
        device_type,
      </if>
      <if test="supply != null">
        supply,
      </if>
      <if test="contractTerm != null">
        contract_term,
      </if>
      <if test="termianlOwner != null">
        termianl_owner,
      </if>
      <if test="deviceCartonNum != null">
        device_carton_num,
      </if>
      <if test="deviceAttr != null">
        device_attr,
      </if>
      <if test="terminalCondition != null">
        terminal_condition,
      </if>
      <if test="inventoryFlag != null">
        inventory_flag,
      </if>
      <if test="useMode != null">
        use_mode,
      </if>
      <if test="physicsFlag != null">
        physics_flag,
      </if>
      <if test="conditionTime != null">
        condition_time,
      </if>
      <if test="teminalCreateTime != null">
        teminal_create_time,
      </if>
      <if test="teminalRemark != null">
        teminal_remark,
      </if>
      <if test="recycleSource != null">
        recycle_source,
      </if>
      <if test="chabaMsisdn != null">
        chaba_msisdn,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="templateName != null">
        template_name,
      </if>
      <if test="custCode != null">
        cust_code,
      </if>
      <if test="custName != null">
        cust_name,
      </if>
      <if test="cardDeliverTime != null">
        card_deliver_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="importNum != null">
        import_num,
      </if>
      <if test="createdUser != null">
        created_user,
      </if>
      <if test="createdUserName != null">
        created_user_name,
      </if>
      <if test="deleteTime != null">
        delete_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="imei != null">
        #{imei,jdbcType=VARCHAR},
      </if>
      <if test="tempIccid != null">
        #{tempIccid,jdbcType=VARCHAR},
      </if>
      <if test="clientName != null">
        #{clientName,jdbcType=VARCHAR},
      </if>
      <if test="productNum != null">
        #{productNum,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderAtomInfoId != null">
        #{orderAtomInfoId,jdbcType=VARCHAR},
      </if>
      <if test="msisdn != null">
        #{msisdn,jdbcType=VARCHAR},
      </if>
      <if test="sellStatus != null">
        #{sellStatus,jdbcType=VARCHAR},
      </if>
      <if test="terminalType != null">
        #{terminalType,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="deviceVersion != null">
        #{deviceVersion,jdbcType=VARCHAR},
      </if>
      <if test="misNum != null">
        #{misNum,jdbcType=VARCHAR},
      </if>
      <if test="orderSource != null">
        #{orderSource,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="deviceType != null">
        #{deviceType,jdbcType=VARCHAR},
      </if>
      <if test="supply != null">
        #{supply,jdbcType=VARCHAR},
      </if>
      <if test="contractTerm != null">
        #{contractTerm,jdbcType=VARCHAR},
      </if>
      <if test="termianlOwner != null">
        #{termianlOwner,jdbcType=VARCHAR},
      </if>
      <if test="deviceCartonNum != null">
        #{deviceCartonNum,jdbcType=VARCHAR},
      </if>
      <if test="deviceAttr != null">
        #{deviceAttr,jdbcType=VARCHAR},
      </if>
      <if test="terminalCondition != null">
        #{terminalCondition,jdbcType=VARCHAR},
      </if>
      <if test="inventoryFlag != null">
        #{inventoryFlag,jdbcType=VARCHAR},
      </if>
      <if test="useMode != null">
        #{useMode,jdbcType=VARCHAR},
      </if>
      <if test="physicsFlag != null">
        #{physicsFlag,jdbcType=VARCHAR},
      </if>
      <if test="conditionTime != null">
        #{conditionTime,jdbcType=VARCHAR},
      </if>
      <if test="teminalCreateTime != null">
        #{teminalCreateTime,jdbcType=VARCHAR},
      </if>
      <if test="teminalRemark != null">
        #{teminalRemark,jdbcType=VARCHAR},
      </if>
      <if test="recycleSource != null">
        #{recycleSource,jdbcType=VARCHAR},
      </if>
      <if test="chabaMsisdn != null">
        #{chabaMsisdn,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="templateName != null">
        #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="custCode != null">
        #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="custName != null">
        #{custName,jdbcType=VARCHAR},
      </if>
      <if test="cardDeliverTime != null">
        #{cardDeliverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="importNum != null">
        #{importNum,jdbcType=VARCHAR},
      </if>
      <if test="createdUser != null">
        #{createdUser,jdbcType=VARCHAR},
      </if>
      <if test="createdUserName != null">
        #{createdUserName,jdbcType=VARCHAR},
      </if>
      <if test="deleteTime != null">
        #{deleteTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.CardRelationExample" resultType="java.lang.Long">
    select count(*) from card_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update card_relation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.imei != null">
        imei = #{record.imei,jdbcType=VARCHAR},
      </if>
      <if test="record.tempIccid != null">
        temp_iccid = #{record.tempIccid,jdbcType=VARCHAR},
      </if>
      <if test="record.clientName != null">
        client_name = #{record.clientName,jdbcType=VARCHAR},
      </if>
      <if test="record.productNum != null">
        product_num = #{record.productNum,jdbcType=VARCHAR},
      </if>
      <if test="record.sn != null">
        sn = #{record.sn,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderAtomInfoId != null">
        order_atom_info_id = #{record.orderAtomInfoId,jdbcType=VARCHAR},
      </if>
      <if test="record.msisdn != null">
        msisdn = #{record.msisdn,jdbcType=VARCHAR},
      </if>
      <if test="record.sellStatus != null">
        sell_status = #{record.sellStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.terminalType != null">
        terminal_type = #{record.terminalType,jdbcType=VARCHAR},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.location != null">
        location = #{record.location,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceVersion != null">
        device_version = #{record.deviceVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.misNum != null">
        mis_num = #{record.misNum,jdbcType=VARCHAR},
      </if>
      <if test="record.orderSource != null">
        order_source = #{record.orderSource,jdbcType=VARCHAR},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceType != null">
        device_type = #{record.deviceType,jdbcType=VARCHAR},
      </if>
      <if test="record.supply != null">
        supply = #{record.supply,jdbcType=VARCHAR},
      </if>
      <if test="record.contractTerm != null">
        contract_term = #{record.contractTerm,jdbcType=VARCHAR},
      </if>
      <if test="record.termianlOwner != null">
        termianl_owner = #{record.termianlOwner,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceCartonNum != null">
        device_carton_num = #{record.deviceCartonNum,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceAttr != null">
        device_attr = #{record.deviceAttr,jdbcType=VARCHAR},
      </if>
      <if test="record.terminalCondition != null">
        terminal_condition = #{record.terminalCondition,jdbcType=VARCHAR},
      </if>
      <if test="record.inventoryFlag != null">
        inventory_flag = #{record.inventoryFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.useMode != null">
        use_mode = #{record.useMode,jdbcType=VARCHAR},
      </if>
      <if test="record.physicsFlag != null">
        physics_flag = #{record.physicsFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.conditionTime != null">
        condition_time = #{record.conditionTime,jdbcType=VARCHAR},
      </if>
      <if test="record.teminalCreateTime != null">
        teminal_create_time = #{record.teminalCreateTime,jdbcType=VARCHAR},
      </if>
      <if test="record.teminalRemark != null">
        teminal_remark = #{record.teminalRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.recycleSource != null">
        recycle_source = #{record.recycleSource,jdbcType=VARCHAR},
      </if>
      <if test="record.chabaMsisdn != null">
        chaba_msisdn = #{record.chabaMsisdn,jdbcType=VARCHAR},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=VARCHAR},
      </if>
      <if test="record.templateName != null">
        template_name = #{record.templateName,jdbcType=VARCHAR},
      </if>
      <if test="record.custCode != null">
        cust_code = #{record.custCode,jdbcType=VARCHAR},
      </if>
      <if test="record.custName != null">
        cust_name = #{record.custName,jdbcType=VARCHAR},
      </if>
      <if test="record.cardDeliverTime != null">
        card_deliver_time = #{record.cardDeliverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.importNum != null">
        import_num = #{record.importNum,jdbcType=VARCHAR},
      </if>
      <if test="record.createdUser != null">
        created_user = #{record.createdUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createdUserName != null">
        created_user_name = #{record.createdUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.deleteTime != null">
        delete_time = #{record.deleteTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update card_relation
    set id = #{record.id,jdbcType=VARCHAR},
      imei = #{record.imei,jdbcType=VARCHAR},
      temp_iccid = #{record.tempIccid,jdbcType=VARCHAR},
      client_name = #{record.clientName,jdbcType=VARCHAR},
      product_num = #{record.productNum,jdbcType=VARCHAR},
      sn = #{record.sn,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      order_atom_info_id = #{record.orderAtomInfoId,jdbcType=VARCHAR},
      msisdn = #{record.msisdn,jdbcType=VARCHAR},
      sell_status = #{record.sellStatus,jdbcType=VARCHAR},
      terminal_type = #{record.terminalType,jdbcType=VARCHAR},
      be_id = #{record.beId,jdbcType=VARCHAR},
      location = #{record.location,jdbcType=VARCHAR},
      device_version = #{record.deviceVersion,jdbcType=VARCHAR},
      mis_num = #{record.misNum,jdbcType=VARCHAR},
      order_source = #{record.orderSource,jdbcType=VARCHAR},
      channel = #{record.channel,jdbcType=VARCHAR},
      device_type = #{record.deviceType,jdbcType=VARCHAR},
      supply = #{record.supply,jdbcType=VARCHAR},
      contract_term = #{record.contractTerm,jdbcType=VARCHAR},
      termianl_owner = #{record.termianlOwner,jdbcType=VARCHAR},
      device_carton_num = #{record.deviceCartonNum,jdbcType=VARCHAR},
      device_attr = #{record.deviceAttr,jdbcType=VARCHAR},
      terminal_condition = #{record.terminalCondition,jdbcType=VARCHAR},
      inventory_flag = #{record.inventoryFlag,jdbcType=VARCHAR},
      use_mode = #{record.useMode,jdbcType=VARCHAR},
      physics_flag = #{record.physicsFlag,jdbcType=VARCHAR},
      condition_time = #{record.conditionTime,jdbcType=VARCHAR},
      teminal_create_time = #{record.teminalCreateTime,jdbcType=VARCHAR},
      teminal_remark = #{record.teminalRemark,jdbcType=VARCHAR},
      recycle_source = #{record.recycleSource,jdbcType=VARCHAR},
      chaba_msisdn = #{record.chabaMsisdn,jdbcType=VARCHAR},
      template_id = #{record.templateId,jdbcType=VARCHAR},
      template_name = #{record.templateName,jdbcType=VARCHAR},
      cust_code = #{record.custCode,jdbcType=VARCHAR},
      cust_name = #{record.custName,jdbcType=VARCHAR},
      card_deliver_time = #{record.cardDeliverTime,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      import_num = #{record.importNum,jdbcType=VARCHAR},
      created_user = #{record.createdUser,jdbcType=VARCHAR},
      created_user_name = #{record.createdUserName,jdbcType=VARCHAR},
      delete_time = #{record.deleteTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.CardRelation">
    update card_relation
    <set>
      <if test="imei != null">
        imei = #{imei,jdbcType=VARCHAR},
      </if>
      <if test="tempIccid != null">
        temp_iccid = #{tempIccid,jdbcType=VARCHAR},
      </if>
      <if test="clientName != null">
        client_name = #{clientName,jdbcType=VARCHAR},
      </if>
      <if test="productNum != null">
        product_num = #{productNum,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        sn = #{sn,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderAtomInfoId != null">
        order_atom_info_id = #{orderAtomInfoId,jdbcType=VARCHAR},
      </if>
      <if test="msisdn != null">
        msisdn = #{msisdn,jdbcType=VARCHAR},
      </if>
      <if test="sellStatus != null">
        sell_status = #{sellStatus,jdbcType=VARCHAR},
      </if>
      <if test="terminalType != null">
        terminal_type = #{terminalType,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="deviceVersion != null">
        device_version = #{deviceVersion,jdbcType=VARCHAR},
      </if>
      <if test="misNum != null">
        mis_num = #{misNum,jdbcType=VARCHAR},
      </if>
      <if test="orderSource != null">
        order_source = #{orderSource,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="deviceType != null">
        device_type = #{deviceType,jdbcType=VARCHAR},
      </if>
      <if test="supply != null">
        supply = #{supply,jdbcType=VARCHAR},
      </if>
      <if test="contractTerm != null">
        contract_term = #{contractTerm,jdbcType=VARCHAR},
      </if>
      <if test="termianlOwner != null">
        termianl_owner = #{termianlOwner,jdbcType=VARCHAR},
      </if>
      <if test="deviceCartonNum != null">
        device_carton_num = #{deviceCartonNum,jdbcType=VARCHAR},
      </if>
      <if test="deviceAttr != null">
        device_attr = #{deviceAttr,jdbcType=VARCHAR},
      </if>
      <if test="terminalCondition != null">
        terminal_condition = #{terminalCondition,jdbcType=VARCHAR},
      </if>
      <if test="inventoryFlag != null">
        inventory_flag = #{inventoryFlag,jdbcType=VARCHAR},
      </if>
      <if test="useMode != null">
        use_mode = #{useMode,jdbcType=VARCHAR},
      </if>
      <if test="physicsFlag != null">
        physics_flag = #{physicsFlag,jdbcType=VARCHAR},
      </if>
      <if test="conditionTime != null">
        condition_time = #{conditionTime,jdbcType=VARCHAR},
      </if>
      <if test="teminalCreateTime != null">
        teminal_create_time = #{teminalCreateTime,jdbcType=VARCHAR},
      </if>
      <if test="teminalRemark != null">
        teminal_remark = #{teminalRemark,jdbcType=VARCHAR},
      </if>
      <if test="recycleSource != null">
        recycle_source = #{recycleSource,jdbcType=VARCHAR},
      </if>
      <if test="chabaMsisdn != null">
        chaba_msisdn = #{chabaMsisdn,jdbcType=VARCHAR},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=VARCHAR},
      </if>
      <if test="templateName != null">
        template_name = #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="custCode != null">
        cust_code = #{custCode,jdbcType=VARCHAR},
      </if>
      <if test="custName != null">
        cust_name = #{custName,jdbcType=VARCHAR},
      </if>
      <if test="cardDeliverTime != null">
        card_deliver_time = #{cardDeliverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="importNum != null">
        import_num = #{importNum,jdbcType=VARCHAR},
      </if>
      <if test="createdUser != null">
        created_user = #{createdUser,jdbcType=VARCHAR},
      </if>
      <if test="createdUserName != null">
        created_user_name = #{createdUserName,jdbcType=VARCHAR},
      </if>
      <if test="deleteTime != null">
        delete_time = #{deleteTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.CardRelation">
    update card_relation
    set imei = #{imei,jdbcType=VARCHAR},
      temp_iccid = #{tempIccid,jdbcType=VARCHAR},
      client_name = #{clientName,jdbcType=VARCHAR},
      product_num = #{productNum,jdbcType=VARCHAR},
      sn = #{sn,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      order_atom_info_id = #{orderAtomInfoId,jdbcType=VARCHAR},
      msisdn = #{msisdn,jdbcType=VARCHAR},
      sell_status = #{sellStatus,jdbcType=VARCHAR},
      terminal_type = #{terminalType,jdbcType=VARCHAR},
      be_id = #{beId,jdbcType=VARCHAR},
      location = #{location,jdbcType=VARCHAR},
      device_version = #{deviceVersion,jdbcType=VARCHAR},
      mis_num = #{misNum,jdbcType=VARCHAR},
      order_source = #{orderSource,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=VARCHAR},
      device_type = #{deviceType,jdbcType=VARCHAR},
      supply = #{supply,jdbcType=VARCHAR},
      contract_term = #{contractTerm,jdbcType=VARCHAR},
      termianl_owner = #{termianlOwner,jdbcType=VARCHAR},
      device_carton_num = #{deviceCartonNum,jdbcType=VARCHAR},
      device_attr = #{deviceAttr,jdbcType=VARCHAR},
      terminal_condition = #{terminalCondition,jdbcType=VARCHAR},
      inventory_flag = #{inventoryFlag,jdbcType=VARCHAR},
      use_mode = #{useMode,jdbcType=VARCHAR},
      physics_flag = #{physicsFlag,jdbcType=VARCHAR},
      condition_time = #{conditionTime,jdbcType=VARCHAR},
      teminal_create_time = #{teminalCreateTime,jdbcType=VARCHAR},
      teminal_remark = #{teminalRemark,jdbcType=VARCHAR},
      recycle_source = #{recycleSource,jdbcType=VARCHAR},
      chaba_msisdn = #{chabaMsisdn,jdbcType=VARCHAR},
      template_id = #{templateId,jdbcType=VARCHAR},
      template_name = #{templateName,jdbcType=VARCHAR},
      cust_code = #{custCode,jdbcType=VARCHAR},
      cust_name = #{custName,jdbcType=VARCHAR},
      card_deliver_time = #{cardDeliverTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      import_num = #{importNum,jdbcType=VARCHAR},
      created_user = #{createdUser,jdbcType=VARCHAR},
      created_user_name = #{createdUserName,jdbcType=VARCHAR},
      delete_time = #{deleteTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into card_relation
    (id, imei, temp_iccid, client_name, product_num, sn, order_id, order_atom_info_id, 
      msisdn, sell_status, terminal_type, be_id, location, device_version, mis_num, order_source, 
      channel, device_type, supply, contract_term, termianl_owner, device_carton_num, 
      device_attr, terminal_condition, inventory_flag, use_mode, physics_flag, condition_time, 
      teminal_create_time, teminal_remark, recycle_source, chaba_msisdn, template_id, 
      template_name, cust_code, cust_name, card_deliver_time, create_time, update_time, 
      import_num, created_user, created_user_name, delete_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.imei,jdbcType=VARCHAR}, #{item.tempIccid,jdbcType=VARCHAR}, 
        #{item.clientName,jdbcType=VARCHAR}, #{item.productNum,jdbcType=VARCHAR}, #{item.sn,jdbcType=VARCHAR}, 
        #{item.orderId,jdbcType=VARCHAR}, #{item.orderAtomInfoId,jdbcType=VARCHAR}, #{item.msisdn,jdbcType=VARCHAR}, 
        #{item.sellStatus,jdbcType=VARCHAR}, #{item.terminalType,jdbcType=VARCHAR}, #{item.beId,jdbcType=VARCHAR}, 
        #{item.location,jdbcType=VARCHAR}, #{item.deviceVersion,jdbcType=VARCHAR}, #{item.misNum,jdbcType=VARCHAR}, 
        #{item.orderSource,jdbcType=VARCHAR}, #{item.channel,jdbcType=VARCHAR}, #{item.deviceType,jdbcType=VARCHAR}, 
        #{item.supply,jdbcType=VARCHAR}, #{item.contractTerm,jdbcType=VARCHAR}, #{item.termianlOwner,jdbcType=VARCHAR}, 
        #{item.deviceCartonNum,jdbcType=VARCHAR}, #{item.deviceAttr,jdbcType=VARCHAR}, 
        #{item.terminalCondition,jdbcType=VARCHAR}, #{item.inventoryFlag,jdbcType=VARCHAR}, 
        #{item.useMode,jdbcType=VARCHAR}, #{item.physicsFlag,jdbcType=VARCHAR}, #{item.conditionTime,jdbcType=VARCHAR}, 
        #{item.teminalCreateTime,jdbcType=VARCHAR}, #{item.teminalRemark,jdbcType=VARCHAR}, 
        #{item.recycleSource,jdbcType=VARCHAR}, #{item.chabaMsisdn,jdbcType=VARCHAR}, #{item.templateId,jdbcType=VARCHAR}, 
        #{item.templateName,jdbcType=VARCHAR}, #{item.custCode,jdbcType=VARCHAR}, #{item.custName,jdbcType=VARCHAR}, 
        #{item.cardDeliverTime,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.importNum,jdbcType=VARCHAR}, #{item.createdUser,jdbcType=VARCHAR}, 
        #{item.createdUserName,jdbcType=VARCHAR}, #{item.deleteTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into card_relation (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'imei'.toString() == column.value">
          #{item.imei,jdbcType=VARCHAR}
        </if>
        <if test="'temp_iccid'.toString() == column.value">
          #{item.tempIccid,jdbcType=VARCHAR}
        </if>
        <if test="'client_name'.toString() == column.value">
          #{item.clientName,jdbcType=VARCHAR}
        </if>
        <if test="'product_num'.toString() == column.value">
          #{item.productNum,jdbcType=VARCHAR}
        </if>
        <if test="'sn'.toString() == column.value">
          #{item.sn,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'order_atom_info_id'.toString() == column.value">
          #{item.orderAtomInfoId,jdbcType=VARCHAR}
        </if>
        <if test="'msisdn'.toString() == column.value">
          #{item.msisdn,jdbcType=VARCHAR}
        </if>
        <if test="'sell_status'.toString() == column.value">
          #{item.sellStatus,jdbcType=VARCHAR}
        </if>
        <if test="'terminal_type'.toString() == column.value">
          #{item.terminalType,jdbcType=VARCHAR}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'location'.toString() == column.value">
          #{item.location,jdbcType=VARCHAR}
        </if>
        <if test="'device_version'.toString() == column.value">
          #{item.deviceVersion,jdbcType=VARCHAR}
        </if>
        <if test="'mis_num'.toString() == column.value">
          #{item.misNum,jdbcType=VARCHAR}
        </if>
        <if test="'order_source'.toString() == column.value">
          #{item.orderSource,jdbcType=VARCHAR}
        </if>
        <if test="'channel'.toString() == column.value">
          #{item.channel,jdbcType=VARCHAR}
        </if>
        <if test="'device_type'.toString() == column.value">
          #{item.deviceType,jdbcType=VARCHAR}
        </if>
        <if test="'supply'.toString() == column.value">
          #{item.supply,jdbcType=VARCHAR}
        </if>
        <if test="'contract_term'.toString() == column.value">
          #{item.contractTerm,jdbcType=VARCHAR}
        </if>
        <if test="'termianl_owner'.toString() == column.value">
          #{item.termianlOwner,jdbcType=VARCHAR}
        </if>
        <if test="'device_carton_num'.toString() == column.value">
          #{item.deviceCartonNum,jdbcType=VARCHAR}
        </if>
        <if test="'device_attr'.toString() == column.value">
          #{item.deviceAttr,jdbcType=VARCHAR}
        </if>
        <if test="'terminal_condition'.toString() == column.value">
          #{item.terminalCondition,jdbcType=VARCHAR}
        </if>
        <if test="'inventory_flag'.toString() == column.value">
          #{item.inventoryFlag,jdbcType=VARCHAR}
        </if>
        <if test="'use_mode'.toString() == column.value">
          #{item.useMode,jdbcType=VARCHAR}
        </if>
        <if test="'physics_flag'.toString() == column.value">
          #{item.physicsFlag,jdbcType=VARCHAR}
        </if>
        <if test="'condition_time'.toString() == column.value">
          #{item.conditionTime,jdbcType=VARCHAR}
        </if>
        <if test="'teminal_create_time'.toString() == column.value">
          #{item.teminalCreateTime,jdbcType=VARCHAR}
        </if>
        <if test="'teminal_remark'.toString() == column.value">
          #{item.teminalRemark,jdbcType=VARCHAR}
        </if>
        <if test="'recycle_source'.toString() == column.value">
          #{item.recycleSource,jdbcType=VARCHAR}
        </if>
        <if test="'chaba_msisdn'.toString() == column.value">
          #{item.chabaMsisdn,jdbcType=VARCHAR}
        </if>
        <if test="'template_id'.toString() == column.value">
          #{item.templateId,jdbcType=VARCHAR}
        </if>
        <if test="'template_name'.toString() == column.value">
          #{item.templateName,jdbcType=VARCHAR}
        </if>
        <if test="'cust_code'.toString() == column.value">
          #{item.custCode,jdbcType=VARCHAR}
        </if>
        <if test="'cust_name'.toString() == column.value">
          #{item.custName,jdbcType=VARCHAR}
        </if>
        <if test="'card_deliver_time'.toString() == column.value">
          #{item.cardDeliverTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'import_num'.toString() == column.value">
          #{item.importNum,jdbcType=VARCHAR}
        </if>
        <if test="'created_user'.toString() == column.value">
          #{item.createdUser,jdbcType=VARCHAR}
        </if>
        <if test="'created_user_name'.toString() == column.value">
          #{item.createdUserName,jdbcType=VARCHAR}
        </if>
        <if test="'delete_time'.toString() == column.value">
          #{item.deleteTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>