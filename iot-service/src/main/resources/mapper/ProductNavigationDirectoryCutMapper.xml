<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ProductNavigationDirectoryCutMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.ProductNavigationDirectoryCut">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 23 10:34:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="image" jdbcType="VARCHAR" property="image" />
    <result column="menu" jdbcType="VARCHAR" property="menu" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 23 10:34:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 23 10:34:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 23 10:34:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, name, parent_id, sort, image, menu, is_delete
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ProductNavigationDirectoryCutExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 23 10:34:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from product_navigation_directory_cut
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 23 10:34:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from product_navigation_directory_cut
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 23 10:34:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from product_navigation_directory_cut
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ProductNavigationDirectoryCutExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 23 10:34:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from product_navigation_directory_cut
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.ProductNavigationDirectoryCut">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 23 10:34:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into product_navigation_directory_cut (id, name, parent_id, 
      sort, image, menu, 
      is_delete)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{parentId,jdbcType=VARCHAR}, 
      #{sort,jdbcType=INTEGER}, #{image,jdbcType=VARCHAR}, #{menu,jdbcType=VARCHAR}, 
      #{isDelete,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ProductNavigationDirectoryCut">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 23 10:34:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into product_navigation_directory_cut
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="image != null">
        image,
      </if>
      <if test="menu != null">
        menu,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="image != null">
        #{image,jdbcType=VARCHAR},
      </if>
      <if test="menu != null">
        #{menu,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ProductNavigationDirectoryCutExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 23 10:34:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from product_navigation_directory_cut
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 23 10:34:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    update product_navigation_directory_cut
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.image != null">
        image = #{record.image,jdbcType=VARCHAR},
      </if>
      <if test="record.menu != null">
        menu = #{record.menu,jdbcType=VARCHAR},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 23 10:34:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    update product_navigation_directory_cut
    set id = #{record.id,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      parent_id = #{record.parentId,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=INTEGER},
      image = #{record.image,jdbcType=VARCHAR},
      menu = #{record.menu,jdbcType=VARCHAR},
      is_delete = #{record.isDelete,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ProductNavigationDirectoryCut">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 23 10:34:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    update product_navigation_directory_cut
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="image != null">
        image = #{image,jdbcType=VARCHAR},
      </if>
      <if test="menu != null">
        menu = #{menu,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.ProductNavigationDirectoryCut">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 23 10:34:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    update product_navigation_directory_cut
    set name = #{name,jdbcType=VARCHAR},
      parent_id = #{parentId,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      image = #{image,jdbcType=VARCHAR},
      menu = #{menu,jdbcType=VARCHAR},
      is_delete = #{isDelete,jdbcType=BIT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 23 10:34:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into product_navigation_directory_cut
    (id, name, parent_id, sort, image, menu, is_delete)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.parentId,jdbcType=VARCHAR}, 
        #{item.sort,jdbcType=INTEGER}, #{item.image,jdbcType=VARCHAR}, #{item.menu,jdbcType=VARCHAR}, 
        #{item.isDelete,jdbcType=BIT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Apr 23 10:34:44 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into product_navigation_directory_cut (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'parent_id'.toString() == column.value">
          #{item.parentId,jdbcType=VARCHAR}
        </if>
        <if test="'sort'.toString() == column.value">
          #{item.sort,jdbcType=INTEGER}
        </if>
        <if test="'image'.toString() == column.value">
          #{item.image,jdbcType=VARCHAR}
        </if>
        <if test="'menu'.toString() == column.value">
          #{item.menu,jdbcType=VARCHAR}
        </if>
        <if test="'is_delete'.toString() == column.value">
          #{item.isDelete,jdbcType=BIT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>