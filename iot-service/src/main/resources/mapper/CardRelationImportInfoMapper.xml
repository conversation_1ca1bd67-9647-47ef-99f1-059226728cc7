<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.CardRelationImportInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.CardRelationImportInfo">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="import_num" jdbcType="VARCHAR" property="importNum" />
    <result column="device_version" jdbcType="VARCHAR" property="deviceVersion" />
    <result column="import_count" jdbcType="INTEGER" property="importCount" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="created_user" jdbcType="VARCHAR" property="createdUser" />
    <result column="created_user_name" jdbcType="VARCHAR" property="createdUserName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, import_num, device_version, import_count, be_id, created_user, created_user_name, 
    create_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.CardRelationImportInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from card_relation_import_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from card_relation_import_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from card_relation_import_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.CardRelationImportInfoExample">
    delete from card_relation_import_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.CardRelationImportInfo">
    insert into card_relation_import_info (id, import_num, device_version, 
      import_count, be_id, created_user, 
      created_user_name, create_time)
    values (#{id,jdbcType=VARCHAR}, #{importNum,jdbcType=VARCHAR}, #{deviceVersion,jdbcType=VARCHAR}, 
      #{importCount,jdbcType=INTEGER}, #{beId,jdbcType=VARCHAR}, #{createdUser,jdbcType=VARCHAR}, 
      #{createdUserName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.CardRelationImportInfo">
    insert into card_relation_import_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="importNum != null">
        import_num,
      </if>
      <if test="deviceVersion != null">
        device_version,
      </if>
      <if test="importCount != null">
        import_count,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="createdUser != null">
        created_user,
      </if>
      <if test="createdUserName != null">
        created_user_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="importNum != null">
        #{importNum,jdbcType=VARCHAR},
      </if>
      <if test="deviceVersion != null">
        #{deviceVersion,jdbcType=VARCHAR},
      </if>
      <if test="importCount != null">
        #{importCount,jdbcType=INTEGER},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="createdUser != null">
        #{createdUser,jdbcType=VARCHAR},
      </if>
      <if test="createdUserName != null">
        #{createdUserName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.CardRelationImportInfoExample" resultType="java.lang.Long">
    select count(*) from card_relation_import_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update card_relation_import_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.importNum != null">
        import_num = #{record.importNum,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceVersion != null">
        device_version = #{record.deviceVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.importCount != null">
        import_count = #{record.importCount,jdbcType=INTEGER},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.createdUser != null">
        created_user = #{record.createdUser,jdbcType=VARCHAR},
      </if>
      <if test="record.createdUserName != null">
        created_user_name = #{record.createdUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update card_relation_import_info
    set id = #{record.id,jdbcType=VARCHAR},
      import_num = #{record.importNum,jdbcType=VARCHAR},
      device_version = #{record.deviceVersion,jdbcType=VARCHAR},
      import_count = #{record.importCount,jdbcType=INTEGER},
      be_id = #{record.beId,jdbcType=VARCHAR},
      created_user = #{record.createdUser,jdbcType=VARCHAR},
      created_user_name = #{record.createdUserName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.CardRelationImportInfo">
    update card_relation_import_info
    <set>
      <if test="importNum != null">
        import_num = #{importNum,jdbcType=VARCHAR},
      </if>
      <if test="deviceVersion != null">
        device_version = #{deviceVersion,jdbcType=VARCHAR},
      </if>
      <if test="importCount != null">
        import_count = #{importCount,jdbcType=INTEGER},
      </if>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="createdUser != null">
        created_user = #{createdUser,jdbcType=VARCHAR},
      </if>
      <if test="createdUserName != null">
        created_user_name = #{createdUserName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.CardRelationImportInfo">
    update card_relation_import_info
    set import_num = #{importNum,jdbcType=VARCHAR},
      device_version = #{deviceVersion,jdbcType=VARCHAR},
      import_count = #{importCount,jdbcType=INTEGER},
      be_id = #{beId,jdbcType=VARCHAR},
      created_user = #{createdUser,jdbcType=VARCHAR},
      created_user_name = #{createdUserName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into card_relation_import_info
    (id, import_num, device_version, import_count, be_id, created_user, created_user_name, 
      create_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.importNum,jdbcType=VARCHAR}, #{item.deviceVersion,jdbcType=VARCHAR}, 
        #{item.importCount,jdbcType=INTEGER}, #{item.beId,jdbcType=VARCHAR}, #{item.createdUser,jdbcType=VARCHAR}, 
        #{item.createdUserName,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into card_relation_import_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'import_num'.toString() == column.value">
          #{item.importNum,jdbcType=VARCHAR}
        </if>
        <if test="'device_version'.toString() == column.value">
          #{item.deviceVersion,jdbcType=VARCHAR}
        </if>
        <if test="'import_count'.toString() == column.value">
          #{item.importCount,jdbcType=INTEGER}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'created_user'.toString() == column.value">
          #{item.createdUser,jdbcType=VARCHAR}
        </if>
        <if test="'created_user_name'.toString() == column.value">
          #{item.createdUserName,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>