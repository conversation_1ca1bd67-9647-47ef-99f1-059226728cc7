<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.K3ProductMaterialMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.K3ProductMaterial">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Nov 08 17:40:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="atom_id" jdbcType="VARCHAR" property="atomId" />
    <result column="spu_code" jdbcType="VARCHAR" property="spuCode" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="atom_offering_name" jdbcType="VARCHAR" property="atomOfferingName" />
    <result column="atom_offering_code" jdbcType="VARCHAR" property="atomOfferingCode" />
    <result column="material_num" jdbcType="VARCHAR" property="materialNum" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="material_dept" jdbcType="VARCHAR" property="materialDept" />
    <result column="material_model" jdbcType="VARCHAR" property="materialModel" />
    <result column="material_pcode" jdbcType="VARCHAR" property="materialPcode" />
    <result column="contract_num" jdbcType="VARCHAR" property="contractNum" />
    <result column="contract_name" jdbcType="VARCHAR" property="contractName" />
    <result column="contract_prop" jdbcType="VARCHAR" property="contractProp" />
    <result column="contract_status" jdbcType="VARCHAR" property="contractStatus" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="seller_name" jdbcType="VARCHAR" property="sellerName" />
    <result column="seller_dept" jdbcType="VARCHAR" property="sellerDept" />
    <result column="money_type" jdbcType="VARCHAR" property="moneyType" />
    <result column="money_unit" jdbcType="VARCHAR" property="moneyUnit" />
    <result column="contract_price" jdbcType="VARCHAR" property="contractPrice" />
    <result column="expired_date" jdbcType="VARCHAR" property="expiredDate" />
    <result column="contract_effective" jdbcType="INTEGER" property="contractEffective" />
    <result column="material_count" jdbcType="DECIMAL" property="materialCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="contract_type" jdbcType="INTEGER" property="contractType" />
    <result column="material_settle_price" jdbcType="BIGINT" property="materialSettlePrice" />
    <result column="service_pack_id" jdbcType="VARCHAR" property="servicePackId" />
    <result column="service_pack_name" jdbcType="VARCHAR" property="servicePackName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Nov 08 17:40:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Nov 08 17:40:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Nov 08 17:40:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    id, atom_id, spu_code, sku_code, atom_offering_name, atom_offering_code, material_num, 
    material_name, material_dept, material_model, material_pcode, contract_num, contract_name, 
    contract_prop, contract_status, buyer_name, seller_name, seller_dept, money_type, 
    money_unit, contract_price, expired_date, contract_effective, material_count, create_time, 
    update_time, contract_type, material_settle_price, service_pack_id, service_pack_name
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.K3ProductMaterialExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Nov 08 17:40:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from k3_product_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Nov 08 17:40:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from k3_product_material
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Nov 08 17:40:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from k3_product_material
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.K3ProductMaterialExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Nov 08 17:40:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from k3_product_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.K3ProductMaterial">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Nov 08 17:40:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into k3_product_material (id, atom_id, spu_code, 
      sku_code, atom_offering_name, atom_offering_code, 
      material_num, material_name, material_dept, 
      material_model, material_pcode, contract_num, 
      contract_name, contract_prop, contract_status, 
      buyer_name, seller_name, seller_dept, 
      money_type, money_unit, contract_price, 
      expired_date, contract_effective, material_count, 
      create_time, update_time, contract_type, 
      material_settle_price, service_pack_id, service_pack_name
      )
    values (#{id,jdbcType=VARCHAR}, #{atomId,jdbcType=VARCHAR}, #{spuCode,jdbcType=VARCHAR}, 
      #{skuCode,jdbcType=VARCHAR}, #{atomOfferingName,jdbcType=VARCHAR}, #{atomOfferingCode,jdbcType=VARCHAR}, 
      #{materialNum,jdbcType=VARCHAR}, #{materialName,jdbcType=VARCHAR}, #{materialDept,jdbcType=VARCHAR}, 
      #{materialModel,jdbcType=VARCHAR}, #{materialPcode,jdbcType=VARCHAR}, #{contractNum,jdbcType=VARCHAR}, 
      #{contractName,jdbcType=VARCHAR}, #{contractProp,jdbcType=VARCHAR}, #{contractStatus,jdbcType=VARCHAR}, 
      #{buyerName,jdbcType=VARCHAR}, #{sellerName,jdbcType=VARCHAR}, #{sellerDept,jdbcType=VARCHAR}, 
      #{moneyType,jdbcType=VARCHAR}, #{moneyUnit,jdbcType=VARCHAR}, #{contractPrice,jdbcType=VARCHAR}, 
      #{expiredDate,jdbcType=VARCHAR}, #{contractEffective,jdbcType=INTEGER}, #{materialCount,jdbcType=DECIMAL}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{contractType,jdbcType=INTEGER}, 
      #{materialSettlePrice,jdbcType=BIGINT}, #{servicePackId,jdbcType=VARCHAR}, #{servicePackName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.K3ProductMaterial">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Nov 08 17:40:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into k3_product_material
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="atomId != null">
        atom_id,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="skuCode != null">
        sku_code,
      </if>
      <if test="atomOfferingName != null">
        atom_offering_name,
      </if>
      <if test="atomOfferingCode != null">
        atom_offering_code,
      </if>
      <if test="materialNum != null">
        material_num,
      </if>
      <if test="materialName != null">
        material_name,
      </if>
      <if test="materialDept != null">
        material_dept,
      </if>
      <if test="materialModel != null">
        material_model,
      </if>
      <if test="materialPcode != null">
        material_pcode,
      </if>
      <if test="contractNum != null">
        contract_num,
      </if>
      <if test="contractName != null">
        contract_name,
      </if>
      <if test="contractProp != null">
        contract_prop,
      </if>
      <if test="contractStatus != null">
        contract_status,
      </if>
      <if test="buyerName != null">
        buyer_name,
      </if>
      <if test="sellerName != null">
        seller_name,
      </if>
      <if test="sellerDept != null">
        seller_dept,
      </if>
      <if test="moneyType != null">
        money_type,
      </if>
      <if test="moneyUnit != null">
        money_unit,
      </if>
      <if test="contractPrice != null">
        contract_price,
      </if>
      <if test="expiredDate != null">
        expired_date,
      </if>
      <if test="contractEffective != null">
        contract_effective,
      </if>
      <if test="materialCount != null">
        material_count,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="contractType != null">
        contract_type,
      </if>
      <if test="materialSettlePrice != null">
        material_settle_price,
      </if>
      <if test="servicePackId != null">
        service_pack_id,
      </if>
      <if test="servicePackName != null">
        service_pack_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="atomId != null">
        #{atomId,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingName != null">
        #{atomOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingCode != null">
        #{atomOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="materialNum != null">
        #{materialNum,jdbcType=VARCHAR},
      </if>
      <if test="materialName != null">
        #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="materialDept != null">
        #{materialDept,jdbcType=VARCHAR},
      </if>
      <if test="materialModel != null">
        #{materialModel,jdbcType=VARCHAR},
      </if>
      <if test="materialPcode != null">
        #{materialPcode,jdbcType=VARCHAR},
      </if>
      <if test="contractNum != null">
        #{contractNum,jdbcType=VARCHAR},
      </if>
      <if test="contractName != null">
        #{contractName,jdbcType=VARCHAR},
      </if>
      <if test="contractProp != null">
        #{contractProp,jdbcType=VARCHAR},
      </if>
      <if test="contractStatus != null">
        #{contractStatus,jdbcType=VARCHAR},
      </if>
      <if test="buyerName != null">
        #{buyerName,jdbcType=VARCHAR},
      </if>
      <if test="sellerName != null">
        #{sellerName,jdbcType=VARCHAR},
      </if>
      <if test="sellerDept != null">
        #{sellerDept,jdbcType=VARCHAR},
      </if>
      <if test="moneyType != null">
        #{moneyType,jdbcType=VARCHAR},
      </if>
      <if test="moneyUnit != null">
        #{moneyUnit,jdbcType=VARCHAR},
      </if>
      <if test="contractPrice != null">
        #{contractPrice,jdbcType=VARCHAR},
      </if>
      <if test="expiredDate != null">
        #{expiredDate,jdbcType=VARCHAR},
      </if>
      <if test="contractEffective != null">
        #{contractEffective,jdbcType=INTEGER},
      </if>
      <if test="materialCount != null">
        #{materialCount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractType != null">
        #{contractType,jdbcType=INTEGER},
      </if>
      <if test="materialSettlePrice != null">
        #{materialSettlePrice,jdbcType=BIGINT},
      </if>
      <if test="servicePackId != null">
        #{servicePackId,jdbcType=VARCHAR},
      </if>
      <if test="servicePackName != null">
        #{servicePackName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.K3ProductMaterialExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Nov 08 17:40:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    select count(*) from k3_product_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Nov 08 17:40:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    update k3_product_material
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.atomId != null">
        atom_id = #{record.atomId,jdbcType=VARCHAR},
      </if>
      <if test="record.spuCode != null">
        spu_code = #{record.spuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.skuCode != null">
        sku_code = #{record.skuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOfferingName != null">
        atom_offering_name = #{record.atomOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOfferingCode != null">
        atom_offering_code = #{record.atomOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="record.materialNum != null">
        material_num = #{record.materialNum,jdbcType=VARCHAR},
      </if>
      <if test="record.materialName != null">
        material_name = #{record.materialName,jdbcType=VARCHAR},
      </if>
      <if test="record.materialDept != null">
        material_dept = #{record.materialDept,jdbcType=VARCHAR},
      </if>
      <if test="record.materialModel != null">
        material_model = #{record.materialModel,jdbcType=VARCHAR},
      </if>
      <if test="record.materialPcode != null">
        material_pcode = #{record.materialPcode,jdbcType=VARCHAR},
      </if>
      <if test="record.contractNum != null">
        contract_num = #{record.contractNum,jdbcType=VARCHAR},
      </if>
      <if test="record.contractName != null">
        contract_name = #{record.contractName,jdbcType=VARCHAR},
      </if>
      <if test="record.contractProp != null">
        contract_prop = #{record.contractProp,jdbcType=VARCHAR},
      </if>
      <if test="record.contractStatus != null">
        contract_status = #{record.contractStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerName != null">
        buyer_name = #{record.buyerName,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerName != null">
        seller_name = #{record.sellerName,jdbcType=VARCHAR},
      </if>
      <if test="record.sellerDept != null">
        seller_dept = #{record.sellerDept,jdbcType=VARCHAR},
      </if>
      <if test="record.moneyType != null">
        money_type = #{record.moneyType,jdbcType=VARCHAR},
      </if>
      <if test="record.moneyUnit != null">
        money_unit = #{record.moneyUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.contractPrice != null">
        contract_price = #{record.contractPrice,jdbcType=VARCHAR},
      </if>
      <if test="record.expiredDate != null">
        expired_date = #{record.expiredDate,jdbcType=VARCHAR},
      </if>
      <if test="record.contractEffective != null">
        contract_effective = #{record.contractEffective,jdbcType=INTEGER},
      </if>
      <if test="record.materialCount != null">
        material_count = #{record.materialCount,jdbcType=DECIMAL},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.contractType != null">
        contract_type = #{record.contractType,jdbcType=INTEGER},
      </if>
      <if test="record.materialSettlePrice != null">
        material_settle_price = #{record.materialSettlePrice,jdbcType=BIGINT},
      </if>
      <if test="record.servicePackId != null">
        service_pack_id = #{record.servicePackId,jdbcType=VARCHAR},
      </if>
      <if test="record.servicePackName != null">
        service_pack_name = #{record.servicePackName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Nov 08 17:40:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    update k3_product_material
    set id = #{record.id,jdbcType=VARCHAR},
      atom_id = #{record.atomId,jdbcType=VARCHAR},
      spu_code = #{record.spuCode,jdbcType=VARCHAR},
      sku_code = #{record.skuCode,jdbcType=VARCHAR},
      atom_offering_name = #{record.atomOfferingName,jdbcType=VARCHAR},
      atom_offering_code = #{record.atomOfferingCode,jdbcType=VARCHAR},
      material_num = #{record.materialNum,jdbcType=VARCHAR},
      material_name = #{record.materialName,jdbcType=VARCHAR},
      material_dept = #{record.materialDept,jdbcType=VARCHAR},
      material_model = #{record.materialModel,jdbcType=VARCHAR},
      material_pcode = #{record.materialPcode,jdbcType=VARCHAR},
      contract_num = #{record.contractNum,jdbcType=VARCHAR},
      contract_name = #{record.contractName,jdbcType=VARCHAR},
      contract_prop = #{record.contractProp,jdbcType=VARCHAR},
      contract_status = #{record.contractStatus,jdbcType=VARCHAR},
      buyer_name = #{record.buyerName,jdbcType=VARCHAR},
      seller_name = #{record.sellerName,jdbcType=VARCHAR},
      seller_dept = #{record.sellerDept,jdbcType=VARCHAR},
      money_type = #{record.moneyType,jdbcType=VARCHAR},
      money_unit = #{record.moneyUnit,jdbcType=VARCHAR},
      contract_price = #{record.contractPrice,jdbcType=VARCHAR},
      expired_date = #{record.expiredDate,jdbcType=VARCHAR},
      contract_effective = #{record.contractEffective,jdbcType=INTEGER},
      material_count = #{record.materialCount,jdbcType=DECIMAL},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      contract_type = #{record.contractType,jdbcType=INTEGER},
      material_settle_price = #{record.materialSettlePrice,jdbcType=BIGINT},
      service_pack_id = #{record.servicePackId,jdbcType=VARCHAR},
      service_pack_name = #{record.servicePackName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.K3ProductMaterial">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Nov 08 17:40:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    update k3_product_material
    <set>
      <if test="atomId != null">
        atom_id = #{atomId,jdbcType=VARCHAR},
      </if>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        sku_code = #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingName != null">
        atom_offering_name = #{atomOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="atomOfferingCode != null">
        atom_offering_code = #{atomOfferingCode,jdbcType=VARCHAR},
      </if>
      <if test="materialNum != null">
        material_num = #{materialNum,jdbcType=VARCHAR},
      </if>
      <if test="materialName != null">
        material_name = #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="materialDept != null">
        material_dept = #{materialDept,jdbcType=VARCHAR},
      </if>
      <if test="materialModel != null">
        material_model = #{materialModel,jdbcType=VARCHAR},
      </if>
      <if test="materialPcode != null">
        material_pcode = #{materialPcode,jdbcType=VARCHAR},
      </if>
      <if test="contractNum != null">
        contract_num = #{contractNum,jdbcType=VARCHAR},
      </if>
      <if test="contractName != null">
        contract_name = #{contractName,jdbcType=VARCHAR},
      </if>
      <if test="contractProp != null">
        contract_prop = #{contractProp,jdbcType=VARCHAR},
      </if>
      <if test="contractStatus != null">
        contract_status = #{contractStatus,jdbcType=VARCHAR},
      </if>
      <if test="buyerName != null">
        buyer_name = #{buyerName,jdbcType=VARCHAR},
      </if>
      <if test="sellerName != null">
        seller_name = #{sellerName,jdbcType=VARCHAR},
      </if>
      <if test="sellerDept != null">
        seller_dept = #{sellerDept,jdbcType=VARCHAR},
      </if>
      <if test="moneyType != null">
        money_type = #{moneyType,jdbcType=VARCHAR},
      </if>
      <if test="moneyUnit != null">
        money_unit = #{moneyUnit,jdbcType=VARCHAR},
      </if>
      <if test="contractPrice != null">
        contract_price = #{contractPrice,jdbcType=VARCHAR},
      </if>
      <if test="expiredDate != null">
        expired_date = #{expiredDate,jdbcType=VARCHAR},
      </if>
      <if test="contractEffective != null">
        contract_effective = #{contractEffective,jdbcType=INTEGER},
      </if>
      <if test="materialCount != null">
        material_count = #{materialCount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractType != null">
        contract_type = #{contractType,jdbcType=INTEGER},
      </if>
      <if test="materialSettlePrice != null">
        material_settle_price = #{materialSettlePrice,jdbcType=BIGINT},
      </if>
      <if test="servicePackId != null">
        service_pack_id = #{servicePackId,jdbcType=VARCHAR},
      </if>
      <if test="servicePackName != null">
        service_pack_name = #{servicePackName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.K3ProductMaterial">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Nov 08 17:40:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    update k3_product_material
    set atom_id = #{atomId,jdbcType=VARCHAR},
      spu_code = #{spuCode,jdbcType=VARCHAR},
      sku_code = #{skuCode,jdbcType=VARCHAR},
      atom_offering_name = #{atomOfferingName,jdbcType=VARCHAR},
      atom_offering_code = #{atomOfferingCode,jdbcType=VARCHAR},
      material_num = #{materialNum,jdbcType=VARCHAR},
      material_name = #{materialName,jdbcType=VARCHAR},
      material_dept = #{materialDept,jdbcType=VARCHAR},
      material_model = #{materialModel,jdbcType=VARCHAR},
      material_pcode = #{materialPcode,jdbcType=VARCHAR},
      contract_num = #{contractNum,jdbcType=VARCHAR},
      contract_name = #{contractName,jdbcType=VARCHAR},
      contract_prop = #{contractProp,jdbcType=VARCHAR},
      contract_status = #{contractStatus,jdbcType=VARCHAR},
      buyer_name = #{buyerName,jdbcType=VARCHAR},
      seller_name = #{sellerName,jdbcType=VARCHAR},
      seller_dept = #{sellerDept,jdbcType=VARCHAR},
      money_type = #{moneyType,jdbcType=VARCHAR},
      money_unit = #{moneyUnit,jdbcType=VARCHAR},
      contract_price = #{contractPrice,jdbcType=VARCHAR},
      expired_date = #{expiredDate,jdbcType=VARCHAR},
      contract_effective = #{contractEffective,jdbcType=INTEGER},
      material_count = #{materialCount,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      contract_type = #{contractType,jdbcType=INTEGER},
      material_settle_price = #{materialSettlePrice,jdbcType=BIGINT},
      service_pack_id = #{servicePackId,jdbcType=VARCHAR},
      service_pack_name = #{servicePackName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Nov 08 17:40:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into k3_product_material
    (id, atom_id, spu_code, sku_code, atom_offering_name, atom_offering_code, material_num, 
      material_name, material_dept, material_model, material_pcode, contract_num, contract_name, 
      contract_prop, contract_status, buyer_name, seller_name, seller_dept, money_type, 
      money_unit, contract_price, expired_date, contract_effective, material_count, create_time, 
      update_time, contract_type, material_settle_price, service_pack_id, service_pack_name
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.atomId,jdbcType=VARCHAR}, #{item.spuCode,jdbcType=VARCHAR}, 
        #{item.skuCode,jdbcType=VARCHAR}, #{item.atomOfferingName,jdbcType=VARCHAR}, #{item.atomOfferingCode,jdbcType=VARCHAR}, 
        #{item.materialNum,jdbcType=VARCHAR}, #{item.materialName,jdbcType=VARCHAR}, #{item.materialDept,jdbcType=VARCHAR}, 
        #{item.materialModel,jdbcType=VARCHAR}, #{item.materialPcode,jdbcType=VARCHAR}, 
        #{item.contractNum,jdbcType=VARCHAR}, #{item.contractName,jdbcType=VARCHAR}, #{item.contractProp,jdbcType=VARCHAR}, 
        #{item.contractStatus,jdbcType=VARCHAR}, #{item.buyerName,jdbcType=VARCHAR}, #{item.sellerName,jdbcType=VARCHAR}, 
        #{item.sellerDept,jdbcType=VARCHAR}, #{item.moneyType,jdbcType=VARCHAR}, #{item.moneyUnit,jdbcType=VARCHAR}, 
        #{item.contractPrice,jdbcType=VARCHAR}, #{item.expiredDate,jdbcType=VARCHAR}, #{item.contractEffective,jdbcType=INTEGER}, 
        #{item.materialCount,jdbcType=DECIMAL}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.contractType,jdbcType=INTEGER}, #{item.materialSettlePrice,jdbcType=BIGINT}, 
        #{item.servicePackId,jdbcType=VARCHAR}, #{item.servicePackName,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Nov 08 17:40:28 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into k3_product_material (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'atom_id'.toString() == column.value">
          #{item.atomId,jdbcType=VARCHAR}
        </if>
        <if test="'spu_code'.toString() == column.value">
          #{item.spuCode,jdbcType=VARCHAR}
        </if>
        <if test="'sku_code'.toString() == column.value">
          #{item.skuCode,jdbcType=VARCHAR}
        </if>
        <if test="'atom_offering_name'.toString() == column.value">
          #{item.atomOfferingName,jdbcType=VARCHAR}
        </if>
        <if test="'atom_offering_code'.toString() == column.value">
          #{item.atomOfferingCode,jdbcType=VARCHAR}
        </if>
        <if test="'material_num'.toString() == column.value">
          #{item.materialNum,jdbcType=VARCHAR}
        </if>
        <if test="'material_name'.toString() == column.value">
          #{item.materialName,jdbcType=VARCHAR}
        </if>
        <if test="'material_dept'.toString() == column.value">
          #{item.materialDept,jdbcType=VARCHAR}
        </if>
        <if test="'material_model'.toString() == column.value">
          #{item.materialModel,jdbcType=VARCHAR}
        </if>
        <if test="'material_pcode'.toString() == column.value">
          #{item.materialPcode,jdbcType=VARCHAR}
        </if>
        <if test="'contract_num'.toString() == column.value">
          #{item.contractNum,jdbcType=VARCHAR}
        </if>
        <if test="'contract_name'.toString() == column.value">
          #{item.contractName,jdbcType=VARCHAR}
        </if>
        <if test="'contract_prop'.toString() == column.value">
          #{item.contractProp,jdbcType=VARCHAR}
        </if>
        <if test="'contract_status'.toString() == column.value">
          #{item.contractStatus,jdbcType=VARCHAR}
        </if>
        <if test="'buyer_name'.toString() == column.value">
          #{item.buyerName,jdbcType=VARCHAR}
        </if>
        <if test="'seller_name'.toString() == column.value">
          #{item.sellerName,jdbcType=VARCHAR}
        </if>
        <if test="'seller_dept'.toString() == column.value">
          #{item.sellerDept,jdbcType=VARCHAR}
        </if>
        <if test="'money_type'.toString() == column.value">
          #{item.moneyType,jdbcType=VARCHAR}
        </if>
        <if test="'money_unit'.toString() == column.value">
          #{item.moneyUnit,jdbcType=VARCHAR}
        </if>
        <if test="'contract_price'.toString() == column.value">
          #{item.contractPrice,jdbcType=VARCHAR}
        </if>
        <if test="'expired_date'.toString() == column.value">
          #{item.expiredDate,jdbcType=VARCHAR}
        </if>
        <if test="'contract_effective'.toString() == column.value">
          #{item.contractEffective,jdbcType=INTEGER}
        </if>
        <if test="'material_count'.toString() == column.value">
          #{item.materialCount,jdbcType=DECIMAL}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'contract_type'.toString() == column.value">
          #{item.contractType,jdbcType=INTEGER}
        </if>
        <if test="'material_settle_price'.toString() == column.value">
          #{item.materialSettlePrice,jdbcType=BIGINT}
        </if>
        <if test="'service_pack_id'.toString() == column.value">
          #{item.servicePackId,jdbcType=VARCHAR}
        </if>
        <if test="'service_pack_name'.toString() == column.value">
          #{item.servicePackName,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>