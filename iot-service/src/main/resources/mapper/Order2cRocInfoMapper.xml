<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.Order2cRocInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.Order2cRocInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 10:34:21 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="refund_order_id" jdbcType="VARCHAR" property="refundOrderId" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="atom_order_id" jdbcType="VARCHAR" property="atomOrderId" />
    <result column="cooperator_id" jdbcType="VARCHAR" property="cooperatorId" />
    <result column="finish_cooperator_id" jdbcType="VARCHAR" property="finishCooperatorId" />
    <result column="refunds_type" jdbcType="VARCHAR" property="refundsType" />
    <result column="refunds_number" jdbcType="VARCHAR" property="refundsNumber" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="picture" jdbcType="VARCHAR" property="picture" />
    <result column="pictures_inner_url" jdbcType="VARCHAR" property="picturesInnerUrl" />
    <result column="pictures_outer_url" jdbcType="VARCHAR" property="picturesOuterUrl" />
    <result column="original_status" jdbcType="INTEGER" property="originalStatus" />
    <result column="inner_status" jdbcType="INTEGER" property="innerStatus" />
    <result column="audit_id" jdbcType="VARCHAR" property="auditId" />
    <result column="audit_result" jdbcType="VARCHAR" property="auditResult" />
    <result column="audit_result_reason" jdbcType="VARCHAR" property="auditResultReason" />
    <result column="contactInfo" jdbcType="VARCHAR" property="contactinfo" />
    <result column="receipt_id" jdbcType="VARCHAR" property="receiptId" />
    <result column="receipt_result" jdbcType="VARCHAR" property="receiptResult" />
    <result column="receipt_result_reason" jdbcType="VARCHAR" property="receiptResultReason" />
    <result column="reminder_count" jdbcType="INTEGER" property="reminderCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 10:34:21 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 10:34:21 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 10:34:21 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, refund_order_id, order_id, atom_order_id, cooperator_id, finish_cooperator_id, 
    refunds_type, refunds_number, reason, remark, picture, pictures_inner_url, pictures_outer_url, 
    original_status, inner_status, audit_id, audit_result, audit_result_reason, contactInfo, 
    receipt_id, receipt_result, receipt_result_reason, reminder_count, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cRocInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 10:34:21 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_2c_roc_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 10:34:21 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from order_2c_roc_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 10:34:21 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from order_2c_roc_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cRocInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 10:34:21 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from order_2c_roc_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.Order2cRocInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 10:34:21 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_roc_info (id, refund_order_id, order_id, 
      atom_order_id, cooperator_id, finish_cooperator_id, 
      refunds_type, refunds_number, reason, 
      remark, picture, pictures_inner_url, 
      pictures_outer_url, original_status, inner_status, 
      audit_id, audit_result, audit_result_reason, 
      contactInfo, receipt_id, receipt_result, 
      receipt_result_reason, reminder_count, create_time, 
      update_time)
    values (#{id,jdbcType=VARCHAR}, #{refundOrderId,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, 
      #{atomOrderId,jdbcType=VARCHAR}, #{cooperatorId,jdbcType=VARCHAR}, #{finishCooperatorId,jdbcType=VARCHAR}, 
      #{refundsType,jdbcType=VARCHAR}, #{refundsNumber,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{picture,jdbcType=VARCHAR}, #{picturesInnerUrl,jdbcType=VARCHAR}, 
      #{picturesOuterUrl,jdbcType=VARCHAR}, #{originalStatus,jdbcType=INTEGER}, #{innerStatus,jdbcType=INTEGER}, 
      #{auditId,jdbcType=VARCHAR}, #{auditResult,jdbcType=VARCHAR}, #{auditResultReason,jdbcType=VARCHAR}, 
      #{contactinfo,jdbcType=VARCHAR}, #{receiptId,jdbcType=VARCHAR}, #{receiptResult,jdbcType=VARCHAR}, 
      #{receiptResultReason,jdbcType=VARCHAR}, #{reminderCount,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.Order2cRocInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 10:34:21 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_roc_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="refundOrderId != null">
        refund_order_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="atomOrderId != null">
        atom_order_id,
      </if>
      <if test="cooperatorId != null">
        cooperator_id,
      </if>
      <if test="finishCooperatorId != null">
        finish_cooperator_id,
      </if>
      <if test="refundsType != null">
        refunds_type,
      </if>
      <if test="refundsNumber != null">
        refunds_number,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="picture != null">
        picture,
      </if>
      <if test="picturesInnerUrl != null">
        pictures_inner_url,
      </if>
      <if test="picturesOuterUrl != null">
        pictures_outer_url,
      </if>
      <if test="originalStatus != null">
        original_status,
      </if>
      <if test="innerStatus != null">
        inner_status,
      </if>
      <if test="auditId != null">
        audit_id,
      </if>
      <if test="auditResult != null">
        audit_result,
      </if>
      <if test="auditResultReason != null">
        audit_result_reason,
      </if>
      <if test="contactinfo != null">
        contactInfo,
      </if>
      <if test="receiptId != null">
        receipt_id,
      </if>
      <if test="receiptResult != null">
        receipt_result,
      </if>
      <if test="receiptResultReason != null">
        receipt_result_reason,
      </if>
      <if test="reminderCount != null">
        reminder_count,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="refundOrderId != null">
        #{refundOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="atomOrderId != null">
        #{atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="cooperatorId != null">
        #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="finishCooperatorId != null">
        #{finishCooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="refundsType != null">
        #{refundsType,jdbcType=VARCHAR},
      </if>
      <if test="refundsNumber != null">
        #{refundsNumber,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="picture != null">
        #{picture,jdbcType=VARCHAR},
      </if>
      <if test="picturesInnerUrl != null">
        #{picturesInnerUrl,jdbcType=VARCHAR},
      </if>
      <if test="picturesOuterUrl != null">
        #{picturesOuterUrl,jdbcType=VARCHAR},
      </if>
      <if test="originalStatus != null">
        #{originalStatus,jdbcType=INTEGER},
      </if>
      <if test="innerStatus != null">
        #{innerStatus,jdbcType=INTEGER},
      </if>
      <if test="auditId != null">
        #{auditId,jdbcType=VARCHAR},
      </if>
      <if test="auditResult != null">
        #{auditResult,jdbcType=VARCHAR},
      </if>
      <if test="auditResultReason != null">
        #{auditResultReason,jdbcType=VARCHAR},
      </if>
      <if test="contactinfo != null">
        #{contactinfo,jdbcType=VARCHAR},
      </if>
      <if test="receiptId != null">
        #{receiptId,jdbcType=VARCHAR},
      </if>
      <if test="receiptResult != null">
        #{receiptResult,jdbcType=VARCHAR},
      </if>
      <if test="receiptResultReason != null">
        #{receiptResultReason,jdbcType=VARCHAR},
      </if>
      <if test="reminderCount != null">
        #{reminderCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cRocInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 10:34:21 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from order_2c_roc_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 10:34:21 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_2c_roc_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.refundOrderId != null">
        refund_order_id = #{record.refundOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.atomOrderId != null">
        atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.cooperatorId != null">
        cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.finishCooperatorId != null">
        finish_cooperator_id = #{record.finishCooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.refundsType != null">
        refunds_type = #{record.refundsType,jdbcType=VARCHAR},
      </if>
      <if test="record.refundsNumber != null">
        refunds_number = #{record.refundsNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.picture != null">
        picture = #{record.picture,jdbcType=VARCHAR},
      </if>
      <if test="record.picturesInnerUrl != null">
        pictures_inner_url = #{record.picturesInnerUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.picturesOuterUrl != null">
        pictures_outer_url = #{record.picturesOuterUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.originalStatus != null">
        original_status = #{record.originalStatus,jdbcType=INTEGER},
      </if>
      <if test="record.innerStatus != null">
        inner_status = #{record.innerStatus,jdbcType=INTEGER},
      </if>
      <if test="record.auditId != null">
        audit_id = #{record.auditId,jdbcType=VARCHAR},
      </if>
      <if test="record.auditResult != null">
        audit_result = #{record.auditResult,jdbcType=VARCHAR},
      </if>
      <if test="record.auditResultReason != null">
        audit_result_reason = #{record.auditResultReason,jdbcType=VARCHAR},
      </if>
      <if test="record.contactinfo != null">
        contactInfo = #{record.contactinfo,jdbcType=VARCHAR},
      </if>
      <if test="record.receiptId != null">
        receipt_id = #{record.receiptId,jdbcType=VARCHAR},
      </if>
      <if test="record.receiptResult != null">
        receipt_result = #{record.receiptResult,jdbcType=VARCHAR},
      </if>
      <if test="record.receiptResultReason != null">
        receipt_result_reason = #{record.receiptResultReason,jdbcType=VARCHAR},
      </if>
      <if test="record.reminderCount != null">
        reminder_count = #{record.reminderCount,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 10:34:21 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_2c_roc_info
    set id = #{record.id,jdbcType=VARCHAR},
      refund_order_id = #{record.refundOrderId,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      cooperator_id = #{record.cooperatorId,jdbcType=VARCHAR},
      finish_cooperator_id = #{record.finishCooperatorId,jdbcType=VARCHAR},
      refunds_type = #{record.refundsType,jdbcType=VARCHAR},
      refunds_number = #{record.refundsNumber,jdbcType=VARCHAR},
      reason = #{record.reason,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      picture = #{record.picture,jdbcType=VARCHAR},
      pictures_inner_url = #{record.picturesInnerUrl,jdbcType=VARCHAR},
      pictures_outer_url = #{record.picturesOuterUrl,jdbcType=VARCHAR},
      original_status = #{record.originalStatus,jdbcType=INTEGER},
      inner_status = #{record.innerStatus,jdbcType=INTEGER},
      audit_id = #{record.auditId,jdbcType=VARCHAR},
      audit_result = #{record.auditResult,jdbcType=VARCHAR},
      audit_result_reason = #{record.auditResultReason,jdbcType=VARCHAR},
      contactInfo = #{record.contactinfo,jdbcType=VARCHAR},
      receipt_id = #{record.receiptId,jdbcType=VARCHAR},
      receipt_result = #{record.receiptResult,jdbcType=VARCHAR},
      receipt_result_reason = #{record.receiptResultReason,jdbcType=VARCHAR},
      reminder_count = #{record.reminderCount,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.Order2cRocInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 10:34:21 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_2c_roc_info
    <set>
      <if test="refundOrderId != null">
        refund_order_id = #{refundOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="atomOrderId != null">
        atom_order_id = #{atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="cooperatorId != null">
        cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="finishCooperatorId != null">
        finish_cooperator_id = #{finishCooperatorId,jdbcType=VARCHAR},
      </if>
      <if test="refundsType != null">
        refunds_type = #{refundsType,jdbcType=VARCHAR},
      </if>
      <if test="refundsNumber != null">
        refunds_number = #{refundsNumber,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="picture != null">
        picture = #{picture,jdbcType=VARCHAR},
      </if>
      <if test="picturesInnerUrl != null">
        pictures_inner_url = #{picturesInnerUrl,jdbcType=VARCHAR},
      </if>
      <if test="picturesOuterUrl != null">
        pictures_outer_url = #{picturesOuterUrl,jdbcType=VARCHAR},
      </if>
      <if test="originalStatus != null">
        original_status = #{originalStatus,jdbcType=INTEGER},
      </if>
      <if test="innerStatus != null">
        inner_status = #{innerStatus,jdbcType=INTEGER},
      </if>
      <if test="auditId != null">
        audit_id = #{auditId,jdbcType=VARCHAR},
      </if>
      <if test="auditResult != null">
        audit_result = #{auditResult,jdbcType=VARCHAR},
      </if>
      <if test="auditResultReason != null">
        audit_result_reason = #{auditResultReason,jdbcType=VARCHAR},
      </if>
      <if test="contactinfo != null">
        contactInfo = #{contactinfo,jdbcType=VARCHAR},
      </if>
      <if test="receiptId != null">
        receipt_id = #{receiptId,jdbcType=VARCHAR},
      </if>
      <if test="receiptResult != null">
        receipt_result = #{receiptResult,jdbcType=VARCHAR},
      </if>
      <if test="receiptResultReason != null">
        receipt_result_reason = #{receiptResultReason,jdbcType=VARCHAR},
      </if>
      <if test="reminderCount != null">
        reminder_count = #{reminderCount,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.Order2cRocInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 10:34:21 CST 2025. by MyBatis Generator, do not modify.
    -->
    update order_2c_roc_info
    set refund_order_id = #{refundOrderId,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      atom_order_id = #{atomOrderId,jdbcType=VARCHAR},
      cooperator_id = #{cooperatorId,jdbcType=VARCHAR},
      finish_cooperator_id = #{finishCooperatorId,jdbcType=VARCHAR},
      refunds_type = #{refundsType,jdbcType=VARCHAR},
      refunds_number = #{refundsNumber,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      picture = #{picture,jdbcType=VARCHAR},
      pictures_inner_url = #{picturesInnerUrl,jdbcType=VARCHAR},
      pictures_outer_url = #{picturesOuterUrl,jdbcType=VARCHAR},
      original_status = #{originalStatus,jdbcType=INTEGER},
      inner_status = #{innerStatus,jdbcType=INTEGER},
      audit_id = #{auditId,jdbcType=VARCHAR},
      audit_result = #{auditResult,jdbcType=VARCHAR},
      audit_result_reason = #{auditResultReason,jdbcType=VARCHAR},
      contactInfo = #{contactinfo,jdbcType=VARCHAR},
      receipt_id = #{receiptId,jdbcType=VARCHAR},
      receipt_result = #{receiptResult,jdbcType=VARCHAR},
      receipt_result_reason = #{receiptResultReason,jdbcType=VARCHAR},
      reminder_count = #{reminderCount,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 10:34:21 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_roc_info
    (id, refund_order_id, order_id, atom_order_id, cooperator_id, finish_cooperator_id, 
      refunds_type, refunds_number, reason, remark, picture, pictures_inner_url, pictures_outer_url, 
      original_status, inner_status, audit_id, audit_result, audit_result_reason, contactInfo, 
      receipt_id, receipt_result, receipt_result_reason, reminder_count, create_time, 
      update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.refundOrderId,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR}, 
        #{item.atomOrderId,jdbcType=VARCHAR}, #{item.cooperatorId,jdbcType=VARCHAR}, #{item.finishCooperatorId,jdbcType=VARCHAR}, 
        #{item.refundsType,jdbcType=VARCHAR}, #{item.refundsNumber,jdbcType=VARCHAR}, #{item.reason,jdbcType=VARCHAR}, 
        #{item.remark,jdbcType=VARCHAR}, #{item.picture,jdbcType=VARCHAR}, #{item.picturesInnerUrl,jdbcType=VARCHAR}, 
        #{item.picturesOuterUrl,jdbcType=VARCHAR}, #{item.originalStatus,jdbcType=INTEGER}, 
        #{item.innerStatus,jdbcType=INTEGER}, #{item.auditId,jdbcType=VARCHAR}, #{item.auditResult,jdbcType=VARCHAR}, 
        #{item.auditResultReason,jdbcType=VARCHAR}, #{item.contactinfo,jdbcType=VARCHAR}, 
        #{item.receiptId,jdbcType=VARCHAR}, #{item.receiptResult,jdbcType=VARCHAR}, #{item.receiptResultReason,jdbcType=VARCHAR}, 
        #{item.reminderCount,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 24 10:34:21 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into order_2c_roc_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'refund_order_id'.toString() == column.value">
          #{item.refundOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'atom_order_id'.toString() == column.value">
          #{item.atomOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'cooperator_id'.toString() == column.value">
          #{item.cooperatorId,jdbcType=VARCHAR}
        </if>
        <if test="'finish_cooperator_id'.toString() == column.value">
          #{item.finishCooperatorId,jdbcType=VARCHAR}
        </if>
        <if test="'refunds_type'.toString() == column.value">
          #{item.refundsType,jdbcType=VARCHAR}
        </if>
        <if test="'refunds_number'.toString() == column.value">
          #{item.refundsNumber,jdbcType=VARCHAR}
        </if>
        <if test="'reason'.toString() == column.value">
          #{item.reason,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'picture'.toString() == column.value">
          #{item.picture,jdbcType=VARCHAR}
        </if>
        <if test="'pictures_inner_url'.toString() == column.value">
          #{item.picturesInnerUrl,jdbcType=VARCHAR}
        </if>
        <if test="'pictures_outer_url'.toString() == column.value">
          #{item.picturesOuterUrl,jdbcType=VARCHAR}
        </if>
        <if test="'original_status'.toString() == column.value">
          #{item.originalStatus,jdbcType=INTEGER}
        </if>
        <if test="'inner_status'.toString() == column.value">
          #{item.innerStatus,jdbcType=INTEGER}
        </if>
        <if test="'audit_id'.toString() == column.value">
          #{item.auditId,jdbcType=VARCHAR}
        </if>
        <if test="'audit_result'.toString() == column.value">
          #{item.auditResult,jdbcType=VARCHAR}
        </if>
        <if test="'audit_result_reason'.toString() == column.value">
          #{item.auditResultReason,jdbcType=VARCHAR}
        </if>
        <if test="'contactInfo'.toString() == column.value">
          #{item.contactinfo,jdbcType=VARCHAR}
        </if>
        <if test="'receipt_id'.toString() == column.value">
          #{item.receiptId,jdbcType=VARCHAR}
        </if>
        <if test="'receipt_result'.toString() == column.value">
          #{item.receiptResult,jdbcType=VARCHAR}
        </if>
        <if test="'receipt_result_reason'.toString() == column.value">
          #{item.receiptResultReason,jdbcType=VARCHAR}
        </if>
        <if test="'reminder_count'.toString() == column.value">
          #{item.reminderCount,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>