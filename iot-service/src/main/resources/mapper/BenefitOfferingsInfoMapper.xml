<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.BenefitOfferingsInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.BenefitOfferingsInfo">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="goods_id" jdbcType="VARCHAR" property="goodsId" />
    <result column="iot_mall_offering_name" jdbcType="VARCHAR" property="iotMallOfferingName" />
    <result column="iot_mall_offering_type" jdbcType="VARCHAR" property="iotMallOfferingType" />
    <result column="iot_release_area_id" jdbcType="VARCHAR" property="iotReleaseAreaId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, goods_id, iot_mall_offering_name, iot_mall_offering_type, iot_release_area_id, 
    create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.BenefitOfferingsInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from benefit_offerings_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from benefit_offerings_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from benefit_offerings_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.BenefitOfferingsInfoExample">
    delete from benefit_offerings_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.BenefitOfferingsInfo">
    insert into benefit_offerings_info (id, goods_id, iot_mall_offering_name, 
      iot_mall_offering_type, iot_release_area_id, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{goodsId,jdbcType=VARCHAR}, #{iotMallOfferingName,jdbcType=VARCHAR}, 
      #{iotMallOfferingType,jdbcType=VARCHAR}, #{iotReleaseAreaId,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.BenefitOfferingsInfo">
    insert into benefit_offerings_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="goodsId != null">
        goods_id,
      </if>
      <if test="iotMallOfferingName != null">
        iot_mall_offering_name,
      </if>
      <if test="iotMallOfferingType != null">
        iot_mall_offering_type,
      </if>
      <if test="iotReleaseAreaId != null">
        iot_release_area_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=VARCHAR},
      </if>
      <if test="iotMallOfferingName != null">
        #{iotMallOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="iotMallOfferingType != null">
        #{iotMallOfferingType,jdbcType=VARCHAR},
      </if>
      <if test="iotReleaseAreaId != null">
        #{iotReleaseAreaId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.BenefitOfferingsInfoExample" resultType="java.lang.Long">
    select count(*) from benefit_offerings_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update benefit_offerings_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsId != null">
        goods_id = #{record.goodsId,jdbcType=VARCHAR},
      </if>
      <if test="record.iotMallOfferingName != null">
        iot_mall_offering_name = #{record.iotMallOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="record.iotMallOfferingType != null">
        iot_mall_offering_type = #{record.iotMallOfferingType,jdbcType=VARCHAR},
      </if>
      <if test="record.iotReleaseAreaId != null">
        iot_release_area_id = #{record.iotReleaseAreaId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update benefit_offerings_info
    set id = #{record.id,jdbcType=VARCHAR},
      goods_id = #{record.goodsId,jdbcType=VARCHAR},
      iot_mall_offering_name = #{record.iotMallOfferingName,jdbcType=VARCHAR},
      iot_mall_offering_type = #{record.iotMallOfferingType,jdbcType=VARCHAR},
      iot_release_area_id = #{record.iotReleaseAreaId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.BenefitOfferingsInfo">
    update benefit_offerings_info
    <set>
      <if test="goodsId != null">
        goods_id = #{goodsId,jdbcType=VARCHAR},
      </if>
      <if test="iotMallOfferingName != null">
        iot_mall_offering_name = #{iotMallOfferingName,jdbcType=VARCHAR},
      </if>
      <if test="iotMallOfferingType != null">
        iot_mall_offering_type = #{iotMallOfferingType,jdbcType=VARCHAR},
      </if>
      <if test="iotReleaseAreaId != null">
        iot_release_area_id = #{iotReleaseAreaId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.BenefitOfferingsInfo">
    update benefit_offerings_info
    set goods_id = #{goodsId,jdbcType=VARCHAR},
      iot_mall_offering_name = #{iotMallOfferingName,jdbcType=VARCHAR},
      iot_mall_offering_type = #{iotMallOfferingType,jdbcType=VARCHAR},
      iot_release_area_id = #{iotReleaseAreaId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into benefit_offerings_info
    (id, goods_id, iot_mall_offering_name, iot_mall_offering_type, iot_release_area_id, 
      create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.goodsId,jdbcType=VARCHAR}, #{item.iotMallOfferingName,jdbcType=VARCHAR}, 
        #{item.iotMallOfferingType,jdbcType=VARCHAR}, #{item.iotReleaseAreaId,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into benefit_offerings_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'goods_id'.toString() == column.value">
          #{item.goodsId,jdbcType=VARCHAR}
        </if>
        <if test="'iot_mall_offering_name'.toString() == column.value">
          #{item.iotMallOfferingName,jdbcType=VARCHAR}
        </if>
        <if test="'iot_mall_offering_type'.toString() == column.value">
          #{item.iotMallOfferingType,jdbcType=VARCHAR}
        </if>
        <if test="'iot_release_area_id'.toString() == column.value">
          #{item.iotReleaseAreaId,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>