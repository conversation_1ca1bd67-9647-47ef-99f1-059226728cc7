<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.NewProductRequestOnlineOfflineAfterSaleInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineAfterSaleInfo">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="new_product_request_id" jdbcType="VARCHAR" property="newProductRequestId" />
    <result column="combo_info_id" jdbcType="VARCHAR" property="comboInfoId" />
    <result column="store_order_handler_user_id" jdbcType="VARCHAR" property="storeOrderHandlerUserId" />
    <result column="store_order_handler_user_name" jdbcType="VARCHAR" property="storeOrderHandlerUserName" />
    <result column="store_order_handler_user_phone" jdbcType="VARCHAR" property="storeOrderHandlerUserPhone" />
    <result column="store_order_handler_user_email" jdbcType="VARCHAR" property="storeOrderHandlerUserEmail" />
    <result column="store_order_handler_sub_user_id" jdbcType="VARCHAR" property="storeOrderHandlerSubUserId" />
    <result column="store_order_handler_sub_user_name" jdbcType="VARCHAR" property="storeOrderHandlerSubUserName" />
    <result column="store_order_handler_sub_user_phone" jdbcType="VARCHAR" property="storeOrderHandlerSubUserPhone" />
    <result column="store_order_handler_sub_user_email" jdbcType="VARCHAR" property="storeOrderHandlerSubUserEmail" />
    <result column="pre_sale_manager_user_name" jdbcType="VARCHAR" property="preSaleManagerUserName" />
    <result column="pre_sale_manager_user_phone" jdbcType="VARCHAR" property="preSaleManagerUserPhone" />
    <result column="pre_sale_manager_user_email" jdbcType="VARCHAR" property="preSaleManagerUserEmail" />
    <result column="product_ship_user_name" jdbcType="VARCHAR" property="productShipUserName" />
    <result column="product_ship_user_phone" jdbcType="VARCHAR" property="productShipUserPhone" />
    <result column="product_ship_user_email" jdbcType="VARCHAR" property="productShipUserEmail" />
    <result column="product_install_user_name" jdbcType="VARCHAR" property="productInstallUserName" />
    <result column="product_install_user_phone" jdbcType="VARCHAR" property="productInstallUserPhone" />
    <result column="product_install_user_email" jdbcType="VARCHAR" property="productInstallUserEmail" />
    <result column="things_card_user_name" jdbcType="VARCHAR" property="thingsCardUserName" />
    <result column="things_card_user_phone" jdbcType="VARCHAR" property="thingsCardUserPhone" />
    <result column="things_card_user_email" jdbcType="VARCHAR" property="thingsCardUserEmail" />
    <result column="software_authority_user_name" jdbcType="VARCHAR" property="softwareAuthorityUserName" />
    <result column="software_authority_user_phone" jdbcType="VARCHAR" property="softwareAuthorityUserPhone" />
    <result column="software_authority_user_email" jdbcType="VARCHAR" property="softwareAuthorityUserEmail" />
    <result column="after_sale_user_name" jdbcType="VARCHAR" property="afterSaleUserName" />
    <result column="after_sale_user_phone" jdbcType="VARCHAR" property="afterSaleUserPhone" />
    <result column="after_sale_user_email" jdbcType="VARCHAR" property="afterSaleUserEmail" />
    <result column="after_sale_detail" jdbcType="VARCHAR" property="afterSaleDetail" />
    <result column="product_maintain_info" jdbcType="VARCHAR" property="productMaintainInfo" />
    <result column="product_sale_return_info" jdbcType="VARCHAR" property="productSaleReturnInfo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, new_product_request_id, combo_info_id, store_order_handler_user_id, store_order_handler_user_name, 
    store_order_handler_user_phone, store_order_handler_user_email, store_order_handler_sub_user_id, 
    store_order_handler_sub_user_name, store_order_handler_sub_user_phone, store_order_handler_sub_user_email, 
    pre_sale_manager_user_name, pre_sale_manager_user_phone, pre_sale_manager_user_email, 
    product_ship_user_name, product_ship_user_phone, product_ship_user_email, product_install_user_name, 
    product_install_user_phone, product_install_user_email, things_card_user_name, things_card_user_phone, 
    things_card_user_email, software_authority_user_name, software_authority_user_phone, 
    software_authority_user_email, after_sale_user_name, after_sale_user_phone, after_sale_user_email, 
    after_sale_detail, product_maintain_info, product_sale_return_info, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineAfterSaleInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from new_product_request_online_offline_after_sale_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from new_product_request_online_offline_after_sale_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from new_product_request_online_offline_after_sale_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineAfterSaleInfoExample">
    delete from new_product_request_online_offline_after_sale_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineAfterSaleInfo">
    insert into new_product_request_online_offline_after_sale_info (id, new_product_request_id, combo_info_id, 
      store_order_handler_user_id, store_order_handler_user_name, 
      store_order_handler_user_phone, store_order_handler_user_email, 
      store_order_handler_sub_user_id, store_order_handler_sub_user_name, 
      store_order_handler_sub_user_phone, store_order_handler_sub_user_email, 
      pre_sale_manager_user_name, pre_sale_manager_user_phone, 
      pre_sale_manager_user_email, product_ship_user_name, 
      product_ship_user_phone, product_ship_user_email, 
      product_install_user_name, product_install_user_phone, 
      product_install_user_email, things_card_user_name, 
      things_card_user_phone, things_card_user_email, 
      software_authority_user_name, software_authority_user_phone, 
      software_authority_user_email, after_sale_user_name, 
      after_sale_user_phone, after_sale_user_email, 
      after_sale_detail, product_maintain_info, product_sale_return_info, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{newProductRequestId,jdbcType=VARCHAR}, #{comboInfoId,jdbcType=VARCHAR}, 
      #{storeOrderHandlerUserId,jdbcType=VARCHAR}, #{storeOrderHandlerUserName,jdbcType=VARCHAR}, 
      #{storeOrderHandlerUserPhone,jdbcType=VARCHAR}, #{storeOrderHandlerUserEmail,jdbcType=VARCHAR}, 
      #{storeOrderHandlerSubUserId,jdbcType=VARCHAR}, #{storeOrderHandlerSubUserName,jdbcType=VARCHAR}, 
      #{storeOrderHandlerSubUserPhone,jdbcType=VARCHAR}, #{storeOrderHandlerSubUserEmail,jdbcType=VARCHAR}, 
      #{preSaleManagerUserName,jdbcType=VARCHAR}, #{preSaleManagerUserPhone,jdbcType=VARCHAR}, 
      #{preSaleManagerUserEmail,jdbcType=VARCHAR}, #{productShipUserName,jdbcType=VARCHAR}, 
      #{productShipUserPhone,jdbcType=VARCHAR}, #{productShipUserEmail,jdbcType=VARCHAR}, 
      #{productInstallUserName,jdbcType=VARCHAR}, #{productInstallUserPhone,jdbcType=VARCHAR}, 
      #{productInstallUserEmail,jdbcType=VARCHAR}, #{thingsCardUserName,jdbcType=VARCHAR}, 
      #{thingsCardUserPhone,jdbcType=VARCHAR}, #{thingsCardUserEmail,jdbcType=VARCHAR}, 
      #{softwareAuthorityUserName,jdbcType=VARCHAR}, #{softwareAuthorityUserPhone,jdbcType=VARCHAR}, 
      #{softwareAuthorityUserEmail,jdbcType=VARCHAR}, #{afterSaleUserName,jdbcType=VARCHAR}, 
      #{afterSaleUserPhone,jdbcType=VARCHAR}, #{afterSaleUserEmail,jdbcType=VARCHAR}, 
      #{afterSaleDetail,jdbcType=VARCHAR}, #{productMaintainInfo,jdbcType=VARCHAR}, #{productSaleReturnInfo,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineAfterSaleInfo">
    insert into new_product_request_online_offline_after_sale_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="newProductRequestId != null">
        new_product_request_id,
      </if>
      <if test="comboInfoId != null">
        combo_info_id,
      </if>
      <if test="storeOrderHandlerUserId != null">
        store_order_handler_user_id,
      </if>
      <if test="storeOrderHandlerUserName != null">
        store_order_handler_user_name,
      </if>
      <if test="storeOrderHandlerUserPhone != null">
        store_order_handler_user_phone,
      </if>
      <if test="storeOrderHandlerUserEmail != null">
        store_order_handler_user_email,
      </if>
      <if test="storeOrderHandlerSubUserId != null">
        store_order_handler_sub_user_id,
      </if>
      <if test="storeOrderHandlerSubUserName != null">
        store_order_handler_sub_user_name,
      </if>
      <if test="storeOrderHandlerSubUserPhone != null">
        store_order_handler_sub_user_phone,
      </if>
      <if test="storeOrderHandlerSubUserEmail != null">
        store_order_handler_sub_user_email,
      </if>
      <if test="preSaleManagerUserName != null">
        pre_sale_manager_user_name,
      </if>
      <if test="preSaleManagerUserPhone != null">
        pre_sale_manager_user_phone,
      </if>
      <if test="preSaleManagerUserEmail != null">
        pre_sale_manager_user_email,
      </if>
      <if test="productShipUserName != null">
        product_ship_user_name,
      </if>
      <if test="productShipUserPhone != null">
        product_ship_user_phone,
      </if>
      <if test="productShipUserEmail != null">
        product_ship_user_email,
      </if>
      <if test="productInstallUserName != null">
        product_install_user_name,
      </if>
      <if test="productInstallUserPhone != null">
        product_install_user_phone,
      </if>
      <if test="productInstallUserEmail != null">
        product_install_user_email,
      </if>
      <if test="thingsCardUserName != null">
        things_card_user_name,
      </if>
      <if test="thingsCardUserPhone != null">
        things_card_user_phone,
      </if>
      <if test="thingsCardUserEmail != null">
        things_card_user_email,
      </if>
      <if test="softwareAuthorityUserName != null">
        software_authority_user_name,
      </if>
      <if test="softwareAuthorityUserPhone != null">
        software_authority_user_phone,
      </if>
      <if test="softwareAuthorityUserEmail != null">
        software_authority_user_email,
      </if>
      <if test="afterSaleUserName != null">
        after_sale_user_name,
      </if>
      <if test="afterSaleUserPhone != null">
        after_sale_user_phone,
      </if>
      <if test="afterSaleUserEmail != null">
        after_sale_user_email,
      </if>
      <if test="afterSaleDetail != null">
        after_sale_detail,
      </if>
      <if test="productMaintainInfo != null">
        product_maintain_info,
      </if>
      <if test="productSaleReturnInfo != null">
        product_sale_return_info,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="newProductRequestId != null">
        #{newProductRequestId,jdbcType=VARCHAR},
      </if>
      <if test="comboInfoId != null">
        #{comboInfoId,jdbcType=VARCHAR},
      </if>
      <if test="storeOrderHandlerUserId != null">
        #{storeOrderHandlerUserId,jdbcType=VARCHAR},
      </if>
      <if test="storeOrderHandlerUserName != null">
        #{storeOrderHandlerUserName,jdbcType=VARCHAR},
      </if>
      <if test="storeOrderHandlerUserPhone != null">
        #{storeOrderHandlerUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="storeOrderHandlerUserEmail != null">
        #{storeOrderHandlerUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="storeOrderHandlerSubUserId != null">
        #{storeOrderHandlerSubUserId,jdbcType=VARCHAR},
      </if>
      <if test="storeOrderHandlerSubUserName != null">
        #{storeOrderHandlerSubUserName,jdbcType=VARCHAR},
      </if>
      <if test="storeOrderHandlerSubUserPhone != null">
        #{storeOrderHandlerSubUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="storeOrderHandlerSubUserEmail != null">
        #{storeOrderHandlerSubUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="preSaleManagerUserName != null">
        #{preSaleManagerUserName,jdbcType=VARCHAR},
      </if>
      <if test="preSaleManagerUserPhone != null">
        #{preSaleManagerUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="preSaleManagerUserEmail != null">
        #{preSaleManagerUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="productShipUserName != null">
        #{productShipUserName,jdbcType=VARCHAR},
      </if>
      <if test="productShipUserPhone != null">
        #{productShipUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="productShipUserEmail != null">
        #{productShipUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="productInstallUserName != null">
        #{productInstallUserName,jdbcType=VARCHAR},
      </if>
      <if test="productInstallUserPhone != null">
        #{productInstallUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="productInstallUserEmail != null">
        #{productInstallUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="thingsCardUserName != null">
        #{thingsCardUserName,jdbcType=VARCHAR},
      </if>
      <if test="thingsCardUserPhone != null">
        #{thingsCardUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="thingsCardUserEmail != null">
        #{thingsCardUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="softwareAuthorityUserName != null">
        #{softwareAuthorityUserName,jdbcType=VARCHAR},
      </if>
      <if test="softwareAuthorityUserPhone != null">
        #{softwareAuthorityUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="softwareAuthorityUserEmail != null">
        #{softwareAuthorityUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleUserName != null">
        #{afterSaleUserName,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleUserPhone != null">
        #{afterSaleUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleUserEmail != null">
        #{afterSaleUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleDetail != null">
        #{afterSaleDetail,jdbcType=VARCHAR},
      </if>
      <if test="productMaintainInfo != null">
        #{productMaintainInfo,jdbcType=VARCHAR},
      </if>
      <if test="productSaleReturnInfo != null">
        #{productSaleReturnInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineAfterSaleInfoExample" resultType="java.lang.Long">
    select count(*) from new_product_request_online_offline_after_sale_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update new_product_request_online_offline_after_sale_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.newProductRequestId != null">
        new_product_request_id = #{record.newProductRequestId,jdbcType=VARCHAR},
      </if>
      <if test="record.comboInfoId != null">
        combo_info_id = #{record.comboInfoId,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOrderHandlerUserId != null">
        store_order_handler_user_id = #{record.storeOrderHandlerUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOrderHandlerUserName != null">
        store_order_handler_user_name = #{record.storeOrderHandlerUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOrderHandlerUserPhone != null">
        store_order_handler_user_phone = #{record.storeOrderHandlerUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOrderHandlerUserEmail != null">
        store_order_handler_user_email = #{record.storeOrderHandlerUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOrderHandlerSubUserId != null">
        store_order_handler_sub_user_id = #{record.storeOrderHandlerSubUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOrderHandlerSubUserName != null">
        store_order_handler_sub_user_name = #{record.storeOrderHandlerSubUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOrderHandlerSubUserPhone != null">
        store_order_handler_sub_user_phone = #{record.storeOrderHandlerSubUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.storeOrderHandlerSubUserEmail != null">
        store_order_handler_sub_user_email = #{record.storeOrderHandlerSubUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.preSaleManagerUserName != null">
        pre_sale_manager_user_name = #{record.preSaleManagerUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.preSaleManagerUserPhone != null">
        pre_sale_manager_user_phone = #{record.preSaleManagerUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.preSaleManagerUserEmail != null">
        pre_sale_manager_user_email = #{record.preSaleManagerUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.productShipUserName != null">
        product_ship_user_name = #{record.productShipUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.productShipUserPhone != null">
        product_ship_user_phone = #{record.productShipUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.productShipUserEmail != null">
        product_ship_user_email = #{record.productShipUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.productInstallUserName != null">
        product_install_user_name = #{record.productInstallUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.productInstallUserPhone != null">
        product_install_user_phone = #{record.productInstallUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.productInstallUserEmail != null">
        product_install_user_email = #{record.productInstallUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.thingsCardUserName != null">
        things_card_user_name = #{record.thingsCardUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.thingsCardUserPhone != null">
        things_card_user_phone = #{record.thingsCardUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.thingsCardUserEmail != null">
        things_card_user_email = #{record.thingsCardUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.softwareAuthorityUserName != null">
        software_authority_user_name = #{record.softwareAuthorityUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.softwareAuthorityUserPhone != null">
        software_authority_user_phone = #{record.softwareAuthorityUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.softwareAuthorityUserEmail != null">
        software_authority_user_email = #{record.softwareAuthorityUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.afterSaleUserName != null">
        after_sale_user_name = #{record.afterSaleUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.afterSaleUserPhone != null">
        after_sale_user_phone = #{record.afterSaleUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.afterSaleUserEmail != null">
        after_sale_user_email = #{record.afterSaleUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.afterSaleDetail != null">
        after_sale_detail = #{record.afterSaleDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.productMaintainInfo != null">
        product_maintain_info = #{record.productMaintainInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.productSaleReturnInfo != null">
        product_sale_return_info = #{record.productSaleReturnInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update new_product_request_online_offline_after_sale_info
    set id = #{record.id,jdbcType=VARCHAR},
      new_product_request_id = #{record.newProductRequestId,jdbcType=VARCHAR},
      combo_info_id = #{record.comboInfoId,jdbcType=VARCHAR},
      store_order_handler_user_id = #{record.storeOrderHandlerUserId,jdbcType=VARCHAR},
      store_order_handler_user_name = #{record.storeOrderHandlerUserName,jdbcType=VARCHAR},
      store_order_handler_user_phone = #{record.storeOrderHandlerUserPhone,jdbcType=VARCHAR},
      store_order_handler_user_email = #{record.storeOrderHandlerUserEmail,jdbcType=VARCHAR},
      store_order_handler_sub_user_id = #{record.storeOrderHandlerSubUserId,jdbcType=VARCHAR},
      store_order_handler_sub_user_name = #{record.storeOrderHandlerSubUserName,jdbcType=VARCHAR},
      store_order_handler_sub_user_phone = #{record.storeOrderHandlerSubUserPhone,jdbcType=VARCHAR},
      store_order_handler_sub_user_email = #{record.storeOrderHandlerSubUserEmail,jdbcType=VARCHAR},
      pre_sale_manager_user_name = #{record.preSaleManagerUserName,jdbcType=VARCHAR},
      pre_sale_manager_user_phone = #{record.preSaleManagerUserPhone,jdbcType=VARCHAR},
      pre_sale_manager_user_email = #{record.preSaleManagerUserEmail,jdbcType=VARCHAR},
      product_ship_user_name = #{record.productShipUserName,jdbcType=VARCHAR},
      product_ship_user_phone = #{record.productShipUserPhone,jdbcType=VARCHAR},
      product_ship_user_email = #{record.productShipUserEmail,jdbcType=VARCHAR},
      product_install_user_name = #{record.productInstallUserName,jdbcType=VARCHAR},
      product_install_user_phone = #{record.productInstallUserPhone,jdbcType=VARCHAR},
      product_install_user_email = #{record.productInstallUserEmail,jdbcType=VARCHAR},
      things_card_user_name = #{record.thingsCardUserName,jdbcType=VARCHAR},
      things_card_user_phone = #{record.thingsCardUserPhone,jdbcType=VARCHAR},
      things_card_user_email = #{record.thingsCardUserEmail,jdbcType=VARCHAR},
      software_authority_user_name = #{record.softwareAuthorityUserName,jdbcType=VARCHAR},
      software_authority_user_phone = #{record.softwareAuthorityUserPhone,jdbcType=VARCHAR},
      software_authority_user_email = #{record.softwareAuthorityUserEmail,jdbcType=VARCHAR},
      after_sale_user_name = #{record.afterSaleUserName,jdbcType=VARCHAR},
      after_sale_user_phone = #{record.afterSaleUserPhone,jdbcType=VARCHAR},
      after_sale_user_email = #{record.afterSaleUserEmail,jdbcType=VARCHAR},
      after_sale_detail = #{record.afterSaleDetail,jdbcType=VARCHAR},
      product_maintain_info = #{record.productMaintainInfo,jdbcType=VARCHAR},
      product_sale_return_info = #{record.productSaleReturnInfo,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineAfterSaleInfo">
    update new_product_request_online_offline_after_sale_info
    <set>
      <if test="newProductRequestId != null">
        new_product_request_id = #{newProductRequestId,jdbcType=VARCHAR},
      </if>
      <if test="comboInfoId != null">
        combo_info_id = #{comboInfoId,jdbcType=VARCHAR},
      </if>
      <if test="storeOrderHandlerUserId != null">
        store_order_handler_user_id = #{storeOrderHandlerUserId,jdbcType=VARCHAR},
      </if>
      <if test="storeOrderHandlerUserName != null">
        store_order_handler_user_name = #{storeOrderHandlerUserName,jdbcType=VARCHAR},
      </if>
      <if test="storeOrderHandlerUserPhone != null">
        store_order_handler_user_phone = #{storeOrderHandlerUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="storeOrderHandlerUserEmail != null">
        store_order_handler_user_email = #{storeOrderHandlerUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="storeOrderHandlerSubUserId != null">
        store_order_handler_sub_user_id = #{storeOrderHandlerSubUserId,jdbcType=VARCHAR},
      </if>
      <if test="storeOrderHandlerSubUserName != null">
        store_order_handler_sub_user_name = #{storeOrderHandlerSubUserName,jdbcType=VARCHAR},
      </if>
      <if test="storeOrderHandlerSubUserPhone != null">
        store_order_handler_sub_user_phone = #{storeOrderHandlerSubUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="storeOrderHandlerSubUserEmail != null">
        store_order_handler_sub_user_email = #{storeOrderHandlerSubUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="preSaleManagerUserName != null">
        pre_sale_manager_user_name = #{preSaleManagerUserName,jdbcType=VARCHAR},
      </if>
      <if test="preSaleManagerUserPhone != null">
        pre_sale_manager_user_phone = #{preSaleManagerUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="preSaleManagerUserEmail != null">
        pre_sale_manager_user_email = #{preSaleManagerUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="productShipUserName != null">
        product_ship_user_name = #{productShipUserName,jdbcType=VARCHAR},
      </if>
      <if test="productShipUserPhone != null">
        product_ship_user_phone = #{productShipUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="productShipUserEmail != null">
        product_ship_user_email = #{productShipUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="productInstallUserName != null">
        product_install_user_name = #{productInstallUserName,jdbcType=VARCHAR},
      </if>
      <if test="productInstallUserPhone != null">
        product_install_user_phone = #{productInstallUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="productInstallUserEmail != null">
        product_install_user_email = #{productInstallUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="thingsCardUserName != null">
        things_card_user_name = #{thingsCardUserName,jdbcType=VARCHAR},
      </if>
      <if test="thingsCardUserPhone != null">
        things_card_user_phone = #{thingsCardUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="thingsCardUserEmail != null">
        things_card_user_email = #{thingsCardUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="softwareAuthorityUserName != null">
        software_authority_user_name = #{softwareAuthorityUserName,jdbcType=VARCHAR},
      </if>
      <if test="softwareAuthorityUserPhone != null">
        software_authority_user_phone = #{softwareAuthorityUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="softwareAuthorityUserEmail != null">
        software_authority_user_email = #{softwareAuthorityUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleUserName != null">
        after_sale_user_name = #{afterSaleUserName,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleUserPhone != null">
        after_sale_user_phone = #{afterSaleUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleUserEmail != null">
        after_sale_user_email = #{afterSaleUserEmail,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleDetail != null">
        after_sale_detail = #{afterSaleDetail,jdbcType=VARCHAR},
      </if>
      <if test="productMaintainInfo != null">
        product_maintain_info = #{productMaintainInfo,jdbcType=VARCHAR},
      </if>
      <if test="productSaleReturnInfo != null">
        product_sale_return_info = #{productSaleReturnInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineAfterSaleInfo">
    update new_product_request_online_offline_after_sale_info
    set new_product_request_id = #{newProductRequestId,jdbcType=VARCHAR},
      combo_info_id = #{comboInfoId,jdbcType=VARCHAR},
      store_order_handler_user_id = #{storeOrderHandlerUserId,jdbcType=VARCHAR},
      store_order_handler_user_name = #{storeOrderHandlerUserName,jdbcType=VARCHAR},
      store_order_handler_user_phone = #{storeOrderHandlerUserPhone,jdbcType=VARCHAR},
      store_order_handler_user_email = #{storeOrderHandlerUserEmail,jdbcType=VARCHAR},
      store_order_handler_sub_user_id = #{storeOrderHandlerSubUserId,jdbcType=VARCHAR},
      store_order_handler_sub_user_name = #{storeOrderHandlerSubUserName,jdbcType=VARCHAR},
      store_order_handler_sub_user_phone = #{storeOrderHandlerSubUserPhone,jdbcType=VARCHAR},
      store_order_handler_sub_user_email = #{storeOrderHandlerSubUserEmail,jdbcType=VARCHAR},
      pre_sale_manager_user_name = #{preSaleManagerUserName,jdbcType=VARCHAR},
      pre_sale_manager_user_phone = #{preSaleManagerUserPhone,jdbcType=VARCHAR},
      pre_sale_manager_user_email = #{preSaleManagerUserEmail,jdbcType=VARCHAR},
      product_ship_user_name = #{productShipUserName,jdbcType=VARCHAR},
      product_ship_user_phone = #{productShipUserPhone,jdbcType=VARCHAR},
      product_ship_user_email = #{productShipUserEmail,jdbcType=VARCHAR},
      product_install_user_name = #{productInstallUserName,jdbcType=VARCHAR},
      product_install_user_phone = #{productInstallUserPhone,jdbcType=VARCHAR},
      product_install_user_email = #{productInstallUserEmail,jdbcType=VARCHAR},
      things_card_user_name = #{thingsCardUserName,jdbcType=VARCHAR},
      things_card_user_phone = #{thingsCardUserPhone,jdbcType=VARCHAR},
      things_card_user_email = #{thingsCardUserEmail,jdbcType=VARCHAR},
      software_authority_user_name = #{softwareAuthorityUserName,jdbcType=VARCHAR},
      software_authority_user_phone = #{softwareAuthorityUserPhone,jdbcType=VARCHAR},
      software_authority_user_email = #{softwareAuthorityUserEmail,jdbcType=VARCHAR},
      after_sale_user_name = #{afterSaleUserName,jdbcType=VARCHAR},
      after_sale_user_phone = #{afterSaleUserPhone,jdbcType=VARCHAR},
      after_sale_user_email = #{afterSaleUserEmail,jdbcType=VARCHAR},
      after_sale_detail = #{afterSaleDetail,jdbcType=VARCHAR},
      product_maintain_info = #{productMaintainInfo,jdbcType=VARCHAR},
      product_sale_return_info = #{productSaleReturnInfo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into new_product_request_online_offline_after_sale_info
    (id, new_product_request_id, combo_info_id, store_order_handler_user_id, store_order_handler_user_name, 
      store_order_handler_user_phone, store_order_handler_user_email, store_order_handler_sub_user_id, 
      store_order_handler_sub_user_name, store_order_handler_sub_user_phone, store_order_handler_sub_user_email, 
      pre_sale_manager_user_name, pre_sale_manager_user_phone, pre_sale_manager_user_email, 
      product_ship_user_name, product_ship_user_phone, product_ship_user_email, product_install_user_name, 
      product_install_user_phone, product_install_user_email, things_card_user_name, 
      things_card_user_phone, things_card_user_email, software_authority_user_name, software_authority_user_phone, 
      software_authority_user_email, after_sale_user_name, after_sale_user_phone, after_sale_user_email, 
      after_sale_detail, product_maintain_info, product_sale_return_info, create_time, 
      update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.newProductRequestId,jdbcType=VARCHAR}, #{item.comboInfoId,jdbcType=VARCHAR}, 
        #{item.storeOrderHandlerUserId,jdbcType=VARCHAR}, #{item.storeOrderHandlerUserName,jdbcType=VARCHAR}, 
        #{item.storeOrderHandlerUserPhone,jdbcType=VARCHAR}, #{item.storeOrderHandlerUserEmail,jdbcType=VARCHAR}, 
        #{item.storeOrderHandlerSubUserId,jdbcType=VARCHAR}, #{item.storeOrderHandlerSubUserName,jdbcType=VARCHAR}, 
        #{item.storeOrderHandlerSubUserPhone,jdbcType=VARCHAR}, #{item.storeOrderHandlerSubUserEmail,jdbcType=VARCHAR}, 
        #{item.preSaleManagerUserName,jdbcType=VARCHAR}, #{item.preSaleManagerUserPhone,jdbcType=VARCHAR}, 
        #{item.preSaleManagerUserEmail,jdbcType=VARCHAR}, #{item.productShipUserName,jdbcType=VARCHAR}, 
        #{item.productShipUserPhone,jdbcType=VARCHAR}, #{item.productShipUserEmail,jdbcType=VARCHAR}, 
        #{item.productInstallUserName,jdbcType=VARCHAR}, #{item.productInstallUserPhone,jdbcType=VARCHAR}, 
        #{item.productInstallUserEmail,jdbcType=VARCHAR}, #{item.thingsCardUserName,jdbcType=VARCHAR}, 
        #{item.thingsCardUserPhone,jdbcType=VARCHAR}, #{item.thingsCardUserEmail,jdbcType=VARCHAR}, 
        #{item.softwareAuthorityUserName,jdbcType=VARCHAR}, #{item.softwareAuthorityUserPhone,jdbcType=VARCHAR}, 
        #{item.softwareAuthorityUserEmail,jdbcType=VARCHAR}, #{item.afterSaleUserName,jdbcType=VARCHAR}, 
        #{item.afterSaleUserPhone,jdbcType=VARCHAR}, #{item.afterSaleUserEmail,jdbcType=VARCHAR}, 
        #{item.afterSaleDetail,jdbcType=VARCHAR}, #{item.productMaintainInfo,jdbcType=VARCHAR}, 
        #{item.productSaleReturnInfo,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into new_product_request_online_offline_after_sale_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'new_product_request_id'.toString() == column.value">
          #{item.newProductRequestId,jdbcType=VARCHAR}
        </if>
        <if test="'combo_info_id'.toString() == column.value">
          #{item.comboInfoId,jdbcType=VARCHAR}
        </if>
        <if test="'store_order_handler_user_id'.toString() == column.value">
          #{item.storeOrderHandlerUserId,jdbcType=VARCHAR}
        </if>
        <if test="'store_order_handler_user_name'.toString() == column.value">
          #{item.storeOrderHandlerUserName,jdbcType=VARCHAR}
        </if>
        <if test="'store_order_handler_user_phone'.toString() == column.value">
          #{item.storeOrderHandlerUserPhone,jdbcType=VARCHAR}
        </if>
        <if test="'store_order_handler_user_email'.toString() == column.value">
          #{item.storeOrderHandlerUserEmail,jdbcType=VARCHAR}
        </if>
        <if test="'store_order_handler_sub_user_id'.toString() == column.value">
          #{item.storeOrderHandlerSubUserId,jdbcType=VARCHAR}
        </if>
        <if test="'store_order_handler_sub_user_name'.toString() == column.value">
          #{item.storeOrderHandlerSubUserName,jdbcType=VARCHAR}
        </if>
        <if test="'store_order_handler_sub_user_phone'.toString() == column.value">
          #{item.storeOrderHandlerSubUserPhone,jdbcType=VARCHAR}
        </if>
        <if test="'store_order_handler_sub_user_email'.toString() == column.value">
          #{item.storeOrderHandlerSubUserEmail,jdbcType=VARCHAR}
        </if>
        <if test="'pre_sale_manager_user_name'.toString() == column.value">
          #{item.preSaleManagerUserName,jdbcType=VARCHAR}
        </if>
        <if test="'pre_sale_manager_user_phone'.toString() == column.value">
          #{item.preSaleManagerUserPhone,jdbcType=VARCHAR}
        </if>
        <if test="'pre_sale_manager_user_email'.toString() == column.value">
          #{item.preSaleManagerUserEmail,jdbcType=VARCHAR}
        </if>
        <if test="'product_ship_user_name'.toString() == column.value">
          #{item.productShipUserName,jdbcType=VARCHAR}
        </if>
        <if test="'product_ship_user_phone'.toString() == column.value">
          #{item.productShipUserPhone,jdbcType=VARCHAR}
        </if>
        <if test="'product_ship_user_email'.toString() == column.value">
          #{item.productShipUserEmail,jdbcType=VARCHAR}
        </if>
        <if test="'product_install_user_name'.toString() == column.value">
          #{item.productInstallUserName,jdbcType=VARCHAR}
        </if>
        <if test="'product_install_user_phone'.toString() == column.value">
          #{item.productInstallUserPhone,jdbcType=VARCHAR}
        </if>
        <if test="'product_install_user_email'.toString() == column.value">
          #{item.productInstallUserEmail,jdbcType=VARCHAR}
        </if>
        <if test="'things_card_user_name'.toString() == column.value">
          #{item.thingsCardUserName,jdbcType=VARCHAR}
        </if>
        <if test="'things_card_user_phone'.toString() == column.value">
          #{item.thingsCardUserPhone,jdbcType=VARCHAR}
        </if>
        <if test="'things_card_user_email'.toString() == column.value">
          #{item.thingsCardUserEmail,jdbcType=VARCHAR}
        </if>
        <if test="'software_authority_user_name'.toString() == column.value">
          #{item.softwareAuthorityUserName,jdbcType=VARCHAR}
        </if>
        <if test="'software_authority_user_phone'.toString() == column.value">
          #{item.softwareAuthorityUserPhone,jdbcType=VARCHAR}
        </if>
        <if test="'software_authority_user_email'.toString() == column.value">
          #{item.softwareAuthorityUserEmail,jdbcType=VARCHAR}
        </if>
        <if test="'after_sale_user_name'.toString() == column.value">
          #{item.afterSaleUserName,jdbcType=VARCHAR}
        </if>
        <if test="'after_sale_user_phone'.toString() == column.value">
          #{item.afterSaleUserPhone,jdbcType=VARCHAR}
        </if>
        <if test="'after_sale_user_email'.toString() == column.value">
          #{item.afterSaleUserEmail,jdbcType=VARCHAR}
        </if>
        <if test="'after_sale_detail'.toString() == column.value">
          #{item.afterSaleDetail,jdbcType=VARCHAR}
        </if>
        <if test="'product_maintain_info'.toString() == column.value">
          #{item.productMaintainInfo,jdbcType=VARCHAR}
        </if>
        <if test="'product_sale_return_info'.toString() == column.value">
          #{item.productSaleReturnInfo,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>