<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ContractMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.Contract">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 12 16:52:26 CST 2023. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="number" jdbcType="VARCHAR" property="number" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="property" jdbcType="VARCHAR" property="property" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="vendor_code" jdbcType="VARCHAR" property="vendorCode" />
    <result column="vendor_name" jdbcType="VARCHAR" property="vendorName" />
    <result column="create_company_name" jdbcType="VARCHAR" property="createCompanyName" />
    <result column="create_dept_name" jdbcType="VARCHAR" property="createDeptName" />
    <result column="amount_type" jdbcType="VARCHAR" property="amountType" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="amount_including_tax" jdbcType="DECIMAL" property="amountIncludingTax" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="province_k3_code" jdbcType="VARCHAR" property="provinceK3Code" />
    <result column="province_mall_code" jdbcType="VARCHAR" property="provinceMallCode" />
    <result column="province_mall_name" jdbcType="VARCHAR" property="provinceMallName" />
    <result column="city_k3_code" jdbcType="VARCHAR" property="cityK3Code" />
    <result column="city_mall_code" jdbcType="VARCHAR" property="cityMallCode" />
    <result column="city_mall_name" jdbcType="VARCHAR" property="cityMallName" />
    <result column="count_type" jdbcType="INTEGER" property="countType" />
    <result column="project" jdbcType="VARCHAR" property="project" />
    <result column="sub_project" jdbcType="VARCHAR" property="subProject" />
    <result column="f_number" jdbcType="VARCHAR" property="fNumber" />
    <result column="settlement_mode" jdbcType="INTEGER" property="settlementMode" />
    <result column="active" jdbcType="BIT" property="active" />
    <result column="contract_type" jdbcType="INTEGER" property="contractType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 12 16:52:26 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 12 16:52:26 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 12 16:52:26 CST 2023. by MyBatis Generator, do not modify.
    -->
    id, number, name, property, status, vendor_code, vendor_name, create_company_name, 
    create_dept_name, amount_type, currency, amount_including_tax, end_date, province_k3_code, 
    province_mall_code, province_mall_name, city_k3_code, city_mall_code, city_mall_name, 
    count_type, project, sub_project, f_number, settlement_mode, active, contract_type
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.ContractExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 12 16:52:26 CST 2023. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from contract
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 12 16:52:26 CST 2023. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from contract
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 12 16:52:26 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from contract
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.ContractExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 12 16:52:26 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from contract
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.Contract">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 12 16:52:26 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into contract (id, number, name, 
      property, status, vendor_code, 
      vendor_name, create_company_name, create_dept_name, 
      amount_type, currency, amount_including_tax, 
      end_date, province_k3_code, province_mall_code, 
      province_mall_name, city_k3_code, city_mall_code, 
      city_mall_name, count_type, project, 
      sub_project, f_number, settlement_mode, 
      active, contract_type)
    values (#{id,jdbcType=VARCHAR}, #{number,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{property,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{vendorCode,jdbcType=VARCHAR}, 
      #{vendorName,jdbcType=VARCHAR}, #{createCompanyName,jdbcType=VARCHAR}, #{createDeptName,jdbcType=VARCHAR}, 
      #{amountType,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR}, #{amountIncludingTax,jdbcType=DECIMAL}, 
      #{endDate,jdbcType=TIMESTAMP}, #{provinceK3Code,jdbcType=VARCHAR}, #{provinceMallCode,jdbcType=VARCHAR}, 
      #{provinceMallName,jdbcType=VARCHAR}, #{cityK3Code,jdbcType=VARCHAR}, #{cityMallCode,jdbcType=VARCHAR}, 
      #{cityMallName,jdbcType=VARCHAR}, #{countType,jdbcType=INTEGER}, #{project,jdbcType=VARCHAR}, 
      #{subProject,jdbcType=VARCHAR}, #{fNumber,jdbcType=VARCHAR}, #{settlementMode,jdbcType=INTEGER}, 
      #{active,jdbcType=BIT}, #{contractType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.Contract">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 12 16:52:26 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into contract
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="number != null">
        number,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="property != null">
        property,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="vendorCode != null">
        vendor_code,
      </if>
      <if test="vendorName != null">
        vendor_name,
      </if>
      <if test="createCompanyName != null">
        create_company_name,
      </if>
      <if test="createDeptName != null">
        create_dept_name,
      </if>
      <if test="amountType != null">
        amount_type,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="amountIncludingTax != null">
        amount_including_tax,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="provinceK3Code != null">
        province_k3_code,
      </if>
      <if test="provinceMallCode != null">
        province_mall_code,
      </if>
      <if test="provinceMallName != null">
        province_mall_name,
      </if>
      <if test="cityK3Code != null">
        city_k3_code,
      </if>
      <if test="cityMallCode != null">
        city_mall_code,
      </if>
      <if test="cityMallName != null">
        city_mall_name,
      </if>
      <if test="countType != null">
        count_type,
      </if>
      <if test="project != null">
        project,
      </if>
      <if test="subProject != null">
        sub_project,
      </if>
      <if test="fNumber != null">
        f_number,
      </if>
      <if test="settlementMode != null">
        settlement_mode,
      </if>
      <if test="active != null">
        active,
      </if>
      <if test="contractType != null">
        contract_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        #{number,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="property != null">
        #{property,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="vendorCode != null">
        #{vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="vendorName != null">
        #{vendorName,jdbcType=VARCHAR},
      </if>
      <if test="createCompanyName != null">
        #{createCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="createDeptName != null">
        #{createDeptName,jdbcType=VARCHAR},
      </if>
      <if test="amountType != null">
        #{amountType,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="amountIncludingTax != null">
        #{amountIncludingTax,jdbcType=DECIMAL},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="provinceK3Code != null">
        #{provinceK3Code,jdbcType=VARCHAR},
      </if>
      <if test="provinceMallCode != null">
        #{provinceMallCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceMallName != null">
        #{provinceMallName,jdbcType=VARCHAR},
      </if>
      <if test="cityK3Code != null">
        #{cityK3Code,jdbcType=VARCHAR},
      </if>
      <if test="cityMallCode != null">
        #{cityMallCode,jdbcType=VARCHAR},
      </if>
      <if test="cityMallName != null">
        #{cityMallName,jdbcType=VARCHAR},
      </if>
      <if test="countType != null">
        #{countType,jdbcType=INTEGER},
      </if>
      <if test="project != null">
        #{project,jdbcType=VARCHAR},
      </if>
      <if test="subProject != null">
        #{subProject,jdbcType=VARCHAR},
      </if>
      <if test="fNumber != null">
        #{fNumber,jdbcType=VARCHAR},
      </if>
      <if test="settlementMode != null">
        #{settlementMode,jdbcType=INTEGER},
      </if>
      <if test="active != null">
        #{active,jdbcType=BIT},
      </if>
      <if test="contractType != null">
        #{contractType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.ContractExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 12 16:52:26 CST 2023. by MyBatis Generator, do not modify.
    -->
    select count(*) from contract
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 12 16:52:26 CST 2023. by MyBatis Generator, do not modify.
    -->
    update contract
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.number != null">
        number = #{record.number,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.property != null">
        property = #{record.property,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.vendorCode != null">
        vendor_code = #{record.vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.vendorName != null">
        vendor_name = #{record.vendorName,jdbcType=VARCHAR},
      </if>
      <if test="record.createCompanyName != null">
        create_company_name = #{record.createCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.createDeptName != null">
        create_dept_name = #{record.createDeptName,jdbcType=VARCHAR},
      </if>
      <if test="record.amountType != null">
        amount_type = #{record.amountType,jdbcType=VARCHAR},
      </if>
      <if test="record.currency != null">
        currency = #{record.currency,jdbcType=VARCHAR},
      </if>
      <if test="record.amountIncludingTax != null">
        amount_including_tax = #{record.amountIncludingTax,jdbcType=DECIMAL},
      </if>
      <if test="record.endDate != null">
        end_date = #{record.endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.provinceK3Code != null">
        province_k3_code = #{record.provinceK3Code,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceMallCode != null">
        province_mall_code = #{record.provinceMallCode,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceMallName != null">
        province_mall_name = #{record.provinceMallName,jdbcType=VARCHAR},
      </if>
      <if test="record.cityK3Code != null">
        city_k3_code = #{record.cityK3Code,jdbcType=VARCHAR},
      </if>
      <if test="record.cityMallCode != null">
        city_mall_code = #{record.cityMallCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityMallName != null">
        city_mall_name = #{record.cityMallName,jdbcType=VARCHAR},
      </if>
      <if test="record.countType != null">
        count_type = #{record.countType,jdbcType=INTEGER},
      </if>
      <if test="record.project != null">
        project = #{record.project,jdbcType=VARCHAR},
      </if>
      <if test="record.subProject != null">
        sub_project = #{record.subProject,jdbcType=VARCHAR},
      </if>
      <if test="record.fNumber != null">
        f_number = #{record.fNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.settlementMode != null">
        settlement_mode = #{record.settlementMode,jdbcType=INTEGER},
      </if>
      <if test="record.active != null">
        active = #{record.active,jdbcType=BIT},
      </if>
      <if test="record.contractType != null">
        contract_type = #{record.contractType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 12 16:52:26 CST 2023. by MyBatis Generator, do not modify.
    -->
    update contract
    set id = #{record.id,jdbcType=VARCHAR},
      number = #{record.number,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      property = #{record.property,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      vendor_code = #{record.vendorCode,jdbcType=VARCHAR},
      vendor_name = #{record.vendorName,jdbcType=VARCHAR},
      create_company_name = #{record.createCompanyName,jdbcType=VARCHAR},
      create_dept_name = #{record.createDeptName,jdbcType=VARCHAR},
      amount_type = #{record.amountType,jdbcType=VARCHAR},
      currency = #{record.currency,jdbcType=VARCHAR},
      amount_including_tax = #{record.amountIncludingTax,jdbcType=DECIMAL},
      end_date = #{record.endDate,jdbcType=TIMESTAMP},
      province_k3_code = #{record.provinceK3Code,jdbcType=VARCHAR},
      province_mall_code = #{record.provinceMallCode,jdbcType=VARCHAR},
      province_mall_name = #{record.provinceMallName,jdbcType=VARCHAR},
      city_k3_code = #{record.cityK3Code,jdbcType=VARCHAR},
      city_mall_code = #{record.cityMallCode,jdbcType=VARCHAR},
      city_mall_name = #{record.cityMallName,jdbcType=VARCHAR},
      count_type = #{record.countType,jdbcType=INTEGER},
      project = #{record.project,jdbcType=VARCHAR},
      sub_project = #{record.subProject,jdbcType=VARCHAR},
      f_number = #{record.fNumber,jdbcType=VARCHAR},
      settlement_mode = #{record.settlementMode,jdbcType=INTEGER},
      active = #{record.active,jdbcType=BIT},
      contract_type = #{record.contractType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.Contract">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 12 16:52:26 CST 2023. by MyBatis Generator, do not modify.
    -->
    update contract
    <set>
      <if test="number != null">
        number = #{number,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="property != null">
        property = #{property,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="vendorCode != null">
        vendor_code = #{vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="vendorName != null">
        vendor_name = #{vendorName,jdbcType=VARCHAR},
      </if>
      <if test="createCompanyName != null">
        create_company_name = #{createCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="createDeptName != null">
        create_dept_name = #{createDeptName,jdbcType=VARCHAR},
      </if>
      <if test="amountType != null">
        amount_type = #{amountType,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="amountIncludingTax != null">
        amount_including_tax = #{amountIncludingTax,jdbcType=DECIMAL},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="provinceK3Code != null">
        province_k3_code = #{provinceK3Code,jdbcType=VARCHAR},
      </if>
      <if test="provinceMallCode != null">
        province_mall_code = #{provinceMallCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceMallName != null">
        province_mall_name = #{provinceMallName,jdbcType=VARCHAR},
      </if>
      <if test="cityK3Code != null">
        city_k3_code = #{cityK3Code,jdbcType=VARCHAR},
      </if>
      <if test="cityMallCode != null">
        city_mall_code = #{cityMallCode,jdbcType=VARCHAR},
      </if>
      <if test="cityMallName != null">
        city_mall_name = #{cityMallName,jdbcType=VARCHAR},
      </if>
      <if test="countType != null">
        count_type = #{countType,jdbcType=INTEGER},
      </if>
      <if test="project != null">
        project = #{project,jdbcType=VARCHAR},
      </if>
      <if test="subProject != null">
        sub_project = #{subProject,jdbcType=VARCHAR},
      </if>
      <if test="fNumber != null">
        f_number = #{fNumber,jdbcType=VARCHAR},
      </if>
      <if test="settlementMode != null">
        settlement_mode = #{settlementMode,jdbcType=INTEGER},
      </if>
      <if test="active != null">
        active = #{active,jdbcType=BIT},
      </if>
      <if test="contractType != null">
        contract_type = #{contractType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.Contract">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 12 16:52:26 CST 2023. by MyBatis Generator, do not modify.
    -->
    update contract
    set number = #{number,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      property = #{property,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      vendor_code = #{vendorCode,jdbcType=VARCHAR},
      vendor_name = #{vendorName,jdbcType=VARCHAR},
      create_company_name = #{createCompanyName,jdbcType=VARCHAR},
      create_dept_name = #{createDeptName,jdbcType=VARCHAR},
      amount_type = #{amountType,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      amount_including_tax = #{amountIncludingTax,jdbcType=DECIMAL},
      end_date = #{endDate,jdbcType=TIMESTAMP},
      province_k3_code = #{provinceK3Code,jdbcType=VARCHAR},
      province_mall_code = #{provinceMallCode,jdbcType=VARCHAR},
      province_mall_name = #{provinceMallName,jdbcType=VARCHAR},
      city_k3_code = #{cityK3Code,jdbcType=VARCHAR},
      city_mall_code = #{cityMallCode,jdbcType=VARCHAR},
      city_mall_name = #{cityMallName,jdbcType=VARCHAR},
      count_type = #{countType,jdbcType=INTEGER},
      project = #{project,jdbcType=VARCHAR},
      sub_project = #{subProject,jdbcType=VARCHAR},
      f_number = #{fNumber,jdbcType=VARCHAR},
      settlement_mode = #{settlementMode,jdbcType=INTEGER},
      active = #{active,jdbcType=BIT},
      contract_type = #{contractType,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 12 16:52:26 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into contract
    (id, number, name, property, status, vendor_code, vendor_name, create_company_name, 
      create_dept_name, amount_type, currency, amount_including_tax, end_date, province_k3_code, 
      province_mall_code, province_mall_name, city_k3_code, city_mall_code, city_mall_name, 
      count_type, project, sub_project, f_number, settlement_mode, active, contract_type
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.number,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, 
        #{item.property,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR}, #{item.vendorCode,jdbcType=VARCHAR}, 
        #{item.vendorName,jdbcType=VARCHAR}, #{item.createCompanyName,jdbcType=VARCHAR}, 
        #{item.createDeptName,jdbcType=VARCHAR}, #{item.amountType,jdbcType=VARCHAR}, #{item.currency,jdbcType=VARCHAR}, 
        #{item.amountIncludingTax,jdbcType=DECIMAL}, #{item.endDate,jdbcType=TIMESTAMP}, 
        #{item.provinceK3Code,jdbcType=VARCHAR}, #{item.provinceMallCode,jdbcType=VARCHAR}, 
        #{item.provinceMallName,jdbcType=VARCHAR}, #{item.cityK3Code,jdbcType=VARCHAR}, 
        #{item.cityMallCode,jdbcType=VARCHAR}, #{item.cityMallName,jdbcType=VARCHAR}, #{item.countType,jdbcType=INTEGER}, 
        #{item.project,jdbcType=VARCHAR}, #{item.subProject,jdbcType=VARCHAR}, #{item.fNumber,jdbcType=VARCHAR}, 
        #{item.settlementMode,jdbcType=INTEGER}, #{item.active,jdbcType=BIT}, #{item.contractType,jdbcType=INTEGER}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 12 16:52:26 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into contract (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'number'.toString() == column.value">
          #{item.number,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'property'.toString() == column.value">
          #{item.property,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=VARCHAR}
        </if>
        <if test="'vendor_code'.toString() == column.value">
          #{item.vendorCode,jdbcType=VARCHAR}
        </if>
        <if test="'vendor_name'.toString() == column.value">
          #{item.vendorName,jdbcType=VARCHAR}
        </if>
        <if test="'create_company_name'.toString() == column.value">
          #{item.createCompanyName,jdbcType=VARCHAR}
        </if>
        <if test="'create_dept_name'.toString() == column.value">
          #{item.createDeptName,jdbcType=VARCHAR}
        </if>
        <if test="'amount_type'.toString() == column.value">
          #{item.amountType,jdbcType=VARCHAR}
        </if>
        <if test="'currency'.toString() == column.value">
          #{item.currency,jdbcType=VARCHAR}
        </if>
        <if test="'amount_including_tax'.toString() == column.value">
          #{item.amountIncludingTax,jdbcType=DECIMAL}
        </if>
        <if test="'end_date'.toString() == column.value">
          #{item.endDate,jdbcType=TIMESTAMP}
        </if>
        <if test="'province_k3_code'.toString() == column.value">
          #{item.provinceK3Code,jdbcType=VARCHAR}
        </if>
        <if test="'province_mall_code'.toString() == column.value">
          #{item.provinceMallCode,jdbcType=VARCHAR}
        </if>
        <if test="'province_mall_name'.toString() == column.value">
          #{item.provinceMallName,jdbcType=VARCHAR}
        </if>
        <if test="'city_k3_code'.toString() == column.value">
          #{item.cityK3Code,jdbcType=VARCHAR}
        </if>
        <if test="'city_mall_code'.toString() == column.value">
          #{item.cityMallCode,jdbcType=VARCHAR}
        </if>
        <if test="'city_mall_name'.toString() == column.value">
          #{item.cityMallName,jdbcType=VARCHAR}
        </if>
        <if test="'count_type'.toString() == column.value">
          #{item.countType,jdbcType=INTEGER}
        </if>
        <if test="'project'.toString() == column.value">
          #{item.project,jdbcType=VARCHAR}
        </if>
        <if test="'sub_project'.toString() == column.value">
          #{item.subProject,jdbcType=VARCHAR}
        </if>
        <if test="'f_number'.toString() == column.value">
          #{item.fNumber,jdbcType=VARCHAR}
        </if>
        <if test="'settlement_mode'.toString() == column.value">
          #{item.settlementMode,jdbcType=INTEGER}
        </if>
        <if test="'active'.toString() == column.value">
          #{item.active,jdbcType=BIT}
        </if>
        <if test="'contract_type'.toString() == column.value">
          #{item.contractType,jdbcType=INTEGER}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>