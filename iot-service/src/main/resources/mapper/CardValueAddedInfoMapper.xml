<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.CardValueAddedInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.CardValueAddedInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 09 17:24:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="offering_name" jdbcType="VARCHAR" property="offeringName" />
    <result column="offering_type" jdbcType="VARCHAR" property="offeringType" />
    <result column="main_offering" jdbcType="VARCHAR" property="mainOffering" />
    <result column="expenses_price" jdbcType="VARCHAR" property="expensesPrice" />
    <result column="expenses_term" jdbcType="VARCHAR" property="expensesTerm" />
    <result column="validity_period_unit" jdbcType="VARCHAR" property="validityPeriodUnit" />
    <result column="order_quantity" jdbcType="VARCHAR" property="orderQuantity" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 09 17:24:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 09 17:24:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 09 17:24:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, order_id, offering_name, offering_type, main_offering, expenses_price, expenses_term, 
    validity_period_unit, order_quantity, create_time, update_time, delete_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.CardValueAddedInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 09 17:24:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from card_value_added_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 09 17:24:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from card_value_added_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 09 17:24:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from card_value_added_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.CardValueAddedInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 09 17:24:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from card_value_added_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.CardValueAddedInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 09 17:24:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into card_value_added_info (id, order_id, offering_name, 
      offering_type, main_offering, expenses_price, 
      expenses_term, validity_period_unit, order_quantity, 
      create_time, update_time, delete_time
      )
    values (#{id,jdbcType=VARCHAR}, #{orderId,jdbcType=VARCHAR}, #{offeringName,jdbcType=VARCHAR}, 
      #{offeringType,jdbcType=VARCHAR}, #{mainOffering,jdbcType=VARCHAR}, #{expensesPrice,jdbcType=VARCHAR}, 
      #{expensesTerm,jdbcType=VARCHAR}, #{validityPeriodUnit,jdbcType=VARCHAR}, #{orderQuantity,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deleteTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.CardValueAddedInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 09 17:24:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into card_value_added_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="offeringName != null">
        offering_name,
      </if>
      <if test="offeringType != null">
        offering_type,
      </if>
      <if test="mainOffering != null">
        main_offering,
      </if>
      <if test="expensesPrice != null">
        expenses_price,
      </if>
      <if test="expensesTerm != null">
        expenses_term,
      </if>
      <if test="validityPeriodUnit != null">
        validity_period_unit,
      </if>
      <if test="orderQuantity != null">
        order_quantity,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleteTime != null">
        delete_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="offeringName != null">
        #{offeringName,jdbcType=VARCHAR},
      </if>
      <if test="offeringType != null">
        #{offeringType,jdbcType=VARCHAR},
      </if>
      <if test="mainOffering != null">
        #{mainOffering,jdbcType=VARCHAR},
      </if>
      <if test="expensesPrice != null">
        #{expensesPrice,jdbcType=VARCHAR},
      </if>
      <if test="expensesTerm != null">
        #{expensesTerm,jdbcType=VARCHAR},
      </if>
      <if test="validityPeriodUnit != null">
        #{validityPeriodUnit,jdbcType=VARCHAR},
      </if>
      <if test="orderQuantity != null">
        #{orderQuantity,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteTime != null">
        #{deleteTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.CardValueAddedInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 09 17:24:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from card_value_added_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 09 17:24:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    update card_value_added_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.offeringName != null">
        offering_name = #{record.offeringName,jdbcType=VARCHAR},
      </if>
      <if test="record.offeringType != null">
        offering_type = #{record.offeringType,jdbcType=VARCHAR},
      </if>
      <if test="record.mainOffering != null">
        main_offering = #{record.mainOffering,jdbcType=VARCHAR},
      </if>
      <if test="record.expensesPrice != null">
        expenses_price = #{record.expensesPrice,jdbcType=VARCHAR},
      </if>
      <if test="record.expensesTerm != null">
        expenses_term = #{record.expensesTerm,jdbcType=VARCHAR},
      </if>
      <if test="record.validityPeriodUnit != null">
        validity_period_unit = #{record.validityPeriodUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.orderQuantity != null">
        order_quantity = #{record.orderQuantity,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleteTime != null">
        delete_time = #{record.deleteTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 09 17:24:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    update card_value_added_info
    set id = #{record.id,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      offering_name = #{record.offeringName,jdbcType=VARCHAR},
      offering_type = #{record.offeringType,jdbcType=VARCHAR},
      main_offering = #{record.mainOffering,jdbcType=VARCHAR},
      expenses_price = #{record.expensesPrice,jdbcType=VARCHAR},
      expenses_term = #{record.expensesTerm,jdbcType=VARCHAR},
      validity_period_unit = #{record.validityPeriodUnit,jdbcType=VARCHAR},
      order_quantity = #{record.orderQuantity,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      delete_time = #{record.deleteTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.CardValueAddedInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 09 17:24:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    update card_value_added_info
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="offeringName != null">
        offering_name = #{offeringName,jdbcType=VARCHAR},
      </if>
      <if test="offeringType != null">
        offering_type = #{offeringType,jdbcType=VARCHAR},
      </if>
      <if test="mainOffering != null">
        main_offering = #{mainOffering,jdbcType=VARCHAR},
      </if>
      <if test="expensesPrice != null">
        expenses_price = #{expensesPrice,jdbcType=VARCHAR},
      </if>
      <if test="expensesTerm != null">
        expenses_term = #{expensesTerm,jdbcType=VARCHAR},
      </if>
      <if test="validityPeriodUnit != null">
        validity_period_unit = #{validityPeriodUnit,jdbcType=VARCHAR},
      </if>
      <if test="orderQuantity != null">
        order_quantity = #{orderQuantity,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteTime != null">
        delete_time = #{deleteTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.CardValueAddedInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 09 17:24:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    update card_value_added_info
    set order_id = #{orderId,jdbcType=VARCHAR},
      offering_name = #{offeringName,jdbcType=VARCHAR},
      offering_type = #{offeringType,jdbcType=VARCHAR},
      main_offering = #{mainOffering,jdbcType=VARCHAR},
      expenses_price = #{expensesPrice,jdbcType=VARCHAR},
      expenses_term = #{expensesTerm,jdbcType=VARCHAR},
      validity_period_unit = #{validityPeriodUnit,jdbcType=VARCHAR},
      order_quantity = #{orderQuantity,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      delete_time = #{deleteTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 09 17:24:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into card_value_added_info
    (id, order_id, offering_name, offering_type, main_offering, expenses_price, expenses_term, 
      validity_period_unit, order_quantity, create_time, update_time, delete_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.orderId,jdbcType=VARCHAR}, #{item.offeringName,jdbcType=VARCHAR}, 
        #{item.offeringType,jdbcType=VARCHAR}, #{item.mainOffering,jdbcType=VARCHAR}, #{item.expensesPrice,jdbcType=VARCHAR}, 
        #{item.expensesTerm,jdbcType=VARCHAR}, #{item.validityPeriodUnit,jdbcType=VARCHAR}, 
        #{item.orderQuantity,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.deleteTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 09 17:24:06 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into card_value_added_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'offering_name'.toString() == column.value">
          #{item.offeringName,jdbcType=VARCHAR}
        </if>
        <if test="'offering_type'.toString() == column.value">
          #{item.offeringType,jdbcType=VARCHAR}
        </if>
        <if test="'main_offering'.toString() == column.value">
          #{item.mainOffering,jdbcType=VARCHAR}
        </if>
        <if test="'expenses_price'.toString() == column.value">
          #{item.expensesPrice,jdbcType=VARCHAR}
        </if>
        <if test="'expenses_term'.toString() == column.value">
          #{item.expensesTerm,jdbcType=VARCHAR}
        </if>
        <if test="'validity_period_unit'.toString() == column.value">
          #{item.validityPeriodUnit,jdbcType=VARCHAR}
        </if>
        <if test="'order_quantity'.toString() == column.value">
          #{item.orderQuantity,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'delete_time'.toString() == column.value">
          #{item.deleteTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>