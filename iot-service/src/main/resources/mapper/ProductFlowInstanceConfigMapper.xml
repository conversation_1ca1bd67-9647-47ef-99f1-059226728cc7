<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ProductFlowInstanceConfigMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.ProductFlowInstanceConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 05 17:28:46 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="flow_instance_id" jdbcType="VARCHAR" property="flowInstanceId" />
    <result column="flow_id" jdbcType="VARCHAR" property="flowId" />
    <result column="config_remark" jdbcType="VARCHAR" property="configRemark" />
    <result column="standard_service_name" jdbcType="VARCHAR" property="standardServiceName" />
    <result column="real_product_name" jdbcType="VARCHAR" property="realProductName" />
    <result column="product_property" jdbcType="VARCHAR" property="productProperty" />
    <result column="product_department" jdbcType="VARCHAR" property="productDepartment" />
    <result column="service_provider_name" jdbcType="VARCHAR" property="serviceProviderName" />
    <result column="order_partner_master_account" jdbcType="VARCHAR" property="orderPartnerMasterAccount" />
    <result column="order_partner_slave_account" jdbcType="VARCHAR" property="orderPartnerSlaveAccount" />
    <result column="before_sale_manager" jdbcType="VARCHAR" property="beforeSaleManager" />
    <result column="send_contact_person" jdbcType="VARCHAR" property="sendContactPerson" />
    <result column="install_contact_person" jdbcType="VARCHAR" property="installContactPerson" />
    <result column="iot_package_contact_person" jdbcType="VARCHAR" property="iotPackageContactPerson" />
    <result column="soft_auth_contact_person" jdbcType="VARCHAR" property="softAuthContactPerson" />
    <result column="after_sale_contact_person" jdbcType="VARCHAR" property="afterSaleContactPerson" />
    <result column="after_market_rule" jdbcType="VARCHAR" property="afterMarketRule" />
    <result column="repair_contact_info" jdbcType="VARCHAR" property="repairContactInfo" />
    <result column="return_contact_info" jdbcType="VARCHAR" property="returnContactInfo" />
    <result column="product_company_info" jdbcType="VARCHAR" property="productCompanyInfo" />
    <result column="product_communication_method" jdbcType="VARCHAR" property="productCommunicationMethod" />
    <result column="iot_package_info" jdbcType="VARCHAR" property="iotPackageInfo" />
    <result column="hardware_send_list" jdbcType="VARCHAR" property="hardwareSendList" />
    <result column="product_param_info" jdbcType="VARCHAR" property="productParamInfo" />
    <result column="product_send_address" jdbcType="VARCHAR" property="productSendAddress" />
    <result column="hardware_express" jdbcType="VARCHAR" property="hardwareExpress" />
    <result column="product_send_time_info" jdbcType="VARCHAR" property="productSendTimeInfo" />
    <result column="product_use_condition" jdbcType="VARCHAR" property="productUseCondition" />
    <result column="soft_platform_info" jdbcType="VARCHAR" property="softPlatformInfo" />
    <result column="soft_platform_download_info" jdbcType="VARCHAR" property="softPlatformDownloadInfo" />
    <result column="app_info" jdbcType="VARCHAR" property="appInfo" />
    <result column="app_download_info" jdbcType="VARCHAR" property="appDownloadInfo" />
    <result column="install_info" jdbcType="VARCHAR" property="installInfo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 05 17:28:46 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 05 17:28:46 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 05 17:28:46 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, flow_instance_id, flow_id, config_remark, standard_service_name, real_product_name, 
    product_property, product_department, service_provider_name, order_partner_master_account, 
    order_partner_slave_account, before_sale_manager, send_contact_person, install_contact_person, 
    iot_package_contact_person, soft_auth_contact_person, after_sale_contact_person, 
    after_market_rule, repair_contact_info, return_contact_info, product_company_info, 
    product_communication_method, iot_package_info, hardware_send_list, product_param_info, 
    product_send_address, hardware_express, product_send_time_info, product_use_condition, 
    soft_platform_info, soft_platform_download_info, app_info, app_download_info, install_info, 
    create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceConfigExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 05 17:28:46 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from product_flow_instance_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 05 17:28:46 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from product_flow_instance_config
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 05 17:28:46 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from product_flow_instance_config
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceConfigExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 05 17:28:46 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from product_flow_instance_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 05 17:28:46 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_config (id, flow_instance_id, flow_id, 
      config_remark, standard_service_name, real_product_name, 
      product_property, product_department, service_provider_name, 
      order_partner_master_account, order_partner_slave_account, 
      before_sale_manager, send_contact_person, install_contact_person, 
      iot_package_contact_person, soft_auth_contact_person, 
      after_sale_contact_person, after_market_rule, 
      repair_contact_info, return_contact_info, product_company_info, 
      product_communication_method, iot_package_info, 
      hardware_send_list, product_param_info, product_send_address, 
      hardware_express, product_send_time_info, product_use_condition, 
      soft_platform_info, soft_platform_download_info, 
      app_info, app_download_info, install_info, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{flowInstanceId,jdbcType=VARCHAR}, #{flowId,jdbcType=VARCHAR}, 
      #{configRemark,jdbcType=VARCHAR}, #{standardServiceName,jdbcType=VARCHAR}, #{realProductName,jdbcType=VARCHAR}, 
      #{productProperty,jdbcType=VARCHAR}, #{productDepartment,jdbcType=VARCHAR}, #{serviceProviderName,jdbcType=VARCHAR}, 
      #{orderPartnerMasterAccount,jdbcType=VARCHAR}, #{orderPartnerSlaveAccount,jdbcType=VARCHAR}, 
      #{beforeSaleManager,jdbcType=VARCHAR}, #{sendContactPerson,jdbcType=VARCHAR}, #{installContactPerson,jdbcType=VARCHAR}, 
      #{iotPackageContactPerson,jdbcType=VARCHAR}, #{softAuthContactPerson,jdbcType=VARCHAR}, 
      #{afterSaleContactPerson,jdbcType=VARCHAR}, #{afterMarketRule,jdbcType=VARCHAR}, 
      #{repairContactInfo,jdbcType=VARCHAR}, #{returnContactInfo,jdbcType=VARCHAR}, #{productCompanyInfo,jdbcType=VARCHAR}, 
      #{productCommunicationMethod,jdbcType=VARCHAR}, #{iotPackageInfo,jdbcType=VARCHAR}, 
      #{hardwareSendList,jdbcType=VARCHAR}, #{productParamInfo,jdbcType=VARCHAR}, #{productSendAddress,jdbcType=VARCHAR}, 
      #{hardwareExpress,jdbcType=VARCHAR}, #{productSendTimeInfo,jdbcType=VARCHAR}, #{productUseCondition,jdbcType=VARCHAR}, 
      #{softPlatformInfo,jdbcType=VARCHAR}, #{softPlatformDownloadInfo,jdbcType=VARCHAR}, 
      #{appInfo,jdbcType=VARCHAR}, #{appDownloadInfo,jdbcType=VARCHAR}, #{installInfo,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 05 17:28:46 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="flowInstanceId != null">
        flow_instance_id,
      </if>
      <if test="flowId != null">
        flow_id,
      </if>
      <if test="configRemark != null">
        config_remark,
      </if>
      <if test="standardServiceName != null">
        standard_service_name,
      </if>
      <if test="realProductName != null">
        real_product_name,
      </if>
      <if test="productProperty != null">
        product_property,
      </if>
      <if test="productDepartment != null">
        product_department,
      </if>
      <if test="serviceProviderName != null">
        service_provider_name,
      </if>
      <if test="orderPartnerMasterAccount != null">
        order_partner_master_account,
      </if>
      <if test="orderPartnerSlaveAccount != null">
        order_partner_slave_account,
      </if>
      <if test="beforeSaleManager != null">
        before_sale_manager,
      </if>
      <if test="sendContactPerson != null">
        send_contact_person,
      </if>
      <if test="installContactPerson != null">
        install_contact_person,
      </if>
      <if test="iotPackageContactPerson != null">
        iot_package_contact_person,
      </if>
      <if test="softAuthContactPerson != null">
        soft_auth_contact_person,
      </if>
      <if test="afterSaleContactPerson != null">
        after_sale_contact_person,
      </if>
      <if test="afterMarketRule != null">
        after_market_rule,
      </if>
      <if test="repairContactInfo != null">
        repair_contact_info,
      </if>
      <if test="returnContactInfo != null">
        return_contact_info,
      </if>
      <if test="productCompanyInfo != null">
        product_company_info,
      </if>
      <if test="productCommunicationMethod != null">
        product_communication_method,
      </if>
      <if test="iotPackageInfo != null">
        iot_package_info,
      </if>
      <if test="hardwareSendList != null">
        hardware_send_list,
      </if>
      <if test="productParamInfo != null">
        product_param_info,
      </if>
      <if test="productSendAddress != null">
        product_send_address,
      </if>
      <if test="hardwareExpress != null">
        hardware_express,
      </if>
      <if test="productSendTimeInfo != null">
        product_send_time_info,
      </if>
      <if test="productUseCondition != null">
        product_use_condition,
      </if>
      <if test="softPlatformInfo != null">
        soft_platform_info,
      </if>
      <if test="softPlatformDownloadInfo != null">
        soft_platform_download_info,
      </if>
      <if test="appInfo != null">
        app_info,
      </if>
      <if test="appDownloadInfo != null">
        app_download_info,
      </if>
      <if test="installInfo != null">
        install_info,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="flowInstanceId != null">
        #{flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="flowId != null">
        #{flowId,jdbcType=VARCHAR},
      </if>
      <if test="configRemark != null">
        #{configRemark,jdbcType=VARCHAR},
      </if>
      <if test="standardServiceName != null">
        #{standardServiceName,jdbcType=VARCHAR},
      </if>
      <if test="realProductName != null">
        #{realProductName,jdbcType=VARCHAR},
      </if>
      <if test="productProperty != null">
        #{productProperty,jdbcType=VARCHAR},
      </if>
      <if test="productDepartment != null">
        #{productDepartment,jdbcType=VARCHAR},
      </if>
      <if test="serviceProviderName != null">
        #{serviceProviderName,jdbcType=VARCHAR},
      </if>
      <if test="orderPartnerMasterAccount != null">
        #{orderPartnerMasterAccount,jdbcType=VARCHAR},
      </if>
      <if test="orderPartnerSlaveAccount != null">
        #{orderPartnerSlaveAccount,jdbcType=VARCHAR},
      </if>
      <if test="beforeSaleManager != null">
        #{beforeSaleManager,jdbcType=VARCHAR},
      </if>
      <if test="sendContactPerson != null">
        #{sendContactPerson,jdbcType=VARCHAR},
      </if>
      <if test="installContactPerson != null">
        #{installContactPerson,jdbcType=VARCHAR},
      </if>
      <if test="iotPackageContactPerson != null">
        #{iotPackageContactPerson,jdbcType=VARCHAR},
      </if>
      <if test="softAuthContactPerson != null">
        #{softAuthContactPerson,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleContactPerson != null">
        #{afterSaleContactPerson,jdbcType=VARCHAR},
      </if>
      <if test="afterMarketRule != null">
        #{afterMarketRule,jdbcType=VARCHAR},
      </if>
      <if test="repairContactInfo != null">
        #{repairContactInfo,jdbcType=VARCHAR},
      </if>
      <if test="returnContactInfo != null">
        #{returnContactInfo,jdbcType=VARCHAR},
      </if>
      <if test="productCompanyInfo != null">
        #{productCompanyInfo,jdbcType=VARCHAR},
      </if>
      <if test="productCommunicationMethod != null">
        #{productCommunicationMethod,jdbcType=VARCHAR},
      </if>
      <if test="iotPackageInfo != null">
        #{iotPackageInfo,jdbcType=VARCHAR},
      </if>
      <if test="hardwareSendList != null">
        #{hardwareSendList,jdbcType=VARCHAR},
      </if>
      <if test="productParamInfo != null">
        #{productParamInfo,jdbcType=VARCHAR},
      </if>
      <if test="productSendAddress != null">
        #{productSendAddress,jdbcType=VARCHAR},
      </if>
      <if test="hardwareExpress != null">
        #{hardwareExpress,jdbcType=VARCHAR},
      </if>
      <if test="productSendTimeInfo != null">
        #{productSendTimeInfo,jdbcType=VARCHAR},
      </if>
      <if test="productUseCondition != null">
        #{productUseCondition,jdbcType=VARCHAR},
      </if>
      <if test="softPlatformInfo != null">
        #{softPlatformInfo,jdbcType=VARCHAR},
      </if>
      <if test="softPlatformDownloadInfo != null">
        #{softPlatformDownloadInfo,jdbcType=VARCHAR},
      </if>
      <if test="appInfo != null">
        #{appInfo,jdbcType=VARCHAR},
      </if>
      <if test="appDownloadInfo != null">
        #{appDownloadInfo,jdbcType=VARCHAR},
      </if>
      <if test="installInfo != null">
        #{installInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceConfigExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 05 17:28:46 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from product_flow_instance_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 05 17:28:46 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.flowInstanceId != null">
        flow_instance_id = #{record.flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.flowId != null">
        flow_id = #{record.flowId,jdbcType=VARCHAR},
      </if>
      <if test="record.configRemark != null">
        config_remark = #{record.configRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.standardServiceName != null">
        standard_service_name = #{record.standardServiceName,jdbcType=VARCHAR},
      </if>
      <if test="record.realProductName != null">
        real_product_name = #{record.realProductName,jdbcType=VARCHAR},
      </if>
      <if test="record.productProperty != null">
        product_property = #{record.productProperty,jdbcType=VARCHAR},
      </if>
      <if test="record.productDepartment != null">
        product_department = #{record.productDepartment,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceProviderName != null">
        service_provider_name = #{record.serviceProviderName,jdbcType=VARCHAR},
      </if>
      <if test="record.orderPartnerMasterAccount != null">
        order_partner_master_account = #{record.orderPartnerMasterAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.orderPartnerSlaveAccount != null">
        order_partner_slave_account = #{record.orderPartnerSlaveAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.beforeSaleManager != null">
        before_sale_manager = #{record.beforeSaleManager,jdbcType=VARCHAR},
      </if>
      <if test="record.sendContactPerson != null">
        send_contact_person = #{record.sendContactPerson,jdbcType=VARCHAR},
      </if>
      <if test="record.installContactPerson != null">
        install_contact_person = #{record.installContactPerson,jdbcType=VARCHAR},
      </if>
      <if test="record.iotPackageContactPerson != null">
        iot_package_contact_person = #{record.iotPackageContactPerson,jdbcType=VARCHAR},
      </if>
      <if test="record.softAuthContactPerson != null">
        soft_auth_contact_person = #{record.softAuthContactPerson,jdbcType=VARCHAR},
      </if>
      <if test="record.afterSaleContactPerson != null">
        after_sale_contact_person = #{record.afterSaleContactPerson,jdbcType=VARCHAR},
      </if>
      <if test="record.afterMarketRule != null">
        after_market_rule = #{record.afterMarketRule,jdbcType=VARCHAR},
      </if>
      <if test="record.repairContactInfo != null">
        repair_contact_info = #{record.repairContactInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.returnContactInfo != null">
        return_contact_info = #{record.returnContactInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.productCompanyInfo != null">
        product_company_info = #{record.productCompanyInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.productCommunicationMethod != null">
        product_communication_method = #{record.productCommunicationMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.iotPackageInfo != null">
        iot_package_info = #{record.iotPackageInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.hardwareSendList != null">
        hardware_send_list = #{record.hardwareSendList,jdbcType=VARCHAR},
      </if>
      <if test="record.productParamInfo != null">
        product_param_info = #{record.productParamInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.productSendAddress != null">
        product_send_address = #{record.productSendAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.hardwareExpress != null">
        hardware_express = #{record.hardwareExpress,jdbcType=VARCHAR},
      </if>
      <if test="record.productSendTimeInfo != null">
        product_send_time_info = #{record.productSendTimeInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.productUseCondition != null">
        product_use_condition = #{record.productUseCondition,jdbcType=VARCHAR},
      </if>
      <if test="record.softPlatformInfo != null">
        soft_platform_info = #{record.softPlatformInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.softPlatformDownloadInfo != null">
        soft_platform_download_info = #{record.softPlatformDownloadInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.appInfo != null">
        app_info = #{record.appInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.appDownloadInfo != null">
        app_download_info = #{record.appDownloadInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.installInfo != null">
        install_info = #{record.installInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 05 17:28:46 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_config
    set id = #{record.id,jdbcType=VARCHAR},
      flow_instance_id = #{record.flowInstanceId,jdbcType=VARCHAR},
      flow_id = #{record.flowId,jdbcType=VARCHAR},
      config_remark = #{record.configRemark,jdbcType=VARCHAR},
      standard_service_name = #{record.standardServiceName,jdbcType=VARCHAR},
      real_product_name = #{record.realProductName,jdbcType=VARCHAR},
      product_property = #{record.productProperty,jdbcType=VARCHAR},
      product_department = #{record.productDepartment,jdbcType=VARCHAR},
      service_provider_name = #{record.serviceProviderName,jdbcType=VARCHAR},
      order_partner_master_account = #{record.orderPartnerMasterAccount,jdbcType=VARCHAR},
      order_partner_slave_account = #{record.orderPartnerSlaveAccount,jdbcType=VARCHAR},
      before_sale_manager = #{record.beforeSaleManager,jdbcType=VARCHAR},
      send_contact_person = #{record.sendContactPerson,jdbcType=VARCHAR},
      install_contact_person = #{record.installContactPerson,jdbcType=VARCHAR},
      iot_package_contact_person = #{record.iotPackageContactPerson,jdbcType=VARCHAR},
      soft_auth_contact_person = #{record.softAuthContactPerson,jdbcType=VARCHAR},
      after_sale_contact_person = #{record.afterSaleContactPerson,jdbcType=VARCHAR},
      after_market_rule = #{record.afterMarketRule,jdbcType=VARCHAR},
      repair_contact_info = #{record.repairContactInfo,jdbcType=VARCHAR},
      return_contact_info = #{record.returnContactInfo,jdbcType=VARCHAR},
      product_company_info = #{record.productCompanyInfo,jdbcType=VARCHAR},
      product_communication_method = #{record.productCommunicationMethod,jdbcType=VARCHAR},
      iot_package_info = #{record.iotPackageInfo,jdbcType=VARCHAR},
      hardware_send_list = #{record.hardwareSendList,jdbcType=VARCHAR},
      product_param_info = #{record.productParamInfo,jdbcType=VARCHAR},
      product_send_address = #{record.productSendAddress,jdbcType=VARCHAR},
      hardware_express = #{record.hardwareExpress,jdbcType=VARCHAR},
      product_send_time_info = #{record.productSendTimeInfo,jdbcType=VARCHAR},
      product_use_condition = #{record.productUseCondition,jdbcType=VARCHAR},
      soft_platform_info = #{record.softPlatformInfo,jdbcType=VARCHAR},
      soft_platform_download_info = #{record.softPlatformDownloadInfo,jdbcType=VARCHAR},
      app_info = #{record.appInfo,jdbcType=VARCHAR},
      app_download_info = #{record.appDownloadInfo,jdbcType=VARCHAR},
      install_info = #{record.installInfo,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 05 17:28:46 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_config
    <set>
      <if test="flowInstanceId != null">
        flow_instance_id = #{flowInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="flowId != null">
        flow_id = #{flowId,jdbcType=VARCHAR},
      </if>
      <if test="configRemark != null">
        config_remark = #{configRemark,jdbcType=VARCHAR},
      </if>
      <if test="standardServiceName != null">
        standard_service_name = #{standardServiceName,jdbcType=VARCHAR},
      </if>
      <if test="realProductName != null">
        real_product_name = #{realProductName,jdbcType=VARCHAR},
      </if>
      <if test="productProperty != null">
        product_property = #{productProperty,jdbcType=VARCHAR},
      </if>
      <if test="productDepartment != null">
        product_department = #{productDepartment,jdbcType=VARCHAR},
      </if>
      <if test="serviceProviderName != null">
        service_provider_name = #{serviceProviderName,jdbcType=VARCHAR},
      </if>
      <if test="orderPartnerMasterAccount != null">
        order_partner_master_account = #{orderPartnerMasterAccount,jdbcType=VARCHAR},
      </if>
      <if test="orderPartnerSlaveAccount != null">
        order_partner_slave_account = #{orderPartnerSlaveAccount,jdbcType=VARCHAR},
      </if>
      <if test="beforeSaleManager != null">
        before_sale_manager = #{beforeSaleManager,jdbcType=VARCHAR},
      </if>
      <if test="sendContactPerson != null">
        send_contact_person = #{sendContactPerson,jdbcType=VARCHAR},
      </if>
      <if test="installContactPerson != null">
        install_contact_person = #{installContactPerson,jdbcType=VARCHAR},
      </if>
      <if test="iotPackageContactPerson != null">
        iot_package_contact_person = #{iotPackageContactPerson,jdbcType=VARCHAR},
      </if>
      <if test="softAuthContactPerson != null">
        soft_auth_contact_person = #{softAuthContactPerson,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleContactPerson != null">
        after_sale_contact_person = #{afterSaleContactPerson,jdbcType=VARCHAR},
      </if>
      <if test="afterMarketRule != null">
        after_market_rule = #{afterMarketRule,jdbcType=VARCHAR},
      </if>
      <if test="repairContactInfo != null">
        repair_contact_info = #{repairContactInfo,jdbcType=VARCHAR},
      </if>
      <if test="returnContactInfo != null">
        return_contact_info = #{returnContactInfo,jdbcType=VARCHAR},
      </if>
      <if test="productCompanyInfo != null">
        product_company_info = #{productCompanyInfo,jdbcType=VARCHAR},
      </if>
      <if test="productCommunicationMethod != null">
        product_communication_method = #{productCommunicationMethod,jdbcType=VARCHAR},
      </if>
      <if test="iotPackageInfo != null">
        iot_package_info = #{iotPackageInfo,jdbcType=VARCHAR},
      </if>
      <if test="hardwareSendList != null">
        hardware_send_list = #{hardwareSendList,jdbcType=VARCHAR},
      </if>
      <if test="productParamInfo != null">
        product_param_info = #{productParamInfo,jdbcType=VARCHAR},
      </if>
      <if test="productSendAddress != null">
        product_send_address = #{productSendAddress,jdbcType=VARCHAR},
      </if>
      <if test="hardwareExpress != null">
        hardware_express = #{hardwareExpress,jdbcType=VARCHAR},
      </if>
      <if test="productSendTimeInfo != null">
        product_send_time_info = #{productSendTimeInfo,jdbcType=VARCHAR},
      </if>
      <if test="productUseCondition != null">
        product_use_condition = #{productUseCondition,jdbcType=VARCHAR},
      </if>
      <if test="softPlatformInfo != null">
        soft_platform_info = #{softPlatformInfo,jdbcType=VARCHAR},
      </if>
      <if test="softPlatformDownloadInfo != null">
        soft_platform_download_info = #{softPlatformDownloadInfo,jdbcType=VARCHAR},
      </if>
      <if test="appInfo != null">
        app_info = #{appInfo,jdbcType=VARCHAR},
      </if>
      <if test="appDownloadInfo != null">
        app_download_info = #{appDownloadInfo,jdbcType=VARCHAR},
      </if>
      <if test="installInfo != null">
        install_info = #{installInfo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.ProductFlowInstanceConfig">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 05 17:28:46 CST 2024. by MyBatis Generator, do not modify.
    -->
    update product_flow_instance_config
    set flow_instance_id = #{flowInstanceId,jdbcType=VARCHAR},
      flow_id = #{flowId,jdbcType=VARCHAR},
      config_remark = #{configRemark,jdbcType=VARCHAR},
      standard_service_name = #{standardServiceName,jdbcType=VARCHAR},
      real_product_name = #{realProductName,jdbcType=VARCHAR},
      product_property = #{productProperty,jdbcType=VARCHAR},
      product_department = #{productDepartment,jdbcType=VARCHAR},
      service_provider_name = #{serviceProviderName,jdbcType=VARCHAR},
      order_partner_master_account = #{orderPartnerMasterAccount,jdbcType=VARCHAR},
      order_partner_slave_account = #{orderPartnerSlaveAccount,jdbcType=VARCHAR},
      before_sale_manager = #{beforeSaleManager,jdbcType=VARCHAR},
      send_contact_person = #{sendContactPerson,jdbcType=VARCHAR},
      install_contact_person = #{installContactPerson,jdbcType=VARCHAR},
      iot_package_contact_person = #{iotPackageContactPerson,jdbcType=VARCHAR},
      soft_auth_contact_person = #{softAuthContactPerson,jdbcType=VARCHAR},
      after_sale_contact_person = #{afterSaleContactPerson,jdbcType=VARCHAR},
      after_market_rule = #{afterMarketRule,jdbcType=VARCHAR},
      repair_contact_info = #{repairContactInfo,jdbcType=VARCHAR},
      return_contact_info = #{returnContactInfo,jdbcType=VARCHAR},
      product_company_info = #{productCompanyInfo,jdbcType=VARCHAR},
      product_communication_method = #{productCommunicationMethod,jdbcType=VARCHAR},
      iot_package_info = #{iotPackageInfo,jdbcType=VARCHAR},
      hardware_send_list = #{hardwareSendList,jdbcType=VARCHAR},
      product_param_info = #{productParamInfo,jdbcType=VARCHAR},
      product_send_address = #{productSendAddress,jdbcType=VARCHAR},
      hardware_express = #{hardwareExpress,jdbcType=VARCHAR},
      product_send_time_info = #{productSendTimeInfo,jdbcType=VARCHAR},
      product_use_condition = #{productUseCondition,jdbcType=VARCHAR},
      soft_platform_info = #{softPlatformInfo,jdbcType=VARCHAR},
      soft_platform_download_info = #{softPlatformDownloadInfo,jdbcType=VARCHAR},
      app_info = #{appInfo,jdbcType=VARCHAR},
      app_download_info = #{appDownloadInfo,jdbcType=VARCHAR},
      install_info = #{installInfo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 05 17:28:46 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_config
    (id, flow_instance_id, flow_id, config_remark, standard_service_name, real_product_name, 
      product_property, product_department, service_provider_name, order_partner_master_account, 
      order_partner_slave_account, before_sale_manager, send_contact_person, install_contact_person, 
      iot_package_contact_person, soft_auth_contact_person, after_sale_contact_person, 
      after_market_rule, repair_contact_info, return_contact_info, product_company_info, 
      product_communication_method, iot_package_info, hardware_send_list, product_param_info, 
      product_send_address, hardware_express, product_send_time_info, product_use_condition, 
      soft_platform_info, soft_platform_download_info, app_info, app_download_info, install_info, 
      create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.flowInstanceId,jdbcType=VARCHAR}, #{item.flowId,jdbcType=VARCHAR}, 
        #{item.configRemark,jdbcType=VARCHAR}, #{item.standardServiceName,jdbcType=VARCHAR}, 
        #{item.realProductName,jdbcType=VARCHAR}, #{item.productProperty,jdbcType=VARCHAR}, 
        #{item.productDepartment,jdbcType=VARCHAR}, #{item.serviceProviderName,jdbcType=VARCHAR}, 
        #{item.orderPartnerMasterAccount,jdbcType=VARCHAR}, #{item.orderPartnerSlaveAccount,jdbcType=VARCHAR}, 
        #{item.beforeSaleManager,jdbcType=VARCHAR}, #{item.sendContactPerson,jdbcType=VARCHAR}, 
        #{item.installContactPerson,jdbcType=VARCHAR}, #{item.iotPackageContactPerson,jdbcType=VARCHAR}, 
        #{item.softAuthContactPerson,jdbcType=VARCHAR}, #{item.afterSaleContactPerson,jdbcType=VARCHAR}, 
        #{item.afterMarketRule,jdbcType=VARCHAR}, #{item.repairContactInfo,jdbcType=VARCHAR}, 
        #{item.returnContactInfo,jdbcType=VARCHAR}, #{item.productCompanyInfo,jdbcType=VARCHAR}, 
        #{item.productCommunicationMethod,jdbcType=VARCHAR}, #{item.iotPackageInfo,jdbcType=VARCHAR}, 
        #{item.hardwareSendList,jdbcType=VARCHAR}, #{item.productParamInfo,jdbcType=VARCHAR}, 
        #{item.productSendAddress,jdbcType=VARCHAR}, #{item.hardwareExpress,jdbcType=VARCHAR}, 
        #{item.productSendTimeInfo,jdbcType=VARCHAR}, #{item.productUseCondition,jdbcType=VARCHAR}, 
        #{item.softPlatformInfo,jdbcType=VARCHAR}, #{item.softPlatformDownloadInfo,jdbcType=VARCHAR}, 
        #{item.appInfo,jdbcType=VARCHAR}, #{item.appDownloadInfo,jdbcType=VARCHAR}, #{item.installInfo,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Mar 05 17:28:46 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into product_flow_instance_config (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'flow_instance_id'.toString() == column.value">
          #{item.flowInstanceId,jdbcType=VARCHAR}
        </if>
        <if test="'flow_id'.toString() == column.value">
          #{item.flowId,jdbcType=VARCHAR}
        </if>
        <if test="'config_remark'.toString() == column.value">
          #{item.configRemark,jdbcType=VARCHAR}
        </if>
        <if test="'standard_service_name'.toString() == column.value">
          #{item.standardServiceName,jdbcType=VARCHAR}
        </if>
        <if test="'real_product_name'.toString() == column.value">
          #{item.realProductName,jdbcType=VARCHAR}
        </if>
        <if test="'product_property'.toString() == column.value">
          #{item.productProperty,jdbcType=VARCHAR}
        </if>
        <if test="'product_department'.toString() == column.value">
          #{item.productDepartment,jdbcType=VARCHAR}
        </if>
        <if test="'service_provider_name'.toString() == column.value">
          #{item.serviceProviderName,jdbcType=VARCHAR}
        </if>
        <if test="'order_partner_master_account'.toString() == column.value">
          #{item.orderPartnerMasterAccount,jdbcType=VARCHAR}
        </if>
        <if test="'order_partner_slave_account'.toString() == column.value">
          #{item.orderPartnerSlaveAccount,jdbcType=VARCHAR}
        </if>
        <if test="'before_sale_manager'.toString() == column.value">
          #{item.beforeSaleManager,jdbcType=VARCHAR}
        </if>
        <if test="'send_contact_person'.toString() == column.value">
          #{item.sendContactPerson,jdbcType=VARCHAR}
        </if>
        <if test="'install_contact_person'.toString() == column.value">
          #{item.installContactPerson,jdbcType=VARCHAR}
        </if>
        <if test="'iot_package_contact_person'.toString() == column.value">
          #{item.iotPackageContactPerson,jdbcType=VARCHAR}
        </if>
        <if test="'soft_auth_contact_person'.toString() == column.value">
          #{item.softAuthContactPerson,jdbcType=VARCHAR}
        </if>
        <if test="'after_sale_contact_person'.toString() == column.value">
          #{item.afterSaleContactPerson,jdbcType=VARCHAR}
        </if>
        <if test="'after_market_rule'.toString() == column.value">
          #{item.afterMarketRule,jdbcType=VARCHAR}
        </if>
        <if test="'repair_contact_info'.toString() == column.value">
          #{item.repairContactInfo,jdbcType=VARCHAR}
        </if>
        <if test="'return_contact_info'.toString() == column.value">
          #{item.returnContactInfo,jdbcType=VARCHAR}
        </if>
        <if test="'product_company_info'.toString() == column.value">
          #{item.productCompanyInfo,jdbcType=VARCHAR}
        </if>
        <if test="'product_communication_method'.toString() == column.value">
          #{item.productCommunicationMethod,jdbcType=VARCHAR}
        </if>
        <if test="'iot_package_info'.toString() == column.value">
          #{item.iotPackageInfo,jdbcType=VARCHAR}
        </if>
        <if test="'hardware_send_list'.toString() == column.value">
          #{item.hardwareSendList,jdbcType=VARCHAR}
        </if>
        <if test="'product_param_info'.toString() == column.value">
          #{item.productParamInfo,jdbcType=VARCHAR}
        </if>
        <if test="'product_send_address'.toString() == column.value">
          #{item.productSendAddress,jdbcType=VARCHAR}
        </if>
        <if test="'hardware_express'.toString() == column.value">
          #{item.hardwareExpress,jdbcType=VARCHAR}
        </if>
        <if test="'product_send_time_info'.toString() == column.value">
          #{item.productSendTimeInfo,jdbcType=VARCHAR}
        </if>
        <if test="'product_use_condition'.toString() == column.value">
          #{item.productUseCondition,jdbcType=VARCHAR}
        </if>
        <if test="'soft_platform_info'.toString() == column.value">
          #{item.softPlatformInfo,jdbcType=VARCHAR}
        </if>
        <if test="'soft_platform_download_info'.toString() == column.value">
          #{item.softPlatformDownloadInfo,jdbcType=VARCHAR}
        </if>
        <if test="'app_info'.toString() == column.value">
          #{item.appInfo,jdbcType=VARCHAR}
        </if>
        <if test="'app_download_info'.toString() == column.value">
          #{item.appDownloadInfo,jdbcType=VARCHAR}
        </if>
        <if test="'install_info'.toString() == column.value">
          #{item.installInfo,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>