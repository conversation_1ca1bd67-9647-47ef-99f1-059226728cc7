<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.AftermarketOrderHistoryMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.AftermarketOrderHistory">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="service_order_id" jdbcType="VARCHAR" property="serviceOrderId" />
    <result column="refund_serv_order_id" jdbcType="VARCHAR" property="refundServOrderId" />
    <result column="operate_type" jdbcType="INTEGER" property="operateType" />
    <result column="inner_status" jdbcType="INTEGER" property="innerStatus" />
    <result column="operator_id" jdbcType="VARCHAR" property="operatorId" />
    <result column="operate_message" jdbcType="VARCHAR" property="operateMessage" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, service_order_id, refund_serv_order_id, operate_type, inner_status, operator_id, 
    operate_message, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.AftermarketOrderHistoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from aftermarket_order_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from aftermarket_order_history
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from aftermarket_order_history
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.AftermarketOrderHistoryExample">
    delete from aftermarket_order_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.AftermarketOrderHistory">
    insert into aftermarket_order_history (id, service_order_id, refund_serv_order_id, 
      operate_type, inner_status, operator_id, 
      operate_message, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{serviceOrderId,jdbcType=VARCHAR}, #{refundServOrderId,jdbcType=VARCHAR}, 
      #{operateType,jdbcType=INTEGER}, #{innerStatus,jdbcType=INTEGER}, #{operatorId,jdbcType=VARCHAR}, 
      #{operateMessage,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.AftermarketOrderHistory">
    insert into aftermarket_order_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="serviceOrderId != null">
        service_order_id,
      </if>
      <if test="refundServOrderId != null">
        refund_serv_order_id,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="innerStatus != null">
        inner_status,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operateMessage != null">
        operate_message,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="serviceOrderId != null">
        #{serviceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="refundServOrderId != null">
        #{refundServOrderId,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=INTEGER},
      </if>
      <if test="innerStatus != null">
        #{innerStatus,jdbcType=INTEGER},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="operateMessage != null">
        #{operateMessage,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.AftermarketOrderHistoryExample" resultType="java.lang.Long">
    select count(*) from aftermarket_order_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update aftermarket_order_history
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceOrderId != null">
        service_order_id = #{record.serviceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.refundServOrderId != null">
        refund_serv_order_id = #{record.refundServOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.operateType != null">
        operate_type = #{record.operateType,jdbcType=INTEGER},
      </if>
      <if test="record.innerStatus != null">
        inner_status = #{record.innerStatus,jdbcType=INTEGER},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=VARCHAR},
      </if>
      <if test="record.operateMessage != null">
        operate_message = #{record.operateMessage,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update aftermarket_order_history
    set id = #{record.id,jdbcType=VARCHAR},
      service_order_id = #{record.serviceOrderId,jdbcType=VARCHAR},
      refund_serv_order_id = #{record.refundServOrderId,jdbcType=VARCHAR},
      operate_type = #{record.operateType,jdbcType=INTEGER},
      inner_status = #{record.innerStatus,jdbcType=INTEGER},
      operator_id = #{record.operatorId,jdbcType=VARCHAR},
      operate_message = #{record.operateMessage,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.AftermarketOrderHistory">
    update aftermarket_order_history
    <set>
      <if test="serviceOrderId != null">
        service_order_id = #{serviceOrderId,jdbcType=VARCHAR},
      </if>
      <if test="refundServOrderId != null">
        refund_serv_order_id = #{refundServOrderId,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=INTEGER},
      </if>
      <if test="innerStatus != null">
        inner_status = #{innerStatus,jdbcType=INTEGER},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=VARCHAR},
      </if>
      <if test="operateMessage != null">
        operate_message = #{operateMessage,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.AftermarketOrderHistory">
    update aftermarket_order_history
    set service_order_id = #{serviceOrderId,jdbcType=VARCHAR},
      refund_serv_order_id = #{refundServOrderId,jdbcType=VARCHAR},
      operate_type = #{operateType,jdbcType=INTEGER},
      inner_status = #{innerStatus,jdbcType=INTEGER},
      operator_id = #{operatorId,jdbcType=VARCHAR},
      operate_message = #{operateMessage,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into aftermarket_order_history
    (id, service_order_id, refund_serv_order_id, operate_type, inner_status, operator_id, 
      operate_message, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.serviceOrderId,jdbcType=VARCHAR}, #{item.refundServOrderId,jdbcType=VARCHAR}, 
        #{item.operateType,jdbcType=INTEGER}, #{item.innerStatus,jdbcType=INTEGER}, #{item.operatorId,jdbcType=VARCHAR}, 
        #{item.operateMessage,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into aftermarket_order_history (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'service_order_id'.toString() == column.value">
          #{item.serviceOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'refund_serv_order_id'.toString() == column.value">
          #{item.refundServOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'operate_type'.toString() == column.value">
          #{item.operateType,jdbcType=INTEGER}
        </if>
        <if test="'inner_status'.toString() == column.value">
          #{item.innerStatus,jdbcType=INTEGER}
        </if>
        <if test="'operator_id'.toString() == column.value">
          #{item.operatorId,jdbcType=VARCHAR}
        </if>
        <if test="'operate_message'.toString() == column.value">
          #{item.operateMessage,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>