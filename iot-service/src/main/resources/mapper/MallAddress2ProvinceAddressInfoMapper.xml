<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.MallAddress2ProvinceAddressInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.MallAddress2ProvinceAddressInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 06 10:19:32 CST 2023. by MyBatis Generator, do not modify.
    -->
    <result column="district_province_name" jdbcType="VARCHAR" property="districtProvinceName" />
    <result column="district_province_code" jdbcType="VARCHAR" property="districtProvinceCode" />
    <result column="district_mall_code" jdbcType="VARCHAR" property="districtMallCode" />
    <result column="district_mall_name" jdbcType="VARCHAR" property="districtMallName" />
    <result column="city_mall_name" jdbcType="VARCHAR" property="cityMallName" />
    <result column="city_province_name" jdbcType="VARCHAR" property="cityProvinceName" />
    <result column="city_province_code" jdbcType="VARCHAR" property="cityProvinceCode" />
    <result column="city_mall_code" jdbcType="VARCHAR" property="cityMallCode" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="province_province_code" jdbcType="VARCHAR" property="provinceProvinceCode" />
    <result column="province_mall_code" jdbcType="VARCHAR" property="provinceMallCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 06 10:19:32 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 06 10:19:32 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 06 10:19:32 CST 2023. by MyBatis Generator, do not modify.
    -->
    district_province_name, district_province_code, district_mall_code, district_mall_name, 
    city_mall_name, city_province_name, city_province_code, city_mall_code, province_name, 
    province_province_code, province_mall_code
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.MallAddress2ProvinceAddressInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 06 10:19:32 CST 2023. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mall_address_2_province_address_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.MallAddress2ProvinceAddressInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 06 10:19:32 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from mall_address_2_province_address_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.MallAddress2ProvinceAddressInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 06 10:19:32 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into mall_address_2_province_address_info (district_province_name, district_province_code, 
      district_mall_code, district_mall_name, city_mall_name, 
      city_province_name, city_province_code, city_mall_code, 
      province_name, province_province_code, province_mall_code
      )
    values (#{districtProvinceName,jdbcType=VARCHAR}, #{districtProvinceCode,jdbcType=VARCHAR}, 
      #{districtMallCode,jdbcType=VARCHAR}, #{districtMallName,jdbcType=VARCHAR}, #{cityMallName,jdbcType=VARCHAR}, 
      #{cityProvinceName,jdbcType=VARCHAR}, #{cityProvinceCode,jdbcType=VARCHAR}, #{cityMallCode,jdbcType=VARCHAR}, 
      #{provinceName,jdbcType=VARCHAR}, #{provinceProvinceCode,jdbcType=VARCHAR}, #{provinceMallCode,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.MallAddress2ProvinceAddressInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 06 10:19:32 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into mall_address_2_province_address_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="districtProvinceName != null">
        district_province_name,
      </if>
      <if test="districtProvinceCode != null">
        district_province_code,
      </if>
      <if test="districtMallCode != null">
        district_mall_code,
      </if>
      <if test="districtMallName != null">
        district_mall_name,
      </if>
      <if test="cityMallName != null">
        city_mall_name,
      </if>
      <if test="cityProvinceName != null">
        city_province_name,
      </if>
      <if test="cityProvinceCode != null">
        city_province_code,
      </if>
      <if test="cityMallCode != null">
        city_mall_code,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="provinceProvinceCode != null">
        province_province_code,
      </if>
      <if test="provinceMallCode != null">
        province_mall_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="districtProvinceName != null">
        #{districtProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="districtProvinceCode != null">
        #{districtProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="districtMallCode != null">
        #{districtMallCode,jdbcType=VARCHAR},
      </if>
      <if test="districtMallName != null">
        #{districtMallName,jdbcType=VARCHAR},
      </if>
      <if test="cityMallName != null">
        #{cityMallName,jdbcType=VARCHAR},
      </if>
      <if test="cityProvinceName != null">
        #{cityProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityProvinceCode != null">
        #{cityProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityMallCode != null">
        #{cityMallCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="provinceProvinceCode != null">
        #{provinceProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceMallCode != null">
        #{provinceMallCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.MallAddress2ProvinceAddressInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 06 10:19:32 CST 2023. by MyBatis Generator, do not modify.
    -->
    select count(*) from mall_address_2_province_address_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 06 10:19:32 CST 2023. by MyBatis Generator, do not modify.
    -->
    update mall_address_2_province_address_info
    <set>
      <if test="record.districtProvinceName != null">
        district_province_name = #{record.districtProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.districtProvinceCode != null">
        district_province_code = #{record.districtProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.districtMallCode != null">
        district_mall_code = #{record.districtMallCode,jdbcType=VARCHAR},
      </if>
      <if test="record.districtMallName != null">
        district_mall_name = #{record.districtMallName,jdbcType=VARCHAR},
      </if>
      <if test="record.cityMallName != null">
        city_mall_name = #{record.cityMallName,jdbcType=VARCHAR},
      </if>
      <if test="record.cityProvinceName != null">
        city_province_name = #{record.cityProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.cityProvinceCode != null">
        city_province_code = #{record.cityProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityMallCode != null">
        city_mall_code = #{record.cityMallCode,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceName != null">
        province_name = #{record.provinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceProvinceCode != null">
        province_province_code = #{record.provinceProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceMallCode != null">
        province_mall_code = #{record.provinceMallCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 06 10:19:32 CST 2023. by MyBatis Generator, do not modify.
    -->
    update mall_address_2_province_address_info
    set district_province_name = #{record.districtProvinceName,jdbcType=VARCHAR},
      district_province_code = #{record.districtProvinceCode,jdbcType=VARCHAR},
      district_mall_code = #{record.districtMallCode,jdbcType=VARCHAR},
      district_mall_name = #{record.districtMallName,jdbcType=VARCHAR},
      city_mall_name = #{record.cityMallName,jdbcType=VARCHAR},
      city_province_name = #{record.cityProvinceName,jdbcType=VARCHAR},
      city_province_code = #{record.cityProvinceCode,jdbcType=VARCHAR},
      city_mall_code = #{record.cityMallCode,jdbcType=VARCHAR},
      province_name = #{record.provinceName,jdbcType=VARCHAR},
      province_province_code = #{record.provinceProvinceCode,jdbcType=VARCHAR},
      province_mall_code = #{record.provinceMallCode,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 06 10:19:32 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into mall_address_2_province_address_info
    (district_province_name, district_province_code, district_mall_code, district_mall_name, 
      city_mall_name, city_province_name, city_province_code, city_mall_code, province_name, 
      province_province_code, province_mall_code)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.districtProvinceName,jdbcType=VARCHAR}, #{item.districtProvinceCode,jdbcType=VARCHAR}, 
        #{item.districtMallCode,jdbcType=VARCHAR}, #{item.districtMallName,jdbcType=VARCHAR}, 
        #{item.cityMallName,jdbcType=VARCHAR}, #{item.cityProvinceName,jdbcType=VARCHAR}, 
        #{item.cityProvinceCode,jdbcType=VARCHAR}, #{item.cityMallCode,jdbcType=VARCHAR}, 
        #{item.provinceName,jdbcType=VARCHAR}, #{item.provinceProvinceCode,jdbcType=VARCHAR}, 
        #{item.provinceMallCode,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Fri Jan 06 10:19:32 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into mall_address_2_province_address_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'district_province_name'.toString() == column.value">
          #{item.districtProvinceName,jdbcType=VARCHAR}
        </if>
        <if test="'district_province_code'.toString() == column.value">
          #{item.districtProvinceCode,jdbcType=VARCHAR}
        </if>
        <if test="'district_mall_code'.toString() == column.value">
          #{item.districtMallCode,jdbcType=VARCHAR}
        </if>
        <if test="'district_mall_name'.toString() == column.value">
          #{item.districtMallName,jdbcType=VARCHAR}
        </if>
        <if test="'city_mall_name'.toString() == column.value">
          #{item.cityMallName,jdbcType=VARCHAR}
        </if>
        <if test="'city_province_name'.toString() == column.value">
          #{item.cityProvinceName,jdbcType=VARCHAR}
        </if>
        <if test="'city_province_code'.toString() == column.value">
          #{item.cityProvinceCode,jdbcType=VARCHAR}
        </if>
        <if test="'city_mall_code'.toString() == column.value">
          #{item.cityMallCode,jdbcType=VARCHAR}
        </if>
        <if test="'province_name'.toString() == column.value">
          #{item.provinceName,jdbcType=VARCHAR}
        </if>
        <if test="'province_province_code'.toString() == column.value">
          #{item.provinceProvinceCode,jdbcType=VARCHAR}
        </if>
        <if test="'province_mall_code'.toString() == column.value">
          #{item.provinceMallCode,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>