<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.Order2cAtomSnMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.Order2cAtomSn">
    <result column="atom_order_id" jdbcType="VARCHAR" property="atomOrderId" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="card_number" jdbcType="VARCHAR" property="cardNumber" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    atom_order_id, sn, card_number
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomSnExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_2c_atom_sn
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomSnExample">
    delete from order_2c_atom_sn
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomSn">
    insert into order_2c_atom_sn (atom_order_id, sn, card_number
      )
    values (#{atomOrderId,jdbcType=VARCHAR}, #{sn,jdbcType=VARCHAR}, #{cardNumber,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomSn">
    insert into order_2c_atom_sn
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="atomOrderId != null">
        atom_order_id,
      </if>
      <if test="sn != null">
        sn,
      </if>
      <if test="cardNumber != null">
        card_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="atomOrderId != null">
        #{atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
      <if test="cardNumber != null">
        #{cardNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomSnExample" resultType="java.lang.Long">
    select count(*) from order_2c_atom_sn
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_2c_atom_sn
    <set>
      <if test="record.atomOrderId != null">
        atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.sn != null">
        sn = #{record.sn,jdbcType=VARCHAR},
      </if>
      <if test="record.cardNumber != null">
        card_number = #{record.cardNumber,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_2c_atom_sn
    set atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
      sn = #{record.sn,jdbcType=VARCHAR},
      card_number = #{record.cardNumber,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into order_2c_atom_sn
    (atom_order_id, sn, card_number)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.atomOrderId,jdbcType=VARCHAR}, #{item.sn,jdbcType=VARCHAR}, #{item.cardNumber,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into order_2c_atom_sn (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'atom_order_id'.toString() == column.value">
          #{item.atomOrderId,jdbcType=VARCHAR}
        </if>
        <if test="'sn'.toString() == column.value">
          #{item.sn,jdbcType=VARCHAR}
        </if>
        <if test="'card_number'.toString() == column.value">
          #{item.cardNumber,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
    <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.Order2cAtomSn">
        <result column="atom_order_id" jdbcType="VARCHAR" property="atomOrderId" />
        <result column="sn" jdbcType="VARCHAR" property="sn" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        atom_order_id
        , sn
    </sql>
    <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomSnExample" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List" />
        from order_2c_atom_sn
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomSnExample">
        delete from order_2c_atom_sn
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </delete>
    <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomSn">
        insert into order_2c_atom_sn (atom_order_id, sn)
        values (#{atomOrderId,jdbcType=VARCHAR}, #{sn,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomSn">
        insert into order_2c_atom_sn
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="atomOrderId != null">
                atom_order_id,
            </if>
            <if test="sn != null">
                sn,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="atomOrderId != null">
                #{atomOrderId,jdbcType=VARCHAR},
            </if>
            <if test="sn != null">
                #{sn,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.Order2cAtomSnExample" resultType="java.lang.Long">
        select count(*) from order_2c_atom_sn
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update order_2c_atom_sn
        <set>
            <if test="record.atomOrderId != null">
                atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
            </if>
            <if test="record.sn != null">
                sn = #{record.sn,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause" />
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update order_2c_atom_sn
        set atom_order_id = #{record.atomOrderId,jdbcType=VARCHAR},
        sn = #{record.sn,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause" />
        </if>
    </update>
    <insert id="batchInsert" parameterType="map">
        insert into order_2c_atom_sn
        (atom_order_id, sn)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.atomOrderId,jdbcType=VARCHAR}, #{item.sn,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <insert id="batchInsertSelective" parameterType="map">
        insert into order_2c_atom_sn (
        <foreach collection="selective" item="column" separator=",">
            ${column.escapedColumnName}
        </foreach>
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            <foreach collection="selective" item="column" separator=",">
                <if test="'atom_order_id'.toString() == column.value">
                    #{item.atomOrderId,jdbcType=VARCHAR}
                </if>
                <if test="'sn'.toString() == column.value">
                    #{item.sn,jdbcType=VARCHAR}
                </if>
            </foreach>
            )
        </foreach>
    </insert>
</mapper>