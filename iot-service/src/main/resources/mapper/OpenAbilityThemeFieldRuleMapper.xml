<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.OpenAbilityThemeFieldRuleMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.OpenAbilityThemeFieldRule">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 04 10:28:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="theme_id" jdbcType="VARCHAR" property="themeId" />
    <result column="content_id" jdbcType="VARCHAR" property="contentId" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="range_json" jdbcType="VARCHAR" property="rangeJson" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 04 10:28:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 04 10:28:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 04 10:28:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    id, name, theme_id, content_id, description, type, range_json
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeFieldRuleExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 04 10:28:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from open_ability_theme_field_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 04 10:28:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from open_ability_theme_field_rule
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 04 10:28:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from open_ability_theme_field_rule
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeFieldRuleExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 04 10:28:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from open_ability_theme_field_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeFieldRule">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 04 10:28:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into open_ability_theme_field_rule (id, name, theme_id, 
      content_id, description, type, 
      range_json)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{themeId,jdbcType=VARCHAR}, 
      #{contentId,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, 
      #{rangeJson,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeFieldRule">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 04 10:28:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into open_ability_theme_field_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="themeId != null">
        theme_id,
      </if>
      <if test="contentId != null">
        content_id,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="rangeJson != null">
        range_json,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="themeId != null">
        #{themeId,jdbcType=VARCHAR},
      </if>
      <if test="contentId != null">
        #{contentId,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="rangeJson != null">
        #{rangeJson,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeFieldRuleExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 04 10:28:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from open_ability_theme_field_rule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 04 10:28:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    update open_ability_theme_field_rule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.themeId != null">
        theme_id = #{record.themeId,jdbcType=VARCHAR},
      </if>
      <if test="record.contentId != null">
        content_id = #{record.contentId,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.rangeJson != null">
        range_json = #{record.rangeJson,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 04 10:28:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    update open_ability_theme_field_rule
    set id = #{record.id,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      theme_id = #{record.themeId,jdbcType=VARCHAR},
      content_id = #{record.contentId,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=VARCHAR},
      range_json = #{record.rangeJson,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeFieldRule">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 04 10:28:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    update open_ability_theme_field_rule
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="themeId != null">
        theme_id = #{themeId,jdbcType=VARCHAR},
      </if>
      <if test="contentId != null">
        content_id = #{contentId,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="rangeJson != null">
        range_json = #{rangeJson,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.OpenAbilityThemeFieldRule">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 04 10:28:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    update open_ability_theme_field_rule
    set name = #{name,jdbcType=VARCHAR},
      theme_id = #{themeId,jdbcType=VARCHAR},
      content_id = #{contentId,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      range_json = #{rangeJson,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 04 10:28:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into open_ability_theme_field_rule
    (id, name, theme_id, content_id, description, type, range_json)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.themeId,jdbcType=VARCHAR}, 
        #{item.contentId,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR}, 
        #{item.rangeJson,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Jun 04 10:28:17 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into open_ability_theme_field_rule (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'theme_id'.toString() == column.value">
          #{item.themeId,jdbcType=VARCHAR}
        </if>
        <if test="'content_id'.toString() == column.value">
          #{item.contentId,jdbcType=VARCHAR}
        </if>
        <if test="'description'.toString() == column.value">
          #{item.description,jdbcType=VARCHAR}
        </if>
        <if test="'type'.toString() == column.value">
          #{item.type,jdbcType=VARCHAR}
        </if>
        <if test="'range_json'.toString() == column.value">
          #{item.rangeJson,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>