package com.chinamobile.iot.sc.response.iot;

import com.chinamobile.iot.sc.pojo.vo.OneKeyLoginResponseDataVO;
import lombok.Data;

@Data
public class OneKeyLoginResponse {

    private Header header;

    private Body body;

    @Data
    public static class Header {

        /**
         * 应用 ID
         * 移动认证分配的固定值
         */
        private String appId;

        /**
         * 时间跟踪 ID
         * 业务方生成唯一标识
         */
        private String traceId;


        /**
         * 请求消息发送的系统时间
         * 精确到毫秒 共17位
         */
        private String timestamp;

    }

    @Data
    public static class Body {

        /**
         * 状态码
         */
        private String resultCode;

        /**
         * 状态码描述
         */
        private String desc;

        /**
         * 附加业务状态码
         */
        private String bcode;

        /**
         * 时间戳
         */
        private String serviceTime;

        /**
         * 返回数据
         */
        private OneKeyLoginResponseDataVO data;

    }
}
