package com.chinamobile.iot.sc.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * created by l<PERSON><PERSON><PERSON> on 2023/2/7 11:24
 * 导出主商品实体类
 */
@Data
public class ProductExportDTO {

    /**
     * 商品组/销售商品名称
     */
    @Excel(name = "商品组/销售商品名称")
    private String spuOfferingName;

    /**
     * 商品组/销售商品编码
     */
    @Excel(name = "商品组/销售商品编码")
    private String spuOfferingCode;
    /**
     * spu版本号
     */
    @Excel(name = "销售商品版本号")
    private String spuOfferingVersion;
    /**
     * 商品类型
     */
    @Excel(name = "商品类型")
    private String spuOfferingClass;
//    /**
//     * 销售对象
//     */
//    @Excel(name = "销售对象")
//    private String saleObject;
    /**
     * 创建时间
     */
    @Excel(name = "创建时间")
    private String createTime;
    /**
     * 销售商品状态
     */
    @Excel(name = "销售商品状态")
    private String spuOfferingStatus;

    /**
     * 一级导航目录名称
     */
    @Excel(name = "一级导航目录")
    private String firstNavigationName;

    /**
     * 二级导航目录名称
     */
    @Excel(name = "二级导航目录")
    private String secondNavigationName;

    /**
     * 三级导航目录名称
     */
    @Excel(name = "三级导航目录")
    private String thirdNavigationName;

    /**
     * 是否隐秘上架,0：是 , 1：否
     */
    @Excel(name = "是否隐藏上架")
    private String secretlyListed;

     /**
     * 商品关键字
     */
    @Excel(name = "商品关键字")
    private String productKeywords;

    /**
     * 产品简介
     */
    @Excel(name = "产品简介")
    private String productDescription;

    /**
     * 主销售标签
     */
    @Excel(name = "主销售标签")
    private String mainSaleLabel;

    /**
     * 副销售标签
     */
    @Excel(name = "副销售标签")
    private String subSaleLabel;

    /**
     * 商品名称（规格）
     */
    @Excel(name = "商品名称（规格）")
    private String skuOfferingName;

    /**
     *商品编码（规格）
     */
    @Excel(name = "商品编码（规格）")
    private String skuOfferingCode;
    /**
     * sku版本号
     */
    @Excel(name = "规格商品版本号")
    private String skuOfferingVersion;
    /**
     * 销售目录价（规格）
     */
    @Excel(name = "销售目录价,单位元（规格）")
    private Double price;
    /**
     * 规格商品状态
     */
    @Excel(name = "规格商品状态")
    private String skuOfferingStatus;

    /**
     * 规格发布省
     */
    @Excel(name = "规格发布省")
    private String skuReleaseProvince;
    /**
     * 规格发布市
     */
    @Excel(name = "规格发布市")
    private String skuReleaseCity;

    /**
     * 供应商名称
     */
    @Excel(name = "供应商名称")
    private String supplierName;

    /**
     * 接单合作伙伴名称电话
     */
    @Excel(name = "商城合作伙伴（接单）")
    private String receiveOrderNamePhone;

    /**
     * 交付合作伙伴名称电话
     */
    @Excel(name = "商城合作伙伴（交付）")
    private String deliverNamePhone;

    /**
     * 售后合作伙伴名称电话
     */
    @Excel(name = "商城合作伙伴（售后）")
    private String aftermarketNamePhone;

    /**
     * 原子商品名称
     */
    @Excel(name = "原子商品名称")
    private String atomOfferingName;
    /**
     * 原子商品编码
     */
    @Excel(name = "原子商品编码")
    private String atomOfferingCode;
    /**
     * 原子商品版本号
     */
    @Excel(name = "原子商品版本号")
    private String atomOfferingVersion;
    /**
     * 原子商品类型
     */
    @Excel(name = "原子商品类型")
    private String atomOfferingClass;


    /**
     * 账目项名称
     */
    @Excel(name = "账目项名称")
    private String chargeName;

    /**
     * 账目项编码
     */
    @Excel(name = "账目项ID")
    private String chargeId;

    /**
     * 软件平台编码
     */
    @Excel(name = "软件平台编码")
    private String extSoftOfferingCode;
    /**
     * 终端物料编码
     */
    @Excel(name = "终端物料编码")
    private String extHardOfferingCode;
    /**
     * 数量（原子商品）
     */
    @Excel(name = "数量（原子商品）")
    private Long atomQuantity;
    /**
     * 型号（原子商品）
     */
    @Excel(name = "型号（原子商品）")
    private String model;
    /**
     * 颜色（原子商品）
     */
    @Excel(name = "颜色（原子商品）")
    private String color;
    /**
     * 销售价（原子商品）
     */
    @Excel(name = "销售价（原子商品）")
    private Double atomSalePrice;
    /**
     * 结算单价（原子商品）
     */
    @Excel(name = "结算单价，单位元（原子商品）")
    private Double settlePrice;
    /**
     * 销售省/市区域（原子商品）
     */
    @Excel(name = "销售省/市区域（原子商品）")
    private String offeringSaleRegion;

    /**
     * 合作伙伴名称
     */
    @Excel(name = "合作伙伴名称")
    private String partnerName;
    /**
     * 联系人姓名
     */
    @Excel(name = "联系人姓名")
    private String cooperatorName;

    /**
     * 标准服务编码
     */
    @Excel(name = "标准服务编码")
    private String standardServiceCode;
    /**
     * 标准服务名称
     */
    @Excel(name = "标准服务名称")
    private String standardServiceName;
    /**
     * 实质产品名称
     */
    @Excel(name = "核心部件")
    private String realProductName;
    /**
     * 产品部门
     */
    @Excel(name = "产品部门")
    private String department;
    /**
     * 产品属性
     */
    @Excel(name = "产品属性")
    private String property;

    @Excel(name = "备注1")
    private String remark1;

    @Excel(name = "备注2")
    private String remark2;


}
