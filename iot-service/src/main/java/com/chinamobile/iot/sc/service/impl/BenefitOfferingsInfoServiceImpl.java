package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.BenefitOfferingsInfoMapper;
import com.chinamobile.iot.sc.pojo.entity.BenefitOfferingsInfo;
import com.chinamobile.iot.sc.pojo.entity.BenefitOfferingsInfoExample;
import com.chinamobile.iot.sc.service.BenefitOfferingsInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/19
 * @description 省内融合包信息同步service实现类
 */
@Service
public class BenefitOfferingsInfoServiceImpl implements BenefitOfferingsInfoService {

    @Resource
    private BenefitOfferingsInfoMapper benefitOfferingsInfoMapper;

    @Override
    public List<BenefitOfferingsInfo> listBenefitOfferingsInfoByNeed(BenefitOfferingsInfoExample benefitOfferingsInfoExample) {
        return benefitOfferingsInfoMapper.selectByExample(benefitOfferingsInfoExample);
    }

    @Override
    public void batchAddBenefitOfferingsInfo(List<BenefitOfferingsInfo> benefitOfferingsInfoList) {
        benefitOfferingsInfoMapper.batchInsert(benefitOfferingsInfoList);
    }

    @Override
    public void addBenefitOfferingsInfo(BenefitOfferingsInfo benefitOfferingsInfo) {
        benefitOfferingsInfoMapper.insert(benefitOfferingsInfo);
    }

    @Override
    public void updateBenefitOfferingsInfoByNeed(BenefitOfferingsInfo benefitOfferingsInfo,
                                                 BenefitOfferingsInfoExample benefitOfferingsInfoExample) {
        benefitOfferingsInfoMapper.updateByExampleSelective(benefitOfferingsInfo,benefitOfferingsInfoExample);
    }

    @Override
    public void updateBenefitOfferingsInfoById(BenefitOfferingsInfo benefitOfferingsInfo) {
        benefitOfferingsInfoMapper.updateByPrimaryKeySelective(benefitOfferingsInfo);
    }
}
