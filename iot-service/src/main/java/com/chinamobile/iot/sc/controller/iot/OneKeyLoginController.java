package com.chinamobile.iot.sc.controller.iot;

import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.service.OneKeyLoginService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/os/oneKeyLogin")
public class OneKeyLoginController {
    @Resource
    private OneKeyLoginService oneKeyLoginService;

    /**
     * 商城免认证登录接口
     * @param request
     * @return
     */
    @PostMapping("/tokenValidate")
    public IOTAnswer tokenValidate(@RequestBody IOTRequest request) {
        IOTAnswer iotAnswer = oneKeyLoginService.tokenValidate(request);
        return iotAnswer;
    }
}
