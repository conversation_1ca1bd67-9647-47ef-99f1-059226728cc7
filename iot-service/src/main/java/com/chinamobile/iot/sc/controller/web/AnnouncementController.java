package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.AnnouncementAuditParam;
import com.chinamobile.iot.sc.pojo.param.AnnouncementListParam;
import com.chinamobile.iot.sc.pojo.param.AnnouncementVisibleParam;
import com.chinamobile.iot.sc.pojo.param.SaveAnnouncementParam;
import com.chinamobile.iot.sc.pojo.vo.AnnouncementVO;
import com.chinamobile.iot.sc.service.AnnouncementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping(value = "/osweb/announcement")
public class AnnouncementController {

    @Autowired
    private AnnouncementService announcementService;

    /**
     * 新增/编辑公告
     */
    @PostMapping("/save")
    public BaseAnswer saveAnnouncement(@Valid @RequestBody SaveAnnouncementParam param,
                                       @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return announcementService.saveAnnouncement(param,loginIfo4Redis);
    }

    /**
     * 编辑公告
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping("/update")
    public BaseAnswer updateAnnouncement(@Valid @RequestBody SaveAnnouncementParam param,
                                       @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return announcementService.updateAnnouncement(param,loginIfo4Redis);
    }


    /**
     * (系统设置)分页查询公告列表
     */
    @GetMapping("/list")
    public BaseAnswer<PageData<AnnouncementVO>> announcementList(AnnouncementListParam param
            ,@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return announcementService.announcementList(param,loginIfo4Redis);
    }


    /**
     * 查看公告详情
     */
    @GetMapping("/detail")
    public BaseAnswer<AnnouncementVO> announcementDetail(@RequestParam(value = "id",required = true) String id,
                                                         @RequestParam(value = "operate",required = true) String operate){
        return announcementService.announcementDetail(id,operate);
    }

    /**
     * 首页查询公告详情
     * @param id
     * @param operate
     * @return
     */
    @GetMapping("/pageHomeDetail")
    public BaseAnswer<AnnouncementVO> announcementDetailpageHome(@RequestParam(value = "id",required = true) String id,
                                                         @RequestParam(value = "operate",required = true) String operate){
        return announcementService.announcementDetail(id,operate);
    }


    /**
     * 显示/隐藏公告
     */
    @PostMapping("/visible")
    public BaseAnswer announcementVisible(@RequestBody @Valid AnnouncementVisibleParam param){
        return announcementService.announcementVisible(param);
    }



    /**
     * 审核公告
     */
    @PostMapping("/audit")
    public BaseAnswer announcementAudit(@RequestBody @Valid AnnouncementAuditParam param){
        return announcementService.announcementAudit(param);
    }

    /**
     * (首页)用户登录时获取公告弹窗列表（仅每天首次登录时提示，针对每条公告每个人）
     */
    @GetMapping("/popupList")
    public BaseAnswer<PageData<AnnouncementVO>> announcementPopupList(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return announcementService.announcementPopupList(loginIfo4Redis);
    }

    /**
     * (首页)公告已读
     */
    @PostMapping("/read")
    public BaseAnswer announcementRead(@RequestParam(value = "id",required = true) String id,
                                       @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return announcementService.announcementRead(id,loginIfo4Redis);
    }

    /**
     * (首页)公告列表
     */
    @GetMapping("/list4home")
    public BaseAnswer<PageData<AnnouncementVO>> announcementList4Home(AnnouncementListParam param,
                                                                      @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return announcementService.announcementList4Home(param,loginIfo4Redis);
    }



}
