package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class ConfigCardXInventoryDeviceParam {

    /**
     * 省份编码
     */
    @NotEmpty(message = "省份编码不能为空")
    private String beId;

    /**
     * 开卡模版名称
     */
//    @NotEmpty(message = "开卡模版不能为空")
//    private String templateName;

    /**
     * 开卡模板编码
      */
    private String templateId;

    /**
     * 卡服务商名称
     */
//    @NotEmpty(message = "卡服务商名称不能为空")
//    private String custName;

    /**
     * 归属卡服务商EC编码
     */
    private String custCode;

    /**
     * 终端类型,含卡终端的传值和号卡类型一致，不含卡类型传51
     */
    @NotEmpty(message = "终端类型不能为空")
    private String terminalType;

}
