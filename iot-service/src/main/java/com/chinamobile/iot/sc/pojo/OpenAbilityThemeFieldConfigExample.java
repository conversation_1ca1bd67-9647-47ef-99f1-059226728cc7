package com.chinamobile.iot.sc.pojo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OpenAbilityThemeFieldConfigExample {
    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    public OpenAbilityThemeFieldConfigExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    public OpenAbilityThemeFieldConfigExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    public OpenAbilityThemeFieldConfigExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        OpenAbilityThemeFieldConfigExample example = new OpenAbilityThemeFieldConfigExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    public OpenAbilityThemeFieldConfigExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    public OpenAbilityThemeFieldConfigExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNull() {
            addCriterion("app_id is null");
            return (Criteria) this;
        }

        public Criteria andAppIdIsNotNull() {
            addCriterion("app_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualTo(String value) {
            addCriterion("app_id =", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("app_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualTo(String value) {
            addCriterion("app_id <>", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("app_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThan(String value) {
            addCriterion("app_id >", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("app_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualTo(String value) {
            addCriterion("app_id >=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdGreaterThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("app_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppIdLessThan(String value) {
            addCriterion("app_id <", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("app_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualTo(String value) {
            addCriterion("app_id <=", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdLessThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("app_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppIdLike(String value) {
            addCriterion("app_id like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotLike(String value) {
            addCriterion("app_id not like", value, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdIn(List<String> values) {
            addCriterion("app_id in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotIn(List<String> values) {
            addCriterion("app_id not in", values, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdBetween(String value1, String value2) {
            addCriterion("app_id between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andAppIdNotBetween(String value1, String value2) {
            addCriterion("app_id not between", value1, value2, "appId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdIsNull() {
            addCriterion("organization_id is null");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdIsNotNull() {
            addCriterion("organization_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdEqualTo(String value) {
            addCriterion("organization_id =", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("organization_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrganizationIdNotEqualTo(String value) {
            addCriterion("organization_id <>", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdNotEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("organization_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrganizationIdGreaterThan(String value) {
            addCriterion("organization_id >", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdGreaterThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("organization_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrganizationIdGreaterThanOrEqualTo(String value) {
            addCriterion("organization_id >=", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdGreaterThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("organization_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrganizationIdLessThan(String value) {
            addCriterion("organization_id <", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdLessThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("organization_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrganizationIdLessThanOrEqualTo(String value) {
            addCriterion("organization_id <=", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdLessThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("organization_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrganizationIdLike(String value) {
            addCriterion("organization_id like", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdNotLike(String value) {
            addCriterion("organization_id not like", value, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdIn(List<String> values) {
            addCriterion("organization_id in", values, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdNotIn(List<String> values) {
            addCriterion("organization_id not in", values, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdBetween(String value1, String value2) {
            addCriterion("organization_id between", value1, value2, "organizationId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdNotBetween(String value1, String value2) {
            addCriterion("organization_id not between", value1, value2, "organizationId");
            return (Criteria) this;
        }

        public Criteria andRuleIdIsNull() {
            addCriterion("rule_id is null");
            return (Criteria) this;
        }

        public Criteria andRuleIdIsNotNull() {
            addCriterion("rule_id is not null");
            return (Criteria) this;
        }

        public Criteria andRuleIdEqualTo(String value) {
            addCriterion("rule_id =", value, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleIdNotEqualTo(String value) {
            addCriterion("rule_id <>", value, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdNotEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleIdGreaterThan(String value) {
            addCriterion("rule_id >", value, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdGreaterThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleIdGreaterThanOrEqualTo(String value) {
            addCriterion("rule_id >=", value, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdGreaterThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleIdLessThan(String value) {
            addCriterion("rule_id <", value, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdLessThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleIdLessThanOrEqualTo(String value) {
            addCriterion("rule_id <=", value, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdLessThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleIdLike(String value) {
            addCriterion("rule_id like", value, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdNotLike(String value) {
            addCriterion("rule_id not like", value, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdIn(List<String> values) {
            addCriterion("rule_id in", values, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdNotIn(List<String> values) {
            addCriterion("rule_id not in", values, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdBetween(String value1, String value2) {
            addCriterion("rule_id between", value1, value2, "ruleId");
            return (Criteria) this;
        }

        public Criteria andRuleIdNotBetween(String value1, String value2) {
            addCriterion("rule_id not between", value1, value2, "ruleId");
            return (Criteria) this;
        }

        public Criteria andFieldIdIsNull() {
            addCriterion("field_id is null");
            return (Criteria) this;
        }

        public Criteria andFieldIdIsNotNull() {
            addCriterion("field_id is not null");
            return (Criteria) this;
        }

        public Criteria andFieldIdEqualTo(String value) {
            addCriterion("field_id =", value, "fieldId");
            return (Criteria) this;
        }

        public Criteria andFieldIdEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("field_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFieldIdNotEqualTo(String value) {
            addCriterion("field_id <>", value, "fieldId");
            return (Criteria) this;
        }

        public Criteria andFieldIdNotEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("field_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFieldIdGreaterThan(String value) {
            addCriterion("field_id >", value, "fieldId");
            return (Criteria) this;
        }

        public Criteria andFieldIdGreaterThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("field_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFieldIdGreaterThanOrEqualTo(String value) {
            addCriterion("field_id >=", value, "fieldId");
            return (Criteria) this;
        }

        public Criteria andFieldIdGreaterThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("field_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFieldIdLessThan(String value) {
            addCriterion("field_id <", value, "fieldId");
            return (Criteria) this;
        }

        public Criteria andFieldIdLessThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("field_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFieldIdLessThanOrEqualTo(String value) {
            addCriterion("field_id <=", value, "fieldId");
            return (Criteria) this;
        }

        public Criteria andFieldIdLessThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("field_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFieldIdLike(String value) {
            addCriterion("field_id like", value, "fieldId");
            return (Criteria) this;
        }

        public Criteria andFieldIdNotLike(String value) {
            addCriterion("field_id not like", value, "fieldId");
            return (Criteria) this;
        }

        public Criteria andFieldIdIn(List<String> values) {
            addCriterion("field_id in", values, "fieldId");
            return (Criteria) this;
        }

        public Criteria andFieldIdNotIn(List<String> values) {
            addCriterion("field_id not in", values, "fieldId");
            return (Criteria) this;
        }

        public Criteria andFieldIdBetween(String value1, String value2) {
            addCriterion("field_id between", value1, value2, "fieldId");
            return (Criteria) this;
        }

        public Criteria andFieldIdNotBetween(String value1, String value2) {
            addCriterion("field_id not between", value1, value2, "fieldId");
            return (Criteria) this;
        }

        public Criteria andRuleTypeIsNull() {
            addCriterion("rule_type is null");
            return (Criteria) this;
        }

        public Criteria andRuleTypeIsNotNull() {
            addCriterion("rule_type is not null");
            return (Criteria) this;
        }

        public Criteria andRuleTypeEqualTo(String value) {
            addCriterion("rule_type =", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleTypeNotEqualTo(String value) {
            addCriterion("rule_type <>", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeNotEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleTypeGreaterThan(String value) {
            addCriterion("rule_type >", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeGreaterThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleTypeGreaterThanOrEqualTo(String value) {
            addCriterion("rule_type >=", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeGreaterThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleTypeLessThan(String value) {
            addCriterion("rule_type <", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeLessThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleTypeLessThanOrEqualTo(String value) {
            addCriterion("rule_type <=", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeLessThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleTypeLike(String value) {
            addCriterion("rule_type like", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeNotLike(String value) {
            addCriterion("rule_type not like", value, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeIn(List<String> values) {
            addCriterion("rule_type in", values, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeNotIn(List<String> values) {
            addCriterion("rule_type not in", values, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeBetween(String value1, String value2) {
            addCriterion("rule_type between", value1, value2, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleTypeNotBetween(String value1, String value2) {
            addCriterion("rule_type not between", value1, value2, "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleValueObjIsNull() {
            addCriterion("rule_value_obj is null");
            return (Criteria) this;
        }

        public Criteria andRuleValueObjIsNotNull() {
            addCriterion("rule_value_obj is not null");
            return (Criteria) this;
        }

        public Criteria andRuleValueObjEqualTo(String value) {
            addCriterion("rule_value_obj =", value, "ruleValueObj");
            return (Criteria) this;
        }

        public Criteria andRuleValueObjEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_value_obj = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleValueObjNotEqualTo(String value) {
            addCriterion("rule_value_obj <>", value, "ruleValueObj");
            return (Criteria) this;
        }

        public Criteria andRuleValueObjNotEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_value_obj <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleValueObjGreaterThan(String value) {
            addCriterion("rule_value_obj >", value, "ruleValueObj");
            return (Criteria) this;
        }

        public Criteria andRuleValueObjGreaterThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_value_obj > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleValueObjGreaterThanOrEqualTo(String value) {
            addCriterion("rule_value_obj >=", value, "ruleValueObj");
            return (Criteria) this;
        }

        public Criteria andRuleValueObjGreaterThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_value_obj >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleValueObjLessThan(String value) {
            addCriterion("rule_value_obj <", value, "ruleValueObj");
            return (Criteria) this;
        }

        public Criteria andRuleValueObjLessThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_value_obj < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleValueObjLessThanOrEqualTo(String value) {
            addCriterion("rule_value_obj <=", value, "ruleValueObj");
            return (Criteria) this;
        }

        public Criteria andRuleValueObjLessThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_value_obj <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleValueObjLike(String value) {
            addCriterion("rule_value_obj like", value, "ruleValueObj");
            return (Criteria) this;
        }

        public Criteria andRuleValueObjNotLike(String value) {
            addCriterion("rule_value_obj not like", value, "ruleValueObj");
            return (Criteria) this;
        }

        public Criteria andRuleValueObjIn(List<String> values) {
            addCriterion("rule_value_obj in", values, "ruleValueObj");
            return (Criteria) this;
        }

        public Criteria andRuleValueObjNotIn(List<String> values) {
            addCriterion("rule_value_obj not in", values, "ruleValueObj");
            return (Criteria) this;
        }

        public Criteria andRuleValueObjBetween(String value1, String value2) {
            addCriterion("rule_value_obj between", value1, value2, "ruleValueObj");
            return (Criteria) this;
        }

        public Criteria andRuleValueObjNotBetween(String value1, String value2) {
            addCriterion("rule_value_obj not between", value1, value2, "ruleValueObj");
            return (Criteria) this;
        }

        public Criteria andRuleRangeListIsNull() {
            addCriterion("rule_range_list is null");
            return (Criteria) this;
        }

        public Criteria andRuleRangeListIsNotNull() {
            addCriterion("rule_range_list is not null");
            return (Criteria) this;
        }

        public Criteria andRuleRangeListEqualTo(String value) {
            addCriterion("rule_range_list =", value, "ruleRangeList");
            return (Criteria) this;
        }

        public Criteria andRuleRangeListEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_range_list = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleRangeListNotEqualTo(String value) {
            addCriterion("rule_range_list <>", value, "ruleRangeList");
            return (Criteria) this;
        }

        public Criteria andRuleRangeListNotEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_range_list <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleRangeListGreaterThan(String value) {
            addCriterion("rule_range_list >", value, "ruleRangeList");
            return (Criteria) this;
        }

        public Criteria andRuleRangeListGreaterThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_range_list > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleRangeListGreaterThanOrEqualTo(String value) {
            addCriterion("rule_range_list >=", value, "ruleRangeList");
            return (Criteria) this;
        }

        public Criteria andRuleRangeListGreaterThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_range_list >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleRangeListLessThan(String value) {
            addCriterion("rule_range_list <", value, "ruleRangeList");
            return (Criteria) this;
        }

        public Criteria andRuleRangeListLessThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_range_list < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleRangeListLessThanOrEqualTo(String value) {
            addCriterion("rule_range_list <=", value, "ruleRangeList");
            return (Criteria) this;
        }

        public Criteria andRuleRangeListLessThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("rule_range_list <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRuleRangeListLike(String value) {
            addCriterion("rule_range_list like", value, "ruleRangeList");
            return (Criteria) this;
        }

        public Criteria andRuleRangeListNotLike(String value) {
            addCriterion("rule_range_list not like", value, "ruleRangeList");
            return (Criteria) this;
        }

        public Criteria andRuleRangeListIn(List<String> values) {
            addCriterion("rule_range_list in", values, "ruleRangeList");
            return (Criteria) this;
        }

        public Criteria andRuleRangeListNotIn(List<String> values) {
            addCriterion("rule_range_list not in", values, "ruleRangeList");
            return (Criteria) this;
        }

        public Criteria andRuleRangeListBetween(String value1, String value2) {
            addCriterion("rule_range_list between", value1, value2, "ruleRangeList");
            return (Criteria) this;
        }

        public Criteria andRuleRangeListNotBetween(String value1, String value2) {
            addCriterion("rule_range_list not between", value1, value2, "ruleRangeList");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(OpenAbilityThemeFieldConfig.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andAppIdLikeInsensitive(String value) {
            addCriterion("upper(app_id) like", value.toUpperCase(), "appId");
            return (Criteria) this;
        }

        public Criteria andOrganizationIdLikeInsensitive(String value) {
            addCriterion("upper(organization_id) like", value.toUpperCase(), "organizationId");
            return (Criteria) this;
        }

        public Criteria andRuleIdLikeInsensitive(String value) {
            addCriterion("upper(rule_id) like", value.toUpperCase(), "ruleId");
            return (Criteria) this;
        }

        public Criteria andFieldIdLikeInsensitive(String value) {
            addCriterion("upper(field_id) like", value.toUpperCase(), "fieldId");
            return (Criteria) this;
        }

        public Criteria andRuleTypeLikeInsensitive(String value) {
            addCriterion("upper(rule_type) like", value.toUpperCase(), "ruleType");
            return (Criteria) this;
        }

        public Criteria andRuleValueObjLikeInsensitive(String value) {
            addCriterion("upper(rule_value_obj) like", value.toUpperCase(), "ruleValueObj");
            return (Criteria) this;
        }

        public Criteria andRuleRangeListLikeInsensitive(String value) {
            addCriterion("upper(rule_range_list) like", value.toUpperCase(), "ruleRangeList");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Tue Jun 18 16:08:52 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Tue Jun 18 16:08:52 CST 2024
         */
        private OpenAbilityThemeFieldConfigExample example;

        /**
         *
         * @mbg.generated Tue Jun 18 16:08:52 CST 2024
         */
        protected Criteria(OpenAbilityThemeFieldConfigExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Tue Jun 18 16:08:52 CST 2024
         */
        public OpenAbilityThemeFieldConfigExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Tue Jun 18 16:08:52 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Jun 18 16:08:52 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Jun 18 16:08:52 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Tue Jun 18 16:08:52 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Tue Jun 18 16:08:52 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Tue Jun 18 16:08:52 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Tue Jun 18 16:08:52 CST 2024
         */
        void example(com.chinamobile.iot.sc.pojo.OpenAbilityThemeFieldConfigExample example);
    }
}