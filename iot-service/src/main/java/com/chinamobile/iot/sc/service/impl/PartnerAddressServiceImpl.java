package com.chinamobile.iot.sc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.common.utils.RegexUtil;
import com.chinamobile.iot.sc.dao.PartnerAddressMapper;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.UserCenterOperateEnum;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.address.PartnerAddress;
import com.chinamobile.iot.sc.request.address.Request4ParAddrAdd;
import com.chinamobile.iot.sc.request.address.Request4ParAddrPage;
import com.chinamobile.iot.sc.request.address.Request4ParAddrUpdate;
import com.chinamobile.iot.sc.response.web.invoice.Data4PartnerAddress;
import com.chinamobile.iot.sc.response.web.invoice.Data4PartnerAddressDetailVO;
import com.chinamobile.iot.sc.service.IPartnerAddressService;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.util.DesensitizationUtils;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import com.chinamobile.iot.sc.util.IotLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.chinamobile.iot.sc.exception.StatusConstant.ADDRESS_UPDATE_DEFAULT_ERROR;
import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.SUCCESS;

/**
 * @package: com.chinamobile.iot.sc.service.impl
 * @ClassName: PartnerAddressServiceImpl
 * @description: 合作伙伴地址管理实现Service
 * @author: zyj
 * @create: 2021/12/15 14:46
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Slf4j
@Service
public class PartnerAddressServiceImpl implements IPartnerAddressService {
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private PartnerAddressMapper addressMapper;

    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private LogService logService;

    @Value("${iot.encodeKey}")
    private String encodeKey;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;
    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;
    private ExecutorService executorService = new ThreadPoolExecutor(50, 50, 1L, TimeUnit.MINUTES, new LinkedBlockingDeque<>(10000));


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<Void> addPartnerAddress(Request4ParAddrAdd request, String partnerId, LoginIfo4Redis loginIfo4Redis,String ip) {
        log.info("新增合作伙伴地址 addPartnerAddress request = {}",request);
        //校验字段
        //联系人校验
        if(!RegexUtil.regexOperatorName(request.getContactName())){
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ACCOUNT_INFO_MANAGE.code,
                        "【新建退换货地址】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.RETURN_CONTACT_ERROR.getMessage());
            });
            throw new BusinessException(StatusConstant.RETURN_CONTACT_ERROR);
        }
        //手机格式校验
        if(!RegexUtil.regexPhone(request.getContactPhone())){
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ACCOUNT_INFO_MANAGE.code,
                        "【新建退换货地址】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_ERROR.getMessage());
            });
            throw new BusinessException(BaseErrorConstant.PHONE_ERROR);
        }
        String roleType = loginIfo4Redis.getRoleType();
        // 超管-运营查询
//        if(BaseConstant.ADMIN_ROLE.equals(roleType) || BaseConstant.OPERATOR_ROLE.equals(roleType)){
//            //超管-运营时，合作伙伴id不能为空
//            if(ObjectUtils.isEmpty(request.getPartnerId())){
//                throw new BusinessException(StatusConstant.PARTNER_ID_NULL_ERROR);
//            }
//            partnerId = request.getPartnerId();
//            // 合作伙伴查询
//        }else if(BaseConstant.PARTNER_ROLE.equals(roleType)){
//
//        } else if(BaseConstant.PARTNER_LORD_ROLE.equals(roleType)){
//            String operate = request.getOperate();
//            if (StringUtils.isEmpty(operate)){
//                throw new BusinessException(StatusConstant.PARTNER_OPERATE_NULL_ERROR);
//            }
//            if ("1".equals(operate)){
//                //超管-运营主合作伙伴时，合作伙伴id不能为空
//                if(ObjectUtils.isEmpty(request.getPartnerId())){
//                    throw new BusinessException(StatusConstant.PARTNER_ID_NULL_ERROR);
//                }
//                partnerId = request.getPartnerId();
//            }
//        }
        /**合作伙伴主从特殊处理，其他角色只有有入口权限通用处理，且在添加退换是必传partnerId*/
        if(loginIfo4Redis.getIsPartner() && (loginIfo4Redis.getIsPrimary() != null && !loginIfo4Redis.getIsPrimary())){
            /**合伙伙伴-从，只能修改自己的退换货地址*/
            request.setPartnerId(loginIfo4Redis.getUserId());

        } else if(loginIfo4Redis.getIsPartner() && loginIfo4Redis.getIsPrimary() != null && loginIfo4Redis.getIsPrimary()){
            if (StringUtils.isBlank(request.getPartnerId())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ACCOUNT_INFO_MANAGE.code,
                            "【新建退换货地址】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PARTNER_ID_NULL_ERROR.getMessage());
                });
                throw new BusinessException(StatusConstant.PARTNER_ID_NULL_ERROR);
            }
            //主伙伴
            /**合伙伙伴-主，修改不是自己的退换货地址时需要校验是否为自己的从伙伴*/
            if (!loginIfo4Redis.getUserId().equals(request.getPartnerId())) {
                BaseAnswer<Data4User> baseAnswer = userFeignClient.queryPrimaryUserPhone(request.getPartnerId());
                if (baseAnswer == null || baseAnswer.getData() == null
                        || !StringUtils.equals(baseAnswer.getData().getUserId(),loginIfo4Redis.getUserId())) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ACCOUNT_INFO_MANAGE.code,
                                "【新建退换货地址】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "主合作伙伴只能为自己以及对应从合伙伙伴添加地址");
                    });
                    throw new BusinessException(StatusConstant.AUTH_ERROR,"主合作伙伴只能为自己以及对应从合伙伙伴添加地址");
                }

            }
            String operate = request.getOperate();
            if (StringUtils.isEmpty(operate)){
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ACCOUNT_INFO_MANAGE.code,
                            "【新建退换货地址】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PARTNER_OPERATE_NULL_ERROR.getMessage());
                });
                throw new BusinessException(StatusConstant.PARTNER_OPERATE_NULL_ERROR);
            }
        } else {
            /**其他角色增加地址，要校验是否传递partnerId*/
            if (StringUtils.isBlank(request.getPartnerId())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ACCOUNT_INFO_MANAGE.code,
                            "【新建退换货地址】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, StatusConstant.PARTNER_OPERATE_NULL_ERROR.getMessage());
                });
                throw new BusinessException(StatusConstant.PARTNER_OPERATE_NULL_ERROR);
            }
        }

        //查询合作伙伴下是否存在退换货默认地址
        List<PartnerAddress> addressList = addressMapper.selectList(new QueryWrapper<PartnerAddress>().lambda()
                .eq(PartnerAddress::getIsDefault, true).eq(PartnerAddress::getPartnerId, request.getPartnerId()));
        PartnerAddress partnerAddress = null;
        partnerAddress = new PartnerAddress().setId(BaseServiceUtils.getId()).setContactName(request.getContactName())
                .setContactPhone(request.getContactPhone()).setReturnAddress(request.getReturnAddress())
                .setIsDefault(request.getIsDefault())
                .setPartnerSlaveId(request.getPartnerSlaveId())
                .setPartnerSlaveName(request.getPartnerSlaveName())
                .setPartnerId(request.getPartnerId()).setCreateTime(new Date()).setUpdateTime(new Date());
        if(ObjectUtils.isNotEmpty(addressList)){
            if(request.getIsDefault()){
                PartnerAddress partnerAddressDefault = addressList.get(0);
                partnerAddressDefault.setIsDefault(false).setUpdateTime(new Date());
                addressMapper.updateById(partnerAddressDefault);
            }
            // 若第一次新增退换货地址，则刷新loginIfo4Redis中退换货标识符
        }else{
            partnerAddress.setIsDefault(true);
            loginIfo4Redis.setIsReAddrNull(false);
            //将权限信息放入redis
            redisTemplate.opsForValue().set(Constant.REDIS_KEY_USER_TOKEN + request.getPartnerId() , loginIfo4Redis,24, TimeUnit.HOURS);
        }
        addressMapper.insert(partnerAddress);

        BaseAnswer<Data4User> answer = userFeignClient.partnerInfoById(request.getPartnerId());
        if(answer != null && answer.getData() != null) {
            //记录日志
            logService.recordOperateLog(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ADDRESS_MANAGE.code,
                    IotLogUtil.addPartnerAddressContentFromRequest(partnerAddress, answer.getData()), LogResultEnum.LOG_SUCESS.code, null);
        }
        return new BaseAnswer<>();
    }

    @Override
    public BaseAnswer<Void> deletePartnerAddress(String id, String partnerId) {
        //删除最后一个地址，系统提示：退换地址不能为空，删除前请新增其他地址。
        PartnerAddress partnerAddress1 = addressMapper.selectById(id);
        String partnerId1 = partnerAddress1.getPartnerId();
        List<PartnerAddress> partnerAddresses = addressMapper.selectList(new QueryWrapper<PartnerAddress>().lambda()
                .eq(PartnerAddress::getPartnerId, partnerId1));
        //退换地址不能为空，删除前请新增其他地址。
        if(ObjectUtils.isNotEmpty(partnerAddresses)){
            if(partnerAddresses.size() == 1){
                logService.recordOperateLog(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ADDRESS_MANAGE.code,
                      "【删除退换货地址】",LogResultEnum.LOG_FAIL.code, StatusConstant.PARTNER_ADDRESS_NULL_ERROR.getMessage());
                throw new BusinessException(StatusConstant.PARTNER_ADDRESS_NULL_ERROR);
            }else{
                PartnerAddress partnerAddress = addressMapper.selectById(id);
                Boolean isDefault = partnerAddress.getIsDefault();
                //3）删除非最后一个地址，且删除为默认地址时，系统提示：确认删除吗？当确认后，OS自动将合作伙伴创建的最新的地址设置为默认地址。
                if(isDefault){
                    PartnerAddress partnerAddressFirst = null;
                    List<PartnerAddress> addresses = addressMapper.selectList(new QueryWrapper<PartnerAddress>().lambda()
                            .eq(PartnerAddress::getPartnerId, partnerId1)
                            .orderByDesc(PartnerAddress::getUpdateTime));
                    partnerAddressFirst = addresses.get(0);
                    if(partnerAddressFirst.getIsDefault() == true){
                        partnerAddressFirst = addresses.get(1);
                    }
                    partnerAddressFirst.setIsDefault(true);
                    addressMapper.updateById(partnerAddressFirst);
                }
                addressMapper.deleteById(id);
            }
        }
        //记录日志
        BaseAnswer<Data4User> answer = userFeignClient.partnerInfoById(partnerId);
        if(answer != null && answer.getData() != null) {
//            logService.recordOperateLog(ModuleEnum.ACCOUNT_MANAGE.code, AccountManageOperateEnum.ADDRESS_MANAGE.code,
//                    IotLogUtil.deletePartnerAddressContentFromRequest(partnerAddress1, answer.getData()));
            logService.recordOperateLog(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ADDRESS_MANAGE.code,
                    IotLogUtil.deletePartnerAddressContentFromRequest(partnerAddress1, answer.getData()),LogResultEnum.LOG_SUCESS.code, null);
        }
        return new BaseAnswer<>();
    }

    @Override
    public BaseAnswer<String> isLastPAddress(String id, String partnerId) {
        BaseAnswer<String> answer = new BaseAnswer<>();
        /**
         * 1）删除最后一个地址，系统提示：退换地址不能为空，删除前请新增其他地址。
         * 2）删除非最后一个地址，且删除为非默认地址时，弹框提示：确认删除吗？
         * 3）删除非最后一个地址，且删除为默认地址时，系统提示：确认删除吗？当确认后，OS自动将合作伙伴创建的最新的地址设置为默认地址。
         */
        List<PartnerAddress> partnerAddresses = addressMapper.selectList(new QueryWrapper<PartnerAddress>().lambda()
                .eq(PartnerAddress::getPartnerId, partnerId));
        if(ObjectUtils.isNotEmpty(partnerAddresses)){
            //1）删除最后一个地址，系统提示：退换地址不能为空，删除前请新增其他地址。
            if(partnerAddresses.size() == 1){
                answer.setData("1");
            }else{
                answer.setData("0");
            }

        }
        return answer;
    }

    @Override
    public Data4PartnerAddressDetailVO partnerAddrById(String id) {
        List<Data4PartnerAddress> data4PartnerAddresses = addressMapper.partnerAddrById(id);
        if (CollectionUtil.isNotEmpty(data4PartnerAddresses)){
            Data4PartnerAddressDetailVO  data4PartnerAddressDetailVO = new Data4PartnerAddressDetailVO();
            Data4PartnerAddress data4PartnerAddress = data4PartnerAddresses.get(0);
            String phone = data4PartnerAddress.getPhone();
            Long addressCount = addressMapper.pageCount4LordPartnerAddr( null,"", "", phone,"","");
            data4PartnerAddressDetailVO.setAddressCount(addressCount);
            dePhone(data4PartnerAddress);
            BeanUtils.copyProperties(data4PartnerAddress,data4PartnerAddressDetailVO);
            return data4PartnerAddressDetailVO;
        }else {
            return null;
        }
    }

    @Override
    public BaseAnswer<Void> updatePartnerAddress(Request4ParAddrUpdate request, String partnerId, LoginIfo4Redis loginIfo4Redis) {
        //地址id判空
        PartnerAddress partnerAddress = addressMapper.selectById(request.getId());
        if(ObjectUtils.isEmpty(partnerAddress)){
            logService.recordOperateLog(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ADDRESS_MANAGE.code,
                    "【修改退换货地址】",LogResultEnum.LOG_FAIL.code, StatusConstant.PARTNER_ADDRESS_ERROR.getMessage());
            throw new BusinessException(StatusConstant.PARTNER_ADDRESS_ERROR);
        }
        //校验字段
        //联系人校验
        if(!RegexUtil.regexOperatorName(request.getContactName())){
            logService.recordOperateLog(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ADDRESS_MANAGE.code,
                    "【修改退换货地址】",LogResultEnum.LOG_FAIL.code, StatusConstant.RETURN_CONTACT_ERROR.getMessage());
            throw new BusinessException(StatusConstant.RETURN_CONTACT_ERROR);
        }
        //手机格式校验
        if(!RegexUtil.regexPhone(request.getContactPhone())){
            logService.recordOperateLog(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ADDRESS_MANAGE.code,
                    "【修改退换货地址】",LogResultEnum.LOG_FAIL.code, StatusConstant.PHONE_ERROR.getMessage());
            throw new BusinessException(BaseErrorConstant.PHONE_ERROR);
        }
        String roleType = loginIfo4Redis.getRoleType();
        // 超管-运营查询
//        if(BaseConstant.ADMIN_ROLE.equals(roleType) || BaseConstant.OPERATOR_ROLE.equals(roleType)){
//            //超管-运营时，合作伙伴id不能为空
//            if(ObjectUtils.isEmpty(request.getPartnerId())){
//                throw new BusinessException(StatusConstant.PARTNER_ID_NULL_ERROR);
//            }
//            partnerId = request.getPartnerId();
//            // 合作伙伴查询
//        }else if(BaseConstant.PARTNER_ROLE.equals(roleType)){
//
//            //主合作伙伴
//        }else if(BaseConstant.PARTNER_LORD_ROLE.equals(roleType)){
//            String operate = request.getOperate();
//            if (StringUtils.isEmpty(operate)){
//                throw new BusinessException(StatusConstant.PARTNER_OPERATE_NULL_ERROR);
//            }
//            if ("1".equals(operate)){
//                //超管-运营主合作伙伴时，合作伙伴id不能为空
//                if(ObjectUtils.isEmpty(request.getPartnerId())){
//                    throw new BusinessException(StatusConstant.PARTNER_ID_NULL_ERROR);
//                }
//                partnerId = request.getPartnerId();
//            }
//        }
        /**合作伙伴主从特殊处理，其他角色只有有入口权限通用处理，且在添加退换是必传partnerId*/
        if(loginIfo4Redis.getIsPartner() && (loginIfo4Redis.getIsPrimary() == null || !loginIfo4Redis.getIsPrimary())){
            /**合伙伙伴-从，只能修改自己的退换货地址*/
            if (!loginIfo4Redis.getUserId().equals(request.getPartnerId())) {
                logService.recordOperateLog(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ADDRESS_MANAGE.code,
                        "【修改退换货地址】",LogResultEnum.LOG_FAIL.code, StatusConstant.AUTH_ERROR.getMessage());
                throw new BusinessException(StatusConstant.AUTH_ERROR,"从合作伙伴只能为自己添加地址");
            }
        } else if(loginIfo4Redis.getIsPartner() && loginIfo4Redis.getIsPrimary() != null && loginIfo4Redis.getIsPrimary()){
            //主伙伴
            /**合伙伙伴-主，修改不是自己的退换货地址时需要校验是否为自己的从伙伴*/
            if (!loginIfo4Redis.getUserId().equals(request.getPartnerId())) {
                BaseAnswer<Data4User> baseAnswer = userFeignClient.queryPrimaryUserPhone(request.getPartnerId());
                if (baseAnswer == null || baseAnswer.getData() == null
                        || !StringUtils.equals(baseAnswer.getData().getUserId(),loginIfo4Redis.getUserId())) {
                    logService.recordOperateLog(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ADDRESS_MANAGE.code,
                            "【修改退换货地址】",LogResultEnum.LOG_FAIL.code, StatusConstant.AUTH_ERROR.getMessage());
                    throw new BusinessException(StatusConstant.AUTH_ERROR,"主合作伙伴只能为自己以及对应从合伙伙伴添加地址");
                }

            }
            String operate = request.getOperate();
            if (StringUtils.isEmpty(operate)){
                logService.recordOperateLog(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ADDRESS_MANAGE.code,
                        "【修改退换货地址】",LogResultEnum.LOG_FAIL.code, StatusConstant.PARTNER_OPERATE_NULL_ERROR.getMessage());
                throw new BusinessException(StatusConstant.PARTNER_OPERATE_NULL_ERROR);
            }
        }
        //默认地址-true
        if(request.getIsDefault()){
            //将其他默认地址置为非默认
            List<PartnerAddress> addressList = addressMapper.selectList(new QueryWrapper<PartnerAddress>().lambda()
                    .eq(PartnerAddress::getIsDefault, true).eq(PartnerAddress::getPartnerId, request.getPartnerId()));
            PartnerAddress partnerAddress1 = addressList.get(0);
            partnerAddress1.setIsDefault(false);
            addressMapper.updateById(partnerAddress1);
        }
        // 若被修改地址，原为默认地址，修改为非默认地址。则将最新的非默认地址修改为默认地址
        Boolean isDefault = partnerAddress.getIsDefault();
        if(isDefault && request.getIsDefault().equals(false)){
            //若只有一个地址，则不让修改
            List<PartnerAddress> partnerAddresses = addressMapper.selectList(new QueryWrapper<PartnerAddress>().lambda()
                    .eq(PartnerAddress::getPartnerId, request.getPartnerId()));
            if(partnerAddresses.size() == 1){
                logService.recordOperateLog(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ADDRESS_MANAGE.code,
                        "【修改退换货地址】",LogResultEnum.LOG_FAIL.code, ADDRESS_UPDATE_DEFAULT_ERROR.getMessage());
                throw new BusinessException(ADDRESS_UPDATE_DEFAULT_ERROR);
            }else{
                List<PartnerAddress> noDefaultAddr = addressMapper.selectList(new QueryWrapper<PartnerAddress>().lambda()
                        .eq(PartnerAddress::getIsDefault, false).eq(PartnerAddress::getPartnerId, request.getPartnerId())
                        .orderByDesc(PartnerAddress::getUpdateTime));
                PartnerAddress partnerAddress1 = noDefaultAddr.get(0);
                partnerAddress1.setIsDefault(true);
                addressMapper.updateById(partnerAddress1);
            }
        }
        //修改前先留存原件，用于记录日志
        PartnerAddress old = new PartnerAddress();
        BeanUtils.copyProperties(partnerAddress,old);

        partnerAddress.setReturnAddress(request.getReturnAddress()).setContactName(request.getContactName())
                .setContactPhone(request.getContactPhone()).setIsDefault(request.getIsDefault())
                .setPartnerId(request.getPartnerId())
                .setPartnerSlaveId(request.getPartnerSlaveId())
                .setPartnerSlaveName(request.getPartnerSlaveName())
                .setUpdateTime(new Date());
        addressMapper.updateById(partnerAddress);

        //记录日志
        BaseAnswer<Data4User> answer = userFeignClient.partnerInfoById(request.getPartnerId());
        if(answer != null && answer.getData() != null) {
            logService.recordOperateLog(ModuleEnum.USER_CENTER.code, UserCenterOperateEnum.ADDRESS_MANAGE.code,
                    IotLogUtil.updatePartnerAddressContentFromRequest(old, partnerAddress, answer.getData()),LogResultEnum.LOG_SUCESS.code, null);
        }
        return new BaseAnswer<>();
    }

    // 数据权限-地址管理
    @Override
    public BaseAnswer<PageData<Data4PartnerAddress>> findPage(Request4ParAddrPage request, String partnerId
            , LoginIfo4Redis loginIfo4Redis) {

        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(dataPermissionCodes) || (
            !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ADDRESS_SYSTEM)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ADDRESS_COMPANY)
                && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ADDRESS_PERSONAL)
        )) {
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }

        BaseAnswer<PageData<Data4PartnerAddress>> answer = new BaseAnswer<>();
        PageData<Data4PartnerAddress> pageData = new PageData<>();
        answer.setData(pageData);
        Integer pageNum = request.getPageNum() != null ? request.getPageNum() : 1;
        Integer pageSize = request.getPageSize() != null ? request.getPageSize() : 10;
        String nameOrPhone = request.getNameOrPhone();
        List<String> partnerIdList = request.getPartnerIdList();
        String name = request.getName();
        String phone = request.getPhone();
        if (StringUtils.isNotEmpty(phone)){
            phone = IOTEncodeUtils.encryptSM4(phone,iotSm4Key,iotSm4Iv);
        }

        if (StringUtils.isNotEmpty(nameOrPhone)){
            if (RegexUtil.regexPhone(nameOrPhone)){
                nameOrPhone = IOTEncodeUtils.encryptSM4(nameOrPhone,iotSm4Key,iotSm4Iv);
            }
        }

        String roleType = loginIfo4Redis.getRoleType();
        List<Data4PartnerAddress> contacts = new ArrayList<Data4PartnerAddress>();
        Long count = 0L;
        String beId = "";
        String location = "";
        // 超管-运营查询，接口校验权限，合作伙伴特殊处理，其他角色按照通用处理
//        if(BaseConstant.ADMIN_ROLE.equals(roleType) || BaseConstant.OPERATOR_ROLE.equals(roleType)){
//            partnerId = request.getPartnerId();
//            // 合作伙伴查询
//        }else if(BaseConstant.PARTNER_ROLE.equals(roleType)){
//            //主合作伙伴查询所以从合作地址信息
//        }else if(BaseConstant.PARTNER_LORD_ROLE.equals(roleType)){
//            String queryOperate = request.getQueryOperate();
//            if (StringUtils.isEmpty(queryOperate)){
//                throw new BusinessException(StatusConstant.PARTNER_OPERATE_NULL_ERROR);
//            }
//            if ("1".equals(queryOperate)){
//                BaseAnswer<List<String>> downUserIds = userFeignClient.getDownUserIds(partnerId);
//                List<String> userList = new ArrayList<>();
//                if (CollectionUtil.isNotEmpty(downUserIds.getData())){
//                    userList = downUserIds.getData();
//                    contacts = addressMapper.page4LordPartnerAddr( userList,nameOrPhone,(pageNum - 1)*pageSize, pageSize);
//                    count = addressMapper.pageCount4LordPartnerAddr( userList,nameOrPhone);
//                    pageData.setPage(pageNum);
//                    pageData.setCount(count);
//                    if(CollectionUtil.isNotEmpty(contacts)){
//                        pageData.setData(contacts);
//                    }else{
//                        pageData.setData(new ArrayList<>());
//                    }
//                    return answer;
//                }else {
//                    pageData.setData(new ArrayList<>());
//                    return answer;
//                }
//               // userList.add(partnerId);
//            }
//        }
        if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ADDRESS_PERSONAL)){
            /**合伙伙伴-从，只能查询自己*/
            if (CollectionUtil.isEmpty(partnerIdList)){
                partnerIdList = new ArrayList<>();
                partnerIdList.add(partnerId);
            }

        } else if(dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ADDRESS_COMPANY)){
            /**合伙伙伴-主，可以查询自己以及对应从伙伴*/
            String queryOperate = request.getQueryOperate();
            if (StringUtils.isEmpty(queryOperate)){
                throw new BusinessException(StatusConstant.PARTNER_OPERATE_NULL_ERROR);
            }
            if ("1".equals(queryOperate)){
                List<String> userList = new ArrayList<>();
                if (roleType.equals(BaseConstant.PARTNER_LORD_ROLE)){
                    // 主合作伙伴查自己和下面的从合作伙伴地址
                    BaseAnswer<List<String>> downUserIds = userFeignClient.getDownUserIds(partnerId);
                    if (CollectionUtil.isNotEmpty(downUserIds.getData())){
                        userList = downUserIds.getData();
                    }
                    userList.add(partnerId);
                }else if (roleType.equals(BaseConstant.PARTNER_PROVINCE)) {
                    log.info("合作伙伴省管查询库存信息：partnerProvince：{}",roleType);
                    // 省管配置权限为主合作伙伴权限
                    BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(loginIfo4Redis.getUserId());
                    if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                        throw new BusinessException("10004", "合作伙伴省管账号错误");
                    }

                    Data4User data4User = data4UserBaseAnswer.getData();
                    String companyType = data4User.getCompanyType();
                    boolean isProvinceUser = org.apache.commons.lang3.StringUtils.isNotEmpty(companyType) && "2".equals(companyType);
                    String userLocation = data4User.getLocationIdPartner();
                    log.info("登录用户是否是合作伙伴省管：partnerIsProvinceUser:{}",isProvinceUser);
                    if (isProvinceUser) {
                        BaseAnswer<Data4User> userPartner = userFeignClient.getUserPartnerPrimaryByPartnerName(data4User.getPartnerName());
                        if (userPartner == null || !SUCCESS.getStateCode().equals(userPartner.getStateCode())) {
                            throw new BusinessException("10004", "合作伙伴省管获取主合作伙伴账号错误");
                        }
                        Data4User userPartnerData = userPartner.getData();
                        if (Optional.ofNullable(userPartnerData).isPresent()) {
                            BaseAnswer<List<String>> downUserIds = userFeignClient.getDownUserIds(userPartnerData.getUserId());
                            if (CollectionUtil.isNotEmpty(downUserIds.getData())) {
                                userList = downUserIds.getData();
                            }
                        }
                        if ("all".equals(userLocation)) {
                            beId = data4User.getBeIdPartner();
                        } else {
                            location = userLocation;
                        }
                    }else {
                        throw new BusinessException(StatusConstant.AUTH_ERROR,"账号不属于省公司");
                    }
                } else{
                    throw new BusinessException(StatusConstant.AUTH_ERROR);
                }
                if (CollectionUtil.isNotEmpty(userList)){
                    contacts = addressMapper.page4LordPartnerAddr( userList,nameOrPhone,(pageNum - 1)*pageSize, pageSize,
                            name, phone,beId,location);
                    count = addressMapper.pageCount4LordPartnerAddr( userList,nameOrPhone, name, phone,beId,location);
                    pageData.setPage(pageNum);
                    pageData.setCount(count);
                    if(CollectionUtil.isNotEmpty(contacts)){
                        decryptPhone(contacts);
                        pageData.setData(contacts);
                    }else{
                        pageData.setData(new ArrayList<>());
                    }
                    return answer;
                }else {
                    pageData.setData(new ArrayList<>());
                    return answer;
                }
                // userList.add(partnerId);
            }else {
            //在售后订单获取地址的时候只查询带入的从合作地址
//            partnerId = request.getPartnerId();
        }

    } else {
            /**其他角色按照参数查询*/
//            partnerId = request.getPartnerId();
    }
        /*List<String> idList = new ArrayList<>();
        String downId = loginIfo4Redis.getUserId();
        log.info("userid = {}",downId);
        idList.add(downId);*/
        if (!dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ADDRESS_SYSTEM)){
            if (CollectionUtil.isEmpty(partnerIdList)){
                throw new BusinessException("10008","合作伙伴id不能为空");
            }
        }
        contacts = addressMapper.page4PartnerAddr(nameOrPhone, partnerIdList, (pageNum - 1)*pageSize, pageSize, name, phone);
        count = addressMapper.pageCount4PartnerAddr(nameOrPhone, partnerIdList, name, phone);
        pageData.setPage(pageNum);
        pageData.setCount(count);
        if(CollectionUtil.isNotEmpty(contacts)){
            decryptPhone(contacts);
            pageData.setData(contacts);
        }else{
            pageData.setData(new ArrayList<>());
        }
        return answer;
    }

    private void decryptPhone(List<Data4PartnerAddress> contacts){
        contacts.stream().forEach(contact->{
            dePhone(contact);
        });
    }

    private void dePhone(Data4PartnerAddress data4PartnerAddress){
        String partnerPhone = data4PartnerAddress.getPhone();
        if (StringUtils.isNotEmpty(partnerPhone)){
            partnerPhone = IOTEncodeUtils.decryptIOTMessage(partnerPhone, encodeKey);
            data4PartnerAddress.setPhone(DesensitizationUtils.replaceWithStar(partnerPhone));
        }
    }

}
