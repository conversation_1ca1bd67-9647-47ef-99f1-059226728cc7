package com.chinamobile.iot.sc.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @AUTHOR: HWF
 * @DATE: 2023/2/7
 */
@Data
public class ProMaterialDTO {

    @NotEmpty
    private String atomId;

    @NotEmpty(message = "物料编码不能为空")
    private String materialNum;
    @NotEmpty(message = "物料数量不能为空")
    private String materialCount;

    @NotEmpty(message = "合同编码不能为空")
    private String contractNum;

    /**
     * 合同类型1--销售合同 2--采购合同
     */
    private Integer contractType;


}
