package com.chinamobile.iot.sc.pojo.dto.gio;

import com.chinamobile.iot.sc.pojo.LogisticsInfo;
import com.chinamobile.iot.sc.pojo.Order2cAgentInfo;
import com.chinamobile.iot.sc.pojo.Order2cDistributorInfo;
import com.chinamobile.iot.sc.request.order2c.CouponInfoDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class GioOrderOriginDTO {
    private String orderId;
    private String custMgPhone;
    private String provinceOrgName;
    private Integer orderStatus;
    private String spuOfferingClass;
    private String saleOrderType;
    private Date orderStatusTime;
    private String createTime;
    private Date billNoTime;
    private String sendGoodsTime;
    private String receiveOrderTime;
    private String valetOrderCompleteTime;
    private String arrivalTime;
    private List<LogisticsInfo> logisticsInfoList;

    public List<Order2cDistributorInfo> order2cDistributorInfoList;
    private String createOperUserId;
    private String createOperCode;
    private String custName;
    private String custCode;
    private String custUserId;
    private List<Order2cAgentInfo> order2cAgentInfoList;
    private String orderingChannelName;
    private List<CouponInfoDTO> couponInfoList;
    private String addr1;
    private String addr2;
    private String addr3;
    private List<String> order2cAtomSnList;
    private String orderGrid;
    private String businessCode;

    // 网格信息字段 (来自 rise_order_2c_grid 表)
    private String gridProvince;
    private String gridCity;
    private String gridDistrict;
    private String gridName;

}
