package com.chinamobile.iot.sc.controller.iot;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.param.UpdateCustomerInfoParam;
import com.chinamobile.iot.sc.request.AllocateCardResultRequest;
import com.chinamobile.iot.sc.request.QrySubscribersRequest;
import com.chinamobile.iot.sc.request.SpecialReturnRefundRequest;
import com.chinamobile.iot.sc.response.iot.ServiceResultInfoResponse;
import com.chinamobile.iot.sc.service.IOrder2CService;
import com.chinamobile.iot.sc.service.Order2IOTService;
import com.chinamobile.iot.sc.service.ShopUserOperationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * @Author: YSC
 * @Date: 2021/11/3 16:51
 * @Description: C端订单同步
 */
@RestController
@RequestMapping("/os/orderservice")
public class Order2CController {

    @Resource
    private IOrder2CService iOrder2CService;

    @Resource
    private Order2IOTService order2IOTService;

    @Resource
    private ShopUserOperationService shopUserOperationService;

    /**
     * 订单同步接口
     *
     * @param baseRequest
     * @return
     */
    @RequestMapping("/Sync2COrderInfo")
    public IOTAnswer<Void> sync2COrderInfo(@RequestBody IOTRequest baseRequest) {
        return iOrder2CService.sync2COrderInfo(baseRequest);
    }

    /**
     * 订单退货退款申请/取消（含文件）
     *
     * @param baseRequest
     */
    @RequestMapping("/refundApplyOrCancel")
    public IOTAnswer<Void> refundApplyOrCancel(@RequestBody IOTRequest baseRequest) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return iOrder2CService.refundApplyOrCancel(baseRequest, ip);
    }

    /**
     * IOT应用市场向OS同步订单交付信息，含订单状态、物流信息等。
     *
     * @param baseRequest
     * @return
     */
    @RequestMapping("/syncReturnOrdersLogisInfo")
    public IOTAnswer<Void> syncReturnOrdersLogisInfo(@RequestBody IOTRequest baseRequest) {
        return iOrder2CService.syncReturnOrdersLogisInfo(baseRequest);
    }

    /**
     * 订单数据割接
     *
     * @param excel
     */
    @PostMapping("/orderInfoCutOver")
    public BaseAnswer orderInfoCutOver(@RequestPart("file") MultipartFile excel) {
        return iOrder2CService.orderInfoCutOver(excel);
    }

    @Autowired
    public void setIOrder2CService(IOrder2CService iOrder2CService) {
        this.iOrder2CService = iOrder2CService;
    }

    /**
     * 售后订单同步接口
     *
     * @param baseRequest
     * @return
     */
    @RequestMapping("/SyncAfterMarketOrderInfo")
    public IOTAnswer<Void> syncAfterMarketOrderInfo(@RequestBody IOTRequest baseRequest) {
        return iOrder2CService.syncAfterMarketOrderInfo(baseRequest);
    }


    /**
     * 商城同步特殊退货退款状态信息
     *
     * @param baseRequest
     * @return
     */
    @RequestMapping("/SpecialRefundResult")
    public IOTAnswer<Void> syncSpecialRefundStateBack(@RequestBody IOTRequest baseRequest) {
        return iOrder2CService.syncSpecialRefundState(baseRequest);
    }



    /**
     * 订单开启特殊退货退款申请,调试用例接口
     *
     * @param specialReturnRefundRequest
     * @return
     */
    @PostMapping("/openSpecialRefund")
    public BaseAnswer testOpenSpecialRefundOsLaunchRequest(@RequestBody SpecialReturnRefundRequest specialReturnRefundRequest) {
        iOrder2CService.openSpecialRefundOsLaunchRequest(null);
        return new BaseAnswer();
    }

    /**
     * test同步商城客户经理接口
     *
     * @return
     */
    @PostMapping("/sftpManagerInfo")
    public BaseAnswer testSftpOperationAccountManagerData() {
        iOrder2CService.sftpOperationAccountManagerData();
        return new BaseAnswer();
    }

    /**
     * test同步商城分销及注册用户接口
     *
     * @return
     */
    @PostMapping("/sftpCustomerInfo")
    public BaseAnswer testSftpOperationCustomerData() {
        iOrder2CService.sftpOperationCustomerData();
        return new BaseAnswer();
    }

    /**
     * 导入处理客户经理新增字段 历史数据  生成text文档sql语句
     *
     * @param file
     * @return
     */
    @PostMapping("/sftpManagerInfoImport")
    public BaseAnswer testShopManagerAddFieldImport(MultipartFile file) {
        iOrder2CService.shopManagerAddFieldImport(file);
        return new BaseAnswer();
    }

    /**
     * 导入处理普通用户分销员新增字段 历史数据  生成text文档sql语句
     *
     * @param file
     * @return
     */
    @PostMapping("/sftpCustomerInfoImport")
    public BaseAnswer testShopCustomerAddFieldImport(MultipartFile file) {
        iOrder2CService.shopCustomerAddFieldImport(file);
        return new BaseAnswer();
    }

    /**
     * 导入处理客户经理新增字段 历史数据 直接入库
     *
     * @param file
     * @return
     */
    @PostMapping("/sftpManagerInfoImportList")
    public BaseAnswer testShopManagerAddFieldImportList(MultipartFile file) {
        shopUserOperationService.shopManagerAddFieldImportList(file);
        return new BaseAnswer();
    }

    /**
     * 导入处理普通用户分销员新增字段 历史数据 直接入库
     *
     * @param file
     * @return
     */
    @PostMapping("/sftpCustomerInfoImportList")
    public BaseAnswer testShopCustomerAddFieldImportList(MultipartFile file) {
        shopUserOperationService.shopCustomerAddFieldImportList(file);
        return new BaseAnswer();
    }


    /**
     * 删除客户经理重复操作1的数据
     *
     * @param param
     * @return
     */
    @DeleteMapping("/deleteManagerRepetition")
    public void deleteSynAccountManagerRepetitionList(@RequestParam(value = "param") Integer param, HttpServletResponse response) throws Exception {
        iOrder2CService.deleteSynAccountManagerRepetition(param, response);
    }

    /**
     * 删除普通用户重复操作1的数据
     *
     * @param param
     * @return
     */
    @DeleteMapping("/deleteCustomerRepetition")
    public void deleteSynAccountCustomerRepetitionList(@RequestParam(value = "param") Integer param, HttpServletResponse response) throws Exception {
        iOrder2CService.deleteSynAccountCustomerRepetition(param, response);
    }

    /**
     * 删除已经注销的商城同步用户
     */
    @DeleteMapping("/deleteLogout")
    public void deleteCustomerLogoutList() {
        iOrder2CService.deleteCustomerLogout();
    }


    /**
     * 根据主键id 修改商城不能处理同步用户
     *
     * @param param
     */
    @PostMapping("/updateCustomerUser")
    public BaseAnswer updateCustomerUserInfo(@RequestBody UpdateCustomerInfoParam param) {
        iOrder2CService.updateCustomerUser(param);
        return new BaseAnswer();
    }

    /**
     * 导入普通用户数据
     * @param file
     */
    @PostMapping("/updateCustomerUserImport")
    public void sftpOperationCustomerListImport(MultipartFile file ) {
        shopUserOperationService.sftpOperationCustomerList(file);
    }

    /**
     * 订购结果反馈接口(卡+X代客下单不会走此接口，购失败了会产生失败单，人工介入处理)
     *
     * @param baseRequest
     * @return
     */
    @RequestMapping("/syncOrderingResult")
    public IOTAnswer<Void> syncOrderingResult(@RequestBody IOTRequest baseRequest) {
        return order2IOTService.syncOrderingResult(baseRequest);
    }

    /*
     * 下载增量订单状态变更的数据
     * @return
     */
    @GetMapping("/downloadDictOrderInfo")
    public BaseAnswer downloadDictOrderInfo() {
        iOrder2CService.downloadDictOrderInfo();
        return new BaseAnswer();
    }

    /**
     * 订单数据割接增加支付/退款时间
     *
     * @param excel
     */
    @PostMapping("/orderInfoTime")
    public BaseAnswer orderInfoTime(@RequestPart("file") MultipartFile excel) {
        return iOrder2CService.orderInfoTime(excel);
    }

    /**
     * 软件服务开通号码同步接口（云视讯手机号校验）
     *
     * @param baseRequest
     * @return
     */
    @RequestMapping("/syncServiceNumberInfo")
    public IOTAnswer<ServiceResultInfoResponse> syncServiceNumberInfo(@RequestBody IOTRequest baseRequest) {
        return iOrder2CService.syncServiceNumberInfo(baseRequest);
    }

    /**
     * 订单续费操作
     * @param baseRequest
     * @return
     */
    @RequestMapping("/synchronizeRenewalInformation")
    public IOTAnswer<Void> synchronizeRenewalInformation(@RequestBody IOTRequest baseRequest) {
        return order2IOTService.synchronizeRenewalInformation(baseRequest);
    }

    /**
     * 商城向物联网OS平台请求分配号卡信息
     *
     * @param baseRequest
     * @return
     */
    @RequestMapping("/AllocateCardRequest")
    public IOTAnswer<Void> allocateCardRequest(@RequestBody IOTRequest baseRequest) {
        return iOrder2CService.allocateCardRequest(baseRequest);
    }

    /**
     * 号卡分配结果反馈接口
     *
     * @param allocateCardResultRequest
     * @return
     */
    @PostMapping("/AllocateCardResultRequest")
    public BaseAnswer testAllocateCardResultRequest(@RequestBody AllocateCardResultRequest allocateCardResultRequest) {
        iOrder2CService.allocateCardResultRequest(allocateCardResultRequest);
        return new BaseAnswer();
    }

    /**
     * 号卡信息查询接口
     *
     * @param qrySubscribersRequest
     * @return
     */
    @PostMapping("/qrySubscribers")
    public BaseAnswer testQrySubscribers(@RequestBody QrySubscribersRequest qrySubscribersRequest) {
        iOrder2CService.qrySubscribers(qrySubscribersRequest);
        return new BaseAnswer();
    }
}
