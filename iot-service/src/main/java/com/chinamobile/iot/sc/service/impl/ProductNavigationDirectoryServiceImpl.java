package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.constant.RedisLockConstant;
import com.chinamobile.iot.sc.dao.ProductNavigationDirectoryMapper;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.MiniProgramOperateEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exception.IOTException;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceSpu;
import com.chinamobile.iot.sc.pojo.ProductNavigationDirectory;
import com.chinamobile.iot.sc.pojo.ProductNavigationDirectoryExample;
import com.chinamobile.iot.sc.pojo.param.SortNavigationParam;
import com.chinamobile.iot.sc.pojo.param.UpdateNavigationImageParam;
import com.chinamobile.iot.sc.pojo.vo.miniprogram.ProductNavigationDirectoryVO;
import com.chinamobile.iot.sc.request.ProductNavigationInfoMigrateRequest;
import com.chinamobile.iot.sc.request.ProductNavigationInfoRequest;
import com.chinamobile.iot.sc.service.IProductNavigationDirectoryService;
import com.chinamobile.iot.sc.service.LogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/21 11:18
 * @description TODO
 */
@Service
@Slf4j
public class ProductNavigationDirectoryServiceImpl implements IProductNavigationDirectoryService {

    @Resource
    private ProductNavigationDirectoryMapper productNavigationDirectoryMapper;

    @Resource
    private LogService logService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private RedissonClient redissonClient;

    @Override
    public List<ProductNavigationDirectoryVO> listAll() {
        String key = Constant.REDIS_KEY_PRODUCT_NAVIGATION_DIRECTORY;
        String lockKey = RedisLockConstant.PRODUCT_NAVIGATION_DIRECTORY_LOCK;

        List<ProductNavigationDirectoryVO> directory = (List<ProductNavigationDirectoryVO>) redisTemplate.opsForValue().get(key);
        if (!CollectionUtils.isEmpty(directory)) {
            return directory;
        }

        RLock rl = redissonClient.getLock(lockKey);
        try {
            rl.lock();
            // 再次检查缓存，防止其他线程已加载完成
            directory = (List<ProductNavigationDirectoryVO>) redisTemplate.opsForValue().get(key);
            if (CollectionUtils.isEmpty(directory)) {
                List<ProductNavigationDirectory> first = productNavigationDirectoryMapper.selectByExample(
                        new ProductNavigationDirectoryExample().createCriteria()
                                .andParentIdEqualTo("-1")
                                .andMenuEqualTo("1")
                                .andIsDeleteEqualTo(false)
                                .example().orderBy("sort")
                );
                if (!CollectionUtils.isEmpty(first)) {
                    directory = first.stream().map(item -> {
                        ProductNavigationDirectoryVO vo = new ProductNavigationDirectoryVO();
                        BeanUtils.copyProperties(item, vo);
                        List<ProductNavigationDirectory> second = productNavigationDirectoryMapper.selectByExample(
                                new ProductNavigationDirectoryExample().createCriteria()
                                        .andParentIdEqualTo(item.getId())
                                        .andIsDeleteEqualTo(false)
                                        .example().orderBy("sort")
                        );
                        if (!CollectionUtils.isEmpty(second)) {
                            List<ProductNavigationDirectoryVO> children = second.stream().map(child -> {
                                ProductNavigationDirectoryVO childVO = new ProductNavigationDirectoryVO();
                                BeanUtils.copyProperties(child, childVO);
                                childVO.setIsNew(childVO.getImage() == null);

                                List<ProductNavigationDirectory> third = productNavigationDirectoryMapper.selectByExample(
                                        new ProductNavigationDirectoryExample().createCriteria()
                                                .andParentIdEqualTo(child.getId())
                                                .andIsDeleteEqualTo(false)
                                                .example().orderBy("sort")
                                );
                                if (!CollectionUtils.isEmpty(third)) {
                                    childVO.setChildren(third.stream().map(thirdChild -> {
                                        ProductNavigationDirectoryVO thirdChildVO = new ProductNavigationDirectoryVO();
                                        BeanUtils.copyProperties(thirdChild, thirdChildVO);
                                        thirdChildVO.setIsNew(thirdChildVO.getImage() == null);
                                        return thirdChildVO;
                                    }).collect(Collectors.toList()));
                                }
                                return childVO;
                            }).collect(Collectors.toList());
                            vo.setChildren(children);
                        }
                        return vo;
                    }).collect(Collectors.toList());
                }
                if (!ObjectUtils.isEmpty(directory)) {
                    redisTemplate.opsForValue().set(key, directory); // 缓存新值
                }
            }
            return directory != null ? directory : Collections.emptyList();
        } finally {
            if (rl.isHeldByCurrentThread()) {
                rl.unlock(); // 释放写锁
            }
        }
    }

    @Override
    public void updateImage(UpdateNavigationImageParam param) {
        ProductNavigationDirectory productNavigationDirectory = productNavigationDirectoryMapper.selectByPrimaryKey(param.getDirectoryId());
        if (productNavigationDirectory == null) {
            throw new BusinessException(StatusConstant.PRODUCT_NAVIGATION_NOT_EXIST);
        }
        productNavigationDirectory.setImage(param.getImage());
        productNavigationDirectoryMapper.updateByPrimaryKeySelective(productNavigationDirectory);

        String key = Constant.REDIS_KEY_PRODUCT_NAVIGATION_DIRECTORY;
        redisTemplate.delete(key);
        redisTemplate.delete(Constant.REDIS_KEY_MINI_PRODUCT_NAVIGATION_DIRECTORY);

        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        String userId = request.getHeader(Constant.HEADER_KEY_USER_ID);
        ProductNavigationDirectory parent = productNavigationDirectoryMapper.selectByPrimaryKey(productNavigationDirectory.getParentId());
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.PRODUCT.code, "【编辑】\n'" + parent.getName() + "-" + productNavigationDirectory.getName() + "'目录图片重新上传", userId, 0, LogResultEnum.LOG_SUCESS.code, null);
    }

    @Override
    public void sort(List<SortNavigationParam> params) {
        List<ProductNavigationDirectory> list = new ArrayList<>();
        for (int i = 0; i < params.size(); i++) {
            SortNavigationParam param = params.get(i);
            ProductNavigationDirectory productNavigationDirectory = new ProductNavigationDirectory();
            productNavigationDirectory.setId(param.getDirectoryId());
            productNavigationDirectory.setSort(i);
            list.add(productNavigationDirectory);

            List<SortNavigationParam.SecondDir> secondDirs = param.getSecondDirs();
            if (!CollectionUtils.isEmpty(secondDirs)) {
                for (int j = 0; j < secondDirs.size(); j++) {
                    ProductNavigationDirectory child = new ProductNavigationDirectory();
                    child.setId(secondDirs.get(j).getSecondDirectoryId());
                    child.setSort(j);
                    list.add(child);
                    List<String> thirdIds = secondDirs.get(j).getThirdIds();
                    if (!CollectionUtils.isEmpty(thirdIds)) {
                        for (int k = 0; k < thirdIds.size(); k++) {
                            ProductNavigationDirectory third = new ProductNavigationDirectory();
                            third.setId(thirdIds.get(k));
                            third.setSort(k);
                            list.add(third);
                        }
                    }
                }
            }
        }
        list.forEach(productNavigationDirectory -> {
            productNavigationDirectoryMapper.updateByPrimaryKeySelective(productNavigationDirectory);
        });

        String key = Constant.REDIS_KEY_PRODUCT_NAVIGATION_DIRECTORY;
        redisTemplate.delete(key);
        redisTemplate.delete(Constant.REDIS_KEY_MINI_PRODUCT_NAVIGATION_DIRECTORY);

        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        String userId = request.getHeader(Constant.HEADER_KEY_USER_ID);
        logService.recordOperateLog(ModuleEnum.MINI_PROGRAM.code, MiniProgramOperateEnum.PRODUCT.code, "【编辑】\n修改商品导航目录排序", userId, 0, LogResultEnum.LOG_SUCESS.code, null);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public IOTAnswer<Void> SyncNavigationInfo(IOTRequest baseRequest) {
        log.info("导航目录同步请求:{}", JSON.toJSONString(baseRequest));
        Date now = new Date();
        IOTAnswer<Void> iotAnswer = new IOTAnswer<>();
        iotAnswer.setMessageSeq(baseRequest.getMessageSeq());
        ProductNavigationInfoRequest productNavigationInfoRequest;
        try {
            productNavigationInfoRequest = JSON.parseObject(baseRequest.getContent(), ProductNavigationInfoRequest.class);
        } catch (Exception e) {
            log.error("解析异常:" + e);
            throw new IOTException(iotAnswer, "数据解析异常");
        }
        String menu = productNavigationInfoRequest.getMenu();
        List<String> iotIds = new ArrayList<>();
        List<ProductNavigationDirectory> newList = new ArrayList<>();
        List<ProductNavigationDirectory> editList = new ArrayList<>();
        List<ProductNavigationDirectory> hasSortList = new ArrayList<>();
        productNavigationInfoRequest.getLevel1Navigation().forEach(level1Navigation -> {
            iotIds.add(level1Navigation.getLevel1NavigationCode());
            ProductNavigationDirectory level1 = productNavigationDirectoryMapper.selectByPrimaryKey(level1Navigation.getLevel1NavigationCode());
            if (level1 == null) {
                //新增
                level1 = new ProductNavigationDirectory();
                level1.setMenu(menu);
                level1.setParentId("-1");
                level1.setId(level1Navigation.getLevel1NavigationCode());
                level1.setName(level1Navigation.getLevel1NavigationName());
                level1.setIsDelete(false);
                level1.setCreateTime(new Date());
                level1.setUpdateTime(new Date());
                newList.add(level1);
            } else if (!StringUtils.equals(level1.getName(), level1Navigation.getLevel1NavigationName())) {
                //修改
                level1.setName(level1Navigation.getLevel1NavigationName());
                level1.setParentId("-1");
                level1.setUpdateTime(new Date());
                editList.add(level1);
                hasSortList.add(level1);
            } else {
                //不变，用于修改sort值
                hasSortList.add(level1);
            }

            if (!CollectionUtils.isEmpty(level1Navigation.getLevel2Navigation())) {
                level1Navigation.getLevel2Navigation().forEach(level2Navigation -> {
                    iotIds.add(level2Navigation.getLevel2NavigationCode());
                    ProductNavigationDirectory level2 = productNavigationDirectoryMapper.selectByPrimaryKey(level2Navigation.getLevel2NavigationCode());
                    if (level2 == null) {
                        //新增
                        level2 = new ProductNavigationDirectory();
                        level2.setMenu(menu);
                        level2.setParentId(level1Navigation.getLevel1NavigationCode());
                        level2.setId(level2Navigation.getLevel2NavigationCode());
                        level2.setName(level2Navigation.getLevel2NavigationName());
                        level2.setIsDelete(false);
                        level2.setCreateTime(new Date());
                        level2.setUpdateTime(new Date());
                        newList.add(level2);
                    } else if (!StringUtils.equals(level2.getName(), level2Navigation.getLevel2NavigationName())) {
                        //修改
                        level2.setName(level2Navigation.getLevel2NavigationName());
                        level2.setParentId(level1Navigation.getLevel1NavigationCode());
                        level2.setUpdateTime(new Date());
                        editList.add(level2);
                        hasSortList.add(level2);
                    } else {
                        //不变，用于修改sort值
                        hasSortList.add(level2);
                    }

                    if (!CollectionUtils.isEmpty(level2Navigation.getLevel3Navigation())) {
                        level2Navigation.getLevel3Navigation().forEach(level3Navigation -> {
                            iotIds.add(level3Navigation.getLevel3NavigationCode());
                            ProductNavigationDirectory level3 = productNavigationDirectoryMapper.selectByPrimaryKey(level3Navigation.getLevel3NavigationCode());
                            if (level3 == null) {
                                //新增
                                level3 = new ProductNavigationDirectory();
                                level3.setMenu(menu);
                                level3.setParentId(level2Navigation.getLevel2NavigationCode());
                                level3.setId(level3Navigation.getLevel3NavigationCode());
                                level3.setName(level3Navigation.getLevel3NavigationName());
                                level3.setIsDelete(false);
                                level3.setCreateTime(new Date());
                                level3.setUpdateTime(new Date());
                                newList.add(level3);
                            } else if (!StringUtils.equals(level3.getName(), level3Navigation.getLevel3NavigationName())) {
                                //修改
                                level3.setName(level3Navigation.getLevel3NavigationName());
                                level3.setParentId(level2Navigation.getLevel2NavigationCode());
                                level3.setUpdateTime(new Date());
                                editList.add(level3);
                                hasSortList.add(level3);
                            } else {
                                //不变，用于修改sort值
                                hasSortList.add(level3);
                            }
                        });
                    }

                });
            }
        });

        //处理sort值
        if (!CollectionUtils.isEmpty(newList)) {
            newList.forEach(x -> {
                Optional<ProductNavigationDirectory> optional = hasSortList.stream().filter(n -> StringUtils.equals(x.getParentId(),
                        n.getParentId()) && n.getSort() != null).max(Comparator.comparingInt(ProductNavigationDirectory::getSort));
                if (optional.isPresent()) {
                    x.setSort(optional.get().getSort() + 1);
                } else {
                    x.setSort(0);
                }
            });

            productNavigationDirectoryMapper.batchInsert(newList);
        }
        if (!CollectionUtils.isEmpty(editList)) {
            editList.forEach(x -> productNavigationDirectoryMapper.updateByPrimaryKey(x));
        }
        //不在商城同步的数据的目录，打上删除标记
        ProductNavigationDirectory delete = new ProductNavigationDirectory();
        delete.setIsDelete(true);
        productNavigationDirectoryMapper.updateByExampleSelective(delete, new ProductNavigationDirectoryExample().createCriteria()
                .andMenuEqualTo(menu).andIdNotIn(iotIds).example());

        redisTemplate.delete(Constant.REDIS_KEY_PRODUCT_NAVIGATION_DIRECTORY);
        redisTemplate.delete(Constant.REDIS_KEY_MINI_PRODUCT_LIST + "*");
        redisTemplate.delete(Constant.REDIS_KEY_MINI_PRODUCT_COUNT + "*");
        redisTemplate.delete(Constant.REDIS_KEY_MINI_PRODUCT_NAVIGATION_DIRECTORY);

        return iotAnswer;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void migrateProductNavigation(ProductNavigationInfoMigrateRequest request) {
        if (CollectionUtils.isEmpty(request.getData())) {
            return;
        }
        //迁移已有数据为物联网目录
        List<ProductNavigationDirectory> oldIotList = productNavigationDirectoryMapper.selectByExample(new ProductNavigationDirectoryExample());
        Map<String, ProductNavigationDirectory> old2NewIotMap = new LinkedHashMap<>();
        Map<String, ProductNavigationDirectory> oldNameMap = new LinkedHashMap<>();
        Map<String, ProductNavigationDirectory> oldIdMap = new LinkedHashMap<>();
        Set<String> level1Set = new HashSet<>();
        oldIotList.forEach(x -> oldIdMap.put(x.getId(), x));
        oldIotList.forEach(x -> {
            String pref = "";
            if (!StringUtils.equals(x.getParentId(), "-1")) {
                pref = pref + oldIdMap.get(x.getParentId()).getName();
            }
            oldNameMap.put(pref + x.getName(), x);
        });
        List<ProductNavigationDirectory> newList = new ArrayList<>();
        request.getData().forEach(item -> {
            if (!level1Set.contains(item.getLevel1NavigationCode())) {
                ProductNavigationDirectory directory = new ProductNavigationDirectory();
                directory.setId(item.getLevel1NavigationCode());
                directory.setName(item.getLevel1NavigationName());
                directory.setParentId("-1");
                directory.setMenu(item.getMenu());
                directory.setIsDelete(false);
                if (StringUtils.equals("1", item.getMenu())) {
                    ProductNavigationDirectory oldItem = oldNameMap.get(directory.getName());
                    if (oldItem != null) {
                        directory.setSort(oldItem.getSort());
                        directory.setImage(oldItem.getImage());
                        old2NewIotMap.put(oldItem.getId(), directory);
                    }
                }

                newList.add(directory);
                level1Set.add(item.getLevel1NavigationCode());
            }

            ProductNavigationDirectory level2 = new ProductNavigationDirectory();
            level2.setId(item.getLevel2NavigationCode());
            level2.setName(item.getLevel2NavigationName());
            level2.setParentId(item.getLevel1NavigationCode());
            level2.setMenu(item.getMenu());
            level2.setIsDelete(false);
            if (StringUtils.equals("1", item.getMenu())) {
                ProductNavigationDirectory oldItem2 = oldNameMap.get(item.getLevel1NavigationName() + level2.getName());
                if (oldItem2 != null) {
                    level2.setSort(oldItem2.getSort());
                    level2.setImage(oldItem2.getImage());
                    old2NewIotMap.put(oldItem2.getId(), level2);
                }
            }
            newList.add(level2);
        });


        if (oldIotList.stream().allMatch(x -> old2NewIotMap.containsKey(x.getId()))) {
            //迁移上下架中目录ID
            oldIotList.forEach(old -> {
                ProductNavigationDirectory directory = old2NewIotMap.get(old.getId());
                if (StringUtils.equals("-1", directory.getParentId())) {
                    ProductFlowInstanceSpu spu = new ProductFlowInstanceSpu();
//                    spu.setFirstDirectoryId(directory.getId());
//                    productFlowInstanceSpuMapper.updateByExampleSelective(spu, new ProductFlowInstanceSpuExample().createCriteria()
//                            .andFirstDirectoryIdEqualTo(old.getId()).example());
                } else {
                    ProductFlowInstanceSpu spu = new ProductFlowInstanceSpu();
//                    spu.setSecondDirectoryId(directory.getId());
//                    productFlowInstanceSpuMapper.updateByExampleSelective(spu, new ProductFlowInstanceSpuExample().createCriteria()
//                            .andSecondDirectoryIdEqualTo(old.getId()).example());
                }
            });

            productNavigationDirectoryMapper.deleteByExample(new ProductNavigationDirectoryExample());
            productNavigationDirectoryMapper.batchInsert(newList);

            redisTemplate.delete(Constant.REDIS_KEY_PRODUCT_NAVIGATION_DIRECTORY);
            redisTemplate.delete(Constant.REDIS_KEY_MINI_PRODUCT_LIST + "*");
            redisTemplate.delete(Constant.REDIS_KEY_MINI_PRODUCT_COUNT + "*");
            redisTemplate.delete(Constant.REDIS_KEY_MINI_PRODUCT_NAVIGATION_DIRECTORY);

        } else {
            throw new BusinessException(StatusConstant.PARAM_ERROR, "存在历史数据有而迁移之后没有的情况:"
                    + JSON.toJSONString(org.apache.commons.collections4.CollectionUtils.subtract(
                    oldIotList.stream().map(ProductNavigationDirectory::getId).collect(Collectors.toList()),
                    new ArrayList<>(old2NewIotMap.keySet()))));
        }

    }
}
