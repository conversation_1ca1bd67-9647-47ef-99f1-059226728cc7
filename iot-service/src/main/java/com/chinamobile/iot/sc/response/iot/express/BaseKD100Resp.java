package com.chinamobile.iot.sc.response.iot.express;

import lombok.Data;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2022/4/13 16:47
 * @description: 快递100返回
 **/
@Data
public class BaseKD100Resp {


    /**
     * 结果
     */
    private Boolean result;

    /**
     * 监控状态相关消息，如:3天查询无记录，60天无变化
     */
    private String returnCode;

    /**
     *
     */
    private String message;

    public BaseKD100Resp() {
        this.result = true;
        this.returnCode = "200";
        this.message = "成功";
    }

    public BaseKD100Resp(Boolean result, String returnCode, String message) {
        this.result = result;
        this.returnCode = returnCode;
        this.message = message;
    }
}
