package com.chinamobile.iot.sc.pojo.dto.gio;

import com.chinamobile.iot.sc.pojo.Order2cAgentInfo;
import com.chinamobile.iot.sc.request.order2c.CouponInfoDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class GioPrudctOrderPndingOriginDTO {
    private String orderId;
    private String custMgPhone;
    private String provinceOrgName;
    private Integer orderStatus;
    private String spuOfferingClass;
    private String saleOrderType;
    private Date orderStatusTime;
    private String createTime;
    private Date billNoTime;
    private String sendGoodsTime;
    private String receiveOrderTime;
    private String valetOrderCompleteTime;
    private String arrivalTime;
    private Long atomQuantity;
    private Long quantity;
    private String expensesPrice;
    private Integer orderQuantity;
    private String expensesTerm;
    private String orderType;

    private String createOperUserId;
    private String createOperCode;
    private String custName;
    private String custUserId;
    private Long atomPrice;
    private String orderingChannelName;

    private String addr1;
    private String addr2;
    private String addr3;

    private String orderGrid;
    private String custCode;
    private String totalPrice;
    private String businessCode;
    private String beId;
    private String location;
    private String regionID;
    //    GioPrudctOrderPndingOriginDTO
    private String skuCode;
    private Long atomSettlePrice;
    private Long atomSalePrice;
    private String spuVersion;
    private String skuVersion;
    private String offeringVersion;
    private String offeringId;
    private Long skuPrice;
    private Long atomOfferingQuantity;
    private Long offeringSettlePrice;
    private Long offeringPrice;
    private String deductPrice;
    private List<CouponInfoDTO> couponInfoList;
    private List<Order2cAgentInfo> agentInfoList;

    // 添加chargeCode字段以避免单独查询数据库
    private String chargeCode;

    // 添加chargeId字段用于埋点（替代chargeCode用于charge_code_var）
    private String chargeId;

    // 添加offeringCode字段用于埋点
    private String offeringCode;

    // 添加atomOfferingClass字段用于埋点
    private String atomOfferingClass;

}
