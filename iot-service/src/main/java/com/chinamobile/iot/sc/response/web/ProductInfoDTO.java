package com.chinamobile.iot.sc.response.web;

import lombok.Data;

import java.util.Date;

/**
 * @Author: YSC
 * @Date: 2021/11/10 11:10
 * @Description:
 */
@Data
public class ProductInfoDTO {
    /**
     * 规格商品ID
     */
    private String id;
    /**
     * 商品类型
     */
    private String spuOfferingClass;
//    /**
//     *  商品类型
//     */
//    private String skuComposition;
    /**
     * 商品名称（规格）
     */
    private String skuOfferingName;
    /**
     * 原子商品名称
     */
    private String atomOfferingName;
    /**
     * 原子商品类型
     */
    private String atomOfferingClass;
    /**
     * 型号
     */
    private String model;
    /**
     * 颜色
     */
    private String color;
    /**
     * 单价
     */
    private Long atomPrice;
    /**
     * 配置状态 0--未配置  1--已配置
     */
    private Integer configAllStatus;

    /**
     * 合作伙伴联系人姓名
     */
    private String cooperatorName;

    /**
     * 合作伙伴id
     */
    private String cooperatorId;

    /**
     * 合作伙伴名
     */
    private String partnerName;

    /**
     * 商品组/销售商品编码
     */
    private String  spuOfferingCode;

    /**
     *商品组/销售商品名称
     */
    private String  spuOfferingName;

    /**
     * 销售商品状态（0：测试，1：发布，2：下架）
     */
    private String  spuOfferingStatus;

    /**
     *商品编码（规格）
     */
    private String  skuOfferingCode;

    /**
     * 规格商品状态（0：测试，1：发布，2：下架）
     */
    private String  skuOfferingStatus;

    /**
     * 创建时间
     */
    private String  createTime;

    /**
     * 全量配置时间
     */
    private String  configAllTime;

    /**
     * 是否关闭配置合作伙伴的功能，用于ONENET和DICT类商品。默认不关闭
     */
    private Boolean disableConfig = false;

    /**
     * 商品封面图url
     *
     */
    private String imgUrl;

    /**
     * 原子编码
     *
     */
    private String atomOfferingCode;

}
