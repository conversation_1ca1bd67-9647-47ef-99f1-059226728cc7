package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> xie<PERSON><PERSON><PERSON>
 * @date : 2022/9/23 11:04
 * @description: 查询产品信息详情VO
 **/
@Data
public class NewProductManageDetailsVO {

    /**
     * spu商品名称
     */
    private String spuOfferingName;

    /**
     * 商品名称SKU
     */
    private String skuOfferingName;

    /**
     * 合作伙伴名称
     */
    private String cooperatorName;

    /**
     * 典型应用领域
     */
    private String applicationDomain;


    /**
     * 商品简介
     */
    private String productIntroduction;

    /**
     * 产品销售区域
     */
    private String productSaleArea;

    /**
     * 商品规格供货价（元）
     */
    private BigDecimal supplyPrice;

    /**
     * 产品经理名字
     */
    private String productManagerName;


    /**
     * 产品经理电话
     */
    private String productManagerPhone;

    /**
     * 产品经理邮箱
     */
    private String productManagerEmail;
}
