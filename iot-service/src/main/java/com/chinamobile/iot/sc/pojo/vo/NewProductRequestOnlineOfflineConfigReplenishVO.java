package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/21
 * @description 新产品上下架配置补充说明
 */
@Data
public class NewProductRequestOnlineOfflineConfigReplenishVO {

    /**
     * 配置补充主键id
     */
    private String configReplenishId;

    /**
     * 商城展示一级类目
     */
    private String storeFirstCatalog;

    private String storeFirstCatalogName;

    /**
     * 商城展示二级类目
     */
    private String storeSecondCatalog;

    private String storeSecondCatalogName;

    /**
     * 原子上架类目
     */
    private String spuOfferingClass;

    /**
     * 原子商品名称
     */
    private String atomOfferingName;

    /**
     * 商品规格销售价（元）
     */
    private BigDecimal salePrice;

    /**
     * 硬件原子结算价（元）
     */
    private BigDecimal atomHardwareSettlePrice;

    /**
     * 硬件原子销售价（元）
     */
    private BigDecimal atomHardwareSalePrice;

    /**
     * 软件原子销售价（元）
     */
    private BigDecimal atomSoftwareSalePrice;

    /**
     * 省公司计收额（元）
     */
    private BigDecimal provinceAccrual;

    /**
     * 产品归属部门
     */
    private Integer departmentId;

    /**
     * 产品归属部门名称
     */
    private String departmentName;

    /**
     * 产品属性01--自有  02--非自有
     */
    private String productProperty;
}
