package com.chinamobile.iot.sc.controller.order;

import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.request.DeleteDirtyOrderRequest;
import com.chinamobile.iot.sc.service.IOrder2CService;
import com.chinamobile.iot.sc.service.MagicService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2022/4/13 14:27
 */
@RestController
@RequestMapping("/magicorder")
public class MagicOrderController {

    @Autowired
    private IOrder2CService iOrder2CService;

    @Resource
    private MagicService magicService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @PostMapping("/order/delete")
    @Auth(authCode = {BaseConstant.ADMIN_ROLE})
    public BaseAnswer<Void> deleteOrder(
            @RequestBody @Valid DeleteDirtyOrderRequest orders) {
        return iOrder2CService.deleteDirtyOrder(orders);
    }

    @PostMapping("/order/testupdate")
    @Auth(authCode = {BaseConstant.ADMIN_ROLE})
    public BaseAnswer<Void> testUpdate(@RequestParam String orderId, @RequestParam Integer status, @RequestParam String remark) {
        return iOrder2CService.testUpdate(orderId, status, remark);
    }



    /**
     * 获取redis字符串消息
     * @param redisKey
     * @return
     */
    @GetMapping(value = "/getRedisStringInfo")
    public BaseAnswer getRedisStringInfo(@RequestParam(value = "redisKey")String redisKey){
        BaseAnswer baseAnswer = new BaseAnswer();
        String value = stringRedisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNotEmpty(value)){
            baseAnswer.setData(value);
        }
        return baseAnswer;
    }

    /**
     * 删除redis字符串消息
     * @param redisKey
     * @return
     */
    @GetMapping(value = "/deleteRedisString")
    public BaseAnswer deleteRedisString(@RequestParam(value = "redisKey")String redisKey){
        BaseAnswer baseAnswer = new BaseAnswer();
        Boolean isRedisKey = stringRedisTemplate.hasKey(redisKey);
        if (isRedisKey != null && isRedisKey){
            stringRedisTemplate.delete(redisKey);
        }
        return baseAnswer;
    }

    @GetMapping("/order/testOrder2cInfoSM4")
    public void testOrder2cInfoSM4() {
         magicService.testOrder2cInfoSM4Sql();
    }

    @PostMapping("/order/cutover/provinceorg")
    public BaseAnswer<Void> cutoverOrderProvinceorg(@RequestPart("file") MultipartFile file){
        magicService.cutoverOrderProvinceorg(file);
        return new BaseAnswer<>();
    }

    /**
     * 由于查合作伙伴地址失败，退换货请求重复同步，造成数据重复处理
     */
    @PostMapping("/order/roc/duplicate")
    public BaseAnswer<Void> duplicateOrderRoc(@RequestParam String prikey){
        magicService.duplicateOrderRoc(prikey);
        return new BaseAnswer<>();
    }

    @PostMapping("/order/roc/duplicate/history")
    public BaseAnswer<Void> duplicateOrderRocHistory(@RequestParam String atomId, @RequestParam String refundOrderId, @RequestParam Integer operType,
                                                     @RequestParam Integer innerStatus, @RequestParam String createTime, @RequestParam String updateTime){
        magicService.duplicateOrderRocHistory(atomId, refundOrderId, operType, innerStatus, createTime, updateTime);
        return new BaseAnswer<>();
    }

    @PostMapping("/order/roc/duplicate/modistatus")
    public BaseAnswer<Void> duplicateOrderRocModiStatus(@RequestParam String prikey, @RequestParam Integer innerStatus,
                                                        @RequestParam Integer orgStatus, @RequestParam String updateTime){
        magicService.duplicateOrderRocModiStatus(prikey, innerStatus, orgStatus, updateTime);
        return new BaseAnswer<>();
    }

}
