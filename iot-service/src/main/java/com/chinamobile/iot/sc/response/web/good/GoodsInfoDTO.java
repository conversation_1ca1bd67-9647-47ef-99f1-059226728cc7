package com.chinamobile.iot.sc.response.web.good;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @package: com.chinamobile.iot.sc.pojo.goods
 * @ClassName: GoodsInfo
 * @description: 货物清单信息
 * @author: zyj
 * @create: 2022/1/30 9:36
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
@Accessors(chain = true)
public class GoodsInfoDTO {
    /**
     * 订单编号(业务订单id)
     */
    private String orderId;
    /**
     * 下单时间
     */
    private Date createTime;
    /**
     * 客户姓名
     */
    private String custName;
    /**
     * 商品数量
     */
    private Long quantity;
    /**
     * 商品详情
     */
    private List<Good> goodList;

    @Data
    @Accessors(chain = true)
    public static class Good{
        /**
         * 商品名称(规格)
         */
        private String skuOfferingName;
        /**
         * 商品编码
         */
        private String skuOfferingCode;
        /**
         * 原子商品名称
         */
        private String atomOfferingName;
        /**
         * skuQuantity * atomQuantity
         */
        private String saQuantity;
        /**
         * 单价  元
         */
        private String atomPrice;
        /**
         * 总价  元
         */
        private String sumPrice;
    }

}
