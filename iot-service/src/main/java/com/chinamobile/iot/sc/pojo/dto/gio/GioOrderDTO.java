package com.chinamobile.iot.sc.pojo.dto.gio;

import lombok.Data;

@Data
public class GioOrderDTO {

    private String item_id;
    private GioOrderAttr attrs;
    @Data
    public static class GioOrderAttr{
        private String order_status;
        private String order_status_change_time;
        private String create_time;
        private String shipping_time;
        private String accept_time;
        private String billing_time;
        private String arrival_time;
        private String refund_amount;
        private String received_amount;
        private String tracking_number;
        private String chack_time;  // 注意：疑似拼写错误（check -> chack）
        private String billing_order_type;
        private String purchase_order_code;
        private String settlement_status;
        private String distributor_l1_user_id;
        private String distributor_l2_user_id;
        private String share_code_l1;
        private String share_code_l2;
        private String distributor_l1_phone;
        private String distributor_l2_phone;
        private String customer_manager_id;
        private String customer_manager_code;
        private String customer_manager_name;
        private String customer_manager_phone;
        private String channel_partner_name;
        private String channel_partner_phone;
        private String channel_partner_code;
        private String channel_partner_user_id;
        private String order_channel_name;
        private String coupon_info;
        private String shipping_province;
        private String shipping_city;
        private String shipping_area;
        private String sn_var;
        private String order_province;
        private String order_city;
        private String order_area;
        private String order_grid;
        private String cus_code;
        private String cus_name;
        private String business_code;

        // 新增字段
        private String channelTag;        // 渠道商标签
        private String channelCategory;   // 渠道商类别
        private String busiPersonCode;    // 业务人员编码
        private String busiPersonName;    // 业务人员姓名

        // 网格字段（从文杰清洗获取）
        private String gridProvince;      // 网格省
        private String gridCity;          // 网格市
        private String gridDistrict;      // 网格区
    }


}
