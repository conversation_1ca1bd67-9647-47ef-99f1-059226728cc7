package com.chinamobile.iot.sc.pojo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class NewProductRequestOnlineOfflineComboInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public NewProductRequestOnlineOfflineComboInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public NewProductRequestOnlineOfflineComboInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    public NewProductRequestOnlineOfflineComboInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    public static Criteria newAndCreateCriteria() {
        NewProductRequestOnlineOfflineComboInfoExample example = new NewProductRequestOnlineOfflineComboInfoExample();
        return example.createCriteria();
    }

    public NewProductRequestOnlineOfflineComboInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    public NewProductRequestOnlineOfflineComboInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdIsNull() {
            addCriterion("new_product_request_id is null");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdIsNotNull() {
            addCriterion("new_product_request_id is not null");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdEqualTo(String value) {
            addCriterion("new_product_request_id =", value, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("new_product_request_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdNotEqualTo(String value) {
            addCriterion("new_product_request_id <>", value, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("new_product_request_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdGreaterThan(String value) {
            addCriterion("new_product_request_id >", value, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("new_product_request_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdGreaterThanOrEqualTo(String value) {
            addCriterion("new_product_request_id >=", value, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("new_product_request_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdLessThan(String value) {
            addCriterion("new_product_request_id <", value, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("new_product_request_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdLessThanOrEqualTo(String value) {
            addCriterion("new_product_request_id <=", value, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("new_product_request_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdLike(String value) {
            addCriterion("new_product_request_id like", value, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdNotLike(String value) {
            addCriterion("new_product_request_id not like", value, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdIn(List<String> values) {
            addCriterion("new_product_request_id in", values, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdNotIn(List<String> values) {
            addCriterion("new_product_request_id not in", values, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdBetween(String value1, String value2) {
            addCriterion("new_product_request_id between", value1, value2, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdNotBetween(String value1, String value2) {
            addCriterion("new_product_request_id not between", value1, value2, "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andRequestNoIsNull() {
            addCriterion("request_no is null");
            return (Criteria) this;
        }

        public Criteria andRequestNoIsNotNull() {
            addCriterion("request_no is not null");
            return (Criteria) this;
        }

        public Criteria andRequestNoEqualTo(String value) {
            addCriterion("request_no =", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("request_no = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestNoNotEqualTo(String value) {
            addCriterion("request_no <>", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("request_no <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestNoGreaterThan(String value) {
            addCriterion("request_no >", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("request_no > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestNoGreaterThanOrEqualTo(String value) {
            addCriterion("request_no >=", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("request_no >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestNoLessThan(String value) {
            addCriterion("request_no <", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("request_no < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestNoLessThanOrEqualTo(String value) {
            addCriterion("request_no <=", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("request_no <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestNoLike(String value) {
            addCriterion("request_no like", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoNotLike(String value) {
            addCriterion("request_no not like", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoIn(List<String> values) {
            addCriterion("request_no in", values, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoNotIn(List<String> values) {
            addCriterion("request_no not in", values, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoBetween(String value1, String value2) {
            addCriterion("request_no between", value1, value2, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoNotBetween(String value1, String value2) {
            addCriterion("request_no not between", value1, value2, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestPassIsNull() {
            addCriterion("request_pass is null");
            return (Criteria) this;
        }

        public Criteria andRequestPassIsNotNull() {
            addCriterion("request_pass is not null");
            return (Criteria) this;
        }

        public Criteria andRequestPassEqualTo(Integer value) {
            addCriterion("request_pass =", value, "requestPass");
            return (Criteria) this;
        }

        public Criteria andRequestPassEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("request_pass = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestPassNotEqualTo(Integer value) {
            addCriterion("request_pass <>", value, "requestPass");
            return (Criteria) this;
        }

        public Criteria andRequestPassNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("request_pass <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestPassGreaterThan(Integer value) {
            addCriterion("request_pass >", value, "requestPass");
            return (Criteria) this;
        }

        public Criteria andRequestPassGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("request_pass > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestPassGreaterThanOrEqualTo(Integer value) {
            addCriterion("request_pass >=", value, "requestPass");
            return (Criteria) this;
        }

        public Criteria andRequestPassGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("request_pass >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestPassLessThan(Integer value) {
            addCriterion("request_pass <", value, "requestPass");
            return (Criteria) this;
        }

        public Criteria andRequestPassLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("request_pass < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestPassLessThanOrEqualTo(Integer value) {
            addCriterion("request_pass <=", value, "requestPass");
            return (Criteria) this;
        }

        public Criteria andRequestPassLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("request_pass <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestPassIn(List<Integer> values) {
            addCriterion("request_pass in", values, "requestPass");
            return (Criteria) this;
        }

        public Criteria andRequestPassNotIn(List<Integer> values) {
            addCriterion("request_pass not in", values, "requestPass");
            return (Criteria) this;
        }

        public Criteria andRequestPassBetween(Integer value1, Integer value2) {
            addCriterion("request_pass between", value1, value2, "requestPass");
            return (Criteria) this;
        }

        public Criteria andRequestPassNotBetween(Integer value1, Integer value2) {
            addCriterion("request_pass not between", value1, value2, "requestPass");
            return (Criteria) this;
        }

        public Criteria andThingsCardComboIsNull() {
            addCriterion("things_card_combo is null");
            return (Criteria) this;
        }

        public Criteria andThingsCardComboIsNotNull() {
            addCriterion("things_card_combo is not null");
            return (Criteria) this;
        }

        public Criteria andThingsCardComboEqualTo(String value) {
            addCriterion("things_card_combo =", value, "thingsCardCombo");
            return (Criteria) this;
        }

        public Criteria andThingsCardComboEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("things_card_combo = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardComboNotEqualTo(String value) {
            addCriterion("things_card_combo <>", value, "thingsCardCombo");
            return (Criteria) this;
        }

        public Criteria andThingsCardComboNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("things_card_combo <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardComboGreaterThan(String value) {
            addCriterion("things_card_combo >", value, "thingsCardCombo");
            return (Criteria) this;
        }

        public Criteria andThingsCardComboGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("things_card_combo > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardComboGreaterThanOrEqualTo(String value) {
            addCriterion("things_card_combo >=", value, "thingsCardCombo");
            return (Criteria) this;
        }

        public Criteria andThingsCardComboGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("things_card_combo >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardComboLessThan(String value) {
            addCriterion("things_card_combo <", value, "thingsCardCombo");
            return (Criteria) this;
        }

        public Criteria andThingsCardComboLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("things_card_combo < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardComboLessThanOrEqualTo(String value) {
            addCriterion("things_card_combo <=", value, "thingsCardCombo");
            return (Criteria) this;
        }

        public Criteria andThingsCardComboLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("things_card_combo <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andThingsCardComboLike(String value) {
            addCriterion("things_card_combo like", value, "thingsCardCombo");
            return (Criteria) this;
        }

        public Criteria andThingsCardComboNotLike(String value) {
            addCriterion("things_card_combo not like", value, "thingsCardCombo");
            return (Criteria) this;
        }

        public Criteria andThingsCardComboIn(List<String> values) {
            addCriterion("things_card_combo in", values, "thingsCardCombo");
            return (Criteria) this;
        }

        public Criteria andThingsCardComboNotIn(List<String> values) {
            addCriterion("things_card_combo not in", values, "thingsCardCombo");
            return (Criteria) this;
        }

        public Criteria andThingsCardComboBetween(String value1, String value2) {
            addCriterion("things_card_combo between", value1, value2, "thingsCardCombo");
            return (Criteria) this;
        }

        public Criteria andThingsCardComboNotBetween(String value1, String value2) {
            addCriterion("things_card_combo not between", value1, value2, "thingsCardCombo");
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListIsNull() {
            addCriterion("hardware_shipping_list is null");
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListIsNotNull() {
            addCriterion("hardware_shipping_list is not null");
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListEqualTo(String value) {
            addCriterion("hardware_shipping_list =", value, "hardwareShippingList");
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("hardware_shipping_list = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListNotEqualTo(String value) {
            addCriterion("hardware_shipping_list <>", value, "hardwareShippingList");
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("hardware_shipping_list <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListGreaterThan(String value) {
            addCriterion("hardware_shipping_list >", value, "hardwareShippingList");
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("hardware_shipping_list > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListGreaterThanOrEqualTo(String value) {
            addCriterion("hardware_shipping_list >=", value, "hardwareShippingList");
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("hardware_shipping_list >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListLessThan(String value) {
            addCriterion("hardware_shipping_list <", value, "hardwareShippingList");
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("hardware_shipping_list < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListLessThanOrEqualTo(String value) {
            addCriterion("hardware_shipping_list <=", value, "hardwareShippingList");
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("hardware_shipping_list <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListLike(String value) {
            addCriterion("hardware_shipping_list like", value, "hardwareShippingList");
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListNotLike(String value) {
            addCriterion("hardware_shipping_list not like", value, "hardwareShippingList");
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListIn(List<String> values) {
            addCriterion("hardware_shipping_list in", values, "hardwareShippingList");
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListNotIn(List<String> values) {
            addCriterion("hardware_shipping_list not in", values, "hardwareShippingList");
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListBetween(String value1, String value2) {
            addCriterion("hardware_shipping_list between", value1, value2, "hardwareShippingList");
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListNotBetween(String value1, String value2) {
            addCriterion("hardware_shipping_list not between", value1, value2, "hardwareShippingList");
            return (Criteria) this;
        }

        public Criteria andProductParamIsNull() {
            addCriterion("product_param is null");
            return (Criteria) this;
        }

        public Criteria andProductParamIsNotNull() {
            addCriterion("product_param is not null");
            return (Criteria) this;
        }

        public Criteria andProductParamEqualTo(String value) {
            addCriterion("product_param =", value, "productParam");
            return (Criteria) this;
        }

        public Criteria andProductParamEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_param = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductParamNotEqualTo(String value) {
            addCriterion("product_param <>", value, "productParam");
            return (Criteria) this;
        }

        public Criteria andProductParamNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_param <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductParamGreaterThan(String value) {
            addCriterion("product_param >", value, "productParam");
            return (Criteria) this;
        }

        public Criteria andProductParamGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_param > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductParamGreaterThanOrEqualTo(String value) {
            addCriterion("product_param >=", value, "productParam");
            return (Criteria) this;
        }

        public Criteria andProductParamGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_param >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductParamLessThan(String value) {
            addCriterion("product_param <", value, "productParam");
            return (Criteria) this;
        }

        public Criteria andProductParamLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_param < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductParamLessThanOrEqualTo(String value) {
            addCriterion("product_param <=", value, "productParam");
            return (Criteria) this;
        }

        public Criteria andProductParamLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_param <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductParamLike(String value) {
            addCriterion("product_param like", value, "productParam");
            return (Criteria) this;
        }

        public Criteria andProductParamNotLike(String value) {
            addCriterion("product_param not like", value, "productParam");
            return (Criteria) this;
        }

        public Criteria andProductParamIn(List<String> values) {
            addCriterion("product_param in", values, "productParam");
            return (Criteria) this;
        }

        public Criteria andProductParamNotIn(List<String> values) {
            addCriterion("product_param not in", values, "productParam");
            return (Criteria) this;
        }

        public Criteria andProductParamBetween(String value1, String value2) {
            addCriterion("product_param between", value1, value2, "productParam");
            return (Criteria) this;
        }

        public Criteria andProductParamNotBetween(String value1, String value2) {
            addCriterion("product_param not between", value1, value2, "productParam");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceIsNull() {
            addCriterion("product_ship_province is null");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceIsNotNull() {
            addCriterion("product_ship_province is not null");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceEqualTo(String value) {
            addCriterion("product_ship_province =", value, "productShipProvince");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_province = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNotEqualTo(String value) {
            addCriterion("product_ship_province <>", value, "productShipProvince");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_province <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceGreaterThan(String value) {
            addCriterion("product_ship_province >", value, "productShipProvince");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_province > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("product_ship_province >=", value, "productShipProvince");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_province >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceLessThan(String value) {
            addCriterion("product_ship_province <", value, "productShipProvince");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_province < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceLessThanOrEqualTo(String value) {
            addCriterion("product_ship_province <=", value, "productShipProvince");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_province <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceLike(String value) {
            addCriterion("product_ship_province like", value, "productShipProvince");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNotLike(String value) {
            addCriterion("product_ship_province not like", value, "productShipProvince");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceIn(List<String> values) {
            addCriterion("product_ship_province in", values, "productShipProvince");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNotIn(List<String> values) {
            addCriterion("product_ship_province not in", values, "productShipProvince");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceBetween(String value1, String value2) {
            addCriterion("product_ship_province between", value1, value2, "productShipProvince");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNotBetween(String value1, String value2) {
            addCriterion("product_ship_province not between", value1, value2, "productShipProvince");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameIsNull() {
            addCriterion("product_ship_province_name is null");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameIsNotNull() {
            addCriterion("product_ship_province_name is not null");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameEqualTo(String value) {
            addCriterion("product_ship_province_name =", value, "productShipProvinceName");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_province_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameNotEqualTo(String value) {
            addCriterion("product_ship_province_name <>", value, "productShipProvinceName");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_province_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameGreaterThan(String value) {
            addCriterion("product_ship_province_name >", value, "productShipProvinceName");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_province_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameGreaterThanOrEqualTo(String value) {
            addCriterion("product_ship_province_name >=", value, "productShipProvinceName");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_province_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameLessThan(String value) {
            addCriterion("product_ship_province_name <", value, "productShipProvinceName");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_province_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameLessThanOrEqualTo(String value) {
            addCriterion("product_ship_province_name <=", value, "productShipProvinceName");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_province_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameLike(String value) {
            addCriterion("product_ship_province_name like", value, "productShipProvinceName");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameNotLike(String value) {
            addCriterion("product_ship_province_name not like", value, "productShipProvinceName");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameIn(List<String> values) {
            addCriterion("product_ship_province_name in", values, "productShipProvinceName");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameNotIn(List<String> values) {
            addCriterion("product_ship_province_name not in", values, "productShipProvinceName");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameBetween(String value1, String value2) {
            addCriterion("product_ship_province_name between", value1, value2, "productShipProvinceName");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameNotBetween(String value1, String value2) {
            addCriterion("product_ship_province_name not between", value1, value2, "productShipProvinceName");
            return (Criteria) this;
        }

        public Criteria andProductShipCityIsNull() {
            addCriterion("product_ship_city is null");
            return (Criteria) this;
        }

        public Criteria andProductShipCityIsNotNull() {
            addCriterion("product_ship_city is not null");
            return (Criteria) this;
        }

        public Criteria andProductShipCityEqualTo(String value) {
            addCriterion("product_ship_city =", value, "productShipCity");
            return (Criteria) this;
        }

        public Criteria andProductShipCityEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_city = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipCityNotEqualTo(String value) {
            addCriterion("product_ship_city <>", value, "productShipCity");
            return (Criteria) this;
        }

        public Criteria andProductShipCityNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_city <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipCityGreaterThan(String value) {
            addCriterion("product_ship_city >", value, "productShipCity");
            return (Criteria) this;
        }

        public Criteria andProductShipCityGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_city > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipCityGreaterThanOrEqualTo(String value) {
            addCriterion("product_ship_city >=", value, "productShipCity");
            return (Criteria) this;
        }

        public Criteria andProductShipCityGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_city >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipCityLessThan(String value) {
            addCriterion("product_ship_city <", value, "productShipCity");
            return (Criteria) this;
        }

        public Criteria andProductShipCityLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_city < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipCityLessThanOrEqualTo(String value) {
            addCriterion("product_ship_city <=", value, "productShipCity");
            return (Criteria) this;
        }

        public Criteria andProductShipCityLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_city <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipCityLike(String value) {
            addCriterion("product_ship_city like", value, "productShipCity");
            return (Criteria) this;
        }

        public Criteria andProductShipCityNotLike(String value) {
            addCriterion("product_ship_city not like", value, "productShipCity");
            return (Criteria) this;
        }

        public Criteria andProductShipCityIn(List<String> values) {
            addCriterion("product_ship_city in", values, "productShipCity");
            return (Criteria) this;
        }

        public Criteria andProductShipCityNotIn(List<String> values) {
            addCriterion("product_ship_city not in", values, "productShipCity");
            return (Criteria) this;
        }

        public Criteria andProductShipCityBetween(String value1, String value2) {
            addCriterion("product_ship_city between", value1, value2, "productShipCity");
            return (Criteria) this;
        }

        public Criteria andProductShipCityNotBetween(String value1, String value2) {
            addCriterion("product_ship_city not between", value1, value2, "productShipCity");
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameIsNull() {
            addCriterion("product_ship_city_name is null");
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameIsNotNull() {
            addCriterion("product_ship_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameEqualTo(String value) {
            addCriterion("product_ship_city_name =", value, "productShipCityName");
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_city_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameNotEqualTo(String value) {
            addCriterion("product_ship_city_name <>", value, "productShipCityName");
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_city_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameGreaterThan(String value) {
            addCriterion("product_ship_city_name >", value, "productShipCityName");
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_city_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("product_ship_city_name >=", value, "productShipCityName");
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_city_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameLessThan(String value) {
            addCriterion("product_ship_city_name <", value, "productShipCityName");
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_city_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameLessThanOrEqualTo(String value) {
            addCriterion("product_ship_city_name <=", value, "productShipCityName");
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_city_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameLike(String value) {
            addCriterion("product_ship_city_name like", value, "productShipCityName");
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameNotLike(String value) {
            addCriterion("product_ship_city_name not like", value, "productShipCityName");
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameIn(List<String> values) {
            addCriterion("product_ship_city_name in", values, "productShipCityName");
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameNotIn(List<String> values) {
            addCriterion("product_ship_city_name not in", values, "productShipCityName");
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameBetween(String value1, String value2) {
            addCriterion("product_ship_city_name between", value1, value2, "productShipCityName");
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameNotBetween(String value1, String value2) {
            addCriterion("product_ship_city_name not between", value1, value2, "productShipCityName");
            return (Criteria) this;
        }

        public Criteria andProductShipAddressIsNull() {
            addCriterion("product_ship_address is null");
            return (Criteria) this;
        }

        public Criteria andProductShipAddressIsNotNull() {
            addCriterion("product_ship_address is not null");
            return (Criteria) this;
        }

        public Criteria andProductShipAddressEqualTo(String value) {
            addCriterion("product_ship_address =", value, "productShipAddress");
            return (Criteria) this;
        }

        public Criteria andProductShipAddressEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_address = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipAddressNotEqualTo(String value) {
            addCriterion("product_ship_address <>", value, "productShipAddress");
            return (Criteria) this;
        }

        public Criteria andProductShipAddressNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_address <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipAddressGreaterThan(String value) {
            addCriterion("product_ship_address >", value, "productShipAddress");
            return (Criteria) this;
        }

        public Criteria andProductShipAddressGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_address > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipAddressGreaterThanOrEqualTo(String value) {
            addCriterion("product_ship_address >=", value, "productShipAddress");
            return (Criteria) this;
        }

        public Criteria andProductShipAddressGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_address >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipAddressLessThan(String value) {
            addCriterion("product_ship_address <", value, "productShipAddress");
            return (Criteria) this;
        }

        public Criteria andProductShipAddressLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_address < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipAddressLessThanOrEqualTo(String value) {
            addCriterion("product_ship_address <=", value, "productShipAddress");
            return (Criteria) this;
        }

        public Criteria andProductShipAddressLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_address <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipAddressLike(String value) {
            addCriterion("product_ship_address like", value, "productShipAddress");
            return (Criteria) this;
        }

        public Criteria andProductShipAddressNotLike(String value) {
            addCriterion("product_ship_address not like", value, "productShipAddress");
            return (Criteria) this;
        }

        public Criteria andProductShipAddressIn(List<String> values) {
            addCriterion("product_ship_address in", values, "productShipAddress");
            return (Criteria) this;
        }

        public Criteria andProductShipAddressNotIn(List<String> values) {
            addCriterion("product_ship_address not in", values, "productShipAddress");
            return (Criteria) this;
        }

        public Criteria andProductShipAddressBetween(String value1, String value2) {
            addCriterion("product_ship_address between", value1, value2, "productShipAddress");
            return (Criteria) this;
        }

        public Criteria andProductShipAddressNotBetween(String value1, String value2) {
            addCriterion("product_ship_address not between", value1, value2, "productShipAddress");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameIsNull() {
            addCriterion("hardware_expressage_simple_name is null");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameIsNotNull() {
            addCriterion("hardware_expressage_simple_name is not null");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameEqualTo(String value) {
            addCriterion("hardware_expressage_simple_name =", value, "hardwareExpressageSimpleName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("hardware_expressage_simple_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameNotEqualTo(String value) {
            addCriterion("hardware_expressage_simple_name <>", value, "hardwareExpressageSimpleName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("hardware_expressage_simple_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameGreaterThan(String value) {
            addCriterion("hardware_expressage_simple_name >", value, "hardwareExpressageSimpleName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("hardware_expressage_simple_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameGreaterThanOrEqualTo(String value) {
            addCriterion("hardware_expressage_simple_name >=", value, "hardwareExpressageSimpleName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("hardware_expressage_simple_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameLessThan(String value) {
            addCriterion("hardware_expressage_simple_name <", value, "hardwareExpressageSimpleName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("hardware_expressage_simple_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameLessThanOrEqualTo(String value) {
            addCriterion("hardware_expressage_simple_name <=", value, "hardwareExpressageSimpleName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("hardware_expressage_simple_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameLike(String value) {
            addCriterion("hardware_expressage_simple_name like", value, "hardwareExpressageSimpleName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameNotLike(String value) {
            addCriterion("hardware_expressage_simple_name not like", value, "hardwareExpressageSimpleName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameIn(List<String> values) {
            addCriterion("hardware_expressage_simple_name in", values, "hardwareExpressageSimpleName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameNotIn(List<String> values) {
            addCriterion("hardware_expressage_simple_name not in", values, "hardwareExpressageSimpleName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameBetween(String value1, String value2) {
            addCriterion("hardware_expressage_simple_name between", value1, value2, "hardwareExpressageSimpleName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameNotBetween(String value1, String value2) {
            addCriterion("hardware_expressage_simple_name not between", value1, value2, "hardwareExpressageSimpleName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameIsNull() {
            addCriterion("hardware_expressage_name is null");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameIsNotNull() {
            addCriterion("hardware_expressage_name is not null");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameEqualTo(String value) {
            addCriterion("hardware_expressage_name =", value, "hardwareExpressageName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("hardware_expressage_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameNotEqualTo(String value) {
            addCriterion("hardware_expressage_name <>", value, "hardwareExpressageName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("hardware_expressage_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameGreaterThan(String value) {
            addCriterion("hardware_expressage_name >", value, "hardwareExpressageName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("hardware_expressage_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameGreaterThanOrEqualTo(String value) {
            addCriterion("hardware_expressage_name >=", value, "hardwareExpressageName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("hardware_expressage_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameLessThan(String value) {
            addCriterion("hardware_expressage_name <", value, "hardwareExpressageName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("hardware_expressage_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameLessThanOrEqualTo(String value) {
            addCriterion("hardware_expressage_name <=", value, "hardwareExpressageName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("hardware_expressage_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameLike(String value) {
            addCriterion("hardware_expressage_name like", value, "hardwareExpressageName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameNotLike(String value) {
            addCriterion("hardware_expressage_name not like", value, "hardwareExpressageName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameIn(List<String> values) {
            addCriterion("hardware_expressage_name in", values, "hardwareExpressageName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameNotIn(List<String> values) {
            addCriterion("hardware_expressage_name not in", values, "hardwareExpressageName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameBetween(String value1, String value2) {
            addCriterion("hardware_expressage_name between", value1, value2, "hardwareExpressageName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameNotBetween(String value1, String value2) {
            addCriterion("hardware_expressage_name not between", value1, value2, "hardwareExpressageName");
            return (Criteria) this;
        }

        public Criteria andProductShipTimeIsNull() {
            addCriterion("product_ship_time is null");
            return (Criteria) this;
        }

        public Criteria andProductShipTimeIsNotNull() {
            addCriterion("product_ship_time is not null");
            return (Criteria) this;
        }

        public Criteria andProductShipTimeEqualTo(String value) {
            addCriterion("product_ship_time =", value, "productShipTime");
            return (Criteria) this;
        }

        public Criteria andProductShipTimeEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipTimeNotEqualTo(String value) {
            addCriterion("product_ship_time <>", value, "productShipTime");
            return (Criteria) this;
        }

        public Criteria andProductShipTimeNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipTimeGreaterThan(String value) {
            addCriterion("product_ship_time >", value, "productShipTime");
            return (Criteria) this;
        }

        public Criteria andProductShipTimeGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipTimeGreaterThanOrEqualTo(String value) {
            addCriterion("product_ship_time >=", value, "productShipTime");
            return (Criteria) this;
        }

        public Criteria andProductShipTimeGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipTimeLessThan(String value) {
            addCriterion("product_ship_time <", value, "productShipTime");
            return (Criteria) this;
        }

        public Criteria andProductShipTimeLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipTimeLessThanOrEqualTo(String value) {
            addCriterion("product_ship_time <=", value, "productShipTime");
            return (Criteria) this;
        }

        public Criteria andProductShipTimeLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_ship_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductShipTimeLike(String value) {
            addCriterion("product_ship_time like", value, "productShipTime");
            return (Criteria) this;
        }

        public Criteria andProductShipTimeNotLike(String value) {
            addCriterion("product_ship_time not like", value, "productShipTime");
            return (Criteria) this;
        }

        public Criteria andProductShipTimeIn(List<String> values) {
            addCriterion("product_ship_time in", values, "productShipTime");
            return (Criteria) this;
        }

        public Criteria andProductShipTimeNotIn(List<String> values) {
            addCriterion("product_ship_time not in", values, "productShipTime");
            return (Criteria) this;
        }

        public Criteria andProductShipTimeBetween(String value1, String value2) {
            addCriterion("product_ship_time between", value1, value2, "productShipTime");
            return (Criteria) this;
        }

        public Criteria andProductShipTimeNotBetween(String value1, String value2) {
            addCriterion("product_ship_time not between", value1, value2, "productShipTime");
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionIsNull() {
            addCriterion("product_working_condition is null");
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionIsNotNull() {
            addCriterion("product_working_condition is not null");
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionEqualTo(String value) {
            addCriterion("product_working_condition =", value, "productWorkingCondition");
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_working_condition = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionNotEqualTo(String value) {
            addCriterion("product_working_condition <>", value, "productWorkingCondition");
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_working_condition <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionGreaterThan(String value) {
            addCriterion("product_working_condition >", value, "productWorkingCondition");
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_working_condition > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionGreaterThanOrEqualTo(String value) {
            addCriterion("product_working_condition >=", value, "productWorkingCondition");
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_working_condition >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionLessThan(String value) {
            addCriterion("product_working_condition <", value, "productWorkingCondition");
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_working_condition < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionLessThanOrEqualTo(String value) {
            addCriterion("product_working_condition <=", value, "productWorkingCondition");
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("product_working_condition <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionLike(String value) {
            addCriterion("product_working_condition like", value, "productWorkingCondition");
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionNotLike(String value) {
            addCriterion("product_working_condition not like", value, "productWorkingCondition");
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionIn(List<String> values) {
            addCriterion("product_working_condition in", values, "productWorkingCondition");
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionNotIn(List<String> values) {
            addCriterion("product_working_condition not in", values, "productWorkingCondition");
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionBetween(String value1, String value2) {
            addCriterion("product_working_condition between", value1, value2, "productWorkingCondition");
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionNotBetween(String value1, String value2) {
            addCriterion("product_working_condition not between", value1, value2, "productWorkingCondition");
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoIsNull() {
            addCriterion("software_info is null");
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoIsNotNull() {
            addCriterion("software_info is not null");
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoEqualTo(String value) {
            addCriterion("software_info =", value, "softwareInfo");
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("software_info = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoNotEqualTo(String value) {
            addCriterion("software_info <>", value, "softwareInfo");
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("software_info <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoGreaterThan(String value) {
            addCriterion("software_info >", value, "softwareInfo");
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("software_info > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoGreaterThanOrEqualTo(String value) {
            addCriterion("software_info >=", value, "softwareInfo");
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("software_info >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoLessThan(String value) {
            addCriterion("software_info <", value, "softwareInfo");
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("software_info < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoLessThanOrEqualTo(String value) {
            addCriterion("software_info <=", value, "softwareInfo");
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("software_info <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoLike(String value) {
            addCriterion("software_info like", value, "softwareInfo");
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoNotLike(String value) {
            addCriterion("software_info not like", value, "softwareInfo");
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoIn(List<String> values) {
            addCriterion("software_info in", values, "softwareInfo");
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoNotIn(List<String> values) {
            addCriterion("software_info not in", values, "softwareInfo");
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoBetween(String value1, String value2) {
            addCriterion("software_info between", value1, value2, "softwareInfo");
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoNotBetween(String value1, String value2) {
            addCriterion("software_info not between", value1, value2, "softwareInfo");
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayIsNull() {
            addCriterion("software_get_way is null");
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayIsNotNull() {
            addCriterion("software_get_way is not null");
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayEqualTo(String value) {
            addCriterion("software_get_way =", value, "softwareGetWay");
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("software_get_way = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayNotEqualTo(String value) {
            addCriterion("software_get_way <>", value, "softwareGetWay");
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("software_get_way <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayGreaterThan(String value) {
            addCriterion("software_get_way >", value, "softwareGetWay");
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("software_get_way > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayGreaterThanOrEqualTo(String value) {
            addCriterion("software_get_way >=", value, "softwareGetWay");
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("software_get_way >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayLessThan(String value) {
            addCriterion("software_get_way <", value, "softwareGetWay");
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("software_get_way < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayLessThanOrEqualTo(String value) {
            addCriterion("software_get_way <=", value, "softwareGetWay");
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("software_get_way <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayLike(String value) {
            addCriterion("software_get_way like", value, "softwareGetWay");
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayNotLike(String value) {
            addCriterion("software_get_way not like", value, "softwareGetWay");
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayIn(List<String> values) {
            addCriterion("software_get_way in", values, "softwareGetWay");
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayNotIn(List<String> values) {
            addCriterion("software_get_way not in", values, "softwareGetWay");
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayBetween(String value1, String value2) {
            addCriterion("software_get_way between", value1, value2, "softwareGetWay");
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayNotBetween(String value1, String value2) {
            addCriterion("software_get_way not between", value1, value2, "softwareGetWay");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoIsNull() {
            addCriterion("app_mini_program_info is null");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoIsNotNull() {
            addCriterion("app_mini_program_info is not null");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoEqualTo(String value) {
            addCriterion("app_mini_program_info =", value, "appMiniProgramInfo");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("app_mini_program_info = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoNotEqualTo(String value) {
            addCriterion("app_mini_program_info <>", value, "appMiniProgramInfo");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("app_mini_program_info <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoGreaterThan(String value) {
            addCriterion("app_mini_program_info >", value, "appMiniProgramInfo");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("app_mini_program_info > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoGreaterThanOrEqualTo(String value) {
            addCriterion("app_mini_program_info >=", value, "appMiniProgramInfo");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("app_mini_program_info >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoLessThan(String value) {
            addCriterion("app_mini_program_info <", value, "appMiniProgramInfo");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("app_mini_program_info < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoLessThanOrEqualTo(String value) {
            addCriterion("app_mini_program_info <=", value, "appMiniProgramInfo");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("app_mini_program_info <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoLike(String value) {
            addCriterion("app_mini_program_info like", value, "appMiniProgramInfo");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoNotLike(String value) {
            addCriterion("app_mini_program_info not like", value, "appMiniProgramInfo");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoIn(List<String> values) {
            addCriterion("app_mini_program_info in", values, "appMiniProgramInfo");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoNotIn(List<String> values) {
            addCriterion("app_mini_program_info not in", values, "appMiniProgramInfo");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoBetween(String value1, String value2) {
            addCriterion("app_mini_program_info between", value1, value2, "appMiniProgramInfo");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoNotBetween(String value1, String value2) {
            addCriterion("app_mini_program_info not between", value1, value2, "appMiniProgramInfo");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayIsNull() {
            addCriterion("app_mini_program_get_way is null");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayIsNotNull() {
            addCriterion("app_mini_program_get_way is not null");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayEqualTo(String value) {
            addCriterion("app_mini_program_get_way =", value, "appMiniProgramGetWay");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("app_mini_program_get_way = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayNotEqualTo(String value) {
            addCriterion("app_mini_program_get_way <>", value, "appMiniProgramGetWay");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("app_mini_program_get_way <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayGreaterThan(String value) {
            addCriterion("app_mini_program_get_way >", value, "appMiniProgramGetWay");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("app_mini_program_get_way > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayGreaterThanOrEqualTo(String value) {
            addCriterion("app_mini_program_get_way >=", value, "appMiniProgramGetWay");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("app_mini_program_get_way >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayLessThan(String value) {
            addCriterion("app_mini_program_get_way <", value, "appMiniProgramGetWay");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("app_mini_program_get_way < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayLessThanOrEqualTo(String value) {
            addCriterion("app_mini_program_get_way <=", value, "appMiniProgramGetWay");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("app_mini_program_get_way <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayLike(String value) {
            addCriterion("app_mini_program_get_way like", value, "appMiniProgramGetWay");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayNotLike(String value) {
            addCriterion("app_mini_program_get_way not like", value, "appMiniProgramGetWay");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayIn(List<String> values) {
            addCriterion("app_mini_program_get_way in", values, "appMiniProgramGetWay");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayNotIn(List<String> values) {
            addCriterion("app_mini_program_get_way not in", values, "appMiniProgramGetWay");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayBetween(String value1, String value2) {
            addCriterion("app_mini_program_get_way between", value1, value2, "appMiniProgramGetWay");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayNotBetween(String value1, String value2) {
            addCriterion("app_mini_program_get_way not between", value1, value2, "appMiniProgramGetWay");
            return (Criteria) this;
        }

        public Criteria andInstallationServicesIsNull() {
            addCriterion("installation_services is null");
            return (Criteria) this;
        }

        public Criteria andInstallationServicesIsNotNull() {
            addCriterion("installation_services is not null");
            return (Criteria) this;
        }

        public Criteria andInstallationServicesEqualTo(String value) {
            addCriterion("installation_services =", value, "installationServices");
            return (Criteria) this;
        }

        public Criteria andInstallationServicesEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("installation_services = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallationServicesNotEqualTo(String value) {
            addCriterion("installation_services <>", value, "installationServices");
            return (Criteria) this;
        }

        public Criteria andInstallationServicesNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("installation_services <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallationServicesGreaterThan(String value) {
            addCriterion("installation_services >", value, "installationServices");
            return (Criteria) this;
        }

        public Criteria andInstallationServicesGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("installation_services > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallationServicesGreaterThanOrEqualTo(String value) {
            addCriterion("installation_services >=", value, "installationServices");
            return (Criteria) this;
        }

        public Criteria andInstallationServicesGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("installation_services >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallationServicesLessThan(String value) {
            addCriterion("installation_services <", value, "installationServices");
            return (Criteria) this;
        }

        public Criteria andInstallationServicesLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("installation_services < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallationServicesLessThanOrEqualTo(String value) {
            addCriterion("installation_services <=", value, "installationServices");
            return (Criteria) this;
        }

        public Criteria andInstallationServicesLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("installation_services <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallationServicesLike(String value) {
            addCriterion("installation_services like", value, "installationServices");
            return (Criteria) this;
        }

        public Criteria andInstallationServicesNotLike(String value) {
            addCriterion("installation_services not like", value, "installationServices");
            return (Criteria) this;
        }

        public Criteria andInstallationServicesIn(List<String> values) {
            addCriterion("installation_services in", values, "installationServices");
            return (Criteria) this;
        }

        public Criteria andInstallationServicesNotIn(List<String> values) {
            addCriterion("installation_services not in", values, "installationServices");
            return (Criteria) this;
        }

        public Criteria andInstallationServicesBetween(String value1, String value2) {
            addCriterion("installation_services between", value1, value2, "installationServices");
            return (Criteria) this;
        }

        public Criteria andInstallationServicesNotBetween(String value1, String value2) {
            addCriterion("installation_services not between", value1, value2, "installationServices");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonIsNull() {
            addCriterion("offline_reason is null");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonIsNotNull() {
            addCriterion("offline_reason is not null");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonEqualTo(String value) {
            addCriterion("offline_reason =", value, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("offline_reason = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfflineReasonNotEqualTo(String value) {
            addCriterion("offline_reason <>", value, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("offline_reason <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfflineReasonGreaterThan(String value) {
            addCriterion("offline_reason >", value, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("offline_reason > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfflineReasonGreaterThanOrEqualTo(String value) {
            addCriterion("offline_reason >=", value, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("offline_reason >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfflineReasonLessThan(String value) {
            addCriterion("offline_reason <", value, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("offline_reason < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfflineReasonLessThanOrEqualTo(String value) {
            addCriterion("offline_reason <=", value, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("offline_reason <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfflineReasonLike(String value) {
            addCriterion("offline_reason like", value, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonNotLike(String value) {
            addCriterion("offline_reason not like", value, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonIn(List<String> values) {
            addCriterion("offline_reason in", values, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonNotIn(List<String> values) {
            addCriterion("offline_reason not in", values, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonBetween(String value1, String value2) {
            addCriterion("offline_reason between", value1, value2, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonNotBetween(String value1, String value2) {
            addCriterion("offline_reason not between", value1, value2, "offlineReason");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("creator = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("creator <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("creator > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("creator >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("creator < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("creator <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdIsNull() {
            addCriterion("request_offline_user_id is null");
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdIsNotNull() {
            addCriterion("request_offline_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdEqualTo(String value) {
            addCriterion("request_offline_user_id =", value, "requestOfflineUserId");
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("request_offline_user_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdNotEqualTo(String value) {
            addCriterion("request_offline_user_id <>", value, "requestOfflineUserId");
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("request_offline_user_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdGreaterThan(String value) {
            addCriterion("request_offline_user_id >", value, "requestOfflineUserId");
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("request_offline_user_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("request_offline_user_id >=", value, "requestOfflineUserId");
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("request_offline_user_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdLessThan(String value) {
            addCriterion("request_offline_user_id <", value, "requestOfflineUserId");
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("request_offline_user_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdLessThanOrEqualTo(String value) {
            addCriterion("request_offline_user_id <=", value, "requestOfflineUserId");
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("request_offline_user_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdLike(String value) {
            addCriterion("request_offline_user_id like", value, "requestOfflineUserId");
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdNotLike(String value) {
            addCriterion("request_offline_user_id not like", value, "requestOfflineUserId");
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdIn(List<String> values) {
            addCriterion("request_offline_user_id in", values, "requestOfflineUserId");
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdNotIn(List<String> values) {
            addCriterion("request_offline_user_id not in", values, "requestOfflineUserId");
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdBetween(String value1, String value2) {
            addCriterion("request_offline_user_id between", value1, value2, "requestOfflineUserId");
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdNotBetween(String value1, String value2) {
            addCriterion("request_offline_user_id not between", value1, value2, "requestOfflineUserId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(NewProductRequestOnlineOfflineComboInfo.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andNewProductRequestIdLikeInsensitive(String value) {
            addCriterion("upper(new_product_request_id) like", value.toUpperCase(), "newProductRequestId");
            return (Criteria) this;
        }

        public Criteria andRequestNoLikeInsensitive(String value) {
            addCriterion("upper(request_no) like", value.toUpperCase(), "requestNo");
            return (Criteria) this;
        }

        public Criteria andThingsCardComboLikeInsensitive(String value) {
            addCriterion("upper(things_card_combo) like", value.toUpperCase(), "thingsCardCombo");
            return (Criteria) this;
        }

        public Criteria andHardwareShippingListLikeInsensitive(String value) {
            addCriterion("upper(hardware_shipping_list) like", value.toUpperCase(), "hardwareShippingList");
            return (Criteria) this;
        }

        public Criteria andProductParamLikeInsensitive(String value) {
            addCriterion("upper(product_param) like", value.toUpperCase(), "productParam");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceLikeInsensitive(String value) {
            addCriterion("upper(product_ship_province) like", value.toUpperCase(), "productShipProvince");
            return (Criteria) this;
        }

        public Criteria andProductShipProvinceNameLikeInsensitive(String value) {
            addCriterion("upper(product_ship_province_name) like", value.toUpperCase(), "productShipProvinceName");
            return (Criteria) this;
        }

        public Criteria andProductShipCityLikeInsensitive(String value) {
            addCriterion("upper(product_ship_city) like", value.toUpperCase(), "productShipCity");
            return (Criteria) this;
        }

        public Criteria andProductShipCityNameLikeInsensitive(String value) {
            addCriterion("upper(product_ship_city_name) like", value.toUpperCase(), "productShipCityName");
            return (Criteria) this;
        }

        public Criteria andProductShipAddressLikeInsensitive(String value) {
            addCriterion("upper(product_ship_address) like", value.toUpperCase(), "productShipAddress");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageSimpleNameLikeInsensitive(String value) {
            addCriterion("upper(hardware_expressage_simple_name) like", value.toUpperCase(), "hardwareExpressageSimpleName");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressageNameLikeInsensitive(String value) {
            addCriterion("upper(hardware_expressage_name) like", value.toUpperCase(), "hardwareExpressageName");
            return (Criteria) this;
        }

        public Criteria andProductShipTimeLikeInsensitive(String value) {
            addCriterion("upper(product_ship_time) like", value.toUpperCase(), "productShipTime");
            return (Criteria) this;
        }

        public Criteria andProductWorkingConditionLikeInsensitive(String value) {
            addCriterion("upper(product_working_condition) like", value.toUpperCase(), "productWorkingCondition");
            return (Criteria) this;
        }

        public Criteria andSoftwareInfoLikeInsensitive(String value) {
            addCriterion("upper(software_info) like", value.toUpperCase(), "softwareInfo");
            return (Criteria) this;
        }

        public Criteria andSoftwareGetWayLikeInsensitive(String value) {
            addCriterion("upper(software_get_way) like", value.toUpperCase(), "softwareGetWay");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramInfoLikeInsensitive(String value) {
            addCriterion("upper(app_mini_program_info) like", value.toUpperCase(), "appMiniProgramInfo");
            return (Criteria) this;
        }

        public Criteria andAppMiniProgramGetWayLikeInsensitive(String value) {
            addCriterion("upper(app_mini_program_get_way) like", value.toUpperCase(), "appMiniProgramGetWay");
            return (Criteria) this;
        }

        public Criteria andInstallationServicesLikeInsensitive(String value) {
            addCriterion("upper(installation_services) like", value.toUpperCase(), "installationServices");
            return (Criteria) this;
        }

        public Criteria andOfflineReasonLikeInsensitive(String value) {
            addCriterion("upper(offline_reason) like", value.toUpperCase(), "offlineReason");
            return (Criteria) this;
        }

        public Criteria andCreatorLikeInsensitive(String value) {
            addCriterion("upper(creator) like", value.toUpperCase(), "creator");
            return (Criteria) this;
        }

        public Criteria andRequestOfflineUserIdLikeInsensitive(String value) {
            addCriterion("upper(request_offline_user_id) like", value.toUpperCase(), "requestOfflineUserId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        private NewProductRequestOnlineOfflineComboInfoExample example;

        protected Criteria(NewProductRequestOnlineOfflineComboInfoExample example) {
            super();
            this.example = example;
        }

        public NewProductRequestOnlineOfflineComboInfoExample example() {
            return this.example;
        }

        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            Criteria add(Criteria add);
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        void example(com.chinamobile.iot.sc.pojo.NewProductRequestOnlineOfflineComboInfoExample example);
    }
}