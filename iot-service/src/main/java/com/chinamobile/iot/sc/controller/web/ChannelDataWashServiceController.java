package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.Order2cAgentInfo;
import com.chinamobile.iot.sc.pojo.param.OrderChannelDataWashParam;
import com.chinamobile.iot.sc.pojo.param.UpdateOrderChannelParam;
import com.chinamobile.iot.sc.service.ChannelDataWashService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON>
 * @date : 2025/4/29 14:48
 * @description: 渠道商数据清洗控制类、
 **/
@RestController
@Slf4j
@RequestMapping(value = "/osweb/channel")
public class ChannelDataWashServiceController {

    @Resource
    private ChannelDataWashService channelDataWashService;

    /**
     * 批量导入订单渠道商信息清洗
     * @param file
     */
    @PostMapping("/wash/import")
    public void importChannelDataWashMessage(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,@RequestParam("file") MultipartFile file){
        channelDataWashService.importChannelDataWash(loginIfo4Redis,file);
    }

    /**
     * 导出订单渠道商信息
     * @param param
     */
    @GetMapping ("/wash/export")
    public void getExportOrderChannelDataWash(OrderChannelDataWashParam param){
        channelDataWashService.exportOrderChannelDataWash(param);
    }


    /**
     * 分页查询订单渠道商信息
     * @param param
     */
    @GetMapping ("/wash/page/list")
    public BaseAnswer<PageData<Order2cAgentInfo>> getOrderChannelDataWashListMessage(OrderChannelDataWashParam param){
       return channelDataWashService.getOrderChannelDataWashList(param);
    }

    /**
     * 单个数据渠道商信息清洗
     * @param loginIfo4Redis
     * @param param
     * @return
     */
    @PostMapping ("/wash/update")
    public BaseAnswer<Void> orderAccurateAgentWashUpdate(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,@RequestBody @Valid UpdateOrderChannelParam param){
         channelDataWashService.orderAccurateAgentWash(loginIfo4Redis,param);
         return new BaseAnswer<>();
    }
}
