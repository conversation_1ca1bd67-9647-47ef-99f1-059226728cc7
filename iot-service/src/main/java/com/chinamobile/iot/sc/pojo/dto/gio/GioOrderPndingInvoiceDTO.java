package com.chinamobile.iot.sc.pojo.dto.gio;

import lombok.Data;

@Data
public class GioOrderPndingInvoiceDTO {

    private String event;
    private String userId;
    private Long timestamp;
    private GioOrderPndingInvoiceAttr attrs;

    @Data
    public static class GioOrderPndingInvoiceAttr {
        private String platformName_var;
        private String orderId_var;
        private String orderType_var;
        private String payAmount_var;
        private String amountDue_var;
        private String ordersource_var;
        private String orderTime_var;
        private String couponInfo_var;
        private String cusCode_var;
        private String cusName_var;
        private String cusProvinceCode_var;
        private String cusCityCode_var;
        private String cusAreaCode_var;
        private String cusProvince_var;
        private String cusCity_var;
        private String cusArea_var;
        private String channelPartnerPhone_var;
        private String customerManagerPhone_var;
        private String customerManagerCode_var;
        // 新增字段
        private String orderProvince_var;
        private String orderCity_var;
        private String OrderArea_var;
        private String charge_code_var;
        private String charge_code_name_var;
        private String busiPersonJobNumber_var;
        private String busiPersonPhoneNum_var;
        private String channelPartnerCode_var;
        private String deductAmount;
        private String prductType;
        private String customerType_var;

    }

}
