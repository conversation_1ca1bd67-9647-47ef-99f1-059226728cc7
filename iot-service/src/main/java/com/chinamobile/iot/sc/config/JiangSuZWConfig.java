package com.chinamobile.iot.sc.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * created by l<PERSON><PERSON><PERSON> on 2024/1/11 11:24
 */
@Component
@Data
public class JiangSuZWConfig {

    @Value("#{'${jszw.skuOfferingCodes}'.replaceAll(' ','').split(',')}")
    private List<String> jszwOfferingCodes;

    @Value("${jszw.openUrl}")
    private String jszwOpenUrl;

    @Value("${jszw.cancelUrl}")
    private String jszwCancelUrl;

}
