package com.chinamobile.iot.sc.pojo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StandardServiceExample {
    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    public StandardServiceExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    public StandardServiceExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    public StandardServiceExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    public static Criteria newAndCreateCriteria() {
        StandardServiceExample example = new StandardServiceExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    public StandardServiceExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    public StandardServiceExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdIsNull() {
            addCriterion("product_department_id is null");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdIsNotNull() {
            addCriterion("product_department_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdEqualTo(Integer value) {
            addCriterion("product_department_id =", value, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("product_department_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdNotEqualTo(Integer value) {
            addCriterion("product_department_id <>", value, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdNotEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("product_department_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdGreaterThan(Integer value) {
            addCriterion("product_department_id >", value, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdGreaterThanColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("product_department_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("product_department_id >=", value, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdGreaterThanOrEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("product_department_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdLessThan(Integer value) {
            addCriterion("product_department_id <", value, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdLessThanColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("product_department_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdLessThanOrEqualTo(Integer value) {
            addCriterion("product_department_id <=", value, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdLessThanOrEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("product_department_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdIn(List<Integer> values) {
            addCriterion("product_department_id in", values, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdNotIn(List<Integer> values) {
            addCriterion("product_department_id not in", values, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdBetween(Integer value1, Integer value2) {
            addCriterion("product_department_id between", value1, value2, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("product_department_id not between", value1, value2, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andRealProductNameIsNull() {
            addCriterion("real_product_name is null");
            return (Criteria) this;
        }

        public Criteria andRealProductNameIsNotNull() {
            addCriterion("real_product_name is not null");
            return (Criteria) this;
        }

        public Criteria andRealProductNameEqualTo(String value) {
            addCriterion("real_product_name =", value, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("real_product_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRealProductNameNotEqualTo(String value) {
            addCriterion("real_product_name <>", value, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameNotEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("real_product_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRealProductNameGreaterThan(String value) {
            addCriterion("real_product_name >", value, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameGreaterThanColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("real_product_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRealProductNameGreaterThanOrEqualTo(String value) {
            addCriterion("real_product_name >=", value, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameGreaterThanOrEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("real_product_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRealProductNameLessThan(String value) {
            addCriterion("real_product_name <", value, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameLessThanColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("real_product_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRealProductNameLessThanOrEqualTo(String value) {
            addCriterion("real_product_name <=", value, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameLessThanOrEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("real_product_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRealProductNameLike(String value) {
            addCriterion("real_product_name like", value, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameNotLike(String value) {
            addCriterion("real_product_name not like", value, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameIn(List<String> values) {
            addCriterion("real_product_name in", values, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameNotIn(List<String> values) {
            addCriterion("real_product_name not in", values, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameBetween(String value1, String value2) {
            addCriterion("real_product_name between", value1, value2, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameNotBetween(String value1, String value2) {
            addCriterion("real_product_name not between", value1, value2, "realProductName");
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdIsNull() {
            addCriterion("product_property_id is null");
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdIsNotNull() {
            addCriterion("product_property_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdEqualTo(String value) {
            addCriterion("product_property_id =", value, "productPropertyId");
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("product_property_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdNotEqualTo(String value) {
            addCriterion("product_property_id <>", value, "productPropertyId");
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdNotEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("product_property_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdGreaterThan(String value) {
            addCriterion("product_property_id >", value, "productPropertyId");
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdGreaterThanColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("product_property_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdGreaterThanOrEqualTo(String value) {
            addCriterion("product_property_id >=", value, "productPropertyId");
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdGreaterThanOrEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("product_property_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdLessThan(String value) {
            addCriterion("product_property_id <", value, "productPropertyId");
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdLessThanColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("product_property_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdLessThanOrEqualTo(String value) {
            addCriterion("product_property_id <=", value, "productPropertyId");
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdLessThanOrEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("product_property_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdLike(String value) {
            addCriterion("product_property_id like", value, "productPropertyId");
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdNotLike(String value) {
            addCriterion("product_property_id not like", value, "productPropertyId");
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdIn(List<String> values) {
            addCriterion("product_property_id in", values, "productPropertyId");
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdNotIn(List<String> values) {
            addCriterion("product_property_id not in", values, "productPropertyId");
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdBetween(String value1, String value2) {
            addCriterion("product_property_id between", value1, value2, "productPropertyId");
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdNotBetween(String value1, String value2) {
            addCriterion("product_property_id not between", value1, value2, "productPropertyId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(StandardService.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andNameLikeInsensitive(String value) {
            addCriterion("upper(name) like", value.toUpperCase(), "name");
            return (Criteria) this;
        }

        public Criteria andRealProductNameLikeInsensitive(String value) {
            addCriterion("upper(real_product_name) like", value.toUpperCase(), "realProductName");
            return (Criteria) this;
        }

        public Criteria andProductPropertyIdLikeInsensitive(String value) {
            addCriterion("upper(product_property_id) like", value.toUpperCase(), "productPropertyId");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Fri Feb 03 15:26:22 CST 2023
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Fri Feb 03 15:26:22 CST 2023
         */
        private StandardServiceExample example;

        /**
         *
         * @mbg.generated Fri Feb 03 15:26:22 CST 2023
         */
        protected Criteria(StandardServiceExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Fri Feb 03 15:26:22 CST 2023
         */
        public StandardServiceExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Fri Feb 03 15:26:22 CST 2023
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Fri Feb 03 15:26:22 CST 2023
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Fri Feb 03 15:26:22 CST 2023
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Fri Feb 03 15:26:22 CST 2023
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Fri Feb 03 15:26:22 CST 2023
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Fri Feb 03 15:26:22 CST 2023
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Fri Feb 03 15:26:22 CST 2023
         */
        void example(com.chinamobile.iot.sc.pojo.StandardServiceExample example);
    }
}