package com.chinamobile.iot.sc.pojo.dto.gio;

import lombok.Data;

@Data
public class GioPrudctOrderPndingDTO {

    private String event;
    private String userId;
    private Long timestamp;
    private GioPrudctOrderPndingAttr attrs;

    @Data
    public static class  GioPrudctOrderPndingAttr {
        private String orderId_var;
        private String orderType_var;
        private String skuCode_var;
        private String spu_version_var;
        private String sku_version_var;
        private String offering_version_var;
        private Long quantity_var;
        private String offeringId_var;
        private String productPrice_var;
        private String offeringQuantity_var;
        private String offeringsettlePrice_var;
        private String offeringPrice_var;
        private String totalPrice_var;
        private String offeringtotalPrice_var;
        private String cusProvinceCode_var;
        private String cusCityCode_var;
        private String cusAreaCode_var;
        private String cusProvince_var;
        private String cusCity_var;
        private String cusArea_var;
        private String channelPartnerPhone_var;
        private String customerManagerPhone_var;
        private String customerManagerCode_var;
        // 新增字段
        private String orderProvince_var;
        private String orderCity_var;
        private String OrderArea_var;
        private String charge_code_var;
        private String charge_code_name_var;
        private String busiPersonJobNumber_var;
        private String busiPersonPhoneNum_var;
        private String deductAmount;
        private String channelPartnerCode_var;
        private String offeringCode_var;
        private String prductType;
        private String customerType_var;
        private String cusCode_var;
        private String cusName_var;
        private String offering_class_var;
        private String couponInfo_var;
    }

}
