package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.interceptor.AuthorityInterceptor;
import com.chinamobile.iot.sc.config.SupplierInfoMapConfig;
import com.chinamobile.iot.sc.config.ThreadExecutorConfig;
import com.chinamobile.iot.sc.dao.Order2cAtomInfoMapper;
import com.chinamobile.iot.sc.enums.OnlineSettleStatusEnum;
import com.chinamobile.iot.sc.enums.SettleStatusEnum;
import com.chinamobile.iot.sc.enums.log.*;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.AuthCode;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.UnionSellOrderExcelHistory;
import com.chinamobile.iot.sc.pojo.dto.BusinessCodeListDTO;
import com.chinamobile.iot.sc.pojo.exhandle.ExHandleInfo;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.GetOrderDetailVO;
import com.chinamobile.iot.sc.pojo.vo.OnlineSettleStatusVO;
import com.chinamobile.iot.sc.pojo.vo.SettleStatusVO;
import com.chinamobile.iot.sc.pojo.vo.SpecialAfterMarketResultVO;
import com.chinamobile.iot.sc.request.*;
import com.chinamobile.iot.sc.request.exhandle.Request4ExHandleAdd;
import com.chinamobile.iot.sc.request.order2c.OrderRemarkParam;
import com.chinamobile.iot.sc.request.order2c.OrderReminderParam;
import com.chinamobile.iot.sc.request.order2c.OrderSalesReportParam;
import com.chinamobile.iot.sc.response.SimpleItemDTO;
import com.chinamobile.iot.sc.response.web.*;
import com.chinamobile.iot.sc.response.web.logistics.LogisticsVO;
import com.chinamobile.iot.sc.service.*;
import com.chinamobile.iot.sc.task.AfterPayOrderStatusTask;
import com.chinamobile.iot.sc.util.IotLogUtil;
import com.chinamobile.iot.sc.util.SmsValidUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.chinamobile.iot.sc.common.BaseConstant.*;

/**
 * @Author: YSC
 * @Date: 2021/11/9 14:42
 * @Description:
 */
@RestController
@RequestMapping("/osweb")
@Slf4j
public class OrderWebController {
    @Autowired
    private IOrder2CService iOrder2CService;
    @Resource
    private IExHandleService exHandleService;
    @Resource
    private IGoodsService goodsService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private AuthorityInterceptor authorityInterceptor;

    @Resource
    private LogService logService;

    @Resource
    private SupplierInfoMapConfig supplierMap;

    @Resource
    private SpecialAfterMarketResultService specialAfterMarketResultService;

    @Autowired
    private AfterPayOrderStatusTask afterPayOrderStatusTask;

    @Resource
    private Order2IOTService order2IOTService;

    @Resource
    private Order2cAtomInfoMapper atomOrderInfoMapper;

    private ExecutorService executorService = new ThreadPoolExecutor(50, 50, 1L, TimeUnit.MINUTES,
            new LinkedBlockingDeque<>(10000));

    /**
     * 商品订单查询
     *
     * @param userId
     * @return
     */
    @GetMapping("/order/list")
    @Auth(authCode = {BaseConstant.IOT_ORDER_QUERY})
    public BaseAnswer<PageData<Order2CInfoDTO>> getOrderList(
            OrderListQueryParam param,
            @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId,
            @RequestParam(value = "settleStatus", required = false) Integer settleStatus,
            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return iOrder2CService.getOrderList(param, userId, loginIfo4Redis);
    }

    /**
     * 根据订单ID查看订单详情 包含物流信息
     *
     * @param order2CInfoDetailParam
     * @return
     */
    @Auth(authCode = {BaseConstant.IOT_ORDER_DETAIL})
    @GetMapping("/order")
    public BaseAnswer<Order2CInfoDetailDTO> getOrderDetail(@Valid Order2CInfoDetailParam order2CInfoDetailParam,
                                                           @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        log.info("根据订单ID查看订单详情 包含物流信息..........");
        return iOrder2CService.getOrderDetail(order2CInfoDetailParam,loginIfo4Redis);
    }

    /**
     * 获取首页订单的待办列表
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping("/order/backlog/list")
    public BaseAnswer<PageData<Order2CInfoToBacklogDTO>> getOrderListToBacklog(
            OrderListBacklogQueryParam param,
            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return iOrder2CService.getOrderListToBacklog(param, loginIfo4Redis);
    }



    /**
     * 同步物流信息
     *
     * @param request
     * @return
     */
    @Auth(authCode = BaseConstant.IOT_ORDER_HANDLE)
    @PostMapping("/order/logisticsSync")
    public BaseAnswer<Void> syncLogistics(@RequestBody @Valid LogisticsInfoRequest request,
                                          @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        BaseAnswer<Void> result = iOrder2CService.syncLogistics(request, userId,ip);

        //记录日志
        logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                IotLogUtil.orderSendLogContentFromRequest(request, supplierMap), LogResultEnum.LOG_SUCESS.code, null);
        return result;
    }

    /**
     * 同步物流信息
     *
     * @param request
     * @return
     */
    @PostMapping("/order/logisticsSyncInternal")
    public BaseAnswer<Void> logisticsSyncInternal(@RequestBody @Valid LogisticsInfoRequest request) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        BaseAnswer<Void> result = iOrder2CService.syncLogistics(request, "jkiot",ip);
        //记录日志
        logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                IotLogUtil.orderSendLogContentFromRequest(request, supplierMap),LogResultEnum.LOG_SUCESS.code, null);
        return result;
    }
    @PostMapping("/order/logRecord")
    public BaseAnswer<Void> logRecord(@RequestBody @Valid LogRecord request) {
        String content ="";
        Integer operationModule = 0;
        Integer operationSubModule = 0;
        switch (request.getCode()) {
            case "1-CQIOTMALLOS-10200":
                content = "【导出订单信息表】";
                operationModule= ModuleEnum.ORDER_MANAGE.code;
                operationSubModule= OrderManageOperateEnum.MALL_SYNC_ORDER.code;
                break;
            case "1-CQIOTMALLOS-10300":
                content = "【调整积分】";
                operationModule= ModuleEnum.RETAIL_MANAGE.code;
                operationSubModule= RetailManageOperateEnum.PARTNER_POINTS_MANAGE.code;
                break;
//            case "1-CQIOTMALLOS-10400":
//
//                break;
            case "1-CQI0TMALLOS-10500":
                content = "【数据导出】";
                operationModule= ModuleEnum.OPERATION_KANBAN.code;
                operationSubModule= OperationKanbanOperateEnum.MALL_USER_INFO.code;
                break;
            case "1-CQIOTMALLOS-10600":
                content = "【切换】";
                operationModule= ModuleEnum.SYSTEM_INSTALL.code;
                operationSubModule= SystemInstallEnum.INVENTORY_PATTERN.code;
                break;
            case "1-CQIOTMALLOS-10700":
                content = "【修改流程】";
                operationModule= ModuleEnum.SYSTEM_INSTALL.code;
                operationSubModule= SystemInstallEnum.FLOW_CENTER.code;
                break;
            default:
                break;
        }
        //记录日志
        logService.recordOperateLog(operationModule, operationSubModule,
                content,request.getResult(), request.getFailReason());
        return new BaseAnswer<>() ;
    }

    /**
     * 订单退款退货等审核
     *
     * @param request
     * @param userId
     * @return
     */
    @Auth(authCode = BaseConstant.REAFTER_CONSIGNMENT_HANDLE)
    @PostMapping("/order/refund/audit")
    public BaseAnswer<Void> refundAudit(@RequestBody @Valid AuditRefundRequest request,
                                        @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return iOrder2CService.refundAudit(request, userId,ip);
    }

    /**
     * 售后订单列表
     *
     * @param page
     * @param num
     * @param userId
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping("/order/roc/list")
    @Auth(authCode = BaseConstant.REAFTER_CONSIGNMENT_QUERY)
    public BaseAnswer<PageData<OrderRocInfoDTO>> getOrderRocList(@RequestParam(value = "rocId", required = false) String rocId,
                                                                 @RequestParam(value = "refundType", required = false) List<String> refundType,
                                                                 @RequestParam(value = "orderStatus", required = false) List<Integer> orderStatus,
                                                                 @RequestParam(value = "orderId", required = false) String orderId,
                                                                 @RequestParam(value = "orderType", required = false) String orderType,
                                                                 @RequestParam(value = "spuOfferingClass", required = false) List<String>  spuOfferingClass,
                                                                 @RequestParam(value = "spuOfferingName", required = false) String spuOfferingName,
                                                                 @RequestParam(value = "spuOfferingCode", required = false) String spuOfferingCode,
                                                                 @RequestParam(value = "skuOfferingName", required = false) String skuOfferingName,
                                                                 @RequestParam(value = "skuOfferingCode", required = false) String skuOfferingCode,
                                                                 @RequestParam(value = "atomOfferingCode", required = false) String atomOfferingCode,
                                                                 @RequestParam(value = "atomOfferingName", required = false) String atomOfferingName,
                                                                 @RequestParam(value = "partnerName", required = false) String partnerName,
                                                                 @RequestParam(value = "cooperatorName", required = false) String cooperatorName,
                                                                 @RequestParam(value = "orderText", required = false) String orderText,
                                                                 @RequestParam(value = "orderRocTabStatus", required = false) Integer orderRocTabStatus,
                                                                 @RequestParam(value = "spuOfferingClassList", required = false) List<String> spuOfferingClassList,
                                                                 @RequestParam("page") Integer page,
                                                                 @RequestParam("num") Integer num,
                                                                 @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId,
                                                                 @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return iOrder2CService.getOrderRocList(rocId, refundType, orderStatus, orderId,orderType, spuOfferingClass, spuOfferingName, spuOfferingCode, skuOfferingName, skuOfferingCode, atomOfferingCode, atomOfferingName, partnerName, cooperatorName,orderText,orderRocTabStatus,spuOfferingClassList, page, num, userId, loginIfo4Redis);
    }

    @GetMapping("/order/roc/backlog/list")
    @Auth(authCode = BaseConstant.REAFTER_CONSIGNMENT_QUERY)
    public BaseAnswer<PageData<OrderRocBacklogInfoDTO>> getOrderRocBacklogList(
                                                                 @RequestParam(value = "refundOrderId",required = false) String refundOrderId,
                                                                 @RequestParam("pageSize") Integer pageSize,
                                                                 @RequestParam("pageNum") Integer pageNum,
                                                                 @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return iOrder2CService.getOrderRocBacklogList(refundOrderId,pageSize, pageNum, loginIfo4Redis);
    }

    /**
     * 售后订单详情
     *
     * @param orderRocDetailParam
     * @return
     */
    @GetMapping("/order/roc")
    @Auth(authCode = BaseConstant.REAFTER_CONSIGNMENT_QUERY)
    public BaseAnswer<OrderRocInfoDetailDTO> getOrderRocDetail(@Valid OrderRocDetailParam orderRocDetailParam,
                                                               @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return iOrder2CService.getOrderRocDetail(orderRocDetailParam,loginIfo4Redis);
    }

    /**
     * 订单验收
     *
     * @param confirmReturnOrder
     * @param userId
     * @return
     */
    @PostMapping("/order/confirmReturnOrder")
    public BaseAnswer<Void> confirmReturnOrder(@RequestBody @Valid ConfirmReturnOrder confirmReturnOrder,
                                               @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return iOrder2CService.confirmReturnOrder(confirmReturnOrder, userId,ip);
    }

    @PostMapping("/order/confirmReturnOrderInternal")
    public BaseAnswer<Void> confirmReturnOrderInternal(@RequestBody @Valid ConfirmReturnOrder confirmReturnOrder) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return iOrder2CService.confirmReturnOrder(confirmReturnOrder, "jkiot",ip);
    }

    /**
     * 获取物流供应商列表
     *
     * @return
     */
    @GetMapping("/order/supplier/list")
    public BaseAnswer<List<LogisticsInfoDTO>> getSupplierList() {
        return iOrder2CService.getSupplierList();
    }

    /**
     * 异常处理订单
     *
     * @param request
     * @param userId
     * @return
     */
    @PostMapping("/order/ex/add")
    @Auth(authCode = {BaseConstant.IOT_ORDER_CANCEL, BaseConstant.REAFTER_CONSIGNMENT_CANCEL})
    public BaseAnswer<Void> addExHandleInfo(@RequestBody @Valid Request4ExHandleAdd request
            , @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId) {
        return exHandleService.addExHandleInfo(request, userId);
    }

    /**
     * 查询异常处理信息
     *
     * @param orderId
     * @param userId
     * @return
     */
    @GetMapping("/order/ex/info")
    public BaseAnswer<ExHandleInfo> queryExHandleInfo(@RequestParam("orderId") String orderId
            , @RequestParam("orderType") String orderType
            , @RequestParam(value = "refundOrderId", required = false) String refundOrderId
            , @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId) {
        return exHandleService.queryExHandleInfo(orderId, orderType, refundOrderId, userId);
    }

    /**
     * 导出订单商品目录信息
     *
     * @param orderId
     * @param userId
     * @return
     */
    @GetMapping("/order/goods/download")
    public BaseAnswer<ResponseEntity<byte[]>> downLoadGoodsList(String orderId,
                                                                String userId) {
        return goodsService.downLoadGoodsList(orderId, userId);
    }

    /**
     * 获取默认编码
     *
     * @return
     */
    @GetMapping("/charset")
    public BaseAnswer<String> getDefaultCharset() {
        BaseAnswer<String> answer = new BaseAnswer<>();
        answer.setData(System.getProperty("file.encoding"));
        return answer;
    }

    /**
     * 获取商品类型列表
     */
    @GetMapping("/order/spuOfferingClassList")
    public BaseAnswer<List<SimpleItemDTO>> getSpuOfferingClassList() {
        return iOrder2CService.getSpuOfferingClassList();
    }


    /**
     * 导出订单列表(返回二进制流) -- 接口废弃，现在导出订单通过export-service服务
     */
    /*@GetMapping("/order/export")
    @Auth(authCode = {BaseConstant.IOT_ORDER_QUERY})
    public void exportOrder(@Valid OrderExportRequest request,
                            @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId,
                            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis
    ) {
        String exportPhone = request.getExportPhone();
        String exportMask = request.getExportMask() == null?"":request.getExportMask()+"";
        Boolean isAdmin = loginIfo4Redis.getIsAdmin();
        // 验证当前用户是否超管，及验证码是否正确
        SmsValidUtil.checkSmsValid(isAdmin,exportMask,exportPhone,redisTemplate);
        iOrder2CService.exportOrder(request, userId, loginIfo4Redis);
    }*/

    /**
     * 不通过网关，直接导出订单列表(返回二进制流)
     */
   /* @GetMapping("/order/directExport")
    public void directExportOrder(@Valid OrderExportRequest request,
                                  @RequestHeader(name = Constant.HEADER_KEY_TOKEN) String token

    ) {
        String exportPhone = request.getExportPhone();
        String exportMask = request.getExportMask() == null ? "":request.getExportMask()+"";
        //从token解析UserId
        AccessToken accessToken = JWTUtil.parseToken(token, AccessToken.class);
        String userId = accessToken.getUserId();
        //获取用户基本信息
        LoginIfo4Redis loginIfo4Redis = (LoginIfo4Redis) redisTemplate.opsForValue().get(Constant.REDIS_KEY_USER_TOKEN + userId);
        if (loginIfo4Redis == null || !token.equals(loginIfo4Redis.getToken())) {
            log.info("token已失效,token:{}", token);
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION, "token已失效，请重新登录");
        }
        //校验权限
        checkAuth(loginIfo4Redis.getAuthCodes(), BaseConstant.IOT_ORDER_QUERY);
        Boolean isAdmin = loginIfo4Redis.getIsAdmin();
        // 验证当前用户是否超管，及验证码是否正确
        SmsValidUtil.checkSmsValid(isAdmin,exportMask,exportPhone,redisTemplate);
        iOrder2CService.exportOrder(request, userId, loginIfo4Redis);
    }*/

    /**
     * 校验权限
     */
    private void checkAuth(List<AuthCode> authCodeList, String... authCodes) {
        // 校验是否有此权限
        // 整理权限码至集合
        List<String> checkCodes = new ArrayList<>();
        authorityInterceptor.reverseAddCode(checkCodes, authCodeList);
        boolean isAuth = false;
        for (String code : authCodes) {
            if (code != null) {
                //用户包含要求的任何一个权限即可
                if (checkCodes.contains(code)) {
                    isAuth = true;
                    break;
                }
            }
        }
        if (!isAuth) {
            throw new BusinessException(BaseErrorConstant.UN_PERMISSION);
        }
    }

    /**
     * 发送导出订单的短信验证码
     *
     * @param phone
     * @return
     */
    @GetMapping(value = "/order/sendOrderExportMask")
    @Auth(authCode = {BaseConstant.IOT_ORDER_QUERY})
    public BaseAnswer<Void> sendOrderExportMask(@RequestParam(value = "phone") String phone) {
        return iOrder2CService.sendExportOrderMask(phone);
    }


    /**
     * 新增订单备注
     *
     * @param orderRemarkParam
     * @param loginIfo4Redis
     */
    @PostMapping("/order/remark")
    @Auth(authCode = {BaseConstant.IOT_ORDER_REMARK})
    public BaseAnswer saveOrderRemark(@RequestBody @Valid OrderRemarkParam orderRemarkParam,
                                      @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        iOrder2CService.insertOrderRemark(orderRemarkParam, loginIfo4Redis);
        return new BaseAnswer();
    }

    /**
     * 订单催单
     *
     * @param orderReminderParam
     * @return
     */
    @PostMapping("/order/reminder")
    @Auth(authCode = {BaseConstant.IOT_ORDER_REMINDER})
    public BaseAnswer<String> saveOrderReminder(@RequestBody @Valid OrderReminderParam orderReminderParam,
                                                @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return iOrder2CService.orderReminder(orderReminderParam,loginIfo4Redis);
    }

    /**
     * 退换新增订单备注
     *
     * @param orderRemarkParam
     * @param loginIfo4Redis
     */
    @PostMapping("/order/after/remark")
    public BaseAnswer saveOrderRemarkForAfter(@RequestBody @Valid OrderRemarkParam orderRemarkParam,
                                      @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        iOrder2CService.insertOrderRemark(orderRemarkParam, loginIfo4Redis);
        return new BaseAnswer();
    }

    /**
     * 退换订单催单
     *
     * @param orderReminderParam
     * @return
     */
    @PostMapping("/order/after/reminder")
    @Auth(authCode = {BaseConstant.IOT_ORDER_REMINDER})
    public BaseAnswer<String> saveOrderReminderForAfter(@RequestBody @Valid OrderReminderParam orderReminderParam,
                                                        @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return iOrder2CService.orderReminder(orderReminderParam,loginIfo4Redis);
    }

    @PostMapping("/order/batchDeliver")
    @Auth(authCode = {BaseConstant.IOT_ORDER_QUERY})
    public void batchDeliver(@RequestPart("file") MultipartFile file, @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId,
                             @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);

        log.info("userId = {}", userId);
        HttpServletResponse response = requestAttr.getResponse();
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        if (file.isEmpty()) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                        "【商品发货】", userId, ip, LogResultEnum.LOG_FAIL.code,
                        BaseErrorConstant.FILE_NOT_EXIST.getMessage());
            });
            throw new BusinessException(BaseErrorConstant.FILE_NOT_EXIST);
        }
        String oldName = file.getOriginalFilename();
        if (oldName == null || (!oldName.endsWith(".xlsx") && !oldName.endsWith(".xls"))) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                        "【商品发货】", userId, ip, LogResultEnum.LOG_FAIL.code,
                        BaseErrorConstant.FILE_TYPE_ERROR.getMessage());
            });
            throw new BusinessException(BaseErrorConstant.FILE_TYPE_ERROR);
        }
        log.info("解析批量发货文件: {}", oldName);

        String importRedisKey = "import_kx_order_deliver_".concat(userId);
        Boolean isUserImport = stringRedisTemplate.hasKey(importRedisKey);
        if (isUserImport != null && isUserImport){
            throw new BusinessException("10004","您有一个任务执行中，请稍后再操作");
        }

        stringRedisTemplate.opsForValue().set(importRedisKey,importRedisKey,12, TimeUnit.HOURS);

        try {
            InputStream fileInputStream = file.getInputStream();
            ThreadExecutorConfig.executorService.execute(() -> {
                try {
                    iOrder2CService.batchDeliver(fileInputStream,oldName, userId,ip,loginIfo4Redis,importRedisKey);
                } catch (Exception e) {
                    log.info("导入批量发货异常:{}",e);
                    stringRedisTemplate.delete(importRedisKey);
                    throw new RuntimeException(e);
                }
            });
            response.setHeader("statecode", "00000");
            response.setHeader("message", URLEncoder.encode("文件导入中，请稍后刷新查看", "UTF-8").replaceAll("\\+", "%20"));
        }catch (Exception e){
            stringRedisTemplate.delete(importRedisKey);
            log.info("批量发货数据异常:{}",e);
            throw new BusinessException("10008","批量发货数据异常");
        }
    }

//    @PostMapping("/order/batchDeliver")
//    public void batchDeliver(@RequestPart("file") MultipartFile file){
//        iOrder2CService.batchDeliver(file,"");
//    }


    /**
     * 同步软件商品信息到订单表中
     *
     * @return
     */
    @PostMapping("/order/software")
    @Auth(authCode = {IOT_USER_ROLE})
    public BaseAnswer<Void> synSoftwareMessageToOrder() {
        return iOrder2CService.synOrderSoftware();
    }

    /**
     * 获取订单业务编码列表
     */
    @GetMapping("/order/businessCodeList")
    public BaseAnswer<List<BusinessCodeListDTO>> getBusinessCodeList() {
        return iOrder2CService.getBusinessCodeList();
    }

    /**
     * 查询订单物流详情信息
     * @param logisticsCode
     * @param supplierName
     * @param contactPhone
     * @return
     */
    @GetMapping("/order/logistics")
    public BaseAnswer<LogisticsVO> getOrderLogisticsMessage(@RequestParam("logisticsCode") String logisticsCode,
                                                            @RequestParam("supplierName") String supplierName,
                                                            @RequestParam(value = "contactPhone",required = false) String contactPhone) {
        return iOrder2CService.getLogisticsDetails(logisticsCode, supplierName,contactPhone);
    }
    /**
     * 查询订单物流详情信息
     * @param logisticsCode
     * @param supplierName
     * @param contactPhone
     * @return
     */
    @GetMapping("/order/logisticsAfterMarket")
    public BaseAnswer<LogisticsVO> logisticsAfterMarket(@RequestParam("logisticsCode") String logisticsCode,
                                                            @RequestParam("supplierName") String supplierName,
                                                            @RequestParam(value = "contactPhone",required = false) String contactPhone) {
        return iOrder2CService.getLogisticsDetails(logisticsCode, supplierName,contactPhone);
    }

    /**
     * 导出IOT商城售后服务信息（web页面现在不使用）
     */
    @GetMapping("/exportStoreSalesReportList")
    @Auth(authCode = {RETAIL})
    public void exportStoreSalesReportList(OrderSalesReportParam orderSalesReportParam, HttpServletResponse response) {
        iOrder2CService.exportStoreSalesReport(orderSalesReportParam, response);
    }

    /**
     * 特殊售后请求
     *
     * @param requestParam
     * @return
     */
    @PostMapping(value = "/requestSpecialAfter")
    @Auth(authCode = {ORDER_CONSIGNMENT_SPECIAL_AFTER_REQUEST})
    public BaseAnswer requestSpecialAfterMarket(@RequestBody @Valid SpecialAfterMarketRequestParam requestParam,
                                                @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) throws Exception {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        BaseAnswer baseAnswer = iOrder2CService.requestSpecialAfterMarket(requestParam, loginIfo4Redis,ip);
        return baseAnswer;
    }

    /**
     * 获取特殊售后列表信息
     *
     * @param orderId
     * @return
     */
//    @GetMapping(value = "/listSpecialAfter")
    public BaseAnswer<List<SpecialAfterMarketResultVO>> listSpecialAfterMarketResult(@RequestParam(value = "orderId") String orderId) {
        BaseAnswer<List<SpecialAfterMarketResultVO>> baseAnswer = new BaseAnswer<>();
        List<SpecialAfterMarketResultVO> resultList = specialAfterMarketResultService.listSpecialAfterMarketResult(orderId);
        baseAnswer.setData(resultList);
        return baseAnswer;
    }

    /**
     * 订单接单
     * @param orderGetOrderParam
     * @return
     */
    @PostMapping(value = "/order/getOrder")
    @Auth(authCode = {BaseConstant.ORDER_GET_ORDER})
    public BaseAnswer orderGetOrderRequest(@RequestBody @Valid OrderGetOrderParam orderGetOrderParam,
                                           @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        iOrder2CService.orderGetOrderRequest(orderGetOrderParam,loginIfo4Redis.getUserId(),ip);
        //记录日志
        logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code, OrderManageOperateEnum.MALL_SYNC_ORDER.code,
                IotLogUtil.orderGetOrder(orderGetOrderParam, supplierMap),LogResultEnum.LOG_SUCESS.code, null);
        return new BaseAnswer();
    }

    /**
     * 获取接单详情
     * @param getOrderDetailParam
     * @return
     */
    @GetMapping(value = "/order/getOrderDetail")
    @Auth(authCode = {BaseConstant.ORDER_GET_ORDER})
    public BaseAnswer<GetOrderDetailVO> getGetOrderDetail(@Valid GetOrderDetailParam getOrderDetailParam){
        return iOrder2CService.getGetOrderDetail(getOrderDetailParam);
    }

    /**
     * 订单接单更新物流信息
     * @param getOrderUpdateLogisticsParam
     * @return
     */
    @PostMapping(value = "/order/updateLogistics")
    @Auth(authCode = {BaseConstant.ORDER_GET_ORDER})
    public BaseAnswer getOrderUpdateLogistics(@RequestBody @Valid GetOrderUpdateLogisticsParam getOrderUpdateLogisticsParam){
        return order2IOTService.syncUpdateLogistics(getOrderUpdateLogisticsParam);
    }

    /**
     * （后台接口）手动执行定时任务，将状态文件中的代客下单订单状态，同步更新到订单表。
     */
    @PostMapping("/backend/afterPayOrderStatusTask")
    public BaseAnswer afterPayOrderStatusTask(){
        afterPayOrderStatusTask.work();
        return BaseAnswer.success("ok");
    }


    /**
     * 生成联合销售excel
     */
    @GetMapping("/union/sell/order/export")
    public void unionSellOrderExport(String date) {
        iOrder2CService.unionSellOrderExport(date);
    }

    /**
     * 下载联合销售excel
     */
    @PostMapping("/union/sell/order/download")
    @Auth(authCode = {BaseConstant.SETTLEMENT_MANAGEMENT_CONSIGNMENT_HARDWARE_MANAGEMENT})
    public void downloadUnionSellOrder(@RequestBody @Valid UnionSellOrderDownloadRequest request,
                                       @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        String exportPhone = request.getExportPhone();
        String exportMask = request.getExportMask() == null?"":request.getExportMask()+"";
        Boolean isAdmin = loginIfo4Redis.getIsAdmin();
        // 验证当前用户是否超管，及验证码是否正确
        SmsValidUtil.checkSmsValid(isAdmin,exportMask,exportPhone,redisTemplate);
        iOrder2CService.downloadUnionSellOrder(request, loginIfo4Redis);
    }

    /**
     * 联合销售excel列表
     */
    @GetMapping("/union/sell/order/list")
    @Auth(authCode = {BaseConstant.SETTLEMENT_MANAGEMENT_CONSIGNMENT_HARDWARE_MANAGEMENT})
    public BaseAnswer<PageData<UnionSellOrderExcelHistory>> unionSellOrderList (@Valid UnionSellOrderListQueryParam param) {
        return iOrder2CService.unionSellOrderList(param);
    }

    /**
     * 生成联合销售excel(时间段)
     */
    @GetMapping("/union/sell/order/export/batch")
    @Auth(authCode = {BaseConstant.SETTLEMENT_MANAGEMENT_CONSIGNMENT_HARDWARE_MANAGEMENT})
    public BaseAnswer<Void> unionSellOrderExportBatch(String startTime, String endTime) {
        return iOrder2CService.unionSellOrderExportBatch(startTime, endTime);
    }


    /**
     * (后台接口)手动修复order_2c_info表的order_status_time为空的问题，将成功或失败的订单的order_2c_atom_history表对应状态的时间设置到order_2c_info表的order_status_time(仅针对普通订单，代客下单订单不存在此问题，因为order_status_time字段的增加早于代客下单)
     */
    @PostMapping("/fixOrderStatusTime")
    public BaseAnswer<String> fixOrderStatusTime(Integer pageSize,@RequestParam(required = true) Integer times){
        return iOrder2CService.fixOrderStatusTime(pageSize,times);
    }

    /**
     * (后台接口)手动将已出账的代客下单存量订单的create_time,设置到valet_order_complete_time
     * @return
     */
    @PostMapping("/initValetOrderCompleteTime")
    public BaseAnswer<String> initValetOrderCompleteTime(){
        return iOrder2CService.initValetOrderCompleteTime();
    }

    /**
     * 手动操作云视讯订单，订阅或者退订
     * @return
     */
    @PostMapping("/order/oprYsxOrder")
    public BaseAnswer<String> oprYsxOrder(@RequestBody @Valid YsxOprParam param){
        return iOrder2CService.oprYsxOrder(param.getOrderId(), param.getOprCode());
    }

    /**
     * 行车卫士订购/退订操作
     * @param carSecuritySubscribeParam
     * @return
     * @throws Exception
     */
    @PostMapping("/order/carSecuritySubscribe")
    public BaseAnswer<String> turnOnCarSecurityProductSubscribe(@RequestBody @Valid CarSecuritySubscribeParam carSecuritySubscribeParam) throws Exception {
        return iOrder2CService.turnOnCarSecurityProductSubscribe(carSecuritySubscribeParam);
    }

     

    /**
     * 处理卡+X接单前是否同意退款
     * @param kxOrderRocParam
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping(value = "/order/handleKxOrderRoc")
    public BaseAnswer handleKxOrderRoc(@Valid @RequestBody KxOrderRocParam kxOrderRocParam,
                                       @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return iOrder2CService.handleKxOrderRoc(kxOrderRocParam,loginIfo4Redis,ip);
    }

    /**
     * 手动更新卡+X的物流信息到商城
     * @param orderId
     * @param spuCode
     * @param skuCode
     * @param atomOfferingCode
     * @return
     */
//    @GetMapping(value = "/synKxLogisticsInfoToIot")
//    public BaseAnswer synKxLogisticsInfoToIot(@RequestParam(value = "orderId")String orderId,
//                                              @RequestParam(value = "spuCode")String spuCode,
//                                              @RequestParam(value = "skuCode")String skuCode,
//                                              @RequestParam(value = "atomOfferingCode")String atomOfferingCode){
//        Order2cAtomInfoExample order2cAtomInfoExample = new Order2cAtomInfoExample();
//        order2cAtomInfoExample.createCriteria()
//                .andOrderIdEqualTo(orderId)
//                .andSpuOfferingCodeEqualTo(spuCode)
//                .andSkuOfferingCodeEqualTo(skuCode)
//                .andAtomOfferingCodeEqualTo(atomOfferingCode);
//
//        List<Order2cAtomInfo> order2cAtomInfoList = atomOrderInfoMapper.selectByExample(order2cAtomInfoExample);
//
//        if (CollectionUtils.isNotEmpty(order2cAtomInfoList)) {
//            IOTAnswer<Void> iotAnswer = new IOTAnswer<>();
//            Date now = new Date();
//            // 处理物流信息
//            iOrder2CService.handleLogisticsInfo(order2cAtomInfoList.get(0), orderId, iotAnswer, now);
//        }
//        return new BaseAnswer();
//    }

    /**
     * 获取卡+X订单的卡片类型
     */
    @GetMapping("/order/cardType")
    public BaseAnswer<String> getOrderCardType(@RequestParam(required = true)String orderId){
        return iOrder2CService.getOrderCardType(orderId);
    }

    /**
     * 获取订单结算状态
     * @return
     */
    @GetMapping(value = "/order/listSettleStatus")
    public BaseAnswer<List<SettleStatusVO>> listSettleStatus(){
        List<SettleStatusVO> settleStatusVOList = new ArrayList();
        SettleStatusVO settleStatusVOAll = new SettleStatusVO();
        settleStatusVOAll.setCode(null);
        settleStatusVOAll.setDesc("全部");
        settleStatusVOList.add(settleStatusVOAll);
        for (SettleStatusEnum settleStatus: SettleStatusEnum.values()){
            SettleStatusVO settleStatusVO = new SettleStatusVO();
            settleStatusVO.setCode(settleStatus.getCode());
            settleStatusVO.setDesc(settleStatus.getDesc());
            settleStatusVOList.add(settleStatusVO);
        }
        return new BaseAnswer<List<SettleStatusVO>>().setData(settleStatusVOList);
    }

    /**
     * 获取线上结算采购订单结算状态
     * @return
     */
    @GetMapping(value = "/order/listOnlineSettleStatus")
    public BaseAnswer<List<OnlineSettleStatusVO>> listOnlineSettleStatus(){
        List<OnlineSettleStatusVO> onlineSettleStatusVOList = new ArrayList();
        OnlineSettleStatusVO onlineSettleStatusVOAll = new OnlineSettleStatusVO();
        onlineSettleStatusVOAll.setCode(null);
        onlineSettleStatusVOAll.setDesc("全部");
        onlineSettleStatusVOList.add(onlineSettleStatusVOAll);
        for (OnlineSettleStatusEnum onlineSettleStatus: OnlineSettleStatusEnum.values()){
            OnlineSettleStatusVO onlineSettleStatusVO = new OnlineSettleStatusVO();
            onlineSettleStatusVO.setCode(Integer.parseInt(onlineSettleStatus.getStatus()));
            onlineSettleStatusVO.setDesc(onlineSettleStatus.getDesc());
            onlineSettleStatusVOList.add(onlineSettleStatusVO);
        }
        return new BaseAnswer<List<OnlineSettleStatusVO>>().setData(onlineSettleStatusVOList);
    }

    /**
     * （后台接口）K+X代客下单，自动接单失败后，手动处理数据，完成接单
     * @return
     */
    @PostMapping("/back/allowOrder")
    public BaseAnswer syncAllowOrdeResult(String orderId){
        iOrder2CService.syncAllowOrerResult(orderId);
        return BaseAnswer.success(null);
    }

    /**
     * (异步，消息中心)批量导出货单PDF,合同履约/联合销售/卡+X
     */
    @GetMapping("/batchExportCardxDeliveryNotes")
    public BaseAnswer batchExportCardxDeliveryNotes(@Valid BatchExportCardxDeliveryNoteParam param,
                                              HttpServletResponse response,
                                              @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return iOrder2CService.batchExportCardxDeliveryNotes(param,response,loginIfo4Redis,ip);
    }

    /**
     * (异步，消息中心)单个导出货单PDF,合同履约/联合销售/卡+X,订单同步过来就可以导出
     */
    @GetMapping("/singleExportCardxDeliveryNotes")
    public BaseAnswer singleExportCardxDeliveryNotes(/*@RequestParam(required = true) String atomOrderId,*/
            @RequestParam(required = true) String orderId,
                                               HttpServletResponse response,
                                               @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        String userId = loginIfo4Redis.getUserId();
        String importRedisKey = "single_export_cardx_delivery_notes".concat(userId);
        Boolean isUserImport = stringRedisTemplate.hasKey(importRedisKey);
        if (isUserImport != null && isUserImport){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "货单导出中，稍后短信提醒您");
        }

        stringRedisTemplate.opsForValue().set(importRedisKey,importRedisKey,12, TimeUnit.HOURS);

        ThreadExecutorConfig.executorService.execute(() -> {
            iOrder2CService.singleExportCardxDeliveryNotes(orderId,response,loginIfo4Redis,ip,importRedisKey);
        });
        return BaseAnswer.success(null);
    }

    /**
     * 迁移订单的userId
     * @return
     */
    @PostMapping("/order/migrateUserId")
    public BaseAnswer migrateOrderUserId(){
        iOrder2CService.migrateOrderUserId();
        return BaseAnswer.success(null);
    }



}
