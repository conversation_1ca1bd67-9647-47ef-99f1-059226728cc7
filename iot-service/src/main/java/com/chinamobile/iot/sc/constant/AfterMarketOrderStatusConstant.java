package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2022/12/13 17:13
 * @description: 售后订单状态常量类
 **/
public class AfterMarketOrderStatusConstant {


    /**
     * 售后服务订单状态:
     * 1:待预约 -对应商城同步状态【1待预约（付款完成）】
     * 2:派单中-对应商城同步状态【2派单中（预约完成）】
     * 3:已预约
     * 4.已完结（成功）
     * 5.已完成（失败）
     * 6.已撤销
     * 7.交易完成-对应商城同步状态【3订单计收（订单同步至CMIoT成功后，同步本状态）】
     * 8.交易失败-对应商城同步状态【4退款完成】
     * 9.同步省侧，省测接单专用
     * */
    public static final Integer TO_APPOINTMENT_STATUS = 1;

    public static final Integer DISPATCHING_STATUS = 2;

    public static final Integer DISPATCHED_STATUS = 3;

    public static final Integer DELIVERY_SUCCESS_STATUS = 4;

    public static final Integer DELIVERY_FAIL_STATUS = 5;

    public static final Integer ORDER_CANCELED_STATUS = 6;

    public static final Integer ORDER_FINISH_STATUS = 7;

    public static final Integer ORDER_FAIL_STATUS = 8;

    public static final Integer ORDER_SYNC_PROVINCE = 9;

    public static final Integer ORDER_CLOSED_STATUS = 10;
}
