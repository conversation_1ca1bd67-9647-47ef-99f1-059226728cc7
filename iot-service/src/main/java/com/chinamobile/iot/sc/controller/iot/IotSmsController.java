package com.chinamobile.iot.sc.controller.iot;

import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.request.sms.Msg4Request;
import com.chinamobile.iot.sc.response.iot.IOTSmsResponse;
import com.chinamobile.iot.sc.service.IOTSmsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * created by l<PERSON>xia<PERSON> on 2022/4/12 10:25
 * 封装短信接口给IOT商城使用，透传IOT商城的全部参数，包括sicode和signid
 */
@RestController()
@RequestMapping("/os/note")
public class IotSmsController {

    @Autowired
    private IOTSmsService iotSmsService;

    /**
     * 发送短信(透传方式)
     */
    @PostMapping("/SendSMS")
    public IOTAnswer<JSONObject> sendSms(@RequestBody IOTRequest baseRequest){
        return iotSmsService.sendSms(baseRequest);
    }


}
