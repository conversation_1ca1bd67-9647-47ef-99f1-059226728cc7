package com.chinamobile.iot.sc.constant;

/**
 * @Author: YSC
 * @Date: 2021/12/22 19:41
 * @Description:
 */
public enum OrderRocReasonEnum {
    A("1", "不喜欢/不想要"),
    B("2", "商品错发"),
    C("3", "收到商品与描述不符"),
    D("4", "商品质量问题"),
    E("5", "快递/物流一直未送到"),
    F("6", "其他"),
    G("7", "七天无理由退换货"),
    H("8", "商品信息拍错"),
    I("9", "地址/电话信息填写错误"),
    J("10", "拍多了"),
    K("11", "协商一致退款"),
    L("12", "缺货"),
    M("13", "发货速度不满意"),
    N("14", "对售后服务不满意"),
    O("15", "商品退换货，售后服务退款"),
    P("16", "售后服务交付失败"),
    Q("17", "卡+X订购失败"),
    U("18", "终端运营人员接单失败");

    /**
     * 订单状态码
     */
    private final String reason;
    /**
     * 订单状态信息
     */
    private final String message;

    OrderRocReasonEnum(String status, String message) {
        this.reason = status;
        this.message = message;
    }


    public String getReason() {
        return reason;
    }

    public String getMessage() {
        return message;
    }

    public static String getDescribe(String reason) {
        for (OrderRocReasonEnum value : OrderRocReasonEnum.values()) {
            if (value.reason.equals(reason)) {
                return value.message;
            }
        }
        return null;
    }
}
