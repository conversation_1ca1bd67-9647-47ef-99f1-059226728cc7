package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 规格编码
 *
 * <AUTHOR>
public class SkuOfferingInfoHistory implements Serializable {
    /**
     * sku主键id
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.id
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String id;

    /**
     * spu 主键id
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.spu_id
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String spuId;

    /**
     * 商品组/销售商品编码
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.spu_code
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String spuCode;

    /**
     * 规格编码
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.offering_code
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String offeringCode;

    /**
     * 商品名称
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.offering_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String offeringName;

    /**
     * sku商品状态 0：测试、1：发布、2：下架
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.offering_status
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String offeringStatus;

    /**
     * sku商品状态变更时间
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.offering_status_time
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private Date offeringStatusTime;

    /**
     * 商品构成
offeringClass = A02时，本字段必填;
0:纯软件
1：软硬一体
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.composition
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String composition;

    /**
     * 型号
composition=1时必填;
或者offeringClass = A03时必填;
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.model
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String model;

    /**
     * 部件数量
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.quantity
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private Long quantity;

    /**
     * 尺寸
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.size
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String size;

    /**
     * 操作类型
A：新增
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.oper_type
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String operType;

    /**
     * 建议零售价
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.recommend_price
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private Long recommendPrice;

    /**
     * G:集团客户
P:个人客户
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.sale_object
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String saleObject;

    /**
     * 销售目录价
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.price
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private Long price;

    /**
     * 计量单位
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.unit
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String unit;

    /**
     * 营销案名称
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.market_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String marketName;

    /**
     * 营销案编码
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.market_code
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String marketCode;

    /**
     * 供应商名称
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.supplier_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String supplierName;

    /**
     * X产品类型,1:5G CPE,2:5G 快线,3:千里眼,4:合同履约,5:OneNET独立服务,6:标准产品(OneNET）,7:OnePark独立服务,8:标准产品（OnePark）9：千里眼独立服务10：和对讲独立服务 11：云视讯独立服务
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.product_type
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String productType;

    /**
     * 是否需要合作伙伴接单,1：是,2：否
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.receive_order
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String receiveOrder;

    /**
     * 卡服务商EC编码
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.cust_code
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String custCode;

    /**
     * 卡服务商名称
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.cust_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String custName;

    /**
     * 卡片类型,0：插拔卡,1：贴片卡,2：M2M芯片非空写卡,3：M2M芯片空写卡
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.card_type
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String cardType;

    /**
     * 主商品,01：物联卡个人、03：窄带网个人、04：和对讲个人 16：行车卫士个人
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.main_offering_code
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String mainOfferingCode;

    /**
     * 模板名称
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.template_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String templateName;

    /**
     * 开卡模板id
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.template_id
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String templateId;

    /**
     * 销售模式1：省内融合 2：商城直销  当offeringClass=A11卡+X时必传
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.sale_model
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String saleModel;

    /**
     * 项目信息
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.project
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String project;

    /**
     * 积分状态, 1-- 暂停  2-- 生效
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.point_status
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private Integer pointStatus;

    /**
     * 创建时间
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.create_time
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private Date createTime;

    /**
     * 更新时间
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.update_time
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private Date updateTime;

    /**
     * 删除时间
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.delete_time
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private Date deleteTime;

    /**
     * 套餐类型0：连续包月    1：一次性包
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.package_type
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String packageType;

    /**
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.sku_offering_version
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String skuOfferingVersion;

    /**
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.spu_offering_version
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String spuOfferingVersion;

    /**
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.sku_id
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String skuId;

    /**
     * 规格简称
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.sku_abbreviation
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String skuAbbreviation;

    /**
     * 接单人员名称，加密
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.receive_order_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String receiveOrderName;

    /**
     * 接单人员联系电话，加密
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.receive_order_phone
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String receiveOrderPhone;

    /**
     * 交付人员名称，加密
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.deliver_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String deliverName;

    /**
     * 交付人员联系电话，加密
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.deliver_phone
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String deliverPhone;

    /**
     * 售后人员名称，加密
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.aftermarket_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String aftermarketName;

    /**
     * 售后人员联系电话，加密
     *
     * Corresponding to the database column supply_chain..sku_offering_info_history.aftermarket_phone
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private String aftermarketPhone;

    /**
     * Corresponding to the database table supply_chain..sku_offering_info_history
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.id
     *
     * @return the value of supply_chain..sku_offering_info_history.id
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.id
     *
     * @param id the value for supply_chain..sku_offering_info_history.id
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.spu_id
     *
     * @return the value of supply_chain..sku_offering_info_history.spu_id
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getSpuId() {
        return spuId;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withSpuId(String spuId) {
        this.setSpuId(spuId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.spu_id
     *
     * @param spuId the value for supply_chain..sku_offering_info_history.spu_id
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setSpuId(String spuId) {
        this.spuId = spuId == null ? null : spuId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.spu_code
     *
     * @return the value of supply_chain..sku_offering_info_history.spu_code
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getSpuCode() {
        return spuCode;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withSpuCode(String spuCode) {
        this.setSpuCode(spuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.spu_code
     *
     * @param spuCode the value for supply_chain..sku_offering_info_history.spu_code
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setSpuCode(String spuCode) {
        this.spuCode = spuCode == null ? null : spuCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.offering_code
     *
     * @return the value of supply_chain..sku_offering_info_history.offering_code
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getOfferingCode() {
        return offeringCode;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withOfferingCode(String offeringCode) {
        this.setOfferingCode(offeringCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.offering_code
     *
     * @param offeringCode the value for supply_chain..sku_offering_info_history.offering_code
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setOfferingCode(String offeringCode) {
        this.offeringCode = offeringCode == null ? null : offeringCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.offering_name
     *
     * @return the value of supply_chain..sku_offering_info_history.offering_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getOfferingName() {
        return offeringName;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withOfferingName(String offeringName) {
        this.setOfferingName(offeringName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.offering_name
     *
     * @param offeringName the value for supply_chain..sku_offering_info_history.offering_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setOfferingName(String offeringName) {
        this.offeringName = offeringName == null ? null : offeringName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.offering_status
     *
     * @return the value of supply_chain..sku_offering_info_history.offering_status
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getOfferingStatus() {
        return offeringStatus;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withOfferingStatus(String offeringStatus) {
        this.setOfferingStatus(offeringStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.offering_status
     *
     * @param offeringStatus the value for supply_chain..sku_offering_info_history.offering_status
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setOfferingStatus(String offeringStatus) {
        this.offeringStatus = offeringStatus == null ? null : offeringStatus.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.offering_status_time
     *
     * @return the value of supply_chain..sku_offering_info_history.offering_status_time
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public Date getOfferingStatusTime() {
        return offeringStatusTime;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withOfferingStatusTime(Date offeringStatusTime) {
        this.setOfferingStatusTime(offeringStatusTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.offering_status_time
     *
     * @param offeringStatusTime the value for supply_chain..sku_offering_info_history.offering_status_time
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setOfferingStatusTime(Date offeringStatusTime) {
        this.offeringStatusTime = offeringStatusTime;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.composition
     *
     * @return the value of supply_chain..sku_offering_info_history.composition
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getComposition() {
        return composition;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withComposition(String composition) {
        this.setComposition(composition);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.composition
     *
     * @param composition the value for supply_chain..sku_offering_info_history.composition
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setComposition(String composition) {
        this.composition = composition == null ? null : composition.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.model
     *
     * @return the value of supply_chain..sku_offering_info_history.model
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getModel() {
        return model;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withModel(String model) {
        this.setModel(model);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.model
     *
     * @param model the value for supply_chain..sku_offering_info_history.model
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.quantity
     *
     * @return the value of supply_chain..sku_offering_info_history.quantity
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public Long getQuantity() {
        return quantity;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withQuantity(Long quantity) {
        this.setQuantity(quantity);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.quantity
     *
     * @param quantity the value for supply_chain..sku_offering_info_history.quantity
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.size
     *
     * @return the value of supply_chain..sku_offering_info_history.size
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getSize() {
        return size;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withSize(String size) {
        this.setSize(size);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.size
     *
     * @param size the value for supply_chain..sku_offering_info_history.size
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setSize(String size) {
        this.size = size == null ? null : size.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.oper_type
     *
     * @return the value of supply_chain..sku_offering_info_history.oper_type
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getOperType() {
        return operType;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withOperType(String operType) {
        this.setOperType(operType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.oper_type
     *
     * @param operType the value for supply_chain..sku_offering_info_history.oper_type
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setOperType(String operType) {
        this.operType = operType == null ? null : operType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.recommend_price
     *
     * @return the value of supply_chain..sku_offering_info_history.recommend_price
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public Long getRecommendPrice() {
        return recommendPrice;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withRecommendPrice(Long recommendPrice) {
        this.setRecommendPrice(recommendPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.recommend_price
     *
     * @param recommendPrice the value for supply_chain..sku_offering_info_history.recommend_price
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setRecommendPrice(Long recommendPrice) {
        this.recommendPrice = recommendPrice;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.sale_object
     *
     * @return the value of supply_chain..sku_offering_info_history.sale_object
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getSaleObject() {
        return saleObject;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withSaleObject(String saleObject) {
        this.setSaleObject(saleObject);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.sale_object
     *
     * @param saleObject the value for supply_chain..sku_offering_info_history.sale_object
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setSaleObject(String saleObject) {
        this.saleObject = saleObject == null ? null : saleObject.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.price
     *
     * @return the value of supply_chain..sku_offering_info_history.price
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public Long getPrice() {
        return price;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withPrice(Long price) {
        this.setPrice(price);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.price
     *
     * @param price the value for supply_chain..sku_offering_info_history.price
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setPrice(Long price) {
        this.price = price;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.unit
     *
     * @return the value of supply_chain..sku_offering_info_history.unit
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getUnit() {
        return unit;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withUnit(String unit) {
        this.setUnit(unit);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.unit
     *
     * @param unit the value for supply_chain..sku_offering_info_history.unit
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.market_name
     *
     * @return the value of supply_chain..sku_offering_info_history.market_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getMarketName() {
        return marketName;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withMarketName(String marketName) {
        this.setMarketName(marketName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.market_name
     *
     * @param marketName the value for supply_chain..sku_offering_info_history.market_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setMarketName(String marketName) {
        this.marketName = marketName == null ? null : marketName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.market_code
     *
     * @return the value of supply_chain..sku_offering_info_history.market_code
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getMarketCode() {
        return marketCode;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withMarketCode(String marketCode) {
        this.setMarketCode(marketCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.market_code
     *
     * @param marketCode the value for supply_chain..sku_offering_info_history.market_code
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setMarketCode(String marketCode) {
        this.marketCode = marketCode == null ? null : marketCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.supplier_name
     *
     * @return the value of supply_chain..sku_offering_info_history.supplier_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getSupplierName() {
        return supplierName;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withSupplierName(String supplierName) {
        this.setSupplierName(supplierName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.supplier_name
     *
     * @param supplierName the value for supply_chain..sku_offering_info_history.supplier_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName == null ? null : supplierName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.product_type
     *
     * @return the value of supply_chain..sku_offering_info_history.product_type
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getProductType() {
        return productType;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withProductType(String productType) {
        this.setProductType(productType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.product_type
     *
     * @param productType the value for supply_chain..sku_offering_info_history.product_type
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setProductType(String productType) {
        this.productType = productType == null ? null : productType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.receive_order
     *
     * @return the value of supply_chain..sku_offering_info_history.receive_order
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getReceiveOrder() {
        return receiveOrder;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withReceiveOrder(String receiveOrder) {
        this.setReceiveOrder(receiveOrder);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.receive_order
     *
     * @param receiveOrder the value for supply_chain..sku_offering_info_history.receive_order
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setReceiveOrder(String receiveOrder) {
        this.receiveOrder = receiveOrder == null ? null : receiveOrder.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.cust_code
     *
     * @return the value of supply_chain..sku_offering_info_history.cust_code
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getCustCode() {
        return custCode;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withCustCode(String custCode) {
        this.setCustCode(custCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.cust_code
     *
     * @param custCode the value for supply_chain..sku_offering_info_history.cust_code
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setCustCode(String custCode) {
        this.custCode = custCode == null ? null : custCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.cust_name
     *
     * @return the value of supply_chain..sku_offering_info_history.cust_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getCustName() {
        return custName;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withCustName(String custName) {
        this.setCustName(custName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.cust_name
     *
     * @param custName the value for supply_chain..sku_offering_info_history.cust_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setCustName(String custName) {
        this.custName = custName == null ? null : custName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.card_type
     *
     * @return the value of supply_chain..sku_offering_info_history.card_type
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getCardType() {
        return cardType;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withCardType(String cardType) {
        this.setCardType(cardType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.card_type
     *
     * @param cardType the value for supply_chain..sku_offering_info_history.card_type
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setCardType(String cardType) {
        this.cardType = cardType == null ? null : cardType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.main_offering_code
     *
     * @return the value of supply_chain..sku_offering_info_history.main_offering_code
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getMainOfferingCode() {
        return mainOfferingCode;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withMainOfferingCode(String mainOfferingCode) {
        this.setMainOfferingCode(mainOfferingCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.main_offering_code
     *
     * @param mainOfferingCode the value for supply_chain..sku_offering_info_history.main_offering_code
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setMainOfferingCode(String mainOfferingCode) {
        this.mainOfferingCode = mainOfferingCode == null ? null : mainOfferingCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.template_name
     *
     * @return the value of supply_chain..sku_offering_info_history.template_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getTemplateName() {
        return templateName;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withTemplateName(String templateName) {
        this.setTemplateName(templateName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.template_name
     *
     * @param templateName the value for supply_chain..sku_offering_info_history.template_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setTemplateName(String templateName) {
        this.templateName = templateName == null ? null : templateName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.template_id
     *
     * @return the value of supply_chain..sku_offering_info_history.template_id
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getTemplateId() {
        return templateId;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withTemplateId(String templateId) {
        this.setTemplateId(templateId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.template_id
     *
     * @param templateId the value for supply_chain..sku_offering_info_history.template_id
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setTemplateId(String templateId) {
        this.templateId = templateId == null ? null : templateId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.sale_model
     *
     * @return the value of supply_chain..sku_offering_info_history.sale_model
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getSaleModel() {
        return saleModel;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withSaleModel(String saleModel) {
        this.setSaleModel(saleModel);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.sale_model
     *
     * @param saleModel the value for supply_chain..sku_offering_info_history.sale_model
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setSaleModel(String saleModel) {
        this.saleModel = saleModel == null ? null : saleModel.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.project
     *
     * @return the value of supply_chain..sku_offering_info_history.project
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getProject() {
        return project;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withProject(String project) {
        this.setProject(project);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.project
     *
     * @param project the value for supply_chain..sku_offering_info_history.project
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setProject(String project) {
        this.project = project == null ? null : project.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.point_status
     *
     * @return the value of supply_chain..sku_offering_info_history.point_status
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public Integer getPointStatus() {
        return pointStatus;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withPointStatus(Integer pointStatus) {
        this.setPointStatus(pointStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.point_status
     *
     * @param pointStatus the value for supply_chain..sku_offering_info_history.point_status
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setPointStatus(Integer pointStatus) {
        this.pointStatus = pointStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.create_time
     *
     * @return the value of supply_chain..sku_offering_info_history.create_time
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.create_time
     *
     * @param createTime the value for supply_chain..sku_offering_info_history.create_time
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.update_time
     *
     * @return the value of supply_chain..sku_offering_info_history.update_time
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.update_time
     *
     * @param updateTime the value for supply_chain..sku_offering_info_history.update_time
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.delete_time
     *
     * @return the value of supply_chain..sku_offering_info_history.delete_time
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public Date getDeleteTime() {
        return deleteTime;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withDeleteTime(Date deleteTime) {
        this.setDeleteTime(deleteTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.delete_time
     *
     * @param deleteTime the value for supply_chain..sku_offering_info_history.delete_time
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.package_type
     *
     * @return the value of supply_chain..sku_offering_info_history.package_type
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getPackageType() {
        return packageType;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withPackageType(String packageType) {
        this.setPackageType(packageType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.package_type
     *
     * @param packageType the value for supply_chain..sku_offering_info_history.package_type
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setPackageType(String packageType) {
        this.packageType = packageType == null ? null : packageType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.sku_offering_version
     *
     * @return the value of supply_chain..sku_offering_info_history.sku_offering_version
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getSkuOfferingVersion() {
        return skuOfferingVersion;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withSkuOfferingVersion(String skuOfferingVersion) {
        this.setSkuOfferingVersion(skuOfferingVersion);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.sku_offering_version
     *
     * @param skuOfferingVersion the value for supply_chain..sku_offering_info_history.sku_offering_version
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setSkuOfferingVersion(String skuOfferingVersion) {
        this.skuOfferingVersion = skuOfferingVersion == null ? null : skuOfferingVersion.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.spu_offering_version
     *
     * @return the value of supply_chain..sku_offering_info_history.spu_offering_version
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getSpuOfferingVersion() {
        return spuOfferingVersion;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withSpuOfferingVersion(String spuOfferingVersion) {
        this.setSpuOfferingVersion(spuOfferingVersion);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.spu_offering_version
     *
     * @param spuOfferingVersion the value for supply_chain..sku_offering_info_history.spu_offering_version
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setSpuOfferingVersion(String spuOfferingVersion) {
        this.spuOfferingVersion = spuOfferingVersion == null ? null : spuOfferingVersion.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.sku_id
     *
     * @return the value of supply_chain..sku_offering_info_history.sku_id
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getSkuId() {
        return skuId;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withSkuId(String skuId) {
        this.setSkuId(skuId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.sku_id
     *
     * @param skuId the value for supply_chain..sku_offering_info_history.sku_id
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setSkuId(String skuId) {
        this.skuId = skuId == null ? null : skuId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.sku_abbreviation
     *
     * @return the value of supply_chain..sku_offering_info_history.sku_abbreviation
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getSkuAbbreviation() {
        return skuAbbreviation;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withSkuAbbreviation(String skuAbbreviation) {
        this.setSkuAbbreviation(skuAbbreviation);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.sku_abbreviation
     *
     * @param skuAbbreviation the value for supply_chain..sku_offering_info_history.sku_abbreviation
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setSkuAbbreviation(String skuAbbreviation) {
        this.skuAbbreviation = skuAbbreviation == null ? null : skuAbbreviation.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.receive_order_name
     *
     * @return the value of supply_chain..sku_offering_info_history.receive_order_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getReceiveOrderName() {
        return receiveOrderName;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withReceiveOrderName(String receiveOrderName) {
        this.setReceiveOrderName(receiveOrderName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.receive_order_name
     *
     * @param receiveOrderName the value for supply_chain..sku_offering_info_history.receive_order_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setReceiveOrderName(String receiveOrderName) {
        this.receiveOrderName = receiveOrderName == null ? null : receiveOrderName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.receive_order_phone
     *
     * @return the value of supply_chain..sku_offering_info_history.receive_order_phone
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getReceiveOrderPhone() {
        return receiveOrderPhone;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withReceiveOrderPhone(String receiveOrderPhone) {
        this.setReceiveOrderPhone(receiveOrderPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.receive_order_phone
     *
     * @param receiveOrderPhone the value for supply_chain..sku_offering_info_history.receive_order_phone
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setReceiveOrderPhone(String receiveOrderPhone) {
        this.receiveOrderPhone = receiveOrderPhone == null ? null : receiveOrderPhone.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.deliver_name
     *
     * @return the value of supply_chain..sku_offering_info_history.deliver_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getDeliverName() {
        return deliverName;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withDeliverName(String deliverName) {
        this.setDeliverName(deliverName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.deliver_name
     *
     * @param deliverName the value for supply_chain..sku_offering_info_history.deliver_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setDeliverName(String deliverName) {
        this.deliverName = deliverName == null ? null : deliverName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.deliver_phone
     *
     * @return the value of supply_chain..sku_offering_info_history.deliver_phone
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getDeliverPhone() {
        return deliverPhone;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withDeliverPhone(String deliverPhone) {
        this.setDeliverPhone(deliverPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.deliver_phone
     *
     * @param deliverPhone the value for supply_chain..sku_offering_info_history.deliver_phone
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setDeliverPhone(String deliverPhone) {
        this.deliverPhone = deliverPhone == null ? null : deliverPhone.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.aftermarket_name
     *
     * @return the value of supply_chain..sku_offering_info_history.aftermarket_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getAftermarketName() {
        return aftermarketName;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withAftermarketName(String aftermarketName) {
        this.setAftermarketName(aftermarketName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.aftermarket_name
     *
     * @param aftermarketName the value for supply_chain..sku_offering_info_history.aftermarket_name
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setAftermarketName(String aftermarketName) {
        this.aftermarketName = aftermarketName == null ? null : aftermarketName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..sku_offering_info_history.aftermarket_phone
     *
     * @return the value of supply_chain..sku_offering_info_history.aftermarket_phone
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public String getAftermarketPhone() {
        return aftermarketPhone;
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public SkuOfferingInfoHistory withAftermarketPhone(String aftermarketPhone) {
        this.setAftermarketPhone(aftermarketPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..sku_offering_info_history.aftermarket_phone
     *
     * @param aftermarketPhone the value for supply_chain..sku_offering_info_history.aftermarket_phone
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public void setAftermarketPhone(String aftermarketPhone) {
        this.aftermarketPhone = aftermarketPhone == null ? null : aftermarketPhone.trim();
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", spuId=").append(spuId);
        sb.append(", spuCode=").append(spuCode);
        sb.append(", offeringCode=").append(offeringCode);
        sb.append(", offeringName=").append(offeringName);
        sb.append(", offeringStatus=").append(offeringStatus);
        sb.append(", offeringStatusTime=").append(offeringStatusTime);
        sb.append(", composition=").append(composition);
        sb.append(", model=").append(model);
        sb.append(", quantity=").append(quantity);
        sb.append(", size=").append(size);
        sb.append(", operType=").append(operType);
        sb.append(", recommendPrice=").append(recommendPrice);
        sb.append(", saleObject=").append(saleObject);
        sb.append(", price=").append(price);
        sb.append(", unit=").append(unit);
        sb.append(", marketName=").append(marketName);
        sb.append(", marketCode=").append(marketCode);
        sb.append(", supplierName=").append(supplierName);
        sb.append(", productType=").append(productType);
        sb.append(", receiveOrder=").append(receiveOrder);
        sb.append(", custCode=").append(custCode);
        sb.append(", custName=").append(custName);
        sb.append(", cardType=").append(cardType);
        sb.append(", mainOfferingCode=").append(mainOfferingCode);
        sb.append(", templateName=").append(templateName);
        sb.append(", templateId=").append(templateId);
        sb.append(", saleModel=").append(saleModel);
        sb.append(", project=").append(project);
        sb.append(", pointStatus=").append(pointStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", deleteTime=").append(deleteTime);
        sb.append(", packageType=").append(packageType);
        sb.append(", skuOfferingVersion=").append(skuOfferingVersion);
        sb.append(", spuOfferingVersion=").append(spuOfferingVersion);
        sb.append(", skuId=").append(skuId);
        sb.append(", skuAbbreviation=").append(skuAbbreviation);
        sb.append(", receiveOrderName=").append(receiveOrderName);
        sb.append(", receiveOrderPhone=").append(receiveOrderPhone);
        sb.append(", deliverName=").append(deliverName);
        sb.append(", deliverPhone=").append(deliverPhone);
        sb.append(", aftermarketName=").append(aftermarketName);
        sb.append(", aftermarketPhone=").append(aftermarketPhone);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SkuOfferingInfoHistory other = (SkuOfferingInfoHistory) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSpuId() == null ? other.getSpuId() == null : this.getSpuId().equals(other.getSpuId()))
            && (this.getSpuCode() == null ? other.getSpuCode() == null : this.getSpuCode().equals(other.getSpuCode()))
            && (this.getOfferingCode() == null ? other.getOfferingCode() == null : this.getOfferingCode().equals(other.getOfferingCode()))
            && (this.getOfferingName() == null ? other.getOfferingName() == null : this.getOfferingName().equals(other.getOfferingName()))
            && (this.getOfferingStatus() == null ? other.getOfferingStatus() == null : this.getOfferingStatus().equals(other.getOfferingStatus()))
            && (this.getOfferingStatusTime() == null ? other.getOfferingStatusTime() == null : this.getOfferingStatusTime().equals(other.getOfferingStatusTime()))
            && (this.getComposition() == null ? other.getComposition() == null : this.getComposition().equals(other.getComposition()))
            && (this.getModel() == null ? other.getModel() == null : this.getModel().equals(other.getModel()))
            && (this.getQuantity() == null ? other.getQuantity() == null : this.getQuantity().equals(other.getQuantity()))
            && (this.getSize() == null ? other.getSize() == null : this.getSize().equals(other.getSize()))
            && (this.getOperType() == null ? other.getOperType() == null : this.getOperType().equals(other.getOperType()))
            && (this.getRecommendPrice() == null ? other.getRecommendPrice() == null : this.getRecommendPrice().equals(other.getRecommendPrice()))
            && (this.getSaleObject() == null ? other.getSaleObject() == null : this.getSaleObject().equals(other.getSaleObject()))
            && (this.getPrice() == null ? other.getPrice() == null : this.getPrice().equals(other.getPrice()))
            && (this.getUnit() == null ? other.getUnit() == null : this.getUnit().equals(other.getUnit()))
            && (this.getMarketName() == null ? other.getMarketName() == null : this.getMarketName().equals(other.getMarketName()))
            && (this.getMarketCode() == null ? other.getMarketCode() == null : this.getMarketCode().equals(other.getMarketCode()))
            && (this.getSupplierName() == null ? other.getSupplierName() == null : this.getSupplierName().equals(other.getSupplierName()))
            && (this.getProductType() == null ? other.getProductType() == null : this.getProductType().equals(other.getProductType()))
            && (this.getReceiveOrder() == null ? other.getReceiveOrder() == null : this.getReceiveOrder().equals(other.getReceiveOrder()))
            && (this.getCustCode() == null ? other.getCustCode() == null : this.getCustCode().equals(other.getCustCode()))
            && (this.getCustName() == null ? other.getCustName() == null : this.getCustName().equals(other.getCustName()))
            && (this.getCardType() == null ? other.getCardType() == null : this.getCardType().equals(other.getCardType()))
            && (this.getMainOfferingCode() == null ? other.getMainOfferingCode() == null : this.getMainOfferingCode().equals(other.getMainOfferingCode()))
            && (this.getTemplateName() == null ? other.getTemplateName() == null : this.getTemplateName().equals(other.getTemplateName()))
            && (this.getTemplateId() == null ? other.getTemplateId() == null : this.getTemplateId().equals(other.getTemplateId()))
            && (this.getSaleModel() == null ? other.getSaleModel() == null : this.getSaleModel().equals(other.getSaleModel()))
            && (this.getProject() == null ? other.getProject() == null : this.getProject().equals(other.getProject()))
            && (this.getPointStatus() == null ? other.getPointStatus() == null : this.getPointStatus().equals(other.getPointStatus()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getDeleteTime() == null ? other.getDeleteTime() == null : this.getDeleteTime().equals(other.getDeleteTime()))
            && (this.getPackageType() == null ? other.getPackageType() == null : this.getPackageType().equals(other.getPackageType()))
            && (this.getSkuOfferingVersion() == null ? other.getSkuOfferingVersion() == null : this.getSkuOfferingVersion().equals(other.getSkuOfferingVersion()))
            && (this.getSpuOfferingVersion() == null ? other.getSpuOfferingVersion() == null : this.getSpuOfferingVersion().equals(other.getSpuOfferingVersion()))
            && (this.getSkuId() == null ? other.getSkuId() == null : this.getSkuId().equals(other.getSkuId()))
            && (this.getSkuAbbreviation() == null ? other.getSkuAbbreviation() == null : this.getSkuAbbreviation().equals(other.getSkuAbbreviation()))
            && (this.getReceiveOrderName() == null ? other.getReceiveOrderName() == null : this.getReceiveOrderName().equals(other.getReceiveOrderName()))
            && (this.getReceiveOrderPhone() == null ? other.getReceiveOrderPhone() == null : this.getReceiveOrderPhone().equals(other.getReceiveOrderPhone()))
            && (this.getDeliverName() == null ? other.getDeliverName() == null : this.getDeliverName().equals(other.getDeliverName()))
            && (this.getDeliverPhone() == null ? other.getDeliverPhone() == null : this.getDeliverPhone().equals(other.getDeliverPhone()))
            && (this.getAftermarketName() == null ? other.getAftermarketName() == null : this.getAftermarketName().equals(other.getAftermarketName()))
            && (this.getAftermarketPhone() == null ? other.getAftermarketPhone() == null : this.getAftermarketPhone().equals(other.getAftermarketPhone()));
    }

    /**
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSpuId() == null) ? 0 : getSpuId().hashCode());
        result = prime * result + ((getSpuCode() == null) ? 0 : getSpuCode().hashCode());
        result = prime * result + ((getOfferingCode() == null) ? 0 : getOfferingCode().hashCode());
        result = prime * result + ((getOfferingName() == null) ? 0 : getOfferingName().hashCode());
        result = prime * result + ((getOfferingStatus() == null) ? 0 : getOfferingStatus().hashCode());
        result = prime * result + ((getOfferingStatusTime() == null) ? 0 : getOfferingStatusTime().hashCode());
        result = prime * result + ((getComposition() == null) ? 0 : getComposition().hashCode());
        result = prime * result + ((getModel() == null) ? 0 : getModel().hashCode());
        result = prime * result + ((getQuantity() == null) ? 0 : getQuantity().hashCode());
        result = prime * result + ((getSize() == null) ? 0 : getSize().hashCode());
        result = prime * result + ((getOperType() == null) ? 0 : getOperType().hashCode());
        result = prime * result + ((getRecommendPrice() == null) ? 0 : getRecommendPrice().hashCode());
        result = prime * result + ((getSaleObject() == null) ? 0 : getSaleObject().hashCode());
        result = prime * result + ((getPrice() == null) ? 0 : getPrice().hashCode());
        result = prime * result + ((getUnit() == null) ? 0 : getUnit().hashCode());
        result = prime * result + ((getMarketName() == null) ? 0 : getMarketName().hashCode());
        result = prime * result + ((getMarketCode() == null) ? 0 : getMarketCode().hashCode());
        result = prime * result + ((getSupplierName() == null) ? 0 : getSupplierName().hashCode());
        result = prime * result + ((getProductType() == null) ? 0 : getProductType().hashCode());
        result = prime * result + ((getReceiveOrder() == null) ? 0 : getReceiveOrder().hashCode());
        result = prime * result + ((getCustCode() == null) ? 0 : getCustCode().hashCode());
        result = prime * result + ((getCustName() == null) ? 0 : getCustName().hashCode());
        result = prime * result + ((getCardType() == null) ? 0 : getCardType().hashCode());
        result = prime * result + ((getMainOfferingCode() == null) ? 0 : getMainOfferingCode().hashCode());
        result = prime * result + ((getTemplateName() == null) ? 0 : getTemplateName().hashCode());
        result = prime * result + ((getTemplateId() == null) ? 0 : getTemplateId().hashCode());
        result = prime * result + ((getSaleModel() == null) ? 0 : getSaleModel().hashCode());
        result = prime * result + ((getProject() == null) ? 0 : getProject().hashCode());
        result = prime * result + ((getPointStatus() == null) ? 0 : getPointStatus().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getDeleteTime() == null) ? 0 : getDeleteTime().hashCode());
        result = prime * result + ((getPackageType() == null) ? 0 : getPackageType().hashCode());
        result = prime * result + ((getSkuOfferingVersion() == null) ? 0 : getSkuOfferingVersion().hashCode());
        result = prime * result + ((getSpuOfferingVersion() == null) ? 0 : getSpuOfferingVersion().hashCode());
        result = prime * result + ((getSkuId() == null) ? 0 : getSkuId().hashCode());
        result = prime * result + ((getSkuAbbreviation() == null) ? 0 : getSkuAbbreviation().hashCode());
        result = prime * result + ((getReceiveOrderName() == null) ? 0 : getReceiveOrderName().hashCode());
        result = prime * result + ((getReceiveOrderPhone() == null) ? 0 : getReceiveOrderPhone().hashCode());
        result = prime * result + ((getDeliverName() == null) ? 0 : getDeliverName().hashCode());
        result = prime * result + ((getDeliverPhone() == null) ? 0 : getDeliverPhone().hashCode());
        result = prime * result + ((getAftermarketName() == null) ? 0 : getAftermarketName().hashCode());
        result = prime * result + ((getAftermarketPhone() == null) ? 0 : getAftermarketPhone().hashCode());
        return result;
    }

    /**
     * corresponding to the database table supply_chain..sku_offering_info_history
     *
     * @mbg.generated Fri Apr 25 16:34:24 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        spuId("spu_id", "spuId", "VARCHAR", false),
        spuCode("spu_code", "spuCode", "VARCHAR", false),
        offeringCode("offering_code", "offeringCode", "VARCHAR", false),
        offeringName("offering_name", "offeringName", "VARCHAR", false),
        offeringStatus("offering_status", "offeringStatus", "VARCHAR", false),
        offeringStatusTime("offering_status_time", "offeringStatusTime", "TIMESTAMP", false),
        composition("composition", "composition", "VARCHAR", false),
        model("model", "model", "VARCHAR", false),
        quantity("quantity", "quantity", "BIGINT", false),
        size("size", "size", "VARCHAR", false),
        operType("oper_type", "operType", "VARCHAR", false),
        recommendPrice("recommend_price", "recommendPrice", "BIGINT", false),
        saleObject("sale_object", "saleObject", "VARCHAR", false),
        price("price", "price", "BIGINT", false),
        unit("unit", "unit", "VARCHAR", false),
        marketName("market_name", "marketName", "VARCHAR", false),
        marketCode("market_code", "marketCode", "VARCHAR", false),
        supplierName("supplier_name", "supplierName", "VARCHAR", false),
        productType("product_type", "productType", "VARCHAR", false),
        receiveOrder("receive_order", "receiveOrder", "VARCHAR", false),
        custCode("cust_code", "custCode", "VARCHAR", false),
        custName("cust_name", "custName", "VARCHAR", false),
        cardType("card_type", "cardType", "VARCHAR", false),
        mainOfferingCode("main_offering_code", "mainOfferingCode", "VARCHAR", false),
        templateName("template_name", "templateName", "VARCHAR", false),
        templateId("template_id", "templateId", "VARCHAR", false),
        saleModel("sale_model", "saleModel", "VARCHAR", false),
        project("project", "project", "VARCHAR", false),
        pointStatus("point_status", "pointStatus", "INTEGER", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        deleteTime("delete_time", "deleteTime", "TIMESTAMP", false),
        packageType("package_type", "packageType", "VARCHAR", false),
        skuOfferingVersion("sku_offering_version", "skuOfferingVersion", "VARCHAR", false),
        spuOfferingVersion("spu_offering_version", "spuOfferingVersion", "VARCHAR", false),
        skuId("sku_id", "skuId", "VARCHAR", false),
        skuAbbreviation("sku_abbreviation", "skuAbbreviation", "VARCHAR", false),
        receiveOrderName("receive_order_name", "receiveOrderName", "VARCHAR", false),
        receiveOrderPhone("receive_order_phone", "receiveOrderPhone", "VARCHAR", false),
        deliverName("deliver_name", "deliverName", "VARCHAR", false),
        deliverPhone("deliver_phone", "deliverPhone", "VARCHAR", false),
        aftermarketName("aftermarket_name", "aftermarketName", "VARCHAR", false),
        aftermarketPhone("aftermarket_phone", "aftermarketPhone", "VARCHAR", false);

        /**
         * Corresponding to the database table supply_chain..sku_offering_info_history
         *
         * @mbg.generated Fri Apr 25 16:34:24 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..sku_offering_info_history
         *
         * @mbg.generated Fri Apr 25 16:34:24 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..sku_offering_info_history
         *
         * @mbg.generated Fri Apr 25 16:34:24 CST 2025
         */
        private final String column;

        /**
         * Corresponding to the database table supply_chain..sku_offering_info_history
         *
         * @mbg.generated Fri Apr 25 16:34:24 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         * Corresponding to the database table supply_chain..sku_offering_info_history
         *
         * @mbg.generated Fri Apr 25 16:34:24 CST 2025
         */
        private final String javaProperty;

        /**
         * Corresponding to the database table supply_chain..sku_offering_info_history
         *
         * @mbg.generated Fri Apr 25 16:34:24 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Fri Apr 25 16:34:24 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri Apr 25 16:34:24 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri Apr 25 16:34:24 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Fri Apr 25 16:34:24 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Fri Apr 25 16:34:24 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Fri Apr 25 16:34:24 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Fri Apr 25 16:34:24 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Fri Apr 25 16:34:24 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Fri Apr 25 16:34:24 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Fri Apr 25 16:34:24 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Fri Apr 25 16:34:24 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}