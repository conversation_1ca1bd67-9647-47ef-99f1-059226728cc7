package com.chinamobile.iot.sc.pojo.vo;

import com.chinamobile.iot.sc.pojo.entity.CardRelation;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/9
 * @description x终端详情展示类
 */
@Data
public class CardRelationXDetailVO extends CardRelation {

    private String id;

    /**
     * 终端类型
     */
    private String terminalTypeName;

    /**
     * 码号
     */
    private String msisdn;

    /**
     * 省份
     */
    private String provinceName;

    /**
     * 地市
     */
    private String cityName;

    /**
     * 销售状态
     */
    private String sellStatusName;

    /**
     * 售后服务订单信息
     */
    private List<CardAfterMarketOrderDetailVO> cardAfterMarketOrderDetailVO;

    /**
     * 商品订单信息
     */
    private CardOrderDetailVO cardOrderDetailVO;
}
