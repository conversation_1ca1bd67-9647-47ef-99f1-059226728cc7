package com.chinamobile.iot.sc;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * @Author: YSC
 * @Date: 2021/10/29 14:11
 * &#064;Description:
 */
//@EnableRetry
@EnableDiscoveryClient
@EnableAsync
@EnableScheduling
@EnableApolloConfig
@SpringBootApplication
@EnableFeignClients
@MapperScan(basePackages = "com.chinamobile.iot.sc.dao")
public class IOTApplication {
    public static void main(String[] args) {
        SpringApplication.run(IOTApplication.class,args);
    }
}
