package com.chinamobile.iot.sc.dao.handle;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.pojo.Order2cInfo;
import com.chinamobile.iot.sc.pojo.UnionSellOrderExcelHistory;
import com.chinamobile.iot.sc.pojo.dto.AtomOfferingInfoByAtomOrderIdDTO;
import com.chinamobile.iot.sc.pojo.handle.OrderExportHandle;
import com.chinamobile.iot.sc.pojo.handle.OrderInfoDetailHandle;
import com.chinamobile.iot.sc.pojo.handle.OrderInfoHandle;
import com.chinamobile.iot.sc.pojo.mapper.DeliveryNotesDO;
import com.chinamobile.iot.sc.pojo.mapper.OrderIdAtomOrderIdDO;
import com.chinamobile.iot.sc.pojo.mapper.UnionSellOrderExportDO;
import com.chinamobile.iot.sc.pojo.param.OrderListBacklogQueryParam;
import com.chinamobile.iot.sc.pojo.param.OrderListQueryParam;
import com.chinamobile.iot.sc.pojo.param.UnionSellOrderListQueryParam;
import com.chinamobile.iot.sc.pojo.vo.OrderSalesReportVO;
import com.chinamobile.iot.sc.response.web.good.AtomOrderInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/11/12 13:58
 * @Description:
 */
@Mapper
public interface OrderHandleMapper {
    List<OrderInfoHandle> selectOrderListByHandle(OrderListQueryParam param);


    long countOrderList(OrderListQueryParam param);


    OrderInfoDetailHandle selectOrderDetailByHandle(@Param("id")String id);

    /**
     * 根据订单ID获取订单详情
     * @param orderId
     * @return
     */
    List<OrderInfoDetailHandle> selectOrderDetailByOrderId(@Param("orderId")String orderId);

    /**
     * 根据原子订单ID获取原子商品相关信息
     * @param orderId
     * @return
     */
    List<AtomOfferingInfoByAtomOrderIdDTO> listAtomOfferingInfoByAtomOrderId(@Param("orderId")String orderId);

    List<AtomOrderInfo> selectGoodList(@Param("orderId") String orderId);


    List<OrderExportHandle> selectOrderExportListByHandle(@Param("userIdList") List<String> userIdList,
                                                          String orderId,
                                                          Integer orderStatus,
                                                          String startTime,
                                                          String endTime,
                                                          String spuOfferingClass,
                                                          String skuOfferingName,
                                                          String skuOfferingCode,
                                                          String atomOfferingName,
                                                          Integer rocStatus,
                                                          String receiverPhone,
                                                          String businessCode,
                                                          String roleType,
                                                          Date finishStartTime,
                                                          Date finishEndTime,
                                                          Integer specialAfterMarketHandle,
                                                          String specialAfterStatus, String orderType);

    long countOrderListWithoutUser(OrderListQueryParam param);

    List<OrderInfoHandle> selectOrderListByHandleWithoutUser(OrderListQueryParam param);

    /**
     * 用于订单待办查询
     * @param param
     * @return
     */
    List<OrderInfoHandle> selectOrderListByHandleToBacklog(@Param(value = "page") Page page,
                                                           @Param(value = "param") OrderListBacklogQueryParam param);

    /**
     * 查询订单商城销售报表
     * @param beId
     * @param startTime
     * @param endTime
     * @return
     */
   List<OrderSalesReportVO> selectOrderStoreSalesReport(String beId, String startTime, String endTime);

    List<UnionSellOrderExportDO> selectUnionSellOrderExport(String startTime, String endTime);
    List<UnionSellOrderExcelHistory> selectUnionSellOrderList(UnionSellOrderListQueryParam param);

    List<UnionSellOrderExcelHistory> selectUnionSellOrderInfo(List<String> dateList);

    List<Order2cInfo> findNoStatusTimeOrder(Integer pageSize);

    void initValetOrderCompleteTime();

    void initValetOrderCompleteTimeAtom();

    List<OrderInfoDetailHandle> selectOrderDetailForOpen(@Param("page") Integer page, @Param("num") Integer num,
                                                  @Param("userIdList") List<String> userIdList, String orderId, String startTime,
                                                  String endTime, @Param("orderStatus") List<Integer> orderStatus, String spuOfferingName,
                                                  String spuOfferingCode, String skuOfferingName, String skuOfferingCode,
                                                  String atomOfferingCode, String atomOfferingName, String partnerName, String cooperatorName,
                                                  String phone, Integer specialAfterMarketHandle, String specialAfterStatus,
                                                  String spuOfferingClass, String roleType, String orderType,Integer qlyStatus,
                                                  String h5Key,Integer h5Status,List<String> h5SpuOfferingClasses,Integer softServiceStatus,
                                                         String clusterCode,String orderingChannelSource,String encryptCustCode);
    long countOrderForOpen(@Param("userIdList") List<String> userIdList, String orderId, String startTime,
                           String endTime, @Param("orderStatus") List<Integer> orderStatus, String spuOfferingName,
                           String spuOfferingCode, String skuOfferingName, String skuOfferingCode,
                           String atomOfferingCode, String atomOfferingName, String partnerName, String cooperatorName,
                           String phone, Integer specialAfterMarketHandle, String specialAfterStatus,
                           String spuOfferingClass, String roleType, String orderType,Integer qlyStatus,
                           String h5Key,Integer h5Status,List<String> h5SpuOfferingClasses,Integer softServiceStatus,
                           String clusterCode,String orderingChannelSource,String encryptCustCode);

    DeliveryNotesDO selectCardxDeliveryNotesInfo(String atomOrderId);

    List<OrderIdAtomOrderIdDO> getOrderIdAndAtomOrderIdList(@Param("orderStatusList") List<Integer> orderStatusList,
                                                            String beId,
                                                            String location,
                                                            @Param("userIdList") List<String> userIdList);
}
