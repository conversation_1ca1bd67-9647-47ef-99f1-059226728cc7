package com.chinamobile.iot.sc.request.invoice;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;

/**
 * @package: com.chinamobile.iot.sc.request.invoice
 * @ClassName: Request4InvoRecPage
 * @description: 申请开发票记录-分页请求
 * @author: zyj
 * @create: 2021/12/9 19:46
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
public class Request4InvoRecPage extends BasePageQuery {
    //开票订单号(申请id)
    private String invoiceApplyId;
    //业务订单id
    private String orderId;
    //订单状态
    private Integer status;

    /**
     * 请求流水号
     */
    private String orderSeq;

}
