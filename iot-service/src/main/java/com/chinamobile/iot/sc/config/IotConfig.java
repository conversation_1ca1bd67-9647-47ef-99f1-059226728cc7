package com.chinamobile.iot.sc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * created by l<PERSON>xiang on 2023/11/22 11:03
 */
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix="iot")
public class IotConfig {

    private Boolean checkDictInventory = true;
}
