package com.chinamobile.iot.sc.controller.open;

import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.interceptor.AuthorityInterceptor;
import com.chinamobile.iot.sc.common.utils.BaseUtils;
import com.chinamobile.iot.sc.config.SupplierInfoMapConfig;
import com.chinamobile.iot.sc.dao.Order2cAtomInfoMapper;
import com.chinamobile.iot.sc.mode.*;
import com.chinamobile.iot.sc.mode.OpenAbilityAppRO;
import com.chinamobile.iot.sc.mode.OpenAbilityOrganizationRO;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.CyzqListParam;
import com.chinamobile.iot.sc.pojo.vo.CyzqListVO;
import com.chinamobile.iot.sc.response.web.Order2CInfoDetailDTO;
import com.chinamobile.iot.sc.service.IOrder2CService;
import com.chinamobile.iot.sc.service.OpenOrderService;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/11/9 14:42
 * @Description:
 */
@RestController
@RequestMapping("/open/order")
@Slf4j
public class OpenOrderController {
    @Autowired
    private IOrder2CService iOrder2CService;

    @Resource
    private OpenOrderService openOrderService;


    /**
     * 商品订单查询
     *
     * @param orderStatus 订单状态 0待发货 1待收货 2已完成
     * @param page
     * @param num
     * @return
     */
    @GetMapping("/list")
    @Auth(authCode = {BaseConstant.IOT_ORDER_QUERY})
    public BaseAnswer<String> getOrderList(
            @RequestParam(value = "orderId", required = false) String orderId,
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "orderStatus", required = false) List<Integer> orderStatus,
            @RequestParam(value = "spuOfferingClass", required = false) String spuOfferingClass,
            @RequestParam(value = "spuOfferingName", required = false) String spuOfferingName,
            @RequestParam(value = "spuOfferingCode", required = false) String spuOfferingCode,
            @RequestParam(value = "skuOfferingName", required = false) String skuOfferingName,
            @RequestParam(value = "skuOfferingCode", required = false) String skuOfferingCode,
            @RequestParam(value = "atomOfferingCode", required = false) String atomOfferingCode,
            @RequestParam(value = "atomOfferingName", required = false) String atomOfferingName,
            @RequestParam(value = "partnerName", required = false) String partnerName,
            @RequestParam(value = "cooperatorName", required = false) String cooperatorName,
            @RequestParam(value = "phone", required = false) String phone,
            @RequestParam(value = "specialAfterMarketHandle", required = false) Integer specialAfterMarketHandle,
            @RequestParam(value = "specialAfterStatus", required = false) String specialAfterStatus,
            @RequestParam(value = "orderType", required = false) String orderType,
            @RequestParam(value = "qlyStatus", required = false) Integer qlyStatus,
            @RequestParam(value = "h5Key", required = false) String h5Key,
            @RequestParam(value = "h5Status", required = false) Integer h5Status,
            @RequestParam(value = "h5SpuOfferingClasses", required = false) List<String> h5SpuOfferingClasses,
            @RequestParam(value = "softServiceStatus", required = false) Integer softServiceStatus,
            @RequestParam(value = "clusterCode", required = false) String clusterCode,
            @RequestParam(value = "orderingChannelSource", required = false) String orderingChannelSource,
            @RequestParam(value = "custPhone", required = false) String custPhone,
            @RequestParam("page") Integer page,
            @RequestParam("num") Integer num,
            @RequestAttribute(Constant.ATTR_KEY_OPEN_APP_INFO) OpenAbilityAppRO appRO,
            @RequestAttribute(Constant.ATTR_KEY_OPEN_ORGANIZATION_INFO) OpenAbilityOrganizationRO organizationRO) {
        //暂特殊处理，后续处理为通用手段
        if (StringUtils.equals(organizationRO.getName(),"商客部")) {
            String appid = appRO.getId();
            String name = appRO.getName();
            log.info("商客部开放能力，appid = {}, name = {}",appid, name);
            if("1371425547814957056".equals(appid)){
                //湖南移智商企
                orderingChannelSource = "037022";
            }else{
                orderingChannelSource = "016035";
            }
        }
        PageData<Order2CInfoDetailDTO> result = iOrder2CService.getOrderListForOpen(orderId, startTime, endTime, orderStatus, spuOfferingClass, spuOfferingName,
                spuOfferingCode, skuOfferingName, skuOfferingCode, atomOfferingCode, atomOfferingName, partnerName,
                cooperatorName, phone, specialAfterMarketHandle, specialAfterStatus, orderType, qlyStatus, h5Key, h5Status,
                h5SpuOfferingClasses, softServiceStatus, clusterCode,orderingChannelSource,custPhone, page, num);
        if (CollectionUtils.isNotEmpty(result.getData())) {
            result.getData().forEach(order-> {
                try {
                    order.setContactPhone(BaseUtils.aesEncrypt(order.getContactPhone(),organizationRO.getSecret()));
                    order.setContactPersonName(BaseUtils.aesEncrypt(order.getContactPersonName(),organizationRO.getSecret()));
                    order.setAddr1(BaseUtils.aesEncrypt(order.getAddr1(),organizationRO.getSecret()));
                    order.setAddr2(BaseUtils.aesEncrypt(order.getAddr2(),organizationRO.getSecret()));
                    if (StringUtils.isNotEmpty(order.getAddr3())) {
                        order.setAddr3(BaseUtils.aesEncrypt(order.getAddr3(), organizationRO.getSecret()));
                    }
                    if (StringUtils.isNotEmpty(order.getAddr4())) {
                        order.setAddr4(BaseUtils.aesEncrypt(order.getAddr4(), organizationRO.getSecret()));
                    }
                    if (StringUtils.isNotEmpty(order.getUsaddr())) {
                        order.setUsaddr(BaseUtils.aesEncrypt(order.getUsaddr(), organizationRO.getSecret()));
                    }
                } catch (Exception e) {
                    log.info("/open/order/list加密失败");
                }

            });
        }
        return BaseAnswer.success(result, organizationRO.getApiKey());
    }

    /**
     * 云南彩云智企商客产品订单数据查询
     * @param cyzqListParam
     * @return
     */
    @GetMapping(value = "/cyzq/list")
    public BaseAnswer<PageData<CyzqListVO>> pageCyzqList(CyzqListParam cyzqListParam,/*
                                                         @RequestAttribute(Constant.ATTR_KEY_OPEN_APP_INFO) OpenAbilityAppRO appRO,*/
                                                         @RequestAttribute(Constant.ATTR_KEY_OPEN_ORGANIZATION_INFO) OpenAbilityOrganizationRO organizationRO){
        BaseAnswer<PageData<CyzqListVO>> baseAnswer = new BaseAnswer<>();
        PageData<CyzqListVO> pageData = openOrderService.pageCyzqList(cyzqListParam, organizationRO);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }

}
