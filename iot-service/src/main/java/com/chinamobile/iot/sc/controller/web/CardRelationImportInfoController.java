package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.CardRelationImportInfoParam;
import com.chinamobile.iot.sc.pojo.vo.CardRelationImportInfoVO;
import com.chinamobile.iot.sc.service.CardRelationImportInfoService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/13
 * @description 卡+X终端导入批次相关信息controller类
 */
@RestController
@RequestMapping(value = "/osweb/card")
public class CardRelationImportInfoController {

    @Resource
    private CardRelationImportInfoService cardRelationImportInfoService;

    /**
     * 分页获取卡+X终端导入批次相关信息
     * @param cardRelationImportInfoParam
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping(value = "/pageCardRelationImportInfo")
    public BaseAnswer<PageData<CardRelationImportInfoVO>> pageCardRelationImportInfo(CardRelationImportInfoParam cardRelationImportInfoParam,
                                                                            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {

        BaseAnswer baseAnswer = new BaseAnswer();
        PageData<CardRelationImportInfoVO> pageData = cardRelationImportInfoService.pageCardRelationImportInfo(cardRelationImportInfoParam, loginIfo4Redis);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }

}
