package com.chinamobile.iot.sc.controller.gioBurialPoint;

import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.service.GioBurialPointService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;

@RestController
@RequestMapping("/os/gio")
public class GioBurialPointController {
    @Resource
    GioBurialPointService gioBurialPointService;

    /**
     * 用户属性生成
     * 
     * @return
     */
    @GetMapping("/getuserInfo")
    public void getuserInfo() throws IOException {
        gioBurialPointService.getUserInfo();
    }

    /**
     * sku商品维度表生成
     * 
     * @return
     */
    @GetMapping("/getSkuMsg")
    public void getSkuMsg() throws IOException {
        gioBurialPointService.getSkuMsg();
    }

    /**
     * 原子维度表生成
     * 
     * @return
     */
    @GetMapping("/getAtomMsg")
    public void getAtomMsg() throws IOException {
        gioBurialPointService.getAtomMsg();
    }

    /**
     * 订单维度表生成
     * 
     * @return
     */
    @GetMapping("/getOrderMsg")
    public void getOrderMsg(@RequestParam String startTime,@RequestParam String endTime) throws IOException, ParseException {
        gioBurialPointService.getOrderMsg(startTime,endTime);
    }

    /**
     * 订单维度事件H5_orderCreated_HD生成
     * 
     * @return
     */
    @GetMapping("/getOrderCreated")
    public void getOrderCreated(@RequestParam String startTime,@RequestParam String endTime)
            throws IOException, ParseException {
        gioBurialPointService.getOrderCreated(startTime,endTime);
    }

    /**
     * 订单维度事件H5_orderPndingInvoice生成
     *
     * @return
     */
    @GetMapping("/getOrderPnding")
    public void getOrderPnding(@RequestParam String startTime, @RequestParam String endTime)
            throws IOException, ParseException {
        gioBurialPointService.getOrderPnding(startTime, endTime);
    }

    /**
     * 订单维度事件H5_prudctOrderPndingInvoice生成
     *
     * @return
     */
    @GetMapping("/getPrudctOrderPnding")
    public void getPrudctOrderPnding(@RequestParam String startTime, @RequestParam String endTime)
            throws IOException, ParseException {
        gioBurialPointService.getPrudctOrderPnding(startTime, endTime);
    }

    /**
     * 订单维度事件H5_ProductOrderCreate生成
     * 
     * @return
     */
    @GetMapping("/getProductOrderCreate")
    public void getProductOrderCreate(@RequestParam String startTime,@RequestParam String endTime)
            throws IOException, ParseException {
        gioBurialPointService.getProductOrderCreate(startTime,endTime);
    }

    /**
     * 订单维度事件H5_productLaunch生成
     * 
     * @return
     */
    @GetMapping("/getH5ProductLaunch")
    public void getH5ProductLaunch(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis)
            throws IOException, ParseException {
        gioBurialPointService.getH5ProductLaunch(loginIfo4Redis.getUserId());
    }
    /**
     * 用户事件H5_userregistration生成
     *
     * @return
     */
    @GetMapping("/getH5Userregistration")
    public void getH5Userregistration(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis)
            throws IOException, ParseException {
        gioBurialPointService.getH5Userregistration(loginIfo4Redis.getUserId());
    }

}
