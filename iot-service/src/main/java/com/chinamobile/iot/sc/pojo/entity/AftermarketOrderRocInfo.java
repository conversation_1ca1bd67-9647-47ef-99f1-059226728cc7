package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class AftermarketOrderRocInfo implements Serializable {
    private String refundServOrderId;

    private String servOrderId;

    private String orderId;

    private String refundsType;

    private String reason;

    private String remark;

    private String picture;

    private String pictureOuterUrl;

    private Integer originalStatus;

    private Integer innerStatus;

    private String auditId;

    private String auditResult;

    private String auditResultReason;

    private Date createTime;

    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public String getRefundServOrderId() {
        return refundServOrderId;
    }

    public AftermarketOrderRocInfo withRefundServOrderId(String refundServOrderId) {
        this.setRefundServOrderId(refundServOrderId);
        return this;
    }

    public void setRefundServOrderId(String refundServOrderId) {
        this.refundServOrderId = refundServOrderId == null ? null : refundServOrderId.trim();
    }

    public String getServOrderId() {
        return servOrderId;
    }

    public AftermarketOrderRocInfo withServOrderId(String servOrderId) {
        this.setServOrderId(servOrderId);
        return this;
    }

    public void setServOrderId(String servOrderId) {
        this.servOrderId = servOrderId == null ? null : servOrderId.trim();
    }

    public String getOrderId() {
        return orderId;
    }

    public AftermarketOrderRocInfo withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    public String getRefundsType() {
        return refundsType;
    }

    public AftermarketOrderRocInfo withRefundsType(String refundsType) {
        this.setRefundsType(refundsType);
        return this;
    }

    public void setRefundsType(String refundsType) {
        this.refundsType = refundsType == null ? null : refundsType.trim();
    }

    public String getReason() {
        return reason;
    }

    public AftermarketOrderRocInfo withReason(String reason) {
        this.setReason(reason);
        return this;
    }

    public void setReason(String reason) {
        this.reason = reason == null ? null : reason.trim();
    }

    public String getRemark() {
        return remark;
    }

    public AftermarketOrderRocInfo withRemark(String remark) {
        this.setRemark(remark);
        return this;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getPicture() {
        return picture;
    }

    public AftermarketOrderRocInfo withPicture(String picture) {
        this.setPicture(picture);
        return this;
    }

    public void setPicture(String picture) {
        this.picture = picture == null ? null : picture.trim();
    }

    public String getPictureOuterUrl() {
        return pictureOuterUrl;
    }

    public AftermarketOrderRocInfo withPictureOuterUrl(String pictureOuterUrl) {
        this.setPictureOuterUrl(pictureOuterUrl);
        return this;
    }

    public void setPictureOuterUrl(String pictureOuterUrl) {
        this.pictureOuterUrl = pictureOuterUrl == null ? null : pictureOuterUrl.trim();
    }

    public Integer getOriginalStatus() {
        return originalStatus;
    }

    public AftermarketOrderRocInfo withOriginalStatus(Integer originalStatus) {
        this.setOriginalStatus(originalStatus);
        return this;
    }

    public void setOriginalStatus(Integer originalStatus) {
        this.originalStatus = originalStatus;
    }

    public Integer getInnerStatus() {
        return innerStatus;
    }

    public AftermarketOrderRocInfo withInnerStatus(Integer innerStatus) {
        this.setInnerStatus(innerStatus);
        return this;
    }

    public void setInnerStatus(Integer innerStatus) {
        this.innerStatus = innerStatus;
    }

    public String getAuditId() {
        return auditId;
    }

    public AftermarketOrderRocInfo withAuditId(String auditId) {
        this.setAuditId(auditId);
        return this;
    }

    public void setAuditId(String auditId) {
        this.auditId = auditId == null ? null : auditId.trim();
    }

    public String getAuditResult() {
        return auditResult;
    }

    public AftermarketOrderRocInfo withAuditResult(String auditResult) {
        this.setAuditResult(auditResult);
        return this;
    }

    public void setAuditResult(String auditResult) {
        this.auditResult = auditResult == null ? null : auditResult.trim();
    }

    public String getAuditResultReason() {
        return auditResultReason;
    }

    public AftermarketOrderRocInfo withAuditResultReason(String auditResultReason) {
        this.setAuditResultReason(auditResultReason);
        return this;
    }

    public void setAuditResultReason(String auditResultReason) {
        this.auditResultReason = auditResultReason == null ? null : auditResultReason.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public AftermarketOrderRocInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public AftermarketOrderRocInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", refundServOrderId=").append(refundServOrderId);
        sb.append(", servOrderId=").append(servOrderId);
        sb.append(", orderId=").append(orderId);
        sb.append(", refundsType=").append(refundsType);
        sb.append(", reason=").append(reason);
        sb.append(", remark=").append(remark);
        sb.append(", picture=").append(picture);
        sb.append(", pictureOuterUrl=").append(pictureOuterUrl);
        sb.append(", originalStatus=").append(originalStatus);
        sb.append(", innerStatus=").append(innerStatus);
        sb.append(", auditId=").append(auditId);
        sb.append(", auditResult=").append(auditResult);
        sb.append(", auditResultReason=").append(auditResultReason);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        AftermarketOrderRocInfo other = (AftermarketOrderRocInfo) that;
        return (this.getRefundServOrderId() == null ? other.getRefundServOrderId() == null : this.getRefundServOrderId().equals(other.getRefundServOrderId()))
            && (this.getServOrderId() == null ? other.getServOrderId() == null : this.getServOrderId().equals(other.getServOrderId()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getRefundsType() == null ? other.getRefundsType() == null : this.getRefundsType().equals(other.getRefundsType()))
            && (this.getReason() == null ? other.getReason() == null : this.getReason().equals(other.getReason()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getPicture() == null ? other.getPicture() == null : this.getPicture().equals(other.getPicture()))
            && (this.getPictureOuterUrl() == null ? other.getPictureOuterUrl() == null : this.getPictureOuterUrl().equals(other.getPictureOuterUrl()))
            && (this.getOriginalStatus() == null ? other.getOriginalStatus() == null : this.getOriginalStatus().equals(other.getOriginalStatus()))
            && (this.getInnerStatus() == null ? other.getInnerStatus() == null : this.getInnerStatus().equals(other.getInnerStatus()))
            && (this.getAuditId() == null ? other.getAuditId() == null : this.getAuditId().equals(other.getAuditId()))
            && (this.getAuditResult() == null ? other.getAuditResult() == null : this.getAuditResult().equals(other.getAuditResult()))
            && (this.getAuditResultReason() == null ? other.getAuditResultReason() == null : this.getAuditResultReason().equals(other.getAuditResultReason()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getRefundServOrderId() == null) ? 0 : getRefundServOrderId().hashCode());
        result = prime * result + ((getServOrderId() == null) ? 0 : getServOrderId().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getRefundsType() == null) ? 0 : getRefundsType().hashCode());
        result = prime * result + ((getReason() == null) ? 0 : getReason().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getPicture() == null) ? 0 : getPicture().hashCode());
        result = prime * result + ((getPictureOuterUrl() == null) ? 0 : getPictureOuterUrl().hashCode());
        result = prime * result + ((getOriginalStatus() == null) ? 0 : getOriginalStatus().hashCode());
        result = prime * result + ((getInnerStatus() == null) ? 0 : getInnerStatus().hashCode());
        result = prime * result + ((getAuditId() == null) ? 0 : getAuditId().hashCode());
        result = prime * result + ((getAuditResult() == null) ? 0 : getAuditResult().hashCode());
        result = prime * result + ((getAuditResultReason() == null) ? 0 : getAuditResultReason().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    public enum Column {
        refundServOrderId("refund_serv_order_id", "refundServOrderId", "VARCHAR", false),
        servOrderId("serv_order_id", "servOrderId", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        refundsType("refunds_type", "refundsType", "VARCHAR", false),
        reason("reason", "reason", "VARCHAR", false),
        remark("remark", "remark", "VARCHAR", false),
        picture("picture", "picture", "VARCHAR", false),
        pictureOuterUrl("picture_outer_url", "pictureOuterUrl", "VARCHAR", false),
        originalStatus("original_status", "originalStatus", "INTEGER", false),
        innerStatus("inner_status", "innerStatus", "INTEGER", false),
        auditId("audit_id", "auditId", "VARCHAR", false),
        auditResult("audit_result", "auditResult", "VARCHAR", false),
        auditResultReason("audit_result_reason", "auditResultReason", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}