package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 原子商品
offeringClass=A06时，必传;
 *
 * <AUTHOR>
public class AtomOfferingInfoHistory implements Serializable {
    /**
     * 原子商品主键ID
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.id
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String id;

    /**
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.spu_id
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String spuId;

    /**
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.spu_code
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String spuCode;

    /**
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.sku_id
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String skuId;

    /**
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.sku_code
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String skuCode;

    /**
     * 原子商品编码
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.offering_code
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String offeringCode;

    /**
     * 原子商品名称
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.offering_name
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String offeringName;

    /**
     * 原子商品类型\\nS：软件功能费\\nH：硬件（代销) ， O：OneNET独立服务，D：（DICT）产品增值服务包 ,K：OneTraffic独立服务 C:卡资费 X：（卡+X类）硬件
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.offering_class
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String offeringClass;

    /**
     * 当前sku下配置的原子商品数量
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.quantity
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private Long quantity;

    /**
     * 平台软件编码
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.ext_soft_offering_code
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String extSoftOfferingCode;

    /**
     * 终端物料编码
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.ext_hard_offering_code
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String extHardOfferingCode;

    /**
     * 原子商品结算单价
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.settle_price
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private Long settlePrice;

    /**
     * 账目项ID
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.charge_code
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String chargeCode;

    /**
     * 账目ID
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.charge_id
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String chargeId;

    /**
     * 颜色
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.color
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String color;

    /**
     * 型号
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.model
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String model;

    /**
     * 计量单位
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.unit
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String unit;

    /**
     * 原子商品销售目录价
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.atom_sale_price
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private Long atomSalePrice;

    /**
     * 库存数量
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.inventory
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private Long inventory;

    /**
     * 预占库存
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.reserve_inventory
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private Long reserveInventory;

    /**
     * 是否配置库存
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.is_inventory
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private Boolean isInventory;

    /**
     * 合作伙伴ID
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.cooperator_id
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String cooperatorId;

    /**
     * 库存预警值
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.inventory_threshold
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private Long inventoryThreshold;

    /**
     * 是否短信告警
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.is_notice
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private Boolean isNotice;

    /**
     * 全量配置时间
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.config_all_time
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private Date configAllTime;

    /**
     * 配置合作伙伴时间
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.config_time
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private Date configTime;

    /**
     * 创建时间
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.create_time
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private Date createTime;

    /**
     * 更新时间
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.update_time
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private Date updateTime;

    /**
     * 是否已经发送短信通知 true 已发送 false未发送
如果添加了库存则修改此状态为未发送
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.notified
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private Boolean notified;

    /**
     * 销售省市/区域
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.offeringSaleRegion
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String offeringsaleregion;

    /**
     * 原子商品结算单价（专合）
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.settlePricePartner
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String settlepricepartner;

    /**
     * 结算明细服务名称
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.settleServiceName
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String settleservicename;

    /**
     * CMIOT关联商品
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.associated_offer
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String associatedOffer;

    /**
     * 资费有效期,需带单位，例如：3月、180天
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.validity_period
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String validityPeriod;

    /**
     * 删除时间
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.delete_time
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private Date deleteTime;

    /**
     * 接单方式  1--OS接单   2--省内接单
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.get_order_way
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private Integer getOrderWay;

    /**
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.atom_offering_version
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String atomOfferingVersion;

    /**
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.sku_offering_version
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String skuOfferingVersion;

    /**
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.spu_offering_version
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String spuOfferingVersion;

    /**
     * 专合结算价
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.special_cooperative_settlement_price
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String specialCooperativeSettlementPrice;

    /**
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.atom_id
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String atomId;

    /**
     * 产品类型
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.product_type
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String productType;

    /**
     * 是否为含卡终端。1：是 , 2：否 ；当offeringClass=A11卡+X、X产品类型（productType）=4/5/6/7/8/9/10/11时必传，其他不传
     *
     * Corresponding to the database column supply_chain..atom_offering_info_history.card_containing_terminal
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private String cardContainingTerminal;

    /**
     * Corresponding to the database table supply_chain..atom_offering_info_history
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.id
     *
     * @return the value of supply_chain..atom_offering_info_history.id
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.id
     *
     * @param id the value for supply_chain..atom_offering_info_history.id
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.spu_id
     *
     * @return the value of supply_chain..atom_offering_info_history.spu_id
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getSpuId() {
        return spuId;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withSpuId(String spuId) {
        this.setSpuId(spuId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.spu_id
     *
     * @param spuId the value for supply_chain..atom_offering_info_history.spu_id
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setSpuId(String spuId) {
        this.spuId = spuId == null ? null : spuId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.spu_code
     *
     * @return the value of supply_chain..atom_offering_info_history.spu_code
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getSpuCode() {
        return spuCode;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withSpuCode(String spuCode) {
        this.setSpuCode(spuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.spu_code
     *
     * @param spuCode the value for supply_chain..atom_offering_info_history.spu_code
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setSpuCode(String spuCode) {
        this.spuCode = spuCode == null ? null : spuCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.sku_id
     *
     * @return the value of supply_chain..atom_offering_info_history.sku_id
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getSkuId() {
        return skuId;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withSkuId(String skuId) {
        this.setSkuId(skuId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.sku_id
     *
     * @param skuId the value for supply_chain..atom_offering_info_history.sku_id
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setSkuId(String skuId) {
        this.skuId = skuId == null ? null : skuId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.sku_code
     *
     * @return the value of supply_chain..atom_offering_info_history.sku_code
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getSkuCode() {
        return skuCode;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withSkuCode(String skuCode) {
        this.setSkuCode(skuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.sku_code
     *
     * @param skuCode the value for supply_chain..atom_offering_info_history.sku_code
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode == null ? null : skuCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.offering_code
     *
     * @return the value of supply_chain..atom_offering_info_history.offering_code
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getOfferingCode() {
        return offeringCode;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withOfferingCode(String offeringCode) {
        this.setOfferingCode(offeringCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.offering_code
     *
     * @param offeringCode the value for supply_chain..atom_offering_info_history.offering_code
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setOfferingCode(String offeringCode) {
        this.offeringCode = offeringCode == null ? null : offeringCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.offering_name
     *
     * @return the value of supply_chain..atom_offering_info_history.offering_name
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getOfferingName() {
        return offeringName;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withOfferingName(String offeringName) {
        this.setOfferingName(offeringName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.offering_name
     *
     * @param offeringName the value for supply_chain..atom_offering_info_history.offering_name
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setOfferingName(String offeringName) {
        this.offeringName = offeringName == null ? null : offeringName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.offering_class
     *
     * @return the value of supply_chain..atom_offering_info_history.offering_class
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getOfferingClass() {
        return offeringClass;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withOfferingClass(String offeringClass) {
        this.setOfferingClass(offeringClass);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.offering_class
     *
     * @param offeringClass the value for supply_chain..atom_offering_info_history.offering_class
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setOfferingClass(String offeringClass) {
        this.offeringClass = offeringClass == null ? null : offeringClass.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.quantity
     *
     * @return the value of supply_chain..atom_offering_info_history.quantity
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public Long getQuantity() {
        return quantity;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withQuantity(Long quantity) {
        this.setQuantity(quantity);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.quantity
     *
     * @param quantity the value for supply_chain..atom_offering_info_history.quantity
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.ext_soft_offering_code
     *
     * @return the value of supply_chain..atom_offering_info_history.ext_soft_offering_code
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getExtSoftOfferingCode() {
        return extSoftOfferingCode;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withExtSoftOfferingCode(String extSoftOfferingCode) {
        this.setExtSoftOfferingCode(extSoftOfferingCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.ext_soft_offering_code
     *
     * @param extSoftOfferingCode the value for supply_chain..atom_offering_info_history.ext_soft_offering_code
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setExtSoftOfferingCode(String extSoftOfferingCode) {
        this.extSoftOfferingCode = extSoftOfferingCode == null ? null : extSoftOfferingCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.ext_hard_offering_code
     *
     * @return the value of supply_chain..atom_offering_info_history.ext_hard_offering_code
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getExtHardOfferingCode() {
        return extHardOfferingCode;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withExtHardOfferingCode(String extHardOfferingCode) {
        this.setExtHardOfferingCode(extHardOfferingCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.ext_hard_offering_code
     *
     * @param extHardOfferingCode the value for supply_chain..atom_offering_info_history.ext_hard_offering_code
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setExtHardOfferingCode(String extHardOfferingCode) {
        this.extHardOfferingCode = extHardOfferingCode == null ? null : extHardOfferingCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.settle_price
     *
     * @return the value of supply_chain..atom_offering_info_history.settle_price
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public Long getSettlePrice() {
        return settlePrice;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withSettlePrice(Long settlePrice) {
        this.setSettlePrice(settlePrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.settle_price
     *
     * @param settlePrice the value for supply_chain..atom_offering_info_history.settle_price
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setSettlePrice(Long settlePrice) {
        this.settlePrice = settlePrice;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.charge_code
     *
     * @return the value of supply_chain..atom_offering_info_history.charge_code
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getChargeCode() {
        return chargeCode;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withChargeCode(String chargeCode) {
        this.setChargeCode(chargeCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.charge_code
     *
     * @param chargeCode the value for supply_chain..atom_offering_info_history.charge_code
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setChargeCode(String chargeCode) {
        this.chargeCode = chargeCode == null ? null : chargeCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.charge_id
     *
     * @return the value of supply_chain..atom_offering_info_history.charge_id
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getChargeId() {
        return chargeId;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withChargeId(String chargeId) {
        this.setChargeId(chargeId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.charge_id
     *
     * @param chargeId the value for supply_chain..atom_offering_info_history.charge_id
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setChargeId(String chargeId) {
        this.chargeId = chargeId == null ? null : chargeId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.color
     *
     * @return the value of supply_chain..atom_offering_info_history.color
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getColor() {
        return color;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withColor(String color) {
        this.setColor(color);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.color
     *
     * @param color the value for supply_chain..atom_offering_info_history.color
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setColor(String color) {
        this.color = color == null ? null : color.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.model
     *
     * @return the value of supply_chain..atom_offering_info_history.model
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getModel() {
        return model;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withModel(String model) {
        this.setModel(model);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.model
     *
     * @param model the value for supply_chain..atom_offering_info_history.model
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.unit
     *
     * @return the value of supply_chain..atom_offering_info_history.unit
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getUnit() {
        return unit;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withUnit(String unit) {
        this.setUnit(unit);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.unit
     *
     * @param unit the value for supply_chain..atom_offering_info_history.unit
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.atom_sale_price
     *
     * @return the value of supply_chain..atom_offering_info_history.atom_sale_price
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public Long getAtomSalePrice() {
        return atomSalePrice;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withAtomSalePrice(Long atomSalePrice) {
        this.setAtomSalePrice(atomSalePrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.atom_sale_price
     *
     * @param atomSalePrice the value for supply_chain..atom_offering_info_history.atom_sale_price
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setAtomSalePrice(Long atomSalePrice) {
        this.atomSalePrice = atomSalePrice;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.inventory
     *
     * @return the value of supply_chain..atom_offering_info_history.inventory
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public Long getInventory() {
        return inventory;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withInventory(Long inventory) {
        this.setInventory(inventory);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.inventory
     *
     * @param inventory the value for supply_chain..atom_offering_info_history.inventory
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setInventory(Long inventory) {
        this.inventory = inventory;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.reserve_inventory
     *
     * @return the value of supply_chain..atom_offering_info_history.reserve_inventory
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public Long getReserveInventory() {
        return reserveInventory;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withReserveInventory(Long reserveInventory) {
        this.setReserveInventory(reserveInventory);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.reserve_inventory
     *
     * @param reserveInventory the value for supply_chain..atom_offering_info_history.reserve_inventory
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setReserveInventory(Long reserveInventory) {
        this.reserveInventory = reserveInventory;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.is_inventory
     *
     * @return the value of supply_chain..atom_offering_info_history.is_inventory
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public Boolean getIsInventory() {
        return isInventory;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withIsInventory(Boolean isInventory) {
        this.setIsInventory(isInventory);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.is_inventory
     *
     * @param isInventory the value for supply_chain..atom_offering_info_history.is_inventory
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setIsInventory(Boolean isInventory) {
        this.isInventory = isInventory;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.cooperator_id
     *
     * @return the value of supply_chain..atom_offering_info_history.cooperator_id
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getCooperatorId() {
        return cooperatorId;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withCooperatorId(String cooperatorId) {
        this.setCooperatorId(cooperatorId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.cooperator_id
     *
     * @param cooperatorId the value for supply_chain..atom_offering_info_history.cooperator_id
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setCooperatorId(String cooperatorId) {
        this.cooperatorId = cooperatorId == null ? null : cooperatorId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.inventory_threshold
     *
     * @return the value of supply_chain..atom_offering_info_history.inventory_threshold
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public Long getInventoryThreshold() {
        return inventoryThreshold;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withInventoryThreshold(Long inventoryThreshold) {
        this.setInventoryThreshold(inventoryThreshold);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.inventory_threshold
     *
     * @param inventoryThreshold the value for supply_chain..atom_offering_info_history.inventory_threshold
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setInventoryThreshold(Long inventoryThreshold) {
        this.inventoryThreshold = inventoryThreshold;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.is_notice
     *
     * @return the value of supply_chain..atom_offering_info_history.is_notice
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public Boolean getIsNotice() {
        return isNotice;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withIsNotice(Boolean isNotice) {
        this.setIsNotice(isNotice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.is_notice
     *
     * @param isNotice the value for supply_chain..atom_offering_info_history.is_notice
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setIsNotice(Boolean isNotice) {
        this.isNotice = isNotice;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.config_all_time
     *
     * @return the value of supply_chain..atom_offering_info_history.config_all_time
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public Date getConfigAllTime() {
        return configAllTime;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withConfigAllTime(Date configAllTime) {
        this.setConfigAllTime(configAllTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.config_all_time
     *
     * @param configAllTime the value for supply_chain..atom_offering_info_history.config_all_time
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setConfigAllTime(Date configAllTime) {
        this.configAllTime = configAllTime;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.config_time
     *
     * @return the value of supply_chain..atom_offering_info_history.config_time
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public Date getConfigTime() {
        return configTime;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withConfigTime(Date configTime) {
        this.setConfigTime(configTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.config_time
     *
     * @param configTime the value for supply_chain..atom_offering_info_history.config_time
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setConfigTime(Date configTime) {
        this.configTime = configTime;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.create_time
     *
     * @return the value of supply_chain..atom_offering_info_history.create_time
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.create_time
     *
     * @param createTime the value for supply_chain..atom_offering_info_history.create_time
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.update_time
     *
     * @return the value of supply_chain..atom_offering_info_history.update_time
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.update_time
     *
     * @param updateTime the value for supply_chain..atom_offering_info_history.update_time
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.notified
     *
     * @return the value of supply_chain..atom_offering_info_history.notified
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public Boolean getNotified() {
        return notified;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withNotified(Boolean notified) {
        this.setNotified(notified);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.notified
     *
     * @param notified the value for supply_chain..atom_offering_info_history.notified
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setNotified(Boolean notified) {
        this.notified = notified;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.offeringSaleRegion
     *
     * @return the value of supply_chain..atom_offering_info_history.offeringSaleRegion
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getOfferingsaleregion() {
        return offeringsaleregion;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withOfferingsaleregion(String offeringsaleregion) {
        this.setOfferingsaleregion(offeringsaleregion);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.offeringSaleRegion
     *
     * @param offeringsaleregion the value for supply_chain..atom_offering_info_history.offeringSaleRegion
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setOfferingsaleregion(String offeringsaleregion) {
        this.offeringsaleregion = offeringsaleregion == null ? null : offeringsaleregion.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.settlePricePartner
     *
     * @return the value of supply_chain..atom_offering_info_history.settlePricePartner
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getSettlepricepartner() {
        return settlepricepartner;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withSettlepricepartner(String settlepricepartner) {
        this.setSettlepricepartner(settlepricepartner);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.settlePricePartner
     *
     * @param settlepricepartner the value for supply_chain..atom_offering_info_history.settlePricePartner
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setSettlepricepartner(String settlepricepartner) {
        this.settlepricepartner = settlepricepartner == null ? null : settlepricepartner.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.settleServiceName
     *
     * @return the value of supply_chain..atom_offering_info_history.settleServiceName
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getSettleservicename() {
        return settleservicename;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withSettleservicename(String settleservicename) {
        this.setSettleservicename(settleservicename);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.settleServiceName
     *
     * @param settleservicename the value for supply_chain..atom_offering_info_history.settleServiceName
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setSettleservicename(String settleservicename) {
        this.settleservicename = settleservicename == null ? null : settleservicename.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.associated_offer
     *
     * @return the value of supply_chain..atom_offering_info_history.associated_offer
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getAssociatedOffer() {
        return associatedOffer;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withAssociatedOffer(String associatedOffer) {
        this.setAssociatedOffer(associatedOffer);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.associated_offer
     *
     * @param associatedOffer the value for supply_chain..atom_offering_info_history.associated_offer
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setAssociatedOffer(String associatedOffer) {
        this.associatedOffer = associatedOffer == null ? null : associatedOffer.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.validity_period
     *
     * @return the value of supply_chain..atom_offering_info_history.validity_period
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getValidityPeriod() {
        return validityPeriod;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withValidityPeriod(String validityPeriod) {
        this.setValidityPeriod(validityPeriod);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.validity_period
     *
     * @param validityPeriod the value for supply_chain..atom_offering_info_history.validity_period
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setValidityPeriod(String validityPeriod) {
        this.validityPeriod = validityPeriod == null ? null : validityPeriod.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.delete_time
     *
     * @return the value of supply_chain..atom_offering_info_history.delete_time
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public Date getDeleteTime() {
        return deleteTime;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withDeleteTime(Date deleteTime) {
        this.setDeleteTime(deleteTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.delete_time
     *
     * @param deleteTime the value for supply_chain..atom_offering_info_history.delete_time
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.get_order_way
     *
     * @return the value of supply_chain..atom_offering_info_history.get_order_way
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public Integer getGetOrderWay() {
        return getOrderWay;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withGetOrderWay(Integer getOrderWay) {
        this.setGetOrderWay(getOrderWay);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.get_order_way
     *
     * @param getOrderWay the value for supply_chain..atom_offering_info_history.get_order_way
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setGetOrderWay(Integer getOrderWay) {
        this.getOrderWay = getOrderWay;
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.atom_offering_version
     *
     * @return the value of supply_chain..atom_offering_info_history.atom_offering_version
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getAtomOfferingVersion() {
        return atomOfferingVersion;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withAtomOfferingVersion(String atomOfferingVersion) {
        this.setAtomOfferingVersion(atomOfferingVersion);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.atom_offering_version
     *
     * @param atomOfferingVersion the value for supply_chain..atom_offering_info_history.atom_offering_version
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setAtomOfferingVersion(String atomOfferingVersion) {
        this.atomOfferingVersion = atomOfferingVersion == null ? null : atomOfferingVersion.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.sku_offering_version
     *
     * @return the value of supply_chain..atom_offering_info_history.sku_offering_version
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getSkuOfferingVersion() {
        return skuOfferingVersion;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withSkuOfferingVersion(String skuOfferingVersion) {
        this.setSkuOfferingVersion(skuOfferingVersion);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.sku_offering_version
     *
     * @param skuOfferingVersion the value for supply_chain..atom_offering_info_history.sku_offering_version
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setSkuOfferingVersion(String skuOfferingVersion) {
        this.skuOfferingVersion = skuOfferingVersion == null ? null : skuOfferingVersion.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.spu_offering_version
     *
     * @return the value of supply_chain..atom_offering_info_history.spu_offering_version
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getSpuOfferingVersion() {
        return spuOfferingVersion;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withSpuOfferingVersion(String spuOfferingVersion) {
        this.setSpuOfferingVersion(spuOfferingVersion);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.spu_offering_version
     *
     * @param spuOfferingVersion the value for supply_chain..atom_offering_info_history.spu_offering_version
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setSpuOfferingVersion(String spuOfferingVersion) {
        this.spuOfferingVersion = spuOfferingVersion == null ? null : spuOfferingVersion.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.special_cooperative_settlement_price
     *
     * @return the value of supply_chain..atom_offering_info_history.special_cooperative_settlement_price
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getSpecialCooperativeSettlementPrice() {
        return specialCooperativeSettlementPrice;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withSpecialCooperativeSettlementPrice(String specialCooperativeSettlementPrice) {
        this.setSpecialCooperativeSettlementPrice(specialCooperativeSettlementPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.special_cooperative_settlement_price
     *
     * @param specialCooperativeSettlementPrice the value for supply_chain..atom_offering_info_history.special_cooperative_settlement_price
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setSpecialCooperativeSettlementPrice(String specialCooperativeSettlementPrice) {
        this.specialCooperativeSettlementPrice = specialCooperativeSettlementPrice == null ? null : specialCooperativeSettlementPrice.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.atom_id
     *
     * @return the value of supply_chain..atom_offering_info_history.atom_id
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getAtomId() {
        return atomId;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withAtomId(String atomId) {
        this.setAtomId(atomId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.atom_id
     *
     * @param atomId the value for supply_chain..atom_offering_info_history.atom_id
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setAtomId(String atomId) {
        this.atomId = atomId == null ? null : atomId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.product_type
     *
     * @return the value of supply_chain..atom_offering_info_history.product_type
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getProductType() {
        return productType;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withProductType(String productType) {
        this.setProductType(productType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.product_type
     *
     * @param productType the value for supply_chain..atom_offering_info_history.product_type
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setProductType(String productType) {
        this.productType = productType == null ? null : productType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..atom_offering_info_history.card_containing_terminal
     *
     * @return the value of supply_chain..atom_offering_info_history.card_containing_terminal
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public String getCardContainingTerminal() {
        return cardContainingTerminal;
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public AtomOfferingInfoHistory withCardContainingTerminal(String cardContainingTerminal) {
        this.setCardContainingTerminal(cardContainingTerminal);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..atom_offering_info_history.card_containing_terminal
     *
     * @param cardContainingTerminal the value for supply_chain..atom_offering_info_history.card_containing_terminal
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public void setCardContainingTerminal(String cardContainingTerminal) {
        this.cardContainingTerminal = cardContainingTerminal == null ? null : cardContainingTerminal.trim();
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", spuId=").append(spuId);
        sb.append(", spuCode=").append(spuCode);
        sb.append(", skuId=").append(skuId);
        sb.append(", skuCode=").append(skuCode);
        sb.append(", offeringCode=").append(offeringCode);
        sb.append(", offeringName=").append(offeringName);
        sb.append(", offeringClass=").append(offeringClass);
        sb.append(", quantity=").append(quantity);
        sb.append(", extSoftOfferingCode=").append(extSoftOfferingCode);
        sb.append(", extHardOfferingCode=").append(extHardOfferingCode);
        sb.append(", settlePrice=").append(settlePrice);
        sb.append(", chargeCode=").append(chargeCode);
        sb.append(", chargeId=").append(chargeId);
        sb.append(", color=").append(color);
        sb.append(", model=").append(model);
        sb.append(", unit=").append(unit);
        sb.append(", atomSalePrice=").append(atomSalePrice);
        sb.append(", inventory=").append(inventory);
        sb.append(", reserveInventory=").append(reserveInventory);
        sb.append(", isInventory=").append(isInventory);
        sb.append(", cooperatorId=").append(cooperatorId);
        sb.append(", inventoryThreshold=").append(inventoryThreshold);
        sb.append(", isNotice=").append(isNotice);
        sb.append(", configAllTime=").append(configAllTime);
        sb.append(", configTime=").append(configTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", notified=").append(notified);
        sb.append(", offeringsaleregion=").append(offeringsaleregion);
        sb.append(", settlepricepartner=").append(settlepricepartner);
        sb.append(", settleservicename=").append(settleservicename);
        sb.append(", associatedOffer=").append(associatedOffer);
        sb.append(", validityPeriod=").append(validityPeriod);
        sb.append(", deleteTime=").append(deleteTime);
        sb.append(", getOrderWay=").append(getOrderWay);
        sb.append(", atomOfferingVersion=").append(atomOfferingVersion);
        sb.append(", skuOfferingVersion=").append(skuOfferingVersion);
        sb.append(", spuOfferingVersion=").append(spuOfferingVersion);
        sb.append(", specialCooperativeSettlementPrice=").append(specialCooperativeSettlementPrice);
        sb.append(", atomId=").append(atomId);
        sb.append(", productType=").append(productType);
        sb.append(", cardContainingTerminal=").append(cardContainingTerminal);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        AtomOfferingInfoHistory other = (AtomOfferingInfoHistory) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSpuId() == null ? other.getSpuId() == null : this.getSpuId().equals(other.getSpuId()))
            && (this.getSpuCode() == null ? other.getSpuCode() == null : this.getSpuCode().equals(other.getSpuCode()))
            && (this.getSkuId() == null ? other.getSkuId() == null : this.getSkuId().equals(other.getSkuId()))
            && (this.getSkuCode() == null ? other.getSkuCode() == null : this.getSkuCode().equals(other.getSkuCode()))
            && (this.getOfferingCode() == null ? other.getOfferingCode() == null : this.getOfferingCode().equals(other.getOfferingCode()))
            && (this.getOfferingName() == null ? other.getOfferingName() == null : this.getOfferingName().equals(other.getOfferingName()))
            && (this.getOfferingClass() == null ? other.getOfferingClass() == null : this.getOfferingClass().equals(other.getOfferingClass()))
            && (this.getQuantity() == null ? other.getQuantity() == null : this.getQuantity().equals(other.getQuantity()))
            && (this.getExtSoftOfferingCode() == null ? other.getExtSoftOfferingCode() == null : this.getExtSoftOfferingCode().equals(other.getExtSoftOfferingCode()))
            && (this.getExtHardOfferingCode() == null ? other.getExtHardOfferingCode() == null : this.getExtHardOfferingCode().equals(other.getExtHardOfferingCode()))
            && (this.getSettlePrice() == null ? other.getSettlePrice() == null : this.getSettlePrice().equals(other.getSettlePrice()))
            && (this.getChargeCode() == null ? other.getChargeCode() == null : this.getChargeCode().equals(other.getChargeCode()))
            && (this.getChargeId() == null ? other.getChargeId() == null : this.getChargeId().equals(other.getChargeId()))
            && (this.getColor() == null ? other.getColor() == null : this.getColor().equals(other.getColor()))
            && (this.getModel() == null ? other.getModel() == null : this.getModel().equals(other.getModel()))
            && (this.getUnit() == null ? other.getUnit() == null : this.getUnit().equals(other.getUnit()))
            && (this.getAtomSalePrice() == null ? other.getAtomSalePrice() == null : this.getAtomSalePrice().equals(other.getAtomSalePrice()))
            && (this.getInventory() == null ? other.getInventory() == null : this.getInventory().equals(other.getInventory()))
            && (this.getReserveInventory() == null ? other.getReserveInventory() == null : this.getReserveInventory().equals(other.getReserveInventory()))
            && (this.getIsInventory() == null ? other.getIsInventory() == null : this.getIsInventory().equals(other.getIsInventory()))
            && (this.getCooperatorId() == null ? other.getCooperatorId() == null : this.getCooperatorId().equals(other.getCooperatorId()))
            && (this.getInventoryThreshold() == null ? other.getInventoryThreshold() == null : this.getInventoryThreshold().equals(other.getInventoryThreshold()))
            && (this.getIsNotice() == null ? other.getIsNotice() == null : this.getIsNotice().equals(other.getIsNotice()))
            && (this.getConfigAllTime() == null ? other.getConfigAllTime() == null : this.getConfigAllTime().equals(other.getConfigAllTime()))
            && (this.getConfigTime() == null ? other.getConfigTime() == null : this.getConfigTime().equals(other.getConfigTime()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getNotified() == null ? other.getNotified() == null : this.getNotified().equals(other.getNotified()))
            && (this.getOfferingsaleregion() == null ? other.getOfferingsaleregion() == null : this.getOfferingsaleregion().equals(other.getOfferingsaleregion()))
            && (this.getSettlepricepartner() == null ? other.getSettlepricepartner() == null : this.getSettlepricepartner().equals(other.getSettlepricepartner()))
            && (this.getSettleservicename() == null ? other.getSettleservicename() == null : this.getSettleservicename().equals(other.getSettleservicename()))
            && (this.getAssociatedOffer() == null ? other.getAssociatedOffer() == null : this.getAssociatedOffer().equals(other.getAssociatedOffer()))
            && (this.getValidityPeriod() == null ? other.getValidityPeriod() == null : this.getValidityPeriod().equals(other.getValidityPeriod()))
            && (this.getDeleteTime() == null ? other.getDeleteTime() == null : this.getDeleteTime().equals(other.getDeleteTime()))
            && (this.getGetOrderWay() == null ? other.getGetOrderWay() == null : this.getGetOrderWay().equals(other.getGetOrderWay()))
            && (this.getAtomOfferingVersion() == null ? other.getAtomOfferingVersion() == null : this.getAtomOfferingVersion().equals(other.getAtomOfferingVersion()))
            && (this.getSkuOfferingVersion() == null ? other.getSkuOfferingVersion() == null : this.getSkuOfferingVersion().equals(other.getSkuOfferingVersion()))
            && (this.getSpuOfferingVersion() == null ? other.getSpuOfferingVersion() == null : this.getSpuOfferingVersion().equals(other.getSpuOfferingVersion()))
            && (this.getSpecialCooperativeSettlementPrice() == null ? other.getSpecialCooperativeSettlementPrice() == null : this.getSpecialCooperativeSettlementPrice().equals(other.getSpecialCooperativeSettlementPrice()))
            && (this.getAtomId() == null ? other.getAtomId() == null : this.getAtomId().equals(other.getAtomId()))
            && (this.getProductType() == null ? other.getProductType() == null : this.getProductType().equals(other.getProductType()))
            && (this.getCardContainingTerminal() == null ? other.getCardContainingTerminal() == null : this.getCardContainingTerminal().equals(other.getCardContainingTerminal()));
    }

    /**
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSpuId() == null) ? 0 : getSpuId().hashCode());
        result = prime * result + ((getSpuCode() == null) ? 0 : getSpuCode().hashCode());
        result = prime * result + ((getSkuId() == null) ? 0 : getSkuId().hashCode());
        result = prime * result + ((getSkuCode() == null) ? 0 : getSkuCode().hashCode());
        result = prime * result + ((getOfferingCode() == null) ? 0 : getOfferingCode().hashCode());
        result = prime * result + ((getOfferingName() == null) ? 0 : getOfferingName().hashCode());
        result = prime * result + ((getOfferingClass() == null) ? 0 : getOfferingClass().hashCode());
        result = prime * result + ((getQuantity() == null) ? 0 : getQuantity().hashCode());
        result = prime * result + ((getExtSoftOfferingCode() == null) ? 0 : getExtSoftOfferingCode().hashCode());
        result = prime * result + ((getExtHardOfferingCode() == null) ? 0 : getExtHardOfferingCode().hashCode());
        result = prime * result + ((getSettlePrice() == null) ? 0 : getSettlePrice().hashCode());
        result = prime * result + ((getChargeCode() == null) ? 0 : getChargeCode().hashCode());
        result = prime * result + ((getChargeId() == null) ? 0 : getChargeId().hashCode());
        result = prime * result + ((getColor() == null) ? 0 : getColor().hashCode());
        result = prime * result + ((getModel() == null) ? 0 : getModel().hashCode());
        result = prime * result + ((getUnit() == null) ? 0 : getUnit().hashCode());
        result = prime * result + ((getAtomSalePrice() == null) ? 0 : getAtomSalePrice().hashCode());
        result = prime * result + ((getInventory() == null) ? 0 : getInventory().hashCode());
        result = prime * result + ((getReserveInventory() == null) ? 0 : getReserveInventory().hashCode());
        result = prime * result + ((getIsInventory() == null) ? 0 : getIsInventory().hashCode());
        result = prime * result + ((getCooperatorId() == null) ? 0 : getCooperatorId().hashCode());
        result = prime * result + ((getInventoryThreshold() == null) ? 0 : getInventoryThreshold().hashCode());
        result = prime * result + ((getIsNotice() == null) ? 0 : getIsNotice().hashCode());
        result = prime * result + ((getConfigAllTime() == null) ? 0 : getConfigAllTime().hashCode());
        result = prime * result + ((getConfigTime() == null) ? 0 : getConfigTime().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getNotified() == null) ? 0 : getNotified().hashCode());
        result = prime * result + ((getOfferingsaleregion() == null) ? 0 : getOfferingsaleregion().hashCode());
        result = prime * result + ((getSettlepricepartner() == null) ? 0 : getSettlepricepartner().hashCode());
        result = prime * result + ((getSettleservicename() == null) ? 0 : getSettleservicename().hashCode());
        result = prime * result + ((getAssociatedOffer() == null) ? 0 : getAssociatedOffer().hashCode());
        result = prime * result + ((getValidityPeriod() == null) ? 0 : getValidityPeriod().hashCode());
        result = prime * result + ((getDeleteTime() == null) ? 0 : getDeleteTime().hashCode());
        result = prime * result + ((getGetOrderWay() == null) ? 0 : getGetOrderWay().hashCode());
        result = prime * result + ((getAtomOfferingVersion() == null) ? 0 : getAtomOfferingVersion().hashCode());
        result = prime * result + ((getSkuOfferingVersion() == null) ? 0 : getSkuOfferingVersion().hashCode());
        result = prime * result + ((getSpuOfferingVersion() == null) ? 0 : getSpuOfferingVersion().hashCode());
        result = prime * result + ((getSpecialCooperativeSettlementPrice() == null) ? 0 : getSpecialCooperativeSettlementPrice().hashCode());
        result = prime * result + ((getAtomId() == null) ? 0 : getAtomId().hashCode());
        result = prime * result + ((getProductType() == null) ? 0 : getProductType().hashCode());
        result = prime * result + ((getCardContainingTerminal() == null) ? 0 : getCardContainingTerminal().hashCode());
        return result;
    }

    /**
     * corresponding to the database table supply_chain..atom_offering_info_history
     *
     * @mbg.generated Wed Jul 09 09:37:38 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        spuId("spu_id", "spuId", "VARCHAR", false),
        spuCode("spu_code", "spuCode", "VARCHAR", false),
        skuId("sku_id", "skuId", "VARCHAR", false),
        skuCode("sku_code", "skuCode", "VARCHAR", false),
        offeringCode("offering_code", "offeringCode", "VARCHAR", false),
        offeringName("offering_name", "offeringName", "VARCHAR", false),
        offeringClass("offering_class", "offeringClass", "VARCHAR", false),
        quantity("quantity", "quantity", "BIGINT", false),
        extSoftOfferingCode("ext_soft_offering_code", "extSoftOfferingCode", "VARCHAR", false),
        extHardOfferingCode("ext_hard_offering_code", "extHardOfferingCode", "VARCHAR", false),
        settlePrice("settle_price", "settlePrice", "BIGINT", false),
        chargeCode("charge_code", "chargeCode", "VARCHAR", false),
        chargeId("charge_id", "chargeId", "VARCHAR", false),
        color("color", "color", "VARCHAR", false),
        model("model", "model", "VARCHAR", false),
        unit("unit", "unit", "VARCHAR", false),
        atomSalePrice("atom_sale_price", "atomSalePrice", "BIGINT", false),
        inventory("inventory", "inventory", "BIGINT", false),
        reserveInventory("reserve_inventory", "reserveInventory", "BIGINT", false),
        isInventory("is_inventory", "isInventory", "BIT", false),
        cooperatorId("cooperator_id", "cooperatorId", "VARCHAR", false),
        inventoryThreshold("inventory_threshold", "inventoryThreshold", "BIGINT", false),
        isNotice("is_notice", "isNotice", "BIT", false),
        configAllTime("config_all_time", "configAllTime", "TIMESTAMP", false),
        configTime("config_time", "configTime", "TIMESTAMP", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        notified("notified", "notified", "BIT", false),
        offeringsaleregion("offeringSaleRegion", "offeringsaleregion", "VARCHAR", false),
        settlepricepartner("settlePricePartner", "settlepricepartner", "VARCHAR", false),
        settleservicename("settleServiceName", "settleservicename", "VARCHAR", false),
        associatedOffer("associated_offer", "associatedOffer", "VARCHAR", false),
        validityPeriod("validity_period", "validityPeriod", "VARCHAR", false),
        deleteTime("delete_time", "deleteTime", "TIMESTAMP", false),
        getOrderWay("get_order_way", "getOrderWay", "INTEGER", false),
        atomOfferingVersion("atom_offering_version", "atomOfferingVersion", "VARCHAR", false),
        skuOfferingVersion("sku_offering_version", "skuOfferingVersion", "VARCHAR", false),
        spuOfferingVersion("spu_offering_version", "spuOfferingVersion", "VARCHAR", false),
        specialCooperativeSettlementPrice("special_cooperative_settlement_price", "specialCooperativeSettlementPrice", "VARCHAR", false),
        atomId("atom_id", "atomId", "VARCHAR", false),
        productType("product_type", "productType", "VARCHAR", false),
        cardContainingTerminal("card_containing_terminal", "cardContainingTerminal", "VARCHAR", false);

        /**
         * Corresponding to the database table supply_chain..atom_offering_info_history
         *
         * @mbg.generated Wed Jul 09 09:37:38 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..atom_offering_info_history
         *
         * @mbg.generated Wed Jul 09 09:37:38 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..atom_offering_info_history
         *
         * @mbg.generated Wed Jul 09 09:37:38 CST 2025
         */
        private final String column;

        /**
         * Corresponding to the database table supply_chain..atom_offering_info_history
         *
         * @mbg.generated Wed Jul 09 09:37:38 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         * Corresponding to the database table supply_chain..atom_offering_info_history
         *
         * @mbg.generated Wed Jul 09 09:37:38 CST 2025
         */
        private final String javaProperty;

        /**
         * Corresponding to the database table supply_chain..atom_offering_info_history
         *
         * @mbg.generated Wed Jul 09 09:37:38 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Jul 09 09:37:38 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Jul 09 09:37:38 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Jul 09 09:37:38 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Jul 09 09:37:38 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Jul 09 09:37:38 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Jul 09 09:37:38 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Jul 09 09:37:38 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Jul 09 09:37:38 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Jul 09 09:37:38 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Jul 09 09:37:38 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Jul 09 09:37:38 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}