package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AfterMarketOrder2cOfferingInfoExample {
    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public static Criteria newAndCreateCriteria() {
        AfterMarketOrder2cOfferingInfoExample example = new AfterMarketOrder2cOfferingInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdIsNull() {
            addCriterion("service_order_id is null");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdIsNotNull() {
            addCriterion("service_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdEqualTo(String value) {
            addCriterion("service_order_id =", value, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("service_order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdNotEqualTo(String value) {
            addCriterion("service_order_id <>", value, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("service_order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdGreaterThan(String value) {
            addCriterion("service_order_id >", value, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("service_order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("service_order_id >=", value, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("service_order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdLessThan(String value) {
            addCriterion("service_order_id <", value, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("service_order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdLessThanOrEqualTo(String value) {
            addCriterion("service_order_id <=", value, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("service_order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdLike(String value) {
            addCriterion("service_order_id like", value, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdNotLike(String value) {
            addCriterion("service_order_id not like", value, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdIn(List<String> values) {
            addCriterion("service_order_id in", values, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdNotIn(List<String> values) {
            addCriterion("service_order_id not in", values, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdBetween(String value1, String value2) {
            addCriterion("service_order_id between", value1, value2, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdNotBetween(String value1, String value2) {
            addCriterion("service_order_id not between", value1, value2, "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andAfterMarketTypeIsNull() {
            addCriterion("after_market_type is null");
            return (Criteria) this;
        }

        public Criteria andAfterMarketTypeIsNotNull() {
            addCriterion("after_market_type is not null");
            return (Criteria) this;
        }

        public Criteria andAfterMarketTypeEqualTo(Integer value) {
            addCriterion("after_market_type =", value, "afterMarketType");
            return (Criteria) this;
        }

        public Criteria andAfterMarketTypeEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketTypeNotEqualTo(Integer value) {
            addCriterion("after_market_type <>", value, "afterMarketType");
            return (Criteria) this;
        }

        public Criteria andAfterMarketTypeNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketTypeGreaterThan(Integer value) {
            addCriterion("after_market_type >", value, "afterMarketType");
            return (Criteria) this;
        }

        public Criteria andAfterMarketTypeGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("after_market_type >=", value, "afterMarketType");
            return (Criteria) this;
        }

        public Criteria andAfterMarketTypeGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketTypeLessThan(Integer value) {
            addCriterion("after_market_type <", value, "afterMarketType");
            return (Criteria) this;
        }

        public Criteria andAfterMarketTypeLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketTypeLessThanOrEqualTo(Integer value) {
            addCriterion("after_market_type <=", value, "afterMarketType");
            return (Criteria) this;
        }

        public Criteria andAfterMarketTypeLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketTypeIn(List<Integer> values) {
            addCriterion("after_market_type in", values, "afterMarketType");
            return (Criteria) this;
        }

        public Criteria andAfterMarketTypeNotIn(List<Integer> values) {
            addCriterion("after_market_type not in", values, "afterMarketType");
            return (Criteria) this;
        }

        public Criteria andAfterMarketTypeBetween(Integer value1, Integer value2) {
            addCriterion("after_market_type between", value1, value2, "afterMarketType");
            return (Criteria) this;
        }

        public Criteria andAfterMarketTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("after_market_type not between", value1, value2, "afterMarketType");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeIsNull() {
            addCriterion("after_market_code is null");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeIsNotNull() {
            addCriterion("after_market_code is not null");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeEqualTo(String value) {
            addCriterion("after_market_code =", value, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeNotEqualTo(String value) {
            addCriterion("after_market_code <>", value, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeGreaterThan(String value) {
            addCriterion("after_market_code >", value, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeGreaterThanOrEqualTo(String value) {
            addCriterion("after_market_code >=", value, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeLessThan(String value) {
            addCriterion("after_market_code <", value, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeLessThanOrEqualTo(String value) {
            addCriterion("after_market_code <=", value, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeLike(String value) {
            addCriterion("after_market_code like", value, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeNotLike(String value) {
            addCriterion("after_market_code not like", value, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeIn(List<String> values) {
            addCriterion("after_market_code in", values, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeNotIn(List<String> values) {
            addCriterion("after_market_code not in", values, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeBetween(String value1, String value2) {
            addCriterion("after_market_code between", value1, value2, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeNotBetween(String value1, String value2) {
            addCriterion("after_market_code not between", value1, value2, "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNull() {
            addCriterion("quantity is null");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNotNull() {
            addCriterion("quantity is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualTo(Long value) {
            addCriterion("quantity =", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("quantity = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualTo(Long value) {
            addCriterion("quantity <>", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("quantity <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThan(Long value) {
            addCriterion("quantity >", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("quantity > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualTo(Long value) {
            addCriterion("quantity >=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("quantity >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityLessThan(Long value) {
            addCriterion("quantity <", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("quantity < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualTo(Long value) {
            addCriterion("quantity <=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("quantity <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityIn(List<Long> values) {
            addCriterion("quantity in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotIn(List<Long> values) {
            addCriterion("quantity not in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityBetween(Long value1, Long value2) {
            addCriterion("quantity between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotBetween(Long value1, Long value2) {
            addCriterion("quantity not between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameIsNull() {
            addCriterion("present_send_order_name is null");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameIsNotNull() {
            addCriterion("present_send_order_name is not null");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameEqualTo(String value) {
            addCriterion("present_send_order_name =", value, "presentSendOrderName");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("present_send_order_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameNotEqualTo(String value) {
            addCriterion("present_send_order_name <>", value, "presentSendOrderName");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("present_send_order_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameGreaterThan(String value) {
            addCriterion("present_send_order_name >", value, "presentSendOrderName");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("present_send_order_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameGreaterThanOrEqualTo(String value) {
            addCriterion("present_send_order_name >=", value, "presentSendOrderName");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("present_send_order_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameLessThan(String value) {
            addCriterion("present_send_order_name <", value, "presentSendOrderName");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("present_send_order_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameLessThanOrEqualTo(String value) {
            addCriterion("present_send_order_name <=", value, "presentSendOrderName");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("present_send_order_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameLike(String value) {
            addCriterion("present_send_order_name like", value, "presentSendOrderName");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameNotLike(String value) {
            addCriterion("present_send_order_name not like", value, "presentSendOrderName");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameIn(List<String> values) {
            addCriterion("present_send_order_name in", values, "presentSendOrderName");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameNotIn(List<String> values) {
            addCriterion("present_send_order_name not in", values, "presentSendOrderName");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameBetween(String value1, String value2) {
            addCriterion("present_send_order_name between", value1, value2, "presentSendOrderName");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameNotBetween(String value1, String value2) {
            addCriterion("present_send_order_name not between", value1, value2, "presentSendOrderName");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneIsNull() {
            addCriterion("present_send_order_phone is null");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneIsNotNull() {
            addCriterion("present_send_order_phone is not null");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneEqualTo(String value) {
            addCriterion("present_send_order_phone =", value, "presentSendOrderPhone");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("present_send_order_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneNotEqualTo(String value) {
            addCriterion("present_send_order_phone <>", value, "presentSendOrderPhone");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("present_send_order_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneGreaterThan(String value) {
            addCriterion("present_send_order_phone >", value, "presentSendOrderPhone");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("present_send_order_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("present_send_order_phone >=", value, "presentSendOrderPhone");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("present_send_order_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneLessThan(String value) {
            addCriterion("present_send_order_phone <", value, "presentSendOrderPhone");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("present_send_order_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneLessThanOrEqualTo(String value) {
            addCriterion("present_send_order_phone <=", value, "presentSendOrderPhone");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("present_send_order_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneLike(String value) {
            addCriterion("present_send_order_phone like", value, "presentSendOrderPhone");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneNotLike(String value) {
            addCriterion("present_send_order_phone not like", value, "presentSendOrderPhone");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneIn(List<String> values) {
            addCriterion("present_send_order_phone in", values, "presentSendOrderPhone");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneNotIn(List<String> values) {
            addCriterion("present_send_order_phone not in", values, "presentSendOrderPhone");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneBetween(String value1, String value2) {
            addCriterion("present_send_order_phone between", value1, value2, "presentSendOrderPhone");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneNotBetween(String value1, String value2) {
            addCriterion("present_send_order_phone not between", value1, value2, "presentSendOrderPhone");
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyIsNull() {
            addCriterion("send_order_company is null");
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyIsNotNull() {
            addCriterion("send_order_company is not null");
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyEqualTo(String value) {
            addCriterion("send_order_company =", value, "sendOrderCompany");
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("send_order_company = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyNotEqualTo(String value) {
            addCriterion("send_order_company <>", value, "sendOrderCompany");
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("send_order_company <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyGreaterThan(String value) {
            addCriterion("send_order_company >", value, "sendOrderCompany");
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("send_order_company > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyGreaterThanOrEqualTo(String value) {
            addCriterion("send_order_company >=", value, "sendOrderCompany");
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("send_order_company >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyLessThan(String value) {
            addCriterion("send_order_company <", value, "sendOrderCompany");
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("send_order_company < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyLessThanOrEqualTo(String value) {
            addCriterion("send_order_company <=", value, "sendOrderCompany");
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("send_order_company <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyLike(String value) {
            addCriterion("send_order_company like", value, "sendOrderCompany");
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyNotLike(String value) {
            addCriterion("send_order_company not like", value, "sendOrderCompany");
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyIn(List<String> values) {
            addCriterion("send_order_company in", values, "sendOrderCompany");
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyNotIn(List<String> values) {
            addCriterion("send_order_company not in", values, "sendOrderCompany");
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyBetween(String value1, String value2) {
            addCriterion("send_order_company between", value1, value2, "sendOrderCompany");
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyNotBetween(String value1, String value2) {
            addCriterion("send_order_company not between", value1, value2, "sendOrderCompany");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(String value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("user_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(String value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("user_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(String value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("user_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("user_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(String value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("user_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(String value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("user_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLike(String value) {
            addCriterion("user_id like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotLike(String value) {
            addCriterion("user_id not like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<String> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<String> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(String value1, String value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(String value1, String value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameIsNull() {
            addCriterion("after_market_name is null");
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameIsNotNull() {
            addCriterion("after_market_name is not null");
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameEqualTo(String value) {
            addCriterion("after_market_name =", value, "afterMarketName");
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameNotEqualTo(String value) {
            addCriterion("after_market_name <>", value, "afterMarketName");
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameGreaterThan(String value) {
            addCriterion("after_market_name >", value, "afterMarketName");
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameGreaterThanOrEqualTo(String value) {
            addCriterion("after_market_name >=", value, "afterMarketName");
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameLessThan(String value) {
            addCriterion("after_market_name <", value, "afterMarketName");
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameLessThanOrEqualTo(String value) {
            addCriterion("after_market_name <=", value, "afterMarketName");
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameLike(String value) {
            addCriterion("after_market_name like", value, "afterMarketName");
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameNotLike(String value) {
            addCriterion("after_market_name not like", value, "afterMarketName");
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameIn(List<String> values) {
            addCriterion("after_market_name in", values, "afterMarketName");
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameNotIn(List<String> values) {
            addCriterion("after_market_name not in", values, "afterMarketName");
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameBetween(String value1, String value2) {
            addCriterion("after_market_name between", value1, value2, "afterMarketName");
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameNotBetween(String value1, String value2) {
            addCriterion("after_market_name not between", value1, value2, "afterMarketName");
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceIsNull() {
            addCriterion("after_market_settle_price is null");
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceIsNotNull() {
            addCriterion("after_market_settle_price is not null");
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceEqualTo(String value) {
            addCriterion("after_market_settle_price =", value, "afterMarketSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_settle_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceNotEqualTo(String value) {
            addCriterion("after_market_settle_price <>", value, "afterMarketSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_settle_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceGreaterThan(String value) {
            addCriterion("after_market_settle_price >", value, "afterMarketSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_settle_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceGreaterThanOrEqualTo(String value) {
            addCriterion("after_market_settle_price >=", value, "afterMarketSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_settle_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceLessThan(String value) {
            addCriterion("after_market_settle_price <", value, "afterMarketSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_settle_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceLessThanOrEqualTo(String value) {
            addCriterion("after_market_settle_price <=", value, "afterMarketSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_settle_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceLike(String value) {
            addCriterion("after_market_settle_price like", value, "afterMarketSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceNotLike(String value) {
            addCriterion("after_market_settle_price not like", value, "afterMarketSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceIn(List<String> values) {
            addCriterion("after_market_settle_price in", values, "afterMarketSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceNotIn(List<String> values) {
            addCriterion("after_market_settle_price not in", values, "afterMarketSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceBetween(String value1, String value2) {
            addCriterion("after_market_settle_price between", value1, value2, "afterMarketSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceNotBetween(String value1, String value2) {
            addCriterion("after_market_settle_price not between", value1, value2, "afterMarketSettlePrice");
            return (Criteria) this;
        }

        public Criteria andOrderTakeTypeIsNull() {
            addCriterion("order_take_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderTakeTypeIsNotNull() {
            addCriterion("order_take_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTakeTypeEqualTo(Integer value) {
            addCriterion("order_take_type =", value, "orderTakeType");
            return (Criteria) this;
        }

        public Criteria andOrderTakeTypeEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("order_take_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTakeTypeNotEqualTo(Integer value) {
            addCriterion("order_take_type <>", value, "orderTakeType");
            return (Criteria) this;
        }

        public Criteria andOrderTakeTypeNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("order_take_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTakeTypeGreaterThan(Integer value) {
            addCriterion("order_take_type >", value, "orderTakeType");
            return (Criteria) this;
        }

        public Criteria andOrderTakeTypeGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("order_take_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTakeTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_take_type >=", value, "orderTakeType");
            return (Criteria) this;
        }

        public Criteria andOrderTakeTypeGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("order_take_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTakeTypeLessThan(Integer value) {
            addCriterion("order_take_type <", value, "orderTakeType");
            return (Criteria) this;
        }

        public Criteria andOrderTakeTypeLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("order_take_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTakeTypeLessThanOrEqualTo(Integer value) {
            addCriterion("order_take_type <=", value, "orderTakeType");
            return (Criteria) this;
        }

        public Criteria andOrderTakeTypeLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("order_take_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTakeTypeIn(List<Integer> values) {
            addCriterion("order_take_type in", values, "orderTakeType");
            return (Criteria) this;
        }

        public Criteria andOrderTakeTypeNotIn(List<Integer> values) {
            addCriterion("order_take_type not in", values, "orderTakeType");
            return (Criteria) this;
        }

        public Criteria andOrderTakeTypeBetween(Integer value1, Integer value2) {
            addCriterion("order_take_type between", value1, value2, "orderTakeType");
            return (Criteria) this;
        }

        public Criteria andOrderTakeTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("order_take_type not between", value1, value2, "orderTakeType");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdIsNull() {
            addCriterion("admin_cooperator_id is null");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdIsNotNull() {
            addCriterion("admin_cooperator_id is not null");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdEqualTo(String value) {
            addCriterion("admin_cooperator_id =", value, "adminCooperatorId");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("admin_cooperator_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdNotEqualTo(String value) {
            addCriterion("admin_cooperator_id <>", value, "adminCooperatorId");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("admin_cooperator_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdGreaterThan(String value) {
            addCriterion("admin_cooperator_id >", value, "adminCooperatorId");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("admin_cooperator_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdGreaterThanOrEqualTo(String value) {
            addCriterion("admin_cooperator_id >=", value, "adminCooperatorId");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("admin_cooperator_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdLessThan(String value) {
            addCriterion("admin_cooperator_id <", value, "adminCooperatorId");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("admin_cooperator_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdLessThanOrEqualTo(String value) {
            addCriterion("admin_cooperator_id <=", value, "adminCooperatorId");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("admin_cooperator_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdLike(String value) {
            addCriterion("admin_cooperator_id like", value, "adminCooperatorId");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdNotLike(String value) {
            addCriterion("admin_cooperator_id not like", value, "adminCooperatorId");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdIn(List<String> values) {
            addCriterion("admin_cooperator_id in", values, "adminCooperatorId");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdNotIn(List<String> values) {
            addCriterion("admin_cooperator_id not in", values, "adminCooperatorId");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdBetween(String value1, String value2) {
            addCriterion("admin_cooperator_id between", value1, value2, "adminCooperatorId");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdNotBetween(String value1, String value2) {
            addCriterion("admin_cooperator_id not between", value1, value2, "adminCooperatorId");
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdIsNull() {
            addCriterion("install_manager_id is null");
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdIsNotNull() {
            addCriterion("install_manager_id is not null");
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdEqualTo(String value) {
            addCriterion("install_manager_id =", value, "installManagerId");
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("install_manager_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdNotEqualTo(String value) {
            addCriterion("install_manager_id <>", value, "installManagerId");
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("install_manager_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdGreaterThan(String value) {
            addCriterion("install_manager_id >", value, "installManagerId");
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("install_manager_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdGreaterThanOrEqualTo(String value) {
            addCriterion("install_manager_id >=", value, "installManagerId");
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("install_manager_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdLessThan(String value) {
            addCriterion("install_manager_id <", value, "installManagerId");
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("install_manager_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdLessThanOrEqualTo(String value) {
            addCriterion("install_manager_id <=", value, "installManagerId");
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("install_manager_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdLike(String value) {
            addCriterion("install_manager_id like", value, "installManagerId");
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdNotLike(String value) {
            addCriterion("install_manager_id not like", value, "installManagerId");
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdIn(List<String> values) {
            addCriterion("install_manager_id in", values, "installManagerId");
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdNotIn(List<String> values) {
            addCriterion("install_manager_id not in", values, "installManagerId");
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdBetween(String value1, String value2) {
            addCriterion("install_manager_id between", value1, value2, "installManagerId");
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdNotBetween(String value1, String value2) {
            addCriterion("install_manager_id not between", value1, value2, "installManagerId");
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformIsNull() {
            addCriterion("province_install_platform is null");
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformIsNotNull() {
            addCriterion("province_install_platform is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformEqualTo(String value) {
            addCriterion("province_install_platform =", value, "provinceInstallPlatform");
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("province_install_platform = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformNotEqualTo(String value) {
            addCriterion("province_install_platform <>", value, "provinceInstallPlatform");
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("province_install_platform <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformGreaterThan(String value) {
            addCriterion("province_install_platform >", value, "provinceInstallPlatform");
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("province_install_platform > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformGreaterThanOrEqualTo(String value) {
            addCriterion("province_install_platform >=", value, "provinceInstallPlatform");
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("province_install_platform >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformLessThan(String value) {
            addCriterion("province_install_platform <", value, "provinceInstallPlatform");
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("province_install_platform < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformLessThanOrEqualTo(String value) {
            addCriterion("province_install_platform <=", value, "provinceInstallPlatform");
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("province_install_platform <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformLike(String value) {
            addCriterion("province_install_platform like", value, "provinceInstallPlatform");
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformNotLike(String value) {
            addCriterion("province_install_platform not like", value, "provinceInstallPlatform");
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformIn(List<String> values) {
            addCriterion("province_install_platform in", values, "provinceInstallPlatform");
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformNotIn(List<String> values) {
            addCriterion("province_install_platform not in", values, "provinceInstallPlatform");
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformBetween(String value1, String value2) {
            addCriterion("province_install_platform between", value1, value2, "provinceInstallPlatform");
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformNotBetween(String value1, String value2) {
            addCriterion("province_install_platform not between", value1, value2, "provinceInstallPlatform");
            return (Criteria) this;
        }

        public Criteria andSendOrderTimeIsNull() {
            addCriterion("send_order_time is null");
            return (Criteria) this;
        }

        public Criteria andSendOrderTimeIsNotNull() {
            addCriterion("send_order_time is not null");
            return (Criteria) this;
        }

        public Criteria andSendOrderTimeEqualTo(Date value) {
            addCriterion("send_order_time =", value, "sendOrderTime");
            return (Criteria) this;
        }

        public Criteria andSendOrderTimeEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("send_order_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSendOrderTimeNotEqualTo(Date value) {
            addCriterion("send_order_time <>", value, "sendOrderTime");
            return (Criteria) this;
        }

        public Criteria andSendOrderTimeNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("send_order_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSendOrderTimeGreaterThan(Date value) {
            addCriterion("send_order_time >", value, "sendOrderTime");
            return (Criteria) this;
        }

        public Criteria andSendOrderTimeGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("send_order_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSendOrderTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("send_order_time >=", value, "sendOrderTime");
            return (Criteria) this;
        }

        public Criteria andSendOrderTimeGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("send_order_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSendOrderTimeLessThan(Date value) {
            addCriterion("send_order_time <", value, "sendOrderTime");
            return (Criteria) this;
        }

        public Criteria andSendOrderTimeLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("send_order_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSendOrderTimeLessThanOrEqualTo(Date value) {
            addCriterion("send_order_time <=", value, "sendOrderTime");
            return (Criteria) this;
        }

        public Criteria andSendOrderTimeLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("send_order_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSendOrderTimeIn(List<Date> values) {
            addCriterion("send_order_time in", values, "sendOrderTime");
            return (Criteria) this;
        }

        public Criteria andSendOrderTimeNotIn(List<Date> values) {
            addCriterion("send_order_time not in", values, "sendOrderTime");
            return (Criteria) this;
        }

        public Criteria andSendOrderTimeBetween(Date value1, Date value2) {
            addCriterion("send_order_time between", value1, value2, "sendOrderTime");
            return (Criteria) this;
        }

        public Criteria andSendOrderTimeNotBetween(Date value1, Date value2) {
            addCriterion("send_order_time not between", value1, value2, "sendOrderTime");
            return (Criteria) this;
        }

        public Criteria andDeliverTimeIsNull() {
            addCriterion("deliver_time is null");
            return (Criteria) this;
        }

        public Criteria andDeliverTimeIsNotNull() {
            addCriterion("deliver_time is not null");
            return (Criteria) this;
        }

        public Criteria andDeliverTimeEqualTo(Date value) {
            addCriterion("deliver_time =", value, "deliverTime");
            return (Criteria) this;
        }

        public Criteria andDeliverTimeEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverTimeNotEqualTo(Date value) {
            addCriterion("deliver_time <>", value, "deliverTime");
            return (Criteria) this;
        }

        public Criteria andDeliverTimeNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverTimeGreaterThan(Date value) {
            addCriterion("deliver_time >", value, "deliverTime");
            return (Criteria) this;
        }

        public Criteria andDeliverTimeGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("deliver_time >=", value, "deliverTime");
            return (Criteria) this;
        }

        public Criteria andDeliverTimeGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverTimeLessThan(Date value) {
            addCriterion("deliver_time <", value, "deliverTime");
            return (Criteria) this;
        }

        public Criteria andDeliverTimeLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverTimeLessThanOrEqualTo(Date value) {
            addCriterion("deliver_time <=", value, "deliverTime");
            return (Criteria) this;
        }

        public Criteria andDeliverTimeLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverTimeIn(List<Date> values) {
            addCriterion("deliver_time in", values, "deliverTime");
            return (Criteria) this;
        }

        public Criteria andDeliverTimeNotIn(List<Date> values) {
            addCriterion("deliver_time not in", values, "deliverTime");
            return (Criteria) this;
        }

        public Criteria andDeliverTimeBetween(Date value1, Date value2) {
            addCriterion("deliver_time between", value1, value2, "deliverTime");
            return (Criteria) this;
        }

        public Criteria andDeliverTimeNotBetween(Date value1, Date value2) {
            addCriterion("deliver_time not between", value1, value2, "deliverTime");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeIsNull() {
            addCriterion("sku_offering_code is null");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeIsNotNull() {
            addCriterion("sku_offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeEqualTo(String value) {
            addCriterion("sku_offering_code =", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeNotEqualTo(String value) {
            addCriterion("sku_offering_code <>", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeGreaterThan(String value) {
            addCriterion("sku_offering_code >", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sku_offering_code >=", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeLessThan(String value) {
            addCriterion("sku_offering_code <", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("sku_offering_code <=", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeLike(String value) {
            addCriterion("sku_offering_code like", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeNotLike(String value) {
            addCriterion("sku_offering_code not like", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeIn(List<String> values) {
            addCriterion("sku_offering_code in", values, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeNotIn(List<String> values) {
            addCriterion("sku_offering_code not in", values, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeBetween(String value1, String value2) {
            addCriterion("sku_offering_code between", value1, value2, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("sku_offering_code not between", value1, value2, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeIsNull() {
            addCriterion("atom_offering_code is null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeIsNotNull() {
            addCriterion("atom_offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeEqualTo(String value) {
            addCriterion("atom_offering_code =", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeNotEqualTo(String value) {
            addCriterion("atom_offering_code <>", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeGreaterThan(String value) {
            addCriterion("atom_offering_code >", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("atom_offering_code >=", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeLessThan(String value) {
            addCriterion("atom_offering_code <", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("atom_offering_code <=", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeLike(String value) {
            addCriterion("atom_offering_code like", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeNotLike(String value) {
            addCriterion("atom_offering_code not like", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeIn(List<String> values) {
            addCriterion("atom_offering_code in", values, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeNotIn(List<String> values) {
            addCriterion("atom_offering_code not in", values, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeBetween(String value1, String value2) {
            addCriterion("atom_offering_code between", value1, value2, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("atom_offering_code not between", value1, value2, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeIsNull() {
            addCriterion("spu_offering_code is null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeIsNotNull() {
            addCriterion("spu_offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeEqualTo(String value) {
            addCriterion("spu_offering_code =", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotEqualTo(String value) {
            addCriterion("spu_offering_code <>", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThan(String value) {
            addCriterion("spu_offering_code >", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("spu_offering_code >=", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThan(String value) {
            addCriterion("spu_offering_code <", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("spu_offering_code <=", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLike(String value) {
            addCriterion("spu_offering_code like", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotLike(String value) {
            addCriterion("spu_offering_code not like", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeIn(List<String> values) {
            addCriterion("spu_offering_code in", values, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotIn(List<String> values) {
            addCriterion("spu_offering_code not in", values, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeBetween(String value1, String value2) {
            addCriterion("spu_offering_code between", value1, value2, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("spu_offering_code not between", value1, value2, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassIsNull() {
            addCriterion("spu_offering_class is null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassIsNotNull() {
            addCriterion("spu_offering_class is not null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassEqualTo(String value) {
            addCriterion("spu_offering_class =", value, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_class = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassNotEqualTo(String value) {
            addCriterion("spu_offering_class <>", value, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_class <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassGreaterThan(String value) {
            addCriterion("spu_offering_class >", value, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_class > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassGreaterThanOrEqualTo(String value) {
            addCriterion("spu_offering_class >=", value, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_class >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassLessThan(String value) {
            addCriterion("spu_offering_class <", value, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_class < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassLessThanOrEqualTo(String value) {
            addCriterion("spu_offering_class <=", value, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_class <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassLike(String value) {
            addCriterion("spu_offering_class like", value, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassNotLike(String value) {
            addCriterion("spu_offering_class not like", value, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassIn(List<String> values) {
            addCriterion("spu_offering_class in", values, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassNotIn(List<String> values) {
            addCriterion("spu_offering_class not in", values, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassBetween(String value1, String value2) {
            addCriterion("spu_offering_class between", value1, value2, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassNotBetween(String value1, String value2) {
            addCriterion("spu_offering_class not between", value1, value2, "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameIsNull() {
            addCriterion("sku_offering_name is null");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameIsNotNull() {
            addCriterion("sku_offering_name is not null");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameEqualTo(String value) {
            addCriterion("sku_offering_name =", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameNotEqualTo(String value) {
            addCriterion("sku_offering_name <>", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameGreaterThan(String value) {
            addCriterion("sku_offering_name >", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameGreaterThanOrEqualTo(String value) {
            addCriterion("sku_offering_name >=", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameLessThan(String value) {
            addCriterion("sku_offering_name <", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameLessThanOrEqualTo(String value) {
            addCriterion("sku_offering_name <=", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameLike(String value) {
            addCriterion("sku_offering_name like", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameNotLike(String value) {
            addCriterion("sku_offering_name not like", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameIn(List<String> values) {
            addCriterion("sku_offering_name in", values, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameNotIn(List<String> values) {
            addCriterion("sku_offering_name not in", values, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameBetween(String value1, String value2) {
            addCriterion("sku_offering_name between", value1, value2, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameNotBetween(String value1, String value2) {
            addCriterion("sku_offering_name not between", value1, value2, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameIsNull() {
            addCriterion("atom_offering_name is null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameIsNotNull() {
            addCriterion("atom_offering_name is not null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameEqualTo(String value) {
            addCriterion("atom_offering_name =", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameNotEqualTo(String value) {
            addCriterion("atom_offering_name <>", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameGreaterThan(String value) {
            addCriterion("atom_offering_name >", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameGreaterThanOrEqualTo(String value) {
            addCriterion("atom_offering_name >=", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameLessThan(String value) {
            addCriterion("atom_offering_name <", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameLessThanOrEqualTo(String value) {
            addCriterion("atom_offering_name <=", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameLike(String value) {
            addCriterion("atom_offering_name like", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameNotLike(String value) {
            addCriterion("atom_offering_name not like", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameIn(List<String> values) {
            addCriterion("atom_offering_name in", values, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameNotIn(List<String> values) {
            addCriterion("atom_offering_name not in", values, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameBetween(String value1, String value2) {
            addCriterion("atom_offering_name between", value1, value2, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameNotBetween(String value1, String value2) {
            addCriterion("atom_offering_name not between", value1, value2, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameIsNull() {
            addCriterion("admin_cooperator_name is null");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameIsNotNull() {
            addCriterion("admin_cooperator_name is not null");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameEqualTo(String value) {
            addCriterion("admin_cooperator_name =", value, "adminCooperatorName");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("admin_cooperator_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameNotEqualTo(String value) {
            addCriterion("admin_cooperator_name <>", value, "adminCooperatorName");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("admin_cooperator_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameGreaterThan(String value) {
            addCriterion("admin_cooperator_name >", value, "adminCooperatorName");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("admin_cooperator_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameGreaterThanOrEqualTo(String value) {
            addCriterion("admin_cooperator_name >=", value, "adminCooperatorName");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("admin_cooperator_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameLessThan(String value) {
            addCriterion("admin_cooperator_name <", value, "adminCooperatorName");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("admin_cooperator_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameLessThanOrEqualTo(String value) {
            addCriterion("admin_cooperator_name <=", value, "adminCooperatorName");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("admin_cooperator_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameLike(String value) {
            addCriterion("admin_cooperator_name like", value, "adminCooperatorName");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameNotLike(String value) {
            addCriterion("admin_cooperator_name not like", value, "adminCooperatorName");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameIn(List<String> values) {
            addCriterion("admin_cooperator_name in", values, "adminCooperatorName");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameNotIn(List<String> values) {
            addCriterion("admin_cooperator_name not in", values, "adminCooperatorName");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameBetween(String value1, String value2) {
            addCriterion("admin_cooperator_name between", value1, value2, "adminCooperatorName");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameNotBetween(String value1, String value2) {
            addCriterion("admin_cooperator_name not between", value1, value2, "adminCooperatorName");
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgIsNull() {
            addCriterion("deliver_failed_msg is null");
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgIsNotNull() {
            addCriterion("deliver_failed_msg is not null");
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgEqualTo(String value) {
            addCriterion("deliver_failed_msg =", value, "deliverFailedMsg");
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_failed_msg = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgNotEqualTo(String value) {
            addCriterion("deliver_failed_msg <>", value, "deliverFailedMsg");
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_failed_msg <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgGreaterThan(String value) {
            addCriterion("deliver_failed_msg >", value, "deliverFailedMsg");
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_failed_msg > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgGreaterThanOrEqualTo(String value) {
            addCriterion("deliver_failed_msg >=", value, "deliverFailedMsg");
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_failed_msg >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgLessThan(String value) {
            addCriterion("deliver_failed_msg <", value, "deliverFailedMsg");
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_failed_msg < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgLessThanOrEqualTo(String value) {
            addCriterion("deliver_failed_msg <=", value, "deliverFailedMsg");
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("deliver_failed_msg <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgLike(String value) {
            addCriterion("deliver_failed_msg like", value, "deliverFailedMsg");
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgNotLike(String value) {
            addCriterion("deliver_failed_msg not like", value, "deliverFailedMsg");
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgIn(List<String> values) {
            addCriterion("deliver_failed_msg in", values, "deliverFailedMsg");
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgNotIn(List<String> values) {
            addCriterion("deliver_failed_msg not in", values, "deliverFailedMsg");
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgBetween(String value1, String value2) {
            addCriterion("deliver_failed_msg between", value1, value2, "deliverFailedMsg");
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgNotBetween(String value1, String value2) {
            addCriterion("deliver_failed_msg not between", value1, value2, "deliverFailedMsg");
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionIsNull() {
            addCriterion("after_market_version is null");
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionIsNotNull() {
            addCriterion("after_market_version is not null");
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionEqualTo(String value) {
            addCriterion("after_market_version =", value, "afterMarketVersion");
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_version = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionNotEqualTo(String value) {
            addCriterion("after_market_version <>", value, "afterMarketVersion");
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_version <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionGreaterThan(String value) {
            addCriterion("after_market_version >", value, "afterMarketVersion");
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_version > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionGreaterThanOrEqualTo(String value) {
            addCriterion("after_market_version >=", value, "afterMarketVersion");
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_version >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionLessThan(String value) {
            addCriterion("after_market_version <", value, "afterMarketVersion");
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_version < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionLessThanOrEqualTo(String value) {
            addCriterion("after_market_version <=", value, "afterMarketVersion");
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("after_market_version <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionLike(String value) {
            addCriterion("after_market_version like", value, "afterMarketVersion");
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionNotLike(String value) {
            addCriterion("after_market_version not like", value, "afterMarketVersion");
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionIn(List<String> values) {
            addCriterion("after_market_version in", values, "afterMarketVersion");
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionNotIn(List<String> values) {
            addCriterion("after_market_version not in", values, "afterMarketVersion");
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionBetween(String value1, String value2) {
            addCriterion("after_market_version between", value1, value2, "afterMarketVersion");
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionNotBetween(String value1, String value2) {
            addCriterion("after_market_version not between", value1, value2, "afterMarketVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionIsNull() {
            addCriterion("spu_offering_version is null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionIsNotNull() {
            addCriterion("spu_offering_version is not null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionEqualTo(String value) {
            addCriterion("spu_offering_version =", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_version = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotEqualTo(String value) {
            addCriterion("spu_offering_version <>", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_version <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionGreaterThan(String value) {
            addCriterion("spu_offering_version >", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_version > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionGreaterThanOrEqualTo(String value) {
            addCriterion("spu_offering_version >=", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_version >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLessThan(String value) {
            addCriterion("spu_offering_version <", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_version < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLessThanOrEqualTo(String value) {
            addCriterion("spu_offering_version <=", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_version <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLike(String value) {
            addCriterion("spu_offering_version like", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotLike(String value) {
            addCriterion("spu_offering_version not like", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionIn(List<String> values) {
            addCriterion("spu_offering_version in", values, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotIn(List<String> values) {
            addCriterion("spu_offering_version not in", values, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionBetween(String value1, String value2) {
            addCriterion("spu_offering_version between", value1, value2, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotBetween(String value1, String value2) {
            addCriterion("spu_offering_version not between", value1, value2, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionIsNull() {
            addCriterion("sku_offering_version is null");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionIsNotNull() {
            addCriterion("sku_offering_version is not null");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionEqualTo(String value) {
            addCriterion("sku_offering_version =", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_version = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionNotEqualTo(String value) {
            addCriterion("sku_offering_version <>", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_version <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionGreaterThan(String value) {
            addCriterion("sku_offering_version >", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_version > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionGreaterThanOrEqualTo(String value) {
            addCriterion("sku_offering_version >=", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_version >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionLessThan(String value) {
            addCriterion("sku_offering_version <", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_version < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionLessThanOrEqualTo(String value) {
            addCriterion("sku_offering_version <=", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_version <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionLike(String value) {
            addCriterion("sku_offering_version like", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionNotLike(String value) {
            addCriterion("sku_offering_version not like", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionIn(List<String> values) {
            addCriterion("sku_offering_version in", values, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionNotIn(List<String> values) {
            addCriterion("sku_offering_version not in", values, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionBetween(String value1, String value2) {
            addCriterion("sku_offering_version between", value1, value2, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionNotBetween(String value1, String value2) {
            addCriterion("sku_offering_version not between", value1, value2, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionIsNull() {
            addCriterion("atom_offering_version is null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionIsNotNull() {
            addCriterion("atom_offering_version is not null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionEqualTo(String value) {
            addCriterion("atom_offering_version =", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_version = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionNotEqualTo(String value) {
            addCriterion("atom_offering_version <>", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionNotEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_version <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionGreaterThan(String value) {
            addCriterion("atom_offering_version >", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionGreaterThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_version > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionGreaterThanOrEqualTo(String value) {
            addCriterion("atom_offering_version >=", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionGreaterThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_version >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionLessThan(String value) {
            addCriterion("atom_offering_version <", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionLessThanColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_version < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionLessThanOrEqualTo(String value) {
            addCriterion("atom_offering_version <=", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionLessThanOrEqualToColumn(AfterMarketOrder2cOfferingInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_version <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionLike(String value) {
            addCriterion("atom_offering_version like", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionNotLike(String value) {
            addCriterion("atom_offering_version not like", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionIn(List<String> values) {
            addCriterion("atom_offering_version in", values, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionNotIn(List<String> values) {
            addCriterion("atom_offering_version not in", values, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionBetween(String value1, String value2) {
            addCriterion("atom_offering_version between", value1, value2, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionNotBetween(String value1, String value2) {
            addCriterion("atom_offering_version not between", value1, value2, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andServiceOrderIdLikeInsensitive(String value) {
            addCriterion("upper(service_order_id) like", value.toUpperCase(), "serviceOrderId");
            return (Criteria) this;
        }

        public Criteria andAfterMarketCodeLikeInsensitive(String value) {
            addCriterion("upper(after_market_code) like", value.toUpperCase(), "afterMarketCode");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderNameLikeInsensitive(String value) {
            addCriterion("upper(present_send_order_name) like", value.toUpperCase(), "presentSendOrderName");
            return (Criteria) this;
        }

        public Criteria andPresentSendOrderPhoneLikeInsensitive(String value) {
            addCriterion("upper(present_send_order_phone) like", value.toUpperCase(), "presentSendOrderPhone");
            return (Criteria) this;
        }

        public Criteria andSendOrderCompanyLikeInsensitive(String value) {
            addCriterion("upper(send_order_company) like", value.toUpperCase(), "sendOrderCompany");
            return (Criteria) this;
        }

        public Criteria andUserIdLikeInsensitive(String value) {
            addCriterion("upper(user_id) like", value.toUpperCase(), "userId");
            return (Criteria) this;
        }

        public Criteria andAfterMarketNameLikeInsensitive(String value) {
            addCriterion("upper(after_market_name) like", value.toUpperCase(), "afterMarketName");
            return (Criteria) this;
        }

        public Criteria andAfterMarketSettlePriceLikeInsensitive(String value) {
            addCriterion("upper(after_market_settle_price) like", value.toUpperCase(), "afterMarketSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorIdLikeInsensitive(String value) {
            addCriterion("upper(admin_cooperator_id) like", value.toUpperCase(), "adminCooperatorId");
            return (Criteria) this;
        }

        public Criteria andInstallManagerIdLikeInsensitive(String value) {
            addCriterion("upper(install_manager_id) like", value.toUpperCase(), "installManagerId");
            return (Criteria) this;
        }

        public Criteria andProvinceInstallPlatformLikeInsensitive(String value) {
            addCriterion("upper(province_install_platform) like", value.toUpperCase(), "provinceInstallPlatform");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(sku_offering_code) like", value.toUpperCase(), "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(atom_offering_code) like", value.toUpperCase(), "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(spu_offering_code) like", value.toUpperCase(), "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingClassLikeInsensitive(String value) {
            addCriterion("upper(spu_offering_class) like", value.toUpperCase(), "spuOfferingClass");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameLikeInsensitive(String value) {
            addCriterion("upper(sku_offering_name) like", value.toUpperCase(), "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameLikeInsensitive(String value) {
            addCriterion("upper(atom_offering_name) like", value.toUpperCase(), "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAdminCooperatorNameLikeInsensitive(String value) {
            addCriterion("upper(admin_cooperator_name) like", value.toUpperCase(), "adminCooperatorName");
            return (Criteria) this;
        }

        public Criteria andDeliverFailedMsgLikeInsensitive(String value) {
            addCriterion("upper(deliver_failed_msg) like", value.toUpperCase(), "deliverFailedMsg");
            return (Criteria) this;
        }

        public Criteria andAfterMarketVersionLikeInsensitive(String value) {
            addCriterion("upper(after_market_version) like", value.toUpperCase(), "afterMarketVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLikeInsensitive(String value) {
            addCriterion("upper(spu_offering_version) like", value.toUpperCase(), "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionLikeInsensitive(String value) {
            addCriterion("upper(sku_offering_version) like", value.toUpperCase(), "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionLikeInsensitive(String value) {
            addCriterion("upper(atom_offering_version) like", value.toUpperCase(), "atomOfferingVersion");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Thu May 29 15:25:45 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        private AfterMarketOrder2cOfferingInfoExample example;

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        protected Criteria(AfterMarketOrder2cOfferingInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        public AfterMarketOrder2cOfferingInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Thu May 29 15:25:45 CST 2025
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        void example(com.chinamobile.iot.sc.pojo.entity.AfterMarketOrder2cOfferingInfoExample example);
    }
}