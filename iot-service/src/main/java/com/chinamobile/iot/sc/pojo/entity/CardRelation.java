package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class CardRelation implements Serializable {
    private String id;

    private String imei;

    private String tempIccid;

    private String clientName;

    private String productNum;

    private String sn;

    private String orderId;

    private String orderAtomInfoId;

    private String msisdn;

    private String sellStatus;

    private String terminalType;

    private String beId;

    private String location;

    private String deviceVersion;

    private String misNum;

    private String orderSource;

    private String channel;

    private String deviceType;

    private String supply;

    private String contractTerm;

    private String termianlOwner;

    private String deviceCartonNum;

    private String deviceAttr;

    private String terminalCondition;

    private String inventoryFlag;

    private String useMode;

    private String physicsFlag;

    private String conditionTime;

    private String teminalCreateTime;

    private String teminalRemark;

    private String recycleSource;

    private String chabaMsisdn;

    private String templateId;

    private String templateName;

    private String custCode;

    private String custName;

    private Date cardDeliverTime;

    private Date createTime;

    private Date updateTime;

    private String importNum;

    private String createdUser;

    private String createdUserName;

    private Date deleteTime;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public CardRelation withId(String id) {
        this.setId(id);
        return this;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getImei() {
        return imei;
    }

    public CardRelation withImei(String imei) {
        this.setImei(imei);
        return this;
    }

    public void setImei(String imei) {
        this.imei = imei == null ? null : imei.trim();
    }

    public String getTempIccid() {
        return tempIccid;
    }

    public CardRelation withTempIccid(String tempIccid) {
        this.setTempIccid(tempIccid);
        return this;
    }

    public void setTempIccid(String tempIccid) {
        this.tempIccid = tempIccid == null ? null : tempIccid.trim();
    }

    public String getClientName() {
        return clientName;
    }

    public CardRelation withClientName(String clientName) {
        this.setClientName(clientName);
        return this;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName == null ? null : clientName.trim();
    }

    public String getProductNum() {
        return productNum;
    }

    public CardRelation withProductNum(String productNum) {
        this.setProductNum(productNum);
        return this;
    }

    public void setProductNum(String productNum) {
        this.productNum = productNum == null ? null : productNum.trim();
    }

    public String getSn() {
        return sn;
    }

    public CardRelation withSn(String sn) {
        this.setSn(sn);
        return this;
    }

    public void setSn(String sn) {
        this.sn = sn == null ? null : sn.trim();
    }

    public String getOrderId() {
        return orderId;
    }

    public CardRelation withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    public String getOrderAtomInfoId() {
        return orderAtomInfoId;
    }

    public CardRelation withOrderAtomInfoId(String orderAtomInfoId) {
        this.setOrderAtomInfoId(orderAtomInfoId);
        return this;
    }

    public void setOrderAtomInfoId(String orderAtomInfoId) {
        this.orderAtomInfoId = orderAtomInfoId == null ? null : orderAtomInfoId.trim();
    }

    public String getMsisdn() {
        return msisdn;
    }

    public CardRelation withMsisdn(String msisdn) {
        this.setMsisdn(msisdn);
        return this;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn == null ? null : msisdn.trim();
    }

    public String getSellStatus() {
        return sellStatus;
    }

    public CardRelation withSellStatus(String sellStatus) {
        this.setSellStatus(sellStatus);
        return this;
    }

    public void setSellStatus(String sellStatus) {
        this.sellStatus = sellStatus == null ? null : sellStatus.trim();
    }

    public String getTerminalType() {
        return terminalType;
    }

    public CardRelation withTerminalType(String terminalType) {
        this.setTerminalType(terminalType);
        return this;
    }

    public void setTerminalType(String terminalType) {
        this.terminalType = terminalType == null ? null : terminalType.trim();
    }

    public String getBeId() {
        return beId;
    }

    public CardRelation withBeId(String beId) {
        this.setBeId(beId);
        return this;
    }

    public void setBeId(String beId) {
        this.beId = beId == null ? null : beId.trim();
    }

    public String getLocation() {
        return location;
    }

    public CardRelation withLocation(String location) {
        this.setLocation(location);
        return this;
    }

    public void setLocation(String location) {
        this.location = location == null ? null : location.trim();
    }

    public String getDeviceVersion() {
        return deviceVersion;
    }

    public CardRelation withDeviceVersion(String deviceVersion) {
        this.setDeviceVersion(deviceVersion);
        return this;
    }

    public void setDeviceVersion(String deviceVersion) {
        this.deviceVersion = deviceVersion == null ? null : deviceVersion.trim();
    }

    public String getMisNum() {
        return misNum;
    }

    public CardRelation withMisNum(String misNum) {
        this.setMisNum(misNum);
        return this;
    }

    public void setMisNum(String misNum) {
        this.misNum = misNum == null ? null : misNum.trim();
    }

    public String getOrderSource() {
        return orderSource;
    }

    public CardRelation withOrderSource(String orderSource) {
        this.setOrderSource(orderSource);
        return this;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource == null ? null : orderSource.trim();
    }

    public String getChannel() {
        return channel;
    }

    public CardRelation withChannel(String channel) {
        this.setChannel(channel);
        return this;
    }

    public void setChannel(String channel) {
        this.channel = channel == null ? null : channel.trim();
    }

    public String getDeviceType() {
        return deviceType;
    }

    public CardRelation withDeviceType(String deviceType) {
        this.setDeviceType(deviceType);
        return this;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType == null ? null : deviceType.trim();
    }

    public String getSupply() {
        return supply;
    }

    public CardRelation withSupply(String supply) {
        this.setSupply(supply);
        return this;
    }

    public void setSupply(String supply) {
        this.supply = supply == null ? null : supply.trim();
    }

    public String getContractTerm() {
        return contractTerm;
    }

    public CardRelation withContractTerm(String contractTerm) {
        this.setContractTerm(contractTerm);
        return this;
    }

    public void setContractTerm(String contractTerm) {
        this.contractTerm = contractTerm == null ? null : contractTerm.trim();
    }

    public String getTermianlOwner() {
        return termianlOwner;
    }

    public CardRelation withTermianlOwner(String termianlOwner) {
        this.setTermianlOwner(termianlOwner);
        return this;
    }

    public void setTermianlOwner(String termianlOwner) {
        this.termianlOwner = termianlOwner == null ? null : termianlOwner.trim();
    }

    public String getDeviceCartonNum() {
        return deviceCartonNum;
    }

    public CardRelation withDeviceCartonNum(String deviceCartonNum) {
        this.setDeviceCartonNum(deviceCartonNum);
        return this;
    }

    public void setDeviceCartonNum(String deviceCartonNum) {
        this.deviceCartonNum = deviceCartonNum == null ? null : deviceCartonNum.trim();
    }

    public String getDeviceAttr() {
        return deviceAttr;
    }

    public CardRelation withDeviceAttr(String deviceAttr) {
        this.setDeviceAttr(deviceAttr);
        return this;
    }

    public void setDeviceAttr(String deviceAttr) {
        this.deviceAttr = deviceAttr == null ? null : deviceAttr.trim();
    }

    public String getTerminalCondition() {
        return terminalCondition;
    }

    public CardRelation withTerminalCondition(String terminalCondition) {
        this.setTerminalCondition(terminalCondition);
        return this;
    }

    public void setTerminalCondition(String terminalCondition) {
        this.terminalCondition = terminalCondition == null ? null : terminalCondition.trim();
    }

    public String getInventoryFlag() {
        return inventoryFlag;
    }

    public CardRelation withInventoryFlag(String inventoryFlag) {
        this.setInventoryFlag(inventoryFlag);
        return this;
    }

    public void setInventoryFlag(String inventoryFlag) {
        this.inventoryFlag = inventoryFlag == null ? null : inventoryFlag.trim();
    }

    public String getUseMode() {
        return useMode;
    }

    public CardRelation withUseMode(String useMode) {
        this.setUseMode(useMode);
        return this;
    }

    public void setUseMode(String useMode) {
        this.useMode = useMode == null ? null : useMode.trim();
    }

    public String getPhysicsFlag() {
        return physicsFlag;
    }

    public CardRelation withPhysicsFlag(String physicsFlag) {
        this.setPhysicsFlag(physicsFlag);
        return this;
    }

    public void setPhysicsFlag(String physicsFlag) {
        this.physicsFlag = physicsFlag == null ? null : physicsFlag.trim();
    }

    public String getConditionTime() {
        return conditionTime;
    }

    public CardRelation withConditionTime(String conditionTime) {
        this.setConditionTime(conditionTime);
        return this;
    }

    public void setConditionTime(String conditionTime) {
        this.conditionTime = conditionTime == null ? null : conditionTime.trim();
    }

    public String getTeminalCreateTime() {
        return teminalCreateTime;
    }

    public CardRelation withTeminalCreateTime(String teminalCreateTime) {
        this.setTeminalCreateTime(teminalCreateTime);
        return this;
    }

    public void setTeminalCreateTime(String teminalCreateTime) {
        this.teminalCreateTime = teminalCreateTime == null ? null : teminalCreateTime.trim();
    }

    public String getTeminalRemark() {
        return teminalRemark;
    }

    public CardRelation withTeminalRemark(String teminalRemark) {
        this.setTeminalRemark(teminalRemark);
        return this;
    }

    public void setTeminalRemark(String teminalRemark) {
        this.teminalRemark = teminalRemark == null ? null : teminalRemark.trim();
    }

    public String getRecycleSource() {
        return recycleSource;
    }

    public CardRelation withRecycleSource(String recycleSource) {
        this.setRecycleSource(recycleSource);
        return this;
    }

    public void setRecycleSource(String recycleSource) {
        this.recycleSource = recycleSource == null ? null : recycleSource.trim();
    }

    public String getChabaMsisdn() {
        return chabaMsisdn;
    }

    public CardRelation withChabaMsisdn(String chabaMsisdn) {
        this.setChabaMsisdn(chabaMsisdn);
        return this;
    }

    public void setChabaMsisdn(String chabaMsisdn) {
        this.chabaMsisdn = chabaMsisdn == null ? null : chabaMsisdn.trim();
    }

    public String getTemplateId() {
        return templateId;
    }

    public CardRelation withTemplateId(String templateId) {
        this.setTemplateId(templateId);
        return this;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId == null ? null : templateId.trim();
    }

    public String getTemplateName() {
        return templateName;
    }

    public CardRelation withTemplateName(String templateName) {
        this.setTemplateName(templateName);
        return this;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName == null ? null : templateName.trim();
    }

    public String getCustCode() {
        return custCode;
    }

    public CardRelation withCustCode(String custCode) {
        this.setCustCode(custCode);
        return this;
    }

    public void setCustCode(String custCode) {
        this.custCode = custCode == null ? null : custCode.trim();
    }

    public String getCustName() {
        return custName;
    }

    public CardRelation withCustName(String custName) {
        this.setCustName(custName);
        return this;
    }

    public void setCustName(String custName) {
        this.custName = custName == null ? null : custName.trim();
    }

    public Date getCardDeliverTime() {
        return cardDeliverTime;
    }

    public CardRelation withCardDeliverTime(Date cardDeliverTime) {
        this.setCardDeliverTime(cardDeliverTime);
        return this;
    }

    public void setCardDeliverTime(Date cardDeliverTime) {
        this.cardDeliverTime = cardDeliverTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public CardRelation withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public CardRelation withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getImportNum() {
        return importNum;
    }

    public CardRelation withImportNum(String importNum) {
        this.setImportNum(importNum);
        return this;
    }

    public void setImportNum(String importNum) {
        this.importNum = importNum == null ? null : importNum.trim();
    }

    public String getCreatedUser() {
        return createdUser;
    }

    public CardRelation withCreatedUser(String createdUser) {
        this.setCreatedUser(createdUser);
        return this;
    }

    public void setCreatedUser(String createdUser) {
        this.createdUser = createdUser == null ? null : createdUser.trim();
    }

    public String getCreatedUserName() {
        return createdUserName;
    }

    public CardRelation withCreatedUserName(String createdUserName) {
        this.setCreatedUserName(createdUserName);
        return this;
    }

    public void setCreatedUserName(String createdUserName) {
        this.createdUserName = createdUserName == null ? null : createdUserName.trim();
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public CardRelation withDeleteTime(Date deleteTime) {
        this.setDeleteTime(deleteTime);
        return this;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", imei=").append(imei);
        sb.append(", tempIccid=").append(tempIccid);
        sb.append(", clientName=").append(clientName);
        sb.append(", productNum=").append(productNum);
        sb.append(", sn=").append(sn);
        sb.append(", orderId=").append(orderId);
        sb.append(", orderAtomInfoId=").append(orderAtomInfoId);
        sb.append(", msisdn=").append(msisdn);
        sb.append(", sellStatus=").append(sellStatus);
        sb.append(", terminalType=").append(terminalType);
        sb.append(", beId=").append(beId);
        sb.append(", location=").append(location);
        sb.append(", deviceVersion=").append(deviceVersion);
        sb.append(", misNum=").append(misNum);
        sb.append(", orderSource=").append(orderSource);
        sb.append(", channel=").append(channel);
        sb.append(", deviceType=").append(deviceType);
        sb.append(", supply=").append(supply);
        sb.append(", contractTerm=").append(contractTerm);
        sb.append(", termianlOwner=").append(termianlOwner);
        sb.append(", deviceCartonNum=").append(deviceCartonNum);
        sb.append(", deviceAttr=").append(deviceAttr);
        sb.append(", terminalCondition=").append(terminalCondition);
        sb.append(", inventoryFlag=").append(inventoryFlag);
        sb.append(", useMode=").append(useMode);
        sb.append(", physicsFlag=").append(physicsFlag);
        sb.append(", conditionTime=").append(conditionTime);
        sb.append(", teminalCreateTime=").append(teminalCreateTime);
        sb.append(", teminalRemark=").append(teminalRemark);
        sb.append(", recycleSource=").append(recycleSource);
        sb.append(", chabaMsisdn=").append(chabaMsisdn);
        sb.append(", templateId=").append(templateId);
        sb.append(", templateName=").append(templateName);
        sb.append(", custCode=").append(custCode);
        sb.append(", custName=").append(custName);
        sb.append(", cardDeliverTime=").append(cardDeliverTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", importNum=").append(importNum);
        sb.append(", createdUser=").append(createdUser);
        sb.append(", createdUserName=").append(createdUserName);
        sb.append(", deleteTime=").append(deleteTime);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CardRelation other = (CardRelation) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getImei() == null ? other.getImei() == null : this.getImei().equals(other.getImei()))
            && (this.getTempIccid() == null ? other.getTempIccid() == null : this.getTempIccid().equals(other.getTempIccid()))
            && (this.getClientName() == null ? other.getClientName() == null : this.getClientName().equals(other.getClientName()))
            && (this.getProductNum() == null ? other.getProductNum() == null : this.getProductNum().equals(other.getProductNum()))
            && (this.getSn() == null ? other.getSn() == null : this.getSn().equals(other.getSn()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getOrderAtomInfoId() == null ? other.getOrderAtomInfoId() == null : this.getOrderAtomInfoId().equals(other.getOrderAtomInfoId()))
            && (this.getMsisdn() == null ? other.getMsisdn() == null : this.getMsisdn().equals(other.getMsisdn()))
            && (this.getSellStatus() == null ? other.getSellStatus() == null : this.getSellStatus().equals(other.getSellStatus()))
            && (this.getTerminalType() == null ? other.getTerminalType() == null : this.getTerminalType().equals(other.getTerminalType()))
            && (this.getBeId() == null ? other.getBeId() == null : this.getBeId().equals(other.getBeId()))
            && (this.getLocation() == null ? other.getLocation() == null : this.getLocation().equals(other.getLocation()))
            && (this.getDeviceVersion() == null ? other.getDeviceVersion() == null : this.getDeviceVersion().equals(other.getDeviceVersion()))
            && (this.getMisNum() == null ? other.getMisNum() == null : this.getMisNum().equals(other.getMisNum()))
            && (this.getOrderSource() == null ? other.getOrderSource() == null : this.getOrderSource().equals(other.getOrderSource()))
            && (this.getChannel() == null ? other.getChannel() == null : this.getChannel().equals(other.getChannel()))
            && (this.getDeviceType() == null ? other.getDeviceType() == null : this.getDeviceType().equals(other.getDeviceType()))
            && (this.getSupply() == null ? other.getSupply() == null : this.getSupply().equals(other.getSupply()))
            && (this.getContractTerm() == null ? other.getContractTerm() == null : this.getContractTerm().equals(other.getContractTerm()))
            && (this.getTermianlOwner() == null ? other.getTermianlOwner() == null : this.getTermianlOwner().equals(other.getTermianlOwner()))
            && (this.getDeviceCartonNum() == null ? other.getDeviceCartonNum() == null : this.getDeviceCartonNum().equals(other.getDeviceCartonNum()))
            && (this.getDeviceAttr() == null ? other.getDeviceAttr() == null : this.getDeviceAttr().equals(other.getDeviceAttr()))
            && (this.getTerminalCondition() == null ? other.getTerminalCondition() == null : this.getTerminalCondition().equals(other.getTerminalCondition()))
            && (this.getInventoryFlag() == null ? other.getInventoryFlag() == null : this.getInventoryFlag().equals(other.getInventoryFlag()))
            && (this.getUseMode() == null ? other.getUseMode() == null : this.getUseMode().equals(other.getUseMode()))
            && (this.getPhysicsFlag() == null ? other.getPhysicsFlag() == null : this.getPhysicsFlag().equals(other.getPhysicsFlag()))
            && (this.getConditionTime() == null ? other.getConditionTime() == null : this.getConditionTime().equals(other.getConditionTime()))
            && (this.getTeminalCreateTime() == null ? other.getTeminalCreateTime() == null : this.getTeminalCreateTime().equals(other.getTeminalCreateTime()))
            && (this.getTeminalRemark() == null ? other.getTeminalRemark() == null : this.getTeminalRemark().equals(other.getTeminalRemark()))
            && (this.getRecycleSource() == null ? other.getRecycleSource() == null : this.getRecycleSource().equals(other.getRecycleSource()))
            && (this.getChabaMsisdn() == null ? other.getChabaMsisdn() == null : this.getChabaMsisdn().equals(other.getChabaMsisdn()))
            && (this.getTemplateId() == null ? other.getTemplateId() == null : this.getTemplateId().equals(other.getTemplateId()))
            && (this.getTemplateName() == null ? other.getTemplateName() == null : this.getTemplateName().equals(other.getTemplateName()))
            && (this.getCustCode() == null ? other.getCustCode() == null : this.getCustCode().equals(other.getCustCode()))
            && (this.getCustName() == null ? other.getCustName() == null : this.getCustName().equals(other.getCustName()))
            && (this.getCardDeliverTime() == null ? other.getCardDeliverTime() == null : this.getCardDeliverTime().equals(other.getCardDeliverTime()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getImportNum() == null ? other.getImportNum() == null : this.getImportNum().equals(other.getImportNum()))
            && (this.getCreatedUser() == null ? other.getCreatedUser() == null : this.getCreatedUser().equals(other.getCreatedUser()))
            && (this.getCreatedUserName() == null ? other.getCreatedUserName() == null : this.getCreatedUserName().equals(other.getCreatedUserName()))
            && (this.getDeleteTime() == null ? other.getDeleteTime() == null : this.getDeleteTime().equals(other.getDeleteTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getImei() == null) ? 0 : getImei().hashCode());
        result = prime * result + ((getTempIccid() == null) ? 0 : getTempIccid().hashCode());
        result = prime * result + ((getClientName() == null) ? 0 : getClientName().hashCode());
        result = prime * result + ((getProductNum() == null) ? 0 : getProductNum().hashCode());
        result = prime * result + ((getSn() == null) ? 0 : getSn().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getOrderAtomInfoId() == null) ? 0 : getOrderAtomInfoId().hashCode());
        result = prime * result + ((getMsisdn() == null) ? 0 : getMsisdn().hashCode());
        result = prime * result + ((getSellStatus() == null) ? 0 : getSellStatus().hashCode());
        result = prime * result + ((getTerminalType() == null) ? 0 : getTerminalType().hashCode());
        result = prime * result + ((getBeId() == null) ? 0 : getBeId().hashCode());
        result = prime * result + ((getLocation() == null) ? 0 : getLocation().hashCode());
        result = prime * result + ((getDeviceVersion() == null) ? 0 : getDeviceVersion().hashCode());
        result = prime * result + ((getMisNum() == null) ? 0 : getMisNum().hashCode());
        result = prime * result + ((getOrderSource() == null) ? 0 : getOrderSource().hashCode());
        result = prime * result + ((getChannel() == null) ? 0 : getChannel().hashCode());
        result = prime * result + ((getDeviceType() == null) ? 0 : getDeviceType().hashCode());
        result = prime * result + ((getSupply() == null) ? 0 : getSupply().hashCode());
        result = prime * result + ((getContractTerm() == null) ? 0 : getContractTerm().hashCode());
        result = prime * result + ((getTermianlOwner() == null) ? 0 : getTermianlOwner().hashCode());
        result = prime * result + ((getDeviceCartonNum() == null) ? 0 : getDeviceCartonNum().hashCode());
        result = prime * result + ((getDeviceAttr() == null) ? 0 : getDeviceAttr().hashCode());
        result = prime * result + ((getTerminalCondition() == null) ? 0 : getTerminalCondition().hashCode());
        result = prime * result + ((getInventoryFlag() == null) ? 0 : getInventoryFlag().hashCode());
        result = prime * result + ((getUseMode() == null) ? 0 : getUseMode().hashCode());
        result = prime * result + ((getPhysicsFlag() == null) ? 0 : getPhysicsFlag().hashCode());
        result = prime * result + ((getConditionTime() == null) ? 0 : getConditionTime().hashCode());
        result = prime * result + ((getTeminalCreateTime() == null) ? 0 : getTeminalCreateTime().hashCode());
        result = prime * result + ((getTeminalRemark() == null) ? 0 : getTeminalRemark().hashCode());
        result = prime * result + ((getRecycleSource() == null) ? 0 : getRecycleSource().hashCode());
        result = prime * result + ((getChabaMsisdn() == null) ? 0 : getChabaMsisdn().hashCode());
        result = prime * result + ((getTemplateId() == null) ? 0 : getTemplateId().hashCode());
        result = prime * result + ((getTemplateName() == null) ? 0 : getTemplateName().hashCode());
        result = prime * result + ((getCustCode() == null) ? 0 : getCustCode().hashCode());
        result = prime * result + ((getCustName() == null) ? 0 : getCustName().hashCode());
        result = prime * result + ((getCardDeliverTime() == null) ? 0 : getCardDeliverTime().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getImportNum() == null) ? 0 : getImportNum().hashCode());
        result = prime * result + ((getCreatedUser() == null) ? 0 : getCreatedUser().hashCode());
        result = prime * result + ((getCreatedUserName() == null) ? 0 : getCreatedUserName().hashCode());
        result = prime * result + ((getDeleteTime() == null) ? 0 : getDeleteTime().hashCode());
        return result;
    }

    public enum Column {
        id("id", "id", "VARCHAR", false),
        imei("imei", "imei", "VARCHAR", false),
        tempIccid("temp_iccid", "tempIccid", "VARCHAR", false),
        clientName("client_name", "clientName", "VARCHAR", false),
        productNum("product_num", "productNum", "VARCHAR", false),
        sn("sn", "sn", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        orderAtomInfoId("order_atom_info_id", "orderAtomInfoId", "VARCHAR", false),
        msisdn("msisdn", "msisdn", "VARCHAR", false),
        sellStatus("sell_status", "sellStatus", "VARCHAR", false),
        terminalType("terminal_type", "terminalType", "VARCHAR", false),
        beId("be_id", "beId", "VARCHAR", false),
        location("location", "location", "VARCHAR", false),
        deviceVersion("device_version", "deviceVersion", "VARCHAR", false),
        misNum("mis_num", "misNum", "VARCHAR", false),
        orderSource("order_source", "orderSource", "VARCHAR", false),
        channel("channel", "channel", "VARCHAR", false),
        deviceType("device_type", "deviceType", "VARCHAR", false),
        supply("supply", "supply", "VARCHAR", false),
        contractTerm("contract_term", "contractTerm", "VARCHAR", false),
        termianlOwner("termianl_owner", "termianlOwner", "VARCHAR", false),
        deviceCartonNum("device_carton_num", "deviceCartonNum", "VARCHAR", false),
        deviceAttr("device_attr", "deviceAttr", "VARCHAR", false),
        terminalCondition("terminal_condition", "terminalCondition", "VARCHAR", false),
        inventoryFlag("inventory_flag", "inventoryFlag", "VARCHAR", false),
        useMode("use_mode", "useMode", "VARCHAR", false),
        physicsFlag("physics_flag", "physicsFlag", "VARCHAR", false),
        conditionTime("condition_time", "conditionTime", "VARCHAR", false),
        teminalCreateTime("teminal_create_time", "teminalCreateTime", "VARCHAR", false),
        teminalRemark("teminal_remark", "teminalRemark", "VARCHAR", false),
        recycleSource("recycle_source", "recycleSource", "VARCHAR", false),
        chabaMsisdn("chaba_msisdn", "chabaMsisdn", "VARCHAR", false),
        templateId("template_id", "templateId", "VARCHAR", false),
        templateName("template_name", "templateName", "VARCHAR", false),
        custCode("cust_code", "custCode", "VARCHAR", false),
        custName("cust_name", "custName", "VARCHAR", false),
        cardDeliverTime("card_deliver_time", "cardDeliverTime", "TIMESTAMP", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        importNum("import_num", "importNum", "VARCHAR", false),
        createdUser("created_user", "createdUser", "VARCHAR", false),
        createdUserName("created_user_name", "createdUserName", "VARCHAR", false),
        deleteTime("delete_time", "deleteTime", "TIMESTAMP", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}