package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.config.ThreadExecutorConfig;
import com.chinamobile.iot.sc.entity.iot.IotSearchMallLinkRequest;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.CardInfo;
import com.chinamobile.iot.sc.pojo.dto.CardRelationImportInfoDetailDTO;
import com.chinamobile.iot.sc.pojo.dto.KXInventoryDetailImeiDTO;
import com.chinamobile.iot.sc.pojo.dto.KXInventoryDetailLocationDTO;
import com.chinamobile.iot.sc.pojo.dto.ProMaterialDTO;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryMainInfo;
import com.chinamobile.iot.sc.pojo.entity.ServicePackLimitAmount;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.ProductConfigVO;
import com.chinamobile.iot.sc.pojo.vo.ProductMaterialDetailItemVO;
import com.chinamobile.iot.sc.pojo.vo.ProductMaterialsItemVO;
import com.chinamobile.iot.sc.request.*;
import com.chinamobile.iot.sc.request.inventory.LimitListRequest;
import com.chinamobile.iot.sc.response.SimpleItemDTO;
import com.chinamobile.iot.sc.response.web.*;
import com.chinamobile.iot.sc.service.*;
import com.chinamobile.iot.sc.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @Author: YSC
 * @Date: 2021/11/10 10:10
 * @Description:
 */
@RestController
@RequestMapping("/osweb")
@Slf4j
public class ProductWebController {
    @Autowired
    private IInventoryService iInventoryService;
    @Autowired
    private IProductService iProductService;

    @Resource
    private OsMallSyncService osMallSyncService;

    @Resource
    private CardInfoService cardInfoService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IProductNavigationDirectoryService productNavigationDirectoryService;

//    /**
//     * 添加合作伙伴
//     *
//     * @param cooperAddRequest
//     * @return
//     */
//    @Auth(authCode = BaseConstant.IOT_MERCHANDISE_BUSINESS)
//    @PostMapping("/product/addCooperator")
//    public BaseAnswer<Void> addCooperator(@RequestBody @Valid CooperAddRequest cooperAddRequest,
//                                          @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
//        return iProductService.addCooperator(cooperAddRequest,loginIfo4Redis);
//    }

    /**
     * 商品列表
     * @param configAllStatus 配置状态 0--未配置 1--已配置
     * @param page 页
     * @param num 页数
     * @return
     */
    @Auth(authCode = BaseConstant.IOT_MERCHANDISE_QUERY)
    @GetMapping("/product/list")
    public BaseAnswer<PageData<ProductInfoDTO>> getProductList(@RequestParam(value = "spuOfferingName", required = false) String spuOfferingName,
                                                               @RequestParam(value = "spuOfferingCode", required = false) String spuOfferingCode,
                                                               @RequestParam(value = "skuOfferingName", required = false) String skuOfferingName,
                                                               @RequestParam(value = "skuOfferingCode", required = false) String skuOfferingCode,
                                                               @RequestParam(value = "atomOfferingName", required = false) String atomOfferingName,
                                                               @RequestParam(value = "spuOfferingClass", required = false) List<String> spuOfferingClass,
                                                               @RequestParam(value = "partnerName", required = false) String partnerName,
                                                               @RequestParam(value = "cooperatorName", required = false) String cooperatorName,
                                                               @RequestParam(value = "spuOfferingStatus", required = false) List<String> spuOfferingStatus,
                                                               @RequestParam(value = "skuOfferingStatus", required = false) List<String> skuOfferingStatus,
                                                               @RequestParam(value = "configAllStatus", required = false) String configAllStatus,
                                                               @RequestParam(value = "h5Key", required = false) String h5Key,
                                                               @RequestParam(value = "h5SpuOfferingClasses", required = false) List<String> h5SpuOfferingClasses,
                                                               @RequestParam(value = "templateId", required = false) String templateId,
                                                               @RequestParam(value = "templateName", required = false) String templateName,
                                                               @RequestParam(value = "deviceVersion", required = false) String deviceVersion,
                                                               @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                                                               @NotNull(message = "页数必填")@RequestParam("pageNum") Integer page,
                                                               @RequestParam("pageSize") Integer num){
        return iProductService.getProductList(spuOfferingName,spuOfferingCode,skuOfferingName,skuOfferingCode,atomOfferingName,spuOfferingClass,partnerName,
                cooperatorName, spuOfferingStatus,skuOfferingStatus, configAllStatus, h5Key,h5SpuOfferingClasses,
                templateId,templateName,deviceVersion,loginIfo4Redis,page,num);
    }

    /**
     * 查询商品库存
     * @param inventoryStatus 库存状态
     * @param page 页
     * @param num 页数
     * @param loginIfo4Redis redis缓存内容
     * @return
     */
    @GetMapping("/inventory/list")
    @Auth(authCode = BaseConstant.IOT_MERCHANDISE_INVENTORY_QUERY)
    public BaseAnswer<PageData<InventoryInfoDTO>> getInventory(@RequestParam(value = "spuOfferingName", required = false) String spuOfferingName,
                                                               @RequestParam(value = "skuOfferingName", required = false) String skuOfferingName,
                                                               @RequestParam(value = "spuOfferingCode", required = false) String spuOfferingCode,
                                                               @RequestParam(value = "skuOfferingCode", required = false) String skuOfferingCode,
                                                               @RequestParam(value = "atomOfferingName", required = false) String atomOfferingName,
                                                               @RequestParam(value = "spuOfferingClass", required = false) String spuOfferingClass,
                                                               @RequestParam(value = "partnerName", required = false) String partnerName,
                                                               @RequestParam(value = "cooperatorName", required = false) String cooperatorName,
                                                               @RequestParam(value = "inventoryStatus", required = false) Integer inventoryStatus,
                                                               @RequestParam(value = "spuOfferingStatus", required = false) String spuOfferingStatus,
                                                               @RequestParam(value = "skuOfferingStatus", required = false) String skuOfferingStatus,
                                                               @RequestParam(value = "h5Key", required = false) String h5Key,
                                                               @RequestParam(value = "h5SpuOfferingClasses", required = false) List<String> h5SpuOfferingClasses,
                                                               @NotNull(message = "页数必填")@RequestParam("page") Integer page,
                                                               @RequestParam("num") Integer num,
                                                               @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return iInventoryService.getInventoryList(spuOfferingName,skuOfferingName,spuOfferingCode,skuOfferingCode,atomOfferingName,spuOfferingClass,partnerName,cooperatorName,
                inventoryStatus,spuOfferingStatus,skuOfferingStatus, h5Key,h5SpuOfferingClasses,loginIfo4Redis,page,num);
    }

    /**
     * 库存配置
     *
     * @param request
     * @param userId
     * @param loginIfo4Redis
     * @return
     */
    @Auth(authCode = BaseConstant.IOT_MERCHANDISE_INVENTORY)
    @PostMapping("/product/inventory")
    public BaseAnswer<Void> configInventory(@RequestBody @Valid InventoryConfigRequest request,
                                            @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId,
                                            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return iInventoryService.configInventory(request, userId, loginIfo4Redis,ip);
    }

    /**
     * 导出库存(返回二进制流)
     */
    @GetMapping("/inventory/export")
    @Auth(authCode = BaseConstant.IOT_MERCHANDISE_INVENTORY_EXPORT)
    public void exportInventory(@Valid InventoryExportParam inventoryExportParam,
                                @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis)
    {
        String exportPhone = inventoryExportParam.getExportPhone();
        String exportMask = inventoryExportParam.getExportMask() == null?"":inventoryExportParam.getExportMask()+"";
        Boolean isAdmin = loginIfo4Redis.getIsAdmin();
        // 验证当前用户是否超管，及验证码是否正确
        SmsValidUtil.checkSmsValid(isAdmin,exportMask,exportPhone,redisTemplate);
        String startTime = inventoryExportParam.getStartTime();
        String endTime = inventoryExportParam.getEndTime();
        try {
            DateUtils.strToDate(startTime,DateUtils.DEFAULT_DATE_FORMAT);
            DateUtils.strToDate(endTime,DateUtils.DEFAULT_DATE_FORMAT);
        } catch (ParseException e) {
            throw new BusinessException(BaseErrorConstant.DATE_PARSE_ERROR);
        }
        iInventoryService.exportInventory(inventoryExportParam, loginIfo4Redis);
    }


    /**
     * 判断用户是否绑定指定商品，判断用户是否还有未完成订单
     * true 包含
     * false 不包含
     * @param userId
     * @return
     */
    @GetMapping("/internal/judgeInfoByUserId")
    public BaseAnswer<Boolean> judgeInfoByUserId(@RequestParam("userId")String userId){
        return iProductService.judgeInfoByUserId(userId);
    }

    /**
     * 获取SPU商品的分享链接
     * @param request
     * @return
     */
    @PostMapping("/product/internal/getShareUrl")
    public BaseAnswer<String> getShareUrl(@RequestBody IotSearchMallLinkRequest request){
        return iProductService.getShareUrl(request);

    }

    /**
     * 商品信息,商品详情
     * @param id 规格商品id
     * @return
     */

    @GetMapping("/product/config/info")
    public BaseAnswer<ProductConfigVO> getProductConfigInfo(@RequestParam(value = "id") String id){
        return iProductService.getProductConfigInfo(id);
    }

    /**
     * 修改配置项
     *
     * @param productConfigRequest
     * @return
     */
    @PostMapping("/product/config")
    public BaseAnswer<Void> setProductConfig(@RequestBody @Valid ProductConfigRequest productConfigRequest,
                                          @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return iProductService.setProductConfig(productConfigRequest,loginIfo4Redis);
    }

    /**
     * 导出主商品(返回二进制流)
     */
    @GetMapping("/product/export")
    @Auth(authCode = BaseConstant.IOT_MERCHANDISE_QUERY)
    public BaseAnswer productExport(@Valid ProductExportParam param,
                            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){

        //后台执行异步导出，导出结果通过消息中心通知前端
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);

        String startTimeParam = param.getStartTime();
        String endTimeParam = param.getEndTime();
        if (StringUtils.isNotEmpty(startTimeParam)) {
            if (StringUtils.isEmpty(endTimeParam)){
                throw new BusinessException("10039","请选择结束日期");
            }
        }
        if (StringUtils.isNotEmpty(endTimeParam)) {
            if (StringUtils.isEmpty(startTimeParam)){
                throw new BusinessException("10039","请选择开始日期");
            }
        }


        String mask = param.getMask();
        String exportPhone = param.getExportPhone();
        Boolean isAdmin = loginIfo4Redis.getIsAdmin();
        SmsValidUtil.checkSmsValid(isAdmin, mask, exportPhone, redisTemplate);
        ThreadExecutorConfig.executorService.execute(() -> {
            iProductService.productExport(param,loginIfo4Redis,ip);
        });
        return BaseAnswer.success(null);
    }

    /**
     * 获取导出主商品的数量
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping("/product/exportCount")
    @Auth(authCode = BaseConstant.IOT_MERCHANDISE_QUERY)
    public BaseAnswer<Integer> getProductExportCount(@Valid ProductExportParam param,
                              @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){

        //后台执行异步导出，导出结果通过消息中心通知前端
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);

        String startTimeParam = param.getStartTime();
        String endTimeParam = param.getEndTime();
        if (StringUtils.isNotEmpty(startTimeParam)) {
            if (StringUtils.isEmpty(endTimeParam)){
                throw new BusinessException("10039","请选择结束日期");
            }
        }
        if (StringUtils.isNotEmpty(endTimeParam)) {
            if (StringUtils.isEmpty(startTimeParam)){
                throw new BusinessException("10039","请选择开始日期");
            }
        }

        Integer productExportCount = iProductService.getProductExportCount(param, loginIfo4Redis, ip);
        return new BaseAnswer<Integer>().setData(productExportCount);
    }

    /**
     * 更新商品和从合作伙伴历史数据
     * @param atomOfferingId
     * @return
     */
    @PostMapping(value = "/product/handleProductCooperatorRelation")
    public BaseAnswer handleProductCooperatorRelation(@RequestParam(value = "atomOfferingId",required = false)String atomOfferingId){
        iProductService.updateAtomOfferingHistoryCooperator(atomOfferingId);
        return new BaseAnswer();
    }

    /**
     * 处理合作伙伴为“-1”和sku商品合作伙伴不相同的数据
     * @return
     */
    @PostMapping(value = "/product/updateCooperateIdNotEqual")
    public BaseAnswer updateCooperateIdNotEqual(){
        iProductService.updateCooperateIdNotEqual();
        return new BaseAnswer();
    }



    /**
     * 通过excel文件批量配置商品
     * @return
     *
     */
//    @Auth(authCode = {BaseConstant.ADMIN_ROLE,BaseConstant.PRODUCT_RUN})
    @PostMapping("/product/importProductConfig")
    public void importProductConfig(@RequestPart("file") MultipartFile file
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        iProductService.importProductConfig(file,loginIfo4Redis);
    }

    /**
     * 获取已绑定的物料信息
     * @param atomId
     * @return
     */
    @GetMapping("/product/material/configed")
    public BaseAnswer<List<ProductMaterialsItemVO>> getConfigedProductMaterails(@RequestParam String atomId){
        return iProductService.getConfigedMaterials(atomId);
    }

    /**
     * 批量导入商品的专合结算价
     * @param file
     * @param loginIfo4Redis
     * @param response
     */
    @PostMapping("/product/importProductStdPriceConfig")
    public void batchProductStdPriceConfig(@RequestParam MultipartFile file,
                             @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                             HttpServletResponse response){
        String userId = loginIfo4Redis.getUserId();

        String importRedisKey = "import_product_std_config_".concat(userId);
        try {
            if (!ExcelUtils.suffixCheck(file.getOriginalFilename())) {
                //用于前端区分99999，88888是不下载文件，只展示message
                response.setHeader("statecode", "88888");
                response.setHeader("message", URLEncoder.encode("文件格式错误，只能是xlsx,xls类型", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }

            Boolean isUserImport = stringRedisTemplate.hasKey(importRedisKey);
            if (isUserImport){
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("有文件正在导入中，在该文件成功前请不要再次导入", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }

            String fileHeader = "销售组/销售商品编码,商品规格编码,原子商品编码,专合结算价";
            boolean checkExcelHeaders = ExcelHeaderCheckUtil.checkExcelHeaders(file, Arrays.asList(fileHeader.split(",")), 0);
            if (!checkExcelHeaders){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "文件模板错误");
            }

            stringRedisTemplate.opsForValue().set(importRedisKey,importRedisKey,12, TimeUnit.HOURS);

            InputStream fileInputStream = file.getInputStream();
            iProductService.batchProductStdPriceConfig(fileInputStream,loginIfo4Redis,response,importRedisKey);
        } catch (Exception e) {
            stringRedisTemplate.delete(importRedisKey);
            log.error("批量导入商品的专合结算价异常:{}",e);
            if (e instanceof BusinessException){
                throw new BusinessException("10008",((BusinessException)e).getMessage());
            }else{
                throw new BusinessException("10008","批量导入商品的专合结算价异常");
            }
        }
    }

    /**
     * 批量新增商品物料信息
     * @param file
     * @param loginIfo4Redis
     * @param response
     */
    @PostMapping("/product/importProductMaterialConfig")
    public void batchProductMaterialConfig(@RequestParam MultipartFile file,
                                           @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                                           HttpServletResponse response){

        String userId = loginIfo4Redis.getUserId();

        String importRedisKey = "import_product_std_config_".concat(userId);
        try {
            if (!ExcelUtils.suffixCheck(file.getOriginalFilename())) {
                //用于前端区分99999，88888是不下载文件，只展示message
                response.setHeader("statecode", "88888");
                response.setHeader("message", URLEncoder.encode("文件格式错误，只能是xlsx,xls类型", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }

            Boolean isUserImport = stringRedisTemplate.hasKey(importRedisKey);
            if (isUserImport){
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("有文件正在导入中，在该文件成功前请不要再次导入", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }


            List<String> fileHeaderList = new ArrayList();
            fileHeaderList.add(0,"销售组/销售商品编码,商品规格编码,原子商品编码,物料信息,-1,-1");
            fileHeaderList.add(1,"-1,-1,-1,物料编码,物料数量,合同编号");
            boolean checkExcelHeaders = ExcelHeaderCheckUtil.checkExcelMergeHeaders(file, fileHeaderList, 2);
            if (!checkExcelHeaders){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "文件模板错误");
            }

            stringRedisTemplate.opsForValue().set(importRedisKey,importRedisKey,12, TimeUnit.HOURS);

            InputStream fileInputStream = file.getInputStream();
            iProductService.batchProductMaterialConfig(fileInputStream,loginIfo4Redis,response,importRedisKey);
        } catch (Exception e) {
            stringRedisTemplate.delete(importRedisKey);
            log.error("批量导入商品的物料信息异常:{}",e);
            if (e instanceof BusinessException){
                throw new BusinessException("10008",((BusinessException)e).getMessage());
            }else{
                throw new BusinessException("10008","批量导入商品的物料信息异常");
            }
        }
    }


    /**
     * 获取未绑定商品的物料
     * @param atomId
     * @param materialNum
     * @return
     */
    @GetMapping("/product/material/unconfiged")
    public BaseAnswer<List<ProductMaterialDetailItemVO>> getUnConfigedMaterials(@RequestParam String atomId, @RequestParam String materialNum){
        return iProductService.getUnConfigedMaterials(atomId, materialNum);
    }

    @PostMapping("/product/handleCardInfoFile")
    public BaseAnswer<Void> handleCardInfoFile(@RequestParam(value = "cardInfoId") String cardInfoId){
        BaseAnswer baseAnswer = new BaseAnswer<>();
        CardInfo cardInfo = cardInfoService.getCardInfoById(cardInfoId);
        if (!Optional.ofNullable(cardInfo).isPresent()){
            baseAnswer.setStateCode("10004");
            baseAnswer.setMessage("没有卡信息");
            return baseAnswer;
        }
        osMallSyncService.handleCardInfoFile(cardInfo);
        return baseAnswer;
    }

    /**
     * 配置卡+X商品的库存
     * @param configCardXInventoryParam
     * @return
     */
    @PostMapping(value = "/product/configCardXInventory")
    public BaseAnswer configCardXInventory(@RequestBody @Valid ConfigCardXInventoryParam configCardXInventoryParam,
                                           @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        iInventoryService.configCardXInventory(configCardXInventoryParam,
                loginIfo4Redis);
        return new BaseAnswer();
    }

    @PostMapping("/product/material/testsave")
    public BaseAnswer<Void> test(@RequestBody ProMaterialDTO param){
        iProductService.testSave(param);
        return new BaseAnswer<>();
    }


    /**
     * 卡+x配置库存模式
     * @param param
     * @param
     * @return 配置的库存id
     */
    @PostMapping("/inventory/modeConfigKx")
    public BaseAnswer<String> inventoryManagementModeConfigKxById(@RequestBody @Valid InventoryKxPatternParam param){
        String inventoryId = iInventoryService.inventoryManagementModeConfigKx(param.getAtomId(), param.getInventoryManagementModeKx());
        return new BaseAnswer<String>().setData(inventoryId);
    }


    /**
     * 查询卡+x商品库存信息集合
     * @param inventoryId
     * @return
     */
    @GetMapping("/inventory/queryKxInventoryInfo")
    public BaseAnswer< List<DkCardxInventoryInfoDTO>> queryKxInventoryList(@RequestParam("inventoryId")String inventoryId){
        List<DkCardxInventoryInfoDTO> kxInventoryList = iInventoryService.getKxInventoryList(inventoryId);
        return new BaseAnswer< List<DkCardxInventoryInfoDTO>>().setData(kxInventoryList);
    }

    /**
     * 查询卡+x商品库存配置信息集合
     * @param inventoryId
     * @param saleStatus
     * @param cityCode
     * @return
     */
    @GetMapping("/inventory/queryInventoryConfigKx")
    public BaseAnswer< List<InventoryConfigKxDetailsDTO>> queryInventoryConfigKxList(@RequestParam("inventoryId")String inventoryId,
                                                         @RequestParam(value = "saleStatus",required = false)String saleStatus,
                                                         @RequestParam(value = "cityCode",required = false)String cityCode){
        List<InventoryConfigKxDetailsDTO> inventoryConfigKxList = iInventoryService.getInventoryConfigKxList(inventoryId, saleStatus, cityCode);
        return new BaseAnswer< List<InventoryConfigKxDetailsDTO>>().setData(inventoryConfigKxList);
    }


    /**
     * 剔除卡+x关联的imei设备
     * @param id
     * @return
     */
    @DeleteMapping("/inventory/deleteKxInventoryConfig")
    public BaseAnswer<Void> deleteKxInventoryConfigById(@RequestParam("id")String id ,
                                                        @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        iInventoryService.deleteKxInventoryConfig(id,loginIfo4Redis);
        return new BaseAnswer<>();
    }

    /**
     *设置卡+x库存信息配置
     * @param params
     * @return
     */
    @PostMapping("/inventory/setInventoryWarnValueById")
    public BaseAnswer<Void> setInventoryWarnValueById(@RequestBody @Valid InstallInventoryWarnParam params,
                                                      @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO)LoginIfo4Redis loginIfo4Redis){
        iInventoryService.setInventoryWarnValue(params,loginIfo4Redis);
        return new BaseAnswer<>();
    }

    /**
     * (后台接口)割接sku合伙人,从原子商品读取
     */
    @PostMapping("/back/syncSkuCooperatorId")
    public BaseAnswer syncSkuCooperatorId(){
        return iProductService.syncSkuCooperatorId();
    }



    /**
     *设置库存模式
     * @param param
     * @return
     */
    @PostMapping("/inventory/setType")
    public BaseAnswer<Void> setInventoryType(@RequestBody @Valid InventoryTypeRequest param){
        iInventoryService.setInventoryType(param);
        return new BaseAnswer<>();
    }

    /**
     * 查询商品库存
     * @param inventoryStatus 库存状态
     * @param page 页
     * @param num 页数
     * @param loginIfo4Redis redis缓存内容
     * @return
     */
    @GetMapping("/inventory/new/list")
    @Auth(authCode = BaseConstant.IOT_MERCHANDISE_INVENTORY_QUERY)
    public BaseAnswer<PageData<InventoryInfoDTO>> getNewInventory(@RequestParam(value = "spuOfferingName", required = false) String spuOfferingName,
                                                               @RequestParam(value = "skuOfferingName", required = false) String skuOfferingName,
                                                               @RequestParam(value = "spuOfferingCode", required = false) String spuOfferingCode,
                                                               @RequestParam(value = "skuOfferingCode", required = false) String skuOfferingCode,
                                                               @RequestParam(value = "atomOfferingName", required = false) String atomOfferingName,
                                                               @RequestParam(value = "spuOfferingClass", required = false) List<String> spuOfferingClass,
                                                               @RequestParam(value = "partnerName", required = false) String partnerName,
                                                               @RequestParam(value = "cooperatorName", required = false) String cooperatorName,
                                                               @RequestParam(value = "inventoryStatus", required = false) Integer inventoryStatus,
                                                               @RequestParam(value = "spuOfferingStatus", required = false) List<String> spuOfferingStatus,
                                                               @RequestParam(value = "skuOfferingStatus", required = false) List<String> skuOfferingStatus,
                                                               @RequestParam(value = "h5Key", required = false) String h5Key,
                                                               @RequestParam(value = "h5SpuOfferingClasses", required = false) List<String> h5SpuOfferingClasses,
                                                               @RequestParam(value = "inventoryType", required = false) String inventoryType,
                                                               @NotNull(message = "页数必填")@RequestParam("page") Integer page,
                                                               @RequestParam("num") Integer num,
                                                               @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return iInventoryService.getInventoryNewList(spuOfferingName,skuOfferingName,spuOfferingCode,skuOfferingCode,atomOfferingName,spuOfferingClass,partnerName,cooperatorName,
                inventoryStatus,spuOfferingStatus,skuOfferingStatus, h5Key,h5SpuOfferingClasses,inventoryType,loginIfo4Redis,page,num);
    }
    /**
     * 获取商品额度状态列表
     * @return
     */
    @GetMapping("/inventory/limit/status")
    public BaseAnswer<List<SimpleItemDTO>> getLimitStatus(){
        return iInventoryService.getLimitStatus();
    }
    /**
     * 查询商品额度

     * @param loginIfo4Redis redis缓存内容
     * @return
     */
    @GetMapping("/inventory/limit/list")
    public BaseAnswer<PageData<LimitInfoDTO>> getLimitList(@Valid LimitListRequest param,
                                                                     @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return iInventoryService.getLimitList(param,loginIfo4Redis);
    }
    /**
     * 导出商品额度

     * @param loginIfo4Redis redis缓存内容
     * @return
     */
    @GetMapping("/inventory/limit/export")
    public void getLimitExport(@Valid LimitListRequest param,
                                                                     @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) throws IOException {
        iInventoryService.getLimitExport(param,loginIfo4Redis);

    }
    /**
     * 查询商品库存
     * @param page 页
     * @param num 页数
     * @param loginIfo4Redis redis缓存内容
     * @return
     */
    @GetMapping("/inventory/type/list")
    @Auth(authCode = BaseConstant.IOT_MERCHANDISE_INVENTORY_QUERY)
    public BaseAnswer<PageData<InventoryInfoDTO>> getTypeInventory(@RequestParam(value = "spuOfferingName", required = false) String spuOfferingName,

                                                                  @RequestParam(value = "spuOfferingCode", required = false) String spuOfferingCode,

                                                                  @RequestParam(value = "spuOfferingClass", required = false) String spuOfferingClass,

                                                                  @RequestParam(value = "h5SpuOfferingClasses", required = false) List<String> h5SpuOfferingClasses,
                                                                   @RequestParam(value = "inventoryType", required = false) String inventoryType,
                                                                  @NotNull(message = "页数必填")@RequestParam("page") Integer page,
                                                                  @RequestParam("num") Integer num,
                                                                  @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return iInventoryService.getInventoryTypeList(spuOfferingName,spuOfferingCode,spuOfferingClass,h5SpuOfferingClasses,inventoryType,loginIfo4Redis,page,num);
    }

    /**
     * 获取kx设备型号列表
     * @param param
     * @return
     */
    @GetMapping("/inventory/device/list")
    public BaseAnswer<List<DkcardxInventoryMainInfo>> getInventoryKxDeviceList(@Valid ConfigCardXInventoryDeviceParam param){
        List<DkcardxInventoryMainInfo> inventoryKxDeviceList = iInventoryService.getInventoryKxDeviceList(param);
        return new BaseAnswer<List<DkcardxInventoryMainInfo>>().setData(inventoryKxDeviceList);
    }

    /**
     * 卡+X库存配置详情imei列表获取
     * @param kxInventoryDetailImeiParam
     * @return
     */
    @GetMapping("/inventory/atom/detail/listKXInventoryDetailImei")
    public BaseAnswer<PageData<KXInventoryDetailImeiDTO>> listKXInventoryDetailImei(@Valid KXInventoryDetailImeiParam kxInventoryDetailImeiParam){
        BaseAnswer<PageData<KXInventoryDetailImeiDTO>> baseAnswer = new BaseAnswer<>();

        PageData<KXInventoryDetailImeiDTO> pageData = iInventoryService.listKXInventoryDetailImei(kxInventoryDetailImeiParam);

        baseAnswer.setData(pageData);
        return baseAnswer;
    }

    /**
     * 卡+X库存详情地市列表
     * @param atomId
     * @return
     */
    @GetMapping("/inventory/atom/detail/listKXInventoryDetailLocation")
    public BaseAnswer<List<KXInventoryDetailLocationDTO>> listKXInventoryDetailLocation(@RequestParam("atomId")String atomId){
        List<KXInventoryDetailLocationDTO> detailLocationDTOList = iInventoryService.listKXInventoryDetailLocation(atomId);
        return new BaseAnswer<List<KXInventoryDetailLocationDTO>>().setData(detailLocationDTOList);
    }

    /**
     * 获取kx库存原子详情列表
     * @param atomId
     * @return
     */
    @GetMapping("/inventory/atom/detail/list")
    public BaseAnswer<List<DkCardxInventoryDetailInfoDTO>> getInventoryKxDetailList(@RequestParam("atomId")String atomId){
        List<DkCardxInventoryDetailInfoDTO> inventoryKxDeviceList = iInventoryService.getInventoryKxDetailList(atomId);
        return new BaseAnswer<List<DkCardxInventoryDetailInfoDTO>>().setData(inventoryKxDeviceList);
    }


    @PostMapping("/inventory/atom/magic/inventory")
    public BaseAnswer<Void> disposePassageKXOrderInventoryMessage(){
        iInventoryService.disposePassageKXOrderInventoryMessage();
        return new BaseAnswer<>();
    }

    /**
     * 修改本商品预占数
     * @param id
     * @param atomInventory
     * @return
     */
    @PostMapping("/inventory/atom/history/inventory")
    public BaseAnswer<Void> updateHistoryInventoryAtomInfoMessage(@RequestParam("id")String id,@RequestParam("atomInventory")Long atomInventory){
        iInventoryService.updateHistoryInventoryAtomInfo(id,atomInventory);
        return new BaseAnswer<>();
    }

    /**
     * 重建之前只有省级的原子库存信息 补全下面的省市信息
     * @return
     */
    @GetMapping("/inventory/atom/fix")
    public BaseAnswer<Void> updateInventoryAtomInfoFix(){
        iInventoryService.updateInventoryAtomInfoFix();
        return new BaseAnswer<>();
    }


    /**
     * 修改码号库存信息
     * @param id
     * @param currentInventory
     * @param totalInventory
     * @return
     */
    @PostMapping("/inventory/updateCardInventory")
    public BaseAnswer<Void> updateCardInventory(@RequestParam(value = "id") String id,@RequestParam(value = "currentInventory") Integer currentInventory
            ,@RequestParam(value = "totalInventory") Integer totalInventory){
        osMallSyncService.updateCardInventory(id,currentInventory,totalInventory);
        return new BaseAnswer<>();
    }
}
