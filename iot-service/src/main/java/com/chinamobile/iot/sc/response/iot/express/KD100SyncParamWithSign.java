package com.chinamobile.iot.sc.response.iot.express;

import lombok.Data;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2022/4/13 16:47
 * @description: 快递100返回
 **/
@Data
public class KD100SyncParamWithSign {


    /**
     * 订阅参数salt值不为null时，推送数据将包含该加密签名，加密方式：md5(param+salt)，注意加密后字符串一定要转32位大写。注意： salt值为空串时，推送的数据也会包含sign
     */
    private String sign;

    /**
     * 快递参数
     */
    private KD100SyncParam param;


}
