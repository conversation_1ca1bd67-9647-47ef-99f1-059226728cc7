package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2022/12/26 15:10
 * @description: 商城订单销售报表返回VO
 **/
@Data
public class OrderSalesReportVO {


    /**
     * 订单号
     */
    private String orderId;

    /**
     * 分销员电话
     */
    private String distributorPhone;

    /**
     * 业务编码
     */
    private String businessCode;

    /**
     * 客户省份
     */
    private String clientProvince;

    /**
     * 客户地市
     */
    private String clientCity;

    /**
     * 客户区县
     */
    private String clientDistricts;

    /**
     * 乡镇
     */
    private String clientVillagesTowns;


    /**|
     * 非结构地址
     */
    private String  usaddr;

    /**
     * 分销员对应客户经理省工号
     */
    private String employeeNum;

    /**
     * 客户经理名称
     */
    private String custMgName;

    /**
     * 客户经理电话
     */
    private String custMgPhone;

    /**
     * 订单总金额
     */
    private String totalPrice;

    /**
     * 订单创建时间
     */
    private String createTime;

    /**
     * 收货人姓名
     */
    private String contactPersonName;

    /**
     * 收货人电话
     */
    private String contactPhone;

    /**
     * spu名称
     */
    private String spuOfferingName;

    /**
     * sku名称
     */
    private String skuOfferingName;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 分销员级别
     */
    private String distributorLevel;
}
