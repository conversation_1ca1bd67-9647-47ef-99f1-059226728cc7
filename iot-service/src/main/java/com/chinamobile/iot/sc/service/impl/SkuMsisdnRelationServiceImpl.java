package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.SkuMsisdnRelationMapper;
import com.chinamobile.iot.sc.pojo.entity.CardMallSync;
import com.chinamobile.iot.sc.pojo.entity.CardMallSyncExample;
import com.chinamobile.iot.sc.pojo.entity.SkuMsisdnRelation;
import com.chinamobile.iot.sc.pojo.entity.SkuMsisdnRelationExample;
import com.chinamobile.iot.sc.pojo.param.GetOrderResultParam;
import com.chinamobile.iot.sc.pojo.vo.GetOrderResultVO;
import com.chinamobile.iot.sc.service.CardMallSyncService;
import com.chinamobile.iot.sc.service.SkuMsisdnRelationService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/2
 * @description 订单原子信息和卡号关系service实现类
 */
@Service
public class SkuMsisdnRelationServiceImpl implements SkuMsisdnRelationService {

    @Resource
    private SkuMsisdnRelationMapper skuMsisdnRelationMapper;

    @Resource
    private CardMallSyncService cardMallSyncService;

    @Override
    public void batchAddSkuMsisdnRelation(List<SkuMsisdnRelation> skuMsisdnRelationList) {
        skuMsisdnRelationMapper.batchInsert(skuMsisdnRelationList);
    }

    @Override
    public void updateSkuMsisdnRelationByNeed(SkuMsisdnRelation skuMsisdnRelation, SkuMsisdnRelationExample skuMsisdnRelationExample) {
        skuMsisdnRelationMapper.updateByExampleSelective(skuMsisdnRelation,skuMsisdnRelationExample);
    }

    @Override
    public List<SkuMsisdnRelation> listSkuMsisdnRelationByNeed(SkuMsisdnRelationExample skuMsisdnRelationExample) {
        return skuMsisdnRelationMapper.selectByExample(skuMsisdnRelationExample);
    }

    @Override
    public void updateSkuMsisdnRelationById(SkuMsisdnRelation skuMsisdnRelation) {
        skuMsisdnRelationMapper.updateByPrimaryKeySelective(skuMsisdnRelation);
    }

    @Override
    public void deleteSkuMsisdnRelationByNeed(SkuMsisdnRelationExample skuMsisdnRelationExample) {
        skuMsisdnRelationMapper.deleteByExample(skuMsisdnRelationExample);
    }

    @Override
    public List<GetOrderResultVO> listGetOrderResult(GetOrderResultParam getOrderResultParam) {
        String orderId = getOrderResultParam.getOrderId();
        String id = getOrderResultParam.getId();
        List<GetOrderResultVO> getOrderResultVOList = new ArrayList<>();
        SkuMsisdnRelationExample skuMsisdnRelationExample = new SkuMsisdnRelationExample();
        skuMsisdnRelationExample.createCriteria()
                .andOrderAtomInfoIdEqualTo(id)
                .andOrderIdEqualTo(orderId);
        List<SkuMsisdnRelation> skuMsisdnRelationList = listSkuMsisdnRelationByNeed(skuMsisdnRelationExample);
        skuMsisdnRelationList.stream().forEach(skuMsisdnRelation -> {
            GetOrderResultVO getOrderResultVO = new GetOrderResultVO();
            BeanUtils.copyProperties(skuMsisdnRelation,getOrderResultVO);
            Integer orderResult = skuMsisdnRelation.getOrderResult();
            if (orderResult != null){
                if (orderResult == 1){
                    getOrderResultVO.setOrderResultStr("成功");
                }else {
                    getOrderResultVO.setOrderResultStr("失败");
                }
            }else {
                getOrderResultVO.setOrderResultStr("暂无结果");
            }

            // 获取iccid
            CardMallSyncExample cardMallSyncExample = new CardMallSyncExample();
            cardMallSyncExample.createCriteria()
                            .andOrderIdEqualTo(orderId)
                            .andAtomOrderIdEqualTo(id)
                            .andMsisdnEqualTo(skuMsisdnRelation.getMsisdn());
            List<CardMallSync> cardMallSyncList = cardMallSyncService.listCardMallSyncByNeed(cardMallSyncExample);
            if (CollectionUtils.isNotEmpty(cardMallSyncList)){
                getOrderResultVO.setIccid(cardMallSyncList.get(0).getIccid());
            }

            getOrderResultVOList.add(getOrderResultVO);
        });
        return getOrderResultVOList;
    }
}
