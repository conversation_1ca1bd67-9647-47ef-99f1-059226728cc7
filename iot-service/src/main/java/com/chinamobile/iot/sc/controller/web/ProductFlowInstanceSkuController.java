package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceSku;
import com.chinamobile.iot.sc.pojo.dto.ProductFlowInstanceSkuDTO;
import com.chinamobile.iot.sc.pojo.param.ProductFlowSkuParam;
import com.chinamobile.iot.sc.service.ProductFlowInstanceSkuService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/5
 * @description 产品流程sku controller类
 */
@RequestMapping("/osweb/product/flow/sku")
@RestController
public class ProductFlowInstanceSkuController {

    @Resource
    private ProductFlowInstanceSkuService productFlowInstanceSkuService;

    /**
     * 获取已有sku名称和编码列表
     */
    @GetMapping(value = "/listProductFlowSku")
    public BaseAnswer<List<ProductFlowInstanceSkuDTO>> listProductFlowInstanceSku(@Valid ProductFlowSkuParam productFlowSkuParam){
        return productFlowInstanceSkuService.listProductFlowInstanceSku(productFlowSkuParam);
    }
}
