package com.chinamobile.iot.sc.response.web.invoice;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @package: com.chinamobile.iot.sc.response.web.invoice
 * @ClassName: Data4InvoiceRevRec
 * @description: 发票冲红申请记录结果
 * @author: zyj
 * @create: 2021/12/31 9:21
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
@Accessors(chain = true)
public class Data4InvoiceRevRec {

    private String id;

    private String atomOrderId;

    private String orderSeq;

    private String orderId;

    private String operType;

    private String customerType;

    private String customerNumber;

    private Long orderPrice;

    private Integer status;

    private String errorDesc;

    private String cooperatorId;

    private Date createTime;

    private Date updateTime;

    private String partnerName;

    private Integer reminderCount;
    /**
     * 合作伙伴联系人姓名
     */
    private String cooperatorName;

    private String spuOfferingClassName;

}
