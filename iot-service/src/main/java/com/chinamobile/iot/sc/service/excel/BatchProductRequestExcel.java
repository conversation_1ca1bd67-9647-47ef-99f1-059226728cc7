package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;



/**
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON>
 * @date : 2022/9/16 11:25
 * @description: 批量导入产品实体类
 **/
@Data
public class BatchProductRequestExcel {

    @ExcelProperty(value = "商品名称SPU",index = 0)
    private String spuOfferingName;

    /**
     * 商品名称SKU
     */
    @ExcelProperty(value = "商品规格名称SKU",index = 1)
    private String skuOfferingName;


    /**
     * 合作伙伴名称
     */
    @ExcelProperty(value = "合作伙伴名称",index = 2)
    private String cooperatorName;
    /**
     * '品牌、品牌方
     */
    @ExcelProperty(value = "品牌、品牌方",index = 3)
    private String brand;

    /**
     * 联网属性:无、2G、4G、蓝牙、WIFI、NB
     */
    @ExcelProperty(value = "联网属性",index = 4)
    private String networkProperty;

    /**
     * 型号
     */
    @ExcelProperty(value = "型号",index = 5)
    private String model;

    /**
     * 颜色
     */
    @ExcelProperty(value = "颜色",index = 6)
    private String color;

    /**
     * 物料编码
     */
    @ExcelProperty(value = "物料编码",index = 7)
    private String materialCode;



    /**
     * 典型应用领域
     */
    @ExcelProperty(value = "典型应用领域",index = 8)
    private String applicationDomain;

    /**
     * 商品简介
     */
    @ExcelProperty(value = "商品简介",index = 9)
    private String productIntroduction;

    /**
     * 商品规格套餐销售内容
     */
    @ExcelProperty(value = "商品规格套餐销售内容",index = 10)
    private String productSaleContent;


    /**
     * 产品销售区域
     */
    @ExcelProperty(value = "产品可销售区域",index = 11)
    private String productSaleArea;


    /**
     * 商品规格供货价（元）
     */
    @ExcelProperty(value = "商品规格供货价(元)",index = 12)
    private String supplyPriceStr;


    /**
     * 市场价格（元
     */
    @ExcelProperty(value = "市场价格(元)",index = 13)
    private String marketPriceStr;



    /**
     * 产品经理名字
     */
    @ExcelProperty(value = "产品经理姓名",index = 14)
    private String productManagerName;


    /**
     * 产品经理电话
     */
    @ExcelProperty(value = "产品经理手机号",index = 15)
    private String productManagerPhone;

    /**
     * 产品经理邮箱
     */
    @ExcelProperty(value = "产品经理邮箱",index = 16)
    private String productManagerEmail;

    /**
     * 产品经理邮箱
     */
    @ExcelProperty(value = "下一处理人",index = 17)
    private String operatorImportUser;
}
