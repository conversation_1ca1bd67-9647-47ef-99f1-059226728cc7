package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 产品流程实例表（开启一个流程，就新建一个实例）
 *
 * <AUTHOR>
public class ProductFlowInstance implements Serializable {
    /**
     *
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    private String id;

    /**
     * 流程id
     *
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    private String flowId;

    /**
     * 流程实例编号
     *
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    private String flowInstanceNumber;

    /**
     * 创建人id
     *
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    private String creatorId;

    /**
     * 创建人姓名
     *
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    private String creatorName;

    /**
     * 当前步骤id,-1表示结束
     *
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    private String currentStepId;

    /**
     * 流程状态 0-进行中 1-结束 2-废止
     *
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    private Integer status;

    /**
     * 下架理由
     *
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    private String offShelfReason;

    /**
     * 上架类型 1-SPU上架 2-SKU上架
     *
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    private Integer shelfType;

    /**
     * 改动的字段，逗号分隔
     *
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    private String changeList;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    private Date updateTime;

    /**
     * 可编辑(流程在第一个步骤时)
     *
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    private Boolean canEdit;

    /**
     * 可废止(流程在第一个步骤时)
     *
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    private Boolean canCancel;

    /**
     * 可审核(流程在第一个和最后一个步骤之间时)
     *
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    private Boolean canAudit;

    /**
     * 可配置(流程在最后一个步骤时)
     *
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    private Boolean canConfig;

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance.id
     *
     * @return the value of supply_chain..product_flow_instance.id
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public ProductFlowInstance withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance.id
     *
     * @param id the value for supply_chain..product_flow_instance.id
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance.flow_id
     *
     * @return the value of supply_chain..product_flow_instance.flow_id
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public String getFlowId() {
        return flowId;
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public ProductFlowInstance withFlowId(String flowId) {
        this.setFlowId(flowId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance.flow_id
     *
     * @param flowId the value for supply_chain..product_flow_instance.flow_id
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public void setFlowId(String flowId) {
        this.flowId = flowId;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance.flow_instance_number
     *
     * @return the value of supply_chain..product_flow_instance.flow_instance_number
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public String getFlowInstanceNumber() {
        return flowInstanceNumber;
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public ProductFlowInstance withFlowInstanceNumber(String flowInstanceNumber) {
        this.setFlowInstanceNumber(flowInstanceNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance.flow_instance_number
     *
     * @param flowInstanceNumber the value for supply_chain..product_flow_instance.flow_instance_number
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public void setFlowInstanceNumber(String flowInstanceNumber) {
        this.flowInstanceNumber = flowInstanceNumber;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance.creator_id
     *
     * @return the value of supply_chain..product_flow_instance.creator_id
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public String getCreatorId() {
        return creatorId;
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public ProductFlowInstance withCreatorId(String creatorId) {
        this.setCreatorId(creatorId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance.creator_id
     *
     * @param creatorId the value for supply_chain..product_flow_instance.creator_id
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance.creator_name
     *
     * @return the value of supply_chain..product_flow_instance.creator_name
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public String getCreatorName() {
        return creatorName;
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public ProductFlowInstance withCreatorName(String creatorName) {
        this.setCreatorName(creatorName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance.creator_name
     *
     * @param creatorName the value for supply_chain..product_flow_instance.creator_name
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance.current_step_id
     *
     * @return the value of supply_chain..product_flow_instance.current_step_id
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public String getCurrentStepId() {
        return currentStepId;
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public ProductFlowInstance withCurrentStepId(String currentStepId) {
        this.setCurrentStepId(currentStepId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance.current_step_id
     *
     * @param currentStepId the value for supply_chain..product_flow_instance.current_step_id
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public void setCurrentStepId(String currentStepId) {
        this.currentStepId = currentStepId;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance.status
     *
     * @return the value of supply_chain..product_flow_instance.status
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public Integer getStatus() {
        return status;
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public ProductFlowInstance withStatus(Integer status) {
        this.setStatus(status);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance.status
     *
     * @param status the value for supply_chain..product_flow_instance.status
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance.off_shelf_reason
     *
     * @return the value of supply_chain..product_flow_instance.off_shelf_reason
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public String getOffShelfReason() {
        return offShelfReason;
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public ProductFlowInstance withOffShelfReason(String offShelfReason) {
        this.setOffShelfReason(offShelfReason);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance.off_shelf_reason
     *
     * @param offShelfReason the value for supply_chain..product_flow_instance.off_shelf_reason
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public void setOffShelfReason(String offShelfReason) {
        this.offShelfReason = offShelfReason;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance.shelf_type
     *
     * @return the value of supply_chain..product_flow_instance.shelf_type
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public Integer getShelfType() {
        return shelfType;
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public ProductFlowInstance withShelfType(Integer shelfType) {
        this.setShelfType(shelfType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance.shelf_type
     *
     * @param shelfType the value for supply_chain..product_flow_instance.shelf_type
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public void setShelfType(Integer shelfType) {
        this.shelfType = shelfType;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance.change_list
     *
     * @return the value of supply_chain..product_flow_instance.change_list
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public String getChangeList() {
        return changeList;
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public ProductFlowInstance withChangeList(String changeList) {
        this.setChangeList(changeList);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance.change_list
     *
     * @param changeList the value for supply_chain..product_flow_instance.change_list
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public void setChangeList(String changeList) {
        this.changeList = changeList;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance.create_time
     *
     * @return the value of supply_chain..product_flow_instance.create_time
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public ProductFlowInstance withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance.create_time
     *
     * @param createTime the value for supply_chain..product_flow_instance.create_time
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance.update_time
     *
     * @return the value of supply_chain..product_flow_instance.update_time
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public ProductFlowInstance withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance.update_time
     *
     * @param updateTime the value for supply_chain..product_flow_instance.update_time
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance.can_edit
     *
     * @return the value of supply_chain..product_flow_instance.can_edit
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public Boolean getCanEdit() {
        return canEdit;
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public ProductFlowInstance withCanEdit(Boolean canEdit) {
        this.setCanEdit(canEdit);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance.can_edit
     *
     * @param canEdit the value for supply_chain..product_flow_instance.can_edit
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public void setCanEdit(Boolean canEdit) {
        this.canEdit = canEdit;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance.can_cancel
     *
     * @return the value of supply_chain..product_flow_instance.can_cancel
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public Boolean getCanCancel() {
        return canCancel;
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public ProductFlowInstance withCanCancel(Boolean canCancel) {
        this.setCanCancel(canCancel);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance.can_cancel
     *
     * @param canCancel the value for supply_chain..product_flow_instance.can_cancel
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public void setCanCancel(Boolean canCancel) {
        this.canCancel = canCancel;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance.can_audit
     *
     * @return the value of supply_chain..product_flow_instance.can_audit
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public Boolean getCanAudit() {
        return canAudit;
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public ProductFlowInstance withCanAudit(Boolean canAudit) {
        this.setCanAudit(canAudit);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance.can_audit
     *
     * @param canAudit the value for supply_chain..product_flow_instance.can_audit
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public void setCanAudit(Boolean canAudit) {
        this.canAudit = canAudit;
    }

    /**
     * This method returns the value of the database column supply_chain..product_flow_instance.can_config
     *
     * @return the value of supply_chain..product_flow_instance.can_config
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public Boolean getCanConfig() {
        return canConfig;
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public ProductFlowInstance withCanConfig(Boolean canConfig) {
        this.setCanConfig(canConfig);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..product_flow_instance.can_config
     *
     * @param canConfig the value for supply_chain..product_flow_instance.can_config
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public void setCanConfig(Boolean canConfig) {
        this.canConfig = canConfig;
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", flowId=").append(flowId);
        sb.append(", flowInstanceNumber=").append(flowInstanceNumber);
        sb.append(", creatorId=").append(creatorId);
        sb.append(", creatorName=").append(creatorName);
        sb.append(", currentStepId=").append(currentStepId);
        sb.append(", status=").append(status);
        sb.append(", offShelfReason=").append(offShelfReason);
        sb.append(", shelfType=").append(shelfType);
        sb.append(", changeList=").append(changeList);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", canEdit=").append(canEdit);
        sb.append(", canCancel=").append(canCancel);
        sb.append(", canAudit=").append(canAudit);
        sb.append(", canConfig=").append(canConfig);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ProductFlowInstance other = (ProductFlowInstance) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getFlowId() == null ? other.getFlowId() == null : this.getFlowId().equals(other.getFlowId()))
            && (this.getFlowInstanceNumber() == null ? other.getFlowInstanceNumber() == null : this.getFlowInstanceNumber().equals(other.getFlowInstanceNumber()))
            && (this.getCreatorId() == null ? other.getCreatorId() == null : this.getCreatorId().equals(other.getCreatorId()))
            && (this.getCreatorName() == null ? other.getCreatorName() == null : this.getCreatorName().equals(other.getCreatorName()))
            && (this.getCurrentStepId() == null ? other.getCurrentStepId() == null : this.getCurrentStepId().equals(other.getCurrentStepId()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getOffShelfReason() == null ? other.getOffShelfReason() == null : this.getOffShelfReason().equals(other.getOffShelfReason()))
            && (this.getShelfType() == null ? other.getShelfType() == null : this.getShelfType().equals(other.getShelfType()))
            && (this.getChangeList() == null ? other.getChangeList() == null : this.getChangeList().equals(other.getChangeList()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getCanEdit() == null ? other.getCanEdit() == null : this.getCanEdit().equals(other.getCanEdit()))
            && (this.getCanCancel() == null ? other.getCanCancel() == null : this.getCanCancel().equals(other.getCanCancel()))
            && (this.getCanAudit() == null ? other.getCanAudit() == null : this.getCanAudit().equals(other.getCanAudit()))
            && (this.getCanConfig() == null ? other.getCanConfig() == null : this.getCanConfig().equals(other.getCanConfig()));
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getFlowId() == null) ? 0 : getFlowId().hashCode());
        result = prime * result + ((getFlowInstanceNumber() == null) ? 0 : getFlowInstanceNumber().hashCode());
        result = prime * result + ((getCreatorId() == null) ? 0 : getCreatorId().hashCode());
        result = prime * result + ((getCreatorName() == null) ? 0 : getCreatorName().hashCode());
        result = prime * result + ((getCurrentStepId() == null) ? 0 : getCurrentStepId().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getOffShelfReason() == null) ? 0 : getOffShelfReason().hashCode());
        result = prime * result + ((getShelfType() == null) ? 0 : getShelfType().hashCode());
        result = prime * result + ((getChangeList() == null) ? 0 : getChangeList().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getCanEdit() == null) ? 0 : getCanEdit().hashCode());
        result = prime * result + ((getCanCancel() == null) ? 0 : getCanCancel().hashCode());
        result = prime * result + ((getCanAudit() == null) ? 0 : getCanAudit().hashCode());
        result = prime * result + ((getCanConfig() == null) ? 0 : getCanConfig().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Thu Apr 25 17:15:18 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        flowId("flow_id", "flowId", "VARCHAR", false),
        flowInstanceNumber("flow_instance_number", "flowInstanceNumber", "VARCHAR", false),
        creatorId("creator_id", "creatorId", "VARCHAR", false),
        creatorName("creator_name", "creatorName", "VARCHAR", false),
        currentStepId("current_step_id", "currentStepId", "VARCHAR", false),
        status("status", "status", "INTEGER", false),
        offShelfReason("off_shelf_reason", "offShelfReason", "VARCHAR", false),
        shelfType("shelf_type", "shelfType", "INTEGER", false),
        changeList("change_list", "changeList", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        canEdit("can_edit", "canEdit", "BIT", false),
        canCancel("can_cancel", "canCancel", "BIT", false),
        canAudit("can_audit", "canAudit", "BIT", false),
        canConfig("can_config", "canConfig", "BIT", false);

        /**
         *
         * @mbg.generated Thu Apr 25 17:15:18 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Apr 25 17:15:18 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Apr 25 17:15:18 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Thu Apr 25 17:15:18 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Thu Apr 25 17:15:18 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Thu Apr 25 17:15:18 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Thu Apr 25 17:15:18 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Apr 25 17:15:18 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Apr 25 17:15:18 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Thu Apr 25 17:15:18 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Thu Apr 25 17:15:18 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Thu Apr 25 17:15:18 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Thu Apr 25 17:15:18 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Thu Apr 25 17:15:18 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Thu Apr 25 17:15:18 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Thu Apr 25 17:15:18 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Thu Apr 25 17:15:18 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}