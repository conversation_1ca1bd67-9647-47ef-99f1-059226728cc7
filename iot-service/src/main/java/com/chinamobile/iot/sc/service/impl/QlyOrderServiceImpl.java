package com.chinamobile.iot.sc.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.constant.QlyStatusConstant;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.excel.QlyOrderImportExcel;
import com.chinamobile.iot.sc.excel.QlyOrderImportListener;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.QlyOpenFeignClient;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.pojo.dto.QlyOrderFailDTO;
import com.chinamobile.iot.sc.pojo.dto.QlyOrderSucceedDTO;
import com.chinamobile.iot.sc.pojo.entity.ChargeItemConfig;
import com.chinamobile.iot.sc.pojo.entity.ChargeItemConfigExample;
import com.chinamobile.iot.sc.pojo.param.QlyCheckSnParam;
import com.chinamobile.iot.sc.pojo.param.QlyOperationParam;
import com.chinamobile.iot.sc.pojo.vo.QlyTurnOnFailedVO;
import com.chinamobile.iot.sc.response.QlyBaseResponse;
import com.chinamobile.iot.sc.response.QlyCheckResponse;
import com.chinamobile.iot.sc.service.QlyOrderService;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.ExcelUtils;
import com.chinamobile.iot.sc.util.IOTEncodeUtils;
import com.chinamobile.iot.sc.util.excel.EasyExcelUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/26 15:25
 * @description 千里眼服务实现类
 */
@Slf4j
@Service
public class QlyOrderServiceImpl implements QlyOrderService {
    @Resource
    private QlyOpenFeignClient qlyOpenFeignClient;

    @Resource
    private QlyOrderSnMapper qlyOrderSnMapper;

    @Resource
    private Order2cInfoMapper order2cInfoMapper;

    @Resource
    private Order2cAtomInfoMapper order2cAtomInfoMapper;
    @Resource
    private LogisticsInfoMapper logisticsInfoMapper;

    @Resource
    private AtomOfferingInfoMapper atomOfferingInfoMapper;

    @Value("${iot.encodeKey}")
    private String encodeKey;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;

    @Value("#{'${qly.chargeCodes}'.replaceAll(' ','').split(',')}")
    private List<String> qlyChargeCodes;


    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer<List<String>> checkSns(String orderId, List<String> deviceSns) {
        deviceSns = deviceSns.stream().distinct().collect(Collectors.toList());
        List<String> failedList = new ArrayList<>();
        List<List<String>> snLists = new ArrayList<>();
        if (deviceSns.size() > 100) {
            snLists = Lists.partition(deviceSns, 100);
        } else {
            snLists.add(deviceSns);
        }
        snLists.forEach(list -> {
            QlyCheckSnParam param = new QlyCheckSnParam();
            param.setTransIDO(String.valueOf(System.currentTimeMillis()));
            param.setDevIds(list);
            log.info("千里眼设备校验，param:{}", JSON.toJSONString(param));
            String result = qlyOpenFeignClient.checkDevicesSn(param);
            log.info("千里眼设备校验，result:{}", result);
            QlyCheckResponse response = JSON.parseObject(result, QlyCheckResponse.class);
            //注意，千里眼接口校验失败resultCode也是0,所以为0的时候也需要判断data是否为空
            if (response == null) {
                failedList.addAll(list);
            } else if (!response.isSuccess()) {
                failedList.addAll(response.getData());
            }
        });
        BaseAnswer<List<String>> answer = new BaseAnswer<>();
        if (CollectionUtils.isEmpty(failedList)) {
            //全部校验通过，将sn存入qly_order_sn中
            List<QlyOrderSn> qlyOrderSnList = deviceSns.stream().map(sn -> {
                QlyOrderSn qlyOrderSn = new QlyOrderSn();
                qlyOrderSn.setOrderId(orderId);
                qlyOrderSn.setSn(sn);
                qlyOrderSn.setQlyStatus(QlyStatusConstant.QLY_CHECK_SUCCESS);
                return qlyOrderSn;
            }).collect(Collectors.toList());
            qlyOrderSnMapper.batchInsert(qlyOrderSnList);
            Order2cInfo order2cInfo = new Order2cInfo();
            order2cInfo.setOrderId(orderId);
            order2cInfo.setQlyStatus(QlyStatusConstant.QLY_CHECK_SUCCESS);
            order2cInfoMapper.updateByPrimaryKeySelective(order2cInfo);

            answer = BaseAnswer.success(null);
        } else {
            //存在校验失败
            answer.setStatus(BaseErrorConstant.QLY_CHECK_FAILED);
            answer.setData(failedList);
        }
        return answer;
    }

    /**
     * 开通千里眼设备，内部调用，不校验是否为千里眼订单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseAnswer<List<String>> turnOnDevices(String orderId, String phone, List<String> chargeCodeList
            , List<String> deviceSns, String areaCode, String cityCode, String countyCode) {
        if (CollectionUtils.isEmpty(deviceSns)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "设备sn不能为空");
        }
        deviceSns = deviceSns.stream().distinct().collect(Collectors.toList());
        List<QlyTurnOnFailedVO> failedList = new ArrayList<>();
        List<String> successList = new ArrayList<>();
        List<List<String>> snLists = new ArrayList<>();
        if (deviceSns.size() > 100) {
            snLists = Lists.partition(deviceSns, 100);
        } else {
            snLists.add(deviceSns);
        }

        for (List<String> list : snLists) {
            QlyOperationParam param = new QlyOperationParam();
            param.setTransIDO(String.valueOf(System.currentTimeMillis()));
            param.setDevIds(list);
            param.setOrderNumber(orderId);
            param.setIdValue(phone);
            //服务开通
            param.setOprCode("01");
            param.setProductIdList(chargeCodeList);
            param.setAreaCode(areaCode);
            param.setCityCode(cityCode);
            param.setCountyCode(countyCode);
            Date now = new Date();
            param.setEffectTime(DateUtils.dateToStr(now, DateUtils.DATE_FORMAT_NO_SYMBOL));
            param.setFailureTime(DateUtils.dateToStr(DateUtils.addMonth(now, 24), DateUtils.DATE_FORMAT_NO_SYMBOL));
            log.info("千里眼设备开通，param:{}", JSON.toJSONString(param));
            String result = null;
            QlyBaseResponse response = null;
            try {
                result = qlyOpenFeignClient.operateDevices(param);
                log.info("千里眼设备开通，result:{}", result);
                response = JSON.parseObject(result, QlyBaseResponse.class);
            } catch (Exception e) {
                log.error("千里眼设备开通发生异常", e);
            }
            if (response == null) {
                failedList.addAll(list.stream().map(sn -> {
                    QlyTurnOnFailedVO vo = new QlyTurnOnFailedVO();
                    vo.setSn(sn);
                    vo.setTurnOnCode("9999");
                    return vo;
                }).collect(Collectors.toList()));
            } else if (!response.isSuccess()) {
                QlyBaseResponse finalResponse = response;
                failedList.addAll(list.stream().map(sn -> {
                    QlyTurnOnFailedVO vo = new QlyTurnOnFailedVO();
                    vo.setSn(sn);
                    vo.setTurnOnCode(finalResponse.getResultCode());
                    return vo;
                }).collect(Collectors.toList()));
            } else {
                successList.addAll(list);
            }
        }

        Integer qlyOrderStatus;
        BaseAnswer<List<String>> answer = new BaseAnswer<>();
        if (CollectionUtils.isNotEmpty(failedList)) {
            qlyOrderStatus = QlyStatusConstant.QLY_TURN_ON_FAILED;
            answer.setStatus(BaseErrorConstant.QLY_TURN_ON_FAILED);
            answer.setData(failedList.stream().map(QlyTurnOnFailedVO::getSn).collect(Collectors.toList()));
        } else {
            //全部开通成功
            qlyOrderStatus = QlyStatusConstant.QLY_TURN_ON_SUCCESS;
            answer.setStatus(BaseErrorConstant.SUCCESS);
            answer.setMessage("千里眼开通成功");
        }

        if (CollectionUtils.isNotEmpty(successList)) {
            QlyOrderSn orderSn = new QlyOrderSn();
            orderSn.setQlyStatus(QlyStatusConstant.QLY_TURN_ON_SUCCESS);
            orderSn.setTurnOnCode("0000");
            QlyOrderSnExample example = new QlyOrderSnExample().createCriteria().andSnIn(successList)
                    .andOrderIdEqualTo(orderId).example();
            qlyOrderSnMapper.updateByExampleSelective(orderSn, example);
        }

        if (CollectionUtils.isNotEmpty(failedList)) {
            failedList.forEach(failedVO -> {
                QlyOrderSn orderSn = new QlyOrderSn();
                orderSn.setQlyStatus(QlyStatusConstant.QLY_TURN_ON_FAILED);
                orderSn.setTurnOnCode(failedVO.getTurnOnCode());
                QlyOrderSnExample example = new QlyOrderSnExample().createCriteria().andSnEqualTo(failedVO.getSn())
                        .andOrderIdEqualTo(orderId).example();
                qlyOrderSnMapper.updateByExampleSelective(orderSn, example);
            });

        }

        Order2cInfo order2cInfo = new Order2cInfo();
        order2cInfo.setQlyStatus(qlyOrderStatus);
        order2cInfo.setOrderId(orderId);
        order2cInfoMapper.updateByPrimaryKeySelective(order2cInfo);

        return answer;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseAnswer<Void> cancelDevices(String orderId, String phone, List<String> chargeCodeList,String areaCode, String cityCode, String countyCode) {
        QlyOperationParam param = new QlyOperationParam();
        param.setTransIDO(String.valueOf(System.currentTimeMillis()));
        param.setOrderNumber(orderId);
        param.setIdValue(phone);
        //服务退订
        param.setOprCode("03");
        param.setAreaCode(areaCode);
        param.setCityCode(cityCode);
        param.setCountyCode(countyCode);
        param.setProductIdList(chargeCodeList);
        Date now = new Date();
        param.setEffectTime(DateUtils.dateToStr(now, DateUtils.DATE_FORMAT_NO_SYMBOL));
        param.setFailureTime(DateUtils.dateToStr(DateUtils.addMonth(now, 24), DateUtils.DATE_FORMAT_NO_SYMBOL));
        log.info("千里眼设备退订，param:{}", JSON.toJSONString(param));
        String result = null;
        QlyBaseResponse response = null;
        try {
            result = qlyOpenFeignClient.operateDevices(param);
            log.info("千里眼设备退订，result:{}", result);
            response = JSON.parseObject(result, QlyBaseResponse.class);
        } catch (Exception e) {
            log.error("千里眼设备退订发生异常", e);
        }
        Integer qlyStatus;
        BaseAnswer<Void> answer = new BaseAnswer<>();
        if (response != null && response.isSuccess()) {
            //退订成功
            qlyStatus = QlyStatusConstant.QLY_CANCEL_SUCCESS;
            answer.setStatus(BaseErrorConstant.SUCCESS);
            answer.setMessage("千里眼退订成功");
        } else {
            //退订失败
            qlyStatus = QlyStatusConstant.QLY_CANCEL_FAILED;
            answer.setStatus(BaseErrorConstant.QLY_CANCEL_FAILED);
        }

        QlyOrderSn orderSn = new QlyOrderSn();
        orderSn.setQlyStatus(qlyStatus);
        QlyOrderSnExample example = new QlyOrderSnExample().createCriteria().andOrderIdEqualTo(orderId).example();
        qlyOrderSnMapper.updateByExampleSelective(orderSn, example);

        Order2cInfo order2cInfo = new Order2cInfo();
        order2cInfo.setQlyStatus(qlyStatus);
        order2cInfo.setOrderId(orderId);
        order2cInfoMapper.updateByPrimaryKeySelective(order2cInfo);

        return answer;
    }

    /**
     * 开通千里眼设备，Web调用，需要校验是否为千里眼订单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseAnswer<List<String>> turnOnDevices(String orderId, List<String> devicesSns) {
        //软件平台编码和手机号
        Map<String, Object> map = checkQlyOrderAndGetSoftCode(orderId);
        Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(orderId);
        if (order2cInfo.getQlyStatus() == null || order2cInfo.getQlyStatus().equals(QlyStatusConstant.QLY_NOT_CHECK)) {
            //千里眼设备还没校验过
            if (CollectionUtils.isEmpty(devicesSns)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "设备IMEI数组不能为空");
            }
            devicesSns = devicesSns.stream().distinct().collect(Collectors.toList());
            int snCount = Integer.parseInt((String) map.get("snCount"));
            if (snCount != devicesSns.size()) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "设备IMEI数量与订单中的原子数量不一致");
            }

            //检测IMEI是不是已经被开通
            List<QlyOrderSn> existSn = qlyOrderSnMapper.selectByExample(new QlyOrderSnExample().createCriteria()
                    .andSnIn(devicesSns).andQlyStatusEqualTo(QlyStatusConstant.QLY_TURN_ON_SUCCESS).example());
            if (CollectionUtils.isNotEmpty(existSn)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, String.format("设备%s已开开通千里眼服务，不能重复开通",
                        JSON.toJSONString(existSn.stream().map(QlyOrderSn::getSn).collect(Collectors.toList()))));
            }

            BaseAnswer<List<String>> checkAnswer = checkSns(orderId, devicesSns);
            if (!StringUtils.equals(checkAnswer.getStateCode(), BaseErrorConstant.SUCCESS.getStateCode())) {
                return checkAnswer;
            }
        }
        List<String> deviceSns = qlyOrderSnMapper.selectByExample(new QlyOrderSnExample().createCriteria()
                        .andOrderIdEqualTo(orderId).andQlyStatusIn(Arrays.asList(QlyStatusConstant.QLY_CHECK_SUCCESS,
                                QlyStatusConstant.QLY_TURN_ON_FAILED)).example())
                .stream().map(QlyOrderSn::getSn).collect(Collectors.toList());
        log.info("手动开通千里眼订单,订单号:{}", orderId);
        return turnOnDevices(orderId, (String) map.get("phone"), (List<String>) map.get("softCode"), deviceSns, (String) map.get("areaCode"),
                (String) map.get("cityCode"),(String) map.get("countyCode"));
    }

    @Override
    public BaseAnswer<Void> cancelDevices(String orderId) {
        //软件平台编码和手机号
        Map<String, Object> map = checkQlyOrderAndGetSoftCode(orderId);

        return cancelDevices(orderId, (String) map.get("phone"), (List<String>) map.get("softCode"), (String) map.get("areaCode"),
                (String) map.get("cityCode"),(String) map.get("countyCode"));
    }

    @Override
    public void qlyOrderImport(LoginIfo4Redis loginIfo4Redis, MultipartFile file) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        if (file.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.FILE_NOT_EXIST);
        }
        String oldName = file.getOriginalFilename();
        boolean fileTypeBool = oldName == null || (!oldName.endsWith(".xlsx") && !oldName.endsWith(".xls"));
        if (fileTypeBool) {
            throw new BusinessException(BaseErrorConstant.FILE_TYPE_ERROR);
        }

        QlyOrderImportListener excelListener = new QlyOrderImportListener(logisticsInfoMapper, qlyOrderSnMapper);

        try {
            List<Object> list = EasyExcel.read(file.getInputStream(), QlyOrderImportExcel.class, excelListener)
                    .sheet(0).headRowNumber(1).doReadSync();
            //成功数
            List<QlyOrderSucceedDTO> qlyOrderSucceedList = excelListener.getQlyOrderSucceedList();
            //失败数量
            List<QlyOrderFailDTO> qlyOrderFailList = excelListener.getQlyOrderFailList();
            if (CollectionUtils.isNotEmpty(qlyOrderFailList)) {
                String excelName = "批量导入合同履约千里眼订单失败";
//                excelName = URLEncoder.encode(excelName, "UTF-8");
                ClassPathResource classPathResource = new ClassPathResource("template/qlyorder_error.xlsx");
                InputStream templateFileName = classPathResource.getInputStream();
                String stateCode = BaseErrorConstant.QLY_IMPORT_FAILD.getStateCode();
                String message = BaseErrorConstant.QLY_IMPORT_FAILD.getMessage();
                //构建填充excel参数
                Map<String, Object> map = new HashMap<String, Object>();
                EasyExcelUtils.exportExcel(response, "list", qlyOrderFailList, map, excelName, templateFileName,
                        0, "失败描述", stateCode, message);
            } else {
                //更新千里眼开通状态
                if (CollectionUtils.isNotEmpty(qlyOrderSucceedList)) {
                    for (QlyOrderSucceedDTO succeedVO : qlyOrderSucceedList) {
                        if (StringUtils.isNotEmpty(succeedVO.getSnSucessList())) {
                            List<QlyOrderSn> qlyOrderSnAll = qlyOrderSnMapper.selectByExample(new QlyOrderSnExample().createCriteria().andOrderIdEqualTo(succeedVO.getOrderId()).example());
                            //部分开通
                            List<String> snSucessList = Arrays.asList(succeedVO.getSnSucessList().split(","));
                            for (String sn : snSucessList) {
                                QlyOrderSnExample example = new QlyOrderSnExample();
                                example.createCriteria().andOrderIdEqualTo(succeedVO.getOrderId()).andSnEqualTo(sn);
                                List<QlyOrderSn> qlyOrderSns = qlyOrderSnMapper.selectByExample(example);
                                QlyOrderSn orderSn = qlyOrderSns.get(0);
                                orderSn.setQlyStatus(QlyStatusConstant.QLY_TURN_ON_SUCCESS);
                                orderSn.setTurnOnCode("0000");
                                qlyOrderSnMapper.updateByExampleSelective(orderSn, example);
                            }
                            //判断是否全部开通成功
                            if(qlyOrderSnAll.size()==snSucessList.size()){
                                Order2cInfo order2cInfo = new Order2cInfo();
                                order2cInfo.setQlyStatus(QlyStatusConstant.QLY_TURN_ON_SUCCESS);
                                order2cInfo.setOrderId(succeedVO.getOrderId());
                                order2cInfoMapper.updateByPrimaryKeySelective(order2cInfo);
                            }
                        } else {
                            //全部开通
                            QlyOrderSn orderSn = new QlyOrderSn();
                            orderSn.setTurnOnCode("0000");
                            orderSn.setQlyStatus(QlyStatusConstant.QLY_TURN_ON_SUCCESS);
                            QlyOrderSnExample example = new QlyOrderSnExample().createCriteria().andOrderIdEqualTo(succeedVO.getOrderId()).example();
                            qlyOrderSnMapper.updateByExampleSelective(orderSn, example);
                            Order2cInfo order2cInfo = new Order2cInfo();
                            order2cInfo.setQlyStatus(QlyStatusConstant.QLY_TURN_ON_SUCCESS);
                            order2cInfo.setOrderId(succeedVO.getOrderId());
                            order2cInfoMapper.updateByPrimaryKeySelective(order2cInfo);
                        }
                    }

                }
                log.info("批量导入千里眼订单成功");
            }
        } catch (IOException e) {
            log.error("读取文件异常，文件名: {},异常描述：{}", oldName, e);
            throw new BusinessException(BaseErrorConstant.FILE_PARSE_ERROR);
        } catch (ExcelAnalysisException e) {
            e.printStackTrace();
            throw new BusinessException(BaseErrorConstant.RULE_NOT_ALLOW, e.getCause().getMessage());
        } catch (BusinessException e) {
            e.printStackTrace();
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(BaseErrorConstant.FILE_PARSE_ERROR);
        }
    }

    /**
     * 校验订单是否为千里眼订单，如果是则返回soft_code和phone
     */
    private Map<String, Object> checkQlyOrderAndGetSoftCode(String orderId) {
        Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(orderId);
        if (order2cInfo == null) {
            throw new BusinessException(BaseErrorConstant.QLY_ORDER_ID_ERROR);
        }

        //判断订单是否是千里眼订单
        List<Order2cAtomInfo> order2cAtomInfos = order2cAtomInfoMapper.selectByExample(new Order2cAtomInfoExample()
                .createCriteria().andOrderIdEqualTo(orderId).example());
        boolean isQlyOrder = false;
        //软件平台编码
        List<String> softCodeList = new ArrayList<>();
        int snCount = 0;
        for (Order2cAtomInfo order2cAtomInfo : order2cAtomInfos) {
            List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper.selectByExample(new AtomOfferingInfoExample()
                    .createCriteria().andSpuCodeEqualTo(order2cAtomInfo.getSpuOfferingCode())
                    .andSkuCodeEqualTo(order2cAtomInfo.getSkuOfferingCode())
                    .andOfferingCodeEqualTo(order2cAtomInfo.getAtomOfferingCode()).example());
            if (atomOfferingInfos.size() > 0) {
                //根据chargeCode是否是千里眼订单
                AtomOfferingInfo atomOfferingInfo = atomOfferingInfos.get(0);
                if (StringUtils.isNotBlank(atomOfferingInfo.getChargeCode())) {
                    String chargeCode = atomOfferingInfo.getChargeCode().substring(0, atomOfferingInfo.getChargeCode().lastIndexOf("_"));
                    isQlyOrder = qlyChargeCodes.contains(chargeCode) && StringUtils.isNotBlank(atomOfferingInfo.getExtSoftOfferingCode());
                }

                if ("S".equals(atomOfferingInfo.getOfferingClass()) && StringUtils.isNotBlank(atomOfferingInfo.getExtSoftOfferingCode())) {
                    if (!softCodeList.contains(atomOfferingInfo.getExtSoftOfferingCode())) {
                        softCodeList.add(atomOfferingInfo.getExtSoftOfferingCode());
                    }

                }

                if ("H".equals(atomOfferingInfo.getOfferingClass())) {
                    snCount += order2cAtomInfo.getSkuQuantity();
                }
            }
        }

        if (!isQlyOrder) {
            throw new BusinessException(BaseErrorConstant.QLY_ORDER_ID_ERROR, "订单不是千里眼订单");
        }
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("softCode", softCodeList);
        map.put("phone", IOTEncodeUtils.decryptSM4(order2cInfo.getContactPhone(), iotSm4Key, iotSm4Iv));

        if (StringUtils.isNotEmpty(order2cInfo.getBeId())){
            map.put("areaCode", order2cInfo.getBeId());
        }else {
            map.put("areaCode", StringUtils.EMPTY);
        }
        if (StringUtils.isNotEmpty(order2cInfo.getLocation())){
            map.put("cityCode", order2cInfo.getLocation());
        }else {
            map.put("cityCode", StringUtils.EMPTY);
        }
        if (StringUtils.isNotEmpty(order2cInfo.getRegionId())){
            map.put("countyCode", order2cInfo.getRegionId());
        }else {
            map.put("countyCode", StringUtils.EMPTY);
        }
        map.put("snCount", String.valueOf(snCount));
        return map;
    }

    @Override
    public void exportFailedSn(String orderId, HttpServletResponse response) throws IOException {

        List<QlyTurnOnFailedVO> failedVOS = qlyOrderSnMapper.selectByExample(new QlyOrderSnExample().createCriteria()
                        .andOrderIdEqualTo(orderId).andQlyStatusNotEqualTo(QlyStatusConstant.QLY_TURN_ON_SUCCESS).example())
                .stream().map(qlyOrderSn -> {
                    QlyTurnOnFailedVO vo = new QlyTurnOnFailedVO();
                    vo.setSn(qlyOrderSn.getSn());
                    vo.setTurnOnCode(qlyOrderSn.getTurnOnCode());
                    return vo;
                }).collect(Collectors.toList());

        String fileName = "导出千里眼开通失败的设备数据";
        ExportParams exportParams = new ExportParams(fileName,
                fileName, ExcelType.XSSF);
        ExcelUtils.exportExcel(failedVOS, QlyTurnOnFailedVO.class,
                fileName, exportParams, response);
    }

    @Override
    public BaseAnswer<List<QlyOrderSn>> getImeiList(String orderId) {
        BaseAnswer<List<QlyOrderSn>> answer = new BaseAnswer<>();
        answer.setData(qlyOrderSnMapper.selectByExample(new QlyOrderSnExample().createCriteria().andOrderIdEqualTo(orderId).example()));
        return answer;
    }
}
