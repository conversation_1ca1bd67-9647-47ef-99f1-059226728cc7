package com.chinamobile.iot.sc.pojo.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/21
 * @description 云南彩云智企商客产品订单数据实体类
 */
@Data
public class CyzqListDTO {

    /**
     * 订单编号（订单ID）
     */
    private String orderId;

    /**
     * 下单时间（订单创建时间）
     */
    private String createTime;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 订单类型 01--自主下单
     * 00--代客下单
     */
    private Integer orderType;

    /**
     * 商品名称（商品组/销售商品名称）
     */
    private String spuOfferingName;

    /**
     * 商品编码（商品组/销售商品编码）
     */
    private String spuOfferingCode;

    /**
     * 规格名称
     */
    private String skuOfferingName;

    /**
     * 规格编码
     */
    private String skuOfferingCode;

    /**
     * 商品类型
     */
    private String spuOfferingClass;

    /**
     * 订购数量
     */
    private String skuQuantity;

    /**
     * 原子商品类型:
     * 原子商品类型：
     * A("A", "自营软件服务"),
     * S("S", "软件功能费"),
     * H("H", "硬件（代销)"),
     *  O("O", "OneNET独立服务"),
     * D("D", "（DICT）产品增值服务包"),
     * P("P", "OnePark独立服务"),
     *  F("F", "行车卫士标准产品"),
     * K("K", "OneTraffic独立服务"),
     *  C("C", "卡资费"),
     * X("X", "（卡+X类）硬件")
     */
    private String atomOfferingClass;

    /**
     * 原子商品名称
     */
    private String atomOfferingName;

    /**
     * 原子商品编码
     */
    private String atomOfferingCode;

    /**
     * 原子商品数量
     */
    private String atomQuantity;

    /**
     * 原子商品单位
     */
    private String atomUnit;

    /**
     * 收货人省（加密）
     */
    private String addr1;


    /**
     * 收货人市（加密）
     */
    private String addr2;

    /**
     * 收货人区（加密）
     */
    private String addr3;

    /**
     * 收货人乡镇（加密）
     */
    private String addr4;

    /**
     * 非结构地址(加密）
     */
    private String usaddr;

    /**
     * 收货人姓名（加密传输）
     */
    private String contactPersonName;

    /**
     * 收货人电话（加密传输）
     */
    private String contactPhone;

    /**
     * 客户编码
     */
    private String custCode;

    /**
     * 客户电话
     */
    private String custId;
}
