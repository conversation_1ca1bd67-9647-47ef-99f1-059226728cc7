package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.BenefitOfferingMapper;
import com.chinamobile.iot.sc.pojo.entity.BenefitOffering;
import com.chinamobile.iot.sc.pojo.entity.BenefitOfferingExample;
import com.chinamobile.iot.sc.service.BenefitOfferingService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/19
 * @description IoT省内融合包信息关联商品信息service实现类
 */
@Service
public class BenefitOfferingServiceImpl implements BenefitOfferingService {

    @Resource
    private BenefitOfferingMapper benefitOfferingMapper;

    @Override
    public List<BenefitOffering> listBenefitOfferingByNeed(BenefitOfferingExample benefitOfferingExample) {
        return benefitOfferingMapper.selectByExample(benefitOfferingExample);
    }

    @Override
    public void batchAddBenefitOffering(List<BenefitOffering> benefitOfferingList) {
        benefitOfferingMapper.batchInsert(benefitOfferingList);
    }

    @Override
    public void updateBenefitOfferingByNeed(BenefitOffering benefitOffering,
                                            BenefitOfferingExample benefitOfferingExample) {
        benefitOfferingMapper.updateByExampleSelective(benefitOffering,benefitOfferingExample);
    }

    @Override
    public void updateBenefitOfferingById(BenefitOffering benefitOffering) {
        benefitOfferingMapper.updateByPrimaryKeySelective(benefitOffering);
    }
}
