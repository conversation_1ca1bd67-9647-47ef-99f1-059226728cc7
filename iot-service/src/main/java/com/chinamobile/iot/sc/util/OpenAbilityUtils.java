package com.chinamobile.iot.sc.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.OpenAbilityConstant;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.exception.ServicePowerException;
import com.chinamobile.iot.sc.mode.*;
import com.chinamobile.iot.sc.pojo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 开放能力工具类
 */
@Component
@Slf4j
public class OpenAbilityUtils {
    @Resource
    private OpenAbilityAppMapper openAbilityAppMapper;

    @Resource
    private OpenAbilityThemeContentMapper openAbilityThemeContentMapper;

    @Resource
    private OpenAbilityOrganizationMapper openAbilityOrganizationMapper;

    @Resource
    private OpenAbilityThemeFieldConfigMapper openAbilityThemeFieldConfigMapper;

    @Resource
    private OpenAbilityThemeContentConfigMapper openAbilityThemeContentConfigMapper;

    @Resource
    private OpenAbilityThemeFieldRuleMapper openAbilityThemeFieldRuleMapper;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private OpenAbilityThemeMapper openAbilityThemeMapper;

    @PostConstruct
    public void init() {
        load2redis();
    }

    public List<OpenAbilityAppRO> getAppBySubscribe(String themeCode,String dataCode) {
        List<OpenAbilityAppRO> appROS = getAllApp().stream().filter(x-> CollectionUtils.isNotEmpty(x.getThemeContentConfigList())
                && x.getThemeContentConfigList().stream().anyMatch(content -> StringUtils.equals(content.getThemeCode(),themeCode)
                        && StringUtils.equals(content.getPathCode(),dataCode))).collect(Collectors.toList());

        appROS.forEach(app -> {
            app.setThemeContentConfigList(app.getThemeContentConfigList().stream().filter(x->StringUtils.equals(x.getThemeCode(),themeCode)
                    && StringUtils.equals(x.getPathCode(),dataCode)).collect(Collectors.toList()));
            String contentId = app.getThemeContentConfigList().get(0).getContentId();
            app.setThemeFieldConfigList(app.getThemeFieldConfigList().stream().filter(x -> StringUtils.equals(x.getContentId(),contentId))
                    .collect(Collectors.toList()));
        });
        return appROS;
    }

    private List<OpenAbilityAppRO> getAllApp() {
        Set<String> keys = redisTemplate.keys(Constant.REDIS_KEY_OPEN_ABILITY_APP_ID + "*");
        if (CollectionUtils.isNotEmpty(keys)) {
            return (List<OpenAbilityAppRO>)redisTemplate.opsForValue().multiGet(keys);
        } else {
            return new ArrayList<>();
        }
    }

    public OpenAbilityOrganizationRO getOrganization(String organizationId) {
        return (OpenAbilityOrganizationRO)redisTemplate.opsForValue().get(Constant.REDIS_KEY_OPEN_ABILITY_ORGANIZATION_ID+organizationId);
    }

    public OpenAbilityAppRO getApp(String appId) {
        return (OpenAbilityAppRO)redisTemplate.opsForValue().get(Constant.REDIS_KEY_OPEN_ABILITY_APP_ID+appId);
    }

    public boolean checkDataMatched(Object object,OpenAbilityAppRO app) {
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(object));
        Map<String,List<OpenAbilityThemeFieldConfigRO>> ruleMap = app.getThemeFieldConfigList().stream()
                .collect(Collectors.groupingBy(OpenAbilityThemeFieldConfigRO::getRuleId));
        List<List<OpenAbilityThemeFieldConfigRO>> ruleFields = new ArrayList<>(ruleMap.values());
        return ruleFields.stream().anyMatch(list -> list.stream().allMatch(item -> checkDataMathField(jsonObject,item)));
    }

    private boolean checkDataMathField (JSONObject jsonObject,OpenAbilityThemeFieldConfigRO field) {
        Object target = jsonObject.get(field.getName());
        /**1.找不到筛选规则字段则表示，则表示筛选不通过*/
        if (ObjectUtils.isEmpty(target)) {
            return false;
        }
        boolean result = true;
        switch (field.getType()) {
            case OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_NUMBER:
                result = checkNumberData(target,field);
                break;
            case OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_ENUM:
                result = checkEnumData(target,field);
                break;
            case OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_STRING:
                result = checkStringData(target,field);
                break;
            case OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_BOOL:
                result = checkBoolData(target,field);
                break;
            case OpenAbilityConstant.OPEN_ABILITY_DATA_TYPE_TIME:
                result = checkTimeData(target,field);
                break;
            default:
                result = false;
        }
        return result;
    }

    private boolean checkNumberData(Object target,OpenAbilityThemeFieldConfigRO field) {
        try {
            boolean result = false;
            if (target instanceof Integer || target instanceof Long) {
                long value = (long)target;
                Long filterValue = StringUtils.isNotBlank(field.getRuleValueObj()) ?
                        Long.parseLong(field.getRuleValueObj()) : null;
                List<Long> filterRange = StringUtils.isNotBlank(field.getRuleRangeList()) ?
                        JSON.parseArray(field.getRuleRangeList(),Long.class) : new ArrayList<>();
                switch (field.getRuleType()) {
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_EQUAL:
                        result = value == filterValue;
                        break;
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_NOT_EQUAL:
                        result = value != filterValue;
                        break;
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_GREATER_THAN:
                        result = value > filterValue;
                        break;
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_GREATER_EQUAL_THAN:
                        result = value >= filterValue;
                        break;
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_LESS_THAN:
                        result = value < filterValue;
                        break;
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_LESS_EQUAL_THAN:
                        result = value <= filterValue;
                        break;
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_IN:
                        result = filterRange.contains(value);
                        break;
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_NOT_IN:
                        result = !filterRange.contains(value);
                        break;
                }
            } else if (target instanceof Float || target instanceof Double) {
                double value = (double)target;
                Double filterValue = StringUtils.isNotBlank(field.getRuleValueObj()) ? Double.parseDouble(field.getRuleValueObj()) : 0;
                List<Double> filterRange = StringUtils.isNotBlank(field.getRuleRangeList())
                        ?JSON.parseArray(field.getRuleRangeList(),Double.class) : new ArrayList<>();
                switch (field.getRuleType()) {
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_EQUAL:
                        result = Math.abs(value - filterValue) < 0.00001f;
                        break;
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_NOT_EQUAL:
                        result = Math.abs(value - filterValue) > 0.00001f;
                        break;
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_GREATER_THAN:
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_GREATER_EQUAL_THAN:
                        result = value - filterValue > 0.00001f;
                        break;

                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_LESS_THAN:
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_LESS_EQUAL_THAN:
                        result = value - filterValue < -0.00001f;
                        break;
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_IN:
                        result = filterRange.stream().anyMatch( x->Math.abs(value - x) < 0.00001f);
                        break;
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_NOT_IN:
                        result = filterRange.stream().noneMatch( x->Math.abs(value - x) < 0.00001f);
                        break;
                }

            }
            return result;
        } catch (Exception exception) {
            log.info("openAbilityDecode：数据解析异常，target:{},field:{}",JSON.toJSONString(target),JSON.toJSONString(field));
            return false;
        }

    }

    private boolean checkStringData(Object target,OpenAbilityThemeFieldConfigRO field) {
        try {
            boolean result = true;
            String value = (String)target;
            String filterValue = field.getRuleValueObj();
            List<String> filterRange = StringUtils.isNotBlank(field.getRuleRangeList()) ?JSON.parseArray(field.getRuleRangeList(),String.class): new ArrayList<>();
            switch (field.getRuleType()) {
                case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_EQUAL:
                    result = StringUtils.equals(value,filterValue);
                    break;
                case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_NOT_EQUAL:
                    result = !StringUtils.equals(value,filterValue);
                    break;
                case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_IN:
                    result = filterRange.contains(value);
                    break;
                case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_NOT_IN:
                    result = !filterRange.contains(value);
                    break;
                case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_LIKE:
                    result = Pattern.matches(filterValue,value);
                    break;
                case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_NOT_LIKE:
                    result = !Pattern.matches(filterValue,value);
                    break;
            }
            return result;
        } catch (Exception exception) {
            log.info("openAbilityDecode：数据解析异常，target:{},field:{}",JSON.toJSONString(target),JSON.toJSONString(field));
            return false;
        }
    }

    private boolean checkBoolData(Object target,OpenAbilityThemeFieldConfigRO field) {
        try {
            boolean result = true;
            boolean value = (Boolean)target;
            Boolean filterValue = Boolean.valueOf(field.getRuleValueObj());
            switch (field.getRuleType()) {
                case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_EQUAL:
                    result = value == filterValue;
                    break;
            }
            return result;
        } catch (Exception exception) {
            log.info("openAbilityDecode：数据解析异常，target:{},field:{}",JSON.toJSONString(target),JSON.toJSONString(field));
            return false;
        }
    }

    private boolean checkTimeData(Object target,OpenAbilityThemeFieldConfigRO field) {
        try {
            boolean result = true;
            long value = DateUtils.strToDate((String)target,DateUtils.DEFAULT_DATETIME_FORMAT).getTime();
            long filterValue = StringUtils.isNotBlank(field.getRuleValueObj()) ?
                    DateUtils.strToDate(field.getRuleValueObj(),DateUtils.DEFAULT_DATETIME_FORMAT).getTime() : 0;
            List<Long> filterRange = new ArrayList<>();
            if (StringUtils.isNotBlank(field.getRuleRangeList())) {
                filterRange = JSON.parseArray(field.getRuleRangeList(),Date.class).stream().map(Date::getTime).collect(Collectors.toList());
            }
            switch (field.getRuleType()) {
                case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_EQUAL:
                    result = value == filterValue;
                    break;
                case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_NOT_EQUAL:
                    result = value != filterValue;
                    break;
                case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_GREATER_THAN:
                    result = value > filterValue;
                    break;
                case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_GREATER_EQUAL_THAN:
                    result = value >= filterValue;
                    break;
                case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_LESS_THAN:
                    result = value < filterValue;
                    break;
                case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_LESS_EQUAL_THAN:
                    result = value <= filterValue;
                    break;
                case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_IN:
                    result = filterRange.contains(value);
                    break;
                case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_NOT_IN:
                    result = !filterRange.contains(value);
                    break;
            }
            return result;
        } catch (Exception exception) {
            log.info("openAbilityDecode：数据解析异常，target:{},field:{}",JSON.toJSONString(target),JSON.toJSONString(field));
            return false;
        }
    }

    private boolean checkEnumData(Object target,OpenAbilityThemeFieldConfigRO field) {
        try {
            boolean result = false;
            if (target instanceof Integer) {
                long value = (Integer)target;
                long filterValue = StringUtils.isNotBlank(field.getRuleValueObj()) ?
                        Long.parseLong(field.getRuleValueObj()) : 0;
                List<Long> filterRange = StringUtils.isNotBlank(field.getRuleRangeList()) ?
                        JSON.parseArray(field.getRuleRangeList(), Long.class) : new ArrayList<>();
                switch (field.getRuleType()) {
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_EQUAL:
                        result = value == filterValue;
                        break;
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_NOT_EQUAL:
                        result = value != filterValue;
                        break;
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_IN:
                        result = filterRange.contains(value);
                        break;
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_NOT_IN:
                        result = !filterRange.contains(value);
                        break;
                }
            } else if (target instanceof String) {
                String value = (String)target;
                String filterValue = field.getRuleValueObj();
                List<String> filterRange = StringUtils.isNotBlank(field.getRuleRangeList()) ?
                        JSON.parseArray(field.getRuleRangeList(),String.class): new ArrayList<>();
                switch (field.getRuleType()) {
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_EQUAL:
                        result = StringUtils.equals(value, filterValue);
                        break;
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_NOT_EQUAL:
                        result = !StringUtils.equals(value, filterValue);
                        break;
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_IN:
                        result = filterRange.contains(value);
                        break;
                    case OpenAbilityConstant.OPEN_ABILITY_FILTER_TYPE_NOT_IN:
                        result = !filterRange.contains(value);
                        break;
                }
            }
            return result;
        } catch (Exception exception) {
            log.info("openAbilityDecode：数据解析异常，target:{},field:{}",JSON.toJSONString(target),JSON.toJSONString(field));
            return false;
        }
    }

    public void deleteKeysByPrefix(String prefix) {
        Set<String> keys = redisTemplate.keys(prefix + "*");
        List<String> batchDeleteKeys = new ArrayList<>(keys);
        if (!batchDeleteKeys.isEmpty()) {
            redisTemplate.delete(batchDeleteKeys);
        }
    }

    public void load2redis() {
        //加载机构信息进redis缓存
        deleteKeysByPrefix(Constant.REDIS_KEY_OPEN_ABILITY_ORGANIZATION_ID);
        List<OpenAbilityOrganization> organizations = openAbilityOrganizationMapper.selectByExample(new OpenAbilityOrganizationExample()
                .createCriteria().andEnableEqualTo(true).example());
        List<String> organizationIds = organizations.stream().map(OpenAbilityOrganization::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(organizations)) {
            List<OpenAbilityThemeContentConfig> contentConfigs = openAbilityThemeContentConfigMapper.selectByExample(
                    new OpenAbilityThemeContentConfigExample().createCriteria().andOrganizationIdIn(organizationIds).example());
            List<OpenAbilityThemeFieldConfig> fieldConfigs = openAbilityThemeFieldConfigMapper.selectByExample(
                    new OpenAbilityThemeFieldConfigExample().createCriteria().andOrganizationIdIn(organizationIds).example());
            List<String> contentIds = contentConfigs.stream().map(OpenAbilityThemeContentConfig::getContentId).collect(Collectors.toList());
            List<String> fieldIds = fieldConfigs.stream().map(OpenAbilityThemeFieldConfig::getFieldId).collect(Collectors.toList());
            Map<String,List<OpenAbilityThemeContentConfigRO>> contentConfigMap = new LinkedHashMap<>();
            if (CollectionUtils.isNotEmpty(contentIds)) {
                Map<String,OpenAbilityThemeContent> contentMap = new LinkedHashMap<>();
                openAbilityThemeContentMapper.selectByExample(new OpenAbilityThemeContentExample().createCriteria()
                                .andIdIn(contentIds).example()).forEach(x-> contentMap.put(x.getId(),x));
                Map<String,OpenAbilityTheme> themeMap = new LinkedHashMap<>();
                openAbilityThemeMapper.selectByExample(new OpenAbilityThemeExample().createCriteria().andIdIn(
                        contentMap.values().stream().map(OpenAbilityThemeContent::getThemeId).distinct().collect(Collectors.toList())).example())
                        .forEach(x-> themeMap.put(x.getId(),x));
                contentConfigMap = contentConfigs.stream().map(x -> {
                    OpenAbilityThemeContentConfigRO ro = new OpenAbilityThemeContentConfigRO();
                    BeanUtils.copyProperties(contentMap.get(x.getContentId()), ro);
                    BeanUtils.copyProperties(x, ro);
                    ro.setThemeCode(themeMap.get(ro.getThemeId()).getCode());
                    return ro;
                }).collect(Collectors.groupingBy(OpenAbilityThemeContentConfigRO::getOrganizationId));
            }

            Map<String,List<OpenAbilityThemeFieldConfigRO>> feildConfigMap = new LinkedHashMap<>();
            if (CollectionUtils.isNotEmpty(fieldIds)) {
                Map<String,OpenAbilityThemeFieldRule> feildMap = new LinkedHashMap<>();
                openAbilityThemeFieldRuleMapper.selectByExample(new OpenAbilityThemeFieldRuleExample().createCriteria()
                                .andIdIn(fieldIds).example()).forEach(x-> feildMap.put(x.getId(),x));
                feildConfigMap = fieldConfigs.stream().map(x -> {
                    OpenAbilityThemeFieldConfigRO ro = new OpenAbilityThemeFieldConfigRO();
                    BeanUtils.copyProperties(feildMap.get(x.getFieldId()), ro);
                    BeanUtils.copyProperties(x, ro);
                    return ro;
                }).collect(Collectors.groupingBy(OpenAbilityThemeFieldConfigRO::getOrganizationId));
            }

            Map<String, List<OpenAbilityThemeContentConfigRO>> finalContentConfigMap = contentConfigMap;
            Map<String, List<OpenAbilityThemeFieldConfigRO>> finalFeildConfigMap = feildConfigMap;
            Map<String, OpenAbilityOrganizationRO> openAbilityOrganizationROMap = new LinkedHashMap<>();
            organizations.forEach(x -> {
                OpenAbilityOrganizationRO ro = new OpenAbilityOrganizationRO();
                BeanUtils.copyProperties(x,ro);
                ro.setThemeContentConfigList(finalContentConfigMap.get(x.getId()));
                ro.setThemeFieldConfigList(finalFeildConfigMap.get(x.getId()));

                openAbilityOrganizationROMap.put(Constant.REDIS_KEY_OPEN_ABILITY_ORGANIZATION_ID + ro.getId(),ro);
            });
            System.out.println("加载进redis的机构信息：");
            System.out.println(JSON.toJSONString(openAbilityOrganizationROMap));
            redisTemplate.opsForValue().multiSet(openAbilityOrganizationROMap);
        }

        //加载APP信息进redis缓存
        deleteKeysByPrefix(Constant.REDIS_KEY_OPEN_ABILITY_APP_ID);
        List<OpenAbilityApp> apps = openAbilityAppMapper.selectByExample(new OpenAbilityAppExample()
                .createCriteria().andOrganizationIdIn(organizationIds).example());
        List<String> appIds = apps.stream().map(OpenAbilityApp::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(apps)) {
            List<OpenAbilityThemeContentConfig> contentConfigs = openAbilityThemeContentConfigMapper.selectByExample(
                    new OpenAbilityThemeContentConfigExample().createCriteria().andAppIdIn(appIds).example());
            List<OpenAbilityThemeFieldConfig> fieldConfigs = openAbilityThemeFieldConfigMapper.selectByExample(
                    new OpenAbilityThemeFieldConfigExample().createCriteria().andAppIdIn(appIds).example());
            List<String> contentIds = contentConfigs.stream().map(OpenAbilityThemeContentConfig::getContentId).distinct()
                    .collect(Collectors.toList());
            List<String> fieldIds = fieldConfigs.stream().map(OpenAbilityThemeFieldConfig::getFieldId).distinct()
                    .collect(Collectors.toList());
            Map<String,List<OpenAbilityThemeContentConfigRO>> contentConfigMap = new LinkedHashMap<>();
            if (CollectionUtils.isNotEmpty(contentIds)) {
                Map<String,OpenAbilityThemeContent> contentMap = new LinkedHashMap<>();
                openAbilityThemeContentMapper.selectByExample(new OpenAbilityThemeContentExample().createCriteria()
                        .andIdIn(contentIds).example()).forEach(x-> contentMap.put(x.getId(),x));
                Map<String,OpenAbilityTheme> themeMap = new LinkedHashMap<>();
                openAbilityThemeMapper.selectByExample(new OpenAbilityThemeExample().createCriteria().andIdIn(
                                contentMap.values().stream().map(OpenAbilityThemeContent::getThemeId).distinct().collect(Collectors.toList())).example())
                        .forEach(x-> themeMap.put(x.getId(),x));
                contentConfigMap = contentConfigs.stream().map(x -> {
                    OpenAbilityThemeContentConfigRO ro = new OpenAbilityThemeContentConfigRO();
                    BeanUtils.copyProperties(contentMap.get(x.getContentId()), ro);
                    BeanUtils.copyProperties(x, ro);
                    ro.setThemeCode(themeMap.get(ro.getThemeId()).getCode());
                    return ro;
                }).collect(Collectors.groupingBy(OpenAbilityThemeContentConfigRO::getAppId));
            }

            Map<String,List<OpenAbilityThemeFieldConfigRO>> feildConfigMap = new LinkedHashMap<>();
            if (CollectionUtils.isNotEmpty(fieldIds)) {
                Map<String,OpenAbilityThemeFieldRule> feildMap = new LinkedHashMap<>();
                openAbilityThemeFieldRuleMapper.selectByExample(new OpenAbilityThemeFieldRuleExample().createCriteria()
                                .andIdIn(fieldIds).example()).forEach(x-> feildMap.put(x.getId(),x));
                feildConfigMap = fieldConfigs.stream().map(x -> {
                    OpenAbilityThemeFieldConfigRO ro = new OpenAbilityThemeFieldConfigRO();
                    BeanUtils.copyProperties(feildMap.get(x.getFieldId()), ro);
                    BeanUtils.copyProperties(x, ro);
                    return ro;
                }).collect(Collectors.groupingBy(OpenAbilityThemeFieldConfigRO::getAppId));
            }

            Map<String, List<OpenAbilityThemeContentConfigRO>> finalContentConfigMap = contentConfigMap;
            Map<String, List<OpenAbilityThemeFieldConfigRO>> finalFeildConfigMap = feildConfigMap;
            Map<String, OpenAbilityAppRO> openAbilityAppROMap = new LinkedHashMap<>();
            apps.forEach(x -> {
                OpenAbilityAppRO ro = new OpenAbilityAppRO();
                BeanUtils.copyProperties(x,ro);
                ro.setThemeContentConfigList(finalContentConfigMap.get(x.getId()));
                ro.setThemeFieldConfigList(finalFeildConfigMap.get(x.getId()));

                openAbilityAppROMap.put(Constant.REDIS_KEY_OPEN_ABILITY_APP_ID + ro.getId(),ro);
            });
            System.out.println("加载进redis的应用信息：");
            System.out.println(JSON.toJSONString(openAbilityAppROMap));
            redisTemplate.opsForValue().multiSet(openAbilityAppROMap);
        }

        //加载接口信息进redis缓存
        redisTemplate.delete(Constant.REDIS_KEY_OPEN_ABILITY_API_LIST);
        List<String> apiThemeContents = openAbilityThemeContentMapper.selectByExample(new OpenAbilityThemeContentExample()
                .createCriteria().andTypeEqualTo(BaseConstant.OPEN_ABILITY_CONTENT_TPE_API).example()).stream()
                .map(x -> x.getPathCode() +"_"+StringUtils.lowerCase(x.getMethod())).collect(Collectors.toList());
        System.out.println("加载进redis的开放接口信息：");
        System.out.println(JSON.toJSONString(apiThemeContents));
        redisTemplate.opsForValue().set(Constant.REDIS_KEY_OPEN_ABILITY_API_LIST,apiThemeContents);
    }
}
