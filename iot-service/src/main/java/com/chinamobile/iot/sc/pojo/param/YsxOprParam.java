package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/18
 * @description 千里眼平台校验SN参数
 */
@Data
public class YsxOprParam {

    /**
     * 订单id
     */
    @NotEmpty(message = "订单ID不能为空")
    private String orderId;

    /**
     * 操作类型，"01"-订阅，"02"-退订
     */
    @NotEmpty(message = "操作类型不能为空")
    private String oprCode;
}
