package com.chinamobile.iot.sc.response.iot;

import lombok.Data;

import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/11/15 19:28
 * @Description: 库存预占返回结果
 */
@Data
public class ReserveInventoryResponse {

    /**
     * 总预占状态
     * 0：预占成功（全部预占成功）
     * 1：预占失败（全部或部分预占失败）
     */
    private String allPreemptStatus;

    private List<InventoryInfo> inventoryInfo;

    @Data
    public static class InventoryInfo{
        /**
         * sku offeringCode
         */
        private String offeringCode;
        /**
         * 预占状态
         * 0:预占成功
         * 1：资源不足
         */
        private String preemptStatus;
    }
}
