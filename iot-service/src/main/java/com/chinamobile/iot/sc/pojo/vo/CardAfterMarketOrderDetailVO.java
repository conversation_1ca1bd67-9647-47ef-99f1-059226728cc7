package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/23
 * @description x终端详情的售后服务订单信息
 */
@Data
public class CardAfterMarketOrderDetailVO {

    /**
     * 售后订单关联售后服务表主键
     */
    private String id;

    /**
     * 售后订单id
     */
    private String serviceOrderId;

    /**
     * 售后商品code
     */
    private String afterMarketCode;

    /**
     * 售后商品名称
     */
    private String afterMarketName;

    /**
     * 售后服务类型,1、OneNET/OnePark属地化服务；2、铁通增值服务；
     */
    private Integer afterMarketType;

    /**
     * 接单方式 1--OS接单；2--省内接单
     */
    private Integer orderTakeType;

    /**
     * 售后服务商品结算单价
     */
    private String afterMarketSettlePrice;

    /**
     * 合作伙伴主账号名称
     */
    private String adminCooperatorName;

    /**
     * 合作伙伴主账号ID
     */
    private String adminCooperatorId;

    /**
     * 规格商品名称
     */
    private String skuOfferingName;

    /**
     * 原子商品名称
     */
    private String atomOfferingName;

    /**
     * 订购数量
     */
    private Long quantity;


    /**
     * 当前派单人员姓名
     */
    private String presentSendOrderName;

    /**
     * 当前派单人员电话
     */
    private String presentSendOrderPhone;

    /**
     * 售后商品版本号
     */
    private String afterMarketVersion;
}
