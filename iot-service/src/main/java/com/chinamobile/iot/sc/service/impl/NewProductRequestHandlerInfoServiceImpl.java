package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.dao.NewProductRequestHandlerInfoMapper;
import com.chinamobile.iot.sc.dao.ext.NewProductRequestHandlerInfoMapperExt;
import com.chinamobile.iot.sc.pojo.NewProductRequestHandlerInfo;
import com.chinamobile.iot.sc.pojo.NewProductRequestHandlerInfoExample;
import com.chinamobile.iot.sc.pojo.param.NewProductRequestHandlerInfoParam;
import com.chinamobile.iot.sc.pojo.vo.NewProductRequestHandlerInfoVO;
import com.chinamobile.iot.sc.service.NewProductRequestHandlerInfoService;
import com.chinamobile.iot.sc.util.DateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

import static com.chinamobile.iot.sc.constant.NewProductRequestFlowTypeConstant.ADD_NEW_PRODUCT;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/15
 * @description 新产品引入相关流程处理信息service实现类
 */
@Service
public class NewProductRequestHandlerInfoServiceImpl implements NewProductRequestHandlerInfoService {

    @Resource
    private NewProductRequestHandlerInfoMapper newProductRequestHandlerInfoMapper;

    @Resource
    private NewProductRequestHandlerInfoMapperExt newProductRequestHandlerInfoMapperExt;

    @Override
    public void saveHandlerInfo(NewProductRequestHandlerInfo handlerInfo) {
        newProductRequestHandlerInfoMapper.insert(handlerInfo);
    }

    @Override
    public NewProductRequestHandlerInfo getLastHandlerInfo(NewProductRequestHandlerInfoParam handlerInfoParam) {
        NewProductRequestHandlerInfoExample example = new NewProductRequestHandlerInfoExample();
        NewProductRequestHandlerInfoExample.Criteria criteria = example.createCriteria()
                .andFlowSourceIdEqualTo(handlerInfoParam.getFlowSourceId())
                .andFlowTypeEqualTo(handlerInfoParam.getFlowType())
                .andNextHandlerUserIdEqualTo(handlerInfoParam.getNextHandlerUserId());
        String onlineStatus = handlerInfoParam.getOnlineStatus();
        if (StringUtils.isNotEmpty(onlineStatus)){
            criteria.andOnlineStatusEqualTo(onlineStatus);
        }
        example.orderBy("update_time desc");
        List<NewProductRequestHandlerInfo> handlerInfoList = newProductRequestHandlerInfoMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(handlerInfoList)){
            return handlerInfoList.get(0);
        }else {
            return null;
        }
    }

    @Override
    public void batchSaveHandlerInfo(List<NewProductRequestHandlerInfo> handlerInfoList) {
        newProductRequestHandlerInfoMapper.batchInsert(handlerInfoList);
    }

    @Override
    public List<NewProductRequestHandlerInfoVO> listNewProductRequestHandlerInfo(String flowSourceId, String flowType) {
        List<NewProductRequestHandlerInfoVO> handlerInfoVOList = newProductRequestHandlerInfoMapperExt.listNewProductRequestHandlerInfo(flowSourceId, flowType);
        if (CollectionUtils.isNotEmpty(handlerInfoVOList)){
            handlerInfoVOList.stream().forEach(handlerInfo -> {
                handlerInfo.setCreateTimeStr(DateUtils.dateToStr(handlerInfo.getCreateTime(),DateUtils.DEFAULT_DATETIME_FORMAT));
                handlerInfo.setUpdateTimeStr(DateUtils.dateToStr(handlerInfo.getUpdateTime(),DateUtils.DEFAULT_DATETIME_FORMAT));
            });
        }
        return handlerInfoVOList;
    }

    @Override
    public List<NewProductRequestHandlerInfo> getListHandlerInfo(String flowSourceId, String flowType) {
        NewProductRequestHandlerInfoExample example = new NewProductRequestHandlerInfoExample().createCriteria().andFlowSourceIdEqualTo(flowSourceId).andFlowTypeEqualTo(flowType).example();
        return newProductRequestHandlerInfoMapper.selectByExample(example);
    }

    @Override
    public NewProductRequestHandlerInfo getReviewDetails(String flowSourceId,String flowType,String nextUserId) {
        NewProductRequestHandlerInfoExample example = new NewProductRequestHandlerInfoExample().createCriteria().andFlowSourceIdEqualTo(flowSourceId).andNextHandlerUserIdEqualTo(nextUserId).andFlowTypeEqualTo(flowType).example();
        List<NewProductRequestHandlerInfo> newProductRequestHandlerInfos = newProductRequestHandlerInfoMapper.selectByExample(example);
        return newProductRequestHandlerInfos.get(0);
    }

    @Override
    public void updateHandlerInfo(NewProductRequestHandlerInfo handlerInfo) {
        newProductRequestHandlerInfoMapper.updateByPrimaryKeySelective(handlerInfo);
    }
}
