package com.chinamobile.iot.sc.response.web.good;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @package: com.chinamobile.iot.sc.response.web.good
 * @ClassName: AtomOrderInfo
 * @description: 原子-货物订单详细信息
 * @author: zyj
 * @create: 2022/1/30 9:59
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AtomOrderInfo {
    private String id;
    /**
     * 订单ID
     */
    private String orderId;
    /**
     * 商品名称(规格)
     */
    private String skuOfferingName;
    /**
     * 商品编码
     */
    private String skuOfferingCode;
    /**
     * 原子商品名称
     */
    private String atomOfferingName;
    /**
     * sku订购数量
     */
    private Long skuQuantity;
    /**
     * atom订购数量
     */
    private Long atomQuantity;
    /**
     * 单价 厘
     */
    private Long atomPrice;
    /**
     * 收货人姓名
     */
    private String contactPersonName;
    /**
     * 合作伙伴公司
     */
    private String partnerName;
    /**
     * 下单时间
     */
    private String createTime;
}
