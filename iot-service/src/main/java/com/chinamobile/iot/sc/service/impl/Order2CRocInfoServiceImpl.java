package com.chinamobile.iot.sc.service.impl;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.constant.OrderRocReasonEnum;
import com.chinamobile.iot.sc.dao.Order2cRocInfoMapper;
import com.chinamobile.iot.sc.dao.handle.OrderRocHandleMapper;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.pojo.Order2cRocInfo;
import com.chinamobile.iot.sc.pojo.Order2cRocInfoExample;
import com.chinamobile.iot.sc.pojo.vo.LatestRefundRocVO;
import com.chinamobile.iot.sc.service.Order2CRocInfoService;
import com.github.pagehelper.PageHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/1
 * @description 订单退货退款换货记录service实现类
 */
@Service
public class Order2CRocInfoServiceImpl implements Order2CRocInfoService {

    @Resource
    private OrderRocHandleMapper orderRocHandleMapper;

    @Resource
    private Order2cRocInfoMapper order2cRocInfoMapper;

    @Override
    public LatestRefundRocVO getLatestRefundRocByOrderId(String orderId) {
        LatestRefundRocVO latestRefundRocVO = orderRocHandleMapper.getLatestRefundRocByOrderId(orderId);
        if (!Optional.ofNullable(latestRefundRocVO).isPresent()){
            throw new BusinessException("10028","未找到退款信息");
        }

        String reason = latestRefundRocVO.getReason();
        if (StringUtils.isNotEmpty(reason)){
            String describe = OrderRocReasonEnum.getDescribe(reason);
            if (StringUtils.isNotEmpty(describe)){
                latestRefundRocVO.setReasonStr(describe);
            }
        }

        return latestRefundRocVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer initRocId() {
        int pageSize = 500;
        int page = 1;
        while (true){
            PageHelper.startPage(1,pageSize);
            Order2cRocInfoExample rocInfoExample = new Order2cRocInfoExample();
            rocInfoExample.createCriteria().andIdIsNull();
            rocInfoExample.orderBy("create_time ASC");
            List<Order2cRocInfo> order2cRocInfos = order2cRocInfoMapper.selectByExample(rocInfoExample);
            for (Order2cRocInfo order2cRocInfo : order2cRocInfos) {
                order2cRocInfo.setId(BaseServiceUtils.getId());
                order2cRocInfoMapper.updateByPrimaryKeySelective(order2cRocInfo);
            }
            if(order2cRocInfos.size() < pageSize){
                break;
            }
            page++;
        }

        return BaseAnswer.success("ok");
    }
}
