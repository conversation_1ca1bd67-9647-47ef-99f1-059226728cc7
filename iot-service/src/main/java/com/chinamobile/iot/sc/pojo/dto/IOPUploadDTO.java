package com.chinamobile.iot.sc.pojo.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class IOPUploadDTO implements Serializable {

    /**
     * 业务订单流水号
     */
    private String orderId;


    /**
     * 规格商品编码
     */
    private String skuOfferingCode;

    /**
     * 商品规格名
     */
    private String skuOfferingName;

    /**
     * 订购数量
     * 门户选择的规格数量
     */
    private Long skuQuantity;


    /**
     * 原子商品编码
     */
    private String atomOfferingCode;

    /**
     * 原子商品名称
     */
    private String atomOfferingName;

    /**
     * 规格配置的原子商品的数量
     */
    private Long atomQuantity;

    /**
     * 订单状态，0 待发货、1 待收货、2 已收货、3 开票、4 退款中、
     * 5 退货退款中、6 换货中、7 交易完成、8 交易失败
     */
    private Integer orderStatus;


    /**
     * 省编码
     */
    private String beId;



    /**
     * 订单创建时间 取IOT商城传递过来的
     */
    private String timeStr;

    /**
     * 订单金额
     */
    private String totalPrice;

    /**
     * 操作员姓名
     */
    private String name;
    /**
     * 操作员电话
     */
    private String phone;
}