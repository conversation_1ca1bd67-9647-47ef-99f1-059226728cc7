package com.chinamobile.iot.sc.pojo.vo;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> xie<PERSON><PERSON><PERSON>
 * @date : 2022/9/19 11:06
 * @description: 产品列表VO
 **/
@Data
public class NewProductRequestManageListVO{

    private String id;

    /**
     * 申请编号
     */
    private String requestNo;

    /**
     * spu商品名称
     */
    private String spuOfferingName;

    /**
     * 商品名称SKU
     */
    private String skuOfferingName;

    /**
     * 审核状态
     */
    private Integer requestStatus;

    /**
     * 合作厂商id,相当于用户表id
     */
    private String cooperatorId;

    /**
     * 合作伙伴名称
     */
    private String cooperatorName;

    /**
     * '品牌、品牌方
     */
    private String brand;

    /**
     * 型号
     */
    private String model;

    /**
     * 颜色
     */
    private String color;

    /**
     * 物料编码
     */
    private String materialCode;


    /**
     * 联网属性:无、2G、4G、蓝牙、WIFI、NB
     */
    private String networkProperty;

    /**
     * 典型应用领域
     */
    private String applicationDomain;

    /**
     * 商品简介
     */
    private String productIntroduction;

    /**
     * 商品规格套餐销售内容
     */
    private String productSaleContent;

    /**
     * 产品销售区域编码
     */
    private String productSaleAreaCode;

    /**
     * 产品销售区域
     */
    private String productSaleArea;


    /**
     * 商品规格供货价（元）
     */
    private BigDecimal supplyPrice;


    /**
     * 市场价格（元
     */
    private BigDecimal marketPrice;

    /**
     * 市场销售价
     */
    private BigDecimal salePrice;

    /**
     * 产品经理名字
     */
    private String productManagerName;


    /**
     * 产品经理电话
     */
    private String productManagerPhone;

    /**
     * 产品经理邮箱
     */
    private String productManagerEmail;

    /**
     * 产品引入当前处理人
     */
    private String requestCurrentHandlerUserId;

    /**
     * 创建时间
     */
    private Date createTime;
}
