package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.pojo.param.SortNavigationParam;
import com.chinamobile.iot.sc.pojo.param.UpdateNavigationImageParam;
import com.chinamobile.iot.sc.pojo.vo.miniprogram.ProductNavigationDirectoryVO;
import com.chinamobile.iot.sc.request.ProductNavigationInfoMigrateRequest;
import com.chinamobile.iot.sc.service.IProductNavigationDirectoryService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/21 11:10
 * @description 商城产品导航目录Controller
 */
@RestController
@RequestMapping("/osweb/product/navigation")
public class ProductNavigationDirectoryController {

    @Resource
    private IProductNavigationDirectoryService productNavigationDirectoryService;

    /**
     * 列出商品导航目录
     * @return
     */
    @GetMapping("/list")
    public BaseAnswer<List<ProductNavigationDirectoryVO>> list() {
        List<ProductNavigationDirectoryVO> vos = productNavigationDirectoryService.listAll();
        return new BaseAnswer<List<ProductNavigationDirectoryVO>>().setData(vos);
    }

    /**
     * 更新商品导航目录头图
     * @param param
     * @return
     */
    @PutMapping("/updateImage")
    public BaseAnswer<Void> updateImage(@Valid @RequestBody UpdateNavigationImageParam param) {
        productNavigationDirectoryService.updateImage(param);
        return new BaseAnswer<>();
    }

    /**
     * 商品导航目录排序
     * @param params
     * @return
     */
    @PutMapping("/sort")
    public BaseAnswer<Void> sort(@Valid @RequestBody @NotEmpty List<SortNavigationParam> params) {
        productNavigationDirectoryService.sort(params);
        return new BaseAnswer<>();
    }

    /**
     * 迁移目录数据
     */
    @PostMapping("/migrate")
    public BaseAnswer<Void> migrateProductNavigation(@RequestBody ProductNavigationInfoMigrateRequest request){
        productNavigationDirectoryService.migrateProductNavigation(request);
        return new BaseAnswer<>();
    }
}
