package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.response.web.Order2CInfoDetailDTO;
import com.chinamobile.iot.sc.service.OrderTakeEffectTimeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> xie<PERSON><PERSON><PERSON>
 * @date : 2025/4/28 16:35
 * @description:
 **/
@RestController
@RequestMapping("/osweb/take")
@Slf4j
public class OrderTakeEffectTimeController {

    @Resource
    private OrderTakeEffectTimeService orderTakeEffectTimeService;


    /**
     * 手动处理定时生效订单
     * @param dayBegin
     * @return
     */
    @PostMapping("/effectTime")
    public BaseAnswer<Void> OrderTakeEffectTimeTaskMessage(@RequestParam("dayBegin") String dayBegin) {
        orderTakeEffectTimeService.orderTakeEffectTimeTask(dayBegin);
        return new BaseAnswer<>();
    }
}
