package com.chinamobile.iot.sc.controller.iot;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.entity.ByteArrayUpload;
import com.chinamobile.iot.sc.entity.UpResult;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.service.IStorageService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/19 14:36
 * @description TODO
 */
@RestController
@RequestMapping("/osweb")
public class StorageController {

    private static final Logger log = LoggerFactory.getLogger(StorageController.class);
    @Autowired
    private IStorageService storageService;

    @PostMapping("/file/upload")
    public BaseAnswer<UpResult> uploadFile(@RequestPart("file") MultipartFile file) throws Exception {
        //应信安要求，限制非法文件上传
        String filename = file.getOriginalFilename();
        log.info("uploadFile filename = {}", filename);
        if(StringUtils.isEmpty(filename)){
            throw new BusinessException("500", "文件名不能为空");
        }
        int lastDotIndex = filename.lastIndexOf(".");
        if(lastDotIndex == -1 || lastDotIndex == filename.length() - 1){
            throw new BusinessException("500", "文件名格式错误");
        }
        String suffix = filename.substring(lastDotIndex+1).trim();
        if(suffix.equals("php") || suffix.equals("jsp") || suffix.equals("jar")
            || suffix.equals("asp") || suffix.equals("c") || suffix.equals("java") || suffix.equals("js")){
            throw new BusinessException("500", "不支持非法文件上传");
        }
//        if(filename.endsWith(".php") || filename.endsWith(".jsp")||filename.endsWith(".jar")
//                ||filename.endsWith(".asp")||filename.endsWith(".c")||filename.endsWith(".java")||filename.endsWith(".js")){
//            throw new BusinessException("500", "不支持非法文件上传");
//        }
        ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
        byteArrayUpload.setBytes(file.getBytes());
        byteArrayUpload.setCover(true);
        //生成唯一文件名，避免覆盖
        byteArrayUpload.setFileName(BaseServiceUtils.getId()
                + file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".")));
        return storageService.uploadByte(byteArrayUpload);
    }

}
