package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/13
 * @description 新产品上线申请返回值
 */
@Data
public class NewProductOnlineRequestVO {

    /**
     * 新产品引入申请表id
     */
    private String newProductRequestId;

    /**
     * 产品引入申请编号
     */
    private String requestNo;

    /**
     * 商品名称SPU
     */
    private String spuOfferingName;

    /**
     * 商品名称SKU
     */
    private String skuOfferingName;

    /**
     * 合作伙伴名称
     */
    private String cooperatorName;

    /**
     * 典型应用领域
     */
    private String applicationDomain;


    /**
     * 商品简介
     */
    private String productIntroduction;

    /**
     * 产品销售区域
     */
    private String productSaleArea;

    /**
     * 商品规格供货价（元）
     */
    private BigDecimal supplyPrice;

    /**
     * 产品经理名字
     */
    private String productManagerName;


    /**
     * 产品经理电话
     */
    private String productManagerPhone;

    /**
     * 产品经理邮箱
     */
    private String productManagerEmail;

    /**
     * 合作伙伴Id
     */
    private String cooperatorId;

    /**
     * 合作伙伴电话
     */
    private String cooperatorPhone;

    /**
     * 合作伙伴邮箱
     */
    private String cooperatorEmail;

}
