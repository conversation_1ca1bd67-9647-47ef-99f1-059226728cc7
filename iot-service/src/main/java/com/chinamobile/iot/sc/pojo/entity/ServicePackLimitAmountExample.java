package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ServicePackLimitAmountExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public ServicePackLimitAmountExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public ServicePackLimitAmountExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    public ServicePackLimitAmountExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    public static Criteria newAndCreateCriteria() {
        ServicePackLimitAmountExample example = new ServicePackLimitAmountExample();
        return example.createCriteria();
    }

    public ServicePackLimitAmountExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    public ServicePackLimitAmountExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    public ServicePackLimitAmountExample distinct(boolean distinct) {
        this.setDistinct(distinct);
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProductNameIsNull() {
            addCriterion("product_name is null");
            return (Criteria) this;
        }

        public Criteria andProductNameIsNotNull() {
            addCriterion("product_name is not null");
            return (Criteria) this;
        }

        public Criteria andProductNameEqualTo(String value) {
            addCriterion("product_name =", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("product_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductNameNotEqualTo(String value) {
            addCriterion("product_name <>", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("product_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThan(String value) {
            addCriterion("product_name >", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("product_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThanOrEqualTo(String value) {
            addCriterion("product_name >=", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("product_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductNameLessThan(String value) {
            addCriterion("product_name <", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLessThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("product_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductNameLessThanOrEqualTo(String value) {
            addCriterion("product_name <=", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLessThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("product_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductNameLike(String value) {
            addCriterion("product_name like", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotLike(String value) {
            addCriterion("product_name not like", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameIn(List<String> values) {
            addCriterion("product_name in", values, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotIn(List<String> values) {
            addCriterion("product_name not in", values, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameBetween(String value1, String value2) {
            addCriterion("product_name between", value1, value2, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotBetween(String value1, String value2) {
            addCriterion("product_name not between", value1, value2, "productName");
            return (Criteria) this;
        }

        public Criteria andServiceCodeIsNull() {
            addCriterion("service_code is null");
            return (Criteria) this;
        }

        public Criteria andServiceCodeIsNotNull() {
            addCriterion("service_code is not null");
            return (Criteria) this;
        }

        public Criteria andServiceCodeEqualTo(String value) {
            addCriterion("service_code =", value, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("service_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceCodeNotEqualTo(String value) {
            addCriterion("service_code <>", value, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeNotEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("service_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceCodeGreaterThan(String value) {
            addCriterion("service_code >", value, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeGreaterThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("service_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("service_code >=", value, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeGreaterThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("service_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceCodeLessThan(String value) {
            addCriterion("service_code <", value, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeLessThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("service_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceCodeLessThanOrEqualTo(String value) {
            addCriterion("service_code <=", value, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeLessThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("service_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceCodeLike(String value) {
            addCriterion("service_code like", value, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeNotLike(String value) {
            addCriterion("service_code not like", value, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeIn(List<String> values) {
            addCriterion("service_code in", values, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeNotIn(List<String> values) {
            addCriterion("service_code not in", values, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeBetween(String value1, String value2) {
            addCriterion("service_code between", value1, value2, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeNotBetween(String value1, String value2) {
            addCriterion("service_code not between", value1, value2, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceNameIsNull() {
            addCriterion("service_name is null");
            return (Criteria) this;
        }

        public Criteria andServiceNameIsNotNull() {
            addCriterion("service_name is not null");
            return (Criteria) this;
        }

        public Criteria andServiceNameEqualTo(String value) {
            addCriterion("service_name =", value, "serviceName");
            return (Criteria) this;
        }

        public Criteria andServiceNameEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("service_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceNameNotEqualTo(String value) {
            addCriterion("service_name <>", value, "serviceName");
            return (Criteria) this;
        }

        public Criteria andServiceNameNotEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("service_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceNameGreaterThan(String value) {
            addCriterion("service_name >", value, "serviceName");
            return (Criteria) this;
        }

        public Criteria andServiceNameGreaterThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("service_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceNameGreaterThanOrEqualTo(String value) {
            addCriterion("service_name >=", value, "serviceName");
            return (Criteria) this;
        }

        public Criteria andServiceNameGreaterThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("service_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceNameLessThan(String value) {
            addCriterion("service_name <", value, "serviceName");
            return (Criteria) this;
        }

        public Criteria andServiceNameLessThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("service_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceNameLessThanOrEqualTo(String value) {
            addCriterion("service_name <=", value, "serviceName");
            return (Criteria) this;
        }

        public Criteria andServiceNameLessThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("service_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceNameLike(String value) {
            addCriterion("service_name like", value, "serviceName");
            return (Criteria) this;
        }

        public Criteria andServiceNameNotLike(String value) {
            addCriterion("service_name not like", value, "serviceName");
            return (Criteria) this;
        }

        public Criteria andServiceNameIn(List<String> values) {
            addCriterion("service_name in", values, "serviceName");
            return (Criteria) this;
        }

        public Criteria andServiceNameNotIn(List<String> values) {
            addCriterion("service_name not in", values, "serviceName");
            return (Criteria) this;
        }

        public Criteria andServiceNameBetween(String value1, String value2) {
            addCriterion("service_name between", value1, value2, "serviceName");
            return (Criteria) this;
        }

        public Criteria andServiceNameNotBetween(String value1, String value2) {
            addCriterion("service_name not between", value1, value2, "serviceName");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(String value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("company_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(String value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("company_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(String value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("company_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(String value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("company_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(String value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("company_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(String value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("company_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCompanyIdLike(String value) {
            addCriterion("company_id like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotLike(String value) {
            addCriterion("company_id not like", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<String> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<String> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(String value1, String value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(String value1, String value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andIotLimitIsNull() {
            addCriterion("iot_limit is null");
            return (Criteria) this;
        }

        public Criteria andIotLimitIsNotNull() {
            addCriterion("iot_limit is not null");
            return (Criteria) this;
        }

        public Criteria andIotLimitEqualTo(Double value) {
            addCriterion("iot_limit =", value, "iotLimit");
            return (Criteria) this;
        }

        public Criteria andIotLimitEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("iot_limit = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotLimitNotEqualTo(Double value) {
            addCriterion("iot_limit <>", value, "iotLimit");
            return (Criteria) this;
        }

        public Criteria andIotLimitNotEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("iot_limit <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotLimitGreaterThan(Double value) {
            addCriterion("iot_limit >", value, "iotLimit");
            return (Criteria) this;
        }

        public Criteria andIotLimitGreaterThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("iot_limit > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotLimitGreaterThanOrEqualTo(Double value) {
            addCriterion("iot_limit >=", value, "iotLimit");
            return (Criteria) this;
        }

        public Criteria andIotLimitGreaterThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("iot_limit >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotLimitLessThan(Double value) {
            addCriterion("iot_limit <", value, "iotLimit");
            return (Criteria) this;
        }

        public Criteria andIotLimitLessThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("iot_limit < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotLimitLessThanOrEqualTo(Double value) {
            addCriterion("iot_limit <=", value, "iotLimit");
            return (Criteria) this;
        }

        public Criteria andIotLimitLessThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("iot_limit <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotLimitIn(List<Double> values) {
            addCriterion("iot_limit in", values, "iotLimit");
            return (Criteria) this;
        }

        public Criteria andIotLimitNotIn(List<Double> values) {
            addCriterion("iot_limit not in", values, "iotLimit");
            return (Criteria) this;
        }

        public Criteria andIotLimitBetween(Double value1, Double value2) {
            addCriterion("iot_limit between", value1, value2, "iotLimit");
            return (Criteria) this;
        }

        public Criteria andIotLimitNotBetween(Double value1, Double value2) {
            addCriterion("iot_limit not between", value1, value2, "iotLimit");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityIsNull() {
            addCriterion("reserve_quatity is null");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityIsNotNull() {
            addCriterion("reserve_quatity is not null");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityEqualTo(Double value) {
            addCriterion("reserve_quatity =", value, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("reserve_quatity = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveQuatityNotEqualTo(Double value) {
            addCriterion("reserve_quatity <>", value, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityNotEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("reserve_quatity <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveQuatityGreaterThan(Double value) {
            addCriterion("reserve_quatity >", value, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityGreaterThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("reserve_quatity > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveQuatityGreaterThanOrEqualTo(Double value) {
            addCriterion("reserve_quatity >=", value, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityGreaterThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("reserve_quatity >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveQuatityLessThan(Double value) {
            addCriterion("reserve_quatity <", value, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityLessThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("reserve_quatity < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveQuatityLessThanOrEqualTo(Double value) {
            addCriterion("reserve_quatity <=", value, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityLessThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("reserve_quatity <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveQuatityIn(List<Double> values) {
            addCriterion("reserve_quatity in", values, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityNotIn(List<Double> values) {
            addCriterion("reserve_quatity not in", values, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityBetween(Double value1, Double value2) {
            addCriterion("reserve_quatity between", value1, value2, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityNotBetween(Double value1, Double value2) {
            addCriterion("reserve_quatity not between", value1, value2, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryIsNull() {
            addCriterion("current_inventory is null");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryIsNotNull() {
            addCriterion("current_inventory is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryEqualTo(Double value) {
            addCriterion("current_inventory =", value, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("current_inventory = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryNotEqualTo(Double value) {
            addCriterion("current_inventory <>", value, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryNotEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("current_inventory <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryGreaterThan(Double value) {
            addCriterion("current_inventory >", value, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryGreaterThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("current_inventory > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryGreaterThanOrEqualTo(Double value) {
            addCriterion("current_inventory >=", value, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryGreaterThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("current_inventory >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryLessThan(Double value) {
            addCriterion("current_inventory <", value, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryLessThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("current_inventory < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryLessThanOrEqualTo(Double value) {
            addCriterion("current_inventory <=", value, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryLessThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("current_inventory <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryIn(List<Double> values) {
            addCriterion("current_inventory in", values, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryNotIn(List<Double> values) {
            addCriterion("current_inventory not in", values, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryBetween(Double value1, Double value2) {
            addCriterion("current_inventory between", value1, value2, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryNotBetween(Double value1, Double value2) {
            addCriterion("current_inventory not between", value1, value2, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andEfftimeIsNull() {
            addCriterion("efftime is null");
            return (Criteria) this;
        }

        public Criteria andEfftimeIsNotNull() {
            addCriterion("efftime is not null");
            return (Criteria) this;
        }

        public Criteria andEfftimeEqualTo(String value) {
            addCriterion("efftime =", value, "efftime");
            return (Criteria) this;
        }

        public Criteria andEfftimeEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("efftime = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEfftimeNotEqualTo(String value) {
            addCriterion("efftime <>", value, "efftime");
            return (Criteria) this;
        }

        public Criteria andEfftimeNotEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("efftime <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEfftimeGreaterThan(String value) {
            addCriterion("efftime >", value, "efftime");
            return (Criteria) this;
        }

        public Criteria andEfftimeGreaterThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("efftime > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEfftimeGreaterThanOrEqualTo(String value) {
            addCriterion("efftime >=", value, "efftime");
            return (Criteria) this;
        }

        public Criteria andEfftimeGreaterThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("efftime >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEfftimeLessThan(String value) {
            addCriterion("efftime <", value, "efftime");
            return (Criteria) this;
        }

        public Criteria andEfftimeLessThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("efftime < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEfftimeLessThanOrEqualTo(String value) {
            addCriterion("efftime <=", value, "efftime");
            return (Criteria) this;
        }

        public Criteria andEfftimeLessThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("efftime <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEfftimeLike(String value) {
            addCriterion("efftime like", value, "efftime");
            return (Criteria) this;
        }

        public Criteria andEfftimeNotLike(String value) {
            addCriterion("efftime not like", value, "efftime");
            return (Criteria) this;
        }

        public Criteria andEfftimeIn(List<String> values) {
            addCriterion("efftime in", values, "efftime");
            return (Criteria) this;
        }

        public Criteria andEfftimeNotIn(List<String> values) {
            addCriterion("efftime not in", values, "efftime");
            return (Criteria) this;
        }

        public Criteria andEfftimeBetween(String value1, String value2) {
            addCriterion("efftime between", value1, value2, "efftime");
            return (Criteria) this;
        }

        public Criteria andEfftimeNotBetween(String value1, String value2) {
            addCriterion("efftime not between", value1, value2, "efftime");
            return (Criteria) this;
        }

        public Criteria andExptimeIsNull() {
            addCriterion("exptime is null");
            return (Criteria) this;
        }

        public Criteria andExptimeIsNotNull() {
            addCriterion("exptime is not null");
            return (Criteria) this;
        }

        public Criteria andExptimeEqualTo(String value) {
            addCriterion("exptime =", value, "exptime");
            return (Criteria) this;
        }

        public Criteria andExptimeEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("exptime = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExptimeNotEqualTo(String value) {
            addCriterion("exptime <>", value, "exptime");
            return (Criteria) this;
        }

        public Criteria andExptimeNotEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("exptime <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExptimeGreaterThan(String value) {
            addCriterion("exptime >", value, "exptime");
            return (Criteria) this;
        }

        public Criteria andExptimeGreaterThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("exptime > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExptimeGreaterThanOrEqualTo(String value) {
            addCriterion("exptime >=", value, "exptime");
            return (Criteria) this;
        }

        public Criteria andExptimeGreaterThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("exptime >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExptimeLessThan(String value) {
            addCriterion("exptime <", value, "exptime");
            return (Criteria) this;
        }

        public Criteria andExptimeLessThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("exptime < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExptimeLessThanOrEqualTo(String value) {
            addCriterion("exptime <=", value, "exptime");
            return (Criteria) this;
        }

        public Criteria andExptimeLessThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("exptime <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExptimeLike(String value) {
            addCriterion("exptime like", value, "exptime");
            return (Criteria) this;
        }

        public Criteria andExptimeNotLike(String value) {
            addCriterion("exptime not like", value, "exptime");
            return (Criteria) this;
        }

        public Criteria andExptimeIn(List<String> values) {
            addCriterion("exptime in", values, "exptime");
            return (Criteria) this;
        }

        public Criteria andExptimeNotIn(List<String> values) {
            addCriterion("exptime not in", values, "exptime");
            return (Criteria) this;
        }

        public Criteria andExptimeBetween(String value1, String value2) {
            addCriterion("exptime between", value1, value2, "exptime");
            return (Criteria) this;
        }

        public Criteria andExptimeNotBetween(String value1, String value2) {
            addCriterion("exptime not between", value1, value2, "exptime");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNull() {
            addCriterion("company_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNotNull() {
            addCriterion("company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameEqualTo(String value) {
            addCriterion("company_name =", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("company_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotEqualTo(String value) {
            addCriterion("company_name <>", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("company_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThan(String value) {
            addCriterion("company_name >", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("company_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("company_name >=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("company_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThan(String value) {
            addCriterion("company_name <", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("company_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("company_name <=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("company_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCompanyNameLike(String value) {
            addCriterion("company_name like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotLike(String value) {
            addCriterion("company_name not like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIn(List<String> values) {
            addCriterion("company_name in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotIn(List<String> values) {
            addCriterion("company_name not in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameBetween(String value1, String value2) {
            addCriterion("company_name between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotBetween(String value1, String value2) {
            addCriterion("company_name not between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andUseInventoryIsNull() {
            addCriterion("use_inventory is null");
            return (Criteria) this;
        }

        public Criteria andUseInventoryIsNotNull() {
            addCriterion("use_inventory is not null");
            return (Criteria) this;
        }

        public Criteria andUseInventoryEqualTo(Double value) {
            addCriterion("use_inventory =", value, "useInventory");
            return (Criteria) this;
        }

        public Criteria andUseInventoryEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("use_inventory = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUseInventoryNotEqualTo(Double value) {
            addCriterion("use_inventory <>", value, "useInventory");
            return (Criteria) this;
        }

        public Criteria andUseInventoryNotEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("use_inventory <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUseInventoryGreaterThan(Double value) {
            addCriterion("use_inventory >", value, "useInventory");
            return (Criteria) this;
        }

        public Criteria andUseInventoryGreaterThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("use_inventory > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUseInventoryGreaterThanOrEqualTo(Double value) {
            addCriterion("use_inventory >=", value, "useInventory");
            return (Criteria) this;
        }

        public Criteria andUseInventoryGreaterThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("use_inventory >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUseInventoryLessThan(Double value) {
            addCriterion("use_inventory <", value, "useInventory");
            return (Criteria) this;
        }

        public Criteria andUseInventoryLessThanColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("use_inventory < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUseInventoryLessThanOrEqualTo(Double value) {
            addCriterion("use_inventory <=", value, "useInventory");
            return (Criteria) this;
        }

        public Criteria andUseInventoryLessThanOrEqualToColumn(ServicePackLimitAmount.Column column) {
            addCriterion(new StringBuilder("use_inventory <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUseInventoryIn(List<Double> values) {
            addCriterion("use_inventory in", values, "useInventory");
            return (Criteria) this;
        }

        public Criteria andUseInventoryNotIn(List<Double> values) {
            addCriterion("use_inventory not in", values, "useInventory");
            return (Criteria) this;
        }

        public Criteria andUseInventoryBetween(Double value1, Double value2) {
            addCriterion("use_inventory between", value1, value2, "useInventory");
            return (Criteria) this;
        }

        public Criteria andUseInventoryNotBetween(Double value1, Double value2) {
            addCriterion("use_inventory not between", value1, value2, "useInventory");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andProductNameLikeInsensitive(String value) {
            addCriterion("upper(product_name) like", value.toUpperCase(), "productName");
            return (Criteria) this;
        }

        public Criteria andServiceCodeLikeInsensitive(String value) {
            addCriterion("upper(service_code) like", value.toUpperCase(), "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceNameLikeInsensitive(String value) {
            addCriterion("upper(service_name) like", value.toUpperCase(), "serviceName");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLikeInsensitive(String value) {
            addCriterion("upper(company_id) like", value.toUpperCase(), "companyId");
            return (Criteria) this;
        }

        public Criteria andEfftimeLikeInsensitive(String value) {
            addCriterion("upper(efftime) like", value.toUpperCase(), "efftime");
            return (Criteria) this;
        }

        public Criteria andExptimeLikeInsensitive(String value) {
            addCriterion("upper(exptime) like", value.toUpperCase(), "exptime");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLikeInsensitive(String value) {
            addCriterion("upper(company_name) like", value.toUpperCase(), "companyName");
            return (Criteria) this;
        }

        public Criteria andStatusLikeInsensitive(String value) {
            addCriterion("upper(status) like", value.toUpperCase(), "status");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        private ServicePackLimitAmountExample example;

        protected Criteria(ServicePackLimitAmountExample example) {
            super();
            this.example = example;
        }

        public ServicePackLimitAmountExample example() {
            return this.example;
        }

        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            Criteria add(Criteria add);
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        void example(com.chinamobile.iot.sc.pojo.entity.ServicePackLimitAmountExample example);
    }
}