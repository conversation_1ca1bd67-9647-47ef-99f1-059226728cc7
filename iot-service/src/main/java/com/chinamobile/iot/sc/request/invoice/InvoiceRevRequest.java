package com.chinamobile.iot.sc.request.invoice;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @package: com.chinamobile.iot.sc.request.invoice
 * @ClassName: InvoiceRevRequest
 * @description: 发票冲红录入信息请求
 * @author: zyj
 * @create: 2021/12/30 15:43
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class InvoiceRevRequest {
    //冲红发票号码
//    @NotBlank(message = "冲红发票号码必填！")
//    private String creditNoteNum;
//    //冲红发票代码
//    @NotBlank(message = "冲红发票代码必填！")
//    private String creditNoteId;
    //发票号码
    @NotBlank(message = "发票号码必填！")
    private String voucherNum;
    //发票代码
    @NotBlank(message = "发票代码必填！")
    private String voucherID;
    //发票金额，单位厘
    @NotBlank(message = "发票金额必填！")
    private Long voucherSum;
    //发票开具时间
    @NotBlank(message = "发票开具时间必填！")
    private String billingDate;
    //原子商品订单信息
    private String atomOrderId;
    //发票备注信息
    private String remark;
    //录入顺序：从0开始
//    private Integer sort;
}
