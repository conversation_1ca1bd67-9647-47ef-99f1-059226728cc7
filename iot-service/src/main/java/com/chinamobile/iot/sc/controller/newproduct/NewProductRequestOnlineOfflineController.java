package com.chinamobile.iot.sc.controller.newproduct;

import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.constant.NewProductOnlineStatusConstant;
import com.chinamobile.iot.sc.constant.NewProductRequestStatusConstant;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.NewProductRequestManage;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.NewProductOnlineOfflineVO;
import com.chinamobile.iot.sc.pojo.vo.OnlineRequestVO;
import com.chinamobile.iot.sc.pojo.vo.ProOnlineDownloadVO;
import com.chinamobile.iot.sc.pojo.vo.ProOnlineUploadVO;
import com.chinamobile.iot.sc.service.NewProductRequestManageService;
import com.chinamobile.iot.sc.service.NewProductRequestOnlineOfflineConfigReplenishService;
import com.chinamobile.iot.sc.service.NewProductRequestOnlineOfflineService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Optional;

import static com.chinamobile.iot.sc.common.BaseConstant.PRODUCT_UP_DOWN_FRAME;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/15
 * @description 新产品引入申请上下架controller类
 */
@RestController
@RequestMapping(value = "/osweb/newProduct")
public class NewProductRequestOnlineOfflineController {

    @Resource
    private NewProductRequestOnlineOfflineService newProductRequestOnlineOfflineService;

    @Resource
    private NewProductRequestManageService newProductRequestManageService;

    @Resource
    private NewProductRequestOnlineOfflineConfigReplenishService newProductRequestOnlineOfflineConfigReplenishService;

    /**
     * 新增新产品上线请求
     *
     * @param onlineRequestParam
     * @return
     */
    @PostMapping(value = "/saveOnline")
    @Auth(authCode = {PRODUCT_UP_DOWN_FRAME})
    public BaseAnswer saveNewProductRequestOnline(@Valid @RequestBody OnlineRequestParam onlineRequestParam,
                                                  @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer baseAnswer = new BaseAnswer();
        String newProductRequestId = onlineRequestParam.getNewProductRequestId();
        NewProductRequestManage manage = newProductRequestManageService.getNewProductRequestManageById(newProductRequestId);
        if (!Optional.ofNullable(manage).isPresent()) {
            baseAnswer.setStatus(BaseErrorConstant.NWE_PRODUCT_REQUEST_NOT_EXISTS);
            return baseAnswer;
        }

        Integer requestStatus = manage.getRequestStatus();
        if (NewProductRequestStatusConstant.PASS != (int) requestStatus) {
            baseAnswer.setStatus(BaseErrorConstant.NWE_PRODUCT_REQUEST_NOT_PASS);
            return baseAnswer;
        }

        String onlineStatus = manage.getOnlineStatus();
        if (NewProductOnlineStatusConstant.ONLINE.equals(onlineStatus)
                || NewProductOnlineStatusConstant.ONLINING.equals(onlineStatus)) {
            baseAnswer.setStatus(BaseErrorConstant.NWE_PRODUCT_ONLINING);
            return baseAnswer;
        }

        newProductRequestOnlineOfflineService.saveOnlineRequestInfo(onlineRequestParam, loginIfo4Redis);
        return baseAnswer;
    }

    /**
     * 新增下架请求
     *
     * @param offlineRequestParam
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping(value = "/saveOffline")
    @Auth(authCode = {PRODUCT_UP_DOWN_FRAME})
    public BaseAnswer saveOffline(@RequestBody @Valid NewProductOfflineRequestParam offlineRequestParam,
                                  @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        newProductRequestOnlineOfflineService.saveOfflineRequestInfo(offlineRequestParam, loginIfo4Redis);
        return new BaseAnswer();
    }

    /**
     * 复制重新上架请求
     *
     * @param copyOnlineParam
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping(value = "/copyReOnline")
    @Auth(authCode = {PRODUCT_UP_DOWN_FRAME})
    public BaseAnswer copyNotPassOnlineToReOnline(@RequestBody @Valid NewProductCopyOnlineParam copyOnlineParam,
                                                  @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        newProductRequestOnlineOfflineService.copyNotPassOnlineToReOnline(copyOnlineParam, loginIfo4Redis);
        return new BaseAnswer();
    }

    /**
     * 合作伙伴上下架分页查询
     *
     * @param onlineOfflineParam
     * @return
     */
    @GetMapping(value = "/pagePrimaryOnlineOffline")
    @Auth(authCode = {PRODUCT_UP_DOWN_FRAME})
    public BaseAnswer<PageData<NewProductOnlineOfflineVO>> pageNewProductOnlineOffline(NewProductOnlineOfflineParam onlineOfflineParam,
                                                                                       @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        PageData<NewProductOnlineOfflineVO> pageData = newProductRequestManageService.pageNewProductOnlineOffline(onlineOfflineParam, loginIfo4Redis);
        return new BaseAnswer<PageData<NewProductOnlineOfflineVO>>().setData(pageData);
    }

    /**
     * 非合作伙伴上下架分页查询
     *
     * @param onlineOfflineParam
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping(value = "/pageNotPrimaryOnlineOffline")
    @Auth(authCode = {PRODUCT_UP_DOWN_FRAME})
    public BaseAnswer<PageData<NewProductOnlineOfflineVO>> pageNotPrimaryNewProductOnlineOffline(NewProductOnlineOfflineParam onlineOfflineParam,
                                                                                                 @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {

        PageData<NewProductOnlineOfflineVO> pageData = newProductRequestOnlineOfflineService.pageNewProductOnlineOffline(onlineOfflineParam, loginIfo4Redis);
        return new BaseAnswer<PageData<NewProductOnlineOfflineVO>>().setData(pageData);
    }

    /**
     * 获取上架信息详情
     *
     * @param detailParam
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping(value = "/getOnlineDetail")
    @Auth(authCode = {PRODUCT_UP_DOWN_FRAME})
    public BaseAnswer<OnlineRequestVO> getOnlineRequestDetail(@Valid NewProductOnlineOfflineDetailParam detailParam,
                                                              @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        OnlineRequestVO onlineRequestDetail = newProductRequestOnlineOfflineService.getOnlineRequestDetail(detailParam, loginIfo4Redis);
        return new BaseAnswer<OnlineRequestVO>().setData(onlineRequestDetail);
    }

    /**
     * 流程审批
     *
     * @param onlineOfflineParam
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping(value = "/judgeOnlineOfflineFlow")
    @Auth(authCode = {PRODUCT_UP_DOWN_FRAME})
    public BaseAnswer judgeOnlineOfflineFlow(@RequestBody @Valid NewProductJudgeOnlineOfflineParam onlineOfflineParam,
                                             @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        newProductRequestOnlineOfflineService.judgeOnlineOfflineFlow(onlineOfflineParam, loginIfo4Redis);
        return new BaseAnswer();
    }

    /**
     * 导出商品上架信息
     */
    @GetMapping("/exportOnlineOfflineInfo")
    @Auth(authCode = {PRODUCT_UP_DOWN_FRAME})
    public void exportOnlineOfflineInfo(@Valid NewProductOnlineOfflineDetailParam detailParam,
                                              @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                                              HttpServletResponse response) {
        newProductRequestOnlineOfflineService.exportNewProductOnlineOfflineInfo(detailParam, loginIfo4Redis, response);
    }

    /**
     * 下载单个文件
     */
    @PostMapping("/download/singleFile")
    public BaseAnswer<Void> downLoadSingleFile(){
        return new BaseAnswer<>();
    }

    /**
     * 下载全部文件并打包成zip
     * @return
     */
    @PostMapping("/download/filePack")
    public BaseAnswer<Void> downLoadFilePack(){
        return new BaseAnswer<>();
    }


    @PostMapping("/download/file")
    public BaseAnswer<ProOnlineDownloadVO> downLoadFile(@RequestBody NewProductOnlineOfflineDownloadParam param){
        return newProductRequestOnlineOfflineService.downLoadFile(param);
    }

    /**
     * 删除上传文件
     * @return
     */
    @DeleteMapping("/delete/file")
    public BaseAnswer<Void> deleteFile(@RequestBody NewProductOnlineOfflineDeleteParam param){
        return newProductRequestOnlineOfflineService.deleteFileByKey(param);
    }

    @PostMapping("/upload/file")
    public BaseAnswer<ProOnlineUploadVO> uploadFile(@RequestPart("file") MultipartFile upfile, @RequestParam("fileType") String fileType){
        return newProductRequestOnlineOfflineService.uploadFile(upfile, fileType);
    }


    @PostMapping("/upload/backend/file")
    public BaseAnswer<String> uploadBackendFile(@RequestPart("file") MultipartFile upfile, @RequestParam("dir") String dir){
        return newProductRequestOnlineOfflineService.uploadBackendFile(upfile, dir);
    }


    @DeleteMapping("/delete/dir")
    public BaseAnswer<Void> deleteDir(@RequestParam("dir") String dir){
        return newProductRequestOnlineOfflineService.deleteDir(dir);
    }

    @DeleteMapping("/test/delete/file")
    public BaseAnswer<Void>  deleteFolder(@RequestParam("path") String path){
        return newProductRequestOnlineOfflineService.deleteDir(path);
    }


}
