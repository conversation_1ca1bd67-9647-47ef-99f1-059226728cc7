package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.service.DataCutService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR> xie<PERSON>oh<PERSON>
 * @date : 2024/1/10 15:39
 * @description: 数据切割控制类
 **/
@RestController
@RequestMapping(value = "/osweb/cut")
public class DataCutController {

    @Resource
    private DataCutService dataCutService;

    /**
     * 快线商品目录价格调整数据割接
     * @param file
     */
    @PostMapping("/price")
    public void batchPriceCutImport(@RequestParam("file") MultipartFile file){
        dataCutService.batchPriceCut(file);
    }

}
