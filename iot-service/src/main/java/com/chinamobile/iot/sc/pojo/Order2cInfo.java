package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 2C类商品订单信息
 *
 * <AUTHOR>
public class Order2cInfo implements Serializable {
    /**
     * 业务订单流水号
     *
     * Corresponding to the database column supply_chain..order_2c_info.order_id
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String orderId;

    /**
     * 订单类型00：代客下单（商城直销订单） 01：自主下单 02：代客下单（省内融合集团客户订单） 03:代客下单（省内融合个人客户订单）
     *
     * Corresponding to the database column supply_chain..order_2c_info.order_type
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String orderType;

    /**
     * 业务编码
     *
     * Corresponding to the database column supply_chain..order_2c_info.business_code
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String businessCode;

    /**
     * 操作员编码
取个人客户所属的客户经理的操作员编码;
分享订购场景，取分享链接中的客户经理编码。（本场景暂不支持）
自主注册个人客户，本字段为空。
     *
     * Corresponding to the database column supply_chain..order_2c_info.create_oper_code
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String createOperCode;

    /**
     * 与操作员编码（ createOperCode）搭配使用，传操作员的UserID信息；
     *
     * Corresponding to the database column supply_chain..order_2c_info.create_oper_user_id
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String createOperUserId;

    /**
     * 操作员省工号 与操作员编码（ createOperCode）搭配使用，对应操作员的省员工工号；
     *
     * Corresponding to the database column supply_chain..order_2c_info.employee_num
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String employeeNum;

    /**
     * 操作员姓名
     *
     * Corresponding to the database column supply_chain..order_2c_info.cust_mg_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String custMgName;

    /**
     * 操作员电话,解密
     *
     * Corresponding to the database column supply_chain..order_2c_info.cust_mg_phone
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String custMgPhone;

    /**
     * 订单状态变更时间
     *
     * Corresponding to the database column supply_chain..order_2c_info.order_status_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private Date orderStatusTime;

    /**
     * 客户编码
     *
     * Corresponding to the database column supply_chain..order_2c_info.cust_code
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String custCode;

    /**
     * 与客户编码（custCode）搭配使用，传在商城注册的个人客户、集团客户的UserID信息，省侧集团客户不传，代客下单场景下不传
     *
     * Corresponding to the database column supply_chain..order_2c_info.cust_user_id
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String custUserId;

    /**
     * 客户名称
     *
     * Corresponding to the database column supply_chain..order_2c_info.cust_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String custName;

    /**
     * 个人客户所属省份。
     *
     * Corresponding to the database column supply_chain..order_2c_info.be_id
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String beId;

    /**
     * 个人客户所属归属地市编码
     *
     * Corresponding to the database column supply_chain..order_2c_info.location
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String location;

    /**
     * 个人客户所属归属区县
     *
     * Corresponding to the database column supply_chain..order_2c_info.region_ID
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String regionId;

    /**
     * 订单收入归属省公司组织机构标识,省公司的组织机构标识，如果客户没有组织机构，则设置默认值：0。若组织机构已补充省公司组织机构标识则该字段为省公司组织机构标识；若组织机构未补充省公司组织机构标识则该字段为空。
     * 如果本字段不为空，则表示该笔收入所属的省公司组织机构。
     *
     * Corresponding to the database column supply_chain..order_2c_info.order_org_biz_code
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String orderOrgBizCode;

    /**
     * 组织级别	对应订单收入归属省公司组织机构级别
     * 1：集团
     * 2：省
     * 3：地市
     * 4：区县
     * 5：营业厅
     *
     * Corresponding to the database column supply_chain..order_2c_info.org_level
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String orgLevel;

    /**
     * 全组织机构名称	全组织机构名称为拼接字段，按订单收入归属省公司组织机构的父组织机构拼接，最多取订单收入归属组织机构及以上共5层父组织机构，各级别间以“-”连接；
     *
     * Corresponding to the database column supply_chain..order_2c_info.org_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String orgName;

    /**
     * 当ordertype=00且offeringClass=A11:卡+X硬件时，取省BOSS回传的省内集团客户上的“归属省内组织机构全称”
     *
     * Corresponding to the database column supply_chain..order_2c_info.province_org_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String provinceOrgName;

    /**
     * 备注
个人客户非标准化的要求在备注中说明。
     *
     * Corresponding to the database column supply_chain..order_2c_info.remarks
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String remarks;

    /**
     * 预占流水号
物联网服务类业务以及软件功能费+
（代销类）硬件业务，本字段必传;
     *
     * Corresponding to the database column supply_chain..order_2c_info.bookId
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String bookid;

    /**
     * 订单状态
0:订单创建
1:订单验收（个人客户确认收货时，同步本状态）
3:订单计收;（订单同步至CMIoT成功后，同步本状态）4.订单退款完成
     *
     * Corresponding to the database column supply_chain..order_2c_info.status
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private Integer status;

    /**
     * 订单总金额
单位：厘;
订单创建场景必填，其他同步场景非必填。
     *
     * Corresponding to the database column supply_chain..order_2c_info.total_price
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String totalPrice;

    /**
     * 订单创建时间	
格式：yyyyMMddHHmmss
订单创建场景必填，其他同步场景非必填。
     *
     * Corresponding to the database column supply_chain..order_2c_info.create_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String createTime;

    /**
     * 收货人姓名
     *
     * Corresponding to the database column supply_chain..order_2c_info.contact_person_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String contactPersonName;

    /**
     * 收货人手机号
     *
     * Corresponding to the database column supply_chain..order_2c_info.contact_phone
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String contactPhone;

    /**
     * 省份;
     *
     * Corresponding to the database column supply_chain..order_2c_info.addr1
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String addr1;

    /**
     * 地市
     *
     * Corresponding to the database column supply_chain..order_2c_info.addr2
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String addr2;

    /**
     * 区县
     *
     * Corresponding to the database column supply_chain..order_2c_info.addr3
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String addr3;

    /**
     * 乡镇
     *
     * Corresponding to the database column supply_chain..order_2c_info.addr4
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String addr4;

    /**
     * 非结构地址
     *
     * Corresponding to the database column supply_chain..order_2c_info.usaddr
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String usaddr;

    /**
     * 河南订单实名修改收货人姓名
     *
     * Corresponding to the database column supply_chain..order_2c_info.henan_real_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String henanRealName;

    /**
     * 河南实名订单收货人电话
     *
     * Corresponding to the database column supply_chain..order_2c_info.henan_real_phone
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String henanRealPhone;

    /**
     * 河南实名修改订单收货人详情地址
     *
     * Corresponding to the database column supply_chain..order_2c_info.henan_real_address
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String henanRealAddress;

    /**
     * 一级管理目录
A01-基础产品
A02-行业应用
A03-硬件终端
A06-软件功能费+（代销类）硬件
     *
     * Corresponding to the database column supply_chain..order_2c_info.spu_offering_class
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String spuOfferingClass;

    /**
     * 商品组编码/销售商品编码
     *
     * Corresponding to the database column supply_chain..order_2c_info.spu_offering_code
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String spuOfferingCode;

    /**
     * 服务商唯一标识
     *
     * Corresponding to the database column supply_chain..order_2c_info.supplier_code
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String supplierCode;

    /**
     * 更新时间
     *
     * Corresponding to the database column supply_chain..order_2c_info.update_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private Date updateTime;

    /**
     * 订单状态
0 待发货
1 待收货
2 已完成
暂时不用，订单中只看原子订单商品状态
     *
     * Corresponding to the database column supply_chain..order_2c_info.order_status
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private Integer orderStatus;

    /**
     * 订单抵扣金额，单位：厘
     *
     * Corresponding to the database column supply_chain..order_2c_info.deduct_price
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String deductPrice;

    /**
     * 订购渠道来源,商城自有渠道时，默认传019030-移动物联网商城/移动视联网商城
     *
     * Corresponding to the database column supply_chain..order_2c_info.ordering_channel_source
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String orderingChannelSource;

    /**
     * 订购渠道名称,商城自有渠道时，依据前台记录的域名信息传019030对应的“移动物联网商城”或“移动视联网商城”
     *
     * Corresponding to the database column supply_chain..order_2c_info.ordering_channel_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String orderingChannelName;

    /**
     * 4A单点登录访问类型
     *
     * Corresponding to the database column supply_chain..order_2c_info.sso_terminal_type
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String ssoTerminalType;

    /**
     * 是否同步到k3   0--未同步 1--同步
     *
     * Corresponding to the database column supply_chain..order_2c_info.to_k3
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private Integer toK3;

    /**
     * 是否经过特殊的售后处理标识  0--未经过  1--经过
     *
     * Corresponding to the database column supply_chain..order_2c_info.special_after_market_handle
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private Integer specialAfterMarketHandle;

    /**
     * 特殊的售后状态：1-待退款 2-退款中 3-退款成功 4-退款取消 5-部分退款取消 6-部分退款成功
     *
     * Corresponding to the database column supply_chain..order_2c_info.special_after_status
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String specialAfterStatus;

    /**
     * 特殊的售后状态变更时间
     *
     * Corresponding to the database column supply_chain..order_2c_info.special_after_status_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String specialAfterStatusTime;

    /**
     * 特殊的售后截止时间
     *
     * Corresponding to the database column supply_chain..order_2c_info.special_after_latest_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String specialAfterLatestTime;

    /**
     * 枚举值：
0：立即生效
1：定时生效
当offeringClass=A13：软件服务且订单状态status=0（订单创建）时必传；

     *
     * Corresponding to the database column supply_chain..order_2c_info.effective_rules
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String effectiveRules;

    /**
     * 当effectiveRules=0：立即生效时传空值；
当effectiveRules=1：定时生效时传用户选择的生效日期

     *
     * Corresponding to the database column supply_chain..order_2c_info.effective_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String effectiveTime;

    /**
     * K3同步数据的省销售数据编码号
     *
     * Corresponding to the database column supply_chain..order_2c_info.sync_k3_id
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String syncK3Id;

    /**
     * 支付时间
     *
     * Corresponding to the database column supply_chain..order_2c_info.pay_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private Date payTime;

    /**
     * 退款时间
     *
     * Corresponding to the database column supply_chain..order_2c_info.refund_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private Date refundTime;

    /**
     * 代客下单订单的待出账时间
     *
     * Corresponding to the database column supply_chain..order_2c_info.valet_order_complete_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String valetOrderCompleteTime;

    /**
     * 提单人类型,代客下单订单必传；
     * 1：客户经理代客下单
     * 2：营业员代客下单
     *
     * Corresponding to the database column supply_chain..order_2c_info.bill_ladder_type
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String billLadderType;

    /**
     * 千里眼平台开通状态，0-未校验，1-校验成功，2-开通成功，3-开通失败，4-部分开通成功，5-退订成功
     *
     * Corresponding to the database column supply_chain..order_2c_info.qly_status
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private Integer qlyStatus;

    /**
     * 云视讯平台服务开通状态，1：已校验，2：开通成功，3：开通失败，4：退订成功，5：退订失败
     *
     * Corresponding to the database column supply_chain..order_2c_info.ysx_status
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private Integer ysxStatus;

    /**
     * 卡+X订单退款状态 0--申请退款 1--同意退款  2--不同意退款 3--取消退款
     *
     * Corresponding to the database column supply_chain..order_2c_info.kx_refund_status
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private Integer kxRefundStatus;

    /**
     * 卡+X订单退款是否同意的原因
     *
     * Corresponding to the database column supply_chain..order_2c_info.kx_refund_reason
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String kxRefundReason;

    /**
     *
     * Corresponding to the database column supply_chain..order_2c_info.spu_offering_version
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String spuOfferingVersion;

    /**
     * （仅卡+x可能有值）预占时的提单人归属省份id
     *
     * Corresponding to the database column supply_chain..order_2c_info.reserve_be_id
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String reserveBeId;

    /**
     * （仅卡+x可能有值）预占时的提单人归属地市id
     *
     * Corresponding to the database column supply_chain..order_2c_info.reserve_location
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String reserveLocation;

    /**
     * 软件服务订单类型0：开通订单 1：续费订单
当offeringClass= A13：软件服务时必传；首次订购的订单（含一次性包）传0：开通订单；后续续费订单传1：续费订单
     *
     * Corresponding to the database column supply_chain..order_2c_info.software_order_type
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String softwareOrderType;

    /**
     * 关联主订单 当softwareOrderType=1：续费订单时该字段必传，传续费订单所关联的开通订单的订单号
     *
     * Corresponding to the database column supply_chain..order_2c_info.associated_order
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String associatedOrder;

    /**
     * 退款数量
     *
     * Corresponding to the database column supply_chain..order_2c_info.special_after_refunds_number
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String specialAfterRefundsNumber;

    /**
     * 上架平台 0：物联网商城 1：视联网商城
     *
     * Corresponding to the database column supply_chain..order_2c_info.spu_list_platform
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String spuListPlatform;

    /**
     * 同步给市场销售系统成功后返回的应收单号
     *
     * Corresponding to the database column supply_chain..order_2c_info.bill_no_number
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String billNoNumber;

    /**
     * 待发货催单次数
     *
     * Corresponding to the database column supply_chain..order_2c_info.reminder_wait_send
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private Integer reminderWaitSend;

    /**
     * 待接单催单次数
     *
     * Corresponding to the database column supply_chain..order_2c_info.reminder_valet_taking
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private Integer reminderValetTaking;

    /**
     * 待交付催单次数
     *
     * Corresponding to the database column supply_chain..order_2c_info.reminder_wait_deliver
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private Integer reminderWaitDeliver;

    /**
     * 客户类型,0：个人客户 1：集团客户
     *
     * Corresponding to the database column supply_chain..order_2c_info.customer_type
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private String customerType;

    /**
     * Corresponding to the database table supply_chain..order_2c_info
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.order_id
     *
     * @return the value of supply_chain..order_2c_info.order_id
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.order_id
     *
     * @param orderId the value for supply_chain..order_2c_info.order_id
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.order_type
     *
     * @return the value of supply_chain..order_2c_info.order_type
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getOrderType() {
        return orderType;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withOrderType(String orderType) {
        this.setOrderType(orderType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.order_type
     *
     * @param orderType the value for supply_chain..order_2c_info.order_type
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setOrderType(String orderType) {
        this.orderType = orderType == null ? null : orderType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.business_code
     *
     * @return the value of supply_chain..order_2c_info.business_code
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getBusinessCode() {
        return businessCode;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withBusinessCode(String businessCode) {
        this.setBusinessCode(businessCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.business_code
     *
     * @param businessCode the value for supply_chain..order_2c_info.business_code
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode == null ? null : businessCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.create_oper_code
     *
     * @return the value of supply_chain..order_2c_info.create_oper_code
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getCreateOperCode() {
        return createOperCode;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withCreateOperCode(String createOperCode) {
        this.setCreateOperCode(createOperCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.create_oper_code
     *
     * @param createOperCode the value for supply_chain..order_2c_info.create_oper_code
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setCreateOperCode(String createOperCode) {
        this.createOperCode = createOperCode == null ? null : createOperCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.create_oper_user_id
     *
     * @return the value of supply_chain..order_2c_info.create_oper_user_id
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getCreateOperUserId() {
        return createOperUserId;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withCreateOperUserId(String createOperUserId) {
        this.setCreateOperUserId(createOperUserId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.create_oper_user_id
     *
     * @param createOperUserId the value for supply_chain..order_2c_info.create_oper_user_id
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setCreateOperUserId(String createOperUserId) {
        this.createOperUserId = createOperUserId == null ? null : createOperUserId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.employee_num
     *
     * @return the value of supply_chain..order_2c_info.employee_num
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getEmployeeNum() {
        return employeeNum;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withEmployeeNum(String employeeNum) {
        this.setEmployeeNum(employeeNum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.employee_num
     *
     * @param employeeNum the value for supply_chain..order_2c_info.employee_num
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setEmployeeNum(String employeeNum) {
        this.employeeNum = employeeNum == null ? null : employeeNum.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.cust_mg_name
     *
     * @return the value of supply_chain..order_2c_info.cust_mg_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getCustMgName() {
        return custMgName;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withCustMgName(String custMgName) {
        this.setCustMgName(custMgName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.cust_mg_name
     *
     * @param custMgName the value for supply_chain..order_2c_info.cust_mg_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setCustMgName(String custMgName) {
        this.custMgName = custMgName == null ? null : custMgName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.cust_mg_phone
     *
     * @return the value of supply_chain..order_2c_info.cust_mg_phone
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getCustMgPhone() {
        return custMgPhone;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withCustMgPhone(String custMgPhone) {
        this.setCustMgPhone(custMgPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.cust_mg_phone
     *
     * @param custMgPhone the value for supply_chain..order_2c_info.cust_mg_phone
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setCustMgPhone(String custMgPhone) {
        this.custMgPhone = custMgPhone == null ? null : custMgPhone.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.order_status_time
     *
     * @return the value of supply_chain..order_2c_info.order_status_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Date getOrderStatusTime() {
        return orderStatusTime;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withOrderStatusTime(Date orderStatusTime) {
        this.setOrderStatusTime(orderStatusTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.order_status_time
     *
     * @param orderStatusTime the value for supply_chain..order_2c_info.order_status_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setOrderStatusTime(Date orderStatusTime) {
        this.orderStatusTime = orderStatusTime;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.cust_code
     *
     * @return the value of supply_chain..order_2c_info.cust_code
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getCustCode() {
        return custCode;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withCustCode(String custCode) {
        this.setCustCode(custCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.cust_code
     *
     * @param custCode the value for supply_chain..order_2c_info.cust_code
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setCustCode(String custCode) {
        this.custCode = custCode == null ? null : custCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.cust_user_id
     *
     * @return the value of supply_chain..order_2c_info.cust_user_id
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getCustUserId() {
        return custUserId;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withCustUserId(String custUserId) {
        this.setCustUserId(custUserId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.cust_user_id
     *
     * @param custUserId the value for supply_chain..order_2c_info.cust_user_id
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setCustUserId(String custUserId) {
        this.custUserId = custUserId == null ? null : custUserId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.cust_name
     *
     * @return the value of supply_chain..order_2c_info.cust_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getCustName() {
        return custName;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withCustName(String custName) {
        this.setCustName(custName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.cust_name
     *
     * @param custName the value for supply_chain..order_2c_info.cust_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setCustName(String custName) {
        this.custName = custName == null ? null : custName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.be_id
     *
     * @return the value of supply_chain..order_2c_info.be_id
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getBeId() {
        return beId;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withBeId(String beId) {
        this.setBeId(beId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.be_id
     *
     * @param beId the value for supply_chain..order_2c_info.be_id
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setBeId(String beId) {
        this.beId = beId == null ? null : beId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.location
     *
     * @return the value of supply_chain..order_2c_info.location
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getLocation() {
        return location;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withLocation(String location) {
        this.setLocation(location);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.location
     *
     * @param location the value for supply_chain..order_2c_info.location
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setLocation(String location) {
        this.location = location == null ? null : location.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.region_ID
     *
     * @return the value of supply_chain..order_2c_info.region_ID
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getRegionId() {
        return regionId;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withRegionId(String regionId) {
        this.setRegionId(regionId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.region_ID
     *
     * @param regionId the value for supply_chain..order_2c_info.region_ID
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setRegionId(String regionId) {
        this.regionId = regionId == null ? null : regionId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.order_org_biz_code
     *
     * @return the value of supply_chain..order_2c_info.order_org_biz_code
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getOrderOrgBizCode() {
        return orderOrgBizCode;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withOrderOrgBizCode(String orderOrgBizCode) {
        this.setOrderOrgBizCode(orderOrgBizCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.order_org_biz_code
     *
     * @param orderOrgBizCode the value for supply_chain..order_2c_info.order_org_biz_code
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setOrderOrgBizCode(String orderOrgBizCode) {
        this.orderOrgBizCode = orderOrgBizCode == null ? null : orderOrgBizCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.org_level
     *
     * @return the value of supply_chain..order_2c_info.org_level
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getOrgLevel() {
        return orgLevel;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withOrgLevel(String orgLevel) {
        this.setOrgLevel(orgLevel);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.org_level
     *
     * @param orgLevel the value for supply_chain..order_2c_info.org_level
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setOrgLevel(String orgLevel) {
        this.orgLevel = orgLevel == null ? null : orgLevel.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.org_name
     *
     * @return the value of supply_chain..order_2c_info.org_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getOrgName() {
        return orgName;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withOrgName(String orgName) {
        this.setOrgName(orgName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.org_name
     *
     * @param orgName the value for supply_chain..order_2c_info.org_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.province_org_name
     *
     * @return the value of supply_chain..order_2c_info.province_org_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getProvinceOrgName() {
        return provinceOrgName;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withProvinceOrgName(String provinceOrgName) {
        this.setProvinceOrgName(provinceOrgName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.province_org_name
     *
     * @param provinceOrgName the value for supply_chain..order_2c_info.province_org_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setProvinceOrgName(String provinceOrgName) {
        this.provinceOrgName = provinceOrgName == null ? null : provinceOrgName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.remarks
     *
     * @return the value of supply_chain..order_2c_info.remarks
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getRemarks() {
        return remarks;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withRemarks(String remarks) {
        this.setRemarks(remarks);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.remarks
     *
     * @param remarks the value for supply_chain..order_2c_info.remarks
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setRemarks(String remarks) {
        this.remarks = remarks == null ? null : remarks.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.bookId
     *
     * @return the value of supply_chain..order_2c_info.bookId
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getBookid() {
        return bookid;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withBookid(String bookid) {
        this.setBookid(bookid);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.bookId
     *
     * @param bookid the value for supply_chain..order_2c_info.bookId
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setBookid(String bookid) {
        this.bookid = bookid == null ? null : bookid.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.status
     *
     * @return the value of supply_chain..order_2c_info.status
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Integer getStatus() {
        return status;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withStatus(Integer status) {
        this.setStatus(status);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.status
     *
     * @param status the value for supply_chain..order_2c_info.status
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.total_price
     *
     * @return the value of supply_chain..order_2c_info.total_price
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getTotalPrice() {
        return totalPrice;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withTotalPrice(String totalPrice) {
        this.setTotalPrice(totalPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.total_price
     *
     * @param totalPrice the value for supply_chain..order_2c_info.total_price
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setTotalPrice(String totalPrice) {
        this.totalPrice = totalPrice == null ? null : totalPrice.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.create_time
     *
     * @return the value of supply_chain..order_2c_info.create_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withCreateTime(String createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.create_time
     *
     * @param createTime the value for supply_chain..order_2c_info.create_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setCreateTime(String createTime) {
        this.createTime = createTime == null ? null : createTime.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.contact_person_name
     *
     * @return the value of supply_chain..order_2c_info.contact_person_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getContactPersonName() {
        return contactPersonName;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withContactPersonName(String contactPersonName) {
        this.setContactPersonName(contactPersonName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.contact_person_name
     *
     * @param contactPersonName the value for supply_chain..order_2c_info.contact_person_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setContactPersonName(String contactPersonName) {
        this.contactPersonName = contactPersonName == null ? null : contactPersonName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.contact_phone
     *
     * @return the value of supply_chain..order_2c_info.contact_phone
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getContactPhone() {
        return contactPhone;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withContactPhone(String contactPhone) {
        this.setContactPhone(contactPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.contact_phone
     *
     * @param contactPhone the value for supply_chain..order_2c_info.contact_phone
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone == null ? null : contactPhone.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.addr1
     *
     * @return the value of supply_chain..order_2c_info.addr1
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getAddr1() {
        return addr1;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withAddr1(String addr1) {
        this.setAddr1(addr1);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.addr1
     *
     * @param addr1 the value for supply_chain..order_2c_info.addr1
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setAddr1(String addr1) {
        this.addr1 = addr1 == null ? null : addr1.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.addr2
     *
     * @return the value of supply_chain..order_2c_info.addr2
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getAddr2() {
        return addr2;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withAddr2(String addr2) {
        this.setAddr2(addr2);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.addr2
     *
     * @param addr2 the value for supply_chain..order_2c_info.addr2
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setAddr2(String addr2) {
        this.addr2 = addr2 == null ? null : addr2.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.addr3
     *
     * @return the value of supply_chain..order_2c_info.addr3
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getAddr3() {
        return addr3;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withAddr3(String addr3) {
        this.setAddr3(addr3);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.addr3
     *
     * @param addr3 the value for supply_chain..order_2c_info.addr3
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setAddr3(String addr3) {
        this.addr3 = addr3 == null ? null : addr3.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.addr4
     *
     * @return the value of supply_chain..order_2c_info.addr4
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getAddr4() {
        return addr4;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withAddr4(String addr4) {
        this.setAddr4(addr4);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.addr4
     *
     * @param addr4 the value for supply_chain..order_2c_info.addr4
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setAddr4(String addr4) {
        this.addr4 = addr4 == null ? null : addr4.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.usaddr
     *
     * @return the value of supply_chain..order_2c_info.usaddr
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getUsaddr() {
        return usaddr;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withUsaddr(String usaddr) {
        this.setUsaddr(usaddr);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.usaddr
     *
     * @param usaddr the value for supply_chain..order_2c_info.usaddr
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setUsaddr(String usaddr) {
        this.usaddr = usaddr == null ? null : usaddr.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.henan_real_name
     *
     * @return the value of supply_chain..order_2c_info.henan_real_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getHenanRealName() {
        return henanRealName;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withHenanRealName(String henanRealName) {
        this.setHenanRealName(henanRealName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.henan_real_name
     *
     * @param henanRealName the value for supply_chain..order_2c_info.henan_real_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setHenanRealName(String henanRealName) {
        this.henanRealName = henanRealName == null ? null : henanRealName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.henan_real_phone
     *
     * @return the value of supply_chain..order_2c_info.henan_real_phone
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getHenanRealPhone() {
        return henanRealPhone;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withHenanRealPhone(String henanRealPhone) {
        this.setHenanRealPhone(henanRealPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.henan_real_phone
     *
     * @param henanRealPhone the value for supply_chain..order_2c_info.henan_real_phone
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setHenanRealPhone(String henanRealPhone) {
        this.henanRealPhone = henanRealPhone == null ? null : henanRealPhone.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.henan_real_address
     *
     * @return the value of supply_chain..order_2c_info.henan_real_address
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getHenanRealAddress() {
        return henanRealAddress;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withHenanRealAddress(String henanRealAddress) {
        this.setHenanRealAddress(henanRealAddress);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.henan_real_address
     *
     * @param henanRealAddress the value for supply_chain..order_2c_info.henan_real_address
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setHenanRealAddress(String henanRealAddress) {
        this.henanRealAddress = henanRealAddress == null ? null : henanRealAddress.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.spu_offering_class
     *
     * @return the value of supply_chain..order_2c_info.spu_offering_class
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getSpuOfferingClass() {
        return spuOfferingClass;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withSpuOfferingClass(String spuOfferingClass) {
        this.setSpuOfferingClass(spuOfferingClass);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.spu_offering_class
     *
     * @param spuOfferingClass the value for supply_chain..order_2c_info.spu_offering_class
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setSpuOfferingClass(String spuOfferingClass) {
        this.spuOfferingClass = spuOfferingClass == null ? null : spuOfferingClass.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.spu_offering_code
     *
     * @return the value of supply_chain..order_2c_info.spu_offering_code
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getSpuOfferingCode() {
        return spuOfferingCode;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withSpuOfferingCode(String spuOfferingCode) {
        this.setSpuOfferingCode(spuOfferingCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.spu_offering_code
     *
     * @param spuOfferingCode the value for supply_chain..order_2c_info.spu_offering_code
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setSpuOfferingCode(String spuOfferingCode) {
        this.spuOfferingCode = spuOfferingCode == null ? null : spuOfferingCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.supplier_code
     *
     * @return the value of supply_chain..order_2c_info.supplier_code
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getSupplierCode() {
        return supplierCode;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withSupplierCode(String supplierCode) {
        this.setSupplierCode(supplierCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.supplier_code
     *
     * @param supplierCode the value for supply_chain..order_2c_info.supplier_code
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode == null ? null : supplierCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.update_time
     *
     * @return the value of supply_chain..order_2c_info.update_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.update_time
     *
     * @param updateTime the value for supply_chain..order_2c_info.update_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.order_status
     *
     * @return the value of supply_chain..order_2c_info.order_status
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Integer getOrderStatus() {
        return orderStatus;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withOrderStatus(Integer orderStatus) {
        this.setOrderStatus(orderStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.order_status
     *
     * @param orderStatus the value for supply_chain..order_2c_info.order_status
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.deduct_price
     *
     * @return the value of supply_chain..order_2c_info.deduct_price
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getDeductPrice() {
        return deductPrice;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withDeductPrice(String deductPrice) {
        this.setDeductPrice(deductPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.deduct_price
     *
     * @param deductPrice the value for supply_chain..order_2c_info.deduct_price
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setDeductPrice(String deductPrice) {
        this.deductPrice = deductPrice == null ? null : deductPrice.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.ordering_channel_source
     *
     * @return the value of supply_chain..order_2c_info.ordering_channel_source
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getOrderingChannelSource() {
        return orderingChannelSource;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withOrderingChannelSource(String orderingChannelSource) {
        this.setOrderingChannelSource(orderingChannelSource);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.ordering_channel_source
     *
     * @param orderingChannelSource the value for supply_chain..order_2c_info.ordering_channel_source
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setOrderingChannelSource(String orderingChannelSource) {
        this.orderingChannelSource = orderingChannelSource == null ? null : orderingChannelSource.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.ordering_channel_name
     *
     * @return the value of supply_chain..order_2c_info.ordering_channel_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getOrderingChannelName() {
        return orderingChannelName;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withOrderingChannelName(String orderingChannelName) {
        this.setOrderingChannelName(orderingChannelName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.ordering_channel_name
     *
     * @param orderingChannelName the value for supply_chain..order_2c_info.ordering_channel_name
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setOrderingChannelName(String orderingChannelName) {
        this.orderingChannelName = orderingChannelName == null ? null : orderingChannelName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.sso_terminal_type
     *
     * @return the value of supply_chain..order_2c_info.sso_terminal_type
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getSsoTerminalType() {
        return ssoTerminalType;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withSsoTerminalType(String ssoTerminalType) {
        this.setSsoTerminalType(ssoTerminalType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.sso_terminal_type
     *
     * @param ssoTerminalType the value for supply_chain..order_2c_info.sso_terminal_type
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setSsoTerminalType(String ssoTerminalType) {
        this.ssoTerminalType = ssoTerminalType == null ? null : ssoTerminalType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.to_k3
     *
     * @return the value of supply_chain..order_2c_info.to_k3
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Integer getToK3() {
        return toK3;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withToK3(Integer toK3) {
        this.setToK3(toK3);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.to_k3
     *
     * @param toK3 the value for supply_chain..order_2c_info.to_k3
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setToK3(Integer toK3) {
        this.toK3 = toK3;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.special_after_market_handle
     *
     * @return the value of supply_chain..order_2c_info.special_after_market_handle
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Integer getSpecialAfterMarketHandle() {
        return specialAfterMarketHandle;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withSpecialAfterMarketHandle(Integer specialAfterMarketHandle) {
        this.setSpecialAfterMarketHandle(specialAfterMarketHandle);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.special_after_market_handle
     *
     * @param specialAfterMarketHandle the value for supply_chain..order_2c_info.special_after_market_handle
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setSpecialAfterMarketHandle(Integer specialAfterMarketHandle) {
        this.specialAfterMarketHandle = specialAfterMarketHandle;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.special_after_status
     *
     * @return the value of supply_chain..order_2c_info.special_after_status
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getSpecialAfterStatus() {
        return specialAfterStatus;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withSpecialAfterStatus(String specialAfterStatus) {
        this.setSpecialAfterStatus(specialAfterStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.special_after_status
     *
     * @param specialAfterStatus the value for supply_chain..order_2c_info.special_after_status
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setSpecialAfterStatus(String specialAfterStatus) {
        this.specialAfterStatus = specialAfterStatus == null ? null : specialAfterStatus.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.special_after_status_time
     *
     * @return the value of supply_chain..order_2c_info.special_after_status_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getSpecialAfterStatusTime() {
        return specialAfterStatusTime;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withSpecialAfterStatusTime(String specialAfterStatusTime) {
        this.setSpecialAfterStatusTime(specialAfterStatusTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.special_after_status_time
     *
     * @param specialAfterStatusTime the value for supply_chain..order_2c_info.special_after_status_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setSpecialAfterStatusTime(String specialAfterStatusTime) {
        this.specialAfterStatusTime = specialAfterStatusTime == null ? null : specialAfterStatusTime.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.special_after_latest_time
     *
     * @return the value of supply_chain..order_2c_info.special_after_latest_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getSpecialAfterLatestTime() {
        return specialAfterLatestTime;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withSpecialAfterLatestTime(String specialAfterLatestTime) {
        this.setSpecialAfterLatestTime(specialAfterLatestTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.special_after_latest_time
     *
     * @param specialAfterLatestTime the value for supply_chain..order_2c_info.special_after_latest_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setSpecialAfterLatestTime(String specialAfterLatestTime) {
        this.specialAfterLatestTime = specialAfterLatestTime == null ? null : specialAfterLatestTime.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.effective_rules
     *
     * @return the value of supply_chain..order_2c_info.effective_rules
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getEffectiveRules() {
        return effectiveRules;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withEffectiveRules(String effectiveRules) {
        this.setEffectiveRules(effectiveRules);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.effective_rules
     *
     * @param effectiveRules the value for supply_chain..order_2c_info.effective_rules
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setEffectiveRules(String effectiveRules) {
        this.effectiveRules = effectiveRules == null ? null : effectiveRules.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.effective_time
     *
     * @return the value of supply_chain..order_2c_info.effective_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getEffectiveTime() {
        return effectiveTime;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withEffectiveTime(String effectiveTime) {
        this.setEffectiveTime(effectiveTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.effective_time
     *
     * @param effectiveTime the value for supply_chain..order_2c_info.effective_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setEffectiveTime(String effectiveTime) {
        this.effectiveTime = effectiveTime == null ? null : effectiveTime.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.sync_k3_id
     *
     * @return the value of supply_chain..order_2c_info.sync_k3_id
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getSyncK3Id() {
        return syncK3Id;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withSyncK3Id(String syncK3Id) {
        this.setSyncK3Id(syncK3Id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.sync_k3_id
     *
     * @param syncK3Id the value for supply_chain..order_2c_info.sync_k3_id
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setSyncK3Id(String syncK3Id) {
        this.syncK3Id = syncK3Id == null ? null : syncK3Id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.pay_time
     *
     * @return the value of supply_chain..order_2c_info.pay_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Date getPayTime() {
        return payTime;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withPayTime(Date payTime) {
        this.setPayTime(payTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.pay_time
     *
     * @param payTime the value for supply_chain..order_2c_info.pay_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.refund_time
     *
     * @return the value of supply_chain..order_2c_info.refund_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Date getRefundTime() {
        return refundTime;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withRefundTime(Date refundTime) {
        this.setRefundTime(refundTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.refund_time
     *
     * @param refundTime the value for supply_chain..order_2c_info.refund_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setRefundTime(Date refundTime) {
        this.refundTime = refundTime;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.valet_order_complete_time
     *
     * @return the value of supply_chain..order_2c_info.valet_order_complete_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getValetOrderCompleteTime() {
        return valetOrderCompleteTime;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withValetOrderCompleteTime(String valetOrderCompleteTime) {
        this.setValetOrderCompleteTime(valetOrderCompleteTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.valet_order_complete_time
     *
     * @param valetOrderCompleteTime the value for supply_chain..order_2c_info.valet_order_complete_time
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setValetOrderCompleteTime(String valetOrderCompleteTime) {
        this.valetOrderCompleteTime = valetOrderCompleteTime == null ? null : valetOrderCompleteTime.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.bill_ladder_type
     *
     * @return the value of supply_chain..order_2c_info.bill_ladder_type
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getBillLadderType() {
        return billLadderType;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withBillLadderType(String billLadderType) {
        this.setBillLadderType(billLadderType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.bill_ladder_type
     *
     * @param billLadderType the value for supply_chain..order_2c_info.bill_ladder_type
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setBillLadderType(String billLadderType) {
        this.billLadderType = billLadderType == null ? null : billLadderType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.qly_status
     *
     * @return the value of supply_chain..order_2c_info.qly_status
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Integer getQlyStatus() {
        return qlyStatus;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withQlyStatus(Integer qlyStatus) {
        this.setQlyStatus(qlyStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.qly_status
     *
     * @param qlyStatus the value for supply_chain..order_2c_info.qly_status
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setQlyStatus(Integer qlyStatus) {
        this.qlyStatus = qlyStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.ysx_status
     *
     * @return the value of supply_chain..order_2c_info.ysx_status
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Integer getYsxStatus() {
        return ysxStatus;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withYsxStatus(Integer ysxStatus) {
        this.setYsxStatus(ysxStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.ysx_status
     *
     * @param ysxStatus the value for supply_chain..order_2c_info.ysx_status
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setYsxStatus(Integer ysxStatus) {
        this.ysxStatus = ysxStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.kx_refund_status
     *
     * @return the value of supply_chain..order_2c_info.kx_refund_status
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Integer getKxRefundStatus() {
        return kxRefundStatus;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withKxRefundStatus(Integer kxRefundStatus) {
        this.setKxRefundStatus(kxRefundStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.kx_refund_status
     *
     * @param kxRefundStatus the value for supply_chain..order_2c_info.kx_refund_status
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setKxRefundStatus(Integer kxRefundStatus) {
        this.kxRefundStatus = kxRefundStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.kx_refund_reason
     *
     * @return the value of supply_chain..order_2c_info.kx_refund_reason
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getKxRefundReason() {
        return kxRefundReason;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withKxRefundReason(String kxRefundReason) {
        this.setKxRefundReason(kxRefundReason);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.kx_refund_reason
     *
     * @param kxRefundReason the value for supply_chain..order_2c_info.kx_refund_reason
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setKxRefundReason(String kxRefundReason) {
        this.kxRefundReason = kxRefundReason == null ? null : kxRefundReason.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.spu_offering_version
     *
     * @return the value of supply_chain..order_2c_info.spu_offering_version
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getSpuOfferingVersion() {
        return spuOfferingVersion;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withSpuOfferingVersion(String spuOfferingVersion) {
        this.setSpuOfferingVersion(spuOfferingVersion);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.spu_offering_version
     *
     * @param spuOfferingVersion the value for supply_chain..order_2c_info.spu_offering_version
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setSpuOfferingVersion(String spuOfferingVersion) {
        this.spuOfferingVersion = spuOfferingVersion == null ? null : spuOfferingVersion.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.reserve_be_id
     *
     * @return the value of supply_chain..order_2c_info.reserve_be_id
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getReserveBeId() {
        return reserveBeId;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withReserveBeId(String reserveBeId) {
        this.setReserveBeId(reserveBeId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.reserve_be_id
     *
     * @param reserveBeId the value for supply_chain..order_2c_info.reserve_be_id
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setReserveBeId(String reserveBeId) {
        this.reserveBeId = reserveBeId == null ? null : reserveBeId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.reserve_location
     *
     * @return the value of supply_chain..order_2c_info.reserve_location
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getReserveLocation() {
        return reserveLocation;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withReserveLocation(String reserveLocation) {
        this.setReserveLocation(reserveLocation);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.reserve_location
     *
     * @param reserveLocation the value for supply_chain..order_2c_info.reserve_location
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setReserveLocation(String reserveLocation) {
        this.reserveLocation = reserveLocation == null ? null : reserveLocation.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.software_order_type
     *
     * @return the value of supply_chain..order_2c_info.software_order_type
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getSoftwareOrderType() {
        return softwareOrderType;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withSoftwareOrderType(String softwareOrderType) {
        this.setSoftwareOrderType(softwareOrderType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.software_order_type
     *
     * @param softwareOrderType the value for supply_chain..order_2c_info.software_order_type
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setSoftwareOrderType(String softwareOrderType) {
        this.softwareOrderType = softwareOrderType == null ? null : softwareOrderType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.associated_order
     *
     * @return the value of supply_chain..order_2c_info.associated_order
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getAssociatedOrder() {
        return associatedOrder;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withAssociatedOrder(String associatedOrder) {
        this.setAssociatedOrder(associatedOrder);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.associated_order
     *
     * @param associatedOrder the value for supply_chain..order_2c_info.associated_order
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setAssociatedOrder(String associatedOrder) {
        this.associatedOrder = associatedOrder == null ? null : associatedOrder.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.special_after_refunds_number
     *
     * @return the value of supply_chain..order_2c_info.special_after_refunds_number
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getSpecialAfterRefundsNumber() {
        return specialAfterRefundsNumber;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withSpecialAfterRefundsNumber(String specialAfterRefundsNumber) {
        this.setSpecialAfterRefundsNumber(specialAfterRefundsNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.special_after_refunds_number
     *
     * @param specialAfterRefundsNumber the value for supply_chain..order_2c_info.special_after_refunds_number
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setSpecialAfterRefundsNumber(String specialAfterRefundsNumber) {
        this.specialAfterRefundsNumber = specialAfterRefundsNumber == null ? null : specialAfterRefundsNumber.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.spu_list_platform
     *
     * @return the value of supply_chain..order_2c_info.spu_list_platform
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getSpuListPlatform() {
        return spuListPlatform;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withSpuListPlatform(String spuListPlatform) {
        this.setSpuListPlatform(spuListPlatform);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.spu_list_platform
     *
     * @param spuListPlatform the value for supply_chain..order_2c_info.spu_list_platform
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setSpuListPlatform(String spuListPlatform) {
        this.spuListPlatform = spuListPlatform == null ? null : spuListPlatform.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.bill_no_number
     *
     * @return the value of supply_chain..order_2c_info.bill_no_number
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getBillNoNumber() {
        return billNoNumber;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withBillNoNumber(String billNoNumber) {
        this.setBillNoNumber(billNoNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.bill_no_number
     *
     * @param billNoNumber the value for supply_chain..order_2c_info.bill_no_number
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setBillNoNumber(String billNoNumber) {
        this.billNoNumber = billNoNumber == null ? null : billNoNumber.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.reminder_wait_send
     *
     * @return the value of supply_chain..order_2c_info.reminder_wait_send
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Integer getReminderWaitSend() {
        return reminderWaitSend;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withReminderWaitSend(Integer reminderWaitSend) {
        this.setReminderWaitSend(reminderWaitSend);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.reminder_wait_send
     *
     * @param reminderWaitSend the value for supply_chain..order_2c_info.reminder_wait_send
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setReminderWaitSend(Integer reminderWaitSend) {
        this.reminderWaitSend = reminderWaitSend;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.reminder_valet_taking
     *
     * @return the value of supply_chain..order_2c_info.reminder_valet_taking
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Integer getReminderValetTaking() {
        return reminderValetTaking;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withReminderValetTaking(Integer reminderValetTaking) {
        this.setReminderValetTaking(reminderValetTaking);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.reminder_valet_taking
     *
     * @param reminderValetTaking the value for supply_chain..order_2c_info.reminder_valet_taking
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setReminderValetTaking(Integer reminderValetTaking) {
        this.reminderValetTaking = reminderValetTaking;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.reminder_wait_deliver
     *
     * @return the value of supply_chain..order_2c_info.reminder_wait_deliver
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Integer getReminderWaitDeliver() {
        return reminderWaitDeliver;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withReminderWaitDeliver(Integer reminderWaitDeliver) {
        this.setReminderWaitDeliver(reminderWaitDeliver);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.reminder_wait_deliver
     *
     * @param reminderWaitDeliver the value for supply_chain..order_2c_info.reminder_wait_deliver
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setReminderWaitDeliver(Integer reminderWaitDeliver) {
        this.reminderWaitDeliver = reminderWaitDeliver;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_info.customer_type
     *
     * @return the value of supply_chain..order_2c_info.customer_type
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public String getCustomerType() {
        return customerType;
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public Order2cInfo withCustomerType(String customerType) {
        this.setCustomerType(customerType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_info.customer_type
     *
     * @param customerType the value for supply_chain..order_2c_info.customer_type
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public void setCustomerType(String customerType) {
        this.customerType = customerType == null ? null : customerType.trim();
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", orderId=").append(orderId);
        sb.append(", orderType=").append(orderType);
        sb.append(", businessCode=").append(businessCode);
        sb.append(", createOperCode=").append(createOperCode);
        sb.append(", createOperUserId=").append(createOperUserId);
        sb.append(", employeeNum=").append(employeeNum);
        sb.append(", custMgName=").append(custMgName);
        sb.append(", custMgPhone=").append(custMgPhone);
        sb.append(", orderStatusTime=").append(orderStatusTime);
        sb.append(", custCode=").append(custCode);
        sb.append(", custUserId=").append(custUserId);
        sb.append(", custName=").append(custName);
        sb.append(", beId=").append(beId);
        sb.append(", location=").append(location);
        sb.append(", regionId=").append(regionId);
        sb.append(", orderOrgBizCode=").append(orderOrgBizCode);
        sb.append(", orgLevel=").append(orgLevel);
        sb.append(", orgName=").append(orgName);
        sb.append(", provinceOrgName=").append(provinceOrgName);
        sb.append(", remarks=").append(remarks);
        sb.append(", bookid=").append(bookid);
        sb.append(", status=").append(status);
        sb.append(", totalPrice=").append(totalPrice);
        sb.append(", createTime=").append(createTime);
        sb.append(", contactPersonName=").append(contactPersonName);
        sb.append(", contactPhone=").append(contactPhone);
        sb.append(", addr1=").append(addr1);
        sb.append(", addr2=").append(addr2);
        sb.append(", addr3=").append(addr3);
        sb.append(", addr4=").append(addr4);
        sb.append(", usaddr=").append(usaddr);
        sb.append(", henanRealName=").append(henanRealName);
        sb.append(", henanRealPhone=").append(henanRealPhone);
        sb.append(", henanRealAddress=").append(henanRealAddress);
        sb.append(", spuOfferingClass=").append(spuOfferingClass);
        sb.append(", spuOfferingCode=").append(spuOfferingCode);
        sb.append(", supplierCode=").append(supplierCode);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", orderStatus=").append(orderStatus);
        sb.append(", deductPrice=").append(deductPrice);
        sb.append(", orderingChannelSource=").append(orderingChannelSource);
        sb.append(", orderingChannelName=").append(orderingChannelName);
        sb.append(", ssoTerminalType=").append(ssoTerminalType);
        sb.append(", toK3=").append(toK3);
        sb.append(", specialAfterMarketHandle=").append(specialAfterMarketHandle);
        sb.append(", specialAfterStatus=").append(specialAfterStatus);
        sb.append(", specialAfterStatusTime=").append(specialAfterStatusTime);
        sb.append(", specialAfterLatestTime=").append(specialAfterLatestTime);
        sb.append(", effectiveRules=").append(effectiveRules);
        sb.append(", effectiveTime=").append(effectiveTime);
        sb.append(", syncK3Id=").append(syncK3Id);
        sb.append(", payTime=").append(payTime);
        sb.append(", refundTime=").append(refundTime);
        sb.append(", valetOrderCompleteTime=").append(valetOrderCompleteTime);
        sb.append(", billLadderType=").append(billLadderType);
        sb.append(", qlyStatus=").append(qlyStatus);
        sb.append(", ysxStatus=").append(ysxStatus);
        sb.append(", kxRefundStatus=").append(kxRefundStatus);
        sb.append(", kxRefundReason=").append(kxRefundReason);
        sb.append(", spuOfferingVersion=").append(spuOfferingVersion);
        sb.append(", reserveBeId=").append(reserveBeId);
        sb.append(", reserveLocation=").append(reserveLocation);
        sb.append(", softwareOrderType=").append(softwareOrderType);
        sb.append(", associatedOrder=").append(associatedOrder);
        sb.append(", specialAfterRefundsNumber=").append(specialAfterRefundsNumber);
        sb.append(", spuListPlatform=").append(spuListPlatform);
        sb.append(", billNoNumber=").append(billNoNumber);
        sb.append(", reminderWaitSend=").append(reminderWaitSend);
        sb.append(", reminderValetTaking=").append(reminderValetTaking);
        sb.append(", reminderWaitDeliver=").append(reminderWaitDeliver);
        sb.append(", customerType=").append(customerType);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Order2cInfo other = (Order2cInfo) that;
        return (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getOrderType() == null ? other.getOrderType() == null : this.getOrderType().equals(other.getOrderType()))
            && (this.getBusinessCode() == null ? other.getBusinessCode() == null : this.getBusinessCode().equals(other.getBusinessCode()))
            && (this.getCreateOperCode() == null ? other.getCreateOperCode() == null : this.getCreateOperCode().equals(other.getCreateOperCode()))
            && (this.getCreateOperUserId() == null ? other.getCreateOperUserId() == null : this.getCreateOperUserId().equals(other.getCreateOperUserId()))
            && (this.getEmployeeNum() == null ? other.getEmployeeNum() == null : this.getEmployeeNum().equals(other.getEmployeeNum()))
            && (this.getCustMgName() == null ? other.getCustMgName() == null : this.getCustMgName().equals(other.getCustMgName()))
            && (this.getCustMgPhone() == null ? other.getCustMgPhone() == null : this.getCustMgPhone().equals(other.getCustMgPhone()))
            && (this.getOrderStatusTime() == null ? other.getOrderStatusTime() == null : this.getOrderStatusTime().equals(other.getOrderStatusTime()))
            && (this.getCustCode() == null ? other.getCustCode() == null : this.getCustCode().equals(other.getCustCode()))
            && (this.getCustUserId() == null ? other.getCustUserId() == null : this.getCustUserId().equals(other.getCustUserId()))
            && (this.getCustName() == null ? other.getCustName() == null : this.getCustName().equals(other.getCustName()))
            && (this.getBeId() == null ? other.getBeId() == null : this.getBeId().equals(other.getBeId()))
            && (this.getLocation() == null ? other.getLocation() == null : this.getLocation().equals(other.getLocation()))
            && (this.getRegionId() == null ? other.getRegionId() == null : this.getRegionId().equals(other.getRegionId()))
            && (this.getOrderOrgBizCode() == null ? other.getOrderOrgBizCode() == null : this.getOrderOrgBizCode().equals(other.getOrderOrgBizCode()))
            && (this.getOrgLevel() == null ? other.getOrgLevel() == null : this.getOrgLevel().equals(other.getOrgLevel()))
            && (this.getOrgName() == null ? other.getOrgName() == null : this.getOrgName().equals(other.getOrgName()))
            && (this.getProvinceOrgName() == null ? other.getProvinceOrgName() == null : this.getProvinceOrgName().equals(other.getProvinceOrgName()))
            && (this.getRemarks() == null ? other.getRemarks() == null : this.getRemarks().equals(other.getRemarks()))
            && (this.getBookid() == null ? other.getBookid() == null : this.getBookid().equals(other.getBookid()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getTotalPrice() == null ? other.getTotalPrice() == null : this.getTotalPrice().equals(other.getTotalPrice()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getContactPersonName() == null ? other.getContactPersonName() == null : this.getContactPersonName().equals(other.getContactPersonName()))
            && (this.getContactPhone() == null ? other.getContactPhone() == null : this.getContactPhone().equals(other.getContactPhone()))
            && (this.getAddr1() == null ? other.getAddr1() == null : this.getAddr1().equals(other.getAddr1()))
            && (this.getAddr2() == null ? other.getAddr2() == null : this.getAddr2().equals(other.getAddr2()))
            && (this.getAddr3() == null ? other.getAddr3() == null : this.getAddr3().equals(other.getAddr3()))
            && (this.getAddr4() == null ? other.getAddr4() == null : this.getAddr4().equals(other.getAddr4()))
            && (this.getUsaddr() == null ? other.getUsaddr() == null : this.getUsaddr().equals(other.getUsaddr()))
            && (this.getHenanRealName() == null ? other.getHenanRealName() == null : this.getHenanRealName().equals(other.getHenanRealName()))
            && (this.getHenanRealPhone() == null ? other.getHenanRealPhone() == null : this.getHenanRealPhone().equals(other.getHenanRealPhone()))
            && (this.getHenanRealAddress() == null ? other.getHenanRealAddress() == null : this.getHenanRealAddress().equals(other.getHenanRealAddress()))
            && (this.getSpuOfferingClass() == null ? other.getSpuOfferingClass() == null : this.getSpuOfferingClass().equals(other.getSpuOfferingClass()))
            && (this.getSpuOfferingCode() == null ? other.getSpuOfferingCode() == null : this.getSpuOfferingCode().equals(other.getSpuOfferingCode()))
            && (this.getSupplierCode() == null ? other.getSupplierCode() == null : this.getSupplierCode().equals(other.getSupplierCode()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getOrderStatus() == null ? other.getOrderStatus() == null : this.getOrderStatus().equals(other.getOrderStatus()))
            && (this.getDeductPrice() == null ? other.getDeductPrice() == null : this.getDeductPrice().equals(other.getDeductPrice()))
            && (this.getOrderingChannelSource() == null ? other.getOrderingChannelSource() == null : this.getOrderingChannelSource().equals(other.getOrderingChannelSource()))
            && (this.getOrderingChannelName() == null ? other.getOrderingChannelName() == null : this.getOrderingChannelName().equals(other.getOrderingChannelName()))
            && (this.getSsoTerminalType() == null ? other.getSsoTerminalType() == null : this.getSsoTerminalType().equals(other.getSsoTerminalType()))
            && (this.getToK3() == null ? other.getToK3() == null : this.getToK3().equals(other.getToK3()))
            && (this.getSpecialAfterMarketHandle() == null ? other.getSpecialAfterMarketHandle() == null : this.getSpecialAfterMarketHandle().equals(other.getSpecialAfterMarketHandle()))
            && (this.getSpecialAfterStatus() == null ? other.getSpecialAfterStatus() == null : this.getSpecialAfterStatus().equals(other.getSpecialAfterStatus()))
            && (this.getSpecialAfterStatusTime() == null ? other.getSpecialAfterStatusTime() == null : this.getSpecialAfterStatusTime().equals(other.getSpecialAfterStatusTime()))
            && (this.getSpecialAfterLatestTime() == null ? other.getSpecialAfterLatestTime() == null : this.getSpecialAfterLatestTime().equals(other.getSpecialAfterLatestTime()))
            && (this.getEffectiveRules() == null ? other.getEffectiveRules() == null : this.getEffectiveRules().equals(other.getEffectiveRules()))
            && (this.getEffectiveTime() == null ? other.getEffectiveTime() == null : this.getEffectiveTime().equals(other.getEffectiveTime()))
            && (this.getSyncK3Id() == null ? other.getSyncK3Id() == null : this.getSyncK3Id().equals(other.getSyncK3Id()))
            && (this.getPayTime() == null ? other.getPayTime() == null : this.getPayTime().equals(other.getPayTime()))
            && (this.getRefundTime() == null ? other.getRefundTime() == null : this.getRefundTime().equals(other.getRefundTime()))
            && (this.getValetOrderCompleteTime() == null ? other.getValetOrderCompleteTime() == null : this.getValetOrderCompleteTime().equals(other.getValetOrderCompleteTime()))
            && (this.getBillLadderType() == null ? other.getBillLadderType() == null : this.getBillLadderType().equals(other.getBillLadderType()))
            && (this.getQlyStatus() == null ? other.getQlyStatus() == null : this.getQlyStatus().equals(other.getQlyStatus()))
            && (this.getYsxStatus() == null ? other.getYsxStatus() == null : this.getYsxStatus().equals(other.getYsxStatus()))
            && (this.getKxRefundStatus() == null ? other.getKxRefundStatus() == null : this.getKxRefundStatus().equals(other.getKxRefundStatus()))
            && (this.getKxRefundReason() == null ? other.getKxRefundReason() == null : this.getKxRefundReason().equals(other.getKxRefundReason()))
            && (this.getSpuOfferingVersion() == null ? other.getSpuOfferingVersion() == null : this.getSpuOfferingVersion().equals(other.getSpuOfferingVersion()))
            && (this.getReserveBeId() == null ? other.getReserveBeId() == null : this.getReserveBeId().equals(other.getReserveBeId()))
            && (this.getReserveLocation() == null ? other.getReserveLocation() == null : this.getReserveLocation().equals(other.getReserveLocation()))
            && (this.getSoftwareOrderType() == null ? other.getSoftwareOrderType() == null : this.getSoftwareOrderType().equals(other.getSoftwareOrderType()))
            && (this.getAssociatedOrder() == null ? other.getAssociatedOrder() == null : this.getAssociatedOrder().equals(other.getAssociatedOrder()))
            && (this.getSpecialAfterRefundsNumber() == null ? other.getSpecialAfterRefundsNumber() == null : this.getSpecialAfterRefundsNumber().equals(other.getSpecialAfterRefundsNumber()))
            && (this.getSpuListPlatform() == null ? other.getSpuListPlatform() == null : this.getSpuListPlatform().equals(other.getSpuListPlatform()))
            && (this.getBillNoNumber() == null ? other.getBillNoNumber() == null : this.getBillNoNumber().equals(other.getBillNoNumber()))
            && (this.getReminderWaitSend() == null ? other.getReminderWaitSend() == null : this.getReminderWaitSend().equals(other.getReminderWaitSend()))
            && (this.getReminderValetTaking() == null ? other.getReminderValetTaking() == null : this.getReminderValetTaking().equals(other.getReminderValetTaking()))
            && (this.getReminderWaitDeliver() == null ? other.getReminderWaitDeliver() == null : this.getReminderWaitDeliver().equals(other.getReminderWaitDeliver()))
            && (this.getCustomerType() == null ? other.getCustomerType() == null : this.getCustomerType().equals(other.getCustomerType()));
    }

    /**
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getOrderType() == null) ? 0 : getOrderType().hashCode());
        result = prime * result + ((getBusinessCode() == null) ? 0 : getBusinessCode().hashCode());
        result = prime * result + ((getCreateOperCode() == null) ? 0 : getCreateOperCode().hashCode());
        result = prime * result + ((getCreateOperUserId() == null) ? 0 : getCreateOperUserId().hashCode());
        result = prime * result + ((getEmployeeNum() == null) ? 0 : getEmployeeNum().hashCode());
        result = prime * result + ((getCustMgName() == null) ? 0 : getCustMgName().hashCode());
        result = prime * result + ((getCustMgPhone() == null) ? 0 : getCustMgPhone().hashCode());
        result = prime * result + ((getOrderStatusTime() == null) ? 0 : getOrderStatusTime().hashCode());
        result = prime * result + ((getCustCode() == null) ? 0 : getCustCode().hashCode());
        result = prime * result + ((getCustUserId() == null) ? 0 : getCustUserId().hashCode());
        result = prime * result + ((getCustName() == null) ? 0 : getCustName().hashCode());
        result = prime * result + ((getBeId() == null) ? 0 : getBeId().hashCode());
        result = prime * result + ((getLocation() == null) ? 0 : getLocation().hashCode());
        result = prime * result + ((getRegionId() == null) ? 0 : getRegionId().hashCode());
        result = prime * result + ((getOrderOrgBizCode() == null) ? 0 : getOrderOrgBizCode().hashCode());
        result = prime * result + ((getOrgLevel() == null) ? 0 : getOrgLevel().hashCode());
        result = prime * result + ((getOrgName() == null) ? 0 : getOrgName().hashCode());
        result = prime * result + ((getProvinceOrgName() == null) ? 0 : getProvinceOrgName().hashCode());
        result = prime * result + ((getRemarks() == null) ? 0 : getRemarks().hashCode());
        result = prime * result + ((getBookid() == null) ? 0 : getBookid().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getTotalPrice() == null) ? 0 : getTotalPrice().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getContactPersonName() == null) ? 0 : getContactPersonName().hashCode());
        result = prime * result + ((getContactPhone() == null) ? 0 : getContactPhone().hashCode());
        result = prime * result + ((getAddr1() == null) ? 0 : getAddr1().hashCode());
        result = prime * result + ((getAddr2() == null) ? 0 : getAddr2().hashCode());
        result = prime * result + ((getAddr3() == null) ? 0 : getAddr3().hashCode());
        result = prime * result + ((getAddr4() == null) ? 0 : getAddr4().hashCode());
        result = prime * result + ((getUsaddr() == null) ? 0 : getUsaddr().hashCode());
        result = prime * result + ((getHenanRealName() == null) ? 0 : getHenanRealName().hashCode());
        result = prime * result + ((getHenanRealPhone() == null) ? 0 : getHenanRealPhone().hashCode());
        result = prime * result + ((getHenanRealAddress() == null) ? 0 : getHenanRealAddress().hashCode());
        result = prime * result + ((getSpuOfferingClass() == null) ? 0 : getSpuOfferingClass().hashCode());
        result = prime * result + ((getSpuOfferingCode() == null) ? 0 : getSpuOfferingCode().hashCode());
        result = prime * result + ((getSupplierCode() == null) ? 0 : getSupplierCode().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getOrderStatus() == null) ? 0 : getOrderStatus().hashCode());
        result = prime * result + ((getDeductPrice() == null) ? 0 : getDeductPrice().hashCode());
        result = prime * result + ((getOrderingChannelSource() == null) ? 0 : getOrderingChannelSource().hashCode());
        result = prime * result + ((getOrderingChannelName() == null) ? 0 : getOrderingChannelName().hashCode());
        result = prime * result + ((getSsoTerminalType() == null) ? 0 : getSsoTerminalType().hashCode());
        result = prime * result + ((getToK3() == null) ? 0 : getToK3().hashCode());
        result = prime * result + ((getSpecialAfterMarketHandle() == null) ? 0 : getSpecialAfterMarketHandle().hashCode());
        result = prime * result + ((getSpecialAfterStatus() == null) ? 0 : getSpecialAfterStatus().hashCode());
        result = prime * result + ((getSpecialAfterStatusTime() == null) ? 0 : getSpecialAfterStatusTime().hashCode());
        result = prime * result + ((getSpecialAfterLatestTime() == null) ? 0 : getSpecialAfterLatestTime().hashCode());
        result = prime * result + ((getEffectiveRules() == null) ? 0 : getEffectiveRules().hashCode());
        result = prime * result + ((getEffectiveTime() == null) ? 0 : getEffectiveTime().hashCode());
        result = prime * result + ((getSyncK3Id() == null) ? 0 : getSyncK3Id().hashCode());
        result = prime * result + ((getPayTime() == null) ? 0 : getPayTime().hashCode());
        result = prime * result + ((getRefundTime() == null) ? 0 : getRefundTime().hashCode());
        result = prime * result + ((getValetOrderCompleteTime() == null) ? 0 : getValetOrderCompleteTime().hashCode());
        result = prime * result + ((getBillLadderType() == null) ? 0 : getBillLadderType().hashCode());
        result = prime * result + ((getQlyStatus() == null) ? 0 : getQlyStatus().hashCode());
        result = prime * result + ((getYsxStatus() == null) ? 0 : getYsxStatus().hashCode());
        result = prime * result + ((getKxRefundStatus() == null) ? 0 : getKxRefundStatus().hashCode());
        result = prime * result + ((getKxRefundReason() == null) ? 0 : getKxRefundReason().hashCode());
        result = prime * result + ((getSpuOfferingVersion() == null) ? 0 : getSpuOfferingVersion().hashCode());
        result = prime * result + ((getReserveBeId() == null) ? 0 : getReserveBeId().hashCode());
        result = prime * result + ((getReserveLocation() == null) ? 0 : getReserveLocation().hashCode());
        result = prime * result + ((getSoftwareOrderType() == null) ? 0 : getSoftwareOrderType().hashCode());
        result = prime * result + ((getAssociatedOrder() == null) ? 0 : getAssociatedOrder().hashCode());
        result = prime * result + ((getSpecialAfterRefundsNumber() == null) ? 0 : getSpecialAfterRefundsNumber().hashCode());
        result = prime * result + ((getSpuListPlatform() == null) ? 0 : getSpuListPlatform().hashCode());
        result = prime * result + ((getBillNoNumber() == null) ? 0 : getBillNoNumber().hashCode());
        result = prime * result + ((getReminderWaitSend() == null) ? 0 : getReminderWaitSend().hashCode());
        result = prime * result + ((getReminderValetTaking() == null) ? 0 : getReminderValetTaking().hashCode());
        result = prime * result + ((getReminderWaitDeliver() == null) ? 0 : getReminderWaitDeliver().hashCode());
        result = prime * result + ((getCustomerType() == null) ? 0 : getCustomerType().hashCode());
        return result;
    }

    /**
     * corresponding to the database table supply_chain..order_2c_info
     *
     * @mbg.generated Mon Jun 09 09:29:34 CST 2025
     */
    public enum Column {
        orderId("order_id", "orderId", "VARCHAR", false),
        orderType("order_type", "orderType", "VARCHAR", false),
        businessCode("business_code", "businessCode", "VARCHAR", false),
        createOperCode("create_oper_code", "createOperCode", "VARCHAR", false),
        createOperUserId("create_oper_user_id", "createOperUserId", "VARCHAR", false),
        employeeNum("employee_num", "employeeNum", "VARCHAR", false),
        custMgName("cust_mg_name", "custMgName", "VARCHAR", false),
        custMgPhone("cust_mg_phone", "custMgPhone", "VARCHAR", false),
        orderStatusTime("order_status_time", "orderStatusTime", "TIMESTAMP", false),
        custCode("cust_code", "custCode", "VARCHAR", false),
        custUserId("cust_user_id", "custUserId", "VARCHAR", false),
        custName("cust_name", "custName", "VARCHAR", false),
        beId("be_id", "beId", "VARCHAR", false),
        location("location", "location", "VARCHAR", false),
        regionId("region_ID", "regionId", "VARCHAR", false),
        orderOrgBizCode("order_org_biz_code", "orderOrgBizCode", "VARCHAR", false),
        orgLevel("org_level", "orgLevel", "VARCHAR", false),
        orgName("org_name", "orgName", "VARCHAR", false),
        provinceOrgName("province_org_name", "provinceOrgName", "VARCHAR", false),
        remarks("remarks", "remarks", "VARCHAR", false),
        bookid("bookId", "bookid", "VARCHAR", false),
        status("status", "status", "INTEGER", false),
        totalPrice("total_price", "totalPrice", "VARCHAR", false),
        createTime("create_time", "createTime", "VARCHAR", false),
        contactPersonName("contact_person_name", "contactPersonName", "VARCHAR", false),
        contactPhone("contact_phone", "contactPhone", "VARCHAR", false),
        addr1("addr1", "addr1", "VARCHAR", false),
        addr2("addr2", "addr2", "VARCHAR", false),
        addr3("addr3", "addr3", "VARCHAR", false),
        addr4("addr4", "addr4", "VARCHAR", false),
        usaddr("usaddr", "usaddr", "VARCHAR", false),
        henanRealName("henan_real_name", "henanRealName", "VARCHAR", false),
        henanRealPhone("henan_real_phone", "henanRealPhone", "VARCHAR", false),
        henanRealAddress("henan_real_address", "henanRealAddress", "VARCHAR", false),
        spuOfferingClass("spu_offering_class", "spuOfferingClass", "VARCHAR", false),
        spuOfferingCode("spu_offering_code", "spuOfferingCode", "VARCHAR", false),
        supplierCode("supplier_code", "supplierCode", "VARCHAR", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        orderStatus("order_status", "orderStatus", "INTEGER", false),
        deductPrice("deduct_price", "deductPrice", "VARCHAR", false),
        orderingChannelSource("ordering_channel_source", "orderingChannelSource", "VARCHAR", false),
        orderingChannelName("ordering_channel_name", "orderingChannelName", "VARCHAR", false),
        ssoTerminalType("sso_terminal_type", "ssoTerminalType", "VARCHAR", false),
        toK3("to_k3", "toK3", "INTEGER", false),
        specialAfterMarketHandle("special_after_market_handle", "specialAfterMarketHandle", "INTEGER", false),
        specialAfterStatus("special_after_status", "specialAfterStatus", "VARCHAR", false),
        specialAfterStatusTime("special_after_status_time", "specialAfterStatusTime", "VARCHAR", false),
        specialAfterLatestTime("special_after_latest_time", "specialAfterLatestTime", "VARCHAR", false),
        effectiveRules("effective_rules", "effectiveRules", "VARCHAR", false),
        effectiveTime("effective_time", "effectiveTime", "VARCHAR", false),
        syncK3Id("sync_k3_id", "syncK3Id", "VARCHAR", false),
        payTime("pay_time", "payTime", "TIMESTAMP", false),
        refundTime("refund_time", "refundTime", "TIMESTAMP", false),
        valetOrderCompleteTime("valet_order_complete_time", "valetOrderCompleteTime", "VARCHAR", false),
        billLadderType("bill_ladder_type", "billLadderType", "VARCHAR", false),
        qlyStatus("qly_status", "qlyStatus", "INTEGER", false),
        ysxStatus("ysx_status", "ysxStatus", "INTEGER", false),
        kxRefundStatus("kx_refund_status", "kxRefundStatus", "INTEGER", false),
        kxRefundReason("kx_refund_reason", "kxRefundReason", "VARCHAR", false),
        spuOfferingVersion("spu_offering_version", "spuOfferingVersion", "VARCHAR", false),
        reserveBeId("reserve_be_id", "reserveBeId", "VARCHAR", false),
        reserveLocation("reserve_location", "reserveLocation", "VARCHAR", false),
        softwareOrderType("software_order_type", "softwareOrderType", "VARCHAR", false),
        associatedOrder("associated_order", "associatedOrder", "VARCHAR", false),
        specialAfterRefundsNumber("special_after_refunds_number", "specialAfterRefundsNumber", "VARCHAR", false),
        spuListPlatform("spu_list_platform", "spuListPlatform", "VARCHAR", false),
        billNoNumber("bill_no_number", "billNoNumber", "VARCHAR", false),
        reminderWaitSend("reminder_wait_send", "reminderWaitSend", "INTEGER", false),
        reminderValetTaking("reminder_valet_taking", "reminderValetTaking", "INTEGER", false),
        reminderWaitDeliver("reminder_wait_deliver", "reminderWaitDeliver", "INTEGER", false),
        customerType("customer_type", "customerType", "VARCHAR", false);

        /**
         * Corresponding to the database table supply_chain..order_2c_info
         *
         * @mbg.generated Mon Jun 09 09:29:34 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..order_2c_info
         *
         * @mbg.generated Mon Jun 09 09:29:34 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..order_2c_info
         *
         * @mbg.generated Mon Jun 09 09:29:34 CST 2025
         */
        private final String column;

        /**
         * Corresponding to the database table supply_chain..order_2c_info
         *
         * @mbg.generated Mon Jun 09 09:29:34 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         * Corresponding to the database table supply_chain..order_2c_info
         *
         * @mbg.generated Mon Jun 09 09:29:34 CST 2025
         */
        private final String javaProperty;

        /**
         * Corresponding to the database table supply_chain..order_2c_info
         *
         * @mbg.generated Mon Jun 09 09:29:34 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Jun 09 09:29:34 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Jun 09 09:29:34 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Jun 09 09:29:34 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Jun 09 09:29:34 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Jun 09 09:29:34 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Jun 09 09:29:34 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Jun 09 09:29:34 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Jun 09 09:29:34 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Jun 09 09:29:34 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Jun 09 09:29:34 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Jun 09 09:29:34 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}