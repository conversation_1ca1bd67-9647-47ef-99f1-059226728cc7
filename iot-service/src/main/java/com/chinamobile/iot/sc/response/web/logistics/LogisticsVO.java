package com.chinamobile.iot.sc.response.web.logistics;

import com.amazonaws.services.dynamodbv2.xspec.S;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> xiemaoh<PERSON>
 * @date : 2022/8/12 14:31
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> xie<PERSON><PERSON><PERSON>
 * @date : 2022/8/15 9:43
 * @description: 物流详情返回VO
 **/
@Data
public class LogisticsVO {

    /**
     * 物流单号
     */
    private String trackingCode;

    /**
     * 物流服务商
     */
    private String supplierName;

    /**
     * 物流详情
     */
    private List<LogisticsDetails> logisticsDetails;


    /**
     * 错误信息
     */
    private String otherMsg;


    @Data
    public static class LogisticsDetails {

        /**
         * 年月日
         */
        private String yearToDate;

        /**
         * 时分秒
         */
        private String hourMinuteSecond;

        /**
         * 星期几
         */
        private String whatDay;

        /**
         * 物流内容
         */
        private String context;

    }

}
