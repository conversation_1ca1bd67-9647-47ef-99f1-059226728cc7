package com.chinamobile.iot.sc.pojo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProductFlowInstanceConfigExample {
    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    public ProductFlowInstanceConfigExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    public ProductFlowInstanceConfigExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    public ProductFlowInstanceConfigExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        ProductFlowInstanceConfigExample example = new ProductFlowInstanceConfigExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    public ProductFlowInstanceConfigExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    public ProductFlowInstanceConfigExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdIsNull() {
            addCriterion("flow_instance_id is null");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdIsNotNull() {
            addCriterion("flow_instance_id is not null");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdEqualTo(String value) {
            addCriterion("flow_instance_id =", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("flow_instance_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotEqualTo(String value) {
            addCriterion("flow_instance_id <>", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("flow_instance_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdGreaterThan(String value) {
            addCriterion("flow_instance_id >", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("flow_instance_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdGreaterThanOrEqualTo(String value) {
            addCriterion("flow_instance_id >=", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("flow_instance_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLessThan(String value) {
            addCriterion("flow_instance_id <", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("flow_instance_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLessThanOrEqualTo(String value) {
            addCriterion("flow_instance_id <=", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("flow_instance_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLike(String value) {
            addCriterion("flow_instance_id like", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotLike(String value) {
            addCriterion("flow_instance_id not like", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdIn(List<String> values) {
            addCriterion("flow_instance_id in", values, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotIn(List<String> values) {
            addCriterion("flow_instance_id not in", values, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdBetween(String value1, String value2) {
            addCriterion("flow_instance_id between", value1, value2, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotBetween(String value1, String value2) {
            addCriterion("flow_instance_id not between", value1, value2, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowIdIsNull() {
            addCriterion("flow_id is null");
            return (Criteria) this;
        }

        public Criteria andFlowIdIsNotNull() {
            addCriterion("flow_id is not null");
            return (Criteria) this;
        }

        public Criteria andFlowIdEqualTo(String value) {
            addCriterion("flow_id =", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("flow_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdNotEqualTo(String value) {
            addCriterion("flow_id <>", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("flow_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThan(String value) {
            addCriterion("flow_id >", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("flow_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThanOrEqualTo(String value) {
            addCriterion("flow_id >=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("flow_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThan(String value) {
            addCriterion("flow_id <", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("flow_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThanOrEqualTo(String value) {
            addCriterion("flow_id <=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("flow_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdLike(String value) {
            addCriterion("flow_id like", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotLike(String value) {
            addCriterion("flow_id not like", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdIn(List<String> values) {
            addCriterion("flow_id in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotIn(List<String> values) {
            addCriterion("flow_id not in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdBetween(String value1, String value2) {
            addCriterion("flow_id between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotBetween(String value1, String value2) {
            addCriterion("flow_id not between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andConfigRemarkIsNull() {
            addCriterion("config_remark is null");
            return (Criteria) this;
        }

        public Criteria andConfigRemarkIsNotNull() {
            addCriterion("config_remark is not null");
            return (Criteria) this;
        }

        public Criteria andConfigRemarkEqualTo(String value) {
            addCriterion("config_remark =", value, "configRemark");
            return (Criteria) this;
        }

        public Criteria andConfigRemarkEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("config_remark = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigRemarkNotEqualTo(String value) {
            addCriterion("config_remark <>", value, "configRemark");
            return (Criteria) this;
        }

        public Criteria andConfigRemarkNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("config_remark <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigRemarkGreaterThan(String value) {
            addCriterion("config_remark >", value, "configRemark");
            return (Criteria) this;
        }

        public Criteria andConfigRemarkGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("config_remark > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("config_remark >=", value, "configRemark");
            return (Criteria) this;
        }

        public Criteria andConfigRemarkGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("config_remark >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigRemarkLessThan(String value) {
            addCriterion("config_remark <", value, "configRemark");
            return (Criteria) this;
        }

        public Criteria andConfigRemarkLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("config_remark < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigRemarkLessThanOrEqualTo(String value) {
            addCriterion("config_remark <=", value, "configRemark");
            return (Criteria) this;
        }

        public Criteria andConfigRemarkLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("config_remark <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigRemarkLike(String value) {
            addCriterion("config_remark like", value, "configRemark");
            return (Criteria) this;
        }

        public Criteria andConfigRemarkNotLike(String value) {
            addCriterion("config_remark not like", value, "configRemark");
            return (Criteria) this;
        }

        public Criteria andConfigRemarkIn(List<String> values) {
            addCriterion("config_remark in", values, "configRemark");
            return (Criteria) this;
        }

        public Criteria andConfigRemarkNotIn(List<String> values) {
            addCriterion("config_remark not in", values, "configRemark");
            return (Criteria) this;
        }

        public Criteria andConfigRemarkBetween(String value1, String value2) {
            addCriterion("config_remark between", value1, value2, "configRemark");
            return (Criteria) this;
        }

        public Criteria andConfigRemarkNotBetween(String value1, String value2) {
            addCriterion("config_remark not between", value1, value2, "configRemark");
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameIsNull() {
            addCriterion("standard_service_name is null");
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameIsNotNull() {
            addCriterion("standard_service_name is not null");
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameEqualTo(String value) {
            addCriterion("standard_service_name =", value, "standardServiceName");
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("standard_service_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameNotEqualTo(String value) {
            addCriterion("standard_service_name <>", value, "standardServiceName");
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("standard_service_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameGreaterThan(String value) {
            addCriterion("standard_service_name >", value, "standardServiceName");
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("standard_service_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameGreaterThanOrEqualTo(String value) {
            addCriterion("standard_service_name >=", value, "standardServiceName");
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("standard_service_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameLessThan(String value) {
            addCriterion("standard_service_name <", value, "standardServiceName");
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("standard_service_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameLessThanOrEqualTo(String value) {
            addCriterion("standard_service_name <=", value, "standardServiceName");
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("standard_service_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameLike(String value) {
            addCriterion("standard_service_name like", value, "standardServiceName");
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameNotLike(String value) {
            addCriterion("standard_service_name not like", value, "standardServiceName");
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameIn(List<String> values) {
            addCriterion("standard_service_name in", values, "standardServiceName");
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameNotIn(List<String> values) {
            addCriterion("standard_service_name not in", values, "standardServiceName");
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameBetween(String value1, String value2) {
            addCriterion("standard_service_name between", value1, value2, "standardServiceName");
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameNotBetween(String value1, String value2) {
            addCriterion("standard_service_name not between", value1, value2, "standardServiceName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameIsNull() {
            addCriterion("real_product_name is null");
            return (Criteria) this;
        }

        public Criteria andRealProductNameIsNotNull() {
            addCriterion("real_product_name is not null");
            return (Criteria) this;
        }

        public Criteria andRealProductNameEqualTo(String value) {
            addCriterion("real_product_name =", value, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("real_product_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRealProductNameNotEqualTo(String value) {
            addCriterion("real_product_name <>", value, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("real_product_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRealProductNameGreaterThan(String value) {
            addCriterion("real_product_name >", value, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("real_product_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRealProductNameGreaterThanOrEqualTo(String value) {
            addCriterion("real_product_name >=", value, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("real_product_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRealProductNameLessThan(String value) {
            addCriterion("real_product_name <", value, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("real_product_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRealProductNameLessThanOrEqualTo(String value) {
            addCriterion("real_product_name <=", value, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("real_product_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRealProductNameLike(String value) {
            addCriterion("real_product_name like", value, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameNotLike(String value) {
            addCriterion("real_product_name not like", value, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameIn(List<String> values) {
            addCriterion("real_product_name in", values, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameNotIn(List<String> values) {
            addCriterion("real_product_name not in", values, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameBetween(String value1, String value2) {
            addCriterion("real_product_name between", value1, value2, "realProductName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameNotBetween(String value1, String value2) {
            addCriterion("real_product_name not between", value1, value2, "realProductName");
            return (Criteria) this;
        }

        public Criteria andProductPropertyIsNull() {
            addCriterion("product_property is null");
            return (Criteria) this;
        }

        public Criteria andProductPropertyIsNotNull() {
            addCriterion("product_property is not null");
            return (Criteria) this;
        }

        public Criteria andProductPropertyEqualTo(String value) {
            addCriterion("product_property =", value, "productProperty");
            return (Criteria) this;
        }

        public Criteria andProductPropertyEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_property = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductPropertyNotEqualTo(String value) {
            addCriterion("product_property <>", value, "productProperty");
            return (Criteria) this;
        }

        public Criteria andProductPropertyNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_property <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductPropertyGreaterThan(String value) {
            addCriterion("product_property >", value, "productProperty");
            return (Criteria) this;
        }

        public Criteria andProductPropertyGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_property > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductPropertyGreaterThanOrEqualTo(String value) {
            addCriterion("product_property >=", value, "productProperty");
            return (Criteria) this;
        }

        public Criteria andProductPropertyGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_property >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductPropertyLessThan(String value) {
            addCriterion("product_property <", value, "productProperty");
            return (Criteria) this;
        }

        public Criteria andProductPropertyLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_property < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductPropertyLessThanOrEqualTo(String value) {
            addCriterion("product_property <=", value, "productProperty");
            return (Criteria) this;
        }

        public Criteria andProductPropertyLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_property <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductPropertyLike(String value) {
            addCriterion("product_property like", value, "productProperty");
            return (Criteria) this;
        }

        public Criteria andProductPropertyNotLike(String value) {
            addCriterion("product_property not like", value, "productProperty");
            return (Criteria) this;
        }

        public Criteria andProductPropertyIn(List<String> values) {
            addCriterion("product_property in", values, "productProperty");
            return (Criteria) this;
        }

        public Criteria andProductPropertyNotIn(List<String> values) {
            addCriterion("product_property not in", values, "productProperty");
            return (Criteria) this;
        }

        public Criteria andProductPropertyBetween(String value1, String value2) {
            addCriterion("product_property between", value1, value2, "productProperty");
            return (Criteria) this;
        }

        public Criteria andProductPropertyNotBetween(String value1, String value2) {
            addCriterion("product_property not between", value1, value2, "productProperty");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIsNull() {
            addCriterion("product_department is null");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIsNotNull() {
            addCriterion("product_department is not null");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentEqualTo(String value) {
            addCriterion("product_department =", value, "productDepartment");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_department = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNotEqualTo(String value) {
            addCriterion("product_department <>", value, "productDepartment");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_department <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentGreaterThan(String value) {
            addCriterion("product_department >", value, "productDepartment");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_department > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentGreaterThanOrEqualTo(String value) {
            addCriterion("product_department >=", value, "productDepartment");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_department >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentLessThan(String value) {
            addCriterion("product_department <", value, "productDepartment");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_department < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentLessThanOrEqualTo(String value) {
            addCriterion("product_department <=", value, "productDepartment");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_department <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentLike(String value) {
            addCriterion("product_department like", value, "productDepartment");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNotLike(String value) {
            addCriterion("product_department not like", value, "productDepartment");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIn(List<String> values) {
            addCriterion("product_department in", values, "productDepartment");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNotIn(List<String> values) {
            addCriterion("product_department not in", values, "productDepartment");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentBetween(String value1, String value2) {
            addCriterion("product_department between", value1, value2, "productDepartment");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNotBetween(String value1, String value2) {
            addCriterion("product_department not between", value1, value2, "productDepartment");
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameIsNull() {
            addCriterion("service_provider_name is null");
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameIsNotNull() {
            addCriterion("service_provider_name is not null");
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameEqualTo(String value) {
            addCriterion("service_provider_name =", value, "serviceProviderName");
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("service_provider_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameNotEqualTo(String value) {
            addCriterion("service_provider_name <>", value, "serviceProviderName");
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("service_provider_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameGreaterThan(String value) {
            addCriterion("service_provider_name >", value, "serviceProviderName");
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("service_provider_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameGreaterThanOrEqualTo(String value) {
            addCriterion("service_provider_name >=", value, "serviceProviderName");
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("service_provider_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameLessThan(String value) {
            addCriterion("service_provider_name <", value, "serviceProviderName");
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("service_provider_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameLessThanOrEqualTo(String value) {
            addCriterion("service_provider_name <=", value, "serviceProviderName");
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("service_provider_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameLike(String value) {
            addCriterion("service_provider_name like", value, "serviceProviderName");
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameNotLike(String value) {
            addCriterion("service_provider_name not like", value, "serviceProviderName");
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameIn(List<String> values) {
            addCriterion("service_provider_name in", values, "serviceProviderName");
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameNotIn(List<String> values) {
            addCriterion("service_provider_name not in", values, "serviceProviderName");
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameBetween(String value1, String value2) {
            addCriterion("service_provider_name between", value1, value2, "serviceProviderName");
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameNotBetween(String value1, String value2) {
            addCriterion("service_provider_name not between", value1, value2, "serviceProviderName");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountIsNull() {
            addCriterion("order_partner_master_account is null");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountIsNotNull() {
            addCriterion("order_partner_master_account is not null");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountEqualTo(String value) {
            addCriterion("order_partner_master_account =", value, "orderPartnerMasterAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("order_partner_master_account = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountNotEqualTo(String value) {
            addCriterion("order_partner_master_account <>", value, "orderPartnerMasterAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("order_partner_master_account <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountGreaterThan(String value) {
            addCriterion("order_partner_master_account >", value, "orderPartnerMasterAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("order_partner_master_account > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountGreaterThanOrEqualTo(String value) {
            addCriterion("order_partner_master_account >=", value, "orderPartnerMasterAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("order_partner_master_account >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountLessThan(String value) {
            addCriterion("order_partner_master_account <", value, "orderPartnerMasterAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("order_partner_master_account < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountLessThanOrEqualTo(String value) {
            addCriterion("order_partner_master_account <=", value, "orderPartnerMasterAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("order_partner_master_account <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountLike(String value) {
            addCriterion("order_partner_master_account like", value, "orderPartnerMasterAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountNotLike(String value) {
            addCriterion("order_partner_master_account not like", value, "orderPartnerMasterAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountIn(List<String> values) {
            addCriterion("order_partner_master_account in", values, "orderPartnerMasterAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountNotIn(List<String> values) {
            addCriterion("order_partner_master_account not in", values, "orderPartnerMasterAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountBetween(String value1, String value2) {
            addCriterion("order_partner_master_account between", value1, value2, "orderPartnerMasterAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountNotBetween(String value1, String value2) {
            addCriterion("order_partner_master_account not between", value1, value2, "orderPartnerMasterAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountIsNull() {
            addCriterion("order_partner_slave_account is null");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountIsNotNull() {
            addCriterion("order_partner_slave_account is not null");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountEqualTo(String value) {
            addCriterion("order_partner_slave_account =", value, "orderPartnerSlaveAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("order_partner_slave_account = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountNotEqualTo(String value) {
            addCriterion("order_partner_slave_account <>", value, "orderPartnerSlaveAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("order_partner_slave_account <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountGreaterThan(String value) {
            addCriterion("order_partner_slave_account >", value, "orderPartnerSlaveAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("order_partner_slave_account > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountGreaterThanOrEqualTo(String value) {
            addCriterion("order_partner_slave_account >=", value, "orderPartnerSlaveAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("order_partner_slave_account >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountLessThan(String value) {
            addCriterion("order_partner_slave_account <", value, "orderPartnerSlaveAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("order_partner_slave_account < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountLessThanOrEqualTo(String value) {
            addCriterion("order_partner_slave_account <=", value, "orderPartnerSlaveAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("order_partner_slave_account <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountLike(String value) {
            addCriterion("order_partner_slave_account like", value, "orderPartnerSlaveAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountNotLike(String value) {
            addCriterion("order_partner_slave_account not like", value, "orderPartnerSlaveAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountIn(List<String> values) {
            addCriterion("order_partner_slave_account in", values, "orderPartnerSlaveAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountNotIn(List<String> values) {
            addCriterion("order_partner_slave_account not in", values, "orderPartnerSlaveAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountBetween(String value1, String value2) {
            addCriterion("order_partner_slave_account between", value1, value2, "orderPartnerSlaveAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountNotBetween(String value1, String value2) {
            addCriterion("order_partner_slave_account not between", value1, value2, "orderPartnerSlaveAccount");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerIsNull() {
            addCriterion("before_sale_manager is null");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerIsNotNull() {
            addCriterion("before_sale_manager is not null");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerEqualTo(String value) {
            addCriterion("before_sale_manager =", value, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("before_sale_manager = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNotEqualTo(String value) {
            addCriterion("before_sale_manager <>", value, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("before_sale_manager <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerGreaterThan(String value) {
            addCriterion("before_sale_manager >", value, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("before_sale_manager > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerGreaterThanOrEqualTo(String value) {
            addCriterion("before_sale_manager >=", value, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("before_sale_manager >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerLessThan(String value) {
            addCriterion("before_sale_manager <", value, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("before_sale_manager < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerLessThanOrEqualTo(String value) {
            addCriterion("before_sale_manager <=", value, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("before_sale_manager <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerLike(String value) {
            addCriterion("before_sale_manager like", value, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNotLike(String value) {
            addCriterion("before_sale_manager not like", value, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerIn(List<String> values) {
            addCriterion("before_sale_manager in", values, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNotIn(List<String> values) {
            addCriterion("before_sale_manager not in", values, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerBetween(String value1, String value2) {
            addCriterion("before_sale_manager between", value1, value2, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerNotBetween(String value1, String value2) {
            addCriterion("before_sale_manager not between", value1, value2, "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andSendContactPersonIsNull() {
            addCriterion("send_contact_person is null");
            return (Criteria) this;
        }

        public Criteria andSendContactPersonIsNotNull() {
            addCriterion("send_contact_person is not null");
            return (Criteria) this;
        }

        public Criteria andSendContactPersonEqualTo(String value) {
            addCriterion("send_contact_person =", value, "sendContactPerson");
            return (Criteria) this;
        }

        public Criteria andSendContactPersonEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("send_contact_person = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSendContactPersonNotEqualTo(String value) {
            addCriterion("send_contact_person <>", value, "sendContactPerson");
            return (Criteria) this;
        }

        public Criteria andSendContactPersonNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("send_contact_person <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSendContactPersonGreaterThan(String value) {
            addCriterion("send_contact_person >", value, "sendContactPerson");
            return (Criteria) this;
        }

        public Criteria andSendContactPersonGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("send_contact_person > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSendContactPersonGreaterThanOrEqualTo(String value) {
            addCriterion("send_contact_person >=", value, "sendContactPerson");
            return (Criteria) this;
        }

        public Criteria andSendContactPersonGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("send_contact_person >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSendContactPersonLessThan(String value) {
            addCriterion("send_contact_person <", value, "sendContactPerson");
            return (Criteria) this;
        }

        public Criteria andSendContactPersonLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("send_contact_person < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSendContactPersonLessThanOrEqualTo(String value) {
            addCriterion("send_contact_person <=", value, "sendContactPerson");
            return (Criteria) this;
        }

        public Criteria andSendContactPersonLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("send_contact_person <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSendContactPersonLike(String value) {
            addCriterion("send_contact_person like", value, "sendContactPerson");
            return (Criteria) this;
        }

        public Criteria andSendContactPersonNotLike(String value) {
            addCriterion("send_contact_person not like", value, "sendContactPerson");
            return (Criteria) this;
        }

        public Criteria andSendContactPersonIn(List<String> values) {
            addCriterion("send_contact_person in", values, "sendContactPerson");
            return (Criteria) this;
        }

        public Criteria andSendContactPersonNotIn(List<String> values) {
            addCriterion("send_contact_person not in", values, "sendContactPerson");
            return (Criteria) this;
        }

        public Criteria andSendContactPersonBetween(String value1, String value2) {
            addCriterion("send_contact_person between", value1, value2, "sendContactPerson");
            return (Criteria) this;
        }

        public Criteria andSendContactPersonNotBetween(String value1, String value2) {
            addCriterion("send_contact_person not between", value1, value2, "sendContactPerson");
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonIsNull() {
            addCriterion("install_contact_person is null");
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonIsNotNull() {
            addCriterion("install_contact_person is not null");
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonEqualTo(String value) {
            addCriterion("install_contact_person =", value, "installContactPerson");
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("install_contact_person = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonNotEqualTo(String value) {
            addCriterion("install_contact_person <>", value, "installContactPerson");
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("install_contact_person <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonGreaterThan(String value) {
            addCriterion("install_contact_person >", value, "installContactPerson");
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("install_contact_person > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonGreaterThanOrEqualTo(String value) {
            addCriterion("install_contact_person >=", value, "installContactPerson");
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("install_contact_person >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonLessThan(String value) {
            addCriterion("install_contact_person <", value, "installContactPerson");
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("install_contact_person < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonLessThanOrEqualTo(String value) {
            addCriterion("install_contact_person <=", value, "installContactPerson");
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("install_contact_person <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonLike(String value) {
            addCriterion("install_contact_person like", value, "installContactPerson");
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonNotLike(String value) {
            addCriterion("install_contact_person not like", value, "installContactPerson");
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonIn(List<String> values) {
            addCriterion("install_contact_person in", values, "installContactPerson");
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonNotIn(List<String> values) {
            addCriterion("install_contact_person not in", values, "installContactPerson");
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonBetween(String value1, String value2) {
            addCriterion("install_contact_person between", value1, value2, "installContactPerson");
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonNotBetween(String value1, String value2) {
            addCriterion("install_contact_person not between", value1, value2, "installContactPerson");
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonIsNull() {
            addCriterion("iot_package_contact_person is null");
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonIsNotNull() {
            addCriterion("iot_package_contact_person is not null");
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonEqualTo(String value) {
            addCriterion("iot_package_contact_person =", value, "iotPackageContactPerson");
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("iot_package_contact_person = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonNotEqualTo(String value) {
            addCriterion("iot_package_contact_person <>", value, "iotPackageContactPerson");
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("iot_package_contact_person <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonGreaterThan(String value) {
            addCriterion("iot_package_contact_person >", value, "iotPackageContactPerson");
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("iot_package_contact_person > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonGreaterThanOrEqualTo(String value) {
            addCriterion("iot_package_contact_person >=", value, "iotPackageContactPerson");
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("iot_package_contact_person >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonLessThan(String value) {
            addCriterion("iot_package_contact_person <", value, "iotPackageContactPerson");
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("iot_package_contact_person < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonLessThanOrEqualTo(String value) {
            addCriterion("iot_package_contact_person <=", value, "iotPackageContactPerson");
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("iot_package_contact_person <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonLike(String value) {
            addCriterion("iot_package_contact_person like", value, "iotPackageContactPerson");
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonNotLike(String value) {
            addCriterion("iot_package_contact_person not like", value, "iotPackageContactPerson");
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonIn(List<String> values) {
            addCriterion("iot_package_contact_person in", values, "iotPackageContactPerson");
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonNotIn(List<String> values) {
            addCriterion("iot_package_contact_person not in", values, "iotPackageContactPerson");
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonBetween(String value1, String value2) {
            addCriterion("iot_package_contact_person between", value1, value2, "iotPackageContactPerson");
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonNotBetween(String value1, String value2) {
            addCriterion("iot_package_contact_person not between", value1, value2, "iotPackageContactPerson");
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonIsNull() {
            addCriterion("soft_auth_contact_person is null");
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonIsNotNull() {
            addCriterion("soft_auth_contact_person is not null");
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonEqualTo(String value) {
            addCriterion("soft_auth_contact_person =", value, "softAuthContactPerson");
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("soft_auth_contact_person = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonNotEqualTo(String value) {
            addCriterion("soft_auth_contact_person <>", value, "softAuthContactPerson");
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("soft_auth_contact_person <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonGreaterThan(String value) {
            addCriterion("soft_auth_contact_person >", value, "softAuthContactPerson");
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("soft_auth_contact_person > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonGreaterThanOrEqualTo(String value) {
            addCriterion("soft_auth_contact_person >=", value, "softAuthContactPerson");
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("soft_auth_contact_person >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonLessThan(String value) {
            addCriterion("soft_auth_contact_person <", value, "softAuthContactPerson");
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("soft_auth_contact_person < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonLessThanOrEqualTo(String value) {
            addCriterion("soft_auth_contact_person <=", value, "softAuthContactPerson");
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("soft_auth_contact_person <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonLike(String value) {
            addCriterion("soft_auth_contact_person like", value, "softAuthContactPerson");
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonNotLike(String value) {
            addCriterion("soft_auth_contact_person not like", value, "softAuthContactPerson");
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonIn(List<String> values) {
            addCriterion("soft_auth_contact_person in", values, "softAuthContactPerson");
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonNotIn(List<String> values) {
            addCriterion("soft_auth_contact_person not in", values, "softAuthContactPerson");
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonBetween(String value1, String value2) {
            addCriterion("soft_auth_contact_person between", value1, value2, "softAuthContactPerson");
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonNotBetween(String value1, String value2) {
            addCriterion("soft_auth_contact_person not between", value1, value2, "softAuthContactPerson");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonIsNull() {
            addCriterion("after_sale_contact_person is null");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonIsNotNull() {
            addCriterion("after_sale_contact_person is not null");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonEqualTo(String value) {
            addCriterion("after_sale_contact_person =", value, "afterSaleContactPerson");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("after_sale_contact_person = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonNotEqualTo(String value) {
            addCriterion("after_sale_contact_person <>", value, "afterSaleContactPerson");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("after_sale_contact_person <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonGreaterThan(String value) {
            addCriterion("after_sale_contact_person >", value, "afterSaleContactPerson");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("after_sale_contact_person > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonGreaterThanOrEqualTo(String value) {
            addCriterion("after_sale_contact_person >=", value, "afterSaleContactPerson");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("after_sale_contact_person >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonLessThan(String value) {
            addCriterion("after_sale_contact_person <", value, "afterSaleContactPerson");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("after_sale_contact_person < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonLessThanOrEqualTo(String value) {
            addCriterion("after_sale_contact_person <=", value, "afterSaleContactPerson");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("after_sale_contact_person <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonLike(String value) {
            addCriterion("after_sale_contact_person like", value, "afterSaleContactPerson");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonNotLike(String value) {
            addCriterion("after_sale_contact_person not like", value, "afterSaleContactPerson");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonIn(List<String> values) {
            addCriterion("after_sale_contact_person in", values, "afterSaleContactPerson");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonNotIn(List<String> values) {
            addCriterion("after_sale_contact_person not in", values, "afterSaleContactPerson");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonBetween(String value1, String value2) {
            addCriterion("after_sale_contact_person between", value1, value2, "afterSaleContactPerson");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonNotBetween(String value1, String value2) {
            addCriterion("after_sale_contact_person not between", value1, value2, "afterSaleContactPerson");
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleIsNull() {
            addCriterion("after_market_rule is null");
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleIsNotNull() {
            addCriterion("after_market_rule is not null");
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleEqualTo(String value) {
            addCriterion("after_market_rule =", value, "afterMarketRule");
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("after_market_rule = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleNotEqualTo(String value) {
            addCriterion("after_market_rule <>", value, "afterMarketRule");
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("after_market_rule <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleGreaterThan(String value) {
            addCriterion("after_market_rule >", value, "afterMarketRule");
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("after_market_rule > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleGreaterThanOrEqualTo(String value) {
            addCriterion("after_market_rule >=", value, "afterMarketRule");
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("after_market_rule >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleLessThan(String value) {
            addCriterion("after_market_rule <", value, "afterMarketRule");
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("after_market_rule < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleLessThanOrEqualTo(String value) {
            addCriterion("after_market_rule <=", value, "afterMarketRule");
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("after_market_rule <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleLike(String value) {
            addCriterion("after_market_rule like", value, "afterMarketRule");
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleNotLike(String value) {
            addCriterion("after_market_rule not like", value, "afterMarketRule");
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleIn(List<String> values) {
            addCriterion("after_market_rule in", values, "afterMarketRule");
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleNotIn(List<String> values) {
            addCriterion("after_market_rule not in", values, "afterMarketRule");
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleBetween(String value1, String value2) {
            addCriterion("after_market_rule between", value1, value2, "afterMarketRule");
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleNotBetween(String value1, String value2) {
            addCriterion("after_market_rule not between", value1, value2, "afterMarketRule");
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoIsNull() {
            addCriterion("repair_contact_info is null");
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoIsNotNull() {
            addCriterion("repair_contact_info is not null");
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoEqualTo(String value) {
            addCriterion("repair_contact_info =", value, "repairContactInfo");
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("repair_contact_info = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoNotEqualTo(String value) {
            addCriterion("repair_contact_info <>", value, "repairContactInfo");
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("repair_contact_info <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoGreaterThan(String value) {
            addCriterion("repair_contact_info >", value, "repairContactInfo");
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("repair_contact_info > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoGreaterThanOrEqualTo(String value) {
            addCriterion("repair_contact_info >=", value, "repairContactInfo");
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("repair_contact_info >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoLessThan(String value) {
            addCriterion("repair_contact_info <", value, "repairContactInfo");
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("repair_contact_info < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoLessThanOrEqualTo(String value) {
            addCriterion("repair_contact_info <=", value, "repairContactInfo");
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("repair_contact_info <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoLike(String value) {
            addCriterion("repair_contact_info like", value, "repairContactInfo");
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoNotLike(String value) {
            addCriterion("repair_contact_info not like", value, "repairContactInfo");
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoIn(List<String> values) {
            addCriterion("repair_contact_info in", values, "repairContactInfo");
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoNotIn(List<String> values) {
            addCriterion("repair_contact_info not in", values, "repairContactInfo");
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoBetween(String value1, String value2) {
            addCriterion("repair_contact_info between", value1, value2, "repairContactInfo");
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoNotBetween(String value1, String value2) {
            addCriterion("repair_contact_info not between", value1, value2, "repairContactInfo");
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoIsNull() {
            addCriterion("return_contact_info is null");
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoIsNotNull() {
            addCriterion("return_contact_info is not null");
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoEqualTo(String value) {
            addCriterion("return_contact_info =", value, "returnContactInfo");
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("return_contact_info = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoNotEqualTo(String value) {
            addCriterion("return_contact_info <>", value, "returnContactInfo");
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("return_contact_info <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoGreaterThan(String value) {
            addCriterion("return_contact_info >", value, "returnContactInfo");
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("return_contact_info > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoGreaterThanOrEqualTo(String value) {
            addCriterion("return_contact_info >=", value, "returnContactInfo");
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("return_contact_info >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoLessThan(String value) {
            addCriterion("return_contact_info <", value, "returnContactInfo");
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("return_contact_info < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoLessThanOrEqualTo(String value) {
            addCriterion("return_contact_info <=", value, "returnContactInfo");
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("return_contact_info <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoLike(String value) {
            addCriterion("return_contact_info like", value, "returnContactInfo");
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoNotLike(String value) {
            addCriterion("return_contact_info not like", value, "returnContactInfo");
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoIn(List<String> values) {
            addCriterion("return_contact_info in", values, "returnContactInfo");
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoNotIn(List<String> values) {
            addCriterion("return_contact_info not in", values, "returnContactInfo");
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoBetween(String value1, String value2) {
            addCriterion("return_contact_info between", value1, value2, "returnContactInfo");
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoNotBetween(String value1, String value2) {
            addCriterion("return_contact_info not between", value1, value2, "returnContactInfo");
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoIsNull() {
            addCriterion("product_company_info is null");
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoIsNotNull() {
            addCriterion("product_company_info is not null");
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoEqualTo(String value) {
            addCriterion("product_company_info =", value, "productCompanyInfo");
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_company_info = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoNotEqualTo(String value) {
            addCriterion("product_company_info <>", value, "productCompanyInfo");
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_company_info <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoGreaterThan(String value) {
            addCriterion("product_company_info >", value, "productCompanyInfo");
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_company_info > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoGreaterThanOrEqualTo(String value) {
            addCriterion("product_company_info >=", value, "productCompanyInfo");
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_company_info >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoLessThan(String value) {
            addCriterion("product_company_info <", value, "productCompanyInfo");
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_company_info < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoLessThanOrEqualTo(String value) {
            addCriterion("product_company_info <=", value, "productCompanyInfo");
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_company_info <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoLike(String value) {
            addCriterion("product_company_info like", value, "productCompanyInfo");
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoNotLike(String value) {
            addCriterion("product_company_info not like", value, "productCompanyInfo");
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoIn(List<String> values) {
            addCriterion("product_company_info in", values, "productCompanyInfo");
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoNotIn(List<String> values) {
            addCriterion("product_company_info not in", values, "productCompanyInfo");
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoBetween(String value1, String value2) {
            addCriterion("product_company_info between", value1, value2, "productCompanyInfo");
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoNotBetween(String value1, String value2) {
            addCriterion("product_company_info not between", value1, value2, "productCompanyInfo");
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodIsNull() {
            addCriterion("product_communication_method is null");
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodIsNotNull() {
            addCriterion("product_communication_method is not null");
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodEqualTo(String value) {
            addCriterion("product_communication_method =", value, "productCommunicationMethod");
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_communication_method = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodNotEqualTo(String value) {
            addCriterion("product_communication_method <>", value, "productCommunicationMethod");
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_communication_method <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodGreaterThan(String value) {
            addCriterion("product_communication_method >", value, "productCommunicationMethod");
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_communication_method > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodGreaterThanOrEqualTo(String value) {
            addCriterion("product_communication_method >=", value, "productCommunicationMethod");
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_communication_method >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodLessThan(String value) {
            addCriterion("product_communication_method <", value, "productCommunicationMethod");
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_communication_method < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodLessThanOrEqualTo(String value) {
            addCriterion("product_communication_method <=", value, "productCommunicationMethod");
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_communication_method <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodLike(String value) {
            addCriterion("product_communication_method like", value, "productCommunicationMethod");
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodNotLike(String value) {
            addCriterion("product_communication_method not like", value, "productCommunicationMethod");
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodIn(List<String> values) {
            addCriterion("product_communication_method in", values, "productCommunicationMethod");
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodNotIn(List<String> values) {
            addCriterion("product_communication_method not in", values, "productCommunicationMethod");
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodBetween(String value1, String value2) {
            addCriterion("product_communication_method between", value1, value2, "productCommunicationMethod");
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodNotBetween(String value1, String value2) {
            addCriterion("product_communication_method not between", value1, value2, "productCommunicationMethod");
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoIsNull() {
            addCriterion("iot_package_info is null");
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoIsNotNull() {
            addCriterion("iot_package_info is not null");
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoEqualTo(String value) {
            addCriterion("iot_package_info =", value, "iotPackageInfo");
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("iot_package_info = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoNotEqualTo(String value) {
            addCriterion("iot_package_info <>", value, "iotPackageInfo");
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("iot_package_info <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoGreaterThan(String value) {
            addCriterion("iot_package_info >", value, "iotPackageInfo");
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("iot_package_info > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoGreaterThanOrEqualTo(String value) {
            addCriterion("iot_package_info >=", value, "iotPackageInfo");
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("iot_package_info >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoLessThan(String value) {
            addCriterion("iot_package_info <", value, "iotPackageInfo");
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("iot_package_info < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoLessThanOrEqualTo(String value) {
            addCriterion("iot_package_info <=", value, "iotPackageInfo");
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("iot_package_info <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoLike(String value) {
            addCriterion("iot_package_info like", value, "iotPackageInfo");
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoNotLike(String value) {
            addCriterion("iot_package_info not like", value, "iotPackageInfo");
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoIn(List<String> values) {
            addCriterion("iot_package_info in", values, "iotPackageInfo");
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoNotIn(List<String> values) {
            addCriterion("iot_package_info not in", values, "iotPackageInfo");
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoBetween(String value1, String value2) {
            addCriterion("iot_package_info between", value1, value2, "iotPackageInfo");
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoNotBetween(String value1, String value2) {
            addCriterion("iot_package_info not between", value1, value2, "iotPackageInfo");
            return (Criteria) this;
        }

        public Criteria andHardwareSendListIsNull() {
            addCriterion("hardware_send_list is null");
            return (Criteria) this;
        }

        public Criteria andHardwareSendListIsNotNull() {
            addCriterion("hardware_send_list is not null");
            return (Criteria) this;
        }

        public Criteria andHardwareSendListEqualTo(String value) {
            addCriterion("hardware_send_list =", value, "hardwareSendList");
            return (Criteria) this;
        }

        public Criteria andHardwareSendListEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("hardware_send_list = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareSendListNotEqualTo(String value) {
            addCriterion("hardware_send_list <>", value, "hardwareSendList");
            return (Criteria) this;
        }

        public Criteria andHardwareSendListNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("hardware_send_list <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareSendListGreaterThan(String value) {
            addCriterion("hardware_send_list >", value, "hardwareSendList");
            return (Criteria) this;
        }

        public Criteria andHardwareSendListGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("hardware_send_list > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareSendListGreaterThanOrEqualTo(String value) {
            addCriterion("hardware_send_list >=", value, "hardwareSendList");
            return (Criteria) this;
        }

        public Criteria andHardwareSendListGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("hardware_send_list >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareSendListLessThan(String value) {
            addCriterion("hardware_send_list <", value, "hardwareSendList");
            return (Criteria) this;
        }

        public Criteria andHardwareSendListLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("hardware_send_list < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareSendListLessThanOrEqualTo(String value) {
            addCriterion("hardware_send_list <=", value, "hardwareSendList");
            return (Criteria) this;
        }

        public Criteria andHardwareSendListLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("hardware_send_list <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareSendListLike(String value) {
            addCriterion("hardware_send_list like", value, "hardwareSendList");
            return (Criteria) this;
        }

        public Criteria andHardwareSendListNotLike(String value) {
            addCriterion("hardware_send_list not like", value, "hardwareSendList");
            return (Criteria) this;
        }

        public Criteria andHardwareSendListIn(List<String> values) {
            addCriterion("hardware_send_list in", values, "hardwareSendList");
            return (Criteria) this;
        }

        public Criteria andHardwareSendListNotIn(List<String> values) {
            addCriterion("hardware_send_list not in", values, "hardwareSendList");
            return (Criteria) this;
        }

        public Criteria andHardwareSendListBetween(String value1, String value2) {
            addCriterion("hardware_send_list between", value1, value2, "hardwareSendList");
            return (Criteria) this;
        }

        public Criteria andHardwareSendListNotBetween(String value1, String value2) {
            addCriterion("hardware_send_list not between", value1, value2, "hardwareSendList");
            return (Criteria) this;
        }

        public Criteria andProductParamInfoIsNull() {
            addCriterion("product_param_info is null");
            return (Criteria) this;
        }

        public Criteria andProductParamInfoIsNotNull() {
            addCriterion("product_param_info is not null");
            return (Criteria) this;
        }

        public Criteria andProductParamInfoEqualTo(String value) {
            addCriterion("product_param_info =", value, "productParamInfo");
            return (Criteria) this;
        }

        public Criteria andProductParamInfoEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_param_info = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductParamInfoNotEqualTo(String value) {
            addCriterion("product_param_info <>", value, "productParamInfo");
            return (Criteria) this;
        }

        public Criteria andProductParamInfoNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_param_info <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductParamInfoGreaterThan(String value) {
            addCriterion("product_param_info >", value, "productParamInfo");
            return (Criteria) this;
        }

        public Criteria andProductParamInfoGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_param_info > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductParamInfoGreaterThanOrEqualTo(String value) {
            addCriterion("product_param_info >=", value, "productParamInfo");
            return (Criteria) this;
        }

        public Criteria andProductParamInfoGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_param_info >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductParamInfoLessThan(String value) {
            addCriterion("product_param_info <", value, "productParamInfo");
            return (Criteria) this;
        }

        public Criteria andProductParamInfoLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_param_info < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductParamInfoLessThanOrEqualTo(String value) {
            addCriterion("product_param_info <=", value, "productParamInfo");
            return (Criteria) this;
        }

        public Criteria andProductParamInfoLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_param_info <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductParamInfoLike(String value) {
            addCriterion("product_param_info like", value, "productParamInfo");
            return (Criteria) this;
        }

        public Criteria andProductParamInfoNotLike(String value) {
            addCriterion("product_param_info not like", value, "productParamInfo");
            return (Criteria) this;
        }

        public Criteria andProductParamInfoIn(List<String> values) {
            addCriterion("product_param_info in", values, "productParamInfo");
            return (Criteria) this;
        }

        public Criteria andProductParamInfoNotIn(List<String> values) {
            addCriterion("product_param_info not in", values, "productParamInfo");
            return (Criteria) this;
        }

        public Criteria andProductParamInfoBetween(String value1, String value2) {
            addCriterion("product_param_info between", value1, value2, "productParamInfo");
            return (Criteria) this;
        }

        public Criteria andProductParamInfoNotBetween(String value1, String value2) {
            addCriterion("product_param_info not between", value1, value2, "productParamInfo");
            return (Criteria) this;
        }

        public Criteria andProductSendAddressIsNull() {
            addCriterion("product_send_address is null");
            return (Criteria) this;
        }

        public Criteria andProductSendAddressIsNotNull() {
            addCriterion("product_send_address is not null");
            return (Criteria) this;
        }

        public Criteria andProductSendAddressEqualTo(String value) {
            addCriterion("product_send_address =", value, "productSendAddress");
            return (Criteria) this;
        }

        public Criteria andProductSendAddressEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_send_address = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSendAddressNotEqualTo(String value) {
            addCriterion("product_send_address <>", value, "productSendAddress");
            return (Criteria) this;
        }

        public Criteria andProductSendAddressNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_send_address <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSendAddressGreaterThan(String value) {
            addCriterion("product_send_address >", value, "productSendAddress");
            return (Criteria) this;
        }

        public Criteria andProductSendAddressGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_send_address > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSendAddressGreaterThanOrEqualTo(String value) {
            addCriterion("product_send_address >=", value, "productSendAddress");
            return (Criteria) this;
        }

        public Criteria andProductSendAddressGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_send_address >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSendAddressLessThan(String value) {
            addCriterion("product_send_address <", value, "productSendAddress");
            return (Criteria) this;
        }

        public Criteria andProductSendAddressLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_send_address < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSendAddressLessThanOrEqualTo(String value) {
            addCriterion("product_send_address <=", value, "productSendAddress");
            return (Criteria) this;
        }

        public Criteria andProductSendAddressLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_send_address <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSendAddressLike(String value) {
            addCriterion("product_send_address like", value, "productSendAddress");
            return (Criteria) this;
        }

        public Criteria andProductSendAddressNotLike(String value) {
            addCriterion("product_send_address not like", value, "productSendAddress");
            return (Criteria) this;
        }

        public Criteria andProductSendAddressIn(List<String> values) {
            addCriterion("product_send_address in", values, "productSendAddress");
            return (Criteria) this;
        }

        public Criteria andProductSendAddressNotIn(List<String> values) {
            addCriterion("product_send_address not in", values, "productSendAddress");
            return (Criteria) this;
        }

        public Criteria andProductSendAddressBetween(String value1, String value2) {
            addCriterion("product_send_address between", value1, value2, "productSendAddress");
            return (Criteria) this;
        }

        public Criteria andProductSendAddressNotBetween(String value1, String value2) {
            addCriterion("product_send_address not between", value1, value2, "productSendAddress");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressIsNull() {
            addCriterion("hardware_express is null");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressIsNotNull() {
            addCriterion("hardware_express is not null");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressEqualTo(String value) {
            addCriterion("hardware_express =", value, "hardwareExpress");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("hardware_express = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareExpressNotEqualTo(String value) {
            addCriterion("hardware_express <>", value, "hardwareExpress");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("hardware_express <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareExpressGreaterThan(String value) {
            addCriterion("hardware_express >", value, "hardwareExpress");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("hardware_express > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareExpressGreaterThanOrEqualTo(String value) {
            addCriterion("hardware_express >=", value, "hardwareExpress");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("hardware_express >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareExpressLessThan(String value) {
            addCriterion("hardware_express <", value, "hardwareExpress");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("hardware_express < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareExpressLessThanOrEqualTo(String value) {
            addCriterion("hardware_express <=", value, "hardwareExpress");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("hardware_express <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwareExpressLike(String value) {
            addCriterion("hardware_express like", value, "hardwareExpress");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressNotLike(String value) {
            addCriterion("hardware_express not like", value, "hardwareExpress");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressIn(List<String> values) {
            addCriterion("hardware_express in", values, "hardwareExpress");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressNotIn(List<String> values) {
            addCriterion("hardware_express not in", values, "hardwareExpress");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressBetween(String value1, String value2) {
            addCriterion("hardware_express between", value1, value2, "hardwareExpress");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressNotBetween(String value1, String value2) {
            addCriterion("hardware_express not between", value1, value2, "hardwareExpress");
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoIsNull() {
            addCriterion("product_send_time_info is null");
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoIsNotNull() {
            addCriterion("product_send_time_info is not null");
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoEqualTo(String value) {
            addCriterion("product_send_time_info =", value, "productSendTimeInfo");
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_send_time_info = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoNotEqualTo(String value) {
            addCriterion("product_send_time_info <>", value, "productSendTimeInfo");
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_send_time_info <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoGreaterThan(String value) {
            addCriterion("product_send_time_info >", value, "productSendTimeInfo");
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_send_time_info > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoGreaterThanOrEqualTo(String value) {
            addCriterion("product_send_time_info >=", value, "productSendTimeInfo");
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_send_time_info >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoLessThan(String value) {
            addCriterion("product_send_time_info <", value, "productSendTimeInfo");
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_send_time_info < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoLessThanOrEqualTo(String value) {
            addCriterion("product_send_time_info <=", value, "productSendTimeInfo");
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_send_time_info <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoLike(String value) {
            addCriterion("product_send_time_info like", value, "productSendTimeInfo");
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoNotLike(String value) {
            addCriterion("product_send_time_info not like", value, "productSendTimeInfo");
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoIn(List<String> values) {
            addCriterion("product_send_time_info in", values, "productSendTimeInfo");
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoNotIn(List<String> values) {
            addCriterion("product_send_time_info not in", values, "productSendTimeInfo");
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoBetween(String value1, String value2) {
            addCriterion("product_send_time_info between", value1, value2, "productSendTimeInfo");
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoNotBetween(String value1, String value2) {
            addCriterion("product_send_time_info not between", value1, value2, "productSendTimeInfo");
            return (Criteria) this;
        }

        public Criteria andProductUseConditionIsNull() {
            addCriterion("product_use_condition is null");
            return (Criteria) this;
        }

        public Criteria andProductUseConditionIsNotNull() {
            addCriterion("product_use_condition is not null");
            return (Criteria) this;
        }

        public Criteria andProductUseConditionEqualTo(String value) {
            addCriterion("product_use_condition =", value, "productUseCondition");
            return (Criteria) this;
        }

        public Criteria andProductUseConditionEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_use_condition = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductUseConditionNotEqualTo(String value) {
            addCriterion("product_use_condition <>", value, "productUseCondition");
            return (Criteria) this;
        }

        public Criteria andProductUseConditionNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_use_condition <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductUseConditionGreaterThan(String value) {
            addCriterion("product_use_condition >", value, "productUseCondition");
            return (Criteria) this;
        }

        public Criteria andProductUseConditionGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_use_condition > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductUseConditionGreaterThanOrEqualTo(String value) {
            addCriterion("product_use_condition >=", value, "productUseCondition");
            return (Criteria) this;
        }

        public Criteria andProductUseConditionGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_use_condition >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductUseConditionLessThan(String value) {
            addCriterion("product_use_condition <", value, "productUseCondition");
            return (Criteria) this;
        }

        public Criteria andProductUseConditionLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_use_condition < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductUseConditionLessThanOrEqualTo(String value) {
            addCriterion("product_use_condition <=", value, "productUseCondition");
            return (Criteria) this;
        }

        public Criteria andProductUseConditionLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("product_use_condition <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductUseConditionLike(String value) {
            addCriterion("product_use_condition like", value, "productUseCondition");
            return (Criteria) this;
        }

        public Criteria andProductUseConditionNotLike(String value) {
            addCriterion("product_use_condition not like", value, "productUseCondition");
            return (Criteria) this;
        }

        public Criteria andProductUseConditionIn(List<String> values) {
            addCriterion("product_use_condition in", values, "productUseCondition");
            return (Criteria) this;
        }

        public Criteria andProductUseConditionNotIn(List<String> values) {
            addCriterion("product_use_condition not in", values, "productUseCondition");
            return (Criteria) this;
        }

        public Criteria andProductUseConditionBetween(String value1, String value2) {
            addCriterion("product_use_condition between", value1, value2, "productUseCondition");
            return (Criteria) this;
        }

        public Criteria andProductUseConditionNotBetween(String value1, String value2) {
            addCriterion("product_use_condition not between", value1, value2, "productUseCondition");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoIsNull() {
            addCriterion("soft_platform_info is null");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoIsNotNull() {
            addCriterion("soft_platform_info is not null");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoEqualTo(String value) {
            addCriterion("soft_platform_info =", value, "softPlatformInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("soft_platform_info = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoNotEqualTo(String value) {
            addCriterion("soft_platform_info <>", value, "softPlatformInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("soft_platform_info <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoGreaterThan(String value) {
            addCriterion("soft_platform_info >", value, "softPlatformInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("soft_platform_info > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoGreaterThanOrEqualTo(String value) {
            addCriterion("soft_platform_info >=", value, "softPlatformInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("soft_platform_info >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoLessThan(String value) {
            addCriterion("soft_platform_info <", value, "softPlatformInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("soft_platform_info < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoLessThanOrEqualTo(String value) {
            addCriterion("soft_platform_info <=", value, "softPlatformInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("soft_platform_info <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoLike(String value) {
            addCriterion("soft_platform_info like", value, "softPlatformInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoNotLike(String value) {
            addCriterion("soft_platform_info not like", value, "softPlatformInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoIn(List<String> values) {
            addCriterion("soft_platform_info in", values, "softPlatformInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoNotIn(List<String> values) {
            addCriterion("soft_platform_info not in", values, "softPlatformInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoBetween(String value1, String value2) {
            addCriterion("soft_platform_info between", value1, value2, "softPlatformInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoNotBetween(String value1, String value2) {
            addCriterion("soft_platform_info not between", value1, value2, "softPlatformInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoIsNull() {
            addCriterion("soft_platform_download_info is null");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoIsNotNull() {
            addCriterion("soft_platform_download_info is not null");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoEqualTo(String value) {
            addCriterion("soft_platform_download_info =", value, "softPlatformDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("soft_platform_download_info = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoNotEqualTo(String value) {
            addCriterion("soft_platform_download_info <>", value, "softPlatformDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("soft_platform_download_info <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoGreaterThan(String value) {
            addCriterion("soft_platform_download_info >", value, "softPlatformDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("soft_platform_download_info > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoGreaterThanOrEqualTo(String value) {
            addCriterion("soft_platform_download_info >=", value, "softPlatformDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("soft_platform_download_info >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoLessThan(String value) {
            addCriterion("soft_platform_download_info <", value, "softPlatformDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("soft_platform_download_info < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoLessThanOrEqualTo(String value) {
            addCriterion("soft_platform_download_info <=", value, "softPlatformDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("soft_platform_download_info <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoLike(String value) {
            addCriterion("soft_platform_download_info like", value, "softPlatformDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoNotLike(String value) {
            addCriterion("soft_platform_download_info not like", value, "softPlatformDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoIn(List<String> values) {
            addCriterion("soft_platform_download_info in", values, "softPlatformDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoNotIn(List<String> values) {
            addCriterion("soft_platform_download_info not in", values, "softPlatformDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoBetween(String value1, String value2) {
            addCriterion("soft_platform_download_info between", value1, value2, "softPlatformDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoNotBetween(String value1, String value2) {
            addCriterion("soft_platform_download_info not between", value1, value2, "softPlatformDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andAppInfoIsNull() {
            addCriterion("app_info is null");
            return (Criteria) this;
        }

        public Criteria andAppInfoIsNotNull() {
            addCriterion("app_info is not null");
            return (Criteria) this;
        }

        public Criteria andAppInfoEqualTo(String value) {
            addCriterion("app_info =", value, "appInfo");
            return (Criteria) this;
        }

        public Criteria andAppInfoEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("app_info = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppInfoNotEqualTo(String value) {
            addCriterion("app_info <>", value, "appInfo");
            return (Criteria) this;
        }

        public Criteria andAppInfoNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("app_info <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppInfoGreaterThan(String value) {
            addCriterion("app_info >", value, "appInfo");
            return (Criteria) this;
        }

        public Criteria andAppInfoGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("app_info > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppInfoGreaterThanOrEqualTo(String value) {
            addCriterion("app_info >=", value, "appInfo");
            return (Criteria) this;
        }

        public Criteria andAppInfoGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("app_info >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppInfoLessThan(String value) {
            addCriterion("app_info <", value, "appInfo");
            return (Criteria) this;
        }

        public Criteria andAppInfoLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("app_info < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppInfoLessThanOrEqualTo(String value) {
            addCriterion("app_info <=", value, "appInfo");
            return (Criteria) this;
        }

        public Criteria andAppInfoLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("app_info <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppInfoLike(String value) {
            addCriterion("app_info like", value, "appInfo");
            return (Criteria) this;
        }

        public Criteria andAppInfoNotLike(String value) {
            addCriterion("app_info not like", value, "appInfo");
            return (Criteria) this;
        }

        public Criteria andAppInfoIn(List<String> values) {
            addCriterion("app_info in", values, "appInfo");
            return (Criteria) this;
        }

        public Criteria andAppInfoNotIn(List<String> values) {
            addCriterion("app_info not in", values, "appInfo");
            return (Criteria) this;
        }

        public Criteria andAppInfoBetween(String value1, String value2) {
            addCriterion("app_info between", value1, value2, "appInfo");
            return (Criteria) this;
        }

        public Criteria andAppInfoNotBetween(String value1, String value2) {
            addCriterion("app_info not between", value1, value2, "appInfo");
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoIsNull() {
            addCriterion("app_download_info is null");
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoIsNotNull() {
            addCriterion("app_download_info is not null");
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoEqualTo(String value) {
            addCriterion("app_download_info =", value, "appDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("app_download_info = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoNotEqualTo(String value) {
            addCriterion("app_download_info <>", value, "appDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("app_download_info <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoGreaterThan(String value) {
            addCriterion("app_download_info >", value, "appDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("app_download_info > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoGreaterThanOrEqualTo(String value) {
            addCriterion("app_download_info >=", value, "appDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("app_download_info >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoLessThan(String value) {
            addCriterion("app_download_info <", value, "appDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("app_download_info < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoLessThanOrEqualTo(String value) {
            addCriterion("app_download_info <=", value, "appDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("app_download_info <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoLike(String value) {
            addCriterion("app_download_info like", value, "appDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoNotLike(String value) {
            addCriterion("app_download_info not like", value, "appDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoIn(List<String> values) {
            addCriterion("app_download_info in", values, "appDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoNotIn(List<String> values) {
            addCriterion("app_download_info not in", values, "appDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoBetween(String value1, String value2) {
            addCriterion("app_download_info between", value1, value2, "appDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoNotBetween(String value1, String value2) {
            addCriterion("app_download_info not between", value1, value2, "appDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andInstallInfoIsNull() {
            addCriterion("install_info is null");
            return (Criteria) this;
        }

        public Criteria andInstallInfoIsNotNull() {
            addCriterion("install_info is not null");
            return (Criteria) this;
        }

        public Criteria andInstallInfoEqualTo(String value) {
            addCriterion("install_info =", value, "installInfo");
            return (Criteria) this;
        }

        public Criteria andInstallInfoEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("install_info = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallInfoNotEqualTo(String value) {
            addCriterion("install_info <>", value, "installInfo");
            return (Criteria) this;
        }

        public Criteria andInstallInfoNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("install_info <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallInfoGreaterThan(String value) {
            addCriterion("install_info >", value, "installInfo");
            return (Criteria) this;
        }

        public Criteria andInstallInfoGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("install_info > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallInfoGreaterThanOrEqualTo(String value) {
            addCriterion("install_info >=", value, "installInfo");
            return (Criteria) this;
        }

        public Criteria andInstallInfoGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("install_info >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallInfoLessThan(String value) {
            addCriterion("install_info <", value, "installInfo");
            return (Criteria) this;
        }

        public Criteria andInstallInfoLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("install_info < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallInfoLessThanOrEqualTo(String value) {
            addCriterion("install_info <=", value, "installInfo");
            return (Criteria) this;
        }

        public Criteria andInstallInfoLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("install_info <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallInfoLike(String value) {
            addCriterion("install_info like", value, "installInfo");
            return (Criteria) this;
        }

        public Criteria andInstallInfoNotLike(String value) {
            addCriterion("install_info not like", value, "installInfo");
            return (Criteria) this;
        }

        public Criteria andInstallInfoIn(List<String> values) {
            addCriterion("install_info in", values, "installInfo");
            return (Criteria) this;
        }

        public Criteria andInstallInfoNotIn(List<String> values) {
            addCriterion("install_info not in", values, "installInfo");
            return (Criteria) this;
        }

        public Criteria andInstallInfoBetween(String value1, String value2) {
            addCriterion("install_info between", value1, value2, "installInfo");
            return (Criteria) this;
        }

        public Criteria andInstallInfoNotBetween(String value1, String value2) {
            addCriterion("install_info not between", value1, value2, "installInfo");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(ProductFlowInstanceConfig.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLikeInsensitive(String value) {
            addCriterion("upper(flow_instance_id) like", value.toUpperCase(), "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLikeInsensitive(String value) {
            addCriterion("upper(flow_id) like", value.toUpperCase(), "flowId");
            return (Criteria) this;
        }

        public Criteria andConfigRemarkLikeInsensitive(String value) {
            addCriterion("upper(config_remark) like", value.toUpperCase(), "configRemark");
            return (Criteria) this;
        }

        public Criteria andStandardServiceNameLikeInsensitive(String value) {
            addCriterion("upper(standard_service_name) like", value.toUpperCase(), "standardServiceName");
            return (Criteria) this;
        }

        public Criteria andRealProductNameLikeInsensitive(String value) {
            addCriterion("upper(real_product_name) like", value.toUpperCase(), "realProductName");
            return (Criteria) this;
        }

        public Criteria andProductPropertyLikeInsensitive(String value) {
            addCriterion("upper(product_property) like", value.toUpperCase(), "productProperty");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentLikeInsensitive(String value) {
            addCriterion("upper(product_department) like", value.toUpperCase(), "productDepartment");
            return (Criteria) this;
        }

        public Criteria andServiceProviderNameLikeInsensitive(String value) {
            addCriterion("upper(service_provider_name) like", value.toUpperCase(), "serviceProviderName");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerMasterAccountLikeInsensitive(String value) {
            addCriterion("upper(order_partner_master_account) like", value.toUpperCase(), "orderPartnerMasterAccount");
            return (Criteria) this;
        }

        public Criteria andOrderPartnerSlaveAccountLikeInsensitive(String value) {
            addCriterion("upper(order_partner_slave_account) like", value.toUpperCase(), "orderPartnerSlaveAccount");
            return (Criteria) this;
        }

        public Criteria andBeforeSaleManagerLikeInsensitive(String value) {
            addCriterion("upper(before_sale_manager) like", value.toUpperCase(), "beforeSaleManager");
            return (Criteria) this;
        }

        public Criteria andSendContactPersonLikeInsensitive(String value) {
            addCriterion("upper(send_contact_person) like", value.toUpperCase(), "sendContactPerson");
            return (Criteria) this;
        }

        public Criteria andInstallContactPersonLikeInsensitive(String value) {
            addCriterion("upper(install_contact_person) like", value.toUpperCase(), "installContactPerson");
            return (Criteria) this;
        }

        public Criteria andIotPackageContactPersonLikeInsensitive(String value) {
            addCriterion("upper(iot_package_contact_person) like", value.toUpperCase(), "iotPackageContactPerson");
            return (Criteria) this;
        }

        public Criteria andSoftAuthContactPersonLikeInsensitive(String value) {
            addCriterion("upper(soft_auth_contact_person) like", value.toUpperCase(), "softAuthContactPerson");
            return (Criteria) this;
        }

        public Criteria andAfterSaleContactPersonLikeInsensitive(String value) {
            addCriterion("upper(after_sale_contact_person) like", value.toUpperCase(), "afterSaleContactPerson");
            return (Criteria) this;
        }

        public Criteria andAfterMarketRuleLikeInsensitive(String value) {
            addCriterion("upper(after_market_rule) like", value.toUpperCase(), "afterMarketRule");
            return (Criteria) this;
        }

        public Criteria andRepairContactInfoLikeInsensitive(String value) {
            addCriterion("upper(repair_contact_info) like", value.toUpperCase(), "repairContactInfo");
            return (Criteria) this;
        }

        public Criteria andReturnContactInfoLikeInsensitive(String value) {
            addCriterion("upper(return_contact_info) like", value.toUpperCase(), "returnContactInfo");
            return (Criteria) this;
        }

        public Criteria andProductCompanyInfoLikeInsensitive(String value) {
            addCriterion("upper(product_company_info) like", value.toUpperCase(), "productCompanyInfo");
            return (Criteria) this;
        }

        public Criteria andProductCommunicationMethodLikeInsensitive(String value) {
            addCriterion("upper(product_communication_method) like", value.toUpperCase(), "productCommunicationMethod");
            return (Criteria) this;
        }

        public Criteria andIotPackageInfoLikeInsensitive(String value) {
            addCriterion("upper(iot_package_info) like", value.toUpperCase(), "iotPackageInfo");
            return (Criteria) this;
        }

        public Criteria andHardwareSendListLikeInsensitive(String value) {
            addCriterion("upper(hardware_send_list) like", value.toUpperCase(), "hardwareSendList");
            return (Criteria) this;
        }

        public Criteria andProductParamInfoLikeInsensitive(String value) {
            addCriterion("upper(product_param_info) like", value.toUpperCase(), "productParamInfo");
            return (Criteria) this;
        }

        public Criteria andProductSendAddressLikeInsensitive(String value) {
            addCriterion("upper(product_send_address) like", value.toUpperCase(), "productSendAddress");
            return (Criteria) this;
        }

        public Criteria andHardwareExpressLikeInsensitive(String value) {
            addCriterion("upper(hardware_express) like", value.toUpperCase(), "hardwareExpress");
            return (Criteria) this;
        }

        public Criteria andProductSendTimeInfoLikeInsensitive(String value) {
            addCriterion("upper(product_send_time_info) like", value.toUpperCase(), "productSendTimeInfo");
            return (Criteria) this;
        }

        public Criteria andProductUseConditionLikeInsensitive(String value) {
            addCriterion("upper(product_use_condition) like", value.toUpperCase(), "productUseCondition");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformInfoLikeInsensitive(String value) {
            addCriterion("upper(soft_platform_info) like", value.toUpperCase(), "softPlatformInfo");
            return (Criteria) this;
        }

        public Criteria andSoftPlatformDownloadInfoLikeInsensitive(String value) {
            addCriterion("upper(soft_platform_download_info) like", value.toUpperCase(), "softPlatformDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andAppInfoLikeInsensitive(String value) {
            addCriterion("upper(app_info) like", value.toUpperCase(), "appInfo");
            return (Criteria) this;
        }

        public Criteria andAppDownloadInfoLikeInsensitive(String value) {
            addCriterion("upper(app_download_info) like", value.toUpperCase(), "appDownloadInfo");
            return (Criteria) this;
        }

        public Criteria andInstallInfoLikeInsensitive(String value) {
            addCriterion("upper(install_info) like", value.toUpperCase(), "installInfo");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Tue Mar 05 17:28:46 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Tue Mar 05 17:28:46 CST 2024
         */
        private ProductFlowInstanceConfigExample example;

        /**
         *
         * @mbg.generated Tue Mar 05 17:28:46 CST 2024
         */
        protected Criteria(ProductFlowInstanceConfigExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Tue Mar 05 17:28:46 CST 2024
         */
        public ProductFlowInstanceConfigExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Tue Mar 05 17:28:46 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Mar 05 17:28:46 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Mar 05 17:28:46 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Tue Mar 05 17:28:46 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Tue Mar 05 17:28:46 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Tue Mar 05 17:28:46 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Tue Mar 05 17:28:46 CST 2024
         */
        void example(com.chinamobile.iot.sc.pojo.ProductFlowInstanceConfigExample example);
    }
}