package com.chinamobile.iot.sc.controller.iot;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.service.IInvoiceService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;


/**
 * @package: com.chinamobile.iot.sc.controller.iot
 * @ClassName: InvoiceController
 * @description: 发票管理-对接IOT商城接口
 * @author: zyj
 * @create: 2021/11/12 14:41
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@RequestMapping("/os/invoiceservice")
@RestController
public class InvoiceController {
    @Resource
    private IInvoiceService invoiceService;

    // 同步发票申请信息
    @PostMapping("/InvoiceRequest2OS")
    public IOTAnswer<Void> InvoiceRequest2OS(@RequestBody IOTRequest baseRequest){
        return invoiceService.InvoiceRequest2OS(baseRequest);
    }

    // 同步发票冲红申请信息
    @PostMapping("/InvoiceVoid")
    public IOTAnswer<Void> InvoiceVoid(@RequestBody IOTRequest baseRequest){
        return invoiceService.InvoiceVoid(baseRequest);
    }

    // 同步发票冲红申请信息
    @GetMapping ("/test/revenue")
    public Void invoicingResult2IOTMall1(@RequestParam("orderSeq") String orderSeq){
        return invoiceService.invoicingResult2IOTMall1(orderSeq);
    }


    // 同步发票冲红申请信息
    @PostMapping ("/revenue/invoice")
    public BaseAnswer<Void> revenueInvoiceApply(@RequestParam("recId") String recId){
        return invoiceService.revenueInvoiceApply(recId);
    }
}
