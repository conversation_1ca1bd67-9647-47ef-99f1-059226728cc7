package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.pojo.param.OrderRealNameUpdateParam;
import com.chinamobile.iot.sc.pojo.vo.NotHandleKxOrderH5VO;
import com.chinamobile.iot.sc.pojo.vo.OrderRealNameVO;
import com.chinamobile.iot.sc.service.IOrderRealNameService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR> xiemaohua
 * @date : 2025/3/4 17:28
 * @description: 订单实名修改控制类
 **/
@RestController
@RequestMapping("/osweb/realName")
@Slf4j
public class OrderRealNameController {


    @Resource
    private IOrderRealNameService orderRealNameService;


    /**
     * 查询合同履约 卡+x 收货地址详情
     * @param orderId
     * @return
     */
    @GetMapping("/details")
    public BaseAnswer<OrderRealNameVO> getOrderRealNameMessage(@RequestParam("orderId") String orderId){
        BaseAnswer<OrderRealNameVO> orderRealName = orderRealNameService.getOrderRealName(orderId);
        return orderRealName;
    }


    /**
     * 修改收货实名信息
     * @param param
     * @return
     */
    @PostMapping("/update")
    public BaseAnswer<Void> updateOrderRealNameMessage(@RequestBody @Valid  OrderRealNameUpdateParam param,
                                                       @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
         orderRealNameService.updateOrderRealName(param,loginIfo4Redis);
        return new BaseAnswer<>();
    }
}
