package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 用于保存代销硬件每月自动生成的账单
 *
 * <AUTHOR>
public class UnionSellOrderExcelHistory implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    private String id;

    /**
     * 时间
     *
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    private String date;

    /**
     * 原始文件url
     *
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    private String url;

    /**
     *
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    private Date createTime;

    /**
     *
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    private Date updateTime;

    /**
     * 结算金额
     *
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    private String amount;

    /**
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..union_sell_order_excel_history.id
     *
     * @return the value of supply_chain..union_sell_order_excel_history.id
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    public UnionSellOrderExcelHistory withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..union_sell_order_excel_history.id
     *
     * @param id the value for supply_chain..union_sell_order_excel_history.id
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..union_sell_order_excel_history.date
     *
     * @return the value of supply_chain..union_sell_order_excel_history.date
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    public String getDate() {
        return date;
    }

    /**
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    public UnionSellOrderExcelHistory withDate(String date) {
        this.setDate(date);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..union_sell_order_excel_history.date
     *
     * @param date the value for supply_chain..union_sell_order_excel_history.date
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    public void setDate(String date) {
        this.date = date;
    }

    /**
     * This method returns the value of the database column supply_chain..union_sell_order_excel_history.url
     *
     * @return the value of supply_chain..union_sell_order_excel_history.url
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    public String getUrl() {
        return url;
    }

    /**
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    public UnionSellOrderExcelHistory withUrl(String url) {
        this.setUrl(url);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..union_sell_order_excel_history.url
     *
     * @param url the value for supply_chain..union_sell_order_excel_history.url
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * This method returns the value of the database column supply_chain..union_sell_order_excel_history.create_time
     *
     * @return the value of supply_chain..union_sell_order_excel_history.create_time
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    public UnionSellOrderExcelHistory withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..union_sell_order_excel_history.create_time
     *
     * @param createTime the value for supply_chain..union_sell_order_excel_history.create_time
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..union_sell_order_excel_history.update_time
     *
     * @return the value of supply_chain..union_sell_order_excel_history.update_time
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    public UnionSellOrderExcelHistory withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..union_sell_order_excel_history.update_time
     *
     * @param updateTime the value for supply_chain..union_sell_order_excel_history.update_time
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..union_sell_order_excel_history.amount
     *
     * @return the value of supply_chain..union_sell_order_excel_history.amount
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    public String getAmount() {
        return amount;
    }

    /**
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    public UnionSellOrderExcelHistory withAmount(String amount) {
        this.setAmount(amount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..union_sell_order_excel_history.amount
     *
     * @param amount the value for supply_chain..union_sell_order_excel_history.amount
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    public void setAmount(String amount) {
        this.amount = amount;
    }

    /**
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", date=").append(date);
        sb.append(", url=").append(url);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", amount=").append(amount);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UnionSellOrderExcelHistory other = (UnionSellOrderExcelHistory) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getDate() == null ? other.getDate() == null : this.getDate().equals(other.getDate()))
            && (this.getUrl() == null ? other.getUrl() == null : this.getUrl().equals(other.getUrl()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getAmount() == null ? other.getAmount() == null : this.getAmount().equals(other.getAmount()));
    }

    /**
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getDate() == null) ? 0 : getDate().hashCode());
        result = prime * result + ((getUrl() == null) ? 0 : getUrl().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getAmount() == null) ? 0 : getAmount().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon May 08 16:39:27 CST 2023
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        date("date", "date", "VARCHAR", false),
        url("url", "url", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        amount("amount", "amount", "VARCHAR", false);

        /**
         *
         * @mbg.generated Mon May 08 16:39:27 CST 2023
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon May 08 16:39:27 CST 2023
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon May 08 16:39:27 CST 2023
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon May 08 16:39:27 CST 2023
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon May 08 16:39:27 CST 2023
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon May 08 16:39:27 CST 2023
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon May 08 16:39:27 CST 2023
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon May 08 16:39:27 CST 2023
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon May 08 16:39:27 CST 2023
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon May 08 16:39:27 CST 2023
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon May 08 16:39:27 CST 2023
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon May 08 16:39:27 CST 2023
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon May 08 16:39:27 CST 2023
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon May 08 16:39:27 CST 2023
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon May 08 16:39:27 CST 2023
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon May 08 16:39:27 CST 2023
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon May 08 16:39:27 CST 2023
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}