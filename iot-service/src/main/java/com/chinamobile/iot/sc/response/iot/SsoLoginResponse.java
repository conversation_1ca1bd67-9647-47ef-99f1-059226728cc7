package com.chinamobile.iot.sc.response.iot;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/26 15:23
 * @description 商城单点登录响应
 */
@Data
public class SsoLoginResponse {

    private Header header;

    private Body body;

    @Data
    public static class Header {

        /**
         * 版本号,初始版本号1.0
         */
        private String version;

        /**
         * 对应的请求消息中的msgid
         */
        private String inresponseto;

        /**
         * 系统时间
         * 请求消息发送的系统时间，精确到毫秒，共17位，格式：20121227180001165
         */
        private String systemtime;

        /**
         * 响应码
         */
        private String resultcode;

        /**
         * 消息，异常时的描述信息
         */
        private String resultdesc;

        /**
         * 签名
         */
        private String mac;

    }

    @Data
    public static class Body {

        /**
         * 移动认证uid
         */
        private String usessionid;

        /**
         * UID有效时间
         */
        private String uidExpireTime;

        /**
         * 推送服务id
         */
        private String pushId;

    }

}
