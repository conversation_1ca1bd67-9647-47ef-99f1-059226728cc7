package com.chinamobile.iot.sc.response.web;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/12/22 11:17
 * @Description:
 */
@Data
public class OrderRocInfoDetailDTO {
    private String refundOrderId;
    private String refundsType;
    /**
     * 内部退款状态 1 通过 2 不通过 3 取消
     */
    private Integer orderStatus;

    /**
     * 退款、售后状态描述
     */
    private String orderStatusDescribe;
    /**
     * 退款原因
     */
    private String reason;


    private String reasonStr;

    /**
     * 退款说明
     */
    private String remark;
    /**
     * 订单号
     */
    private String orderId;

    /**
     * 原子订单id
     */
    private String atomOrderId;

    /**
     * 商品名称(规格)
     */
    private String skuOfferingName;
    /**
     * 原子商品名称
     */
    private String atomOfferingName;
    /**
     * 原子商品类型
     */
    private String atomOfferingClass;
    /**
     * SPU一级销售目录
     */
    private String spuOfferingClass;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 型号
     */
    private String model;
    /**
     * 颜色
     */
    private String color;
    /**
     * 订购数量 skuQuantity*atomQuantity
     */
    private Integer quantity;
    /**
     * 原子商品结算价
     */
    private String atomPrice;

    /**
     * 订购数量(规格)
     */
    private Integer skuQuantity;
    /**
     * 价格(规格)
     */
    private String skuPrice;

    /**
     * 合作伙伴ID
     */
    private String cooperatorId;
    /**
     * 合作伙伴姓名
     */
    private String cooperatorName;
    /**
     * 合作伙伴公司
     */
    private String partnerName;

    /**
     * 主合作伙伴id
     */
    private String primaryCooperatorId;

    /**
     * 图片数组
     */
    private List<String> pictures;
    /**
     * 处理信息 历史
     */
    private List<OrderRocHandleHistory> historyList;

    /**
     * 原子商品信息
     */
    private List<Order2CInfoDetailDTO.OrderAtomDetail> orderAtomDetailList;

    /**
     * 异常处理信息
     */
    private Order2CInfoDetailDTO.ExHandleInfo exHandleInfo;

    /**
     * 收货人姓名
     * 客户的信息
     */
    private String contactPersonName;
    /**
     * 收货人手机号
     * 客户的联系方式
     */
    private String contactPhone;
    /**
     * 收货地址
     * 客户的收货地址
     */
    private String addressInfo;
    /**
     * 收货人姓名
     * 合作伙伴的信息
     */
    private String rcvContactPersonName;
    /**
     * 收货人手机号
     * 合作伙伴的联系方式
     */
    private String rcvContactPhone;
    /**
     * 收货地址
     * 合作伙伴的收货地址
     */
    private String rcvAddressInfo;
    //物流信息
    private List<LogisticsMsg> logisticsMsgs;
    /**
     * 商品组/销售商品编码
     */
    private String  spuOfferingCode;

    /**
     *商品组/销售商品名称
     */
    private String  spuOfferingName;

    /**
     *商品编码（规格）
     */
    private String  skuOfferingCode;

    /**
     * 订单同步备注
     */
    private String remarks;

    /**
     * 退款数量
     */
    private Integer refundsNumber;

    /**
     * 退款金额
     */
    private String returnPrice;

    private BigDecimal returnPriceDec;

    /**
     * 订单备注信息
     */
    private List<OrderRemarkHistory> orderRemarkHistory;

    /**
     * 图片外部链接路径
     */
    private String url;

    /**
     * 图片内部路径
     */
    private String imgUrl;

    /**
     * 销售商品版本号
     */
    private String spuOfferingVersion;

    /**
     * 规格商品版本号
     */
    private String skuOfferingVersion;

    /**
     * 原子商品版本号
     */
    private String atomOfferingVersion;

    /**
     * 操作员姓名
     */
    private String custMgName;

    /**
     * 操作员电话
     */
    private String custMgPhone;

    /**
     * 操作员省工号
     */
    private String employeeNum;

    /**
     * 操作员编码
     */
    private String createOperCode;

    /**
     * 客户类型名称
     */
    private String customerTypeName;

    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 客户编码
     */
    private String custCode;

    /**
     * 客户手机号
     */
    private String custId;

    /**
     * 提单人省名称
     */
    private String reserveProvinceName;

    /**
     * 提单人市名称
     */
    private String reserveCityName;

    /**
     * 订单抵扣金额(单位厘，已加密)
     */
    private String orderDeductPrice;

    /**
     * 订单总金额（接口带来的，已加密）
     */
    private String orderTotalPrice;

    @Data
    public static class LogisticsMsg {
        /**
         * 物流单号
         */
        private String logisCode;
        /**
         * 物流服务商编码
         */
        private String supplierName;
        /**
         * 备注信息
         */
        private String description;
    }

    @Data
    public static class OrderRocHandleHistory {
        /**
         * 操作时间
         */
        private Date createTime;
        /**
         * 订单内部状态
         */
        private Integer innerStatus;
        /**
         * 内部状态描述
         */
        private String innerStatusDescribe;
        /**
         * 信息
         */
        private String message;
    }

    /**
     * 订单备注信息
     */
    @Data
    public static class OrderRemarkHistory {

        /**
         * 订单内部状态
         */
        private String roleType;
        /**
         * 创建者
         */
        private String createUser;
        /**
         * 备注信息
         */
        private String orderRemark;

        /**
         * 操作时间
         */
        private Date createTime;
    }
}
