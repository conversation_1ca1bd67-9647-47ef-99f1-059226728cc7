package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2024/2/4 11:02
 * @description: 库存模式枚举类
 **/
public enum InventoryPatternEnum {
    /**
     * 枚举类
     */
    NOTICE_TYPE_YSX_SERVICE("1","拍下减库存"),
    NOTICE_TYPE_CAR_SECURITY_SERVICE("2","付款减库存");

    /**
     * 类型
     */
    private String type;

    /**
     * 描述
     */
    private String desc;

    InventoryPatternEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 获取中文描述
     * @param type
     * @return
     */
    public static String getDesc(String type) {
        for (InventoryPatternEnum value : InventoryPatternEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return "";
    }
}
