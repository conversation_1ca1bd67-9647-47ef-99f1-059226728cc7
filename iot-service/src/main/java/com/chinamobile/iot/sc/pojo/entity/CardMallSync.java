package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 商城同步的卡信息
 *
 * <AUTHOR>
public class CardMallSync implements Serializable {
    /**
     *
     * Corresponding to the database column supply_chain..card_mall_sync.id
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    private String id;

    /**
     * 号卡信息表id
     *
     * Corresponding to the database column supply_chain..card_mall_sync.card_info_id
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    private String cardInfoId;

    /**
     * 码号库存信息表主键id
     *
     * Corresponding to the database column supply_chain..card_mall_sync.card_inventory_main_id
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    private String cardInventoryMainId;

    /**
     * 卡片类型0：插拔卡  1：贴片卡  2：M2M芯片非空写卡 3: M2M芯片空写卡
     *
     * Corresponding to the database column supply_chain..card_mall_sync.card_type
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    private String cardType;

    /**
     * 正式服务号码
     *
     * Corresponding to the database column supply_chain..card_mall_sync.msisdn
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    private String msisdn;

    /**
     *
     * Corresponding to the database column supply_chain..card_mall_sync.iccid
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    private String iccid;

    /**
     * 码号状态：1--未销售 2--销售中 3--已销售  4--销售失败 9--不可销售
     *
     * Corresponding to the database column supply_chain..card_mall_sync.card_status
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    private String cardStatus;

    /**
     * 订单id
     *
     * Corresponding to the database column supply_chain..card_mall_sync.order_id
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    private String orderId;

    /**
     * 原子订单id
     *
     * Corresponding to the database column supply_chain..card_mall_sync.atom_order_id
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    private String atomOrderId;

    /**
     * 创建时间
     *
     * Corresponding to the database column supply_chain..card_mall_sync.create_time
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    private Date createTime;

    /**
     * 更新时间
     *
     * Corresponding to the database column supply_chain..card_mall_sync.update_time
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    private Date updateTime;

    /**
     * Corresponding to the database table supply_chain..card_mall_sync
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..card_mall_sync.id
     *
     * @return the value of supply_chain..card_mall_sync.id
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public CardMallSync withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_mall_sync.id
     *
     * @param id the value for supply_chain..card_mall_sync.id
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_mall_sync.card_info_id
     *
     * @return the value of supply_chain..card_mall_sync.card_info_id
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public String getCardInfoId() {
        return cardInfoId;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public CardMallSync withCardInfoId(String cardInfoId) {
        this.setCardInfoId(cardInfoId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_mall_sync.card_info_id
     *
     * @param cardInfoId the value for supply_chain..card_mall_sync.card_info_id
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public void setCardInfoId(String cardInfoId) {
        this.cardInfoId = cardInfoId == null ? null : cardInfoId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_mall_sync.card_inventory_main_id
     *
     * @return the value of supply_chain..card_mall_sync.card_inventory_main_id
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public String getCardInventoryMainId() {
        return cardInventoryMainId;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public CardMallSync withCardInventoryMainId(String cardInventoryMainId) {
        this.setCardInventoryMainId(cardInventoryMainId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_mall_sync.card_inventory_main_id
     *
     * @param cardInventoryMainId the value for supply_chain..card_mall_sync.card_inventory_main_id
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public void setCardInventoryMainId(String cardInventoryMainId) {
        this.cardInventoryMainId = cardInventoryMainId == null ? null : cardInventoryMainId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_mall_sync.card_type
     *
     * @return the value of supply_chain..card_mall_sync.card_type
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public String getCardType() {
        return cardType;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public CardMallSync withCardType(String cardType) {
        this.setCardType(cardType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_mall_sync.card_type
     *
     * @param cardType the value for supply_chain..card_mall_sync.card_type
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public void setCardType(String cardType) {
        this.cardType = cardType == null ? null : cardType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_mall_sync.msisdn
     *
     * @return the value of supply_chain..card_mall_sync.msisdn
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public String getMsisdn() {
        return msisdn;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public CardMallSync withMsisdn(String msisdn) {
        this.setMsisdn(msisdn);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_mall_sync.msisdn
     *
     * @param msisdn the value for supply_chain..card_mall_sync.msisdn
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn == null ? null : msisdn.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_mall_sync.iccid
     *
     * @return the value of supply_chain..card_mall_sync.iccid
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public String getIccid() {
        return iccid;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public CardMallSync withIccid(String iccid) {
        this.setIccid(iccid);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_mall_sync.iccid
     *
     * @param iccid the value for supply_chain..card_mall_sync.iccid
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public void setIccid(String iccid) {
        this.iccid = iccid == null ? null : iccid.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_mall_sync.card_status
     *
     * @return the value of supply_chain..card_mall_sync.card_status
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public String getCardStatus() {
        return cardStatus;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public CardMallSync withCardStatus(String cardStatus) {
        this.setCardStatus(cardStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_mall_sync.card_status
     *
     * @param cardStatus the value for supply_chain..card_mall_sync.card_status
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public void setCardStatus(String cardStatus) {
        this.cardStatus = cardStatus == null ? null : cardStatus.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_mall_sync.order_id
     *
     * @return the value of supply_chain..card_mall_sync.order_id
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public CardMallSync withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_mall_sync.order_id
     *
     * @param orderId the value for supply_chain..card_mall_sync.order_id
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_mall_sync.atom_order_id
     *
     * @return the value of supply_chain..card_mall_sync.atom_order_id
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public String getAtomOrderId() {
        return atomOrderId;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public CardMallSync withAtomOrderId(String atomOrderId) {
        this.setAtomOrderId(atomOrderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_mall_sync.atom_order_id
     *
     * @param atomOrderId the value for supply_chain..card_mall_sync.atom_order_id
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public void setAtomOrderId(String atomOrderId) {
        this.atomOrderId = atomOrderId == null ? null : atomOrderId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_mall_sync.create_time
     *
     * @return the value of supply_chain..card_mall_sync.create_time
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public CardMallSync withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_mall_sync.create_time
     *
     * @param createTime the value for supply_chain..card_mall_sync.create_time
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..card_mall_sync.update_time
     *
     * @return the value of supply_chain..card_mall_sync.update_time
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public CardMallSync withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_mall_sync.update_time
     *
     * @param updateTime the value for supply_chain..card_mall_sync.update_time
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", cardInfoId=").append(cardInfoId);
        sb.append(", cardInventoryMainId=").append(cardInventoryMainId);
        sb.append(", cardType=").append(cardType);
        sb.append(", msisdn=").append(msisdn);
        sb.append(", iccid=").append(iccid);
        sb.append(", cardStatus=").append(cardStatus);
        sb.append(", orderId=").append(orderId);
        sb.append(", atomOrderId=").append(atomOrderId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CardMallSync other = (CardMallSync) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCardInfoId() == null ? other.getCardInfoId() == null : this.getCardInfoId().equals(other.getCardInfoId()))
            && (this.getCardInventoryMainId() == null ? other.getCardInventoryMainId() == null : this.getCardInventoryMainId().equals(other.getCardInventoryMainId()))
            && (this.getCardType() == null ? other.getCardType() == null : this.getCardType().equals(other.getCardType()))
            && (this.getMsisdn() == null ? other.getMsisdn() == null : this.getMsisdn().equals(other.getMsisdn()))
            && (this.getIccid() == null ? other.getIccid() == null : this.getIccid().equals(other.getIccid()))
            && (this.getCardStatus() == null ? other.getCardStatus() == null : this.getCardStatus().equals(other.getCardStatus()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getAtomOrderId() == null ? other.getAtomOrderId() == null : this.getAtomOrderId().equals(other.getAtomOrderId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCardInfoId() == null) ? 0 : getCardInfoId().hashCode());
        result = prime * result + ((getCardInventoryMainId() == null) ? 0 : getCardInventoryMainId().hashCode());
        result = prime * result + ((getCardType() == null) ? 0 : getCardType().hashCode());
        result = prime * result + ((getMsisdn() == null) ? 0 : getMsisdn().hashCode());
        result = prime * result + ((getIccid() == null) ? 0 : getIccid().hashCode());
        result = prime * result + ((getCardStatus() == null) ? 0 : getCardStatus().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getAtomOrderId() == null) ? 0 : getAtomOrderId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     * corresponding to the database table supply_chain..card_mall_sync
     *
     * @mbg.generated Fri Dec 06 09:49:55 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        cardInfoId("card_info_id", "cardInfoId", "VARCHAR", false),
        cardInventoryMainId("card_inventory_main_id", "cardInventoryMainId", "VARCHAR", false),
        cardType("card_type", "cardType", "VARCHAR", false),
        msisdn("msisdn", "msisdn", "VARCHAR", false),
        iccid("iccid", "iccid", "VARCHAR", false),
        cardStatus("card_status", "cardStatus", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        atomOrderId("atom_order_id", "atomOrderId", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         * Corresponding to the database table supply_chain..card_mall_sync
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..card_mall_sync
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..card_mall_sync
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        private final String column;

        /**
         * Corresponding to the database table supply_chain..card_mall_sync
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         * Corresponding to the database table supply_chain..card_mall_sync
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        private final String javaProperty;

        /**
         * Corresponding to the database table supply_chain..card_mall_sync
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Fri Dec 06 09:49:55 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}