package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.BasePageQuery;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.FinancingRepaymentListParam;
import com.chinamobile.iot.sc.pojo.vo.FinancingRepaymentBaseVO;
import com.chinamobile.iot.sc.pojo.vo.FinancingRepaymentHistoryVO;
import com.chinamobile.iot.sc.pojo.vo.FinancingRepaymentListVO;
import com.chinamobile.iot.sc.service.OrderFinancingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * created by liuxiang on 2023/7/13 16:36
 * 融资还款接口
 */
@RequestMapping("/osweb/financing")
@RestController
public class FinancingRepaymentController {

    @Autowired
    private OrderFinancingService orderFinancingService;


    /**
     * 查询还款信息列表
     */
    @GetMapping("/repayment/list")
    public BaseAnswer<PageData<FinancingRepaymentListVO>> repaymentList(FinancingRepaymentListParam param,
                                                                        @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return orderFinancingService.repaymentList(param,loginIfo4Redis);
    }

    /**
     * 查询还款基本信息
     */
    @GetMapping("/repayment/baseInfo")
    public BaseAnswer<FinancingRepaymentBaseVO> repaymentBaseInfo(@RequestParam String orderCode){
        return orderFinancingService.repaymentBaseInfo(orderCode);
    }

    /**
     * 查询还款记录
     */
    @GetMapping("/repayment/history")
    public BaseAnswer<PageData<FinancingRepaymentHistoryVO>> repaymentHistory(@RequestParam String orderCode, BasePageQuery pageQuery){
        return orderFinancingService.repaymentHistory(orderCode,pageQuery);
    }
}
