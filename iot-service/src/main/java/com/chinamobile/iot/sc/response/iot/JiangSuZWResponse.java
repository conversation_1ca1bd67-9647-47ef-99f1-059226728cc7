package com.chinamobile.iot.sc.response.iot;

import com.chinamobile.iot.sc.request.jiangsuzw.JiangSuZWHeader;
import lombok.Data;


/**
 * created by l<PERSON><PERSON><PERSON> on 2024/1/11 11:01
 * 江苏装维生态平台响应
 */
@Data
public class JiangSuZWResponse {

    private Root ROOT;

    @Data
    public static class Root{

        private Body BODY;

        private JiangSuZWHeader HEADER;

    }

    @Data
    public static class Body{

        //返回码 0成功，其他失败
        private String RETURN_CODE;

        //返回信息
        private String RETURN_MSG;

        //详细信息
        private String DETAIL_MSG;

        private String PROMPT_MSG;
    }

}
