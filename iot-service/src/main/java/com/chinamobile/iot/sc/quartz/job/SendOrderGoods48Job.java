package com.chinamobile.iot.sc.quartz.job;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.constant.OrderStatusInnerEnum;
import com.chinamobile.iot.sc.dao.Order2cAtomInfoMapper;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.pojo.Order2cAtomInfo;
import com.chinamobile.iot.sc.pojo.Order2cAtomInfoExample;
import com.chinamobile.iot.sc.quartz.QuartzJobConf;
import com.chinamobile.iot.sc.quartz.QuartzManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> xiemaohua
 * @date : 2022/5/7 16:20
 * @description: 定时任务48小时订单未发货短信提醒
 **/
@Slf4j
@Component
@DisallowConcurrentExecution
public class SendOrderGoods48Job implements Job {


    @Autowired
    private SmsFeignClient smsFeignClient;
    @Autowired
    private QuartzManager quartzManager;

    @Resource
    private Order2cAtomInfoMapper atomOrderInfoMapper;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        QuartzJobConf jobConf = JSON.parseObject(context.getJobDetail().getJobDataMap().getString("quartConf"), QuartzJobConf.class);
        log.info("开始执行48小时订单发货通知短信，订单号：{}", jobConf.getOrderId());
        List<String> hAndX = new ArrayList<>();
        hAndX.add("H");
        hAndX.add("X");
        Order2cAtomInfoExample atomInfoExample = new Order2cAtomInfoExample();
        atomInfoExample.createCriteria().andOrderIdEqualTo(jobConf.getOrderId()).andAtomOfferingClassIn(hAndX);
        List<Order2cAtomInfo> order2cAtomInfos = atomOrderInfoMapper.selectByExample(atomInfoExample);
        if (CollectionUtils.isNotEmpty(order2cAtomInfos)) {
            Order2cAtomInfo order2cAtomInfo = order2cAtomInfos.get(0);
            Integer orderStatus = order2cAtomInfo.getOrderStatus();
            if (orderStatus.equals(OrderStatusInnerEnum.WAIT_SEND.getStatus())) {
                Msg4Request msg4Request = new Msg4Request();
        /*List<String> phones = new ArrayList<>();
        phones.add(jobConf.getPhone());*/
                msg4Request.setMobiles(jobConf.getPhone());
                msg4Request.setTemplateId(jobConf.getTemplateId());
                Map<String, String> msgMap = new HashMap<>();
                msgMap.put("orderId", jobConf.getOrderId());
                msgMap.put("atomOfferingName", order2cAtomInfo.getAtomOfferingName());
                msgMap.put("skuQuantity", String.valueOf(order2cAtomInfo.getSkuQuantity()));
                msg4Request.setMessage(msgMap);
                smsFeignClient.asySendMessage(msg4Request);
                quartzManager.removeQuartzJobByTaskId(jobConf);
                log.info("执行完成48小时订单发货通知短信，订单号：{}", jobConf.getOrderId());
            }else {
                log.info("48小时的时候已经发货不用短信通知，订单号：{}", jobConf.getOrderId());
            }
        }
    }
}
