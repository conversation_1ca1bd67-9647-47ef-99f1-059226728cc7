package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/22
 * @description 卡+x库存管理模式枚举类
 */
public enum InventoryManagementModeKxEnum {

    PROVINCE("1","省级管理"),
    CITY("2","地市管理");

    /**
     * 卡+x库存管理模式类型
     */
    private String type;

    /**
     * 卡+x库存管理模式描述
     */
    private String desc;

    InventoryManagementModeKxEnum(String type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public static String getDesc(String type) {
        for (InventoryManagementModeKxEnum value : InventoryManagementModeKxEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return "";
    }

    public static Boolean containDesc(String desc) {
        for (InventoryManagementModeKxEnum value : InventoryManagementModeKxEnum.values()) {
            if (value.desc.equals(desc)) {
                return true;
            }
        }
        return false;
    }

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }}
