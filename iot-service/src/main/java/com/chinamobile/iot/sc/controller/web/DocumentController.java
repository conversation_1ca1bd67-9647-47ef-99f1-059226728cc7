package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.Document;
import com.chinamobile.iot.sc.pojo.param.DocumentListParam;
import com.chinamobile.iot.sc.pojo.param.SaveAnnouncementParam;
import com.chinamobile.iot.sc.pojo.vo.DocumentListVO;
import com.chinamobile.iot.sc.service.DocumentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

@RestController
@RequestMapping(value = "/osweb/document")
public class DocumentController {

    @Autowired
    private DocumentService documentService;


    /**
     * 创建文档
     * @param file
     * @param name
     * @param visibleRange
     * @param isRepetitionName
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping("/save")
    public BaseAnswer saveAnnouncementMessage(@RequestParam("file") MultipartFile file,
                                       @RequestParam("name") String name,
                                       @RequestParam("visibleRange") String visibleRange,
                                       @RequestParam("isRepetitionName") Boolean isRepetitionName,
                                       @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return documentService.saveDocument(file,name,visibleRange,isRepetitionName,loginIfo4Redis);
    }

    /**
     * 更新文档
     * @param id
     * @param file
     * @param name
     * @param visibleRange
     * @param isRepetitionName
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping("/update")
    public BaseAnswer updateDocumentMessage(@RequestParam("id") String id,
                                       @RequestParam(value = "file",required = false) MultipartFile file,
                                       @RequestParam(value = "name",required = false) String name,
                                       @RequestParam(value = "visibleRange",required = false) String visibleRange,
                                       @RequestParam(value = "isRepetitionName") Boolean isRepetitionName,
                                       @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return documentService.updateDocument(id,file,name,visibleRange,isRepetitionName,loginIfo4Redis);
    }

    /**
     * 删除文档
     * @param id
     * @return
     */
    @DeleteMapping("/delete")
    public BaseAnswer deleteDocumentMessage(@RequestParam("id") String id){
        return documentService.deleteDocument(id);
    }

    /**
     * 查询文档详情
     * @param id
     * @return
     */
    @GetMapping("/detail")
    public  BaseAnswer<Document> selectDocumentDetailMessage(@RequestParam("id") String id){
        return documentService.selectDocumentDetail(id);
    }

    /**
     * 下载文档
     * @param id
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping("/download")
    public BaseAnswer downloadDocumentFileMessage(@RequestParam("id") String id,
                                                  @RequestParam("operate") String operate,
                                                  @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return documentService.downloadDocumentFile(id,operate,loginIfo4Redis);
    }


    /**
     * (管理文档分页查询)
     * @param param
     * @return
     */
    @GetMapping("/pageList")
    public BaseAnswer<PageData<DocumentListVO>> getDocumentListPage(DocumentListParam param){
        return documentService.getDocumentList(param);
    }


    /**
     * (首页文档分页查询)
     * @param param
     * @return
     */
    @GetMapping("/pageHomeList")
    public BaseAnswer<PageData<DocumentListVO>> getDocumentHomeListPage(DocumentListParam param,
                                                                        @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return documentService.getDocumentHomeList(param,loginIfo4Redis);
    }


}
