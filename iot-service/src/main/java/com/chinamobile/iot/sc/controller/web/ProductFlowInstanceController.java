package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.config.ThreadExecutorConfig;
import com.chinamobile.iot.sc.enums.log.ProductManageOperateEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.NavigationInfo;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceSku;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceSpu;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.*;
import com.chinamobile.iot.sc.pojo.vo.productFlowInfo.ShelfSpuDetailVO;
import com.chinamobile.iot.sc.service.ProductFlowInstanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;


/**
 * created by liuxiang on 2024/2/26 16:11
 * 产品流程控制类，包括发起流程，流程查看，流程审核等
 */
@RestController
@RequestMapping("/osweb/product/flow/instance")
public class ProductFlowInstanceController {

    @Autowired
    private ProductFlowInstanceService productFlowInstanceService;

    /**
     * 获取产品标准列表
     */
    @GetMapping("/productStandardList")
    public BaseAnswer<List<ProductStandardListVO>> getProductStandardList() {
        return productFlowInstanceService.getProductStandardList();
    }

    /**
     * 根据产品标准，获取对应的产品类别列表
     */
    @GetMapping("/productTypeList")
    public BaseAnswer<List<ProductTypeListVO>> getProductTypeList(@RequestParam Integer productStandardCode) {
        return productFlowInstanceService.getProductTypeList(productStandardCode);
    }
    /**
     * 获取产品运营类型列表
     */
    @GetMapping("/productOperateList")
    public BaseAnswer<List<ProductTypeListVO>> getProductOperateList() {
        return productFlowInstanceService.getProductOperateList();
    }
    /**
     * 新建spu上架流程
     */
    @PostMapping("/spuShelf")
    public BaseAnswer spuShelf(@RequestParam Integer productStandard,
                               @RequestParam Integer productType,
                               @RequestParam MultipartFile file,
                               @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) throws IOException {
        return productFlowInstanceService.spuShelf(productStandard, productType, file, loginIfo4Redis);
    }


    /**
     * 新建sku上架流程
     */
    @PostMapping("/skuShelf")
    public BaseAnswer skuShelf(@RequestParam String spuCode,
                               String skuCode,
                               MultipartFile file,
                               @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) throws IOException {
        return productFlowInstanceService.skuShelf(spuCode, skuCode, file, loginIfo4Redis);
    }

    /**
     * 产品流程非价格变更流程
     *
     * @param skuInfoUpdateParam
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping(value = "/skuNotPriceUpdate")
    public BaseAnswer skuNotPriceUpdate(@Valid @RequestBody SkuInfoUpdateParam skuInfoUpdateParam,
                                    @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return productFlowInstanceService.skuInfoUpdate(skuInfoUpdateParam, loginIfo4Redis);
    }

    /**
     * 产品流程所有信息变更流程
     *
     * @param skuInfoUpdateParam
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping(value = "/skuAllInfoUpdate")
    public BaseAnswer skuAllInfoUpdate(@Valid @RequestBody SkuInfoUpdateParam skuInfoUpdateParam,
                                        @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return productFlowInstanceService.skuInfoUpdate(skuInfoUpdateParam, loginIfo4Redis);
    }

    /**
     * 产品流程销售价格信息变更流程
     *
     * @param skuInfoUpdateParam
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping(value = "/skuSalePriceUpdate")
    public BaseAnswer skuSalePriceUpdate(@Valid @RequestBody SkuInfoUpdateParam skuInfoUpdateParam,
                                    @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return productFlowInstanceService.skuInfoUpdate(skuInfoUpdateParam, loginIfo4Redis);
    }

    /**
     * 产品流程结算价格信息变更流程
     *
     * @param skuInfoUpdateParam
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping(value = "/skuSettlePriceUpdate")
    public BaseAnswer skuSettlePriceUpdate(@Valid @RequestBody SkuInfoUpdateParam skuInfoUpdateParam,
                                     @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return productFlowInstanceService.skuInfoUpdate(skuInfoUpdateParam, loginIfo4Redis);
    }


    /**
     * 新建sku下架流程
     */
    @PostMapping("/skuDelist")
    public BaseAnswer skuDelist(@Valid @RequestBody SkuInfoDelistParam skuInfoDelist,
                                @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return productFlowInstanceService.skuDelist(skuInfoDelist,loginIfo4Redis,ip);
    }

    /**
     * 产品信息 产品列表
     */
    @GetMapping("/shelfSpuList")
    public BaseAnswer<PageData<ShelfSpuInfoVO>> getShelfSpuList(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                                                                @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId,
                                                                @Valid FlowInstanceSpuListParam param) {
        return productFlowInstanceService.getShelfSpuList(loginIfo4Redis, userId, param);
    }

    /**
     * 产品信息，产品类型 详情
     */
    @GetMapping("/shelfSpuDetail")

    public BaseAnswer<ShelfSpuDetailVO> getShelfSpuDetail(@RequestParam String flowInstanceId,@RequestParam(required = false) String spuCode,@RequestParam(required = false) String skuCode){
        return productFlowInstanceService.getShelfSpuDeatail(flowInstanceId,spuCode,skuCode);
    }
    /**
     * 产品信息，详情里面，下载附件，日志记录
     */
    @PostMapping("/shelfSpuDetailRecord")

    public BaseAnswer shelfSpuDetailRecord(@Valid @RequestBody ShelfSpuDetailRecordParam param
    ){
       return productFlowInstanceService.shelfSpuDetailRecord(param);
    }
    /**
     /**
     * sku导出(返回二进制流)
     */
    @GetMapping("/skuExport")
    public BaseAnswer skuExport(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,@RequestParam String flowInstanceId,@RequestParam(required = false) String spuCode,@RequestParam(required = false) String skuCode) {
        //后台执行异步导出，导出结果通过消息中心通知前端
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        ThreadExecutorConfig.executorService.execute(() -> {
          productFlowInstanceService.skuExport(loginIfo4Redis,flowInstanceId,spuCode,skuCode,ip);
        });
        return BaseAnswer.success(null);
    }
    /**
     /**
     * sku导出(返回二进制流)
     */
    @GetMapping("/skuExportListing")
    public BaseAnswer skuExportListing(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,@RequestParam String flowInstanceId,@RequestParam(required = false) String spuCode,@RequestParam(required = false) String skuCode) {
        //后台执行异步导出，导出结果通过消息中心通知前端
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        ThreadExecutorConfig.executorService.execute(() -> {
            productFlowInstanceService.skuExport(loginIfo4Redis,flowInstanceId,spuCode,skuCode,ip);
        });
        return BaseAnswer.success(null);
    }
    /**
     /**
     * sku导出全部(返回二进制流)
     */
    @GetMapping("/skuExportAll")
    public BaseAnswer skuExportAll(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        //后台执行异步导出，导出结果通过消息中心通知前端
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        ThreadExecutorConfig.executorService.execute(() -> {
            productFlowInstanceService.skuExportAll(loginIfo4Redis,ip);
        });
        return BaseAnswer.success(null);

    }
    /**
     * 下架 编辑流程
     */
    @PostMapping("/editDel")
    public BaseAnswer editDelFlow(@Valid @RequestBody AuditProductFlowParam param,
                                  @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        param.setConfig(false);
        param.setEdit(true);
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return productFlowInstanceService.auditProductFlow(param,loginIfo4Redis, ProductManageOperateEnum.OFF_SHELF.code,ip);
    }
    /**
     *下架 废止流程
     */
    @PostMapping("/cancelDel")
    public BaseAnswer cancelDelFlow(@Valid @RequestBody CancelFlowParam param,
                                 @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return productFlowInstanceService.cancelFlow(param.getFlowInstanceId(), loginIfo4Redis, ProductManageOperateEnum.OFF_SHELF.code);
    }
    /**
     * 根据spu获取其下面已上架的sku列表
     */
    @GetMapping("/shelfSkuListBySpu")
    public BaseAnswer<List<ProductFlowInstanceSku>> getShelfSkuListBySpu(@RequestParam String spuCode) {
        return productFlowInstanceService.getShelfSkuListBySpu(spuCode);
    }
    /**
     * 根据spu获取其下面已上架的sku列表,且最新状态为已上架
     */
    @GetMapping("/getShelfSkuNewListBySpu")
    public BaseAnswer<List<ProductFlowInstanceSku>> getShelfSkuNewListBySpu(@RequestParam String spuCode) {
        return productFlowInstanceService.getShelfSkuNewListBySpu(spuCode);
    }

    /**
     * 查询流程列表(超管可查看所有流程，具体用户查看当前处理人是自己或者自己创建的流程)
     */
    @GetMapping("/list")
    public BaseAnswer<PageData<ProductFlowInstanceListVO>> getFlowInstanceList(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                                                                               FlowInstanceListParam param) {
        return productFlowInstanceService.getFlowInstanceList(loginIfo4Redis, param);
    }

    /**
     * 查询流程详情
     */
    @GetMapping("/detail")
    public BaseAnswer<ProductFlowInstanceDetailVO> getInstanceDetail(@RequestParam String flowInstanceId,
                                                                     @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return productFlowInstanceService.getInstanceDetail(flowInstanceId,loginIfo4Redis,ProductManageOperateEnum.SHELF.code);

    }
    /**
     * 查询流程详情
     */
    @GetMapping("/detailDel")
    public BaseAnswer<ProductFlowInstanceDetailVO> getInstanceDetailDel(@RequestParam String flowInstanceId,
                                                                     @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return productFlowInstanceService.getInstanceDetail(flowInstanceId,loginIfo4Redis, ProductManageOperateEnum.OFF_SHELF.code);

    }

    /**
     * 产品信息变更查询流程详情
     */
    @GetMapping("/skuDetail")
    public BaseAnswer<ProductFlowInstanceDetailVO> getInstanceSkuDetail(@RequestParam String flowInstanceId,
                                                                     @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return productFlowInstanceService.getInstanceDetail(flowInstanceId,loginIfo4Redis, ProductManageOperateEnum.UPDATE.code);

    }


    /**
     * 编辑流程
     */
    @PostMapping("/edit")
    public BaseAnswer editFlow(@Valid @RequestBody AuditProductFlowParam param,
                               @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        param.setConfig(false);
        param.setEdit(true);
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return productFlowInstanceService.auditProductFlow(param,loginIfo4Redis, ProductManageOperateEnum.SHELF.code,ip);
    }

    /**
     * 产品信息变更编辑流程
     */
    @PostMapping("/skuEdit")
    public BaseAnswer skuEditFlow(@Valid @RequestBody AuditProductFlowParam param,
                               @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        param.setConfig(false);
        param.setEdit(true);
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return productFlowInstanceService.auditProductFlow(param,loginIfo4Redis, ProductManageOperateEnum.UPDATE.code,ip);
    }


    /**
     * 获取上架类目列表
     */
    @GetMapping("/shelfCategoryList")
    public BaseAnswer<List<ShelfCategoryListVO>> getShelfCategoryList() {
        return productFlowInstanceService.getShelfCategoryList();
    }

    /**
     * 根据上架类目，获取对应CMIOT账目项
     */
    @GetMapping("/cmiotCostListByShelfCategory")
    public BaseAnswer<List<ShelfCategoryListVO>> getCmiotCostListByCategoryId(@RequestParam String categoryId) {
        return productFlowInstanceService.getCmiotCostListByCategoryId(categoryId);
    }

    /**
     * 获取一级导航目录列表
     */
    @GetMapping("/firstNavigationList")
    public BaseAnswer<List<NavigationListVO>> getFirstNavigationList() {
        return productFlowInstanceService.getFirstNavigationList();
    }

    /**
     * 根据上级导航目录，获取下级导航目录列表
     */
    @PostMapping("/secondNavigationList")
    public BaseAnswer<List<NavigationListVO>> getSecondNavigationList(List<String> parentId) {
        return productFlowInstanceService.getSecondNavigationList(parentId);
    }

    /**
     * 获取导航目录树状结构数据
     */
    @GetMapping("/navigationData")
    public BaseAnswer<List<ProductFlowInstanceDetailVO.NavigationDirectory>> navigationData(){
        return productFlowInstanceService.navigationData();
    }

    /**
     * 上传流程附件
     */
    @PostMapping("/uploadAttachment")
    public BaseAnswer uploadAttachment(@RequestParam MultipartFile file,
                                       @RequestParam String flowInstanceId,
                                       @RequestParam Integer type) {
        return productFlowInstanceService.uploadAttachment(file, flowInstanceId,type);
    }

    /**
     * 获取流程附件列表
     */
    @GetMapping("/attachmentList")
    public BaseAnswer<List<AttachmentListVO>> getAttachmentList(@RequestParam String flowInstanceId) {
        return productFlowInstanceService.getAttachmentList(flowInstanceId);
    }

    /**
     * 删除附件
     * @return
     */
    @PostMapping("/deleteAttachment")
    public BaseAnswer deleteAttachment(@RequestBody Map<String,String> map){
        if(map.get("id") == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"请传递id");
        }
        return productFlowInstanceService.deleteAttachment(map.get("id"));
    }

    /**
     * 产品信息变更废止流程
     */
    @PostMapping("/skuCancel")
    public BaseAnswer skuCancelFlow(@Valid @RequestBody CancelFlowParam param,
                                 @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return productFlowInstanceService.cancelFlow(param.getFlowInstanceId(), loginIfo4Redis,ProductManageOperateEnum.UPDATE.code);
    }

    /**
     * 废止流程
     */
    @PostMapping("/cancel")
    public BaseAnswer cancelFlow(@Valid @RequestBody CancelFlowParam param,
                                 @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {

        return productFlowInstanceService.cancelFlow(param.getFlowInstanceId(), loginIfo4Redis,ProductManageOperateEnum.SHELF.code);
    }

    /**
     * 审核流程
     */
    @PostMapping("/audit")
    public BaseAnswer auditProductFlow(@Valid @RequestBody AuditProductFlowParam param,
                                       @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis
    ) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return productFlowInstanceService.auditProductFlow(param, loginIfo4Redis, ProductManageOperateEnum.SHELF.code,ip);
    }

    /**
     * 产品信息变更审核流程(包括专员配置)
     */
    @PostMapping("/skuAudit")
    public BaseAnswer skuAuditProductFlow(@Valid @RequestBody AuditProductFlowParam param,
                                          @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis
    ) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return productFlowInstanceService.auditProductFlow(param, loginIfo4Redis, ProductManageOperateEnum.UPDATE.code,ip);
    }

    /**
     * 专员配置流程
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping("/config")
    public BaseAnswer configProductFlow(@Valid @RequestBody AuditProductFlowParam param,
                                       @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis
    ) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return productFlowInstanceService.auditProductFlow(param, loginIfo4Redis, ProductManageOperateEnum.SHELF.code,ip);
    }

    /**
     * 产品信息变更专员配置流程
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping("/skuConfig")
    public BaseAnswer skuConfigProductFlow(@Valid @RequestBody AuditProductFlowParam param,
                                        @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis
    ) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return productFlowInstanceService.auditProductFlow(param, loginIfo4Redis,ProductManageOperateEnum.UPDATE.code,ip);
    }
     /*
     * 下架 审核流程
     */
    @PostMapping("/auditDel")
    public BaseAnswer auditDelProductFlow(@Valid @RequestBody AuditProductFlowParam param,
                                       @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis
    ) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return productFlowInstanceService.auditProductFlow(param, loginIfo4Redis, ProductManageOperateEnum.OFF_SHELF.code,ip);
    }
    /**
     * 下架 配置流程
     */
    @PostMapping("/configDel")
    public BaseAnswer configDelProductFlow(@Valid @RequestBody AuditProductFlowParam param,
                                          @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis
    ) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return productFlowInstanceService.auditProductFlow(param, loginIfo4Redis, ProductManageOperateEnum.OFF_SHELF.code,ip);
    }

    /**
     * 判断某个用户是否有未完结流程实例(内部接口)
     * 某个流程任务当前处理人是这个用户，或者明确了下一步处理人是这个用户，就表示这个用户有运行中流程
     */
    @GetMapping("/hasRunningFlow")
    public BaseAnswer<Boolean> hasRunningFlowInstance(@RequestParam String userId){
        return productFlowInstanceService.hasRunningFlowInstance(userId);
    }

    /**
     * 查看详情页面，下载附件后通过本接口新增下载日志
     */
    @PostMapping("/addDetaiAttachmentlLog")
    public BaseAnswer addDetaiAttachmentlLog(@Valid @RequestBody FlowInstanceAddDetaiAttachmentlLogParam param,
                                             @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return productFlowInstanceService.addDetaiAttachmentlLog(param,loginIfo4Redis);
    }

    /**
     * 获取已上架SPU列表
     */
    @GetMapping("/shelfSpuSimpleList")
    public BaseAnswer<PageData<ShelfSpuSimpleListVO>> getShelfSpuSimpleList(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                                                                        ShelfSpuSimpleListParam param
                                                                        ){
        return productFlowInstanceService.getShelfSpuSimpleList(loginIfo4Redis,param);
    }

    /**
     * 查看流程图（审核记录）
     */
    @GetMapping("/auditStepList")
    public BaseAnswer<List<ProductFlowAuditStepListVO>> getAuditStepList(@RequestParam String flowInstanceId){
        return productFlowInstanceService.getAuditStepList(flowInstanceId);
    }

    /**
     * 二次编辑产品信息（当割接商品信息填充完后，关闭二次编辑功能）
     */
    @PostMapping("/specialEdit")
    public BaseAnswer specialEdit(@RequestBody @Valid ProductFlowInstanceEditParam param,
                                  @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return productFlowInstanceService.specialEdit(param,loginIfo4Redis);
    }

    /**
     * 批量导入流程附件(不会删除现有的附件,叠加新增附件)
     * 仅支持导入zip文件
     */
    @PostMapping("/importAttachment")
    public BaseAnswer importAttachment(MultipartFile file){
        return productFlowInstanceService.importAttachment(file);
    }


    /**
     * 处理流程目录关系  目录名称
     * @return
     */
    @PostMapping("/updateProductFlowInstanceDirectoryName")
    public BaseAnswer updateProductFlowInstanceDirectoryNameMessage(){
        productFlowInstanceService.updateProductFlowInstanceDirectoryName();
        return new BaseAnswer();
    }

    /**
     * 处理测试环境流程目录关系  目录名称
     * @return
     */
    @PostMapping("/updateProductFlowInstanceDirectoryNameTest")
    public BaseAnswer updateProductFlowInstanceDirectoryNameTestMessage(){
        productFlowInstanceService.updateProductFlowInstanceDirectoryNameTest();
        return new BaseAnswer();
    }

    /**
     * 处理流程导航目录存量数据（内部接口）
     */
    @PostMapping("/dealProductFlowInstanceDirectory")
    public BaseAnswer dealProductFlowInstanceDirectory(){
        return productFlowInstanceService.dealProductFlowInstanceDirectory();
    }

}
