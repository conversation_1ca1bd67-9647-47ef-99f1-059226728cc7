package com.chinamobile.iot.sc.controller.newproduct;

import com.amazonaws.services.dynamodbv2.xspec.S;
import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.pojo.vo.StoreCatalogVO;
import com.chinamobile.iot.sc.service.StoreCatalogService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static com.chinamobile.iot.sc.common.BaseConstant.PRODUCT_UP_DOWN_FRAME;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/14
 * @description 商城目录controller类
 */
@RestController
@RequestMapping(value = "/os/newProduct")
public class StoreCatalogController {

    @Resource
    private StoreCatalogService storeCatalogService;

    /**
     * 获取商城目录
     * @param parentId
     * @return
     */
    @GetMapping(value = "/listCatalog")
    @Auth(authCode = {PRODUCT_UP_DOWN_FRAME})
    public BaseAnswer<List<StoreCatalogVO>> listStoreCatalog(@RequestParam(value = "parentId",required = false)String parentId){
        List<StoreCatalogVO> storeCatalogVOList = storeCatalogService.listStoreCatalog(parentId);
        return new BaseAnswer<List<StoreCatalogVO>>().setData(storeCatalogVOList);
    }

}
