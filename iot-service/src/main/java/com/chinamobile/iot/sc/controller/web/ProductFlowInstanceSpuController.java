package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceSpu;

import com.chinamobile.iot.sc.service.ProductFlowInstanceSpuService;
import com.chinamobile.iot.sc.service.ProductFlowInstanceSpuService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/8
 * @description 产品流程spu controller类
 */
@RequestMapping("/osweb/product/flow/spu")
@RestController
public class ProductFlowInstanceSpuController {

    @Resource
    private ProductFlowInstanceSpuService productFlowInstanceSpuService;

    /**
     * 获取已有spu名称和编码列表
     */
    @GetMapping(value = "/listFlowInstanceSpu")
    public BaseAnswer<List<ProductFlowInstanceSpu>> listProductFlowInstanceSpu(@RequestParam(value = "spuInfo",required = false) String spuInfo){
        return productFlowInstanceSpuService.listProductFlowInstanceSpu(spuInfo);
    }
}
