package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.KafkaTopic;
import com.chinamobile.iot.sc.mode.AddMessageParam;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.MessageListParam;
import com.chinamobile.iot.sc.pojo.vo.MessageDetailVO;
import com.chinamobile.iot.sc.pojo.vo.MessageListVO;
import com.chinamobile.iot.sc.service.MessageCenterService;
import com.chinamobile.iot.sc.task.ExpiredOrderExportFileDeteleTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * created by liuxiang on 2023/9/1 10:29
 * 消息中心
 */
@RestController
@RequestMapping("/osweb/message")
@Slf4j
public class MessageCenterController {

    @Autowired
    private MessageCenterService messageCenterService;

    @Autowired
    private ExpiredOrderExportFileDeteleTask expiredOrderExportFileDeteleTask;

    @Autowired
    private KafkaTemplate<String,byte[]> kafkaTemplate;

    /**
     * 分页查询消息列表
     */
    @GetMapping("/list")
    public BaseAnswer<PageData<MessageListVO>> getMessageList(MessageListParam param,
                                                              @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId){
        return messageCenterService.getMessageList(param,userId);
    }


    /**
     * 查看消息内容(消息变为已读)
     */
    @GetMapping("/detail")
    public BaseAnswer<MessageDetailVO> getMessageDetail(@RequestParam String id){
        return messageCenterService.getMessageDetail(id);
    }

    /**
     * 获取未读消息数量
     */
    @GetMapping("/unReadCount")
    public BaseAnswer<Integer> getUnReadMsgCount(@RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId,
                                                 Integer source){
        return messageCenterService.getUnReadMsgCount(userId,source);
    }

    /**
     * 添加消息(内部调用)
     * @return
     */
    @PostMapping("/add")
    public BaseAnswer<Void> addMessage(@RequestBody @Valid AddMessageParam param){
        return messageCenterService.addMessage(param);
    }


    /**
     * 发送新消息推送(内部接口)
     */
    @PostMapping("/websocket")
    public void websocket(){
        //通过kafka推送消息，更新未读数量
        ProducerRecord<String, byte[]> record = new ProducerRecord<>(KafkaTopic.SUPPLY_CHAIN_WEBSOCKET.getTopic(), "/message/monitor".getBytes());
        kafkaTemplate.send(record);
        log.info("消息中心推送websocket");
    }

    /**
     * 手动触发删除过期文件的定时任务
     * @return
     */
    @PostMapping("/testDeleteExcel")
    public BaseAnswer testDeleteExcel(){
        expiredOrderExportFileDeteleTask.expiredOrderExportFileDeteleTask();
        return BaseAnswer.success(null);
    }

    /**
     * 删除消息
     */
    @PostMapping("/delete/{id}")
    public BaseAnswer<Void> deleteMessage(@PathVariable String id,
                                          @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId){
        return messageCenterService.deleteMessage(id,userId);
    }

    /**
     * （内部接口）新增module字段后，为订单导出类型赋默认值：订单管理
     */
    @PostMapping("/initModule")
    public BaseAnswer initModule(){
        return messageCenterService.initModule();
    }
}
