package com.chinamobile.iot.sc.response;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2023/8/18
 * 千里眼平台接口返回
 */
@Data
public class QlyCheckResponse implements Serializable {

    private String resultCode;

    private String resultDesc;

    private List<String> data;
    public boolean isSuccess() {
        return StringUtils.equals("0000",resultCode);
    }


}
