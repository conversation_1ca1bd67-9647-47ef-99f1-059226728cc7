package com.chinamobile.iot.sc.controller.iot;

import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.pojo.param.LoginStatusQueryParam;
import com.chinamobile.iot.sc.pojo.vo.LoginStatusQueryVO;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.service.SsoService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/26 15:02
 * @description 商城单点登录相关接口
 */
@RestController
@RequestMapping("/os/sso")
public class SsoController {

    @Resource
    private SsoService ssoService;

    /**
     * 商城免认证登录接口
     * @param request
     * @return
     */
    @PostMapping("/authenticationExemptLogin")
    public IOTAnswer authenticationExemptLogin(@RequestBody IOTRequest request) {
        IOTAnswer iotAnswer = ssoService.authenticationExemptLogin(request);
        return iotAnswer;
    }


    /**
     * 转发移动认证向商城发起的业务平台登录状态查询请求
     */
    @PostMapping("/loginStatusQuery")
    public LoginStatusQueryVO loginStatusQuery(@RequestBody LoginStatusQueryParam param){
        return ssoService.loginStatusQuery(param);
    }

}
