package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.InvoiceInfoH5Param;
import com.chinamobile.iot.sc.pojo.param.OrderKxH5Param;
import com.chinamobile.iot.sc.pojo.param.OrderRocKxH5Param;
import com.chinamobile.iot.sc.pojo.vo.InvoiceInfoH5VO;
import com.chinamobile.iot.sc.pojo.vo.NotHandleKxOrderH5VO;
import com.chinamobile.iot.sc.pojo.vo.OrderKxH5VO;
import com.chinamobile.iot.sc.pojo.vo.OrderRocKxH5VO;
import com.chinamobile.iot.sc.service.OrderKxH5Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/28
 * @description 订单卡+X的H5 controller接口类
 */
@RestController
@RequestMapping(value = "/osweb/kxH5")
public class OrderKxH5Controller {

    @Resource
    private OrderKxH5Service orderKxH5Service;

    /**
     * 获取未处理的卡+x、联合销售、合同履约订单数量
     * @return
     */
    @GetMapping("/notHandleKxCount")
    public BaseAnswer<NotHandleKxOrderH5VO> getNotHandleKxOrderCount(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        BaseAnswer<NotHandleKxOrderH5VO> baseAnswer = new BaseAnswer();
        NotHandleKxOrderH5VO notHandleKxOrderH5VO = orderKxH5Service.getNotHandleKxOrderCount(loginIfo4Redis);
        baseAnswer.setData(notHandleKxOrderH5VO);
        return baseAnswer;
    }

    /**
     * 分页获取发票相关的H5数据
     * @param invoiceInfoH5Param
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping(value="/pageInvoiceH5")
    public BaseAnswer<PageData<InvoiceInfoH5VO>> pageInvoiceH5(InvoiceInfoH5Param invoiceInfoH5Param,
                                                           @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer baseAnswer = new BaseAnswer();
        PageData<InvoiceInfoH5VO> pageData = orderKxH5Service.pageInvoiceH5(invoiceInfoH5Param, loginIfo4Redis);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }

    /**
     * 分页获取订单的卡+X的H5数据
     * @param orderKxH5Param
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping(value="/pageOrderKxH5")
    public BaseAnswer<PageData<OrderKxH5VO>> pageOrderKxH5(OrderKxH5Param orderKxH5Param,
                                                            @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer baseAnswer = new BaseAnswer();
        PageData<OrderKxH5VO> pageData = orderKxH5Service.pageOrderKxH5(orderKxH5Param,loginIfo4Redis);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }

    /**
     * 分页获取订单退换的卡+X的H5数据
     * @param orderRocKxH5Param
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping(value="/pageOrderRocKxH5")
    public BaseAnswer<PageData<OrderKxH5VO>> pageOrderRocKxH5(OrderRocKxH5Param orderRocKxH5Param,
                                                           @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer baseAnswer = new BaseAnswer();
        PageData<OrderRocKxH5VO> pageData = orderKxH5Service.pageOrderRocKxH5(orderRocKxH5Param,loginIfo4Redis);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }
}
