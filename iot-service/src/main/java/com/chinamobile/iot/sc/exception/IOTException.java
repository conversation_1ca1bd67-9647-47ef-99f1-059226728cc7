package com.chinamobile.iot.sc.exception;

import com.chinamobile.iot.sc.mode.IOTAnswer;
import lombok.Getter;

/**
 * @Author: YSC
 * @Date: 2021/11/8 16:43
 * @Description: IOT商城规定异常
 */
public class IOTException extends RuntimeException{

    @Getter
    private final IOTAnswer answer;

    public IOTException(IOTAnswer answer) {
        super(answer.getMessageSeq()+">>>"+answer.getResultCode()+","+answer.getResultDesc());
        this.answer=answer;
    }
    public IOTException(IOTAnswer answer,String resultDesc){
        super(answer.getMessageSeq()+">>>-1,"+resultDesc);
        this.answer=answer;
        this.answer.setResultCode("-1");
        this.answer.setResultDesc(resultDesc);
    }
    public IOTException(IOTAnswer answer,String errorCode,String resultDesc){
        super(answer.getMessageSeq()+">>>"+errorCode+","+resultDesc);
        this.answer=answer;
        this.answer.setResultCode(errorCode);
        this.answer.setResultDesc(resultDesc);
    }

}
