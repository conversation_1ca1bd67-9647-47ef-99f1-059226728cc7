package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.pojo.entity.CardChooseDelivery;
import com.chinamobile.iot.sc.pojo.vo.CardChooseDeliveryVO;
import com.chinamobile.iot.sc.service.CardChooseDeliveryService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/9
 * @description 批量导入交付卡信息controller类
 */
@RestController
@RequestMapping(value = "/osweb/card")
public class CardChooseDeliveryController {

    @Resource
    private CardChooseDeliveryService cardChooseDeliveryService;


    /**
     * 根据原子订单id获取批量导入交付卡信息
     * @param atomOrderIdList
     * @return
     */
    @GetMapping(value = "/listCardChooseDeliveryByAtomOrderId")
    public BaseAnswer<List<CardChooseDeliveryVO>> listCardChooseDeliveryByAtomOrderId(@RequestParam(value = "atomOrderIdList") List<String> atomOrderIdList){
        BaseAnswer<List<CardChooseDeliveryVO>> baseAnswer = new BaseAnswer();
        List<CardChooseDeliveryVO> cardChooseDeliveryList = cardChooseDeliveryService.listCardChooseDeliveryByAtomOrderId(atomOrderIdList);
        baseAnswer.setData(cardChooseDeliveryList);
        return baseAnswer;
    }

    /**
     * 根据订单id获取批量导入交付卡信息
     * @param orderIdList
     * @return
     */
    @GetMapping(value = "/listCardChooseDeliveryByOrderId")
    public BaseAnswer<List<CardChooseDeliveryVO>> listCardChooseDeliveryByOrderId(@RequestParam(value = "orderIdList") List<String> orderIdList){
        BaseAnswer<List<CardChooseDeliveryVO>> baseAnswer = new BaseAnswer();
        List<CardChooseDeliveryVO> cardChooseDeliveryList = cardChooseDeliveryService.listCardChooseDeliveryByOrderId(orderIdList);
        baseAnswer.setData(cardChooseDeliveryList);
        return baseAnswer;
    }

    /**
     * 根据主键id删除交付卡信息
     * @param id
     * @return
     */
    @DeleteMapping(value = "/deleteCardChooseDeliveryById")
    public BaseAnswer deleteCardChooseDeliveryById(@RequestParam(value = "id")String id){
        cardChooseDeliveryService.deleteCardChooseDeliveryById(id);
        return new BaseAnswer();
    }

}
