package com.chinamobile.iot.sc.util;

import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2023/4/26 14:35
 * @description: 分批处理list集合工具类
 **/
public class BatchHandlerUtil<T> {


    public void batchResolve(List<T> list, int batchSize, CommonResolve commonResolve) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (int i = 0; i < list.size(); i += batchSize) {
            int end = i + batchSize;
            if (end > list.size()) {
                end = list.size();
            }
            List<T> items = list.subList(i, end);
            commonResolve.resolve(items);
        }
    }
}
