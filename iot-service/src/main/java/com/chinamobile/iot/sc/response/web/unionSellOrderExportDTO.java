package com.chinamobile.iot.sc.response.web;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class unionSellOrderExportDTO {

    /**
     * 订单收入归属省;
     */
    @Excel(name = "订单收入归属省")
    private String orgProvince;

    /**
     * 订单收入归地市;
     */
    @Excel(name = "订单收入归地市")
    private String orgLocation;

    /**
     * 下单时间
     */
    @Excel(name = "下单时间")
    private String createTime;

    /**
     * 订单号
     */
    @Excel(name = "订单号")
    private String orderId;
    /**
     * 支付时间
     */
    @Excel(name = "支付时间")
    private String payTime;

    /**
     * 订单状态描述
     */
    @Excel(name = "订单状态")
    private String orderStatusDescribe;

    /**
     * 订单完成时间
     */
    @Excel(name = "订单完成时间")
    private String finishTime;
    /**
     * 退款时间
     */
    @Excel(name = "退款时间")
    private String refundTime;

    /**
     * 订单总金额
     */
    @Excel(name = "订单总金额")
    private Double orderTotalPrice;

    /**
     * 商品组/销售商品名称
     */
    @Excel(name = "商品组/销售商品名称")
    private String spuOfferingName;

    /**
     * 商品组/销售商品编码
     */
    @Excel(name = "商品组/销售商品编码")
    private String spuOfferingCode;

    /**
     * SPU一级销售目录
     */
    @Excel(name = "商品类型")
    private String spuOfferingClass;
    /**
     * 商品名称(规格)
     */
    @Excel(name = "商品名称(规格)")
    private String skuOfferingName;

    /**
     * 商品规格编码
     */
    @Excel(name = "商品编码（规格）")
    private String skuOfferingCode;

    /**
     * 订购数量（规格）
     */
    @Excel(name = "订购数量（规格）")
    private Long skuQuantity;


    /**
     * 销售目录价（规格）
     */
    @Excel(name = "销售目录价（规格）")
    private Double price;
    /**
     * 原子商品名称
     */
    @Excel(name = "原子商品名称")
    private String atomOfferingName;
    /**
     * 原子商品编码
     */
    @Excel(name = "原子商品编码")
    private String atomOfferingCode;
    /**
     * 原子商品类型
     */
    @Excel(name = "原子商品类型")
    private String atomOfferingClass;

    /**
     * 数量（原子商品） skuQuantity*atomQuantity
     */
    @Excel(name = "数量（原子商品）")
    private Integer quantity;

    /**
     * 销售金额（原子商品）
     */
    @Excel(name = "销售金额（原子商品）")
    private Double atomTotalPrice;

    @Excel(name = "结算单价（原子商品）")
    /**
     * 结算单价（原子商品）
     */
    private Double settlePrice;

    @Excel(name = "结算金额（原子商品）")
    /**
     * 结算金额（原子商品）
     */
    private Double totalSettlePrice;
    /**
     * 合作伙伴名
     */
    @Excel(name = "合作伙伴名称")
    private String partnerName;
    /**
     * 产品部门
     */
    @Excel(name = "产品部门")
    private String department;
    /**
     * 合作伙伴姓名
     */
    @Excel(name = "合作伙伴联系人")
    private String cooperatorName;
    /**
     * 支付金额
     */
    @Excel(name = "支付金额")
    private Double payPrice;
    /**
     * 退款金额
     */
    @Excel(name = "退款金额")
    private Double refundPrice;
    /**
     * 可结算金额
     */
    @Excel(name = "可结算金额")
    private Double settlablePrice;
}
