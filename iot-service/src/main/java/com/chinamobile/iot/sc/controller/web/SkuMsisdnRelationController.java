package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.pojo.param.GetOrderResultParam;
import com.chinamobile.iot.sc.pojo.vo.GetOrderResultVO;
import com.chinamobile.iot.sc.service.SkuMsisdnRelationService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/9
 * @description 订单原子信息和卡号关系controller类
 */
@RestController
@RequestMapping(value = "/osweb/skuMsisdn")
public class SkuMsisdnRelationController {

    @Resource
    private SkuMsisdnRelationService skuMsisdnRelationService;

    /**
     * 获取接单结果信息
     * @param getOrderResultParam
     * @return
     */
    @GetMapping(value = "/listGetOrderResult")
    @Auth(authCode = {BaseConstant.ORDER_GET_ORDER})
    public BaseAnswer<List<GetOrderResultVO>> listGetOrderResult(@Valid GetOrderResultParam getOrderResultParam){
        BaseAnswer<List<GetOrderResultVO>> baseAnswer = new BaseAnswer<>();
        List<GetOrderResultVO> getOrderResultVOList = skuMsisdnRelationService.listGetOrderResult(getOrderResultParam);
        baseAnswer.setData(getOrderResultVOList);
        return baseAnswer;
    }

}
