package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.interceptor.AuthorityInterceptor;
import com.chinamobile.iot.sc.config.SupplierInfoMapConfig;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.OrderManageOperateEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.AuthCode;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.QlyOrderSn;
import com.chinamobile.iot.sc.pojo.UnionSellOrderExcelHistory;
import com.chinamobile.iot.sc.pojo.dto.BusinessCodeListDTO;
import com.chinamobile.iot.sc.pojo.exhandle.ExHandleInfo;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.GetOrderDetailVO;
import com.chinamobile.iot.sc.pojo.vo.SpecialAfterMarketResultVO;
import com.chinamobile.iot.sc.request.AuditRefundRequest;
import com.chinamobile.iot.sc.request.ConfirmReturnOrder;
import com.chinamobile.iot.sc.request.LogisticsInfoRequest;
import com.chinamobile.iot.sc.request.UnionSellOrderDownloadRequest;
import com.chinamobile.iot.sc.request.exhandle.Request4ExHandleAdd;
import com.chinamobile.iot.sc.request.order2c.OrderRemarkParam;
import com.chinamobile.iot.sc.request.order2c.OrderReminderParam;
import com.chinamobile.iot.sc.request.order2c.OrderSalesReportParam;
import com.chinamobile.iot.sc.response.SimpleItemDTO;
import com.chinamobile.iot.sc.response.web.*;
import com.chinamobile.iot.sc.response.web.logistics.LogisticsVO;
import com.chinamobile.iot.sc.service.*;
import com.chinamobile.iot.sc.task.AfterPayOrderStatusTask;
import com.chinamobile.iot.sc.util.IotLogUtil;
import com.chinamobile.iot.sc.util.SmsValidUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.chinamobile.iot.sc.common.BaseConstant.*;

/**
 * @Author: daiguojun
 * @Date: 2023/8/19
 * @Description:
 */
@RestController
@RequestMapping("/osweb")
@Slf4j
public class QlyOrderController {

    @Resource
    private QlyOrderService qlyOrderService;


    /**
     * 开通千里眼设备
     *
     * @param param
     * @return
     */
    @PostMapping("/qlyOrder/turnOn")
    public BaseAnswer<List<String>> turnOnDevices(@RequestBody @Valid QlyTurnOnWebParam param) {
        return qlyOrderService.turnOnDevices(param.getOrderId(),param.getDeviceSns());
    }

    /**
     * 退订千里眼设备
     *
     * @param param
     * @return
     */
    @PostMapping("/qlyOrder/cancel")
    public BaseAnswer<Void> cancelDevices(@RequestBody @Valid QlyTurnOnWebParam param) {
        return qlyOrderService.cancelDevices(param.getOrderId());
    }
    /**
     * 千里眼设备数据割接
     *
     * @param param
     * @return
     */
    @PostMapping("/qlyOrderImport")
    public void qlyOrderImport(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis, @RequestParam("file") MultipartFile file) {
         qlyOrderService.qlyOrderImport(loginIfo4Redis,file);
    }

    /**
     * 服务开通失败的订单导出
     *
     * @param param
     * @return
     */
    @GetMapping("/qlyOrder/failedExport")
    public void exportFailedSn(QlyTurnOnWebParam param,
                                HttpServletResponse response) throws IOException {
        qlyOrderService.exportFailedSn(param.getOrderId(),response);
    }

    @GetMapping("/qlyOrder/getImeiList")
    public BaseAnswer<List<QlyOrderSn>> getImeiList(@RequestParam("orderId") String orderId) {
        return qlyOrderService.getImeiList(orderId);
    }




}
