package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.service.ProductExistDataFlowService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/27
 * @description 已存在产品数据流程controller类
 */
@RestController
@RequestMapping("/osweb/product/flow")
public class ProductExistDataFlowController {

    @Resource
    private ProductExistDataFlowService productExistDataFlowService;

    /**
     * 导出发布了的存量商品信息
     * @param response
     * @throws IOException
     */
    @GetMapping(value = "/exportUsedProductExistData")
    public void exportUsedProductExistData(HttpServletResponse response) throws IOException {
        productExistDataFlowService.exportUsedProductExistData(response);
    }


    /**
     * 导入发布了的存量商品信息
     * @param file
     * @param request
     * @param response
     * @throws Exception
     */
    @PostMapping("/importUsedProductExistData")
    public BaseAnswer importCardRelation(MultipartFile file,
                                         MultipartFile iotFile,
                                         HttpServletRequest request,
                                         HttpServletResponse response) throws Exception {
        return productExistDataFlowService.importUsedProductExistData(file,iotFile,request,response);
    }


}
