package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2022/12/14 11:34
 * @description: 售后订单同步商城结果常量类
 **/
public class AfterMarketOrderResultConstant {

    /**
     * 派单
     */
    public static final String AFTER_MARKET_ORDER_DISPATCH ="1";

    /**
     * 交付
     */
    public static final String AFTER_MARKET_ORDER_DELIVERY ="2";

    /**
     * 修改
     */
    public static final String AFTER_MARKET_ORDER_UPDATE ="3";

    /**
     * 售后订单接单方式
     */
    public static final Integer AFTER_MARKET_ORDER_ORDER_TAKE_TYPE_OS=1;

    public static final Integer AFTER_MARKET_ORDER_ORDER_TAKE_TYPE_PROVINCE =2;

    /**
     * 历史售后订单操作类型常量
     */
    public static final Integer AFTER_MARKET_ORDER_HISTORY_OPERATE_TYPE_=1;

    /**
     * 售后退货订单操作类型常量
     */
    public static final Integer EXCHANGE_PURCHASE_ORDER_HISTORY_OPERATE_TYPE_=2;

    /**
     * 售后订单关闭操作类型常量
     */
    public static final Integer AFTER_MARKET_ORDER_HISTORY_OPERATE_TYPE_CLOSE = 5;
}
