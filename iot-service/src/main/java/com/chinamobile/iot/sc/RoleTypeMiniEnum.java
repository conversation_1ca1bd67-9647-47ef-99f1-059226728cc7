package com.chinamobile.iot.sc;

import java.util.Objects;

public enum RoleTypeMiniEnum {

    NORMAL("0", "普通用户"),
    LEVEL_1_DISTRIBUTOR("1", "一级分销员"),
    LEVEL_2_DISTRIBUTOR("2", "二级分销员"),
    CHANNEL_PARTNER("3", "渠道商"),
    CUSTOMER_MANAGER("4", "客户经理");

    public String code;
    public String name;

    RoleTypeMiniEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Boolean contains(String code) {
        if (code == null) {
            return false;
        }
        RoleTypeMiniEnum[] values = RoleTypeMiniEnum.values();
        for (RoleTypeMiniEnum value : values) {
            if (Objects.equals(value.code, code)) {
                return true;
            }
        }
        return false;
    }

    public static RoleTypeMiniEnum fromCode(String code) {
        if (code == null) {
            return null;
        }
        RoleTypeMiniEnum[] values = RoleTypeMiniEnum.values();
        for (RoleTypeMiniEnum value : values) {
            if (Objects.equals(value.code, code)) {
                return value;
            }
        }
        return null;
    }

    public static String getName(String status) {
        if (status == null) {
            return null;
        }
        RoleTypeMiniEnum[] values = RoleTypeMiniEnum.values();
        for (RoleTypeMiniEnum value : values) {
            if (Objects.equals(value.code, status)) {
                return value.name;
            }
        }
        return null;
    }
}
