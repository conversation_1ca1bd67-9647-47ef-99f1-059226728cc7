package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/14
 * @description 新产品申请上下架状态常量类
 */
public class NewProductOnlineOfflineRequestStatusConstant {

    /**
     * 新增(引入产品通过后的状态)
     */
    public static final Integer NEW_ADD = 0;

    /**
     * 待审核（上架初审）
     */
    public static final Integer ONLINE_FIRST_TRIAL = 1;

    /**
     * 待审核（上架复审）
     */
    public static final Integer ONLINE_RECHECK = 2;

    /**
     * 待审核（上架终审）
     */
    public static final Integer ONLINE_FINAL_JUDGMENT = 3;

    /**
     * 待审核（上架运管审核）
     */
    public static final Integer ONLINE_OPERATOR_JUDGMENT = 4;

    /**
     * 已上架
     */
    public static final Integer ONLINE = 5;

    /**
     * 审核不通过(上架)
     */
    public static final Integer ONLINE_NOT_PASS = 6;

    /**
     * 待审核（下架初审）
     */
    public static final Integer OFFLINE_FIRST_TRIAL = 7;

    /**
     * 待审核（下架复审）
     */
    public static final Integer OFFLINE_RECHECK = 8;

    /**
     * 待审核（下架终审）
     */
    public static final Integer OFFLINE_FINAL_JUDGMENT = 9;

    /**
     * 待审核（下架运管审核）
     */
    public static final Integer OFFLINE_OPERATER_JUDGMENT = 10;

    /**
     * 已下架
     */
    public static final Integer OFFLINE = 11;

    /**
     * 审核不通过(下架)
     */
    public static final Integer OFFLINE_NOT_PASS = 12;


    /**
     * 上下架存储覆盖
     */
    public static final Boolean IS_COVER =  true;


    /**
     * 上下架存储有效期
     */
    public static final Integer EXPIRED_DAY = -1;

}
