package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.pojo.StoreCatalog;
import com.chinamobile.iot.sc.pojo.vo.StoreCatalogVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/14
 * @description 商城目录service接口类
 */
public interface StoreCatalogService {

    /**
     * 获取商城目录列表
     * @param parentId
     * @return
     */
    List<StoreCatalogVO> listStoreCatalog(String parentId);

    /**
     * 根据主键id获取商城目录
     * @param id
     * @return
     */
    StoreCatalog getStoreCatalogById(String id);
}
