package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/19
 * @description 分页查询新产品上下线列表(主合作伙伴页面)
 */
@Data
public class NewProductOnlineOfflineVO {

    /**
     * 新产品引入申请表id
     */
    private String newProductRequestId;

    /**
     * 商品套餐信息id
     */
    private String comboInfoId;

    /**
     * 申请编号
     */
    private String requestNo;

    /**
     * 商品名称SPU
     */
    private String spuOfferingName;

    /**
     * 商品规格SKU
     */
    private String skuOfferingName;

    /**
     * 合作伙伴名称
     */
    private String cooperatorName;

    /**
     * 上下架状态
     */
    private String onlineStatus;

    private String onlineStatusName;

    /**
     * 上下架申请状态
     */
    private Integer onlineOfflineRequestStatus;

    private String onlineOfflineRequestStatusName;

    /**
     * 上下架当前处理人
     */
    private String onlineOfflineCurrentHandlerUserId;
}
