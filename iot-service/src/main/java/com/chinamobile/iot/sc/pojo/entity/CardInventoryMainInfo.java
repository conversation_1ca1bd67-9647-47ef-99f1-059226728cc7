package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 码号库存信息表
 *
 * <AUTHOR>
public class CardInventoryMainInfo implements Serializable {
    /**
     * 码号库存主键id
     *
     * Corresponding to the database column supply_chain..card_inventory_main_info.id
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    private String id;

    /**
     * 归属卡服务商EC编码
     *
     * Corresponding to the database column supply_chain..card_inventory_main_info.cust_code
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    private String custCode;

    /**
     * 归属卡服务商EC名称
     *
     * Corresponding to the database column supply_chain..card_inventory_main_info.cust_name
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    private String custName;

    /**
     * 开卡模板编码
     *
     * Corresponding to the database column supply_chain..card_inventory_main_info.template_id
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    private String templateId;

    /**
     * 开卡模板名称
     *
     * Corresponding to the database column supply_chain..card_inventory_main_info.template_name
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    private String templateName;

    /**
     * 客户归属省份
     *
     * Corresponding to the database column supply_chain..card_inventory_main_info.be_id
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    private String beId;

    /**
     * 省份名称
     *
     * Corresponding to the database column supply_chain..card_inventory_main_info.provice_name
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    private String proviceName;

    /**
     * 客户归属地市
     *
     * Corresponding to the database column supply_chain..card_inventory_main_info.region_id
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    private String regionId;

    /**
     * 地市名称
     *
     * Corresponding to the database column supply_chain..card_inventory_main_info.region_name
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    private String regionName;

    /**
     * 卡片类型0：插拔卡  1：贴片卡  2：M2M芯片非空写卡 3: M2M芯片空写卡
     *
     * Corresponding to the database column supply_chain..card_inventory_main_info.card_type
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    private String cardType;

    /**
     * 预占数量
     *
     * Corresponding to the database column supply_chain..card_inventory_main_info.reserve_quatity
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    private Integer reserveQuatity;

    /**
     * 当前库存数
     *
     * Corresponding to the database column supply_chain..card_inventory_main_info.current_inventory
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    private Integer currentInventory;

    /**
     * 总库存数
     *
     * Corresponding to the database column supply_chain..card_inventory_main_info.total_inventory
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    private Integer totalInventory;

    /**
     * 库存预警值
     *
     * Corresponding to the database column supply_chain..card_inventory_main_info.inventory_threshold
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    private Integer inventoryThreshold;

    /**
     * 是否短信预警
     *
     * Corresponding to the database column supply_chain..card_inventory_main_info.is_notice
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    private Boolean isNotice;

    /**
     * 库存状态：0：短缺，1：充足
     *
     * Corresponding to the database column supply_chain..card_inventory_main_info.inventory_status
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    private String inventoryStatus;

    /**
     * 创建时间
     *
     * Corresponding to the database column supply_chain..card_inventory_main_info.create_time
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    private Date createTime;

    /**
     * 更新时间
     *
     * Corresponding to the database column supply_chain..card_inventory_main_info.update_time
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    private Date updateTime;

    /**
     * Corresponding to the database table supply_chain..card_inventory_main_info
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..card_inventory_main_info.id
     *
     * @return the value of supply_chain..card_inventory_main_info.id
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfo withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_inventory_main_info.id
     *
     * @param id the value for supply_chain..card_inventory_main_info.id
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_inventory_main_info.cust_code
     *
     * @return the value of supply_chain..card_inventory_main_info.cust_code
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public String getCustCode() {
        return custCode;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfo withCustCode(String custCode) {
        this.setCustCode(custCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_inventory_main_info.cust_code
     *
     * @param custCode the value for supply_chain..card_inventory_main_info.cust_code
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setCustCode(String custCode) {
        this.custCode = custCode == null ? null : custCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_inventory_main_info.cust_name
     *
     * @return the value of supply_chain..card_inventory_main_info.cust_name
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public String getCustName() {
        return custName;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfo withCustName(String custName) {
        this.setCustName(custName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_inventory_main_info.cust_name
     *
     * @param custName the value for supply_chain..card_inventory_main_info.cust_name
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setCustName(String custName) {
        this.custName = custName == null ? null : custName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_inventory_main_info.template_id
     *
     * @return the value of supply_chain..card_inventory_main_info.template_id
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public String getTemplateId() {
        return templateId;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfo withTemplateId(String templateId) {
        this.setTemplateId(templateId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_inventory_main_info.template_id
     *
     * @param templateId the value for supply_chain..card_inventory_main_info.template_id
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setTemplateId(String templateId) {
        this.templateId = templateId == null ? null : templateId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_inventory_main_info.template_name
     *
     * @return the value of supply_chain..card_inventory_main_info.template_name
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public String getTemplateName() {
        return templateName;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfo withTemplateName(String templateName) {
        this.setTemplateName(templateName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_inventory_main_info.template_name
     *
     * @param templateName the value for supply_chain..card_inventory_main_info.template_name
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setTemplateName(String templateName) {
        this.templateName = templateName == null ? null : templateName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_inventory_main_info.be_id
     *
     * @return the value of supply_chain..card_inventory_main_info.be_id
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public String getBeId() {
        return beId;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfo withBeId(String beId) {
        this.setBeId(beId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_inventory_main_info.be_id
     *
     * @param beId the value for supply_chain..card_inventory_main_info.be_id
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setBeId(String beId) {
        this.beId = beId == null ? null : beId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_inventory_main_info.provice_name
     *
     * @return the value of supply_chain..card_inventory_main_info.provice_name
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public String getProviceName() {
        return proviceName;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfo withProviceName(String proviceName) {
        this.setProviceName(proviceName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_inventory_main_info.provice_name
     *
     * @param proviceName the value for supply_chain..card_inventory_main_info.provice_name
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setProviceName(String proviceName) {
        this.proviceName = proviceName == null ? null : proviceName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_inventory_main_info.region_id
     *
     * @return the value of supply_chain..card_inventory_main_info.region_id
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public String getRegionId() {
        return regionId;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfo withRegionId(String regionId) {
        this.setRegionId(regionId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_inventory_main_info.region_id
     *
     * @param regionId the value for supply_chain..card_inventory_main_info.region_id
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setRegionId(String regionId) {
        this.regionId = regionId == null ? null : regionId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_inventory_main_info.region_name
     *
     * @return the value of supply_chain..card_inventory_main_info.region_name
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public String getRegionName() {
        return regionName;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfo withRegionName(String regionName) {
        this.setRegionName(regionName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_inventory_main_info.region_name
     *
     * @param regionName the value for supply_chain..card_inventory_main_info.region_name
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setRegionName(String regionName) {
        this.regionName = regionName == null ? null : regionName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_inventory_main_info.card_type
     *
     * @return the value of supply_chain..card_inventory_main_info.card_type
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public String getCardType() {
        return cardType;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfo withCardType(String cardType) {
        this.setCardType(cardType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_inventory_main_info.card_type
     *
     * @param cardType the value for supply_chain..card_inventory_main_info.card_type
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setCardType(String cardType) {
        this.cardType = cardType == null ? null : cardType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_inventory_main_info.reserve_quatity
     *
     * @return the value of supply_chain..card_inventory_main_info.reserve_quatity
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public Integer getReserveQuatity() {
        return reserveQuatity;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfo withReserveQuatity(Integer reserveQuatity) {
        this.setReserveQuatity(reserveQuatity);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_inventory_main_info.reserve_quatity
     *
     * @param reserveQuatity the value for supply_chain..card_inventory_main_info.reserve_quatity
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setReserveQuatity(Integer reserveQuatity) {
        this.reserveQuatity = reserveQuatity;
    }

    /**
     * This method returns the value of the database column supply_chain..card_inventory_main_info.current_inventory
     *
     * @return the value of supply_chain..card_inventory_main_info.current_inventory
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public Integer getCurrentInventory() {
        return currentInventory;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfo withCurrentInventory(Integer currentInventory) {
        this.setCurrentInventory(currentInventory);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_inventory_main_info.current_inventory
     *
     * @param currentInventory the value for supply_chain..card_inventory_main_info.current_inventory
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setCurrentInventory(Integer currentInventory) {
        this.currentInventory = currentInventory;
    }

    /**
     * This method returns the value of the database column supply_chain..card_inventory_main_info.total_inventory
     *
     * @return the value of supply_chain..card_inventory_main_info.total_inventory
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public Integer getTotalInventory() {
        return totalInventory;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfo withTotalInventory(Integer totalInventory) {
        this.setTotalInventory(totalInventory);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_inventory_main_info.total_inventory
     *
     * @param totalInventory the value for supply_chain..card_inventory_main_info.total_inventory
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setTotalInventory(Integer totalInventory) {
        this.totalInventory = totalInventory;
    }

    /**
     * This method returns the value of the database column supply_chain..card_inventory_main_info.inventory_threshold
     *
     * @return the value of supply_chain..card_inventory_main_info.inventory_threshold
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public Integer getInventoryThreshold() {
        return inventoryThreshold;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfo withInventoryThreshold(Integer inventoryThreshold) {
        this.setInventoryThreshold(inventoryThreshold);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_inventory_main_info.inventory_threshold
     *
     * @param inventoryThreshold the value for supply_chain..card_inventory_main_info.inventory_threshold
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setInventoryThreshold(Integer inventoryThreshold) {
        this.inventoryThreshold = inventoryThreshold;
    }

    /**
     * This method returns the value of the database column supply_chain..card_inventory_main_info.is_notice
     *
     * @return the value of supply_chain..card_inventory_main_info.is_notice
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public Boolean getIsNotice() {
        return isNotice;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfo withIsNotice(Boolean isNotice) {
        this.setIsNotice(isNotice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_inventory_main_info.is_notice
     *
     * @param isNotice the value for supply_chain..card_inventory_main_info.is_notice
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setIsNotice(Boolean isNotice) {
        this.isNotice = isNotice;
    }

    /**
     * This method returns the value of the database column supply_chain..card_inventory_main_info.inventory_status
     *
     * @return the value of supply_chain..card_inventory_main_info.inventory_status
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public String getInventoryStatus() {
        return inventoryStatus;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfo withInventoryStatus(String inventoryStatus) {
        this.setInventoryStatus(inventoryStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_inventory_main_info.inventory_status
     *
     * @param inventoryStatus the value for supply_chain..card_inventory_main_info.inventory_status
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setInventoryStatus(String inventoryStatus) {
        this.inventoryStatus = inventoryStatus == null ? null : inventoryStatus.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..card_inventory_main_info.create_time
     *
     * @return the value of supply_chain..card_inventory_main_info.create_time
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_inventory_main_info.create_time
     *
     * @param createTime the value for supply_chain..card_inventory_main_info.create_time
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..card_inventory_main_info.update_time
     *
     * @return the value of supply_chain..card_inventory_main_info.update_time
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..card_inventory_main_info.update_time
     *
     * @param updateTime the value for supply_chain..card_inventory_main_info.update_time
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", custCode=").append(custCode);
        sb.append(", custName=").append(custName);
        sb.append(", templateId=").append(templateId);
        sb.append(", templateName=").append(templateName);
        sb.append(", beId=").append(beId);
        sb.append(", proviceName=").append(proviceName);
        sb.append(", regionId=").append(regionId);
        sb.append(", regionName=").append(regionName);
        sb.append(", cardType=").append(cardType);
        sb.append(", reserveQuatity=").append(reserveQuatity);
        sb.append(", currentInventory=").append(currentInventory);
        sb.append(", totalInventory=").append(totalInventory);
        sb.append(", inventoryThreshold=").append(inventoryThreshold);
        sb.append(", isNotice=").append(isNotice);
        sb.append(", inventoryStatus=").append(inventoryStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CardInventoryMainInfo other = (CardInventoryMainInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCustCode() == null ? other.getCustCode() == null : this.getCustCode().equals(other.getCustCode()))
            && (this.getCustName() == null ? other.getCustName() == null : this.getCustName().equals(other.getCustName()))
            && (this.getTemplateId() == null ? other.getTemplateId() == null : this.getTemplateId().equals(other.getTemplateId()))
            && (this.getTemplateName() == null ? other.getTemplateName() == null : this.getTemplateName().equals(other.getTemplateName()))
            && (this.getBeId() == null ? other.getBeId() == null : this.getBeId().equals(other.getBeId()))
            && (this.getProviceName() == null ? other.getProviceName() == null : this.getProviceName().equals(other.getProviceName()))
            && (this.getRegionId() == null ? other.getRegionId() == null : this.getRegionId().equals(other.getRegionId()))
            && (this.getRegionName() == null ? other.getRegionName() == null : this.getRegionName().equals(other.getRegionName()))
            && (this.getCardType() == null ? other.getCardType() == null : this.getCardType().equals(other.getCardType()))
            && (this.getReserveQuatity() == null ? other.getReserveQuatity() == null : this.getReserveQuatity().equals(other.getReserveQuatity()))
            && (this.getCurrentInventory() == null ? other.getCurrentInventory() == null : this.getCurrentInventory().equals(other.getCurrentInventory()))
            && (this.getTotalInventory() == null ? other.getTotalInventory() == null : this.getTotalInventory().equals(other.getTotalInventory()))
            && (this.getInventoryThreshold() == null ? other.getInventoryThreshold() == null : this.getInventoryThreshold().equals(other.getInventoryThreshold()))
            && (this.getIsNotice() == null ? other.getIsNotice() == null : this.getIsNotice().equals(other.getIsNotice()))
            && (this.getInventoryStatus() == null ? other.getInventoryStatus() == null : this.getInventoryStatus().equals(other.getInventoryStatus()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCustCode() == null) ? 0 : getCustCode().hashCode());
        result = prime * result + ((getCustName() == null) ? 0 : getCustName().hashCode());
        result = prime * result + ((getTemplateId() == null) ? 0 : getTemplateId().hashCode());
        result = prime * result + ((getTemplateName() == null) ? 0 : getTemplateName().hashCode());
        result = prime * result + ((getBeId() == null) ? 0 : getBeId().hashCode());
        result = prime * result + ((getProviceName() == null) ? 0 : getProviceName().hashCode());
        result = prime * result + ((getRegionId() == null) ? 0 : getRegionId().hashCode());
        result = prime * result + ((getRegionName() == null) ? 0 : getRegionName().hashCode());
        result = prime * result + ((getCardType() == null) ? 0 : getCardType().hashCode());
        result = prime * result + ((getReserveQuatity() == null) ? 0 : getReserveQuatity().hashCode());
        result = prime * result + ((getCurrentInventory() == null) ? 0 : getCurrentInventory().hashCode());
        result = prime * result + ((getTotalInventory() == null) ? 0 : getTotalInventory().hashCode());
        result = prime * result + ((getInventoryThreshold() == null) ? 0 : getInventoryThreshold().hashCode());
        result = prime * result + ((getIsNotice() == null) ? 0 : getIsNotice().hashCode());
        result = prime * result + ((getInventoryStatus() == null) ? 0 : getInventoryStatus().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     * corresponding to the database table supply_chain..card_inventory_main_info
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        custCode("cust_code", "custCode", "VARCHAR", false),
        custName("cust_name", "custName", "VARCHAR", false),
        templateId("template_id", "templateId", "VARCHAR", false),
        templateName("template_name", "templateName", "VARCHAR", false),
        beId("be_id", "beId", "VARCHAR", false),
        proviceName("provice_name", "proviceName", "VARCHAR", false),
        regionId("region_id", "regionId", "VARCHAR", false),
        regionName("region_name", "regionName", "VARCHAR", false),
        cardType("card_type", "cardType", "VARCHAR", false),
        reserveQuatity("reserve_quatity", "reserveQuatity", "INTEGER", false),
        currentInventory("current_inventory", "currentInventory", "INTEGER", false),
        totalInventory("total_inventory", "totalInventory", "INTEGER", false),
        inventoryThreshold("inventory_threshold", "inventoryThreshold", "INTEGER", false),
        isNotice("is_notice", "isNotice", "BIT", false),
        inventoryStatus("inventory_status", "inventoryStatus", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         * Corresponding to the database table supply_chain..card_inventory_main_info
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..card_inventory_main_info
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..card_inventory_main_info
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        private final String column;

        /**
         * Corresponding to the database table supply_chain..card_inventory_main_info
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         * Corresponding to the database table supply_chain..card_inventory_main_info
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        private final String javaProperty;

        /**
         * Corresponding to the database table supply_chain..card_inventory_main_info
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}