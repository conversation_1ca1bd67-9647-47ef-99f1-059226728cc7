package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.address.PartnerAddress;
import com.chinamobile.iot.sc.request.address.Request4ParAddrAdd;
import com.chinamobile.iot.sc.request.address.Request4ParAddrPage;
import com.chinamobile.iot.sc.request.address.Request4ParAddrUpdate;
import com.chinamobile.iot.sc.response.web.invoice.Data4PartnerAddress;
import com.chinamobile.iot.sc.service.IPartnerAddressService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.chinamobile.iot.sc.common.BaseConstant.MY_READDRESS;

/**
 * @package: com.chinamobile.iot.sc.controller.web
 * @ClassName: PartnerAddressController
 * @description: 合作伙伴地址Controller
 * @author: zyj
 * @create: 2021/12/15 16:16
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@RequestMapping("/osweb/partner/address")
@RestController
public class PartnerAddressController {

    @Resource
    private IPartnerAddressService addressService;

    @Auth(authCode = {BaseConstant.ACCOUNT_READDRESS,MY_READDRESS})
    @PostMapping("/add")
    BaseAnswer<Void> addPartnerAddress(@Valid @RequestBody Request4ParAddrAdd request
            ,@RequestHeader(Constant.HEADER_KEY_USER_ID) String partnerId
            ,@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return addressService.addPartnerAddress(request, partnerId, loginIfo4Redis,ip);
    }

    @Auth(authCode = {BaseConstant.ACCOUNT_READDRESS,MY_READDRESS})
    @GetMapping("/delete")
    BaseAnswer<Void> deletePartnerAddress(String id, @RequestParam(required = false) String partnerId){
        return addressService.deletePartnerAddress(id, partnerId);
    }

    @Auth(authCode = {BaseConstant.ACCOUNT_READDRESS,MY_READDRESS})
    @PostMapping("/update")
    BaseAnswer<Void> updatePartnerAddress(@Valid @RequestBody Request4ParAddrUpdate request
            ,@RequestHeader(Constant.HEADER_KEY_USER_ID) String partnerId
            ,@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return addressService.updatePartnerAddress(request, partnerId, loginIfo4Redis);
    }

    @Auth(authCode = {BaseConstant.ACCOUNT_READDRESS,MY_READDRESS})
    @PostMapping("/findPage")
    BaseAnswer<PageData<Data4PartnerAddress>> findPage(@RequestBody Request4ParAddrPage request
            , @RequestHeader(Constant.HEADER_KEY_USER_ID) String partnerId
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return addressService.findPage(request, partnerId, loginIfo4Redis);
    }

    @GetMapping("/isLast")
    public BaseAnswer<String> isLastPAddress(String id, String partnerId) {
        return addressService.isLastPAddress(id, partnerId);
    }


    /**
     *  获取地址详情
     * @param id
     * @return
     */
    @GetMapping("/getPartnerAddrById")
    public BaseAnswer<Data4PartnerAddress> partnerAddrById(String id) {
        BaseAnswer baseAnswer = new BaseAnswer();
        Data4PartnerAddress data4PartnerAddress = addressService.partnerAddrById(id);
        baseAnswer.setData(data4PartnerAddress);
        return baseAnswer;
    }

}
