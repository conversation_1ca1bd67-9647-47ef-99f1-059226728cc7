package com.chinamobile.iot.sc.pojo.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2022/9/20 9:29
 * @description:
 **/
@Data
public class OperateRecordDTO {

    /**
     * 主键

     */
    private String id;

    /**
     * 日志时间
     */
    private Date time;

    /**
     * ip地址
     */
    private String ip;

    /**
     * 操作人姓名，超级管理员中心为空，其他中心为用户姓名
     */
    private String operatorName;

    /**
     * 操作人账号,超级管理员中心为固定账号，其他中心为用户账号（手机号)
     */
    private String operatorAccount;

    /**
     * 角色名称，枚举值：超级管理员、运营管理员、客服管理员、合作伙伴主、合作伙伴从、产品运营管理员、积分管理员、客户经理、一级分销员、二级分销员、大屏超管、大屏管理员、大屏省账号
     */
    private String roleStr;

    /**
     * 操作模块，枚举值：参照sheet表“操作日志（功能目录）”的“一级菜单目录”
     */
    private String modulesStr;

    /**
     * 操作子模块，枚举值：参照sheet表“操作日志（功能目录）”的“二级菜单目录”
     */
    private String subModuleStr;

    /**
     * 操作内容，生成模板参照sheet表“操作日志（功能目录）”的“操作内容”
     */
    private String content;

    /**
     * 数据来源 0--PC端  1--H5端
     */
    private Integer dataFrom;

    private String dataFromStr;
    /**
     * 操作结果 0--成功 1--失败
     */
    private Integer result;
    /**
     * 失败原因
     */
    private String failReason;

}
