package com.chinamobile.iot.sc.pojo.dto;

import lombok.Data;

/**
 * created by l<PERSON><PERSON><PERSON> on 2024/6/3 17:22
 */
@Data
public class AtomInfoDTO {


    /**
     * 原子商品名称
     */
    private String atomOfferingName;

    /**
     * 原子商品编码
     */
    private String atomOfferingCode;

    /**
     * 原子数量
     */
    private Integer atomQuantity;

    /**
     * 终端物料编码
     */
    private String extHardOfferingCode;

    /**
     * 平台软件编码
     */
    private String extSoftOfferingCode;

    /**
     * 原子商品销售价
     */
    private Long salePrice;

    /**
     * 原子商品结算单价
     */
    private Long settlePrice;

    /**
     * 原子商品结算单价(专-合)
     */
    private String settlePricePartner;

    /**
     * cmiot账目项id
     */
    private String chargeId;
}
