package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CardChooseDeliveryExample {
    /**
     * Corresponding to the database table supply_chain..card_choose_delivery
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    protected String orderByClause;

    /**
     * Corresponding to the database table supply_chain..card_choose_delivery
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    protected boolean distinct;

    /**
     * Corresponding to the database table supply_chain..card_choose_delivery
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    public CardChooseDeliveryExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    public CardChooseDeliveryExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    public CardChooseDeliveryExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    public static Criteria newAndCreateCriteria() {
        CardChooseDeliveryExample example = new CardChooseDeliveryExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    public CardChooseDeliveryExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    public CardChooseDeliveryExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     * Corresponding to the database table supply_chain..card_choose_delivery
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdIsNull() {
            addCriterion("atom_order_id is null");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdIsNotNull() {
            addCriterion("atom_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdEqualTo(String value) {
            addCriterion("atom_order_id =", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("atom_order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotEqualTo(String value) {
            addCriterion("atom_order_id <>", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("atom_order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdGreaterThan(String value) {
            addCriterion("atom_order_id >", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdGreaterThanColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("atom_order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("atom_order_id >=", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdGreaterThanOrEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("atom_order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLessThan(String value) {
            addCriterion("atom_order_id <", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLessThanColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("atom_order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLessThanOrEqualTo(String value) {
            addCriterion("atom_order_id <=", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLessThanOrEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("atom_order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLike(String value) {
            addCriterion("atom_order_id like", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotLike(String value) {
            addCriterion("atom_order_id not like", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdIn(List<String> values) {
            addCriterion("atom_order_id in", values, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotIn(List<String> values) {
            addCriterion("atom_order_id not in", values, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdBetween(String value1, String value2) {
            addCriterion("atom_order_id between", value1, value2, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotBetween(String value1, String value2) {
            addCriterion("atom_order_id not between", value1, value2, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionIsNull() {
            addCriterion("device_version is null");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionIsNotNull() {
            addCriterion("device_version is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionEqualTo(String value) {
            addCriterion("device_version =", value, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("device_version = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceVersionNotEqualTo(String value) {
            addCriterion("device_version <>", value, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionNotEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("device_version <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceVersionGreaterThan(String value) {
            addCriterion("device_version >", value, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionGreaterThanColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("device_version > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceVersionGreaterThanOrEqualTo(String value) {
            addCriterion("device_version >=", value, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionGreaterThanOrEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("device_version >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceVersionLessThan(String value) {
            addCriterion("device_version <", value, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionLessThanColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("device_version < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceVersionLessThanOrEqualTo(String value) {
            addCriterion("device_version <=", value, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionLessThanOrEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("device_version <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeviceVersionLike(String value) {
            addCriterion("device_version like", value, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionNotLike(String value) {
            addCriterion("device_version not like", value, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionIn(List<String> values) {
            addCriterion("device_version in", values, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionNotIn(List<String> values) {
            addCriterion("device_version not in", values, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionBetween(String value1, String value2) {
            addCriterion("device_version between", value1, value2, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionNotBetween(String value1, String value2) {
            addCriterion("device_version not between", value1, value2, "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andImeiIsNull() {
            addCriterion("imei is null");
            return (Criteria) this;
        }

        public Criteria andImeiIsNotNull() {
            addCriterion("imei is not null");
            return (Criteria) this;
        }

        public Criteria andImeiEqualTo(String value) {
            addCriterion("imei =", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("imei = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImeiNotEqualTo(String value) {
            addCriterion("imei <>", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiNotEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("imei <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImeiGreaterThan(String value) {
            addCriterion("imei >", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiGreaterThanColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("imei > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImeiGreaterThanOrEqualTo(String value) {
            addCriterion("imei >=", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiGreaterThanOrEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("imei >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImeiLessThan(String value) {
            addCriterion("imei <", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiLessThanColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("imei < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImeiLessThanOrEqualTo(String value) {
            addCriterion("imei <=", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiLessThanOrEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("imei <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andImeiLike(String value) {
            addCriterion("imei like", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiNotLike(String value) {
            addCriterion("imei not like", value, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiIn(List<String> values) {
            addCriterion("imei in", values, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiNotIn(List<String> values) {
            addCriterion("imei not in", values, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiBetween(String value1, String value2) {
            addCriterion("imei between", value1, value2, "imei");
            return (Criteria) this;
        }

        public Criteria andImeiNotBetween(String value1, String value2) {
            addCriterion("imei not between", value1, value2, "imei");
            return (Criteria) this;
        }

        public Criteria andTempIccidIsNull() {
            addCriterion("temp_iccid is null");
            return (Criteria) this;
        }

        public Criteria andTempIccidIsNotNull() {
            addCriterion("temp_iccid is not null");
            return (Criteria) this;
        }

        public Criteria andTempIccidEqualTo(String value) {
            addCriterion("temp_iccid =", value, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("temp_iccid = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTempIccidNotEqualTo(String value) {
            addCriterion("temp_iccid <>", value, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidNotEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("temp_iccid <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTempIccidGreaterThan(String value) {
            addCriterion("temp_iccid >", value, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidGreaterThanColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("temp_iccid > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTempIccidGreaterThanOrEqualTo(String value) {
            addCriterion("temp_iccid >=", value, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidGreaterThanOrEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("temp_iccid >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTempIccidLessThan(String value) {
            addCriterion("temp_iccid <", value, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidLessThanColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("temp_iccid < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTempIccidLessThanOrEqualTo(String value) {
            addCriterion("temp_iccid <=", value, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidLessThanOrEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("temp_iccid <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTempIccidLike(String value) {
            addCriterion("temp_iccid like", value, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidNotLike(String value) {
            addCriterion("temp_iccid not like", value, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidIn(List<String> values) {
            addCriterion("temp_iccid in", values, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidNotIn(List<String> values) {
            addCriterion("temp_iccid not in", values, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidBetween(String value1, String value2) {
            addCriterion("temp_iccid between", value1, value2, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andTempIccidNotBetween(String value1, String value2) {
            addCriterion("temp_iccid not between", value1, value2, "tempIccid");
            return (Criteria) this;
        }

        public Criteria andMsisdnIsNull() {
            addCriterion("msisdn is null");
            return (Criteria) this;
        }

        public Criteria andMsisdnIsNotNull() {
            addCriterion("msisdn is not null");
            return (Criteria) this;
        }

        public Criteria andMsisdnEqualTo(String value) {
            addCriterion("msisdn =", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("msisdn = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMsisdnNotEqualTo(String value) {
            addCriterion("msisdn <>", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnNotEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("msisdn <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMsisdnGreaterThan(String value) {
            addCriterion("msisdn >", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnGreaterThanColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("msisdn > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMsisdnGreaterThanOrEqualTo(String value) {
            addCriterion("msisdn >=", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnGreaterThanOrEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("msisdn >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMsisdnLessThan(String value) {
            addCriterion("msisdn <", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnLessThanColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("msisdn < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMsisdnLessThanOrEqualTo(String value) {
            addCriterion("msisdn <=", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnLessThanOrEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("msisdn <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMsisdnLike(String value) {
            addCriterion("msisdn like", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnNotLike(String value) {
            addCriterion("msisdn not like", value, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnIn(List<String> values) {
            addCriterion("msisdn in", values, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnNotIn(List<String> values) {
            addCriterion("msisdn not in", values, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnBetween(String value1, String value2) {
            addCriterion("msisdn between", value1, value2, "msisdn");
            return (Criteria) this;
        }

        public Criteria andMsisdnNotBetween(String value1, String value2) {
            addCriterion("msisdn not between", value1, value2, "msisdn");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(CardChooseDelivery.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLikeInsensitive(String value) {
            addCriterion("upper(atom_order_id) like", value.toUpperCase(), "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLikeInsensitive(String value) {
            addCriterion("upper(order_id) like", value.toUpperCase(), "orderId");
            return (Criteria) this;
        }

        public Criteria andDeviceVersionLikeInsensitive(String value) {
            addCriterion("upper(device_version) like", value.toUpperCase(), "deviceVersion");
            return (Criteria) this;
        }

        public Criteria andImeiLikeInsensitive(String value) {
            addCriterion("upper(imei) like", value.toUpperCase(), "imei");
            return (Criteria) this;
        }

        public Criteria andTempIccidLikeInsensitive(String value) {
            addCriterion("upper(temp_iccid) like", value.toUpperCase(), "tempIccid");
            return (Criteria) this;
        }

        public Criteria andMsisdnLikeInsensitive(String value) {
            addCriterion("upper(msisdn) like", value.toUpperCase(), "msisdn");
            return (Criteria) this;
        }
    }

    /**
     * Corresponding to the database table supply_chain..card_choose_delivery
     *
     * @mbg.generated do_not_delete_during_merge Thu Jun 26 17:36:32 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         * Corresponding to the database table supply_chain..card_choose_delivery
         *
         * @mbg.generated Thu Jun 26 17:36:32 CST 2025
         */
        private CardChooseDeliveryExample example;

        /**
         *
         * @mbg.generated Thu Jun 26 17:36:32 CST 2025
         */
        protected Criteria(CardChooseDeliveryExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Thu Jun 26 17:36:32 CST 2025
         */
        public CardChooseDeliveryExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Thu Jun 26 17:36:32 CST 2025
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Jun 26 17:36:32 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Jun 26 17:36:32 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Thu Jun 26 17:36:32 CST 2025
             */
            Criteria add(Criteria add);
        }
    }

    /**
     * Corresponding to the database table supply_chain..card_choose_delivery
     *
     * @mbg.generated Thu Jun 26 17:36:32 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Thu Jun 26 17:36:32 CST 2025
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Thu Jun 26 17:36:32 CST 2025
         */
        void example(com.chinamobile.iot.sc.pojo.entity.CardChooseDeliveryExample example);
    }
}