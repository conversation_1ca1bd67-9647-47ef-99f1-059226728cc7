package com.chinamobile.iot.sc.exception;

import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;

import static com.chinamobile.iot.sc.exceptions.ExcepStatus.createInstance;

/**
 * @Author: YSC
 * @Date: 2021/11/10 15:54
 * @Description: 异常返回映射
 */
public class StatusConstant extends BaseErrorConstant {
    private static final  String PREF = "30";
    public static final ExcepStatus PRODUCT_NOT_EXIST = createInstance(PREF+"001", "该原子商品不存在！");
    public static final ExcepStatus ALREADY_BIND = createInstance(PREF+"002", "该原子商品已绑定请勿重新添加！");
    public static final ExcepStatus ORDER_NOT_EXIST = createInstance(PREF+"003", "订单不存在");
    public static final ExcepStatus AUTH_ERROR = createInstance(PREF+"004", "用户无权限");
    public static final ExcepStatus DUPLICATE_LOGISTICS = createInstance(PREF+"005", "请勿重复添加物流信息");
    public static final ExcepStatus SYNC_IOT_FAILED = createInstance(PREF+"006", "同步到IOT商城失败，请重试");
    public static final ExcepStatus NO_INVENTORY = createInstance(PREF + "007", "库存容量不足，请修改减少库存量");
    public static final ExcepStatus NO_AUDIT_REASON = createInstance(PREF + "010", "请输入审核不通过原因");
    ;
    public static final ExcepStatus NO_ORDER = createInstance(PREF+"008", "查无此订单");
    public static final ExcepStatus NO_COOPERATOR = createInstance(PREF + "009", "请先添加合作伙伴");
    public static final ExcepStatus AUDIT_FAIL = createInstance(PREF + "010", "订单已审核");
    public static final ExcepStatus LOGISTICS_ERROR = createInstance(PREF + "011", "订单状态有误");
    public static final ExcepStatus ORDER_NULL_ERROR = createInstance(PREF + "012", "订单号不能为空！");
    public static final ExcepStatus INVOICE_RECORD_NOT_EXIST = createInstance(PREF + "013", "没有对应记录");
    public static final ExcepStatus LOGISTICS_ERROR_WAITREVEIVE = createInstance(PREF + "014", "订单处于已发货状态");
    public static final ExcepStatus DELIVER_SN_REQUIRED = createInstance(PREF + "015","合同履约类产品必须填入SN");
    public static final ExcepStatus DELIVER_SN_FORMATERROR = createInstance(PREF + "016", "SN格式有误，或包含中文字符，请检查分隔符");
    public static final ExcepStatus DELIVER_NOT_SUPPORT = createInstance(PREF + "017", "系统不支持合同履约或代销以外的产品发货");
    public static final ExcepStatus DELIVER_ORDER_NOT_EXIST = createInstance(PREF+"018", "硬件原子订单不存在");
    public static final ExcepStatus INVENTORY_MODE_CONFING_KX = createInstance(PREF+"019", "模式已经确定，无法再修改");
    public static final ExcepStatus INVENTORY_WARN_SIZE_KX = createInstance(PREF+"020", "预警值不得低于0，不得高于9999");
    public static final ExcepStatus DELIVER_KX_TYPE_UNSUPPORT = createInstance(PREF+"021", "不支持发货的卡+X商品类型");
    /**
     * 发票管理相关业务码
     */
    public static ExcepStatus INVOICE_INFO_FILE_ERROR = createInstance(PREF+"100", "录入发票信息、发票文件不一致！");
    public static ExcepStatus INVOICE_TOTAL_PRICE_ERROR = createInstance(PREF+"101", "录入发票金额必须等于订单金额一致！");
    public static ExcepStatus INVOICEENTRYREQUEST_JSON_ERROR = createInstance(PREF+"102", "解析InvoiceEntryRequest-jsonArray失败！");
    public static ExcepStatus INVOICEREVREQUEST_JSON_ERROR = createInstance(PREF+"103", "解析InvoiceRevRequest-jsonArray失败！");
    public static ExcepStatus REQUEST_FILE_ERROR = createInstance(PREF+"104", "请求或发票文件为空！");
    public static ExcepStatus REVERSE_INFO_REC_NULL = createInstance(PREF+"105", "未查询到冲红发票记录！");
    public static ExcepStatus REVERSE_REC_SUCC_REPEAT = createInstance(PREF+"106", "勿重复提交，已完成冲红！");

    public static ExcepStatus REVERSE_INFO_ATOMORDER_NULL = createInstance(PREF+"107","冲红关联的原子订单不存在！");
    public static ExcepStatus REQUEST_ORDER_NOT_EXISTS = createInstance(PREF+"108", "未找到原子订单对应的申请发票记录！");

    public static ExcepStatus ORDER_HENAN_REAL_NAME_UPDATE = createInstance(PREF+"109", "河南合同履约硬件、卡+x订单硬件（产品类型为123的）处于“待发货”状态订单才能修改实名！");

    /**
     * 对象存储相关
     */
    public static final ExcepStatus OSS_UPLOAD_SAVE_LOCAL_ERROR = createInstance(PREF+"200", "文件转存本地错误");
    public static final ExcepStatus OSS_UPLOAD_ERROR = createInstance(PREF+"201", "上传文件失败");
    public static final ExcepStatus OSS_DEL_OR_SETEXPIRED_ERROR = createInstance(PREF+"202", "删除(或过期设置)文件失败");
    public static final ExcepStatus OSS_QUERY_ERROR = createInstance(PREF+"203", "获取对象存储地址失败");
    public static final ExcepStatus OSS_COPY_ERROR = createInstance(PREF+"204", "复制存储数据失败");
    public static final ExcepStatus UNEXIST_FILE_ERROR = createInstance(PREF+"205", "文件不存在");
    public static final ExcepStatus UNEXIST_DELFILE_ERROR = createInstance(PREF+"206", "待删除文件不存在");
    public static final ExcepStatus NO_UPLOAD_FILE = createInstance(PREF+"207", "无上传文件");
    public static final ExcepStatus OSS_DEL_ERROR = createInstance(PREF+"208","删除文件失败！");
    /**
     * 退换货地址相关
     */
    public static final ExcepStatus RETURN_CONTACT_ERROR = createInstance(PREF+"300", "联系姓名，2-5汉字！");
    public static final ExcepStatus PARTNER_ADDRESS_ERROR = createInstance(PREF+"301", "退换货地址id不存在！");
    public static final ExcepStatus PARTNER_ADDRESS_NULL_ERROR = createInstance(PREF+"302", "退换地址不能为空，删除前请新增其他地址。");
    public static final ExcepStatus ADDRESS_UPDATE_DEFAULT_ERROR = createInstance(PREF+"303", "默认退换地址不能为空，请添加其他地址后再修改。");
    public static final ExcepStatus PARTNER_ID_NULL_ERROR = createInstance(PREF+"304", "合作伙伴id不能为空！");
    public static final ExcepStatus PARTNER_OPERATE_NULL_ERROR = createInstance(PREF+"305", "新增主合作地址操作标识不能为空！");
    /**
     * 货物清单下载
     */
    public static final ExcepStatus GOODS_LIST_TEMP_ERROR = createInstance(PREF+"400", "获取货单下载模板失败！");
    public static final ExcepStatus DATA_IMPORT_AUTH_ERROR = createInstance(PREF+"401", "数字导入鉴权失败！");
    /**
     *订单备注，催单相关
     *
     */
    public static final ExcepStatus ORDER_REMARK_LEN_ERROR = createInstance(PREF+"500", "订单备注不能超过500字符！");
    public static final ExcepStatus ORDER_REMINDER__ERROR = createInstance(PREF+"501", "该状态订单不允许催单！");
    public static final ExcepStatus QLY_ORDER_CAN_NOT_SEND = createInstance(PREF+"502", "千里眼服务没有开通成功，不能发货");

    public static final ExcepStatus LOGISTICS_QUERY__ERROR = createInstance(PREF+"601", "物流详情信息查询错误！");
    public static final ExcepStatus SHUNFENG_QUERY_PHONE_NOT_NULL = createInstance(PREF+"603", "物流详情查询顺丰时电话号码不能为空！");
    public static final ExcepStatus LINK_QUERY__ERROR = createInstance(PREF+"602", "获取商品分享链接查询错误!");

    /**
     * 产品引入上下架
     */
    public static final ExcepStatus DATE_FORMAT_ANALYSIS_ERROR = createInstance(PREF+"700", "时间格式解析错误！");
    public static final ExcepStatus PRODUCT_UPDATE_ONLY_NO_PASS = createInstance(PREF+"701", "只有未通过审核产品才能编辑！");
    public static final ExcepStatus EXPORT_PRODUCT_ONLINE_INFO_ERROR = createInstance(PREF+"800", "获取货单下载模板失败！");
    public static final ExcepStatus EXPORT_SALES_REPORT_INFO_ERROR = createInstance(PREF+"801", "获取商城订单销售报表失败！");

    public static final ExcepStatus SSO_LOGIN_FAILED = createInstance(PREF+"900", "单点登录失败");

    public static final ExcepStatus ONE_KEY_LOGIN_FAILED = createInstance(PREF+"901", "一键登录失败");
    public static final ExcepStatus CARD_CHECK_FAILED = createInstance(PREF+"902", "码号校验失败");

    public static final ExcepStatus PRODUCT_NAVIGATION_NOT_EXIST = createInstance(PREF+"1001", "商品导航目录不存在");

    public static final ExcepStatus FAILED_GENERATE_SOFTWARE_SERVICES = createInstance(PREF+"1010", "生成软件服务列表失败");
    public static final ExcepStatus SYNC_PROVINCE_FAILED =  createInstance(PREF + "1020", "同步到省侧失败！");
}
