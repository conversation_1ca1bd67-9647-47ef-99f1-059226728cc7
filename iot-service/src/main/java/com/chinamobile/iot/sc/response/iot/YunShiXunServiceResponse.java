package com.chinamobile.iot.sc.response.iot;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/23 15:23
 * @description 云视讯服务开通
 */
@Data
public class YunShiXunServiceResponse implements Serializable {

    /**
     * 响应状态码，200响应成功，1响应失败
     */
    private Integer code;
    /**
     * 响应消息，简单的文字说明
     */
    private String msg;
    /**
     * 响应时间戳，单位为ms
     */
    private Long timestamp;

    /**
     * 用户数组
     */
    private List<YunShiXunPhoneCheckResponse.UserData> userData;

    /**
     * 订单号
     */
    private String orderNumber;

}
