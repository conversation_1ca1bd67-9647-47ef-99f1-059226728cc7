package com.chinamobile.iot.sc.pojo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ProductFlowInstanceAtomExample {
    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    public ProductFlowInstanceAtomExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    public ProductFlowInstanceAtomExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    public ProductFlowInstanceAtomExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    public static Criteria newAndCreateCriteria() {
        ProductFlowInstanceAtomExample example = new ProductFlowInstanceAtomExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    public ProductFlowInstanceAtomExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    public ProductFlowInstanceAtomExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdIsNull() {
            addCriterion("flow_instance_id is null");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdIsNotNull() {
            addCriterion("flow_instance_id is not null");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdEqualTo(String value) {
            addCriterion("flow_instance_id =", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("flow_instance_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotEqualTo(String value) {
            addCriterion("flow_instance_id <>", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("flow_instance_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdGreaterThan(String value) {
            addCriterion("flow_instance_id >", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("flow_instance_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdGreaterThanOrEqualTo(String value) {
            addCriterion("flow_instance_id >=", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("flow_instance_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLessThan(String value) {
            addCriterion("flow_instance_id <", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("flow_instance_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLessThanOrEqualTo(String value) {
            addCriterion("flow_instance_id <=", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("flow_instance_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLike(String value) {
            addCriterion("flow_instance_id like", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotLike(String value) {
            addCriterion("flow_instance_id not like", value, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdIn(List<String> values) {
            addCriterion("flow_instance_id in", values, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotIn(List<String> values) {
            addCriterion("flow_instance_id not in", values, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdBetween(String value1, String value2) {
            addCriterion("flow_instance_id between", value1, value2, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdNotBetween(String value1, String value2) {
            addCriterion("flow_instance_id not between", value1, value2, "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowIdIsNull() {
            addCriterion("flow_id is null");
            return (Criteria) this;
        }

        public Criteria andFlowIdIsNotNull() {
            addCriterion("flow_id is not null");
            return (Criteria) this;
        }

        public Criteria andFlowIdEqualTo(String value) {
            addCriterion("flow_id =", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("flow_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdNotEqualTo(String value) {
            addCriterion("flow_id <>", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("flow_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThan(String value) {
            addCriterion("flow_id >", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("flow_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThanOrEqualTo(String value) {
            addCriterion("flow_id >=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("flow_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThan(String value) {
            addCriterion("flow_id <", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("flow_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThanOrEqualTo(String value) {
            addCriterion("flow_id <=", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("flow_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFlowIdLike(String value) {
            addCriterion("flow_id like", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotLike(String value) {
            addCriterion("flow_id not like", value, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdIn(List<String> values) {
            addCriterion("flow_id in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotIn(List<String> values) {
            addCriterion("flow_id not in", values, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdBetween(String value1, String value2) {
            addCriterion("flow_id between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andFlowIdNotBetween(String value1, String value2) {
            addCriterion("flow_id not between", value1, value2, "flowId");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNull() {
            addCriterion("spu_code is null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNotNull() {
            addCriterion("spu_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualTo(String value) {
            addCriterion("spu_code =", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("spu_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualTo(String value) {
            addCriterion("spu_code <>", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("spu_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThan(String value) {
            addCriterion("spu_code >", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("spu_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("spu_code >=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("spu_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThan(String value) {
            addCriterion("spu_code <", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("spu_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualTo(String value) {
            addCriterion("spu_code <=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("spu_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLike(String value) {
            addCriterion("spu_code like", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotLike(String value) {
            addCriterion("spu_code not like", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIn(List<String> values) {
            addCriterion("spu_code in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotIn(List<String> values) {
            addCriterion("spu_code not in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeBetween(String value1, String value2) {
            addCriterion("spu_code between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotBetween(String value1, String value2) {
            addCriterion("spu_code not between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNull() {
            addCriterion("sku_code is null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNotNull() {
            addCriterion("sku_code is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeEqualTo(String value) {
            addCriterion("sku_code =", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sku_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotEqualTo(String value) {
            addCriterion("sku_code <>", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sku_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThan(String value) {
            addCriterion("sku_code >", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sku_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sku_code >=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sku_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThan(String value) {
            addCriterion("sku_code <", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sku_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanOrEqualTo(String value) {
            addCriterion("sku_code <=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sku_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeLike(String value) {
            addCriterion("sku_code like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotLike(String value) {
            addCriterion("sku_code not like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIn(List<String> values) {
            addCriterion("sku_code in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotIn(List<String> values) {
            addCriterion("sku_code not in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeBetween(String value1, String value2) {
            addCriterion("sku_code between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotBetween(String value1, String value2) {
            addCriterion("sku_code not between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andAtomNameIsNull() {
            addCriterion("atom_name is null");
            return (Criteria) this;
        }

        public Criteria andAtomNameIsNotNull() {
            addCriterion("atom_name is not null");
            return (Criteria) this;
        }

        public Criteria andAtomNameEqualTo(String value) {
            addCriterion("atom_name =", value, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("atom_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomNameNotEqualTo(String value) {
            addCriterion("atom_name <>", value, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("atom_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomNameGreaterThan(String value) {
            addCriterion("atom_name >", value, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("atom_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomNameGreaterThanOrEqualTo(String value) {
            addCriterion("atom_name >=", value, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("atom_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomNameLessThan(String value) {
            addCriterion("atom_name <", value, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("atom_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomNameLessThanOrEqualTo(String value) {
            addCriterion("atom_name <=", value, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("atom_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomNameLike(String value) {
            addCriterion("atom_name like", value, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameNotLike(String value) {
            addCriterion("atom_name not like", value, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameIn(List<String> values) {
            addCriterion("atom_name in", values, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameNotIn(List<String> values) {
            addCriterion("atom_name not in", values, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameBetween(String value1, String value2) {
            addCriterion("atom_name between", value1, value2, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameNotBetween(String value1, String value2) {
            addCriterion("atom_name not between", value1, value2, "atomName");
            return (Criteria) this;
        }

        public Criteria andSettlePriceIsNull() {
            addCriterion("settle_price is null");
            return (Criteria) this;
        }

        public Criteria andSettlePriceIsNotNull() {
            addCriterion("settle_price is not null");
            return (Criteria) this;
        }

        public Criteria andSettlePriceEqualTo(Long value) {
            addCriterion("settle_price =", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("settle_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceNotEqualTo(Long value) {
            addCriterion("settle_price <>", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("settle_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceGreaterThan(Long value) {
            addCriterion("settle_price >", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("settle_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceGreaterThanOrEqualTo(Long value) {
            addCriterion("settle_price >=", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("settle_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceLessThan(Long value) {
            addCriterion("settle_price <", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("settle_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceLessThanOrEqualTo(Long value) {
            addCriterion("settle_price <=", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("settle_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceIn(List<Long> values) {
            addCriterion("settle_price in", values, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceNotIn(List<Long> values) {
            addCriterion("settle_price not in", values, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceBetween(Long value1, Long value2) {
            addCriterion("settle_price between", value1, value2, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceNotBetween(Long value1, Long value2) {
            addCriterion("settle_price not between", value1, value2, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceCheckIsNull() {
            addCriterion("settle_price_check is null");
            return (Criteria) this;
        }

        public Criteria andSettlePriceCheckIsNotNull() {
            addCriterion("settle_price_check is not null");
            return (Criteria) this;
        }

        public Criteria andSettlePriceCheckEqualTo(Long value) {
            addCriterion("settle_price_check =", value, "settlePriceCheck");
            return (Criteria) this;
        }

        public Criteria andSettlePriceCheckEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("settle_price_check = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceCheckNotEqualTo(Long value) {
            addCriterion("settle_price_check <>", value, "settlePriceCheck");
            return (Criteria) this;
        }

        public Criteria andSettlePriceCheckNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("settle_price_check <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceCheckGreaterThan(Long value) {
            addCriterion("settle_price_check >", value, "settlePriceCheck");
            return (Criteria) this;
        }

        public Criteria andSettlePriceCheckGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("settle_price_check > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceCheckGreaterThanOrEqualTo(Long value) {
            addCriterion("settle_price_check >=", value, "settlePriceCheck");
            return (Criteria) this;
        }

        public Criteria andSettlePriceCheckGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("settle_price_check >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceCheckLessThan(Long value) {
            addCriterion("settle_price_check <", value, "settlePriceCheck");
            return (Criteria) this;
        }

        public Criteria andSettlePriceCheckLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("settle_price_check < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceCheckLessThanOrEqualTo(Long value) {
            addCriterion("settle_price_check <=", value, "settlePriceCheck");
            return (Criteria) this;
        }

        public Criteria andSettlePriceCheckLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("settle_price_check <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceCheckIn(List<Long> values) {
            addCriterion("settle_price_check in", values, "settlePriceCheck");
            return (Criteria) this;
        }

        public Criteria andSettlePriceCheckNotIn(List<Long> values) {
            addCriterion("settle_price_check not in", values, "settlePriceCheck");
            return (Criteria) this;
        }

        public Criteria andSettlePriceCheckBetween(Long value1, Long value2) {
            addCriterion("settle_price_check between", value1, value2, "settlePriceCheck");
            return (Criteria) this;
        }

        public Criteria andSettlePriceCheckNotBetween(Long value1, Long value2) {
            addCriterion("settle_price_check not between", value1, value2, "settlePriceCheck");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("unit = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("unit <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("unit > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("unit >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("unit < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("unit <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andServiceContentIsNull() {
            addCriterion("service_content is null");
            return (Criteria) this;
        }

        public Criteria andServiceContentIsNotNull() {
            addCriterion("service_content is not null");
            return (Criteria) this;
        }

        public Criteria andServiceContentEqualTo(String value) {
            addCriterion("service_content =", value, "serviceContent");
            return (Criteria) this;
        }

        public Criteria andServiceContentEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("service_content = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceContentNotEqualTo(String value) {
            addCriterion("service_content <>", value, "serviceContent");
            return (Criteria) this;
        }

        public Criteria andServiceContentNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("service_content <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceContentGreaterThan(String value) {
            addCriterion("service_content >", value, "serviceContent");
            return (Criteria) this;
        }

        public Criteria andServiceContentGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("service_content > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceContentGreaterThanOrEqualTo(String value) {
            addCriterion("service_content >=", value, "serviceContent");
            return (Criteria) this;
        }

        public Criteria andServiceContentGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("service_content >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceContentLessThan(String value) {
            addCriterion("service_content <", value, "serviceContent");
            return (Criteria) this;
        }

        public Criteria andServiceContentLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("service_content < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceContentLessThanOrEqualTo(String value) {
            addCriterion("service_content <=", value, "serviceContent");
            return (Criteria) this;
        }

        public Criteria andServiceContentLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("service_content <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceContentLike(String value) {
            addCriterion("service_content like", value, "serviceContent");
            return (Criteria) this;
        }

        public Criteria andServiceContentNotLike(String value) {
            addCriterion("service_content not like", value, "serviceContent");
            return (Criteria) this;
        }

        public Criteria andServiceContentIn(List<String> values) {
            addCriterion("service_content in", values, "serviceContent");
            return (Criteria) this;
        }

        public Criteria andServiceContentNotIn(List<String> values) {
            addCriterion("service_content not in", values, "serviceContent");
            return (Criteria) this;
        }

        public Criteria andServiceContentBetween(String value1, String value2) {
            addCriterion("service_content between", value1, value2, "serviceContent");
            return (Criteria) this;
        }

        public Criteria andServiceContentNotBetween(String value1, String value2) {
            addCriterion("service_content not between", value1, value2, "serviceContent");
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdIsNull() {
            addCriterion("cmiot_cost_project_id is null");
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdIsNotNull() {
            addCriterion("cmiot_cost_project_id is not null");
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdEqualTo(String value) {
            addCriterion("cmiot_cost_project_id =", value, "cmiotCostProjectId");
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("cmiot_cost_project_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdNotEqualTo(String value) {
            addCriterion("cmiot_cost_project_id <>", value, "cmiotCostProjectId");
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("cmiot_cost_project_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdGreaterThan(String value) {
            addCriterion("cmiot_cost_project_id >", value, "cmiotCostProjectId");
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("cmiot_cost_project_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdGreaterThanOrEqualTo(String value) {
            addCriterion("cmiot_cost_project_id >=", value, "cmiotCostProjectId");
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("cmiot_cost_project_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdLessThan(String value) {
            addCriterion("cmiot_cost_project_id <", value, "cmiotCostProjectId");
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("cmiot_cost_project_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdLessThanOrEqualTo(String value) {
            addCriterion("cmiot_cost_project_id <=", value, "cmiotCostProjectId");
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("cmiot_cost_project_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdLike(String value) {
            addCriterion("cmiot_cost_project_id like", value, "cmiotCostProjectId");
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdNotLike(String value) {
            addCriterion("cmiot_cost_project_id not like", value, "cmiotCostProjectId");
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdIn(List<String> values) {
            addCriterion("cmiot_cost_project_id in", values, "cmiotCostProjectId");
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdNotIn(List<String> values) {
            addCriterion("cmiot_cost_project_id not in", values, "cmiotCostProjectId");
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdBetween(String value1, String value2) {
            addCriterion("cmiot_cost_project_id between", value1, value2, "cmiotCostProjectId");
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdNotBetween(String value1, String value2) {
            addCriterion("cmiot_cost_project_id not between", value1, value2, "cmiotCostProjectId");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseNumIsNull() {
            addCriterion("min_purchase_num is null");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseNumIsNotNull() {
            addCriterion("min_purchase_num is not null");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseNumEqualTo(Integer value) {
            addCriterion("min_purchase_num =", value, "minPurchaseNum");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseNumEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("min_purchase_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMinPurchaseNumNotEqualTo(Integer value) {
            addCriterion("min_purchase_num <>", value, "minPurchaseNum");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseNumNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("min_purchase_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMinPurchaseNumGreaterThan(Integer value) {
            addCriterion("min_purchase_num >", value, "minPurchaseNum");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseNumGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("min_purchase_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMinPurchaseNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("min_purchase_num >=", value, "minPurchaseNum");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseNumGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("min_purchase_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMinPurchaseNumLessThan(Integer value) {
            addCriterion("min_purchase_num <", value, "minPurchaseNum");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseNumLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("min_purchase_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMinPurchaseNumLessThanOrEqualTo(Integer value) {
            addCriterion("min_purchase_num <=", value, "minPurchaseNum");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseNumLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("min_purchase_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMinPurchaseNumIn(List<Integer> values) {
            addCriterion("min_purchase_num in", values, "minPurchaseNum");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseNumNotIn(List<Integer> values) {
            addCriterion("min_purchase_num not in", values, "minPurchaseNum");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseNumBetween(Integer value1, Integer value2) {
            addCriterion("min_purchase_num between", value1, value2, "minPurchaseNum");
            return (Criteria) this;
        }

        public Criteria andMinPurchaseNumNotBetween(Integer value1, Integer value2) {
            addCriterion("min_purchase_num not between", value1, value2, "minPurchaseNum");
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractIsNull() {
            addCriterion("province_purchase_contract is null");
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractIsNotNull() {
            addCriterion("province_purchase_contract is not null");
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractEqualTo(String value) {
            addCriterion("province_purchase_contract =", value, "provincePurchaseContract");
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("province_purchase_contract = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractNotEqualTo(String value) {
            addCriterion("province_purchase_contract <>", value, "provincePurchaseContract");
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("province_purchase_contract <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractGreaterThan(String value) {
            addCriterion("province_purchase_contract >", value, "provincePurchaseContract");
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("province_purchase_contract > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractGreaterThanOrEqualTo(String value) {
            addCriterion("province_purchase_contract >=", value, "provincePurchaseContract");
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("province_purchase_contract >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractLessThan(String value) {
            addCriterion("province_purchase_contract <", value, "provincePurchaseContract");
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("province_purchase_contract < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractLessThanOrEqualTo(String value) {
            addCriterion("province_purchase_contract <=", value, "provincePurchaseContract");
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("province_purchase_contract <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractLike(String value) {
            addCriterion("province_purchase_contract like", value, "provincePurchaseContract");
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractNotLike(String value) {
            addCriterion("province_purchase_contract not like", value, "provincePurchaseContract");
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractIn(List<String> values) {
            addCriterion("province_purchase_contract in", values, "provincePurchaseContract");
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractNotIn(List<String> values) {
            addCriterion("province_purchase_contract not in", values, "provincePurchaseContract");
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractBetween(String value1, String value2) {
            addCriterion("province_purchase_contract between", value1, value2, "provincePurchaseContract");
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractNotBetween(String value1, String value2) {
            addCriterion("province_purchase_contract not between", value1, value2, "provincePurchaseContract");
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractIsNull() {
            addCriterion("iot_purchase_contract is null");
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractIsNotNull() {
            addCriterion("iot_purchase_contract is not null");
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractEqualTo(String value) {
            addCriterion("iot_purchase_contract =", value, "iotPurchaseContract");
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("iot_purchase_contract = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractNotEqualTo(String value) {
            addCriterion("iot_purchase_contract <>", value, "iotPurchaseContract");
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("iot_purchase_contract <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractGreaterThan(String value) {
            addCriterion("iot_purchase_contract >", value, "iotPurchaseContract");
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("iot_purchase_contract > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractGreaterThanOrEqualTo(String value) {
            addCriterion("iot_purchase_contract >=", value, "iotPurchaseContract");
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("iot_purchase_contract >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractLessThan(String value) {
            addCriterion("iot_purchase_contract <", value, "iotPurchaseContract");
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("iot_purchase_contract < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractLessThanOrEqualTo(String value) {
            addCriterion("iot_purchase_contract <=", value, "iotPurchaseContract");
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("iot_purchase_contract <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractLike(String value) {
            addCriterion("iot_purchase_contract like", value, "iotPurchaseContract");
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractNotLike(String value) {
            addCriterion("iot_purchase_contract not like", value, "iotPurchaseContract");
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractIn(List<String> values) {
            addCriterion("iot_purchase_contract in", values, "iotPurchaseContract");
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractNotIn(List<String> values) {
            addCriterion("iot_purchase_contract not in", values, "iotPurchaseContract");
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractBetween(String value1, String value2) {
            addCriterion("iot_purchase_contract between", value1, value2, "iotPurchaseContract");
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractNotBetween(String value1, String value2) {
            addCriterion("iot_purchase_contract not between", value1, value2, "iotPurchaseContract");
            return (Criteria) this;
        }

        public Criteria andMaterialNumIsNull() {
            addCriterion("material_num is null");
            return (Criteria) this;
        }

        public Criteria andMaterialNumIsNotNull() {
            addCriterion("material_num is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialNumEqualTo(String value) {
            addCriterion("material_num =", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("material_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumNotEqualTo(String value) {
            addCriterion("material_num <>", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("material_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumGreaterThan(String value) {
            addCriterion("material_num >", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("material_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumGreaterThanOrEqualTo(String value) {
            addCriterion("material_num >=", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("material_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumLessThan(String value) {
            addCriterion("material_num <", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("material_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumLessThanOrEqualTo(String value) {
            addCriterion("material_num <=", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("material_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumLike(String value) {
            addCriterion("material_num like", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumNotLike(String value) {
            addCriterion("material_num not like", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumIn(List<String> values) {
            addCriterion("material_num in", values, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumNotIn(List<String> values) {
            addCriterion("material_num not in", values, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumBetween(String value1, String value2) {
            addCriterion("material_num between", value1, value2, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumNotBetween(String value1, String value2) {
            addCriterion("material_num not between", value1, value2, "materialNum");
            return (Criteria) this;
        }

        public Criteria andAtomRemarkIsNull() {
            addCriterion("atom_remark is null");
            return (Criteria) this;
        }

        public Criteria andAtomRemarkIsNotNull() {
            addCriterion("atom_remark is not null");
            return (Criteria) this;
        }

        public Criteria andAtomRemarkEqualTo(String value) {
            addCriterion("atom_remark =", value, "atomRemark");
            return (Criteria) this;
        }

        public Criteria andAtomRemarkEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("atom_remark = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomRemarkNotEqualTo(String value) {
            addCriterion("atom_remark <>", value, "atomRemark");
            return (Criteria) this;
        }

        public Criteria andAtomRemarkNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("atom_remark <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomRemarkGreaterThan(String value) {
            addCriterion("atom_remark >", value, "atomRemark");
            return (Criteria) this;
        }

        public Criteria andAtomRemarkGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("atom_remark > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("atom_remark >=", value, "atomRemark");
            return (Criteria) this;
        }

        public Criteria andAtomRemarkGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("atom_remark >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomRemarkLessThan(String value) {
            addCriterion("atom_remark <", value, "atomRemark");
            return (Criteria) this;
        }

        public Criteria andAtomRemarkLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("atom_remark < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomRemarkLessThanOrEqualTo(String value) {
            addCriterion("atom_remark <=", value, "atomRemark");
            return (Criteria) this;
        }

        public Criteria andAtomRemarkLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("atom_remark <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomRemarkLike(String value) {
            addCriterion("atom_remark like", value, "atomRemark");
            return (Criteria) this;
        }

        public Criteria andAtomRemarkNotLike(String value) {
            addCriterion("atom_remark not like", value, "atomRemark");
            return (Criteria) this;
        }

        public Criteria andAtomRemarkIn(List<String> values) {
            addCriterion("atom_remark in", values, "atomRemark");
            return (Criteria) this;
        }

        public Criteria andAtomRemarkNotIn(List<String> values) {
            addCriterion("atom_remark not in", values, "atomRemark");
            return (Criteria) this;
        }

        public Criteria andAtomRemarkBetween(String value1, String value2) {
            addCriterion("atom_remark between", value1, value2, "atomRemark");
            return (Criteria) this;
        }

        public Criteria andAtomRemarkNotBetween(String value1, String value2) {
            addCriterion("atom_remark not between", value1, value2, "atomRemark");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityIsNull() {
            addCriterion("atom_quantity is null");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityIsNotNull() {
            addCriterion("atom_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityEqualTo(Integer value) {
            addCriterion("atom_quantity =", value, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("atom_quantity = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomQuantityNotEqualTo(Integer value) {
            addCriterion("atom_quantity <>", value, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("atom_quantity <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomQuantityGreaterThan(Integer value) {
            addCriterion("atom_quantity >", value, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("atom_quantity > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("atom_quantity >=", value, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("atom_quantity >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomQuantityLessThan(Integer value) {
            addCriterion("atom_quantity <", value, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("atom_quantity < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("atom_quantity <=", value, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("atom_quantity <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomQuantityIn(List<Integer> values) {
            addCriterion("atom_quantity in", values, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityNotIn(List<Integer> values) {
            addCriterion("atom_quantity not in", values, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityBetween(Integer value1, Integer value2) {
            addCriterion("atom_quantity between", value1, value2, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("atom_quantity not between", value1, value2, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andColorIsNull() {
            addCriterion("color is null");
            return (Criteria) this;
        }

        public Criteria andColorIsNotNull() {
            addCriterion("color is not null");
            return (Criteria) this;
        }

        public Criteria andColorEqualTo(String value) {
            addCriterion("color =", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("color = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorNotEqualTo(String value) {
            addCriterion("color <>", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("color <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorGreaterThan(String value) {
            addCriterion("color >", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("color > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorGreaterThanOrEqualTo(String value) {
            addCriterion("color >=", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("color >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorLessThan(String value) {
            addCriterion("color <", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("color < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorLessThanOrEqualTo(String value) {
            addCriterion("color <=", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("color <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorLike(String value) {
            addCriterion("color like", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotLike(String value) {
            addCriterion("color not like", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorIn(List<String> values) {
            addCriterion("color in", values, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotIn(List<String> values) {
            addCriterion("color not in", values, "color");
            return (Criteria) this;
        }

        public Criteria andColorBetween(String value1, String value2) {
            addCriterion("color between", value1, value2, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotBetween(String value1, String value2) {
            addCriterion("color not between", value1, value2, "color");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("model = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("model <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("model > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("model >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("model < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("model <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andHardwarePriceIsNull() {
            addCriterion("hardware_price is null");
            return (Criteria) this;
        }

        public Criteria andHardwarePriceIsNotNull() {
            addCriterion("hardware_price is not null");
            return (Criteria) this;
        }

        public Criteria andHardwarePriceEqualTo(Long value) {
            addCriterion("hardware_price =", value, "hardwarePrice");
            return (Criteria) this;
        }

        public Criteria andHardwarePriceEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("hardware_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwarePriceNotEqualTo(Long value) {
            addCriterion("hardware_price <>", value, "hardwarePrice");
            return (Criteria) this;
        }

        public Criteria andHardwarePriceNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("hardware_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwarePriceGreaterThan(Long value) {
            addCriterion("hardware_price >", value, "hardwarePrice");
            return (Criteria) this;
        }

        public Criteria andHardwarePriceGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("hardware_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwarePriceGreaterThanOrEqualTo(Long value) {
            addCriterion("hardware_price >=", value, "hardwarePrice");
            return (Criteria) this;
        }

        public Criteria andHardwarePriceGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("hardware_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwarePriceLessThan(Long value) {
            addCriterion("hardware_price <", value, "hardwarePrice");
            return (Criteria) this;
        }

        public Criteria andHardwarePriceLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("hardware_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwarePriceLessThanOrEqualTo(Long value) {
            addCriterion("hardware_price <=", value, "hardwarePrice");
            return (Criteria) this;
        }

        public Criteria andHardwarePriceLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("hardware_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andHardwarePriceIn(List<Long> values) {
            addCriterion("hardware_price in", values, "hardwarePrice");
            return (Criteria) this;
        }

        public Criteria andHardwarePriceNotIn(List<Long> values) {
            addCriterion("hardware_price not in", values, "hardwarePrice");
            return (Criteria) this;
        }

        public Criteria andHardwarePriceBetween(Long value1, Long value2) {
            addCriterion("hardware_price between", value1, value2, "hardwarePrice");
            return (Criteria) this;
        }

        public Criteria andHardwarePriceNotBetween(Long value1, Long value2) {
            addCriterion("hardware_price not between", value1, value2, "hardwarePrice");
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameIsNull() {
            addCriterion("soft_atom_name is null");
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameIsNotNull() {
            addCriterion("soft_atom_name is not null");
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameEqualTo(String value) {
            addCriterion("soft_atom_name =", value, "softAtomName");
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_atom_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameNotEqualTo(String value) {
            addCriterion("soft_atom_name <>", value, "softAtomName");
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_atom_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameGreaterThan(String value) {
            addCriterion("soft_atom_name >", value, "softAtomName");
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_atom_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameGreaterThanOrEqualTo(String value) {
            addCriterion("soft_atom_name >=", value, "softAtomName");
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_atom_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameLessThan(String value) {
            addCriterion("soft_atom_name <", value, "softAtomName");
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_atom_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameLessThanOrEqualTo(String value) {
            addCriterion("soft_atom_name <=", value, "softAtomName");
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_atom_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameLike(String value) {
            addCriterion("soft_atom_name like", value, "softAtomName");
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameNotLike(String value) {
            addCriterion("soft_atom_name not like", value, "softAtomName");
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameIn(List<String> values) {
            addCriterion("soft_atom_name in", values, "softAtomName");
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameNotIn(List<String> values) {
            addCriterion("soft_atom_name not in", values, "softAtomName");
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameBetween(String value1, String value2) {
            addCriterion("soft_atom_name between", value1, value2, "softAtomName");
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameNotBetween(String value1, String value2) {
            addCriterion("soft_atom_name not between", value1, value2, "softAtomName");
            return (Criteria) this;
        }

        public Criteria andSoftSettlePriceIsNull() {
            addCriterion("soft_settle_price is null");
            return (Criteria) this;
        }

        public Criteria andSoftSettlePriceIsNotNull() {
            addCriterion("soft_settle_price is not null");
            return (Criteria) this;
        }

        public Criteria andSoftSettlePriceEqualTo(Long value) {
            addCriterion("soft_settle_price =", value, "softSettlePrice");
            return (Criteria) this;
        }

        public Criteria andSoftSettlePriceEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_settle_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftSettlePriceNotEqualTo(Long value) {
            addCriterion("soft_settle_price <>", value, "softSettlePrice");
            return (Criteria) this;
        }

        public Criteria andSoftSettlePriceNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_settle_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftSettlePriceGreaterThan(Long value) {
            addCriterion("soft_settle_price >", value, "softSettlePrice");
            return (Criteria) this;
        }

        public Criteria andSoftSettlePriceGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_settle_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftSettlePriceGreaterThanOrEqualTo(Long value) {
            addCriterion("soft_settle_price >=", value, "softSettlePrice");
            return (Criteria) this;
        }

        public Criteria andSoftSettlePriceGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_settle_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftSettlePriceLessThan(Long value) {
            addCriterion("soft_settle_price <", value, "softSettlePrice");
            return (Criteria) this;
        }

        public Criteria andSoftSettlePriceLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_settle_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftSettlePriceLessThanOrEqualTo(Long value) {
            addCriterion("soft_settle_price <=", value, "softSettlePrice");
            return (Criteria) this;
        }

        public Criteria andSoftSettlePriceLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_settle_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftSettlePriceIn(List<Long> values) {
            addCriterion("soft_settle_price in", values, "softSettlePrice");
            return (Criteria) this;
        }

        public Criteria andSoftSettlePriceNotIn(List<Long> values) {
            addCriterion("soft_settle_price not in", values, "softSettlePrice");
            return (Criteria) this;
        }

        public Criteria andSoftSettlePriceBetween(Long value1, Long value2) {
            addCriterion("soft_settle_price between", value1, value2, "softSettlePrice");
            return (Criteria) this;
        }

        public Criteria andSoftSettlePriceNotBetween(Long value1, Long value2) {
            addCriterion("soft_settle_price not between", value1, value2, "softSettlePrice");
            return (Criteria) this;
        }

        public Criteria andSoftUnitIsNull() {
            addCriterion("soft_unit is null");
            return (Criteria) this;
        }

        public Criteria andSoftUnitIsNotNull() {
            addCriterion("soft_unit is not null");
            return (Criteria) this;
        }

        public Criteria andSoftUnitEqualTo(String value) {
            addCriterion("soft_unit =", value, "softUnit");
            return (Criteria) this;
        }

        public Criteria andSoftUnitEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_unit = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftUnitNotEqualTo(String value) {
            addCriterion("soft_unit <>", value, "softUnit");
            return (Criteria) this;
        }

        public Criteria andSoftUnitNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_unit <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftUnitGreaterThan(String value) {
            addCriterion("soft_unit >", value, "softUnit");
            return (Criteria) this;
        }

        public Criteria andSoftUnitGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_unit > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftUnitGreaterThanOrEqualTo(String value) {
            addCriterion("soft_unit >=", value, "softUnit");
            return (Criteria) this;
        }

        public Criteria andSoftUnitGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_unit >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftUnitLessThan(String value) {
            addCriterion("soft_unit <", value, "softUnit");
            return (Criteria) this;
        }

        public Criteria andSoftUnitLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_unit < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftUnitLessThanOrEqualTo(String value) {
            addCriterion("soft_unit <=", value, "softUnit");
            return (Criteria) this;
        }

        public Criteria andSoftUnitLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_unit <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftUnitLike(String value) {
            addCriterion("soft_unit like", value, "softUnit");
            return (Criteria) this;
        }

        public Criteria andSoftUnitNotLike(String value) {
            addCriterion("soft_unit not like", value, "softUnit");
            return (Criteria) this;
        }

        public Criteria andSoftUnitIn(List<String> values) {
            addCriterion("soft_unit in", values, "softUnit");
            return (Criteria) this;
        }

        public Criteria andSoftUnitNotIn(List<String> values) {
            addCriterion("soft_unit not in", values, "softUnit");
            return (Criteria) this;
        }

        public Criteria andSoftUnitBetween(String value1, String value2) {
            addCriterion("soft_unit between", value1, value2, "softUnit");
            return (Criteria) this;
        }

        public Criteria andSoftUnitNotBetween(String value1, String value2) {
            addCriterion("soft_unit not between", value1, value2, "softUnit");
            return (Criteria) this;
        }

        public Criteria andSoftQuantityIsNull() {
            addCriterion("soft_quantity is null");
            return (Criteria) this;
        }

        public Criteria andSoftQuantityIsNotNull() {
            addCriterion("soft_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andSoftQuantityEqualTo(Integer value) {
            addCriterion("soft_quantity =", value, "softQuantity");
            return (Criteria) this;
        }

        public Criteria andSoftQuantityEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_quantity = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftQuantityNotEqualTo(Integer value) {
            addCriterion("soft_quantity <>", value, "softQuantity");
            return (Criteria) this;
        }

        public Criteria andSoftQuantityNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_quantity <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftQuantityGreaterThan(Integer value) {
            addCriterion("soft_quantity >", value, "softQuantity");
            return (Criteria) this;
        }

        public Criteria andSoftQuantityGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_quantity > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("soft_quantity >=", value, "softQuantity");
            return (Criteria) this;
        }

        public Criteria andSoftQuantityGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_quantity >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftQuantityLessThan(Integer value) {
            addCriterion("soft_quantity <", value, "softQuantity");
            return (Criteria) this;
        }

        public Criteria andSoftQuantityLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_quantity < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("soft_quantity <=", value, "softQuantity");
            return (Criteria) this;
        }

        public Criteria andSoftQuantityLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_quantity <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftQuantityIn(List<Integer> values) {
            addCriterion("soft_quantity in", values, "softQuantity");
            return (Criteria) this;
        }

        public Criteria andSoftQuantityNotIn(List<Integer> values) {
            addCriterion("soft_quantity not in", values, "softQuantity");
            return (Criteria) this;
        }

        public Criteria andSoftQuantityBetween(Integer value1, Integer value2) {
            addCriterion("soft_quantity between", value1, value2, "softQuantity");
            return (Criteria) this;
        }

        public Criteria andSoftQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("soft_quantity not between", value1, value2, "softQuantity");
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeIsNull() {
            addCriterion("soft_product_code is null");
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeIsNotNull() {
            addCriterion("soft_product_code is not null");
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeEqualTo(String value) {
            addCriterion("soft_product_code =", value, "softProductCode");
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_product_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeNotEqualTo(String value) {
            addCriterion("soft_product_code <>", value, "softProductCode");
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_product_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeGreaterThan(String value) {
            addCriterion("soft_product_code >", value, "softProductCode");
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_product_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeGreaterThanOrEqualTo(String value) {
            addCriterion("soft_product_code >=", value, "softProductCode");
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_product_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeLessThan(String value) {
            addCriterion("soft_product_code <", value, "softProductCode");
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_product_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeLessThanOrEqualTo(String value) {
            addCriterion("soft_product_code <=", value, "softProductCode");
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_product_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeLike(String value) {
            addCriterion("soft_product_code like", value, "softProductCode");
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeNotLike(String value) {
            addCriterion("soft_product_code not like", value, "softProductCode");
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeIn(List<String> values) {
            addCriterion("soft_product_code in", values, "softProductCode");
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeNotIn(List<String> values) {
            addCriterion("soft_product_code not in", values, "softProductCode");
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeBetween(String value1, String value2) {
            addCriterion("soft_product_code between", value1, value2, "softProductCode");
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeNotBetween(String value1, String value2) {
            addCriterion("soft_product_code not between", value1, value2, "softProductCode");
            return (Criteria) this;
        }

        public Criteria andNoSettlementIsNull() {
            addCriterion("no_settlement is null");
            return (Criteria) this;
        }

        public Criteria andNoSettlementIsNotNull() {
            addCriterion("no_settlement is not null");
            return (Criteria) this;
        }

        public Criteria andNoSettlementEqualTo(String value) {
            addCriterion("no_settlement =", value, "noSettlement");
            return (Criteria) this;
        }

        public Criteria andNoSettlementEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("no_settlement = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNoSettlementNotEqualTo(String value) {
            addCriterion("no_settlement <>", value, "noSettlement");
            return (Criteria) this;
        }

        public Criteria andNoSettlementNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("no_settlement <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNoSettlementGreaterThan(String value) {
            addCriterion("no_settlement >", value, "noSettlement");
            return (Criteria) this;
        }

        public Criteria andNoSettlementGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("no_settlement > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNoSettlementGreaterThanOrEqualTo(String value) {
            addCriterion("no_settlement >=", value, "noSettlement");
            return (Criteria) this;
        }

        public Criteria andNoSettlementGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("no_settlement >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNoSettlementLessThan(String value) {
            addCriterion("no_settlement <", value, "noSettlement");
            return (Criteria) this;
        }

        public Criteria andNoSettlementLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("no_settlement < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNoSettlementLessThanOrEqualTo(String value) {
            addCriterion("no_settlement <=", value, "noSettlement");
            return (Criteria) this;
        }

        public Criteria andNoSettlementLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("no_settlement <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNoSettlementLike(String value) {
            addCriterion("no_settlement like", value, "noSettlement");
            return (Criteria) this;
        }

        public Criteria andNoSettlementNotLike(String value) {
            addCriterion("no_settlement not like", value, "noSettlement");
            return (Criteria) this;
        }

        public Criteria andNoSettlementIn(List<String> values) {
            addCriterion("no_settlement in", values, "noSettlement");
            return (Criteria) this;
        }

        public Criteria andNoSettlementNotIn(List<String> values) {
            addCriterion("no_settlement not in", values, "noSettlement");
            return (Criteria) this;
        }

        public Criteria andNoSettlementBetween(String value1, String value2) {
            addCriterion("no_settlement between", value1, value2, "noSettlement");
            return (Criteria) this;
        }

        public Criteria andNoSettlementNotBetween(String value1, String value2) {
            addCriterion("no_settlement not between", value1, value2, "noSettlement");
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameIsNull() {
            addCriterion("settlement_detail_name is null");
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameIsNotNull() {
            addCriterion("settlement_detail_name is not null");
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameEqualTo(String value) {
            addCriterion("settlement_detail_name =", value, "settlementDetailName");
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("settlement_detail_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameNotEqualTo(String value) {
            addCriterion("settlement_detail_name <>", value, "settlementDetailName");
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("settlement_detail_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameGreaterThan(String value) {
            addCriterion("settlement_detail_name >", value, "settlementDetailName");
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("settlement_detail_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameGreaterThanOrEqualTo(String value) {
            addCriterion("settlement_detail_name >=", value, "settlementDetailName");
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("settlement_detail_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameLessThan(String value) {
            addCriterion("settlement_detail_name <", value, "settlementDetailName");
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("settlement_detail_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameLessThanOrEqualTo(String value) {
            addCriterion("settlement_detail_name <=", value, "settlementDetailName");
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("settlement_detail_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameLike(String value) {
            addCriterion("settlement_detail_name like", value, "settlementDetailName");
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameNotLike(String value) {
            addCriterion("settlement_detail_name not like", value, "settlementDetailName");
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameIn(List<String> values) {
            addCriterion("settlement_detail_name in", values, "settlementDetailName");
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameNotIn(List<String> values) {
            addCriterion("settlement_detail_name not in", values, "settlementDetailName");
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameBetween(String value1, String value2) {
            addCriterion("settlement_detail_name between", value1, value2, "settlementDetailName");
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameNotBetween(String value1, String value2) {
            addCriterion("settlement_detail_name not between", value1, value2, "settlementDetailName");
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodIsNull() {
            addCriterion("deliver_period is null");
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodIsNotNull() {
            addCriterion("deliver_period is not null");
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodEqualTo(String value) {
            addCriterion("deliver_period =", value, "deliverPeriod");
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("deliver_period = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodNotEqualTo(String value) {
            addCriterion("deliver_period <>", value, "deliverPeriod");
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("deliver_period <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodGreaterThan(String value) {
            addCriterion("deliver_period >", value, "deliverPeriod");
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("deliver_period > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodGreaterThanOrEqualTo(String value) {
            addCriterion("deliver_period >=", value, "deliverPeriod");
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("deliver_period >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodLessThan(String value) {
            addCriterion("deliver_period <", value, "deliverPeriod");
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("deliver_period < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodLessThanOrEqualTo(String value) {
            addCriterion("deliver_period <=", value, "deliverPeriod");
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("deliver_period <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodLike(String value) {
            addCriterion("deliver_period like", value, "deliverPeriod");
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodNotLike(String value) {
            addCriterion("deliver_period not like", value, "deliverPeriod");
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodIn(List<String> values) {
            addCriterion("deliver_period in", values, "deliverPeriod");
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodNotIn(List<String> values) {
            addCriterion("deliver_period not in", values, "deliverPeriod");
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodBetween(String value1, String value2) {
            addCriterion("deliver_period between", value1, value2, "deliverPeriod");
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodNotBetween(String value1, String value2) {
            addCriterion("deliver_period not between", value1, value2, "deliverPeriod");
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentIsNull() {
            addCriterion("soft_service_content is null");
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentIsNotNull() {
            addCriterion("soft_service_content is not null");
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentEqualTo(String value) {
            addCriterion("soft_service_content =", value, "softServiceContent");
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_service_content = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentNotEqualTo(String value) {
            addCriterion("soft_service_content <>", value, "softServiceContent");
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_service_content <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentGreaterThan(String value) {
            addCriterion("soft_service_content >", value, "softServiceContent");
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_service_content > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentGreaterThanOrEqualTo(String value) {
            addCriterion("soft_service_content >=", value, "softServiceContent");
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_service_content >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentLessThan(String value) {
            addCriterion("soft_service_content <", value, "softServiceContent");
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_service_content < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentLessThanOrEqualTo(String value) {
            addCriterion("soft_service_content <=", value, "softServiceContent");
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_service_content <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentLike(String value) {
            addCriterion("soft_service_content like", value, "softServiceContent");
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentNotLike(String value) {
            addCriterion("soft_service_content not like", value, "softServiceContent");
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentIn(List<String> values) {
            addCriterion("soft_service_content in", values, "softServiceContent");
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentNotIn(List<String> values) {
            addCriterion("soft_service_content not in", values, "softServiceContent");
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentBetween(String value1, String value2) {
            addCriterion("soft_service_content between", value1, value2, "softServiceContent");
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentNotBetween(String value1, String value2) {
            addCriterion("soft_service_content not between", value1, value2, "softServiceContent");
            return (Criteria) this;
        }

        public Criteria andSoftPriceIsNull() {
            addCriterion("soft_price is null");
            return (Criteria) this;
        }

        public Criteria andSoftPriceIsNotNull() {
            addCriterion("soft_price is not null");
            return (Criteria) this;
        }

        public Criteria andSoftPriceEqualTo(Long value) {
            addCriterion("soft_price =", value, "softPrice");
            return (Criteria) this;
        }

        public Criteria andSoftPriceEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftPriceNotEqualTo(Long value) {
            addCriterion("soft_price <>", value, "softPrice");
            return (Criteria) this;
        }

        public Criteria andSoftPriceNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftPriceGreaterThan(Long value) {
            addCriterion("soft_price >", value, "softPrice");
            return (Criteria) this;
        }

        public Criteria andSoftPriceGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftPriceGreaterThanOrEqualTo(Long value) {
            addCriterion("soft_price >=", value, "softPrice");
            return (Criteria) this;
        }

        public Criteria andSoftPriceGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftPriceLessThan(Long value) {
            addCriterion("soft_price <", value, "softPrice");
            return (Criteria) this;
        }

        public Criteria andSoftPriceLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftPriceLessThanOrEqualTo(Long value) {
            addCriterion("soft_price <=", value, "softPrice");
            return (Criteria) this;
        }

        public Criteria andSoftPriceLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftPriceIn(List<Long> values) {
            addCriterion("soft_price in", values, "softPrice");
            return (Criteria) this;
        }

        public Criteria andSoftPriceNotIn(List<Long> values) {
            addCriterion("soft_price not in", values, "softPrice");
            return (Criteria) this;
        }

        public Criteria andSoftPriceBetween(Long value1, Long value2) {
            addCriterion("soft_price between", value1, value2, "softPrice");
            return (Criteria) this;
        }

        public Criteria andSoftPriceNotBetween(Long value1, Long value2) {
            addCriterion("soft_price not between", value1, value2, "softPrice");
            return (Criteria) this;
        }

        public Criteria andSoftTotalPriceIsNull() {
            addCriterion("soft_total_price is null");
            return (Criteria) this;
        }

        public Criteria andSoftTotalPriceIsNotNull() {
            addCriterion("soft_total_price is not null");
            return (Criteria) this;
        }

        public Criteria andSoftTotalPriceEqualTo(Long value) {
            addCriterion("soft_total_price =", value, "softTotalPrice");
            return (Criteria) this;
        }

        public Criteria andSoftTotalPriceEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_total_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftTotalPriceNotEqualTo(Long value) {
            addCriterion("soft_total_price <>", value, "softTotalPrice");
            return (Criteria) this;
        }

        public Criteria andSoftTotalPriceNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_total_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftTotalPriceGreaterThan(Long value) {
            addCriterion("soft_total_price >", value, "softTotalPrice");
            return (Criteria) this;
        }

        public Criteria andSoftTotalPriceGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_total_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftTotalPriceGreaterThanOrEqualTo(Long value) {
            addCriterion("soft_total_price >=", value, "softTotalPrice");
            return (Criteria) this;
        }

        public Criteria andSoftTotalPriceGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_total_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftTotalPriceLessThan(Long value) {
            addCriterion("soft_total_price <", value, "softTotalPrice");
            return (Criteria) this;
        }

        public Criteria andSoftTotalPriceLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_total_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftTotalPriceLessThanOrEqualTo(Long value) {
            addCriterion("soft_total_price <=", value, "softTotalPrice");
            return (Criteria) this;
        }

        public Criteria andSoftTotalPriceLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("soft_total_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftTotalPriceIn(List<Long> values) {
            addCriterion("soft_total_price in", values, "softTotalPrice");
            return (Criteria) this;
        }

        public Criteria andSoftTotalPriceNotIn(List<Long> values) {
            addCriterion("soft_total_price not in", values, "softTotalPrice");
            return (Criteria) this;
        }

        public Criteria andSoftTotalPriceBetween(Long value1, Long value2) {
            addCriterion("soft_total_price between", value1, value2, "softTotalPrice");
            return (Criteria) this;
        }

        public Criteria andSoftTotalPriceNotBetween(Long value1, Long value2) {
            addCriterion("soft_total_price not between", value1, value2, "softTotalPrice");
            return (Criteria) this;
        }

        public Criteria andZhuanheSettlePriceIsNull() {
            addCriterion("zhuanhe_settle_price is null");
            return (Criteria) this;
        }

        public Criteria andZhuanheSettlePriceIsNotNull() {
            addCriterion("zhuanhe_settle_price is not null");
            return (Criteria) this;
        }

        public Criteria andZhuanheSettlePriceEqualTo(Long value) {
            addCriterion("zhuanhe_settle_price =", value, "zhuanheSettlePrice");
            return (Criteria) this;
        }

        public Criteria andZhuanheSettlePriceEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("zhuanhe_settle_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andZhuanheSettlePriceNotEqualTo(Long value) {
            addCriterion("zhuanhe_settle_price <>", value, "zhuanheSettlePrice");
            return (Criteria) this;
        }

        public Criteria andZhuanheSettlePriceNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("zhuanhe_settle_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andZhuanheSettlePriceGreaterThan(Long value) {
            addCriterion("zhuanhe_settle_price >", value, "zhuanheSettlePrice");
            return (Criteria) this;
        }

        public Criteria andZhuanheSettlePriceGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("zhuanhe_settle_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andZhuanheSettlePriceGreaterThanOrEqualTo(Long value) {
            addCriterion("zhuanhe_settle_price >=", value, "zhuanheSettlePrice");
            return (Criteria) this;
        }

        public Criteria andZhuanheSettlePriceGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("zhuanhe_settle_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andZhuanheSettlePriceLessThan(Long value) {
            addCriterion("zhuanhe_settle_price <", value, "zhuanheSettlePrice");
            return (Criteria) this;
        }

        public Criteria andZhuanheSettlePriceLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("zhuanhe_settle_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andZhuanheSettlePriceLessThanOrEqualTo(Long value) {
            addCriterion("zhuanhe_settle_price <=", value, "zhuanheSettlePrice");
            return (Criteria) this;
        }

        public Criteria andZhuanheSettlePriceLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("zhuanhe_settle_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andZhuanheSettlePriceIn(List<Long> values) {
            addCriterion("zhuanhe_settle_price in", values, "zhuanheSettlePrice");
            return (Criteria) this;
        }

        public Criteria andZhuanheSettlePriceNotIn(List<Long> values) {
            addCriterion("zhuanhe_settle_price not in", values, "zhuanheSettlePrice");
            return (Criteria) this;
        }

        public Criteria andZhuanheSettlePriceBetween(Long value1, Long value2) {
            addCriterion("zhuanhe_settle_price between", value1, value2, "zhuanheSettlePrice");
            return (Criteria) this;
        }

        public Criteria andZhuanheSettlePriceNotBetween(Long value1, Long value2) {
            addCriterion("zhuanhe_settle_price not between", value1, value2, "zhuanheSettlePrice");
            return (Criteria) this;
        }

        public Criteria andServicePackageNameIsNull() {
            addCriterion("service_package_name is null");
            return (Criteria) this;
        }

        public Criteria andServicePackageNameIsNotNull() {
            addCriterion("service_package_name is not null");
            return (Criteria) this;
        }

        public Criteria andServicePackageNameEqualTo(String value) {
            addCriterion("service_package_name =", value, "servicePackageName");
            return (Criteria) this;
        }

        public Criteria andServicePackageNameEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("service_package_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackageNameNotEqualTo(String value) {
            addCriterion("service_package_name <>", value, "servicePackageName");
            return (Criteria) this;
        }

        public Criteria andServicePackageNameNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("service_package_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackageNameGreaterThan(String value) {
            addCriterion("service_package_name >", value, "servicePackageName");
            return (Criteria) this;
        }

        public Criteria andServicePackageNameGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("service_package_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackageNameGreaterThanOrEqualTo(String value) {
            addCriterion("service_package_name >=", value, "servicePackageName");
            return (Criteria) this;
        }

        public Criteria andServicePackageNameGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("service_package_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackageNameLessThan(String value) {
            addCriterion("service_package_name <", value, "servicePackageName");
            return (Criteria) this;
        }

        public Criteria andServicePackageNameLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("service_package_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackageNameLessThanOrEqualTo(String value) {
            addCriterion("service_package_name <=", value, "servicePackageName");
            return (Criteria) this;
        }

        public Criteria andServicePackageNameLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("service_package_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServicePackageNameLike(String value) {
            addCriterion("service_package_name like", value, "servicePackageName");
            return (Criteria) this;
        }

        public Criteria andServicePackageNameNotLike(String value) {
            addCriterion("service_package_name not like", value, "servicePackageName");
            return (Criteria) this;
        }

        public Criteria andServicePackageNameIn(List<String> values) {
            addCriterion("service_package_name in", values, "servicePackageName");
            return (Criteria) this;
        }

        public Criteria andServicePackageNameNotIn(List<String> values) {
            addCriterion("service_package_name not in", values, "servicePackageName");
            return (Criteria) this;
        }

        public Criteria andServicePackageNameBetween(String value1, String value2) {
            addCriterion("service_package_name between", value1, value2, "servicePackageName");
            return (Criteria) this;
        }

        public Criteria andServicePackageNameNotBetween(String value1, String value2) {
            addCriterion("service_package_name not between", value1, value2, "servicePackageName");
            return (Criteria) this;
        }

        public Criteria andServiceContractIsNull() {
            addCriterion("service_contract is null");
            return (Criteria) this;
        }

        public Criteria andServiceContractIsNotNull() {
            addCriterion("service_contract is not null");
            return (Criteria) this;
        }

        public Criteria andServiceContractEqualTo(String value) {
            addCriterion("service_contract =", value, "serviceContract");
            return (Criteria) this;
        }

        public Criteria andServiceContractEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("service_contract = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceContractNotEqualTo(String value) {
            addCriterion("service_contract <>", value, "serviceContract");
            return (Criteria) this;
        }

        public Criteria andServiceContractNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("service_contract <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceContractGreaterThan(String value) {
            addCriterion("service_contract >", value, "serviceContract");
            return (Criteria) this;
        }

        public Criteria andServiceContractGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("service_contract > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceContractGreaterThanOrEqualTo(String value) {
            addCriterion("service_contract >=", value, "serviceContract");
            return (Criteria) this;
        }

        public Criteria andServiceContractGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("service_contract >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceContractLessThan(String value) {
            addCriterion("service_contract <", value, "serviceContract");
            return (Criteria) this;
        }

        public Criteria andServiceContractLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("service_contract < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceContractLessThanOrEqualTo(String value) {
            addCriterion("service_contract <=", value, "serviceContract");
            return (Criteria) this;
        }

        public Criteria andServiceContractLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("service_contract <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andServiceContractLike(String value) {
            addCriterion("service_contract like", value, "serviceContract");
            return (Criteria) this;
        }

        public Criteria andServiceContractNotLike(String value) {
            addCriterion("service_contract not like", value, "serviceContract");
            return (Criteria) this;
        }

        public Criteria andServiceContractIn(List<String> values) {
            addCriterion("service_contract in", values, "serviceContract");
            return (Criteria) this;
        }

        public Criteria andServiceContractNotIn(List<String> values) {
            addCriterion("service_contract not in", values, "serviceContract");
            return (Criteria) this;
        }

        public Criteria andServiceContractBetween(String value1, String value2) {
            addCriterion("service_contract between", value1, value2, "serviceContract");
            return (Criteria) this;
        }

        public Criteria andServiceContractNotBetween(String value1, String value2) {
            addCriterion("service_contract not between", value1, value2, "serviceContract");
            return (Criteria) this;
        }

        public Criteria andSalePriceIsNull() {
            addCriterion("sale_price is null");
            return (Criteria) this;
        }

        public Criteria andSalePriceIsNotNull() {
            addCriterion("sale_price is not null");
            return (Criteria) this;
        }

        public Criteria andSalePriceEqualTo(Long value) {
            addCriterion("sale_price =", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalePriceNotEqualTo(Long value) {
            addCriterion("sale_price <>", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalePriceGreaterThan(Long value) {
            addCriterion("sale_price >", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalePriceGreaterThanOrEqualTo(Long value) {
            addCriterion("sale_price >=", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalePriceLessThan(Long value) {
            addCriterion("sale_price <", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalePriceLessThanOrEqualTo(Long value) {
            addCriterion("sale_price <=", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalePriceIn(List<Long> values) {
            addCriterion("sale_price in", values, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceNotIn(List<Long> values) {
            addCriterion("sale_price not in", values, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceBetween(Long value1, Long value2) {
            addCriterion("sale_price between", value1, value2, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceNotBetween(Long value1, Long value2) {
            addCriterion("sale_price not between", value1, value2, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSaleMinPriceIsNull() {
            addCriterion("sale_min_price is null");
            return (Criteria) this;
        }

        public Criteria andSaleMinPriceIsNotNull() {
            addCriterion("sale_min_price is not null");
            return (Criteria) this;
        }

        public Criteria andSaleMinPriceEqualTo(Long value) {
            addCriterion("sale_min_price =", value, "saleMinPrice");
            return (Criteria) this;
        }

        public Criteria andSaleMinPriceEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_min_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleMinPriceNotEqualTo(Long value) {
            addCriterion("sale_min_price <>", value, "saleMinPrice");
            return (Criteria) this;
        }

        public Criteria andSaleMinPriceNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_min_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleMinPriceGreaterThan(Long value) {
            addCriterion("sale_min_price >", value, "saleMinPrice");
            return (Criteria) this;
        }

        public Criteria andSaleMinPriceGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_min_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleMinPriceGreaterThanOrEqualTo(Long value) {
            addCriterion("sale_min_price >=", value, "saleMinPrice");
            return (Criteria) this;
        }

        public Criteria andSaleMinPriceGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_min_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleMinPriceLessThan(Long value) {
            addCriterion("sale_min_price <", value, "saleMinPrice");
            return (Criteria) this;
        }

        public Criteria andSaleMinPriceLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_min_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleMinPriceLessThanOrEqualTo(Long value) {
            addCriterion("sale_min_price <=", value, "saleMinPrice");
            return (Criteria) this;
        }

        public Criteria andSaleMinPriceLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_min_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleMinPriceIn(List<Long> values) {
            addCriterion("sale_min_price in", values, "saleMinPrice");
            return (Criteria) this;
        }

        public Criteria andSaleMinPriceNotIn(List<Long> values) {
            addCriterion("sale_min_price not in", values, "saleMinPrice");
            return (Criteria) this;
        }

        public Criteria andSaleMinPriceBetween(Long value1, Long value2) {
            addCriterion("sale_min_price between", value1, value2, "saleMinPrice");
            return (Criteria) this;
        }

        public Criteria andSaleMinPriceNotBetween(Long value1, Long value2) {
            addCriterion("sale_min_price not between", value1, value2, "saleMinPrice");
            return (Criteria) this;
        }

        public Criteria andSaleMaxPriceIsNull() {
            addCriterion("sale_max_price is null");
            return (Criteria) this;
        }

        public Criteria andSaleMaxPriceIsNotNull() {
            addCriterion("sale_max_price is not null");
            return (Criteria) this;
        }

        public Criteria andSaleMaxPriceEqualTo(Long value) {
            addCriterion("sale_max_price =", value, "saleMaxPrice");
            return (Criteria) this;
        }

        public Criteria andSaleMaxPriceEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_max_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleMaxPriceNotEqualTo(Long value) {
            addCriterion("sale_max_price <>", value, "saleMaxPrice");
            return (Criteria) this;
        }

        public Criteria andSaleMaxPriceNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_max_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleMaxPriceGreaterThan(Long value) {
            addCriterion("sale_max_price >", value, "saleMaxPrice");
            return (Criteria) this;
        }

        public Criteria andSaleMaxPriceGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_max_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleMaxPriceGreaterThanOrEqualTo(Long value) {
            addCriterion("sale_max_price >=", value, "saleMaxPrice");
            return (Criteria) this;
        }

        public Criteria andSaleMaxPriceGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_max_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleMaxPriceLessThan(Long value) {
            addCriterion("sale_max_price <", value, "saleMaxPrice");
            return (Criteria) this;
        }

        public Criteria andSaleMaxPriceLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_max_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleMaxPriceLessThanOrEqualTo(Long value) {
            addCriterion("sale_max_price <=", value, "saleMaxPrice");
            return (Criteria) this;
        }

        public Criteria andSaleMaxPriceLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_max_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleMaxPriceIn(List<Long> values) {
            addCriterion("sale_max_price in", values, "saleMaxPrice");
            return (Criteria) this;
        }

        public Criteria andSaleMaxPriceNotIn(List<Long> values) {
            addCriterion("sale_max_price not in", values, "saleMaxPrice");
            return (Criteria) this;
        }

        public Criteria andSaleMaxPriceBetween(Long value1, Long value2) {
            addCriterion("sale_max_price between", value1, value2, "saleMaxPrice");
            return (Criteria) this;
        }

        public Criteria andSaleMaxPriceNotBetween(Long value1, Long value2) {
            addCriterion("sale_max_price not between", value1, value2, "saleMaxPrice");
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeIsNull() {
            addCriterion("sale_out_of_price_range is null");
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeIsNotNull() {
            addCriterion("sale_out_of_price_range is not null");
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeEqualTo(String value) {
            addCriterion("sale_out_of_price_range =", value, "saleOutOfPriceRange");
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_out_of_price_range = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeNotEqualTo(String value) {
            addCriterion("sale_out_of_price_range <>", value, "saleOutOfPriceRange");
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_out_of_price_range <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeGreaterThan(String value) {
            addCriterion("sale_out_of_price_range >", value, "saleOutOfPriceRange");
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_out_of_price_range > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeGreaterThanOrEqualTo(String value) {
            addCriterion("sale_out_of_price_range >=", value, "saleOutOfPriceRange");
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_out_of_price_range >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeLessThan(String value) {
            addCriterion("sale_out_of_price_range <", value, "saleOutOfPriceRange");
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_out_of_price_range < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeLessThanOrEqualTo(String value) {
            addCriterion("sale_out_of_price_range <=", value, "saleOutOfPriceRange");
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("sale_out_of_price_range <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeLike(String value) {
            addCriterion("sale_out_of_price_range like", value, "saleOutOfPriceRange");
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeNotLike(String value) {
            addCriterion("sale_out_of_price_range not like", value, "saleOutOfPriceRange");
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeIn(List<String> values) {
            addCriterion("sale_out_of_price_range in", values, "saleOutOfPriceRange");
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeNotIn(List<String> values) {
            addCriterion("sale_out_of_price_range not in", values, "saleOutOfPriceRange");
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeBetween(String value1, String value2) {
            addCriterion("sale_out_of_price_range between", value1, value2, "saleOutOfPriceRange");
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeNotBetween(String value1, String value2) {
            addCriterion("sale_out_of_price_range not between", value1, value2, "saleOutOfPriceRange");
            return (Criteria) this;
        }

        public Criteria andInventoryIsNull() {
            addCriterion("inventory is null");
            return (Criteria) this;
        }

        public Criteria andInventoryIsNotNull() {
            addCriterion("inventory is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryEqualTo(Integer value) {
            addCriterion("inventory =", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("inventory = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryNotEqualTo(Integer value) {
            addCriterion("inventory <>", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("inventory <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryGreaterThan(Integer value) {
            addCriterion("inventory >", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("inventory > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryGreaterThanOrEqualTo(Integer value) {
            addCriterion("inventory >=", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("inventory >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryLessThan(Integer value) {
            addCriterion("inventory <", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("inventory < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryLessThanOrEqualTo(Integer value) {
            addCriterion("inventory <=", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("inventory <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryIn(List<Integer> values) {
            addCriterion("inventory in", values, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryNotIn(List<Integer> values) {
            addCriterion("inventory not in", values, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryBetween(Integer value1, Integer value2) {
            addCriterion("inventory between", value1, value2, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryNotBetween(Integer value1, Integer value2) {
            addCriterion("inventory not between", value1, value2, "inventory");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(ProductFlowInstanceAtom.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andFlowInstanceIdLikeInsensitive(String value) {
            addCriterion("upper(flow_instance_id) like", value.toUpperCase(), "flowInstanceId");
            return (Criteria) this;
        }

        public Criteria andFlowIdLikeInsensitive(String value) {
            addCriterion("upper(flow_id) like", value.toUpperCase(), "flowId");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLikeInsensitive(String value) {
            addCriterion("upper(spu_code) like", value.toUpperCase(), "spuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLikeInsensitive(String value) {
            addCriterion("upper(sku_code) like", value.toUpperCase(), "skuCode");
            return (Criteria) this;
        }

        public Criteria andAtomNameLikeInsensitive(String value) {
            addCriterion("upper(atom_name) like", value.toUpperCase(), "atomName");
            return (Criteria) this;
        }

        public Criteria andUnitLikeInsensitive(String value) {
            addCriterion("upper(unit) like", value.toUpperCase(), "unit");
            return (Criteria) this;
        }

        public Criteria andServiceContentLikeInsensitive(String value) {
            addCriterion("upper(service_content) like", value.toUpperCase(), "serviceContent");
            return (Criteria) this;
        }

        public Criteria andCmiotCostProjectIdLikeInsensitive(String value) {
            addCriterion("upper(cmiot_cost_project_id) like", value.toUpperCase(), "cmiotCostProjectId");
            return (Criteria) this;
        }

        public Criteria andProvincePurchaseContractLikeInsensitive(String value) {
            addCriterion("upper(province_purchase_contract) like", value.toUpperCase(), "provincePurchaseContract");
            return (Criteria) this;
        }

        public Criteria andIotPurchaseContractLikeInsensitive(String value) {
            addCriterion("upper(iot_purchase_contract) like", value.toUpperCase(), "iotPurchaseContract");
            return (Criteria) this;
        }

        public Criteria andMaterialNumLikeInsensitive(String value) {
            addCriterion("upper(material_num) like", value.toUpperCase(), "materialNum");
            return (Criteria) this;
        }

        public Criteria andAtomRemarkLikeInsensitive(String value) {
            addCriterion("upper(atom_remark) like", value.toUpperCase(), "atomRemark");
            return (Criteria) this;
        }

        public Criteria andColorLikeInsensitive(String value) {
            addCriterion("upper(color) like", value.toUpperCase(), "color");
            return (Criteria) this;
        }

        public Criteria andModelLikeInsensitive(String value) {
            addCriterion("upper(model) like", value.toUpperCase(), "model");
            return (Criteria) this;
        }

        public Criteria andSoftAtomNameLikeInsensitive(String value) {
            addCriterion("upper(soft_atom_name) like", value.toUpperCase(), "softAtomName");
            return (Criteria) this;
        }

        public Criteria andSoftUnitLikeInsensitive(String value) {
            addCriterion("upper(soft_unit) like", value.toUpperCase(), "softUnit");
            return (Criteria) this;
        }

        public Criteria andSoftProductCodeLikeInsensitive(String value) {
            addCriterion("upper(soft_product_code) like", value.toUpperCase(), "softProductCode");
            return (Criteria) this;
        }

        public Criteria andNoSettlementLikeInsensitive(String value) {
            addCriterion("upper(no_settlement) like", value.toUpperCase(), "noSettlement");
            return (Criteria) this;
        }

        public Criteria andSettlementDetailNameLikeInsensitive(String value) {
            addCriterion("upper(settlement_detail_name) like", value.toUpperCase(), "settlementDetailName");
            return (Criteria) this;
        }

        public Criteria andDeliverPeriodLikeInsensitive(String value) {
            addCriterion("upper(deliver_period) like", value.toUpperCase(), "deliverPeriod");
            return (Criteria) this;
        }

        public Criteria andSoftServiceContentLikeInsensitive(String value) {
            addCriterion("upper(soft_service_content) like", value.toUpperCase(), "softServiceContent");
            return (Criteria) this;
        }

        public Criteria andServicePackageNameLikeInsensitive(String value) {
            addCriterion("upper(service_package_name) like", value.toUpperCase(), "servicePackageName");
            return (Criteria) this;
        }

        public Criteria andServiceContractLikeInsensitive(String value) {
            addCriterion("upper(service_contract) like", value.toUpperCase(), "serviceContract");
            return (Criteria) this;
        }

        public Criteria andSaleOutOfPriceRangeLikeInsensitive(String value) {
            addCriterion("upper(sale_out_of_price_range) like", value.toUpperCase(), "saleOutOfPriceRange");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Mon Mar 17 14:51:40 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Mon Mar 17 14:51:40 CST 2025
         */
        private ProductFlowInstanceAtomExample example;

        /**
         *
         * @mbg.generated Mon Mar 17 14:51:40 CST 2025
         */
        protected Criteria(ProductFlowInstanceAtomExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Mon Mar 17 14:51:40 CST 2025
         */
        public ProductFlowInstanceAtomExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Mon Mar 17 14:51:40 CST 2025
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Mar 17 14:51:40 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Mar 17 14:51:40 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Mon Mar 17 14:51:40 CST 2025
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Mon Mar 17 14:51:40 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Mon Mar 17 14:51:40 CST 2025
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Mon Mar 17 14:51:40 CST 2025
         */
        void example(com.chinamobile.iot.sc.pojo.ProductFlowInstanceAtomExample example);
    }
}