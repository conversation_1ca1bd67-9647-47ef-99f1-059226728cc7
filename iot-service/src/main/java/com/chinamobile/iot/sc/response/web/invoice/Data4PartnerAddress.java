package com.chinamobile.iot.sc.response.web.invoice;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @package: com.chinamobile.iot.sc.response.web.invoice
 * @ClassName: Data4PartnerAddress
 * @description: 合作伙伴退换货地址-展示类
 * @author: zyj
 * @create: 2021/12/23 19:22
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
@Accessors(chain = true)
public class Data4PartnerAddress {
    private String id;

    private String returnAddress;

    private String contactName;

    private String contactPhone;

    private Boolean isDefault;

    private String partnerId;

    private String partnerName;

    private String remark;

    private Date createTime;

    private Date updateTime;

    /**
     * 从账号 联系人姓名
     */
    private String partnerSlaveName;

    /**
     * 从账号 id
     */
    private String partnerSlaveId;

    /**
     * 从账号 联系人姓名 兼容
     */
    private String name;

    /**
     * 从账号电话号码
     */
    private String phone;
}
