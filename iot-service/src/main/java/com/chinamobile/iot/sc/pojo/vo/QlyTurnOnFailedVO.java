package com.chinamobile.iot.sc.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/1
 * @description 订单的卡相关信息展示类
 */
@Data
public class QlyTurnOnFailedVO {

    /**
     * 设备imei
     */
    @Excel(name = "IMEI")
    private String sn;


    /**
     * 失败编码
     * */
    @Excel(name = "失败原因")
    private String turnOnCode;

}
