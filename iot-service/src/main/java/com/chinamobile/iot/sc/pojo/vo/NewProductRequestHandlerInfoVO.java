package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;

import java.util.Date;
/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/21
 * @description 处理结果
 */
@Data
public class NewProductRequestHandlerInfoVO {

    /**
     * 申请环节
     */
    private String requestLink;

    private String currentHandlerUserId;

    /**
     * 当前处理人
     */
    private String currentHandlerUserName;

    private String nextHandlerUserId;

    /**
     * 下一处理人
     */
    private String nextHandlerUserName;

    /**
     * 处理状态
     */
    private String handlerStatusName;

    /**
     * 处理意见
     */
    private String handlerRemark;

    /**
     * 上架状态
     */
    private String onlineStatusName;

    /**
     * 创建日期
     */
    private Date createTime;

    private String createTimeStr;

    /**
     * 更新时间
     */
    private Date updateTime;

    private String updateTimeStr;

}