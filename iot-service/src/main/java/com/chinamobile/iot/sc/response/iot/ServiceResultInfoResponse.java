package com.chinamobile.iot.sc.response.iot;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/26 15:23
 * @description 商城单点登录响应
 */
@Data
public class ServiceResultInfoResponse {

    /**返回编码,0：全部成功，所有号码均支持订购；
     *失败依据错误码返回；
     *11 所有或部分号码不支持订购
     *12 服务开通号码校验超时
     *
     * 注：OS系统在30s内未收到云视讯返回结果时直接返回报错码12-服务开通号码校验超时
     * */
    private String resultCode;

    /**错误描述,失败依据错误描述返回；*/
    private String resultDesc;

    /**resultCode=11时，该节点必传，其他值非必传*/
    private List<ServiceResultInfoDTO> serviceResultInfo;

}
