package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 能力开发主题的具体内容，即主动API、订阅数据、主题配置
 *
 * <AUTHOR>
public class OpenAbilityThemeContent implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    private String id;

    /**
     * 主题ID
     *
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    private String themeId;

    /**
     * 类型：api-主动查询，subscribe-订阅
     *
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    private String type;

    /**
     * 接口名称/订阅数据名称
     *
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    private String name;

    /**
     * 接口路径/订阅数据编码
     *
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    private String pathCode;

    /**
     * 接口详情页面url/订阅数据详情页面
     *
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    private String introUrl;

    /**
     * 接口类型
     *
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    private String method;

    /**
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_content.id
     *
     * @return the value of supply_chain..open_ability_theme_content.id
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public OpenAbilityThemeContent withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_content.id
     *
     * @param id the value for supply_chain..open_ability_theme_content.id
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_content.theme_id
     *
     * @return the value of supply_chain..open_ability_theme_content.theme_id
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public String getThemeId() {
        return themeId;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public OpenAbilityThemeContent withThemeId(String themeId) {
        this.setThemeId(themeId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_content.theme_id
     *
     * @param themeId the value for supply_chain..open_ability_theme_content.theme_id
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public void setThemeId(String themeId) {
        this.themeId = themeId;
    }

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_content.type
     *
     * @return the value of supply_chain..open_ability_theme_content.type
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public String getType() {
        return type;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public OpenAbilityThemeContent withType(String type) {
        this.setType(type);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_content.type
     *
     * @param type the value for supply_chain..open_ability_theme_content.type
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_content.name
     *
     * @return the value of supply_chain..open_ability_theme_content.name
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public OpenAbilityThemeContent withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_content.name
     *
     * @param name the value for supply_chain..open_ability_theme_content.name
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_content.path_code
     *
     * @return the value of supply_chain..open_ability_theme_content.path_code
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public String getPathCode() {
        return pathCode;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public OpenAbilityThemeContent withPathCode(String pathCode) {
        this.setPathCode(pathCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_content.path_code
     *
     * @param pathCode the value for supply_chain..open_ability_theme_content.path_code
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public void setPathCode(String pathCode) {
        this.pathCode = pathCode;
    }

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_content.intro_url
     *
     * @return the value of supply_chain..open_ability_theme_content.intro_url
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public String getIntroUrl() {
        return introUrl;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public OpenAbilityThemeContent withIntroUrl(String introUrl) {
        this.setIntroUrl(introUrl);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_content.intro_url
     *
     * @param introUrl the value for supply_chain..open_ability_theme_content.intro_url
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public void setIntroUrl(String introUrl) {
        this.introUrl = introUrl;
    }

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_content.method
     *
     * @return the value of supply_chain..open_ability_theme_content.method
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public String getMethod() {
        return method;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public OpenAbilityThemeContent withMethod(String method) {
        this.setMethod(method);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_content.method
     *
     * @param method the value for supply_chain..open_ability_theme_content.method
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public void setMethod(String method) {
        this.method = method;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", themeId=").append(themeId);
        sb.append(", type=").append(type);
        sb.append(", name=").append(name);
        sb.append(", pathCode=").append(pathCode);
        sb.append(", introUrl=").append(introUrl);
        sb.append(", method=").append(method);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OpenAbilityThemeContent other = (OpenAbilityThemeContent) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getThemeId() == null ? other.getThemeId() == null : this.getThemeId().equals(other.getThemeId()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getPathCode() == null ? other.getPathCode() == null : this.getPathCode().equals(other.getPathCode()))
            && (this.getIntroUrl() == null ? other.getIntroUrl() == null : this.getIntroUrl().equals(other.getIntroUrl()))
            && (this.getMethod() == null ? other.getMethod() == null : this.getMethod().equals(other.getMethod()));
    }

    /**
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getThemeId() == null) ? 0 : getThemeId().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getPathCode() == null) ? 0 : getPathCode().hashCode());
        result = prime * result + ((getIntroUrl() == null) ? 0 : getIntroUrl().hashCode());
        result = prime * result + ((getMethod() == null) ? 0 : getMethod().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Thu Jun 06 16:42:10 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        themeId("theme_id", "themeId", "VARCHAR", false),
        type("type", "type", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        pathCode("path_code", "pathCode", "VARCHAR", false),
        introUrl("intro_url", "introUrl", "VARCHAR", false),
        method("method", "method", "VARCHAR", false);

        /**
         *
         * @mbg.generated Thu Jun 06 16:42:10 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Jun 06 16:42:10 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Jun 06 16:42:10 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Thu Jun 06 16:42:10 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Thu Jun 06 16:42:10 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Thu Jun 06 16:42:10 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Thu Jun 06 16:42:10 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Jun 06 16:42:10 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Jun 06 16:42:10 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Thu Jun 06 16:42:10 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Thu Jun 06 16:42:10 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Thu Jun 06 16:42:10 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Thu Jun 06 16:42:10 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Thu Jun 06 16:42:10 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Thu Jun 06 16:42:10 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Thu Jun 06 16:42:10 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Thu Jun 06 16:42:10 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}