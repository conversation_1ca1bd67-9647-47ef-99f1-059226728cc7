package com.chinamobile.iot.sc.pojo.dto.gio;

import com.chinamobile.iot.sc.pojo.*;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class GioOrderH5OrderCreatedHDOriginDTO {
    private String orderId;
    private String custMgPhone;
    private String provinceOrgName;
    private Integer orderStatus;
    private String spuOfferingClass;
    private String saleOrderType;
    private Date orderStatusTime;
    private String createTime;
    private Date billNoTime;
    private String sendGoodsTime;
    private String receiveOrderTime;
    private String valetOrderCompleteTime;
    private String arrivalTime;
    private List<LogisticsInfo> logisticsInfoList;
    private List<Order2cAtomInfo> order2cAtomInfoList;
    private String expensesPrice;
    private String orderQuantity;
    private String expensesTerm;
    public List<Order2cDistributorInfo> order2cDistributorInfoList;
    private String createOperUserId;
    private String createOperCode;
    private String custName;
    private List<Order2cAgentInfo> order2cAgentInfoList;
    private String orderingChannelName;
    private List<CouponInfo> couponInfoList;
    private String addr1;
    private String addr2;
    private String addr3;
    private List<String> order2cAtomSnList;
    private String orderGrid;
    private String custCode;
    private String totalPrice;
    private String businessCode;
    private String beId;
    private String location;
    private String regionID;






}
