package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 
 *
 * <AUTHOR>
public class K3syncStatisProcity implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String id;

    /**
     * 合同编码
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String contractNum;

    /**
     * 合同名称
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String contractName;

    /**
     * 合同部门
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String contractDept;

    /**
     * 合同统计方式
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String contractStatisType;

    /**
     * 合同销售员(配的)
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String contractSeller;

    /**
     * 合同类型
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String contractType;

    /**
     * 按省统计合同结算方式(0:按地市；1:按省份)
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private Integer contractSettleMode;

    /**
     * 币种
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String moneyUnit;

    /**
     * 销售单位(100)
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String sellUnit;

    /**
     * 商品类型
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String productType;

    /**
     * 订单收入归属省份
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String orderProvinceName;

    /**
     * 订单收入归属省份代码
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String orderProvinceCode;

    /**
     * 订单收入归属地市
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String orderCityName;

    /**
     * 订单收入归属地市代码
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String orderCityCode;

    /**
     * 订单条数
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String orderCount;

    /**
     * 当月金额
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private Long totalPrice;

    /**
     * K3返回订单编号
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String k3RetNum;

    /**
     * 同步状态 0：未同步； 1：成功；2：失败
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String k3SyncStatus;

    /**
     * 提交状态 0：未同步； 1：成功；2：失败
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String k3CommitStatus;

    /**
     * 销售员组织Id
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String sellerOrgId;

    /**
     * 销售员团队Id
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String sellerTeamId;

    /**
     * 销售员部门Id
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String sellerDeptId;

    /**
     * 销售员电话
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String sellerPhone;

    /**
     * 成本中心
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String costCenter;

    /**
     * 项目
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String project;

    /**
     * 子项目
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String subProject;

    /**
     * 上一次同步成功时间
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private Date syncSucTime;

    /**
     * 上一次提交成功时间
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private Date commitSucTime;

    /**
     * 客户编码
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String customCode;

    /**
     * 相对方省份
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String buyerProvince;

    /**
     * 相对方省份代码
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String buyerProvinceCode;

    /**
     * 相对方城市代码
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String buyerCityCode;

    /**
     * 相对方城市
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String buyerCity;

    /**
     * 表格生成时间
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private Date createTime;

    /**
     * 冗余字段，存储查出来的关联订单，不落库
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String relatedOrderIds;

    /**
     * 冗余字段，存储查出来的基础关联订单，不落库
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String relatedK3OrderIds;

    /**
     * 省采返回编号
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String proRetNum;

    /**
     * 省采同步状态 0：未同步； 1：成功；2：失败
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String proSyncStatus;

    /**
     * 省采报账状态：0--待报账  1--已报账
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String proSubmitAccountStatus;

    /**
     * 同步k3的用户名称
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String syncK3UserName;

    /**
     * 提交k3的用户名称
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String commitK3UserName;

    /**
     * 物料所属部门
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String materialDept;

    /**
     * 省销售数据编码
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private String proDataCode;

    /**
     * 是否生成k3销售订单0--未生成  1--已生成
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private Integer k3Status;

    /**
     * 省采材料返回状态 0--未返回 1--已返回
     *
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private Integer proMaterialStatus;

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.id
     *
     * @return the value of supply_chain..k3sync_statis_procity.id
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.id
     *
     * @param id the value for supply_chain..k3sync_statis_procity.id
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.contract_num
     *
     * @return the value of supply_chain..k3sync_statis_procity.contract_num
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getContractNum() {
        return contractNum;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withContractNum(String contractNum) {
        this.setContractNum(contractNum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.contract_num
     *
     * @param contractNum the value for supply_chain..k3sync_statis_procity.contract_num
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setContractNum(String contractNum) {
        this.contractNum = contractNum;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.contract_name
     *
     * @return the value of supply_chain..k3sync_statis_procity.contract_name
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getContractName() {
        return contractName;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withContractName(String contractName) {
        this.setContractName(contractName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.contract_name
     *
     * @param contractName the value for supply_chain..k3sync_statis_procity.contract_name
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setContractName(String contractName) {
        this.contractName = contractName;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.contract_dept
     *
     * @return the value of supply_chain..k3sync_statis_procity.contract_dept
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getContractDept() {
        return contractDept;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withContractDept(String contractDept) {
        this.setContractDept(contractDept);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.contract_dept
     *
     * @param contractDept the value for supply_chain..k3sync_statis_procity.contract_dept
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setContractDept(String contractDept) {
        this.contractDept = contractDept;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.contract_statis_type
     *
     * @return the value of supply_chain..k3sync_statis_procity.contract_statis_type
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getContractStatisType() {
        return contractStatisType;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withContractStatisType(String contractStatisType) {
        this.setContractStatisType(contractStatisType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.contract_statis_type
     *
     * @param contractStatisType the value for supply_chain..k3sync_statis_procity.contract_statis_type
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setContractStatisType(String contractStatisType) {
        this.contractStatisType = contractStatisType;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.contract_seller
     *
     * @return the value of supply_chain..k3sync_statis_procity.contract_seller
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getContractSeller() {
        return contractSeller;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withContractSeller(String contractSeller) {
        this.setContractSeller(contractSeller);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.contract_seller
     *
     * @param contractSeller the value for supply_chain..k3sync_statis_procity.contract_seller
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setContractSeller(String contractSeller) {
        this.contractSeller = contractSeller;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.contract_type
     *
     * @return the value of supply_chain..k3sync_statis_procity.contract_type
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getContractType() {
        return contractType;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withContractType(String contractType) {
        this.setContractType(contractType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.contract_type
     *
     * @param contractType the value for supply_chain..k3sync_statis_procity.contract_type
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.contract_settle_mode
     *
     * @return the value of supply_chain..k3sync_statis_procity.contract_settle_mode
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public Integer getContractSettleMode() {
        return contractSettleMode;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withContractSettleMode(Integer contractSettleMode) {
        this.setContractSettleMode(contractSettleMode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.contract_settle_mode
     *
     * @param contractSettleMode the value for supply_chain..k3sync_statis_procity.contract_settle_mode
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setContractSettleMode(Integer contractSettleMode) {
        this.contractSettleMode = contractSettleMode;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.money_unit
     *
     * @return the value of supply_chain..k3sync_statis_procity.money_unit
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getMoneyUnit() {
        return moneyUnit;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withMoneyUnit(String moneyUnit) {
        this.setMoneyUnit(moneyUnit);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.money_unit
     *
     * @param moneyUnit the value for supply_chain..k3sync_statis_procity.money_unit
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setMoneyUnit(String moneyUnit) {
        this.moneyUnit = moneyUnit;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.sell_unit
     *
     * @return the value of supply_chain..k3sync_statis_procity.sell_unit
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getSellUnit() {
        return sellUnit;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withSellUnit(String sellUnit) {
        this.setSellUnit(sellUnit);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.sell_unit
     *
     * @param sellUnit the value for supply_chain..k3sync_statis_procity.sell_unit
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setSellUnit(String sellUnit) {
        this.sellUnit = sellUnit;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.product_type
     *
     * @return the value of supply_chain..k3sync_statis_procity.product_type
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getProductType() {
        return productType;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withProductType(String productType) {
        this.setProductType(productType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.product_type
     *
     * @param productType the value for supply_chain..k3sync_statis_procity.product_type
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setProductType(String productType) {
        this.productType = productType;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.order_province_name
     *
     * @return the value of supply_chain..k3sync_statis_procity.order_province_name
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getOrderProvinceName() {
        return orderProvinceName;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withOrderProvinceName(String orderProvinceName) {
        this.setOrderProvinceName(orderProvinceName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.order_province_name
     *
     * @param orderProvinceName the value for supply_chain..k3sync_statis_procity.order_province_name
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setOrderProvinceName(String orderProvinceName) {
        this.orderProvinceName = orderProvinceName;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.order_province_code
     *
     * @return the value of supply_chain..k3sync_statis_procity.order_province_code
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getOrderProvinceCode() {
        return orderProvinceCode;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withOrderProvinceCode(String orderProvinceCode) {
        this.setOrderProvinceCode(orderProvinceCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.order_province_code
     *
     * @param orderProvinceCode the value for supply_chain..k3sync_statis_procity.order_province_code
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setOrderProvinceCode(String orderProvinceCode) {
        this.orderProvinceCode = orderProvinceCode;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.order_city_name
     *
     * @return the value of supply_chain..k3sync_statis_procity.order_city_name
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getOrderCityName() {
        return orderCityName;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withOrderCityName(String orderCityName) {
        this.setOrderCityName(orderCityName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.order_city_name
     *
     * @param orderCityName the value for supply_chain..k3sync_statis_procity.order_city_name
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setOrderCityName(String orderCityName) {
        this.orderCityName = orderCityName;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.order_city_code
     *
     * @return the value of supply_chain..k3sync_statis_procity.order_city_code
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getOrderCityCode() {
        return orderCityCode;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withOrderCityCode(String orderCityCode) {
        this.setOrderCityCode(orderCityCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.order_city_code
     *
     * @param orderCityCode the value for supply_chain..k3sync_statis_procity.order_city_code
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setOrderCityCode(String orderCityCode) {
        this.orderCityCode = orderCityCode;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.order_count
     *
     * @return the value of supply_chain..k3sync_statis_procity.order_count
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getOrderCount() {
        return orderCount;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withOrderCount(String orderCount) {
        this.setOrderCount(orderCount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.order_count
     *
     * @param orderCount the value for supply_chain..k3sync_statis_procity.order_count
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setOrderCount(String orderCount) {
        this.orderCount = orderCount;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.total_price
     *
     * @return the value of supply_chain..k3sync_statis_procity.total_price
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public Long getTotalPrice() {
        return totalPrice;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withTotalPrice(Long totalPrice) {
        this.setTotalPrice(totalPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.total_price
     *
     * @param totalPrice the value for supply_chain..k3sync_statis_procity.total_price
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setTotalPrice(Long totalPrice) {
        this.totalPrice = totalPrice;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.k3_ret_num
     *
     * @return the value of supply_chain..k3sync_statis_procity.k3_ret_num
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getK3RetNum() {
        return k3RetNum;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withK3RetNum(String k3RetNum) {
        this.setK3RetNum(k3RetNum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.k3_ret_num
     *
     * @param k3RetNum the value for supply_chain..k3sync_statis_procity.k3_ret_num
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setK3RetNum(String k3RetNum) {
        this.k3RetNum = k3RetNum;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.k3_sync_status
     *
     * @return the value of supply_chain..k3sync_statis_procity.k3_sync_status
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getK3SyncStatus() {
        return k3SyncStatus;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withK3SyncStatus(String k3SyncStatus) {
        this.setK3SyncStatus(k3SyncStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.k3_sync_status
     *
     * @param k3SyncStatus the value for supply_chain..k3sync_statis_procity.k3_sync_status
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setK3SyncStatus(String k3SyncStatus) {
        this.k3SyncStatus = k3SyncStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.k3_commit_status
     *
     * @return the value of supply_chain..k3sync_statis_procity.k3_commit_status
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getK3CommitStatus() {
        return k3CommitStatus;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withK3CommitStatus(String k3CommitStatus) {
        this.setK3CommitStatus(k3CommitStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.k3_commit_status
     *
     * @param k3CommitStatus the value for supply_chain..k3sync_statis_procity.k3_commit_status
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setK3CommitStatus(String k3CommitStatus) {
        this.k3CommitStatus = k3CommitStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.seller_org_id
     *
     * @return the value of supply_chain..k3sync_statis_procity.seller_org_id
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getSellerOrgId() {
        return sellerOrgId;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withSellerOrgId(String sellerOrgId) {
        this.setSellerOrgId(sellerOrgId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.seller_org_id
     *
     * @param sellerOrgId the value for supply_chain..k3sync_statis_procity.seller_org_id
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setSellerOrgId(String sellerOrgId) {
        this.sellerOrgId = sellerOrgId;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.seller_team_id
     *
     * @return the value of supply_chain..k3sync_statis_procity.seller_team_id
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getSellerTeamId() {
        return sellerTeamId;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withSellerTeamId(String sellerTeamId) {
        this.setSellerTeamId(sellerTeamId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.seller_team_id
     *
     * @param sellerTeamId the value for supply_chain..k3sync_statis_procity.seller_team_id
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setSellerTeamId(String sellerTeamId) {
        this.sellerTeamId = sellerTeamId;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.seller_dept_id
     *
     * @return the value of supply_chain..k3sync_statis_procity.seller_dept_id
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getSellerDeptId() {
        return sellerDeptId;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withSellerDeptId(String sellerDeptId) {
        this.setSellerDeptId(sellerDeptId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.seller_dept_id
     *
     * @param sellerDeptId the value for supply_chain..k3sync_statis_procity.seller_dept_id
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setSellerDeptId(String sellerDeptId) {
        this.sellerDeptId = sellerDeptId;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.seller_phone
     *
     * @return the value of supply_chain..k3sync_statis_procity.seller_phone
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getSellerPhone() {
        return sellerPhone;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withSellerPhone(String sellerPhone) {
        this.setSellerPhone(sellerPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.seller_phone
     *
     * @param sellerPhone the value for supply_chain..k3sync_statis_procity.seller_phone
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setSellerPhone(String sellerPhone) {
        this.sellerPhone = sellerPhone;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.cost_center
     *
     * @return the value of supply_chain..k3sync_statis_procity.cost_center
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getCostCenter() {
        return costCenter;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withCostCenter(String costCenter) {
        this.setCostCenter(costCenter);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.cost_center
     *
     * @param costCenter the value for supply_chain..k3sync_statis_procity.cost_center
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setCostCenter(String costCenter) {
        this.costCenter = costCenter;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.project
     *
     * @return the value of supply_chain..k3sync_statis_procity.project
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getProject() {
        return project;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withProject(String project) {
        this.setProject(project);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.project
     *
     * @param project the value for supply_chain..k3sync_statis_procity.project
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setProject(String project) {
        this.project = project;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.sub_project
     *
     * @return the value of supply_chain..k3sync_statis_procity.sub_project
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getSubProject() {
        return subProject;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withSubProject(String subProject) {
        this.setSubProject(subProject);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.sub_project
     *
     * @param subProject the value for supply_chain..k3sync_statis_procity.sub_project
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setSubProject(String subProject) {
        this.subProject = subProject;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.sync_suc_time
     *
     * @return the value of supply_chain..k3sync_statis_procity.sync_suc_time
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public Date getSyncSucTime() {
        return syncSucTime;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withSyncSucTime(Date syncSucTime) {
        this.setSyncSucTime(syncSucTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.sync_suc_time
     *
     * @param syncSucTime the value for supply_chain..k3sync_statis_procity.sync_suc_time
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setSyncSucTime(Date syncSucTime) {
        this.syncSucTime = syncSucTime;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.commit_suc_time
     *
     * @return the value of supply_chain..k3sync_statis_procity.commit_suc_time
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public Date getCommitSucTime() {
        return commitSucTime;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withCommitSucTime(Date commitSucTime) {
        this.setCommitSucTime(commitSucTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.commit_suc_time
     *
     * @param commitSucTime the value for supply_chain..k3sync_statis_procity.commit_suc_time
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setCommitSucTime(Date commitSucTime) {
        this.commitSucTime = commitSucTime;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.custom_code
     *
     * @return the value of supply_chain..k3sync_statis_procity.custom_code
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getCustomCode() {
        return customCode;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withCustomCode(String customCode) {
        this.setCustomCode(customCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.custom_code
     *
     * @param customCode the value for supply_chain..k3sync_statis_procity.custom_code
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setCustomCode(String customCode) {
        this.customCode = customCode;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.buyer_province
     *
     * @return the value of supply_chain..k3sync_statis_procity.buyer_province
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getBuyerProvince() {
        return buyerProvince;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withBuyerProvince(String buyerProvince) {
        this.setBuyerProvince(buyerProvince);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.buyer_province
     *
     * @param buyerProvince the value for supply_chain..k3sync_statis_procity.buyer_province
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setBuyerProvince(String buyerProvince) {
        this.buyerProvince = buyerProvince;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.buyer_province_code
     *
     * @return the value of supply_chain..k3sync_statis_procity.buyer_province_code
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getBuyerProvinceCode() {
        return buyerProvinceCode;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withBuyerProvinceCode(String buyerProvinceCode) {
        this.setBuyerProvinceCode(buyerProvinceCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.buyer_province_code
     *
     * @param buyerProvinceCode the value for supply_chain..k3sync_statis_procity.buyer_province_code
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setBuyerProvinceCode(String buyerProvinceCode) {
        this.buyerProvinceCode = buyerProvinceCode;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.buyer_city_code
     *
     * @return the value of supply_chain..k3sync_statis_procity.buyer_city_code
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getBuyerCityCode() {
        return buyerCityCode;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withBuyerCityCode(String buyerCityCode) {
        this.setBuyerCityCode(buyerCityCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.buyer_city_code
     *
     * @param buyerCityCode the value for supply_chain..k3sync_statis_procity.buyer_city_code
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setBuyerCityCode(String buyerCityCode) {
        this.buyerCityCode = buyerCityCode;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.buyer_city
     *
     * @return the value of supply_chain..k3sync_statis_procity.buyer_city
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getBuyerCity() {
        return buyerCity;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withBuyerCity(String buyerCity) {
        this.setBuyerCity(buyerCity);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.buyer_city
     *
     * @param buyerCity the value for supply_chain..k3sync_statis_procity.buyer_city
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setBuyerCity(String buyerCity) {
        this.buyerCity = buyerCity;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.create_time
     *
     * @return the value of supply_chain..k3sync_statis_procity.create_time
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.create_time
     *
     * @param createTime the value for supply_chain..k3sync_statis_procity.create_time
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.related_order_ids
     *
     * @return the value of supply_chain..k3sync_statis_procity.related_order_ids
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getRelatedOrderIds() {
        return relatedOrderIds;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withRelatedOrderIds(String relatedOrderIds) {
        this.setRelatedOrderIds(relatedOrderIds);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.related_order_ids
     *
     * @param relatedOrderIds the value for supply_chain..k3sync_statis_procity.related_order_ids
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setRelatedOrderIds(String relatedOrderIds) {
        this.relatedOrderIds = relatedOrderIds;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.related_k3_order_ids
     *
     * @return the value of supply_chain..k3sync_statis_procity.related_k3_order_ids
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getRelatedK3OrderIds() {
        return relatedK3OrderIds;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withRelatedK3OrderIds(String relatedK3OrderIds) {
        this.setRelatedK3OrderIds(relatedK3OrderIds);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.related_k3_order_ids
     *
     * @param relatedK3OrderIds the value for supply_chain..k3sync_statis_procity.related_k3_order_ids
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setRelatedK3OrderIds(String relatedK3OrderIds) {
        this.relatedK3OrderIds = relatedK3OrderIds;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.pro_ret_num
     *
     * @return the value of supply_chain..k3sync_statis_procity.pro_ret_num
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getProRetNum() {
        return proRetNum;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withProRetNum(String proRetNum) {
        this.setProRetNum(proRetNum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.pro_ret_num
     *
     * @param proRetNum the value for supply_chain..k3sync_statis_procity.pro_ret_num
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setProRetNum(String proRetNum) {
        this.proRetNum = proRetNum;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.pro_sync_status
     *
     * @return the value of supply_chain..k3sync_statis_procity.pro_sync_status
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getProSyncStatus() {
        return proSyncStatus;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withProSyncStatus(String proSyncStatus) {
        this.setProSyncStatus(proSyncStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.pro_sync_status
     *
     * @param proSyncStatus the value for supply_chain..k3sync_statis_procity.pro_sync_status
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setProSyncStatus(String proSyncStatus) {
        this.proSyncStatus = proSyncStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.pro_submit_account_status
     *
     * @return the value of supply_chain..k3sync_statis_procity.pro_submit_account_status
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getProSubmitAccountStatus() {
        return proSubmitAccountStatus;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withProSubmitAccountStatus(String proSubmitAccountStatus) {
        this.setProSubmitAccountStatus(proSubmitAccountStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.pro_submit_account_status
     *
     * @param proSubmitAccountStatus the value for supply_chain..k3sync_statis_procity.pro_submit_account_status
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setProSubmitAccountStatus(String proSubmitAccountStatus) {
        this.proSubmitAccountStatus = proSubmitAccountStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.sync_k3_user_name
     *
     * @return the value of supply_chain..k3sync_statis_procity.sync_k3_user_name
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getSyncK3UserName() {
        return syncK3UserName;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withSyncK3UserName(String syncK3UserName) {
        this.setSyncK3UserName(syncK3UserName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.sync_k3_user_name
     *
     * @param syncK3UserName the value for supply_chain..k3sync_statis_procity.sync_k3_user_name
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setSyncK3UserName(String syncK3UserName) {
        this.syncK3UserName = syncK3UserName;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.commit_k3_user_name
     *
     * @return the value of supply_chain..k3sync_statis_procity.commit_k3_user_name
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getCommitK3UserName() {
        return commitK3UserName;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withCommitK3UserName(String commitK3UserName) {
        this.setCommitK3UserName(commitK3UserName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.commit_k3_user_name
     *
     * @param commitK3UserName the value for supply_chain..k3sync_statis_procity.commit_k3_user_name
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setCommitK3UserName(String commitK3UserName) {
        this.commitK3UserName = commitK3UserName;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.material_dept
     *
     * @return the value of supply_chain..k3sync_statis_procity.material_dept
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getMaterialDept() {
        return materialDept;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withMaterialDept(String materialDept) {
        this.setMaterialDept(materialDept);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.material_dept
     *
     * @param materialDept the value for supply_chain..k3sync_statis_procity.material_dept
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setMaterialDept(String materialDept) {
        this.materialDept = materialDept;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.pro_data_code
     *
     * @return the value of supply_chain..k3sync_statis_procity.pro_data_code
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getProDataCode() {
        return proDataCode;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withProDataCode(String proDataCode) {
        this.setProDataCode(proDataCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.pro_data_code
     *
     * @param proDataCode the value for supply_chain..k3sync_statis_procity.pro_data_code
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setProDataCode(String proDataCode) {
        this.proDataCode = proDataCode;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.k3_status
     *
     * @return the value of supply_chain..k3sync_statis_procity.k3_status
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public Integer getK3Status() {
        return k3Status;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withK3Status(Integer k3Status) {
        this.setK3Status(k3Status);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.k3_status
     *
     * @param k3Status the value for supply_chain..k3sync_statis_procity.k3_status
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setK3Status(Integer k3Status) {
        this.k3Status = k3Status;
    }

    /**
     * This method returns the value of the database column supply_chain..k3sync_statis_procity.pro_material_status
     *
     * @return the value of supply_chain..k3sync_statis_procity.pro_material_status
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public Integer getProMaterialStatus() {
        return proMaterialStatus;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcity withProMaterialStatus(Integer proMaterialStatus) {
        this.setProMaterialStatus(proMaterialStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3sync_statis_procity.pro_material_status
     *
     * @param proMaterialStatus the value for supply_chain..k3sync_statis_procity.pro_material_status
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setProMaterialStatus(Integer proMaterialStatus) {
        this.proMaterialStatus = proMaterialStatus;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", contractNum=").append(contractNum);
        sb.append(", contractName=").append(contractName);
        sb.append(", contractDept=").append(contractDept);
        sb.append(", contractStatisType=").append(contractStatisType);
        sb.append(", contractSeller=").append(contractSeller);
        sb.append(", contractType=").append(contractType);
        sb.append(", contractSettleMode=").append(contractSettleMode);
        sb.append(", moneyUnit=").append(moneyUnit);
        sb.append(", sellUnit=").append(sellUnit);
        sb.append(", productType=").append(productType);
        sb.append(", orderProvinceName=").append(orderProvinceName);
        sb.append(", orderProvinceCode=").append(orderProvinceCode);
        sb.append(", orderCityName=").append(orderCityName);
        sb.append(", orderCityCode=").append(orderCityCode);
        sb.append(", orderCount=").append(orderCount);
        sb.append(", totalPrice=").append(totalPrice);
        sb.append(", k3RetNum=").append(k3RetNum);
        sb.append(", k3SyncStatus=").append(k3SyncStatus);
        sb.append(", k3CommitStatus=").append(k3CommitStatus);
        sb.append(", sellerOrgId=").append(sellerOrgId);
        sb.append(", sellerTeamId=").append(sellerTeamId);
        sb.append(", sellerDeptId=").append(sellerDeptId);
        sb.append(", sellerPhone=").append(sellerPhone);
        sb.append(", costCenter=").append(costCenter);
        sb.append(", project=").append(project);
        sb.append(", subProject=").append(subProject);
        sb.append(", syncSucTime=").append(syncSucTime);
        sb.append(", commitSucTime=").append(commitSucTime);
        sb.append(", customCode=").append(customCode);
        sb.append(", buyerProvince=").append(buyerProvince);
        sb.append(", buyerProvinceCode=").append(buyerProvinceCode);
        sb.append(", buyerCityCode=").append(buyerCityCode);
        sb.append(", buyerCity=").append(buyerCity);
        sb.append(", createTime=").append(createTime);
        sb.append(", relatedOrderIds=").append(relatedOrderIds);
        sb.append(", relatedK3OrderIds=").append(relatedK3OrderIds);
        sb.append(", proRetNum=").append(proRetNum);
        sb.append(", proSyncStatus=").append(proSyncStatus);
        sb.append(", proSubmitAccountStatus=").append(proSubmitAccountStatus);
        sb.append(", syncK3UserName=").append(syncK3UserName);
        sb.append(", commitK3UserName=").append(commitK3UserName);
        sb.append(", materialDept=").append(materialDept);
        sb.append(", proDataCode=").append(proDataCode);
        sb.append(", k3Status=").append(k3Status);
        sb.append(", proMaterialStatus=").append(proMaterialStatus);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        K3syncStatisProcity other = (K3syncStatisProcity) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getContractNum() == null ? other.getContractNum() == null : this.getContractNum().equals(other.getContractNum()))
            && (this.getContractName() == null ? other.getContractName() == null : this.getContractName().equals(other.getContractName()))
            && (this.getContractDept() == null ? other.getContractDept() == null : this.getContractDept().equals(other.getContractDept()))
            && (this.getContractStatisType() == null ? other.getContractStatisType() == null : this.getContractStatisType().equals(other.getContractStatisType()))
            && (this.getContractSeller() == null ? other.getContractSeller() == null : this.getContractSeller().equals(other.getContractSeller()))
            && (this.getContractType() == null ? other.getContractType() == null : this.getContractType().equals(other.getContractType()))
            && (this.getContractSettleMode() == null ? other.getContractSettleMode() == null : this.getContractSettleMode().equals(other.getContractSettleMode()))
            && (this.getMoneyUnit() == null ? other.getMoneyUnit() == null : this.getMoneyUnit().equals(other.getMoneyUnit()))
            && (this.getSellUnit() == null ? other.getSellUnit() == null : this.getSellUnit().equals(other.getSellUnit()))
            && (this.getProductType() == null ? other.getProductType() == null : this.getProductType().equals(other.getProductType()))
            && (this.getOrderProvinceName() == null ? other.getOrderProvinceName() == null : this.getOrderProvinceName().equals(other.getOrderProvinceName()))
            && (this.getOrderProvinceCode() == null ? other.getOrderProvinceCode() == null : this.getOrderProvinceCode().equals(other.getOrderProvinceCode()))
            && (this.getOrderCityName() == null ? other.getOrderCityName() == null : this.getOrderCityName().equals(other.getOrderCityName()))
            && (this.getOrderCityCode() == null ? other.getOrderCityCode() == null : this.getOrderCityCode().equals(other.getOrderCityCode()))
            && (this.getOrderCount() == null ? other.getOrderCount() == null : this.getOrderCount().equals(other.getOrderCount()))
            && (this.getTotalPrice() == null ? other.getTotalPrice() == null : this.getTotalPrice().equals(other.getTotalPrice()))
            && (this.getK3RetNum() == null ? other.getK3RetNum() == null : this.getK3RetNum().equals(other.getK3RetNum()))
            && (this.getK3SyncStatus() == null ? other.getK3SyncStatus() == null : this.getK3SyncStatus().equals(other.getK3SyncStatus()))
            && (this.getK3CommitStatus() == null ? other.getK3CommitStatus() == null : this.getK3CommitStatus().equals(other.getK3CommitStatus()))
            && (this.getSellerOrgId() == null ? other.getSellerOrgId() == null : this.getSellerOrgId().equals(other.getSellerOrgId()))
            && (this.getSellerTeamId() == null ? other.getSellerTeamId() == null : this.getSellerTeamId().equals(other.getSellerTeamId()))
            && (this.getSellerDeptId() == null ? other.getSellerDeptId() == null : this.getSellerDeptId().equals(other.getSellerDeptId()))
            && (this.getSellerPhone() == null ? other.getSellerPhone() == null : this.getSellerPhone().equals(other.getSellerPhone()))
            && (this.getCostCenter() == null ? other.getCostCenter() == null : this.getCostCenter().equals(other.getCostCenter()))
            && (this.getProject() == null ? other.getProject() == null : this.getProject().equals(other.getProject()))
            && (this.getSubProject() == null ? other.getSubProject() == null : this.getSubProject().equals(other.getSubProject()))
            && (this.getSyncSucTime() == null ? other.getSyncSucTime() == null : this.getSyncSucTime().equals(other.getSyncSucTime()))
            && (this.getCommitSucTime() == null ? other.getCommitSucTime() == null : this.getCommitSucTime().equals(other.getCommitSucTime()))
            && (this.getCustomCode() == null ? other.getCustomCode() == null : this.getCustomCode().equals(other.getCustomCode()))
            && (this.getBuyerProvince() == null ? other.getBuyerProvince() == null : this.getBuyerProvince().equals(other.getBuyerProvince()))
            && (this.getBuyerProvinceCode() == null ? other.getBuyerProvinceCode() == null : this.getBuyerProvinceCode().equals(other.getBuyerProvinceCode()))
            && (this.getBuyerCityCode() == null ? other.getBuyerCityCode() == null : this.getBuyerCityCode().equals(other.getBuyerCityCode()))
            && (this.getBuyerCity() == null ? other.getBuyerCity() == null : this.getBuyerCity().equals(other.getBuyerCity()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getRelatedOrderIds() == null ? other.getRelatedOrderIds() == null : this.getRelatedOrderIds().equals(other.getRelatedOrderIds()))
            && (this.getRelatedK3OrderIds() == null ? other.getRelatedK3OrderIds() == null : this.getRelatedK3OrderIds().equals(other.getRelatedK3OrderIds()))
            && (this.getProRetNum() == null ? other.getProRetNum() == null : this.getProRetNum().equals(other.getProRetNum()))
            && (this.getProSyncStatus() == null ? other.getProSyncStatus() == null : this.getProSyncStatus().equals(other.getProSyncStatus()))
            && (this.getProSubmitAccountStatus() == null ? other.getProSubmitAccountStatus() == null : this.getProSubmitAccountStatus().equals(other.getProSubmitAccountStatus()))
            && (this.getSyncK3UserName() == null ? other.getSyncK3UserName() == null : this.getSyncK3UserName().equals(other.getSyncK3UserName()))
            && (this.getCommitK3UserName() == null ? other.getCommitK3UserName() == null : this.getCommitK3UserName().equals(other.getCommitK3UserName()))
            && (this.getMaterialDept() == null ? other.getMaterialDept() == null : this.getMaterialDept().equals(other.getMaterialDept()))
            && (this.getProDataCode() == null ? other.getProDataCode() == null : this.getProDataCode().equals(other.getProDataCode()))
            && (this.getK3Status() == null ? other.getK3Status() == null : this.getK3Status().equals(other.getK3Status()))
            && (this.getProMaterialStatus() == null ? other.getProMaterialStatus() == null : this.getProMaterialStatus().equals(other.getProMaterialStatus()));
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getContractNum() == null) ? 0 : getContractNum().hashCode());
        result = prime * result + ((getContractName() == null) ? 0 : getContractName().hashCode());
        result = prime * result + ((getContractDept() == null) ? 0 : getContractDept().hashCode());
        result = prime * result + ((getContractStatisType() == null) ? 0 : getContractStatisType().hashCode());
        result = prime * result + ((getContractSeller() == null) ? 0 : getContractSeller().hashCode());
        result = prime * result + ((getContractType() == null) ? 0 : getContractType().hashCode());
        result = prime * result + ((getContractSettleMode() == null) ? 0 : getContractSettleMode().hashCode());
        result = prime * result + ((getMoneyUnit() == null) ? 0 : getMoneyUnit().hashCode());
        result = prime * result + ((getSellUnit() == null) ? 0 : getSellUnit().hashCode());
        result = prime * result + ((getProductType() == null) ? 0 : getProductType().hashCode());
        result = prime * result + ((getOrderProvinceName() == null) ? 0 : getOrderProvinceName().hashCode());
        result = prime * result + ((getOrderProvinceCode() == null) ? 0 : getOrderProvinceCode().hashCode());
        result = prime * result + ((getOrderCityName() == null) ? 0 : getOrderCityName().hashCode());
        result = prime * result + ((getOrderCityCode() == null) ? 0 : getOrderCityCode().hashCode());
        result = prime * result + ((getOrderCount() == null) ? 0 : getOrderCount().hashCode());
        result = prime * result + ((getTotalPrice() == null) ? 0 : getTotalPrice().hashCode());
        result = prime * result + ((getK3RetNum() == null) ? 0 : getK3RetNum().hashCode());
        result = prime * result + ((getK3SyncStatus() == null) ? 0 : getK3SyncStatus().hashCode());
        result = prime * result + ((getK3CommitStatus() == null) ? 0 : getK3CommitStatus().hashCode());
        result = prime * result + ((getSellerOrgId() == null) ? 0 : getSellerOrgId().hashCode());
        result = prime * result + ((getSellerTeamId() == null) ? 0 : getSellerTeamId().hashCode());
        result = prime * result + ((getSellerDeptId() == null) ? 0 : getSellerDeptId().hashCode());
        result = prime * result + ((getSellerPhone() == null) ? 0 : getSellerPhone().hashCode());
        result = prime * result + ((getCostCenter() == null) ? 0 : getCostCenter().hashCode());
        result = prime * result + ((getProject() == null) ? 0 : getProject().hashCode());
        result = prime * result + ((getSubProject() == null) ? 0 : getSubProject().hashCode());
        result = prime * result + ((getSyncSucTime() == null) ? 0 : getSyncSucTime().hashCode());
        result = prime * result + ((getCommitSucTime() == null) ? 0 : getCommitSucTime().hashCode());
        result = prime * result + ((getCustomCode() == null) ? 0 : getCustomCode().hashCode());
        result = prime * result + ((getBuyerProvince() == null) ? 0 : getBuyerProvince().hashCode());
        result = prime * result + ((getBuyerProvinceCode() == null) ? 0 : getBuyerProvinceCode().hashCode());
        result = prime * result + ((getBuyerCityCode() == null) ? 0 : getBuyerCityCode().hashCode());
        result = prime * result + ((getBuyerCity() == null) ? 0 : getBuyerCity().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getRelatedOrderIds() == null) ? 0 : getRelatedOrderIds().hashCode());
        result = prime * result + ((getRelatedK3OrderIds() == null) ? 0 : getRelatedK3OrderIds().hashCode());
        result = prime * result + ((getProRetNum() == null) ? 0 : getProRetNum().hashCode());
        result = prime * result + ((getProSyncStatus() == null) ? 0 : getProSyncStatus().hashCode());
        result = prime * result + ((getProSubmitAccountStatus() == null) ? 0 : getProSubmitAccountStatus().hashCode());
        result = prime * result + ((getSyncK3UserName() == null) ? 0 : getSyncK3UserName().hashCode());
        result = prime * result + ((getCommitK3UserName() == null) ? 0 : getCommitK3UserName().hashCode());
        result = prime * result + ((getMaterialDept() == null) ? 0 : getMaterialDept().hashCode());
        result = prime * result + ((getProDataCode() == null) ? 0 : getProDataCode().hashCode());
        result = prime * result + ((getK3Status() == null) ? 0 : getK3Status().hashCode());
        result = prime * result + ((getProMaterialStatus() == null) ? 0 : getProMaterialStatus().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        contractNum("contract_num", "contractNum", "VARCHAR", false),
        contractName("contract_name", "contractName", "VARCHAR", false),
        contractDept("contract_dept", "contractDept", "VARCHAR", false),
        contractStatisType("contract_statis_type", "contractStatisType", "VARCHAR", false),
        contractSeller("contract_seller", "contractSeller", "VARCHAR", false),
        contractType("contract_type", "contractType", "VARCHAR", false),
        contractSettleMode("contract_settle_mode", "contractSettleMode", "INTEGER", false),
        moneyUnit("money_unit", "moneyUnit", "VARCHAR", false),
        sellUnit("sell_unit", "sellUnit", "VARCHAR", false),
        productType("product_type", "productType", "VARCHAR", false),
        orderProvinceName("order_province_name", "orderProvinceName", "VARCHAR", false),
        orderProvinceCode("order_province_code", "orderProvinceCode", "VARCHAR", false),
        orderCityName("order_city_name", "orderCityName", "VARCHAR", false),
        orderCityCode("order_city_code", "orderCityCode", "VARCHAR", false),
        orderCount("order_count", "orderCount", "VARCHAR", false),
        totalPrice("total_price", "totalPrice", "BIGINT", false),
        k3RetNum("k3_ret_num", "k3RetNum", "VARCHAR", false),
        k3SyncStatus("k3_sync_status", "k3SyncStatus", "VARCHAR", false),
        k3CommitStatus("k3_commit_status", "k3CommitStatus", "VARCHAR", false),
        sellerOrgId("seller_org_id", "sellerOrgId", "VARCHAR", false),
        sellerTeamId("seller_team_id", "sellerTeamId", "VARCHAR", false),
        sellerDeptId("seller_dept_id", "sellerDeptId", "VARCHAR", false),
        sellerPhone("seller_phone", "sellerPhone", "VARCHAR", false),
        costCenter("cost_center", "costCenter", "VARCHAR", false),
        project("project", "project", "VARCHAR", false),
        subProject("sub_project", "subProject", "VARCHAR", false),
        syncSucTime("sync_suc_time", "syncSucTime", "TIMESTAMP", false),
        commitSucTime("commit_suc_time", "commitSucTime", "TIMESTAMP", false),
        customCode("custom_code", "customCode", "VARCHAR", false),
        buyerProvince("buyer_province", "buyerProvince", "VARCHAR", false),
        buyerProvinceCode("buyer_province_code", "buyerProvinceCode", "VARCHAR", false),
        buyerCityCode("buyer_city_code", "buyerCityCode", "VARCHAR", false),
        buyerCity("buyer_city", "buyerCity", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        relatedOrderIds("related_order_ids", "relatedOrderIds", "VARCHAR", false),
        relatedK3OrderIds("related_k3_order_ids", "relatedK3OrderIds", "VARCHAR", false),
        proRetNum("pro_ret_num", "proRetNum", "VARCHAR", false),
        proSyncStatus("pro_sync_status", "proSyncStatus", "VARCHAR", false),
        proSubmitAccountStatus("pro_submit_account_status", "proSubmitAccountStatus", "VARCHAR", false),
        syncK3UserName("sync_k3_user_name", "syncK3UserName", "VARCHAR", false),
        commitK3UserName("commit_k3_user_name", "commitK3UserName", "VARCHAR", false),
        materialDept("material_dept", "materialDept", "VARCHAR", false),
        proDataCode("pro_data_code", "proDataCode", "VARCHAR", false),
        k3Status("k3_status", "k3Status", "INTEGER", false),
        proMaterialStatus("pro_material_status", "proMaterialStatus", "INTEGER", false);

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}