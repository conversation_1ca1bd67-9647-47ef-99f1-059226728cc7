package com.chinamobile.iot.sc.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 物料配置
 */
@Data
public class MaterialConfigDTO {

    private String atomOfferingCode;

    @Excel(name = "物料编码",width = 20)
    private String materialCode;

    @Excel(name = "物料数量",type = 10,width = 20)
    private BigDecimal materialNum;

    @Excel(name = "合同编号",width = 20)
    private String contractSn;

}
