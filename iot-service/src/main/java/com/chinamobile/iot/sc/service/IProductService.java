package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.entity.iot.IotSearchMallLinkRequest;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.AftermarketOfferingCode;
import com.chinamobile.iot.sc.pojo.AtomOfferingInfoHistory;
import com.chinamobile.iot.sc.pojo.SkuOfferingInfoHistory;
import com.chinamobile.iot.sc.pojo.dto.ProMaterialDTO;
import com.chinamobile.iot.sc.pojo.dto.ProductConfigAtomLogDTO;
import com.chinamobile.iot.sc.pojo.param.ProductExportParam;
import com.chinamobile.iot.sc.pojo.param.UpdateAtomInventoryParam;
import com.chinamobile.iot.sc.pojo.vo.ProductConfigVO;
import com.chinamobile.iot.sc.pojo.vo.ProductMaterialDetailItemVO;
import com.chinamobile.iot.sc.pojo.vo.ProductMaterialsItemVO;
import com.chinamobile.iot.sc.request.ProductConfigRequest;
import com.chinamobile.iot.sc.response.web.ProductInfoDTO;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/11/4 17:18
 * @Description:
 */
public interface IProductService {
    IOTAnswer<Void> syncOfferingInfo(IOTRequest baseRequest);

    IOTAnswer<Void> syncSkuOfferingInfos(IOTRequest iotRequest);

    IOTAnswer<Void> syncBenefitOfferingsInfo(IOTRequest iotRequest);

//    BaseAnswer<Void> addCooperator(CooperAddRequest cooperAddRequest, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<PageData<ProductInfoDTO>> getProductList(String spuOfferingName,String spuOfferingCode, String skuOfferingName,String skuOfferingCode,
                                                        String atomOfferingName, List<String> spuOfferingClass,
                                                        String partnerName, String cooperatorName,
                                                        List<String> spuOfferingStatus, List<String> skuOfferingStatus,
                                                        String configAllStatus, String h5Key,List<String> h5SpuOfferingClasses,
                                                        String templateId,String templateName,String deviceVersion,
                                                        LoginIfo4Redis loginIfo4Redis, Integer page, Integer num);

    BaseAnswer<Boolean> judgeInfoByUserId(String userId);

    BaseAnswer<String> getShareUrl(IotSearchMallLinkRequest request);
    /**
     * 商品状态数据割接(spu, sku, atom)
     * @param excel
     */
    BaseAnswer spuStatusCutOver(MultipartFile excel);

    /**
     * IoT售后服务包信息同步
     * @param request
     * @return
     */
    IOTAnswer<Void> syncAftermarketOfferingInfos(IOTRequest request);

    BaseAnswer<ProductConfigVO> getProductConfigInfo(String id);

    BaseAnswer<Void> setProductConfig(ProductConfigRequest productConfigRequest, LoginIfo4Redis loginIfo4Redis);

    void productExport(ProductExportParam param, LoginIfo4Redis loginIfo4Redis,String ip);

    /**
     * 获取商品的导出数量
     * @param param
     * @param loginIfo4Redis
     * @param ip
     * @return
     */
    Integer getProductExportCount(ProductExportParam param, LoginIfo4Redis loginIfo4Redis,String ip);

    void importProductConfig(MultipartFile file, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<List<ProductMaterialsItemVO>>  getConfigedMaterials(String atomId);

    /**
     * 批量导入商品的专合结算价
     * @param inputStream
     * @param loginIfo4Redis
     * @param response
     * @param importRedisKey
     * @throws Exception
     */
    void batchProductStdPriceConfig(InputStream inputStream,
                      LoginIfo4Redis loginIfo4Redis,
                      HttpServletResponse response,
                      String importRedisKey) throws Exception;

    /**
     * 批量导入商品物料
     * @param inputStream
     * @param loginIfo4Redis
     * @param response
     * @param importRedisKey
     * @throws Exception
     */
    void batchProductMaterialConfig(InputStream inputStream,
                                    LoginIfo4Redis loginIfo4Redis,
                                    HttpServletResponse response,
                                    String importRedisKey) throws Exception;

    BaseAnswer<List<ProductMaterialDetailItemVO>> getUnConfigedMaterials(String atomId, String materialNum);

    void testSave(ProMaterialDTO param);

    /**
     * sku商品发布范围割接
     * @param excel
     * @return
     */
    BaseAnswer skuReleaseTargetCutOver(MultipartFile excel);

    /**
     * 商品原子信息结算单价+结算明细服务名称割接模板割接
     * @param excel
     * @return
     */
    BaseAnswer atomSettlePriceCutOver(MultipartFile excel);

    /**
     * 账目项ID割接
     * @param excel
     * @return
     */
    BaseAnswer atomChargeCodeCutOver(MultipartFile excel);

    IOTAnswer<Void> syncAtomOfferingInfos(IOTRequest iotRequest);

    /**
     * 商品版本号数据割接(spu, sku, atom)
     * @param excel
     */
    BaseAnswer productVersionCutOver(MultipartFile excel);

    /**
     * 售后服务商品版本号数据割接
     * @param excel
     */
    BaseAnswer aftermarketVersionCutOver(MultipartFile excel);

    /**
     * 商品版本号数据割接修复(spu, sku, atom)
     * @param excel
     */
    BaseAnswer productVersionCutOverFix(MultipartFile excel);

    /**
     * 售后服务商品版本号数据割接修复
     * @param excel
     */
    BaseAnswer aftermarketVersionCutOverFix(MultipartFile excel);

    /**
     * 商品版本号数据割接修复(spu, sku, atom)
     * @param excel
     */
    BaseAnswer productVersionCutOverFixSku(MultipartFile excel);

    /**
     * 商品版本号数据割接修复(spu, sku, atom)
     * @param excel
     */
    BaseAnswer productVersionCutOverFixAtom(MultipartFile excel);

    /**
     * 商品版本号修复-针对只同步了spu的信息(spu)
     * @param excel
     */
    BaseAnswer productVersionSpuCutOver(MultipartFile excel);

    /**
     * 售后服务商品版本号修复
     * @param request
     */
    BaseAnswer aftermarketVersionFix(AftermarketOfferingCode request);

    BaseAnswer syncSkuCooperatorId();

    String getConfigLogContent(Boolean update, String title, String spuCode, String skuCode, List<ProductConfigAtomLogDTO> productConfigAtomLogDTOS, Integer type);

    @Transactional(rollbackFor = Exception.class)
    IOTAnswer<Void> SyncOfferingNavigationInfo(IOTRequest iotRequest);

    @Transactional(rollbackFor = Exception.class)
    BaseAnswer SyncOfferingNavigationInfoCutOver(MultipartFile excel);

    /**
     * IoT省内融合包信息同步割接
     * @param excel
     * @return
     */
    BaseAnswer syncBenefitOfferingsInfoCutOver(MultipartFile excel) throws Exception;

    /**
     * 商品开卡模板编码数据割接
     * @param excel
     * @return
     * @throws Exception
     */
    BaseAnswer syncSkuTemplateIdCutOver(MultipartFile excel) throws Exception;

    /**
     * sku版本号修复
     * @param request
     */
    @Transactional(rollbackFor = Exception.class)
    BaseAnswer skuOfferingInfoVersionFix(SkuOfferingInfoHistory request);

    /**
     * atom版本号修复
     * @param request
     */
    @Transactional(rollbackFor = Exception.class)
    BaseAnswer atomOfferingInfoVersionFix(AtomOfferingInfoHistory request);

    @Transactional(rollbackFor = Exception.class)
    void importProductDir(MultipartFile excel);

    /**
     * 处理商品的合作伙伴的历史数据
     */
    void updateAtomOfferingHistoryCooperator(String atomOfferingId);

    /**
     * 处理合作伙伴为“-1”和sku商品合作伙伴不相同的数据
     */
    void updateCooperateIdNotEqual();

    /**
     * spu割接
     */
    BaseAnswer spuCutOver(MultipartFile excel) throws Exception;

    /**
     * 设置修改服务类原子预占信息
     * @param param
     */
    void updateAtomInventory(UpdateAtomInventoryParam param);

    /**
     * sku割接
     */
    BaseAnswer skuCutOver(MultipartFile excel) throws Exception;
}
