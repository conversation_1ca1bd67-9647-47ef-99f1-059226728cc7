package com.chinamobile.iot.sc.pojo.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class BRMUploadDTO implements Serializable {
    /**
     * 原子id
     */
    private String id;
    /**
     * 地区标识
     */
    private String region;

    /**
     * 产商品编码
     */
    private String bossId;

    /**
     * 产商品名称
     */
    private String vasName;

    /**
     * 设备型号
     */
    private String xModelName;

    /**
     * 设备可用库存量
     */
    private Long xStockNum;

    /**
     * 设备预占量
     */
    private Long xPickNum;

    /**
     * 开卡模板编码
     */
    private String cardTempleCode;

    /**
     * 开卡模板名称
     */
    private String cardTempleName;

    /**
     * 归属卡服务商编码
     */
    private String cardVenderCode;

    /**
     * 归属卡服务商名称
     */
    private String cardVenderName;

    /**
     * 卡可用库存量
     */
    private Long cardStockNum;

    /**
     * 卡预占量
     */
    private Long cardPickNum;

    /**
     * 销量
     */
    private Long saleNum;
}