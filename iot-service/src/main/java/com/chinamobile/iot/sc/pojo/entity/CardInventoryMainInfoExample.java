package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CardInventoryMainInfoExample {
    /**
     * Corresponding to the database table supply_chain..card_inventory_main_info
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    protected String orderByClause;

    /**
     * Corresponding to the database table supply_chain..card_inventory_main_info
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    protected boolean distinct;

    /**
     * Corresponding to the database table supply_chain..card_inventory_main_info
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        CardInventoryMainInfoExample example = new CardInventoryMainInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public CardInventoryMainInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     * Corresponding to the database table supply_chain..card_inventory_main_info
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCustCodeIsNull() {
            addCriterion("cust_code is null");
            return (Criteria) this;
        }

        public Criteria andCustCodeIsNotNull() {
            addCriterion("cust_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustCodeEqualTo(String value) {
            addCriterion("cust_code =", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("cust_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeNotEqualTo(String value) {
            addCriterion("cust_code <>", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("cust_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThan(String value) {
            addCriterion("cust_code >", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("cust_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanOrEqualTo(String value) {
            addCriterion("cust_code >=", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("cust_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThan(String value) {
            addCriterion("cust_code <", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("cust_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanOrEqualTo(String value) {
            addCriterion("cust_code <=", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("cust_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLike(String value) {
            addCriterion("cust_code like", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotLike(String value) {
            addCriterion("cust_code not like", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeIn(List<String> values) {
            addCriterion("cust_code in", values, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotIn(List<String> values) {
            addCriterion("cust_code not in", values, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeBetween(String value1, String value2) {
            addCriterion("cust_code between", value1, value2, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotBetween(String value1, String value2) {
            addCriterion("cust_code not between", value1, value2, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustNameIsNull() {
            addCriterion("cust_name is null");
            return (Criteria) this;
        }

        public Criteria andCustNameIsNotNull() {
            addCriterion("cust_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustNameEqualTo(String value) {
            addCriterion("cust_name =", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("cust_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameNotEqualTo(String value) {
            addCriterion("cust_name <>", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("cust_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThan(String value) {
            addCriterion("cust_name >", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("cust_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThanOrEqualTo(String value) {
            addCriterion("cust_name >=", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("cust_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameLessThan(String value) {
            addCriterion("cust_name <", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameLessThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("cust_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameLessThanOrEqualTo(String value) {
            addCriterion("cust_name <=", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameLessThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("cust_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameLike(String value) {
            addCriterion("cust_name like", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotLike(String value) {
            addCriterion("cust_name not like", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameIn(List<String> values) {
            addCriterion("cust_name in", values, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotIn(List<String> values) {
            addCriterion("cust_name not in", values, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameBetween(String value1, String value2) {
            addCriterion("cust_name between", value1, value2, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotBetween(String value1, String value2) {
            addCriterion("cust_name not between", value1, value2, "custName");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNull() {
            addCriterion("template_id is null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNotNull() {
            addCriterion("template_id is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdEqualTo(String value) {
            addCriterion("template_id =", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("template_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotEqualTo(String value) {
            addCriterion("template_id <>", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("template_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThan(String value) {
            addCriterion("template_id >", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("template_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanOrEqualTo(String value) {
            addCriterion("template_id >=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("template_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThan(String value) {
            addCriterion("template_id <", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("template_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanOrEqualTo(String value) {
            addCriterion("template_id <=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("template_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateIdLike(String value) {
            addCriterion("template_id like", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotLike(String value) {
            addCriterion("template_id not like", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIn(List<String> values) {
            addCriterion("template_id in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotIn(List<String> values) {
            addCriterion("template_id not in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdBetween(String value1, String value2) {
            addCriterion("template_id between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotBetween(String value1, String value2) {
            addCriterion("template_id not between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateNameIsNull() {
            addCriterion("template_name is null");
            return (Criteria) this;
        }

        public Criteria andTemplateNameIsNotNull() {
            addCriterion("template_name is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateNameEqualTo(String value) {
            addCriterion("template_name =", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("template_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotEqualTo(String value) {
            addCriterion("template_name <>", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("template_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateNameGreaterThan(String value) {
            addCriterion("template_name >", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameGreaterThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("template_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateNameGreaterThanOrEqualTo(String value) {
            addCriterion("template_name >=", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameGreaterThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("template_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateNameLessThan(String value) {
            addCriterion("template_name <", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameLessThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("template_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateNameLessThanOrEqualTo(String value) {
            addCriterion("template_name <=", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameLessThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("template_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTemplateNameLike(String value) {
            addCriterion("template_name like", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotLike(String value) {
            addCriterion("template_name not like", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameIn(List<String> values) {
            addCriterion("template_name in", values, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotIn(List<String> values) {
            addCriterion("template_name not in", values, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameBetween(String value1, String value2) {
            addCriterion("template_name between", value1, value2, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotBetween(String value1, String value2) {
            addCriterion("template_name not between", value1, value2, "templateName");
            return (Criteria) this;
        }

        public Criteria andBeIdIsNull() {
            addCriterion("be_id is null");
            return (Criteria) this;
        }

        public Criteria andBeIdIsNotNull() {
            addCriterion("be_id is not null");
            return (Criteria) this;
        }

        public Criteria andBeIdEqualTo(String value) {
            addCriterion("be_id =", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("be_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdNotEqualTo(String value) {
            addCriterion("be_id <>", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("be_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThan(String value) {
            addCriterion("be_id >", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("be_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanOrEqualTo(String value) {
            addCriterion("be_id >=", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("be_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLessThan(String value) {
            addCriterion("be_id <", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("be_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanOrEqualTo(String value) {
            addCriterion("be_id <=", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("be_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLike(String value) {
            addCriterion("be_id like", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotLike(String value) {
            addCriterion("be_id not like", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdIn(List<String> values) {
            addCriterion("be_id in", values, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotIn(List<String> values) {
            addCriterion("be_id not in", values, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdBetween(String value1, String value2) {
            addCriterion("be_id between", value1, value2, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotBetween(String value1, String value2) {
            addCriterion("be_id not between", value1, value2, "beId");
            return (Criteria) this;
        }

        public Criteria andProviceNameIsNull() {
            addCriterion("provice_name is null");
            return (Criteria) this;
        }

        public Criteria andProviceNameIsNotNull() {
            addCriterion("provice_name is not null");
            return (Criteria) this;
        }

        public Criteria andProviceNameEqualTo(String value) {
            addCriterion("provice_name =", value, "proviceName");
            return (Criteria) this;
        }

        public Criteria andProviceNameEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("provice_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProviceNameNotEqualTo(String value) {
            addCriterion("provice_name <>", value, "proviceName");
            return (Criteria) this;
        }

        public Criteria andProviceNameNotEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("provice_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProviceNameGreaterThan(String value) {
            addCriterion("provice_name >", value, "proviceName");
            return (Criteria) this;
        }

        public Criteria andProviceNameGreaterThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("provice_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProviceNameGreaterThanOrEqualTo(String value) {
            addCriterion("provice_name >=", value, "proviceName");
            return (Criteria) this;
        }

        public Criteria andProviceNameGreaterThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("provice_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProviceNameLessThan(String value) {
            addCriterion("provice_name <", value, "proviceName");
            return (Criteria) this;
        }

        public Criteria andProviceNameLessThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("provice_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProviceNameLessThanOrEqualTo(String value) {
            addCriterion("provice_name <=", value, "proviceName");
            return (Criteria) this;
        }

        public Criteria andProviceNameLessThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("provice_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProviceNameLike(String value) {
            addCriterion("provice_name like", value, "proviceName");
            return (Criteria) this;
        }

        public Criteria andProviceNameNotLike(String value) {
            addCriterion("provice_name not like", value, "proviceName");
            return (Criteria) this;
        }

        public Criteria andProviceNameIn(List<String> values) {
            addCriterion("provice_name in", values, "proviceName");
            return (Criteria) this;
        }

        public Criteria andProviceNameNotIn(List<String> values) {
            addCriterion("provice_name not in", values, "proviceName");
            return (Criteria) this;
        }

        public Criteria andProviceNameBetween(String value1, String value2) {
            addCriterion("provice_name between", value1, value2, "proviceName");
            return (Criteria) this;
        }

        public Criteria andProviceNameNotBetween(String value1, String value2) {
            addCriterion("provice_name not between", value1, value2, "proviceName");
            return (Criteria) this;
        }

        public Criteria andRegionIdIsNull() {
            addCriterion("region_id is null");
            return (Criteria) this;
        }

        public Criteria andRegionIdIsNotNull() {
            addCriterion("region_id is not null");
            return (Criteria) this;
        }

        public Criteria andRegionIdEqualTo(String value) {
            addCriterion("region_id =", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("region_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdNotEqualTo(String value) {
            addCriterion("region_id <>", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("region_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThan(String value) {
            addCriterion("region_id >", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("region_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanOrEqualTo(String value) {
            addCriterion("region_id >=", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("region_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThan(String value) {
            addCriterion("region_id <", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("region_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanOrEqualTo(String value) {
            addCriterion("region_id <=", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("region_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLike(String value) {
            addCriterion("region_id like", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotLike(String value) {
            addCriterion("region_id not like", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdIn(List<String> values) {
            addCriterion("region_id in", values, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotIn(List<String> values) {
            addCriterion("region_id not in", values, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdBetween(String value1, String value2) {
            addCriterion("region_id between", value1, value2, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotBetween(String value1, String value2) {
            addCriterion("region_id not between", value1, value2, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionNameIsNull() {
            addCriterion("region_name is null");
            return (Criteria) this;
        }

        public Criteria andRegionNameIsNotNull() {
            addCriterion("region_name is not null");
            return (Criteria) this;
        }

        public Criteria andRegionNameEqualTo(String value) {
            addCriterion("region_name =", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("region_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameNotEqualTo(String value) {
            addCriterion("region_name <>", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("region_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThan(String value) {
            addCriterion("region_name >", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("region_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThanOrEqualTo(String value) {
            addCriterion("region_name >=", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("region_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThan(String value) {
            addCriterion("region_name <", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("region_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThanOrEqualTo(String value) {
            addCriterion("region_name <=", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("region_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameLike(String value) {
            addCriterion("region_name like", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotLike(String value) {
            addCriterion("region_name not like", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameIn(List<String> values) {
            addCriterion("region_name in", values, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotIn(List<String> values) {
            addCriterion("region_name not in", values, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameBetween(String value1, String value2) {
            addCriterion("region_name between", value1, value2, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotBetween(String value1, String value2) {
            addCriterion("region_name not between", value1, value2, "regionName");
            return (Criteria) this;
        }

        public Criteria andCardTypeIsNull() {
            addCriterion("card_type is null");
            return (Criteria) this;
        }

        public Criteria andCardTypeIsNotNull() {
            addCriterion("card_type is not null");
            return (Criteria) this;
        }

        public Criteria andCardTypeEqualTo(String value) {
            addCriterion("card_type =", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("card_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardTypeNotEqualTo(String value) {
            addCriterion("card_type <>", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("card_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardTypeGreaterThan(String value) {
            addCriterion("card_type >", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeGreaterThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("card_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardTypeGreaterThanOrEqualTo(String value) {
            addCriterion("card_type >=", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeGreaterThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("card_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardTypeLessThan(String value) {
            addCriterion("card_type <", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeLessThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("card_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardTypeLessThanOrEqualTo(String value) {
            addCriterion("card_type <=", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeLessThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("card_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardTypeLike(String value) {
            addCriterion("card_type like", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotLike(String value) {
            addCriterion("card_type not like", value, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeIn(List<String> values) {
            addCriterion("card_type in", values, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotIn(List<String> values) {
            addCriterion("card_type not in", values, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeBetween(String value1, String value2) {
            addCriterion("card_type between", value1, value2, "cardType");
            return (Criteria) this;
        }

        public Criteria andCardTypeNotBetween(String value1, String value2) {
            addCriterion("card_type not between", value1, value2, "cardType");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityIsNull() {
            addCriterion("reserve_quatity is null");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityIsNotNull() {
            addCriterion("reserve_quatity is not null");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityEqualTo(Integer value) {
            addCriterion("reserve_quatity =", value, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("reserve_quatity = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveQuatityNotEqualTo(Integer value) {
            addCriterion("reserve_quatity <>", value, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityNotEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("reserve_quatity <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveQuatityGreaterThan(Integer value) {
            addCriterion("reserve_quatity >", value, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityGreaterThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("reserve_quatity > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveQuatityGreaterThanOrEqualTo(Integer value) {
            addCriterion("reserve_quatity >=", value, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityGreaterThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("reserve_quatity >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveQuatityLessThan(Integer value) {
            addCriterion("reserve_quatity <", value, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityLessThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("reserve_quatity < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveQuatityLessThanOrEqualTo(Integer value) {
            addCriterion("reserve_quatity <=", value, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityLessThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("reserve_quatity <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveQuatityIn(List<Integer> values) {
            addCriterion("reserve_quatity in", values, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityNotIn(List<Integer> values) {
            addCriterion("reserve_quatity not in", values, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityBetween(Integer value1, Integer value2) {
            addCriterion("reserve_quatity between", value1, value2, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andReserveQuatityNotBetween(Integer value1, Integer value2) {
            addCriterion("reserve_quatity not between", value1, value2, "reserveQuatity");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryIsNull() {
            addCriterion("current_inventory is null");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryIsNotNull() {
            addCriterion("current_inventory is not null");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryEqualTo(Integer value) {
            addCriterion("current_inventory =", value, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("current_inventory = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryNotEqualTo(Integer value) {
            addCriterion("current_inventory <>", value, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryNotEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("current_inventory <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryGreaterThan(Integer value) {
            addCriterion("current_inventory >", value, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryGreaterThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("current_inventory > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryGreaterThanOrEqualTo(Integer value) {
            addCriterion("current_inventory >=", value, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryGreaterThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("current_inventory >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryLessThan(Integer value) {
            addCriterion("current_inventory <", value, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryLessThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("current_inventory < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryLessThanOrEqualTo(Integer value) {
            addCriterion("current_inventory <=", value, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryLessThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("current_inventory <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryIn(List<Integer> values) {
            addCriterion("current_inventory in", values, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryNotIn(List<Integer> values) {
            addCriterion("current_inventory not in", values, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryBetween(Integer value1, Integer value2) {
            addCriterion("current_inventory between", value1, value2, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andCurrentInventoryNotBetween(Integer value1, Integer value2) {
            addCriterion("current_inventory not between", value1, value2, "currentInventory");
            return (Criteria) this;
        }

        public Criteria andTotalInventoryIsNull() {
            addCriterion("total_inventory is null");
            return (Criteria) this;
        }

        public Criteria andTotalInventoryIsNotNull() {
            addCriterion("total_inventory is not null");
            return (Criteria) this;
        }

        public Criteria andTotalInventoryEqualTo(Integer value) {
            addCriterion("total_inventory =", value, "totalInventory");
            return (Criteria) this;
        }

        public Criteria andTotalInventoryEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("total_inventory = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalInventoryNotEqualTo(Integer value) {
            addCriterion("total_inventory <>", value, "totalInventory");
            return (Criteria) this;
        }

        public Criteria andTotalInventoryNotEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("total_inventory <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalInventoryGreaterThan(Integer value) {
            addCriterion("total_inventory >", value, "totalInventory");
            return (Criteria) this;
        }

        public Criteria andTotalInventoryGreaterThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("total_inventory > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalInventoryGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_inventory >=", value, "totalInventory");
            return (Criteria) this;
        }

        public Criteria andTotalInventoryGreaterThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("total_inventory >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalInventoryLessThan(Integer value) {
            addCriterion("total_inventory <", value, "totalInventory");
            return (Criteria) this;
        }

        public Criteria andTotalInventoryLessThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("total_inventory < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalInventoryLessThanOrEqualTo(Integer value) {
            addCriterion("total_inventory <=", value, "totalInventory");
            return (Criteria) this;
        }

        public Criteria andTotalInventoryLessThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("total_inventory <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalInventoryIn(List<Integer> values) {
            addCriterion("total_inventory in", values, "totalInventory");
            return (Criteria) this;
        }

        public Criteria andTotalInventoryNotIn(List<Integer> values) {
            addCriterion("total_inventory not in", values, "totalInventory");
            return (Criteria) this;
        }

        public Criteria andTotalInventoryBetween(Integer value1, Integer value2) {
            addCriterion("total_inventory between", value1, value2, "totalInventory");
            return (Criteria) this;
        }

        public Criteria andTotalInventoryNotBetween(Integer value1, Integer value2) {
            addCriterion("total_inventory not between", value1, value2, "totalInventory");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdIsNull() {
            addCriterion("inventory_threshold is null");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdIsNotNull() {
            addCriterion("inventory_threshold is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdEqualTo(Integer value) {
            addCriterion("inventory_threshold =", value, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("inventory_threshold = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdNotEqualTo(Integer value) {
            addCriterion("inventory_threshold <>", value, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdNotEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("inventory_threshold <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdGreaterThan(Integer value) {
            addCriterion("inventory_threshold >", value, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdGreaterThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("inventory_threshold > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdGreaterThanOrEqualTo(Integer value) {
            addCriterion("inventory_threshold >=", value, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdGreaterThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("inventory_threshold >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdLessThan(Integer value) {
            addCriterion("inventory_threshold <", value, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdLessThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("inventory_threshold < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdLessThanOrEqualTo(Integer value) {
            addCriterion("inventory_threshold <=", value, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdLessThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("inventory_threshold <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdIn(List<Integer> values) {
            addCriterion("inventory_threshold in", values, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdNotIn(List<Integer> values) {
            addCriterion("inventory_threshold not in", values, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdBetween(Integer value1, Integer value2) {
            addCriterion("inventory_threshold between", value1, value2, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdNotBetween(Integer value1, Integer value2) {
            addCriterion("inventory_threshold not between", value1, value2, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andIsNoticeIsNull() {
            addCriterion("is_notice is null");
            return (Criteria) this;
        }

        public Criteria andIsNoticeIsNotNull() {
            addCriterion("is_notice is not null");
            return (Criteria) this;
        }

        public Criteria andIsNoticeEqualTo(Boolean value) {
            addCriterion("is_notice =", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("is_notice = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsNoticeNotEqualTo(Boolean value) {
            addCriterion("is_notice <>", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeNotEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("is_notice <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsNoticeGreaterThan(Boolean value) {
            addCriterion("is_notice >", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeGreaterThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("is_notice > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsNoticeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_notice >=", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeGreaterThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("is_notice >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsNoticeLessThan(Boolean value) {
            addCriterion("is_notice <", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeLessThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("is_notice < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsNoticeLessThanOrEqualTo(Boolean value) {
            addCriterion("is_notice <=", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeLessThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("is_notice <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsNoticeIn(List<Boolean> values) {
            addCriterion("is_notice in", values, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeNotIn(List<Boolean> values) {
            addCriterion("is_notice not in", values, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeBetween(Boolean value1, Boolean value2) {
            addCriterion("is_notice between", value1, value2, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_notice not between", value1, value2, "isNotice");
            return (Criteria) this;
        }

        public Criteria andInventoryStatusIsNull() {
            addCriterion("inventory_status is null");
            return (Criteria) this;
        }

        public Criteria andInventoryStatusIsNotNull() {
            addCriterion("inventory_status is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryStatusEqualTo(String value) {
            addCriterion("inventory_status =", value, "inventoryStatus");
            return (Criteria) this;
        }

        public Criteria andInventoryStatusEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("inventory_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryStatusNotEqualTo(String value) {
            addCriterion("inventory_status <>", value, "inventoryStatus");
            return (Criteria) this;
        }

        public Criteria andInventoryStatusNotEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("inventory_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryStatusGreaterThan(String value) {
            addCriterion("inventory_status >", value, "inventoryStatus");
            return (Criteria) this;
        }

        public Criteria andInventoryStatusGreaterThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("inventory_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryStatusGreaterThanOrEqualTo(String value) {
            addCriterion("inventory_status >=", value, "inventoryStatus");
            return (Criteria) this;
        }

        public Criteria andInventoryStatusGreaterThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("inventory_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryStatusLessThan(String value) {
            addCriterion("inventory_status <", value, "inventoryStatus");
            return (Criteria) this;
        }

        public Criteria andInventoryStatusLessThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("inventory_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryStatusLessThanOrEqualTo(String value) {
            addCriterion("inventory_status <=", value, "inventoryStatus");
            return (Criteria) this;
        }

        public Criteria andInventoryStatusLessThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("inventory_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryStatusLike(String value) {
            addCriterion("inventory_status like", value, "inventoryStatus");
            return (Criteria) this;
        }

        public Criteria andInventoryStatusNotLike(String value) {
            addCriterion("inventory_status not like", value, "inventoryStatus");
            return (Criteria) this;
        }

        public Criteria andInventoryStatusIn(List<String> values) {
            addCriterion("inventory_status in", values, "inventoryStatus");
            return (Criteria) this;
        }

        public Criteria andInventoryStatusNotIn(List<String> values) {
            addCriterion("inventory_status not in", values, "inventoryStatus");
            return (Criteria) this;
        }

        public Criteria andInventoryStatusBetween(String value1, String value2) {
            addCriterion("inventory_status between", value1, value2, "inventoryStatus");
            return (Criteria) this;
        }

        public Criteria andInventoryStatusNotBetween(String value1, String value2) {
            addCriterion("inventory_status not between", value1, value2, "inventoryStatus");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(CardInventoryMainInfo.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andCustCodeLikeInsensitive(String value) {
            addCriterion("upper(cust_code) like", value.toUpperCase(), "custCode");
            return (Criteria) this;
        }

        public Criteria andCustNameLikeInsensitive(String value) {
            addCriterion("upper(cust_name) like", value.toUpperCase(), "custName");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLikeInsensitive(String value) {
            addCriterion("upper(template_id) like", value.toUpperCase(), "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateNameLikeInsensitive(String value) {
            addCriterion("upper(template_name) like", value.toUpperCase(), "templateName");
            return (Criteria) this;
        }

        public Criteria andBeIdLikeInsensitive(String value) {
            addCriterion("upper(be_id) like", value.toUpperCase(), "beId");
            return (Criteria) this;
        }

        public Criteria andProviceNameLikeInsensitive(String value) {
            addCriterion("upper(provice_name) like", value.toUpperCase(), "proviceName");
            return (Criteria) this;
        }

        public Criteria andRegionIdLikeInsensitive(String value) {
            addCriterion("upper(region_id) like", value.toUpperCase(), "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionNameLikeInsensitive(String value) {
            addCriterion("upper(region_name) like", value.toUpperCase(), "regionName");
            return (Criteria) this;
        }

        public Criteria andCardTypeLikeInsensitive(String value) {
            addCriterion("upper(card_type) like", value.toUpperCase(), "cardType");
            return (Criteria) this;
        }

        public Criteria andInventoryStatusLikeInsensitive(String value) {
            addCriterion("upper(inventory_status) like", value.toUpperCase(), "inventoryStatus");
            return (Criteria) this;
        }
    }

    /**
     * Corresponding to the database table supply_chain..card_inventory_main_info
     *
     * @mbg.generated do_not_delete_during_merge Tue Dec 24 09:55:53 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         * Corresponding to the database table supply_chain..card_inventory_main_info
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        private CardInventoryMainInfoExample example;

        /**
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        protected Criteria(CardInventoryMainInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        public CardInventoryMainInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Tue Dec 24 09:55:53 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     * Corresponding to the database table supply_chain..card_inventory_main_info
     *
     * @mbg.generated Tue Dec 24 09:55:53 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Tue Dec 24 09:55:53 CST 2024
         */
        void example(com.chinamobile.iot.sc.pojo.entity.CardInventoryMainInfoExample example);
    }
}