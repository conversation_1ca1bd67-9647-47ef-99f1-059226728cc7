package com.chinamobile.iot.sc.controller.softService;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.pojo.param.SoftServiceOpenParam;
import com.chinamobile.iot.sc.request.sync.SyncCommonRequest;
import com.chinamobile.iot.sc.service.SoftService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/5/30 14:27
 */
@RestController
@RequestMapping("/osweb/softservice")
public class SoftServiceOrderController {
    @Resource
    private SoftService softService;
    /**
     * 软件服务开通、退订回调
     *
     */
    @PostMapping("/feedback")
    public BaseAnswer<Void> feedback(
            @RequestBody SyncCommonRequest syncCommonRequest) {
        return softService.feedback(syncCommonRequest);
    }
    /**
     * 软件服务开通、退订回调 新
     *
     */
    @PostMapping("/feedbackNew")
    public BaseAnswer<Void> feedbackNew(
            @RequestBody SyncCommonRequest syncCommonRequest) {
        return softService.feedbackNew(syncCommonRequest);
    }
    /**
     * 软件服务开通
     *
     */
    @PostMapping("/softServiceOpen")
    public BaseAnswer<Void> softServiceOpen(@RequestBody SoftServiceOpenParam softServiceOpenParam) {

        return softService.softServiceOpen(softServiceOpenParam);
    }
    /**
     * 软件服务开通
     *
     */
    @GetMapping("/softServiceOpenTest")
    public BaseAnswer<Void> softServiceOpenTest(@RequestParam String platform, @RequestParam String operateType,@RequestParam String phone,@RequestParam String orderId,@RequestParam String code) {

        return softService.softServiceOpenTest(platform,operateType,phone,orderId,code);
    }
    /**
     * 软件服务重新同步到商城
     *
     */
    @GetMapping("/softServiceSyncIot")
    public BaseAnswer<Void> softServiceSyncIot(@RequestParam String orderId) {

        return softService.softServiceSyncIot(orderId);
    }
    /**
     * 软件服务退订
     *
     */
    @PostMapping("/softServiceRetail")
    public BaseAnswer<Void> softServiceRetail(@RequestBody SoftServiceOpenParam softServiceOpenParam) {

        return softService.softServiceOpen(softServiceOpenParam);
    }
    /**
     * 向商城申请开通失败
     *
     */
    @PostMapping("/softServiceOpenFail")
    public BaseAnswer<Void> softServiceOpenFail(@RequestBody SoftServiceOpenParam softServiceOpenParam) {

        return softService.softServiceOpenFail(softServiceOpenParam);
    }
    @PostMapping("/softServiceImport")
    public BaseAnswer<Void> softServiceImport( @RequestParam("file") MultipartFile file) {

        return softService.softServiceImport(file);
    }
//    /**
//     * 同步开通结果，以及使用中指令到商城
//     *
//     */
//    @PostMapping("/syncSoftResultToIot")
//    public BaseAnswer<Void> syncSoftResultToIot(@RequestBody SoftServiceOpenParam softServiceOpenParam) {
//
//        return softService.syncSoftResultToIot(softServiceOpenParam);
//    }
}
