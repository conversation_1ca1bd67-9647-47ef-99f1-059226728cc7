package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class SaveAnnouncementParam {

    /**
     * 编辑时传递id,新增时不传
     */
    private String id;

    /**
     * 标题
     */
    @NotEmpty(message = "标题不能为空")
    private String title;


    /**
     * 是否弹窗提示
     */
    @NotNull(message = "是否弹窗不能为空")
    private Boolean popup;

    /**
     * 提示截止时间
     */
    private Date endTime;

    /**
     * 内容
     */
    @NotEmpty(message = "内容不能为空")
    private String content;

}
