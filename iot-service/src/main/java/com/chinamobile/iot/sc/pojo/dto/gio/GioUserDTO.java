package com.chinamobile.iot.sc.pojo.dto.gio;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class GioUserDTO {
    private String userId;
    private UserAttr attrs;
    @Data
    public static class UserAttr{
        private String wlw_UserID;
        private String wlw_ustCode;
        private String wlw_custID;
        private String wlw_cusName;
        private String wlw_cusRegistDate;
        private String wlw_roleType;
        private String wlw_roleLevel;
        private String wlw_UserStauts;
        private String wlw_cusProvince;
        private String wlw_cusCity;
        private String wlw_cusRegion;
        private String wlw_cusProvinceCode;
        private String wlw_cusCityCode;
        private String wlw_cusRegionCode;
        private String wlw_GridCity;
        private String wlw_GridRegion;
        private String wlw_GridName;
        private String distributorMrglnf;
        private String distributorMrgCode;
        private String distributorReferralcode;
    }


}
