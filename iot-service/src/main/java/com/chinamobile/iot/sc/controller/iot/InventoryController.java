package com.chinamobile.iot.sc.controller.iot;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.dto.LimitSyncContent;
import com.chinamobile.iot.sc.mode.InventoryInfoRequest;
import com.chinamobile.iot.sc.mode.InventoryInfoResponse;
import com.chinamobile.iot.sc.response.iot.ReserveInventoryResponse;
import com.chinamobile.iot.sc.service.IInventoryService;
import com.chinamobile.iot.sc.service.OsMallSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Author: YSC
 * @Date: 2021/11/3 16:52
 * @Description: 库存管理
 */
@RestController
@RequestMapping("/os/inventoryservice")
@Slf4j
public class InventoryController {
    @Autowired
    private IInventoryService iInventoryService;

    @Resource
    private OsMallSyncService osMallSyncService;

    /**
     * 库存预占
     *
     * @return
     */
    @PostMapping("/reserveInventory")
    public IOTAnswer<ReserveInventoryResponse> reserveInventory(@RequestBody IOTRequest baseRequest) {
        return iInventoryService.reserveInventory(baseRequest);
    }

    /**
     * 库存释放
     *
     * @param baseRequest
     * @return
     */
    @PostMapping("/releaseInventory")
    public IOTAnswer<Void> releaseInventory(@RequestBody IOTRequest baseRequest) {
        return iInventoryService.releaseInventory(baseRequest);
    }

    /**
     * 库存查询
     *
     * @param baseRequest
     * @return
     */
    @PostMapping("/qryInventory")
    public IOTAnswer qryInventory(@RequestBody IOTRequest baseRequest) {
        return iInventoryService.qryInventory(baseRequest);
    }

    /**
     * 小程序库存查询
     * @param baseRequest
     * @return
     */
    @PostMapping("/qryInventoryFromMini")
    public IOTAnswer qryInventoryFromMini(@RequestBody IOTRequest baseRequest) {
        log.info("小程序库存查询请求内容:{}", JSON.toJSONString(baseRequest));
        IOTAnswer<InventoryInfoResponse> iotAnswer = new IOTAnswer<>();
        iotAnswer.setMessageSeq(baseRequest.getMessageSeq());
        InventoryInfoRequest inventoryInfoRequest = JSON.parseObject(baseRequest.getContent(),
                InventoryInfoRequest.class);
        return iInventoryService.getInventoryInfoResponse(baseRequest, inventoryInfoRequest, iotAnswer, true);
    }

    /**
     * 3.3.4号卡信息同步
     *
     * @param baseRequest
     * @return
     */
    @RequestMapping("/SyncNumberCardInfos")
    public IOTAnswer<Void> syncNumberCardInfos(@RequestBody IOTRequest baseRequest) {
        return osMallSyncService.syncNumberCardInfos(baseRequest);
    }

    /**
     * 额度同步
     *
     * @param baseRequest
     * @return
     */
    @PostMapping("/LimitSynchronizationInfo")
    public IOTAnswer<Void> limitSync(@RequestBody IOTRequest baseRequest) {

        return iInventoryService.LimitSynchronizationInfo(baseRequest);
    }

    /**
     * 额度查询
     *
     * @param baseRequest
     * @return
     */
    @PostMapping("/UsedLimitInquiry")
    public IOTAnswer<LimitSyncContent> UsedLimitInquiry(@RequestBody IOTRequest baseRequest) {

        return iInventoryService.UsedLimitInquiry(baseRequest);
    }

}
