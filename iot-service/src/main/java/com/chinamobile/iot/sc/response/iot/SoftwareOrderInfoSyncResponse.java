package com.chinamobile.iot.sc.response.iot;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true, fluent = false )
public class SoftwareOrderInfoSyncResponse {

        @J<PERSON>NField(name = "ROOT")
        private ROOT ROOT;

        @Data
        @Accessors(chain = true, fluent = false )
        public static class ROOT {
                @J<PERSON><PERSON><PERSON>(name = "BODY")
                private BODY BODY;
                @J<PERSON><PERSON>ield(name = "HEADER")
                private HEADER HEADER;
        }
        @Data
        @Accessors(chain = true, fluent = false )
        public static class HEADER {
        }

        @Data
        @Accessors(chain = true, fluent = false )
        public static class BODY {
                @J<PERSON>NField(name = "RETURN_CODE")
                private String RETURN_CODE;
                @J<PERSON><PERSON>ield(name = "RETURN_MSG")
                private String RETURN_MSG;
                @<PERSON><PERSON><PERSON><PERSON>(name = "DE<PERSON>IL_MSG")
                private String DETAIL_MSG;
        }


}
