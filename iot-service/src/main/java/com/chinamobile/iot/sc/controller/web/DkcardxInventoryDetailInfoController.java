package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryDetailInfo;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryDetailInfoExample;
import com.chinamobile.iot.sc.pojo.param.DkcardxInventoryCardDetailParam;
import com.chinamobile.iot.sc.pojo.dto.DkcardxInventoryCardDetailInfoDTO;
import com.chinamobile.iot.sc.pojo.param.DkcardxInventoryDetailInfoParam;
import com.chinamobile.iot.sc.service.DkcardxInventoryDetailInfoService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/18
 * @description 卡+X终端库存信息详情controller类
 */
@RestController
@RequestMapping(value = "/osweb/cardinventorydetailinfo")
public class DkcardxInventoryDetailInfoController {

    @Resource
    private DkcardxInventoryDetailInfoService dkcardxInventoryDetailInfoService;

    /**
     * 根据卡+X主要信息id获取卡信息详情列表
     * @param cardDetailParam
     * @return
     */
    @GetMapping(value = "/listDetailCardInfo")
    public BaseAnswer<PageData<DkcardxInventoryCardDetailInfoDTO>> listDetailInfo(DkcardxInventoryCardDetailParam cardDetailParam,
                                                                                  @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer<PageData<DkcardxInventoryCardDetailInfoDTO>> baseAnswer = new BaseAnswer();
        PageData<DkcardxInventoryCardDetailInfoDTO> pageData
                = dkcardxInventoryDetailInfoService.listDkcardxInventoryCardDetailInfo(cardDetailParam,loginIfo4Redis);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }


    /**
     * 根据卡+X主要信息id获取详情列表
     * @param dkcardxInventoryDetailInfoParam
     * @return
     */
    @GetMapping(value = "/listDetailInfo")
    public BaseAnswer<DkcardxInventoryDetailInfo> listDetailInfo(@Valid DkcardxInventoryDetailInfoParam dkcardxInventoryDetailInfoParam,
                                                                 @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer baseAnswer = new BaseAnswer();
        List<DkcardxInventoryDetailInfo> detailInfoList
                = dkcardxInventoryDetailInfoService.listDkcardxInventoryDetailInfo(dkcardxInventoryDetailInfoParam,loginIfo4Redis);
        baseAnswer.setData(detailInfoList);
        return baseAnswer;
    }


    @PostMapping("/inventory/atom/magic/deldkdetail")
    public BaseAnswer<Void> delDkcardInventoryDetail(@RequestParam("id")String id){
        dkcardxInventoryDetailInfoService.delDkcardInventoryDetail(id);
        return new BaseAnswer<>();
    }

    /**
     * 处理终端库存数据
     * @param detailId
     * @param inventoryAtomId
     * @param reserveQuatity
     * @param currentInventory
     * @param totalInventory
     * @param atomInventory
     * @return
     */
    @PostMapping("/inventory/detail/inventory/handle")
    public BaseAnswer<Void> handleDkcardxInventoryDetailInfoMessage(@RequestParam("detailId")String detailId,
                                                                    @RequestParam("inventoryAtomId")String inventoryAtomId,
                                                                    @RequestParam(value = "reserveQuatity",required = false)Integer reserveQuatity,
                                                                    @RequestParam(value = "currentInventory",required = false)Integer currentInventory,
                                                                    @RequestParam(value = "totalInventory",required = false)Integer totalInventory,
                                                                     @RequestParam("atomInventory")Long atomInventory){
        dkcardxInventoryDetailInfoService.handleDkcardxInventoryDetailInfo(detailId,inventoryAtomId,reserveQuatity,currentInventory,totalInventory,atomInventory);
        return new BaseAnswer<>();
    }

    /**
     * 处理历史导入的卡+X库存详情到原子商品上面
     * @return
     */
    @PostMapping(value = "/handleKxImportHistoryToInventoryAtom")
    public BaseAnswer handleKxImportHistoryToInventoryAtom(){
        dkcardxInventoryDetailInfoService.handleKxImportHistoryToInventoryAtom();
        return new BaseAnswer();
    }
}
