package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.service.IImportService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * @package: com.chinamobile.iot.sc.controller.web
 * @ClassName: ImportController
 * @description: IOT数据导入Controller
 * @author: zyj
 * @create: 2022/3/2 16:01
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@RequestMapping("/osweb/import")
@RestController
public class ImportController {
    @Resource
    private IImportService importService;

    @PostMapping("/iot/data")
    public BaseAnswer<Map<String, String>> importIOTData(@RequestPart(value = "files", required = false) MultipartFile[] files
            , @RequestParam String type
            , @RequestParam String sign) {
        return importService.importIOTData(files, type, sign);
    }

    /**
     * 使用上传的Excel或者默认的excel，将地市和区县信息存入redis中
     * @param file
     * @return
     */
    @PostMapping("/regionAndLocation")
    public BaseAnswer importRegionAndLocation(@RequestPart(value = "file", required = false) MultipartFile file){
        return importService.importRegionAndLocation(file);
    }

    /**
     * 1.7版本需求使用，订单数据导入前（“/iot/data”），需要用本接口将已经删除掉的产品（spu,sku,atom）数据再次导入表中，避免订单导入失败。
     * 使用文件名称来区分三种商品数据
     */
    @PostMapping("/product")
    public BaseAnswer importProductData(@RequestPart MultipartFile file,
                                        @RequestParam String sign){

        return importService.importProductData(file,sign);
    }

    /**
     * 通过Excel导入，修改商品状态（Spu、Sku）
     * @param file
     * @return
     */
    @PostMapping("/productStatus")
    public BaseAnswer importProductStatus(@RequestPart(value = "file", required = false) MultipartFile file){
        return importService.importProductStatus(file);
    }

    /**
     * 导入终端imei相关信息
     * @param file
     * @return
     */
    @PostMapping("/cardRelation")
    @Auth(authCode = {BaseConstant.IMPORT_EXPORT_CARD})
    public void importCardRelation(MultipartFile file,
                                         HttpServletRequest request,
                                         HttpServletResponse response) throws Exception {
        importService.importCardRelation(file,request,response);
    }

    /**
     * 割接代客下单的客户经理信息
     */
    @PostMapping("/updateOrderOrgBizInfo")
    public BaseAnswer updateOrderOrgBizInfo(MultipartFile file){
        return importService.updateOrderOrgBizInfo(file);
    }

}
