package com.chinamobile.iot.sc.response.web.invoice;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/9 17:52
 */
@Data
@Accessors(chain = true)
public class Data4UnRedFlush {

    /**
     * 开票订单号
     */
    @Excel(name = "开票订单号")
    private String id;

    /**
     * 订单号
     */
    @Excel(name = "订单号")
    private String orderId;

    /**
     * 申请时间
     */
    @Excel(name = "申请时间")
    private Date createTime;


    /**
     * 订单金额
     */
    @Excel(name = "订单金额")
    private Long orderPrice;

    /**
     * 合作伙伴
     */
    @Excel(name = "合作伙伴")
    private String partnerName;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    private String cooperatorName;

    /**
     * 开票状态
     */
    @Excel(name = "开票状态")
    private Integer status;

    /**
     * 发票抬头 0:个人；1：团体，前端：不传默认团体
     */
    @Excel(name = "发票抬头")
    private String customerType;
}
