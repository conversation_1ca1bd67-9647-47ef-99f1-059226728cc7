package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.TradeOrderInfo;
import com.chinamobile.iot.sc.pojo.param.TradeOrderInfoParam;
import com.chinamobile.iot.sc.pojo.vo.TradeOrderInfoVO;
import com.chinamobile.iot.sc.response.SimpleItemDTO;
import com.chinamobile.iot.sc.service.TradeOrderInfoService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/13
 * @description 贸易订单信息controller接口类
 */
@RestController
@RequestMapping(value = "/osweb/tradeOrder")
public class TradeOrderInfoController {

    @Resource
    private TradeOrderInfoService tradeOrderInfoService;

    /**
     * 根据合同获取贸易订单信息
     * @param contractNum
     * @return
     */
    @GetMapping(value = "/tradeOrderByContract")
    public BaseAnswer<List<TradeOrderInfo>> listTradeOrderInfoByContract(@RequestParam(value = "contractNum")String contractNum){
        BaseAnswer baseAnswer = new BaseAnswer();
        List<TradeOrderInfo> tradeOrderInfoList = tradeOrderInfoService.listTradeOrderInfoByContract(contractNum);
        baseAnswer.setData(tradeOrderInfoList);
        return baseAnswer;
    }

    /**
     * 分页获取贸易订单信息
     * @param tradeOrderInfoParam
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping(value="/pageTradeOrderInfo")
    public BaseAnswer<PageData<TradeOrderInfoVO>> pageTradeOrderInfo(TradeOrderInfoParam tradeOrderInfoParam,
                                                                 @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {

        BaseAnswer baseAnswer = new BaseAnswer();
        PageData<TradeOrderInfoVO> pageData = tradeOrderInfoService.pageTradeOrderInfo(tradeOrderInfoParam,loginIfo4Redis);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }

    /**
     * 导出贸易订单明细
     * @param tradeNo
     * @throws Exception
     */
    @GetMapping(value="/exportTradeOrderDetail")
    public void exportTradeOrderDetail(@RequestParam(value = "tradeNo")String tradeNo, @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) throws Exception {
        tradeOrderInfoService.exportTradeOrderDetail(tradeNo,loginIfo4Redis);
    }

    /**
     * 导出财务台账详情
     * @param tradeNo
     * @throws Exception
     */
    @GetMapping(value="/exportFinancingBillDetail")
    public void exportFinancingBillDetail(@RequestParam(value = "tradeNo")String tradeNo, @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) throws Exception {
        tradeOrderInfoService.exportFinancingBillDetail(tradeNo,loginIfo4Redis);
    }

    /**
     * 获取保理状态下拉值
     * @return
     */
    @GetMapping(value = "/getBaoliStatusList")
    public BaseAnswer<List<SimpleItemDTO>> getBaoliStatusList(){
        return tradeOrderInfoService.getBaoliStatusList();
    }
}
