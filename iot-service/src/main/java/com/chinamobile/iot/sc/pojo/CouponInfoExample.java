package com.chinamobile.iot.sc.pojo;

import java.util.ArrayList;
import java.util.List;

public class CouponInfoExample {
    /**
     * Corresponding to the database table supply_chain..coupon_info
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    protected String orderByClause;

    /**
     * Corresponding to the database table supply_chain..coupon_info
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    protected boolean distinct;

    /**
     * Corresponding to the database table supply_chain..coupon_info
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    public CouponInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    public CouponInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    public CouponInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        CouponInfoExample example = new CouponInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    public CouponInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    public CouponInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     * Corresponding to the database table supply_chain..coupon_info
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCouponCodeIsNull() {
            addCriterion("coupon_code is null");
            return (Criteria) this;
        }

        public Criteria andCouponCodeIsNotNull() {
            addCriterion("coupon_code is not null");
            return (Criteria) this;
        }

        public Criteria andCouponCodeEqualTo(String value) {
            addCriterion("coupon_code =", value, "couponCode");
            return (Criteria) this;
        }

        public Criteria andCouponCodeEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("coupon_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponCodeNotEqualTo(String value) {
            addCriterion("coupon_code <>", value, "couponCode");
            return (Criteria) this;
        }

        public Criteria andCouponCodeNotEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("coupon_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponCodeGreaterThan(String value) {
            addCriterion("coupon_code >", value, "couponCode");
            return (Criteria) this;
        }

        public Criteria andCouponCodeGreaterThanColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("coupon_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponCodeGreaterThanOrEqualTo(String value) {
            addCriterion("coupon_code >=", value, "couponCode");
            return (Criteria) this;
        }

        public Criteria andCouponCodeGreaterThanOrEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("coupon_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponCodeLessThan(String value) {
            addCriterion("coupon_code <", value, "couponCode");
            return (Criteria) this;
        }

        public Criteria andCouponCodeLessThanColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("coupon_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponCodeLessThanOrEqualTo(String value) {
            addCriterion("coupon_code <=", value, "couponCode");
            return (Criteria) this;
        }

        public Criteria andCouponCodeLessThanOrEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("coupon_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponCodeLike(String value) {
            addCriterion("coupon_code like", value, "couponCode");
            return (Criteria) this;
        }

        public Criteria andCouponCodeNotLike(String value) {
            addCriterion("coupon_code not like", value, "couponCode");
            return (Criteria) this;
        }

        public Criteria andCouponCodeIn(List<String> values) {
            addCriterion("coupon_code in", values, "couponCode");
            return (Criteria) this;
        }

        public Criteria andCouponCodeNotIn(List<String> values) {
            addCriterion("coupon_code not in", values, "couponCode");
            return (Criteria) this;
        }

        public Criteria andCouponCodeBetween(String value1, String value2) {
            addCriterion("coupon_code between", value1, value2, "couponCode");
            return (Criteria) this;
        }

        public Criteria andCouponCodeNotBetween(String value1, String value2) {
            addCriterion("coupon_code not between", value1, value2, "couponCode");
            return (Criteria) this;
        }

        public Criteria andCouponAmountIsNull() {
            addCriterion("coupon_amount is null");
            return (Criteria) this;
        }

        public Criteria andCouponAmountIsNotNull() {
            addCriterion("coupon_amount is not null");
            return (Criteria) this;
        }

        public Criteria andCouponAmountEqualTo(String value) {
            addCriterion("coupon_amount =", value, "couponAmount");
            return (Criteria) this;
        }

        public Criteria andCouponAmountEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("coupon_amount = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponAmountNotEqualTo(String value) {
            addCriterion("coupon_amount <>", value, "couponAmount");
            return (Criteria) this;
        }

        public Criteria andCouponAmountNotEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("coupon_amount <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponAmountGreaterThan(String value) {
            addCriterion("coupon_amount >", value, "couponAmount");
            return (Criteria) this;
        }

        public Criteria andCouponAmountGreaterThanColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("coupon_amount > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponAmountGreaterThanOrEqualTo(String value) {
            addCriterion("coupon_amount >=", value, "couponAmount");
            return (Criteria) this;
        }

        public Criteria andCouponAmountGreaterThanOrEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("coupon_amount >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponAmountLessThan(String value) {
            addCriterion("coupon_amount <", value, "couponAmount");
            return (Criteria) this;
        }

        public Criteria andCouponAmountLessThanColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("coupon_amount < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponAmountLessThanOrEqualTo(String value) {
            addCriterion("coupon_amount <=", value, "couponAmount");
            return (Criteria) this;
        }

        public Criteria andCouponAmountLessThanOrEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("coupon_amount <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCouponAmountLike(String value) {
            addCriterion("coupon_amount like", value, "couponAmount");
            return (Criteria) this;
        }

        public Criteria andCouponAmountNotLike(String value) {
            addCriterion("coupon_amount not like", value, "couponAmount");
            return (Criteria) this;
        }

        public Criteria andCouponAmountIn(List<String> values) {
            addCriterion("coupon_amount in", values, "couponAmount");
            return (Criteria) this;
        }

        public Criteria andCouponAmountNotIn(List<String> values) {
            addCriterion("coupon_amount not in", values, "couponAmount");
            return (Criteria) this;
        }

        public Criteria andCouponAmountBetween(String value1, String value2) {
            addCriterion("coupon_amount between", value1, value2, "couponAmount");
            return (Criteria) this;
        }

        public Criteria andCouponAmountNotBetween(String value1, String value2) {
            addCriterion("coupon_amount not between", value1, value2, "couponAmount");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeIsNull() {
            addCriterion("salesman_code is null");
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeIsNotNull() {
            addCriterion("salesman_code is not null");
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeEqualTo(String value) {
            addCriterion("salesman_code =", value, "salesmanCode");
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("salesman_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeNotEqualTo(String value) {
            addCriterion("salesman_code <>", value, "salesmanCode");
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeNotEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("salesman_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeGreaterThan(String value) {
            addCriterion("salesman_code >", value, "salesmanCode");
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeGreaterThanColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("salesman_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeGreaterThanOrEqualTo(String value) {
            addCriterion("salesman_code >=", value, "salesmanCode");
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeGreaterThanOrEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("salesman_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeLessThan(String value) {
            addCriterion("salesman_code <", value, "salesmanCode");
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeLessThanColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("salesman_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeLessThanOrEqualTo(String value) {
            addCriterion("salesman_code <=", value, "salesmanCode");
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeLessThanOrEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("salesman_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeLike(String value) {
            addCriterion("salesman_code like", value, "salesmanCode");
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeNotLike(String value) {
            addCriterion("salesman_code not like", value, "salesmanCode");
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeIn(List<String> values) {
            addCriterion("salesman_code in", values, "salesmanCode");
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeNotIn(List<String> values) {
            addCriterion("salesman_code not in", values, "salesmanCode");
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeBetween(String value1, String value2) {
            addCriterion("salesman_code between", value1, value2, "salesmanCode");
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeNotBetween(String value1, String value2) {
            addCriterion("salesman_code not between", value1, value2, "salesmanCode");
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneIsNull() {
            addCriterion("salesman_phone is null");
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneIsNotNull() {
            addCriterion("salesman_phone is not null");
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneEqualTo(String value) {
            addCriterion("salesman_phone =", value, "salesmanPhone");
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("salesman_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneNotEqualTo(String value) {
            addCriterion("salesman_phone <>", value, "salesmanPhone");
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneNotEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("salesman_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneGreaterThan(String value) {
            addCriterion("salesman_phone >", value, "salesmanPhone");
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneGreaterThanColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("salesman_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("salesman_phone >=", value, "salesmanPhone");
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneGreaterThanOrEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("salesman_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneLessThan(String value) {
            addCriterion("salesman_phone <", value, "salesmanPhone");
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneLessThanColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("salesman_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneLessThanOrEqualTo(String value) {
            addCriterion("salesman_phone <=", value, "salesmanPhone");
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneLessThanOrEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("salesman_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneLike(String value) {
            addCriterion("salesman_phone like", value, "salesmanPhone");
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneNotLike(String value) {
            addCriterion("salesman_phone not like", value, "salesmanPhone");
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneIn(List<String> values) {
            addCriterion("salesman_phone in", values, "salesmanPhone");
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneNotIn(List<String> values) {
            addCriterion("salesman_phone not in", values, "salesmanPhone");
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneBetween(String value1, String value2) {
            addCriterion("salesman_phone between", value1, value2, "salesmanPhone");
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneNotBetween(String value1, String value2) {
            addCriterion("salesman_phone not between", value1, value2, "salesmanPhone");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdIsNull() {
            addCriterion("employee_id is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdIsNotNull() {
            addCriterion("employee_id is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdEqualTo(String value) {
            addCriterion("employee_id =", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("employee_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotEqualTo(String value) {
            addCriterion("employee_id <>", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("employee_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeIdGreaterThan(String value) {
            addCriterion("employee_id >", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdGreaterThanColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("employee_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeIdGreaterThanOrEqualTo(String value) {
            addCriterion("employee_id >=", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdGreaterThanOrEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("employee_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeIdLessThan(String value) {
            addCriterion("employee_id <", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdLessThanColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("employee_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeIdLessThanOrEqualTo(String value) {
            addCriterion("employee_id <=", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdLessThanOrEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("employee_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeIdLike(String value) {
            addCriterion("employee_id like", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotLike(String value) {
            addCriterion("employee_id not like", value, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdIn(List<String> values) {
            addCriterion("employee_id in", values, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotIn(List<String> values) {
            addCriterion("employee_id not in", values, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdBetween(String value1, String value2) {
            addCriterion("employee_id between", value1, value2, "employeeId");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdNotBetween(String value1, String value2) {
            addCriterion("employee_id not between", value1, value2, "employeeId");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameIsNull() {
            addCriterion("salesman_name is null");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameIsNotNull() {
            addCriterion("salesman_name is not null");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameEqualTo(String value) {
            addCriterion("salesman_name =", value, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("salesman_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanNameNotEqualTo(String value) {
            addCriterion("salesman_name <>", value, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameNotEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("salesman_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanNameGreaterThan(String value) {
            addCriterion("salesman_name >", value, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameGreaterThanColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("salesman_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanNameGreaterThanOrEqualTo(String value) {
            addCriterion("salesman_name >=", value, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameGreaterThanOrEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("salesman_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanNameLessThan(String value) {
            addCriterion("salesman_name <", value, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameLessThanColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("salesman_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanNameLessThanOrEqualTo(String value) {
            addCriterion("salesman_name <=", value, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameLessThanOrEqualToColumn(CouponInfo.Column column) {
            addCriterion(new StringBuilder("salesman_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalesmanNameLike(String value) {
            addCriterion("salesman_name like", value, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameNotLike(String value) {
            addCriterion("salesman_name not like", value, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameIn(List<String> values) {
            addCriterion("salesman_name in", values, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameNotIn(List<String> values) {
            addCriterion("salesman_name not in", values, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameBetween(String value1, String value2) {
            addCriterion("salesman_name between", value1, value2, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameNotBetween(String value1, String value2) {
            addCriterion("salesman_name not between", value1, value2, "salesmanName");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andCouponCodeLikeInsensitive(String value) {
            addCriterion("upper(coupon_code) like", value.toUpperCase(), "couponCode");
            return (Criteria) this;
        }

        public Criteria andCouponAmountLikeInsensitive(String value) {
            addCriterion("upper(coupon_amount) like", value.toUpperCase(), "couponAmount");
            return (Criteria) this;
        }

        public Criteria andOrderIdLikeInsensitive(String value) {
            addCriterion("upper(order_id) like", value.toUpperCase(), "orderId");
            return (Criteria) this;
        }

        public Criteria andSalesmanCodeLikeInsensitive(String value) {
            addCriterion("upper(salesman_code) like", value.toUpperCase(), "salesmanCode");
            return (Criteria) this;
        }

        public Criteria andSalesmanPhoneLikeInsensitive(String value) {
            addCriterion("upper(salesman_phone) like", value.toUpperCase(), "salesmanPhone");
            return (Criteria) this;
        }

        public Criteria andEmployeeIdLikeInsensitive(String value) {
            addCriterion("upper(employee_id) like", value.toUpperCase(), "employeeId");
            return (Criteria) this;
        }

        public Criteria andSalesmanNameLikeInsensitive(String value) {
            addCriterion("upper(salesman_name) like", value.toUpperCase(), "salesmanName");
            return (Criteria) this;
        }
    }

    /**
     * Corresponding to the database table supply_chain..coupon_info
     *
     * @mbg.generated do_not_delete_during_merge Fri May 31 15:49:28 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         * Corresponding to the database table supply_chain..coupon_info
         *
         * @mbg.generated Fri May 31 15:49:28 CST 2024
         */
        private CouponInfoExample example;

        /**
         *
         * @mbg.generated Fri May 31 15:49:28 CST 2024
         */
        protected Criteria(CouponInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Fri May 31 15:49:28 CST 2024
         */
        public CouponInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Fri May 31 15:49:28 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Fri May 31 15:49:28 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Fri May 31 15:49:28 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Fri May 31 15:49:28 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     * Corresponding to the database table supply_chain..coupon_info
     *
     * @mbg.generated Fri May 31 15:49:28 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Fri May 31 15:49:28 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Fri May 31 15:49:28 CST 2024
         */
        void example(com.chinamobile.iot.sc.pojo.CouponInfoExample example);
    }
}