package com.chinamobile.iot.sc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.constant.OrderStatusInnerEnum;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.entity.ByteArrayUpload;
import com.chinamobile.iot.sc.entity.UpResult;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.request.Order2CInfoRequest;
import com.chinamobile.iot.sc.request.ProductInfoRequest;
import com.chinamobile.iot.sc.request.order2c.*;
import com.chinamobile.iot.sc.request.product.AtomOfferingInfoDTO;
import com.chinamobile.iot.sc.request.product.ProductLabelInfoDTO;
import com.chinamobile.iot.sc.request.sku.SkuReleaseTargetDTO;
import com.chinamobile.iot.sc.service.IImportDBService;
import com.chinamobile.iot.sc.service.IStorageService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.SFTPUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @package: com.chinamobile.iot.sc.service.impl
 * @ClassName: ImportDBServiceimpl
 * @description: IOT商城数据导入数据库实现类
 * @author: zyj
 * @create: 2022/3/3 10:30
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Slf4j
@Service
public class ImportDBServiceImpl implements IImportDBService {
    @Resource
    private SpuOfferingInfoMapper spuOfferingInfoMapper;
    @Resource
    private CategoryInfoMapper categoryInfoMapper;
    @Resource
    private SkuOfferingInfoMapper skuOfferingInfoMapper;
    @Resource
    private AtomOfferingInfoMapper atomOfferingInfoMapper;
    @Resource
    private Order2cInfoMapper order2cInfoMapper;
    @Resource
    private Order2cAtomInfoMapper atomOrderInfoMapper;

    @Value("${iot.ftp.name}")
    private String sftpUserName;
    @Value("${iot.ftp.password}")
    private String sftpPassword;
    @Value("${iot.ftp.host}")
    private String sftpHost;
    @Value("${iot.ftp.port}")
    private Integer sftpPort;
    /**
     * SPU商品图片
     */
    @Value("${iot.ftp.spuImagePath}")
    private String sftpSpuImagePath;
    @Resource
    private IStorageService storageService;
    @Resource
    private SkuReleaseTargetMapper skuReleaseTargetMapper;

    /**
     * 解析、处理商品信息
     * @param file
     * @param resMap
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void analyseProductInfo(MultipartFile file, Map<String, String> resMap){
        BufferedReader br = null;
        String strLine = null;
        try {
            //构造一个BufferedReader类来读取文件
            br = new BufferedReader(new InputStreamReader(file.getInputStream(), "UTF-8"));
            //使用readLine方法，一次读一行
            while((strLine = br.readLine())!=null){
                try{
                    // 解析商品信息数据并处理
                    IOTRequest iotRequest = JSON.parseObject(strLine, IOTRequest.class);
                    ProductInfoRequest productInfoRequest = JSON.parseObject(iotRequest.getContent(), ProductInfoRequest.class);

                    com.chinamobile.iot.sc.request.product.SpuOfferingInfoDTO spuOfferingInfoDTO = productInfoRequest.getSpuOfferingInfo();
                    List<SpuOfferingInfo> spuOfferingInfos = spuOfferingInfoMapper.selectByExample(
                        new SpuOfferingInfoExample()
                            .createCriteria()
                            .andOfferingCodeEqualTo(spuOfferingInfoDTO.getOfferingCode())
                            .andDeleteTimeIsNull()
                            .example()
                    );
                    boolean spuInfoExist = CollectionUtil.isNotEmpty(spuOfferingInfos);

                    SpuOfferingInfo spuOfferingInfo;
                    if ("A".equals(spuOfferingInfoDTO.getOperType())) {
                        // 新增
                        if (spuInfoExist) {
                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "新增商品已存在");
                        }
                        spuOfferingInfo = new SpuOfferingInfo();
                        spuOfferingInfo.setId(BaseServiceUtils.getId());
                    } else if ("M".equals(spuOfferingInfoDTO.getOperType())) {
                        // 修改
                        if (!spuInfoExist) {
                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "要修改的商品信息不存在");
                        }
                        spuOfferingInfo = spuOfferingInfos.get(0);
                    } else {
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "操作类型错误");
                    }

                    //讲同步请求注入到squ商品信息中去
                    spuOfferingInfo.setOperId(productInfoRequest.getManagerInfo().getOperId());
                    spuOfferingInfo.setOfferingCode(spuOfferingInfoDTO.getOfferingCode());
                    spuOfferingInfo.setOfferingName(spuOfferingInfoDTO.getOfferingName());
                    spuOfferingInfo.setOfferingStatus(spuOfferingInfoDTO.getOfferingStatus());
//                    spuOfferingInfo.setSaleObject(spuOfferingInfoDTO.getSaleObject());
                    spuOfferingInfo.setOperType(spuOfferingInfoDTO.getOperType());
                    savePictures(spuOfferingInfo, spuOfferingInfoDTO.getProductImage());
                    List<ProductLabelInfoDTO> productLabelInfoDTOS = spuOfferingInfoDTO.getProductLabelInfo();
                    String tag = "";
                    if (CollectionUtil.isNotEmpty(productLabelInfoDTOS)) {
                        tag = productLabelInfoDTOS.get(0).getProductLabel();
                    }
                    spuOfferingInfo.setTag(tag);
                    spuOfferingInfo.setUrl(spuOfferingInfoDTO.getProductLinks());

                    if ("A".equals(spuOfferingInfoDTO.getOperType())) {
                        spuOfferingInfoMapper.insert(spuOfferingInfo);
                        // 添加
                        CategoryInfo categoryInfo = new CategoryInfo();
                        categoryInfo.setId(BaseServiceUtils.getId());
                        categoryInfo.setOfferingClass(spuOfferingInfoDTO.getCategoryInfo().getOfferingClass());
                        categoryInfo.setSpuId(spuOfferingInfo.getId());
                        categoryInfoMapper.insert(categoryInfo);
                    } else if ("M".equals(spuOfferingInfoDTO.getOperType())) {
                        // 修改
                        spuOfferingInfoMapper.updateByPrimaryKey(spuOfferingInfo);
                        categoryInfoMapper.selectByExample(new CategoryInfoExample()
                            .createCriteria()
                            .andSpuIdEqualTo(spuOfferingInfo.getId())
                            .example()).forEach(categoryInfo -> {
                            categoryInfo.setOfferingClass(spuOfferingInfoDTO.getCategoryInfo().getOfferingClass());
                            categoryInfoMapper.updateByPrimaryKey(categoryInfo);
                        });
                    }
                    //这里sku可能为空，直接返回成功即可
                    if(productInfoRequest.getSpuOfferingInfo().getSkuOfferingInfo()==null){
                        continue;
                    }
                    //这里加入sku 规格信息 规格只能存在一个code
                    List<com.chinamobile.iot.sc.request.product.SkuOfferingInfoDTO> skuOfferingInfoDTOList = productInfoRequest
                            .getSpuOfferingInfo().getSkuOfferingInfo();
                    for(com.chinamobile.iot.sc.request.product.SkuOfferingInfoDTO skuInfoDto : skuOfferingInfoDTOList){
                        SkuOfferingInfo skuOfferingInfo = new SkuOfferingInfo();
                        skuOfferingInfo.setId(BaseServiceUtils.getId());
                        //注意，这里可能会存在sku中的spu_id不正确的情况，因为可能spu并未添加到数据库。所以匹配的时候统一使用spu_code
                        skuOfferingInfo.setSpuId(spuOfferingInfo.getId());
                        skuOfferingInfo.setSpuCode(spuOfferingInfo.getOfferingCode());
                        skuOfferingInfo.setOfferingCode(skuInfoDto.getOfferingCode());
                        skuOfferingInfo.setOfferingName(skuInfoDto.getOfferingName());
                        skuOfferingInfo.setOfferingStatus(skuInfoDto.getSkuOfferingStatus());
                        skuOfferingInfo.setOfferingStatusTime(skuInfoDto.getSkuOfferingStatusTime());
                        skuOfferingInfo.setComposition(skuInfoDto.getComposition());
                        skuOfferingInfo.setModel(skuInfoDto.getModel());
                        skuOfferingInfo.setSize(skuInfoDto.getSize());
                        skuOfferingInfo.setOperType(skuInfoDto.getOperType());
                        skuOfferingInfo.setRecommendPrice(skuInfoDto.getRecommendPrice());
                        skuOfferingInfo.setPrice(skuInfoDto.getPrice());
                        skuOfferingInfo.setUnit(skuInfoDto.getUnit());
                        skuOfferingInfo.setMarketName(skuInfoDto.getMarketName());
                        skuOfferingInfo.setMarketCode(skuInfoDto.getMarketCode());
                        skuOfferingInfo.setSupplierName(skuInfoDto.getSupplierName());
                        saveSkuReleaseTarget(skuInfoDto.getOfferingCode(), skuInfoDto.getReleaseTargetList());
                        try {
                            skuOfferingInfoMapper.insert(skuOfferingInfo);
                        } catch (DuplicateKeyException e) {
                            resMap.put(strLine, "重复添加该规格，规格号为:" + skuOfferingInfo.getOfferingCode());
                        }
                        //原子商品入库
                        List<AtomOfferingInfoDTO> atomOfferingInfoDTOList = skuInfoDto.getAtomOfferingInfo();
                        for(AtomOfferingInfoDTO atomInfoDto : atomOfferingInfoDTOList){
                            //注意，这里可能会存在atom中的spu_id不正确的情况，因为可能spu并未添加到数据库。所以查询匹配的时候统一使用spu_code
                            AtomOfferingInfo atomOfferingInfo = generateAtomInfo(atomInfoDto, spuOfferingInfo.getId(), spuOfferingInfo.getOfferingCode(),
                                    skuOfferingInfo.getId(), skuOfferingInfo.getOfferingCode());
                            try {
                                atomOfferingInfoMapper.insert(atomOfferingInfo);
                            } catch (DuplicateKeyException e) {
                                resMap.put(strLine, "重复添加原子商品，原子商品编码为:" + atomInfoDto.getOfferingCode());
                            }
                        }
                    }
                }catch (Exception e){
                    log.error("文件名：{}，读取行内容：{}，解析失败原因：{}", file.getName(), strLine, e.getMessage());
                    resMap.put(strLine, e.getMessage());
                    //手动回滚事物
//                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                }
            }
        }catch (Exception e){
            log.error("读取文件异常！，解析失败原因：{}", file.getName(), e.getMessage());
            resMap.put(strLine, e.getMessage());
        }finally {
            if(br != null){
                try {
                    br.close();
                } catch (IOException e) {
                    log.error("关闭BufferedReader失败！,异常内容：{}", e.getMessage());
                }
            }
        }
    }

    /**
     * 解析、处理订单信息
     * @param file
     * @param resMap
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void analyse2COrderInfo(MultipartFile file, Map<String, String> resMap) {
        BufferedReader br = null;
        String strLine = null;
        List<String> successList = new ArrayList<>();
        try {
            //构造一个BufferedReader类来读取文件
            br = new BufferedReader(new InputStreamReader(file.getInputStream(), "UTF-8"));
            //使用readLine方法，一次读一行
            while((strLine = br.readLine())!=null) {
                try{
                    String finalStrLine = strLine;
                    // 解析订单信息数据并处理
                    IOTRequest iotRequest = JSON.parseObject(strLine, IOTRequest.class);
                    Order2CInfoRequest order2CInfoRequest = JSON.parseObject(iotRequest.getContent(), Order2CInfoRequest.class);
                    OrderInfoDTO orderInfoDto = order2CInfoRequest.getOrderInfo();
                    String beId = iotRequest.getBeId();
                    SpuOfferingInfoDTO spuOfferingInfoDto = orderInfoDto.getSpuOfferingInfo();
                    Order2cInfo order2cInfo = wearOrder2cInfo(orderInfoDto, spuOfferingInfoDto);
                    //第二次同步的时候，由于第一次已经将order2c_info加入了表中，所以这里不必抛异常，避免正常流程走不下去
                    try {
                        order2cInfoMapper.insert(order2cInfo);
                    } catch (DuplicateKeyException e) {
                        log.info("该业务订单流水号:" + order2cInfo.getOrderId() + "已存在，请勿重复添加");
                        resMap.put(finalStrLine,"订单已存在，请勿重复添加");
                    }
                    //这里对原子级别订单信息进行添加
                    List<Order2cAtomInfo> atomInfos = new ArrayList<>();
                    //这个是foreach中的，查出来有且仅有一条。
                    spuOfferingInfoDto.getSkuOfferingInfo().forEach(skuInfoDto -> {
                        SkuOfferingInfoExample skuExample = new SkuOfferingInfoExample();
                        skuExample.createCriteria()
                                .andSpuCodeEqualTo(order2cInfo.getSpuOfferingCode())
                                .andOfferingCodeEqualTo(skuInfoDto.getOfferingCode());
                        List<SkuOfferingInfo> skuOfferingInfos = skuOfferingInfoMapper.selectByExample(skuExample);
                        if (skuOfferingInfos.size() == 0) {
                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "该规格不存在，规格编码为:" + skuInfoDto.getOfferingCode());
                        }
                        String skuOfferingName = skuOfferingInfos.get(0).getOfferingName();
                        //这里根据sku offeringCode查询对应规格信息
                        skuInfoDto.getAtomOfferingInfo().forEach(atomInfoDto -> {
                            //查询原子商品信息
                            AtomOfferingInfoExample atomExample = new AtomOfferingInfoExample();
                            atomExample.createCriteria()
                                    .andSpuCodeEqualTo(spuOfferingInfoDto.getOfferingCode())
                                    .andSkuCodeEqualTo(skuInfoDto.getOfferingCode())
                                    .andOfferingCodeEqualTo(atomInfoDto.getOfferingCode());
                            List<AtomOfferingInfo> atomResultList = atomOfferingInfoMapper.selectByExample(atomExample);
                            //这里肯定能找到该规格商品，找不到就是异常了
                            if (atomResultList.size() == 0) {
                                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "该原子商品不存在，原子商品编码:" + atomInfoDto.getOfferingCode());
                            }
                            //确保数据库中没有重复的order2c_atom_info
                            Order2cAtomInfoExample example = new Order2cAtomInfoExample().createCriteria()
                                    .andOrderIdEqualTo(order2cInfo.getOrderId())
                                    .andSpuOfferingCodeEqualTo(order2cInfo.getSpuOfferingCode())
                                    .andSkuOfferingCodeEqualTo(skuInfoDto.getOfferingCode())
                                    .andAtomOfferingCodeEqualTo(atomInfoDto.getOfferingCode()).example();
                            List<Order2cAtomInfo> order2cAtomInfos = atomOrderInfoMapper.selectByExample(example);
                            if(!order2cAtomInfos.isEmpty()){
                                log.info("原子订单已存在，请勿重复添加");
                                /*Order2cAtomInfo order2cAtomInfo = order2cAtomInfos.get(0);
                                try {
                                    order2cAtomInfo.setUpdateTime(DateTimeUtil.getFormatDate(order2cAtomInfo.getCreateTime(),DateTimeUtil.DB_TIME_STR));
                                    atomOrderInfoMapper.updateByPrimaryKey(order2cAtomInfo);
                                } catch (ParseException e) {
                                    e.printStackTrace();
                                }*/
                                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "原子订单已存在，请勿重复添加");
                            }

                            AtomOfferingInfo originAtomInfo = atomResultList.get(0);
                            //设置合作伙伴ID为-1（合作伙伴不在OS系统，故设置固定值）
                            String cooperatorId = "-1";
                            Order2cAtomInfo order2cAtomInfo = new Order2cAtomInfo();
                            try {
                                order2cAtomInfo.withId(BaseServiceUtils.getId())
                                        .withSpuOfferingCode(order2cInfo.getSpuOfferingCode())
                                        .withSkuOfferingName(skuOfferingName)
                                        .withAtomOfferingName(originAtomInfo.getOfferingName())
                                        .withColor(originAtomInfo.getColor())
                                        .withModel(originAtomInfo.getModel())
                                        .withAtomOfferingClass(originAtomInfo.getOfferingClass())
                                        .withOrderId(order2cInfo.getOrderId())
                                        .withSkuOfferingCode(skuInfoDto.getOfferingCode())
                                        .withSkuQuantity(skuInfoDto.getQuantity())
                                        .withSkuPrice(skuInfoDto.getPrice())
                                        .withAtomOfferingCode(atomInfoDto.getOfferingCode())
                                        .withAtomPrice(atomInfoDto.getPrice())
                                        .withAtomSettlePrice(originAtomInfo.getSettlePrice())
                                        .withAtomQuantity(atomInfoDto.getQuantity())
                                        // 根据IOT订单status返回OS系统内部订单状态
                                        .withOrderStatus(getInnerStatus(orderInfoDto.getStatus()))
                                        .withCreateTime(orderInfoDto.getCreateTime())
                                        //由于是导入的订单，将UpdateTime设置为createTime,避免订单列表排序跑到最前面
                                        .withUpdateTime(DateTimeUtil.getFormatDate(order2cAtomInfo.getCreateTime(),DateTimeUtil.DB_TIME_STR))
                                        .withCooperatorId(cooperatorId)
                                        .withBeId(beId);
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                            atomInfos.add(order2cAtomInfo);
                        });
                    });
                    atomOrderInfoMapper.batchInsert(atomInfos);
                    successList.addAll(atomInfos.stream().map(a -> {return a.getOrderId();}).collect(Collectors.toList()));
                }catch (Exception e){
                    log.error("文件名：{}，读取行内容：{}，解析失败原因：{}", file.getName(), strLine, e.getMessage());
                    resMap.put(strLine, e.getMessage());
                    //手动回滚事物
//                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                }
            }
            resMap.put("successList",JSON.toJSONString(successList));
        }catch (Exception e){
            log.error("读取文件异常！，解析失败原因：{}", file.getName(), e.getMessage());
            resMap.put(strLine, e.getMessage());
        }finally {
            if(br != null){
                try {
                    br.close();
                } catch (IOException e) {
                    log.error("关闭BufferedReader失败！,异常内容：{}", e.getMessage());
                }
            }
        }
    }

    /**
     * 解析、处理组织机构信息
     * @param file
     * @param resMap
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void analyseOrderOrgBizInfo(MultipartFile file, Map<String, String> resMap) {
        BufferedReader br = null;
        String strLine = null;
        try {
            //构造一个BufferedReader类来读取文件
            br = new BufferedReader(new InputStreamReader(file.getInputStream(), "UTF-8"));
            //使用readLine方法，一次读一行
            while ((strLine = br.readLine()) != null) {
                try {
                    // 解析订单组织机构信息、操作员信息并处理
                    String[] fieldArray = strLine.split("\\|");
                    if(fieldArray.length > 6){
                        resMap.put(strLine, "字段格式不正确！");
                        continue;
                    }
                    //若最後位-全组织机构名称為空，則添加一位
                    fieldArray = fixStrArray(fieldArray);
                    // 更新业务订单对象
                    Order2cInfo order2cInfo = new Order2cInfo();
                    order2cInfo.withOrderId(fieldArray[0]).withCreateOperCode(fieldArray[1]).withEmployeeNum(fieldArray[2])
                            .withOrderOrgBizCode(fieldArray[3]).withOrgLevel(fieldArray[4]).withOrgName(fieldArray[5]);
                    int result = order2cInfoMapper.updateByPrimaryKeySelective(order2cInfo);
                    if(result <= 0 ){
                        resMap.put(strLine, "更新失败！");
                    }
                }catch (Exception e){
                    log.error("文件名：{}，读取行内容：{}，解析失败原因：{}", file.getName(), strLine, e.getMessage());
                    resMap.put(strLine, e.getMessage());
                    //手动回滚事物
//                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                }
            }
        }catch (Exception e){
            log.error("读取文件异常！，解析失败原因：{}", file.getName(), e.getMessage());
            resMap.put(strLine, e.getMessage());
        }finally {
            if(br != null){
                try {
                    br.close();
                } catch (IOException e) {
                    log.error("关闭BufferedReader失败！,异常内容：{}", e.getMessage());
                }
            }
        }
    }

    /**
     * 若最後位-全组织机构名称為空，則在數組最後添加一位
     * @param fieldArray
     * @return
     */
    private String[] fixStrArray(String[] fieldArray){
        if(fieldArray.length == 5){
            String[] tempArr = new String[6];
            for(int i=0;i<tempArr.length;i++){
                if(i == 5){
                    tempArr[i] = "";
                }else{
                    tempArr[i] = fieldArray[i];
                }
            }
            return tempArr;
        }
        return fieldArray;
    }

    /**
     * 根据iot商城订单状态，获取OS内部订单状态
     * @param iotOrderStatus
     * @return
     */
    private Integer getInnerStatus(Integer iotOrderStatus){

        switch (iotOrderStatus){
            case 0://订单创建
                return OrderStatusInnerEnum.WAIT_SEND.getStatus();
            case 1://订单验收
                return OrderStatusInnerEnum.COMPLETE.getStatus();
            case 3://订单计收
                return OrderStatusInnerEnum.ORDER_SUCCESS.getStatus();
            case 4://订单退款完成
                return OrderStatusInnerEnum.ORDER_FAIL.getStatus();
            case 5://订单部分退款完成
                return OrderStatusInnerEnum.PART_SUCCESS.getStatus();
            case 6://订单部分退款完成
                return OrderStatusInnerEnum.PART_SUCCESS.getStatus();
            default:
                return OrderStatusInnerEnum.WAIT_SEND.getStatus();

        }
    }

    /**
     * 生成atom商品数据库对象
     * @param atomInfoDto
     * @param spuId
     * @param spuCode
     * @param skuId
     * @param skuCode
     * @return
     */
    private AtomOfferingInfo generateAtomInfo(AtomOfferingInfoDTO atomInfoDto, String spuId, String spuCode, String skuId, String skuCode) {
        AtomOfferingInfo atomOfferingInfo = new AtomOfferingInfo();
        atomOfferingInfo.setId(BaseServiceUtils.getId());
        atomOfferingInfo.setSpuId(spuId);
        atomOfferingInfo.setSpuCode(spuCode);
        atomOfferingInfo.setSkuId(skuId);
        atomOfferingInfo.setSkuCode(skuCode);
        atomOfferingInfo.setOfferingCode(atomInfoDto.getOfferingCode());
        atomOfferingInfo.setOfferingClass(atomInfoDto.getOfferingClass());
        atomOfferingInfo.setOfferingName(atomInfoDto.getOfferingName());
        atomOfferingInfo.setQuantity(atomInfoDto.getQuantity());
        atomOfferingInfo.setExtSoftOfferingCode(atomInfoDto.getExtSoftOfferingCode());
        atomOfferingInfo.setExtHardOfferingCode(atomInfoDto.getExtHardOfferingCode());
        atomOfferingInfo.setSettlePrice(atomInfoDto.getSettlePrice());
        atomOfferingInfo.setAtomSalePrice(atomInfoDto.getAtomSalePrice());
        atomOfferingInfo.setColor(atomInfoDto.getColor());
        atomOfferingInfo.setModel(atomInfoDto.getModel());
        atomOfferingInfo.setUnit(atomInfoDto.getUnit());
        //这里默认设置为0，方便后面对其查询计算
        atomOfferingInfo.setInventory(0L);
        atomOfferingInfo.setReserveInventory(0L);
        atomOfferingInfo.setInventoryThreshold(0L);
        //默认不发送短信
        atomOfferingInfo.setIsNotice(false);
        //设置为没发送短信
        atomOfferingInfo.setNotified(false);
        //设置库存未初始化
        atomOfferingInfo.setIsInventory(false);
        //设置DICT、ONENet独立服务包-合作伙伴id 为-1
        atomOfferingInfo.setCooperatorId("-1");
        return atomOfferingInfo;
    }

    /**
     * 组装订单对象
     * @param orderInfoDto
     * @param spuOfferingInfoDto
     * @return
     */
    private Order2cInfo wearOrder2cInfo(OrderInfoDTO orderInfoDto, SpuOfferingInfoDTO spuOfferingInfoDto) {
        Order2cInfo order2cInfo = new Order2cInfo();
        CustInfoDTO custInfo = orderInfoDto.getCustInfo();
        ContactInfoDTO contactInfo = orderInfoDto.getContactInfo();
        AddressInfoDTO addressInfoDTO = contactInfo.getAddresstInfo();
        if (orderInfoDto.getOrderOrgBizInfo() != null) {
            order2cInfo.withOrderOrgBizCode(orderInfoDto.getOrderOrgBizInfo().getOrderOrgBizCode())
                    .withOrgName(orderInfoDto.getOrderOrgBizInfo().getOrgName())
                    .withOrgLevel(orderInfoDto.getOrderOrgBizInfo().getOrgLevel())
            .withProvinceOrgName(orderInfoDto.getOrderOrgBizInfo().getProvinceOrgName());
        }
        return order2cInfo.withBusinessCode(orderInfoDto.getBusinessCode())
                .withCreateOperCode(orderInfoDto.getCreateOperCode())
                .withEmployeeNum(orderInfoDto.getEmployeeNum())
                .withCustCode(custInfo.getCustCode())
                .withCustName(custInfo.getCustName())
                .withBeId(custInfo.getBeId())
                .withLocation(custInfo.getLocation())
                .withRegionId(custInfo.getRegionID())
                .withRemarks(orderInfoDto.getRemarks())
                .withOrderId(orderInfoDto.getOrderId())
                .withBookid(orderInfoDto.getBookId())
                .withStatus(orderInfoDto.getStatus())
                .withTotalPrice(orderInfoDto.getTotalPrice())
                .withCreateTime(orderInfoDto.getCreateTime())
                .withContactPersonName(contactInfo.getContactPersonName())
                .withContactPhone(contactInfo.getContactPhone())
                .withAddr1(addressInfoDTO.getAddr1())
                .withAddr2(addressInfoDTO.getAddr2())
                .withAddr3(addressInfoDTO.getAddr3())
                .withAddr4(addressInfoDTO.getAddr4())
                .withUsaddr(addressInfoDTO.getUsaddr())
                .withSpuOfferingClass(spuOfferingInfoDto.getOfferingClass())
                .withSpuOfferingCode(spuOfferingInfoDto.getOfferingCode())
                .withSupplierCode(spuOfferingInfoDto.getSupplierCode())
                .withOrderStatus(getInnerStatus(orderInfoDto.getStatus()));
    }

    private void savePictures(SpuOfferingInfo spuOfferingInfo, String fileName) {
        if (fileName == null) {
            log.info("empty picture array，ignore");
            return;
        }
        SFTPUtil sftpUtil = new SFTPUtil(sftpUserName, sftpPassword, sftpHost, sftpPort);
        try {
            log.info("下载sftp地址，host：{}，port：{}，name：{}，workPath：{}", sftpHost, sftpPort, sftpUserName, sftpSpuImagePath);
            if (sftpUtil.login()) {
                log.info("sftp连接成功！");

                //SPU主图文件命名规则：
                //IMAGE_文件ID_同步时间.原始文件名后缀
                //文件ID+文件原始后缀名为商城生产环境LSS存储唯一性名称，同步时间为同步该图片时的系统时间yyyymmdd。
                //目前系统支持的图片格式为jpg、png两种。
                //IoT商城在spu商品图主图生变更时才会同步该文件
                log.info("开始下载文件原名:{}", fileName);
                try {
                    byte[] bytes = sftpUtil.download(sftpSpuImagePath, fileName);
                    ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
                    byteArrayUpload.setBytes(bytes);
                    byteArrayUpload.setCover(true);
                    byteArrayUpload.setFileName(fileName);
                    BaseAnswer<UpResult> upResultBaseAnswer = storageService.uploadByte(byteArrayUpload);
                    if (upResultBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())) {
                        String url = upResultBaseAnswer.getData().getOuterUrl();
                        spuOfferingInfo.setImgUrl(url);
                    } else {
                        //对于存储失败的都将直接存放文件原名到地址列表，后续重新下载即可
                        log.error("上传图片结果失败:"+JSON.toJSONString(upResultBaseAnswer.getData()));
                    }
                } catch (Exception e) {
                    log.error("savePictures发生异常",e);
                    //对于异常不进行处理 直接保存文件原名即可
                    log.warn("图片下载失败");
                }
            }
        } catch (Exception e) {
            //把图片都存在innerUrl和outerUrl中
            log.warn("下载图片失败，失败原因:{}", e.toString());
        } finally {
            sftpUtil.logout();
        }
    }

    /**
     * 保存Sku商品发布范围
     * @param skuOfferingCode
     * @param skuReleaseTargetDTOS
     */
    private void saveSkuReleaseTarget(String skuOfferingCode, List<SkuReleaseTargetDTO> skuReleaseTargetDTOS) {
        skuReleaseTargetMapper.deleteByExample(
            new SkuReleaseTargetExample().createCriteria()
                .andSkuOfferingCodeEqualTo(skuOfferingCode)
                .example()
        );
        if (skuReleaseTargetDTOS != null) {
            for (SkuReleaseTargetDTO skuReleaseTargetDTO : skuReleaseTargetDTOS) {
                List<String> cityList = skuReleaseTargetDTO.getCityList();
                if (cityList != null) {
                    for (String cityCode : cityList) {
                        SkuReleaseTarget skuReleaseTarget = new SkuReleaseTarget();
                        skuReleaseTarget.setId(BaseServiceUtils.getId());
                        skuReleaseTarget.setSkuOfferingCode(skuOfferingCode);
                        skuReleaseTarget.setProvinceCode(skuReleaseTargetDTO.getProvince());
                        skuReleaseTarget.setCityCode(cityCode);
                        skuReleaseTargetMapper.insert(skuReleaseTarget);
                    }
                } else {
                    SkuReleaseTarget skuReleaseTarget = new SkuReleaseTarget();
                    skuReleaseTarget.setId(BaseServiceUtils.getId());
                    skuReleaseTarget.setSkuOfferingCode(skuOfferingCode);
                    skuReleaseTarget.setProvinceCode(skuReleaseTargetDTO.getProvince());
                    skuReleaseTargetMapper.insert(skuReleaseTarget);
                }
            }
        }
    }
}
