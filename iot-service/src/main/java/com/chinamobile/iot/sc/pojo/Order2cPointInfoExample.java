package com.chinamobile.iot.sc.pojo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Order2cPointInfoExample {
    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public Order2cPointInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public Order2cPointInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public Order2cPointInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public static Criteria newAndCreateCriteria() {
        Order2cPointInfoExample example = new Order2cPointInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public Order2cPointInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public Order2cPointInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdIsNull() {
            addCriterion("atom_order_id is null");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdIsNotNull() {
            addCriterion("atom_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdEqualTo(String value) {
            addCriterion("atom_order_id =", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("atom_order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotEqualTo(String value) {
            addCriterion("atom_order_id <>", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("atom_order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdGreaterThan(String value) {
            addCriterion("atom_order_id >", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdGreaterThanColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("atom_order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("atom_order_id >=", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdGreaterThanOrEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("atom_order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLessThan(String value) {
            addCriterion("atom_order_id <", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLessThanColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("atom_order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLessThanOrEqualTo(String value) {
            addCriterion("atom_order_id <=", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLessThanOrEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("atom_order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLike(String value) {
            addCriterion("atom_order_id like", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotLike(String value) {
            addCriterion("atom_order_id not like", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdIn(List<String> values) {
            addCriterion("atom_order_id in", values, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotIn(List<String> values) {
            addCriterion("atom_order_id not in", values, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdBetween(String value1, String value2) {
            addCriterion("atom_order_id between", value1, value2, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotBetween(String value1, String value2) {
            addCriterion("atom_order_id not between", value1, value2, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdIsNull() {
            addCriterion("supplier_id is null");
            return (Criteria) this;
        }

        public Criteria andSupplierIdIsNotNull() {
            addCriterion("supplier_id is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierIdEqualTo(String value) {
            addCriterion("supplier_id =", value, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("supplier_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierIdNotEqualTo(String value) {
            addCriterion("supplier_id <>", value, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdNotEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("supplier_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierIdGreaterThan(String value) {
            addCriterion("supplier_id >", value, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdGreaterThanColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("supplier_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierIdGreaterThanOrEqualTo(String value) {
            addCriterion("supplier_id >=", value, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdGreaterThanOrEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("supplier_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierIdLessThan(String value) {
            addCriterion("supplier_id <", value, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdLessThanColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("supplier_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierIdLessThanOrEqualTo(String value) {
            addCriterion("supplier_id <=", value, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdLessThanOrEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("supplier_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierIdLike(String value) {
            addCriterion("supplier_id like", value, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdNotLike(String value) {
            addCriterion("supplier_id not like", value, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdIn(List<String> values) {
            addCriterion("supplier_id in", values, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdNotIn(List<String> values) {
            addCriterion("supplier_id not in", values, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdBetween(String value1, String value2) {
            addCriterion("supplier_id between", value1, value2, "supplierId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdNotBetween(String value1, String value2) {
            addCriterion("supplier_id not between", value1, value2, "supplierId");
            return (Criteria) this;
        }

        public Criteria andDistributorTypeIsNull() {
            addCriterion("distributor_type is null");
            return (Criteria) this;
        }

        public Criteria andDistributorTypeIsNotNull() {
            addCriterion("distributor_type is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorTypeEqualTo(String value) {
            addCriterion("distributor_type =", value, "distributorType");
            return (Criteria) this;
        }

        public Criteria andDistributorTypeEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("distributor_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorTypeNotEqualTo(String value) {
            addCriterion("distributor_type <>", value, "distributorType");
            return (Criteria) this;
        }

        public Criteria andDistributorTypeNotEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("distributor_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorTypeGreaterThan(String value) {
            addCriterion("distributor_type >", value, "distributorType");
            return (Criteria) this;
        }

        public Criteria andDistributorTypeGreaterThanColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("distributor_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorTypeGreaterThanOrEqualTo(String value) {
            addCriterion("distributor_type >=", value, "distributorType");
            return (Criteria) this;
        }

        public Criteria andDistributorTypeGreaterThanOrEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("distributor_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorTypeLessThan(String value) {
            addCriterion("distributor_type <", value, "distributorType");
            return (Criteria) this;
        }

        public Criteria andDistributorTypeLessThanColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("distributor_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorTypeLessThanOrEqualTo(String value) {
            addCriterion("distributor_type <=", value, "distributorType");
            return (Criteria) this;
        }

        public Criteria andDistributorTypeLessThanOrEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("distributor_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorTypeLike(String value) {
            addCriterion("distributor_type like", value, "distributorType");
            return (Criteria) this;
        }

        public Criteria andDistributorTypeNotLike(String value) {
            addCriterion("distributor_type not like", value, "distributorType");
            return (Criteria) this;
        }

        public Criteria andDistributorTypeIn(List<String> values) {
            addCriterion("distributor_type in", values, "distributorType");
            return (Criteria) this;
        }

        public Criteria andDistributorTypeNotIn(List<String> values) {
            addCriterion("distributor_type not in", values, "distributorType");
            return (Criteria) this;
        }

        public Criteria andDistributorTypeBetween(String value1, String value2) {
            addCriterion("distributor_type between", value1, value2, "distributorType");
            return (Criteria) this;
        }

        public Criteria andDistributorTypeNotBetween(String value1, String value2) {
            addCriterion("distributor_type not between", value1, value2, "distributorType");
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdIsNull() {
            addCriterion("distributor_user_id is null");
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdIsNotNull() {
            addCriterion("distributor_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdEqualTo(String value) {
            addCriterion("distributor_user_id =", value, "distributorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("distributor_user_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdNotEqualTo(String value) {
            addCriterion("distributor_user_id <>", value, "distributorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdNotEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("distributor_user_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdGreaterThan(String value) {
            addCriterion("distributor_user_id >", value, "distributorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdGreaterThanColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("distributor_user_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("distributor_user_id >=", value, "distributorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdGreaterThanOrEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("distributor_user_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdLessThan(String value) {
            addCriterion("distributor_user_id <", value, "distributorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdLessThanColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("distributor_user_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdLessThanOrEqualTo(String value) {
            addCriterion("distributor_user_id <=", value, "distributorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdLessThanOrEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("distributor_user_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdLike(String value) {
            addCriterion("distributor_user_id like", value, "distributorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdNotLike(String value) {
            addCriterion("distributor_user_id not like", value, "distributorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdIn(List<String> values) {
            addCriterion("distributor_user_id in", values, "distributorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdNotIn(List<String> values) {
            addCriterion("distributor_user_id not in", values, "distributorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdBetween(String value1, String value2) {
            addCriterion("distributor_user_id between", value1, value2, "distributorUserId");
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdNotBetween(String value1, String value2) {
            addCriterion("distributor_user_id not between", value1, value2, "distributorUserId");
            return (Criteria) this;
        }

        public Criteria andPointIsNull() {
            addCriterion("point is null");
            return (Criteria) this;
        }

        public Criteria andPointIsNotNull() {
            addCriterion("point is not null");
            return (Criteria) this;
        }

        public Criteria andPointEqualTo(Long value) {
            addCriterion("point =", value, "point");
            return (Criteria) this;
        }

        public Criteria andPointEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("point = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPointNotEqualTo(Long value) {
            addCriterion("point <>", value, "point");
            return (Criteria) this;
        }

        public Criteria andPointNotEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("point <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPointGreaterThan(Long value) {
            addCriterion("point >", value, "point");
            return (Criteria) this;
        }

        public Criteria andPointGreaterThanColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("point > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPointGreaterThanOrEqualTo(Long value) {
            addCriterion("point >=", value, "point");
            return (Criteria) this;
        }

        public Criteria andPointGreaterThanOrEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("point >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPointLessThan(Long value) {
            addCriterion("point <", value, "point");
            return (Criteria) this;
        }

        public Criteria andPointLessThanColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("point < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPointLessThanOrEqualTo(Long value) {
            addCriterion("point <=", value, "point");
            return (Criteria) this;
        }

        public Criteria andPointLessThanOrEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("point <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPointIn(List<Long> values) {
            addCriterion("point in", values, "point");
            return (Criteria) this;
        }

        public Criteria andPointNotIn(List<Long> values) {
            addCriterion("point not in", values, "point");
            return (Criteria) this;
        }

        public Criteria andPointBetween(Long value1, Long value2) {
            addCriterion("point between", value1, value2, "point");
            return (Criteria) this;
        }

        public Criteria andPointNotBetween(Long value1, Long value2) {
            addCriterion("point not between", value1, value2, "point");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(Order2cPointInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLikeInsensitive(String value) {
            addCriterion("upper(atom_order_id) like", value.toUpperCase(), "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLikeInsensitive(String value) {
            addCriterion("upper(order_id) like", value.toUpperCase(), "orderId");
            return (Criteria) this;
        }

        public Criteria andSupplierIdLikeInsensitive(String value) {
            addCriterion("upper(supplier_id) like", value.toUpperCase(), "supplierId");
            return (Criteria) this;
        }

        public Criteria andDistributorTypeLikeInsensitive(String value) {
            addCriterion("upper(distributor_type) like", value.toUpperCase(), "distributorType");
            return (Criteria) this;
        }

        public Criteria andDistributorUserIdLikeInsensitive(String value) {
            addCriterion("upper(distributor_user_id) like", value.toUpperCase(), "distributorUserId");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Tue Nov 22 16:35:07 CST 2022
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        private Order2cPointInfoExample example;

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        protected Criteria(Order2cPointInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        public Order2cPointInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Tue Nov 22 16:35:07 CST 2022
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        void example(com.chinamobile.iot.sc.pojo.Order2cPointInfoExample example);
    }
}