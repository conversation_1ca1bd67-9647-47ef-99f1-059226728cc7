package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.pojo.vo.LatestRefundRocVO;
import com.chinamobile.iot.sc.service.Order2CRocInfoService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/1
 * @description 订单退货退款换货记录controller类
 */
@RestController
@RequestMapping("/osweb")
public class Order2CRocController {

    @Resource
    private Order2CRocInfoService order2CRocInfoService;

    /**
     * 根据订单id获取最新的仅退款信息
     * @param orderId
     * @return
     */
    @GetMapping(value = "/roc/latestRefundRoc")
    public BaseAnswer<LatestRefundRocVO> getLatestRefundRoc(String orderId) {
        BaseAnswer<LatestRefundRocVO> baseAnswer = new BaseAnswer<>();
        LatestRefundRocVO latestRefundRocVO = order2CRocInfoService.getLatestRefundRocByOrderId(orderId);
        baseAnswer.setData(latestRefundRocVO);
        return baseAnswer;
    }

    /**
     * （后台接口）为订单售后表新增主键id字段后，为id赋初始值
     */
    @PostMapping(value = "/roc/initId")
    public BaseAnswer initRocId(){
        return order2CRocInfoService.initRocId();
    }
}
