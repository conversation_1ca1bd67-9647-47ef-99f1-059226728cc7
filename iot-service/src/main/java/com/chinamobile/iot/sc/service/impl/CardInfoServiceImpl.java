package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.common.utils.BaseUtils;
import com.chinamobile.iot.sc.config.AreaDataConfig;
import com.chinamobile.iot.sc.constant.CardStatusEnum;
import com.chinamobile.iot.sc.constant.CardTypeEnum;
import com.chinamobile.iot.sc.constant.SellStatusEnum;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.dao.ext.CardInfoMapperExt;
import com.chinamobile.iot.sc.dao.ext.Order2cAtomInfoMapperExt;
import com.chinamobile.iot.sc.dao.handle.OrderHandleMapper;
import com.chinamobile.iot.sc.entity.user.UserPartner;
import com.chinamobile.iot.sc.enums.log.GoodsManageOperateEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.pojo.entity.CardInventoryMainInfo;
import com.chinamobile.iot.sc.pojo.entity.CardInventoryMainInfoExample;
import com.chinamobile.iot.sc.pojo.entity.CardMallSync;
import com.chinamobile.iot.sc.pojo.entity.CardMallSyncExample;
import com.chinamobile.iot.sc.pojo.entity.CardRelation;
import com.chinamobile.iot.sc.pojo.entity.CardRelationExample;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryDetailInfo;
import com.chinamobile.iot.sc.pojo.handle.OrderInfoDetailHandle;
import com.chinamobile.iot.sc.pojo.mapper.Order2cAtomInfoHistoryDO;
import com.chinamobile.iot.sc.pojo.param.CardInfoParam;
import com.chinamobile.iot.sc.pojo.vo.CardInfoVO;
import com.chinamobile.iot.sc.service.CardInfoService;
import com.chinamobile.iot.sc.service.CardRelationService;
import com.chinamobile.iot.sc.service.DkcardxInventoryDetailInfoService;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.excel.EasyExcelDTO;
import com.chinamobile.iot.sc.util.excel.EasyExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.SUCCESS;
import static com.chinamobile.iot.sc.util.excel.EasyExcelUtils.setEasyExcelDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/7
 * @description 号卡信息service实现类
 */
@Service
@Slf4j
public class CardInfoServiceImpl implements CardInfoService {

    @Resource
    private CardInfoMapper cardInfoMapper;

    @Resource
    private CardInfoMapperExt cardInfoMapperExt;

    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private AreaDataConfig areaDataConfig;

    @Resource
    private LogService logService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private Order2cAtomInfoMapper order2cAtomInfoMapper;

    @Resource
    private SkuOfferingInfoHistoryMapper skuOfferingInfoHistoryMapper;

    @Resource
    private CardInventoryMainInfoMapper cardInventoryMainInfoMapper;

    @Resource
    private CardMallSyncMapper cardMallSyncMapper;

    @Resource
    private OrderHandleMapper orderHandleMapper;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private CardRelationServiceImpl cardRelationService;

    @Autowired
    private DkcardxInventoryDetailInfoService dkcardxInventoryDetailInfoService;
    @Override
    public void addCardInfo(CardInfo cardInfo) {
        cardInfoMapper.insert(cardInfo);
    }

    @Override
    public CardInfo getCardInfoById(String id) {
        return cardInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<CardInfo> listCardInfoByNeed(CardInfoExample cardInfoExample) {
        return cardInfoMapper.selectByExample(cardInfoExample);
    }

    @Override
    public PageData<CardInfoVO> pageCardInfo(CardInfoParam cardInfoParam,
                                             LoginIfo4Redis loginIfo4Redis) {
        PageData<CardInfoVO> pageData = new PageData<>();
        Integer pageNum = cardInfoParam.getPageNum();
        Integer pageSize = cardInfoParam.getPageSize();
        handleCardInfoParam(cardInfoParam, loginIfo4Redis);

        Page<CardInfoVO> page = new Page<>(pageNum, pageSize);


        List<CardInfoVO> cardInfoVOList = cardInfoMapperExt.listCardInfo(page, cardInfoParam);
        if (CollectionUtils.isNotEmpty(cardInfoVOList)) {
            handleCardInfoVOList(cardInfoVOList);
        }

        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(cardInfoVOList);

        return pageData;
    }

    @Override
    public void exportCardInfo(CardInfoParam cardInfoParam,
                               LoginIfo4Redis loginIfo4Redis,
                               HttpServletResponse response) throws Exception {
        String roleType = loginIfo4Redis.getRoleType();
//        if (!BaseConstant.ADMIN_ROLE.equals(roleType)) {
            String smsValidCode = cardInfoParam.getSmsValidCode();
            if (StringUtils.isEmpty(smsValidCode)) {

                throw new BusinessException("10004", "导出短信验证码不能为空");
            }
            String phone = loginIfo4Redis.getPhone();
            String redisKey = BaseUtils.getExportSMSValidKey(phone);
            //查看短信验证码
            Object smsValidCodeObj = redisTemplate.opsForValue().get(redisKey);
            if (smsValidCodeObj == null){
                String message = "导出短信验证码已过期";
                response.setHeader("statecode", "10004");
                response.setHeader("message", URLEncoder.encode(message, "UTF-8").replaceAll("\\+", "%20"));
                String content = "【码号信息导出】\n"
                        .concat(message);
                logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code,
                        GoodsManageOperateEnum.MSISDN_MANAGE.code,
                        content,1,message);
                return;
//                throw new BusinessException("10004", "导出短信验证码已过期");
            }else {
                JSONObject smsJsonObject = JSONObject.parseObject(smsValidCodeObj + "");
                if (!smsValidCode.equals(smsJsonObject.getString("code"))){
                    String message = "导出短信验证码错误";
                    response.setHeader("statecode", "10004");
                    response.setHeader("message", URLEncoder.encode(message, "UTF-8").replaceAll("\\+", "%20"));
                    String content = "【码号信息导出】\n"
                            .concat(message);
                    logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code,
                            GoodsManageOperateEnum.MSISDN_MANAGE.code,
                            content,1,message);
                    return ;
//                    throw new BusinessException("10004", "导出短信验证码错误");
                }
            }
//        }

        handleCardInfoParam(cardInfoParam, loginIfo4Redis);
        List<CardInfoVO> cardInfoVOList = listCardInfo(cardInfoParam);
        if (CollectionUtils.isNotEmpty(cardInfoVOList)) {
            handleCardInfoVOList(cardInfoVOList);

            /*String fileName = "码号信息导出";
            ExportParams exportParams = new ExportParams(fileName,
                    fileName, ExcelType.XSSF);
            exportParams.setTitle("");
            ExcelUtils.exportExcel(cardInfoVOList, CardInfoVO.class,
                    fileName, exportParams, response);*/
        }else{
            cardInfoVOList.add(new CardInfoVO());
        }

        List<EasyExcelDTO> easyExcelDTOList = new ArrayList<>();
        // 码号信息详情
        EasyExcelDTO easyExcelDTO = setEasyExcelDTO(0, "码号信息导出明细", "list",
                cardInfoVOList, null);

        easyExcelDTOList.add(easyExcelDTO);
        String excelName = "码号信息导出";
        excelName = URLEncoder.encode(excelName, "UTF-8");
        ClassPathResource classPathResource = new ClassPathResource("template/card_info_export_template.xlsx");
        InputStream templateFileName = classPathResource.getInputStream();
        // 导出码号信息
        EasyExcelUtils.exportManySheetExcel(response, easyExcelDTOList,
                excelName, templateFileName,
                BaseErrorConstant.SUCCESS.getStateCode(), BaseErrorConstant.SUCCESS.getMessage());

        response.setHeader("statecode", "00000");
        response.setHeader("message", URLEncoder.encode("导出成功", "UTF-8").replaceAll("\\+", "%20"));

        // 记录日志
        String content = "【码号信息导出】\n"
                .concat("导出").concat(cardInfoVOList.size() + "")
                .concat("条");
        logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code,
                GoodsManageOperateEnum.MSISDN_MANAGE.code,
                content, LogResultEnum.LOG_SUCESS.code,null);
    }

    @Override
    public List<CardInfoVO> listCardInfo(CardInfoParam cardInfoParam) {
        return cardInfoMapperExt.listCardInfo(cardInfoParam);
    }

    @Override
    public List<CardInfoVO> listCardInfoCheckImport(CardInfoParam cardInfoParam) {
        return cardInfoMapperExt.listCardInfoCheckImport(cardInfoParam);
    }

    @Override
    public Long countCardInfoCheckImport(CardInfoParam cardInfoParam) {
        return cardInfoMapperExt.countCardInfoCheckImport(cardInfoParam);
    }

    @Override
    public BaseAnswer<List<String>> orderProductTemplateCardList(String orderId, String msisdn) {
        List<OrderInfoDetailHandle> orderInfoDetailHandleList = orderHandleMapper.selectOrderDetailByOrderId(orderId);
        if (CollectionUtils.isEmpty(orderInfoDetailHandleList)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"订单信息不存在");
        }
        OrderInfoDetailHandle orderInfoDetailHandle = orderInfoDetailHandleList.get(0);
        /*Order2cAtomInfo order2cAtomInfo = order2cAtomInfoMapper.selectByPrimaryKey(atomOrderId);
        if(order2cAtomInfo == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"原子订单信息不存在");
        }
        SkuOfferingInfoHistoryExample skuExample = new SkuOfferingInfoHistoryExample().createCriteria()
                .andSpuCodeEqualTo(order2cAtomInfo.getSpuOfferingCode())
                .andSpuOfferingVersionEqualTo(order2cAtomInfo.getSpuOfferingVersion())
                .andOfferingCodeEqualTo(order2cAtomInfo.getSkuOfferingCode())
                .andSkuOfferingVersionEqualTo(order2cAtomInfo.getSkuOfferingVersion()).example();*/
        SkuOfferingInfoHistoryExample skuExample = new SkuOfferingInfoHistoryExample().createCriteria()
                .andSpuCodeEqualTo(orderInfoDetailHandle.getSpuOfferingCode())
                .andSpuOfferingVersionEqualTo(orderInfoDetailHandle.getSpuOfferingVersion())
                .andOfferingCodeEqualTo(orderInfoDetailHandle.getSkuOfferingCode())
                .andSkuOfferingVersionEqualTo(orderInfoDetailHandle.getSkuOfferingVersion()).example();
        List<SkuOfferingInfoHistory> skuOfferingInfoHistories = skuOfferingInfoHistoryMapper.selectByExample(skuExample);
        if(CollectionUtils.isEmpty(skuOfferingInfoHistories)){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"原子订单对应的规格商品不存在");
        }
        String templateName = skuOfferingInfoHistories.get(0).getTemplateName();
        CardInfoParam cardInfoParam = new CardInfoParam();
        cardInfoParam.setTemplateName(templateName);
        cardInfoParam.setCardStatus(CardStatusEnum.NOT_SELL.getType());
        if(StringUtils.isNotEmpty(msisdn)){
            cardInfoParam.setMsisdn(msisdn);
        }
        //查询未销售的
        List<CardInfoVO> cardInfoVOS = cardInfoMapperExt.listCardInfo(cardInfoParam);
        if(CollectionUtils.isEmpty(cardInfoVOS)){
            return BaseAnswer.success(new ArrayList<>());
        }
        List<String> collect = cardInfoVOS.stream().map(c -> {
            return c.getMsisdn();
        }).collect(Collectors.toList());
        return BaseAnswer.success(collect);
    }

    @Override
    public BaseAnswer<String> orderProductTemplateName(String orderId) {
        /*Order2cAtomInfo order2cAtomInfo = order2cAtomInfoMapper.selectByPrimaryKey(atomOrderId);
        if(order2cAtomInfo == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"原子订单信息不存在");
        }*/
        List<OrderInfoDetailHandle> orderInfoDetailHandleList = orderHandleMapper.selectOrderDetailByOrderId(orderId);
        if (CollectionUtils.isEmpty(orderInfoDetailHandleList)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"订单信息不存在");
        }
        OrderInfoDetailHandle orderInfoDetailHandle = orderInfoDetailHandleList.get(0);
        SkuOfferingInfoHistoryExample skuExample = new SkuOfferingInfoHistoryExample().createCriteria()
                .andSpuCodeEqualTo(orderInfoDetailHandle.getSpuOfferingCode())
                .andSpuOfferingVersionEqualTo(orderInfoDetailHandle.getSpuOfferingVersion())
                .andOfferingCodeEqualTo(orderInfoDetailHandle.getSkuOfferingCode())
                .andSkuOfferingVersionEqualTo(orderInfoDetailHandle.getSkuOfferingVersion()).example();
        List<SkuOfferingInfoHistory> skuOfferingInfoHistories = skuOfferingInfoHistoryMapper.selectByExample(skuExample);
        if(CollectionUtils.isEmpty(skuOfferingInfoHistories)){
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"原子订单对应的规格商品不存在");
        }
        String templateName = skuOfferingInfoHistories.get(0).getTemplateName();
        return BaseAnswer.success(templateName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleHistoryCardInventory(CardInfoParam param) {
        CardInfoExample cardInfoExample = new CardInfoExample();
        CardInfoExample.Criteria criteria = cardInfoExample.createCriteria();
        String openCardStartTime = param.getOpenCardStartTime();
        String openCardEndTime = param.getOpenCardEndTime();
        if (StringUtils.isNotEmpty(openCardStartTime)){
            criteria.andOpenCardTimeGreaterThanOrEqualTo(openCardStartTime);
        }
        if (StringUtils.isNotEmpty(openCardEndTime)){
            criteria.andOpenCardTimeLessThanOrEqualTo(openCardEndTime);
        }

        List<CardInfo> cardInfos = cardInfoMapper.selectByExample(cardInfoExample);
        //通过模板编码及服务编码分组
        Map<String, List<CardInfo>> collect = cardInfos.stream().collect(Collectors.groupingBy(en -> en.getTemplateId() + en.getCustCode()));
        Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
        Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
        Date date = new Date();
        log.info("开始执行码号库存历史数据处理startCardInfos：{}",collect.size());
        Integer count =1;
        for (Map.Entry<String, List<CardInfo>> listEntry : collect.entrySet()) {
            log.info("执行码号库存历史数据处理执行条数startCardInfosCount：{}",count++);
            List<CardInfo> cardInfoList = listEntry.getValue();
            CardInfo entityCard = cardInfoList.get(0);
            String templateId = entityCard.getTemplateId();
            String custCode = entityCard.getCustCode();
            String mainId ="";
            List<CardInventoryMainInfo> cardInventoryMainInfos = cardInventoryMainInfoMapper.selectByExample(new CardInventoryMainInfoExample().createCriteria()
                    .andTemplateIdEqualTo(templateId).andCustCodeEqualTo(custCode).example());
            if (CollectionUtils.isEmpty(cardInventoryMainInfos)){
                CardInventoryMainInfo cardInventoryMainInfo = new CardInventoryMainInfo();
                 mainId = BaseServiceUtils.getId();
                cardInventoryMainInfo.setId(mainId);
                cardInventoryMainInfo.setCustCode(entityCard.getCustCode());
                cardInventoryMainInfo.setCustName(entityCard.getCustName());
                cardInventoryMainInfo.setTemplateId(entityCard.getTemplateId());
                cardInventoryMainInfo.setTemplateName(entityCard.getTemplateName());
                cardInventoryMainInfo.setBeId(entityCard.getBeId());
                cardInventoryMainInfo.setProviceName((String) provinceCodeNameMap.get(entityCard.getBeId()));
                cardInventoryMainInfo.setRegionId(entityCard.getRegionId());
                cardInventoryMainInfo.setRegionName((String) locationCodeNameMap.get(entityCard.getRegionId()));
                //查询号卡关联的码号的卡片类型，按照现在来说 相同号卡的码号卡片类型是相同的 任取一个就行。。。。。、
                String id = entityCard.getId();
                List<CardMallSync> cardMallSyncs = cardMallSyncMapper.selectByExample(new CardMallSyncExample().createCriteria().andCardInfoIdEqualTo(id).example());
               if (CollectionUtils.isEmpty(cardMallSyncs)){
                   log.info("号卡关联的码号数据为0：cardInfoId:{}",id);
                   continue;
               }
                CardMallSync cardMallSync = cardMallSyncs.get(0);
                cardInventoryMainInfo.setCardType(cardMallSync.getCardType());
                //TODO 计算初始化历史码号库存
                cardInventoryMainInfo.setReserveQuatity(0);
                cardInventoryMainInfo.setInventoryThreshold(0);
                cardInventoryMainInfo.setIsNotice(true);
                cardInventoryMainInfo.setInventoryStatus("1");
                cardInventoryMainInfo.setCreateTime(date);
                cardInventoryMainInfo.setUpdateTime(date);
                CardInfoParam cardInfoParam = new CardInfoParam();
                param.setTemplateId(entityCard.getTemplateId());
                param.setCustCode(entityCard.getCustCode());
            /*    List<String> cardStatusList = new ArrayList<>();
                List<String> cardTypeList = new ArrayList<>();
                cardStatusList.add("1");
                cardStatusList.add("2");
                param.setCardStatusList(cardStatusList);
                cardTypeList.add("0");
                cardTypeList.add("1");
                param.setCardTypeList(cardTypeList);*/
                List<CardInfoVO> cardInfoVOS = listCardInfo(param);
                cardInventoryMainInfo.setCurrentInventory(cardInfoVOS.size());
                cardInventoryMainInfo.setTotalInventory(cardInfoVOS.size());
                //TODO 查询当前省份是否存在省公司合作伙伴
           /*     BaseAnswer<List<UserPartner>> userPartnerByProvinceList = userFeignClient.getUserPartnerByProvinceList(entityCard.getBeId());
                List<UserPartner> data = userPartnerByProvinceList.getData();
                if (CollectionUtils.isEmpty(data)){
                    cardInventoryMainInfo.setIsProvicePartner("1");
                }else {
                    cardInventoryMainInfo.setIsProvicePartner("0");
                }*/
                //筛选过滤确定码号是未销售及销售中
                cardInventoryMainInfoMapper.insert(cardInventoryMainInfo);
            }else {
                CardInventoryMainInfo cardInventoryMainInfo = cardInventoryMainInfos.get(0);
                param.setTemplateId(templateId);
                param.setCustCode(custCode);
                 mainId = cardInventoryMainInfo.getId();
                List<CardInfoVO> cardInfoVOS = listCardInfo(param);
                CardInventoryMainInfo cardInventoryMainInfoEn = new CardInventoryMainInfo();
                cardInventoryMainInfoEn.setId(cardInventoryMainInfo.getId());
                //空写卡后更新，就等更新了，直接更新成正常未销售库存
                cardInventoryMainInfoEn.setCurrentInventory(cardInfoVOS.size());
                cardInventoryMainInfoEn.setTotalInventory(cardInfoVOS.size());
              /*  cardInventoryMainInfoEn.setCurrentInventory(cardInventoryMainInfo.getCurrentInventory()-cardInfoVOS.size());
                cardInventoryMainInfoEn.setTotalInventory(cardInventoryMainInfo.getTotalInventory()-cardInfoVOS.size());*/
                cardInventoryMainInfoEn.setUpdateTime(new Date());
                cardInventoryMainInfoMapper.updateByPrimaryKeySelective(cardInventoryMainInfoEn);
            }

            //处理历史码号，码号信息表添加码号库存id
            for (CardInfo cardInfo : cardInfoList) {
                String cardId = cardInfo.getId();
                //TODO 要不要排除销售后的码号
                List<CardMallSync> cardMallSyncsList = cardMallSyncMapper.selectByExample(new CardMallSyncExample().createCriteria()
                        .andCardInfoIdEqualTo(cardId).example());
                for (CardMallSync mallSync : cardMallSyncsList) {
                    String cardInventoryMainId = mallSync.getCardInventoryMainId();
                    if (StringUtils.isEmpty(cardInventoryMainId)){
                        mallSync.setCardInventoryMainId(mainId);
                        cardMallSyncMapper.updateByPrimaryKeySelective(mallSync);
                    }
                }
            }
        }
        log.info("执行历史码号库存数据结束endCardInfo:{}",count);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer setStatusCanSell(String id) {
        Date now = new Date();
        CardMallSync cardMallSync = cardMallSyncMapper.selectByPrimaryKey(id);
        if(cardMallSync == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"码号不存在");
        }
        String cardInventoryMainId = cardMallSync.getCardInventoryMainId();
        CardInventoryMainInfo cardInventoryMainInfo = cardInventoryMainInfoMapper.selectByPrimaryKey(cardInventoryMainId);
        if(cardInventoryMainInfo == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"码号库存信息不存在");
        }
        String lockKey = "setStatusCanSell_"+id;
        redisUtil.smartLock(lockKey,() -> {
            String cardStatus = cardMallSync.getCardStatus();
            if(!CardStatusEnum.CAN_NOT_SELL.getType().equals(cardStatus)){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"只有【不可销售】的码号才能转化为可销售");
            }

            //码号更新为未销售
            cardMallSync.setCardStatus(CardStatusEnum.NOT_SELL.getType());
            cardMallSync.setUpdateTime(now);
            cardMallSyncMapper.updateByPrimaryKey(cardMallSync);

            //码号库存+1
            cardInventoryMainInfo.setCurrentInventory(cardInventoryMainInfo.getCurrentInventory()+1);
            cardInventoryMainInfo.setTotalInventory(cardInventoryMainInfo.getTotalInventory()+1);
            cardInventoryMainInfo.setUpdateTime(now);
            cardInventoryMainInfoMapper.updateByPrimaryKey(cardInventoryMainInfo);

            String msisdn = cardMallSync.getMsisdn();
            List<CardRelation> cardRelationList = cardRelationService.listCardRelationByNeed(new CardRelationExample().createCriteria().andMsisdnEqualTo(msisdn).andDeleteTimeIsNull().example());
            if(CollectionUtils.isNotEmpty(cardRelationList)){
                if(cardRelationList.size() > 1){
                    throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR,"数据异常,码号对应的X终端有多个");
                }
                //x终端更新为未销售
                CardRelation cardRelation = cardRelationList.get(0);
                cardRelation.setSellStatus(SellStatusEnum.SELLING.getType());
                cardRelation.setUpdateTime(now);
                cardRelationService.updateCardRelationById(cardRelation);

                //X终端库存+1
                HashMap<String, Integer> detailInventoryMap = new HashMap<>();
                Set<String> mainInventorySet = new HashSet<>();
                cardRelationService.handleKxInventoryAssemble(cardRelation,detailInventoryMap,mainInventorySet);
                if (MapUtils.isNotEmpty(detailInventoryMap)) {
                    detailInventoryMap.forEach((detailId, count) -> {
                        DkcardxInventoryDetailInfo inventoryDetailInfo = dkcardxInventoryDetailInfoService.getDkcardxInventoryDetailInfoById(detailId);
                        if (!Optional.ofNullable(inventoryDetailInfo).isPresent()) {
                            throw new BusinessException("10028", "id为"+detailId+"的x终端库存详情信息不存在");
                        }
                        DkcardxInventoryDetailInfo updateDeatailInfo = new DkcardxInventoryDetailInfo();
                        updateDeatailInfo.setId(detailId);
                        updateDeatailInfo.setCurrentInventory(inventoryDetailInfo.getCurrentInventory() + count);
                        updateDeatailInfo.setTotalInventory(inventoryDetailInfo.getTotalInventory() + count);
                        updateDeatailInfo.setUpdateTime(now);
                        dkcardxInventoryDetailInfoService.updateDkcardxInventoryDetailInfoById(updateDeatailInfo);
                    });
                }

                // 记录日志
                String content = "【转为可销售】\n"
                        .concat("码号:").concat(msisdn)
                        .concat("卡号类型:").concat(CardTypeEnum.getDesc(cardMallSync.getCardType()));
                logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code,
                        GoodsManageOperateEnum.MSISDN_MANAGE.code,
                        content, LogResultEnum.LOG_SUCESS.code,null);
            }else {
                log.info("卡号:{}不存在，不处理",msisdn);
            }
            return null;
        });
        return BaseAnswer.success(null);
    }


    /**
     * 处理查询参数信息
     *
     * @param cardInfoParam
     * @param loginIfo4Redis
     */
    private void handleCardInfoParam(CardInfoParam cardInfoParam,
                                     LoginIfo4Redis loginIfo4Redis) {
        String openCardStartTime = cardInfoParam.getOpenCardStartTime();
        String openCardEndTime = cardInfoParam.getOpenCardEndTime();
        if (StringUtils.isNotEmpty(openCardStartTime)) {
            openCardStartTime = DateUtils.toStringDate(openCardStartTime, DateUtils.DEFAULT_DATETIME_FORMAT, DateUtils.DATETIME_FORMAT_NO_SYMBOL);
            cardInfoParam.setOpenCardStartTime(openCardStartTime);
        }

        if (StringUtils.isNotEmpty(openCardEndTime)) {
            openCardEndTime = DateUtils.toStringDate(openCardEndTime, DateUtils.DEFAULT_DATETIME_FORMAT, DateUtils.DATETIME_FORMAT_NO_SYMBOL);
            cardInfoParam.setOpenCardEndTime(openCardEndTime);
        }

        String roleType = loginIfo4Redis.getRoleType();
        String userId = loginIfo4Redis.getUserId();

        // 如果是主合作伙伴、从合作伙伴及合作伙伴省管账号
        boolean isPartnerLordRole = BaseConstant.PARTNER_LORD_ROLE.equals(roleType);
        boolean isPartnerRole = BaseConstant.PARTNER_ROLE.equals(roleType);
        boolean isPartnerProvince = BaseConstant.PARTNER_PROVINCE.equals(roleType);
        if (isPartnerLordRole || isPartnerRole || isPartnerProvince) {
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
            if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                log.warn("码号查询时主合作伙伴、从合作伙伴及合作伙伴省管账号错误:{}", JSONObject.toJSONString(data4UserBaseAnswer));
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省管账号错误");
            }

            Data4User data4User = data4UserBaseAnswer.getData();
            String beId = cardInfoParam.getBeId();
            String regionId = cardInfoParam.getRegionId();
            // 合作伙伴主账号可查看、导出省级维度的码号信息
            if (isPartnerLordRole && StringUtils.isEmpty(beId)) {
                cardInfoParam.setBeId(data4User.getBeIdPartner());
            }

            // 合作伙伴省管账号、合作伙伴从账号可查看、导出地市维度的码号信息
            if ((isPartnerRole || isPartnerProvince)
                    && StringUtils.isEmpty(regionId)) {
                String userLocation = data4User.getLocationIdPartner();
                if ("all".equals(userLocation)) {
                    cardInfoParam.setBeId(data4User.getBeIdPartner());
                } else {
                    cardInfoParam.setRegionId(userLocation);
                }

            }
        }
    }

    /**
     * 处理码号信息
     *
     * @param cardInfoVOList
     */
    private void handleCardInfoVOList(List<CardInfoVO> cardInfoVOList) {


        Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
        Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
        cardInfoVOList.forEach(cardInfoVO -> {
            String openCardTime = cardInfoVO.getOpenCardTime();
            openCardTime = DateUtils.toStringDate(openCardTime, DateUtils.DATETIME_FORMAT_NO_SYMBOL,
                    DateUtils.DEFAULT_DATE_FORMAT);
            cardInfoVO.setOpenCardTime(openCardTime);

            String beId = cardInfoVO.getBeId();
            Object provinceName = provinceCodeNameMap.get(beId);
            if (provinceName != null) {
                cardInfoVO.setProvinceName((String) provinceName);
            } else {
                cardInfoVO.setProvinceName("");
            }

            String regionId = cardInfoVO.getRegionId();
            if (StringUtils.isNotEmpty(regionId)) {
                Object cityName = locationCodeNameMap.get(regionId);
                if (cityName != null) {
                    cardInfoVO.setCityName((String) cityName);
                } else {
                    cardInfoVO.setCityName("");
                }
            }

            String cardTypeName = CardTypeEnum.getDesc(cardInfoVO.getCardType());
            cardInfoVO.setCardTypeName(cardTypeName);

            String cardStatusName = CardStatusEnum.getDescByType(cardInfoVO.getCardStatus());
            cardInfoVO.setCardStatusName(cardStatusName);
        });
    }
}
