package com.chinamobile.iot.sc.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.builder.ExcelReaderSheetBuilder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.KafkaTopic;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.common.utils.RegexUtil;
import com.chinamobile.iot.sc.config.AreaDataConfig;
import com.chinamobile.iot.sc.config.ProvinceCityConfig;
import com.chinamobile.iot.sc.config.ServiceConfig;
import com.chinamobile.iot.sc.constant.*;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.dao.ext.CardRelationMapperExt;
import com.chinamobile.iot.sc.dao.ext.DkcardxInventoryMainInfoMapperExt;
import com.chinamobile.iot.sc.dao.handle.OrderHandleMapper;
import com.chinamobile.iot.sc.dao.handle.ProductHandlerMapper;
import com.chinamobile.iot.sc.entity.ByteArrayUpload;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.entity.UpResult;
import com.chinamobile.iot.sc.entity.iot.OrderCooperatorInfoByGroupDTO;
import com.chinamobile.iot.sc.entity.iot.OrderCooperatorInfoByGroupParam;
import com.chinamobile.iot.sc.enums.AtomOfferingClassEnum;
import com.chinamobile.iot.sc.enums.SPUOfferingClassEnum;
import com.chinamobile.iot.sc.enums.log.GoodsManageOperateEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.OrderManageOperateEnum;
import com.chinamobile.iot.sc.excel.*;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.feign.IotFeignClient;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.AddMessageParam;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.pojo.dto.*;
import com.chinamobile.iot.sc.pojo.entity.*;
import com.chinamobile.iot.sc.pojo.handle.OrderInfoDetailHandle;
import com.chinamobile.iot.sc.pojo.mapper.OrderProductCardRelationListDO;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.*;
import com.chinamobile.iot.sc.request.AllocateCardResultRequest;
import com.chinamobile.iot.sc.request.QrySubscribersRequest;
import com.chinamobile.iot.sc.request.QuerySubscriberStatusRequest;
import com.chinamobile.iot.sc.request.product.ProvinceCityVO;
import com.chinamobile.iot.sc.response.iot.QrySubscribersResponse;
import com.chinamobile.iot.sc.response.iot.QuerySubscriberStatusResponse;
import com.chinamobile.iot.sc.service.*;
import com.chinamobile.iot.sc.util.*;
import com.chinamobile.iot.sc.util.excel.EasyExcelDTO;
import com.chinamobile.iot.sc.util.excel.EasyExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.common.BaseConstant.PARTNER_LORD_ROLE;
import static com.chinamobile.iot.sc.common.BaseConstant.PARTNER_ROLE;
import static com.chinamobile.iot.sc.common.Constant.*;
import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.SUCCESS;
import static com.chinamobile.iot.sc.util.excel.EasyExcelUtils.setEasyExcelDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/1
 * @description 卡相关信息service实现类
 */
@Service
@Slf4j
public class CardRelationServiceImpl implements CardRelationService {

    private ExecutorService executorService = new ThreadPoolExecutor(8, 16, 1, TimeUnit.MINUTES, new LinkedBlockingQueue<>(100000));

    //批量交付卡+X 的excel表头
    public static final List<String> batchDeliverHeaderList = Arrays.asList("订单号,imei,码号".split(","));

    @Resource
    private CardRelationMapper cardRelationMapper;

    @Resource
    private CardRelationMapperExt cardRelationMapperExt;

    @Resource
    private CardInfoService cardInfoService;

    @Resource
    private OrderHandleMapper orderHandleMapper;

    @Resource
    private ExcelNewCardRelationImportHandler excelNewCardRelationImportHandler;

    @Resource
    private ExcelNewCardRelationImportToCardHandler excelNewCardRelationImportToCardHandler;

    @Resource
    private ExcelBatchDeleteNotUsedCardImportHandler excelBatchDeleteNotUsedCardImportHandler;

    @Resource
    private ExcelImeiErrorImportHandler excelImeiErrorImportHandler;

    @Resource
    private FileUtils fileUtils;

    @Resource
    private AreaDataConfig areaDataConfig;

    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private LogService logService;

    @Resource
    private Order2cInfoMapper order2cInfoMapper;

    @Resource
    private SpuOfferingInfoHistoryMapper spuOfferingInfoHistoryMapper;

    @Resource
    private Order2cAtomInfoMapper order2cAtomInfoMapper;

    @Resource
    private AfterMarketOrder2cInfoMapper afterMarketOrder2cInfoMapper;

    @Resource
    private AfterMarketOrder2cOfferingInfoMapper afterMarketOrder2cOfferingInfoMapper;

    @Resource
    private DkcardxInventoryDetailInfoMapper dkcardxInventoryDetailInfoMapper;

    @Resource
    private DkcardxInventoryAtomInfoMapper dkcardxInventoryAtomInfoMapper;

    @Resource
    private DkcardxInventoryInfoService dkcardxInventoryInfoService;

    @Resource
    private OrderCooperatorRelationService orderCooperatorRelationService;

    @Resource
    private AtomOfferingCooperatorRelationService atomOfferingCooperatorRelationService;

    @Resource
    private Order2cAtomHistoryMapper order2cAtomHistoryMapper;

    @Value("${sms.smsInInventoryKxAlarmTemplateId:107591}")
    private String smsInventoryKxDeficiencyTemplateId;

    @Value("${sms.importKxFail:108065}")
    private String importKxFail;
    @Value("${sms.importKxSucc:108064}")
    private String importKxSucc;
    @Value("${sms.importKxErr:108069}")
    private String importKxErr;
    @Value("${sms.importKxDevlierFail:108071}")
    private String importKxDevlierFail;
    @Value("${sms.importKxDevlierSucc:108070}")
    private String importKxDevlierSucc;
    @Value("${sms.importKxDevlierErr:108069}")
    private String importKxDevlierErr;
    @Value("${sms.importKxOrderDevlierFail:108110}")
    private String importKxOrderDevlierFail;
    @Value("${sms.importKxOrderDevlierSucc:108109}")
    private String importKxOrderDevlierSucc;
    @Value("${sms.importKxOrderDevlierErr:108069}")
    private String importKxOrderDevlierErr;

    @Value("${tocustomer.orderType}")
    private List toCustomerOrderType;


    @Autowired
    private SmsFeignClient smsFeignClient;

    @Resource
    private DkcardxInventoryConfigService dkcardxInventoryConfigService;

    @Resource
    private AtomOfferingInfoMapper atomOfferingInfoMapper;

    @Resource
    private AtomOfferingInfoHistoryMapper atomOfferingInfoHistoryMapper;


    @Value("${iot.encodeKey}")
    private String encodeKey;

    @Value("${iot.sm4Key}")
    private String iotSm4Key;

    @Value("${iot.sm4Iv}")
    private String iotSm4Iv;

    @Autowired
    private IOrder2CService order2CService;

    @Resource
    private SkuOfferingInfoHistoryMapper skuOfferingInfoHistoryMapper;

    @Resource
    private SkuMsisdnRelationMapper skuMsisdnRelationMapper;

    @Resource
    private DkcardxInventoryMainInfoService dkcardxInventoryMainInfoService;

    @Resource
    private DkcardxInventoryDetailInfoService dkcardxInventoryDetailInfoService;

    @Resource
    private ProvinceCityConfig provinceCityConfig;

    @Resource
    private ProductHandlerMapper productHandlerMapper;

    @Autowired
    private CardMallSyncService cardMallSyncService;

    @Resource
    private CardMallSyncMapper cardMallSyncMapper;

    @Resource
    private CardInventoryMainInfoMapper cardInventoryMainInfoMapper;

    @Resource
    private SkuMsisdnRelationService skuMsisdnRelationService;

    @Resource
    private CardChooseDeliveryService cardChooseDeliveryService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IStorageService storageService;

    @Resource
    private IotFeignClient iotFeignClient;

    @Autowired
    private KafkaTemplate<String, byte[]> kafkaTemplate;

    @Autowired
    private ServiceConfig serviceConfig;

    @Resource
    private CardRelationImportInfoService cardRelationImportInfoService;

    @Autowired
    private CardRelationServiceImpl cardRelationServiceImpl;

    @Resource
    private DkcardxInventoryMainInfoMapperExt dkcardxInventoryMainInfoMapperExt;

    @Override
    public List<CardRelation> listCardRelationByNeed(CardRelationExample cardRelationExample) {
        return cardRelationMapper.selectByExample(cardRelationExample);
    }

    @Override
    public Long countCardRelationByNeed(CardRelationExample cardRelationExample) {
        return cardRelationMapper.countByExample(cardRelationExample);
    }


    @Override
    public void batchAddCardRelation(List<CardRelation> cardRelationList) {
        cardRelationMapper.batchInsert(cardRelationList);
    }

    @Override
    public List<CardRelationVO> listCardRelation(CardRelationParam cardRelationParam) {
        return cardRelationMapperExt.listCardRelation(cardRelationParam);
    }

    @Override
    public void exportCardRelation(CardRelationParam cardRelationParam,
                                   HttpServletResponse response) throws IOException {
        List<CardRelationVO> cardRelationVOList = listCardRelation(cardRelationParam);
        if (CollectionUtils.isNotEmpty(cardRelationVOList)) {
            cardRelationVOList.stream().forEach(cardRelationVO -> {
                cardRelationVO.setCreateTimeStr(DateUtils.dateToStr(cardRelationVO.getCreateTime(), DateUtils.DEFAULT_DATETIME_FORMAT));
            });
        }
        String fileName = "导出卡数据(终端IMEI与临时iccid关系)";
        ExportParams exportParams = new ExportParams(fileName,
                fileName, ExcelType.XSSF);
        ExcelUtils.exportExcel(cardRelationVOList, CardRelationVO.class,
                fileName, exportParams, response);
    }

    @Override
    public List<OrderCardVO> listOrderCard(CardRelationParam cardRelationParam) {
        return cardRelationMapperExt.listOrderCard(cardRelationParam);
    }

    @Override
    public void exportOrderCard(CardRelationParam cardRelationParam,
                                LoginIfo4Redis loginIfo4Redis,
                                HttpServletResponse response){
        String userId = loginIfo4Redis.getUserId();
        String ip = cardRelationParam.getIp();
        try {
            String beginDate = cardRelationParam.getBeginDate();
            String endDate = cardRelationParam.getEndDate();
            String deliverBeginDate = cardRelationParam.getDeliverBeginDate();
            String deliverEndDate = cardRelationParam.getDeliverEndDate();
            String clientName = cardRelationParam.getClientName();
            String imei = cardRelationParam.getImei();
            String tempIccid = cardRelationParam.getTempIccid();
            String orderCustName = cardRelationParam.getOrderCustName();


            if (StringUtils.isNotEmpty(beginDate)){
                cardRelationParam.setBeginDate(DateUtils.toStringDate(beginDate,DateUtils.DEFAULT_DATE_FORMAT,DateUtils.DATETIME_FORMAT_NO_SYMBOL));
            }

            if (StringUtils.isNotEmpty(endDate)){
                cardRelationParam.setEndDate(DateUtils.toStringDate(endDate,DateUtils.DEFAULT_DATE_FORMAT,DateUtils.DATETIME_FORMAT_NO_SYMBOL));
            }

            if (StringUtils.isNotEmpty(orderCustName)){
                cardRelationParam.setOrderCustName(IOTEncodeUtils.encryptSM4(orderCustName,iotSm4Key, iotSm4Iv));
            }



            List<OrderCardVO> orderCardVOList = listOrderCard(cardRelationParam);

        /*String fileName = "导出订单的卡数据";
        ExportParams exportParams = new ExportParams(fileName,
                fileName, ExcelType.XSSF);
        ExcelUtils.exportExcel(orderCardVOList, OrderCardVO.class,
                fileName, exportParams, response);*/

            if (CollectionUtils.isEmpty(orderCardVOList)) {
                orderCardVOList.add(new OrderCardVO());
            } else {
                // 判断是否需要脱敏操作
                Integer desensitizationStatus = cardRelationParam.getDesensitizationStatus();
                boolean isDesensitizationStatus = desensitizationStatus != null && desensitizationStatus == 1;

                orderCardVOList.stream().forEach(orderCardVO -> {
                    Date cardDeliverTime = orderCardVO.getCardDeliverTime();
                    if (cardDeliverTime != null) {
                        orderCardVO.setCardDeliverTimeStr(DateUtils.dateToStr(cardDeliverTime, DateUtils.DEFAULT_DATETIME_FORMAT));
                    }

                    String createTime = orderCardVO.getCreateTime();
                    if (StringUtils.isNotEmpty(createTime)){
                        orderCardVO.setCreateTimeStr(DateUtils.toStringDate(createTime,DateUtils.DATETIME_FORMAT_NO_SYMBOL,DateUtils.DEFAULT_DATETIME_FORMAT));
                    }

                    String reviceAddr = contactAddr(orderCardVO.getAddr1(), orderCardVO.getAddr2(), orderCardVO.getAddr3(),
                            orderCardVO.getAddr4(), orderCardVO.getUsaddr());
                    orderCardVO.setReviceAddr(reviceAddr);


                    orderCardVO.setCustName(StringUtils.isNotEmpty(orderCardVO.getCustName()) ? IOTEncodeUtils.decryptSM4(orderCardVO.getCustName(), iotSm4Key, iotSm4Iv) : "");

                    orderCardVO.setCustCode(StringUtils.isNotEmpty(orderCardVO.getCustCode()) ? IOTEncodeUtils.decryptSM4(orderCardVO.getCustCode(), iotSm4Key, iotSm4Iv) : "");

                    orderCardVO.setContactPersonName(StringUtils.isNotEmpty(orderCardVO.getContactPersonName()) ? IOTEncodeUtils.decryptSM4(orderCardVO.getContactPersonName(), iotSm4Key, iotSm4Iv) : "");
                    orderCardVO.setContactPhone(StringUtils.isNotEmpty(orderCardVO.getContactPhone()) ? IOTEncodeUtils.decryptSM4(orderCardVO.getContactPhone(), iotSm4Key, iotSm4Iv) : "");

                    orderCardVO.setTerminalTypeName(TerminalTypeEnum.getDescByType(orderCardVO.getTerminalType()));

                    orderCardVO.setSellStatusName(SellStatusEnum.getDescByType(orderCardVO.getSellStatus()));

                    // 需要脱敏
                    if (isDesensitizationStatus){
                        // 收货地址
                        if (StringUtils.isNotEmpty(reviceAddr)){
                            orderCardVO.setReviceAddr(DesensitizationUtils.maskAddress(reviceAddr));
                        }

                        // 联系人
                        String contactPersonName = orderCardVO.getContactPersonName();
                        if (StringUtils.isNotEmpty(contactPersonName)){
                            orderCardVO.setContactPersonName(DesensitizationUtils.smartDesensitizeName(contactPersonName));
                        }

                        // 电话
                        String contactPhone = orderCardVO.getContactPhone();
                        if (StringUtils.isNotEmpty(contactPhone)){
                            orderCardVO.setContactPhone(DesensitizationUtils.replaceWithStar(contactPhone));
                        }
                    }
                });
            }
            /*List<EasyExcelDTO> easyExcelDTOList = new ArrayList<>();
            // 订单的卡数据详情
            EasyExcelDTO easyExcelDTO = setEasyExcelDTO(0, "订单的卡数据导出明细", "list",
                    orderCardVOList, null);

            easyExcelDTOList.add(easyExcelDTO);
            String excelName = "订单的卡数据导出";
            excelName = URLEncoder.encode(excelName, "UTF-8");
            ClassPathResource classPathResource = new ClassPathResource("template/order_card_export_template.xlsx");
            InputStream templateFileName = classPathResource.getInputStream();
            // 导出订单的卡数据
            EasyExcelUtils.exportManySheetExcel(response, easyExcelDTOList,
                    excelName, templateFileName,
                    BaseErrorConstant.SUCCESS.getStateCode(), BaseErrorConstant.SUCCESS.getMessage());*/

            //导出excel
            ByteArrayOutputStream bytearrayOutputStream = new ByteArrayOutputStream();
            ClassPathResource classPathResource = new ClassPathResource("template/order_card_export_template.xlsx");
            InputStream inputStream = classPathResource.getInputStream();
            try {
                log.info("卡+X订单导出准备构造excel");
                EasyExcelUtils.exportExcel2OutputStream(bytearrayOutputStream, "list", orderCardVOList, null, inputStream, 0, "订单导出", null, null, cardRelationParam.getExportPwd(),false);
                log.info("卡+X订单导出接口构造");
            } catch (Exception e) {
                log.error("卡+X订单导出构建excel出错{}", e);
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.GOODS_ORDER.code,
                        buildExportKxOrderLog(cardRelationParam),userId,ip, LogResultEnum.LOG_FAIL.code, "构建excel出错,请联系管理员");
            }

            //上传文件到对象存储
            String fileName = "kxOrderExport" + new Date().getTime() + ".xlsx";
            ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
            byteArrayUpload.setFileName(fileName);
            byteArrayUpload.setBytes(bytearrayOutputStream.toByteArray());
            BaseAnswer<UpResult> resultBaseAnswer = storageService.uploadByte(byteArrayUpload);
            String stateCode = resultBaseAnswer.getStateCode();
            String message = resultBaseAnswer.getMessage();
            if (!"00000".equals(stateCode)) {
                log.error("卡+X订单导出上传文件出错,请联系管理员,错误信息:{}",message);
                //记录日志
                logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.GOODS_ORDER.code,
                        buildExportKxOrderLog(cardRelationParam), userId,ip,LogResultEnum.LOG_FAIL.code, "卡+X订单导出上传文件出错,请联系管理员");
            }
            log.info("卡+X订单导出接口上传excel完毕");

            //消息中心提醒用户
            UpResult upResult = resultBaseAnswer.getData();
            String outerUrl = upResult.getOuterUrl();
            String fileKey = upResult.getKey();
            AddMessageParam messageParam = new AddMessageParam();
            messageParam.setModule(ModuleEnum.ORDER_MANAGE.name);
            messageParam.setContent("卡+X订单导出成功，请点击下载\n下载有效期" + serviceConfig.getOrderExportExcelExpireDays() + "天，请尽快处理");
            messageParam.setType("卡+X订单导出");
            messageParam.setUserId(userId);
            messageParam.setFileKey(fileKey);
            messageParam.setUrl(outerUrl);
            messageParam.setSource(1);
            iotFeignClient.addMessage(messageParam);

            //通过kafka发送websocket提示前端更新消息未读数
            ProducerRecord<String, byte[]> record = new ProducerRecord<>(KafkaTopic.SUPPLY_CHAIN_WEBSOCKET.getTopic(), "/message/monitor".getBytes());
            kafkaTemplate.send(record);

            //发送短信提示用户
            String phone = loginIfo4Redis.getPhone();
            if (org.apache.commons.lang.StringUtils.isNotEmpty(phone) && RegexUtil.regexPhone(phone)) {
                Msg4Request msg4Request = new Msg4Request();
                msg4Request.setTemplateId(serviceConfig.getNewMessageSms());
                List<String> mobiles = new ArrayList<>();
                mobiles.add(phone);
                msg4Request.setMobiles(mobiles);
                smsFeignClient.asySendMessage(msg4Request);
            }
            //记录订单导出日志
            logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                    OrderManageOperateEnum.GOODS_ORDER.code,
                    buildExportKxOrderLog(cardRelationParam),userId,ip, LogResultEnum.LOG_SUCESS.code, null);

        }catch (Exception e){
            e.printStackTrace();
            log.info("卡+X订单导出上传文件出错,错误信息:{}",e.getMessage());
            //记录日志
            logService.recordOperateLogAsync(ModuleEnum.ORDER_MANAGE.code,
                    OrderManageOperateEnum.GOODS_ORDER.code,
                    buildExportKxOrderLog(cardRelationParam),userId,ip, LogResultEnum.LOG_FAIL.code, "卡+X订单导出上传文件出错");
        }
    }

    @Override
    public Integer getExportOrderCardCount(CardRelationParam cardRelationParam,
                                           LoginIfo4Redis loginIfo4Redis) throws Exception{
        String beginDate = cardRelationParam.getBeginDate();
        String endDate = cardRelationParam.getEndDate();
        String orderCustName = cardRelationParam.getOrderCustName();

        if (StringUtils.isNotEmpty(beginDate)){
            cardRelationParam.setBeginDate(DateUtils.toStringDate(beginDate,DateUtils.DEFAULT_DATE_FORMAT,DateUtils.DATETIME_FORMAT_NO_SYMBOL));
        }

        if (StringUtils.isNotEmpty(endDate)){
            cardRelationParam.setEndDate(DateUtils.toStringDate(endDate,DateUtils.DEFAULT_DATE_FORMAT,DateUtils.DATETIME_FORMAT_NO_SYMBOL));
        }

        if (StringUtils.isNotEmpty(orderCustName)){
            cardRelationParam.setOrderCustName(IOTEncodeUtils.encryptSM4(orderCustName,iotSm4Key, iotSm4Iv));
        }

        // 获取老三类的空写卡订单关联数量
        CompletableFuture<Integer> nullCardCountFuture = CompletableFuture.supplyAsync(()->{
            Integer nullCardCount = cardRelationMapperExt.getNullCardCount(cardRelationParam);
            return nullCardCount;
        });

        // 获取非老三类的空写卡订单关联数量
        CompletableFuture<Integer> nullCardNewProductTypeCountFuture = CompletableFuture.supplyAsync(()->{
            Integer nullCardNewProductTypeCount = cardRelationMapperExt.getNullCardNewProductTypeCount(cardRelationParam);
            return nullCardNewProductTypeCount;
        });

        // 获取空写卡代客下单关联数量
        CompletableFuture<Integer> nullCardCustomerCountFuture = CompletableFuture.supplyAsync(()->{
            Integer nullCardCustomerCount = cardRelationMapperExt.getNullCardCustomerCount(cardRelationParam);
            return nullCardCustomerCount;
        });

        // 获取非空写卡代客下单关联数量
        CompletableFuture<Integer> notNullCardCustomerCountFuture = CompletableFuture.supplyAsync(()->{
            Integer notNullCardCustomerCount = cardRelationMapperExt.getNotNullCardCustomerCount(cardRelationParam);
            return notNullCardCustomerCount;
        });

        Integer nullCardCount = nullCardCountFuture.get();

        Integer nullCardNewProductTypeCount = nullCardNewProductTypeCountFuture.get();

        Integer nullCardCustomerCount = nullCardCustomerCountFuture.get();

        Integer notNullCardCustomerCount = notNullCardCustomerCountFuture.get();

        return nullCardCount+nullCardNewProductTypeCount+nullCardCustomerCount+notNullCardCustomerCount;

    }

    /**
     * 组装卡+X订单导出日志
     * @param cardRelationParam
     * @return
     */
    private String buildExportKxOrderLog(CardRelationParam cardRelationParam){
        String beginDate = cardRelationParam.getBeginDate();
        String endDate = cardRelationParam.getEndDate();
        String deliverBeginDate = cardRelationParam.getDeliverBeginDate();
        String deliverEndDate = cardRelationParam.getDeliverEndDate();
        String clientName = cardRelationParam.getClientName();
        String imei = cardRelationParam.getImei();
        String tempIccid = cardRelationParam.getTempIccid();
        String orderCustName = cardRelationParam.getOrderCustName();
        String skuOfferingName = cardRelationParam.getSkuOfferingName();
        String deviceVersion = cardRelationParam.getDeviceVersion();

        StringBuffer content = new StringBuffer();
        content.append("【导出】\n");
        content = content.append("下单时间");
        if (StringUtils.isNotEmpty(beginDate)) {
            content = content.append(beginDate).append("至");
        } else {
            content = content.append("--至");
        }
        if (StringUtils.isNotEmpty(endDate)) {
            content = content.append(endDate).append("\n");
        } else {
            content = content.append("--\n");
        }

        if (StringUtils.isNotEmpty(deliverBeginDate)) {
            content = content.append("交付时间").append(deliverBeginDate).append("至");
        } else {
            content = content.append("交付时间--至");
        }
        if (StringUtils.isNotEmpty(deliverEndDate)) {
            content = content.append(deliverEndDate).append("\n");
        } else {
            content = content.append("--\n");
        }

        if (StringUtils.isNotEmpty(imei)) {
            content = content.append("终端IMEI:").append(imei).append("\n");
        } else {
            content = content.append("终端IMEI:--\n");
        }
        if (StringUtils.isNotEmpty(tempIccid)) {
            content = content.append("临时iccid:").append(tempIccid).append("\n");
        } else {
            content = content.append("临时iccid:--\n");
        }
        if (StringUtils.isNotEmpty(clientName)) {
            content = content.append("客户名称:").append(clientName).append("\n");
        } else {
            content = content.append("客户名称:--\n");
        }
        if (StringUtils.isNotEmpty(orderCustName)) {
            content = content.append("集团个人客户名称:").append(orderCustName).append("\n");
        } else {
            content = content.append("集团个人客户名称:--\n");
        }
        if (StringUtils.isNotEmpty(skuOfferingName)) {
            content = content.append("规格名称:").append(skuOfferingName).append("\n");
        } else {
            content = content.append("规格名称:--\n");
        }
        if (StringUtils.isNotEmpty(deviceVersion)) {
            content = content.append("设备型号:").append(deviceVersion).append("\n");
        } else {
            content = content.append("设备型号:--\n");
        }
        return content.toString();
    }

    @Override
    public List<NotUseCardVO> listNotUseCard(NotUseCardParam notUseCardParam) {
        return cardRelationMapperExt.listNotUseCard(notUseCardParam);
    }

    @Override
    public void updateCardRelationByNeed(CardRelation cardRelation, CardRelationExample cardRelationExample) {
        cardRelationMapper.updateByExampleSelective(cardRelation, cardRelationExample);
    }

    @Override
    public void updateCardRelationById(CardRelation cardRelation) {
        cardRelationMapper.updateByPrimaryKey(cardRelation);
    }

    @Override
    public void updateCardRelationByIdSelective(CardRelation cardRelation) {
        cardRelationMapper.updateByPrimaryKeySelective(cardRelation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importCardRelationX(InputStream inputStream,
                                    LoginIfo4Redis loginIfo4Redis,
                                    HttpServletRequest request,
                                    HttpServletResponse response) throws Exception {
        /*response.setHeader("content-type", "application/octet-stream");
        if (!ExcelUtils.suffixCheck(file.getOriginalFilename())) {
            log.info("importCardRelationX 导入X终端，文件后缀校验失败");
            response.setHeader("statecode", "99999");
            response.setHeader("message", URLEncoder.encode("文件格式错误，只能是xlsx,xls类型", "UTF-8").replaceAll("\\+", "%20"));
            return;
        }*/

        Date date = new Date();

        String userId = loginIfo4Redis.getUserId();
        String phone = loginIfo4Redis.getPhone();

        String importRedisKey = "import_kx_".concat(userId);
        stringRedisTemplate.opsForValue().set(importRedisKey,importRedisKey,12,TimeUnit.HOURS);

        log.info("读取卡+X终端导入文件 begin:{}",new Date().getTime());
        ExcelImportResult<ImportNewCardRelationDTO> result = ExcelUtils.importExcel(inputStream, 0, 1, 1,
                excelNewCardRelationImportHandler, ImportNewCardRelationDTO.class);
        log.info("读取卡+X终端导入文件 end:{}",new Date().getTime());
        List<ImportNewCardRelationDTO> failList = result.getFailList();
        List<ImportNewCardRelationDTO> cardExcelImportList = result.getList();

        int successSize = cardExcelImportList.size();
        int failSize = failList.size();
        int totalCount = successSize+failSize;
        log.info("importCardRelationX ExcelUtils 导入完毕！successSize = {}, failSize = {}, totalCount = {}",successSize,failSize,totalCount);
        if (CollectionUtils.isNotEmpty(failList)) {
            log.info("importCardRelationX failList Size = {}",failSize);
            long millis = System.currentTimeMillis();
            String fileName = "导入x终端数据".concat(millis + "-fail.xls");
            String failFilePath = getKxFailFilePath(fileName);
            FileOutputStream fos = new FileOutputStream(failFilePath);
            result.getFailWorkbook().write(fos);
            fos.close();
            File downErrorFile = new File(failFilePath);
            /*response.setHeader("statecode", "99998");
            response.setHeader("message", URLEncoder.encode("失败".concat(failSize + "").concat("条，详见结果文件"), "UTF-8").replaceAll("\\+", "%20"));
            fileUtils.downloadFile(downErrorFile, fileName, request, response);*/

            // 处理校验不通过的数据文档信息
            handleImportKxResult(userId,phone,totalCount,
                    failSize,successSize,downErrorFile,millis,
                    importRedisKey,1,date);

            if (CollectionUtils.isEmpty(cardExcelImportList)
                    || successSize == 0) {
                log.info("importCardRelationX cardExcelImportList is Empty");
                //记录日志
                String content = "【导入x终端数据】\n"
                        .concat("导入时间").concat(DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATETIME_FORMAT))
                        .concat(",导入全部失败,一共").concat(failSize + "").concat("条");
                logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code,
                        GoodsManageOperateEnum.X_TERMINAL_MANAGE.code,
                        content, LogResultEnum.LOG_FAIL.code, "数据校验不通过，请参考下载文档");
            } else {
                //记录日志
                String content = "【导入x终端数据】\n"
                        .concat("导入时间").concat(DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATETIME_FORMAT))
                        .concat(",导入部分失败,一共").concat(failSize + "").concat("条");
                logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code,
                        GoodsManageOperateEnum.X_TERMINAL_MANAGE.code,
                        content, LogResultEnum.LOG_FAIL.code, "数据校验不通过，请参考下载文档");
            }

        }

        if (CollectionUtils.isEmpty(cardExcelImportList)
                && CollectionUtils.isEmpty(failList)) {
            // 处理校验不通过的数据文档信息
            handleImportKxResult(userId,phone,totalCount,
                    0,0,null,0,
                    importRedisKey,2,date);
            stringRedisTemplate.delete(importRedisKey);
            log.info("importCardRelationX cardExcelImportList is Empty, failList is Empty 成功失败列表都为空");
            response.setHeader("statecode", "99999");
            response.setHeader("message", URLEncoder.encode("导入的文件不能为空数据", "UTF-8").replaceAll("\\+", "%20"));
            return;
        }
        // 如果没有成功的数据直接返回，因为导入失败会进行信息提示，所以这里直接返回
        if (successSize == 0) {
//            // 处理校验不通过的数据文档信息
//            handleImportKxResult(userId,phone,totalCount,
//                    0,0,null,0,
//                    importRedisKey,2,date);
//            stringRedisTemplate.delete(importRedisKey);
            log.info("importCardRelationX cardExcelImportList is Empty 成功数量为0，返回");
            return;
        }

        Map<Object, Object> provinceNameCodeMap = areaDataConfig.getProvinceNameCodeMap();
        Map<Object, Object> locationNameCodeMap = areaDataConfig.getLocationNameCodeMap();
        List<CardRelation> cardRelationList = new ArrayList<>();
        // 用于存储出现重复的数据
        List<String> reCopyList = new ArrayList<>();
        String roleType = loginIfo4Redis.getRoleType();
        String userName = loginIfo4Redis.getUserName();
        String beId = "";
        String location = "";

        // 如果是主合作伙伴、从合作伙伴及合作伙伴省管账号
        boolean isPartnerLordRole = PARTNER_LORD_ROLE.equals(roleType);
        boolean isPartnerRole = PARTNER_ROLE.equals(roleType);
        boolean isPartnerProvince = BaseConstant.PARTNER_PROVINCE.equals(roleType);
        if (isPartnerLordRole || isPartnerRole || isPartnerProvince) {
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
            if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                log.info("码号查询时主合作伙伴、从合作伙伴及合作伙伴省管账号错误:{}", JSONObject.toJSONString(data4UserBaseAnswer));
                String content = "【导入x终端数据】\n"
                        .concat("导入时间").concat(DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATETIME_FORMAT))
                        .concat(",导入").concat(cardRelationList.size() + "").concat("条");
                logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code,
                        GoodsManageOperateEnum.X_TERMINAL_MANAGE.code,
                        content, LogResultEnum.LOG_FAIL.code, "主合作伙伴、从合作伙伴及合作伙伴省管账号错误");
                // 处理校验不通过的数据文档信息
                handleImportKxResult(userId,phone,totalCount,
                        0,0,null,0,
                        importRedisKey,3,date);
                stringRedisTemplate.delete(importRedisKey);
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省管账号错误");
            }

            Data4User data4User = data4UserBaseAnswer.getData();
            String companyType = data4User.getCompanyType();
            boolean isProvinceUser = StringUtils.isNotEmpty(companyType) && "2".equals(companyType);
            if (!isProvinceUser) {
                log.info("码号查询时主合作伙伴、从合作伙伴及合作伙伴非省管账号companyType:{}", companyType);
                String content = "【导入x终端数据】\n"
                        .concat("导入时间").concat(DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATETIME_FORMAT))
                        .concat(",导入").concat(cardRelationList.size() + "").concat("条");
                logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code,
                        GoodsManageOperateEnum.X_TERMINAL_MANAGE.code,
                        content, LogResultEnum.LOG_FAIL.code, "主合作伙伴、从合作伙伴及合作伙伴省非管账号");
                // 处理校验不通过的数据文档信息
                handleImportKxResult(userId,phone,totalCount,
                        0,0,null,0,
                        importRedisKey,3,date);
                stringRedisTemplate.delete(importRedisKey);
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省非管账号");
            }
            // 如果是省公司合作伙伴主账号可查看、导入省级维度的码号信息
            if (isPartnerLordRole && isProvinceUser) {
                beId = data4User.getBeIdPartner();
            }

            // 合作伙伴省管账号、合作伙伴从账号可查看、导入地市维度的码号信息
            if ((isPartnerRole || isPartnerProvince) && isProvinceUser) {
                String userLocation = data4User.getLocationIdPartner();
                if ("all".equals(userLocation)) {
                    beId = data4User.getBeIdPartner();
                } else {
                    beId = data4User.getBeIdPartner();
                    location = userLocation;
                }
            }
        }

        // 存入导入基础信息
        Map<String, CardRelationImportInfo> importInfoMap = new HashMap();
        log.info("importCardRelationX foreach BEGIN");
        for (int i = 0; i < successSize; i++) {
            ImportNewCardRelationDTO importNewCardRelationDTO = cardExcelImportList.get(i);
            String imei = ExcelUtils.replaceBlank(importNewCardRelationDTO.getImei());
            String tempIccid = ExcelUtils.replaceBlank(importNewCardRelationDTO.getTempIccid());
            String msisdn = ExcelUtils.replaceBlank(importNewCardRelationDTO.getMsisdn());
//            String deviceVersion = ExcelUtils.replaceBlank(importNewCardRelationDTO.getDeviceVersion());
            String deviceVersion = importNewCardRelationDTO.getDeviceVersion();

            CardRelation cardRelation = new CardRelation();
            BeanUtils.copyProperties(importNewCardRelationDTO, cardRelation);
            cardRelation.setImei(imei);
            cardRelation.setTempIccid(tempIccid);
            cardRelation.setMsisdn(msisdn);
            cardRelation.setDeviceVersion(deviceVersion);
            cardRelation.setId(BaseServiceUtils.getId());

            String provinceName = importNewCardRelationDTO.getProvinceName();
            cardRelation.setBeId((String) provinceNameCodeMap.get(provinceName));
            String cardRelationBeId = cardRelation.getBeId();
            if (StringUtils.isNotEmpty(beId)) {
                if (!beId.equals(cardRelationBeId)) {
                    reCopyList.add("imei:".concat(imei).concat("所导入省份").concat(provinceName).concat("不属于用户所在省份"));
                }
            }

            String cityName = importNewCardRelationDTO.getCityName();
            if (StringUtils.isNotEmpty(cityName) && !"省级".equals(cityName)) {
                cardRelation.setLocation((String) locationNameCodeMap.get(cityName));
                if (StringUtils.isNotEmpty(location)) {
                    if (!location.equals(cardRelation.getLocation())) {
                        reCopyList.add("imei:".concat(imei).concat("所导入地市").concat(cityName).concat("不属于用户所在地市"));
                    }
                }
            }

            String terminalType = TerminalTypeEnum.getTypeByDesc(importNewCardRelationDTO.getTerminalType());
            cardRelation.setTerminalType(terminalType);
            cardRelation.setSellStatus(CardStatusEnum.NOT_SELL.getType());
            cardRelation.setCreateTime(date);
            cardRelation.setUpdateTime(date);

            // 获取商城同步过来的卡信息
            if (terminalType.equals(TerminalTypeEnum.TIE_PIAN_CARD.getType())
                /*|| terminalType.equals(TerminalTypeEnum.M2M_NOT_NULL_CARD.getType())*/) {
                CardInfoParam cardInfoParam = new CardInfoParam();
                cardInfoParam.setCardType(terminalType);
                cardInfoParam.setMsisdn(msisdn);
                cardInfoParam.setBeId(beId);
//                cardInfoParam.setIccid(tempIccid);
                if (StringUtils.isNotEmpty(location)) {
                    cardInfoParam.setRegionId(location);
                }
                List<CardInfoVO> cardInfoVOList = cardInfoService.listCardInfo(cardInfoParam);
                if (CollectionUtils.isNotEmpty(cardInfoVOList)) {
                    CardInfoVO cardInfoVO = cardInfoVOList.get(0);
                    cardRelation.setTemplateId(cardInfoVO.getTemplateId());
                    cardRelation.setTemplateName(cardInfoVO.getTemplateName());
                    cardRelation.setCustCode(cardInfoVO.getCustCode());
                    cardRelation.setCustName(cardInfoVO.getCustName());
                }
            }

            // 存入终端导入基础信息
            String cardRelationDeviceVersion = cardRelation.getDeviceVersion();
            String importKey = cardRelationDeviceVersion.concat(cardRelationBeId);
            CardRelationImportInfo cardRelationImportInfo = importInfoMap.get(importKey);
            if (!Optional.ofNullable(cardRelationImportInfo).isPresent()) {
                cardRelationImportInfo = setCardRelationImportInfo(DateUtils.dateToStr(date, DateUtils.DATETIME_FORMAT_NO_SYMBOL),
                        cardRelationDeviceVersion, cardRelationBeId, userId, userName, date);
                importInfoMap.put(importKey, cardRelationImportInfo);
            } else {
                Integer importCount = cardRelationImportInfo.getImportCount()+1;
                cardRelationImportInfo.setImportCount(importCount);
            }

            cardRelation.setImportNum(cardRelationImportInfo.getImportNum());
            cardRelation.setCreatedUser(userId);
            cardRelation.setCreatedUserName(userName);
            cardRelationList.add(cardRelation);

            // 判断当前文档中是否有重复数据
//            log.info("importCardRelationX foreach文档重复数据开始{}",new Date().getTime());
            for (int j = i + 1; j < successSize; j++) {
                ImportNewCardRelationDTO newCardRelationDTO = cardExcelImportList.get(j);
                String msisdn1 = newCardRelationDTO.getMsisdn();
                String tempIccid1 = newCardRelationDTO.getTempIccid();
                String deviceVersion1 = newCardRelationDTO.getDeviceVersion();
                String terminalType1 = TerminalTypeEnum.getTypeByDesc(newCardRelationDTO.getTerminalType());
                if (imei.equals(newCardRelationDTO.getImei())) {
                    reCopyList.add("imei:".concat(imei));
                    continue;
                }

                if (StringUtils.isNotEmpty(tempIccid) && StringUtils.isNotEmpty(tempIccid1)) {
                    if (tempIccid.equals(tempIccid1)) {
                        reCopyList.add("tempIccid:".concat(tempIccid));
                        continue;
                    }
                }

                if (StringUtils.isNotEmpty(msisdn) && StringUtils.isNotEmpty(msisdn1)) {
                    if (msisdn.equals(msisdn1)) {
                        reCopyList.add("msisdn:".concat(msisdn));
                        continue;
                    }
                }

                if (StringUtils.isNotEmpty(deviceVersion) && StringUtils.isNotEmpty(terminalType)
                        && StringUtils.isNotEmpty(deviceVersion1) && StringUtils.isNotEmpty(terminalType1)) {
                    if (deviceVersion.equals(deviceVersion1) && !terminalType.equals(terminalType1)) {
                        reCopyList.add("imei:".concat(imei).concat("设备型号与设备类型在相同设备型号时设备类型不同"));
                    }
                }
            }
//            log.info("importCardRelationX foreach文档重复数据结束{}",new Date().getTime());
        }
        log.info("importCardRelationX foreach END");
        if (CollectionUtils.isNotEmpty(reCopyList)) {
            log.info("importCardRelationX reCopyList is not null");
            String errMsg = "导入数据有错误数据:".concat(reCopyList.toString());
            log.info("importCardRelationX reCopyList errMsg = {}",errMsg);
            /*response.setHeader("statecode", "99999");
            response.setHeader("message", URLEncoder.encode(errMsg, "UTF-8").replaceAll("\\+", "%20"));*/

            // 创建新的Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            // 创建一个工作表(sheet)
            Sheet sheet = workbook.createSheet("失败数据");
            Row row = sheet.createRow(0);
            Cell cell = row.createCell(0);
            cell.setCellValue(errMsg);

            long millis = System.currentTimeMillis();
            String fileName = "导入x终端数据".concat(millis + "-fail.xls");
            String failFilePath = getKxFailFilePath(fileName);
            try(FileOutputStream outputStream = new FileOutputStream(failFilePath)) {
                workbook.write(outputStream);
                outputStream.close();
            }catch (Exception e){
                e.printStackTrace();
                log.info("生成终端数据错误文件异常:{}",e.getMessage());
            }finally {
                workbook.close();
            }
            // 处理校验不通过的数据文档信息
            log.info("reCopy file path = {}",failFilePath);
            File downErrorFile = new File(failFilePath);
            handleImportKxResult(userId,phone,totalCount,
                    0,0,downErrorFile,millis,
                    importRedisKey,1,date);
            stringRedisTemplate.delete(importRedisKey);
            log.info("importCardRelationX if recopyList return");
            return;
        }
        log.info("importCardRelationX befor BatchAdd");
        /*response.setHeader("statecode", "00000");
        response.setHeader("message", URLEncoder.encode("导入成功", "UTF-8").replaceAll("\\+", "%20"));*/
        try {
            int batchSize = 5000;
            for(int i=0;i<cardRelationList.size();i+=batchSize){
                log.info("importCardRelationX batchAddCardRelation i = {}",i);
                List<CardRelation> subList = cardRelationList.subList(i, Math.min(i + batchSize, cardRelationList.size()));
                batchAddCardRelation(subList);
//                if(i+batchSize<cardRelationList.size()){
//                    batchAddCardRelation(cardRelationList.subList(i,i+batchSize));
//                }else{
//                    batchAddCardRelation(cardRelationList.subList(i,cardRelationList.size()));
//                }
            }
//            batchAddCardRelation(cardRelationList);
        }catch (Exception e){
            log.info("importCardRelationX batchAddCardRelation error:{}",e.getMessage());
            e.printStackTrace();
        }
        log.info("importCardRelationX After BatchAdd");
//        batchAddCardRelation(cardRelationList);

        // 进行导入基础信息处理
        log.info("importCardRelationX X终端导入batchInsert开始");
        if (MapUtils.isNotEmpty(importInfoMap)) {
            List<CardRelationImportInfo> cardRelationImportInfoList
                    = importInfoMap.values().stream().collect(Collectors.toList());
            try{
                int batchSize = 5000;
                for(int i=0;i<cardRelationImportInfoList.size();i+=batchSize){
                    log.info("importCardRelationX batchAddCardRelationImportInfo i = {}",i);
                    List<CardRelationImportInfo> subList = cardRelationImportInfoList.subList(i, Math.min(i + batchSize, cardRelationImportInfoList.size()));
                    cardRelationImportInfoService.batchAddCardRelationImportInfo(subList);

//                    if(i+batchSize<cardRelationImportInfoList.size()){
//                        cardRelationImportInfoService.batchAddCardRelationImportInfo(cardRelationImportInfoList.subList(i,i+batchSize));
//                    }else{
//                        cardRelationImportInfoService.batchAddCardRelationImportInfo(cardRelationImportInfoList.subList(i,cardRelationImportInfoList.size()));
//                    }
                }

            }catch (Exception e){
                log.info("importCardRelationX cardRelationImportInfoList error:{}",e.getMessage());
                e.printStackTrace();
            }
//            cardRelationImportInfoService.batchAddCardRelationImportInfo(cardRelationImportInfoList);
        }
        log.info("importCardRelationX X终端导入batchInsert结束");
        // 进行卡+X终端库存处理
        handleInventoryRelation(cardRelationList, date);
        log.info("importCardRelationX X终端导入完毕");

        //批次号是一样的，取一个即可
        String importNum = new ArrayList<>(importInfoMap.values()).get(0).getImportNum();
        //记录日志
        String content = "【导入x终端数据】\n"
                .concat("导入时间").concat(DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATETIME_FORMAT))
                .concat("导入批次").concat(importNum)
                .concat(",导入").concat(cardRelationList.size() + "").concat("条");
        logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code,
                GoodsManageOperateEnum.X_TERMINAL_MANAGE.code,
                content, LogResultEnum.LOG_SUCESS.code, null);

        // 处理校验不通过的数据文档信息
        handleImportKxResult(userId,phone,totalCount,
                0,0,null,0,
                importRedisKey,2,date);
        stringRedisTemplate.delete(importRedisKey);
    }

    @Override
    public void importCardRelationXToCard(ImportCardRelationToCardParam importCardRelationToCardParam,
                                          InputStream inputStream,
                                          DkcardxInventoryMainInfo dkcardxInventoryMainInfo,
                                          LoginIfo4Redis loginIfo4Redis,
                                          HttpServletRequest request,
                                          HttpServletResponse response) throws Exception {
        Date date = new Date();

        String userId = loginIfo4Redis.getUserId();
        String phone = loginIfo4Redis.getPhone();

        String importRedisKey = "import_kx_card_".concat(userId);
        stringRedisTemplate.opsForValue().set(importRedisKey,importRedisKey,12,TimeUnit.HOURS);

        excelNewCardRelationImportToCardHandler.setImportCardRelationToCardParam(importCardRelationToCardParam);
        ExcelImportResult<ImportNewCardRelationToCardDTO> result = ExcelUtils.importExcel(inputStream, 0, 1, 1,
                excelNewCardRelationImportToCardHandler, ImportNewCardRelationToCardDTO.class);
        List<ImportNewCardRelationToCardDTO> failList = result.getFailList();
        List<ImportNewCardRelationToCardDTO> cardExcelImportList = result.getList();
        log.info("importCardRelationXToCard ExcelUtils 导入完毕！");
        int successSize = cardExcelImportList.size();
        int failSize = failList.size();
        int totalCount = successSize+failSize;
        if (CollectionUtils.isNotEmpty(failList)) {
            log.info("importCardRelationXToCard failList Size = {}",failSize);
            long millis = System.currentTimeMillis();
            String fileName = "导入x终端数据".concat(millis + "-fail.xls");
            String failFilePath = getKxFailFilePath(fileName);
            FileOutputStream fos = new FileOutputStream(failFilePath);
            result.getFailWorkbook().write(fos);
            fos.close();
            File downErrorFile = new File(failFilePath);
            /*response.setHeader("statecode", "99998");
            response.setHeader("message", URLEncoder.encode("失败".concat(failSize + "").concat("条，详见结果文件"), "UTF-8").replaceAll("\\+", "%20"));
            fileUtils.downloadFile(downErrorFile, fileName, request, response);*/

            // 处理校验不通过的数据文档信息
            handleImportKxResult(userId,phone,totalCount,
                    failSize,successSize,downErrorFile,millis,
                    importRedisKey,1,date);

            if (CollectionUtils.isEmpty(cardExcelImportList)
                    || successSize == 0) {
                log.info("importCardRelationXToCard cardExcelImportList is Empty");
                //记录日志
                String content = "【导入x终端数据】\n"
                        .concat("导入时间").concat(DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATETIME_FORMAT))
                        .concat(",导入全部失败,一共").concat(failSize + "").concat("条");
                logService.recordOperateLogAsync(ModuleEnum.GOODS_MANAGE.code,
                        GoodsManageOperateEnum.X_TERMINAL_MANAGE.code,
                        content,userId,"", LogResultEnum.LOG_FAIL.code, "数据校验不通过，请参考下载文档");
            } else {
                //记录日志
                String content = "【导入x终端数据】\n"
                        .concat("导入时间").concat(DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATETIME_FORMAT))
                        .concat(",导入部分失败,一共").concat(failSize + "").concat("条");
                logService.recordOperateLogAsync(ModuleEnum.GOODS_MANAGE.code,
                        GoodsManageOperateEnum.X_TERMINAL_MANAGE.code,
                        content,userId,"", LogResultEnum.LOG_FAIL.code, "数据校验不通过，请参考下载文档");
            }

        }

        if (CollectionUtils.isEmpty(cardExcelImportList)
                && CollectionUtils.isEmpty(failList)) {
            // 处理校验不通过的数据文档信息
            handleImportKxResult(userId,phone,totalCount,
                    0,0,null,0,
                    importRedisKey,2,date);
            stringRedisTemplate.delete(importRedisKey);
            log.info("importCardRelationXToCard cardExcelImportList is Empty, failList is Empty 成功失败列表都为空");
            response.setHeader("statecode", "99999");
            response.setHeader("message", URLEncoder.encode("导入的文件不能为空数据", "UTF-8").replaceAll("\\+", "%20"));
            return;
        }
        // 如果没有成功的数据直接返回，因为导入失败会进行信息提示，所以这里直接返回
        if (successSize == 0) {
            // 处理校验不通过的数据文档信息
            handleImportKxResult(userId,phone,totalCount,
                    0,0,null,0,
                    importRedisKey,2,date);
            stringRedisTemplate.delete(importRedisKey);
            log.info("importCardRelationXToCard cardExcelImportList is Empty 成功数量为0，返回");
            return;
        }

        Map<Object, Object> provinceNameCodeMap = areaDataConfig.getProvinceNameCodeMap();
        Map<Object, Object> locationNameCodeMap = areaDataConfig.getLocationNameCodeMap();
        List<CardRelation> cardRelationList = new ArrayList<>();
        // 用于存储出现重复的数据
        List<String> reCopyList = new ArrayList<>();
        String roleType = loginIfo4Redis.getRoleType();
        String userName = loginIfo4Redis.getUserName();
        String beId = "";
        String location = "";

        // 如果是主合作伙伴、从合作伙伴及合作伙伴省管账号
        boolean isPartnerLordRole = PARTNER_LORD_ROLE.equals(roleType);
        boolean isPartnerRole = PARTNER_ROLE.equals(roleType);
        boolean isPartnerProvince = BaseConstant.PARTNER_PROVINCE.equals(roleType);
        if (isPartnerLordRole || isPartnerRole || isPartnerProvince) {
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
            if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                log.warn("码号查询时主合作伙伴、从合作伙伴及合作伙伴省管账号错误:{}", JSONObject.toJSONString(data4UserBaseAnswer));
                String content = "【导入x终端数据】\n"
                        .concat("导入时间").concat(DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATETIME_FORMAT))
                        .concat(",导入").concat(cardRelationList.size() + "").concat("条");
                logService.recordOperateLogAsync(ModuleEnum.GOODS_MANAGE.code,
                        GoodsManageOperateEnum.X_TERMINAL_MANAGE.code,
                        content,userId,"", LogResultEnum.LOG_FAIL.code, "主合作伙伴、从合作伙伴及合作伙伴省管账号错误");
                // 处理校验不通过的数据文档信息
                handleImportKxResult(userId,phone,totalCount,
                        0,0,null,0,
                        importRedisKey,3,date);
                stringRedisTemplate.delete(importRedisKey);
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省管账号错误");
            }

            Data4User data4User = data4UserBaseAnswer.getData();
            String companyType = data4User.getCompanyType();
            boolean isProvinceUser = StringUtils.isNotEmpty(companyType) && "2".equals(companyType);
            if (!isProvinceUser) {
                log.warn("码号查询时主合作伙伴、从合作伙伴及合作伙伴非省管账号companyType:{}", companyType);
                String content = "【导入x终端数据】\n"
                        .concat("导入时间").concat(DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATETIME_FORMAT))
                        .concat(",导入").concat(cardRelationList.size() + "").concat("条");
                logService.recordOperateLogAsync(ModuleEnum.GOODS_MANAGE.code,
                        GoodsManageOperateEnum.X_TERMINAL_MANAGE.code,
                        content,userId,"", LogResultEnum.LOG_FAIL.code, "主合作伙伴、从合作伙伴及合作伙伴省非管账号");
                // 处理校验不通过的数据文档信息
                handleImportKxResult(userId,phone,totalCount,
                        0,0,null,0,
                        importRedisKey,3,date);
                stringRedisTemplate.delete(importRedisKey);
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省非管账号");
            }
            // 如果是省公司合作伙伴主账号可查看、导入省级维度的码号信息
            if (isPartnerLordRole && isProvinceUser) {
                beId = data4User.getBeIdPartner();
            }

            // 合作伙伴省管账号、合作伙伴从账号可查看、导入地市维度的码号信息
            if ((isPartnerRole || isPartnerProvince) && isProvinceUser) {
                String userLocation = data4User.getLocationIdPartner();
                if ("all".equals(userLocation)) {
                    beId = data4User.getBeIdPartner();
                } else {
                    beId = data4User.getBeIdPartner();
                    location = userLocation;
                }
            }
        }

        // 存入导入基础信息
        Map<String, CardRelationImportInfo> importInfoMap = new HashMap();
        String deviceVersion = importCardRelationToCardParam.getDeviceVersion();
        String cardParamBeId = importCardRelationToCardParam.getBeId();
        String terminalType = importCardRelationToCardParam.getTerminalType();
        String templateId = importCardRelationToCardParam.getTemplateId();
        String custCode = importCardRelationToCardParam.getCustCode();
        log.info("importCardRelationXToCard foreach开始");
        for (int i = 0; i < successSize; i++) {
            ImportNewCardRelationToCardDTO importNewCardRelationToCardDTO = cardExcelImportList.get(i);
            String imei = ExcelUtils.replaceBlank(importNewCardRelationToCardDTO.getImei());
            String tempIccid = ExcelUtils.replaceBlank(importNewCardRelationToCardDTO.getTempIccid());
            String msisdn = ExcelUtils.replaceBlank(importNewCardRelationToCardDTO.getMsisdn());
//            String deviceVersion = ExcelUtils.replaceBlank(importNewCardRelationDTO.getDeviceVersion());


            CardRelation cardRelation = new CardRelation();
            BeanUtils.copyProperties(importNewCardRelationToCardDTO, cardRelation);
            cardRelation.setImei(imei);
            cardRelation.setTempIccid(tempIccid);
            cardRelation.setMsisdn(msisdn);
            cardRelation.setDeviceVersion(deviceVersion);
            cardRelation.setId(BaseServiceUtils.getId());


            cardRelation.setBeId(cardParamBeId);
            String cardRelationBeId = cardRelation.getBeId();
            String provinceName = areaDataConfig.getProvinceCodeNameMap().get(cardRelationBeId).toString();

            if (StringUtils.isNotEmpty(beId)) {
                if (!beId.equals(cardRelationBeId)) {
                    reCopyList.add("imei:".concat(imei).concat("所导入省份").concat(provinceName).concat("不属于用户所在省份"));
                }
            }

            String cityName = importNewCardRelationToCardDTO.getCityName();
            if (StringUtils.isNotEmpty(cityName) && !"省级".equals(cityName)) {
                cardRelation.setLocation((String) locationNameCodeMap.get(cityName));
                if (StringUtils.isNotEmpty(location)) {
                    if (!location.equals(cardRelation.getLocation())) {
                        reCopyList.add("imei:".concat(imei).concat("所导入地市").concat(cityName).concat("不属于用户所在地市"));
                    }
                }
            }


            cardRelation.setTerminalType(terminalType);
            cardRelation.setSellStatus(CardStatusEnum.NOT_SELL.getType());
            cardRelation.setCreateTime(date);
            cardRelation.setUpdateTime(date);

            // 获取商城同步过来的卡信息
            if (terminalType.equals(TerminalTypeEnum.TIE_PIAN_CARD.getType())
                /*|| terminalType.equals(TerminalTypeEnum.M2M_NOT_NULL_CARD.getType())*/) {
                CardInfoParam cardInfoParam = new CardInfoParam();
                cardInfoParam.setCardType(terminalType);
                cardInfoParam.setMsisdn(msisdn);
                cardInfoParam.setBeId(beId);
//                cardInfoParam.setIccid(tempIccid);
                if (StringUtils.isNotEmpty(location)) {
                    cardInfoParam.setRegionId(location);
                }


                List<CardInfoVO> cardInfoVOList = cardInfoService.listCardInfoCheckImport(cardInfoParam);
                if (CollectionUtils.isNotEmpty(cardInfoVOList)) {
                    CardInfoVO cardInfoVO = cardInfoVOList.get(0);
                    /*cardRelation.setTemplateId(cardInfoVO.getTemplateId());
                    cardRelation.setTemplateName(cardInfoVO.getTemplateName());
                    cardRelation.setCustCode(cardInfoVO.getCustCode());
                    cardRelation.setCustName(cardInfoVO.getCustName());*/
                    if (StringUtils.isNotEmpty(templateId)
                    && !templateId.equals(cardInfoVO.getTemplateId())){
                        reCopyList.add("imei:".concat(imei).concat("所导入模板信息与原有的卡模板信息不一致"));
                    }

                    if (StringUtils.isNotEmpty(custCode)
                            && !templateId.equals(cardInfoVO.getCustCode())){
                        reCopyList.add("imei:".concat(imei).concat("所导入卡服务商EC编码信息与原有的卡服务商EC编码信息不一致"));
                    }
                }else{
                    reCopyList.add("imei:".concat(imei).concat("所导入码号信息未从商城同步过来"));
                }
            }

            cardRelation.setTemplateId(dkcardxInventoryMainInfo.getTemplateId());
            cardRelation.setTemplateName(dkcardxInventoryMainInfo.getTemplateName());
            cardRelation.setCustCode(dkcardxInventoryMainInfo.getCustCode());
            cardRelation.setCustName(dkcardxInventoryMainInfo.getCustName());

            // 存入终端导入基础信息
            String cardRelationDeviceVersion = cardRelation.getDeviceVersion();
            String importKey = cardRelationDeviceVersion.concat(cardRelationBeId);
            CardRelationImportInfo cardRelationImportInfo = importInfoMap.get(importKey);
            if (!Optional.ofNullable(cardRelationImportInfo).isPresent()) {
                cardRelationImportInfo = setCardRelationImportInfo(DateUtils.dateToStr(date, DateUtils.DATETIME_FORMAT_NO_SYMBOL),
                        cardRelationDeviceVersion, cardRelationBeId, userId, userName, date);
                importInfoMap.put(importKey, cardRelationImportInfo);
            } else {
                Integer importCount = cardRelationImportInfo.getImportCount()+1;
                cardRelationImportInfo.setImportCount(importCount);
            }

            cardRelation.setImportNum(cardRelationImportInfo.getImportNum());
            cardRelation.setCreatedUser(userId);
            cardRelation.setCreatedUserName(userName);
            cardRelationList.add(cardRelation);

            // 判断当前文档中是否有重复数据
            log.info("importCardRelationXToCard foreach文档重复数据开始");
            for (int j = i + 1; j < successSize; j++) {
                ImportNewCardRelationToCardDTO newCardRelationToCardDTO = cardExcelImportList.get(j);
                String msisdn1 = newCardRelationToCardDTO.getMsisdn();
                String tempIccid1 = newCardRelationToCardDTO.getTempIccid();
                if (imei.equals(newCardRelationToCardDTO.getImei())) {
                    reCopyList.add("imei:".concat(imei));
                    continue;
                }

                if (StringUtils.isNotEmpty(tempIccid) && StringUtils.isNotEmpty(tempIccid1)) {
                    if (tempIccid.equals(tempIccid1)) {
                        reCopyList.add("tempIccid:".concat(tempIccid));
                        continue;
                    }
                }

                if (StringUtils.isNotEmpty(msisdn) && StringUtils.isNotEmpty(msisdn1)) {
                    if (msisdn.equals(msisdn1)) {
                        reCopyList.add("msisdn:".concat(msisdn));
                        continue;
                    }
                }

            }
            log.info("importCardRelationXToCard foreach文档重复数据结束");
        }
        log.info("importCardRelationXToCard foreach结束");
        if (CollectionUtils.isNotEmpty(reCopyList)) {
            String errMsg = "导入数据有错误数据:".concat(reCopyList.toString());
            /*response.setHeader("statecode", "99999");
            response.setHeader("message", URLEncoder.encode(errMsg, "UTF-8").replaceAll("\\+", "%20"));*/

            // 创建新的Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            // 创建一个工作表(sheet)
            Sheet sheet = workbook.createSheet("失败数据");
            Row row = sheet.createRow(0);
            Cell cell = row.createCell(0);
            cell.setCellValue(errMsg);

            long millis = System.currentTimeMillis();
            String fileName = "导入x终端数据".concat(millis + "-fail.xls");
            String failFilePath = getKxFailFilePath(fileName);
            try(FileOutputStream outputStream = new FileOutputStream(failFilePath)) {
                workbook.write(outputStream);
                outputStream.close();
            }catch (Exception e){
                log.error("生成终端数据错误文件异常:{}",e);
            }finally {
                workbook.close();
            }
            // 处理校验不通过的数据文档信息
            File downErrorFile = new File(failFilePath);
            handleImportKxResult(userId,phone,totalCount,
                    0,0,downErrorFile,millis,
                    importRedisKey,1,date);
            stringRedisTemplate.delete(importRedisKey);
            return;
        }

        /*response.setHeader("statecode", "00000");
        response.setHeader("message", URLEncoder.encode("导入成功", "UTF-8").replaceAll("\\+", "%20"));*/
        batchAddCardRelation(cardRelationList);

        // 进行导入基础信息处理
        log.info("importCardRelationXToCard X终端导入batchInsert开始");
        if (MapUtils.isNotEmpty(importInfoMap)) {
            List<CardRelationImportInfo> cardRelationImportInfoList
                    = importInfoMap.values().stream().collect(Collectors.toList());
            cardRelationImportInfoService.batchAddCardRelationImportInfo(cardRelationImportInfoList);
        }
        log.info("importCardRelationXToCard X终端导入batchInsert结束");
        // 进行卡+X终端库存处理
        handleInventoryRelation(cardRelationList, date);
        log.info("importCardRelationXToCard X终端导入完毕");

        //批次号是一样的，取一个即可
        String importNum = new ArrayList<>(importInfoMap.values()).get(0).getImportNum();
        //记录日志
        String content = "【导入x终端数据】\n"
                .concat("导入时间").concat(DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATETIME_FORMAT))
                .concat("导入批次").concat(importNum)
                .concat(",导入").concat(cardRelationList.size() + "").concat("条");
        logService.recordOperateLogAsync(ModuleEnum.GOODS_MANAGE.code,
                GoodsManageOperateEnum.X_TERMINAL_MANAGE.code,
                content,userId,"", LogResultEnum.LOG_SUCESS.code, null);

        // 处理校验不通过的数据文档信息
        handleImportKxResult(userId,phone,totalCount,
                0,0,null,0,
                importRedisKey,2,date);
        stringRedisTemplate.delete(importRedisKey);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer addCardRelationX(AddCardRelationParam addCardRelationParam,
                                       LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer baseAnswer = new BaseAnswer();

        String userId = loginIfo4Redis.getUserId();
        String userName = loginIfo4Redis.getUserName();

        String msisdn = addCardRelationParam.getMsisdn();
        String imei = addCardRelationParam.getImei();
        String deviceVersion = addCardRelationParam.getDeviceVersion();
        String terminalType = addCardRelationParam.getTerminalType();
        String tempIccid = addCardRelationParam.getTempIccid();
        String beId = addCardRelationParam.getBeId();
        String location = addCardRelationParam.getLocation();
        String custCode = addCardRelationParam.getCustCode();
        String templateId = addCardRelationParam.getTemplateId();

        Date date = new Date();

        CardRelation cardRelation = new CardRelation();
        BeanUtils.copyProperties(addCardRelationParam,cardRelation);
        cardRelation.setId(BaseServiceUtils.getId());
        cardRelation.setCreateTime(date);
        cardRelation.setUpdateTime(date);
        cardRelation.setCreatedUser(userId);
        cardRelation.setCreatedUserName(userName);
        cardRelation.setSellStatus(SellStatusEnum.NOT_SELL.getType());

        boolean isTiePianOrNotNullCard = TerminalTypeEnum.TIE_PIAN_CARD.getType().equals(terminalType)
                /*|| TerminalTypeEnum.M2M_NOT_NULL_CARD.getType().equals(terminalType)*/;
        boolean isNoCard = TerminalTypeEnum.NO_CARD.getType().equals(terminalType);
        // 是否同步到OS
        boolean isSynToOs = false;

        CardInfoParam cardInfoParam = new CardInfoParam();

        Boolean hasTerminalType = TerminalTypeEnum.containType(terminalType);
        if (!hasTerminalType){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR.getStateCode(),"终端类型不存在");
        }else {
            /**
             * 导入的终端类型为贴片卡、M2M芯片非空写卡时，应校验码号有无同步至OS，
             * 且终端类型应与商城同步至OS的码号的卡号类型一致，且导入省份与地市应
             * 与商城同步至OS的码号的省份、地市一致。若未同步或类型不一致，则导入失败
             */
            if (isTiePianOrNotNullCard) {
                if (StringUtils.isEmpty(msisdn)){
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR.getStateCode(),"贴片卡码号不能为空");
                }else {

                    cardInfoParam.setMsisdn(msisdn);
                    List<CardInfoVO> cardInfoVOList = cardInfoService.listCardInfoCheckImport(cardInfoParam);
                    if (CollectionUtils.isEmpty(cardInfoVOList)){
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR.getStateCode(),"码号没有同步至OS");
                    }else {
                        isSynToOs = true;
                        CardInfoVO cardInfoVO = cardInfoVOList.get(0);
                        if (StringUtils.isNotEmpty(templateId)
                                && !templateId.equals(cardInfoVO.getTemplateId())){
                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR.getStateCode(),"导入数据卡模板信息与原有卡模板信息不一致");
                        }

                        if (StringUtils.isNotEmpty(custCode)
                                && !custCode.equals(cardInfoVO.getCustCode())){
                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR.getStateCode(),"导入数据卡服务商EC编码信息与原有卡服务商EC编码信息不一致");
                        }

                        cardInfoParam.setCardType(terminalType);
                        cardInfoVOList = cardInfoService.listCardInfoCheckImport(cardInfoParam);
                        if (CollectionUtils.isEmpty(cardInfoVOList)){
                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR.getStateCode(),"码号的卡号类型不一致");
                        }
                    }
                }
            }

            if (StringUtils.isNotEmpty(msisdn)){
                CardRelationExample example = new CardRelationExample();
                example.createCriteria().andMsisdnEqualTo(msisdn)
                        .andDeleteTimeIsNull();
                List<CardRelation> cardRelationList = listCardRelationByNeed(example);
                if (CollectionUtils.isNotEmpty(cardRelationList)){
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR.getStateCode(),"码号已经存在");
                }
            }

            if (TerminalTypeEnum.M2M_NULL_CARD.getType().equals(terminalType)){
                if (StringUtils.isEmpty(tempIccid)){
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR.getStateCode(),"临时iccid不能为空");
                }
            }
            if (StringUtils.isNotEmpty(tempIccid)){
                CardRelationExample example = new CardRelationExample();
                example.createCriteria().andTempIccidEqualTo(tempIccid)
                        .andDeleteTimeIsNull();
                List<CardRelation> cardRelationList = listCardRelationByNeed(example);
                if (CollectionUtils.isNotEmpty(cardRelationList)){
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR.getStateCode(),"临时iccid编号已经存在");
                }
            }
        }

        // 如果终端类型为贴片卡、M2M芯片非空写卡并且同步到OS
        if (isTiePianOrNotNullCard && isSynToOs){
            cardInfoParam = new CardInfoParam();
            cardInfoParam.setMsisdn(msisdn);
            cardInfoParam.setBeId(beId);

            List<CardInfoVO> cardInfoVOList = cardInfoService.listCardInfoCheckImport(cardInfoParam);
            if (CollectionUtils.isEmpty(cardInfoVOList)){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR.getStateCode(),"码号的省份不一致");
            }
        }

        if (StringUtils.isEmpty(location)){
            if (isNoCard){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR.getStateCode(),"归属地市不能为空");
            }
        }else {
            if (!"省级".equals(location)){
                List<ProvinceCityVO.CityMall> cityMallList = provinceCityConfig.getProvinceCodeCityMap().get(beId);
                if (CollectionUtils.isNotEmpty(cityMallList)){
                    boolean provinceHasCity = false;
                    for (int i= 0;i<cityMallList.size();i++){
                        ProvinceCityVO.CityMall cityMall = cityMallList.get(i);
                        if (cityMall.getMallCode().equals(location)){
                            provinceHasCity = true;
                        }
                    }
                    if (!provinceHasCity){
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR.getStateCode(),"归属地市信息不属于该省份");
                    }
                }else{
                    log.info("{}没有地市信息",beId);
                }
            }else{
                cardRelation.setLocation(null);
            }
        }


        CardRelationExample example = new CardRelationExample();
        example.createCriteria().andImeiEqualTo(imei)
                .andDeleteTimeIsNull();
        List<CardRelation> cardRelationList = listCardRelationByNeed(example);
        if (CollectionUtils.isNotEmpty(cardRelationList)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR.getStateCode(),"终端IMEI/SN编号已经存在");
        }

        if (StringUtils.isEmpty(deviceVersion)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR.getStateCode(),"设备型号不能为空");
        }

        DkcardxInventoryMainInfoExample dkcardxInventoryMainInfoExample = new DkcardxInventoryMainInfoExample();
        DkcardxInventoryMainInfoExample.Criteria mainInfoCriteria = dkcardxInventoryMainInfoExample.createCriteria()
                .andBeIdEqualTo(beId)
                .andDeviceVersionEqualTo(deviceVersion)
                .andTerminalTypeEqualTo(terminalType);
        if (StringUtils.isEmpty(custCode)){
            mainInfoCriteria.andCustCodeIsNull();
        }else{
            mainInfoCriteria.andCustCodeEqualTo(custCode);
        }
        if (StringUtils.isEmpty(templateId)){
            mainInfoCriteria.andTemplateIdIsNull();
        }else{
            mainInfoCriteria.andTemplateIdEqualTo(templateId);
        }
        List<DkcardxInventoryMainInfo> mainInfoList = dkcardxInventoryMainInfoService.listDkcardxInventoryMainInfoByNeed(dkcardxInventoryMainInfoExample);
        if (CollectionUtils.isEmpty(mainInfoList)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR.getStateCode(),"该终端类型的设备型号没有库存信息");
        }

        if (mainInfoList.size() > 1){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR.getStateCode(),"该终端类型的设备型号大于1个库存信息");
        }

        DkcardxInventoryMainInfo dkcardxInventoryMainInfo = mainInfoList.get(0);
        cardRelation.setTemplateId(dkcardxInventoryMainInfo.getTemplateId());
        cardRelation.setTemplateName(dkcardxInventoryMainInfo.getTemplateName());
        cardRelation.setCustCode(dkcardxInventoryMainInfo.getCustCode());
        cardRelation.setCustName(dkcardxInventoryMainInfo.getCustName());

        cardRelationMapper.insertSelective(cardRelation);

        List<CardRelation> addCardRelationList = new ArrayList<>();
        addCardRelationList.add(cardRelation);

        // 进行卡+X终端库存处理
        handleInventoryRelation(addCardRelationList, date);

        return baseAnswer;
    }

    /**
     * 获取导入x终端数据失败excel存储路径
     * @param fileName
     * @return
     */
    private String getKxFailFilePath(String fileName){

        String failFile = System.getProperty("user.dir")
                .concat(File.separator).concat("execl");
        File fileC = new File(failFile);
        if (!fileC.exists()) {
            fileC.mkdirs();
        }
        String failFilePath = failFile.concat(File.separator).concat(fileName);
        return failFilePath;
    }

    /**
     * 处理导入终端失败文件
     * @param userId
     * @param phone
     */
    private void handleImportKxResult(String userId,
                                      String phone,
                                      Integer totalCount,
                                      Integer failCount,
                                      Integer successCount,
                                      File downErrorFile,
                                      long millis,
                                      String importRedisKey,
                                      Integer phoneType,
                                      Date date) throws Exception{
        String templateId = "";
        Map<String,String> message = new HashMap<>();
        message.put("importTime",DateUtils.dateToStr(date,DateUtils.DEFAULT_DATETIME_FORMAT));
        log.info("handleImportKxResult phoneType = {},phone = {},totalCount = {}, failCount = {}, successCount = {}",phoneType,phone,totalCount,failCount,successCount);
        // 失败
        if (phoneType == 1){
            FileInputStream fileInputStreamToRead = new FileInputStream(downErrorFile);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

            // 将FileOutputStream的内容转移到ByteArrayOutputStream
            byte[] buffer = new byte[1024];
            int length;
            while ((length = fileInputStreamToRead.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, length);
            }

            // 关闭流
            fileInputStreamToRead.close();

            String traceId = String.valueOf(System.currentTimeMillis());
            //上传文件到对象存储
            String fileName = "导入x终端数据".concat(millis + "-fail.xls");;
            ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
            byteArrayUpload.setFileName(fileName);
            byteArrayUpload.setBytes(byteArrayOutputStream.toByteArray());
            BaseAnswer<UpResult> resultBaseAnswer = null;
            try {
                resultBaseAnswer = storageService.uploadByte(byteArrayUpload);
            } catch (Exception e) {
                stringRedisTemplate.delete(importRedisKey);
                e.printStackTrace();
                log.info("handleImportKxResult 上传终端导入文件错误信息异常");
                throw new BusinessException("10008","上传终端导入文件错误信息异常:"+e);
            }
            if (!"00000".equals(resultBaseAnswer.getStateCode())) {
                stringRedisTemplate.delete(importRedisKey);
                log.info("handleImportKxResult 上传终端导入文件错误信息,请联系管理员 retCode = {}", resultBaseAnswer.getStateCode());
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "上传终端导入文件错误信息,请联系管理员");
            }
            log.info("{}订单导出接口上传excel完毕", traceId);
            //消息中心提醒用户
            UpResult upResult = resultBaseAnswer.getData();
            String outerUrl = upResult.getOuterUrl();
            String fileKey = upResult.getKey();
            AddMessageParam messageParam = new AddMessageParam();
            messageParam.setModule(ModuleEnum.GOODS_MANAGE.name);
            messageParam.setContent("卡+x终端导入错误信息，请点击下载");
            messageParam.setType("卡+x终端导入错误");
            messageParam.setUserId(userId);
            messageParam.setFileKey(fileKey);
            messageParam.setUrl(outerUrl);
            messageParam.setSource(1);
            iotFeignClient.addMessage(messageParam);

            //通过kafka发送websocket提示前端更新消息未读数
            ProducerRecord<String, byte[]> record = new ProducerRecord<>(KafkaTopic.SUPPLY_CHAIN_WEBSOCKET.getTopic(), "/message/monitor".getBytes());
            kafkaTemplate.send(record);

            templateId = importKxFail;
            message.put("total",totalCount+"");
            message.put("success",successCount+"");
            message.put("fail",failCount+"");
        } else if (phoneType == 2) {// 成功
            templateId = importKxSucc;
        }else {// 异常
            templateId = importKxErr;
        }

        //发送短信提示用户
        if (org.apache.commons.lang.StringUtils.isNotEmpty(phone) && RegexUtil.regexPhone(phone)) {
            Msg4Request msg4Request = new Msg4Request();
            // 短信模板
            msg4Request.setTemplateId(templateId);
            List<String> mobiles = new ArrayList<>();
            mobiles.add(phone);
            msg4Request.setMobiles(mobiles);
            msg4Request.setMessage(message);
            smsFeignClient.asySendMessage(msg4Request);
        }
        stringRedisTemplate.delete(importRedisKey);
    }

    /**
     * 进行卡+X终端库存处理
     *
     * @param cardRelationList
     * @param date
     */
    private void handleInventoryRelation(List<CardRelation> cardRelationList,
                                         Date date) {

        Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
        Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();

        // 卡+X终端库存主要信息map类
        // 该参数是这次需要入库的
        Map<String, DkcardxInventoryMainInfo> inventoryMainInfoMap = new HashMap();
        // 改参数是这次所有的库存主要信息存储
        Map<String, DkcardxInventoryMainInfo> inventoryMainInfoAllMap = new HashMap();
//        List<DkcardxInventoryMainInfo> mainInfoList = new ArrayList<>();
        // 卡+X终端库存信息详情map类
        Map<String, HashMap<String, DkcardxInventoryDetailInfo>> inventoryDetailInfoMap = new HashMap();
        cardRelationList.forEach(cardRelation -> {
            String cardBeId = cardRelation.getBeId();
            String cardLocation = cardRelation.getLocation();
            String deviceVersion = cardRelation.getDeviceVersion();
            String terminalType = cardRelation.getTerminalType();
            String custCode = cardRelation.getCustCode();
            String templateId = cardRelation.getTemplateId();
            String mainInfoKey = cardBeId.concat(deviceVersion)
                    .concat(terminalType)
                    .concat(StringUtils.isEmpty(custCode) ? "" : custCode)
                    .concat(StringUtils.isEmpty(templateId) ? "" : templateId);
            boolean hasLocation = StringUtils.isNotEmpty(cardLocation);
            String detailInfoKey = mainInfoKey.concat(hasLocation ? cardLocation : "");

            String provinceName = (String) provinceCodeNameMap.get(cardBeId);
            String cityName = (String) locationCodeNameMap.get(cardLocation);
            // 如果没有就新增库存主要信息表和详情表，有就更新详情信息
            if (inventoryMainInfoAllMap.containsKey(mainInfoKey)) {
                HashMap<String, DkcardxInventoryDetailInfo> detailInfoMap = inventoryDetailInfoMap.get(mainInfoKey);
                DkcardxInventoryDetailInfo detailInfo = detailInfoMap.get(detailInfoKey);
                // 判断是否有该库存详情
                if (Optional.ofNullable(detailInfo).isPresent()) {
                    detailInfo.setTotalInventory(detailInfo.getTotalInventory() + 1);
                    detailInfo.setCurrentInventory(detailInfo.getCurrentInventory() + 1);
                } else {
                    String mainInfoId = inventoryMainInfoAllMap.get(mainInfoKey).getId();
                    // 存储库存详情信息
                    setDkcardxInventoryDetailMap(mainInfoId,
                            cardBeId, provinceName, hasLocation, cardLocation, cityName, date,
                            detailInfoKey, mainInfoKey, inventoryDetailInfoMap);
                }
            } else {
                // 判断该信息在数据库中是否已经存在
                DkcardxInventoryMainInfoExample mainInfoExample = new DkcardxInventoryMainInfoExample();
                DkcardxInventoryMainInfoExample.Criteria mainInfoCriteria = mainInfoExample.createCriteria()
                        .andBeIdEqualTo(cardBeId)
                        .andDeviceVersionEqualTo(deviceVersion)
                        .andTerminalTypeEqualTo(terminalType);
                if (StringUtils.isNotEmpty(custCode)) {
                    mainInfoCriteria.andCustCodeEqualTo(custCode);
                } else {
                    mainInfoCriteria.andCustCodeIsNull();
                }

                if (StringUtils.isNotEmpty(templateId)) {
                    mainInfoCriteria.andTemplateIdEqualTo(templateId);
                } else {
                    mainInfoCriteria.andTemplateIdIsNull();
                }
                List<DkcardxInventoryMainInfo> mainInfoListExist = dkcardxInventoryMainInfoService.listDkcardxInventoryMainInfoByNeed(mainInfoExample);
                String mainInfoId = "";
                if (CollectionUtils.isEmpty(mainInfoListExist)) {
                    // 存储库存主要信息
                    DkcardxInventoryMainInfo dkcardxInventoryMainInfo = new DkcardxInventoryMainInfo();
                    dkcardxInventoryMainInfo.setId(BaseServiceUtils.getId());
                    mainInfoId = dkcardxInventoryMainInfo.getId();
                    dkcardxInventoryMainInfo.setBeId(cardBeId);
                    dkcardxInventoryMainInfo.setProvinceName(provinceName);
                    dkcardxInventoryMainInfo.setDeviceVersion(deviceVersion);
                    dkcardxInventoryMainInfo.setTerminalType(terminalType);
                    dkcardxInventoryMainInfo.setCustCode(custCode);
                    dkcardxInventoryMainInfo.setCustName(cardRelation.getCustName());
                    dkcardxInventoryMainInfo.setTemplateId(templateId);
                    dkcardxInventoryMainInfo.setTemplateName(cardRelation.getTemplateName());
                    dkcardxInventoryMainInfo.setCreateTime(date);
                    dkcardxInventoryMainInfo.setUpdateTime(date);
//                    mainInfoList.add(dkcardxInventoryMainInfo);
                    inventoryMainInfoMap.put(mainInfoKey, dkcardxInventoryMainInfo);
                    inventoryMainInfoAllMap.put(mainInfoKey, dkcardxInventoryMainInfo);
                } else {
                    mainInfoId = mainInfoListExist.get(0).getId();
//                    mainInfoList.add(mainInfoListExist.get(0));
                    inventoryMainInfoAllMap.put(mainInfoKey, mainInfoListExist.get(0));
                }

                // 存储库存详情信息
                setDkcardxInventoryDetailMap(mainInfoId,
                        cardBeId, provinceName, hasLocation, cardLocation, cityName, date,
                        detailInfoKey, mainInfoKey, inventoryDetailInfoMap);
            }
        });

        if (MapUtils.isNotEmpty(inventoryMainInfoMap)) {
            // 新增卡+X库存主要信息
            List inventoryMainInfoList = new ArrayList();
            inventoryMainInfoMap.values().forEach(infoList -> {
                inventoryMainInfoList.add(infoList);
            });

            dkcardxInventoryMainInfoService.batchInsertInventoryMainInfo(inventoryMainInfoList);
        }


        if (MapUtils.isNotEmpty(inventoryDetailInfoMap)) {
            // 新增卡+X库存详情信息
            List inventoryDetailInfoList = new ArrayList();
            // 存储库存主要信息id
            Set<String> inventoryMainIdSet = new HashSet<>();
            // 新增卡+X库存原子绑定信息
            List inventoryAtomInfoList = new ArrayList();
            inventoryDetailInfoMap.values().forEach(detailInfoKey -> {
                detailInfoKey.forEach((detailKey, detailInfo) -> {
                    String inventoryMainId = detailInfo.getInventoryMainId();
                    inventoryMainIdSet.add(inventoryMainId);
                    DkcardxInventoryDetailInfoExample detailInfoExample = new DkcardxInventoryDetailInfoExample();
                    String detailLocation = detailInfo.getLocation();
                    DkcardxInventoryDetailInfoExample.Criteria detailCriteria = detailInfoExample.createCriteria()
                            .andInventoryMainIdEqualTo(inventoryMainId)
                            .andBeIdEqualTo(detailInfo.getBeId());
                    if (StringUtils.isNotEmpty(detailLocation)) {
                        detailCriteria.andLocationEqualTo(detailLocation);
                    } else {
                        detailCriteria.andProvinceAliasNameEqualTo("省级");
                    }

                    List<DkcardxInventoryDetailInfo> detailInfoList
                            = dkcardxInventoryDetailInfoService.listDkcardxInventoryDetailInfoByNeed(detailInfoExample);
                    if (CollectionUtils.isEmpty(detailInfoList)) {
                        inventoryDetailInfoList.add(detailInfo);
                        DkcardxInventoryAtomInfoExample dkcardxInventoryAtomInfoExample = new DkcardxInventoryAtomInfoExample();
                        dkcardxInventoryAtomInfoExample.createCriteria()
                                .andInventoryMainIdEqualTo(inventoryMainId);
                        List<DkcardxInventoryAtomInfo> dkcardxInventoryAtomInfoList = dkcardxInventoryAtomInfoMapper.selectByExample(dkcardxInventoryAtomInfoExample);
                        if (CollectionUtils.isNotEmpty(dkcardxInventoryAtomInfoList)){
                            // 如果存在已经绑定过的原子信息，则原子信息是相同的，只需修改绑定的库存详情id
                            DkcardxInventoryAtomInfo dkcardxInventoryAtomInfo = dkcardxInventoryAtomInfoList.get(0);
                            DkcardxInventoryAtomInfo newDkcardxInventoryAtomInfo = new DkcardxInventoryAtomInfo();
                            BeanUtils.copyProperties(dkcardxInventoryAtomInfo,newDkcardxInventoryAtomInfo);
                            newDkcardxInventoryAtomInfo.setId(BaseServiceUtils.getId());
                            newDkcardxInventoryAtomInfo.setInventoryDetailId(detailInfo.getId());
                            newDkcardxInventoryAtomInfo.setAtomInventory(0L);
                            newDkcardxInventoryAtomInfo.setCreateTime(date);
                            newDkcardxInventoryAtomInfo.setUpdateTime(date);
                            inventoryAtomInfoList.add(newDkcardxInventoryAtomInfo);
                        }
                    } else {
                        // 修改库存数量
                        DkcardxInventoryDetailInfo detailInfoExist = detailInfoList.get(0);
                        DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo = new DkcardxInventoryDetailInfo();
                        dkcardxInventoryDetailInfo.setId(detailInfoExist.getId());
                        dkcardxInventoryDetailInfo.setTotalInventory(detailInfoExist.getTotalInventory() + detailInfo.getTotalInventory());
                        dkcardxInventoryDetailInfo.setCurrentInventory(detailInfoExist.getCurrentInventory() + detailInfo.getCurrentInventory());
                        dkcardxInventoryDetailInfo.setUpdateTime(date);
                        dkcardxInventoryDetailInfoService.updateDkcardxInventoryDetailInfoById(dkcardxInventoryDetailInfo);
                    }
                });
            });

            if (CollectionUtils.isNotEmpty(inventoryDetailInfoList)) {
                dkcardxInventoryDetailInfoService.batchInsertDkcardxInventoryDetailInfo(inventoryDetailInfoList);
            }

            if (CollectionUtils.isNotEmpty(inventoryAtomInfoList)){
                dkcardxInventoryAtomInfoMapper.batchInsert(inventoryAtomInfoList);
            }

            // 进行预警短信的判断
            if (CollectionUtils.isNotEmpty(inventoryMainIdSet)) {
                inventoryMainIdSet.forEach(mainId -> {
                    new Thread(() -> {
                        sendKxTerminalInventorySms(mainId);
                    });
                });
            }
        }
    }


    private void handleSellOutInventoryRelation(List<CardRelation> cardRelationList,
                                                Date date) {

        Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
        Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();

        // 卡+X终端库存主要信息map类
        // 该参数是这次需要入库的
        Map<String, DkcardxInventoryMainInfo> inventoryMainInfoMap = new HashMap();
        // 改参数是这次所有的库存主要信息存储
        Map<String, DkcardxInventoryMainInfo> inventoryMainInfoAllMap = new HashMap();
//        List<DkcardxInventoryMainInfo> mainInfoList = new ArrayList<>();
        // 卡+X终端库存信息详情map类
        Map<String, HashMap<String, DkcardxInventoryDetailInfo>> inventoryDetailInfoMap = new HashMap();
        cardRelationList.forEach(cardRelation -> {
            String cardBeId = cardRelation.getBeId();
            String cardLocation = cardRelation.getLocation();
            String deviceVersion = cardRelation.getDeviceVersion();
            String terminalType = cardRelation.getTerminalType();
            String custCode = cardRelation.getCustCode();
            String templateId = cardRelation.getTemplateId();
            String mainInfoKey = cardBeId.concat(deviceVersion)
                    .concat(terminalType)
                    .concat(StringUtils.isEmpty(custCode) ? "" : custCode)
                    .concat(StringUtils.isEmpty(templateId) ? "" : templateId);
            boolean hasLocation = StringUtils.isNotEmpty(cardLocation);
            String detailInfoKey = mainInfoKey.concat(hasLocation ? cardLocation : "");

            String provinceName = (String) provinceCodeNameMap.get(cardBeId);
            String cityName = (String) locationCodeNameMap.get(cardLocation);
            // 如果没有就新增库存主要信息表和详情表，有就更新详情信息
            if (inventoryMainInfoAllMap.containsKey(mainInfoKey)) {
                HashMap<String, DkcardxInventoryDetailInfo> detailInfoMap = inventoryDetailInfoMap.get(mainInfoKey);
                DkcardxInventoryDetailInfo detailInfo = detailInfoMap.get(detailInfoKey);
                // 判断是否有该库存详情
                if (Optional.ofNullable(detailInfo).isPresent()) {
                    /*detailInfo.setTotalInventory(detailInfo.getTotalInventory() + 1);
                    detailInfo.setCurrentInventory(detailInfo.getCurrentInventory() + 1);*/
                } else {
                    String mainInfoId = inventoryMainInfoAllMap.get(mainInfoKey).getId();
                    // 存储库存详情信息
                    setDkcardxInventoryZeroDetailMap(mainInfoId,
                            cardBeId, provinceName, hasLocation, cardLocation, cityName, date,
                            detailInfoKey, mainInfoKey, inventoryDetailInfoMap);
                }
            } else {
                // 判断该信息在数据库中是否已经存在
                DkcardxInventoryMainInfoExample mainInfoExample = new DkcardxInventoryMainInfoExample();
                DkcardxInventoryMainInfoExample.Criteria mainInfoCriteria = mainInfoExample.createCriteria()
                        .andBeIdEqualTo(cardBeId)
                        .andDeviceVersionEqualTo(deviceVersion)
                        .andTerminalTypeEqualTo(terminalType);
                if (StringUtils.isNotEmpty(custCode)) {
                    mainInfoCriteria.andCustCodeEqualTo(custCode);
                } else {
                    mainInfoCriteria.andCustCodeIsNull();
                }

                if (StringUtils.isNotEmpty(templateId)) {
                    mainInfoCriteria.andTemplateIdEqualTo(templateId);
                } else {
                    mainInfoCriteria.andTemplateIdIsNull();
                }
                List<DkcardxInventoryMainInfo> mainInfoListExist = dkcardxInventoryMainInfoService.listDkcardxInventoryMainInfoByNeed(mainInfoExample);
                String mainInfoId = "";
                if (CollectionUtils.isEmpty(mainInfoListExist)) {
                    // 存储库存主要信息
                    DkcardxInventoryMainInfo dkcardxInventoryMainInfo = new DkcardxInventoryMainInfo();
                    dkcardxInventoryMainInfo.setId(BaseServiceUtils.getId());
                    mainInfoId = dkcardxInventoryMainInfo.getId();
                    dkcardxInventoryMainInfo.setBeId(cardBeId);
                    dkcardxInventoryMainInfo.setProvinceName(provinceName);
                    dkcardxInventoryMainInfo.setDeviceVersion(deviceVersion);
                    dkcardxInventoryMainInfo.setTerminalType(terminalType);
                    dkcardxInventoryMainInfo.setCustCode(custCode);
                    dkcardxInventoryMainInfo.setCustName(cardRelation.getCustName());
                    dkcardxInventoryMainInfo.setTemplateId(templateId);
                    dkcardxInventoryMainInfo.setTemplateName(cardRelation.getTemplateName());
                    dkcardxInventoryMainInfo.setCreateTime(date);
                    dkcardxInventoryMainInfo.setUpdateTime(date);
//                    mainInfoList.add(dkcardxInventoryMainInfo);
                    inventoryMainInfoMap.put(mainInfoKey, dkcardxInventoryMainInfo);
                    inventoryMainInfoAllMap.put(mainInfoKey, dkcardxInventoryMainInfo);
                } else {
                    mainInfoId = mainInfoListExist.get(0).getId();
//                    mainInfoList.add(mainInfoListExist.get(0));
                    inventoryMainInfoAllMap.put(mainInfoKey, mainInfoListExist.get(0));
                }

                // 存储库存详情信息
                setDkcardxInventoryZeroDetailMap(mainInfoId,
                        cardBeId, provinceName, hasLocation, cardLocation, cityName, date,
                        detailInfoKey, mainInfoKey, inventoryDetailInfoMap);
            }
        });

        if (MapUtils.isNotEmpty(inventoryMainInfoMap)) {
            // 新增卡+X库存主要信息
            List inventoryMainInfoList = new ArrayList();
            inventoryMainInfoMap.values().forEach(infoList -> {
                inventoryMainInfoList.add(infoList);
            });

            dkcardxInventoryMainInfoService.batchInsertInventoryMainInfo(inventoryMainInfoList);
        }


        if (MapUtils.isNotEmpty(inventoryDetailInfoMap)) {
            // 新增卡+X库存详情信息
            List inventoryDetailInfoList = new ArrayList();
            // 存储库存主要信息id
            Set<String> inventoryMainIdSet = new HashSet<>();
            inventoryDetailInfoMap.values().forEach(detailInfoKey -> {
                detailInfoKey.forEach((detailKey, detailInfo) -> {
                    String inventoryMainId = detailInfo.getInventoryMainId();
                    inventoryMainIdSet.add(inventoryMainId);
                    DkcardxInventoryDetailInfoExample detailInfoExample = new DkcardxInventoryDetailInfoExample();
                    String detailLocation = detailInfo.getLocation();
                    DkcardxInventoryDetailInfoExample.Criteria detailCriteria = detailInfoExample.createCriteria()
                            .andInventoryMainIdEqualTo(inventoryMainId)
                            .andBeIdEqualTo(detailInfo.getBeId());
                    if (StringUtils.isNotEmpty(detailLocation)) {
                        detailCriteria.andLocationEqualTo(detailLocation);
                    } else {
                        detailCriteria.andProvinceAliasNameEqualTo("省级");
                    }

                    List<DkcardxInventoryDetailInfo> detailInfoList
                            = dkcardxInventoryDetailInfoService.listDkcardxInventoryDetailInfoByNeed(detailInfoExample);
                    if (CollectionUtils.isEmpty(detailInfoList)) {
                        inventoryDetailInfoList.add(detailInfo);
                    }/* else {
                        // 修改库存数量
                        DkcardxInventoryDetailInfo detailInfoExist = detailInfoList.get(0);
                        DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo = new DkcardxInventoryDetailInfo();
                        dkcardxInventoryDetailInfo.setId(detailInfoExist.getId());
                        dkcardxInventoryDetailInfo.setTotalInventory(detailInfoExist.getTotalInventory() + detailInfo.getTotalInventory());
                        dkcardxInventoryDetailInfo.setCurrentInventory(detailInfoExist.getCurrentInventory() + detailInfo.getCurrentInventory());
                        dkcardxInventoryDetailInfo.setUpdateTime(date);
                        dkcardxInventoryDetailInfoService.updateDkcardxInventoryDetailInfoById(dkcardxInventoryDetailInfo);
                    }*/
                });
            });

            if (CollectionUtils.isNotEmpty(inventoryDetailInfoList)) {
                dkcardxInventoryDetailInfoService.batchInsertDkcardxInventoryDetailInfo(inventoryDetailInfoList);
            }
        }
    }

    /**
     * 设置详情信息
     *
     * @param inventoryMainId
     * @param beId
     * @param provinceName
     * @param hasLocation
     * @param location
     * @param cityName
     * @param date
     * @return
     */
    private void setDkcardxInventoryDetailMap(String inventoryMainId,
                                              String beId,
                                              String provinceName,
                                              Boolean hasLocation,
                                              String location,
                                              String cityName,
                                              Date date,
                                              String detailInfoKey,
                                              String mainInfoKey,
                                              Map<String, HashMap<String, DkcardxInventoryDetailInfo>> inventoryDetailInfoMap) {
        DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo = new DkcardxInventoryDetailInfo();
        dkcardxInventoryDetailInfo.setId(BaseServiceUtils.getId());
        dkcardxInventoryDetailInfo.setInventoryMainId(inventoryMainId);
        dkcardxInventoryDetailInfo.setBeId(beId);
        dkcardxInventoryDetailInfo.setProvinceName(provinceName);
        if (hasLocation) {
            dkcardxInventoryDetailInfo.setLocation(location);
            dkcardxInventoryDetailInfo.setCityName(cityName);
        } else {
            dkcardxInventoryDetailInfo.setProvinceAliasName("省级");
        }
        dkcardxInventoryDetailInfo.setReserveQuatity(0);
        dkcardxInventoryDetailInfo.setCurrentInventory(1);
        dkcardxInventoryDetailInfo.setTotalInventory(1);
        dkcardxInventoryDetailInfo.setCreateTime(date);
        dkcardxInventoryDetailInfo.setUpdateTime(date);

        HashMap<String, DkcardxInventoryDetailInfo> detailInfoMap
                = inventoryDetailInfoMap.get(mainInfoKey) == null ? new HashMap<>() : inventoryDetailInfoMap.get(mainInfoKey);
        detailInfoMap.put(detailInfoKey, dkcardxInventoryDetailInfo);
        inventoryDetailInfoMap.put(mainInfoKey, detailInfoMap);
    }

    private void setDkcardxInventoryZeroDetailMap(String inventoryMainId,
                                                  String beId,
                                                  String provinceName,
                                                  Boolean hasLocation,
                                                  String location,
                                                  String cityName,
                                                  Date date,
                                                  String detailInfoKey,
                                                  String mainInfoKey,
                                                  Map<String, HashMap<String, DkcardxInventoryDetailInfo>> inventoryDetailInfoMap) {
        DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo = new DkcardxInventoryDetailInfo();
        dkcardxInventoryDetailInfo.setId(BaseServiceUtils.getId());
        dkcardxInventoryDetailInfo.setInventoryMainId(inventoryMainId);
        dkcardxInventoryDetailInfo.setBeId(beId);
        dkcardxInventoryDetailInfo.setProvinceName(provinceName);
        if (hasLocation) {
            dkcardxInventoryDetailInfo.setLocation(location);
            dkcardxInventoryDetailInfo.setCityName(cityName);
        } else {
            dkcardxInventoryDetailInfo.setProvinceAliasName("省级");
        }
        dkcardxInventoryDetailInfo.setReserveQuatity(0);
        dkcardxInventoryDetailInfo.setCurrentInventory(0);
        dkcardxInventoryDetailInfo.setTotalInventory(0);
        dkcardxInventoryDetailInfo.setCreateTime(date);
        dkcardxInventoryDetailInfo.setUpdateTime(date);

        HashMap<String, DkcardxInventoryDetailInfo> detailInfoMap
                = inventoryDetailInfoMap.get(mainInfoKey) == null ? new HashMap<>() : inventoryDetailInfoMap.get(mainInfoKey);
        detailInfoMap.put(detailInfoKey, dkcardxInventoryDetailInfo);
        inventoryDetailInfoMap.put(mainInfoKey, detailInfoMap);
    }

    /**
     * 设置x终端导入基础信息
     *
     * @param importNum
     * @param deviceVersion
     * @param beId
     * @param userId
     * @param date
     * @return
     */
    private CardRelationImportInfo setCardRelationImportInfo(String importNum,
                                                             String deviceVersion,
                                                             String beId,
                                                             String userId,
                                                             String userName,
                                                             Date date) {
        CardRelationImportInfo cardRelationImportInfo = new CardRelationImportInfo();
        cardRelationImportInfo.setId(BaseServiceUtils.getId());
        cardRelationImportInfo.setImportNum(importNum);
        cardRelationImportInfo.setDeviceVersion(deviceVersion);
        cardRelationImportInfo.setImportCount(1);
        cardRelationImportInfo.setBeId(beId);
        cardRelationImportInfo.setCreatedUser(userId);
        cardRelationImportInfo.setCreatedUserName(userName);
        cardRelationImportInfo.setCreateTime(date);
        return cardRelationImportInfo;
    }

    @Override
    public PageData<CardRelationXVO> pageCardInfoX(CardRelationXParam cardRelationXParam,
                                                   LoginIfo4Redis loginIfo4Redis) {
        PageData<CardRelationXVO> pageData = new PageData<>();
        Integer pageNum = cardRelationXParam.getPageNum();
        Integer pageSize = cardRelationXParam.getPageSize();

        handleCardInfoXParam(cardRelationXParam, loginIfo4Redis);

        Page<CardRelation> page = new Page<>(pageNum, pageSize);


        List<CardRelationXDTO> cardRelationXList = cardRelationMapperExt.listCardRelationX(page, cardRelationXParam);
        List<CardRelationXVO> cardRelationXVOList = null;
        if (CollectionUtils.isNotEmpty(cardRelationXList)) {
            cardRelationXVOList = handleCardRelationXList(cardRelationXList);
        }

        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(cardRelationXVOList);

        return pageData;
    }

    @Override
    public void exportCardRelationX(CardRelationXParam cardRelationXParam,
                                    LoginIfo4Redis loginIfo4Redis,
                                    HttpServletResponse response) throws Exception {
        handleCardInfoXParam(cardRelationXParam, loginIfo4Redis);

        List<CardRelationXDTO> cardRelationListX = listCardRelationX(cardRelationXParam);
        List<ExportNewCardRelationDTO> newCardRelationDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cardRelationListX)) {
            /*Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
            Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();*/
            cardRelationListX.forEach(cardRelationX -> {
                ExportNewCardRelationDTO cardRelationDTO = new ExportNewCardRelationDTO();
                BeanUtils.copyProperties(cardRelationX, cardRelationDTO);
                cardRelationDTO.setTerminalType(TerminalTypeEnum.getDescByType(cardRelationX.getTerminalType()));
                cardRelationDTO.setCreateTimeStr(DateUtils.dateToStr(cardRelationX.getCreateTime(), DateUtils.DEFAULT_DATETIME_FORMAT));
                Date cardDeliverTime = cardRelationX.getCardDeliverTime();
                if (cardDeliverTime != null) {
                    cardRelationDTO.setCardDeliverTimeStr(DateUtils.dateToStr(cardDeliverTime, DateUtils.DEFAULT_DATETIME_FORMAT));
                }
                String reviceAddr = contactAddr(cardRelationX.getAddr1(), cardRelationX.getAddr2(), cardRelationX.getAddr3(),
                        cardRelationX.getAddr4(), cardRelationX.getUsaddr());
                cardRelationDTO.setReviceAddr(reviceAddr);

                cardRelationDTO.setContactPersonName(StringUtils.isNotEmpty(cardRelationX.getContactPersonName()) ? IOTEncodeUtils.decryptSM4(cardRelationX.getContactPersonName(), iotSm4Key, iotSm4Iv) : "");
                cardRelationDTO.setContactPhone(StringUtils.isNotEmpty(cardRelationX.getContactPhone()) ? IOTEncodeUtils.decryptSM4(cardRelationX.getContactPhone(), iotSm4Key, iotSm4Iv) : "");


                cardRelationDTO.setSellStatusName(SellStatusEnum.getDescByType(cardRelationX.getSellStatus()));

                String terminalType = cardRelationDTO.getTerminalType();
                if (TerminalTypeEnum.CHA_BO_CARD.getType().equals(terminalType)) {
                    cardRelationDTO.setMsisdn(cardRelationX.getChabaMsisdn());
                }
                /*String beId = cardRelationX.getBeId();
                Object provinceName = provinceCodeNameMap.get(beId);
                if (provinceName != null) {
                    cardRelationDTO.setProvinceName((String) provinceName);
                } else {
                    cardRelationDTO.setProvinceName("");
                }

                String location = cardRelationX.getLocation();
                if (StringUtils.isNotEmpty(location)) {
                    Object cityName = locationCodeNameMap.get(location);
                    if (cityName != null) {
                        cardRelationDTO.setCityName((String) cityName);
                    } else {
                        cardRelationDTO.setCityName("");
                    }
                }*/
                newCardRelationDTOList.add(cardRelationDTO);
            });
        }

        if (CollectionUtils.isEmpty(newCardRelationDTOList)) {
            newCardRelationDTOList.add(new ExportNewCardRelationDTO());
        }

        /*String fileName = "导出x终端数据";
        ExportParams exportParams = new ExportParams(fileName,
                fileName, ExcelType.XSSF);
        ExcelUtils.exportExcel(newCardRelationDTOList, ImportNewCardRelationDTO.class,
                fileName, exportParams, response);*/

        List<EasyExcelDTO> easyExcelDTOList = new ArrayList<>();
        // x终端数据详情
        EasyExcelDTO easyExcelDTO = setEasyExcelDTO(0, "x终端数据导出明细", "list",
                newCardRelationDTOList, null);

        easyExcelDTOList.add(easyExcelDTO);
        String excelName = "x终端数据导出";
        ClassPathResource classPathResource = new ClassPathResource("template/x_card_relation_export_template.xlsx");
        InputStream templateFileName = classPathResource.getInputStream();
        // 导出x终端数据
        EasyExcelUtils.exportManySheetExcel(response, easyExcelDTOList,
                excelName, templateFileName,
                BaseErrorConstant.SUCCESS.getStateCode(), BaseErrorConstant.SUCCESS.getMessage());

        //记录日志
        String content = "【导出x终端数据】\n"
                .concat("导出时间").concat(DateUtils.dateToStr(new Date(), DateUtils.DEFAULT_DATETIME_FORMAT)
                        .concat(",导出").concat(newCardRelationDTOList.size() + "").concat("条"));
        logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code,
                GoodsManageOperateEnum.X_TERMINAL_MANAGE.code,
                content, LogResultEnum.LOG_SUCESS.code, null);
    }

    @Override
    public List<CardRelationXDTO> listCardRelationX(CardRelationXParam cardRelationXParam) {
        return cardRelationMapperExt.listCardRelationX(cardRelationXParam);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteNotSellCardRelationX(String id) {
        CardRelation cardRelation = cardRelationMapper.selectByPrimaryKey(id);
        if (!Optional.ofNullable(cardRelation).isPresent()
                || cardRelation.getDeleteTime() != null) {
            throw new BusinessException("10028", "x终端信息不存在");
        }

        String sellStatus = cardRelation.getSellStatus();
        if (!sellStatus.equals(SellStatusEnum.NOT_SELL.getType())) {
            throw new BusinessException(BaseErrorConstant.ONLY_NOT_SELL);
        }

        String beId = cardRelation.getBeId();
        String location = cardRelation.getLocation();
        boolean hasLocation = StringUtils.isNotEmpty(location);
        /*DkcardxInventoryConfigExample dkcardxInventoryConfigExample = new DkcardxInventoryConfigExample();
        List saleStatusList = new ArrayList();
        saleStatusList.add(SellStatusEnum.NOT_SELL.getType());
        saleStatusList.add(SellStatusEnum.SELLING.getType());
        DkcardxInventoryConfigExample.Criteria criteria = dkcardxInventoryConfigExample.createCriteria();
        criteria
                .andBeIdEqualTo(cardRelation.getBeId())
                .andDeviceVersionEqualTo(cardRelation.getDeviceVersion())
                .andImeiEqualTo(cardRelation.getImei())
                .andSaleStatusIn(saleStatusList);
        if (StringUtils.isNotEmpty(location)) {
            criteria.andLocationEqualTo(cardRelation.getLocation());
        }
        List<DkcardxInventoryConfig> dkcardxInventoryConfigList = dkcardxInventoryConfigService.getDkcardxInventoryConfigByNeed(dkcardxInventoryConfigExample);
        if (CollectionUtils.isNotEmpty(dkcardxInventoryConfigList)) {
            DkcardxInventoryConfig dkcardxInventoryConfig = dkcardxInventoryConfigList.get(0);
            AtomOfferingInfoExample atomOfferingInfoExample = new AtomOfferingInfoExample();
            atomOfferingInfoExample.createCriteria()
                    .andInventoryIdEqualTo(dkcardxInventoryConfig.getInventoryId());
            List<AtomOfferingInfo> atomOfferingInfoList = atomOfferingInfoMapper.selectByExample(atomOfferingInfoExample);
            if (CollectionUtils.isEmpty(atomOfferingInfoList)) {
                throw new BusinessException("10028", "该x终端信息配置的商品信息不存在");
            }

            throw new BusinessException("10028", "该终端已纳入商品库存，请先从商品库存中剔除，商品规格为#".concat(atomOfferingInfoList.get(0).getSkuCode()).concat("#"));
        }*/

        DkcardxInventoryMainInfoExample mainInfoExample = new DkcardxInventoryMainInfoExample();
        DkcardxInventoryMainInfoExample.Criteria mainInfoCriteria = mainInfoExample.createCriteria()
                .andBeIdEqualTo(beId)
                .andDeviceVersionEqualTo(cardRelation.getDeviceVersion())
                .andTerminalTypeEqualTo(cardRelation.getTerminalType());
        String custCode = cardRelation.getCustCode();
        String templateId = cardRelation.getTemplateId();
        if (StringUtils.isNotEmpty(custCode)) {
            mainInfoCriteria.andCustCodeEqualTo(custCode);
        } else {
            mainInfoCriteria.andCustCodeIsNull();
        }
        if (StringUtils.isNotEmpty(templateId)) {
            mainInfoCriteria.andTemplateIdEqualTo(templateId);
        } else {
            mainInfoCriteria.andTemplateIdIsNull();
        }
        List<DkcardxInventoryMainInfo> mainInfoList
                = dkcardxInventoryMainInfoService.listDkcardxInventoryMainInfoByNeed(mainInfoExample);
        Date date = new Date();
        if (CollectionUtils.isNotEmpty(mainInfoList)) {
            // 确定只有1条数据
            DkcardxInventoryDetailInfoExample detailInfoExample = new DkcardxInventoryDetailInfoExample();
            DkcardxInventoryDetailInfoExample.Criteria detailInfoCriteria = detailInfoExample.createCriteria()
                    .andInventoryMainIdEqualTo(mainInfoList.get(0).getId())
                    .andBeIdEqualTo(beId);
            if (hasLocation) {
                detailInfoCriteria.andLocationEqualTo(location);
            } else {
                detailInfoCriteria.andProvinceAliasNameEqualTo("省级");
            }
            List<DkcardxInventoryDetailInfo> detailInfoList
                    = dkcardxInventoryDetailInfoService.listDkcardxInventoryDetailInfoByNeed(detailInfoExample);
            if (detailInfoList.size() > 1) {
                throw new BusinessException("10028", "x终端库存详情信息不唯一");
            }
            DkcardxInventoryDetailInfo detailInfo = detailInfoList.get(0);
            DkcardxInventoryDetailInfo updateDeatailInfo = new DkcardxInventoryDetailInfo();
            updateDeatailInfo.setId(detailInfo.getId());
            updateDeatailInfo.setCurrentInventory(detailInfo.getCurrentInventory() - 1);
            updateDeatailInfo.setTotalInventory(detailInfo.getTotalInventory() - 1);
            updateDeatailInfo.setUpdateTime(date);
            dkcardxInventoryDetailInfoService.updateDkcardxInventoryDetailInfoById(updateDeatailInfo);

            // 进行库存预警判断
            new Thread(() -> {
                sendKxTerminalInventorySms(detailInfo.getInventoryMainId());
            });
        }
        CardRelation updateCardRelation = new CardRelation();
        updateCardRelation.setId(id);
        updateCardRelation.setDeleteTime(date);
        cardRelationMapper.updateByPrimaryKeySelective(updateCardRelation);
//        cardRelationMapper.deleteByPrimaryKey(id);

        String content = "【删除】\n设备型号".concat(cardRelation.getDeviceVersion()).concat(",终端IMEI/SN")
                .concat(cardRelation.getImei());
        Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
        Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
        Object provinceName = provinceCodeNameMap.get(beId);
        if (provinceName != null) {
            content = content.concat(",归属省份").concat(provinceName + "");
        } else {
            content = content.concat(",归属省份").concat("");
        }


        if (hasLocation) {
            Object cityName = locationCodeNameMap.get(location);
            if (cityName != null) {
                content = content.concat(",归属地市").concat(cityName + "");
            } else {
                content = content.concat(",归属地市").concat("");
            }
        }

        logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code,
                GoodsManageOperateEnum.X_TERMINAL_MANAGE.code,
                content, LogResultEnum.LOG_SUCESS.code, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteNotSellCardRelationX(MultipartFile file,
                                                LoginIfo4Redis loginIfo4Redis,
                                                HttpServletRequest request,
                                                HttpServletResponse response) throws Exception {

        response.setHeader("content-type", "application/octet-stream");
        if (!ExcelUtils.suffixCheck(file.getOriginalFilename())) {
            response.setHeader("statecode", "99999");
            response.setHeader("message", URLEncoder.encode("文件格式错误，只能是xlsx,xls类型", "UTF-8").replaceAll("\\+", "%20"));
            return;
        }

        ExcelImportResult<ImportDeleteNotUsedCardDTO> result = ExcelUtils.importExcel(file.getInputStream(), 0, 1,
                excelBatchDeleteNotUsedCardImportHandler, ImportDeleteNotUsedCardDTO.class);
        List<ImportDeleteNotUsedCardDTO> failList = result.getFailList();
        List<ImportDeleteNotUsedCardDTO> excelDeleteImportList = result.getList();

        int successSize = excelDeleteImportList.size();
        if (CollectionUtils.isNotEmpty(failList)) {
            int failSize = failList.size();
            long millis = System.currentTimeMillis();
            String fileName = "批量删除x终端".concat(millis + "-fail.xls");
            String failFile = System.getProperty("user.dir")
                    .concat(File.separator).concat("execl");
            File fileC = new File(failFile);
            if (!fileC.exists()) {
                fileC.mkdirs();
            }
            String failFilePath = failFile.concat(File.separator).concat(fileName);
            FileOutputStream fos = new FileOutputStream(failFilePath);
            result.getFailWorkbook().write(fos);
            fos.close();
            File downErrorFile = new File(failFilePath);
            response.setHeader("statecode", "99998");
            response.setHeader("message", URLEncoder.encode("失败".concat(failSize + "").concat("条，详见结果文件"), "UTF-8").replaceAll("\\+", "%20"));
            fileUtils.downloadFile(downErrorFile, fileName, request, response);

        }

        if (CollectionUtils.isEmpty(excelDeleteImportList)
                && CollectionUtils.isEmpty(failList)) {
            response.setHeader("statecode", "99999");
            response.setHeader("message", URLEncoder.encode("导入的文件不能为空数据", "UTF-8").replaceAll("\\+", "%20"));
            return;
        }
        // 如果没有成功的数据直接返回，因为导入失败会进行信息提示，所以这里直接返回
        if (successSize == 0) {
            return;
        }
        List<String> imeiList = excelDeleteImportList.stream().map(ImportDeleteNotUsedCardDTO::getImei).collect(Collectors.toList());
        CardRelationExample cardRelationExample = new CardRelationExample();
        CardRelationExample.Criteria cardRelationCriteria = cardRelationExample.createCriteria()
                .andImeiIn(imeiList)
                .andSellStatusEqualTo(SellStatusEnum.NOT_SELL.getType())
                .andDeleteTimeIsNull();
        handleDeleteCardRelationParam(cardRelationCriteria, loginIfo4Redis);
        List<CardRelation> cardRelationList = cardRelationMapper.selectByExample(cardRelationExample);
        if (CollectionUtils.isEmpty(cardRelationList)
                || cardRelationList.size() != imeiList.size()
                || cardRelationList.size() == 0) {
            throw new BusinessException("10116","批量删除的数据可能包含已销售或者非用户所在地市数据");
        }

        Map<String, Integer> detailInventoryMap = new HashMap();
        Set<String> mainInventorySet = new HashSet();
        Date date = new Date();
        String content = "【批量删除】\n";
        for (int i = 0; i < cardRelationList.size(); i++) {
            CardRelation cardRelation = cardRelationList.get(i);
            String id = cardRelation.getId();
            String beId = cardRelation.getBeId();
            String location = cardRelation.getLocation();
            boolean hasLocation = StringUtils.isNotEmpty(location);

            /*DkcardxInventoryMainInfoExample mainInfoExample = new DkcardxInventoryMainInfoExample();
            DkcardxInventoryMainInfoExample.Criteria mainInfoCriteria = mainInfoExample.createCriteria()
                    .andBeIdEqualTo(beId)
                    .andDeviceVersionEqualTo(cardRelation.getDeviceVersion())
                    .andTerminalTypeEqualTo(cardRelation.getTerminalType());
            String custCode = cardRelation.getCustCode();
            String templateId = cardRelation.getTemplateId();
            if (StringUtils.isNotEmpty(custCode)) {
                mainInfoCriteria.andCustCodeEqualTo(custCode);
            } else {
                mainInfoCriteria.andCustCodeIsNull();
            }
            if (StringUtils.isNotEmpty(templateId)) {
                mainInfoCriteria.andTemplateIdEqualTo(templateId);
            } else {
                mainInfoCriteria.andTemplateIdIsNull();
            }
            List<DkcardxInventoryMainInfo> mainInfoList
                    = dkcardxInventoryMainInfoService.listDkcardxInventoryMainInfoByNeed(mainInfoExample);
            if (CollectionUtils.isNotEmpty(mainInfoList)) {
                // 确定只有1条数据
                DkcardxInventoryMainInfo dkcardxInventoryMainInfo = mainInfoList.get(0);
                DkcardxInventoryDetailInfoExample detailInfoExample = new DkcardxInventoryDetailInfoExample();
                DkcardxInventoryDetailInfoExample.Criteria detailInfoCriteria = detailInfoExample.createCriteria()
                        .andInventoryMainIdEqualTo(dkcardxInventoryMainInfo.getId())
                        .andBeIdEqualTo(beId);
                if (hasLocation) {
                    detailInfoCriteria.andLocationEqualTo(location);
                } else {
                    detailInfoCriteria.andProvinceAliasNameEqualTo("省级");
                }
                List<DkcardxInventoryDetailInfo> detailInfoList
                        = dkcardxInventoryDetailInfoService.listDkcardxInventoryDetailInfoByNeed(detailInfoExample);
                if (detailInfoList.size() > 1) {
                    String deviceVersion = dkcardxInventoryMainInfo.getDeviceVersion();
                    throw new BusinessException("10028", "x终端库存设备型号为"+deviceVersion+"的详情信息不唯一");
                }
                DkcardxInventoryDetailInfo detailInfo = detailInfoList.get(0);
                String inventoryDetailId = detailInfo.getId();
                mainInventorySet.add(detailInfo.getInventoryMainId());
                Integer inventoryCount = detailInventoryMap.get(inventoryDetailId);
                if (inventoryCount == null) {
                    detailInventoryMap.put(inventoryDetailId, 1);
                } else {
                    inventoryCount = inventoryCount+1;
                    detailInventoryMap.put(inventoryDetailId, inventoryCount);
                }

                //  日志记录
                content = content.concat("设备型号").concat(cardRelation.getDeviceVersion()).concat(",终端IMEI/SN")
                        .concat(cardRelation.getImei());
                Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
                Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
                Object provinceName = provinceCodeNameMap.get(beId);
                if (provinceName != null) {
                    content = content.concat(",归属省份").concat(provinceName + "");
                } else {
                    content = content.concat(",归属省份").concat("");
                }

                if (hasLocation) {
                    Object cityName = locationCodeNameMap.get(location);
                    if (cityName != null) {
                        content = content.concat(",归属地市").concat(cityName + "");
                    } else {
                        content = content.concat(",归属地市").concat("");
                    }
                }
                content = content.concat("\n");
            }*/
            List<DkcardxInventoryMainInfo> mainInfoList
                    = handleKxInventoryAssemble(cardRelation,detailInventoryMap,mainInventorySet);
            if (CollectionUtils.isNotEmpty(mainInfoList)){
                //  日志记录
                content = content.concat("设备型号").concat(cardRelation.getDeviceVersion()).concat(",终端IMEI/SN")
                        .concat(cardRelation.getImei());
                Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
                Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
                Object provinceName = provinceCodeNameMap.get(beId);
                if (provinceName != null) {
                    content = content.concat(",归属省份").concat(provinceName + "");
                } else {
                    content = content.concat(",归属省份").concat("");
                }

                if (hasLocation) {
                    Object cityName = locationCodeNameMap.get(location);
                    if (cityName != null) {
                        content = content.concat(",归属地市").concat(cityName + "");
                    } else {
                        content = content.concat(",归属地市").concat("");
                    }
                }
                content = content.concat("\n");
            }

            // 更新设备删除状态
            CardRelation deleteCardRelation = new CardRelation();
            deleteCardRelation.setDeleteTime(date);
            deleteCardRelation.setUpdateTime(date);
            deleteCardRelation.setId(id);
            cardRelationMapper.updateByPrimaryKeySelective(deleteCardRelation);
        }

        /*if (MapUtils.isNotEmpty(detailInventoryMap)) {
            detailInventoryMap.forEach((id, count) -> {
                DkcardxInventoryDetailInfo inventoryDetailInfo = dkcardxInventoryDetailInfoService.getDkcardxInventoryDetailInfoById(id);
                if (!Optional.ofNullable(inventoryDetailInfo).isPresent()) {
                    throw new BusinessException("10028", "id为"+id+"的x终端库存详情信息不存在");
                }
                DkcardxInventoryDetailInfo updateDeatailInfo = new DkcardxInventoryDetailInfo();
                updateDeatailInfo.setId(id);
                updateDeatailInfo.setCurrentInventory(inventoryDetailInfo.getCurrentInventory() - count);
                updateDeatailInfo.setTotalInventory(inventoryDetailInfo.getTotalInventory() - count);
                updateDeatailInfo.setUpdateTime(date);
                dkcardxInventoryDetailInfoService.updateDkcardxInventoryDetailInfoById(updateDeatailInfo);
            });
        }

        for (String inventoryMainId : mainInventorySet) {
            // 进行库存预警判断
            new Thread(() -> {
                sendKxTerminalInventorySms(inventoryMainId);
            });
        }*/
        handleInventoryMap(detailInventoryMap,mainInventorySet,date);

        response.setHeader("statecode", "00000");
        response.setHeader("message", URLEncoder.encode("批量删除成功", "UTF-8").replaceAll("\\+", "%20"));

        logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code,
                GoodsManageOperateEnum.X_TERMINAL_MANAGE.code,
                content, LogResultEnum.LOG_SUCESS.code, null);
    }

    /**
     * 组装卡+X库存集合数据
     * @param cardRelation
     * @param detailInventoryMap
     * @param mainInventorySet
     */
    public List<DkcardxInventoryMainInfo> handleKxInventoryAssemble(CardRelation cardRelation,
                                           Map<String, Integer> detailInventoryMap,
                                           Set<String> mainInventorySet){
        String beId = cardRelation.getBeId();
        String location = cardRelation.getLocation();
        boolean hasLocation = StringUtils.isNotEmpty(location);

        DkcardxInventoryMainInfoExample mainInfoExample = new DkcardxInventoryMainInfoExample();
        DkcardxInventoryMainInfoExample.Criteria mainInfoCriteria = mainInfoExample.createCriteria()
                .andBeIdEqualTo(beId)
                .andDeviceVersionEqualTo(cardRelation.getDeviceVersion())
                .andTerminalTypeEqualTo(cardRelation.getTerminalType());
        String custCode = cardRelation.getCustCode();
        String templateId = cardRelation.getTemplateId();
        if (StringUtils.isNotEmpty(custCode)) {
            mainInfoCriteria.andCustCodeEqualTo(custCode);
        } else {
            mainInfoCriteria.andCustCodeIsNull();
        }
        if (StringUtils.isNotEmpty(templateId)) {
            mainInfoCriteria.andTemplateIdEqualTo(templateId);
        } else {
            mainInfoCriteria.andTemplateIdIsNull();
        }
        List<DkcardxInventoryMainInfo> mainInfoList
                = dkcardxInventoryMainInfoService.listDkcardxInventoryMainInfoByNeed(mainInfoExample);
        if (CollectionUtils.isNotEmpty(mainInfoList)) {
            // 确定只有1条数据
            DkcardxInventoryMainInfo dkcardxInventoryMainInfo = mainInfoList.get(0);
            DkcardxInventoryDetailInfoExample detailInfoExample = new DkcardxInventoryDetailInfoExample();
            DkcardxInventoryDetailInfoExample.Criteria detailInfoCriteria = detailInfoExample.createCriteria()
                    .andInventoryMainIdEqualTo(dkcardxInventoryMainInfo.getId())
                    .andBeIdEqualTo(beId);
            if (hasLocation) {
                detailInfoCriteria.andLocationEqualTo(location);
            } else {
                detailInfoCriteria.andProvinceAliasNameEqualTo("省级");
            }
            List<DkcardxInventoryDetailInfo> detailInfoList
                    = dkcardxInventoryDetailInfoService.listDkcardxInventoryDetailInfoByNeed(detailInfoExample);
            if (detailInfoList.size() > 1) {
                String deviceVersion = dkcardxInventoryMainInfo.getDeviceVersion();
                throw new BusinessException("10028", "x终端库存设备型号为"+deviceVersion+"的详情信息不唯一");
            }
            DkcardxInventoryDetailInfo detailInfo = detailInfoList.get(0);
            String inventoryDetailId = detailInfo.getId();
            mainInventorySet.add(detailInfo.getInventoryMainId());
            Integer inventoryCount = detailInventoryMap.get(inventoryDetailId);
            if (inventoryCount == null) {
                detailInventoryMap.put(inventoryDetailId, 1);
            } else {
                inventoryCount = inventoryCount+1;
                detailInventoryMap.put(inventoryDetailId, inventoryCount);
            }
        }
        return mainInfoList;
    }

    /**
     * 处理库存
     * @param detailInventoryMap
     * @param mainInventorySet
     * @param date
     */
    private void handleInventoryMap(Map<String, Integer> detailInventoryMap,
                        Set<String> mainInventorySet,
                        Date date){
        if (MapUtils.isNotEmpty(detailInventoryMap)) {
            detailInventoryMap.forEach((id, count) -> {
                DkcardxInventoryDetailInfo inventoryDetailInfo = dkcardxInventoryDetailInfoService.getDkcardxInventoryDetailInfoById(id);
                if (!Optional.ofNullable(inventoryDetailInfo).isPresent()) {
                    throw new BusinessException("10028", "id为"+id+"的x终端库存详情信息不存在");
                }
                DkcardxInventoryDetailInfo updateDeatailInfo = new DkcardxInventoryDetailInfo();
                updateDeatailInfo.setId(id);
                updateDeatailInfo.setCurrentInventory(inventoryDetailInfo.getCurrentInventory() - count);
                updateDeatailInfo.setTotalInventory(inventoryDetailInfo.getTotalInventory() - count);
                updateDeatailInfo.setUpdateTime(date);
                dkcardxInventoryDetailInfoService.updateDkcardxInventoryDetailInfoById(updateDeatailInfo);
            });
        }

        for (String inventoryMainId : mainInventorySet) {
            // 进行库存预警判断
            new Thread(() -> {
                sendKxTerminalInventorySms(inventoryMainId);
            });
        }
    }

    @Override
    public CardRelationXDetailVO getCardRelationXDetail(String id,LoginIfo4Redis loginIfo4Redis) {
        CardRelation cardRelation = cardRelationMapper.selectByPrimaryKey(id);
        if (Optional.ofNullable(cardRelation).isPresent()
                && cardRelation.getDeleteTime() == null) {
            String roleType = loginIfo4Redis.getRoleType();
            String userId = loginIfo4Redis.getUserId();
            BaseAnswer<Data4User> data4UserEn = userFeignClient.userInfoById(userId);
            if (data4UserEn == null || !SUCCESS.getStateCode().equals(data4UserEn.getStateCode())) {
                log.warn("x终端查询时主合作伙伴、从合作伙伴及合作伙伴省管账号错误:{}", JSONObject.toJSONString(data4UserEn));
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省管账号错误");
            }
            Data4User data4User = data4UserEn.getData();
            String beIdPartner = data4User.getBeIdPartner();
            String beIdEn = cardRelation.getBeId();
            String companyType = data4User.getCompanyType();
            String locationIdPartner = data4User.getLocationIdPartner();
            CardRelationXDetailVO cardRelationXDetailVO1 = new CardRelationXDetailVO();
            if ((PARTNER_ROLE.equals(roleType) || PARTNER_LORD_ROLE.equals(roleType)) && "1".equals(companyType)) {
                log.error("合作伙伴是非省公司companyType：{}", companyType);
                return cardRelationXDetailVO1;
            }
            if ((PARTNER_ROLE.equals(roleType) || PARTNER_LORD_ROLE.equals(roleType))) {
                if (StringUtils.isNotEmpty(beIdEn) && !beIdEn.equals(beIdPartner)) {
                    log.error("合作伙伴是省不对应beIdEn：{}", beIdEn);
                    return cardRelationXDetailVO1;
                }
            }
        }

        if (Optional.ofNullable(cardRelation).isPresent()) {
            Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
            Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
            CardRelationXDetailVO cardRelationXDetailVO = new CardRelationXDetailVO();
            BeanUtils.copyProperties(cardRelation, cardRelationXDetailVO);
            cardRelationXDetailVO.setTerminalTypeName(TerminalTypeEnum.getDescByType(cardRelationXDetailVO.getTerminalType()));
            cardRelationXDetailVO.setSellStatusName(SellStatusEnum.getDescByType(cardRelationXDetailVO.getSellStatus()));

            String terminalType = cardRelation.getTerminalType();
            if (TerminalTypeEnum.CHA_BO_CARD.getType().equals(terminalType)) {
                cardRelationXDetailVO.setMsisdn(cardRelation.getChabaMsisdn());
            }


            // 商品订单信息
            String orderId = cardRelation.getOrderId();
            if (StringUtils.isNotEmpty(orderId)) {
                Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(orderId);

                String orderType = order2cInfo.getOrderType();
                if (TerminalTypeEnum.M2M_NULL_CARD.getType().equals(terminalType)
                        && (/*"00".equals(orderType) || "02".equals(orderType)*/toCustomerOrderType.contains(orderType))) {
                    List<SkuMsisdnRelation> skuMsisdnRelationList = skuMsisdnRelationMapper.selectByExample(
                            new SkuMsisdnRelationExample().createCriteria()
                                    .andOrderIdEqualTo(orderId)
                                    .example()
                    );
                    String msisdn = skuMsisdnRelationList.stream().map(SkuMsisdnRelation::getMsisdn).filter(field -> !field.isEmpty())
                            .collect(Collectors.joining(","));
                    cardRelationXDetailVO.setMsisdn(msisdn);
                }

                String spuOfferingClass = order2cInfo.getSpuOfferingClass();
                CardOrderDetailVO cardOrderDeatailVO = new CardOrderDetailVO();
                cardOrderDeatailVO.setOrderId(order2cInfo.getOrderId());
                cardOrderDeatailVO.setSpuCode(order2cInfo.getSpuOfferingCode());
                cardOrderDeatailVO.setSpuOfferingVersion(order2cInfo.getSpuOfferingVersion());
                cardOrderDeatailVO.setSpuOfferingClass(spuOfferingClass);
                cardOrderDeatailVO.setSpuOfferingClassName(SPUOfferingClassEnum.getDescribe(spuOfferingClass));
                cardOrderDeatailVO.setReceiverPhone(IOTEncodeUtils.decryptSM4(order2cInfo.getContactPhone(), iotSm4Key, iotSm4Iv));
                cardOrderDeatailVO.setReceiverName(IOTEncodeUtils.decryptSM4(order2cInfo.getContactPersonName(), iotSm4Key, iotSm4Iv));
                cardOrderDeatailVO.setReceiverAddress(contactAddr(order2cInfo.getAddr1(), order2cInfo.getAddr2(), order2cInfo.getAddr3(),
                        order2cInfo.getAddr4(), order2cInfo.getUsaddr()));
                cardOrderDeatailVO.setTotalPrice(IOTEncodeUtils.decryptSM4(order2cInfo.getTotalPrice(), iotSm4Key, iotSm4Iv));

                SpuOfferingInfoHistoryExample spuOfferingInfoHistoryExample = new SpuOfferingInfoHistoryExample();
                SpuOfferingInfoHistoryExample.Criteria spuCriteria = spuOfferingInfoHistoryExample.createCriteria();
                spuCriteria.andOfferingCodeEqualTo(order2cInfo.getSpuOfferingCode());
                spuCriteria.andSpuOfferingVersionEqualTo(order2cInfo.getSpuOfferingVersion());
                List<SpuOfferingInfoHistory> spuOfferingInfos = spuOfferingInfoHistoryMapper.selectByExample(spuOfferingInfoHistoryExample);
                cardOrderDeatailVO.setSpuName(spuOfferingInfos.get(0).getOfferingName());
                cardRelationXDetailVO.setCardOrderDetailVO(cardOrderDeatailVO);

                Order2cAtomInfoExample atomInfoExample = new Order2cAtomInfoExample();
                Order2cAtomInfoExample.Criteria atomCriteria = atomInfoExample.createCriteria();
                atomCriteria.andOrderIdEqualTo(order2cInfo.getOrderId());
                List<Order2cAtomInfo> atomInfos = order2cAtomInfoMapper.selectByExample(atomInfoExample);
                if (CollectionUtils.isNotEmpty(atomInfos)) {
                    List<CardOrderSkuDetailVO> atomOrderInfoVOS = atomInfos.stream().map(item -> {
                        String atomOfferingClass = item.getAtomOfferingClass();
                        CardOrderSkuDetailVO cardOrderSkuDetailVO = new CardOrderSkuDetailVO();
                        cardOrderSkuDetailVO.setAtomName(item.getAtomOfferingName());
                        cardOrderSkuDetailVO.setAtomOfferingClass(atomOfferingClass);
                        cardOrderSkuDetailVO.setAtomOfferingClassName(AtomOfferingClassEnum.getDescribe(atomOfferingClass));
                        cardOrderSkuDetailVO.setQuantity(item.getAtomQuantity());
                        cardOrderSkuDetailVO.setModel(item.getModel());
                        cardOrderSkuDetailVO.setSkuName(item.getSkuOfferingName());
                        cardOrderSkuDetailVO.setSkuCode(item.getSkuOfferingCode());
                        cardOrderSkuDetailVO.setAtomOfferingVersion(item.getAtomOfferingVersion());
                        cardOrderSkuDetailVO.setSkuOfferingVersion(item.getSkuOfferingVersion());

                        if ("O".equals(atomOfferingClass)
                                || "D".equals(atomOfferingClass)
                                || "P".equals(atomOfferingClass)
                                || "F".equals(atomOfferingClass)
                        ) {
                            cardOrderSkuDetailVO.setCooperatorName(item.getSupplierName());
                        } else {
                            // 获取订单关联的合作伙伴信息
                            OrderCooperatorInfoByGroupParam orderCooperatorInfoByGroupParam = new OrderCooperatorInfoByGroupParam();
                            orderCooperatorInfoByGroupParam.setAtomOrderId(item.getId());
                            orderCooperatorInfoByGroupParam.setOrderId(item.getOrderId());
                            List<OrderCooperatorInfoByGroupDTO> orderCooperatorInfoList = orderCooperatorRelationService.listCooperatorInfoByGroup(orderCooperatorInfoByGroupParam);
                            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(orderCooperatorInfoList)){
                                OrderCooperatorInfoByGroupDTO orderCooperatorInfo = orderCooperatorInfoList.get(0);
                                cardOrderSkuDetailVO.setCooperatorName(orderCooperatorInfo.getPartnerName());
                            }

                            /*String cooperatorId = item.getCooperatorId();
                            if (StringUtils.isNotEmpty(cooperatorId)) {
                                BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(cooperatorId);
                                if (data4UserBaseAnswer != null && data4UserBaseAnswer.getData() != null) {
                                    cardOrderSkuDetailVO.setCooperatorName(data4UserBaseAnswer.getData().getPartnerName());
                                }
                            }*/
                        }
                        return cardOrderSkuDetailVO;
                    }).collect(Collectors.toList());
                    cardOrderDeatailVO.setAtomOrders(atomOrderInfoVOS);
                }

                // 售后服务订单信息
                AfterMarketOrder2cInfoExample afterMarketOrder2cInfoExample = new AfterMarketOrder2cInfoExample();
                afterMarketOrder2cInfoExample.createCriteria()
                        .andOfferingOrderIdEqualTo(orderId);
                afterMarketOrder2cInfoExample.orderBy("create_time desc");
                List<AfterMarketOrder2cInfo> afterMarketOrder2cInfoList = afterMarketOrder2cInfoMapper.selectByExample(afterMarketOrder2cInfoExample);
                if (CollectionUtils.isNotEmpty(afterMarketOrder2cInfoList)) {
                    AfterMarketOrder2cInfo afterMarketOrder2cInfo = afterMarketOrder2cInfoList.get(0);
                    String serviceOrderId = afterMarketOrder2cInfo.getServiceOrderId();
                    AfterMarketOrder2cOfferingInfoExample offeringInfoExample = new AfterMarketOrder2cOfferingInfoExample();
                    AfterMarketOrder2cOfferingInfoExample.Criteria offeringInfoCriteria = offeringInfoExample.createCriteria();
                    offeringInfoCriteria.andServiceOrderIdEqualTo(serviceOrderId);
                    List<AfterMarketOrder2cOfferingInfo> order2cOfferingInfoList = afterMarketOrder2cOfferingInfoMapper.selectByExample(offeringInfoExample);
                    if (CollectionUtils.isNotEmpty(order2cOfferingInfoList)) {
                        List<CardAfterMarketOrderDetailVO> cardAfterMarketOrderDetailVOList;
                        cardAfterMarketOrderDetailVOList = order2cOfferingInfoList.stream().map(item -> {
                            CardAfterMarketOrderDetailVO cardAfterMarketOrderDetailVO = new CardAfterMarketOrderDetailVO();
                            BeanUtils.copyProperties(item, cardAfterMarketOrderDetailVO);
                            if (StringUtils.isEmpty(cardAfterMarketOrderDetailVO.getAdminCooperatorName())
                                    && StringUtils.isNotEmpty(cardAfterMarketOrderDetailVO.getAdminCooperatorId())) {
                                BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(cardAfterMarketOrderDetailVO.getAdminCooperatorId());
                                if (data4UserBaseAnswer != null && data4UserBaseAnswer.getData() != null) {
                                    cardAfterMarketOrderDetailVO.setAdminCooperatorName(data4UserBaseAnswer.getData().getPartnerName());
                                }
                            }
                            return cardAfterMarketOrderDetailVO;
                        }).collect(Collectors.toList());
                        cardRelationXDetailVO.setCardAfterMarketOrderDetailVO(cardAfterMarketOrderDetailVOList);
                    }
                }
            }


            String beId = cardRelationXDetailVO.getBeId();
            Object provinceName = provinceCodeNameMap.get(beId);
            // 记录日志
            String content = "【详情】\n"
                    .concat("终端类型").concat(cardRelationXDetailVO.getTerminalTypeName())
                    .concat(",终端IMEI/SN").concat(cardRelationXDetailVO.getImei());
            if (provinceName != null) {
                cardRelationXDetailVO.setProvinceName((String) provinceName);
                content = content.concat(",归属省份").concat(cardRelationXDetailVO.getProvinceName());
            } else {
                cardRelationXDetailVO.setProvinceName("");
                content = content.concat(",归属省份").concat("");
            }

            String location = cardRelationXDetailVO.getLocation();
            if (StringUtils.isNotEmpty(location)) {
                Object cityName = locationCodeNameMap.get(location);
                if (cityName != null) {
                    cardRelationXDetailVO.setCityName((String) cityName);
                    content = content.concat(",归属地市").concat(cardRelationXDetailVO.getCityName());
                } else {
                    cardRelationXDetailVO.setCityName("");
                    content = content.concat(",归属地市").concat("");
                }
            }

            logService.recordOperateLog(ModuleEnum.GOODS_MANAGE.code,
                    GoodsManageOperateEnum.X_TERMINAL_MANAGE.code,
                    content, LogResultEnum.LOG_SUCESS.code, null);

            return cardRelationXDetailVO;
        }
        return null;
    }

    @Override
    public BaseAnswer<List<OrderProductCardRelationListVO>> orderProductCardRelationList(OrderProductCardRelationListParam param) {
//        String atomOrderId = param.getAtomOrderId();
        String orderId = param.getOrderId();
        String searchWord = param.getSearchWord();
        /*Order2cAtomInfo order2cAtomInfo = order2cAtomInfoMapper.selectByPrimaryKey(atomOrderId);
        if (order2cAtomInfo == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "原子订单不存在");
        }*/
        Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(orderId);
        if (order2cInfo == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订单不存在");
        }

        List<OrderInfoDetailHandle> orderInfoDetailHandleList = orderHandleMapper.selectOrderDetailByOrderId(orderId);
        if (CollectionUtils.isEmpty(orderInfoDetailHandleList)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "原子订单不存在");
        }

        OrderInfoDetailHandle orderInfoDetailHandle = orderInfoDetailHandleList.get(0);

        AtomOfferingInfoExample atomExample = new AtomOfferingInfoExample().createCriteria()
                .andSpuCodeEqualTo(orderInfoDetailHandle.getSpuOfferingCode())
                .andSkuCodeEqualTo(orderInfoDetailHandle.getSkuOfferingCode())
                .andOfferingCodeEqualTo(orderInfoDetailHandle.getAtomOfferingCode())
                .example();
        List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper.selectByExample(atomExample);
        if (CollectionUtils.isEmpty(atomOfferingInfos)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "原子商品不存在");
        }
        AtomOfferingInfo atomOfferingInfo = atomOfferingInfos.get(0);
        //获取预占的区域和数量信息，根据区域去拉取对应区域可选的x终端
        String redisData = stringRedisTemplate.opsForValue().get(REDIS_COMMIT_INVENTORY_AREA + orderId + "_" + atomOfferingInfo.getId());
        if (StringUtils.isEmpty(redisData)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "预占仓库信息不存在");
        }
        List<InventoryAreaDTO> inventoryAreaDTOList = JSON.parseArray(redisData, InventoryAreaDTO.class);
        String beId = null;
        String location = null;
        //预占区域最多2条，一条省级，一条是提单人归属的市级
        /*
        三种情况：1.只预占省级仓库： beId有值，location无值
                 2.只预占市级仓库： beId无值，location有值
                 3.同时预占省级和市级仓库： beId有值，location有值
         */
        for (InventoryAreaDTO inventoryAreaDTO : inventoryAreaDTOList) {
            String areaCode = inventoryAreaDTO.getAreaCode();
            if (areaCode.length() == 3) {
                beId = areaCode;
            } else if (areaCode.length() == 4) {
                location = areaCode;
            }
        }
        String atomOfferingCode = orderInfoDetailHandle.getAtomOfferingCode();
        String spuOfferingCode = orderInfoDetailHandle.getSpuOfferingCode();
        String skuOfferingCode = orderInfoDetailHandle.getSkuOfferingCode();
        //根据原子商品关联的X终端仓库去查询
        List<OrderProductCardRelationListDO> doList = this.orderProductCardRelationList(spuOfferingCode, skuOfferingCode, atomOfferingCode, beId, location, searchWord);
        if (CollectionUtils.isEmpty(doList)) {
            return BaseAnswer.success(new ArrayList<>());
        }
        List<OrderProductCardRelationListVO> collect = doList.stream().map(d -> {
            OrderProductCardRelationListVO vo = new OrderProductCardRelationListVO();
            BeanUtils.copyProperties(d, vo);
            return vo;
        }).collect(Collectors.toList());
        return BaseAnswer.success(collect);
    }

    @Override
    public List<OrderProductCardRelationListDO> orderProductCardRelationList(String spuOfferingCode, String skuOfferingCode, String atomOfferingCode,
                                                                             String reserveBeId, String reserveLocation, String searchWord) {
        return cardRelationMapperExt.orderProductCardRelationList(spuOfferingCode, skuOfferingCode, atomOfferingCode, reserveBeId, reserveLocation, searchWord);
    }

    @Override
    public List<OrderProductCardRelationListDO> cardRelationDeliverList(String spuOfferingCode, String skuOfferingCode, String atomOfferingCode,
                                                                             String reserveBeId, String reserveLocation, String imei,
                                                                        String deviceVersion) {
        return cardRelationMapperExt.cardRelationDeliverList(spuOfferingCode, skuOfferingCode, atomOfferingCode,
                reserveBeId, reserveLocation, imei,deviceVersion);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer deliverCard(DeliverCardParam param, Boolean addLog,
                                  String cardType,LoginIfo4Redis loginIfo4Redis) {
        Date now = new Date();
        String userId = loginIfo4Redis.getUserId();
        List<DeliverCardParam.AtomDeliverItem> atomList = param.getAtomList();
        List<SkuMsisdnRelation> skuMsisdnRelationList = new ArrayList<>();
        List<String> atomIdList = new ArrayList<>();
        Order2cInfo order2cInfo = null;
        String skuCode ="";
        Long skuQuantity = null;
        List<String> atomOrderIdList = new ArrayList<>();
        //含卡的终端码号信息，用于反馈商城，不含卡终端无需反馈
        List<DeliverCardParam.CardItem> hasCardList = new ArrayList<>();
        //保存原子订单id及其对应的码号校验结果（该原子订单的码号有任何一个校验失败就返回其所有码号校验结果）
        List<AtomOrderMsisdnCheckFailVO> atomOrderMsisdnCheckFailVOList = new ArrayList<>();
        Map<String,List<String>> orderIdMap = new HashMap<>();
        Boolean hasAnyAtomOrderMsisdnCheckFail = false;
        for (DeliverCardParam.AtomDeliverItem atomDeliverItem : atomList) {
            /*String atomOrderId = atomDeliverItem.getAtomOrderId();
            Order2cAtomInfo order2cAtomInfo = order2cAtomInfoMapper.selectByPrimaryKey(atomOrderId);
            if (order2cAtomInfo == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "原子订单不存在");
            }*/

            String orderId = atomDeliverItem.getOrderId();
            List<OrderInfoDetailHandle> orderInfoDetailHandleList = orderHandleMapper.selectOrderDetailByOrderId(orderId);
            if (CollectionUtils.isEmpty(orderInfoDetailHandleList)){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订单不存在");
            }
            OrderInfoDetailHandle orderInfoDetailHandle = orderInfoDetailHandleList.get(0);
            Order2cAtomInfoExample order2cAtomInfoExample = new Order2cAtomInfoExample();
            order2cAtomInfoExample.createCriteria()
                            .andIdEqualTo(orderInfoDetailHandle.getId());
            List<Order2cAtomInfo> order2cAtomInfoList = order2cAtomInfoMapper.selectByExample(order2cAtomInfoExample);
            if (CollectionUtils.isEmpty(order2cAtomInfoList)){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "原子订单不存在");
            }

            for (int i = 0; i < order2cAtomInfoList.size(); i++) {
                Order2cAtomInfo order2cAtomInfo = order2cAtomInfoList.get(i);
                String atomOrderId = order2cAtomInfo.getId();
                atomDeliverItem.setOrderId(order2cAtomInfo.getOrderId());
                atomOrderIdList.add(atomOrderId);
                skuQuantity = order2cAtomInfo.getSkuQuantity();
                Integer orderStatus = order2cAtomInfo.getOrderStatus();
                String orderType = order2cAtomInfo.getOrderType();
                // 5类X产品类型支持个人客户预付费订购，包含：“合同履约”、“One NET独立服务”、
                // “标准产品(One NET）”、“One Park独立服务”和“标准产品（One Park）”
                if (OrderStatusInnerEnum.VALET_WAIT_DELIVER.getStatus().intValue() != orderStatus.intValue()) {
                    if ("01".equals(orderType)) {
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "只有 " + OrderStatusInnerEnum.VALET_WAIT_DELIVER.getMessage() + " 状态的卡+X预付费订单才能交付");
                    } else {
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "只有 " + OrderStatusInnerEnum.VALET_WAIT_DELIVER.getMessage() + " 状态的卡+X代客下单订单才能交付");
                    }
                }

                if(order2cInfo == null){
                    order2cInfo = order2cInfoMapper.selectByPrimaryKey(orderId);
                    if (order2cInfo == null) {
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订单不存在");
                    }
                }

                AtomOfferingInfoHistoryExample atomHistoryExample = new AtomOfferingInfoHistoryExample().createCriteria()
                        .andSpuCodeEqualTo(order2cAtomInfo.getSpuOfferingCode())
                        .andSpuOfferingVersionEqualTo(order2cAtomInfo.getSpuOfferingVersion())
                        .andSkuCodeEqualTo(order2cAtomInfo.getSkuOfferingCode())
                        .andSkuOfferingVersionEqualTo(order2cAtomInfo.getSkuOfferingVersion())
                        .andOfferingCodeEqualTo(order2cAtomInfo.getAtomOfferingCode())
                        .andAtomOfferingVersionEqualTo(order2cAtomInfo.getAtomOfferingVersion())
                        .example();
                List<AtomOfferingInfoHistory> atomOfferingInfoHistories = atomOfferingInfoHistoryMapper.selectByExample(atomHistoryExample);
                if (CollectionUtils.isEmpty(atomOfferingInfoHistories)) {
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "原子商品历史信息不存在");
                }
                AtomOfferingInfoHistory atomOfferingInfoHistory = atomOfferingInfoHistories.get(0);
                atomIdList.add(atomOfferingInfoHistory.getAtomId());
                skuCode = atomOfferingInfoHistory.getSkuCode();
                List<DeliverCardParam.CardItem> cardList = atomDeliverItem.getCardList();

                String cardContainingTerminal = atomOfferingInfoHistory.getCardContainingTerminal();
                boolean hasCard = cardContainingTerminal == null || "1".equals(cardContainingTerminal);
                if(hasCard){
                    hasCardList.addAll(cardList);
                }

                //先查出码号,iccid(批量交付时没提供这两个信息,单独交付提供了信息无需查询)
                if(cardType != null){
                    for (DeliverCardParam.CardItem cardItem : cardList) {
                        if(!hasCard){
                            //不含卡终端不涉及码号和iccid
                            continue;
                        }
                        String imei = cardItem.getImei();
                        String msisdn = cardItem.getMsisdn();
                        String iccid = cardItem.getIccid();
                        if (/*CardTypeEnum.M2M_NOT_NULL_CARD.getType().equals(cardType) || */CardTypeEnum.TIE_PIAN_CARD.getType().equals(cardType)) {
                            //贴片卡通过imei去x终端表找码号
                            CardRelationExample tempExample = new CardRelationExample().createCriteria()
                                    .andImeiEqualTo(imei)
                                    .andDeleteTimeIsNull()
                                    .example();
                            msisdn = cardRelationMapper.selectByExample(tempExample).get(0).getMsisdn();
                            cardItem.setMsisdn(msisdn);
                        }
                        if (CardTypeEnum.M2M_NULL_CARD.getType().equals(cardType)) {
                            //空写卡通过imei去x终端表找iccid
                            CardRelationExample tempExample = new CardRelationExample().createCriteria().andImeiEqualTo(imei).andDeleteTimeIsNull().example();
                            CardRelation tempCardRelation = cardRelationMapper.selectByExample(tempExample).get(0);
                            iccid = tempCardRelation.getTempIccid();
                            cardItem.setIccid(iccid);
                        }
                    }
                }

                //批量交付时，传入cardType，减少查询量
                if(cardType == null){
                    SkuOfferingInfoHistoryExample skuExample = new SkuOfferingInfoHistoryExample().createCriteria()
                            .andSpuCodeEqualTo(order2cAtomInfo.getSpuOfferingCode())
                            .andSpuOfferingVersionEqualTo(order2cAtomInfo.getSpuOfferingVersion())
                            .andOfferingCodeEqualTo(order2cAtomInfo.getSkuOfferingCode())
                            .andSkuOfferingVersionEqualTo(order2cAtomInfo.getSkuOfferingVersion()).example();
                    List<SkuOfferingInfoHistory> skuOfferingInfoHistories = skuOfferingInfoHistoryMapper.selectByExample(skuExample);
                    if (CollectionUtils.isEmpty(skuOfferingInfoHistories)) {
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订单规格商品版本信息不存在");
                    }
                    cardType = skuOfferingInfoHistories.get(0).getCardType();
                }
                if (order2cAtomInfo.getSkuQuantity().intValue() != cardList.size()) {
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "交付的数量必须和订购数量一致");
                }

                //暂存码号/iccid和imei对应关系
                Map<String, String> msisdnAndImeiMap = new HashMap<>();
                if(hasCard){
                    if(CardTypeEnum.TIE_PIAN_CARD.getType().equals(cardType)
                            || CardTypeEnum.CHA_BO_CARD.getType().equals(cardType)){
                        cardList.forEach(c -> {
                            String msisdn = c.getMsisdn();
                            if (StringUtils.isEmpty(msisdn)){
                                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "贴片卡和插拔卡的码号不能为空");
                            }
                            msisdnAndImeiMap.put(msisdn,c.getImei());
                        });
                    } else if (CardTypeEnum.M2M_NULL_CARD.getType().equals(cardType)) {
                        cardList.forEach(c -> {
                            String iccid = c.getIccid();
                            if (StringUtils.isEmpty(iccid)){
                                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "空写卡的临时iccid不能为空");
                            }
                            msisdnAndImeiMap.put(iccid,c.getImei());
                        });
                    }
                }

                if(hasCard){
                    // 是否包含不能销售的卡
                    Boolean atomOrderMsisdnCheckFail = false;
                    List<QrySubscribersResponse.MsisdnInfo> successList = new ArrayList<>();
                    List<QrySubscribersResponse.MsisdnInfo> failList = new ArrayList<>();
                    List<QrySubscribersResponse.MsisdnInfo> msisdnDetailInfos = null;
                    // M2M芯片卡：交付时不涉及号卡查询
                    if (!CardTypeEnum.M2M_NULL_CARD.getType().equals(cardType)) {
                        //校验码号可用性
                        QrySubscribersRequest qrySubscribersRequest = new QrySubscribersRequest();
                        qrySubscribersRequest.setOrderId(order2cInfo.getOrderId());
                        List<QrySubscribersRequest.MsisdnInfo> msisdnList = null;
                        if (CardTypeEnum.CHA_BO_CARD.getType().equals(cardType)) {
                            msisdnList = cardList.stream().filter(c -> {
                                return StringUtils.isNotEmpty(c.getMsisdn());
                            }).map(c -> {
                                QrySubscribersRequest.MsisdnInfo msisdnInfo = new QrySubscribersRequest.MsisdnInfo();
                                msisdnInfo.setMsisdn(c.getMsisdn());
                                return msisdnInfo;
                            }).collect(Collectors.toList());
                        }else if (CardTypeEnum.TIE_PIAN_CARD.getType().equals(cardType)){
                            List<String> imeiList
                                    = cardList.stream().map(DeliverCardParam.CardItem::getImei).collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(imeiList)){
                                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "贴片卡的IMEI信息不能为空");
                            }
                            CardRelationExample cardRelationExample = new CardRelationExample().createCriteria()
                                    .andImeiIn(imeiList)
                                    .andDeleteTimeIsNull()
                                    .example();
                            List<CardRelation> cardRelationList = cardRelationMapper.selectByExample(cardRelationExample);
                            if (CollectionUtils.isEmpty(cardRelationList)){
                                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "贴片卡的IMEI对象的终端信息不存在");
                            }
                            msisdnList = cardRelationList.stream().filter(c -> {
                                return StringUtils.isNotEmpty(c.getMsisdn());
                            }).map(c -> {
                                QrySubscribersRequest.MsisdnInfo msisdnInfo = new QrySubscribersRequest.MsisdnInfo();
                                msisdnInfo.setMsisdn(c.getMsisdn());
                                return msisdnInfo;
                            }).collect(Collectors.toList());

                        }
                        if (CollectionUtils.isEmpty(msisdnList)){
                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "进行号卡信息查询时未获取到码号信息");
                        }
                        qrySubscribersRequest.setMsisdnInfos(msisdnList);
                        BaseAnswer baseAnswer = order2CService.qrySubscribers(qrySubscribersRequest);
                        QrySubscribersResponse response = (QrySubscribersResponse) baseAnswer.getData();
                        msisdnDetailInfos = response.getMsisdnDetailInfos();
                        atomOrderMsisdnCheckFail = msisdnDetailInfos.stream().anyMatch(m -> {
                            return !"0".equals(m.getStatus());
                        });
                        //对 错
                        msisdnDetailInfos.stream().forEach(msisdnInfo -> {
                            if("0".equals(msisdnInfo.getStatus())){
                                successList.add(msisdnInfo);
                            }else if("9".equals(msisdnInfo.getStatus())){
                                failList.add(msisdnInfo);
                            }
                        });
                    }else{
                        List<QrySubscribersResponse.MsisdnInfo> msisdnDetailInfoList = new ArrayList<>();
                        cardList.stream().forEach(cardItem -> {
                            QrySubscribersResponse.MsisdnInfo msisdnInfo = new QrySubscribersResponse.MsisdnInfo();
                            msisdnDetailInfoList.add(msisdnInfo);
                            String imei = cardItem.getImei();
                            CardRelationExample cardRelationExample = new CardRelationExample();
                            cardRelationExample.createCriteria()
                                    .andImeiEqualTo(imei)
                                    .andDeleteTimeIsNull();
                            List<CardRelation> cardRelationList = listCardRelationByNeed(cardRelationExample);
                            CardRelation cardRelation = cardRelationList.get(0);
                            String tempIccid = cardRelation.getTempIccid();
                            msisdnInfo.setIccid(tempIccid);
                            String status = querySubscriberStatusResponse(tempIccid,orderId,cardRelationExample);
                            String finalStatus = (Integer.parseInt(status)+50)+"";
                            msisdnInfo.setStatus(finalStatus);
                            if (!"6".equals(status)){
                                failList.add(msisdnInfo);
                            }
                        });
                        if (CollectionUtils.isNotEmpty(msisdnDetailInfoList)){
                            msisdnDetailInfos = new ArrayList<>();
                            msisdnDetailInfos.addAll(msisdnDetailInfoList);
                        }
                    }
                    if(CollectionUtils.isNotEmpty(failList)){
                        Map<String, Integer> detailInventoryMap = new HashMap<>();
                        Set<String> mainInventorySet = new HashSet<>();
                        if (CardTypeEnum.TIE_PIAN_CARD.getType().equals(cardType)
                                || CardTypeEnum.CHA_BO_CARD.getType().equals(cardType)){
                            for (QrySubscribersResponse.MsisdnInfo msisdnInfo : failList) {
                                //处理错的 插拔卡,贴片卡：不可销售号卡OS码号状态更新为“不可销售”
                                String msisdn = msisdnInfo.getMsisdn();
                                String imei = msisdnAndImeiMap.get(msisdn);
                                CardRelationExample cardRelationExample = new CardRelationExample().createCriteria()
                                        .andImeiEqualTo(imei)
                                        .andDeleteTimeIsNull()
                                        .example();
                                List<CardRelation> cardRelationList = cardRelationMapper.selectByExample(cardRelationExample);
                                if (CollectionUtils.isNotEmpty(cardRelationList)){
                                    String currentSellStatus = cardRelationList.get(0).getSellStatus();
                                    // 只有未销售状态才需要处理码号/终端状态和库存
                                    if (SellStatusEnum.NOT_SELL.getType().equals(currentSellStatus)){
                                        CardRelation cardRelation = new CardRelation();
                                        cardRelation.setSellStatus(SellStatusEnum.SELL_FAIL.getType());
                                        cardRelation.setUpdateTime(now);
                                        cardRelationMapper.updateByExampleSelective(cardRelation,cardRelationExample);

                                        // 重新获取终端
                                        cardRelation = cardRelationMapper.selectByExample(cardRelationExample).get(0);

                                        //更新码号销售状态,关联订单
                                        CardMallSyncExample cardMallSyncExample = new CardMallSyncExample().createCriteria().andMsisdnEqualTo(msisdn).example();
                                        CardMallSync cardMallSync = new CardMallSync();
                                        cardMallSync.setUpdateTime(now);
                                        cardMallSync.setCardStatus(CardStatusEnum.CAN_NOT_SELL.getType());
                                        cardMallSyncService.updateByNeed(cardMallSync, cardMallSyncExample);
                                        //码号库存变更后，码号库存直接扣减总库存和当前库存
                                        List<CardMallSync> cardMallSyncs = cardMallSyncMapper.selectByExample(cardMallSyncExample);
                                        for (CardMallSync mallSync : cardMallSyncs) {
                                            String cardInventoryMainId = mallSync.getCardInventoryMainId();
                                            CardInventoryMainInfo cardInventoryMainInfo = new CardInventoryMainInfo();
                                            CardInventoryMainInfo cardInventoryMainInfoOld = cardInventoryMainInfoMapper.selectByPrimaryKey(cardInventoryMainId);
                                            Integer currentInventory = cardInventoryMainInfoOld.getCurrentInventory();
                                            Integer totalInventory = cardInventoryMainInfoOld.getTotalInventory();
                                            cardInventoryMainInfo.setId(cardInventoryMainId);
                                            cardInventoryMainInfo.setCurrentInventory(currentInventory-1);
                                            cardInventoryMainInfo.setTotalInventory(totalInventory-1);
                                            cardInventoryMainInfo.setUpdateTime(now);
                                            cardInventoryMainInfoMapper.updateByPrimaryKeySelective(cardInventoryMainInfo);
                                        }
                                        // 处理库存判断
                                        handleKxInventoryAssemble(cardRelation,detailInventoryMap,mainInventorySet);
                                    }
                                }


                            }
                        } else if (CardTypeEnum.M2M_NULL_CARD.getType().equals(cardType)) {
                            for (QrySubscribersResponse.MsisdnInfo msisdnInfo : failList) {
                                //处理错的 空写卡：不可销售号卡OS码号状态更新为“不可销售”
                                String iccid = msisdnInfo.getIccid();
                                String msisdn = msisdnInfo.getMsisdn();
                                String imei = msisdnAndImeiMap.get(iccid);
                                CardRelationExample cardRelationExample = new CardRelationExample().createCriteria()
                                        .andImeiEqualTo(imei)
                                        .andDeleteTimeIsNull()
                                        .example();
                                List<CardRelation> cardRelationList = cardRelationMapper.selectByExample(cardRelationExample);
                                if (CollectionUtils.isNotEmpty(cardRelationList)){
                                    String currentSellStatus = cardRelationList.get(0).getSellStatus();
                                    // 只有未销售状态才需要处理终端状态和库存
                                    if (SellStatusEnum.NOT_SELL.getType().equals(currentSellStatus)){
                                        CardRelation cardRelation = new CardRelation();
                                        cardRelation.setSellStatus(SellStatusEnum.SELL_FAIL.getType());
                                        cardRelation.setUpdateTime(now);
                                        cardRelationMapper.updateByExampleSelective(cardRelation,cardRelationExample);

                                        // 重新获取终端
                                        cardRelation = cardRelationMapper.selectByExample(cardRelationExample).get(0);

                                        //更新空写卡 码号销售状态 空写卡是临时iccid,失败会重新换一个，到成功不存在修改码号
               /*             CardMallSyncExample cardMallSyncExample = new CardMallSyncExample().createCriteria().andMsisdnEqualTo(msisdn).example();
                            CardMallSync cardMallSync = new CardMallSync();
                            cardMallSync.setUpdateTime(now);
                            cardMallSync.setCardStatus(CardStatusEnum.CAN_NOT_SELL.getType());
                            cardMallSyncService.updateByNeed(cardMallSync, cardMallSyncExample);
                            //码号库存变更后，码号库存直接扣减总库存和当前库存
                            List<CardMallSync> cardMallSyncs = cardMallSyncMapper.selectByExample(cardMallSyncExample);
                            for (CardMallSync mallSync : cardMallSyncs) {
                                String cardInventoryMainId = mallSync.getCardInventoryMainId();
                                CardInventoryMainInfo cardInventoryMainInfo = new CardInventoryMainInfo();
                                CardInventoryMainInfo cardInventoryMainInfoOld = cardInventoryMainInfoMapper.selectByPrimaryKey(cardInventoryMainId);
                                Integer currentInventory = cardInventoryMainInfoOld.getCurrentInventory();
                                Integer totalInventory = cardInventoryMainInfoOld.getTotalInventory();
                                cardInventoryMainInfo.setId(cardInventoryMainId);
                                cardInventoryMainInfo.setCurrentInventory(currentInventory-1);
                                cardInventoryMainInfo.setTotalInventory(totalInventory-1);
                                cardInventoryMainInfo.setUpdateTime(now);
                                cardInventoryMainInfoMapper.updateByPrimaryKeySelective(cardInventoryMainInfo);
                            }*/
                                        // 处理库存判断
                                        handleKxInventoryAssemble(cardRelation,detailInventoryMap,mainInventorySet);
                                    }
                                }
                            }
                        }
                        // 处理库存减少操作
                        handleInventoryMap(detailInventoryMap,mainInventorySet,now);
                    }
                    if(CollectionUtils.isNotEmpty(failList) || atomOrderMsisdnCheckFail){
                        hasAnyAtomOrderMsisdnCheckFail = true;
                        //存储当前原子订单的码号校验信息
                        AtomOrderMsisdnCheckFailVO vo = new AtomOrderMsisdnCheckFailVO();
                        vo.setAtomOrderId(atomOrderId);
                        vo.setMsisdnInfoList(msisdnDetailInfos);
                        atomOrderMsisdnCheckFailVOList.add(vo);
                        continue;
                    }

                    if(hasAnyAtomOrderMsisdnCheckFail){
                        //订单中有任何一个原子订单出现码号校验失败的，就不往下走，继续进行其他原子订单校验步骤
                        continue;
                    }
                }

                List<SkuMsisdnRelation> existSkuMsisdnRelationList = null;
                //如果没错的且全部是未销售，往下走
                //更新终端和码号的销售状态，订单关联关系
                for (DeliverCardParam.CardItem cardItem : cardList) {
                    String imei = cardItem.getImei();
                    String msisdn = cardItem.getMsisdn();
                    String iccid = cardItem.getIccid();
                    CardRelation cardRelation = new CardRelation();
                    cardRelation.setSellStatus(SellStatusEnum.SELLING.getType());
                    cardRelation.setUpdateTime(now);
                    cardRelation.setCardDeliverTime(now);
                    cardRelation.setOrderAtomInfoId(atomOrderId);
                    cardRelation.setOrderId(order2cInfo.getOrderId());
                    if (CardTypeEnum.CHA_BO_CARD.getType().equals(cardType)) {
                        cardRelation.setChabaMsisdn(msisdn);
                    }
                    CardRelationExample cardRelationExample = null;
                    if (/*CardTypeEnum.M2M_NOT_NULL_CARD.getType().equals(cardType) || */CardTypeEnum.TIE_PIAN_CARD.getType().equals(cardType)) {
                        cardRelationExample = new CardRelationExample();
                        CardRelationExample.Criteria criteria = cardRelationExample.createCriteria()
                                .andImeiEqualTo(imei)
                                .andDeleteTimeIsNull();
                        if(StringUtils.isNotEmpty(msisdn)){
                            //对于不含卡终端是没有Msisdn的
                            criteria.andMsisdnEqualTo(msisdn);
                        }
                    }
                    if (CardTypeEnum.M2M_NULL_CARD.getType().equals(cardType)) {
                        //空写卡通过imei去x终端表找iccid
                        cardRelationExample = new CardRelationExample();
                        CardRelationExample.Criteria criteria = cardRelationExample.createCriteria()
                                .andImeiEqualTo(imei)
                                .andDeleteTimeIsNull();
                        if (StringUtils.isNotEmpty(iccid)) {
                            criteria.andTempIccidEqualTo(iccid);
                        }
                    }
                    if (CardTypeEnum.CHA_BO_CARD.getType().equals(cardType)) {
                        cardRelationExample = new CardRelationExample().createCriteria()
                                .andImeiEqualTo(imei)
                                .andDeleteTimeIsNull()
                                .example();
                    }
                    cardRelationMapper.updateByExampleSelective(cardRelation, cardRelationExample);
                    if(hasCard){
                        //含卡终端才需要更新码号以及插入码号订单记录
                        if (CardTypeEnum.TIE_PIAN_CARD.getType().equals(cardType) || CardTypeEnum.CHA_BO_CARD.getType().equals(cardType)) {
                            //更新码号销售状态,关联订单
                            CardMallSyncExample cardMallSyncExample = new CardMallSyncExample().createCriteria().andMsisdnEqualTo(msisdn).example();
                            CardMallSync cardMallSync = new CardMallSync();
                            cardMallSync.setUpdateTime(now);
                            cardMallSync.setCardStatus(CardStatusEnum.SELLING.getType());
                            cardMallSync.setOrderId(order2cInfo.getOrderId());
                            cardMallSync.setAtomOrderId(atomOrderId);
                            cardMallSyncService.updateByNeed(cardMallSync, cardMallSyncExample);
                        }

                        // 往sku_msisdn_relation里面存入数据用作历史记录，
                        // 同时在卡+X自主下单预付费时因为走的代客下单逻辑，但是需要返回订购结果，因此这里需要存储，
                        // 同时在卡+X自主下单预付费退款后可以保留相应数据
                        if(existSkuMsisdnRelationList == null){
                            //一个原子订单只需要查询一次，提高性能
                            SkuMsisdnRelationExample skuMsisdnRelationExample = new SkuMsisdnRelationExample();
                            skuMsisdnRelationExample.createCriteria()
                                    .andOrderIdEqualTo(orderId)
                                    .andOrderAtomInfoIdEqualTo(atomOrderId);
                            existSkuMsisdnRelationList
                                    = skuMsisdnRelationService.listSkuMsisdnRelationByNeed(skuMsisdnRelationExample);

                            //空写卡更新码号销售状态
                            if(CollectionUtils.isNotEmpty(existSkuMsisdnRelationList) && CardTypeEnum.M2M_NULL_CARD.getType().equals(cardType)){
                                List<String> msisdnList = existSkuMsisdnRelationList.stream().map(c -> {
                                    return c.getMsisdn();
                                }).collect(Collectors.toList());
                                CardMallSyncExample cardMallSyncExample = new CardMallSyncExample().createCriteria().andMsisdnIn(msisdnList).example();
                                CardMallSync cardMallSync = new CardMallSync();
                                cardMallSync.setUpdateTime(now);
                                cardMallSync.setCardStatus(CardStatusEnum.SELLING.getType());
                                cardMallSync.setOrderId(order2cInfo.getOrderId());
                                cardMallSync.setAtomOrderId(atomOrderId);
                                cardMallSyncService.updateByNeed(cardMallSync, cardMallSyncExample);
                            }
                        }

                        // 如果未记录过
                        if (CollectionUtils.isEmpty(existSkuMsisdnRelationList)) {
                            if (StringUtils.isEmpty(msisdn)) {
                                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未获取到码号信息");
                            }

                            SkuMsisdnRelation skuMsisdnRelation = new SkuMsisdnRelation();
                            skuMsisdnRelation.setId(BaseServiceUtils.getId());
                            skuMsisdnRelation.setOrderId(orderId);
                            skuMsisdnRelation.setOrderAtomInfoId(atomOrderId);
                            skuMsisdnRelation.setImei(imei);
                            skuMsisdnRelation.setMsisdn(msisdn);
                            skuMsisdnRelation.setCreateTime(now);
                            skuMsisdnRelation.setUpdateTime(now);
                            skuMsisdnRelationList.add(skuMsisdnRelation);
                        }
                    }
                }
            }
            orderIdMap.put(orderId,atomOrderIdList);
            atomDeliverItem.setAtomOrderIdList(atomOrderIdList);
        }

        if(hasAnyAtomOrderMsisdnCheckFail){
            //包含校验失败的，将全部校验结果返回给前端
            BaseAnswer answer = new BaseAnswer();
            answer.setStatus(StatusConstant.CARD_CHECK_FAILED);
            answer.setData(atomOrderMsisdnCheckFailVOList);
            return answer;
        }

        if (CollectionUtils.isNotEmpty(skuMsisdnRelationList)) {
            skuMsisdnRelationService.batchAddSkuMsisdnRelation(skuMsisdnRelationList);
        }
        // 删除批量导入交付卡信息
        CardChooseDeliveryExample cardChooseDeliveryExample = new CardChooseDeliveryExample();
        cardChooseDeliveryExample.createCriteria().andAtomOrderIdIn(atomOrderIdList);
        cardChooseDeliveryService.deleteCardChooseDeliveryByNeed(cardChooseDeliveryExample);



        //记录日志
        if (addLog) {
            String logContent = "【交付】\n" + "订单号" + order2cInfo.getOrderId();
            logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                    OrderManageOperateEnum.GOODS_ORDER.code,
                    logContent, LogResultEnum.LOG_SUCESS.code, null);
        }

        SkuMsisdnRelationExample example = new SkuMsisdnRelationExample().createCriteria().andOrderIdEqualTo(order2cInfo.getOrderId()).example();
        List<SkuMsisdnRelation> skuMsisdnRelations = skuMsisdnRelationMapper.selectByExample(example);
        if (CardTypeEnum.M2M_NULL_CARD.getType().equals(cardType) && skuMsisdnRelations.size() != skuQuantity) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "空写卡，订单" + order2cInfo.getOrderId() + "同步的码号数量[" + skuMsisdnRelations.size() + "]和订购数量[" + skuQuantity + "]不一致");
        }
        ArrayBlockingQueue<String> msisdnQueue = null;
        if (CollectionUtils.isNotEmpty(skuMsisdnRelations)) {
            List<String> list = skuMsisdnRelations.stream().map(s -> {
                return s.getMsisdn();
            }).collect(Collectors.toList());
            msisdnQueue = new ArrayBlockingQueue<>(skuMsisdnRelations.size());
            msisdnQueue.addAll(list);
        }
        //向商城反馈号卡分配结果（放到最后，避免反馈成功但是后续步骤失败导致无法回滚对商城的反馈）
        AllocateCardResultRequest allocateCardResultRequest = new AllocateCardResultRequest();
        allocateCardResultRequest.setOrderSeq(BaseServiceUtils.getId());
        allocateCardResultRequest.setOrderId(order2cInfo.getOrderId());
        allocateCardResultRequest.setCardType(cardType);
        if (CardTypeEnum.M2M_NULL_CARD.getType().equals(cardType)) {
            ArrayBlockingQueue<String> finalMsisdnQueue = msisdnQueue;
            List<AllocateCardResultRequest.MsisdnInfo> msisdnInfos = hasCardList.stream().map(c -> {
                AllocateCardResultRequest.MsisdnInfo msisdnInfo = new AllocateCardResultRequest.MsisdnInfo();
                msisdnInfo.setImei(c.getImei());
                msisdnInfo.setIccid(c.getIccid());
                //码号来自订单同步的时候
                msisdnInfo.setMsisdn(finalMsisdnQueue.poll());
                return msisdnInfo;
            }).collect(Collectors.toList());
            allocateCardResultRequest.setMsisdnInfos(msisdnInfos);
        } else {
            List<AllocateCardResultRequest.CardmsisdnInfo> cardmsisdnInfos = hasCardList.stream().map(c -> {
                AllocateCardResultRequest.CardmsisdnInfo cardmsisdnInfo = new AllocateCardResultRequest.CardmsisdnInfo();
                cardmsisdnInfo.setCardMsisdn(c.getMsisdn());
                cardmsisdnInfo.setCardTac(c.getImei().substring(0, 8));
                return cardmsisdnInfo;
            }).collect(Collectors.toList());
            allocateCardResultRequest.setCardmsisdnInfos(cardmsisdnInfos);
            //imei放在外层
            List<String> collect = hasCardList.stream().map(c -> {
                return c.getImei();
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                allocateCardResultRequest.setCardImei(collect);
            }
        }
        order2CService.allocateCardResultRequest(allocateCardResultRequest);

        // 接单完成后记录接单的历史记录
        List<Order2cAtomHistory> order2cAtomHistoryList = new ArrayList<>();
        orderIdMap.forEach((orderId,idList)->{
            idList.forEach(id->{
                Order2cAtomHistory order2cAtomHistory = new Order2cAtomHistory();
                order2cAtomHistory.setAtomOrderId(id);
                order2cAtomHistory.setOrderId(orderId);
                order2cAtomHistory.setOperateType(1);
                order2cAtomHistory.setOrderId(userId);
                order2cAtomHistory.setInnerStatus(OrderStatusInnerEnum.DELIVER_CARD.getStatus());
                order2cAtomHistory.setCreateTime(now);
                order2cAtomHistory.setUpdateTime(now);
                order2cAtomHistoryList.add(order2cAtomHistory);
            });
        });
        /*atomList.forEach(atomDeliverItem -> {
            Order2cAtomHistory order2cAtomHistory = new Order2cAtomHistory();
            order2cAtomHistory.setAtomOrderId(atomDeliverItem.getAtomOrderId());
            order2cAtomHistory.setOrderId(atomDeliverItem.getOrderId());
            order2cAtomHistory.setOperateType(1);
            order2cAtomHistory.setOrderId(userId);
            order2cAtomHistory.setInnerStatus(OrderStatusInnerEnum.DELIVER_CARD.getStatus());
            order2cAtomHistory.setCreateTime(now);
            order2cAtomHistory.setUpdateTime(now);
            order2cAtomHistoryList.add(order2cAtomHistory);
        });*/
        if (CollectionUtils.isNotEmpty(order2cAtomHistoryList)){
            order2cAtomHistoryMapper.batchInsert(order2cAtomHistoryList);
        }

        // 这个没有数据回滚，放在最后删除预占的仓库信息
        stringRedisTemplate.delete(REDIS_RESERVE_INVENTORY_PREFIX + order2cInfo.getOrderId());
        //删除码号库存预占信息
        stringRedisTemplate.delete(REDIS_CARD_INVENTORY_CONSTANT + order2cInfo.getOrderId()+"_"+skuCode);
        for (String atomId : atomIdList) {
            stringRedisTemplate.delete(REDIS_COMMIT_INVENTORY_AREA + order2cInfo.getOrderId() + "_" + atomId);
        }
        return BaseAnswer.success(null);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importCardXDeliver(//MultipartFile file,
                                   InputStream inputStream,
                                   String orderId,
                                   String cardType,
                                   List<AtomOfferingInfo> atomOfferingInfoList,
                                   List<Order2cAtomInfo> order2cAtomInfoList,
                                   Order2cInfo order2cInfo,
                                   LoginIfo4Redis loginIfo4Redis,
                                   HttpServletRequest request,
                                   HttpServletResponse response) throws Exception {
        log.info("importCardXDeliver 批量导入交付终端");

        String userId = loginIfo4Redis.getUserId();
        String phone = loginIfo4Redis.getPhone();
        String importRedisKey = "import_kx_deliver_".concat(userId);
        stringRedisTemplate.opsForValue().set(importRedisKey,importRedisKey,12,TimeUnit.HOURS);


        List<CardChooseDelivery> cardChooseDeliveryList = new ArrayList<>();
        Date date = new Date();

        if (cardType.equals(CardTypeEnum.CHA_BO_CARD.getType())) {
            log.info("importCardXDeliver CHA_BO_CARD 批量导入开始");
            ExcelCardXType0DeliverImportListener excelCardXType0DeliverImportListener
                    = new ExcelCardXType0DeliverImportListener(this, stringRedisTemplate,
                    cardChooseDeliveryService,cardInfoService);
            excelCardXType0DeliverImportListener.setOrder2cAtomInfoList(order2cAtomInfoList);
            excelCardXType0DeliverImportListener.setAtomOfferingInfoList(atomOfferingInfoList);
            List<ImportCardXType0DeliverDTO> cardExcelImportList = EasyExcel.read(//file.getInputStream(),
                    inputStream,
                            ImportCardXType0DeliverDTO.class, excelCardXType0DeliverImportListener)
                    .sheet(0).headRowNumber(1).doReadSync();
            log.info("importCardXDeliver CHA_BO_CARD 批量导入结束");
            int importDataSize = cardExcelImportList.size();
            List<ImportCardXType0DeliverDTO> failList = excelCardXType0DeliverImportListener.getFailList();
            List<ImportCardXType0DeliverDTO> successList = excelCardXType0DeliverImportListener.getSuccessList();

            if (CollectionUtils.isEmpty(cardExcelImportList)) {
                // 处理失败的数据
                handleKxDeliverFail(null,null,
                        userId,phone,0,0,0,importRedisKey,2,date);
                stringRedisTemplate.delete(importRedisKey);
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("导入的文件不能为空数据", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }
            int succCount = successList.size();
            int failCount = importDataSize - succCount;
            // 如果导入的数据和成功的数据不相等，说明有错误的数据，则将所有的数据全部返回
            if (importDataSize != succCount) {
                String excelName = "imei与码号交付";
                excelName = URLEncoder.encode(excelName, "UTF-8");
                ClassPathResource classPathResource = new ClassPathResource("template/imei_msisdn_deliver_error_template.xlsx");
                InputStream templateFileName = classPathResource.getInputStream();

                /*List<EasyExcelDTO> easyExcelDTOList = new ArrayList<>();
                // 交付信息失败信息
                EasyExcelDTO easyExcelDTO = EasyExcelUtils.setEasyExcelDTO(0, "imei与码号交付", "list",
                        failList, null);
                easyExcelDTOList.add(easyExcelDTO);
                //导出失败信息
                EasyExcelUtils.exportManySheetExcel(response, easyExcelDTOList,
                        excelName, templateFileName,
                        "10088", "imei与码号交付导入失败");*/
                // 处理失败或完成的数据
                handleKxDeliverFail(failList,templateFileName,
                        userId,phone,importDataSize,failCount,succCount,importRedisKey,1,date);
                stringRedisTemplate.delete(importRedisKey);
                return;
            }

            // 如果没有成功的数据直接返回，因为导入失败会进行信息提示，所以这里直接返回
            if (importDataSize == 0) {
                // 处理失败的数据
                handleKxDeliverFail(null,null,
                        userId,phone,0,0,0,importRedisKey,2,date);
                stringRedisTemplate.delete(importRedisKey);
                return;
            }
            log.info("importCardXDeliver CHA_BO_CARD 批量导入forEach开始");
            successList.stream().forEach(importCardXType0DeliverDTO -> {
                CardChooseDelivery cardChooseDelivery = new CardChooseDelivery();
                cardChooseDelivery.setId(BaseServiceUtils.getId());
                cardChooseDelivery.setOrderId(orderId);
                cardChooseDelivery.setCreateTime(date);
                BeanUtils.copyProperties(importCardXType0DeliverDTO, cardChooseDelivery);
                cardChooseDeliveryList.add(cardChooseDelivery);
            });
            log.info("importCardXDeliver CHA_BO_CARD 批量导入forEach结束");
        } else if (cardType.equals(CardTypeEnum.TIE_PIAN_CARD.getType())
                || cardType.equals(CardTypeEnum.M2M_NULL_CARD.getType())) {
            log.info("importCardXDeliver TIE_PIAN_CARD M2M_NULL_CARD 批量导入开始");
            ExcelCardXType1And3DeliverImportListener excelCardXType1And3DeliverImportListener
                    = new ExcelCardXType1And3DeliverImportListener(this, stringRedisTemplate,
                    cardChooseDeliveryService);
            excelCardXType1And3DeliverImportListener.setAtomOfferingInfoList(atomOfferingInfoList);
            excelCardXType1And3DeliverImportListener.setOrder2cAtomInfoList(order2cAtomInfoList);
            excelCardXType1And3DeliverImportListener.setOrder2cInfo(order2cInfo);
            List<ImportCardXType1And3DeliverDTO> cardExcelImportList = EasyExcel.read(//file.getInputStream(),
                    inputStream,
                            ImportCardXType1And3DeliverDTO.class, excelCardXType1And3DeliverImportListener)
                    .sheet(0).headRowNumber(1).doReadSync();
            log.info("importCardXDeliver TIE_PIAN_CARD M2M_NULL_CARD 批量导入结束");
            int importDataSize = cardExcelImportList.size();
            List<ImportCardXType1And3DeliverDTO> failList = excelCardXType1And3DeliverImportListener.getFailList();
            List<ImportCardXType1And3DeliverDTO> successList = excelCardXType1And3DeliverImportListener.getSuccessList();

            if (CollectionUtils.isEmpty(cardExcelImportList)) {
                // 处理失败的数据
                handleKxDeliverFail(null,null,
                        userId,phone,0,0,0,importRedisKey,2,date);
                stringRedisTemplate.delete(importRedisKey);
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("导入的文件不能为空数据", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }

            int succCount = successList.size();
            int failCount = importDataSize - succCount;
            // 如果导入的数据和成功的数据不相等，说明有错误的数据，则将所有的数据全部返回
            if (importDataSize != succCount) {
                String excelName = "imei交付";
                excelName = URLEncoder.encode(excelName, "UTF-8");
                ClassPathResource classPathResource = new ClassPathResource("template/imei_deliver_error_template.xlsx");
                InputStream templateFileName = classPathResource.getInputStream();

                /*List<EasyExcelDTO> easyExcelDTOList = new ArrayList<>();
                // 交付信息失败信息
                EasyExcelDTO easyExcelDTO = EasyExcelUtils.setEasyExcelDTO(0, "imei与码号交付", "list",
                        failList, null);
                easyExcelDTOList.add(easyExcelDTO);
                //导出失败信息
                EasyExcelUtils.exportManySheetExcel(response, easyExcelDTOList,
                        excelName, templateFileName,
                        "10088", "imei交付导入失败");*/
                // 处理失败的数据
                handleKxDeliverFail(failList,templateFileName,
                        userId,phone,importDataSize,failCount,succCount,importRedisKey,1,date);

                stringRedisTemplate.delete(importRedisKey);
                return;
            }

            // 如果没有成功的数据直接返回，因为导入失败会进行信息提示，所以这里直接返回
            if (importDataSize == 0) {
                // 处理失败的数据
                handleKxDeliverFail(null,null,
                        userId,phone,0,0,0,importRedisKey,2,date);
                stringRedisTemplate.delete(importRedisKey);
                return;
            }

            StringBuffer errorBuf = new StringBuffer();
            log.info("importCardXDeliver TIE_PIAN_CARD M2M_NULL_CARD 批量导入foreach开始");
            successList.stream().forEach(importCardXType1And3DeliverDTO -> {
                /*if (cardType.equals(CardTypeEnum.M2M_NULL_CARD.getType())){
                    String imei = importCardXType1And3DeliverDTO.getImei();
                    CardRelationExample cardRelationExample = new CardRelationExample();
                    cardRelationExample.createCriteria()
                            .andImeiEqualTo(imei)
                            .andDeleteTimeIsNull();
                    List<CardRelation> cardRelationList = listCardRelationByNeed(cardRelationExample);
                    CardRelation cardRelation = cardRelationList.get(0);
                    String status = querySubscriberStatusResponse(cardRelation.getTempIccid(),orderId,cardRelationExample);
                    if (!"6".equals(status)){
                        if (StringUtils.isEmpty(errorBuf)){
                            errorBuf.append(imei);
                        }else{
                            errorBuf.append(",").append(imei);
                        }
                    }
                }*/
                CardChooseDelivery cardChooseDelivery = new CardChooseDelivery();
                cardChooseDelivery.setId(BaseServiceUtils.getId());
                cardChooseDelivery.setOrderId(orderId);
                cardChooseDelivery.setCreateTime(date);
                BeanUtils.copyProperties(importCardXType1And3DeliverDTO, cardChooseDelivery);
                cardChooseDeliveryList.add(cardChooseDelivery);
            });
            log.info("importCardXDeliver TIE_PIAN_CARD M2M_NULL_CARD 批量导入foreach结束");
            if (StringUtils.isNotEmpty(errorBuf)){
                String error = "终端imei为".concat(errorBuf.toString()).concat("对应的临时iccid生命周期非测试期无法空写，请重新换终端交付");
                log.info("空写卡生命周期查询有错误:{}",error);
                // 处理失败的数据
                handleKxDeliverFail(null,null,
                        userId,phone,0,0,0,importRedisKey,3,date);
                stringRedisTemplate.delete(importRedisKey);
                throw new BusinessException("10008", error);
            }

        } else {
            // 处理失败的数据
            handleKxDeliverFail(null,null,
                    userId,phone,0,0,0,importRedisKey,3,date);
            stringRedisTemplate.delete(importRedisKey);
            throw new BusinessException("10008", "无效的订单卡+X终端类型");
        }
        log.info("importCardXDeliver orderId = {} 的交付终端导入完毕 ",orderId);
        /*response.setHeader("statecode", "00000");
        response.setHeader("message", URLEncoder.encode("导入成功", "UTF-8").replaceAll("\\+", "%20"));*/

        if (CollectionUtils.isEmpty(cardChooseDeliveryList)){
            throw new BusinessException("10008","可用的导入imei为空");
        }
        cardChooseDeliveryService.batchAddCardChooseDelivery(cardChooseDeliveryList);

        // 接单完成后记录接单的历史记录
        List<Order2cAtomHistory> order2cAtomHistoryList = new ArrayList<>();
        order2cAtomInfoList.forEach(order2cAtomInfo -> {
            Order2cAtomHistory order2cAtomHistory = new Order2cAtomHistory();
            order2cAtomHistory.setAtomOrderId(order2cAtomInfo.getId());
            order2cAtomHistory.setOrderId(order2cAtomInfo.getOrderId());
            order2cAtomHistory.setOperateType(1);
            order2cAtomHistory.setOrderId(userId);
            order2cAtomHistory.setInnerStatus(OrderStatusInnerEnum.DELIVER_CARD.getStatus());
            order2cAtomHistory.setCreateTime(date);
            order2cAtomHistory.setUpdateTime(date);
            order2cAtomHistoryList.add(order2cAtomHistory);
        });
        if (CollectionUtils.isNotEmpty(order2cAtomHistoryList)){
            order2cAtomHistoryMapper.batchInsert(order2cAtomHistoryList);
        }

        // 处理完成后的数据
        handleKxDeliverFail(null,null,
                userId,phone,0,0,0,importRedisKey,2,date);
        stringRedisTemplate.delete(importRedisKey);
    }

    @Override
    public void importCardXDeliverSingle(InputStream inputStream, String orderId, String cardType,
                                         AtomOfferingInfo atomOfferingInfo, Order2cAtomInfo order2cAtomInfo,
                                         LoginIfo4Redis loginIfo4Redis, HttpServletRequest request,
                                         HttpServletResponse response,String importRedisKey) throws Exception {
        log.info("importCardXDeliver 批量导入交付终端");

        String userId = loginIfo4Redis.getUserId();
        String phone = loginIfo4Redis.getPhone();


        List<CardChooseDelivery> cardChooseDeliveryList = new ArrayList<>();
        Date date = new Date();

        if (cardType.equals(CardTypeEnum.CHA_BO_CARD.getType())) {
            log.info("importCardXDeliver CHA_BO_CARD 批量导入开始");
            ExcelCardXType0DeliverImportSingleListener excelCardXType0DeliverImportSingleListener
                    = new ExcelCardXType0DeliverImportSingleListener(this, stringRedisTemplate,
                    cardChooseDeliveryService);
            excelCardXType0DeliverImportSingleListener.setOrder2cAtomInfo(order2cAtomInfo);
            excelCardXType0DeliverImportSingleListener.setAtomOfferingInfo(atomOfferingInfo);
            List<ImportCardXType0DeliverDTO> cardExcelImportList = EasyExcel.read(//file.getInputStream(),
                            inputStream,
                            ImportCardXType0DeliverDTO.class, excelCardXType0DeliverImportSingleListener)
                    .sheet(0).headRowNumber(1).doReadSync();
            log.info("importCardXDeliver CHA_BO_CARD 批量导入结束");
            int importDataSize = cardExcelImportList.size();
            List<ImportCardXType0DeliverDTO> failList = excelCardXType0DeliverImportSingleListener.getFailList();
            List<ImportCardXType0DeliverDTO> successList = excelCardXType0DeliverImportSingleListener.getSuccessList();

            if (CollectionUtils.isEmpty(cardExcelImportList)) {
                // 处理失败的数据
                handleKxDeliverFail(null,null,
                        userId,phone,0,0,0,importRedisKey,2,date);
                stringRedisTemplate.delete(importRedisKey);
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("导入的文件不能为空数据", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }
            int succCount = successList.size();
            int failCount = importDataSize - succCount;
            // 如果导入的数据和成功的数据不相等，说明有错误的数据，则将所有的数据全部返回
            if (importDataSize != succCount) {
                String excelName = "imei与码号交付";
                excelName = URLEncoder.encode(excelName, "UTF-8");
                ClassPathResource classPathResource = new ClassPathResource("template/imei_msisdn_deliver_error_template.xlsx");
                InputStream templateFileName = classPathResource.getInputStream();

                /*List<EasyExcelDTO> easyExcelDTOList = new ArrayList<>();
                // 交付信息失败信息
                EasyExcelDTO easyExcelDTO = EasyExcelUtils.setEasyExcelDTO(0, "imei与码号交付", "list",
                        failList, null);
                easyExcelDTOList.add(easyExcelDTO);
                //导出失败信息
                EasyExcelUtils.exportManySheetExcel(response, easyExcelDTOList,
                        excelName, templateFileName,
                        "10088", "imei与码号交付导入失败");*/
                // 处理失败或完成的数据
                handleKxDeliverFail(failList,templateFileName,
                        userId,phone,importDataSize,failCount,succCount,importRedisKey,1,date);
                stringRedisTemplate.delete(importRedisKey);
                return;
            }

            // 如果没有成功的数据直接返回，因为导入失败会进行信息提示，所以这里直接返回
            if (importDataSize == 0) {
                // 处理失败的数据
                handleKxDeliverFail(null,null,
                        userId,phone,0,0,0,importRedisKey,2,date);
                stringRedisTemplate.delete(importRedisKey);
                return;
            }
            log.info("importCardXDeliver CHA_BO_CARD 批量导入forEach开始");
            successList.stream().forEach(importCardXType0DeliverDTO -> {
                CardChooseDelivery cardChooseDelivery = new CardChooseDelivery();
                cardChooseDelivery.setId(BaseServiceUtils.getId());
//                cardChooseDelivery.setAtomOrderId(atomOrderId);
                cardChooseDelivery.setOrderId(orderId);
                cardChooseDelivery.setCreateTime(date);
                BeanUtils.copyProperties(importCardXType0DeliverDTO, cardChooseDelivery);
                cardChooseDeliveryList.add(cardChooseDelivery);
            });
            log.info("importCardXDeliver CHA_BO_CARD 批量导入forEach结束");
        } else if (cardType.equals(CardTypeEnum.TIE_PIAN_CARD.getType())
                || cardType.equals(CardTypeEnum.M2M_NULL_CARD.getType())) {
            log.info("importCardXDeliver TIE_PIAN_CARD M2M_NULL_CARD 批量导入开始");
            ExcelCardXType1And3DeliverImportSingleListener excelCardXType1And3DeliverImportSingleListener
                    = new ExcelCardXType1And3DeliverImportSingleListener(this, stringRedisTemplate,
                    cardChooseDeliveryService);
            excelCardXType1And3DeliverImportSingleListener.setOrder2cAtomInfo(order2cAtomInfo);
            excelCardXType1And3DeliverImportSingleListener.setAtomOfferingInfo(atomOfferingInfo);
            List<ImportCardXType1And3DeliverDTO> cardExcelImportList = EasyExcel.read(//file.getInputStream(),
                            inputStream,
                            ImportCardXType1And3DeliverDTO.class, excelCardXType1And3DeliverImportSingleListener)
                    .sheet(0).headRowNumber(1).doReadSync();
            log.info("importCardXDeliver TIE_PIAN_CARD M2M_NULL_CARD 批量导入结束");
            int importDataSize = cardExcelImportList.size();
            List<ImportCardXType1And3DeliverDTO> failList = excelCardXType1And3DeliverImportSingleListener.getFailList();
            List<ImportCardXType1And3DeliverDTO> successList = excelCardXType1And3DeliverImportSingleListener.getSuccessList();

            if (CollectionUtils.isEmpty(cardExcelImportList)) {
                // 处理失败的数据
                handleKxDeliverFail(null,null,
                        userId,phone,0,0,0,importRedisKey,2,date);
                stringRedisTemplate.delete(importRedisKey);
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("导入的文件不能为空数据", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }

            int succCount = successList.size();
            int failCount = importDataSize - succCount;
            // 如果导入的数据和成功的数据不相等，说明有错误的数据，则将所有的数据全部返回
            if (importDataSize != succCount) {
                String excelName = "imei交付";
                excelName = URLEncoder.encode(excelName, "UTF-8");
                ClassPathResource classPathResource = new ClassPathResource("template/imei_deliver_error_template.xlsx");
                InputStream templateFileName = classPathResource.getInputStream();

                /*List<EasyExcelDTO> easyExcelDTOList = new ArrayList<>();
                // 交付信息失败信息
                EasyExcelDTO easyExcelDTO = EasyExcelUtils.setEasyExcelDTO(0, "imei与码号交付", "list",
                        failList, null);
                easyExcelDTOList.add(easyExcelDTO);
                //导出失败信息
                EasyExcelUtils.exportManySheetExcel(response, easyExcelDTOList,
                        excelName, templateFileName,
                        "10088", "imei交付导入失败");*/
                // 处理失败的数据
                handleKxDeliverFail(failList,templateFileName,
                        userId,phone,importDataSize,failCount,succCount,importRedisKey,1,date);

                stringRedisTemplate.delete(importRedisKey);
                return;
            }

            // 如果没有成功的数据直接返回，因为导入失败会进行信息提示，所以这里直接返回
            if (importDataSize == 0) {
                // 处理失败的数据
                handleKxDeliverFail(null,null,
                        userId,phone,0,0,0,importRedisKey,2,date);
                stringRedisTemplate.delete(importRedisKey);
                return;
            }

            StringBuffer errorBuf = new StringBuffer();
            log.info("importCardXDeliver TIE_PIAN_CARD M2M_NULL_CARD 批量导入foreach开始");
            successList.stream().forEach(importCardXType1And3DeliverDTO -> {
                /*if (cardType.equals(CardTypeEnum.M2M_NULL_CARD.getType())){
                    String imei = importCardXType1And3DeliverDTO.getImei();
                    CardRelationExample cardRelationExample = new CardRelationExample();
                    cardRelationExample.createCriteria()
                            .andImeiEqualTo(imei)
                            .andDeleteTimeIsNull();
                    List<CardRelation> cardRelationList = listCardRelationByNeed(cardRelationExample);
                    CardRelation cardRelation = cardRelationList.get(0);
                    String status = querySubscriberStatusResponse(cardRelation.getTempIccid(),orderId,cardRelationExample);
                    if (!"6".equals(status)){
                        if (StringUtils.isEmpty(errorBuf)){
                            errorBuf.append(imei);
                        }else{
                            errorBuf.append(",").append(imei);
                        }
                    }
                }*/
                CardChooseDelivery cardChooseDelivery = new CardChooseDelivery();
                cardChooseDelivery.setId(BaseServiceUtils.getId());
//                cardChooseDelivery.setAtomOrderId(atomOrderId);
                cardChooseDelivery.setOrderId(orderId);
                cardChooseDelivery.setCreateTime(date);
                BeanUtils.copyProperties(importCardXType1And3DeliverDTO, cardChooseDelivery);
                cardChooseDeliveryList.add(cardChooseDelivery);
            });
            log.info("importCardXDeliver TIE_PIAN_CARD M2M_NULL_CARD 批量导入foreach结束");
            if (StringUtils.isNotEmpty(errorBuf)){
                String error = "终端imei为".concat(errorBuf.toString()).concat("对应的临时iccid生命周期非测试期无法空写，请重新换终端交付");
                log.info("空写卡生命周期查询有错误:{}",error);
                // 处理失败的数据
                handleKxDeliverFail(null,null,
                        userId,phone,0,0,0,importRedisKey,3,date);
                stringRedisTemplate.delete(importRedisKey);
                throw new BusinessException("10008", error);
            }

        } else {
            // 处理失败的数据
            handleKxDeliverFail(null,null,
                    userId,phone,0,0,0,importRedisKey,3,date);
            stringRedisTemplate.delete(importRedisKey);
            throw new BusinessException("10008", "无效的订单卡+X终端类型");
        }
        log.info("importCardXDeliver orderId = {} 的交付终端导入完毕 ",orderId);
        /*response.setHeader("statecode", "00000");
        response.setHeader("message", URLEncoder.encode("导入成功", "UTF-8").replaceAll("\\+", "%20"));*/

        if (CollectionUtils.isEmpty(cardChooseDeliveryList)){
            throw new BusinessException("10008","可用的导入imei为空");
        }
        cardChooseDeliveryService.batchAddCardChooseDelivery(cardChooseDeliveryList);

        // 接单完成后记录接单的历史记录
        Order2cAtomHistory order2cAtomHistory = new Order2cAtomHistory();
        order2cAtomHistory.setAtomOrderId(order2cAtomInfo.getId());
        order2cAtomHistory.setOrderId(order2cAtomInfo.getOrderId());
        order2cAtomHistory.setOperateType(1);
        order2cAtomHistory.setOrderId(userId);
        order2cAtomHistory.setInnerStatus(OrderStatusInnerEnum.DELIVER_CARD.getStatus());
        order2cAtomHistory.setCreateTime(date);
        order2cAtomHistory.setUpdateTime(date);
        order2cAtomHistoryMapper.insert(order2cAtomHistory);

        // 处理完成后的数据
        handleKxDeliverFail(null,null,
                userId,phone,0,0,0,importRedisKey,2,date);
        stringRedisTemplate.delete(importRedisKey);
    }

    /**
     * 处理批量交付失败或成功的文件
     * @param easyExcelDTOList
     * @param templateFileName
     * @param userId
     * @param importRedisKey
     */
    private void handleKxDeliverFail(List easyExcelDTOList,
                                     InputStream templateFileName,
                                     String userId,
                                     String phone,
                                     Integer totalCount,
                                     Integer failCount,
                                     Integer successCount,
                                     String importRedisKey,
                                     Integer phoneType,
                                     Date date){
        String templateId = "";
        Map<String,String> message = new HashMap<>();
        message.put("importTime",DateUtils.dateToStr(date,DateUtils.DEFAULT_DATETIME_FORMAT));
        // 失败
        if (phoneType == 1){
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            String fileName = "批量导入交付失败" + new Date().getTime() + ".xlsx";
            EasyExcelUtils.exportExcel2OutputStream(outputStream, "list", easyExcelDTOList, null, templateFileName,
                    0, "交付失败导出", null, null, null,false);

            //上传文件到对象存储
            ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
            byteArrayUpload.setFileName(fileName);
            byteArrayUpload.setBytes(outputStream.toByteArray());
            BaseAnswer<UpResult> resultBaseAnswer = null;
            try {
                resultBaseAnswer = storageService.uploadByte(byteArrayUpload);
            } catch (Exception e) {
                stringRedisTemplate.delete(importRedisKey);
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "上传文件出错"+e);
            }
            if (!"00000".equals(resultBaseAnswer.getStateCode())) {
                stringRedisTemplate.delete(importRedisKey);
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "上传文件出错,请联系管理员");
            }
            log.info("运营数据订单导出接口上传excel完毕");

            //消息中心提醒用户
            UpResult upResult = resultBaseAnswer.getData();
            String outerUrl = upResult.getOuterUrl();
            String fileKey = upResult.getKey();
            AddMessageParam messageParam = new AddMessageParam();
            messageParam.setModule(ModuleEnum.ORDER_MANAGE.name);
            messageParam.setContent("卡+x终端信息批量交付失败");
            messageParam.setType("卡+x终端信息批量交付");
            messageParam.setUserId(userId);
            messageParam.setFileKey(fileKey);
            messageParam.setUrl(outerUrl);
            messageParam.setSource(1);
            iotFeignClient.addMessage(messageParam);

            //通过kafka发送websocket提示前端更新消息未读数
            ProducerRecord<String, byte[]> record = new ProducerRecord<>(KafkaTopic.SUPPLY_CHAIN_WEBSOCKET.getTopic(), "/message/monitor".getBytes());
            kafkaTemplate.send(record);

            templateId = importKxDevlierFail;

            message.put("total",totalCount+"");
            message.put("success",successCount+"");
            message.put("fail",failCount+"");
        } else if (phoneType == 2) {// 成功
            templateId = importKxDevlierSucc;
        }else {// 异常
            templateId = importKxDevlierErr;
        }

        //发送短信提示用户
        if (org.apache.commons.lang.StringUtils.isNotEmpty(phone) && RegexUtil.regexPhone(phone)) {
            Msg4Request msg4Request = new Msg4Request();
            msg4Request.setTemplateId(templateId);
            List<String> mobiles = new ArrayList<>();
            mobiles.add(phone);
            msg4Request.setMobiles(mobiles);
            msg4Request.setMessage(message);
            smsFeignClient.asySendMessage(msg4Request);
        }

        stringRedisTemplate.delete(importRedisKey);
    }

    @Override
    public void updateCardOrderToNull(UpdateCardOrderToNullParam updateCardOrderToNullParam) {
        cardRelationMapperExt.updateCardOrderToNull(updateCardOrderToNullParam);
    }

    @Override
    public PageData<CardRelationImportInfoDetailDTO> listCardRelationImportInfoDetail(CardRelationImportInfoDetailParam cardRelationImportInfoDetailParam) {

        PageData<CardRelationImportInfoDetailDTO> pageData = new PageData<>();
        Integer pageNum = cardRelationImportInfoDetailParam.getPageNum();
        Integer pageSize = cardRelationImportInfoDetailParam.getPageSize();

//        CardRelationImportInfoDetailVO importInfoDetailVO = new CardRelationImportInfoDetailVO();
        List<CardRelationImportInfoDetailDTO> importInfoDetailDTOList = new ArrayList<>();

        String locationParam = cardRelationImportInfoDetailParam.getLocation();
        if ("省级".equals(locationParam)) {
            cardRelationImportInfoDetailParam.setLocation("-1");
        }

        String sellStatusParam = cardRelationImportInfoDetailParam.getSellStatus();
        if (StringUtils.isNotEmpty(sellStatusParam)
        && "-".equals(sellStatusParam)){
            cardRelationImportInfoDetailParam.setSellStatus("-1");
        }

        Page<CardRelationImportInfoVO> page = new Page<>(pageNum, pageSize);
        List<CardRelationImportInfoDetailDTO> cardRelationImportInfoDetailDTOList
                = cardRelationMapperExt.listCardRelationImportInfoDetail(page,cardRelationImportInfoDetailParam);
        if (CollectionUtils.isNotEmpty(cardRelationImportInfoDetailDTOList)) {
            Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
            cardRelationImportInfoDetailDTOList.stream().forEach(cardRelationImportInfoDetailVO -> {
                String location = cardRelationImportInfoDetailVO.getLocation();
                if (StringUtils.isNotEmpty(location)) {
                    cardRelationImportInfoDetailVO.setCityName(locationCodeNameMap.get(location) + "");
                }else{
                    cardRelationImportInfoDetailVO.setCityName("省级");
                }

                String terminalTypeName = TerminalTypeEnum.getDescByType(cardRelationImportInfoDetailVO.getTerminalType());
                cardRelationImportInfoDetailVO.setTerminalTypeName(terminalTypeName);

                String sellStatus = cardRelationImportInfoDetailVO.getSellStatus();
                if ("-".equals(sellStatus)) {
                    cardRelationImportInfoDetailVO.setSellStatusName(sellStatus);
                } else {
                    String sellStatusName = SellStatusEnum.getDescByType(sellStatus);
                    cardRelationImportInfoDetailVO.setSellStatusName(sellStatusName);
                }

                importInfoDetailDTOList.add(cardRelationImportInfoDetailVO);
            });
        }


        pageData.setPage(pageNum);
        pageData.setCount(page.getTotal());
        pageData.setData(importInfoDetailDTOList);
        return pageData;
    }

    @Override
    public List<ImportNumCardDetailLocationDTO> listImportNumXDetailLocation(CardRelationImportInfoDetailParam cardRelationImportInfoDetailParam) {
        List<ImportNumCardDetailLocationDTO> detailLocationDTOList = new ArrayList<>();
        List<ImportNumCardDetailLocationDTO> locationDTOList = cardRelationMapperExt.listImportNumXDetailLocation(cardRelationImportInfoDetailParam);
        if (CollectionUtils.isNotEmpty(locationDTOList)) {
            Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
            locationDTOList.stream().forEach(detailLocationDTO -> {
                String location = detailLocationDTO.getLocation();
                if (StringUtils.isEmpty(location)) {
                    detailLocationDTO.setLocation("省级");
                    detailLocationDTO.setLocationName("省级");
                } else {
                    detailLocationDTO.setLocationName(locationCodeNameMap.get(location) + "");
                }
                detailLocationDTOList.add(detailLocationDTO);
            });
        }
        return detailLocationDTOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer queryTempIccidLifeStatus(QueryTempIccidLifeStatusParam queryTempIccidLifeStatusParam) {
        BaseAnswer baseAnswer = new BaseAnswer();
        String orderId = queryTempIccidLifeStatusParam.getOrderId();
        String imei = queryTempIccidLifeStatusParam.getImei();
        Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(orderId);
        if (order2cInfo == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订单不存在");
        }

        CardRelationExample cardRelationExample = new CardRelationExample();
        cardRelationExample.createCriteria()
                        .andImeiEqualTo(imei)
                                .andDeleteTimeIsNull();
        List<CardRelation> cardRelationList = listCardRelationByNeed(cardRelationExample);
        if (CollectionUtils.isEmpty(cardRelationList)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "终端信息不存在");
        }

        if (cardRelationList.size() > 1){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "终端信息数据大于1条");
        }

        CardRelation cardRelation = cardRelationList.get(0);
        String tempIccid = cardRelation.getTempIccid();
        String terminalType = cardRelation.getTerminalType();
        String sellStatus = cardRelation.getSellStatus();
        if (!TerminalTypeEnum.M2M_NULL_CARD.getType().equals(terminalType)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "终端信息不是空写卡类型");
        }
        // todo是否只有未销售状态才能进行查询
        if (!SellStatusEnum.NOT_SELL.getType().equals(sellStatus)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "终端信息不是未销售状态");
        }
        if (StringUtils.isEmpty(tempIccid)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "imei对应的临时iccid不存在");
        }

        String status = querySubscriberStatusResponse(tempIccid,orderId,cardRelationExample);
        if (!"6".equals(status)){
            baseAnswer.setStatus(BaseErrorConstant.PARAM_ERROR);
            baseAnswer.setMessage("该imei对应的临时iccid"+tempIccid+"生命周期非测试期无法空写，请重新换终端交付");
        }
        return baseAnswer;

    }

    /**
     * 查询临时iccid状态
     * @param tempIccid
     * @param orderId
     * @return
     */
    private String querySubscriberStatusResponse(String tempIccid,
                                                 String orderId,
                                                 CardRelationExample cardRelationExample){
        QuerySubscriberStatusRequest querySubscriberStatusRequest = new QuerySubscriberStatusRequest();
        querySubscriberStatusRequest.setBeid("002");
        querySubscriberStatusRequest.setIccid(tempIccid);
        querySubscriberStatusRequest.setAuthType("02");
        BaseAnswer<QuerySubscriberStatusResponse> statusResponseBaseAnswer = order2CService.querySubscriberStatus(orderId, querySubscriberStatusRequest);
        QuerySubscriberStatusResponse querySubscriberStatusResponse = statusResponseBaseAnswer.getData();
        String status = querySubscriberStatusResponse.getStatus();
        /*CardRelation updateCardRelation = new CardRelation();
        updateCardRelation.setUpdateTime(new Date());
        if ("6".equals(status)){
            updateCardRelation.setSellStatus(SellStatusEnum.NOT_SELL.getType());
        }else{
            updateCardRelation.setSellStatus(SellStatusEnum.SELL_FAIL.getType());
        }
        updateCardRelationByNeed(updateCardRelation,cardRelationExample);*/
        return status;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleExistCardRelationToInventory(String id) {
        List<CardRelation> cardRelationList;
        if (StringUtils.isEmpty(id)) {
            CardRelationExample cardRelationExample = new CardRelationExample();
            cardRelationExample.createCriteria()
                    .andSellStatusEqualTo(SellStatusEnum.NOT_SELL.getType())
                    .andBeIdIsNotNull()
                    .andDeviceVersionIsNotNull()
                    .andDeleteTimeIsNull();
            cardRelationList = cardRelationMapper.selectByExample(cardRelationExample);
        } else {
            cardRelationList = new ArrayList<>();
            CardRelation cardRelation = cardRelationMapper.selectByPrimaryKey(id);
            if (Optional.ofNullable(cardRelation).isPresent()) {
                if (SellStatusEnum.NOT_SELL.getType().equals(cardRelation.getSellStatus())) {
                    cardRelationList.add(cardRelation);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(cardRelationList)) {
            handleInventoryRelation(cardRelationList, new Date());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleExistSellOutCardRelationToInventory(String id) {
        List<CardRelation> cardRelationList;
        if (StringUtils.isEmpty(id)) {
            CardRelationExample cardRelationExample = new CardRelationExample();
            cardRelationExample.createCriteria()
                    .andSellStatusNotEqualTo(SellStatusEnum.NOT_SELL.getType())
                    .andBeIdIsNotNull()
                    .andDeleteTimeIsNull()
                    .andDeviceVersionIsNotNull();
            cardRelationList = cardRelationMapper.selectByExample(cardRelationExample);
        } else {
            cardRelationList = new ArrayList<>();
            CardRelation cardRelation = cardRelationMapper.selectByPrimaryKey(id);
            if (Optional.ofNullable(cardRelation).isPresent()) {
                if (!SellStatusEnum.NOT_SELL.getType().equals(cardRelation.getSellStatus())) {
                    cardRelationList.add(cardRelation);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(cardRelationList)) {
            handleSellOutInventoryRelation(cardRelationList, new Date());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleExistInventoryToNewInventory(String inventoryId) {
        // 获取配置了库存的原子商品
        AtomOfferingInfoExample atomOfferingInfoExample = new AtomOfferingInfoExample();
        AtomOfferingInfoExample.Criteria infoExampleCriteria = atomOfferingInfoExample.createCriteria();
        if (StringUtils.isEmpty(inventoryId)) {
            infoExampleCriteria.andInventoryIdIsNotNull();
            log.info("step all atom");
        } else {
            infoExampleCriteria.andInventoryIdEqualTo(inventoryId);
            log.info("step one atom");
        }
        List<AtomOfferingInfo> atomOfferingInfoList = atomOfferingInfoMapper.selectByExample(atomOfferingInfoExample);
        log.info("step atom size:{}", atomOfferingInfoList.size());
        if (CollectionUtils.isNotEmpty(atomOfferingInfoList)) {
            Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
            Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
            Date date = new Date();
            // 用于存储beId+设备型号的数据保证唯一性
            Map<String, DkcardxInventoryMainInfo> beIdDeviceVersionMap = new HashMap();
            // 用于存储detail库存详情的数据
            Map<String, DkcardxInventoryDetailInfo> detailInventoryMap = new HashMap();
            // 用于存储原子订单库存相关
            Map<String, DkcardxInventoryAtomInfo> atomInventoryMap = new HashMap<>();
            // 用于新增卡+X库存主要信息表
            List<DkcardxInventoryMainInfo> dkcardxInventoryMainInfoList = new ArrayList<>();
            atomOfferingInfoList.forEach(atomOfferingInfo -> {
                String atomId = atomOfferingInfo.getId();
                String infoInventoryId = atomOfferingInfo.getInventoryId();
                // 根据infoInventoryId获取dkcardx_inventory_config表中的设备型号和省份数据
                // 来确定dkcardx_inventory_main_info中的关联信息，
                // 目前设备型号和省份数据可以确定唯一的设备终端类型
                DkcardxInventoryConfigExample configExample = new DkcardxInventoryConfigExample();
                configExample.createCriteria()
                        .andInventoryIdEqualTo(infoInventoryId)
                        .andSaleStatusEqualTo(SellStatusEnum.NOT_SELL.getType());
                List<DkcardxInventoryConfig> configList
                        = dkcardxInventoryConfigService.getDkcardxInventoryConfigByNeed(configExample);
                if (CollectionUtils.isNotEmpty(configList)) {
                    // 只有一种设备型号生效
                    DkcardxInventoryConfig dkcardxInventoryConfig = configList.get(0);
                    String beId = dkcardxInventoryConfig.getBeId();
                    String deviceVersion = dkcardxInventoryConfig.getDeviceVersion();
                    String beKey = beId.concat(deviceVersion);
                    DkcardxInventoryMainInfoExample mainInfoExample = new DkcardxInventoryMainInfoExample();
                    mainInfoExample.createCriteria()
                            .andBeIdEqualTo(beId)
                            .andDeviceVersionEqualTo(deviceVersion);
                    List<DkcardxInventoryMainInfo> mainInfoList
                            = dkcardxInventoryMainInfoService.listDkcardxInventoryMainInfoByNeed(mainInfoExample);
//                    DkcardxInventoryMainInfo dkcardxInventoryMainInfo = null;
                    log.info("mainInfoList size:{}", JSON.toJSONString(mainInfoList));
                    if (CollectionUtils.isEmpty(mainInfoList)) {
                        log.warn("库存历史数据转移未发现省份编码为beId：{},设备型号为deviceVersion：{}的卡+X数据", beId, deviceVersion);
                        return;
                        /*if (!beIdDeviceVersionMap.containsKey(beKey)){
                            CardRelationExample cardRelationExample = new CardRelationExample();
                            cardRelationExample.createCriteria()
                                    .andBeIdEqualTo(beId)
                                    .andDeviceVersionEqualTo(deviceVersion);
                            List<CardRelation> cardRelationList = listCardRelationByNeed(cardRelationExample);
                            if (CollectionUtils.isEmpty(cardRelationList)){
                                log.warn("库存历史数据转移未发现省份编码为{},设备型号为{}的卡+X数据",beId,deviceVersion);
                                return;
                            }
                            CardRelation cardRelation = cardRelationList.get(0);
                            dkcardxInventoryMainInfo = new DkcardxInventoryMainInfo();
                            dkcardxInventoryMainInfo.setId(BaseServiceUtils.getId());
                            dkcardxInventoryMainInfo.setBeId(beId);
                            dkcardxInventoryMainInfo.setProvinceName((String) provinceCodeNameMap.get(beId));
                            dkcardxInventoryMainInfo.setDeviceVersion(deviceVersion);
                            dkcardxInventoryMainInfo.setTerminalType(cardRelation.getTerminalType());
                            dkcardxInventoryMainInfo.setCustCode(cardRelation.getCustCode());
                            dkcardxInventoryMainInfo.setCustName(cardRelation.getCustName());
                            dkcardxInventoryMainInfo.setTemplateId(cardRelation.getTemplateId());
                            dkcardxInventoryMainInfo.setTemplateName(cardRelation.getTemplateName());
                            dkcardxInventoryMainInfo.setCreateTime(date);
                            dkcardxInventoryMainInfo.setUpdateTime(date);
                            dkcardxInventoryMainInfoList.add(dkcardxInventoryMainInfo);

                            beIdDeviceVersionMap.put(beKey,dkcardxInventoryMainInfo);
                        }else{
                            dkcardxInventoryMainInfo = beIdDeviceVersionMap.get(beKey);
                        }*/
                    }/*else{
                        dkcardxInventoryMainInfo = mainInfoList.get(0);
                        if (!beIdDeviceVersionMap.containsKey(beKey)){
                            beIdDeviceVersionMap.put(beKey,dkcardxInventoryMainInfo);
                        }
                    }*/
                    DkcardxInventoryMainInfo dkcardxInventoryMainInfo = mainInfoList.get(0);
                    /*if (!beIdDeviceVersionMap.containsKey(beKey)){
                        beIdDeviceVersionMap.put(beKey,dkcardxInventoryMainInfo);
                    }*/

                    // 根据infoInventoryId获取dkcardx_inventory_info表中的预占信息
                    DkcardxInventoryInfoExample inventoryInfoExample = new DkcardxInventoryInfoExample();
                    inventoryInfoExample.createCriteria()
                            .andInventoryIdEqualTo(infoInventoryId);
                    List<DkcardxInventoryInfo> inventoryInfoList
                            = dkcardxInventoryInfoService.getDkcardxInventoryInfoByNeed(inventoryInfoExample);
                    log.info("inventoryInfoList size:{}", JSON.toJSONString(inventoryInfoList));
                    if (CollectionUtils.isNotEmpty(inventoryInfoList)) {
                        // 当前原子预占库存
                        Map<String, Integer> atomReserveMap = new HashMap<>();
                        // 在根据inventory_main_id进行预占和当前剩余库存的计算
                        inventoryInfoList.forEach(dkcardxInventoryInfo -> {
                            Integer atomReserveQuantity = atomReserveMap.get(atomId) == null ? 0 : atomReserveMap.get(atomId);
                            String infoBeId = dkcardxInventoryInfo.getBeId();
                            String location = dkcardxInventoryInfo.getLocation();
                            Integer reserveQuatity = dkcardxInventoryInfo.getReserveQuatity();
                            atomReserveMap.put(atomId, atomReserveQuantity + reserveQuatity);
                            String beIdLocation;
//                            Map<String, DkcardxInventoryDetailInfo> detailInfoMap = detailInventoryMap.get(beKey);
                            /*DkcardxInventoryDetailInfo detailInfo;
                            // 判断是否是第一次存储该省份+设备型号的数据
                            if (detailInfoMap == null){
                                detailInfo = addDkcardxInventoryDetailInfo(infoBeId, dkcardxInventoryMainInfo, provinceCodeNameMap,
                                        locationCodeNameMap, reserveQuatity, date, location, deviceVersion,
                                        beKey, detailInventoryMap);
                            }else{
                                // 判断是否已经存储该省份或地市的数据
                                if (StringUtils.isEmpty(location)){
                                    beIdLocation = infoBeId;
                                }else{
                                    beIdLocation = infoBeId.concat(location);
                                }
                                detailInfo = detailInfoMap.get(beIdLocation);
                                // 存在就更新预占信息，不存在就新增
                                if (Optional.ofNullable(detailInfo).isPresent()){
                                    detailInfo.setReserveQuatity(detailInfo.getReserveQuatity()+reserveQuatity);
                                }else{
                                    detailInfo = addDkcardxInventoryDetailInfo(infoBeId,dkcardxInventoryMainInfo,provinceCodeNameMap,
                                            locationCodeNameMap,reserveQuatity,date,location,deviceVersion,beKey,detailInventoryMap);
                                }
                            }*/
                            String inventoryMainInfoId = dkcardxInventoryMainInfo.getId();
                            DkcardxInventoryDetailInfoExample detailInfoExample = new DkcardxInventoryDetailInfoExample();
                            DkcardxInventoryDetailInfoExample.Criteria detailInfoExampleCriteria = detailInfoExample.createCriteria();
                            detailInfoExampleCriteria
                                    .andInventoryMainIdEqualTo(inventoryMainInfoId)
                                    .andBeIdEqualTo(infoBeId);
                            if (StringUtils.isEmpty(location)) {
                                location = "";
                                detailInfoExampleCriteria.andLocationIsNull();
                            } else {
                                detailInfoExampleCriteria.andLocationEqualTo(location);
                            }
                            List<DkcardxInventoryDetailInfo> detailInfoList
                                    = dkcardxInventoryDetailInfoService.listDkcardxInventoryDetailInfoByNeed(detailInfoExample);
                            log.info("detailInfoList size:{}", JSON.toJSONString(detailInfoList));
                            if (CollectionUtils.isEmpty(detailInfoList)) {
                                log.warn("库存主表信息 DkcardxInventoryDetailInfo id为{}，省份信息为{},地市信息为{}没有库存详情", inventoryMainInfoId, infoBeId, location);
                                return;
                            }

                            DkcardxInventoryDetailInfo detailInfo = detailInfoList.get(0);

                            // 判断是否已经存储过该详情信息
                            String detailKey = inventoryMainInfoId.concat(infoBeId).concat(location);
                            DkcardxInventoryDetailInfo inventoryDetailInfo = detailInventoryMap.get(detailKey);
                            if (Optional.ofNullable(inventoryDetailInfo).isPresent()) {
                                inventoryDetailInfo.setReserveQuatity(inventoryDetailInfo.getReserveQuatity() + reserveQuatity);
                            } else {
//                                detailInfo.setReserveQuatity(detailInfo.getReserveQuatity()+reserveQuatity);
                                //重新计算，替换存量的值
                                detailInfo.setReserveQuatity(reserveQuatity);
                                detailInventoryMap.put(detailKey, detailInfo);
                            }

                            // 回写dkcardx_inventory_atom_info表信息
                            DkcardxInventoryAtomInfo dkcardxInventoryAtomInfo = atomInventoryMap.get(atomId);
                            long reserveQuatityLong = Long.parseLong(reserveQuatity + "");
                            if (Optional.ofNullable(dkcardxInventoryAtomInfo).isPresent()) {
                                dkcardxInventoryAtomInfo.setAtomInventory(dkcardxInventoryAtomInfo.getAtomInventory() + reserveQuatityLong);
                            } else {
                                dkcardxInventoryAtomInfo = new DkcardxInventoryAtomInfo();
                                dkcardxInventoryAtomInfo.setId(BaseServiceUtils.getId());
                                dkcardxInventoryAtomInfo.setInventoryDetailId(detailInfo.getId());
                                dkcardxInventoryAtomInfo.setInventoryMainId(detailInfo.getInventoryMainId());
                                dkcardxInventoryAtomInfo.setAtomId(atomId);
                                dkcardxInventoryAtomInfo.setAtomInventory(reserveQuatityLong);
                                dkcardxInventoryAtomInfo.setSpuCode(atomOfferingInfo.getSpuCode());
                                dkcardxInventoryAtomInfo.setSkuCode(atomOfferingInfo.getSkuCode());
                                dkcardxInventoryAtomInfo.setOfferingCode(atomOfferingInfo.getOfferingCode());
                                dkcardxInventoryAtomInfo.setCreateTime(date);
                                dkcardxInventoryAtomInfo.setUpdateTime(date);
                                atomInventoryMap.put(atomId, dkcardxInventoryAtomInfo);
                            }

                        });
                    }
                }
            });

            // 更新库存详情表
            log.info("detailInventoryMap size:", JSON.toJSONString(detailInventoryMap));
            if (MapUtils.isNotEmpty(detailInventoryMap)) {
                detailInventoryMap.values().forEach(detailInfo -> {
                    DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo = new DkcardxInventoryDetailInfo();
                    dkcardxInventoryDetailInfo.setId(detailInfo.getId());
                    Integer reserveQuatity = detailInfo.getReserveQuatity();
                    dkcardxInventoryDetailInfo.setReserveQuatity(detailInfo.getReserveQuatity());
                    dkcardxInventoryDetailInfo.setCurrentInventory(detailInfo.getTotalInventory() - reserveQuatity);
                    dkcardxInventoryDetailInfo.setUpdateTime(date);
                    dkcardxInventoryDetailInfoService.updateDkcardxInventoryDetailInfoById(dkcardxInventoryDetailInfo);
                });
            }

            // 新增库存与原子关系表
            List<DkcardxInventoryAtomInfo> inventoryAtomInfoList = atomInventoryMap.values().stream().collect(Collectors.toList());
            log.info("inventoryAtomInfoList size:{}", JSON.toJSONString(inventoryAtomInfoList));
            if (CollectionUtils.isNotEmpty(inventoryAtomInfoList)) {
                dkcardxInventoryAtomInfoMapper.batchInsert(inventoryAtomInfoList);

                inventoryAtomInfoList.stream().forEach(dkcardxInventoryAtomInfo -> {
                    AtomOfferingInfo atomOfferingInfo = new AtomOfferingInfo();
                    atomOfferingInfo.setId(dkcardxInventoryAtomInfo.getAtomId());
                    atomOfferingInfo.setInventoryMainId(dkcardxInventoryAtomInfo.getInventoryMainId());
                    atomOfferingInfo.setUpdateTime(date);
                    atomOfferingInfoMapper.updateByPrimaryKeySelective(atomOfferingInfo);
                });
            }
        }
    }

    @Override
    public void handleInventoryMisError(String dkDetailId, String dkAtomId, Long reserveNum) {
        log.info("handleInventoryMisError Enter: dkDetailId = {}, dkAtomId = {}, reserveNum = {}", dkDetailId, dkAtomId, reserveNum);
        DkcardxInventoryDetailInfo detailInfo = dkcardxInventoryDetailInfoMapper.selectByPrimaryKey(dkDetailId);
        DkcardxInventoryAtomInfo atomInfo = dkcardxInventoryAtomInfoMapper.selectByPrimaryKey(dkAtomId);

        Integer dkTotal = detailInfo.getTotalInventory();
        Integer dkReserve = reserveNum.intValue();
        Integer dkCurrent = dkTotal - dkReserve;

        detailInfo.setReserveQuatity(dkReserve);
        detailInfo.setCurrentInventory(dkCurrent);
        detailInfo.setTotalInventory(dkTotal);
        detailInfo.setUpdateTime(new Date());
        atomInfo.setAtomInventory(reserveNum);
        atomInfo.setUpdateTime(new Date());
        log.info("handleInventoryMisError dkReserve = {}, dkCurrent = {}, dkTotal = {}, atomInventory = {}", dkReserve, dkCurrent, dkTotal, reserveNum);
        log.info("handleInventoryMisError detailInfo = {}", detailInfo);
        log.info("handleInventoryMisError atomInfo = {}", atomInfo);
        dkcardxInventoryDetailInfoMapper.updateByPrimaryKeySelective(detailInfo);
        dkcardxInventoryAtomInfoMapper.updateByPrimaryKeySelective(atomInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleNoImportNumCard() {
        List<NoImportNumCardDTO> noImportNumCardDTOList = cardRelationMapperExt.listNoImportNumCard();
        if (CollectionUtils.isNotEmpty(noImportNumCardDTOList)) {
            // 用于表示唯一的批次+省份+设备型号信息
            Map<String, CardRelationImportInfo> importInfoMap = new HashMap<>();
            noImportNumCardDTOList.forEach(noImportNumCardDTO -> {
                Date createTime = noImportNumCardDTO.getCreateTime();
                String importNum = DateUtils.dateToStr(createTime, DateUtils.DATETIME_FORMAT_NO_SYMBOL);
                String beId = noImportNumCardDTO.getBeId();
                String deviceVersion = noImportNumCardDTO.getDeviceVersion();
                String userId = "-";
                String userName = "-";

                BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.getUserPartnerPrimaryByBeId(beId);
                if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                    /*log.warn("省份编码为{}获取合作伙伴主账号错误", beId);
                    throw new BusinessException("10004", "省份编码为" + beId + "获取合作伙伴主账号错误");*/
                }else{
                    Data4User data4User = data4UserBaseAnswer.getData();

                    if (!Optional.ofNullable(data4User).isPresent()){
                        /*log.warn("省份编码为{}未配置合作伙伴主账号", beId);
                        throw new BusinessException("10004", "省份编码为" + beId + "合作伙伴主账号未找到");*/
                    }else{
                        userId = data4User.getUserId();
                        userName = data4User.getName();
                    }
                }

                String mapKey = "";
                if (StringUtils.isEmpty(deviceVersion)){
                    mapKey = importNum.concat(beId);
                }else{
                    mapKey = importNum.concat(beId).concat(deviceVersion);
                }


                CardRelationImportInfo relationImportInfo = importInfoMap.get(mapKey);
                if (Optional.ofNullable(relationImportInfo).isPresent()) {
                    Integer importCount = relationImportInfo.getImportCount()+1;
                    relationImportInfo.setImportCount(importCount);
                } else {

                    CardRelationImportInfo cardRelationImportInfo
                            = setCardRelationImportInfo(importNum, deviceVersion, beId, userId, userName, createTime);

                    importInfoMap.put(mapKey, cardRelationImportInfo);
                }
                // 更新x终端导入批次等信息
                CardRelation updateCardRelation = new CardRelation();
                updateCardRelation.setId(noImportNumCardDTO.getId());
                updateCardRelation.setImportNum(importNum);
                updateCardRelation.setCreatedUser(userId);
                updateCardRelation.setCreatedUserName(userName);
                cardRelationMapper.updateByPrimaryKeySelective(updateCardRelation);
            });

            if (MapUtils.isNotEmpty(importInfoMap)) {
                List<CardRelationImportInfo> relationImportInfoList
                        = importInfoMap.values().stream().collect(Collectors.toList());
                cardRelationImportInfoService.batchAddCardRelationImportInfo(relationImportInfoList);
            }
        }
    }

    @Override
    public void handleInventoryMisErrorAtomInfo(String atomId, Long reserveNum) {
        log.info("handleInventoryMisErrorAtomInfo Enter:  atomId = {}, reserveNum = {}", atomId, reserveNum);
        AtomOfferingInfo atomOfferingInfo = atomOfferingInfoMapper.selectByPrimaryKey(atomId);
        log.info("handleInventoryMisErrorAtomInfo Enter:  ReserveInventory = {}", atomOfferingInfo.getReserveInventory());
        atomOfferingInfo.setReserveInventory(reserveNum);
        atomOfferingInfo.setUpdateTime(new Date());
        log.info("handleInventoryMisErrorAtomInfo atomInfo = {}", atomOfferingInfo);
        atomOfferingInfoMapper.updateByPrimaryKeySelective(atomOfferingInfo);
    }

    @Override
    public void handleInventoryMisErrorAtomCard(String dkAtomId, Long reserveNum) {
        log.info("handleInventoryMisErrorAtomCard Enter: dkAtomId = {}, reserveNum = {}", dkAtomId, reserveNum);
        DkcardxInventoryAtomInfo atomInfo = dkcardxInventoryAtomInfoMapper.selectByPrimaryKey(dkAtomId);
        log.info("handleInventoryMisErrorCardDetailOld Enter: AtomInventory = {}",atomInfo.getAtomInventory());
        atomInfo.setAtomInventory(reserveNum);
        atomInfo.setUpdateTime(new Date());
        log.info("handleInventoryMisErrorAtomCard atomInfo = {}",atomInfo);
        dkcardxInventoryAtomInfoMapper.updateByPrimaryKeySelective(atomInfo);
    }

    @Override
    public void handleInventoryMisErrorCardDetail(String dkDetailId, Long reserveNum) {
        log.info("handleInventoryMisErrorCardDetail Enter: dkDetailId = {}, reserveNum = {}",dkDetailId, reserveNum);
        DkcardxInventoryDetailInfo detailInfo = dkcardxInventoryDetailInfoMapper.selectByPrimaryKey(dkDetailId);
        log.info("handleInventoryMisErrorCardDetailOld Enter: ReserveQuatity = {}, CurrentInventory = {}",detailInfo.getReserveQuatity(), detailInfo.getCurrentInventory());
        Integer dkTotal = detailInfo.getTotalInventory();
        Integer dkReserve = reserveNum.intValue();
        Integer dkCurrent = dkTotal - dkReserve;
        detailInfo.setReserveQuatity(dkReserve);
        detailInfo.setCurrentInventory(dkCurrent);
        detailInfo.setTotalInventory(dkTotal);
        detailInfo.setUpdateTime(new Date());
        log.info("handleInventoryMisErrorCardDetail dkReserve = {}, dkCurrent = {}, dkTotal = {}, atomInventory = {}",dkReserve, dkCurrent, dkTotal, reserveNum);
        dkcardxInventoryDetailInfoMapper.updateByPrimaryKeySelective(detailInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleErrorImei(InputStream inputStream, HttpServletRequest request, HttpServletResponse response) throws Exception {
        ExcelImportResult<ImportErrorImeiDTO> result = ExcelUtils.importExcel(inputStream, 0, 1,
                excelImeiErrorImportHandler, ImportErrorImeiDTO.class);
        List<ImportErrorImeiDTO> failList = result.getFailList();
        List<ImportErrorImeiDTO> cardExcelImportList = result.getList();
        int successSize = cardExcelImportList.size();
        int failSize = failList.size();
        if (CollectionUtils.isNotEmpty(failList)) {
            long millis = System.currentTimeMillis();
            String fileName = "处理错误的imei信息".concat(millis + "-fail.xls");
            String failFilePath = getKxFailFilePath(fileName);
            FileOutputStream fos = new FileOutputStream(failFilePath);
            result.getFailWorkbook().write(fos);
            fos.close();
            File downErrorFile = new File(failFilePath);
            response.setHeader("statecode", "99998");
            response.setHeader("message", URLEncoder.encode("失败".concat(failSize + "").concat("条，详见结果文件"), "UTF-8").replaceAll("\\+", "%20"));
            fileUtils.downloadFile(downErrorFile, fileName, request, response);
            return;
        }

        if (CollectionUtils.isEmpty(cardExcelImportList)
                && CollectionUtils.isEmpty(failList)) {
            response.setHeader("statecode", "99999");
            response.setHeader("message", URLEncoder.encode("导入的文件不能为空数据", "UTF-8").replaceAll("\\+", "%20"));
            return;
        }
        // 如果没有成功的数据直接返回，因为导入失败会进行信息提示，所以这里直接返回
        if (successSize == 0) {
            return;
        }

        Date date = new Date();
        List<CardRelation> errorList = new ArrayList<>();
        List<CardRelation> relationList = new ArrayList<>();
        cardExcelImportList.forEach(importErrorImeiDTO -> {
            String errorImei = importErrorImeiDTO.getErrorImei();
            String imei = importErrorImeiDTO.getImei();
            CardRelationExample cardRelationExample = new CardRelationExample();
            cardRelationExample.createCriteria()
                    .andImeiEqualTo(errorImei)
                    .andDeleteTimeIsNull();
            List<CardRelation> errorCardRelationList = listCardRelationByNeed(cardRelationExample);
            CardRelation errorCardRelation = errorCardRelationList.get(0);
            errorList.add(errorCardRelation);
            String orderId = errorCardRelation.getOrderId();
            String orderAtomInfoId = errorCardRelation.getOrderAtomInfoId();


            // 更新状态为可用
            UpdateCardOrderToNullParam updateCardOrderToNullParam = new UpdateCardOrderToNullParam();
            BeanUtils.copyProperties(errorCardRelation,updateCardOrderToNullParam);
            updateCardOrderToNull(updateCardOrderToNullParam);

            cardRelationExample = new CardRelationExample();
            cardRelationExample.createCriteria()
                    .andImeiEqualTo(imei)
                    .andDeleteTimeIsNull();
            List<CardRelation> cardRelationList = listCardRelationByNeed(cardRelationExample);
            CardRelation cardRelation = cardRelationList.get(0);
            relationList.add(cardRelation);

            // 更新状态为不可用
            CardRelation updateCardRelation = new CardRelation();
            updateCardRelation.setOrderId(orderId);
            updateCardRelation.setOrderAtomInfoId(orderAtomInfoId);
            updateCardRelation.setSellStatus(errorCardRelation.getSellStatus());
            CardRelationExample updateCardRelationExample = new CardRelationExample();
            updateCardRelationExample.createCriteria()
                    .andIdEqualTo(cardRelation.getId())
                    .andDeleteTimeIsNull();
            updateCardRelationByNeed(updateCardRelation,updateCardRelationExample);

            // 更新sku_msisdn_relation关联关系,码号来源于订单同步
            SkuMsisdnRelation skuMsisdnRelation = new SkuMsisdnRelation();
            skuMsisdnRelation.setImei(imei);
            skuMsisdnRelation.setTempIccid(cardRelation.getTempIccid());
            SkuMsisdnRelationExample skuMsisdnRelationExample = new SkuMsisdnRelationExample();
            skuMsisdnRelationExample.createCriteria()
                    .andImeiEqualTo(errorImei)
                    .andOrderIdEqualTo(orderId)
                    .andOrderAtomInfoIdEqualTo(orderAtomInfoId);
            skuMsisdnRelationService.updateSkuMsisdnRelationByNeed(skuMsisdnRelation,skuMsisdnRelationExample);

            // 更新card_choose_delivery批量交付导入表
            CardChooseDelivery cardChooseDelivery = new CardChooseDelivery();
            cardChooseDelivery.setImei(imei);
            cardChooseDelivery.setDeviceVersion(cardRelation.getDeviceVersion());
            cardChooseDelivery.setTempIccid(cardRelation.getTempIccid());
            CardChooseDeliveryExample cardChooseDeliveryExample = new CardChooseDeliveryExample();
            cardChooseDeliveryExample.createCriteria()
                    .andAtomOrderIdEqualTo(orderAtomInfoId)
                    .andImeiEqualTo(errorImei);
            cardChooseDeliveryService.updateCardChooseDeliveryByNeed(cardChooseDelivery,cardChooseDeliveryExample);


        });

        // 将错误的imei进行卡+X终端库存增加处理
        handleInventoryRelation(errorList, date);

        // 将正确的imei进行卡+X终端库存减少处理
        // 用于存放减少的库存信息
        Map<String,Integer> reduceMap = new HashMap<>();
        relationList.forEach(cardRelation -> {
            String beId = cardRelation.getBeId();
            String deviceVersion = cardRelation.getDeviceVersion();
            String terminalType = cardRelation.getTerminalType();
            String custCode = cardRelation.getCustCode();
            String templateId = cardRelation.getTemplateId();
            String location = cardRelation.getLocation();
            // 判断该信息在数据库中是否已经存在
            DkcardxInventoryMainInfoExample mainInfoExample = new DkcardxInventoryMainInfoExample();
            DkcardxInventoryMainInfoExample.Criteria mainInfoCriteria = mainInfoExample.createCriteria()
                    .andBeIdEqualTo(beId)
                    .andDeviceVersionEqualTo(deviceVersion)
                    .andTerminalTypeEqualTo(terminalType);
            if (StringUtils.isNotEmpty(custCode)) {
                mainInfoCriteria.andCustCodeEqualTo(custCode);
            } else {
                mainInfoCriteria.andCustCodeIsNull();
            }

            if (StringUtils.isNotEmpty(templateId)) {
                mainInfoCriteria.andTemplateIdEqualTo(templateId);
            } else {
                mainInfoCriteria.andTemplateIdIsNull();
            }
            List<DkcardxInventoryMainInfo> mainInfoListExist = dkcardxInventoryMainInfoService.listDkcardxInventoryMainInfoByNeed(mainInfoExample);
            if (CollectionUtils.isEmpty(mainInfoListExist)){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"imei为"+cardRelation.getImei()+"没有库存信息");
            }

            DkcardxInventoryMainInfo dkcardxInventoryMainInfo = mainInfoListExist.get(0);

            String key = dkcardxInventoryMainInfo.getId().concat("-")
                            .concat(beId).concat("-")
                            .concat(StringUtils.isEmpty(location)?"999999":location);
            Integer reduceCount = reduceMap.get(key);
            if (reduceCount == null){
                reduceMap.put(key,1);
            }else{
                reduceCount = reduceCount + 1;
                reduceMap.put(key,reduceCount);
            }

        });
        if (MapUtils.isNotEmpty(reduceMap)){
            reduceMap.forEach((key,reduceCount)->{
                String[] param = key.split("-");
                DkcardxInventoryDetailInfoExample detailInfoExample = new DkcardxInventoryDetailInfoExample();
                DkcardxInventoryDetailInfoExample.Criteria detailInfoCriteria = detailInfoExample.createCriteria()
                        .andInventoryMainIdEqualTo(param[0])
                        .andBeIdEqualTo(param[1]);
                if ("999999".equals(param[2])){
                    detailInfoCriteria.andProvinceAliasNameEqualTo("省级")
                            .andLocationIsNull();
                }else{
                    detailInfoCriteria.andLocationEqualTo(param[2]);
                }
                List<DkcardxInventoryDetailInfo> detailInfoList = dkcardxInventoryDetailInfoService.listDkcardxInventoryDetailInfoByNeed(detailInfoExample);
                if (CollectionUtils.isEmpty(detailInfoList)){
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"信息为"+key+"的终端库存信息不存在");
                }
                if (detailInfoList.size()>1){
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"信息为"+key+"的终端库存信息不存在");
                }
                DkcardxInventoryDetailInfo detailInfo = detailInfoList.get(0);
                DkcardxInventoryDetailInfo updateDetailInfo = new DkcardxInventoryDetailInfo();
                updateDetailInfo.setId(detailInfo.getId());
                updateDetailInfo.setCurrentInventory(detailInfo.getCurrentInventory() - reduceCount);
                updateDetailInfo.setTotalInventory(detailInfo.getTotalInventory() - reduceCount);
                dkcardxInventoryDetailInfoService.updateDkcardxInventoryDetailInfoById(updateDetailInfo);
            });
        }
    }

    @Override
    public BaseAnswer handleInventoryNotInAtomInfo(DkcardxInventoryAtomInfo dkcardxInventoryAtomInfo) {
        Date date = new Date();
        dkcardxInventoryAtomInfo.setId(BaseServiceUtils.getId());
        dkcardxInventoryAtomInfo.setCreateTime(date);
        dkcardxInventoryAtomInfo.setUpdateTime(date);
        dkcardxInventoryAtomInfoMapper.insert(dkcardxInventoryAtomInfo);
        return new BaseAnswer();
    }

    private DkcardxInventoryDetailInfo addDkcardxInventoryDetailInfo(String beId,
                                                                     DkcardxInventoryMainInfo dkcardxInventoryMainInfo,
                                                                     Map<Object, Object> provinceCodeNameMap,
                                                                     Map<Object, Object> locationCodeNameMap,
                                                                     Integer reserveQuatity,
                                                                     Date date,
                                                                     String location,
                                                                     String deviceVersion,
                                                                     String beKey,
                                                                     Map<String, HashMap<String, DkcardxInventoryDetailInfo>> detailInventoryMap) {
        String beIdLocation;
        DkcardxInventoryDetailInfo detailInfo = new DkcardxInventoryDetailInfo();
        detailInfo.setId(BaseServiceUtils.getId());
        detailInfo.setInventoryMainId(dkcardxInventoryMainInfo.getId());
        detailInfo.setBeId(beId);
        detailInfo.setProvinceName(provinceCodeNameMap.get(beId) + "");
        detailInfo.setReserveQuatity(reserveQuatity);
        detailInfo.setUpdateTime(date);
        detailInfo.setCreateTime(date);
        // 判断地市是否为空
        if (StringUtils.isEmpty(location)) {
            beIdLocation = beId;
            detailInfo.setProvinceAliasName("省级");
            // 获取总库存
            CardRelationExample cardRelationExample = new CardRelationExample();
            cardRelationExample.createCriteria()
                    .andBeIdEqualTo(beId)
                    .andDeviceVersionEqualTo(deviceVersion)
                    .andDeleteTimeIsNull()
                    .andLocationIsNull();
            List<CardRelation> cardRelationList = listCardRelationByNeed(cardRelationExample);
            if (CollectionUtils.isEmpty(cardRelationList)) {
                detailInfo.setTotalInventory(0);
            } else {
                detailInfo.setTotalInventory(cardRelationList.size());
            }
        } else {
            beIdLocation = beId.concat(location);
            detailInfo.setLocation(location);
            detailInfo.setCityName(locationCodeNameMap.get(location) + "");
            // 获取总库存
            CardRelationExample cardRelationExample = new CardRelationExample();
            cardRelationExample.createCriteria()
                    .andBeIdEqualTo(beId)
                    .andDeviceVersionEqualTo(deviceVersion)
                    .andDeleteTimeIsNull()
                    .andLocationEqualTo(location);
            List<CardRelation> cardRelationList = listCardRelationByNeed(cardRelationExample);
            if (CollectionUtils.isEmpty(cardRelationList)) {
                detailInfo.setTotalInventory(0);
            } else {
                detailInfo.setTotalInventory(cardRelationList.size());
            }
        }
        HashMap<String, DkcardxInventoryDetailInfo> detailMap = new HashMap();
        detailMap.put(beIdLocation, detailInfo);
        detailInventoryMap.put(beKey, detailMap);
        return detailInfo;
    }

    @Override
    public BaseAnswer<List<CardRalationCountByAreaVO>> cardRalationCountByArea(String orderId) {
        List<CardRalationCountByAreaVO> resultList = new ArrayList<>();

        /*Order2cAtomInfo order2cAtomInfo = order2cAtomInfoMapper.selectByPrimaryKey(atomOrderId);
        if (order2cAtomInfo == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "原子订单不存在");
        }*/
        Order2cInfoExample order2cInfoExample = new Order2cInfoExample();
        order2cInfoExample.createCriteria()
                        .andOrderIdEqualTo(orderId);
        List<Order2cInfo> order2cInfoList = order2cInfoMapper.selectByExample(order2cInfoExample);
        if (CollectionUtils.isEmpty(order2cInfoList)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订单不存在");
        }
        //支持多原子订单，需要查询订单下的所有X硬件原子
        Order2cAtomInfoExample xAtomOrderExample = new Order2cAtomInfoExample().createCriteria()
                .andOrderIdEqualTo(orderId)
                .andAtomOfferingClassEqualTo(AtomOfferingClassEnum.X.getAtomOfferingClass())
                .example();
        List<Order2cAtomInfo> xAtomOrderList = order2cAtomInfoMapper.selectByExample(xAtomOrderExample);

        for (Order2cAtomInfo xAtomOrder : xAtomOrderList) {
            CardRalationCountByAreaVO cardRalationCountByAreaVO = new CardRalationCountByAreaVO();
            cardRalationCountByAreaVO.setAtomOrderId(xAtomOrder.getId());
            cardRalationCountByAreaVO.setOrderId(orderId);
            resultList.add(cardRalationCountByAreaVO);

            AtomOfferingInfoExample atomExample = new AtomOfferingInfoExample().createCriteria()
                    .andSpuCodeEqualTo(xAtomOrder.getSpuOfferingCode())
                    .andSkuCodeEqualTo(xAtomOrder.getSkuOfferingCode())
                    .andOfferingCodeEqualTo(xAtomOrder.getAtomOfferingCode())
                    .example();
            List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper.selectByExample(atomExample);
            if (CollectionUtils.isEmpty(atomOfferingInfos)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "原子商品:spuCode:"+xAtomOrder.getSpuOfferingCode()+",skuCode:"+xAtomOrder.getSkuOfferingCode()+",atomCode:"+xAtomOrder.getAtomOfferingCode()+"不存在");
            }
            AtomOfferingInfo atomOfferingInfo = atomOfferingInfos.get(0);
            DkcardxInventoryMainInfoExample inventoryMainExample = new DkcardxInventoryMainInfoExample().createCriteria().andIdEqualTo(atomOfferingInfo.getInventoryMainId()).example();
            List<DkcardxInventoryMainInfo> dkcardxInventoryMainInfos = dkcardxInventoryMainInfoService.listDkcardxInventoryMainInfoByNeed(inventoryMainExample);
            if(CollectionUtils.isEmpty(dkcardxInventoryMainInfos)){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"原子订单"+xAtomOrder.getId()+"对应的原子商品未配置库存");
            }
            String deviceVersion = dkcardxInventoryMainInfos.get(0).getDeviceVersion();
            cardRalationCountByAreaVO.setDeviceVersion(deviceVersion);
            String cardContainingTerminal = atomOfferingInfo.getCardContainingTerminal();
            cardRalationCountByAreaVO.setHasCard("2".equals(cardContainingTerminal)? false : true);
            List<CardRalationCountByAreaVO.AreaCountItem> list = new ArrayList<>();
            cardRalationCountByAreaVO.setList(list);

            //获取预占的区域和数量信息，根据区域去拉取对应区域可选的x终端
            String redisData = stringRedisTemplate.opsForValue().get(REDIS_COMMIT_INVENTORY_AREA + xAtomOrder.getOrderId() + "_" + atomOfferingInfo.getId());
            if (StringUtils.isEmpty(redisData)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订单:"+xAtomOrder.getOrderId()+",atomId:"+atomOfferingInfo.getId()+"预占仓库信息不存在");
            }
            List<InventoryAreaDTO> inventoryAreaDTOList = JSON.parseArray(redisData, InventoryAreaDTO.class);
            Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
            Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();
            for (InventoryAreaDTO inventoryAreaDTO : inventoryAreaDTOList) {
                String areaCode = inventoryAreaDTO.getAreaCode();
                Long reserveQuantity = inventoryAreaDTO.getReserveQuantity();
                if (areaCode.length() == 3) {
                    //省
                    CardRalationCountByAreaVO.AreaCountItem vo = new CardRalationCountByAreaVO.AreaCountItem();
                    vo.setAreaName("省级");
                    vo.setCount(reserveQuantity);
                    list.add(vo);
                } else if (areaCode.length() == 4) {
                    //市
                    CardRalationCountByAreaVO.AreaCountItem vo = new CardRalationCountByAreaVO.AreaCountItem();
                    vo.setAreaName((String) locationCodeNameMap.get(areaCode));
                    vo.setCount(reserveQuantity);
                    list.add(vo);
                }
            }
        }
        return BaseAnswer.success(resultList);
    }

    @Override
    public void batchDeliver(InputStream inputStream,
                             LoginIfo4Redis loginIfo4Redis,
                             HttpServletResponse response,
                             String importRedisKey) throws Exception{

        String userId = loginIfo4Redis.getUserId();
        String phone = loginIfo4Redis.getPhone();
        Date date = new Date();
        long millis = System.currentTimeMillis();

        /*String importRedisKey = "import_kx_order_deliver_".concat(userId);
        Boolean isUserImport = stringRedisTemplate.hasKey(importRedisKey);
        if (isUserImport){
            response.setHeader("statecode", "99999");
            response.setHeader("message", URLEncoder.encode("有文件正在导入中，在该文件成功前请不要再次导入", "UTF-8").replaceAll("\\+", "%20"));
            return;
        }*/

//        stringRedisTemplate.opsForValue().set(importRedisKey,importRedisKey);


        try {
            ExcelReader readerBuilder = EasyExcel.read(inputStream).build();
            List<CardXBatchDeliverExcelDTO> batchDeliverExcelDTOS = new ExcelReaderSheetBuilder(readerBuilder).sheetNo(0)
                    .head(CardXBatchDeliverExcelDTO.class).headRowNumber(1).doReadSync();
            batchDeliverExcelDTOS = batchDeliverExcelDTOS.stream().filter(item -> {
                try {
                    return !ObjectIsNullUitl.isAllFieldNull(item);
                } catch (Exception exception) {
                    return false;
                }
            }).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(batchDeliverExcelDTOS)){
                // 处理交付完成提醒
                handleImportOrderKxDeliverResult(userId,phone,0,0,0,null,0,importRedisKey,2,date);

                stringRedisTemplate.delete(importRedisKey);
                log.info("文件无有效数据");
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"文件无有效数据");
            }
            //处理数据,避免合并单元格造成的空数据，将订单和交付信息对应起来，并且把订单数据拆分为原子订单数据
            List<CardXBatchDeliverResultDTO> resultDTOList = new ArrayList<>();
            Map<String,String> orderIdAndFailReasonMap = new HashMap<>();
            excel2ResultDTO(resultDTOList,batchDeliverExcelDTOS,orderIdAndFailReasonMap);

            List<String> orderLogList = new ArrayList<>();
            // key- skuCode,value - cardType, 保存对应关系，减少数据库查询
            Map<String,String> skuCodeAndCardTypeMap = new HashMap<>();
            //交付
            A: for (CardXBatchDeliverResultDTO dto : resultDTOList) {
                String orderId = dto.getOrderId();
                Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(orderId);
                if(order2cInfo == null){
                    dto.setFailReason("订单不存在");
                    dto.setResult("失败");
                    continue A;
                }
                String deviceVersionFailReason = orderIdAndFailReasonMap.get(order2cInfo.getOrderId());
                if(StringUtils.isNotEmpty(deviceVersionFailReason)){
                    dto.setFailReason(deviceVersionFailReason);
                    dto.setResult("失败");
                    continue A;
                }
                //校验数据
                List<DeliverCardParam.AtomDeliverItem> atomDeliveryItemList = dto.getAtomDeliveryItemList();
                String cardType = null;
                for (DeliverCardParam.AtomDeliverItem atomDeliverItem : atomDeliveryItemList) {
                    List<DeliverCardParam.CardItem> cardItemList = atomDeliverItem.getCardList();
                    /*String atomOrderId = atomDeliverItem.getAtomOrderId();
                    Order2cAtomInfo order2cAtomInfo = order2cAtomInfoMapper.selectByPrimaryKey(atomOrderId);
                    if(order2cAtomInfo == null){
                        dto.setFailReason("原子订单"+atomOrderId+"查询失败");
                        dto.setResult("失败");
                        continue A;
                    }*/
                    Order2cAtomInfoExample order2cAtomInfoExample = new Order2cAtomInfoExample();
                    order2cAtomInfoExample.createCriteria()
                            .andOrderIdEqualTo(orderId);
                    List<Order2cAtomInfo> order2cAtomInfoList = order2cAtomInfoMapper.selectByExample(order2cAtomInfoExample);
                    if(CollectionUtils.isEmpty(order2cAtomInfoList)){
                        dto.setFailReason("订单"+orderId+"查询失败");
                        dto.setResult("失败");
                        continue A;
                    }

                    for (int i = 0; i < order2cAtomInfoList.size(); i++) {
                        Order2cAtomInfo order2cAtomInfo = order2cAtomInfoList.get(i);
                        String atomOrderId = order2cAtomInfo.getId();
                        //校验x终端是否纳入当前订单商品的库存
                        OrderProductCardRelationListParam param = new OrderProductCardRelationListParam();
//                        param.setAtomOrderId(atomOrderId);
                        param.setOrderId(order2cAtomInfo.getOrderId());
                        BaseAnswer<List<OrderProductCardRelationListVO>> cardRelationAnswer = orderProductCardRelationList(param);
                        if(cardRelationAnswer == null || !cardRelationAnswer.getStatus().getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())){
                            dto.setFailReason("查询纳入当前订单商品库存的x终端列表失败");
                            dto.setResult("失败");
                            continue A;
                        }
                        List<OrderProductCardRelationListVO> data = cardRelationAnswer.getData();
                        List<String> correctImeiList = data.stream().map(d -> {
                            return d.getImei();
                        }).collect(Collectors.toList());
                        List<DeliverCardParam.CardItem> incorrectImeiCardList = cardItemList.stream().filter(c -> {
                            String imei = c.getImei();
                            return !correctImeiList.contains(imei);
                        }).collect(Collectors.toList());
                        if(CollectionUtils.isNotEmpty(incorrectImeiCardList)){
                            List<String> incorrectImeiList = incorrectImeiCardList.stream().map(c -> {
                                return c.getImei();
                            }).collect(Collectors.toList());
                            dto.setFailReason("imei:"+String.join(",",incorrectImeiList)+"不属于当前订单的商品库存");
                            dto.setResult("失败");
                            continue A;
                        }
                        String skuOfferingCode = order2cAtomInfo.getSkuOfferingCode();
                        cardType = skuCodeAndCardTypeMap.get(skuOfferingCode);
                        if(cardType == null){
                            SkuOfferingInfoHistoryExample skuExample = new SkuOfferingInfoHistoryExample().createCriteria()
                                    .andSpuCodeEqualTo(order2cAtomInfo.getSpuOfferingCode())
                                    .andSpuOfferingVersionEqualTo(order2cAtomInfo.getSpuOfferingVersion())
                                    .andOfferingCodeEqualTo(order2cAtomInfo.getSkuOfferingCode())
                                    .andSkuOfferingVersionEqualTo(order2cAtomInfo.getSkuOfferingVersion()).example();
                            List<SkuOfferingInfoHistory> skuOfferingInfoHistories = skuOfferingInfoHistoryMapper.selectByExample(skuExample);
                            if (CollectionUtils.isEmpty(skuOfferingInfoHistories)) {
                                dto.setFailReason("订单规格商品版本信息不存在");
                                dto.setResult("失败");
                                continue A;
                            }
                            SkuOfferingInfoHistory skuOfferingInfoHistory = skuOfferingInfoHistories.get(0);
                            cardType = skuOfferingInfoHistory.getCardType();
                            skuCodeAndCardTypeMap.put(skuOfferingCode,cardType);
                        }

                        if(CardTypeEnum.CHA_BO_CARD.getType().equals(cardType)){
                            //插拔卡需要校验码号
                            BaseAnswer<List<String>> cardAnswer = cardInfoService.orderProductTemplateCardList(orderId,null);
                            if(cardAnswer == null || !cardAnswer.getStatus().getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())){
                                dto.setFailReason("查询和当前订单商品的开卡模板名称一致的码号列表失败");
                                dto.setResult("失败");
                                continue A;
                            }
                            List<String> correctMsisdnList = cardAnswer.getData();
                            List<DeliverCardParam.CardItem> incorrectMsisdnCardList = cardItemList.stream().filter(c -> {
                                String msisdn = c.getMsisdn();
                                return !correctMsisdnList.contains(msisdn);
                            }).collect(Collectors.toList());
                            if(CollectionUtils.isNotEmpty(incorrectMsisdnCardList)){
                                List<String> incorrectMsisdnList = incorrectMsisdnCardList.stream().map(c -> {
                                    return c.getMsisdn();
                                }).collect(Collectors.toList());
                                dto.setFailReason("码号:"+String.join(",",incorrectMsisdnList)+"和订单商品的开卡模板名称不一致");
                                dto.setResult("失败");
                                continue A;
                            }
                        }
                    }

                }

                DeliverCardParam deliverCardParam = new DeliverCardParam();
                deliverCardParam.setAtomList(atomDeliveryItemList);
                //复用单个订单交付接口，这里使用内部事务，避免互相影响
                try {
                    BaseAnswer baseAnswer = cardRelationServiceImpl.deliverCard(deliverCardParam, false, cardType,loginIfo4Redis);
                    if(baseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())){
                        dto.setResult("成功");
                        orderLogList.add("订单号:"+orderId);
                    }else if(baseAnswer.getStatus().getStateCode().equals(StatusConstant.CARD_CHECK_FAILED.getStateCode())){
                        //码号校验失败,返回了所有的码号数据
                        List<QrySubscribersResponse.MsisdnInfo> msisdnInfoList = (List<QrySubscribersResponse.MsisdnInfo>) baseAnswer.getData();
                        String failReason = getCardCheckFailReason(msisdnInfoList);
                        dto.setResult("失败");
                        dto.setFailReason(failReason);
                    }
                } catch (Exception e) {
                    dto.setResult("失败");
                    log.error("订单:{}交付失败",orderId,e);
                    if(e instanceof BusinessException){
                        BusinessException businessException = (BusinessException) e;
                        dto.setFailReason(businessException.getStatus().getMessage());
                    }else {
                        dto.setFailReason("内部错误，请联系管理员");
                    }
                    continue;
                }

            }
            if(CollectionUtils.isNotEmpty(orderLogList)){
                //记录日志
                String logContent = "【批量交付】\n" + String.join(System.getProperty("line.separator"), orderLogList);
                logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.GOODS_ORDER.code,
                        logContent, LogResultEnum.LOG_SUCESS.code, null);
            }

            int totalCount = resultDTOList.size();

            List<CardXBatchDeliverResultDTO> failList = resultDTOList.stream().filter(r -> {
                //只导出失败的部分
                return r.getResult().equals("失败");
            }).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(failList)){
                int failCount = failList.size();
                int succCount = totalCount - failCount;
                //导出交付结果excel,合并设备型号，imei/sn，码号表头
                String sheetName = "批量交付";
                ExportParams exportParams = new ExportParams();
                exportParams.setSheetName(sheetName);
                exportParams.setCreateHeadRows(true);
                exportParams.setType(ExcelType.XSSF);
                Workbook workbook = ExcelExportUtil.exportExcel(exportParams, CardXBatchDeliverResultDTO.class, failList);
                Sheet sheet = workbook.getSheet(sheetName);
                sheet.addMergedRegion(new CellRangeAddress(0,1,1,1));
                sheet.addMergedRegion(new CellRangeAddress(0,1,2,2));
                sheet.addMergedRegion(new CellRangeAddress(0,1,3,3));

                /*String excelName = "批量交付失败结果";
                excelName = URLEncoder.encode(excelName, "UTF-8");
                response.setHeader("message", URLEncoder.encode("批量交付出错", "UTF-8").replaceAll("\\+", "%20"));
                response.setHeader("statecode", "99999");
                ExcelUtils.downLoadExcel(excelName,response,workbook);*/
                String fileName = "导入x终端数据".concat(millis + "-fail.xls");
                String failFilePath = getKxFailFilePath(fileName);
                try(FileOutputStream outputStream = new FileOutputStream(failFilePath)) {
                    workbook.write(outputStream);
                    outputStream.close();
                }catch (Exception e){
                    log.error("生成终端订单交付数据错误文件异常:{}",e);
                }finally {
                    workbook.close();
                }
                // 处理校验不通过的数据文档信息
                File downErrorFile = new File(failFilePath);

                // 处理交付完成提醒
                handleImportOrderKxDeliverResult(userId,phone,totalCount,failCount,succCount,downErrorFile, millis,importRedisKey,1,date);
            }else {
                //全部成功
                response.setHeader("message", URLEncoder.encode("批量交付成功", "UTF-8").replaceAll("\\+", "%20"));
                response.setHeader("statecode", ExcepStatus.getSuccInstance().getStateCode());
                // 处理交付完成提醒
                handleImportOrderKxDeliverResult(userId,phone,totalCount,0,0,null, millis,importRedisKey,2,date);
            }


        } catch (Exception e) {
            log.error("批量交付出错",e);
            if(e instanceof BusinessException ){
                BusinessException businessException = (BusinessException) e;
                try {
                    response.setHeader("message", URLEncoder.encode(businessException.getStatus().getMessage(), "UTF-8").replaceAll("\\+", "%20"));
                    response.setHeader("statecode", "88888");
                } catch (UnsupportedEncodingException e1) {
                    e1.printStackTrace();
                }
            }else {
                try {
                    response.setHeader("message", URLEncoder.encode("批量交付出错", "UTF-8").replaceAll("\\+", "%20"));
                    response.setHeader("statecode", "88888");
                } catch (UnsupportedEncodingException e1) {
                    e1.printStackTrace();
                }
            }

            // 处理交付完成提醒
            handleImportOrderKxDeliverResult(userId,phone,0,0,0,null, millis,importRedisKey,3,date);

        }finally {
            if(inputStream != null){
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            stringRedisTemplate.delete(importRedisKey);
        }

    }

    /**
     * 终端订单交付错误文件处理
     * @param userId
     * @param phone
     * @param totalCount
     * @param failCount
     * @param successCount
     * @param downErrorFile
     * @param millis
     * @param importRedisKey
     * @param phoneType
     * @param date
     * @throws Exception
     */
    private void handleImportOrderKxDeliverResult(String userId,
                                      String phone,
                                      Integer totalCount,
                                      Integer failCount,
                                      Integer successCount,
                                      File downErrorFile,
                                      long millis,
                                      String importRedisKey,
                                      Integer phoneType,
                                      Date date) throws Exception{
        String templateId = "";
        Map<String,String> message = new HashMap<>();
        message.put("importTime",DateUtils.dateToStr(date,DateUtils.DEFAULT_DATETIME_FORMAT));
        message.put("date",DateUtils.dateToStr(date,DateUtils.DEFAULT_DATETIME_FORMAT));
        // 失败
        if (phoneType == 1){
            FileInputStream fileInputStreamToRead = new FileInputStream(downErrorFile);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

            // 将FileOutputStream的内容转移到ByteArrayOutputStream
            byte[] buffer = new byte[1024];
            int length;
            while ((length = fileInputStreamToRead.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, length);
            }

            // 关闭流
            fileInputStreamToRead.close();

            String traceId = String.valueOf(System.currentTimeMillis());
            //上传文件到对象存储
            String fileName = "导入x终端订单交付数据".concat(millis + "-fail.xls");;
            ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
            byteArrayUpload.setFileName(fileName);
            byteArrayUpload.setBytes(byteArrayOutputStream.toByteArray());
            BaseAnswer<UpResult> resultBaseAnswer = null;
            try {
                resultBaseAnswer = storageService.uploadByte(byteArrayUpload);
            } catch (Exception e) {
                stringRedisTemplate.delete(importRedisKey);
                log.info("上传终端订单交付导入文件错误信息异常:{}",e);
                throw new BusinessException("10008","上传终端订单交付导入文件错误信息异常:"+e);
            }
            if (!"00000".equals(resultBaseAnswer.getStateCode())) {
                stringRedisTemplate.delete(importRedisKey);
                log.info("上传终端订单交付导入文件错误信息,请联系管理员,{}",JSONObject.toJSONString(resultBaseAnswer));
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "上传终端订单交付导入文件错误信息,请联系管理员");
            }
            log.info("{}终端订单交付错误上传excel完毕", traceId);
            //消息中心提醒用户
            UpResult upResult = resultBaseAnswer.getData();
            String outerUrl = upResult.getOuterUrl();
            String fileKey = upResult.getKey();
            AddMessageParam messageParam = new AddMessageParam();
            messageParam.setModule(ModuleEnum.ORDER_MANAGE.name);
            messageParam.setContent("卡+x终端订单交付导入错误信息，请点击下载");
            messageParam.setType("卡+x终端订单交付导入错误");
            messageParam.setUserId(userId);
            messageParam.setFileKey(fileKey);
            messageParam.setUrl(outerUrl);
            messageParam.setSource(1);
            iotFeignClient.addMessage(messageParam);

            //通过kafka发送websocket提示前端更新消息未读数
            ProducerRecord<String, byte[]> record = new ProducerRecord<>(KafkaTopic.SUPPLY_CHAIN_WEBSOCKET.getTopic(), "/message/monitor".getBytes());
            kafkaTemplate.send(record);

            templateId = importKxOrderDevlierFail;
            message.put("total",totalCount+"");
            message.put("success",successCount+"");
            message.put("fail",failCount+"");
        } else if (phoneType == 2) {// 成功
            templateId = importKxOrderDevlierSucc;
        }else {// 异常
            templateId = importKxOrderDevlierErr;
        }

        //发送短信提示用户
        if (org.apache.commons.lang.StringUtils.isNotEmpty(phone) && RegexUtil.regexPhone(phone)) {
            Msg4Request msg4Request = new Msg4Request();
            // 短信模板
            msg4Request.setTemplateId(templateId);
            List<String> mobiles = new ArrayList<>();
            mobiles.add(phone);
            msg4Request.setMobiles(mobiles);
            msg4Request.setMessage(message);
            smsFeignClient.asySendMessage(msg4Request);
        }
        stringRedisTemplate.delete(importRedisKey);
    }

    /**
     * 获取码号校验的失败结果描述
     */
    private String getCardCheckFailReason(List<QrySubscribersResponse.MsisdnInfo> msisdnInfoList) {
        List<String> failReasonList = new ArrayList<>();
        for (QrySubscribersResponse.MsisdnInfo msisdnInfo : msisdnInfoList) {
            String msisdn = msisdnInfo.getMsisdn();
            String iccid = msisdnInfo.getIccid();
            String status = msisdnInfo.getStatus();
            String statusMsg = "";
            if ("2".equals(status)) {
                statusMsg = "已销售";
            }
            if ("3".equals(status)) {
                statusMsg = "待退款锁定";
            }
            if ("9".equals(status)) {
                statusMsg = "不可销售";
            }
            failReasonList.add("码号" + msisdn + "" + statusMsg);
        }
        return String.join(",", failReasonList);
    }


    private void excel2ResultDTO(List<CardXBatchDeliverResultDTO> resultDTOList, List<CardXBatchDeliverExcelDTO> excelDTOList, Map<String, String> orderIdAndFailReasonMap) {
        //订单+设备型号与原子订单号关联关系
        Map<String,String> orderDeviceVersionAndAtomOrderIdMap = new HashMap<>();
        CardXBatchDeliverResultDTO resultDTO = null;
        for (int i = 0; i < excelDTOList.size(); i++) {
            CardXBatchDeliverExcelDTO row = excelDTOList.get(i);
            String orderId = row.getOrderId();
            String deviceVersion = row.getDeviceVersion();
            if (org.apache.commons.lang.StringUtils.isNotEmpty(orderId)) {
                //有订单号，表示这是此订单号的第一行
                resultDTO = new CardXBatchDeliverResultDTO();
                BeanUtils.copyProperties(row, resultDTO);

                if(org.apache.commons.lang.StringUtils.isNotEmpty(deviceVersion)){
                    List<CardXBatchDeliverResultDTO.DeviceVersionItem> deviceVersionList = new ArrayList<>();
                    CardXBatchDeliverResultDTO.DeviceVersionItem deviceVersionItem = new CardXBatchDeliverResultDTO.DeviceVersionItem();
                    deviceVersionItem.setDeviceVersion(deviceVersion);
                    deviceVersionList.add(deviceVersionItem);
                    resultDTO.setDeviceVersionList(deviceVersionList);

                    //通过设备型号找到原子订单号
                    String atomOrderId = dkcardxInventoryMainInfoMapperExt.findAtomOrderId(resultDTO.getOrderId(), deviceVersion);

                    if(StringUtils.isEmpty(atomOrderId)){
                        //保存订单的所有错误信息
                        String failReason = "设备型号:" + deviceVersion + "找不到对应原子订单";
                        orderIdAndFailReasonMap.put(resultDTO.getOrderId(),failReason);
                    }else {
                        orderDeviceVersionAndAtomOrderIdMap.put(orderId+"_"+deviceVersion,atomOrderId);
                    }

                    List<DeliverCardParam.AtomDeliverItem> atomDeliveryItemList = resultDTO.getAtomDeliveryItemList();
                    if(atomDeliveryItemList == null){
                        atomDeliveryItemList = new ArrayList<>();
                        resultDTO.setAtomDeliveryItemList(atomDeliveryItemList);
                    }
                    DeliverCardParam.AtomDeliverItem atomDeliverItem = new DeliverCardParam.AtomDeliverItem();
                    List<String> atomOrderIdList = atomDeliverItem.getAtomOrderIdList();
                    if (CollectionUtils.isEmpty(atomOrderIdList)){
                        atomOrderIdList = new ArrayList<>();
                    }
                    atomDeliverItem.setAtomOrderIdList(atomOrderIdList);
//                    atomDeliverItem.setAtomOrderId(atomOrderId);
                    atomDeliverItem.setOrderId(orderId);
                    atomDeliveryItemList.add(atomDeliverItem);

                    List<DeliverCardParam.CardItem> cardList = atomDeliverItem.getCardList();
                    if(cardList == null){
                        cardList = new ArrayList<>();
                        atomDeliverItem.setCardList(cardList);
                    }

                    DeliverCardParam.CardItem cardItem = new DeliverCardParam.CardItem();
                    cardList.add(cardItem);
                    if (org.apache.commons.lang.StringUtils.isNotEmpty(row.getImei())) {
                        List<CardXBatchDeliverResultDTO.ImeiItem> imeiItemList = new ArrayList<>();
                        CardXBatchDeliverResultDTO.ImeiItem imeiItem = new CardXBatchDeliverResultDTO.ImeiItem();
                        imeiItem.setImei(row.getImei());
                        imeiItemList.add(imeiItem);
                        resultDTO.setImeiList(imeiItemList);

                        cardItem.setImei(row.getImei());
                    }
                    if (org.apache.commons.lang.StringUtils.isNotEmpty(row.getMsisdn())) {
                        List<CardXBatchDeliverResultDTO.MsisdnItem> msisdnItemList = new ArrayList<>();
                        CardXBatchDeliverResultDTO.MsisdnItem msisdnItem = new CardXBatchDeliverResultDTO.MsisdnItem();
                        msisdnItem.setMsisdn(row.getMsisdn());
                        msisdnItemList.add(msisdnItem);
                        resultDTO.setMsisdnList(msisdnItemList);

                        cardItem.setMsisdn(row.getMsisdn());
                    } else {
                        //如果没填码号，也要进行空字符串占位，避免导出结果时和下面的单元格合并
                        List<CardXBatchDeliverResultDTO.MsisdnItem> msisdnItemList = new ArrayList<>();
                        CardXBatchDeliverResultDTO.MsisdnItem msisdnItem = new CardXBatchDeliverResultDTO.MsisdnItem();
                        msisdnItem.setMsisdn("");
                        msisdnItemList.add(msisdnItem);
                        resultDTO.setMsisdnList(msisdnItemList);
                    }
                    resultDTOList.add(resultDTO);
                }
            } else {
                Boolean anotherDeviceVersion = false;
                if (resultDTO != null) {
                    orderId = resultDTO.getOrderId();
                    //表示这是此订单的后面几行
                    List<DeliverCardParam.AtomDeliverItem> atomDeliveryItemList = resultDTO.getAtomDeliveryItemList();
                    if(org.apache.commons.lang.StringUtils.isNotEmpty(deviceVersion)){
                        //另一个设备型号
                        anotherDeviceVersion = true;
                    }else {
                        //表示被合并的设备型号，实际值是前一行的设备型号
                        deviceVersion = resultDTO.getDeviceVersionList().get(resultDTO.getDeviceVersionList().size()-1).getDeviceVersion();
                    }
                    CardXBatchDeliverResultDTO.DeviceVersionItem deviceVersionItem = new CardXBatchDeliverResultDTO.DeviceVersionItem();
                    deviceVersionItem.setDeviceVersion(deviceVersion);
                    resultDTO.getDeviceVersionList().add(deviceVersionItem);

                    if(deviceVersion != null){

                        DeliverCardParam.CardItem cardItem = new DeliverCardParam.CardItem();
                        if (org.apache.commons.lang.StringUtils.isNotEmpty(row.getImei())) {
                            CardXBatchDeliverResultDTO.ImeiItem imeiItem = new CardXBatchDeliverResultDTO.ImeiItem();
                            imeiItem.setImei(row.getImei());
                            resultDTO.getImeiList().add(imeiItem);

                            cardItem.setImei(row.getImei());
                        }
                        if (org.apache.commons.lang.StringUtils.isNotEmpty(row.getMsisdn())) {
                            CardXBatchDeliverResultDTO.MsisdnItem msisdnItem = new CardXBatchDeliverResultDTO.MsisdnItem();
                            msisdnItem.setMsisdn(row.getMsisdn());
                            resultDTO.getMsisdnList().add(msisdnItem);

                            cardItem.setMsisdn(row.getMsisdn());
                        } else {
                            CardXBatchDeliverResultDTO.MsisdnItem msisdnItem = new CardXBatchDeliverResultDTO.MsisdnItem();
                            msisdnItem.setMsisdn("");
                            resultDTO.getMsisdnList().add(msisdnItem);
                        }

                        String atomOrderId = orderDeviceVersionAndAtomOrderIdMap.get(orderId+"_"+deviceVersion);
                        if(anotherDeviceVersion){
                            //重新查询对应的原子订单
                            atomOrderId = dkcardxInventoryMainInfoMapperExt.findAtomOrderId(resultDTO.getOrderId(), deviceVersion);
                            if(StringUtils.isEmpty(atomOrderId)){
                                //保存订单的所有错误信息
                                String failReason = "设备型号:" + deviceVersion + "找不到对应原子订单";
                                String existedFailReason = orderIdAndFailReasonMap.get(resultDTO.getOrderId());
                                if(existedFailReason == null){
                                    existedFailReason = failReason;
                                }else {
                                    existedFailReason = existedFailReason.concat(";").concat(failReason);
                                }
                                orderIdAndFailReasonMap.put(resultDTO.getOrderId(),existedFailReason);
                            }else {
                                orderDeviceVersionAndAtomOrderIdMap.put(orderId+"_"+deviceVersion,atomOrderId);
                            }
                            //添加新的原子订单及其对应的X终端,号卡信息
                            DeliverCardParam.AtomDeliverItem atomDeliverItem = new DeliverCardParam.AtomDeliverItem();
                            List<String> atomOrderIdList = atomDeliverItem.getAtomOrderIdList();
                            if (CollectionUtils.isEmpty(atomOrderIdList)){
                                atomOrderIdList = new ArrayList<>();
                            }
                            atomOrderIdList.add(atomOrderId);
                            atomDeliverItem.setAtomOrderIdList(atomOrderIdList);
//                            atomDeliverItem.setAtomOrderId(atomOrderId);
                            atomDeliveryItemList.add(atomDeliverItem);

                            List<DeliverCardParam.CardItem> cardList = new ArrayList<>();
                            atomDeliverItem.setCardList(cardList);
                            cardList.add(cardItem);
                        }else {
                            //为之前已经添加的原子订单增加X终端,号卡信息
                            if(atomOrderId == null){
                                //由于是同一个设备型号，之前已经添加了错误信息，这里不再添加
                                continue;
                            }
                            String finalAtomOrderId = atomOrderId;
                            /*DeliverCardParam.AtomDeliverItem existedAtomDeliverItem = atomDeliveryItemList.stream().filter(a -> {
                                return a.getAtomOrderId().equals(finalAtomOrderId);
                            }).collect(Collectors.toList()).get(0);*/
                            DeliverCardParam.AtomDeliverItem existedAtomDeliverItem = atomDeliveryItemList.stream().filter(a -> {
                                return a.getAtomOrderIdList().contains(finalAtomOrderId);
                            }).collect(Collectors.toList()).get(0);
                            List<DeliverCardParam.CardItem> existedCardList = existedAtomDeliverItem.getCardList();
                            existedCardList.add(cardItem);
                        }
                    }
                }
            }
        }
    }

    /**
     * 处理参数信息
     *
     * @param cardRelationXParam
     * @param loginIfo4Redis
     */
    private void handleCardInfoXParam(CardRelationXParam cardRelationXParam,
                                      LoginIfo4Redis loginIfo4Redis) {

        String roleType = loginIfo4Redis.getRoleType();
        String userId = loginIfo4Redis.getUserId();

        // 如果是省公司主合作伙伴、从合作伙伴及合作伙伴省管账号
        boolean isPartnerLordRole = PARTNER_LORD_ROLE.equals(roleType);
        boolean isPartnerRole = PARTNER_ROLE.equals(roleType);
        boolean isPartnerProvince = BaseConstant.PARTNER_PROVINCE.equals(roleType);
        if (isPartnerLordRole || isPartnerRole || isPartnerProvince) {
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
            if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                log.warn("码号查询时主合作伙伴、从合作伙伴及合作伙伴省管账号错误:{}", JSONObject.toJSONString(data4UserBaseAnswer));
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省管账号错误");
            }

            Data4User data4User = data4UserBaseAnswer.getData();
            String companyType = data4User.getCompanyType();
            boolean isProvinceUser = StringUtils.isNotEmpty(companyType) && "2".equals(companyType);
            String beId = cardRelationXParam.getBeId();
            String location = cardRelationXParam.getLocation();

            if (!isProvinceUser) {
                log.warn("码号查询时主合作伙伴、从合作伙伴及合作伙伴非省管账号companyType:{}", companyType);
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省非管账号");
            }
            // 省公司合作伙伴主账号可查看、导出省级维度的码号信息
            if (isPartnerLordRole && isProvinceUser && StringUtils.isEmpty(beId)) {
                cardRelationXParam.setBeId(data4User.getBeIdPartner());
            }

            // 合作伙伴省管账号、合作伙伴从账号可查看、导出地市维度的码号信息
            if ((isPartnerRole || isPartnerProvince)
                    && isProvinceUser && StringUtils.isEmpty(location)) {
                String userLocation = data4User.getLocationIdPartner();
                if ("all".equals(userLocation)) {
                    cardRelationXParam.setBeId(data4User.getBeIdPartner());
                } else {
                    cardRelationXParam.setLocation(userLocation);
                }

            }
        }
    }

    /***
     * 处理删除时的参数信息
     * @param cardRelationCriteria
     * @param loginIfo4Redis
     */
    private void handleDeleteCardRelationParam(CardRelationExample.Criteria cardRelationCriteria,
                                               LoginIfo4Redis loginIfo4Redis) {

        String roleType = loginIfo4Redis.getRoleType();
        String userId = loginIfo4Redis.getUserId();

        // 如果是省公司主合作伙伴、从合作伙伴及合作伙伴省管账号
        boolean isPartnerLordRole = BaseConstant.PARTNER_LORD_ROLE.equals(roleType);
        boolean isPartnerRole = BaseConstant.PARTNER_ROLE.equals(roleType);
        boolean isPartnerProvince = BaseConstant.PARTNER_PROVINCE.equals(roleType);
        if (isPartnerLordRole || isPartnerRole || isPartnerProvince) {
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
            if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                log.warn("查询主合作伙伴、从合作伙伴及合作伙伴省管账号错误:{}", JSONObject.toJSONString(data4UserBaseAnswer));
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省管账号错误");
            }

            Data4User data4User = data4UserBaseAnswer.getData();
            String companyType = data4User.getCompanyType();
            boolean isProvinceUser = StringUtils.isNotEmpty(companyType) && "2".equals(companyType);

            /*if (!isProvinceUser) {
                log.warn("查询主合作伙伴、从合作伙伴及合作伙伴非省管账号companyType:{}", companyType);
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴非省管账号");
            }*/
            // 合作伙伴主/从账号可删除省级维度的码号信息
            if (isPartnerLordRole || isPartnerRole) {
                cardRelationCriteria.andBeIdEqualTo(data4User.getBeIdPartner());
            }

            // 合作伙伴省管账号删除地市维度的码号信息
            if (isPartnerProvince) {
                String userLocation = data4User.getLocationIdPartner();
                if ("all".equals(userLocation)) {
                    cardRelationCriteria.andBeIdEqualTo(data4User.getBeIdPartner());
                } else {
                    cardRelationCriteria.andLocationEqualTo(userLocation);
                }

            }
        }
    }

    private List<CardRelationXVO> handleCardRelationXList(List<CardRelationXDTO> cardRelationXList) {


        /*Map<Object, Object> provinceCodeNameMap = areaDataConfig.getProvinceCodeNameMap();
        Map<Object, Object> locationCodeNameMap = areaDataConfig.getLocationCodeNameMap();*/
        List<CardRelationXVO> cardRelationXVOList = new ArrayList<>();
        cardRelationXList.forEach(cardRelationX -> {
            CardRelationXVO cardRelationXVO = new CardRelationXVO();

            /*String beId = cardRelationX.getBeId();
            Object provinceName = provinceCodeNameMap.get(beId);
            if (provinceName != null) {
                cardRelationXVO.setProvinceName((String) provinceName);
            } else {
                cardRelationXVO.setProvinceName("");
            }

            String location = cardRelationX.getLocation();
            if (StringUtils.isNotEmpty(location)) {
                Object cityName = locationCodeNameMap.get(location);
                if (cityName != null) {
                    cardRelationXVO.setCityName((String) cityName);
                } else {
                    cardRelationXVO.setCityName("");
                }
            }*/

            String terminalType = cardRelationX.getTerminalType();
            String terimalTypeName = TerminalTypeEnum.getDescByType(terminalType);
            cardRelationXVO.setTerminalTypeName(terimalTypeName);

            String sellStatusName = SellStatusEnum.getDescByType(cardRelationX.getSellStatus());
            cardRelationXVO.setSellStatusName(sellStatusName);
            BeanUtils.copyProperties(cardRelationX, cardRelationXVO);
            if (TerminalTypeEnum.CHA_BO_CARD.getType().equals(terminalType)) {
                cardRelationXVO.setMsisdn(cardRelationX.getChabaMsisdn());
            }

            cardRelationXVO.setCreateTimeStr(DateUtils.dateToStr(cardRelationX.getCreateTime(),DateUtils.DEFAULT_DATE_FORMAT));

            cardRelationXVOList.add(cardRelationXVO);
        });

        return cardRelationXVOList;
    }

    private String contactAddr(String addr1, String addr2, String addr3, String addr4, String usaddr) {
        return (StringUtils.isNotEmpty(addr1) ? IOTEncodeUtils.decryptSM4(addr1, iotSm4Key, iotSm4Iv) : "") +
                (StringUtils.isNotEmpty(addr2) ? IOTEncodeUtils.decryptSM4(addr2, iotSm4Key, iotSm4Iv) : "") +
                (StringUtils.isNotEmpty(addr3) ? IOTEncodeUtils.decryptSM4(addr3, iotSm4Key, iotSm4Iv) : "") +
                (StringUtils.isNotEmpty(addr4) ? IOTEncodeUtils.decryptSM4(addr4, iotSm4Key, iotSm4Iv) : "") +
                (StringUtils.isNotEmpty(usaddr) ? IOTEncodeUtils.decryptSM4(usaddr, iotSm4Key, iotSm4Iv) : "");
    }


    /**
     * x终端导入时判断总库存数和预警值  发送预警短信
     *
     * @param inventoryMainId
     */
    @Override
    public void sendKxTerminalInventorySms(String inventoryMainId) {
        List<AtomOfferingInfo> atomOfferingInfoList = atomOfferingInfoMapper.selectByExample(new AtomOfferingInfoExample().createCriteria()
                .andInventoryMainIdEqualTo(inventoryMainId).example());
        if (CollectionUtils.isEmpty(atomOfferingInfoList)) {
            log.info("新增的x终端库存还未配置到原子商品上：inventoryMainId{}", inventoryMainId);
            return;
        }
        //省级库存信息
        List<DkcardxInventoryDetailInfo> dkcardxInventoryDetailInfos = dkcardxInventoryDetailInfoMapper.selectByExample(new DkcardxInventoryDetailInfoExample().createCriteria()
                .andInventoryMainIdEqualTo(inventoryMainId).andProvinceAliasNameIsNotNull().andLocationIsNull().example());
        Integer totalInventoryProvince = 0;
        if (CollectionUtils.isNotEmpty(dkcardxInventoryDetailInfos)) {
            DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo = dkcardxInventoryDetailInfos.get(0);
            totalInventoryProvince = dkcardxInventoryDetailInfo.getTotalInventory();
        }

        for (AtomOfferingInfo atomOfferingInfo : atomOfferingInfoList) {
            Long inventoryThreshold = atomOfferingInfo.getInventoryThreshold();
            //通过原子商品id查询关联的x终端库存信息
            List<DkcardxInventoryAtomInfo> dkcardxInventoryAtomInfos = dkcardxInventoryAtomInfoMapper.selectByExample(new DkcardxInventoryAtomInfoExample().createCriteria()
                    .andAtomIdEqualTo(atomOfferingInfo.getId()).example());
            for (DkcardxInventoryAtomInfo x : dkcardxInventoryAtomInfos) {
                String inventoryDetailId = x.getInventoryDetailId();
                DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo = dkcardxInventoryDetailInfoMapper.selectByPrimaryKey(inventoryDetailId);
                Integer totalInventory = dkcardxInventoryDetailInfo.getTotalInventory();
                String provinceAliasName = dkcardxInventoryDetailInfo.getProvinceAliasName();

                //判断是否发送短信
                if (!atomOfferingInfo.getIsNotice()) {
                    log.info("dealReserveInventory atomCode = {} 不发送短信", atomOfferingInfo.getOfferingCode());
                    break;
                }
                if (StringUtils.isNotEmpty(provinceAliasName)) {
                    if (totalInventoryProvince < inventoryThreshold) {
                        packSendNoteParam(atomOfferingInfo, provinceAliasName, dkcardxInventoryDetailInfo);
                    }
                } else {
                    if (totalInventoryProvince + totalInventory < inventoryThreshold) {
                        packSendNoteParam(atomOfferingInfo, provinceAliasName, dkcardxInventoryDetailInfo);
                    }
                }

            }
        }
    }

    /**
     * 封装发送短信
     *
     * @param atomOfferingInfo
     * @param provinceAliasName
     * @param dkcardxInventoryDetailInfo
     */
    private void packSendNoteParam(AtomOfferingInfo atomOfferingInfo, String provinceAliasName, DkcardxInventoryDetailInfo dkcardxInventoryDetailInfo) {
        //发送库存预警短信
        String cooperatorId = atomOfferingInfo.getCooperatorId();
        if (org.apache.commons.lang.StringUtils.isNotEmpty(cooperatorId) && !"-1".equals(cooperatorId)) {
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfo(cooperatorId);
            Data4User cooperator = data4UserBaseAnswer.getData();

            String phone = cooperator.getPhone();
            List<String> phones = new ArrayList<>();
            //查询主合作伙伴信息
            /*BaseAnswer<Data4User> userBaseAnswer = userFeignClient.queryPrimaryUserPhone(cooperatorId);
            if (userBaseAnswer == null || !userBaseAnswer.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode()) || userBaseAnswer.getData() == null) {
                throw new RuntimeException("调用获取主用户信息失败。从合作伙伴ID:" + cooperatorId);
            }
            if (userBaseAnswer.getData().getIsSend()) {
                phones.add(userBaseAnswer.getData().getPhone());
            }*/
            Boolean isSend = cooperator.getIsSend();
            if (isSend != null && isSend){
                phones.add(phone);
            }

            // 获取原子商品和合作伙伴信息
            List<Data4User> data4UserList = atomOfferingCooperatorRelationService.listCooperatorUserInfo(atomOfferingInfo.getId());
            if (CollectionUtils.isNotEmpty(data4UserList)){
                List<String> cooperatroPhoneList = data4UserList.stream().map(Data4User::getPhone)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(cooperatroPhoneList)){
                    phones.addAll(cooperatroPhoneList);
                }
            }

            log.info("Added x terminal inventory = {} 发送 库存小于预警 短信给 phone = {}", atomOfferingInfo.getOfferingCode(), phones);
            Map<String, String> msgMap = new HashMap<>();
            phones = phones.stream().distinct().collect(Collectors.toList());
            Msg4Request msg4Request = new Msg4Request();
            msg4Request.setMobiles(phones);
            msgMap.put("atomOfferingName", atomOfferingInfo.getOfferingName());
            msgMap.put("offeringCode", atomOfferingInfo.getOfferingCode());
            if (StringUtils.isNotEmpty(provinceAliasName)) {
                msgMap.put("warehouse", dkcardxInventoryDetailInfo.getProvinceName());
            } else {
                msgMap.put("warehouse", dkcardxInventoryDetailInfo.getCityName());
            }
            msg4Request.setMessage(msgMap);
            //库存不足
            msg4Request.setTemplateId(smsInventoryKxDeficiencyTemplateId);
            BaseAnswer<Void> voidBaseAnswer = smsFeignClient.asySendMessage(msg4Request);
            log.info("总库存不足，低于预警值 库存警报短信通知合作伙伴的短信发送结果:{}", JSON.toJSONString(voidBaseAnswer));
        }
    }

}
