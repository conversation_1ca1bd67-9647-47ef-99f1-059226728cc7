package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;
import org.apache.kafka.common.protocol.types.Field;

import java.util.Date;

/**
 * created by l<PERSON><PERSON><PERSON> on 2024/2/27 16:38
 */
@Data
public class ProductFlowInstanceListVO {

    //流程实例id
    private String id;

    //流程编号
    private String flowNumber;

    //spu名称
    private String spuName;

    //sku名称
    private String skuName;

    //产品标准 1-标准类 2-方案类
    private Integer productStandard;

    //产品类别 1-省框 2-省内 3-DICT 4-合同履约 5-联合销售
    private Integer productType;


    //流程类型，如 商品上架,商品下架
    private String flowType;

    //运营类型 如，分省运营类,统一运营类
    private Integer operateType;

    //当前流程环节名称
    private String currentStepName;

    //创建人
    private String creatorName;

    //创建时间
    private Date createTime;

    //可编辑
    private Boolean canEdit;

    //可取消
    private Boolean canCancel;

    //可审核
    private Boolean canAudit;

    //可配置
    private Boolean canConfig;

}
