package com.chinamobile.iot.sc.response.iot;

import lombok.Data;

import java.util.List;

@Data
public class QrySubscribersResponse {

    private List<QrySubscribersResponse.MsisdnInfo> msisdnDetailInfos;

    @Data
    public static class MsisdnInfo{
        /**
         * 正式服务号码
         */
        private String msisdn;
        /**
         * iccid
         */
        private String iccid;
        /**
         * 卡物理类型
         * 0：插拔卡
         * 1：贴片卡
         * 2：M2M芯片非空写卡
         * 3：M2M芯片空写卡
         */
        private String cardPhysicalType;
        /**
         * 号卡销售状态
         * 0：未销售（即“可售”状态）
         * 2：已销售
         * 3：待退款锁定
         * 9：不可销售
         * 51: 待激活
         * 52: 已激活
         * 54: 停机
         * 56: 可测试(未销售)
         * 57: 库存
         * 58: 预销户
         */
        private String status;
    }

}
