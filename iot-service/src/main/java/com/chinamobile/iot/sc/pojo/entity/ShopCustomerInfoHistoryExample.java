package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ShopCustomerInfoHistoryExample {
    /**
     * Corresponding to the database table supply_chain..shop_customer_info_history
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    protected String orderByClause;

    /**
     * Corresponding to the database table supply_chain..shop_customer_info_history
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    protected boolean distinct;

    /**
     * Corresponding to the database table supply_chain..shop_customer_info_history
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistoryExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistoryExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistoryExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        ShopCustomerInfoHistoryExample example = new ShopCustomerInfoHistoryExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistoryExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public ShopCustomerInfoHistoryExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     * Corresponding to the database table supply_chain..shop_customer_info_history
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCustCodeIsNull() {
            addCriterion("cust_code is null");
            return (Criteria) this;
        }

        public Criteria andCustCodeIsNotNull() {
            addCriterion("cust_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustCodeEqualTo(String value) {
            addCriterion("cust_code =", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeNotEqualTo(String value) {
            addCriterion("cust_code <>", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThan(String value) {
            addCriterion("cust_code >", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanOrEqualTo(String value) {
            addCriterion("cust_code >=", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThan(String value) {
            addCriterion("cust_code <", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanOrEqualTo(String value) {
            addCriterion("cust_code <=", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLike(String value) {
            addCriterion("cust_code like", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotLike(String value) {
            addCriterion("cust_code not like", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeIn(List<String> values) {
            addCriterion("cust_code in", values, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotIn(List<String> values) {
            addCriterion("cust_code not in", values, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeBetween(String value1, String value2) {
            addCriterion("cust_code between", value1, value2, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotBetween(String value1, String value2) {
            addCriterion("cust_code not between", value1, value2, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustIdIsNull() {
            addCriterion("cust_id is null");
            return (Criteria) this;
        }

        public Criteria andCustIdIsNotNull() {
            addCriterion("cust_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustIdEqualTo(String value) {
            addCriterion("cust_id =", value, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustIdNotEqualTo(String value) {
            addCriterion("cust_id <>", value, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustIdGreaterThan(String value) {
            addCriterion("cust_id >", value, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustIdGreaterThanOrEqualTo(String value) {
            addCriterion("cust_id >=", value, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustIdLessThan(String value) {
            addCriterion("cust_id <", value, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustIdLessThanOrEqualTo(String value) {
            addCriterion("cust_id <=", value, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustIdLike(String value) {
            addCriterion("cust_id like", value, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdNotLike(String value) {
            addCriterion("cust_id not like", value, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdIn(List<String> values) {
            addCriterion("cust_id in", values, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdNotIn(List<String> values) {
            addCriterion("cust_id not in", values, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdBetween(String value1, String value2) {
            addCriterion("cust_id between", value1, value2, "custId");
            return (Criteria) this;
        }

        public Criteria andCustIdNotBetween(String value1, String value2) {
            addCriterion("cust_id not between", value1, value2, "custId");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(String value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("user_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(String value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("user_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(String value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("user_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("user_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(String value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("user_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(String value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("user_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUserIdLike(String value) {
            addCriterion("user_id like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotLike(String value) {
            addCriterion("user_id not like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<String> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<String> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(String value1, String value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(String value1, String value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andCustNameIsNull() {
            addCriterion("cust_name is null");
            return (Criteria) this;
        }

        public Criteria andCustNameIsNotNull() {
            addCriterion("cust_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustNameEqualTo(String value) {
            addCriterion("cust_name =", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameNotEqualTo(String value) {
            addCriterion("cust_name <>", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThan(String value) {
            addCriterion("cust_name >", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThanOrEqualTo(String value) {
            addCriterion("cust_name >=", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameLessThan(String value) {
            addCriterion("cust_name <", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameLessThanOrEqualTo(String value) {
            addCriterion("cust_name <=", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustNameLike(String value) {
            addCriterion("cust_name like", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotLike(String value) {
            addCriterion("cust_name not like", value, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameIn(List<String> values) {
            addCriterion("cust_name in", values, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotIn(List<String> values) {
            addCriterion("cust_name not in", values, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameBetween(String value1, String value2) {
            addCriterion("cust_name between", value1, value2, "custName");
            return (Criteria) this;
        }

        public Criteria andCustNameNotBetween(String value1, String value2) {
            addCriterion("cust_name not between", value1, value2, "custName");
            return (Criteria) this;
        }

        public Criteria andOprTypeIsNull() {
            addCriterion("opr_type is null");
            return (Criteria) this;
        }

        public Criteria andOprTypeIsNotNull() {
            addCriterion("opr_type is not null");
            return (Criteria) this;
        }

        public Criteria andOprTypeEqualTo(String value) {
            addCriterion("opr_type =", value, "oprType");
            return (Criteria) this;
        }

        public Criteria andOprTypeEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("opr_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOprTypeNotEqualTo(String value) {
            addCriterion("opr_type <>", value, "oprType");
            return (Criteria) this;
        }

        public Criteria andOprTypeNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("opr_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOprTypeGreaterThan(String value) {
            addCriterion("opr_type >", value, "oprType");
            return (Criteria) this;
        }

        public Criteria andOprTypeGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("opr_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOprTypeGreaterThanOrEqualTo(String value) {
            addCriterion("opr_type >=", value, "oprType");
            return (Criteria) this;
        }

        public Criteria andOprTypeGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("opr_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOprTypeLessThan(String value) {
            addCriterion("opr_type <", value, "oprType");
            return (Criteria) this;
        }

        public Criteria andOprTypeLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("opr_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOprTypeLessThanOrEqualTo(String value) {
            addCriterion("opr_type <=", value, "oprType");
            return (Criteria) this;
        }

        public Criteria andOprTypeLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("opr_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOprTypeLike(String value) {
            addCriterion("opr_type like", value, "oprType");
            return (Criteria) this;
        }

        public Criteria andOprTypeNotLike(String value) {
            addCriterion("opr_type not like", value, "oprType");
            return (Criteria) this;
        }

        public Criteria andOprTypeIn(List<String> values) {
            addCriterion("opr_type in", values, "oprType");
            return (Criteria) this;
        }

        public Criteria andOprTypeNotIn(List<String> values) {
            addCriterion("opr_type not in", values, "oprType");
            return (Criteria) this;
        }

        public Criteria andOprTypeBetween(String value1, String value2) {
            addCriterion("opr_type between", value1, value2, "oprType");
            return (Criteria) this;
        }

        public Criteria andOprTypeNotBetween(String value1, String value2) {
            addCriterion("opr_type not between", value1, value2, "oprType");
            return (Criteria) this;
        }

        public Criteria andCustStatusTimeIsNull() {
            addCriterion("cust_status_time is null");
            return (Criteria) this;
        }

        public Criteria andCustStatusTimeIsNotNull() {
            addCriterion("cust_status_time is not null");
            return (Criteria) this;
        }

        public Criteria andCustStatusTimeEqualTo(Date value) {
            addCriterion("cust_status_time =", value, "custStatusTime");
            return (Criteria) this;
        }

        public Criteria andCustStatusTimeEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_status_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustStatusTimeNotEqualTo(Date value) {
            addCriterion("cust_status_time <>", value, "custStatusTime");
            return (Criteria) this;
        }

        public Criteria andCustStatusTimeNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_status_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustStatusTimeGreaterThan(Date value) {
            addCriterion("cust_status_time >", value, "custStatusTime");
            return (Criteria) this;
        }

        public Criteria andCustStatusTimeGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_status_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustStatusTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("cust_status_time >=", value, "custStatusTime");
            return (Criteria) this;
        }

        public Criteria andCustStatusTimeGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_status_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustStatusTimeLessThan(Date value) {
            addCriterion("cust_status_time <", value, "custStatusTime");
            return (Criteria) this;
        }

        public Criteria andCustStatusTimeLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_status_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustStatusTimeLessThanOrEqualTo(Date value) {
            addCriterion("cust_status_time <=", value, "custStatusTime");
            return (Criteria) this;
        }

        public Criteria andCustStatusTimeLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("cust_status_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustStatusTimeIn(List<Date> values) {
            addCriterion("cust_status_time in", values, "custStatusTime");
            return (Criteria) this;
        }

        public Criteria andCustStatusTimeNotIn(List<Date> values) {
            addCriterion("cust_status_time not in", values, "custStatusTime");
            return (Criteria) this;
        }

        public Criteria andCustStatusTimeBetween(Date value1, Date value2) {
            addCriterion("cust_status_time between", value1, value2, "custStatusTime");
            return (Criteria) this;
        }

        public Criteria andCustStatusTimeNotBetween(Date value1, Date value2) {
            addCriterion("cust_status_time not between", value1, value2, "custStatusTime");
            return (Criteria) this;
        }

        public Criteria andRoleTypeIsNull() {
            addCriterion("role_type is null");
            return (Criteria) this;
        }

        public Criteria andRoleTypeIsNotNull() {
            addCriterion("role_type is not null");
            return (Criteria) this;
        }

        public Criteria andRoleTypeEqualTo(String value) {
            addCriterion("role_type =", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("role_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRoleTypeNotEqualTo(String value) {
            addCriterion("role_type <>", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("role_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRoleTypeGreaterThan(String value) {
            addCriterion("role_type >", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("role_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRoleTypeGreaterThanOrEqualTo(String value) {
            addCriterion("role_type >=", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("role_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRoleTypeLessThan(String value) {
            addCriterion("role_type <", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("role_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRoleTypeLessThanOrEqualTo(String value) {
            addCriterion("role_type <=", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("role_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRoleTypeLike(String value) {
            addCriterion("role_type like", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeNotLike(String value) {
            addCriterion("role_type not like", value, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeIn(List<String> values) {
            addCriterion("role_type in", values, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeNotIn(List<String> values) {
            addCriterion("role_type not in", values, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeBetween(String value1, String value2) {
            addCriterion("role_type between", value1, value2, "roleType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeNotBetween(String value1, String value2) {
            addCriterion("role_type not between", value1, value2, "roleType");
            return (Criteria) this;
        }

        public Criteria andBeIdIsNull() {
            addCriterion("be_id is null");
            return (Criteria) this;
        }

        public Criteria andBeIdIsNotNull() {
            addCriterion("be_id is not null");
            return (Criteria) this;
        }

        public Criteria andBeIdEqualTo(String value) {
            addCriterion("be_id =", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("be_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdNotEqualTo(String value) {
            addCriterion("be_id <>", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("be_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThan(String value) {
            addCriterion("be_id >", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("be_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanOrEqualTo(String value) {
            addCriterion("be_id >=", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("be_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLessThan(String value) {
            addCriterion("be_id <", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("be_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanOrEqualTo(String value) {
            addCriterion("be_id <=", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("be_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLike(String value) {
            addCriterion("be_id like", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotLike(String value) {
            addCriterion("be_id not like", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdIn(List<String> values) {
            addCriterion("be_id in", values, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotIn(List<String> values) {
            addCriterion("be_id not in", values, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdBetween(String value1, String value2) {
            addCriterion("be_id between", value1, value2, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotBetween(String value1, String value2) {
            addCriterion("be_id not between", value1, value2, "beId");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIsNull() {
            addCriterion("province_name is null");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIsNotNull() {
            addCriterion("province_name is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceNameEqualTo(String value) {
            addCriterion("province_name =", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("province_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotEqualTo(String value) {
            addCriterion("province_name <>", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("province_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThan(String value) {
            addCriterion("province_name >", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("province_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanOrEqualTo(String value) {
            addCriterion("province_name >=", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("province_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThan(String value) {
            addCriterion("province_name <", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("province_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanOrEqualTo(String value) {
            addCriterion("province_name <=", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("province_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNameLike(String value) {
            addCriterion("province_name like", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotLike(String value) {
            addCriterion("province_name not like", value, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameIn(List<String> values) {
            addCriterion("province_name in", values, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotIn(List<String> values) {
            addCriterion("province_name not in", values, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameBetween(String value1, String value2) {
            addCriterion("province_name between", value1, value2, "provinceName");
            return (Criteria) this;
        }

        public Criteria andProvinceNameNotBetween(String value1, String value2) {
            addCriterion("province_name not between", value1, value2, "provinceName");
            return (Criteria) this;
        }

        public Criteria andLocationIsNull() {
            addCriterion("location is null");
            return (Criteria) this;
        }

        public Criteria andLocationIsNotNull() {
            addCriterion("location is not null");
            return (Criteria) this;
        }

        public Criteria andLocationEqualTo(String value) {
            addCriterion("location =", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("location = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationNotEqualTo(String value) {
            addCriterion("location <>", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("location <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThan(String value) {
            addCriterion("location >", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("location > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThanOrEqualTo(String value) {
            addCriterion("location >=", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("location >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationLessThan(String value) {
            addCriterion("location <", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("location < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationLessThanOrEqualTo(String value) {
            addCriterion("location <=", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("location <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andLocationLike(String value) {
            addCriterion("location like", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotLike(String value) {
            addCriterion("location not like", value, "location");
            return (Criteria) this;
        }

        public Criteria andLocationIn(List<String> values) {
            addCriterion("location in", values, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotIn(List<String> values) {
            addCriterion("location not in", values, "location");
            return (Criteria) this;
        }

        public Criteria andLocationBetween(String value1, String value2) {
            addCriterion("location between", value1, value2, "location");
            return (Criteria) this;
        }

        public Criteria andLocationNotBetween(String value1, String value2) {
            addCriterion("location not between", value1, value2, "location");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNull() {
            addCriterion("city_name is null");
            return (Criteria) this;
        }

        public Criteria andCityNameIsNotNull() {
            addCriterion("city_name is not null");
            return (Criteria) this;
        }

        public Criteria andCityNameEqualTo(String value) {
            addCriterion("city_name =", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("city_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameNotEqualTo(String value) {
            addCriterion("city_name <>", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("city_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThan(String value) {
            addCriterion("city_name >", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("city_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("city_name >=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("city_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLessThan(String value) {
            addCriterion("city_name <", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("city_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanOrEqualTo(String value) {
            addCriterion("city_name <=", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("city_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCityNameLike(String value) {
            addCriterion("city_name like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotLike(String value) {
            addCriterion("city_name not like", value, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameIn(List<String> values) {
            addCriterion("city_name in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotIn(List<String> values) {
            addCriterion("city_name not in", values, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameBetween(String value1, String value2) {
            addCriterion("city_name between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andCityNameNotBetween(String value1, String value2) {
            addCriterion("city_name not between", value1, value2, "cityName");
            return (Criteria) this;
        }

        public Criteria andRegionIdIsNull() {
            addCriterion("region_id is null");
            return (Criteria) this;
        }

        public Criteria andRegionIdIsNotNull() {
            addCriterion("region_id is not null");
            return (Criteria) this;
        }

        public Criteria andRegionIdEqualTo(String value) {
            addCriterion("region_id =", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("region_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdNotEqualTo(String value) {
            addCriterion("region_id <>", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("region_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThan(String value) {
            addCriterion("region_id >", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("region_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanOrEqualTo(String value) {
            addCriterion("region_id >=", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("region_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThan(String value) {
            addCriterion("region_id <", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("region_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanOrEqualTo(String value) {
            addCriterion("region_id <=", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("region_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLike(String value) {
            addCriterion("region_id like", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotLike(String value) {
            addCriterion("region_id not like", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdIn(List<String> values) {
            addCriterion("region_id in", values, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotIn(List<String> values) {
            addCriterion("region_id not in", values, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdBetween(String value1, String value2) {
            addCriterion("region_id between", value1, value2, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotBetween(String value1, String value2) {
            addCriterion("region_id not between", value1, value2, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionNameIsNull() {
            addCriterion("region_name is null");
            return (Criteria) this;
        }

        public Criteria andRegionNameIsNotNull() {
            addCriterion("region_name is not null");
            return (Criteria) this;
        }

        public Criteria andRegionNameEqualTo(String value) {
            addCriterion("region_name =", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("region_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameNotEqualTo(String value) {
            addCriterion("region_name <>", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("region_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThan(String value) {
            addCriterion("region_name >", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("region_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThanOrEqualTo(String value) {
            addCriterion("region_name >=", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("region_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThan(String value) {
            addCriterion("region_name <", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("region_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThanOrEqualTo(String value) {
            addCriterion("region_name <=", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("region_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionNameLike(String value) {
            addCriterion("region_name like", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotLike(String value) {
            addCriterion("region_name not like", value, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameIn(List<String> values) {
            addCriterion("region_name in", values, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotIn(List<String> values) {
            addCriterion("region_name not in", values, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameBetween(String value1, String value2) {
            addCriterion("region_name between", value1, value2, "regionName");
            return (Criteria) this;
        }

        public Criteria andRegionNameNotBetween(String value1, String value2) {
            addCriterion("region_name not between", value1, value2, "regionName");
            return (Criteria) this;
        }

        public Criteria andClientRegisterIsNull() {
            addCriterion("client_register is null");
            return (Criteria) this;
        }

        public Criteria andClientRegisterIsNotNull() {
            addCriterion("client_register is not null");
            return (Criteria) this;
        }

        public Criteria andClientRegisterEqualTo(Date value) {
            addCriterion("client_register =", value, "clientRegister");
            return (Criteria) this;
        }

        public Criteria andClientRegisterEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("client_register = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientRegisterNotEqualTo(Date value) {
            addCriterion("client_register <>", value, "clientRegister");
            return (Criteria) this;
        }

        public Criteria andClientRegisterNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("client_register <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientRegisterGreaterThan(Date value) {
            addCriterion("client_register >", value, "clientRegister");
            return (Criteria) this;
        }

        public Criteria andClientRegisterGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("client_register > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientRegisterGreaterThanOrEqualTo(Date value) {
            addCriterion("client_register >=", value, "clientRegister");
            return (Criteria) this;
        }

        public Criteria andClientRegisterGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("client_register >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientRegisterLessThan(Date value) {
            addCriterion("client_register <", value, "clientRegister");
            return (Criteria) this;
        }

        public Criteria andClientRegisterLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("client_register < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientRegisterLessThanOrEqualTo(Date value) {
            addCriterion("client_register <=", value, "clientRegister");
            return (Criteria) this;
        }

        public Criteria andClientRegisterLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("client_register <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientRegisterIn(List<Date> values) {
            addCriterion("client_register in", values, "clientRegister");
            return (Criteria) this;
        }

        public Criteria andClientRegisterNotIn(List<Date> values) {
            addCriterion("client_register not in", values, "clientRegister");
            return (Criteria) this;
        }

        public Criteria andClientRegisterBetween(Date value1, Date value2) {
            addCriterion("client_register between", value1, value2, "clientRegister");
            return (Criteria) this;
        }

        public Criteria andClientRegisterNotBetween(Date value1, Date value2) {
            addCriterion("client_register not between", value1, value2, "clientRegister");
            return (Criteria) this;
        }

        public Criteria andClientStatusIsNull() {
            addCriterion("client_status is null");
            return (Criteria) this;
        }

        public Criteria andClientStatusIsNotNull() {
            addCriterion("client_status is not null");
            return (Criteria) this;
        }

        public Criteria andClientStatusEqualTo(String value) {
            addCriterion("client_status =", value, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("client_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientStatusNotEqualTo(String value) {
            addCriterion("client_status <>", value, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("client_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientStatusGreaterThan(String value) {
            addCriterion("client_status >", value, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("client_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientStatusGreaterThanOrEqualTo(String value) {
            addCriterion("client_status >=", value, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("client_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientStatusLessThan(String value) {
            addCriterion("client_status <", value, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("client_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientStatusLessThanOrEqualTo(String value) {
            addCriterion("client_status <=", value, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("client_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andClientStatusLike(String value) {
            addCriterion("client_status like", value, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusNotLike(String value) {
            addCriterion("client_status not like", value, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusIn(List<String> values) {
            addCriterion("client_status in", values, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusNotIn(List<String> values) {
            addCriterion("client_status not in", values, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusBetween(String value1, String value2) {
            addCriterion("client_status between", value1, value2, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andClientStatusNotBetween(String value1, String value2) {
            addCriterion("client_status not between", value1, value2, "clientStatus");
            return (Criteria) this;
        }

        public Criteria andNumberLoginsIsNull() {
            addCriterion("number_logins is null");
            return (Criteria) this;
        }

        public Criteria andNumberLoginsIsNotNull() {
            addCriterion("number_logins is not null");
            return (Criteria) this;
        }

        public Criteria andNumberLoginsEqualTo(Integer value) {
            addCriterion("number_logins =", value, "numberLogins");
            return (Criteria) this;
        }

        public Criteria andNumberLoginsEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("number_logins = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNumberLoginsNotEqualTo(Integer value) {
            addCriterion("number_logins <>", value, "numberLogins");
            return (Criteria) this;
        }

        public Criteria andNumberLoginsNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("number_logins <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNumberLoginsGreaterThan(Integer value) {
            addCriterion("number_logins >", value, "numberLogins");
            return (Criteria) this;
        }

        public Criteria andNumberLoginsGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("number_logins > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNumberLoginsGreaterThanOrEqualTo(Integer value) {
            addCriterion("number_logins >=", value, "numberLogins");
            return (Criteria) this;
        }

        public Criteria andNumberLoginsGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("number_logins >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNumberLoginsLessThan(Integer value) {
            addCriterion("number_logins <", value, "numberLogins");
            return (Criteria) this;
        }

        public Criteria andNumberLoginsLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("number_logins < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNumberLoginsLessThanOrEqualTo(Integer value) {
            addCriterion("number_logins <=", value, "numberLogins");
            return (Criteria) this;
        }

        public Criteria andNumberLoginsLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("number_logins <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNumberLoginsIn(List<Integer> values) {
            addCriterion("number_logins in", values, "numberLogins");
            return (Criteria) this;
        }

        public Criteria andNumberLoginsNotIn(List<Integer> values) {
            addCriterion("number_logins not in", values, "numberLogins");
            return (Criteria) this;
        }

        public Criteria andNumberLoginsBetween(Integer value1, Integer value2) {
            addCriterion("number_logins between", value1, value2, "numberLogins");
            return (Criteria) this;
        }

        public Criteria andNumberLoginsNotBetween(Integer value1, Integer value2) {
            addCriterion("number_logins not between", value1, value2, "numberLogins");
            return (Criteria) this;
        }

        public Criteria andDistributorNameIsNull() {
            addCriterion("distributor_name is null");
            return (Criteria) this;
        }

        public Criteria andDistributorNameIsNotNull() {
            addCriterion("distributor_name is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorNameEqualTo(String value) {
            addCriterion("distributor_name =", value, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorNameNotEqualTo(String value) {
            addCriterion("distributor_name <>", value, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorNameGreaterThan(String value) {
            addCriterion("distributor_name >", value, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorNameGreaterThanOrEqualTo(String value) {
            addCriterion("distributor_name >=", value, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorNameLessThan(String value) {
            addCriterion("distributor_name <", value, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorNameLessThanOrEqualTo(String value) {
            addCriterion("distributor_name <=", value, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorNameLike(String value) {
            addCriterion("distributor_name like", value, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameNotLike(String value) {
            addCriterion("distributor_name not like", value, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameIn(List<String> values) {
            addCriterion("distributor_name in", values, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameNotIn(List<String> values) {
            addCriterion("distributor_name not in", values, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameBetween(String value1, String value2) {
            addCriterion("distributor_name between", value1, value2, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorNameNotBetween(String value1, String value2) {
            addCriterion("distributor_name not between", value1, value2, "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorInvitationRegisterSuccessfulQuantityIsNull() {
            addCriterion("distributor_invitation_register_successful_quantity is null");
            return (Criteria) this;
        }

        public Criteria andDistributorInvitationRegisterSuccessfulQuantityIsNotNull() {
            addCriterion("distributor_invitation_register_successful_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorInvitationRegisterSuccessfulQuantityEqualTo(Integer value) {
            addCriterion("distributor_invitation_register_successful_quantity =", value, "distributorInvitationRegisterSuccessfulQuantity");
            return (Criteria) this;
        }

        public Criteria andDistributorInvitationRegisterSuccessfulQuantityEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_invitation_register_successful_quantity = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorInvitationRegisterSuccessfulQuantityNotEqualTo(Integer value) {
            addCriterion("distributor_invitation_register_successful_quantity <>", value, "distributorInvitationRegisterSuccessfulQuantity");
            return (Criteria) this;
        }

        public Criteria andDistributorInvitationRegisterSuccessfulQuantityNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_invitation_register_successful_quantity <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorInvitationRegisterSuccessfulQuantityGreaterThan(Integer value) {
            addCriterion("distributor_invitation_register_successful_quantity >", value, "distributorInvitationRegisterSuccessfulQuantity");
            return (Criteria) this;
        }

        public Criteria andDistributorInvitationRegisterSuccessfulQuantityGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_invitation_register_successful_quantity > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorInvitationRegisterSuccessfulQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("distributor_invitation_register_successful_quantity >=", value, "distributorInvitationRegisterSuccessfulQuantity");
            return (Criteria) this;
        }

        public Criteria andDistributorInvitationRegisterSuccessfulQuantityGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_invitation_register_successful_quantity >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorInvitationRegisterSuccessfulQuantityLessThan(Integer value) {
            addCriterion("distributor_invitation_register_successful_quantity <", value, "distributorInvitationRegisterSuccessfulQuantity");
            return (Criteria) this;
        }

        public Criteria andDistributorInvitationRegisterSuccessfulQuantityLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_invitation_register_successful_quantity < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorInvitationRegisterSuccessfulQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("distributor_invitation_register_successful_quantity <=", value, "distributorInvitationRegisterSuccessfulQuantity");
            return (Criteria) this;
        }

        public Criteria andDistributorInvitationRegisterSuccessfulQuantityLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_invitation_register_successful_quantity <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorInvitationRegisterSuccessfulQuantityIn(List<Integer> values) {
            addCriterion("distributor_invitation_register_successful_quantity in", values, "distributorInvitationRegisterSuccessfulQuantity");
            return (Criteria) this;
        }

        public Criteria andDistributorInvitationRegisterSuccessfulQuantityNotIn(List<Integer> values) {
            addCriterion("distributor_invitation_register_successful_quantity not in", values, "distributorInvitationRegisterSuccessfulQuantity");
            return (Criteria) this;
        }

        public Criteria andDistributorInvitationRegisterSuccessfulQuantityBetween(Integer value1, Integer value2) {
            addCriterion("distributor_invitation_register_successful_quantity between", value1, value2, "distributorInvitationRegisterSuccessfulQuantity");
            return (Criteria) this;
        }

        public Criteria andDistributorInvitationRegisterSuccessfulQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("distributor_invitation_register_successful_quantity not between", value1, value2, "distributorInvitationRegisterSuccessfulQuantity");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdIsNull() {
            addCriterion("distributor_channel_id is null");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdIsNotNull() {
            addCriterion("distributor_channel_id is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdEqualTo(String value) {
            addCriterion("distributor_channel_id =", value, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_channel_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdNotEqualTo(String value) {
            addCriterion("distributor_channel_id <>", value, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_channel_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdGreaterThan(String value) {
            addCriterion("distributor_channel_id >", value, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_channel_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdGreaterThanOrEqualTo(String value) {
            addCriterion("distributor_channel_id >=", value, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_channel_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdLessThan(String value) {
            addCriterion("distributor_channel_id <", value, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_channel_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdLessThanOrEqualTo(String value) {
            addCriterion("distributor_channel_id <=", value, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_channel_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdLike(String value) {
            addCriterion("distributor_channel_id like", value, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdNotLike(String value) {
            addCriterion("distributor_channel_id not like", value, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdIn(List<String> values) {
            addCriterion("distributor_channel_id in", values, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdNotIn(List<String> values) {
            addCriterion("distributor_channel_id not in", values, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdBetween(String value1, String value2) {
            addCriterion("distributor_channel_id between", value1, value2, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdNotBetween(String value1, String value2) {
            addCriterion("distributor_channel_id not between", value1, value2, "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameIsNull() {
            addCriterion("distributor_channel_name is null");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameIsNotNull() {
            addCriterion("distributor_channel_name is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameEqualTo(String value) {
            addCriterion("distributor_channel_name =", value, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_channel_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameNotEqualTo(String value) {
            addCriterion("distributor_channel_name <>", value, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_channel_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameGreaterThan(String value) {
            addCriterion("distributor_channel_name >", value, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_channel_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameGreaterThanOrEqualTo(String value) {
            addCriterion("distributor_channel_name >=", value, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_channel_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameLessThan(String value) {
            addCriterion("distributor_channel_name <", value, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_channel_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameLessThanOrEqualTo(String value) {
            addCriterion("distributor_channel_name <=", value, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_channel_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameLike(String value) {
            addCriterion("distributor_channel_name like", value, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameNotLike(String value) {
            addCriterion("distributor_channel_name not like", value, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameIn(List<String> values) {
            addCriterion("distributor_channel_name in", values, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameNotIn(List<String> values) {
            addCriterion("distributor_channel_name not in", values, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameBetween(String value1, String value2) {
            addCriterion("distributor_channel_name between", value1, value2, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameNotBetween(String value1, String value2) {
            addCriterion("distributor_channel_name not between", value1, value2, "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeIsNull() {
            addCriterion("distributor_referral_code is null");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeIsNotNull() {
            addCriterion("distributor_referral_code is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeEqualTo(String value) {
            addCriterion("distributor_referral_code =", value, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_referral_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeNotEqualTo(String value) {
            addCriterion("distributor_referral_code <>", value, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_referral_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeGreaterThan(String value) {
            addCriterion("distributor_referral_code >", value, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_referral_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeGreaterThanOrEqualTo(String value) {
            addCriterion("distributor_referral_code >=", value, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_referral_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeLessThan(String value) {
            addCriterion("distributor_referral_code <", value, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_referral_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeLessThanOrEqualTo(String value) {
            addCriterion("distributor_referral_code <=", value, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_referral_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeLike(String value) {
            addCriterion("distributor_referral_code like", value, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeNotLike(String value) {
            addCriterion("distributor_referral_code not like", value, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeIn(List<String> values) {
            addCriterion("distributor_referral_code in", values, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeNotIn(List<String> values) {
            addCriterion("distributor_referral_code not in", values, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeBetween(String value1, String value2) {
            addCriterion("distributor_referral_code between", value1, value2, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeNotBetween(String value1, String value2) {
            addCriterion("distributor_referral_code not between", value1, value2, "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfIsNull() {
            addCriterion("distributor_mrg_inf is null");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfIsNotNull() {
            addCriterion("distributor_mrg_inf is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfEqualTo(String value) {
            addCriterion("distributor_mrg_inf =", value, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_inf = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfNotEqualTo(String value) {
            addCriterion("distributor_mrg_inf <>", value, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_inf <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfGreaterThan(String value) {
            addCriterion("distributor_mrg_inf >", value, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_inf > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfGreaterThanOrEqualTo(String value) {
            addCriterion("distributor_mrg_inf >=", value, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_inf >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfLessThan(String value) {
            addCriterion("distributor_mrg_inf <", value, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_inf < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfLessThanOrEqualTo(String value) {
            addCriterion("distributor_mrg_inf <=", value, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_inf <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfLike(String value) {
            addCriterion("distributor_mrg_inf like", value, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfNotLike(String value) {
            addCriterion("distributor_mrg_inf not like", value, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfIn(List<String> values) {
            addCriterion("distributor_mrg_inf in", values, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfNotIn(List<String> values) {
            addCriterion("distributor_mrg_inf not in", values, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfBetween(String value1, String value2) {
            addCriterion("distributor_mrg_inf between", value1, value2, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfNotBetween(String value1, String value2) {
            addCriterion("distributor_mrg_inf not between", value1, value2, "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeIsNull() {
            addCriterion("distributor_mrg_code is null");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeIsNotNull() {
            addCriterion("distributor_mrg_code is not null");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeEqualTo(String value) {
            addCriterion("distributor_mrg_code =", value, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeNotEqualTo(String value) {
            addCriterion("distributor_mrg_code <>", value, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeGreaterThan(String value) {
            addCriterion("distributor_mrg_code >", value, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeGreaterThanOrEqualTo(String value) {
            addCriterion("distributor_mrg_code >=", value, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeLessThan(String value) {
            addCriterion("distributor_mrg_code <", value, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeLessThanOrEqualTo(String value) {
            addCriterion("distributor_mrg_code <=", value, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("distributor_mrg_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeLike(String value) {
            addCriterion("distributor_mrg_code like", value, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeNotLike(String value) {
            addCriterion("distributor_mrg_code not like", value, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeIn(List<String> values) {
            addCriterion("distributor_mrg_code in", values, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeNotIn(List<String> values) {
            addCriterion("distributor_mrg_code not in", values, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeBetween(String value1, String value2) {
            addCriterion("distributor_mrg_code between", value1, value2, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeNotBetween(String value1, String value2) {
            addCriterion("distributor_mrg_code not between", value1, value2, "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andAgentNumberIsNull() {
            addCriterion("agent_number is null");
            return (Criteria) this;
        }

        public Criteria andAgentNumberIsNotNull() {
            addCriterion("agent_number is not null");
            return (Criteria) this;
        }

        public Criteria andAgentNumberEqualTo(String value) {
            addCriterion("agent_number =", value, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("agent_number = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNumberNotEqualTo(String value) {
            addCriterion("agent_number <>", value, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("agent_number <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNumberGreaterThan(String value) {
            addCriterion("agent_number >", value, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("agent_number > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNumberGreaterThanOrEqualTo(String value) {
            addCriterion("agent_number >=", value, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("agent_number >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNumberLessThan(String value) {
            addCriterion("agent_number <", value, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("agent_number < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNumberLessThanOrEqualTo(String value) {
            addCriterion("agent_number <=", value, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("agent_number <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNumberLike(String value) {
            addCriterion("agent_number like", value, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberNotLike(String value) {
            addCriterion("agent_number not like", value, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberIn(List<String> values) {
            addCriterion("agent_number in", values, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberNotIn(List<String> values) {
            addCriterion("agent_number not in", values, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberBetween(String value1, String value2) {
            addCriterion("agent_number between", value1, value2, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNumberNotBetween(String value1, String value2) {
            addCriterion("agent_number not between", value1, value2, "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNameIsNull() {
            addCriterion("agent_name is null");
            return (Criteria) this;
        }

        public Criteria andAgentNameIsNotNull() {
            addCriterion("agent_name is not null");
            return (Criteria) this;
        }

        public Criteria andAgentNameEqualTo(String value) {
            addCriterion("agent_name =", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("agent_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNameNotEqualTo(String value) {
            addCriterion("agent_name <>", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameNotEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("agent_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNameGreaterThan(String value) {
            addCriterion("agent_name >", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameGreaterThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("agent_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNameGreaterThanOrEqualTo(String value) {
            addCriterion("agent_name >=", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameGreaterThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("agent_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNameLessThan(String value) {
            addCriterion("agent_name <", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameLessThanColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("agent_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNameLessThanOrEqualTo(String value) {
            addCriterion("agent_name <=", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameLessThanOrEqualToColumn(ShopCustomerInfoHistory.Column column) {
            addCriterion(new StringBuilder("agent_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAgentNameLike(String value) {
            addCriterion("agent_name like", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameNotLike(String value) {
            addCriterion("agent_name not like", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameIn(List<String> values) {
            addCriterion("agent_name in", values, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameNotIn(List<String> values) {
            addCriterion("agent_name not in", values, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameBetween(String value1, String value2) {
            addCriterion("agent_name between", value1, value2, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameNotBetween(String value1, String value2) {
            addCriterion("agent_name not between", value1, value2, "agentName");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andCustCodeLikeInsensitive(String value) {
            addCriterion("upper(cust_code) like", value.toUpperCase(), "custCode");
            return (Criteria) this;
        }

        public Criteria andCustIdLikeInsensitive(String value) {
            addCriterion("upper(cust_id) like", value.toUpperCase(), "custId");
            return (Criteria) this;
        }

        public Criteria andUserIdLikeInsensitive(String value) {
            addCriterion("upper(user_id) like", value.toUpperCase(), "userId");
            return (Criteria) this;
        }

        public Criteria andCustNameLikeInsensitive(String value) {
            addCriterion("upper(cust_name) like", value.toUpperCase(), "custName");
            return (Criteria) this;
        }

        public Criteria andOprTypeLikeInsensitive(String value) {
            addCriterion("upper(opr_type) like", value.toUpperCase(), "oprType");
            return (Criteria) this;
        }

        public Criteria andRoleTypeLikeInsensitive(String value) {
            addCriterion("upper(role_type) like", value.toUpperCase(), "roleType");
            return (Criteria) this;
        }

        public Criteria andBeIdLikeInsensitive(String value) {
            addCriterion("upper(be_id) like", value.toUpperCase(), "beId");
            return (Criteria) this;
        }

        public Criteria andProvinceNameLikeInsensitive(String value) {
            addCriterion("upper(province_name) like", value.toUpperCase(), "provinceName");
            return (Criteria) this;
        }

        public Criteria andLocationLikeInsensitive(String value) {
            addCriterion("upper(location) like", value.toUpperCase(), "location");
            return (Criteria) this;
        }

        public Criteria andCityNameLikeInsensitive(String value) {
            addCriterion("upper(city_name) like", value.toUpperCase(), "cityName");
            return (Criteria) this;
        }

        public Criteria andRegionIdLikeInsensitive(String value) {
            addCriterion("upper(region_id) like", value.toUpperCase(), "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionNameLikeInsensitive(String value) {
            addCriterion("upper(region_name) like", value.toUpperCase(), "regionName");
            return (Criteria) this;
        }

        public Criteria andClientStatusLikeInsensitive(String value) {
            addCriterion("upper(client_status) like", value.toUpperCase(), "clientStatus");
            return (Criteria) this;
        }

        public Criteria andDistributorNameLikeInsensitive(String value) {
            addCriterion("upper(distributor_name) like", value.toUpperCase(), "distributorName");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelIdLikeInsensitive(String value) {
            addCriterion("upper(distributor_channel_id) like", value.toUpperCase(), "distributorChannelId");
            return (Criteria) this;
        }

        public Criteria andDistributorChannelNameLikeInsensitive(String value) {
            addCriterion("upper(distributor_channel_name) like", value.toUpperCase(), "distributorChannelName");
            return (Criteria) this;
        }

        public Criteria andDistributorReferralCodeLikeInsensitive(String value) {
            addCriterion("upper(distributor_referral_code) like", value.toUpperCase(), "distributorReferralCode");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgInfLikeInsensitive(String value) {
            addCriterion("upper(distributor_mrg_inf) like", value.toUpperCase(), "distributorMrgInf");
            return (Criteria) this;
        }

        public Criteria andDistributorMrgCodeLikeInsensitive(String value) {
            addCriterion("upper(distributor_mrg_code) like", value.toUpperCase(), "distributorMrgCode");
            return (Criteria) this;
        }

        public Criteria andAgentNumberLikeInsensitive(String value) {
            addCriterion("upper(agent_number) like", value.toUpperCase(), "agentNumber");
            return (Criteria) this;
        }

        public Criteria andAgentNameLikeInsensitive(String value) {
            addCriterion("upper(agent_name) like", value.toUpperCase(), "agentName");
            return (Criteria) this;
        }
    }

    /**
     * Corresponding to the database table supply_chain..shop_customer_info_history
     *
     * @mbg.generated do_not_delete_during_merge Fri May 31 11:13:17 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         * Corresponding to the database table supply_chain..shop_customer_info_history
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        private ShopCustomerInfoHistoryExample example;

        /**
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        protected Criteria(ShopCustomerInfoHistoryExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        public ShopCustomerInfoHistoryExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Fri May 31 11:13:17 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     * Corresponding to the database table supply_chain..shop_customer_info_history
     *
     * @mbg.generated Fri May 31 11:13:17 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Fri May 31 11:13:17 CST 2024
         */
        void example(com.chinamobile.iot.sc.pojo.entity.ShopCustomerInfoHistoryExample example);
    }
}