package com.chinamobile.iot.sc.util;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.mode.IOTRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

import java.nio.charset.StandardCharsets;

/**
 * @Author: YSC
 * @Date: 2021/11/11 15:58
 * @Description: 封装IOT商城请求结构体
 */

@Slf4j
public class IOTRequestUtils {

    public static String getIotRequest(String content, String key, String beId, String regionId) {
        IOTRequest iotRequest = new IOTRequest();
        // 002 物联网公司
        iotRequest.setMessageSeq(beId + BaseServiceUtils.getId());
        //1 按照省份路由
        //2 按号码路由
        //3 按操作员路由
        iotRequest.setRouteType("1");
        iotRequest.setRouteValue(beId);
        iotRequest.setRegionId(regionId);
        iotRequest.setBeId(beId);
        String originContent = ("content=" + content + "&" + key);
        log.info("originContent={}",originContent);
        iotRequest.setSign(DigestUtils.md5Hex(originContent.getBytes(StandardCharsets.UTF_8)));
        // 渠道编码 OS系统为31 IOT应用市场为13 CM IOT为601
        iotRequest.setChannelId("31");
        iotRequest.setContent(content);
        return JSON.toJSONString(iotRequest);
    }
}
