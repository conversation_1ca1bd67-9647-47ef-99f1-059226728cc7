package com.chinamobile.iot.sc.pojo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CardValueAddedInfoExample {
    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    public CardValueAddedInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    public CardValueAddedInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    public CardValueAddedInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        CardValueAddedInfoExample example = new CardValueAddedInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    public CardValueAddedInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    public CardValueAddedInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOfferingNameIsNull() {
            addCriterion("offering_name is null");
            return (Criteria) this;
        }

        public Criteria andOfferingNameIsNotNull() {
            addCriterion("offering_name is not null");
            return (Criteria) this;
        }

        public Criteria andOfferingNameEqualTo(String value) {
            addCriterion("offering_name =", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("offering_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotEqualTo(String value) {
            addCriterion("offering_name <>", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("offering_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThan(String value) {
            addCriterion("offering_name >", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("offering_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThanOrEqualTo(String value) {
            addCriterion("offering_name >=", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("offering_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThan(String value) {
            addCriterion("offering_name <", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("offering_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThanOrEqualTo(String value) {
            addCriterion("offering_name <=", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("offering_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameLike(String value) {
            addCriterion("offering_name like", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotLike(String value) {
            addCriterion("offering_name not like", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameIn(List<String> values) {
            addCriterion("offering_name in", values, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotIn(List<String> values) {
            addCriterion("offering_name not in", values, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameBetween(String value1, String value2) {
            addCriterion("offering_name between", value1, value2, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotBetween(String value1, String value2) {
            addCriterion("offering_name not between", value1, value2, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingTypeIsNull() {
            addCriterion("offering_type is null");
            return (Criteria) this;
        }

        public Criteria andOfferingTypeIsNotNull() {
            addCriterion("offering_type is not null");
            return (Criteria) this;
        }

        public Criteria andOfferingTypeEqualTo(String value) {
            addCriterion("offering_type =", value, "offeringType");
            return (Criteria) this;
        }

        public Criteria andOfferingTypeEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("offering_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingTypeNotEqualTo(String value) {
            addCriterion("offering_type <>", value, "offeringType");
            return (Criteria) this;
        }

        public Criteria andOfferingTypeNotEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("offering_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingTypeGreaterThan(String value) {
            addCriterion("offering_type >", value, "offeringType");
            return (Criteria) this;
        }

        public Criteria andOfferingTypeGreaterThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("offering_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingTypeGreaterThanOrEqualTo(String value) {
            addCriterion("offering_type >=", value, "offeringType");
            return (Criteria) this;
        }

        public Criteria andOfferingTypeGreaterThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("offering_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingTypeLessThan(String value) {
            addCriterion("offering_type <", value, "offeringType");
            return (Criteria) this;
        }

        public Criteria andOfferingTypeLessThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("offering_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingTypeLessThanOrEqualTo(String value) {
            addCriterion("offering_type <=", value, "offeringType");
            return (Criteria) this;
        }

        public Criteria andOfferingTypeLessThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("offering_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingTypeLike(String value) {
            addCriterion("offering_type like", value, "offeringType");
            return (Criteria) this;
        }

        public Criteria andOfferingTypeNotLike(String value) {
            addCriterion("offering_type not like", value, "offeringType");
            return (Criteria) this;
        }

        public Criteria andOfferingTypeIn(List<String> values) {
            addCriterion("offering_type in", values, "offeringType");
            return (Criteria) this;
        }

        public Criteria andOfferingTypeNotIn(List<String> values) {
            addCriterion("offering_type not in", values, "offeringType");
            return (Criteria) this;
        }

        public Criteria andOfferingTypeBetween(String value1, String value2) {
            addCriterion("offering_type between", value1, value2, "offeringType");
            return (Criteria) this;
        }

        public Criteria andOfferingTypeNotBetween(String value1, String value2) {
            addCriterion("offering_type not between", value1, value2, "offeringType");
            return (Criteria) this;
        }

        public Criteria andMainOfferingIsNull() {
            addCriterion("main_offering is null");
            return (Criteria) this;
        }

        public Criteria andMainOfferingIsNotNull() {
            addCriterion("main_offering is not null");
            return (Criteria) this;
        }

        public Criteria andMainOfferingEqualTo(String value) {
            addCriterion("main_offering =", value, "mainOffering");
            return (Criteria) this;
        }

        public Criteria andMainOfferingEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("main_offering = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMainOfferingNotEqualTo(String value) {
            addCriterion("main_offering <>", value, "mainOffering");
            return (Criteria) this;
        }

        public Criteria andMainOfferingNotEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("main_offering <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMainOfferingGreaterThan(String value) {
            addCriterion("main_offering >", value, "mainOffering");
            return (Criteria) this;
        }

        public Criteria andMainOfferingGreaterThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("main_offering > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMainOfferingGreaterThanOrEqualTo(String value) {
            addCriterion("main_offering >=", value, "mainOffering");
            return (Criteria) this;
        }

        public Criteria andMainOfferingGreaterThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("main_offering >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMainOfferingLessThan(String value) {
            addCriterion("main_offering <", value, "mainOffering");
            return (Criteria) this;
        }

        public Criteria andMainOfferingLessThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("main_offering < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMainOfferingLessThanOrEqualTo(String value) {
            addCriterion("main_offering <=", value, "mainOffering");
            return (Criteria) this;
        }

        public Criteria andMainOfferingLessThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("main_offering <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMainOfferingLike(String value) {
            addCriterion("main_offering like", value, "mainOffering");
            return (Criteria) this;
        }

        public Criteria andMainOfferingNotLike(String value) {
            addCriterion("main_offering not like", value, "mainOffering");
            return (Criteria) this;
        }

        public Criteria andMainOfferingIn(List<String> values) {
            addCriterion("main_offering in", values, "mainOffering");
            return (Criteria) this;
        }

        public Criteria andMainOfferingNotIn(List<String> values) {
            addCriterion("main_offering not in", values, "mainOffering");
            return (Criteria) this;
        }

        public Criteria andMainOfferingBetween(String value1, String value2) {
            addCriterion("main_offering between", value1, value2, "mainOffering");
            return (Criteria) this;
        }

        public Criteria andMainOfferingNotBetween(String value1, String value2) {
            addCriterion("main_offering not between", value1, value2, "mainOffering");
            return (Criteria) this;
        }

        public Criteria andExpensesPriceIsNull() {
            addCriterion("expenses_price is null");
            return (Criteria) this;
        }

        public Criteria andExpensesPriceIsNotNull() {
            addCriterion("expenses_price is not null");
            return (Criteria) this;
        }

        public Criteria andExpensesPriceEqualTo(String value) {
            addCriterion("expenses_price =", value, "expensesPrice");
            return (Criteria) this;
        }

        public Criteria andExpensesPriceEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("expenses_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpensesPriceNotEqualTo(String value) {
            addCriterion("expenses_price <>", value, "expensesPrice");
            return (Criteria) this;
        }

        public Criteria andExpensesPriceNotEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("expenses_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpensesPriceGreaterThan(String value) {
            addCriterion("expenses_price >", value, "expensesPrice");
            return (Criteria) this;
        }

        public Criteria andExpensesPriceGreaterThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("expenses_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpensesPriceGreaterThanOrEqualTo(String value) {
            addCriterion("expenses_price >=", value, "expensesPrice");
            return (Criteria) this;
        }

        public Criteria andExpensesPriceGreaterThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("expenses_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpensesPriceLessThan(String value) {
            addCriterion("expenses_price <", value, "expensesPrice");
            return (Criteria) this;
        }

        public Criteria andExpensesPriceLessThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("expenses_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpensesPriceLessThanOrEqualTo(String value) {
            addCriterion("expenses_price <=", value, "expensesPrice");
            return (Criteria) this;
        }

        public Criteria andExpensesPriceLessThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("expenses_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpensesPriceLike(String value) {
            addCriterion("expenses_price like", value, "expensesPrice");
            return (Criteria) this;
        }

        public Criteria andExpensesPriceNotLike(String value) {
            addCriterion("expenses_price not like", value, "expensesPrice");
            return (Criteria) this;
        }

        public Criteria andExpensesPriceIn(List<String> values) {
            addCriterion("expenses_price in", values, "expensesPrice");
            return (Criteria) this;
        }

        public Criteria andExpensesPriceNotIn(List<String> values) {
            addCriterion("expenses_price not in", values, "expensesPrice");
            return (Criteria) this;
        }

        public Criteria andExpensesPriceBetween(String value1, String value2) {
            addCriterion("expenses_price between", value1, value2, "expensesPrice");
            return (Criteria) this;
        }

        public Criteria andExpensesPriceNotBetween(String value1, String value2) {
            addCriterion("expenses_price not between", value1, value2, "expensesPrice");
            return (Criteria) this;
        }

        public Criteria andExpensesTermIsNull() {
            addCriterion("expenses_term is null");
            return (Criteria) this;
        }

        public Criteria andExpensesTermIsNotNull() {
            addCriterion("expenses_term is not null");
            return (Criteria) this;
        }

        public Criteria andExpensesTermEqualTo(String value) {
            addCriterion("expenses_term =", value, "expensesTerm");
            return (Criteria) this;
        }

        public Criteria andExpensesTermEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("expenses_term = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpensesTermNotEqualTo(String value) {
            addCriterion("expenses_term <>", value, "expensesTerm");
            return (Criteria) this;
        }

        public Criteria andExpensesTermNotEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("expenses_term <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpensesTermGreaterThan(String value) {
            addCriterion("expenses_term >", value, "expensesTerm");
            return (Criteria) this;
        }

        public Criteria andExpensesTermGreaterThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("expenses_term > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpensesTermGreaterThanOrEqualTo(String value) {
            addCriterion("expenses_term >=", value, "expensesTerm");
            return (Criteria) this;
        }

        public Criteria andExpensesTermGreaterThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("expenses_term >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpensesTermLessThan(String value) {
            addCriterion("expenses_term <", value, "expensesTerm");
            return (Criteria) this;
        }

        public Criteria andExpensesTermLessThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("expenses_term < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpensesTermLessThanOrEqualTo(String value) {
            addCriterion("expenses_term <=", value, "expensesTerm");
            return (Criteria) this;
        }

        public Criteria andExpensesTermLessThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("expenses_term <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExpensesTermLike(String value) {
            addCriterion("expenses_term like", value, "expensesTerm");
            return (Criteria) this;
        }

        public Criteria andExpensesTermNotLike(String value) {
            addCriterion("expenses_term not like", value, "expensesTerm");
            return (Criteria) this;
        }

        public Criteria andExpensesTermIn(List<String> values) {
            addCriterion("expenses_term in", values, "expensesTerm");
            return (Criteria) this;
        }

        public Criteria andExpensesTermNotIn(List<String> values) {
            addCriterion("expenses_term not in", values, "expensesTerm");
            return (Criteria) this;
        }

        public Criteria andExpensesTermBetween(String value1, String value2) {
            addCriterion("expenses_term between", value1, value2, "expensesTerm");
            return (Criteria) this;
        }

        public Criteria andExpensesTermNotBetween(String value1, String value2) {
            addCriterion("expenses_term not between", value1, value2, "expensesTerm");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitIsNull() {
            addCriterion("validity_period_unit is null");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitIsNotNull() {
            addCriterion("validity_period_unit is not null");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitEqualTo(String value) {
            addCriterion("validity_period_unit =", value, "validityPeriodUnit");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("validity_period_unit = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitNotEqualTo(String value) {
            addCriterion("validity_period_unit <>", value, "validityPeriodUnit");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitNotEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("validity_period_unit <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitGreaterThan(String value) {
            addCriterion("validity_period_unit >", value, "validityPeriodUnit");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitGreaterThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("validity_period_unit > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitGreaterThanOrEqualTo(String value) {
            addCriterion("validity_period_unit >=", value, "validityPeriodUnit");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitGreaterThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("validity_period_unit >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitLessThan(String value) {
            addCriterion("validity_period_unit <", value, "validityPeriodUnit");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitLessThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("validity_period_unit < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitLessThanOrEqualTo(String value) {
            addCriterion("validity_period_unit <=", value, "validityPeriodUnit");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitLessThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("validity_period_unit <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitLike(String value) {
            addCriterion("validity_period_unit like", value, "validityPeriodUnit");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitNotLike(String value) {
            addCriterion("validity_period_unit not like", value, "validityPeriodUnit");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitIn(List<String> values) {
            addCriterion("validity_period_unit in", values, "validityPeriodUnit");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitNotIn(List<String> values) {
            addCriterion("validity_period_unit not in", values, "validityPeriodUnit");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitBetween(String value1, String value2) {
            addCriterion("validity_period_unit between", value1, value2, "validityPeriodUnit");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitNotBetween(String value1, String value2) {
            addCriterion("validity_period_unit not between", value1, value2, "validityPeriodUnit");
            return (Criteria) this;
        }

        public Criteria andOrderQuantityIsNull() {
            addCriterion("order_quantity is null");
            return (Criteria) this;
        }

        public Criteria andOrderQuantityIsNotNull() {
            addCriterion("order_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andOrderQuantityEqualTo(String value) {
            addCriterion("order_quantity =", value, "orderQuantity");
            return (Criteria) this;
        }

        public Criteria andOrderQuantityEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("order_quantity = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderQuantityNotEqualTo(String value) {
            addCriterion("order_quantity <>", value, "orderQuantity");
            return (Criteria) this;
        }

        public Criteria andOrderQuantityNotEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("order_quantity <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderQuantityGreaterThan(String value) {
            addCriterion("order_quantity >", value, "orderQuantity");
            return (Criteria) this;
        }

        public Criteria andOrderQuantityGreaterThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("order_quantity > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderQuantityGreaterThanOrEqualTo(String value) {
            addCriterion("order_quantity >=", value, "orderQuantity");
            return (Criteria) this;
        }

        public Criteria andOrderQuantityGreaterThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("order_quantity >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderQuantityLessThan(String value) {
            addCriterion("order_quantity <", value, "orderQuantity");
            return (Criteria) this;
        }

        public Criteria andOrderQuantityLessThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("order_quantity < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderQuantityLessThanOrEqualTo(String value) {
            addCriterion("order_quantity <=", value, "orderQuantity");
            return (Criteria) this;
        }

        public Criteria andOrderQuantityLessThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("order_quantity <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderQuantityLike(String value) {
            addCriterion("order_quantity like", value, "orderQuantity");
            return (Criteria) this;
        }

        public Criteria andOrderQuantityNotLike(String value) {
            addCriterion("order_quantity not like", value, "orderQuantity");
            return (Criteria) this;
        }

        public Criteria andOrderQuantityIn(List<String> values) {
            addCriterion("order_quantity in", values, "orderQuantity");
            return (Criteria) this;
        }

        public Criteria andOrderQuantityNotIn(List<String> values) {
            addCriterion("order_quantity not in", values, "orderQuantity");
            return (Criteria) this;
        }

        public Criteria andOrderQuantityBetween(String value1, String value2) {
            addCriterion("order_quantity between", value1, value2, "orderQuantity");
            return (Criteria) this;
        }

        public Criteria andOrderQuantityNotBetween(String value1, String value2) {
            addCriterion("order_quantity not between", value1, value2, "orderQuantity");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeIsNull() {
            addCriterion("delete_time is null");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeIsNotNull() {
            addCriterion("delete_time is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeEqualTo(Date value) {
            addCriterion("delete_time =", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("delete_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotEqualTo(Date value) {
            addCriterion("delete_time <>", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("delete_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThan(Date value) {
            addCriterion("delete_time >", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("delete_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("delete_time >=", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("delete_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThan(Date value) {
            addCriterion("delete_time <", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThanColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("delete_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThanOrEqualTo(Date value) {
            addCriterion("delete_time <=", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThanOrEqualToColumn(CardValueAddedInfo.Column column) {
            addCriterion(new StringBuilder("delete_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeIn(List<Date> values) {
            addCriterion("delete_time in", values, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotIn(List<Date> values) {
            addCriterion("delete_time not in", values, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeBetween(Date value1, Date value2) {
            addCriterion("delete_time between", value1, value2, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotBetween(Date value1, Date value2) {
            addCriterion("delete_time not between", value1, value2, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andOrderIdLikeInsensitive(String value) {
            addCriterion("upper(order_id) like", value.toUpperCase(), "orderId");
            return (Criteria) this;
        }

        public Criteria andOfferingNameLikeInsensitive(String value) {
            addCriterion("upper(offering_name) like", value.toUpperCase(), "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingTypeLikeInsensitive(String value) {
            addCriterion("upper(offering_type) like", value.toUpperCase(), "offeringType");
            return (Criteria) this;
        }

        public Criteria andMainOfferingLikeInsensitive(String value) {
            addCriterion("upper(main_offering) like", value.toUpperCase(), "mainOffering");
            return (Criteria) this;
        }

        public Criteria andExpensesPriceLikeInsensitive(String value) {
            addCriterion("upper(expenses_price) like", value.toUpperCase(), "expensesPrice");
            return (Criteria) this;
        }

        public Criteria andExpensesTermLikeInsensitive(String value) {
            addCriterion("upper(expenses_term) like", value.toUpperCase(), "expensesTerm");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodUnitLikeInsensitive(String value) {
            addCriterion("upper(validity_period_unit) like", value.toUpperCase(), "validityPeriodUnit");
            return (Criteria) this;
        }

        public Criteria andOrderQuantityLikeInsensitive(String value) {
            addCriterion("upper(order_quantity) like", value.toUpperCase(), "orderQuantity");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Thu May 09 17:24:06 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Thu May 09 17:24:06 CST 2024
         */
        private CardValueAddedInfoExample example;

        /**
         *
         * @mbg.generated Thu May 09 17:24:06 CST 2024
         */
        protected Criteria(CardValueAddedInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Thu May 09 17:24:06 CST 2024
         */
        public CardValueAddedInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Thu May 09 17:24:06 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu May 09 17:24:06 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu May 09 17:24:06 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Thu May 09 17:24:06 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Thu May 09 17:24:06 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Thu May 09 17:24:06 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Thu May 09 17:24:06 CST 2024
         */
        void example(com.chinamobile.iot.sc.pojo.CardValueAddedInfoExample example);
    }
}