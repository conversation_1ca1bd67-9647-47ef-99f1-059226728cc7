package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.FinancingResponseVO;
import com.chinamobile.iot.sc.pojo.vo.StartFinancingMaskParam;
import com.chinamobile.iot.sc.service.OrderFinancingService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * created by liuxiang on 2023/7/5 17:27
 * 保理融资和产金交互相关接口
 * 以及发送验证码，判断用户是否有未完成流程的接口
 */
@RequestMapping("/financing")
@RestController
public class OrderFinancingController {

    @Resource
    private OrderFinancingService orderFinancingService;

    /**
     * 获取跳转链接(注册，认证，激活，方案确认等)
     */
    @PostMapping ("/url")
    public BaseAnswer<String> getUrl(@RequestBody @Valid QueryFinancingUrlParam param){
        return orderFinancingService.getUrl(param);
    }

    /**
     * 认证激活状态推送(产金平台 -> os)
     */
    @PostMapping("/authenticationAndActivationStatus")
    public FinancingResponseVO authenticationAndActivationStatus(@RequestBody @Valid AuthenticationAndActivationStatusParam param){
        return orderFinancingService.authenticationAndActivationStatus(param);
    }

    /**
     * 申请保理融资（推送融资信息到产金平台）
     */
    @PostMapping("/start")
    public BaseAnswer<Void> startFinancing(@RequestBody @Valid StartFinancingParam param,
                                           @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId){
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return orderFinancingService.startFinancing(param,userId,ip);
    }

    /**
     * 撤销保理融资
     */
    @PostMapping("/cancel")
    public BaseAnswer<Void> cancelFinancing(@RequestBody @Valid CancelFinancingParam param,
                                            @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId){
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return orderFinancingService.cancelFinancing(param,userId,ip);
    }

    /**
     * 融资订单确认状态信息推送(产金平台 -> os)
     * 省专审核--确认或拒绝
     */
    @PostMapping("/orderConfirmStatus")
    public FinancingResponseVO orderConfirmStatus(@RequestBody @Valid OrderConfirmStatusParam param){
        return orderFinancingService.orderConfirmStatus(param);
    }

    /**
     * 融资方案状态信息推送(产金平台 -> os)
     */
    @PostMapping("/orderFinancingStatus")
    public FinancingResponseVO orderFinancingStatus(@RequestBody @Valid OrderFinancingStatusParam param){
        return orderFinancingService.orderFinancingStatus(param);
    }

    /**
     * 还款信息推送(产金平台 -> os)
     */
    @PostMapping("/repayment")
    public FinancingResponseVO repayment(@RequestBody @Valid FinancingRepaymentParam param){
        return orderFinancingService.repayment(param);
    }

    /**
     * 发送申请保理融资的验证码
     */
    @PostMapping("/start/mask")
    public BaseAnswer<Void> startFinancingMask(@RequestBody StartFinancingMaskParam param){
        orderFinancingService.sendStartFinancingMask(param.getTradeOrderId());
        return BaseAnswer.success(null);
    }

    /**
     * 判断合伙人是否有在途贸易订单
     */
    @GetMapping("/judgeUserIfHaveEffectiveTrade")
    public BaseAnswer<Boolean> judgeUserIfHaveEffectiveTrade(String userId){
        return orderFinancingService.judgeUserIfHaveEffectiveTrade(userId);
    }

}
