package com.chinamobile.iot.sc.pojo.dto.gio;

import lombok.Data;

import java.util.Date;

@Data
public class GioAtomOriginDTO {
    private String id;
    private String offeringCode;
    private String offeringName;             // offering _name

    private String offeringClass;            // offering_class
    private String chargeCodeName;          // charge_code name
    private String chargeCode;               // charge_code
    private String extSoftOfferingCode;    // ext soft offering_cod e
    private String extHardOfferingCode;    // ext hard offering_co de
    private String offeringQuantity;         // offering_quantity
    private String offeringUnit;             // offering_unit
    private String offeringModel;            // offering model
    private String offeringColor;            // offering_color
    private String offeringPrice;            // offering_price
    private String offeringSettlementPrice; // offering_settlement price
    private String serviceCode;              // service code
    private String serviceName;              // service name
    private String productName;              // product name
    private String productDepartment;        // product department
    private String productAttributes;        // product attributes
    private String isDeleteOffering;        // is_delete_offering
    private Long quantity;
    private String unit;
    private String model;
    private String color;
    private Long atomSalePrice;
    private Long settlePrice;
    private Date deleteTime;

}
