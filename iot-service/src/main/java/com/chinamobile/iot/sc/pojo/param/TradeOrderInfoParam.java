package com.chinamobile.iot.sc.pojo.param;

import com.chinamobile.iot.sc.mode.BasePageQuery;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/17
 * @description 贸易订单参数类
 */
@Data
public class TradeOrderInfoParam extends BasePageQuery {

    /**
     * 贸易编号
     */
    private String tradeNo;

    /**
     * 合同编号(移动省专协议编号)
     */
    private String contractNum;

    /**
     * 合同相对方名称()
     */
    private String buyerName;

    /**
     * 合作伙伴名称
     */
    private String partnerName;

    /**
     * 承建方名称(移动省专公司名称)
     */
    private String sellerName;

    /**
     * 保理状态  0--未申请 1--待申请 2--省专审核中  3--省专已拒绝  4--方案制作中 5--方案确认中 6--方案已拒绝 7--方案已退回 8--方案审核中 9--银行审核中 10--银行已拒绝 11--银行已放款 12--已撤销
     */
    private Integer baoliStatus;

    /**
     * 当前用户id
     */
    private String userId;

    /**
     * 从合作伙伴用户id集合
     */
    private List<String> downUserIdList;
}
