package com.chinamobile.iot.sc.exception;

import com.chinamobile.iot.sc.annotation.LogAspectIgnore;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @Author: YSC
 * @Date: 2021/11/8 16:52
 * @Description: 捕获IOT商城异常
 */
@ControllerAdvice
@Slf4j
public class IOTExceptionHandler {
    @ExceptionHandler(IOTException.class)
    @ResponseBody
    @LogAspectIgnore
    public IOTAnswer handleException(IOTException ex){
        return ex.getAnswer();
    }
}
