package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.constant.*;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.exception.IOTException;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.pojo.entity.*;
import com.chinamobile.iot.sc.pojo.param.GetOrderUpdateLogisticsParam;
import com.chinamobile.iot.sc.pojo.param.SoftServiceOpenParam;
import com.chinamobile.iot.sc.pojo.vo.UserRefundKxVO;
import com.chinamobile.iot.sc.request.IotLogisticRequest;
import com.chinamobile.iot.sc.request.LogisticsInfoRequest;
import com.chinamobile.iot.sc.request.MsisdnInfoResultRequest;
import com.chinamobile.iot.sc.request.RenewalInformationRequest;
import com.chinamobile.iot.sc.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.constant.RedisLockConstant.ORDER_LOCK;
import static com.chinamobile.iot.sc.constant.RedisLockConstant.RENEWAL_ORDER_LOCK;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/7
 * @description OS同步到商城的service实现类
 */
@Service
@Slf4j
public class Order2IOTServiceImpl implements Order2IOTService {

    @Resource
    private SkuMsisdnRelationService skuMsisdnRelationService;

    @Resource
    private CardRelationService cardRelationService;

    @Resource
    private CardMallSyncService cardMallSyncService;

    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private Order2cAtomInfoMapper order2cAtomInfoMapper;

    @Resource
    private SpuOfferingInfoMapper spuOfferingInfoMapper;

    @Resource
    private SmsFeignClient smsFeignClient;

    @Resource
    private AtomOfferingInfoMapper atomOfferingInfoMapper;

    @Resource
    private SkuOfferingInfoHistoryMapper skuOfferingInfoHistoryMapper;

    @Resource
    private LogisticsInfoMapper logisticsInfoMapper;

    @Resource
    private IOrder2CService order2CService;

    @Resource
    private Order2cAtomHistoryMapper order2cAtomHistoryMapper;

    @Resource
    private Order2cInfoMapper order2cInfoMapper;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private UserRefundKxService userRefundKxService;

    @Resource
    private OrderRenewalInformationService orderRenewalInformationService;

    @Resource
    private AtomOfferingCooperatorRelationService atomOfferingCooperatorRelationService;

    @Resource
    private SoftService softService;

    ThreadFactory springThreadFactory = new CustomizableThreadFactory("springThread-pool-");

    ThreadPoolExecutor executor = new ThreadPoolExecutor(4, 10, 4L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(100000), springThreadFactory);

    /**
     * 订购失败短信模板
     */
    @Value("${sms.getOrderFailureTempId:106847}")
    private String getOrderFailureTempId;

    /**
     * 河南退货卡+X短信模板
     */
    @Value("${sms.HenanKXTempId:107064}")
    private String henanKXTempId;

    @Value("${iot.secretKey}")
    private String secretKey;

    /**
     * 同步物流信息的url
     */
    @Value("${iot.syncOrdersLogisInfoUrl}")
    private String syncOrdersLogisInfoUrl;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IOTAnswer<Void> syncOrderingResult(IOTRequest baseRequest) {
        log.info("订购结果请求:{}", JSON.toJSONString(baseRequest));
        IOTAnswer<Void> iotAnswer = new IOTAnswer<>();
        iotAnswer.setMessageSeq(baseRequest.getMessageSeq());
        MsisdnInfoResultRequest msisdnInfoResultRequest;
        try {
            msisdnInfoResultRequest = JSON.parseObject(baseRequest.getContent(), MsisdnInfoResultRequest.class);
        } catch (Exception e) {
            log.error("订购结果解析异常:" + e);
            throw new IOTException(iotAnswer, "订购结果数据解析异常");
        }

        // 使用分布式事务锁
        return redisUtil.smartLock(ORDER_LOCK + msisdnInfoResultRequest.getOrderId(), () -> {
            if (Optional.ofNullable(msisdnInfoResultRequest).isPresent()) {
                AtomicInteger failureCount = new AtomicInteger(0);
                String orderId = msisdnInfoResultRequest.getOrderId();
                List<MsisdnInfoResultRequest.MsisdnInfo> msisdnInfoList = msisdnInfoResultRequest.getMsisdnInfos();
                if (CollectionUtils.isEmpty(msisdnInfoList)) {
                    log.error("订购结果无有效的正式服务号码订购结果");
                    throw new IOTException(iotAnswer, "订购结果无有效的正式服务号码订购结果");
                }

                Date date = new Date();
                List<Order2cAtomInfo> order2cAtomInfoList = new ArrayList<>();
                msisdnInfoList.stream().forEach(msisdnInfo -> {
                    String msisdn = msisdnInfo.getMsisdn();
                    String resultCode = msisdnInfo.getResultCode();
                    // 更新sku和卡关系表
                    SkuMsisdnRelationExample skuMsisdnRelationExample = new SkuMsisdnRelationExample();
                    skuMsisdnRelationExample.createCriteria()
                            .andMsisdnEqualTo(msisdn)
                            .andOrderIdEqualTo(orderId);
                    List<SkuMsisdnRelation> skuMsisdnRelationList = skuMsisdnRelationService.listSkuMsisdnRelationByNeed(skuMsisdnRelationExample);
                    if (CollectionUtils.isEmpty(skuMsisdnRelationList)) {
                        log.error("订单id为{}的正式服务号码为{}的数据不存在", orderId, msisdn);
                        return;
                    }

                    SkuMsisdnRelation msisdnRelation = skuMsisdnRelationList.get(0);

                    Order2cAtomInfo order2cAtomInfo = order2cAtomInfoMapper.selectByPrimaryKey(msisdnRelation.getOrderAtomInfoId());
                    order2cAtomInfoList.add(0, order2cAtomInfo);
                    String skuOfferingCode = order2cAtomInfo.getSkuOfferingCode();
                    SkuOfferingInfoHistoryExample skuOfferingInfoHistoryExample = new SkuOfferingInfoHistoryExample();
                    skuOfferingInfoHistoryExample.createCriteria()
                            .andOfferingCodeEqualTo(skuOfferingCode)
                            .andSkuOfferingVersionEqualTo(order2cAtomInfo.getSkuOfferingVersion());
                    List<SkuOfferingInfoHistory> skuOfferingInfoHistoryList
                            = skuOfferingInfoHistoryMapper.selectByExample(skuOfferingInfoHistoryExample);
                    if (CollectionUtils.isEmpty(skuOfferingInfoHistoryList)){
                        log.error("未发现规格编码为{}的商品信息",skuOfferingCode);
                        throw new IOTException(iotAnswer, "未发现规格编码为"+skuOfferingCode+"的商品信息");
                    }
                    // 判断是否自主下单的预付费的新5类
                    String productType = skuOfferingInfoHistoryList.get(0).getProductType();
                    boolean isNew5 = order2CService.checkSkuNew5ProductType(productType);

                    SkuMsisdnRelation skuMsisdnRelation = new SkuMsisdnRelation();
                    skuMsisdnRelation.setOrderResult(Integer.parseInt(resultCode));
                    skuMsisdnRelation.setUpdateTime(date);
                    skuMsisdnRelation.setId(msisdnRelation.getId());
                    skuMsisdnRelationService.updateSkuMsisdnRelationById(skuMsisdnRelation);
                    String order2cAtomInfoId = order2cAtomInfo.getId();

                    // 订购结果1：成功  2：失败
                    if ("2".equals(resultCode)) {
                        failureCount.incrementAndGet();
                        // 更新卡信息表
                        CardRelation cardRelation = new CardRelation();
                        CardRelationExample cardRelationExample = new CardRelationExample();
                        CardRelationExample.Criteria cardRelationCriteria = cardRelationExample.createCriteria()
                               .andDeleteTimeIsNull()
                                .andOrderIdEqualTo(orderId)
                                .andOrderAtomInfoIdEqualTo(order2cAtomInfoId);
                        // 在退款成功后更新订购相关信息
                        if (!isNew5){
                            /*cardRelation.setOrderId("");
                            cardRelation.setOrderAtomInfoId("");
                            cardRelation.setMsisdn("");*/

                            cardRelationCriteria
                                    .andImeiEqualTo(msisdnRelation.getImei())
                                    .andTempIccidEqualTo(msisdnRelation.getTempIccid());
                        }
                        cardRelation.setUpdateTime(date);
                        // 商城同步订购失败结果，号卡和终端销售状态不变更，即：销售中
                        /*cardRelation.setSellStatus(SellStatusEnum.SELL_FAIL.getType());

                        cardRelationService.updateCardRelationByNeed(cardRelation, cardRelationExample);*/

                        // 订购失败时恢复库存
//                        order2CService.handleCardXInventoryInfo(order2cAtomInfo,date,SellStatusEnum.NOT_SELL.getType(),true);
                    }else if ("1".equals(resultCode)){
                        CardRelation cardRelation = new CardRelation();
                        cardRelation.setUpdateTime(date);
                        cardRelation.setSellStatus(SellStatusEnum.SELL_SUCCESS.getType());
                        CardRelationExample cardRelationExample = new CardRelationExample();
                        CardRelationExample.Criteria cardRelationCriteria = cardRelationExample.createCriteria()
                                .andDeleteTimeIsNull()
                                .andOrderIdEqualTo(orderId)
                                .andOrderAtomInfoIdEqualTo(order2cAtomInfoId);
                        if (!isNew5){
                            cardRelationCriteria
                                    .andImeiEqualTo(msisdnRelation.getImei())
                                    .andTempIccidEqualTo(msisdnRelation.getTempIccid());
                        }
                        cardRelationService.updateCardRelationByNeed(cardRelation, cardRelationExample);

                        CardMallSyncExample cardMallSyncExample = new CardMallSyncExample();
                        cardMallSyncExample.createCriteria()
                                .andOrderIdEqualTo(orderId)
                                .andAtomOrderIdEqualTo(order2cAtomInfoId)
                                .andMsisdnEqualTo(msisdn);
                        CardMallSync cardMallSync = new CardMallSync();
                        cardMallSync.setCardStatus(CardStatusEnum.SELL_SUCCESS.getType());
                        cardMallSync.setUpdateTime(date);
                        cardMallSyncService.updateByNeed(cardMallSync,cardMallSyncExample);


//                        order2CService.handleCardXInventoryInfo(order2cAtomInfo,date,SellStatusEnum.SELL_SUCCESS.getType(),false);
                    }

                });
                int failureCard = failureCount.get();
                if (failureCard > 0) {
                    // 一般不会出现相同的商品和规格出现不同的合作伙伴，因此取其中一个就是
                    if (CollectionUtils.isEmpty(order2cAtomInfoList)) {
                        log.error("无有用的订单信息");
                        throw new IOTException(iotAnswer, "无有用的订单信息");
                    }

                    // 如果是部分失败则修改订单状态为部分退款中
                    if (msisdnInfoList.size() != failureCard){
                        Order2cAtomInfo atomInfo = new Order2cAtomInfo();
                        atomInfo.setPartReturn(1);
                        Order2cAtomInfoExample atomInfoExample = new Order2cAtomInfoExample();
                        atomInfoExample.createCriteria()
                                .andOrderIdEqualTo(orderId);
                        order2cAtomInfoMapper.updateByExampleSelective(atomInfo,atomInfoExample);
                    }

                    Order2cAtomInfo order2cAtomInfo = order2cAtomInfoList.get(0);
                    AtomOfferingInfoExample atomOfferingInfoExample = new AtomOfferingInfoExample();
                    atomOfferingInfoExample.createCriteria()
                            .andSpuCodeEqualTo(order2cAtomInfo.getSpuOfferingCode())
                            .andSkuCodeEqualTo(order2cAtomInfo.getSkuOfferingCode())
                            .andOfferingCodeEqualTo(order2cAtomInfo.getAtomOfferingCode());
                    List<AtomOfferingInfo> atomOfferingInfoList = atomOfferingInfoMapper.selectByExample(atomOfferingInfoExample);
                    if (CollectionUtils.isEmpty(atomOfferingInfoList)) {
                        log.error("无有效的原子商品信息");
                        throw new IOTException(iotAnswer, "无有效的原子商品信息");
                    }

                    AtomOfferingInfo atomOfferingInfo = atomOfferingInfoList.get(0);
                    String cooperatorId = atomOfferingInfo.getCooperatorId();
                    if (StringUtils.isEmpty(cooperatorId)) {
                        log.error("无有效的合作伙伴ID信息");
                        throw new IOTException(iotAnswer, "无有效的合作伙伴ID信息");
                    }

                    Data4User userInfo = userFeignClient.userInfo(cooperatorId).getData();
                    if (!Optional.ofNullable(userInfo).isPresent()) {
                        log.error("无有效的合作伙伴信息");
                        throw new IOTException(iotAnswer, "无有效的合作伙伴信息");
                    }

                    List<String> mobiles = new ArrayList<>();
                    // 获取从合作伙伴信息
                    List<Data4User> data4UserList = atomOfferingCooperatorRelationService.listCooperatorUserInfo(atomOfferingInfo.getId());
                    List<String> cooperatorPhoneList = data4UserList.stream()
                            .map(Data4User::getPhone)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(cooperatorPhoneList)){
                        mobiles.addAll(cooperatorPhoneList);
                    }

                    SpuOfferingInfoExample spuOfferingInfoExample = new SpuOfferingInfoExample();
                    spuOfferingInfoExample.createCriteria()
                            .andOfferingCodeEqualTo(order2cAtomInfo.getSpuOfferingCode());
                    List<SpuOfferingInfo> spuOfferingInfoList = spuOfferingInfoMapper.selectByExample(spuOfferingInfoExample);
                    if (CollectionUtils.isEmpty(spuOfferingInfoList)) {
                        log.error("无有效的商品信息");
                        throw new IOTException(iotAnswer, "无有效的商品信息");
                    }

                    // 发送短信
                    Msg4Request request = new Msg4Request();
                    mobiles = mobiles.stream().distinct().collect(Collectors.toList());
                    Map<String, String> message = new HashMap<>();
                    message.put("orderId", orderId);
                    message.put("spuOfferingName", spuOfferingInfoList.get(0).getOfferingName());
                    message.put("orderQuantity", msisdnInfoList.size() + "");
                    message.put("failureQuantity", failureCard + "");

                    Boolean isSend = userInfo.getIsSend();
                    if (isSend != null && isSend){
                        mobiles.add(userInfo.getPhone());
                    }
                    request.setMessage(message);
                    request.setTemplateId(getOrderFailureTempId);
                    BaseAnswer<Void> messageAnswer = smsFeignClient.asySendMessage(request);
                    log.info("订购结果有失败的数据的短信发送结果:{}", JSON.toJSONString(messageAnswer));

                    // 订购失败发送到指定人员的短信
                    Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(orderId);
                    if (!order2CService.isOsContractFlow(order2cInfo.getAddr1())){
                        List<UserRefundKxVO> userRefundKxVOList = userRefundKxService.listUserRefundKx(NoticeTypeConstant.NOTICE_TYPE_KX);
                        if (CollectionUtils.isNotEmpty(userRefundKxVOList)){
                            List<String> kxPhoneList = userRefundKxVOList.stream().map(UserRefundKxVO::getPhone)
                                    .collect(Collectors.toList());

                            Msg4Request requestKx = new Msg4Request();
                            List<String> mobileKxList = new ArrayList<>();
                            mobileKxList.addAll(kxPhoneList);
                            mobileKxList = mobileKxList.stream().distinct().collect(Collectors.toList());
                            Map<String, String> messageKx = new HashMap<>();
                            messageKx.put("orderId", orderId);

                            requestKx.setMobiles(mobileKxList);
                            requestKx.setMessage(messageKx);
                            requestKx.setTemplateId(henanKXTempId);
                            BaseAnswer<Void> messageKxAnswer = smsFeignClient.asySendMessage(requestKx);
                            log.info("订购结果失败的数据通知指定人员的短信发送结果:{}", JSON.toJSONString(messageKxAnswer));
                        }
                    }
                } else {

                }

                // 判断有没有不含卡的数据
                CardRelationExample noCardRelationExample = new CardRelationExample();
                noCardRelationExample.createCriteria()
                        .andOrderIdEqualTo(orderId)
                        .andTerminalTypeEqualTo(TerminalTypeEnum.NO_CARD.getType());
                List<CardRelation> noCardRelationList = cardRelationService.listCardRelationByNeed(noCardRelationExample);
                if (CollectionUtils.isNotEmpty(noCardRelationList)){
                    noCardRelationList.stream().forEach(noCardRelation -> {
                        CardRelation updateNoCard = new CardRelation();
                        updateNoCard.setSellStatus(SellStatusEnum.SELL_SUCCESS.getType());
                        updateNoCard.setUpdateTime(date);
                        updateNoCard.setId(noCardRelation.getId());
                        cardRelationService.updateCardRelationByIdSelective(updateNoCard);
                    });
                }
            }

            return iotAnswer;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer syncUpdateLogistics(GetOrderUpdateLogisticsParam getOrderUpdateLogisticsParam) {
        String id = getOrderUpdateLogisticsParam.getId();
        String orderId = getOrderUpdateLogisticsParam.getOrderId();
        List<LogisticsInfoRequest.LogisticsMsg> logisticsMsgs = getOrderUpdateLogisticsParam.getLogisticsMsgs();

        Order2cAtomInfoExample order2cAtomInfoExample = new Order2cAtomInfoExample();
        order2cAtomInfoExample.createCriteria()
                .andOrderIdEqualTo(orderId)
                .andIdEqualTo(id);
        List<Order2cAtomInfo> order2cAtomInfos = order2cAtomInfoMapper.selectByExample(order2cAtomInfoExample);

        Order2cAtomInfo order2cAtomInfo = order2cAtomInfos.size() != 0 ? order2cAtomInfos.get(0) : null;
        if (order2cAtomInfo == null) {
            throw new BusinessException(StatusConstant.ORDER_NOT_EXIST);
        }
        //对订单状态进行判断。必须待发货、待收货、订单部分退款成功状态才能同步物流
        Integer orderStatus = order2cAtomInfo.getOrderStatus();
        if (!orderStatus.equals(OrderStatusInnerEnum.WAIT_SEND.getStatus())
                && !orderStatus.equals(OrderStatusInnerEnum.WAIT_RECEIVE.getStatus())
                && !orderStatus.equals(OrderStatusInnerEnum.PART_SUCCESS.getStatus())) {
            throw new BusinessException(StatusConstant.LOGISTICS_ERROR);
        }
        SkuMsisdnRelationExample skuMsisdnRelationExample = new SkuMsisdnRelationExample();
        skuMsisdnRelationExample.createCriteria()
                .andOrderIdEqualTo(orderId)
                .andOrderAtomInfoIdEqualTo(id);
        List<SkuMsisdnRelation> skuMsisdnRelationList = skuMsisdnRelationService.listSkuMsisdnRelationByNeed(skuMsisdnRelationExample);
        if (CollectionUtils.isEmpty(skuMsisdnRelationList)) {
            throw new BusinessException("10004", "没有获取到卡的关联信息");
        }

        // 删除原来的物流信息
        LogisticsInfoExample logisticsInfoExample = new LogisticsInfoExample();
        logisticsInfoExample.createCriteria()
                .andOrderAtomInfoIdEqualTo(id)
                .andOrderIdEqualTo(orderId);
        logisticsInfoMapper.deleteByExample(logisticsInfoExample);

        // 新增物流信息
        order2CService.addLogistics(logisticsMsgs, orderId, id);

        Integer orderResult = skuMsisdnRelationList.get(0).getOrderResult();
        // 不为空可以把物流信息同步到商城
        if (orderResult != null) {
            List<IotLogisticRequest.LogisInfo> logisInfoList = new ArrayList<>();
            logisticsMsgs.stream().forEach(logisticsInfo -> {
                IotLogisticRequest.LogisInfo logisInfo = new IotLogisticRequest.LogisInfo();
                logisInfo.setSignReceiptName(null);
                logisInfo.setSupplierName(logisticsInfo.getSupplierName());
                logisInfo.setLogisCode(logisticsInfo.getLogisCode());
                logisInfo.setDescription(logisticsInfo.getDescription());
                logisInfoList.add(logisInfo);
            });
            // 同步物流信息到商城
            synLogisticToIOT(logisInfoList, orderId, order2cAtomInfo);
        }
        return new BaseAnswer();
    }

    /**
     * 同步物流信息到商城
     *
     * @param logisInfoList
     * @param orderId
     * @param order2cAtomInfo
     */
    @Override
    public void synLogisticToIOT(List<IotLogisticRequest.LogisInfo> logisInfoList,
                                 String orderId,
                                 Order2cAtomInfo order2cAtomInfo) {
        // 进行物流信息同步到商城
        IotLogisticRequest.OrderInfoDTO orderInfoDTO = new IotLogisticRequest.OrderInfoDTO();
        orderInfoDTO.setOrderId(orderId);
        orderInfoDTO.setLogisInfo(logisInfoList);
        List<IotLogisticRequest.OrderInfoDTO> orderInfoDTOS = new ArrayList<>();
        orderInfoDTOS.add(orderInfoDTO);
        IotLogisticRequest iotLogisticRequest = new IotLogisticRequest();
        iotLogisticRequest.setOrderInfo(orderInfoDTOS);
        //封装对应的结构
        order2CService.sendMsgToIot(iotLogisticRequest, order2cAtomInfo.getBeId(), syncOrdersLogisInfoUrl, order2cAtomInfo.getRegionId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IOTAnswer<Void> synchronizeRenewalInformation(IOTRequest baseRequest) {
        log.info("续费操作信息:{}", JSON.toJSONString(baseRequest));
        IOTAnswer<Void> iotAnswer = new IOTAnswer<>();
        iotAnswer.setMessageSeq(baseRequest.getMessageSeq());
        RenewalInformationRequest renewalInformationRequest;
        try {
            renewalInformationRequest = JSON.parseObject(baseRequest.getContent(), RenewalInformationRequest.class);
        } catch (Exception e) {
            log.error("续费操作信息解析异常:" + e);
            throw new IOTException(iotAnswer, "续费操作信息数据解析异常");
        }

        // 使用分布式事务锁
        String orderId = renewalInformationRequest.getOrderId();
        return redisUtil.smartLock(RENEWAL_ORDER_LOCK + orderId, () -> {

            OrderRenewalInformation orderRenewalInformation = new OrderRenewalInformation();
            BeanUtils.copyProperties(renewalInformationRequest,orderRenewalInformation);
            orderRenewalInformation.setId(BaseServiceUtils.getId());
            orderRenewalInformation.setCreateTime(new Date());
            orderRenewalInformationService.addOrderRenewalInformation(orderRenewalInformation);

            // 退订软件服务
            SoftServiceOpenParam softServiceOpenParam = new SoftServiceOpenParam();
            softServiceOpenParam.setOrderId(orderId);
            softServiceOpenParam.setOperateType("03");
            executor.execute(() -> softService.softServiceOpen(softServiceOpenParam));


            return iotAnswer;
        });
    }
}
