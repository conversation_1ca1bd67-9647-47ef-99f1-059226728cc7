package com.chinamobile.iot.sc.response.web;

import com.chinamobile.iot.sc.pojo.CardValueAddedInfo;
import com.chinamobile.iot.sc.pojo.entity.ServiceOpenInfo;
import com.chinamobile.iot.sc.pojo.vo.GetOrderResultVO;
import com.chinamobile.iot.sc.pojo.vo.OrderAttachmentVO;
import com.chinamobile.iot.sc.pojo.vo.SpecialAfterMarketResultVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/11/9 19:10
 * @Description:
 */
@Data
public class Order2CInfoDetailDTO {
    private String id;
    //收货人信息
    /**
     *  收货人姓名
     */
    private String contactPersonName;
    /**
     * 收货人电话
     */
    private String contactPhone;

    private String addr1;
    /**
     * 市
     */
    private String addr2;
    /**
     * 区
     */
    private String addr3;
    /**
     * 乡镇
     */
    private String addr4;
    /**
     * 收货人地址
     */
    private String usaddr;
    //订单信息
    /**
     * 订单ID
     */
    private String orderId;
    /**
     * 商品名称
     */
    private String skuOfferingName;
    /**
     * 商品编码
     */
    private String skuOfferingCode;
    /**
     * 商品类型
     */
    private String composition;
    /**
     * 商品类型，开放能力使用时保持为编码，OS前端详情是为名称
     */
    private String spuOfferingClass;

    /**
     * 商品类型-名称
     */
    private String spuOfferingClassName;
    /**
     * 原子商品名称
     */
    private String atomOfferingName;
    /**
     * 原子商品编码
     */
    private String atomOfferingCode;
    /**
     * 原子商品类型，开放能力使用时保持为编码，OS前端详情是为名称
     */
    private String offeringClass;

    /**
     * 原子商品类型-名称（开放能力使用）
     */
    private String offeringClassName;
    /**
     * 型号
     */
    private String model;
    /**
     * 颜色
     */
    private String color;
    /**
     * 订购数量 skuQuantity*atomQuantity
     */
    private Long quantity;
    /**
     * 订单创建时间
     */
    private String createTime;
    /**
     * 单价 厘
     */
    private String atomPrice;

    /**
     * sku单价 厘
     */
    private String skuPrice;
    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 订单状态变更时间
     */
    private Date orderStatusTime;
    /**
     * 订单类型  01-- 自主下单 00-- 代客下单
     */
    private String orderType;

    private String orderTypeDesc;
    /**
     * 合作伙伴名
     */
    private String partnerName;
    /**
     * 订单SN号
     */
    private String snList;

    /**
     * 操作员姓名
     */
    private String custMgName;

    /**
     * 操作员电话
     */
    private String custMgPhone;

    /**
     * 操作员省工号
     */
    private String employeeNum;

    /**
     * 操作员编码
     */
    private String createOperCode;

    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 客户编码
     */
    private String custCode;

    /**
     * 客户手机号
     */
    private String custId;

    /**
     * 提单人省名称
     */
    private String reserveProvinceName;

    /**
     * 提单人市名称
     */
    private String reserveCityName;

    /**
     * 客户类型名称
     */
    private String customerTypeName;

    /**
     * 为卡+X时的订购结果
     */
    private List<GetOrderResultVO> getOrderResultVOList;
    /**
     * 处理信息 历史
     */
    private List<OrderHandleHistory> historyList;
    //物流信息
    private List<LogisticsMsg> logisticsMsgs;

    /**
     * 原子商品信息
     */
    private List<OrderAtomDetail> orderAtomDetailList;

    /**
     * 结算信息
     */
    private List<SettleInfo> settleInfoList;

    /**
     * 发票信息
     */
    private List<InvoiceInfo> invoiceInfoList;

    /**
     * 商品组/销售商品编码
     */
    private String  spuOfferingCode;

    /**
     *商品组/销售商品名称
     */
    private String  spuOfferingName;
    /**
     * 合作伙伴联系人姓名
     */
    private String cooperatorName;

    /**
     * 订单同步备注
     */
    private String remarks;

    /**
     * 订单历史备注信息
     */
    private List<OrderRemarkHistory> orderRemarkHistory;

    /**
     * 特殊售后状态
     */
    private String specialAfterStatusStr;
    /**
     * 特殊售后退订数量
     */
    private String specialAfterRefundsNumber;

    /**
     * 特殊售后列表信息
     */
    private List<SpecialAfterMarketResultVO> specialAfterMarketResultVOList;

    /**
     * 异常处理信息
     */
    private ExHandleInfo exHandleInfo;

    /**
     * 千里眼服务开通状态
     */
    private Integer qlyStatus;

    /**
     * 千里眼服务开通状态
     */
    private Integer ysxStatus;

    /**
     * 图片外部链接路径
     */
    private String url;

    /**
     * 图片内部路径
     */
    private String imgUrl;

    /**
     * 平台软件编码
     */
    private String extSoftOfferingCode;

    /**云视讯服务开通状态*/
    private List<YsxServiceStatus> ysxServiceStatuses;

    /**
     * 行车卫士订购结果  0--开通失败  1--开通成功  2--退订失败 3--退订成功
     */
    private Integer carOpenStatus;

    /**
     * 销售商品版本号
     */
    private String spuOfferingVersion;

    /**
     * 规格商品版本号
     */
    private String skuOfferingVersion;

    /**
     * 原子商品版本号
     */
    private String atomOfferingVersion;
    /**
     * 软件服务状态
     */
    private Integer softServiceStatus;
    /**
     * 软件服务列表
     */
    private List<ServiceOpenInfo> softService;

    /**
     * 套餐类型
     */
    private String packageTypeName;

    /**
     * 组织级别	对应订单收入归属省公司组织机构级别
     * 1：集团
     * 2：省
     * 3：地市
     * 4：区县
     * 5：营业厅
     */
    private String orgLevel;

    /**
     * 全组织机构名称	全组织机构名称为拼接字段，按订单收入归属省公司组织机构的父组织机构拼接，最多取订单收入归属组织机构及以上共5层父组织机构，各级别间以“-”连接；
     */
    private String orgName;

    /**
     * 当ordertype=00且offeringClass=A11:卡+X硬件时，取省BOSS回传的省内集团客户上的“归属省内组织机构全称”
     */
    private String provinceOrgName;

    /**
     * 订购渠道来源
     * 商城自有渠道时，默认传019030-移动物联网商城；
     * 4A单点登录渠道时，默认传000000-其他；
     * 其他非4A单点登录渠道时，传运营管理员在渠道来源管理菜单中配置的渠道来源编码。
     */
    private String orderingChannelSource;

    /**
     * 订购渠道名称
     * 商城自有渠道时，默认传019030对应的“移动物联网商城”；
     * 4A单点登录渠道时，默认传000000对应的“其他”；
     * 其他非4A单点登录渠道时，传运营管理员在渠道来源管理菜单中配置的渠道来源编码对应的渠道来源名称。
     */
    private String orderingChannelName;

    /**
     * 接单时间
     */
    private String receiveOrderTime;

    /**
     * 发货时间
     */
    private String sendGoodsTime;

    /**
     * 个人客户省份(后缀是移动)
     */
    private String province;

    /**
     * 个人客户省份(后缀是移动)-名称
     */
    private String provinceCompany;

    /**
     * 个人客户所属归属地市编码
     */
    private String location;

    /**
     * 个人客户所属归属地市名称
     */
    private String cityName;

    /**
     * 个人客户所属归属区县
     */
    private String regionID;

    /**
     * 订单抵扣金额(单位厘，已加密)
     */
    private String orderDeductPrice;

    /**
     * 订单总金额（接口带来的，已加密）
     */
    private String orderTotalPrice;

    /**
     * 订单完成时间
     */
    private Date finishTime;

    /**
     * 订购数量（规格）
     */
    private Long skuQuantity;

    /**
     * 订单附件列表
     */
    private List<OrderAttachmentVO> attachmentList;

    /**
     * 客户经理编码
     */
    private String managerCode;

    /**
     * 主合作伙伴id
     */
    private String primaryCooperatorId;

    @Data
    public static class LogisticsMsg {
        /**
         * 物流单号
         */
        private String logisCode;
        /**
         * 物流服务商编码
         */
        private String supplierName;
        /**
         * 备注信息
         */
        private String description;

        private String orderAtomInfoId;

        /**
         * 物流服务商中文名稱
         */
        private String supplierCnName;

    }

    @Data
    public static class OrderHandleHistory {
        /**
         * 操作时间
         */
        private Date createTime;
        /**
         * 前端排序显示用(0 1 2 3 4)
         */
        private Integer innerStatus;
        /**
         * 内部状态描述
         */
        private String innerStatusDescribe;
        /**
         * 信息(前端显示)
         */
        private String message;

        /**
         * 处理人
         */
        private String operatorUserName;
    }

    /**
     * 订单备注信息
     */
    @Data
    public static class OrderRemarkHistory {

        /**
         * 订单内部状态
         */
        private String roleType;
        /**
         * 创建者
         */
        private String createUser;
        /**
         * 备注信息
         */
        private String orderRemark;

        private String orderId;

        /**
         * 操作时间
         */
        //@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT")
        private Date createTime;
    }

    @Data
    public static class YsxServiceStatus {
        /**
         * 手机号
         */
        private String phone;
        /**
         * 服务状态
         */
        private Integer status;
    }

    /**
     * 订单原子编码信息
     */
    @Data
    public static class OrderAtomDetail{
        /**
         * 原子商品名称
         */
        private String atomOfferingName;

        /**
         * 原子商品类型
         */
        private String atomOfferingClass;

        /**
         * 原子商品类型名称
         */
        private String atomOfferingClassName;

        /**
         * 数量
         */
        private Integer quantity;

        /**
         * 金额（元）
         */
        private String atomTotalPrice;

        /**
         * 原子商品编码
         */
        private String atomOfferingCode;

        /**
         * 平台软件编码
         */
        private String extSoftOfferingCode;
    }

    /**
     * 结算信息
     */
    @Data
    public static class SettleInfo{

        /**
         * 采购订单编号
         */
        private String scmOrderNum;

        /**
         * 计收状态
         */
        private String settleStatusName;

        /**
         * 结算状态
         */
        private String onlineSettleStatusName;

    }

    /**
     * 发票信息
     */
    @Data
    public static class InvoiceInfo{

        /**
         * 发票状态
         */
        private String invoiceStatusName;

        /**
         * 上传内部发票地址
         */
        private String voucherInnerUrl;

        /**
         * 上传外部发票地址
         */
        private String voucherOuterUrl;

    }

    @Data
    public static class ExHandleInfo{
        /**
         * 异常类型
         */
        private String expectTypeName;

        /**
         * 处理选项
         */
        private String handleTypeName;

        /**
         * 详细说明
         */
        private String reason;

        /**
         * 创建时间
         */
        private String createTimeStr;
    }


    /**
     * 卡增值服务包信息
     */
    private List<CardValueAddedInfo> cardValueAddedInfoList;
    @Data
    public static class SoftServiceInfo{
        /**
         * 手机号
         */
        private String softServicePhone;
        /**
         * 订购软件服务流水号
         */
        private String softServiceId;
        /**
         * 服务开通结果
         */
        private String softServiceOpenStatus;
        /**
         * 服务退订结果
         */
        private String softServiceRetailStatus;
        /**
         * 服务开通时间
         */
        private String softServiceOpenTime;
        /**
         * 服务退订时间
         */
        private String softServiceRetailTime;
        /**
         * 使用中时间
         */
        private String softServiceUseTime;
        /**
         * 备注
         */
        private String softServiceRemark;
    }

}
