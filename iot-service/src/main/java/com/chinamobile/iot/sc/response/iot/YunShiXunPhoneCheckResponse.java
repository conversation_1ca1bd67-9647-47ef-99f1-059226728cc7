package com.chinamobile.iot.sc.response.iot;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/23 15:23
 * @description 云视讯手机号检测结果
 */
@Data
public class YunShiXunPhoneCheckResponse implements Serializable {

    /**
     * 响应状态码，0响应成功，1响应失败
     */
    @JSONField(name = "res_code")
    private Integer resCode;
    /**
     * 响应消息，简单的文字说明
     */
    @JSONField(name = "res_desc")
    private String resDesc;
    /**
     * 响应时间戳，单位为ms
     */
    private Long timestamp;

    /**
     * 用户数组
     */
    private List<UserData> userData;
    @Data
    public static class UserData implements Serializable {
        private String mobile;
        private String status;
        private String resultDesc;
    }
}
