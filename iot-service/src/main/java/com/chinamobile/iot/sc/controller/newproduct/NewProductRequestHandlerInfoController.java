package com.chinamobile.iot.sc.controller.newproduct;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.pojo.NewProductRequestHandlerInfo;
import com.chinamobile.iot.sc.service.NewProductRequestHandlerInfoService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

import static com.chinamobile.iot.sc.constant.NewProductRequestFlowTypeConstant.ADD_NEW_PRODUCT;

/**
 * <AUTHOR> xie<PERSON><PERSON><PERSON>
 * @date : 2022/9/20 18:05
 * @description: 流程处理控制类
 **/
@RestController
@RequestMapping("/osweb/handler")
public class NewProductRequestHandlerInfoController {

    @Resource
    private NewProductRequestHandlerInfoService newProductRequestHandlerInfoService;

    /**
     * 根据来源信息主键id查询处理流程
     * @param sourceId
     * @return
     */
    @GetMapping("/handlerInfoList")
    public BaseAnswer<List<NewProductRequestHandlerInfo>> getProductHandlerInfoList(@RequestParam("sourceId") String sourceId){
        List<NewProductRequestHandlerInfo> listHandlerInfo = newProductRequestHandlerInfoService.getListHandlerInfo(sourceId, ADD_NEW_PRODUCT);
        return new BaseAnswer<List<NewProductRequestHandlerInfo>>().setData(listHandlerInfo);
    }
}
