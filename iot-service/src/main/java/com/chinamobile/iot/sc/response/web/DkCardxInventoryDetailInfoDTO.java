package com.chinamobile.iot.sc.response.web;

import lombok.Data;

@Data
public class DkCardxInventoryDetailInfoDTO {

    /**
     * 主键
     */
    private String atomId;

    /**
     * 卡+X终端库存主要信息表id
     */
    private String inventoryMainId;

    /**
     * 省地市名称
     *
     */
    private String provinceCityName;


    /**
     * 原子预占数
     *
     */
    private Integer atomInventory;

    /**
     * 预占数量
     *
     */
    private Integer reserveQuatity;

    /**
     * 当前库存数
     *
     */
    private Integer currentInventory;

    /**
     * 总库存数
     *
     */
    private Integer totalInventory;

    /**
     * 库存状态：0：短缺；1：充足
     *
     */
    private Integer inventoryStatus;

    /**
     * 库存预警值
     *
     */
    private Integer inventoryThreshold;

    /**
     * 设备型号
     */
    private String deviceVersion;
}
