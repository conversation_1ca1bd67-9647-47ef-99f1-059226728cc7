package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;

import java.util.List;

/**
 * created by l<PERSON><PERSON><PERSON> on 2024/7/16 17:47
 */
@Data
public class CardRalationCountByAreaVO {

    /**
     * 原子订单id
     */
    private String atomOrderId;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 设备型号
     */
    private String deviceVersion;

    /**
     * 是否含卡
     */
    private Boolean hasCard;

    /**
     * 仓库及应交付数量
     */
    private List<AreaCountItem> list;


    @Data
    public static class AreaCountItem{
        /**
         * 仓库名称，如 省级、广州市
         */
        private String areaName;

        private Long count;
    }


}
