package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 部门/应用主题内容配置
 *
 * <AUTHOR>
public class OpenAbilityThemeContentConfig implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    private String id;

    /**
     * 应用id，给应用配置内容时填写
     *
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    private String appId;

    /**
     * 部门id，给部门配置内容时填写
     *
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    private String organizationId;

    /**
     * 内容ID
     *
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    private String contentId;

    /**
     * 订阅数据接收地址
     *
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    private String webHook;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_content_config.id
     *
     * @return the value of supply_chain..open_ability_theme_content_config.id
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public OpenAbilityThemeContentConfig withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_content_config.id
     *
     * @param id the value for supply_chain..open_ability_theme_content_config.id
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_content_config.app_id
     *
     * @return the value of supply_chain..open_ability_theme_content_config.app_id
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public String getAppId() {
        return appId;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public OpenAbilityThemeContentConfig withAppId(String appId) {
        this.setAppId(appId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_content_config.app_id
     *
     * @param appId the value for supply_chain..open_ability_theme_content_config.app_id
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public void setAppId(String appId) {
        this.appId = appId;
    }

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_content_config.organization_id
     *
     * @return the value of supply_chain..open_ability_theme_content_config.organization_id
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public String getOrganizationId() {
        return organizationId;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public OpenAbilityThemeContentConfig withOrganizationId(String organizationId) {
        this.setOrganizationId(organizationId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_content_config.organization_id
     *
     * @param organizationId the value for supply_chain..open_ability_theme_content_config.organization_id
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_content_config.content_id
     *
     * @return the value of supply_chain..open_ability_theme_content_config.content_id
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public String getContentId() {
        return contentId;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public OpenAbilityThemeContentConfig withContentId(String contentId) {
        this.setContentId(contentId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_content_config.content_id
     *
     * @param contentId the value for supply_chain..open_ability_theme_content_config.content_id
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_content_config.web_hook
     *
     * @return the value of supply_chain..open_ability_theme_content_config.web_hook
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public String getWebHook() {
        return webHook;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public OpenAbilityThemeContentConfig withWebHook(String webHook) {
        this.setWebHook(webHook);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_content_config.web_hook
     *
     * @param webHook the value for supply_chain..open_ability_theme_content_config.web_hook
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public void setWebHook(String webHook) {
        this.webHook = webHook;
    }

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_content_config.create_time
     *
     * @return the value of supply_chain..open_ability_theme_content_config.create_time
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public OpenAbilityThemeContentConfig withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_content_config.create_time
     *
     * @param createTime the value for supply_chain..open_ability_theme_content_config.create_time
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..open_ability_theme_content_config.update_time
     *
     * @return the value of supply_chain..open_ability_theme_content_config.update_time
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public OpenAbilityThemeContentConfig withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..open_ability_theme_content_config.update_time
     *
     * @param updateTime the value for supply_chain..open_ability_theme_content_config.update_time
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", appId=").append(appId);
        sb.append(", organizationId=").append(organizationId);
        sb.append(", contentId=").append(contentId);
        sb.append(", webHook=").append(webHook);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OpenAbilityThemeContentConfig other = (OpenAbilityThemeContentConfig) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAppId() == null ? other.getAppId() == null : this.getAppId().equals(other.getAppId()))
            && (this.getOrganizationId() == null ? other.getOrganizationId() == null : this.getOrganizationId().equals(other.getOrganizationId()))
            && (this.getContentId() == null ? other.getContentId() == null : this.getContentId().equals(other.getContentId()))
            && (this.getWebHook() == null ? other.getWebHook() == null : this.getWebHook().equals(other.getWebHook()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAppId() == null) ? 0 : getAppId().hashCode());
        result = prime * result + ((getOrganizationId() == null) ? 0 : getOrganizationId().hashCode());
        result = prime * result + ((getContentId() == null) ? 0 : getContentId().hashCode());
        result = prime * result + ((getWebHook() == null) ? 0 : getWebHook().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Jun 11 16:21:01 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        appId("app_id", "appId", "VARCHAR", false),
        organizationId("organization_id", "organizationId", "VARCHAR", false),
        contentId("content_id", "contentId", "VARCHAR", false),
        webHook("web_hook", "webHook", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Tue Jun 11 16:21:01 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Jun 11 16:21:01 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Jun 11 16:21:01 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Jun 11 16:21:01 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Jun 11 16:21:01 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Jun 11 16:21:01 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Jun 11 16:21:01 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Jun 11 16:21:01 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Jun 11 16:21:01 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Jun 11 16:21:01 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Jun 11 16:21:01 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Jun 11 16:21:01 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Jun 11 16:21:01 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Jun 11 16:21:01 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Jun 11 16:21:01 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Jun 11 16:21:01 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Jun 11 16:21:01 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}