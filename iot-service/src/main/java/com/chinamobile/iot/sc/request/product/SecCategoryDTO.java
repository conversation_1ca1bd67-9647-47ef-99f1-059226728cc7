package com.chinamobile.iot.sc.request.product;

import lombok.Data;

import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/11/2 10:17
 * @Description: 二级销售目录
 */
@Data
@Deprecated
public class SecCategoryDTO {
    /**
     * 二级销售目录（商品子类目）名称
     */
    private String secCateName;
    /**
     * 三级销售目录
     */
    private String thdCateName;
    /**
     * 操作类型
     * A：新增
     */
    private String operType;
    /**
     * 在仅刷新销售商品基本信息时本节点非必填，其他场景本节点必填
     */
    private List<SkuOfferingInfoDTO> skuOfferingInfo;
}
