package com.chinamobile.iot.sc.controller.iot;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.request.supplychain.SupplyChainImportPoDraftIOTRequest;
import com.chinamobile.iot.sc.request.sync.SyncCommonRequest;
import com.chinamobile.iot.sc.service.SupplyChainSyncService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/os/scm/sync")
public class SupplyChainSyncController {
    @Resource
    private SupplyChainSyncService supplyChainSyncService;

    /**
     * 同步订单报账材料
     * @param syncCommonRequest
     * @return
     */

    @PostMapping("/claimMaterials")
    public BaseAnswer<Void> claimMaterials(@RequestBody SyncCommonRequest syncCommonRequest){
        return supplyChainSyncService.claimMaterials(syncCommonRequest);

    }

    /**
     * 同步报账支付进度
     * @param syncCommonRequest
     * @return
     */

    @PostMapping("/paymentStatus")
    public BaseAnswer<Void> paymentStatus(@RequestBody SyncCommonRequest syncCommonRequest){
        return supplyChainSyncService.paymentStatus(syncCommonRequest);

    }

    /**
     * 同步订单报账材料
     * @param request
     * @return
     */

    @PostMapping("/importPoDraft")
    public BaseAnswer<Void> importPoDraft(@RequestBody SupplyChainImportPoDraftIOTRequest request){
        return supplyChainSyncService.importPoDraft(request);

    }

    /**
     * 返回报账材料下载地址
     * @param
     * @return
     */

    @GetMapping ("/getMaterial")
    public BaseAnswer<String> getMaterial(@RequestParam(value = "contractId", required = true) String contractId,
                                          @RequestParam(value = "segment1", required = true) String segment1
                                          ){
        return supplyChainSyncService.getMaterial(contractId, segment1);

    }

    /**
     * 同步订单给市场
     * @param OrderId
     * @return
     */

    @GetMapping("/test/syncToMarket")
    public BaseAnswer<Void> syncOrderToMarketSystem(@RequestParam(value = "orderId", required = true) List<String> OrderId){
        return supplyChainSyncService.syncOrderToMarketSystem(OrderId);
    }

    /**
     * 同步订单给市场B2C
     * @param orderId
     * @return
     */

    @GetMapping("/test/syncToMarketB2B")
    public BaseAnswer<Void> syncOrderToMarketSystemB2B(@RequestParam(value = "orderId", required = true) String orderId){
        return supplyChainSyncService.syncOrderToMarketSystemB2B(orderId);
    }

    /**
     * 市场同步订单状态过来
     * @param syncCommonRequest
     * @return
     */

    @PostMapping("/order/status")
    public BaseAnswer<Void> orderStatusFeedback(@RequestBody SyncCommonRequest syncCommonRequest){
        return supplyChainSyncService.orderStatusFeedback(syncCommonRequest);
    }

    /**
     * 批量同步订单给市场
     * @param excel
     * @return
     */

    @PostMapping("/cutOver/syncToMarket")
    public BaseAnswer<Void> syncOrderToMarketSystemCutOver(@RequestPart("file") MultipartFile excel){
        return supplyChainSyncService.syncOrderToMarketSystemCutOver(excel);
    }

    /**
     * 同步上月未同步订单给市场
     * @return
     */

    @GetMapping("/test/syncToMarket/lastMonth")
    public void syncOrderToMarketSystemLastMonth(){
        supplyChainSyncService.syncOrderToMarketSystemLastMonth();
    }

    /**
     * 批量清空同步给市场的订单
     * @param excel
     * @return
     */

    @PostMapping("/cutOver/syncToMarket/clear")
    public BaseAnswer<Void> syncOrderToMarketSystemCutOverClear(@RequestPart("file") MultipartFile excel) {
        return supplyChainSyncService.syncOrderToMarketSystemCutOverClear(excel);
    }
    /**
     * 同步订单给市场B2B批量
     * @param orderIds
     * @return
     */

    @PostMapping("/test/syncToMarketB2B/multi")
    public BaseAnswer<Void> syncOrderToMarketSystemB2BMulti(@RequestBody @Valid List<String> orderIds){
        return supplyChainSyncService.syncOrderToMarketSystemB2BMulti(orderIds);
    }
}
