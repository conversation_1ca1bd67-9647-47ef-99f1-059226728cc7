package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.pojo.param.IOPUploadParam;

import com.chinamobile.iot.sc.service.BRMService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;


/**
 * BRM数据管理控制器
 */
@RestController
@RequestMapping("/osweb/brm")
@Slf4j
public class BRMController {

    @Autowired
    private BRMService brmService;

    /**
     * 导出BRM数据
     * 
     * @param param 导出参数
     * @return 文件下载
     */
    @PostMapping("/export")
    public ResponseEntity<Resource> exportBRMData() {
        try {
            String filePath = brmService.exportBRMData();
            File file = new File(filePath);

            if (!file.exists()) {
                return ResponseEntity.notFound().build();
            }

            Resource resource = new FileSystemResource(file);

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + file.getName() + "\"")
                    .body(resource);
        } catch (Exception e) {
            log.error("BRM数据导出失败", e);
            return ResponseEntity.badRequest().build();
        }
    }

}