package com.chinamobile.iot.sc.response.web;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.chinamobile.iot.sc.pojo.Order2cInfo;
import lombok.Data;

import java.util.Date;

/**
 * created by l<PERSON><PERSON><PERSON> on 2022/3/30 16:02
 */
@Data
public class OrderExportDTO {

    @Excel(name = "操作员编码")
    /**
     * 操作员编码
     */
    private String createOperCode;

    @Excel(name = "操作员省工号")
    /**
     * 操作员省工号
     */
    private String employeeNum;

    /**
     * 操作员姓名
     */
    @Excel(name = "操作员姓名")
    private String custMgName;

    /**
     * 操作员电话
     */
    @Excel(name = "操作员电话")
    private String custMgPhone;


    @Excel(name = "客户编码")
    /**
     * 客户编码
     */
    private String custCode;


    @Excel(name = "客户名称")
    /**
     * 客户名称
     */
    private String custName;

    @Excel(name = "个人客户省份")
    /**
     * 个人客户省份
     */
    private String province;

    @Excel(name = "个人客户所属归属地市")
    /**
     * 个人客户所属归属地市编码
     */
    private String location;


    @Excel(name = "个人客户所属归属区县")
    /**
     * 个人客户所属归属区县
     */
    private String regionID;

    /**
     * 订单收入归属省;
     */
    @Excel(name = "订单收入归属省")
    private String orgProvince;

    /**
     * 订单收入归地市;
     */
    @Excel(name = "订单收入归地市")
    private String orgLocation;

    /**
     * 订单收入归区县;
     */
    @Excel(name = "订单收入归区县")
    private String orgRegion;

    /**
     * 订单收入归属营业厅;
     */
//    @Excel(name = "订单收入归属营业厅")
//    private String orgHall;


    /**
     * 下单时间
     */
    @Excel(name = "下单时间")
    private String createTime;

    /**
     * 订单号
     */
    @Excel(name = "订单号")
    private String orderId;
    /**
     * 业务编码
     */
    @Excel(name = "业务编码")
    private String businessCode;

    /**
     * 订单状态描述
     */
    @Excel(name = "订单状态")
    private String orderStatusDescribe;

    /**
     * 订单类型
     */
    @Excel(name = "订单类型")
    private String orderType;

    /**
     * 订单完成时间
     */
    @Excel(name = "订单完成时间")
    private String finishTime;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remarks;

    /**
     * 订单总金额
     */
    @Excel(name = "订单总金额")
    private Double orderTotalPrice;

    /**
     * 订单抵扣金额(单位元)
     */
    @Excel(name = "订单抵扣金额/元")
    private Double orderDeductPrice;

    /**
     * 关联领货码
     */
    @Excel(name = "关联领货码")
    private String couponInfo;

    /**
     * 收货地区(省份)
     */
    @Excel(name = "收货地区")
    private String deliveryArea;

    @Excel(name = "收货人姓名")
    /**
     * 收货人姓名
     */
    private String contactPersonName;

    @Excel(name = "收货人地址")
    /**
     * 收货人省（加密）
     */
    private String address;

    /**
     * 收货人电话
     */
    @Excel(name = "收货人电话")
    private String receiverPhone;


    @Excel(name = "物流单号")
    /**
     * 物流单号，多个逗号分隔
     */
    private String logisCode;

    @Excel(name = "设备sn")
    /**
     * 设备sn，多个逗号分隔
     */
    private String sn;

    /**
     * 商品组/销售商品名称
     */
    @Excel(name = "商品组/销售商品名称")
    private String spuOfferingName;

    /**
     * 商品组/销售商品编码
     */
    @Excel(name = "商品组/销售商品编码")
    private String spuOfferingCode;

    /**
     * SPU一级销售目录
     */
    @Excel(name = "商品类型")
    private String spuOfferingClass;
    /**
     * 商品名称(规格)
     */
    @Excel(name = "商品名称(规格)")
    private String skuOfferingName;

    /**
     * 商品规格编码
     */
    @Excel(name = "商品编码（规格）")
    private String skuOfferingCode;

    /**
     * 订购数量（规格）
     */
    @Excel(name = "订购数量（规格）")
    private Long skuQuantity;

    /**
     * 建议零售价（规格）
     */
//    @Excel(name = "建议零售价（规格）")
//    private Double recommendPrice;

    /**
     * 销售目录价（规格）
     */
    @Excel(name = "销售目录价（规格）")
    private Double price;

    /**
     * 营销案名称
     */
//    @Excel(name = "营销案名称")
//    private String marketName;

    /**
     * 营销案编码
     */
//    @Excel(name = "营销案编码")
//    private String marketCode;

    /**
     * 原子商品名称
     */
    @Excel(name = "原子商品名称")
    private String atomOfferingName;
    /**
     * 原子商品编码
     */
    @Excel(name = "原子商品编码")
    private String atomOfferingCode;
    /**
     * 原子商品类型
     */
    @Excel(name = "原子商品类型")
    private String atomOfferingClass;
    /**
     * 型号
     */
//    @Excel(name = "型号")
//    private String model;


//    @Excel(name = "原子商品计量单位")
//    /**
//     * 计量单位
//     */
//    private String unit;

    /**
     * 颜色
     */
//    @Excel(name = "颜色")
//    private String color;

    /**
     * 数量（原子商品） skuQuantity*atomQuantity
     */
    @Excel(name = "数量（原子商品）")
    private Integer quantity;

    /**
     * 销售目录价（原子商品）
     */
//    @Excel(name = "销售目录价（原子商品）")
//    private Double atomPrice;

    /**
     * 销售金额（原子商品）
     */
    @Excel(name = "销售金额（原子商品）")
    private Double atomTotalPrice;

    @Excel(name = "结算单价（原子商品）")
    /**
     * 结算单价（原子商品）
     */
    private Double settlePrice;

    @Excel(name = "结算金额（原子商品）")
    /**
     * 结算金额（原子商品）
     */
    private Double totalSettlePrice;

    /**
     * 原子订单抵扣金额(单位厘，已加密)
     */
//    @Excel(name = "原子订单抵扣金额/元")
//    private Double atomDeductPrice;

    /**
     * 合作伙伴名
     */
    @Excel(name = "合作伙伴名称")
    private String partnerName;
    /**
     * 合作伙伴姓名
     */
    @Excel(name = "合作伙伴联系人")
    private String cooperatorName;

}
