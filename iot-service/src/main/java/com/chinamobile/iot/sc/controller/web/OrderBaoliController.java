package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.BaoliOrderVO;
import com.chinamobile.iot.sc.pojo.vo.OrderBaoliVO;
import com.chinamobile.iot.sc.service.OrderBaoliService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/13
 * @description 保理订单controller接口类
 */
@RestController
@RequestMapping(value = "/osweb/orderBaoli")
public class OrderBaoliController {

    @Resource
    private OrderBaoliService orderBaoliService;

    /**
     * 分页获取保理订单标记列表
     * @param baoliOrderParam
     * @return
     */
    @GetMapping(value="/pageBaoliOrder")
    public BaseAnswer<PageData<BaoliOrderVO>> pageBaoliOrder(BaoliOrderParam baoliOrderParam,
                                                             @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer baseAnswer = new BaseAnswer();
        PageData<BaoliOrderVO> pageData = orderBaoliService.pageBaoliOrder(baoliOrderParam,loginIfo4Redis);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }

    /**
     * 获取导出保理不可用订单
     * @param baoliOrderExportParam
     * @param response
     * @throws IOException
     */
    @GetMapping(value = "/exportBaoliOrderCanNotUse")
    public void exportBaoliOrderCanNotUse(BaoliOrderExportParam baoliOrderExportParam,
                                          HttpServletResponse response) throws IOException{
        orderBaoliService.exportBaoliOrderCanNotUse(baoliOrderExportParam,response);
    }

    /**
     * 更新保理订单的状态为可用
     * @param orderBaoliStatusChangeParam
     * @return
     */
    @PostMapping(value = "/changeBaoliOrderStatus")
    public BaseAnswer changeBaoliOrderStatus(@Valid @RequestBody OrderBaoliStatusChangeParam orderBaoliStatusChangeParam,LoginIfo4Redis loginIfo4Redis){
        BaseAnswer baseAnswer = new BaseAnswer();
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        orderBaoliService.changeBaoliOrderStatus(orderBaoliStatusChangeParam,loginIfo4Redis.getUserId(), ip);
        return baseAnswer;
    }

    /**
     * 导入保理不可用订单
     * @param file
     * @return
     */
    @PostMapping("/importBaoliOrder")
    public void importBaoliOrder(MultipartFile file,
                                   HttpServletRequest request,
                                   HttpServletResponse response) throws Exception {
        orderBaoliService.importBaoliOrderCanNotUse(file,request,response);
    }

    /**
     * 分页查询保理订单
     * @param orderBaoliParam
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping(value="/pageOrderBaoli")
    public BaseAnswer<PageData<OrderBaoliVO>> pageOrderBaoli(OrderBaoliParam orderBaoliParam,
                                                             @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer baseAnswer = new BaseAnswer();
        PageData<OrderBaoliVO> pageData = orderBaoliService.pageOrderBaoli(orderBaoliParam,loginIfo4Redis);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }

    /**
     * 生成贸易订单
     * @param generateTradeOrderInfoParam
     * @return
     */
    @PostMapping(value = "/generateTradeOrder")
    public BaseAnswer generateTradeOrderInfo(@Valid @RequestBody GenerateTradeOrderInfoParam generateTradeOrderInfoParam,
                                             @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        BaseAnswer baseAnswer = new BaseAnswer();
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        orderBaoliService.generateTradeOrderInfo(generateTradeOrderInfoParam,loginIfo4Redis,true,ip);
        return baseAnswer;
    }

    /**
     * 导出待保理订单excel,包括未申请和已撤销状态(返回二进制流)
     */
    @GetMapping("/exportBaoliOrder")
    public void exportBaoliOrder(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        orderBaoliService.exportBaoliOrder(loginIfo4Redis);
    }

    /**
     * 批量导入订单，生成贸易订单
     */
    @PostMapping("/batchGenerateTradeOrder")
    public void batchGenerateTradeOrder(MultipartFile file,
                                        @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        orderBaoliService.batchGenerateTradeOrder(file,loginIfo4Redis,ip);
    }


}
