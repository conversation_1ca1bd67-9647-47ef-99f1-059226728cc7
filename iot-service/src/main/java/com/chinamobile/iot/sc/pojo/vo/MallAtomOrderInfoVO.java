package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 主订单信息
 */
@Data
public class MallAtomOrderInfoVO {
    /**
     * 商品名称（规格）
     */
    private String skuName;

    /**
     * 商品编码（规格）
     */
    private String skuCode;

    /**
     * 原子商品名称
     */
    private String atomName;

    /**
     * 原子商品类型
     */
    private String atomOfferingClass;

    /**
     * 型号
     */
    private String model;

    /**
     * 订购数量
     */
    private Long quantity;

    /**
     * 合作伙伴
     */
    private String cooperatorName;

    /**
     * 规格商品版本号
     */
    private String skuOfferingVersion;

    /**
     * 原子商品版本号
     */
    private String atomOfferingVersion;


}
