package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;

/**
 * @AUTHOR: HWF
 * @DATE: 2023/2/13
 */
@Data
public class ProductMaterialDetailItemVO {

    /**
     * 商品绑定表id
     */
    private String kpmId;

    /**
     * 物料编码
     */
    private String materialNum;

    /**
     * 物料数量（2位小数）
     */
    private String materialCount;

    /**
     * 物料结算单价
     */
    private String settlePrice;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料部门
     */
    private String materialDept;

    /**
     * 合同编号
     */
    private String contractNum;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 相对方名称(合作伙伴名称)
     */
    private String buyerName;

    /**
     * 合同含税总额
     */
    private String contractPrice;

    /**
     * 合同失效日期
     */
    private String expiredDate;

    /**
     * 合同在本系统状态
     */
    private String contractEffective;

    /**
     * 是否已绑定 0：未绑定，1：已绑定
     */
    private String binded;

    /**
     * 合同类型1--销售合同 2--采购合同
     */
    private Integer contractType;


    private String contractTypeName;

    /**
     * 承建方名称(移动省专公司名称)
     */
    private String sellerName;

    /**
     * 结算单价(厘)
     */
    private Long materialSettlePrice;

    /**
     * 服务包id
     */
    private String servicePackId;

    /**
     * 服务包名称
     */
    private String servicePackName;

    /**
     * 服务包已使用额度
     */
    private Long serviceQuotaUsed;

    /**
     * 服务包剩余额度
     */
    private Long serviceQuotaRemain;

    /**
     * 物料已使用额度
     */
    private Long materialQuotaUsed;

    /**
     * 物料剩余额度
     */
    private Long materialQuotaRemain;

    /**
     * 销项税率
     */
    private String labelTax;



}
