package com.chinamobile.iot.sc.controller.iot;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.entity.iot.CreateOperateRecordParam;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.entity.iot.ModuleInfoDto;
import com.chinamobile.iot.sc.pojo.dto.OperateRecordDTO;
import com.chinamobile.iot.sc.pojo.param.OperateRecordQueryParam;
import com.chinamobile.iot.sc.service.IOperateRecordService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/07
 * @description 日志管理
 */
@RestController
@RequestMapping("/os/log")
public class OperateRecordController {

    @Resource
    private IOperateRecordService operateRecordService;

    @GetMapping("/list")
    public BaseAnswer<PageData<OperateRecordDTO>> getLogList(OperateRecordQueryParam param) {
        return operateRecordService.getLogList(param);
    }

    @PostMapping ("/create")
    public BaseAnswer<Void> createLog(@RequestBody @Valid CreateOperateRecordParam param) {
        operateRecordService.createLog(param);
        return BaseAnswer.success(null);
    }

    @GetMapping("/moduleInfo")
    public BaseAnswer<List<ModuleInfoDto>> getModuleInfo() {
        return BaseAnswer.success(ModuleEnum.getAllModules());
    }

}
