package com.chinamobile.iot.sc.controller.iot;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.service.ShopUserOperationService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> xie<PERSON><PERSON><PERSON>
 * @date : 2024/7/5 15:42
 * @description:
 **/
@RestController
@RequestMapping("/os/shopUser")
public class ShopUserOperationController {


    @Resource
    private ShopUserOperationService shopUserOperationService;


    /**
     * test同步商城客户经理接口
     *
     * @return
     */
    @PostMapping("/sftpManagerInfoBack")
    public BaseAnswer testSftpOperationAccountManagerDataBack(@RequestParam("fileNameParam") String fileNameParam) {
        shopUserOperationService.sftpOperationAccountManagerDataBack(fileNameParam);
        return new BaseAnswer();
    }

    /**
     * test同步商城分销及注册用户接口
     *
     * @return
     */
    @PostMapping("/sftpCustomerInfoBack")
    public BaseAnswer testSftpOperationCustomerDataBack(@RequestParam("fileNameParam") String fileNameParam) {
        shopUserOperationService.sftpOperationCustomerDataBack(fileNameParam);
        return new BaseAnswer();
    }
}
