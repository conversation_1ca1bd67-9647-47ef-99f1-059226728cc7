package com.chinamobile.iot.sc.pojo.dto.gio;

import com.chinamobile.iot.sc.pojo.CategoryInfo;
import com.chinamobile.iot.sc.pojo.SkuReleaseTarget;
import com.chinamobile.iot.sc.pojo.SkuRoleRelation;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class GioSkuOriginDTO {


        private String id;
        private String offeringCode;
        private String spuId;
        private String offeringName;
        private long price;
        private String offeringStatus;
        private String spuOfferingVersion;
        private List<SkuReleaseTarget> skuReleaseTargetList;
        private String spuOfferingName;
        private String spuOfferingCode;
        private Date spuCreateTime;
        private String spuOfferingStatus;
//        private  List<CategoryInfo> categoryInfoList;
        private String spuOfferingClass;
        private String supplierName;
//        private  List<SkuRoleRelation> skuRoleRelationList;
        private Double pointPercent;
        private String deleteTime;

        // 新增字段：导航目录
        private String firstLevelNavCatalog;
        private String secondLevelNavCatalog;
        private String thirdLevelNavCatalog;

        // 新增字段：商品关键字
        private String productKeyword;

        // 新增字段：销售标签
        private String mainSaleTag;
        private String subSaleTag;


}
