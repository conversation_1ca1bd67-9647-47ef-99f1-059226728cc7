package com.chinamobile.iot.sc.response.web.invoice;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @package: com.chinamobile.iot.sc.request.invoice
 * @ClassName: Data4ApplyInvoiceRec
 * @description: 发票申请记录-展示对象
 * @author: zyj
 * @create: 2021/12/22 18:07
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@Data
@Accessors(chain = true)
public class Data4ApplyInvoiceRec {
    private String id;

    private String atomOrderId;

    private String orderId;

    private String orderSeq;
    //客户编码
    private String custCode;
    //省代码
    private String beId;

    private String printDate;

    private String frank;

    @JsonProperty("pName")
    public String getpName() {
        return pName;
    }

    private String pName;

    private String identifyNum;

    private String addressInfo;

    private String phoneNumber;

    private String bankName;

    private String bankId;

    private Long orderPrice;

    private Integer status;

    private String remark;

    private String cooperatorId;

    private String finishCooperatorId;

    private String finishCooperatorName;

    private String partnerName;

    private Date createTime;

    private Date updateTime;
    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 原子商品名称
     */
    private String atomOfferingName;
    /**
     * 原子商品类型
     */
    private String offeringClass;
    /**
     * 型号
     */
    private String model;
    /**
     * 订购数量 skuQuantity*atomQuantity
     */
    private Long quantity;

    /**
     * 合作伙伴联系人姓名
     */
    private String cooperatorName;


    /**
     * 催单次数
     */
    private Integer reminderCount;

    private String spuClassOfferingName;

}
