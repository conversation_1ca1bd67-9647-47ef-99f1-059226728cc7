package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.config.RestTemplateConfig;
import com.chinamobile.iot.sc.entity.iot.SearchMallLinkRequest;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.dto.SSOBaseHeaderDTO;
import com.chinamobile.iot.sc.pojo.param.LoginStatusQueryParam;
import com.chinamobile.iot.sc.pojo.vo.LoginStatusQueryBodyVO;
import com.chinamobile.iot.sc.pojo.vo.LoginStatusQueryVO;
import com.chinamobile.iot.sc.request.sso.SsoLoginRequest;
import com.chinamobile.iot.sc.response.iot.SsoLoginResponse;
import com.chinamobile.iot.sc.service.SsoService;
import com.chinamobile.iot.sc.util.IOTRequestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/26 15:25
 * @description 商城单点登录服务实现类
 */
@Slf4j
@Service
public class SsoServiceImpl implements SsoService {

    @Value("${sso.loginUrl}")
    private String ssoLoginUrl;

    @Value("${iot.loginstatusqueryUrl}")
    private String loginstatusqueryUrl;

    @Value("${iot.secretKey}")
    private String secretKey;


    /**
     * 商城免认证登录接口
     * @param request
     * @return
     */
    @Override
    public IOTAnswer<SsoLoginResponse> authenticationExemptLogin(IOTRequest request) {
        log.debug("IoT商城单点登录信息, data:{}", JSON.toJSONString(request));
        SsoLoginRequest ssoLoginRequest = JSON.parseObject(request.getContent(), SsoLoginRequest.class);

        HttpHeaders headers = new HttpHeaders();
        headers.add("content-type", "application/json;charset=utf-8");
        HttpEntity<SsoLoginRequest> requestEntity = new HttpEntity<>(ssoLoginRequest, headers);
        try {
            RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
            ResponseEntity<SsoLoginResponse> response = restTemplateHttps.postForEntity(ssoLoginUrl, requestEntity, SsoLoginResponse.class);
            SsoLoginResponse ssoLoginResponse = response.getBody();
            log.error("免认证登录接口返回:{}",JSON.toJSONString(ssoLoginResponse));
            IOTAnswer<SsoLoginResponse> iotAnswer = new IOTAnswer<>();
            iotAnswer.setContent(ssoLoginResponse);
            return iotAnswer;
        } catch (Exception e) {
            log.error("单点登录失败:" + e);
            throw new BusinessException(StatusConstant.SSO_LOGIN_FAILED);
        }
    }

    @Override
    public LoginStatusQueryVO loginStatusQuery(LoginStatusQueryParam param) {
        log.info("loginStatusQuery请求参数:{}", JSON.toJSONString(param));
        LoginStatusQueryVO vo = new LoginStatusQueryVO();
        vo.setHeader(param.getHeader());
        LoginStatusQueryBodyVO bodyVO = new LoginStatusQueryBodyVO();
        //转发请求至iot商城
        HttpHeaders headers = new HttpHeaders();
        headers.add("content-type", "application/json;charset=utf-8");
        //beId -- 002 代表物联网公司
        String iotRequest = IOTRequestUtils.getIotRequest(JSON.toJSONString(param), secretKey, "002", null);
        log.info("请求IOT商城内容为:" + iotRequest);
        HttpEntity<String> requestEntity = new HttpEntity<>(iotRequest, headers);
        ResponseEntity<IOTAnswer> response;
        try {
            RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
            response = restTemplateHttps.postForEntity(loginstatusqueryUrl, requestEntity, IOTAnswer.class);
        } catch (Exception e) {
            log.error("商城业务平台登录状态查询接口{}异常捕获:", loginstatusqueryUrl, e);
            //移动认证方，只关心成功响应的报文。出错就视为未认证
            bodyVO.setIsauthen("0");
            vo.setBody(bodyVO);
            return vo;
        }
        IOTAnswer iotAnswer = response.getBody();
        if (iotAnswer == null || !"0".equals(iotAnswer.getResultCode())) {
            //没返回信息则为提示同步失败，返回错误码则提示返回错误描述
            log.error("请求IOT商城业务平台登录状态查询接口失败，请求地址为:{}，返回结果为:{}", loginstatusqueryUrl, iotAnswer);
            bodyVO.setIsauthen("0");
            vo.setBody(bodyVO);
            return vo;
        }
        log.info("请求IOT商城结果返回为:{}", iotAnswer);
        Object content = iotAnswer.getContent();
        if(content != null){
            LinkedHashMap data = (LinkedHashMap) content;
            vo = JSONObject.parseObject(JSON.toJSONString(data), LoginStatusQueryVO.class);
        }
        return vo;
    }

}
