package com.chinamobile.iot.sc.pojo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class K3syncStatisProcityExample {
    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcityExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcityExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcityExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public static Criteria newAndCreateCriteria() {
        K3syncStatisProcityExample example = new K3syncStatisProcityExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcityExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public K3syncStatisProcityExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andContractNumIsNull() {
            addCriterion("contract_num is null");
            return (Criteria) this;
        }

        public Criteria andContractNumIsNotNull() {
            addCriterion("contract_num is not null");
            return (Criteria) this;
        }

        public Criteria andContractNumEqualTo(String value) {
            addCriterion("contract_num =", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumNotEqualTo(String value) {
            addCriterion("contract_num <>", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumGreaterThan(String value) {
            addCriterion("contract_num >", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumGreaterThanOrEqualTo(String value) {
            addCriterion("contract_num >=", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumLessThan(String value) {
            addCriterion("contract_num <", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumLessThanOrEqualTo(String value) {
            addCriterion("contract_num <=", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumLike(String value) {
            addCriterion("contract_num like", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumNotLike(String value) {
            addCriterion("contract_num not like", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumIn(List<String> values) {
            addCriterion("contract_num in", values, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumNotIn(List<String> values) {
            addCriterion("contract_num not in", values, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumBetween(String value1, String value2) {
            addCriterion("contract_num between", value1, value2, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumNotBetween(String value1, String value2) {
            addCriterion("contract_num not between", value1, value2, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNameIsNull() {
            addCriterion("contract_name is null");
            return (Criteria) this;
        }

        public Criteria andContractNameIsNotNull() {
            addCriterion("contract_name is not null");
            return (Criteria) this;
        }

        public Criteria andContractNameEqualTo(String value) {
            addCriterion("contract_name =", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNameNotEqualTo(String value) {
            addCriterion("contract_name <>", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNameGreaterThan(String value) {
            addCriterion("contract_name >", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNameGreaterThanOrEqualTo(String value) {
            addCriterion("contract_name >=", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNameLessThan(String value) {
            addCriterion("contract_name <", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNameLessThanOrEqualTo(String value) {
            addCriterion("contract_name <=", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNameLike(String value) {
            addCriterion("contract_name like", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotLike(String value) {
            addCriterion("contract_name not like", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameIn(List<String> values) {
            addCriterion("contract_name in", values, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotIn(List<String> values) {
            addCriterion("contract_name not in", values, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameBetween(String value1, String value2) {
            addCriterion("contract_name between", value1, value2, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotBetween(String value1, String value2) {
            addCriterion("contract_name not between", value1, value2, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractDeptIsNull() {
            addCriterion("contract_dept is null");
            return (Criteria) this;
        }

        public Criteria andContractDeptIsNotNull() {
            addCriterion("contract_dept is not null");
            return (Criteria) this;
        }

        public Criteria andContractDeptEqualTo(String value) {
            addCriterion("contract_dept =", value, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_dept = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractDeptNotEqualTo(String value) {
            addCriterion("contract_dept <>", value, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_dept <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractDeptGreaterThan(String value) {
            addCriterion("contract_dept >", value, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_dept > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractDeptGreaterThanOrEqualTo(String value) {
            addCriterion("contract_dept >=", value, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_dept >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractDeptLessThan(String value) {
            addCriterion("contract_dept <", value, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_dept < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractDeptLessThanOrEqualTo(String value) {
            addCriterion("contract_dept <=", value, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_dept <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractDeptLike(String value) {
            addCriterion("contract_dept like", value, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptNotLike(String value) {
            addCriterion("contract_dept not like", value, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptIn(List<String> values) {
            addCriterion("contract_dept in", values, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptNotIn(List<String> values) {
            addCriterion("contract_dept not in", values, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptBetween(String value1, String value2) {
            addCriterion("contract_dept between", value1, value2, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptNotBetween(String value1, String value2) {
            addCriterion("contract_dept not between", value1, value2, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeIsNull() {
            addCriterion("contract_statis_type is null");
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeIsNotNull() {
            addCriterion("contract_statis_type is not null");
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeEqualTo(String value) {
            addCriterion("contract_statis_type =", value, "contractStatisType");
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_statis_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeNotEqualTo(String value) {
            addCriterion("contract_statis_type <>", value, "contractStatisType");
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_statis_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeGreaterThan(String value) {
            addCriterion("contract_statis_type >", value, "contractStatisType");
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_statis_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeGreaterThanOrEqualTo(String value) {
            addCriterion("contract_statis_type >=", value, "contractStatisType");
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_statis_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeLessThan(String value) {
            addCriterion("contract_statis_type <", value, "contractStatisType");
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_statis_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeLessThanOrEqualTo(String value) {
            addCriterion("contract_statis_type <=", value, "contractStatisType");
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_statis_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeLike(String value) {
            addCriterion("contract_statis_type like", value, "contractStatisType");
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeNotLike(String value) {
            addCriterion("contract_statis_type not like", value, "contractStatisType");
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeIn(List<String> values) {
            addCriterion("contract_statis_type in", values, "contractStatisType");
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeNotIn(List<String> values) {
            addCriterion("contract_statis_type not in", values, "contractStatisType");
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeBetween(String value1, String value2) {
            addCriterion("contract_statis_type between", value1, value2, "contractStatisType");
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeNotBetween(String value1, String value2) {
            addCriterion("contract_statis_type not between", value1, value2, "contractStatisType");
            return (Criteria) this;
        }

        public Criteria andContractSellerIsNull() {
            addCriterion("contract_seller is null");
            return (Criteria) this;
        }

        public Criteria andContractSellerIsNotNull() {
            addCriterion("contract_seller is not null");
            return (Criteria) this;
        }

        public Criteria andContractSellerEqualTo(String value) {
            addCriterion("contract_seller =", value, "contractSeller");
            return (Criteria) this;
        }

        public Criteria andContractSellerEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_seller = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSellerNotEqualTo(String value) {
            addCriterion("contract_seller <>", value, "contractSeller");
            return (Criteria) this;
        }

        public Criteria andContractSellerNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_seller <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSellerGreaterThan(String value) {
            addCriterion("contract_seller >", value, "contractSeller");
            return (Criteria) this;
        }

        public Criteria andContractSellerGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_seller > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSellerGreaterThanOrEqualTo(String value) {
            addCriterion("contract_seller >=", value, "contractSeller");
            return (Criteria) this;
        }

        public Criteria andContractSellerGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_seller >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSellerLessThan(String value) {
            addCriterion("contract_seller <", value, "contractSeller");
            return (Criteria) this;
        }

        public Criteria andContractSellerLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_seller < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSellerLessThanOrEqualTo(String value) {
            addCriterion("contract_seller <=", value, "contractSeller");
            return (Criteria) this;
        }

        public Criteria andContractSellerLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_seller <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSellerLike(String value) {
            addCriterion("contract_seller like", value, "contractSeller");
            return (Criteria) this;
        }

        public Criteria andContractSellerNotLike(String value) {
            addCriterion("contract_seller not like", value, "contractSeller");
            return (Criteria) this;
        }

        public Criteria andContractSellerIn(List<String> values) {
            addCriterion("contract_seller in", values, "contractSeller");
            return (Criteria) this;
        }

        public Criteria andContractSellerNotIn(List<String> values) {
            addCriterion("contract_seller not in", values, "contractSeller");
            return (Criteria) this;
        }

        public Criteria andContractSellerBetween(String value1, String value2) {
            addCriterion("contract_seller between", value1, value2, "contractSeller");
            return (Criteria) this;
        }

        public Criteria andContractSellerNotBetween(String value1, String value2) {
            addCriterion("contract_seller not between", value1, value2, "contractSeller");
            return (Criteria) this;
        }

        public Criteria andContractTypeIsNull() {
            addCriterion("contract_type is null");
            return (Criteria) this;
        }

        public Criteria andContractTypeIsNotNull() {
            addCriterion("contract_type is not null");
            return (Criteria) this;
        }

        public Criteria andContractTypeEqualTo(String value) {
            addCriterion("contract_type =", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeNotEqualTo(String value) {
            addCriterion("contract_type <>", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThan(String value) {
            addCriterion("contract_type >", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanOrEqualTo(String value) {
            addCriterion("contract_type >=", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThan(String value) {
            addCriterion("contract_type <", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanOrEqualTo(String value) {
            addCriterion("contract_type <=", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeLike(String value) {
            addCriterion("contract_type like", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotLike(String value) {
            addCriterion("contract_type not like", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeIn(List<String> values) {
            addCriterion("contract_type in", values, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotIn(List<String> values) {
            addCriterion("contract_type not in", values, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeBetween(String value1, String value2) {
            addCriterion("contract_type between", value1, value2, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotBetween(String value1, String value2) {
            addCriterion("contract_type not between", value1, value2, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeIsNull() {
            addCriterion("contract_settle_mode is null");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeIsNotNull() {
            addCriterion("contract_settle_mode is not null");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeEqualTo(Integer value) {
            addCriterion("contract_settle_mode =", value, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_settle_mode = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSettleModeNotEqualTo(Integer value) {
            addCriterion("contract_settle_mode <>", value, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_settle_mode <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSettleModeGreaterThan(Integer value) {
            addCriterion("contract_settle_mode >", value, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_settle_mode > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSettleModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("contract_settle_mode >=", value, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_settle_mode >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSettleModeLessThan(Integer value) {
            addCriterion("contract_settle_mode <", value, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_settle_mode < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSettleModeLessThanOrEqualTo(Integer value) {
            addCriterion("contract_settle_mode <=", value, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("contract_settle_mode <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSettleModeIn(List<Integer> values) {
            addCriterion("contract_settle_mode in", values, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeNotIn(List<Integer> values) {
            addCriterion("contract_settle_mode not in", values, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeBetween(Integer value1, Integer value2) {
            addCriterion("contract_settle_mode between", value1, value2, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeNotBetween(Integer value1, Integer value2) {
            addCriterion("contract_settle_mode not between", value1, value2, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitIsNull() {
            addCriterion("money_unit is null");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitIsNotNull() {
            addCriterion("money_unit is not null");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitEqualTo(String value) {
            addCriterion("money_unit =", value, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("money_unit = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMoneyUnitNotEqualTo(String value) {
            addCriterion("money_unit <>", value, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("money_unit <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMoneyUnitGreaterThan(String value) {
            addCriterion("money_unit >", value, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("money_unit > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMoneyUnitGreaterThanOrEqualTo(String value) {
            addCriterion("money_unit >=", value, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("money_unit >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMoneyUnitLessThan(String value) {
            addCriterion("money_unit <", value, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("money_unit < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMoneyUnitLessThanOrEqualTo(String value) {
            addCriterion("money_unit <=", value, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("money_unit <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMoneyUnitLike(String value) {
            addCriterion("money_unit like", value, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitNotLike(String value) {
            addCriterion("money_unit not like", value, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitIn(List<String> values) {
            addCriterion("money_unit in", values, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitNotIn(List<String> values) {
            addCriterion("money_unit not in", values, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitBetween(String value1, String value2) {
            addCriterion("money_unit between", value1, value2, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitNotBetween(String value1, String value2) {
            addCriterion("money_unit not between", value1, value2, "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andSellUnitIsNull() {
            addCriterion("sell_unit is null");
            return (Criteria) this;
        }

        public Criteria andSellUnitIsNotNull() {
            addCriterion("sell_unit is not null");
            return (Criteria) this;
        }

        public Criteria andSellUnitEqualTo(String value) {
            addCriterion("sell_unit =", value, "sellUnit");
            return (Criteria) this;
        }

        public Criteria andSellUnitEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sell_unit = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellUnitNotEqualTo(String value) {
            addCriterion("sell_unit <>", value, "sellUnit");
            return (Criteria) this;
        }

        public Criteria andSellUnitNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sell_unit <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellUnitGreaterThan(String value) {
            addCriterion("sell_unit >", value, "sellUnit");
            return (Criteria) this;
        }

        public Criteria andSellUnitGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sell_unit > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellUnitGreaterThanOrEqualTo(String value) {
            addCriterion("sell_unit >=", value, "sellUnit");
            return (Criteria) this;
        }

        public Criteria andSellUnitGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sell_unit >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellUnitLessThan(String value) {
            addCriterion("sell_unit <", value, "sellUnit");
            return (Criteria) this;
        }

        public Criteria andSellUnitLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sell_unit < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellUnitLessThanOrEqualTo(String value) {
            addCriterion("sell_unit <=", value, "sellUnit");
            return (Criteria) this;
        }

        public Criteria andSellUnitLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sell_unit <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellUnitLike(String value) {
            addCriterion("sell_unit like", value, "sellUnit");
            return (Criteria) this;
        }

        public Criteria andSellUnitNotLike(String value) {
            addCriterion("sell_unit not like", value, "sellUnit");
            return (Criteria) this;
        }

        public Criteria andSellUnitIn(List<String> values) {
            addCriterion("sell_unit in", values, "sellUnit");
            return (Criteria) this;
        }

        public Criteria andSellUnitNotIn(List<String> values) {
            addCriterion("sell_unit not in", values, "sellUnit");
            return (Criteria) this;
        }

        public Criteria andSellUnitBetween(String value1, String value2) {
            addCriterion("sell_unit between", value1, value2, "sellUnit");
            return (Criteria) this;
        }

        public Criteria andSellUnitNotBetween(String value1, String value2) {
            addCriterion("sell_unit not between", value1, value2, "sellUnit");
            return (Criteria) this;
        }

        public Criteria andProductTypeIsNull() {
            addCriterion("product_type is null");
            return (Criteria) this;
        }

        public Criteria andProductTypeIsNotNull() {
            addCriterion("product_type is not null");
            return (Criteria) this;
        }

        public Criteria andProductTypeEqualTo(String value) {
            addCriterion("product_type =", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("product_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeNotEqualTo(String value) {
            addCriterion("product_type <>", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("product_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThan(String value) {
            addCriterion("product_type >", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("product_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThanOrEqualTo(String value) {
            addCriterion("product_type >=", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("product_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThan(String value) {
            addCriterion("product_type <", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("product_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThanOrEqualTo(String value) {
            addCriterion("product_type <=", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("product_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeLike(String value) {
            addCriterion("product_type like", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotLike(String value) {
            addCriterion("product_type not like", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeIn(List<String> values) {
            addCriterion("product_type in", values, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotIn(List<String> values) {
            addCriterion("product_type not in", values, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeBetween(String value1, String value2) {
            addCriterion("product_type between", value1, value2, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotBetween(String value1, String value2) {
            addCriterion("product_type not between", value1, value2, "productType");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameIsNull() {
            addCriterion("order_province_name is null");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameIsNotNull() {
            addCriterion("order_province_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameEqualTo(String value) {
            addCriterion("order_province_name =", value, "orderProvinceName");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_province_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameNotEqualTo(String value) {
            addCriterion("order_province_name <>", value, "orderProvinceName");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_province_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameGreaterThan(String value) {
            addCriterion("order_province_name >", value, "orderProvinceName");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_province_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameGreaterThanOrEqualTo(String value) {
            addCriterion("order_province_name >=", value, "orderProvinceName");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_province_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameLessThan(String value) {
            addCriterion("order_province_name <", value, "orderProvinceName");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_province_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameLessThanOrEqualTo(String value) {
            addCriterion("order_province_name <=", value, "orderProvinceName");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_province_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameLike(String value) {
            addCriterion("order_province_name like", value, "orderProvinceName");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameNotLike(String value) {
            addCriterion("order_province_name not like", value, "orderProvinceName");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameIn(List<String> values) {
            addCriterion("order_province_name in", values, "orderProvinceName");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameNotIn(List<String> values) {
            addCriterion("order_province_name not in", values, "orderProvinceName");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameBetween(String value1, String value2) {
            addCriterion("order_province_name between", value1, value2, "orderProvinceName");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameNotBetween(String value1, String value2) {
            addCriterion("order_province_name not between", value1, value2, "orderProvinceName");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeIsNull() {
            addCriterion("order_province_code is null");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeIsNotNull() {
            addCriterion("order_province_code is not null");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeEqualTo(String value) {
            addCriterion("order_province_code =", value, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_province_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeNotEqualTo(String value) {
            addCriterion("order_province_code <>", value, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_province_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeGreaterThan(String value) {
            addCriterion("order_province_code >", value, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_province_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("order_province_code >=", value, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_province_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeLessThan(String value) {
            addCriterion("order_province_code <", value, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_province_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("order_province_code <=", value, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_province_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeLike(String value) {
            addCriterion("order_province_code like", value, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeNotLike(String value) {
            addCriterion("order_province_code not like", value, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeIn(List<String> values) {
            addCriterion("order_province_code in", values, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeNotIn(List<String> values) {
            addCriterion("order_province_code not in", values, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeBetween(String value1, String value2) {
            addCriterion("order_province_code between", value1, value2, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("order_province_code not between", value1, value2, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityNameIsNull() {
            addCriterion("order_city_name is null");
            return (Criteria) this;
        }

        public Criteria andOrderCityNameIsNotNull() {
            addCriterion("order_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCityNameEqualTo(String value) {
            addCriterion("order_city_name =", value, "orderCityName");
            return (Criteria) this;
        }

        public Criteria andOrderCityNameEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_city_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityNameNotEqualTo(String value) {
            addCriterion("order_city_name <>", value, "orderCityName");
            return (Criteria) this;
        }

        public Criteria andOrderCityNameNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_city_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityNameGreaterThan(String value) {
            addCriterion("order_city_name >", value, "orderCityName");
            return (Criteria) this;
        }

        public Criteria andOrderCityNameGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_city_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("order_city_name >=", value, "orderCityName");
            return (Criteria) this;
        }

        public Criteria andOrderCityNameGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_city_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityNameLessThan(String value) {
            addCriterion("order_city_name <", value, "orderCityName");
            return (Criteria) this;
        }

        public Criteria andOrderCityNameLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_city_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityNameLessThanOrEqualTo(String value) {
            addCriterion("order_city_name <=", value, "orderCityName");
            return (Criteria) this;
        }

        public Criteria andOrderCityNameLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_city_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityNameLike(String value) {
            addCriterion("order_city_name like", value, "orderCityName");
            return (Criteria) this;
        }

        public Criteria andOrderCityNameNotLike(String value) {
            addCriterion("order_city_name not like", value, "orderCityName");
            return (Criteria) this;
        }

        public Criteria andOrderCityNameIn(List<String> values) {
            addCriterion("order_city_name in", values, "orderCityName");
            return (Criteria) this;
        }

        public Criteria andOrderCityNameNotIn(List<String> values) {
            addCriterion("order_city_name not in", values, "orderCityName");
            return (Criteria) this;
        }

        public Criteria andOrderCityNameBetween(String value1, String value2) {
            addCriterion("order_city_name between", value1, value2, "orderCityName");
            return (Criteria) this;
        }

        public Criteria andOrderCityNameNotBetween(String value1, String value2) {
            addCriterion("order_city_name not between", value1, value2, "orderCityName");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeIsNull() {
            addCriterion("order_city_code is null");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeIsNotNull() {
            addCriterion("order_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeEqualTo(String value) {
            addCriterion("order_city_code =", value, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_city_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeNotEqualTo(String value) {
            addCriterion("order_city_code <>", value, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_city_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeGreaterThan(String value) {
            addCriterion("order_city_code >", value, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_city_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("order_city_code >=", value, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_city_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeLessThan(String value) {
            addCriterion("order_city_code <", value, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_city_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeLessThanOrEqualTo(String value) {
            addCriterion("order_city_code <=", value, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_city_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeLike(String value) {
            addCriterion("order_city_code like", value, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeNotLike(String value) {
            addCriterion("order_city_code not like", value, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeIn(List<String> values) {
            addCriterion("order_city_code in", values, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeNotIn(List<String> values) {
            addCriterion("order_city_code not in", values, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeBetween(String value1, String value2) {
            addCriterion("order_city_code between", value1, value2, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeNotBetween(String value1, String value2) {
            addCriterion("order_city_code not between", value1, value2, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCountIsNull() {
            addCriterion("order_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderCountIsNotNull() {
            addCriterion("order_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCountEqualTo(String value) {
            addCriterion("order_count =", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_count = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCountNotEqualTo(String value) {
            addCriterion("order_count <>", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_count <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCountGreaterThan(String value) {
            addCriterion("order_count >", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_count > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCountGreaterThanOrEqualTo(String value) {
            addCriterion("order_count >=", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_count >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCountLessThan(String value) {
            addCriterion("order_count <", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_count < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCountLessThanOrEqualTo(String value) {
            addCriterion("order_count <=", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("order_count <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCountLike(String value) {
            addCriterion("order_count like", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountNotLike(String value) {
            addCriterion("order_count not like", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountIn(List<String> values) {
            addCriterion("order_count in", values, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountNotIn(List<String> values) {
            addCriterion("order_count not in", values, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountBetween(String value1, String value2) {
            addCriterion("order_count between", value1, value2, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountNotBetween(String value1, String value2) {
            addCriterion("order_count not between", value1, value2, "orderCount");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNull() {
            addCriterion("total_price is null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNotNull() {
            addCriterion("total_price is not null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceEqualTo(Long value) {
            addCriterion("total_price =", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("total_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotEqualTo(Long value) {
            addCriterion("total_price <>", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("total_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThan(Long value) {
            addCriterion("total_price >", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("total_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThanOrEqualTo(Long value) {
            addCriterion("total_price >=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("total_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThan(Long value) {
            addCriterion("total_price <", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("total_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThanOrEqualTo(Long value) {
            addCriterion("total_price <=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("total_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceIn(List<Long> values) {
            addCriterion("total_price in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotIn(List<Long> values) {
            addCriterion("total_price not in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceBetween(Long value1, Long value2) {
            addCriterion("total_price between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotBetween(Long value1, Long value2) {
            addCriterion("total_price not between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andK3RetNumIsNull() {
            addCriterion("k3_ret_num is null");
            return (Criteria) this;
        }

        public Criteria andK3RetNumIsNotNull() {
            addCriterion("k3_ret_num is not null");
            return (Criteria) this;
        }

        public Criteria andK3RetNumEqualTo(String value) {
            addCriterion("k3_ret_num =", value, "k3RetNum");
            return (Criteria) this;
        }

        public Criteria andK3RetNumEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_ret_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3RetNumNotEqualTo(String value) {
            addCriterion("k3_ret_num <>", value, "k3RetNum");
            return (Criteria) this;
        }

        public Criteria andK3RetNumNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_ret_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3RetNumGreaterThan(String value) {
            addCriterion("k3_ret_num >", value, "k3RetNum");
            return (Criteria) this;
        }

        public Criteria andK3RetNumGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_ret_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3RetNumGreaterThanOrEqualTo(String value) {
            addCriterion("k3_ret_num >=", value, "k3RetNum");
            return (Criteria) this;
        }

        public Criteria andK3RetNumGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_ret_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3RetNumLessThan(String value) {
            addCriterion("k3_ret_num <", value, "k3RetNum");
            return (Criteria) this;
        }

        public Criteria andK3RetNumLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_ret_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3RetNumLessThanOrEqualTo(String value) {
            addCriterion("k3_ret_num <=", value, "k3RetNum");
            return (Criteria) this;
        }

        public Criteria andK3RetNumLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_ret_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3RetNumLike(String value) {
            addCriterion("k3_ret_num like", value, "k3RetNum");
            return (Criteria) this;
        }

        public Criteria andK3RetNumNotLike(String value) {
            addCriterion("k3_ret_num not like", value, "k3RetNum");
            return (Criteria) this;
        }

        public Criteria andK3RetNumIn(List<String> values) {
            addCriterion("k3_ret_num in", values, "k3RetNum");
            return (Criteria) this;
        }

        public Criteria andK3RetNumNotIn(List<String> values) {
            addCriterion("k3_ret_num not in", values, "k3RetNum");
            return (Criteria) this;
        }

        public Criteria andK3RetNumBetween(String value1, String value2) {
            addCriterion("k3_ret_num between", value1, value2, "k3RetNum");
            return (Criteria) this;
        }

        public Criteria andK3RetNumNotBetween(String value1, String value2) {
            addCriterion("k3_ret_num not between", value1, value2, "k3RetNum");
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusIsNull() {
            addCriterion("k3_sync_status is null");
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusIsNotNull() {
            addCriterion("k3_sync_status is not null");
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusEqualTo(String value) {
            addCriterion("k3_sync_status =", value, "k3SyncStatus");
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_sync_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusNotEqualTo(String value) {
            addCriterion("k3_sync_status <>", value, "k3SyncStatus");
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_sync_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusGreaterThan(String value) {
            addCriterion("k3_sync_status >", value, "k3SyncStatus");
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_sync_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusGreaterThanOrEqualTo(String value) {
            addCriterion("k3_sync_status >=", value, "k3SyncStatus");
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_sync_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusLessThan(String value) {
            addCriterion("k3_sync_status <", value, "k3SyncStatus");
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_sync_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusLessThanOrEqualTo(String value) {
            addCriterion("k3_sync_status <=", value, "k3SyncStatus");
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_sync_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusLike(String value) {
            addCriterion("k3_sync_status like", value, "k3SyncStatus");
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusNotLike(String value) {
            addCriterion("k3_sync_status not like", value, "k3SyncStatus");
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusIn(List<String> values) {
            addCriterion("k3_sync_status in", values, "k3SyncStatus");
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusNotIn(List<String> values) {
            addCriterion("k3_sync_status not in", values, "k3SyncStatus");
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusBetween(String value1, String value2) {
            addCriterion("k3_sync_status between", value1, value2, "k3SyncStatus");
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusNotBetween(String value1, String value2) {
            addCriterion("k3_sync_status not between", value1, value2, "k3SyncStatus");
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusIsNull() {
            addCriterion("k3_commit_status is null");
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusIsNotNull() {
            addCriterion("k3_commit_status is not null");
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusEqualTo(String value) {
            addCriterion("k3_commit_status =", value, "k3CommitStatus");
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_commit_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusNotEqualTo(String value) {
            addCriterion("k3_commit_status <>", value, "k3CommitStatus");
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_commit_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusGreaterThan(String value) {
            addCriterion("k3_commit_status >", value, "k3CommitStatus");
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_commit_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusGreaterThanOrEqualTo(String value) {
            addCriterion("k3_commit_status >=", value, "k3CommitStatus");
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_commit_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusLessThan(String value) {
            addCriterion("k3_commit_status <", value, "k3CommitStatus");
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_commit_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusLessThanOrEqualTo(String value) {
            addCriterion("k3_commit_status <=", value, "k3CommitStatus");
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_commit_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusLike(String value) {
            addCriterion("k3_commit_status like", value, "k3CommitStatus");
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusNotLike(String value) {
            addCriterion("k3_commit_status not like", value, "k3CommitStatus");
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusIn(List<String> values) {
            addCriterion("k3_commit_status in", values, "k3CommitStatus");
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusNotIn(List<String> values) {
            addCriterion("k3_commit_status not in", values, "k3CommitStatus");
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusBetween(String value1, String value2) {
            addCriterion("k3_commit_status between", value1, value2, "k3CommitStatus");
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusNotBetween(String value1, String value2) {
            addCriterion("k3_commit_status not between", value1, value2, "k3CommitStatus");
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdIsNull() {
            addCriterion("seller_org_id is null");
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdIsNotNull() {
            addCriterion("seller_org_id is not null");
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdEqualTo(String value) {
            addCriterion("seller_org_id =", value, "sellerOrgId");
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_org_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdNotEqualTo(String value) {
            addCriterion("seller_org_id <>", value, "sellerOrgId");
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_org_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdGreaterThan(String value) {
            addCriterion("seller_org_id >", value, "sellerOrgId");
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_org_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdGreaterThanOrEqualTo(String value) {
            addCriterion("seller_org_id >=", value, "sellerOrgId");
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_org_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdLessThan(String value) {
            addCriterion("seller_org_id <", value, "sellerOrgId");
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_org_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdLessThanOrEqualTo(String value) {
            addCriterion("seller_org_id <=", value, "sellerOrgId");
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_org_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdLike(String value) {
            addCriterion("seller_org_id like", value, "sellerOrgId");
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdNotLike(String value) {
            addCriterion("seller_org_id not like", value, "sellerOrgId");
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdIn(List<String> values) {
            addCriterion("seller_org_id in", values, "sellerOrgId");
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdNotIn(List<String> values) {
            addCriterion("seller_org_id not in", values, "sellerOrgId");
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdBetween(String value1, String value2) {
            addCriterion("seller_org_id between", value1, value2, "sellerOrgId");
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdNotBetween(String value1, String value2) {
            addCriterion("seller_org_id not between", value1, value2, "sellerOrgId");
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdIsNull() {
            addCriterion("seller_team_id is null");
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdIsNotNull() {
            addCriterion("seller_team_id is not null");
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdEqualTo(String value) {
            addCriterion("seller_team_id =", value, "sellerTeamId");
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_team_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdNotEqualTo(String value) {
            addCriterion("seller_team_id <>", value, "sellerTeamId");
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_team_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdGreaterThan(String value) {
            addCriterion("seller_team_id >", value, "sellerTeamId");
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_team_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdGreaterThanOrEqualTo(String value) {
            addCriterion("seller_team_id >=", value, "sellerTeamId");
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_team_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdLessThan(String value) {
            addCriterion("seller_team_id <", value, "sellerTeamId");
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_team_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdLessThanOrEqualTo(String value) {
            addCriterion("seller_team_id <=", value, "sellerTeamId");
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_team_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdLike(String value) {
            addCriterion("seller_team_id like", value, "sellerTeamId");
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdNotLike(String value) {
            addCriterion("seller_team_id not like", value, "sellerTeamId");
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdIn(List<String> values) {
            addCriterion("seller_team_id in", values, "sellerTeamId");
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdNotIn(List<String> values) {
            addCriterion("seller_team_id not in", values, "sellerTeamId");
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdBetween(String value1, String value2) {
            addCriterion("seller_team_id between", value1, value2, "sellerTeamId");
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdNotBetween(String value1, String value2) {
            addCriterion("seller_team_id not between", value1, value2, "sellerTeamId");
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdIsNull() {
            addCriterion("seller_dept_id is null");
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdIsNotNull() {
            addCriterion("seller_dept_id is not null");
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdEqualTo(String value) {
            addCriterion("seller_dept_id =", value, "sellerDeptId");
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_dept_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdNotEqualTo(String value) {
            addCriterion("seller_dept_id <>", value, "sellerDeptId");
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_dept_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdGreaterThan(String value) {
            addCriterion("seller_dept_id >", value, "sellerDeptId");
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_dept_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdGreaterThanOrEqualTo(String value) {
            addCriterion("seller_dept_id >=", value, "sellerDeptId");
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_dept_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdLessThan(String value) {
            addCriterion("seller_dept_id <", value, "sellerDeptId");
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_dept_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdLessThanOrEqualTo(String value) {
            addCriterion("seller_dept_id <=", value, "sellerDeptId");
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_dept_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdLike(String value) {
            addCriterion("seller_dept_id like", value, "sellerDeptId");
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdNotLike(String value) {
            addCriterion("seller_dept_id not like", value, "sellerDeptId");
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdIn(List<String> values) {
            addCriterion("seller_dept_id in", values, "sellerDeptId");
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdNotIn(List<String> values) {
            addCriterion("seller_dept_id not in", values, "sellerDeptId");
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdBetween(String value1, String value2) {
            addCriterion("seller_dept_id between", value1, value2, "sellerDeptId");
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdNotBetween(String value1, String value2) {
            addCriterion("seller_dept_id not between", value1, value2, "sellerDeptId");
            return (Criteria) this;
        }

        public Criteria andSellerPhoneIsNull() {
            addCriterion("seller_phone is null");
            return (Criteria) this;
        }

        public Criteria andSellerPhoneIsNotNull() {
            addCriterion("seller_phone is not null");
            return (Criteria) this;
        }

        public Criteria andSellerPhoneEqualTo(String value) {
            addCriterion("seller_phone =", value, "sellerPhone");
            return (Criteria) this;
        }

        public Criteria andSellerPhoneEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerPhoneNotEqualTo(String value) {
            addCriterion("seller_phone <>", value, "sellerPhone");
            return (Criteria) this;
        }

        public Criteria andSellerPhoneNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerPhoneGreaterThan(String value) {
            addCriterion("seller_phone >", value, "sellerPhone");
            return (Criteria) this;
        }

        public Criteria andSellerPhoneGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("seller_phone >=", value, "sellerPhone");
            return (Criteria) this;
        }

        public Criteria andSellerPhoneGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerPhoneLessThan(String value) {
            addCriterion("seller_phone <", value, "sellerPhone");
            return (Criteria) this;
        }

        public Criteria andSellerPhoneLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerPhoneLessThanOrEqualTo(String value) {
            addCriterion("seller_phone <=", value, "sellerPhone");
            return (Criteria) this;
        }

        public Criteria andSellerPhoneLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("seller_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerPhoneLike(String value) {
            addCriterion("seller_phone like", value, "sellerPhone");
            return (Criteria) this;
        }

        public Criteria andSellerPhoneNotLike(String value) {
            addCriterion("seller_phone not like", value, "sellerPhone");
            return (Criteria) this;
        }

        public Criteria andSellerPhoneIn(List<String> values) {
            addCriterion("seller_phone in", values, "sellerPhone");
            return (Criteria) this;
        }

        public Criteria andSellerPhoneNotIn(List<String> values) {
            addCriterion("seller_phone not in", values, "sellerPhone");
            return (Criteria) this;
        }

        public Criteria andSellerPhoneBetween(String value1, String value2) {
            addCriterion("seller_phone between", value1, value2, "sellerPhone");
            return (Criteria) this;
        }

        public Criteria andSellerPhoneNotBetween(String value1, String value2) {
            addCriterion("seller_phone not between", value1, value2, "sellerPhone");
            return (Criteria) this;
        }

        public Criteria andCostCenterIsNull() {
            addCriterion("cost_center is null");
            return (Criteria) this;
        }

        public Criteria andCostCenterIsNotNull() {
            addCriterion("cost_center is not null");
            return (Criteria) this;
        }

        public Criteria andCostCenterEqualTo(String value) {
            addCriterion("cost_center =", value, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("cost_center = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterNotEqualTo(String value) {
            addCriterion("cost_center <>", value, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("cost_center <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterGreaterThan(String value) {
            addCriterion("cost_center >", value, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("cost_center > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterGreaterThanOrEqualTo(String value) {
            addCriterion("cost_center >=", value, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("cost_center >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterLessThan(String value) {
            addCriterion("cost_center <", value, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("cost_center < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterLessThanOrEqualTo(String value) {
            addCriterion("cost_center <=", value, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("cost_center <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostCenterLike(String value) {
            addCriterion("cost_center like", value, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterNotLike(String value) {
            addCriterion("cost_center not like", value, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterIn(List<String> values) {
            addCriterion("cost_center in", values, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterNotIn(List<String> values) {
            addCriterion("cost_center not in", values, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterBetween(String value1, String value2) {
            addCriterion("cost_center between", value1, value2, "costCenter");
            return (Criteria) this;
        }

        public Criteria andCostCenterNotBetween(String value1, String value2) {
            addCriterion("cost_center not between", value1, value2, "costCenter");
            return (Criteria) this;
        }

        public Criteria andProjectIsNull() {
            addCriterion("project is null");
            return (Criteria) this;
        }

        public Criteria andProjectIsNotNull() {
            addCriterion("project is not null");
            return (Criteria) this;
        }

        public Criteria andProjectEqualTo(String value) {
            addCriterion("project =", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("project = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectNotEqualTo(String value) {
            addCriterion("project <>", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("project <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThan(String value) {
            addCriterion("project >", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("project > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThanOrEqualTo(String value) {
            addCriterion("project >=", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("project >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectLessThan(String value) {
            addCriterion("project <", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("project < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectLessThanOrEqualTo(String value) {
            addCriterion("project <=", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("project <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectLike(String value) {
            addCriterion("project like", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotLike(String value) {
            addCriterion("project not like", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectIn(List<String> values) {
            addCriterion("project in", values, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotIn(List<String> values) {
            addCriterion("project not in", values, "project");
            return (Criteria) this;
        }

        public Criteria andProjectBetween(String value1, String value2) {
            addCriterion("project between", value1, value2, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotBetween(String value1, String value2) {
            addCriterion("project not between", value1, value2, "project");
            return (Criteria) this;
        }

        public Criteria andSubProjectIsNull() {
            addCriterion("sub_project is null");
            return (Criteria) this;
        }

        public Criteria andSubProjectIsNotNull() {
            addCriterion("sub_project is not null");
            return (Criteria) this;
        }

        public Criteria andSubProjectEqualTo(String value) {
            addCriterion("sub_project =", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sub_project = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSubProjectNotEqualTo(String value) {
            addCriterion("sub_project <>", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sub_project <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSubProjectGreaterThan(String value) {
            addCriterion("sub_project >", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sub_project > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSubProjectGreaterThanOrEqualTo(String value) {
            addCriterion("sub_project >=", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sub_project >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSubProjectLessThan(String value) {
            addCriterion("sub_project <", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sub_project < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSubProjectLessThanOrEqualTo(String value) {
            addCriterion("sub_project <=", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sub_project <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSubProjectLike(String value) {
            addCriterion("sub_project like", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectNotLike(String value) {
            addCriterion("sub_project not like", value, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectIn(List<String> values) {
            addCriterion("sub_project in", values, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectNotIn(List<String> values) {
            addCriterion("sub_project not in", values, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectBetween(String value1, String value2) {
            addCriterion("sub_project between", value1, value2, "subProject");
            return (Criteria) this;
        }

        public Criteria andSubProjectNotBetween(String value1, String value2) {
            addCriterion("sub_project not between", value1, value2, "subProject");
            return (Criteria) this;
        }

        public Criteria andSyncSucTimeIsNull() {
            addCriterion("sync_suc_time is null");
            return (Criteria) this;
        }

        public Criteria andSyncSucTimeIsNotNull() {
            addCriterion("sync_suc_time is not null");
            return (Criteria) this;
        }

        public Criteria andSyncSucTimeEqualTo(Date value) {
            addCriterion("sync_suc_time =", value, "syncSucTime");
            return (Criteria) this;
        }

        public Criteria andSyncSucTimeEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sync_suc_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSyncSucTimeNotEqualTo(Date value) {
            addCriterion("sync_suc_time <>", value, "syncSucTime");
            return (Criteria) this;
        }

        public Criteria andSyncSucTimeNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sync_suc_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSyncSucTimeGreaterThan(Date value) {
            addCriterion("sync_suc_time >", value, "syncSucTime");
            return (Criteria) this;
        }

        public Criteria andSyncSucTimeGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sync_suc_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSyncSucTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("sync_suc_time >=", value, "syncSucTime");
            return (Criteria) this;
        }

        public Criteria andSyncSucTimeGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sync_suc_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSyncSucTimeLessThan(Date value) {
            addCriterion("sync_suc_time <", value, "syncSucTime");
            return (Criteria) this;
        }

        public Criteria andSyncSucTimeLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sync_suc_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSyncSucTimeLessThanOrEqualTo(Date value) {
            addCriterion("sync_suc_time <=", value, "syncSucTime");
            return (Criteria) this;
        }

        public Criteria andSyncSucTimeLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sync_suc_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSyncSucTimeIn(List<Date> values) {
            addCriterion("sync_suc_time in", values, "syncSucTime");
            return (Criteria) this;
        }

        public Criteria andSyncSucTimeNotIn(List<Date> values) {
            addCriterion("sync_suc_time not in", values, "syncSucTime");
            return (Criteria) this;
        }

        public Criteria andSyncSucTimeBetween(Date value1, Date value2) {
            addCriterion("sync_suc_time between", value1, value2, "syncSucTime");
            return (Criteria) this;
        }

        public Criteria andSyncSucTimeNotBetween(Date value1, Date value2) {
            addCriterion("sync_suc_time not between", value1, value2, "syncSucTime");
            return (Criteria) this;
        }

        public Criteria andCommitSucTimeIsNull() {
            addCriterion("commit_suc_time is null");
            return (Criteria) this;
        }

        public Criteria andCommitSucTimeIsNotNull() {
            addCriterion("commit_suc_time is not null");
            return (Criteria) this;
        }

        public Criteria andCommitSucTimeEqualTo(Date value) {
            addCriterion("commit_suc_time =", value, "commitSucTime");
            return (Criteria) this;
        }

        public Criteria andCommitSucTimeEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("commit_suc_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCommitSucTimeNotEqualTo(Date value) {
            addCriterion("commit_suc_time <>", value, "commitSucTime");
            return (Criteria) this;
        }

        public Criteria andCommitSucTimeNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("commit_suc_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCommitSucTimeGreaterThan(Date value) {
            addCriterion("commit_suc_time >", value, "commitSucTime");
            return (Criteria) this;
        }

        public Criteria andCommitSucTimeGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("commit_suc_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCommitSucTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("commit_suc_time >=", value, "commitSucTime");
            return (Criteria) this;
        }

        public Criteria andCommitSucTimeGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("commit_suc_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCommitSucTimeLessThan(Date value) {
            addCriterion("commit_suc_time <", value, "commitSucTime");
            return (Criteria) this;
        }

        public Criteria andCommitSucTimeLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("commit_suc_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCommitSucTimeLessThanOrEqualTo(Date value) {
            addCriterion("commit_suc_time <=", value, "commitSucTime");
            return (Criteria) this;
        }

        public Criteria andCommitSucTimeLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("commit_suc_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCommitSucTimeIn(List<Date> values) {
            addCriterion("commit_suc_time in", values, "commitSucTime");
            return (Criteria) this;
        }

        public Criteria andCommitSucTimeNotIn(List<Date> values) {
            addCriterion("commit_suc_time not in", values, "commitSucTime");
            return (Criteria) this;
        }

        public Criteria andCommitSucTimeBetween(Date value1, Date value2) {
            addCriterion("commit_suc_time between", value1, value2, "commitSucTime");
            return (Criteria) this;
        }

        public Criteria andCommitSucTimeNotBetween(Date value1, Date value2) {
            addCriterion("commit_suc_time not between", value1, value2, "commitSucTime");
            return (Criteria) this;
        }

        public Criteria andCustomCodeIsNull() {
            addCriterion("custom_code is null");
            return (Criteria) this;
        }

        public Criteria andCustomCodeIsNotNull() {
            addCriterion("custom_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustomCodeEqualTo(String value) {
            addCriterion("custom_code =", value, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("custom_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomCodeNotEqualTo(String value) {
            addCriterion("custom_code <>", value, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("custom_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomCodeGreaterThan(String value) {
            addCriterion("custom_code >", value, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("custom_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomCodeGreaterThanOrEqualTo(String value) {
            addCriterion("custom_code >=", value, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("custom_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomCodeLessThan(String value) {
            addCriterion("custom_code <", value, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("custom_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomCodeLessThanOrEqualTo(String value) {
            addCriterion("custom_code <=", value, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("custom_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomCodeLike(String value) {
            addCriterion("custom_code like", value, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeNotLike(String value) {
            addCriterion("custom_code not like", value, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeIn(List<String> values) {
            addCriterion("custom_code in", values, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeNotIn(List<String> values) {
            addCriterion("custom_code not in", values, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeBetween(String value1, String value2) {
            addCriterion("custom_code between", value1, value2, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeNotBetween(String value1, String value2) {
            addCriterion("custom_code not between", value1, value2, "customCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceIsNull() {
            addCriterion("buyer_province is null");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceIsNotNull() {
            addCriterion("buyer_province is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceEqualTo(String value) {
            addCriterion("buyer_province =", value, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_province = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceNotEqualTo(String value) {
            addCriterion("buyer_province <>", value, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_province <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceGreaterThan(String value) {
            addCriterion("buyer_province >", value, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_province > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("buyer_province >=", value, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_province >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceLessThan(String value) {
            addCriterion("buyer_province <", value, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_province < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceLessThanOrEqualTo(String value) {
            addCriterion("buyer_province <=", value, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_province <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceLike(String value) {
            addCriterion("buyer_province like", value, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceNotLike(String value) {
            addCriterion("buyer_province not like", value, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceIn(List<String> values) {
            addCriterion("buyer_province in", values, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceNotIn(List<String> values) {
            addCriterion("buyer_province not in", values, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceBetween(String value1, String value2) {
            addCriterion("buyer_province between", value1, value2, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceNotBetween(String value1, String value2) {
            addCriterion("buyer_province not between", value1, value2, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeIsNull() {
            addCriterion("buyer_province_code is null");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeIsNotNull() {
            addCriterion("buyer_province_code is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeEqualTo(String value) {
            addCriterion("buyer_province_code =", value, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_province_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeNotEqualTo(String value) {
            addCriterion("buyer_province_code <>", value, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_province_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeGreaterThan(String value) {
            addCriterion("buyer_province_code >", value, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_province_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("buyer_province_code >=", value, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_province_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeLessThan(String value) {
            addCriterion("buyer_province_code <", value, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_province_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("buyer_province_code <=", value, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_province_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeLike(String value) {
            addCriterion("buyer_province_code like", value, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeNotLike(String value) {
            addCriterion("buyer_province_code not like", value, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeIn(List<String> values) {
            addCriterion("buyer_province_code in", values, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeNotIn(List<String> values) {
            addCriterion("buyer_province_code not in", values, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeBetween(String value1, String value2) {
            addCriterion("buyer_province_code between", value1, value2, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("buyer_province_code not between", value1, value2, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeIsNull() {
            addCriterion("buyer_city_code is null");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeIsNotNull() {
            addCriterion("buyer_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeEqualTo(String value) {
            addCriterion("buyer_city_code =", value, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_city_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeNotEqualTo(String value) {
            addCriterion("buyer_city_code <>", value, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_city_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeGreaterThan(String value) {
            addCriterion("buyer_city_code >", value, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_city_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("buyer_city_code >=", value, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_city_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeLessThan(String value) {
            addCriterion("buyer_city_code <", value, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_city_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeLessThanOrEqualTo(String value) {
            addCriterion("buyer_city_code <=", value, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_city_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeLike(String value) {
            addCriterion("buyer_city_code like", value, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeNotLike(String value) {
            addCriterion("buyer_city_code not like", value, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeIn(List<String> values) {
            addCriterion("buyer_city_code in", values, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeNotIn(List<String> values) {
            addCriterion("buyer_city_code not in", values, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeBetween(String value1, String value2) {
            addCriterion("buyer_city_code between", value1, value2, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeNotBetween(String value1, String value2) {
            addCriterion("buyer_city_code not between", value1, value2, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityIsNull() {
            addCriterion("buyer_city is null");
            return (Criteria) this;
        }

        public Criteria andBuyerCityIsNotNull() {
            addCriterion("buyer_city is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerCityEqualTo(String value) {
            addCriterion("buyer_city =", value, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_city = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityNotEqualTo(String value) {
            addCriterion("buyer_city <>", value, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_city <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityGreaterThan(String value) {
            addCriterion("buyer_city >", value, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_city > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityGreaterThanOrEqualTo(String value) {
            addCriterion("buyer_city >=", value, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_city >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityLessThan(String value) {
            addCriterion("buyer_city <", value, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_city < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityLessThanOrEqualTo(String value) {
            addCriterion("buyer_city <=", value, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("buyer_city <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityLike(String value) {
            addCriterion("buyer_city like", value, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityNotLike(String value) {
            addCriterion("buyer_city not like", value, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityIn(List<String> values) {
            addCriterion("buyer_city in", values, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityNotIn(List<String> values) {
            addCriterion("buyer_city not in", values, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityBetween(String value1, String value2) {
            addCriterion("buyer_city between", value1, value2, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityNotBetween(String value1, String value2) {
            addCriterion("buyer_city not between", value1, value2, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsIsNull() {
            addCriterion("related_order_ids is null");
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsIsNotNull() {
            addCriterion("related_order_ids is not null");
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsEqualTo(String value) {
            addCriterion("related_order_ids =", value, "relatedOrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("related_order_ids = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsNotEqualTo(String value) {
            addCriterion("related_order_ids <>", value, "relatedOrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("related_order_ids <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsGreaterThan(String value) {
            addCriterion("related_order_ids >", value, "relatedOrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("related_order_ids > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsGreaterThanOrEqualTo(String value) {
            addCriterion("related_order_ids >=", value, "relatedOrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("related_order_ids >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsLessThan(String value) {
            addCriterion("related_order_ids <", value, "relatedOrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("related_order_ids < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsLessThanOrEqualTo(String value) {
            addCriterion("related_order_ids <=", value, "relatedOrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("related_order_ids <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsLike(String value) {
            addCriterion("related_order_ids like", value, "relatedOrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsNotLike(String value) {
            addCriterion("related_order_ids not like", value, "relatedOrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsIn(List<String> values) {
            addCriterion("related_order_ids in", values, "relatedOrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsNotIn(List<String> values) {
            addCriterion("related_order_ids not in", values, "relatedOrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsBetween(String value1, String value2) {
            addCriterion("related_order_ids between", value1, value2, "relatedOrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsNotBetween(String value1, String value2) {
            addCriterion("related_order_ids not between", value1, value2, "relatedOrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsIsNull() {
            addCriterion("related_k3_order_ids is null");
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsIsNotNull() {
            addCriterion("related_k3_order_ids is not null");
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsEqualTo(String value) {
            addCriterion("related_k3_order_ids =", value, "relatedK3OrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("related_k3_order_ids = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsNotEqualTo(String value) {
            addCriterion("related_k3_order_ids <>", value, "relatedK3OrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("related_k3_order_ids <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsGreaterThan(String value) {
            addCriterion("related_k3_order_ids >", value, "relatedK3OrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("related_k3_order_ids > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsGreaterThanOrEqualTo(String value) {
            addCriterion("related_k3_order_ids >=", value, "relatedK3OrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("related_k3_order_ids >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsLessThan(String value) {
            addCriterion("related_k3_order_ids <", value, "relatedK3OrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("related_k3_order_ids < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsLessThanOrEqualTo(String value) {
            addCriterion("related_k3_order_ids <=", value, "relatedK3OrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("related_k3_order_ids <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsLike(String value) {
            addCriterion("related_k3_order_ids like", value, "relatedK3OrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsNotLike(String value) {
            addCriterion("related_k3_order_ids not like", value, "relatedK3OrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsIn(List<String> values) {
            addCriterion("related_k3_order_ids in", values, "relatedK3OrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsNotIn(List<String> values) {
            addCriterion("related_k3_order_ids not in", values, "relatedK3OrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsBetween(String value1, String value2) {
            addCriterion("related_k3_order_ids between", value1, value2, "relatedK3OrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsNotBetween(String value1, String value2) {
            addCriterion("related_k3_order_ids not between", value1, value2, "relatedK3OrderIds");
            return (Criteria) this;
        }

        public Criteria andProRetNumIsNull() {
            addCriterion("pro_ret_num is null");
            return (Criteria) this;
        }

        public Criteria andProRetNumIsNotNull() {
            addCriterion("pro_ret_num is not null");
            return (Criteria) this;
        }

        public Criteria andProRetNumEqualTo(String value) {
            addCriterion("pro_ret_num =", value, "proRetNum");
            return (Criteria) this;
        }

        public Criteria andProRetNumEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_ret_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProRetNumNotEqualTo(String value) {
            addCriterion("pro_ret_num <>", value, "proRetNum");
            return (Criteria) this;
        }

        public Criteria andProRetNumNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_ret_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProRetNumGreaterThan(String value) {
            addCriterion("pro_ret_num >", value, "proRetNum");
            return (Criteria) this;
        }

        public Criteria andProRetNumGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_ret_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProRetNumGreaterThanOrEqualTo(String value) {
            addCriterion("pro_ret_num >=", value, "proRetNum");
            return (Criteria) this;
        }

        public Criteria andProRetNumGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_ret_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProRetNumLessThan(String value) {
            addCriterion("pro_ret_num <", value, "proRetNum");
            return (Criteria) this;
        }

        public Criteria andProRetNumLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_ret_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProRetNumLessThanOrEqualTo(String value) {
            addCriterion("pro_ret_num <=", value, "proRetNum");
            return (Criteria) this;
        }

        public Criteria andProRetNumLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_ret_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProRetNumLike(String value) {
            addCriterion("pro_ret_num like", value, "proRetNum");
            return (Criteria) this;
        }

        public Criteria andProRetNumNotLike(String value) {
            addCriterion("pro_ret_num not like", value, "proRetNum");
            return (Criteria) this;
        }

        public Criteria andProRetNumIn(List<String> values) {
            addCriterion("pro_ret_num in", values, "proRetNum");
            return (Criteria) this;
        }

        public Criteria andProRetNumNotIn(List<String> values) {
            addCriterion("pro_ret_num not in", values, "proRetNum");
            return (Criteria) this;
        }

        public Criteria andProRetNumBetween(String value1, String value2) {
            addCriterion("pro_ret_num between", value1, value2, "proRetNum");
            return (Criteria) this;
        }

        public Criteria andProRetNumNotBetween(String value1, String value2) {
            addCriterion("pro_ret_num not between", value1, value2, "proRetNum");
            return (Criteria) this;
        }

        public Criteria andProSyncStatusIsNull() {
            addCriterion("pro_sync_status is null");
            return (Criteria) this;
        }

        public Criteria andProSyncStatusIsNotNull() {
            addCriterion("pro_sync_status is not null");
            return (Criteria) this;
        }

        public Criteria andProSyncStatusEqualTo(String value) {
            addCriterion("pro_sync_status =", value, "proSyncStatus");
            return (Criteria) this;
        }

        public Criteria andProSyncStatusEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_sync_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProSyncStatusNotEqualTo(String value) {
            addCriterion("pro_sync_status <>", value, "proSyncStatus");
            return (Criteria) this;
        }

        public Criteria andProSyncStatusNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_sync_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProSyncStatusGreaterThan(String value) {
            addCriterion("pro_sync_status >", value, "proSyncStatus");
            return (Criteria) this;
        }

        public Criteria andProSyncStatusGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_sync_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProSyncStatusGreaterThanOrEqualTo(String value) {
            addCriterion("pro_sync_status >=", value, "proSyncStatus");
            return (Criteria) this;
        }

        public Criteria andProSyncStatusGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_sync_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProSyncStatusLessThan(String value) {
            addCriterion("pro_sync_status <", value, "proSyncStatus");
            return (Criteria) this;
        }

        public Criteria andProSyncStatusLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_sync_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProSyncStatusLessThanOrEqualTo(String value) {
            addCriterion("pro_sync_status <=", value, "proSyncStatus");
            return (Criteria) this;
        }

        public Criteria andProSyncStatusLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_sync_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProSyncStatusLike(String value) {
            addCriterion("pro_sync_status like", value, "proSyncStatus");
            return (Criteria) this;
        }

        public Criteria andProSyncStatusNotLike(String value) {
            addCriterion("pro_sync_status not like", value, "proSyncStatus");
            return (Criteria) this;
        }

        public Criteria andProSyncStatusIn(List<String> values) {
            addCriterion("pro_sync_status in", values, "proSyncStatus");
            return (Criteria) this;
        }

        public Criteria andProSyncStatusNotIn(List<String> values) {
            addCriterion("pro_sync_status not in", values, "proSyncStatus");
            return (Criteria) this;
        }

        public Criteria andProSyncStatusBetween(String value1, String value2) {
            addCriterion("pro_sync_status between", value1, value2, "proSyncStatus");
            return (Criteria) this;
        }

        public Criteria andProSyncStatusNotBetween(String value1, String value2) {
            addCriterion("pro_sync_status not between", value1, value2, "proSyncStatus");
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusIsNull() {
            addCriterion("pro_submit_account_status is null");
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusIsNotNull() {
            addCriterion("pro_submit_account_status is not null");
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusEqualTo(String value) {
            addCriterion("pro_submit_account_status =", value, "proSubmitAccountStatus");
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_submit_account_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusNotEqualTo(String value) {
            addCriterion("pro_submit_account_status <>", value, "proSubmitAccountStatus");
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_submit_account_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusGreaterThan(String value) {
            addCriterion("pro_submit_account_status >", value, "proSubmitAccountStatus");
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_submit_account_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusGreaterThanOrEqualTo(String value) {
            addCriterion("pro_submit_account_status >=", value, "proSubmitAccountStatus");
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_submit_account_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusLessThan(String value) {
            addCriterion("pro_submit_account_status <", value, "proSubmitAccountStatus");
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_submit_account_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusLessThanOrEqualTo(String value) {
            addCriterion("pro_submit_account_status <=", value, "proSubmitAccountStatus");
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_submit_account_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusLike(String value) {
            addCriterion("pro_submit_account_status like", value, "proSubmitAccountStatus");
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusNotLike(String value) {
            addCriterion("pro_submit_account_status not like", value, "proSubmitAccountStatus");
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusIn(List<String> values) {
            addCriterion("pro_submit_account_status in", values, "proSubmitAccountStatus");
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusNotIn(List<String> values) {
            addCriterion("pro_submit_account_status not in", values, "proSubmitAccountStatus");
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusBetween(String value1, String value2) {
            addCriterion("pro_submit_account_status between", value1, value2, "proSubmitAccountStatus");
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusNotBetween(String value1, String value2) {
            addCriterion("pro_submit_account_status not between", value1, value2, "proSubmitAccountStatus");
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameIsNull() {
            addCriterion("sync_k3_user_name is null");
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameIsNotNull() {
            addCriterion("sync_k3_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameEqualTo(String value) {
            addCriterion("sync_k3_user_name =", value, "syncK3UserName");
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sync_k3_user_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameNotEqualTo(String value) {
            addCriterion("sync_k3_user_name <>", value, "syncK3UserName");
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sync_k3_user_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameGreaterThan(String value) {
            addCriterion("sync_k3_user_name >", value, "syncK3UserName");
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sync_k3_user_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameGreaterThanOrEqualTo(String value) {
            addCriterion("sync_k3_user_name >=", value, "syncK3UserName");
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sync_k3_user_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameLessThan(String value) {
            addCriterion("sync_k3_user_name <", value, "syncK3UserName");
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sync_k3_user_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameLessThanOrEqualTo(String value) {
            addCriterion("sync_k3_user_name <=", value, "syncK3UserName");
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("sync_k3_user_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameLike(String value) {
            addCriterion("sync_k3_user_name like", value, "syncK3UserName");
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameNotLike(String value) {
            addCriterion("sync_k3_user_name not like", value, "syncK3UserName");
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameIn(List<String> values) {
            addCriterion("sync_k3_user_name in", values, "syncK3UserName");
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameNotIn(List<String> values) {
            addCriterion("sync_k3_user_name not in", values, "syncK3UserName");
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameBetween(String value1, String value2) {
            addCriterion("sync_k3_user_name between", value1, value2, "syncK3UserName");
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameNotBetween(String value1, String value2) {
            addCriterion("sync_k3_user_name not between", value1, value2, "syncK3UserName");
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameIsNull() {
            addCriterion("commit_k3_user_name is null");
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameIsNotNull() {
            addCriterion("commit_k3_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameEqualTo(String value) {
            addCriterion("commit_k3_user_name =", value, "commitK3UserName");
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("commit_k3_user_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameNotEqualTo(String value) {
            addCriterion("commit_k3_user_name <>", value, "commitK3UserName");
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("commit_k3_user_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameGreaterThan(String value) {
            addCriterion("commit_k3_user_name >", value, "commitK3UserName");
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("commit_k3_user_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameGreaterThanOrEqualTo(String value) {
            addCriterion("commit_k3_user_name >=", value, "commitK3UserName");
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("commit_k3_user_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameLessThan(String value) {
            addCriterion("commit_k3_user_name <", value, "commitK3UserName");
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("commit_k3_user_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameLessThanOrEqualTo(String value) {
            addCriterion("commit_k3_user_name <=", value, "commitK3UserName");
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("commit_k3_user_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameLike(String value) {
            addCriterion("commit_k3_user_name like", value, "commitK3UserName");
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameNotLike(String value) {
            addCriterion("commit_k3_user_name not like", value, "commitK3UserName");
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameIn(List<String> values) {
            addCriterion("commit_k3_user_name in", values, "commitK3UserName");
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameNotIn(List<String> values) {
            addCriterion("commit_k3_user_name not in", values, "commitK3UserName");
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameBetween(String value1, String value2) {
            addCriterion("commit_k3_user_name between", value1, value2, "commitK3UserName");
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameNotBetween(String value1, String value2) {
            addCriterion("commit_k3_user_name not between", value1, value2, "commitK3UserName");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptIsNull() {
            addCriterion("material_dept is null");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptIsNotNull() {
            addCriterion("material_dept is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptEqualTo(String value) {
            addCriterion("material_dept =", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("material_dept = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDeptNotEqualTo(String value) {
            addCriterion("material_dept <>", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("material_dept <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDeptGreaterThan(String value) {
            addCriterion("material_dept >", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("material_dept > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDeptGreaterThanOrEqualTo(String value) {
            addCriterion("material_dept >=", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("material_dept >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDeptLessThan(String value) {
            addCriterion("material_dept <", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("material_dept < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDeptLessThanOrEqualTo(String value) {
            addCriterion("material_dept <=", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("material_dept <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDeptLike(String value) {
            addCriterion("material_dept like", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptNotLike(String value) {
            addCriterion("material_dept not like", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptIn(List<String> values) {
            addCriterion("material_dept in", values, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptNotIn(List<String> values) {
            addCriterion("material_dept not in", values, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptBetween(String value1, String value2) {
            addCriterion("material_dept between", value1, value2, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptNotBetween(String value1, String value2) {
            addCriterion("material_dept not between", value1, value2, "materialDept");
            return (Criteria) this;
        }

        public Criteria andProDataCodeIsNull() {
            addCriterion("pro_data_code is null");
            return (Criteria) this;
        }

        public Criteria andProDataCodeIsNotNull() {
            addCriterion("pro_data_code is not null");
            return (Criteria) this;
        }

        public Criteria andProDataCodeEqualTo(String value) {
            addCriterion("pro_data_code =", value, "proDataCode");
            return (Criteria) this;
        }

        public Criteria andProDataCodeEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_data_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProDataCodeNotEqualTo(String value) {
            addCriterion("pro_data_code <>", value, "proDataCode");
            return (Criteria) this;
        }

        public Criteria andProDataCodeNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_data_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProDataCodeGreaterThan(String value) {
            addCriterion("pro_data_code >", value, "proDataCode");
            return (Criteria) this;
        }

        public Criteria andProDataCodeGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_data_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProDataCodeGreaterThanOrEqualTo(String value) {
            addCriterion("pro_data_code >=", value, "proDataCode");
            return (Criteria) this;
        }

        public Criteria andProDataCodeGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_data_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProDataCodeLessThan(String value) {
            addCriterion("pro_data_code <", value, "proDataCode");
            return (Criteria) this;
        }

        public Criteria andProDataCodeLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_data_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProDataCodeLessThanOrEqualTo(String value) {
            addCriterion("pro_data_code <=", value, "proDataCode");
            return (Criteria) this;
        }

        public Criteria andProDataCodeLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_data_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProDataCodeLike(String value) {
            addCriterion("pro_data_code like", value, "proDataCode");
            return (Criteria) this;
        }

        public Criteria andProDataCodeNotLike(String value) {
            addCriterion("pro_data_code not like", value, "proDataCode");
            return (Criteria) this;
        }

        public Criteria andProDataCodeIn(List<String> values) {
            addCriterion("pro_data_code in", values, "proDataCode");
            return (Criteria) this;
        }

        public Criteria andProDataCodeNotIn(List<String> values) {
            addCriterion("pro_data_code not in", values, "proDataCode");
            return (Criteria) this;
        }

        public Criteria andProDataCodeBetween(String value1, String value2) {
            addCriterion("pro_data_code between", value1, value2, "proDataCode");
            return (Criteria) this;
        }

        public Criteria andProDataCodeNotBetween(String value1, String value2) {
            addCriterion("pro_data_code not between", value1, value2, "proDataCode");
            return (Criteria) this;
        }

        public Criteria andK3StatusIsNull() {
            addCriterion("k3_status is null");
            return (Criteria) this;
        }

        public Criteria andK3StatusIsNotNull() {
            addCriterion("k3_status is not null");
            return (Criteria) this;
        }

        public Criteria andK3StatusEqualTo(Integer value) {
            addCriterion("k3_status =", value, "k3Status");
            return (Criteria) this;
        }

        public Criteria andK3StatusEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3StatusNotEqualTo(Integer value) {
            addCriterion("k3_status <>", value, "k3Status");
            return (Criteria) this;
        }

        public Criteria andK3StatusNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3StatusGreaterThan(Integer value) {
            addCriterion("k3_status >", value, "k3Status");
            return (Criteria) this;
        }

        public Criteria andK3StatusGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3StatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("k3_status >=", value, "k3Status");
            return (Criteria) this;
        }

        public Criteria andK3StatusGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3StatusLessThan(Integer value) {
            addCriterion("k3_status <", value, "k3Status");
            return (Criteria) this;
        }

        public Criteria andK3StatusLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3StatusLessThanOrEqualTo(Integer value) {
            addCriterion("k3_status <=", value, "k3Status");
            return (Criteria) this;
        }

        public Criteria andK3StatusLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("k3_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3StatusIn(List<Integer> values) {
            addCriterion("k3_status in", values, "k3Status");
            return (Criteria) this;
        }

        public Criteria andK3StatusNotIn(List<Integer> values) {
            addCriterion("k3_status not in", values, "k3Status");
            return (Criteria) this;
        }

        public Criteria andK3StatusBetween(Integer value1, Integer value2) {
            addCriterion("k3_status between", value1, value2, "k3Status");
            return (Criteria) this;
        }

        public Criteria andK3StatusNotBetween(Integer value1, Integer value2) {
            addCriterion("k3_status not between", value1, value2, "k3Status");
            return (Criteria) this;
        }

        public Criteria andProMaterialStatusIsNull() {
            addCriterion("pro_material_status is null");
            return (Criteria) this;
        }

        public Criteria andProMaterialStatusIsNotNull() {
            addCriterion("pro_material_status is not null");
            return (Criteria) this;
        }

        public Criteria andProMaterialStatusEqualTo(Integer value) {
            addCriterion("pro_material_status =", value, "proMaterialStatus");
            return (Criteria) this;
        }

        public Criteria andProMaterialStatusEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_material_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProMaterialStatusNotEqualTo(Integer value) {
            addCriterion("pro_material_status <>", value, "proMaterialStatus");
            return (Criteria) this;
        }

        public Criteria andProMaterialStatusNotEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_material_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProMaterialStatusGreaterThan(Integer value) {
            addCriterion("pro_material_status >", value, "proMaterialStatus");
            return (Criteria) this;
        }

        public Criteria andProMaterialStatusGreaterThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_material_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProMaterialStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("pro_material_status >=", value, "proMaterialStatus");
            return (Criteria) this;
        }

        public Criteria andProMaterialStatusGreaterThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_material_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProMaterialStatusLessThan(Integer value) {
            addCriterion("pro_material_status <", value, "proMaterialStatus");
            return (Criteria) this;
        }

        public Criteria andProMaterialStatusLessThanColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_material_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProMaterialStatusLessThanOrEqualTo(Integer value) {
            addCriterion("pro_material_status <=", value, "proMaterialStatus");
            return (Criteria) this;
        }

        public Criteria andProMaterialStatusLessThanOrEqualToColumn(K3syncStatisProcity.Column column) {
            addCriterion(new StringBuilder("pro_material_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProMaterialStatusIn(List<Integer> values) {
            addCriterion("pro_material_status in", values, "proMaterialStatus");
            return (Criteria) this;
        }

        public Criteria andProMaterialStatusNotIn(List<Integer> values) {
            addCriterion("pro_material_status not in", values, "proMaterialStatus");
            return (Criteria) this;
        }

        public Criteria andProMaterialStatusBetween(Integer value1, Integer value2) {
            addCriterion("pro_material_status between", value1, value2, "proMaterialStatus");
            return (Criteria) this;
        }

        public Criteria andProMaterialStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("pro_material_status not between", value1, value2, "proMaterialStatus");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andContractNumLikeInsensitive(String value) {
            addCriterion("upper(contract_num) like", value.toUpperCase(), "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNameLikeInsensitive(String value) {
            addCriterion("upper(contract_name) like", value.toUpperCase(), "contractName");
            return (Criteria) this;
        }

        public Criteria andContractDeptLikeInsensitive(String value) {
            addCriterion("upper(contract_dept) like", value.toUpperCase(), "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractStatisTypeLikeInsensitive(String value) {
            addCriterion("upper(contract_statis_type) like", value.toUpperCase(), "contractStatisType");
            return (Criteria) this;
        }

        public Criteria andContractSellerLikeInsensitive(String value) {
            addCriterion("upper(contract_seller) like", value.toUpperCase(), "contractSeller");
            return (Criteria) this;
        }

        public Criteria andContractTypeLikeInsensitive(String value) {
            addCriterion("upper(contract_type) like", value.toUpperCase(), "contractType");
            return (Criteria) this;
        }

        public Criteria andMoneyUnitLikeInsensitive(String value) {
            addCriterion("upper(money_unit) like", value.toUpperCase(), "moneyUnit");
            return (Criteria) this;
        }

        public Criteria andSellUnitLikeInsensitive(String value) {
            addCriterion("upper(sell_unit) like", value.toUpperCase(), "sellUnit");
            return (Criteria) this;
        }

        public Criteria andProductTypeLikeInsensitive(String value) {
            addCriterion("upper(product_type) like", value.toUpperCase(), "productType");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNameLikeInsensitive(String value) {
            addCriterion("upper(order_province_name) like", value.toUpperCase(), "orderProvinceName");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeLikeInsensitive(String value) {
            addCriterion("upper(order_province_code) like", value.toUpperCase(), "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityNameLikeInsensitive(String value) {
            addCriterion("upper(order_city_name) like", value.toUpperCase(), "orderCityName");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeLikeInsensitive(String value) {
            addCriterion("upper(order_city_code) like", value.toUpperCase(), "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCountLikeInsensitive(String value) {
            addCriterion("upper(order_count) like", value.toUpperCase(), "orderCount");
            return (Criteria) this;
        }

        public Criteria andK3RetNumLikeInsensitive(String value) {
            addCriterion("upper(k3_ret_num) like", value.toUpperCase(), "k3RetNum");
            return (Criteria) this;
        }

        public Criteria andK3SyncStatusLikeInsensitive(String value) {
            addCriterion("upper(k3_sync_status) like", value.toUpperCase(), "k3SyncStatus");
            return (Criteria) this;
        }

        public Criteria andK3CommitStatusLikeInsensitive(String value) {
            addCriterion("upper(k3_commit_status) like", value.toUpperCase(), "k3CommitStatus");
            return (Criteria) this;
        }

        public Criteria andSellerOrgIdLikeInsensitive(String value) {
            addCriterion("upper(seller_org_id) like", value.toUpperCase(), "sellerOrgId");
            return (Criteria) this;
        }

        public Criteria andSellerTeamIdLikeInsensitive(String value) {
            addCriterion("upper(seller_team_id) like", value.toUpperCase(), "sellerTeamId");
            return (Criteria) this;
        }

        public Criteria andSellerDeptIdLikeInsensitive(String value) {
            addCriterion("upper(seller_dept_id) like", value.toUpperCase(), "sellerDeptId");
            return (Criteria) this;
        }

        public Criteria andSellerPhoneLikeInsensitive(String value) {
            addCriterion("upper(seller_phone) like", value.toUpperCase(), "sellerPhone");
            return (Criteria) this;
        }

        public Criteria andCostCenterLikeInsensitive(String value) {
            addCriterion("upper(cost_center) like", value.toUpperCase(), "costCenter");
            return (Criteria) this;
        }

        public Criteria andProjectLikeInsensitive(String value) {
            addCriterion("upper(project) like", value.toUpperCase(), "project");
            return (Criteria) this;
        }

        public Criteria andSubProjectLikeInsensitive(String value) {
            addCriterion("upper(sub_project) like", value.toUpperCase(), "subProject");
            return (Criteria) this;
        }

        public Criteria andCustomCodeLikeInsensitive(String value) {
            addCriterion("upper(custom_code) like", value.toUpperCase(), "customCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceLikeInsensitive(String value) {
            addCriterion("upper(buyer_province) like", value.toUpperCase(), "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeLikeInsensitive(String value) {
            addCriterion("upper(buyer_province_code) like", value.toUpperCase(), "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeLikeInsensitive(String value) {
            addCriterion("upper(buyer_city_code) like", value.toUpperCase(), "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityLikeInsensitive(String value) {
            addCriterion("upper(buyer_city) like", value.toUpperCase(), "buyerCity");
            return (Criteria) this;
        }

        public Criteria andRelatedOrderIdsLikeInsensitive(String value) {
            addCriterion("upper(related_order_ids) like", value.toUpperCase(), "relatedOrderIds");
            return (Criteria) this;
        }

        public Criteria andRelatedK3OrderIdsLikeInsensitive(String value) {
            addCriterion("upper(related_k3_order_ids) like", value.toUpperCase(), "relatedK3OrderIds");
            return (Criteria) this;
        }

        public Criteria andProRetNumLikeInsensitive(String value) {
            addCriterion("upper(pro_ret_num) like", value.toUpperCase(), "proRetNum");
            return (Criteria) this;
        }

        public Criteria andProSyncStatusLikeInsensitive(String value) {
            addCriterion("upper(pro_sync_status) like", value.toUpperCase(), "proSyncStatus");
            return (Criteria) this;
        }

        public Criteria andProSubmitAccountStatusLikeInsensitive(String value) {
            addCriterion("upper(pro_submit_account_status) like", value.toUpperCase(), "proSubmitAccountStatus");
            return (Criteria) this;
        }

        public Criteria andSyncK3UserNameLikeInsensitive(String value) {
            addCriterion("upper(sync_k3_user_name) like", value.toUpperCase(), "syncK3UserName");
            return (Criteria) this;
        }

        public Criteria andCommitK3UserNameLikeInsensitive(String value) {
            addCriterion("upper(commit_k3_user_name) like", value.toUpperCase(), "commitK3UserName");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptLikeInsensitive(String value) {
            addCriterion("upper(material_dept) like", value.toUpperCase(), "materialDept");
            return (Criteria) this;
        }

        public Criteria andProDataCodeLikeInsensitive(String value) {
            addCriterion("upper(pro_data_code) like", value.toUpperCase(), "proDataCode");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Tue Apr 25 14:19:25 CST 2023
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        private K3syncStatisProcityExample example;

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        protected Criteria(K3syncStatisProcityExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        public K3syncStatisProcityExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Tue Apr 25 14:19:25 CST 2023
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Tue Apr 25 14:19:25 CST 2023
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Tue Apr 25 14:19:25 CST 2023
         */
        void example(com.chinamobile.iot.sc.pojo.K3syncStatisProcityExample example);
    }
}