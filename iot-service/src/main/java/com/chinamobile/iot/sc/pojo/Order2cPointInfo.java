package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 订单积分表
 *
 * <AUTHOR>
public class Order2cPointInfo implements Serializable {
    /**
     *
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    private String id;

    /**
     * 原子订单id
     *
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    private String atomOrderId;

    /**
     * 订单编号
     *
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    private String orderId;

    /**
     * 积分供应商id
     *
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    private String supplierId;

    /**
     * 合伙人类型  0--客户经理; 1--一级分销员； 2--二级分销员
     *
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    private String distributorType;

    /**
     * 合伙人用户id,对应user_retail表id
     *
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    private String distributorUserId;

    /**
     * 积分，单位厘
     *
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    private Long point;

    /**
     * 创建时间（
     *
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    private Date createTime;

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..order_2c_point_info.id
     *
     * @return the value of supply_chain..order_2c_point_info.id
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public Order2cPointInfo withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_point_info.id
     *
     * @param id the value for supply_chain..order_2c_point_info.id
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_point_info.atom_order_id
     *
     * @return the value of supply_chain..order_2c_point_info.atom_order_id
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public String getAtomOrderId() {
        return atomOrderId;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public Order2cPointInfo withAtomOrderId(String atomOrderId) {
        this.setAtomOrderId(atomOrderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_point_info.atom_order_id
     *
     * @param atomOrderId the value for supply_chain..order_2c_point_info.atom_order_id
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public void setAtomOrderId(String atomOrderId) {
        this.atomOrderId = atomOrderId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_point_info.order_id
     *
     * @return the value of supply_chain..order_2c_point_info.order_id
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public Order2cPointInfo withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_point_info.order_id
     *
     * @param orderId the value for supply_chain..order_2c_point_info.order_id
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_point_info.supplier_id
     *
     * @return the value of supply_chain..order_2c_point_info.supplier_id
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public String getSupplierId() {
        return supplierId;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public Order2cPointInfo withSupplierId(String supplierId) {
        this.setSupplierId(supplierId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_point_info.supplier_id
     *
     * @param supplierId the value for supply_chain..order_2c_point_info.supplier_id
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_point_info.distributor_type
     *
     * @return the value of supply_chain..order_2c_point_info.distributor_type
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public String getDistributorType() {
        return distributorType;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public Order2cPointInfo withDistributorType(String distributorType) {
        this.setDistributorType(distributorType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_point_info.distributor_type
     *
     * @param distributorType the value for supply_chain..order_2c_point_info.distributor_type
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public void setDistributorType(String distributorType) {
        this.distributorType = distributorType;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_point_info.distributor_user_id
     *
     * @return the value of supply_chain..order_2c_point_info.distributor_user_id
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public String getDistributorUserId() {
        return distributorUserId;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public Order2cPointInfo withDistributorUserId(String distributorUserId) {
        this.setDistributorUserId(distributorUserId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_point_info.distributor_user_id
     *
     * @param distributorUserId the value for supply_chain..order_2c_point_info.distributor_user_id
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public void setDistributorUserId(String distributorUserId) {
        this.distributorUserId = distributorUserId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_point_info.point
     *
     * @return the value of supply_chain..order_2c_point_info.point
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public Long getPoint() {
        return point;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public Order2cPointInfo withPoint(Long point) {
        this.setPoint(point);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_point_info.point
     *
     * @param point the value for supply_chain..order_2c_point_info.point
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public void setPoint(Long point) {
        this.point = point;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_point_info.create_time
     *
     * @return the value of supply_chain..order_2c_point_info.create_time
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public Order2cPointInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_point_info.create_time
     *
     * @param createTime the value for supply_chain..order_2c_point_info.create_time
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", atomOrderId=").append(atomOrderId);
        sb.append(", orderId=").append(orderId);
        sb.append(", supplierId=").append(supplierId);
        sb.append(", distributorType=").append(distributorType);
        sb.append(", distributorUserId=").append(distributorUserId);
        sb.append(", point=").append(point);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Order2cPointInfo other = (Order2cPointInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAtomOrderId() == null ? other.getAtomOrderId() == null : this.getAtomOrderId().equals(other.getAtomOrderId()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getSupplierId() == null ? other.getSupplierId() == null : this.getSupplierId().equals(other.getSupplierId()))
            && (this.getDistributorType() == null ? other.getDistributorType() == null : this.getDistributorType().equals(other.getDistributorType()))
            && (this.getDistributorUserId() == null ? other.getDistributorUserId() == null : this.getDistributorUserId().equals(other.getDistributorUserId()))
            && (this.getPoint() == null ? other.getPoint() == null : this.getPoint().equals(other.getPoint()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()));
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAtomOrderId() == null) ? 0 : getAtomOrderId().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getSupplierId() == null) ? 0 : getSupplierId().hashCode());
        result = prime * result + ((getDistributorType() == null) ? 0 : getDistributorType().hashCode());
        result = prime * result + ((getDistributorUserId() == null) ? 0 : getDistributorUserId().hashCode());
        result = prime * result + ((getPoint() == null) ? 0 : getPoint().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:35:07 CST 2022
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        atomOrderId("atom_order_id", "atomOrderId", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        supplierId("supplier_id", "supplierId", "VARCHAR", false),
        distributorType("distributor_type", "distributorType", "VARCHAR", false),
        distributorUserId("distributor_user_id", "distributorUserId", "VARCHAR", false),
        point("point", "point", "BIGINT", false),
        createTime("create_time", "createTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:35:07 CST 2022
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}