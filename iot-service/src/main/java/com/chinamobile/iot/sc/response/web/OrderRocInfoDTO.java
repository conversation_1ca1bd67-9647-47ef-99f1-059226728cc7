package com.chinamobile.iot.sc.response.web;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/12/20 13:58
 * @Description:
 */
@Data
public class OrderRocInfoDTO {

    private String id;

    private String refundOrderId;
    private String refundsType;
    /**
     * 原因
     */
    private String reason;
    /**
     * 备注
     */
    private String remark;
    /**
     * 内部退款状态 1 通过 2 不通过 3 取消
     */
    private Integer orderStatus;

    /**
     * 退款、售后状态描述
     */
    private String orderStatusDescribe;
    /**
     * 订单号
     */
    private String orderId;
    /**
     * 原子订单ID
     */
    private String atomOrderId;
    /**
     * 商品名称(规格)
     */
    private String skuOfferingName;
    /**
     * 原子商品名称
     */
    private String atomOfferingName;
    /**
     * 原子商品类型
     */
    private String atomOfferingClass;
    /**
     * SPU一级销售目录
     */
    private String spuOfferingClass;
    /**
     * 型号
     */
    private String model;
    /**
     * 颜色
     */
    private String color;
    /**
     * 订购数量 skuQuantity*atomQuantity
     */
    private Integer quantity;
    /**
     * 原子商品结算价
     */
    private Long atomPrice;


    /**
     * 订购数量(规格)
     */
    private Integer skuQuantity;
    /**
     * 价格(规格)
     */
    private String skuPrice;

    /**
     * 合作伙伴ID
     */
    private String cooperatorId;
    /**
     * 合作伙伴姓名
     */
    private String cooperatorName;
    /**
     * 合作伙伴名
     */
    private String partnerName;
//    /**
//     * 物流单号
//     */
//    private String logisCode;
//    /**
//     * 物流服务商
//     */
//    private String supplierName;
    /**
     * 图片数组
     */
    private List<String> pictures;
    /**
     * 商品组/销售商品编码
     */
    private String  spuOfferingCode;

    /**
     *商品组/销售商品名称
     */
    private String  spuOfferingName;

    /**
     *商品编码（规格）
     */
    private String  skuOfferingCode;

    /**
     * 是否是湖南合同履约
     */
    private Boolean isHunanA07;

    /**
     * 退款数量
     */
    private Integer refundsNumber;

    /**
     * 退款金额
     */
    private Long returnPrice;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 图片外部链接路径
     */
    private String url;

    /**
     * 图片内部路径
     */
    private String imgUrl;

    /**
     * 是否为含卡终端（卡+X有值，其他无值）
     */
    private Boolean hasCard;

    /**
     * 催单次数
     */
    private Integer reminderCount;
}
