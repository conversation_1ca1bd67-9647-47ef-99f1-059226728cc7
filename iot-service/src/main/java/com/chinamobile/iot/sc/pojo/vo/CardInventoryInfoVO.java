package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON>
 * @date : 2024/12/9 10:48
 * @description:
 **/
@Data
public class CardInventoryInfoVO {

    private String id;

    /**
     * 归属卡服务商EC编码
     */
    private String custCode;

    /**
     * 归属卡服务商EC名称
     */
    private String custName;

    /**
     * 开卡模板编码
     *
     */
    private String templateId;

    /**
     * 开卡模板名称
     *
     */
    private String templateName;

    /**
     * 客户归属省份
     */
    private String beId;

    /**
     * 省份名称
     */
    private String proviceName;

    /**
     * 客户归属地市
     *
     */
    private String regionId;

    /**
     * 地市名称
     */
    private String regionName;

    /**
     * 卡片类型0：插拔卡  1：贴片卡  2：M2M芯片非空写卡 3: M2M芯片空写卡
     */
    private String cardType;

    private String cardTypeName;

    /**
     * 预占数量
     */
    private Integer reserveQuatity;

    /**
     * 当前库存数
     *
     */
    private Integer currentInventory;

    /**
     * 总库存数
     *
     */
    private Integer totalInventory;

    /**
     * 商品原子数
     */
    private Integer atomInventory;

    /**
     * 库存预警值
     *
     */
    private Integer inventoryThreshold;



    /**
     * 库存状态：0：短缺，1：充足
     */
    private String inventoryStatus;
}
