package com.chinamobile.iot.sc.pojo.dto.gio;

import com.chinamobile.iot.sc.pojo.Order2cAgentInfo;
import com.chinamobile.iot.sc.pojo.Order2cAtomInfo;
import com.chinamobile.iot.sc.request.order2c.CouponInfoDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class GioOrderCreatedOriginDTO {


    private String orderId;

    private String custMgPhone;
    private String provinceOrgName;
    private Integer orderStatus;
    private String spuOfferingClass;
    private String saleOrderType;
    private Date orderStatusTime;
    private String createTime;
    private Date billNoTime;
    private String sendGoodsTime;
    private String receiveOrderTime;
    private String valetOrderCompleteTime;
    private String arrivalTime;
    private String atomOfferingClass;
    private String expensesPrice;
    private Integer orderQuantity;
    private String expensesTerm;


    private String createOperUserId;
    private String createOperCode;
    private String custName;
    private String custUserId;
    private String orderType;
    private String orderingChannelName;

    private String addr1;
    private String addr2;
    private String addr3;

    private String orderGrid;
    private String custCode;
    private String totalPrice;
    private String businessCode;
    private String beId;
    private String location;
    private String regionID;
    private List<Order2cAtomInfo> order2cAtomInfoList;
    private List<CouponInfoDTO> couponInfoList;
    private String deductPrice;
    private List<Order2cAgentInfo> agentInfoList;



}
