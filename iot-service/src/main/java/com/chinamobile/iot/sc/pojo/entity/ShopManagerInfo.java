package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 商城客户经理信息表
 *
 * <AUTHOR>
public class ShopManagerInfo implements Serializable {
    /**
     *
     * Corresponding to the database column supply_chain..shop_manager_info.id
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private String id;

    /**
     * 客户经理编码
     *
     * Corresponding to the database column supply_chain..shop_manager_info.create_oper_code
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private String createOperCode;

    /**
     * 客户经理手机号
     *
     * Corresponding to the database column supply_chain..shop_manager_info.create_oper_phone
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private String createOperPhone;

    /**
     * 用户标识
     *
     * Corresponding to the database column supply_chain..shop_manager_info.user_id
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private String userId;

    /**
     * 客户经理姓名
     *
     * Corresponding to the database column supply_chain..shop_manager_info.customer_manager_name
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private String customerManagerName;

    /**
     * 客户经理工号
     *
     * Corresponding to the database column supply_chain..shop_manager_info.employee_num
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private String employeeNum;

    /**
     * 注册时间
     *
     * Corresponding to the database column supply_chain..shop_manager_info.register_date
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private Date registerDate;

    /**
     * 客户经理省份
     *
     * Corresponding to the database column supply_chain..shop_manager_info.be_id
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private String beId;

    /**
     * 客户经理省份名称
     *
     * Corresponding to the database column supply_chain..shop_manager_info.province_name
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private String provinceName;

    /**
     * 客户经理地市
     *
     * Corresponding to the database column supply_chain..shop_manager_info.location
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private String location;

    /**
     * 客户经理地市
     *
     * Corresponding to the database column supply_chain..shop_manager_info.city_name
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private String cityName;

    /**
     * 客户经理区县
     *
     * Corresponding to the database column supply_chain..shop_manager_info.region_id
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private String regionId;

    /**
     * 客户经理区县
     *
     * Corresponding to the database column supply_chain..shop_manager_info.region_name
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private String regionName;

    /**
     * 客户经理状态1：正常  2：挂起（账号冻结，可解冻转变为正常）3：禁用（账号注销，不能转变为正常，只能重新注册）
     *
     * Corresponding to the database column supply_chain..shop_manager_info.mrg_status
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private String mrgStatus;

    /**
     * 创建时间
     *
     * Corresponding to the database column supply_chain..shop_manager_info.create_time
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private Date createTime;

    /**
     * 更新时间
     *
     * Corresponding to the database column supply_chain..shop_manager_info.update_time
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private Date updateTime;

    /**
     * 网格名称
     *
     * Corresponding to the database column supply_chain..shop_manager_info.gridding_name
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private String griddingName;

    /**
     * 渠道类型：1：客户经理 2：社会渠道
     *
     * Corresponding to the database column supply_chain..shop_manager_info.channel_type
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private String channelType;

    /**
     * 客户经理类别：1：重客经理 2：网格经理
     *
     * Corresponding to the database column supply_chain..shop_manager_info.manager_category
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private String managerCategory;

    /**
     * 清洗时间
     *
     * Corresponding to the database column supply_chain..shop_manager_info.rinse_time
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private Date rinseTime;

    /**
     * 是否清洗过：0：没有 1：有
     *
     * Corresponding to the database column supply_chain..shop_manager_info.is_rinse
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private Boolean isRinse;

    /**
     * 是否商客经理：0：否 1：是
     *
     * Corresponding to the database column supply_chain..shop_manager_info.is_merchant
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private Boolean isMerchant;

    /**
     * 账号职责
     *
     * Corresponding to the database column supply_chain..shop_manager_info.account_duty
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private String accountDuty;

    /**
     * Corresponding to the database table supply_chain..shop_manager_info
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.id
     *
     * @return the value of supply_chain..shop_manager_info.id
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.id
     *
     * @param id the value for supply_chain..shop_manager_info.id
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.create_oper_code
     *
     * @return the value of supply_chain..shop_manager_info.create_oper_code
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public String getCreateOperCode() {
        return createOperCode;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withCreateOperCode(String createOperCode) {
        this.setCreateOperCode(createOperCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.create_oper_code
     *
     * @param createOperCode the value for supply_chain..shop_manager_info.create_oper_code
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setCreateOperCode(String createOperCode) {
        this.createOperCode = createOperCode == null ? null : createOperCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.create_oper_phone
     *
     * @return the value of supply_chain..shop_manager_info.create_oper_phone
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public String getCreateOperPhone() {
        return createOperPhone;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withCreateOperPhone(String createOperPhone) {
        this.setCreateOperPhone(createOperPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.create_oper_phone
     *
     * @param createOperPhone the value for supply_chain..shop_manager_info.create_oper_phone
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setCreateOperPhone(String createOperPhone) {
        this.createOperPhone = createOperPhone == null ? null : createOperPhone.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.user_id
     *
     * @return the value of supply_chain..shop_manager_info.user_id
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withUserId(String userId) {
        this.setUserId(userId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.user_id
     *
     * @param userId the value for supply_chain..shop_manager_info.user_id
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.customer_manager_name
     *
     * @return the value of supply_chain..shop_manager_info.customer_manager_name
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public String getCustomerManagerName() {
        return customerManagerName;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withCustomerManagerName(String customerManagerName) {
        this.setCustomerManagerName(customerManagerName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.customer_manager_name
     *
     * @param customerManagerName the value for supply_chain..shop_manager_info.customer_manager_name
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setCustomerManagerName(String customerManagerName) {
        this.customerManagerName = customerManagerName == null ? null : customerManagerName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.employee_num
     *
     * @return the value of supply_chain..shop_manager_info.employee_num
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public String getEmployeeNum() {
        return employeeNum;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withEmployeeNum(String employeeNum) {
        this.setEmployeeNum(employeeNum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.employee_num
     *
     * @param employeeNum the value for supply_chain..shop_manager_info.employee_num
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setEmployeeNum(String employeeNum) {
        this.employeeNum = employeeNum == null ? null : employeeNum.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.register_date
     *
     * @return the value of supply_chain..shop_manager_info.register_date
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public Date getRegisterDate() {
        return registerDate;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withRegisterDate(Date registerDate) {
        this.setRegisterDate(registerDate);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.register_date
     *
     * @param registerDate the value for supply_chain..shop_manager_info.register_date
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setRegisterDate(Date registerDate) {
        this.registerDate = registerDate;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.be_id
     *
     * @return the value of supply_chain..shop_manager_info.be_id
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public String getBeId() {
        return beId;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withBeId(String beId) {
        this.setBeId(beId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.be_id
     *
     * @param beId the value for supply_chain..shop_manager_info.be_id
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setBeId(String beId) {
        this.beId = beId == null ? null : beId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.province_name
     *
     * @return the value of supply_chain..shop_manager_info.province_name
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public String getProvinceName() {
        return provinceName;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withProvinceName(String provinceName) {
        this.setProvinceName(provinceName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.province_name
     *
     * @param provinceName the value for supply_chain..shop_manager_info.province_name
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName == null ? null : provinceName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.location
     *
     * @return the value of supply_chain..shop_manager_info.location
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public String getLocation() {
        return location;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withLocation(String location) {
        this.setLocation(location);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.location
     *
     * @param location the value for supply_chain..shop_manager_info.location
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setLocation(String location) {
        this.location = location == null ? null : location.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.city_name
     *
     * @return the value of supply_chain..shop_manager_info.city_name
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public String getCityName() {
        return cityName;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withCityName(String cityName) {
        this.setCityName(cityName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.city_name
     *
     * @param cityName the value for supply_chain..shop_manager_info.city_name
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.region_id
     *
     * @return the value of supply_chain..shop_manager_info.region_id
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public String getRegionId() {
        return regionId;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withRegionId(String regionId) {
        this.setRegionId(regionId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.region_id
     *
     * @param regionId the value for supply_chain..shop_manager_info.region_id
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setRegionId(String regionId) {
        this.regionId = regionId == null ? null : regionId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.region_name
     *
     * @return the value of supply_chain..shop_manager_info.region_name
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public String getRegionName() {
        return regionName;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withRegionName(String regionName) {
        this.setRegionName(regionName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.region_name
     *
     * @param regionName the value for supply_chain..shop_manager_info.region_name
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setRegionName(String regionName) {
        this.regionName = regionName == null ? null : regionName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.mrg_status
     *
     * @return the value of supply_chain..shop_manager_info.mrg_status
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public String getMrgStatus() {
        return mrgStatus;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withMrgStatus(String mrgStatus) {
        this.setMrgStatus(mrgStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.mrg_status
     *
     * @param mrgStatus the value for supply_chain..shop_manager_info.mrg_status
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setMrgStatus(String mrgStatus) {
        this.mrgStatus = mrgStatus == null ? null : mrgStatus.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.create_time
     *
     * @return the value of supply_chain..shop_manager_info.create_time
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.create_time
     *
     * @param createTime the value for supply_chain..shop_manager_info.create_time
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.update_time
     *
     * @return the value of supply_chain..shop_manager_info.update_time
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.update_time
     *
     * @param updateTime the value for supply_chain..shop_manager_info.update_time
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.gridding_name
     *
     * @return the value of supply_chain..shop_manager_info.gridding_name
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public String getGriddingName() {
        return griddingName;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withGriddingName(String griddingName) {
        this.setGriddingName(griddingName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.gridding_name
     *
     * @param griddingName the value for supply_chain..shop_manager_info.gridding_name
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setGriddingName(String griddingName) {
        this.griddingName = griddingName == null ? null : griddingName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.channel_type
     *
     * @return the value of supply_chain..shop_manager_info.channel_type
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public String getChannelType() {
        return channelType;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withChannelType(String channelType) {
        this.setChannelType(channelType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.channel_type
     *
     * @param channelType the value for supply_chain..shop_manager_info.channel_type
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setChannelType(String channelType) {
        this.channelType = channelType == null ? null : channelType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.manager_category
     *
     * @return the value of supply_chain..shop_manager_info.manager_category
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public String getManagerCategory() {
        return managerCategory;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withManagerCategory(String managerCategory) {
        this.setManagerCategory(managerCategory);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.manager_category
     *
     * @param managerCategory the value for supply_chain..shop_manager_info.manager_category
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setManagerCategory(String managerCategory) {
        this.managerCategory = managerCategory == null ? null : managerCategory.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.rinse_time
     *
     * @return the value of supply_chain..shop_manager_info.rinse_time
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public Date getRinseTime() {
        return rinseTime;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withRinseTime(Date rinseTime) {
        this.setRinseTime(rinseTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.rinse_time
     *
     * @param rinseTime the value for supply_chain..shop_manager_info.rinse_time
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setRinseTime(Date rinseTime) {
        this.rinseTime = rinseTime;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.is_rinse
     *
     * @return the value of supply_chain..shop_manager_info.is_rinse
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public Boolean getIsRinse() {
        return isRinse;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withIsRinse(Boolean isRinse) {
        this.setIsRinse(isRinse);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.is_rinse
     *
     * @param isRinse the value for supply_chain..shop_manager_info.is_rinse
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setIsRinse(Boolean isRinse) {
        this.isRinse = isRinse;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.is_merchant
     *
     * @return the value of supply_chain..shop_manager_info.is_merchant
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public Boolean getIsMerchant() {
        return isMerchant;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withIsMerchant(Boolean isMerchant) {
        this.setIsMerchant(isMerchant);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.is_merchant
     *
     * @param isMerchant the value for supply_chain..shop_manager_info.is_merchant
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setIsMerchant(Boolean isMerchant) {
        this.isMerchant = isMerchant;
    }

    /**
     * This method returns the value of the database column supply_chain..shop_manager_info.account_duty
     *
     * @return the value of supply_chain..shop_manager_info.account_duty
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public String getAccountDuty() {
        return accountDuty;
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public ShopManagerInfo withAccountDuty(String accountDuty) {
        this.setAccountDuty(accountDuty);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..shop_manager_info.account_duty
     *
     * @param accountDuty the value for supply_chain..shop_manager_info.account_duty
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public void setAccountDuty(String accountDuty) {
        this.accountDuty = accountDuty == null ? null : accountDuty.trim();
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", createOperCode=").append(createOperCode);
        sb.append(", createOperPhone=").append(createOperPhone);
        sb.append(", userId=").append(userId);
        sb.append(", customerManagerName=").append(customerManagerName);
        sb.append(", employeeNum=").append(employeeNum);
        sb.append(", registerDate=").append(registerDate);
        sb.append(", beId=").append(beId);
        sb.append(", provinceName=").append(provinceName);
        sb.append(", location=").append(location);
        sb.append(", cityName=").append(cityName);
        sb.append(", regionId=").append(regionId);
        sb.append(", regionName=").append(regionName);
        sb.append(", mrgStatus=").append(mrgStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", griddingName=").append(griddingName);
        sb.append(", channelType=").append(channelType);
        sb.append(", managerCategory=").append(managerCategory);
        sb.append(", rinseTime=").append(rinseTime);
        sb.append(", isRinse=").append(isRinse);
        sb.append(", isMerchant=").append(isMerchant);
        sb.append(", accountDuty=").append(accountDuty);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ShopManagerInfo other = (ShopManagerInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCreateOperCode() == null ? other.getCreateOperCode() == null : this.getCreateOperCode().equals(other.getCreateOperCode()))
            && (this.getCreateOperPhone() == null ? other.getCreateOperPhone() == null : this.getCreateOperPhone().equals(other.getCreateOperPhone()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getCustomerManagerName() == null ? other.getCustomerManagerName() == null : this.getCustomerManagerName().equals(other.getCustomerManagerName()))
            && (this.getEmployeeNum() == null ? other.getEmployeeNum() == null : this.getEmployeeNum().equals(other.getEmployeeNum()))
            && (this.getRegisterDate() == null ? other.getRegisterDate() == null : this.getRegisterDate().equals(other.getRegisterDate()))
            && (this.getBeId() == null ? other.getBeId() == null : this.getBeId().equals(other.getBeId()))
            && (this.getProvinceName() == null ? other.getProvinceName() == null : this.getProvinceName().equals(other.getProvinceName()))
            && (this.getLocation() == null ? other.getLocation() == null : this.getLocation().equals(other.getLocation()))
            && (this.getCityName() == null ? other.getCityName() == null : this.getCityName().equals(other.getCityName()))
            && (this.getRegionId() == null ? other.getRegionId() == null : this.getRegionId().equals(other.getRegionId()))
            && (this.getRegionName() == null ? other.getRegionName() == null : this.getRegionName().equals(other.getRegionName()))
            && (this.getMrgStatus() == null ? other.getMrgStatus() == null : this.getMrgStatus().equals(other.getMrgStatus()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getGriddingName() == null ? other.getGriddingName() == null : this.getGriddingName().equals(other.getGriddingName()))
            && (this.getChannelType() == null ? other.getChannelType() == null : this.getChannelType().equals(other.getChannelType()))
            && (this.getManagerCategory() == null ? other.getManagerCategory() == null : this.getManagerCategory().equals(other.getManagerCategory()))
            && (this.getRinseTime() == null ? other.getRinseTime() == null : this.getRinseTime().equals(other.getRinseTime()))
            && (this.getIsRinse() == null ? other.getIsRinse() == null : this.getIsRinse().equals(other.getIsRinse()))
            && (this.getIsMerchant() == null ? other.getIsMerchant() == null : this.getIsMerchant().equals(other.getIsMerchant()))
            && (this.getAccountDuty() == null ? other.getAccountDuty() == null : this.getAccountDuty().equals(other.getAccountDuty()));
    }

    /**
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCreateOperCode() == null) ? 0 : getCreateOperCode().hashCode());
        result = prime * result + ((getCreateOperPhone() == null) ? 0 : getCreateOperPhone().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getCustomerManagerName() == null) ? 0 : getCustomerManagerName().hashCode());
        result = prime * result + ((getEmployeeNum() == null) ? 0 : getEmployeeNum().hashCode());
        result = prime * result + ((getRegisterDate() == null) ? 0 : getRegisterDate().hashCode());
        result = prime * result + ((getBeId() == null) ? 0 : getBeId().hashCode());
        result = prime * result + ((getProvinceName() == null) ? 0 : getProvinceName().hashCode());
        result = prime * result + ((getLocation() == null) ? 0 : getLocation().hashCode());
        result = prime * result + ((getCityName() == null) ? 0 : getCityName().hashCode());
        result = prime * result + ((getRegionId() == null) ? 0 : getRegionId().hashCode());
        result = prime * result + ((getRegionName() == null) ? 0 : getRegionName().hashCode());
        result = prime * result + ((getMrgStatus() == null) ? 0 : getMrgStatus().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getGriddingName() == null) ? 0 : getGriddingName().hashCode());
        result = prime * result + ((getChannelType() == null) ? 0 : getChannelType().hashCode());
        result = prime * result + ((getManagerCategory() == null) ? 0 : getManagerCategory().hashCode());
        result = prime * result + ((getRinseTime() == null) ? 0 : getRinseTime().hashCode());
        result = prime * result + ((getIsRinse() == null) ? 0 : getIsRinse().hashCode());
        result = prime * result + ((getIsMerchant() == null) ? 0 : getIsMerchant().hashCode());
        result = prime * result + ((getAccountDuty() == null) ? 0 : getAccountDuty().hashCode());
        return result;
    }

    /**
     * corresponding to the database table supply_chain..shop_manager_info
     *
     * @mbg.generated Tue Jun 18 17:11:58 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        createOperCode("create_oper_code", "createOperCode", "VARCHAR", false),
        createOperPhone("create_oper_phone", "createOperPhone", "VARCHAR", false),
        userId("user_id", "userId", "VARCHAR", false),
        customerManagerName("customer_manager_name", "customerManagerName", "VARCHAR", false),
        employeeNum("employee_num", "employeeNum", "VARCHAR", false),
        registerDate("register_date", "registerDate", "TIMESTAMP", false),
        beId("be_id", "beId", "VARCHAR", false),
        provinceName("province_name", "provinceName", "VARCHAR", false),
        location("location", "location", "VARCHAR", false),
        cityName("city_name", "cityName", "VARCHAR", false),
        regionId("region_id", "regionId", "VARCHAR", false),
        regionName("region_name", "regionName", "VARCHAR", false),
        mrgStatus("mrg_status", "mrgStatus", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        griddingName("gridding_name", "griddingName", "VARCHAR", false),
        channelType("channel_type", "channelType", "VARCHAR", false),
        managerCategory("manager_category", "managerCategory", "VARCHAR", false),
        rinseTime("rinse_time", "rinseTime", "TIMESTAMP", false),
        isRinse("is_rinse", "isRinse", "BIT", false),
        isMerchant("is_merchant", "isMerchant", "BIT", false),
        accountDuty("account_duty", "accountDuty", "VARCHAR", false);

        /**
         * Corresponding to the database table supply_chain..shop_manager_info
         *
         * @mbg.generated Tue Jun 18 17:11:58 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..shop_manager_info
         *
         * @mbg.generated Tue Jun 18 17:11:58 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..shop_manager_info
         *
         * @mbg.generated Tue Jun 18 17:11:58 CST 2024
         */
        private final String column;

        /**
         * Corresponding to the database table supply_chain..shop_manager_info
         *
         * @mbg.generated Tue Jun 18 17:11:58 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         * Corresponding to the database table supply_chain..shop_manager_info
         *
         * @mbg.generated Tue Jun 18 17:11:58 CST 2024
         */
        private final String javaProperty;

        /**
         * Corresponding to the database table supply_chain..shop_manager_info
         *
         * @mbg.generated Tue Jun 18 17:11:58 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Jun 18 17:11:58 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Jun 18 17:11:58 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Jun 18 17:11:58 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Jun 18 17:11:58 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Jun 18 17:11:58 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Jun 18 17:11:58 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Jun 18 17:11:58 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Jun 18 17:11:58 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Jun 18 17:11:58 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Jun 18 17:11:58 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Jun 18 17:11:58 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}