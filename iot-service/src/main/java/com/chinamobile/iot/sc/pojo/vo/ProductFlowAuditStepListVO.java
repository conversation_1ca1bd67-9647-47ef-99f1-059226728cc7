package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;

/**
 * created by l<PERSON><PERSON>ng on 2024/5/30 10:52
 */
@Data
public class ProductFlowAuditStepListVO {

    /**
     * 步骤id
     */
    private String id;


    /**
     * 步骤序号，从1开始叠加
     */
    private Integer stepIndex;

    /**
     * 步骤名称
     */
    private String stepName;

    /**
     * 是否高亮显示（当前环节以及处理过的环节需要高亮）
     */
    private boolean highLight;

    /**
     * 可否转办
     */
    private boolean allowRedirect;

    /**
     * 是否已转办
     */
    private boolean redirected;

    /**
     * 可否知悉
     */
    private boolean allowKnown;

    /**
     * 是否已知悉
     */
    private boolean known;

    /**
     * 是否新增限制
     */
    private boolean allowLimit;

    /**
     * 限制条件枚举值: 1.供应额度不足
     */
    private Integer limitId;

    private String limitName;

    /**
     * 是否已限制
     */
    private boolean limited;

}
