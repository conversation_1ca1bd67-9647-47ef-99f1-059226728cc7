package com.chinamobile.iot.sc.pojo.vo;

import com.chinamobile.iot.sc.constant.productflow.ProductOperateTypeEnum;
import com.chinamobile.iot.sc.pojo.ProductFlowInstance;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceAtom;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceAttachment;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceConfig;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceSku;
import com.chinamobile.iot.sc.pojo.ProductFlowInstanceSpu;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * created by liuxiang on 2024/3/6 17:22
 */
@Data
public class ProductFlowInstanceDetailVO {

    /**
     * 基本信息
     */
    private BaseInfo baseInfo;

    /**
     * SPU信息
     */
    private SpuInfo spuInfo;

    /**
     * sku信息
     */
    private List<SkuInfo> skuInfoList;

    /**
     * 原子信息
     */
    private List<AtomInfo> atomInfoList;

    /**
     * 附件信息
     */
    private List<AttachmentInfo> attachmentInfoList;

    /**
     * 配置信息
     */
    private ConfigInfo configInfo;

    /**
     * 审核记录
     */
    private List<AuditInfo> auditInfoList;

    /**
     * 当前处理信息
     */
    private TaskInfo taskInfo;

    @Data
    public static class BaseInfo{

        /**
         * 1 - SPU上架， 2-SKU上架
         */
        private Integer shelfType;

        /**
         * 流程实例id
         */
        private String id;

        /**
         * 流程id
         */
        private String flowId;

        /**
         * 流程实例编号
         */
        private String flowInstanceNumber;

        /**
         * 创建人姓名
         */
        private String creatorName;

        /**
         * 创建时间
         */
        private Date createTime;

        /**
         * 产品标准 1-标准类 2-方案类
         */
        private Integer productStandard;

        /**
         * 产品类别 1-省框 2-省内 3-DICT 4-合同履约 5-联合销售
         */
        private Integer productType;

        /**
         * 运营类型
         */
        private Integer operatorType;

        /**
         * 流程图文件二进制数据
         */
//        private byte[] image;

        /**
         * 下架理由
         */
        private String offShelfReason;

        /**
         * 改动的字段，逗号分隔
         */
        private String changeList;
    }

    @Data
    public static class SpuInfo{

        private String id;

        /**
         * 商品链接
         */
        private String url;

        /**
         * spu编码
         */
        private String spuCode;

        /**
         * 上架类目id
         */
        private String shelfCatagoryId;

        /**
         * 上架类目名称
         */
        private String shelfCatagoryName;

        /**
         * 导航目录信息
         */
        private List<NavigationDirectory> navigationDirectoryList;

        /**
         * spu名称
         */
        private String spuName;

        /**
         * 产品经理
         */
        private String manager;

        /**
         * 商品简介
         */
        private String productDesc;

        /**
         * 典型应用领域
         */
        private String applicationArea;

        /**
         * 是否隐秘上架
         */
        private String isHiddenShelf;

        /**
         * 服务商
         */
        private String spuServiceProvider;

        /**
         * 销售标签，联合销售是联营，其他都是自营。不可修改
         */
        private String saleTag;

        /**
         * 映射检索词
         */
        private String searchWord;

        /**
         * SPU信息备注
         */
        private String spuRemark;

        /**
         * 售后订单管理员信息
         */
        private String aftermarketAdminInfo;

        /**
         * 产品管理部门
         */
        private String manageDepartment;
    }

    @Data
    public static class NavigationDirectory{

        private String id;

        private String name;

        private String parentId;

        //下级导航目录列表
        private List<NavigationDirectory> children;

    }

    @Data
    public static class SkuInfo{

        private String id;

        /**
         * sku编码
         */
        private String skuCode;

        /**
         * sku名称
         */
        private String skuName;

        /**
         * 商品规格简称
         */
        private String skuShortName;

        /**
         * 核心部件名称
         */
        private String keyCompomentName;

        /**
         * 核心部件及服务内容
         */
        private String keyComponentServiceInfo;

        /**
         * 销售价格，单位厘
         */
        private Long salePrice;

        /**
         * 原销售价格，单位厘
         */
        private Long oldSalePrice;

        /**
         * 销售最低价，单位厘
         */
        private Long saleMinPrice;
        /**
         * 原销售最低价，单位厘
         */
        private Long oldSaleMinPrice;

        /**
         * 销售最高价，单位厘
         */
        private Long saleMaxPrice;

        /**
         * 是否允许价格区间外销售
         */
        private String saleOutOfPriceRange;

        /**
         * 商品规格发布订购范围。填写格式：
         */
        private String saleProvinceCity;

        /**
         * 配送范围：
         */
        private String deliveryRange;

        /**
         * 游客/合作伙伴 是否可见
         */
        private String touristPartnerVisible;

        /**
         * 标准服务产品名称
         */
        private String standardProductName;

        /**
         * 产品属性，枚举值：自研、生态
         */
        private String standardProductAttribute;

        /**
         * 服务供应商
         */
        private String skuServiceProvider;

        /**
         * 产品管理部门
         */
        private String manageDepartment;

        /**
         * 标准服务产品经理
         */
        private String standardProductManager;

        /**
         * 接单账号
         */
        private String receiveOrderAccount;

        /**
         * 交付账号
         */
        private String deliverAccount;

        /**
         * 售后账号
         */
        private String aftermarketAccount;

        /**
         * 规格信息备注
         */
        private String skuRemark;

        /**
         * 省公司价格，单位厘
         */
        private Long provincePrice;

        /**
         * 商品套餐销售（实际销售产品）名称
         */
        private String productPackageSaleName;

        /**
         * 商品规格套餐销售产品（实际销售产品）及服务内容
         */
        private String productPackageServiceContent;

        /**
         * 发货接口人发货接口人
         */
        private String sendContactPerson;

        /**
         * 是否发酬金
         */
        private String hasRemuneration;

        /**
         * 酬金比例
         */
        private String remunerationPercent;

        /**
         * 合作厂商名
         */
        private String cooperateCompany;

        /**
         * 商城订单处理人(主)
         */
        private String orderMasterHandler;

        /**
         * 商城订单处理人(次)
         */
        private String orderSlaveHandler;

        /**
         * 起订量
         */
        private Integer minPurchaseNum;

    }

    @Data
    public static class AtomInfo{

        private String id;

        /**
         * 原子商品名称或硬件原子商品名称
         */
        private String atomName;

        /**
         * 销售价格，单位厘
         */
        private Long salePrice;

        /**
         * 历史销售价格
         */
        private Long oldSalePrice;

        /**
         * 销售最低价，单位厘
         */
        private Long saleMinPrice;

        /**
         * 销售最高价，单位厘
         */
        private Long saleMaxPrice;

        /**
         * 是否允许价格区间外销售
         */
        private String saleOutOfPriceRange;


        /**
         * 结算单价或硬件结算单价或省-专结算单价，单位厘
         */
        private Long settlePrice;

        /**
         * 原结算单价或硬件结算单价或省-专结算单价，单位厘
         */
        private Long oldSettlePrice;

        /**
         * 结算单价核对，单位厘
         */
        private Long settlePriceCheck;

        /**
         * 计量单位或硬件计量单位
         */
        private String unit;

        /**
         * 服务内容
         */
        private String serviceContent;

        /**
         * CMIOT账目项id
         */
        private String cmiotCostProjectId;

        /**
         * CMIOT账目项名称
         */
        private String cmiotCostProjectName;

        /**
         * 订购数量最小值
         */
        private Integer minPurchaseNum;

        /**
         * 省公司采购合同信息
         */
        private String provincePurchaseContract;

        /**
         * 物联网公司采购合同信息
         */
        private String iotPurchaseContract;

        /**
         * 物料编码
         */
        private String materialNum;

        /**
         * 原子信息备注
         */
        private String atomRemark;

        /**
         * 数量
         */
        private Integer atomQuantity;

        /**
         * 颜色
         */
        private String color;

        /**
         * 型号
         */
        private String model;

        /**
         * 硬件原子商品销售价格，单位厘
         */
        private Long hardwarePrice;

        /**
         * 原硬件原子商品销售价格，单位厘
         */
        private Long oldHardwarePrice;

        /**
         * 软件原子商品名称
         */
        private String softAtomName;

        /**
         * 软件结算单价，单位厘
         */
        private Long softSettlePrice;

        /**
         * 原软件结算单价，单位厘
         */
        private Long oldSoftSettlePrice;

        /**
         * 软件计量单位
         */
        private String softUnit;

        /**
         * 软件功能费商品数量
         */
        private Integer softQuantity;

        /**
         * 软件平台商品编码
         */
        private String softProductCode;

        /**
         * 不需结算
         */
        private String noSettlement;

        /**
         * 结算明细服务名称
         */
        private String settlementDetailName;

        /**
         * 交付周期
         */
        private String deliverPeriod;

        /**
         * 软件服务内容
         */
        private String softServiceContent;

        /**
         * 软件功能费销售单价，单位厘
         */
        private Long softPrice;

        /**
         * 原软件功能费销售单价，单位厘
         */
        private Long oldSoftPrice;

        /**
         * 软件功能费商品销售价格，单位厘
         */
        private Long softTotalPrice;

        /**
         * 原软件功能费商品销售价格，单位厘
         */
        private Long oldSoftTotalPrice;

        /**
         * （专-合）结算单价
         */
        private Long zhuanheSettlePrice;

        /**
         * 原（专-合）结算单价
         */
        private Long oldZhuanheSettlePrice;

        /**
         * 服务包名称
         */
        private String servicePackageName;

        /**
         * 服务产品合同信息（销售侧）
         */
        private String serviceContract;

        /**
         * 库存数
         */
        private Integer inventory;

    }

    @Data
    public static class AttachmentInfo{
        private String id;

        /**
         * 附件文件名
         */
        private String fileName;

        /**
         * 附件文件下载地址
         */
        private String fileUrl;

        /**
         * 附近类型
         */
        private Integer type;
    }

    @Data
    public static class ConfigInfo{

        private String id;

        /**
         * 配置备注
         */
        private String configRemark;

        /**
         * 标准服务名称
         */
        private String standardServiceName;

        /**
         * 实际产品名称
         */
        private String realProductName;

        /**
         * 产品属性
         */
        private String productProperty;

        /**
         * 产品部门
         */
        private String productDepartment;

        /**
         * 服务商名称
         */
        private String serviceProviderName;

        /**
         * 商城订单管理账号（主账号)
         */
        private String orderPartnerMasterAccount;

        /**
         * 商城订单处理人（次账号）
         */
        private String orderPartnerSlaveAccount;

        /**
         * 售前市场经理
         */
        private String beforeSaleManager;

        /**
         * 商品发货接口人
         */
        private String sendContactPerson;

        /**
         * 商品安装接口人
         */
        private String installContactPerson;

        /**
         * 物联卡套餐接口人
         */
        private String iotPackageContactPerson;

        /**
         * 商品软件权限开通接口人
         */
        private String softAuthContactPerson;

        /**
         * 商品售后接口人
         */
        private String afterSaleContactPerson;

        /**
         * 质保/售后细则
         */
        private String afterMarketRule;

        /**
         * 商品维修联系信息与地址
         */
        private String repairContactInfo;

        /**
         * 商品退货联系信息与地址
         */
        private String returnContactInfo;

        /**
         * 商品厂商信息
         */
        private String productCompanyInfo;

        /**
         * 商品通信方式
         */
        private String productCommunicationMethod;

        /**
         * 物联卡套餐说明
         */
        private String iotPackageInfo;

        /**
         * 硬件商品发货清单
         */
        private String hardwareSendList;

        /**
         * 商品参数信息
         */
        private String productParamInfo;

        /**
         * 商品发货地址
         */
        private String productSendAddress;

        /**
         * 硬件商品发货默认快递
         */
        private String hardwareExpress;

        /**
         * 商品发货时间信息
         */
        private String productSendTimeInfo;

        /**
         * 商品使用条件
         */
        private String productUseCondition;

        /**
         * 软件平台名称及介绍
         */
        private String softPlatformInfo;

        /**
         * 软件平台下载方式及地址
         */
        private String softPlatformDownloadInfo;

        /**
         * APP、小程序名称及介绍
         */
        private String appInfo;

        /**
         * APP、小程序下载方式及地址
         */
        private String appDownloadInfo;

        /**
         * 安装服务说明
         */
        private String installInfo;
    }

    @Data
    public static class AuditInfo{


        /**
         * 序号
         */
        Integer number;


        /**
         * 环节名称
         */
        private String stepName;

        /**
         * 环节处理人
         */
        private String assigneeName;
        /**
         * 处理意见
         */
        private String options;
        /**
         * 到达时间
         */
        private Date createTime;
        /**
         * 处理时间
         */
        private Date handleTime;

    }

    @Data
    public static class TaskInfo{

        /**
         * 当前环节序号
         */
        private Integer stepIndex;

        /**
         * 当前环节名称
         */
        private String stepName;

        /**
         * 提示语
         */
        private String tip;

        /**
         * 是否有通过和不通过按钮
         */
        private Boolean hasAuditButton;

        /**
         * 转办角色id,逗号分隔多个(流程环节不允许转办则为空)
         */
        private String redirectRoleId;

        /**
         * 知悉角色id，逗号分隔多个(流程环节不允许知悉则为空)
         */
        private String knownRoleId;

        /**
         * 限制id(流程环节不允许限制则为空)
         */
        private Integer limitId;

        /** 
         * 限制名称(流程环节不允许限制则为空)
         */
        private String limitName;

        /**
         * 通过后下一个环节名称
         */
        private String passNextStepName;

        /**
         * 通过后下一环节处理人角色id
         */
        private String passNextStepRoleId;

        /**
         * 驳回后下一个环节名称
         */
        private String rejectNextStepName;

        /**
         * 驳回后下一环节处理人角色id
         */
        private String rejectNextStepRoleId;

        /**
         * 通过后下下一个环节名称（用于限制的情况）
         */
        private String passNextNextStepName;

        /**
         * 通过后下下一环节处理人角色id（用于限制的情况）
         */
        private String passNextNextStepRoleId;
    }
}
