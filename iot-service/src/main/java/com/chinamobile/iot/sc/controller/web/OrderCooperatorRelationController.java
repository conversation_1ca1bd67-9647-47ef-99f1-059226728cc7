package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.entity.iot.OrderCooperatorInfoByGroupDTO;
import com.chinamobile.iot.sc.entity.iot.OrderCooperatorInfoByGroupParam;
import com.chinamobile.iot.sc.service.OrderCooperatorRelationService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/12
 * @description 订单合作伙伴关系controller类
 */
@RestController
@RequestMapping(value = "/osweb/orderCooperator")
public class OrderCooperatorRelationController {

    @Resource
    private OrderCooperatorRelationService orderCooperatorRelationService;

    /**
     * 获取组装后的原子订单和合作伙伴列表
     * @param orderCooperatorInfoByGroupParam
     * @return
     */
    @GetMapping(value = "/listCooperatorInfoByGroup")
    public BaseAnswer<List<OrderCooperatorInfoByGroupDTO>> listCooperatorInfoByGroup(OrderCooperatorInfoByGroupParam orderCooperatorInfoByGroupParam){
        List<OrderCooperatorInfoByGroupDTO> orderCooperatorInfoByGroupDTOList = orderCooperatorRelationService.listCooperatorInfoByGroup(orderCooperatorInfoByGroupParam);
        return new BaseAnswer<List<OrderCooperatorInfoByGroupDTO>>().setData(orderCooperatorInfoByGroupDTOList);
    }

}
