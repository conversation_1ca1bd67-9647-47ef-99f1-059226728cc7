package com.chinamobile.iot.sc.pojo.dto.gio;

import lombok.Data;

import java.util.List;

@Data
public class GioAtomDTO {

    private String item_id;
    private GioAtomAttr attrs;
    @Data
    public static class GioAtomAttr{
        private String item_id;                   // item id
        private String offering_code;
        private String offering_name;             // offering _name
        private String offering_version;          // offering version
        private String offering_class;            // offering_class
        private String charge_code_name;          // charge_code name
        private String charge_code;               // charge_code
        private String ext_soft_offering_code;    // ext soft offering_cod e
        private String ext_hard_offering_code;    // ext hard offering_co de
        private String offering_quantity;         // offering_quantity
        private String offering_unit;             // offering_unit
        private String offering_model;            // offering model
        private String offering_color;            // offering_color
        private String offering_price;            // offering_price
        private String offering_settlement_price; // offering_settlement price
        private String service_code;              // service code
        private String service_name;              // service name
        private String product_name;              // product name
        private String product_department;        // product department
        private String product_attributes;        // product attributes
        private String is_delete_offering;        // is_delete_offering
    }


}
