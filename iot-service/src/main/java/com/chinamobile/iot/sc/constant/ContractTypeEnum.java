package com.chinamobile.iot.sc.constant;

public enum ContractTypeEnum {

    SALE(1,"销售合同"),
    PROCURE(2,"采购合同")
    ;

    public Integer code;
    public String name;


    ContractTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static Boolean contains(Integer code){
        if(code == null){
            return false;
        }
        ContractTypeEnum[] values = ContractTypeEnum.values();
        for (ContractTypeEnum value : values) {
            if(value.code.intValue() == code.intValue()){
                return true;
            }
        }
        return false;
    }

    public static ContractTypeEnum fromCode(Integer code){
        if(code == null){
            return null;
        }
        ContractTypeEnum[] values = ContractTypeEnum.values();
        for (ContractTypeEnum value : values) {
            if(value.code.intValue() == code.intValue()){
                return value;
            }
        }
        return null;
    }
}
