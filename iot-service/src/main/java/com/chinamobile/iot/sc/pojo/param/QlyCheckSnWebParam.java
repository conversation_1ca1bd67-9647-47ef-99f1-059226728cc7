package com.chinamobile.iot.sc.pojo.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/6
 * @description 千里眼校验设备SN参数
 */
@Data
public class QlyCheckSnWebParam {

    /**
     * 订单id
     */
    @NotBlank(message = "订单ID不能为空")
    private String orderId;

    /**
     * 设备SN
     */
    @NotEmpty(message = "sn数组不能为空")
    private List<String> deviceSns;
}
