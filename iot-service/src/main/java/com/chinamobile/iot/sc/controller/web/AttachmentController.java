package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.service.IAttachmentService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/17 11:13
 * @description TODO
 */
@RestController
@RequestMapping("/osweb")
public class AttachmentController {

    @Resource
    private IAttachmentService attachmentService;

    /**
     * 下载微前端缓存文件
     * @param filename
     * @param response
     */
    @GetMapping("/file/attachment/download/{filename}")
    public void downloadMicroFrontedFile(@PathVariable(name = "filename") String filename, HttpServletResponse response) {
        attachmentService.downloadAttachment(filename, response);
    }

}
