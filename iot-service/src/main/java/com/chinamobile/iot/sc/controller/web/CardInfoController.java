package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.constant.CardStatusEnum;
import com.chinamobile.iot.sc.constant.CardTypeEnum;
import com.chinamobile.iot.sc.constant.SellStatusEnum;
import com.chinamobile.iot.sc.constant.TerminalTypeEnum;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.CardInfoParam;
import com.chinamobile.iot.sc.pojo.param.OrderProductTemplateCardListParam;
import com.chinamobile.iot.sc.pojo.param.SetStatusCanSellParam;
import com.chinamobile.iot.sc.pojo.vo.*;
import com.chinamobile.iot.sc.service.CardInfoService;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/8
 * @description 卡信息controller类
 */
@RestController
@RequestMapping(value = "/osweb/cardinfo")
public class CardInfoController {

    @Resource
    private CardInfoService cardInfoService;

    /**
     * 分页获取码号信息
     *
     * @param cardInfoParam
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping(value = "/pageCardInfo")
    public BaseAnswer<PageData<CardInfoVO>> pageCardInfo(CardInfoParam cardInfoParam,
                                                         @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {

        BaseAnswer baseAnswer = new BaseAnswer();
        PageData<CardInfoVO> pageData = cardInfoService.pageCardInfo(cardInfoParam, loginIfo4Redis);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }

    /**
     * 导出码号信息
     *
     * @param cardInfoParam
     * @param loginIfo4Redis
     * @param response
     * @throws IOException
     */
    @GetMapping(value = "/exportCardInfo")
    public void exportCardInfo(CardInfoParam cardInfoParam,
                               @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                               HttpServletResponse response) throws Exception {
        cardInfoService.exportCardInfo(cardInfoParam, loginIfo4Redis, response);
    }



    /**
     * 获取卡片类型
     * @return
     */
    @GetMapping(value = "/listCardType")
    public BaseAnswer<List<CardTypeVO>> listCardType(){
        List<CardTypeVO> cardTypeList = new ArrayList();
        CardTypeVO cardTypeAll = new CardTypeVO();
        cardTypeAll.setCardType("");
        cardTypeAll.setCardDesc("全部");
        cardTypeList.add(cardTypeAll);
        for (CardTypeEnum cardType:CardTypeEnum.values()){
            CardTypeVO cardTypeVO = new CardTypeVO();
            cardTypeVO.setCardType(cardType.getType());
            cardTypeVO.setCardDesc(cardType.getDesc());
            cardTypeList.add(cardTypeVO);
        }
        return new BaseAnswer<List<CardTypeVO>>().setData(cardTypeList);
    }

    /**
     * 获取终端类型
     * @return
     */
    @GetMapping(value = "/listTerminalType")
    public BaseAnswer<List<TerminalTypeVO>> listTerminalType(){
        List<TerminalTypeVO> terminalTypeList = new ArrayList();
   /*     TerminalTypeVO terminalTypeAll = new TerminalTypeVO();
        terminalTypeAll.setTerminalType("");
        terminalTypeAll.setTerminalDesc("全部");
        terminalTypeList.add(terminalTypeAll);*/
        for (TerminalTypeEnum terminalType:TerminalTypeEnum.values()){
            TerminalTypeVO terminalTypeVO = new TerminalTypeVO();
            terminalTypeVO.setTerminalType(terminalType.getType());
            terminalTypeVO.setTerminalDesc(terminalType.getDesc());
            terminalTypeList.add(terminalTypeVO);
        }
        return new BaseAnswer<List<TerminalTypeVO>>().setData(terminalTypeList);
    }

    /**
     * 获取销售状态
     * @return
     */
    @GetMapping(value = "/listSellStatus")
    public BaseAnswer<List<SellStatusVO>> listSellStatus(){
        List<SellStatusVO> sellStatusList = new ArrayList();
     /*   SellStatusVO sellStatusAll = new SellStatusVO();
        sellStatusAll.setSellStatus("");
        sellStatusAll.setSellStatusDesc("全部");
        sellStatusList.add(sellStatusAll);*/
        for (SellStatusEnum sellStatus: SellStatusEnum.values()){
            SellStatusVO sellStatusVO = new SellStatusVO();
            sellStatusVO.setSellStatus(sellStatus.getType());
            sellStatusVO.setSellStatusDesc(sellStatus.getDesc());
            sellStatusList.add(sellStatusVO);
        }
        return new BaseAnswer<List<SellStatusVO>>().setData(sellStatusList);
    }

    /**
     * 导入批次的销售状态
     * @return
     */
    @GetMapping(value = "/listImportNumSellStatus")
    public BaseAnswer<List<SellStatusVO>> listImportNumSellStatus(){
        List<SellStatusVO> sellStatusList = new ArrayList();
        SellStatusVO sellStatusAll = new SellStatusVO();
        sellStatusAll.setSellStatus("");
        sellStatusAll.setSellStatusDesc("全部");
        sellStatusList.add(sellStatusAll);
        for (SellStatusEnum sellStatus: SellStatusEnum.values()){
            SellStatusVO sellStatusVO = new SellStatusVO();
            sellStatusVO.setSellStatus(sellStatus.getType());
            sellStatusVO.setSellStatusDesc(sellStatus.getDesc());
            sellStatusList.add(sellStatusVO);
        }
        sellStatusAll = new SellStatusVO();
        sellStatusAll.setSellStatus("-");
        sellStatusAll.setSellStatusDesc("-");
        sellStatusList.add(sellStatusAll);
        return new BaseAnswer<List<SellStatusVO>>().setData(sellStatusList);
    }


    /**
     * 获取销售状态
     * @return
     */
    @GetMapping(value = "/listCardStatus")
    public BaseAnswer<List<CardStatusVO>> listCardStatus(){
        List<CardStatusVO> cardStatusList = new ArrayList();
        CardStatusVO cardStatusAll = new CardStatusVO();
        cardStatusAll.setCardStatus("");
        cardStatusAll.setCardStatusDesc("全部");
        cardStatusList.add(cardStatusAll);
        for (CardStatusEnum cardStatus: CardStatusEnum.values()){
            CardStatusVO cardStatusVO = new CardStatusVO();
            cardStatusVO.setCardStatus(cardStatus.getType());
            cardStatusVO.setCardStatusDesc(cardStatus.getDesc());
            cardStatusList.add(cardStatusVO);
        }
        return new BaseAnswer<List<CardStatusVO>>().setData(cardStatusList);
    }

    /**
     * （卡+X代客下单）根据订单号，获取和订单商品的开卡模板名称一致的码号列表
     */
    @GetMapping("/orderProductTemplateCardList")
    public BaseAnswer<List<String>> orderProductTemplateCardList(@Valid OrderProductTemplateCardListParam param){
        return cardInfoService.orderProductTemplateCardList(param.getOrderId(),param.getMsisdn());
    }


    /**
     * （卡+X代客下单）获取订单商品的开卡模板名称
     */
    @GetMapping("/orderProductTemplateName")
    public BaseAnswer<String> orderProductTemplateName(@RequestParam(required = true) String orderId){
        return cardInfoService.orderProductTemplateName(orderId);
    }


    /**
     * 处理历史码号库存信息
     * @return
     */
    @PostMapping("/handleHistoryCardInventory")
    public BaseAnswer<Void> handleHistoryCardInventoryMessage(@RequestBody CardInfoParam param){
        cardInfoService.handleHistoryCardInventory(param);
        return new BaseAnswer<>();
    }

    /**
     * 将“不可销售”的码号，转为“可销售”
     */
    @PostMapping("/setStatusCanSell")
    public BaseAnswer setStatusCanSell(@RequestBody @Valid SetStatusCanSellParam param){
        return cardInfoService.setStatusCanSell(param.getId());

    }
}
