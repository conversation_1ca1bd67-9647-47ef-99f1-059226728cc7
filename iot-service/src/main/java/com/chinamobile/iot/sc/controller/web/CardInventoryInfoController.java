package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.CardInfoBYInventoryParam;
import com.chinamobile.iot.sc.pojo.param.CardInfoInventoryErrorParam;
import com.chinamobile.iot.sc.pojo.param.CardInfoParam;
import com.chinamobile.iot.sc.pojo.param.CardInventoryWarningParam;
import com.chinamobile.iot.sc.pojo.vo.CardInfoByInventoryVO;
import com.chinamobile.iot.sc.pojo.vo.CardInfoVO;
import com.chinamobile.iot.sc.pojo.vo.CardInventoryInfoVO;
import com.chinamobile.iot.sc.service.CardInventoryInfoService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> xiemaohua
 * @date : 2024/12/9 9:57
 * @description: 码号库存控制类
 **/
@RestController
@RequestMapping(value = "/osweb/cardInventory")
public class CardInventoryInfoController {

     @Resource
     private CardInventoryInfoService cardInventoryInfoService;

    /**
     * 分页获取码号库存
     * @param cardInfoParam
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping(value = "/pageCard")
    public BaseAnswer<PageData<CardInventoryInfoVO>> pageCardInventoryMainInfoMessage(CardInfoParam cardInfoParam,
                                                                  @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer baseAnswer = new BaseAnswer();
        PageData<CardInventoryInfoVO> pageData = cardInventoryInfoService.pageCardInventoryMainInfo(cardInfoParam, loginIfo4Redis);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }


    /**
     * 通过码号主键id查询关联的码号信息
     * @param param
     * @return
     */
    @GetMapping(value = "/pageCardByInventoryId")
    public BaseAnswer<PageData<CardInfoByInventoryVO>> getCardInfoByInventoryIdMessage(@Valid CardInfoBYInventoryParam param) {
        BaseAnswer baseAnswer = new BaseAnswer();
        PageData<CardInfoByInventoryVO> cardInfoByInventoryId = cardInventoryInfoService.getCardInfoByInventoryId(param);
        baseAnswer.setData(cardInfoByInventoryId);
        return baseAnswer;
    }


    /**
     * 设置码号库存预警值
     * @param param
     * @return
     */
    @PostMapping(value = "/settingsCardForewarning")
    public BaseAnswer<Void> settingsCardInventoryForewarningMessage(@RequestBody @Valid CardInventoryWarningParam param) {
       cardInventoryInfoService.settingsCardInventoryForewarning(param);
        return new BaseAnswer();
    }

    /**
     * 原子商品配置处查询码号库存信息
     * @param cardInventoryMainId
     * @param atomId
     * @return
     */
    @GetMapping(value = "/pageAtomCard")
    public BaseAnswer<List<CardInventoryInfoVO>> pageCardInventoryMainInfoMessage(@RequestParam("cardInventoryMainId") String cardInventoryMainId,@RequestParam("atomId") String atomId) {
        BaseAnswer<List<CardInventoryInfoVO>> baseAnswer = new BaseAnswer<>();
        List<CardInventoryInfoVO> atomCardInfoInventoryList = cardInventoryInfoService.getAtomCardInfoInventoryList(cardInventoryMainId, atomId);
        baseAnswer.setData(atomCardInfoInventoryList);
        return baseAnswer;
    }

    /**
     * 处理历史订单码号库存预占（）
     * @return
     */
    @GetMapping(value = "/handleHistoryOrderCardInventory")
    public BaseAnswer<Void> handleHistoryOrderCardInventoryMessage() {
        cardInventoryInfoService.handleHistoryOrderCardInventory();
        return new BaseAnswer();
    }

    /**
     * 处理错误的同步号卡导致码号库存数据错误
     * @return
     */
    @PostMapping(value = "/handleErrorCardInventoryMainInfo")
    public BaseAnswer<Void> handleErrorCardInventoryMainInfoMessage(@RequestBody @Valid CardInfoInventoryErrorParam param) {
        cardInventoryInfoService.handleErrorCardInventoryMainInfo(param);
        return new BaseAnswer();
    }

    /**
     * 处理历史卡+x原子商品码号库存主键id
     * @return
     */
    @GetMapping(value = "/handleHistoryAtomCardInventoryId")
    public BaseAnswer<Void> handleHistoryAtomCardInventoryIdMessage(@RequestParam(value = "atomId",required = false) String atomId) {
        cardInventoryInfoService.handleHistoryAtomCardInventoryId(atomId);
        return new BaseAnswer();
    }

}
