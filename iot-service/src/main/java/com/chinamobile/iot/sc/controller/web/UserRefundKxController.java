package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.InventoryCutListVO;
import com.chinamobile.iot.sc.pojo.vo.KxCanChooseUserVO;
import com.chinamobile.iot.sc.pojo.vo.UserRefundKxVO;
import com.chinamobile.iot.sc.service.UserRefundKxService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/14
 * @description 卡+X退货发送短信的相关人员配置controller类
 */
@RestController
@RequestMapping(value = "/osweb/userkx")
public class UserRefundKxController {

    @Resource
    private UserRefundKxService userRefundKxService;

    /**
     * 获取卡+X退货发送短信的可选人员
     *
     * @param kxCanChooseUserParam
     * @return
     */
    @GetMapping(value = "/getKxCanChooseUser")
    public BaseAnswer<List<KxCanChooseUserVO>> listKxCanChooseUser(KxCanChooseUserParam kxCanChooseUserParam) {
        BaseAnswer<List<KxCanChooseUserVO>> baseAnswer = new BaseAnswer<>();
        List<KxCanChooseUserVO> kxCanChooseUserVOList = userRefundKxService.listKxCanChooseUser(kxCanChooseUserParam);
        baseAnswer.setData(kxCanChooseUserVOList);
        return baseAnswer;
    }

    /**
     * 新增卡+X退货人员
     *
     * @param addUserRefundKxParam
     * @return
     */
    @PostMapping(value = "/saveUserRefundKx")
    public BaseAnswer addUserRefundKx(@RequestBody @Valid AddUserRefundKxParam addUserRefundKxParam,
                                      @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer baseAnswer = new BaseAnswer();
        userRefundKxService.addUserRefundKx(addUserRefundKxParam,loginIfo4Redis);
        return baseAnswer;
    }

    /**
     * 根据id删除卡+X退货人员
     *
     * @param deleteUserRefundKxParam
     * @return
     */
    @DeleteMapping(value = "/deleteUserRefundKxById")
    public BaseAnswer deleteUserRefundKxById(@RequestBody @Valid DeleteUserRefundKxParam deleteUserRefundKxParam) {
        BaseAnswer baseAnswer = new BaseAnswer();
        userRefundKxService.deleteUserRefundKxById(deleteUserRefundKxParam);
        return baseAnswer;
    }

    /**
     * 查询卡+X退货发送短信的人员
     *
     * @return
     */
    @GetMapping(value = "/getUserRefundKx")
    public BaseAnswer<PageData<UserRefundKxVO>> listUserRefundKx(@RequestParam(value = "page",required = false)Integer page,
                                                                 @RequestParam(value = "pageSize",required = false)Integer pageSize,
                                                                 @RequestParam(value = "userName",required = false)String userName,
                                                                 @RequestParam(value = "phone",required = false)String phone) {
        QueryNoticeUserParam param = new QueryNoticeUserParam();
        if (page == null || pageSize == null) {
            page = 1;
            pageSize = 10;
        }
        param.setUserName(userName);
        param.setPhone(phone);
        BaseAnswer<PageData<UserRefundKxVO>> baseAnswer = new BaseAnswer<>();
        PageHelper.startPage(page,pageSize);
        List<UserRefundKxVO> userRefundKxVOList = userRefundKxService.listUserRefundKx(param);
        PageInfo<UserRefundKxVO> pageInfo = new PageInfo<>(userRefundKxVOList);
        PageData pageData = new PageData<>();
        pageData.setPage(pageInfo.getPageNum());
        pageData.setData(pageInfo.getList());
        pageData.setCount(pageInfo.getTotal());
        baseAnswer.setData(pageData);
        return baseAnswer;
    }


    /**
     * 库存模式切换接口
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping(value = "/inventory/cutChange")
    public BaseAnswer cutNowInventoryPatternAdd(@RequestBody @Valid InventoryPatternCutParam param,
                                             @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer baseAnswer = new BaseAnswer();
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        userRefundKxService.cutNowInventoryPattern(param,loginIfo4Redis,ip);
        return baseAnswer;
    }


    /**
     * 分页查询库存切换列表
     * @param param
     * @return
     */
    @GetMapping(value = "/inventory/cutList")
    public BaseAnswer<PageData<InventoryCutListVO>> getInventoryCutListPage(InventoryCutListParam param){
        return userRefundKxService.getInventoryCutList(param);
    }

    /**
     * 获取当前库存模式名称
     * @return
     */
    @GetMapping(value = "/inventory/nowName")
    public BaseAnswer<String> getInventoryCutNow(){
        BaseAnswer<String> baseAnswer = new BaseAnswer<>();
        String nowInventoryPattern = userRefundKxService.getNowInventoryPattern();
        return baseAnswer.setData(nowInventoryPattern);
    }
}
