package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.DkcardxInventoryMainInfoParam;
import com.chinamobile.iot.sc.pojo.dto.DkcardxInventoryCardDetailLocationDTO;
import com.chinamobile.iot.sc.pojo.vo.DkcardxInventoryMainInfoVO;
import com.chinamobile.iot.sc.service.DkcardxInventoryMainInfoService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/17
 * @description 卡+X终端库存主要信息controller类
 */
@RestController
@RequestMapping(value = "/osweb/cardinventorymaininfo")
public class DkcardxInventoryMainInfoController {

    @Resource
    private DkcardxInventoryMainInfoService dkcardxInventoryMainInfoService;

    /**
     * 分页获取卡+X终端库存主要信息
     * @param dkcardxInventoryMainInfoParam
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping(value = "/pageMainInfo")
    public BaseAnswer<PageData<DkcardxInventoryMainInfoVO>> pageCardRelationX(DkcardxInventoryMainInfoParam dkcardxInventoryMainInfoParam,
                                                                   @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer baseAnswer = new BaseAnswer();
        PageData<DkcardxInventoryMainInfoVO> pageData = dkcardxInventoryMainInfoService.pageCardMainInfo(dkcardxInventoryMainInfoParam, loginIfo4Redis);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }

    /**
     * 导出卡+X库存配置
     * @param dkcardxInventoryMainInfoParam
     * @param loginIfo4Redis
     * @param response
     * @throws Exception
     */
    @GetMapping(value = "/exportDxInventoryMainAndDetail")
    public void exportDxInventoryMainAndDetail(DkcardxInventoryMainInfoParam dkcardxInventoryMainInfoParam,
                                   @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                                   HttpServletResponse response) throws Exception {
        dkcardxInventoryMainInfoService.exportDxInventoryMainAndDetail(dkcardxInventoryMainInfoParam,loginIfo4Redis, response);
    }


    /**
     * 卡+X库存详情终端明细地市下拉列表
     * @param inventoryMainId
     * @return
     */
    @GetMapping(value = "/listXDetailLocation")
    public BaseAnswer<List<DkcardxInventoryCardDetailLocationDTO>> listXDetailLocation(@RequestParam(value = "inventoryMainId") String inventoryMainId){
        List<DkcardxInventoryCardDetailLocationDTO> detailLocationDTOList = dkcardxInventoryMainInfoService.listXDetailLocation(inventoryMainId);
        return new BaseAnswer<List<DkcardxInventoryCardDetailLocationDTO>>().setData(detailLocationDTOList);
    }

}
