package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;

import java.util.Date;

@Data
public class ContractProvinceInfoVO {

    /**
     * 申请人
     */
    private String applyName;

    /**
     * 申请人编码
     */
    private String applyNum;


    /**
     * 申请人所在公司代码，主数据统一公司代码
     */
    private String companyCode;

    /**
     * 申请人所在公司名称，主数据统一公司名称
     */
    private String companyName;

    /**
     * 集中化合同系统合同名称
     */
    private String contractName;

    /**
     * 集中化合同系统合同编号
     */
    private String contractNo;

    /**
     * 主数据统一部门编码
     */
    private String deptCode;

    /**
     * 主数据统一部门名称
     */
    private String deptName;

    /**
     * 商城单据号
     */
    private String recordNumber;

    /**
     * 主数据统一供应商编码
     */
    private String vendorCode;

    /**
     * 主数据统一供应商名称
     */
    private String vendorName;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 更新日期
     */
    private Date lastUpdateDate;

}
