package com.chinamobile.iot.sc.pojo.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * created by l<PERSON><PERSON><PERSON> on 2022/12/26 15:43
 */
@Data
public class SSOBaseHeaderDTO {

    //版本号,初始版本号1.0
    @NotNull
    private String version;
    //唯一标识请求的ID
    @NotNull
    private String msgid;
    //请求消息发送的系统时间，精确到毫秒，共17位，格式：20121227180001165
    @NotNull
    private String systemtime;
    //获取详情见《移动认证（对内）现行接入流程》
    @NotNull
    private String sourceid;
    //渠道编码定义
    @NotNull
    private String apptype;
    //签名
    @NotNull
    private String mac;
    //用户公网IP
    private String userip;

}
