package com.chinamobile.iot.sc.response.web;

import com.chinamobile.iot.sc.pojo.dto.CityInfoDTO;
import lombok.Data;

import java.util.List;

/**
 * @Author: YSC
 * @Date: 2021/11/12 11:38
 * @Description:
 */
@Data
public class InventoryInfoDTO {
    /**
     * 原子商品ID
     */
    private String id;
    /**
     * 商品类型
     */
    private String spuOfferingClass;
    /**
     * 商品名称（规格）
     */
    private String skuOfferingName;
    /**
     * 原子商品名称
     */
    private String atomOfferingName;
    /**
     * 类型
     */
    private String atomOfferingClass;
    /**
     * 型号
     */
    private String model;
    /**
     * 颜色
     */
    private String color;
    /**
     * 所属合作伙伴
     */
    private String cooperatorName;
    /**
     * 合作伙伴名
     */
    private String partnerName;
    /**
     * 库存状态
     */
    private Integer inventoryStatus;
    /**
     * 库存数量
     */
    private Long inventory;
    /**
     * 预警库存数量
     */
    private Long inventoryThreshold;
    /**
     * 是否发送短信
     */
    private Boolean isNotice;
    /**
     * 是否配置库存
     */
    private Boolean isInventory;
    /**
     * 商品组/销售商品编码
     */
    private String  spuOfferingCode;

    /**
     *商品组/销售商品名称
     */
    private String  spuOfferingName;

    /**
     * 销售商品状态（0：测试，1：发布，2：下架）
     */
    private String  spuOfferingStatus;

    /**
     *商品编码（规格）
     */
    private String  skuOfferingCode;

    /**
     * 规格商品状态（0：测试，1：发布，2：下架）
     */
    private String  skuOfferingStatus;

    /**
     * 预占库存数量
     */
    private Long reserveInventory;

    /**
     * 是否配置从合作伙伴
     */
    private Boolean hasPartner;

    /**
     * 商品封面图url
     *
     */
    private String imgUrl;

    /**
     * 原子编码
     *
     */
    private String atomOfferingCode;

    /**
     * 卡+x库存管理模式：1：省级管理 2：地市管理
     */
    private String inventoryManagementModeKx;

    /**
     * 'X产品类型,1:5G CPE,2:5G 快线,3:千里眼,4:合同履约,5:OneNET独立服务,6:标准产品(OneNET）,7:OnePark独立服务,8:标准产品（OnePark）',
     */
    private String productType;

    /**
     *  '卡片类型,0：插拔卡,1：贴片卡,2：M2M芯片非空写卡,3：M2M芯片空写卡
     */
    private String cardType;

    /**
     *模板名称
     */
    private String templateName;

    /**
     * 开卡模板编码
     */
    private String templateId;

    /**
     * 配置的库存id
     */
    private String inventoryId;

    /**
     * 商品发布省地市信息
     */
    private List<CityInfoDTO> cityInfo;

    /**
     * 库存模式
     */
    private String inventoryType;

    /**
     * 全量配置时间
     */
    private String  configAllTime;

    private List<InventoryInfoDTO> atomInventoryInfo;

    /**
     * 配置的库存主表id
     */
    private String inventoryMainId;

    /**
     * 号码库存主键id
     */
    private String cardInfoInventoryMainId;

    /**
     * 总库存
     */
    private Long totalInventory;

    /**
     * 当前库存
     */
    private Long currentInventory;

    /**
     * 卡服务商名称
     */
    private String custName;

    /**
     * 归属卡服务商EC编码
     */
    private String custCode;

    /**
     * 省编码
     */
    private String beId;

    /**
     * 是否为含卡终端（卡+X可能为true,其他都是false）
     */
    private Boolean hasCard;

}
