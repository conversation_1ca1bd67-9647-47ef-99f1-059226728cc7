package com.chinamobile.iot.sc.pojo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ApplyInvoiceRecordExample {
    /**
     * Corresponding to the database table supply_chain..apply_invoice_record
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    protected String orderByClause;

    /**
     * Corresponding to the database table supply_chain..apply_invoice_record
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    protected boolean distinct;

    /**
     * Corresponding to the database table supply_chain..apply_invoice_record
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    public ApplyInvoiceRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    public ApplyInvoiceRecordExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    public ApplyInvoiceRecordExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    public static Criteria newAndCreateCriteria() {
        ApplyInvoiceRecordExample example = new ApplyInvoiceRecordExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    public ApplyInvoiceRecordExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    public ApplyInvoiceRecordExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     * Corresponding to the database table supply_chain..apply_invoice_record
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdIsNull() {
            addCriterion("atom_order_id is null");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdIsNotNull() {
            addCriterion("atom_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdEqualTo(String value) {
            addCriterion("atom_order_id =", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("atom_order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotEqualTo(String value) {
            addCriterion("atom_order_id <>", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("atom_order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdGreaterThan(String value) {
            addCriterion("atom_order_id >", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("atom_order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("atom_order_id >=", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("atom_order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLessThan(String value) {
            addCriterion("atom_order_id <", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("atom_order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLessThanOrEqualTo(String value) {
            addCriterion("atom_order_id <=", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("atom_order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLike(String value) {
            addCriterion("atom_order_id like", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotLike(String value) {
            addCriterion("atom_order_id not like", value, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdIn(List<String> values) {
            addCriterion("atom_order_id in", values, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotIn(List<String> values) {
            addCriterion("atom_order_id not in", values, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdBetween(String value1, String value2) {
            addCriterion("atom_order_id between", value1, value2, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdNotBetween(String value1, String value2) {
            addCriterion("atom_order_id not between", value1, value2, "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andOrderSeqIsNull() {
            addCriterion("order_seq is null");
            return (Criteria) this;
        }

        public Criteria andOrderSeqIsNotNull() {
            addCriterion("order_seq is not null");
            return (Criteria) this;
        }

        public Criteria andOrderSeqEqualTo(String value) {
            addCriterion("order_seq =", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("order_seq = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderSeqNotEqualTo(String value) {
            addCriterion("order_seq <>", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("order_seq <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderSeqGreaterThan(String value) {
            addCriterion("order_seq >", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("order_seq > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderSeqGreaterThanOrEqualTo(String value) {
            addCriterion("order_seq >=", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("order_seq >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderSeqLessThan(String value) {
            addCriterion("order_seq <", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("order_seq < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderSeqLessThanOrEqualTo(String value) {
            addCriterion("order_seq <=", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("order_seq <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderSeqLike(String value) {
            addCriterion("order_seq like", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqNotLike(String value) {
            addCriterion("order_seq not like", value, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqIn(List<String> values) {
            addCriterion("order_seq in", values, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqNotIn(List<String> values) {
            addCriterion("order_seq not in", values, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqBetween(String value1, String value2) {
            addCriterion("order_seq between", value1, value2, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderSeqNotBetween(String value1, String value2) {
            addCriterion("order_seq not between", value1, value2, "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andBeIdIsNull() {
            addCriterion("be_id is null");
            return (Criteria) this;
        }

        public Criteria andBeIdIsNotNull() {
            addCriterion("be_id is not null");
            return (Criteria) this;
        }

        public Criteria andBeIdEqualTo(String value) {
            addCriterion("be_id =", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("be_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdNotEqualTo(String value) {
            addCriterion("be_id <>", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("be_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThan(String value) {
            addCriterion("be_id >", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("be_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanOrEqualTo(String value) {
            addCriterion("be_id >=", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("be_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLessThan(String value) {
            addCriterion("be_id <", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("be_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanOrEqualTo(String value) {
            addCriterion("be_id <=", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("be_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLike(String value) {
            addCriterion("be_id like", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotLike(String value) {
            addCriterion("be_id not like", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdIn(List<String> values) {
            addCriterion("be_id in", values, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotIn(List<String> values) {
            addCriterion("be_id not in", values, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdBetween(String value1, String value2) {
            addCriterion("be_id between", value1, value2, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotBetween(String value1, String value2) {
            addCriterion("be_id not between", value1, value2, "beId");
            return (Criteria) this;
        }

        public Criteria andCustCodeIsNull() {
            addCriterion("cust_code is null");
            return (Criteria) this;
        }

        public Criteria andCustCodeIsNotNull() {
            addCriterion("cust_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustCodeEqualTo(String value) {
            addCriterion("cust_code =", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("cust_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeNotEqualTo(String value) {
            addCriterion("cust_code <>", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("cust_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThan(String value) {
            addCriterion("cust_code >", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("cust_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanOrEqualTo(String value) {
            addCriterion("cust_code >=", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("cust_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThan(String value) {
            addCriterion("cust_code <", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("cust_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanOrEqualTo(String value) {
            addCriterion("cust_code <=", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("cust_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustCodeLike(String value) {
            addCriterion("cust_code like", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotLike(String value) {
            addCriterion("cust_code not like", value, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeIn(List<String> values) {
            addCriterion("cust_code in", values, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotIn(List<String> values) {
            addCriterion("cust_code not in", values, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeBetween(String value1, String value2) {
            addCriterion("cust_code between", value1, value2, "custCode");
            return (Criteria) this;
        }

        public Criteria andCustCodeNotBetween(String value1, String value2) {
            addCriterion("cust_code not between", value1, value2, "custCode");
            return (Criteria) this;
        }

        public Criteria andPrintDateIsNull() {
            addCriterion("print_date is null");
            return (Criteria) this;
        }

        public Criteria andPrintDateIsNotNull() {
            addCriterion("print_date is not null");
            return (Criteria) this;
        }

        public Criteria andPrintDateEqualTo(String value) {
            addCriterion("print_date =", value, "printDate");
            return (Criteria) this;
        }

        public Criteria andPrintDateEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("print_date = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrintDateNotEqualTo(String value) {
            addCriterion("print_date <>", value, "printDate");
            return (Criteria) this;
        }

        public Criteria andPrintDateNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("print_date <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrintDateGreaterThan(String value) {
            addCriterion("print_date >", value, "printDate");
            return (Criteria) this;
        }

        public Criteria andPrintDateGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("print_date > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrintDateGreaterThanOrEqualTo(String value) {
            addCriterion("print_date >=", value, "printDate");
            return (Criteria) this;
        }

        public Criteria andPrintDateGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("print_date >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrintDateLessThan(String value) {
            addCriterion("print_date <", value, "printDate");
            return (Criteria) this;
        }

        public Criteria andPrintDateLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("print_date < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrintDateLessThanOrEqualTo(String value) {
            addCriterion("print_date <=", value, "printDate");
            return (Criteria) this;
        }

        public Criteria andPrintDateLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("print_date <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrintDateLike(String value) {
            addCriterion("print_date like", value, "printDate");
            return (Criteria) this;
        }

        public Criteria andPrintDateNotLike(String value) {
            addCriterion("print_date not like", value, "printDate");
            return (Criteria) this;
        }

        public Criteria andPrintDateIn(List<String> values) {
            addCriterion("print_date in", values, "printDate");
            return (Criteria) this;
        }

        public Criteria andPrintDateNotIn(List<String> values) {
            addCriterion("print_date not in", values, "printDate");
            return (Criteria) this;
        }

        public Criteria andPrintDateBetween(String value1, String value2) {
            addCriterion("print_date between", value1, value2, "printDate");
            return (Criteria) this;
        }

        public Criteria andPrintDateNotBetween(String value1, String value2) {
            addCriterion("print_date not between", value1, value2, "printDate");
            return (Criteria) this;
        }

        public Criteria andFrankIsNull() {
            addCriterion("frank is null");
            return (Criteria) this;
        }

        public Criteria andFrankIsNotNull() {
            addCriterion("frank is not null");
            return (Criteria) this;
        }

        public Criteria andFrankEqualTo(String value) {
            addCriterion("frank =", value, "frank");
            return (Criteria) this;
        }

        public Criteria andFrankEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("frank = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrankNotEqualTo(String value) {
            addCriterion("frank <>", value, "frank");
            return (Criteria) this;
        }

        public Criteria andFrankNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("frank <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrankGreaterThan(String value) {
            addCriterion("frank >", value, "frank");
            return (Criteria) this;
        }

        public Criteria andFrankGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("frank > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrankGreaterThanOrEqualTo(String value) {
            addCriterion("frank >=", value, "frank");
            return (Criteria) this;
        }

        public Criteria andFrankGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("frank >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrankLessThan(String value) {
            addCriterion("frank <", value, "frank");
            return (Criteria) this;
        }

        public Criteria andFrankLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("frank < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrankLessThanOrEqualTo(String value) {
            addCriterion("frank <=", value, "frank");
            return (Criteria) this;
        }

        public Criteria andFrankLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("frank <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFrankLike(String value) {
            addCriterion("frank like", value, "frank");
            return (Criteria) this;
        }

        public Criteria andFrankNotLike(String value) {
            addCriterion("frank not like", value, "frank");
            return (Criteria) this;
        }

        public Criteria andFrankIn(List<String> values) {
            addCriterion("frank in", values, "frank");
            return (Criteria) this;
        }

        public Criteria andFrankNotIn(List<String> values) {
            addCriterion("frank not in", values, "frank");
            return (Criteria) this;
        }

        public Criteria andFrankBetween(String value1, String value2) {
            addCriterion("frank between", value1, value2, "frank");
            return (Criteria) this;
        }

        public Criteria andFrankNotBetween(String value1, String value2) {
            addCriterion("frank not between", value1, value2, "frank");
            return (Criteria) this;
        }

        public Criteria andPNameIsNull() {
            addCriterion("p_name is null");
            return (Criteria) this;
        }

        public Criteria andPNameIsNotNull() {
            addCriterion("p_name is not null");
            return (Criteria) this;
        }

        public Criteria andPNameEqualTo(String value) {
            addCriterion("p_name =", value, "pName");
            return (Criteria) this;
        }

        public Criteria andPNameEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("p_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPNameNotEqualTo(String value) {
            addCriterion("p_name <>", value, "pName");
            return (Criteria) this;
        }

        public Criteria andPNameNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("p_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPNameGreaterThan(String value) {
            addCriterion("p_name >", value, "pName");
            return (Criteria) this;
        }

        public Criteria andPNameGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("p_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPNameGreaterThanOrEqualTo(String value) {
            addCriterion("p_name >=", value, "pName");
            return (Criteria) this;
        }

        public Criteria andPNameGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("p_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPNameLessThan(String value) {
            addCriterion("p_name <", value, "pName");
            return (Criteria) this;
        }

        public Criteria andPNameLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("p_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPNameLessThanOrEqualTo(String value) {
            addCriterion("p_name <=", value, "pName");
            return (Criteria) this;
        }

        public Criteria andPNameLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("p_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPNameLike(String value) {
            addCriterion("p_name like", value, "pName");
            return (Criteria) this;
        }

        public Criteria andPNameNotLike(String value) {
            addCriterion("p_name not like", value, "pName");
            return (Criteria) this;
        }

        public Criteria andPNameIn(List<String> values) {
            addCriterion("p_name in", values, "pName");
            return (Criteria) this;
        }

        public Criteria andPNameNotIn(List<String> values) {
            addCriterion("p_name not in", values, "pName");
            return (Criteria) this;
        }

        public Criteria andPNameBetween(String value1, String value2) {
            addCriterion("p_name between", value1, value2, "pName");
            return (Criteria) this;
        }

        public Criteria andPNameNotBetween(String value1, String value2) {
            addCriterion("p_name not between", value1, value2, "pName");
            return (Criteria) this;
        }

        public Criteria andIdentifyNumIsNull() {
            addCriterion("identify_num is null");
            return (Criteria) this;
        }

        public Criteria andIdentifyNumIsNotNull() {
            addCriterion("identify_num is not null");
            return (Criteria) this;
        }

        public Criteria andIdentifyNumEqualTo(String value) {
            addCriterion("identify_num =", value, "identifyNum");
            return (Criteria) this;
        }

        public Criteria andIdentifyNumEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("identify_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdentifyNumNotEqualTo(String value) {
            addCriterion("identify_num <>", value, "identifyNum");
            return (Criteria) this;
        }

        public Criteria andIdentifyNumNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("identify_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdentifyNumGreaterThan(String value) {
            addCriterion("identify_num >", value, "identifyNum");
            return (Criteria) this;
        }

        public Criteria andIdentifyNumGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("identify_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdentifyNumGreaterThanOrEqualTo(String value) {
            addCriterion("identify_num >=", value, "identifyNum");
            return (Criteria) this;
        }

        public Criteria andIdentifyNumGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("identify_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdentifyNumLessThan(String value) {
            addCriterion("identify_num <", value, "identifyNum");
            return (Criteria) this;
        }

        public Criteria andIdentifyNumLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("identify_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdentifyNumLessThanOrEqualTo(String value) {
            addCriterion("identify_num <=", value, "identifyNum");
            return (Criteria) this;
        }

        public Criteria andIdentifyNumLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("identify_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdentifyNumLike(String value) {
            addCriterion("identify_num like", value, "identifyNum");
            return (Criteria) this;
        }

        public Criteria andIdentifyNumNotLike(String value) {
            addCriterion("identify_num not like", value, "identifyNum");
            return (Criteria) this;
        }

        public Criteria andIdentifyNumIn(List<String> values) {
            addCriterion("identify_num in", values, "identifyNum");
            return (Criteria) this;
        }

        public Criteria andIdentifyNumNotIn(List<String> values) {
            addCriterion("identify_num not in", values, "identifyNum");
            return (Criteria) this;
        }

        public Criteria andIdentifyNumBetween(String value1, String value2) {
            addCriterion("identify_num between", value1, value2, "identifyNum");
            return (Criteria) this;
        }

        public Criteria andIdentifyNumNotBetween(String value1, String value2) {
            addCriterion("identify_num not between", value1, value2, "identifyNum");
            return (Criteria) this;
        }

        public Criteria andAddressInfoIsNull() {
            addCriterion("address_info is null");
            return (Criteria) this;
        }

        public Criteria andAddressInfoIsNotNull() {
            addCriterion("address_info is not null");
            return (Criteria) this;
        }

        public Criteria andAddressInfoEqualTo(String value) {
            addCriterion("address_info =", value, "addressInfo");
            return (Criteria) this;
        }

        public Criteria andAddressInfoEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("address_info = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddressInfoNotEqualTo(String value) {
            addCriterion("address_info <>", value, "addressInfo");
            return (Criteria) this;
        }

        public Criteria andAddressInfoNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("address_info <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddressInfoGreaterThan(String value) {
            addCriterion("address_info >", value, "addressInfo");
            return (Criteria) this;
        }

        public Criteria andAddressInfoGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("address_info > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddressInfoGreaterThanOrEqualTo(String value) {
            addCriterion("address_info >=", value, "addressInfo");
            return (Criteria) this;
        }

        public Criteria andAddressInfoGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("address_info >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddressInfoLessThan(String value) {
            addCriterion("address_info <", value, "addressInfo");
            return (Criteria) this;
        }

        public Criteria andAddressInfoLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("address_info < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddressInfoLessThanOrEqualTo(String value) {
            addCriterion("address_info <=", value, "addressInfo");
            return (Criteria) this;
        }

        public Criteria andAddressInfoLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("address_info <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAddressInfoLike(String value) {
            addCriterion("address_info like", value, "addressInfo");
            return (Criteria) this;
        }

        public Criteria andAddressInfoNotLike(String value) {
            addCriterion("address_info not like", value, "addressInfo");
            return (Criteria) this;
        }

        public Criteria andAddressInfoIn(List<String> values) {
            addCriterion("address_info in", values, "addressInfo");
            return (Criteria) this;
        }

        public Criteria andAddressInfoNotIn(List<String> values) {
            addCriterion("address_info not in", values, "addressInfo");
            return (Criteria) this;
        }

        public Criteria andAddressInfoBetween(String value1, String value2) {
            addCriterion("address_info between", value1, value2, "addressInfo");
            return (Criteria) this;
        }

        public Criteria andAddressInfoNotBetween(String value1, String value2) {
            addCriterion("address_info not between", value1, value2, "addressInfo");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberIsNull() {
            addCriterion("phone_number is null");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberIsNotNull() {
            addCriterion("phone_number is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberEqualTo(String value) {
            addCriterion("phone_number =", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("phone_number = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotEqualTo(String value) {
            addCriterion("phone_number <>", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("phone_number <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhoneNumberGreaterThan(String value) {
            addCriterion("phone_number >", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("phone_number > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhoneNumberGreaterThanOrEqualTo(String value) {
            addCriterion("phone_number >=", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("phone_number >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLessThan(String value) {
            addCriterion("phone_number <", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("phone_number < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLessThanOrEqualTo(String value) {
            addCriterion("phone_number <=", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("phone_number <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLike(String value) {
            addCriterion("phone_number like", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotLike(String value) {
            addCriterion("phone_number not like", value, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberIn(List<String> values) {
            addCriterion("phone_number in", values, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotIn(List<String> values) {
            addCriterion("phone_number not in", values, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberBetween(String value1, String value2) {
            addCriterion("phone_number between", value1, value2, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberNotBetween(String value1, String value2) {
            addCriterion("phone_number not between", value1, value2, "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andBankNameIsNull() {
            addCriterion("bank_name is null");
            return (Criteria) this;
        }

        public Criteria andBankNameIsNotNull() {
            addCriterion("bank_name is not null");
            return (Criteria) this;
        }

        public Criteria andBankNameEqualTo(String value) {
            addCriterion("bank_name =", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("bank_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBankNameNotEqualTo(String value) {
            addCriterion("bank_name <>", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("bank_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBankNameGreaterThan(String value) {
            addCriterion("bank_name >", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("bank_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBankNameGreaterThanOrEqualTo(String value) {
            addCriterion("bank_name >=", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("bank_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBankNameLessThan(String value) {
            addCriterion("bank_name <", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("bank_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBankNameLessThanOrEqualTo(String value) {
            addCriterion("bank_name <=", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("bank_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBankNameLike(String value) {
            addCriterion("bank_name like", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameNotLike(String value) {
            addCriterion("bank_name not like", value, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameIn(List<String> values) {
            addCriterion("bank_name in", values, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameNotIn(List<String> values) {
            addCriterion("bank_name not in", values, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameBetween(String value1, String value2) {
            addCriterion("bank_name between", value1, value2, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankNameNotBetween(String value1, String value2) {
            addCriterion("bank_name not between", value1, value2, "bankName");
            return (Criteria) this;
        }

        public Criteria andBankIdIsNull() {
            addCriterion("bank_iD is null");
            return (Criteria) this;
        }

        public Criteria andBankIdIsNotNull() {
            addCriterion("bank_iD is not null");
            return (Criteria) this;
        }

        public Criteria andBankIdEqualTo(String value) {
            addCriterion("bank_iD =", value, "bankId");
            return (Criteria) this;
        }

        public Criteria andBankIdEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("bank_iD = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBankIdNotEqualTo(String value) {
            addCriterion("bank_iD <>", value, "bankId");
            return (Criteria) this;
        }

        public Criteria andBankIdNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("bank_iD <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBankIdGreaterThan(String value) {
            addCriterion("bank_iD >", value, "bankId");
            return (Criteria) this;
        }

        public Criteria andBankIdGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("bank_iD > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBankIdGreaterThanOrEqualTo(String value) {
            addCriterion("bank_iD >=", value, "bankId");
            return (Criteria) this;
        }

        public Criteria andBankIdGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("bank_iD >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBankIdLessThan(String value) {
            addCriterion("bank_iD <", value, "bankId");
            return (Criteria) this;
        }

        public Criteria andBankIdLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("bank_iD < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBankIdLessThanOrEqualTo(String value) {
            addCriterion("bank_iD <=", value, "bankId");
            return (Criteria) this;
        }

        public Criteria andBankIdLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("bank_iD <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBankIdLike(String value) {
            addCriterion("bank_iD like", value, "bankId");
            return (Criteria) this;
        }

        public Criteria andBankIdNotLike(String value) {
            addCriterion("bank_iD not like", value, "bankId");
            return (Criteria) this;
        }

        public Criteria andBankIdIn(List<String> values) {
            addCriterion("bank_iD in", values, "bankId");
            return (Criteria) this;
        }

        public Criteria andBankIdNotIn(List<String> values) {
            addCriterion("bank_iD not in", values, "bankId");
            return (Criteria) this;
        }

        public Criteria andBankIdBetween(String value1, String value2) {
            addCriterion("bank_iD between", value1, value2, "bankId");
            return (Criteria) this;
        }

        public Criteria andBankIdNotBetween(String value1, String value2) {
            addCriterion("bank_iD not between", value1, value2, "bankId");
            return (Criteria) this;
        }

        public Criteria andOrderPriceIsNull() {
            addCriterion("order_price is null");
            return (Criteria) this;
        }

        public Criteria andOrderPriceIsNotNull() {
            addCriterion("order_price is not null");
            return (Criteria) this;
        }

        public Criteria andOrderPriceEqualTo(Long value) {
            addCriterion("order_price =", value, "orderPrice");
            return (Criteria) this;
        }

        public Criteria andOrderPriceEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("order_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderPriceNotEqualTo(Long value) {
            addCriterion("order_price <>", value, "orderPrice");
            return (Criteria) this;
        }

        public Criteria andOrderPriceNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("order_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderPriceGreaterThan(Long value) {
            addCriterion("order_price >", value, "orderPrice");
            return (Criteria) this;
        }

        public Criteria andOrderPriceGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("order_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderPriceGreaterThanOrEqualTo(Long value) {
            addCriterion("order_price >=", value, "orderPrice");
            return (Criteria) this;
        }

        public Criteria andOrderPriceGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("order_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderPriceLessThan(Long value) {
            addCriterion("order_price <", value, "orderPrice");
            return (Criteria) this;
        }

        public Criteria andOrderPriceLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("order_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderPriceLessThanOrEqualTo(Long value) {
            addCriterion("order_price <=", value, "orderPrice");
            return (Criteria) this;
        }

        public Criteria andOrderPriceLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("order_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderPriceIn(List<Long> values) {
            addCriterion("order_price in", values, "orderPrice");
            return (Criteria) this;
        }

        public Criteria andOrderPriceNotIn(List<Long> values) {
            addCriterion("order_price not in", values, "orderPrice");
            return (Criteria) this;
        }

        public Criteria andOrderPriceBetween(Long value1, Long value2) {
            addCriterion("order_price between", value1, value2, "orderPrice");
            return (Criteria) this;
        }

        public Criteria andOrderPriceNotBetween(Long value1, Long value2) {
            addCriterion("order_price not between", value1, value2, "orderPrice");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("remark = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("remark <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("remark > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("remark >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("remark < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("remark <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIsNull() {
            addCriterion("cooperator_id is null");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIsNotNull() {
            addCriterion("cooperator_id is not null");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdEqualTo(String value) {
            addCriterion("cooperator_id =", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("cooperator_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotEqualTo(String value) {
            addCriterion("cooperator_id <>", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("cooperator_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThan(String value) {
            addCriterion("cooperator_id >", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("cooperator_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanOrEqualTo(String value) {
            addCriterion("cooperator_id >=", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("cooperator_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThan(String value) {
            addCriterion("cooperator_id <", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("cooperator_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanOrEqualTo(String value) {
            addCriterion("cooperator_id <=", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("cooperator_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLike(String value) {
            addCriterion("cooperator_id like", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotLike(String value) {
            addCriterion("cooperator_id not like", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIn(List<String> values) {
            addCriterion("cooperator_id in", values, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotIn(List<String> values) {
            addCriterion("cooperator_id not in", values, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdBetween(String value1, String value2) {
            addCriterion("cooperator_id between", value1, value2, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotBetween(String value1, String value2) {
            addCriterion("cooperator_id not between", value1, value2, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdIsNull() {
            addCriterion("finish_cooperator_id is null");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdIsNotNull() {
            addCriterion("finish_cooperator_id is not null");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdEqualTo(String value) {
            addCriterion("finish_cooperator_id =", value, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("finish_cooperator_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdNotEqualTo(String value) {
            addCriterion("finish_cooperator_id <>", value, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("finish_cooperator_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdGreaterThan(String value) {
            addCriterion("finish_cooperator_id >", value, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("finish_cooperator_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdGreaterThanOrEqualTo(String value) {
            addCriterion("finish_cooperator_id >=", value, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("finish_cooperator_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdLessThan(String value) {
            addCriterion("finish_cooperator_id <", value, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("finish_cooperator_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdLessThanOrEqualTo(String value) {
            addCriterion("finish_cooperator_id <=", value, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("finish_cooperator_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdLike(String value) {
            addCriterion("finish_cooperator_id like", value, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdNotLike(String value) {
            addCriterion("finish_cooperator_id not like", value, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdIn(List<String> values) {
            addCriterion("finish_cooperator_id in", values, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdNotIn(List<String> values) {
            addCriterion("finish_cooperator_id not in", values, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdBetween(String value1, String value2) {
            addCriterion("finish_cooperator_id between", value1, value2, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdNotBetween(String value1, String value2) {
            addCriterion("finish_cooperator_id not between", value1, value2, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andVoucherSumIsNull() {
            addCriterion("voucher_sum is null");
            return (Criteria) this;
        }

        public Criteria andVoucherSumIsNotNull() {
            addCriterion("voucher_sum is not null");
            return (Criteria) this;
        }

        public Criteria andVoucherSumEqualTo(Long value) {
            addCriterion("voucher_sum =", value, "voucherSum");
            return (Criteria) this;
        }

        public Criteria andVoucherSumEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("voucher_sum = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVoucherSumNotEqualTo(Long value) {
            addCriterion("voucher_sum <>", value, "voucherSum");
            return (Criteria) this;
        }

        public Criteria andVoucherSumNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("voucher_sum <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVoucherSumGreaterThan(Long value) {
            addCriterion("voucher_sum >", value, "voucherSum");
            return (Criteria) this;
        }

        public Criteria andVoucherSumGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("voucher_sum > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVoucherSumGreaterThanOrEqualTo(Long value) {
            addCriterion("voucher_sum >=", value, "voucherSum");
            return (Criteria) this;
        }

        public Criteria andVoucherSumGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("voucher_sum >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVoucherSumLessThan(Long value) {
            addCriterion("voucher_sum <", value, "voucherSum");
            return (Criteria) this;
        }

        public Criteria andVoucherSumLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("voucher_sum < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVoucherSumLessThanOrEqualTo(Long value) {
            addCriterion("voucher_sum <=", value, "voucherSum");
            return (Criteria) this;
        }

        public Criteria andVoucherSumLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("voucher_sum <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andVoucherSumIn(List<Long> values) {
            addCriterion("voucher_sum in", values, "voucherSum");
            return (Criteria) this;
        }

        public Criteria andVoucherSumNotIn(List<Long> values) {
            addCriterion("voucher_sum not in", values, "voucherSum");
            return (Criteria) this;
        }

        public Criteria andVoucherSumBetween(Long value1, Long value2) {
            addCriterion("voucher_sum between", value1, value2, "voucherSum");
            return (Criteria) this;
        }

        public Criteria andVoucherSumNotBetween(Long value1, Long value2) {
            addCriterion("voucher_sum not between", value1, value2, "voucherSum");
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberIsNull() {
            addCriterion("apply_document_number is null");
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberIsNotNull() {
            addCriterion("apply_document_number is not null");
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberEqualTo(String value) {
            addCriterion("apply_document_number =", value, "applyDocumentNumber");
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("apply_document_number = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberNotEqualTo(String value) {
            addCriterion("apply_document_number <>", value, "applyDocumentNumber");
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("apply_document_number <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberGreaterThan(String value) {
            addCriterion("apply_document_number >", value, "applyDocumentNumber");
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("apply_document_number > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberGreaterThanOrEqualTo(String value) {
            addCriterion("apply_document_number >=", value, "applyDocumentNumber");
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("apply_document_number >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberLessThan(String value) {
            addCriterion("apply_document_number <", value, "applyDocumentNumber");
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("apply_document_number < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberLessThanOrEqualTo(String value) {
            addCriterion("apply_document_number <=", value, "applyDocumentNumber");
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("apply_document_number <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberLike(String value) {
            addCriterion("apply_document_number like", value, "applyDocumentNumber");
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberNotLike(String value) {
            addCriterion("apply_document_number not like", value, "applyDocumentNumber");
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberIn(List<String> values) {
            addCriterion("apply_document_number in", values, "applyDocumentNumber");
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberNotIn(List<String> values) {
            addCriterion("apply_document_number not in", values, "applyDocumentNumber");
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberBetween(String value1, String value2) {
            addCriterion("apply_document_number between", value1, value2, "applyDocumentNumber");
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberNotBetween(String value1, String value2) {
            addCriterion("apply_document_number not between", value1, value2, "applyDocumentNumber");
            return (Criteria) this;
        }

        public Criteria andReminderCountIsNull() {
            addCriterion("reminder_count is null");
            return (Criteria) this;
        }

        public Criteria andReminderCountIsNotNull() {
            addCriterion("reminder_count is not null");
            return (Criteria) this;
        }

        public Criteria andReminderCountEqualTo(Integer value) {
            addCriterion("reminder_count =", value, "reminderCount");
            return (Criteria) this;
        }

        public Criteria andReminderCountEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("reminder_count = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReminderCountNotEqualTo(Integer value) {
            addCriterion("reminder_count <>", value, "reminderCount");
            return (Criteria) this;
        }

        public Criteria andReminderCountNotEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("reminder_count <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReminderCountGreaterThan(Integer value) {
            addCriterion("reminder_count >", value, "reminderCount");
            return (Criteria) this;
        }

        public Criteria andReminderCountGreaterThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("reminder_count > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReminderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("reminder_count >=", value, "reminderCount");
            return (Criteria) this;
        }

        public Criteria andReminderCountGreaterThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("reminder_count >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReminderCountLessThan(Integer value) {
            addCriterion("reminder_count <", value, "reminderCount");
            return (Criteria) this;
        }

        public Criteria andReminderCountLessThanColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("reminder_count < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReminderCountLessThanOrEqualTo(Integer value) {
            addCriterion("reminder_count <=", value, "reminderCount");
            return (Criteria) this;
        }

        public Criteria andReminderCountLessThanOrEqualToColumn(ApplyInvoiceRecord.Column column) {
            addCriterion(new StringBuilder("reminder_count <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReminderCountIn(List<Integer> values) {
            addCriterion("reminder_count in", values, "reminderCount");
            return (Criteria) this;
        }

        public Criteria andReminderCountNotIn(List<Integer> values) {
            addCriterion("reminder_count not in", values, "reminderCount");
            return (Criteria) this;
        }

        public Criteria andReminderCountBetween(Integer value1, Integer value2) {
            addCriterion("reminder_count between", value1, value2, "reminderCount");
            return (Criteria) this;
        }

        public Criteria andReminderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("reminder_count not between", value1, value2, "reminderCount");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andAtomOrderIdLikeInsensitive(String value) {
            addCriterion("upper(atom_order_id) like", value.toUpperCase(), "atomOrderId");
            return (Criteria) this;
        }

        public Criteria andOrderSeqLikeInsensitive(String value) {
            addCriterion("upper(order_seq) like", value.toUpperCase(), "orderSeq");
            return (Criteria) this;
        }

        public Criteria andOrderIdLikeInsensitive(String value) {
            addCriterion("upper(order_id) like", value.toUpperCase(), "orderId");
            return (Criteria) this;
        }

        public Criteria andBeIdLikeInsensitive(String value) {
            addCriterion("upper(be_id) like", value.toUpperCase(), "beId");
            return (Criteria) this;
        }

        public Criteria andCustCodeLikeInsensitive(String value) {
            addCriterion("upper(cust_code) like", value.toUpperCase(), "custCode");
            return (Criteria) this;
        }

        public Criteria andPrintDateLikeInsensitive(String value) {
            addCriterion("upper(print_date) like", value.toUpperCase(), "printDate");
            return (Criteria) this;
        }

        public Criteria andFrankLikeInsensitive(String value) {
            addCriterion("upper(frank) like", value.toUpperCase(), "frank");
            return (Criteria) this;
        }

        public Criteria andPNameLikeInsensitive(String value) {
            addCriterion("upper(p_name) like", value.toUpperCase(), "pName");
            return (Criteria) this;
        }

        public Criteria andIdentifyNumLikeInsensitive(String value) {
            addCriterion("upper(identify_num) like", value.toUpperCase(), "identifyNum");
            return (Criteria) this;
        }

        public Criteria andAddressInfoLikeInsensitive(String value) {
            addCriterion("upper(address_info) like", value.toUpperCase(), "addressInfo");
            return (Criteria) this;
        }

        public Criteria andPhoneNumberLikeInsensitive(String value) {
            addCriterion("upper(phone_number) like", value.toUpperCase(), "phoneNumber");
            return (Criteria) this;
        }

        public Criteria andBankNameLikeInsensitive(String value) {
            addCriterion("upper(bank_name) like", value.toUpperCase(), "bankName");
            return (Criteria) this;
        }

        public Criteria andBankIdLikeInsensitive(String value) {
            addCriterion("upper(bank_iD) like", value.toUpperCase(), "bankId");
            return (Criteria) this;
        }

        public Criteria andRemarkLikeInsensitive(String value) {
            addCriterion("upper(remark) like", value.toUpperCase(), "remark");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLikeInsensitive(String value) {
            addCriterion("upper(cooperator_id) like", value.toUpperCase(), "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdLikeInsensitive(String value) {
            addCriterion("upper(finish_cooperator_id) like", value.toUpperCase(), "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andApplyDocumentNumberLikeInsensitive(String value) {
            addCriterion("upper(apply_document_number) like", value.toUpperCase(), "applyDocumentNumber");
            return (Criteria) this;
        }
    }

    /**
     * Corresponding to the database table supply_chain..apply_invoice_record
     *
     * @mbg.generated do_not_delete_during_merge Tue Jun 10 17:46:15 CST 2025
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         * Corresponding to the database table supply_chain..apply_invoice_record
         *
         * @mbg.generated Tue Jun 10 17:46:15 CST 2025
         */
        private ApplyInvoiceRecordExample example;

        /**
         *
         * @mbg.generated Tue Jun 10 17:46:15 CST 2025
         */
        protected Criteria(ApplyInvoiceRecordExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Tue Jun 10 17:46:15 CST 2025
         */
        public ApplyInvoiceRecordExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Tue Jun 10 17:46:15 CST 2025
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Jun 10 17:46:15 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Tue Jun 10 17:46:15 CST 2025
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Tue Jun 10 17:46:15 CST 2025
             */
            Criteria add(Criteria add);
        }
    }

    /**
     * Corresponding to the database table supply_chain..apply_invoice_record
     *
     * @mbg.generated Tue Jun 10 17:46:15 CST 2025
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Tue Jun 10 17:46:15 CST 2025
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Tue Jun 10 17:46:15 CST 2025
         */
        void example(com.chinamobile.iot.sc.pojo.ApplyInvoiceRecordExample example);
    }
}