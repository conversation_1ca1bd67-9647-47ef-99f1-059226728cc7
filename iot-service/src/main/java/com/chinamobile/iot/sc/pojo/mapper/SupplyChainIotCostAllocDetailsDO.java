package com.chinamobile.iot.sc.pojo.mapper;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 汇总结算单行信息
 **/
@Data
public class SupplyChainIotCostAllocDetailsDO {

    /**
     * 行号
     */
    private String lineNum;

    /**
     * OU编码
     */
    private String ouCode;

    /**
     * OU名称
     */
    private String ouName;

    /**
     * 数量
     */
    private BigDecimal materialsNumber;

    /**
     * 物料单位
     */
    private String materialsUnit;

    /**
     * 税率
     */
    private String taxRate;

    /**
     * 不含税单价
     */
    private BigDecimal unitPrice;

    /**
     * 含税单价
     */
    private BigDecimal discountedUnitPrice;

    /**
     * 含税金额合计
     */
    private BigDecimal costAmountTax;

    /**
     * 不含税金额合计
     */
    private BigDecimal costAmount;


    /**
     * 税额
     */
    private BigDecimal costTax;

}
