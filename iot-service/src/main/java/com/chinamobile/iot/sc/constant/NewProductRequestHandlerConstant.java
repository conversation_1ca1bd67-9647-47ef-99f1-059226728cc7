package com.chinamobile.iot.sc.constant;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2022/9/15 17:29
 * @description: 产品引入处理流程产量类
 **/
public class NewProductRequestHandlerConstant {

    /**
     * 流程类型 引入
     */
    public static final String FLOW_TYPE_IMPORT = "01";

    /**
     * 流程类型 上下架
     */
    public static final String FLOW_TYPE_STAND_UP_DOWN = "02";

    /**
     * 流程处理状态  发起
     */
    public static final String HANDLER_STATUS_SPONSOR = "01";

    /**
     * 流程处理状态  通过
     */
    public static final String HANDLER_STATUS_PASS = "02";

    /**
     * 流程处理状态  不通过
     */
    public static final String HANDLER_STATUS_NO_PASS = "03";

}
