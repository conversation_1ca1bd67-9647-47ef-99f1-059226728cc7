package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.service.CardMallSyncService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/24
 * @description 商城同步的卡信息controller类
 */
@RestController
@RequestMapping(value = "/osweb/cardmall")
public class CardMallSyncController {

    @Resource
    private CardMallSyncService cardMallSyncService;


    /**
     * 获取存量空写卡的码号处理
     * @return
     */
    @PostMapping(value = "/handleNullCardCutOver")
    public BaseAnswer handleNullCardCutOver(){
        cardMallSyncService.handleNullCardCutOver();
        return new BaseAnswer();
    }

    /**
     * 测试数据用于验证导入极限
     * @param dataCount
     * @param cardType
     * @return
     */
    @GetMapping(value = "/testSelfCreateData")
    public BaseAnswer testSelfCreateData(@RequestParam(value = "dataCount") Integer dataCount,
                                         @RequestParam(value = "cardType")String cardType){
        cardMallSyncService.testSelfCreateData(dataCount,cardType);
        return new BaseAnswer();
    }
}
