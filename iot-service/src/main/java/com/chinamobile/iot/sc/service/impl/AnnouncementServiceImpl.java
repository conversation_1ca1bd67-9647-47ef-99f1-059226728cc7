package com.chinamobile.iot.sc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.config.CommonConstant;
import com.chinamobile.iot.sc.constant.AnnouncementStatusEnum;
import com.chinamobile.iot.sc.dao.AnnouncementMapper;
import com.chinamobile.iot.sc.dao.ext.AnnouncementMapperExt;
import com.chinamobile.iot.sc.enums.log.HomePageEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.SystemInstallEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.DataUserVO;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.Announcement;
import com.chinamobile.iot.sc.pojo.AnnouncementExample;
import com.chinamobile.iot.sc.pojo.mapper.AnnouncementDO;
import com.chinamobile.iot.sc.pojo.param.AnnouncementAuditParam;
import com.chinamobile.iot.sc.pojo.param.AnnouncementListParam;
import com.chinamobile.iot.sc.pojo.param.AnnouncementVisibleParam;
import com.chinamobile.iot.sc.pojo.param.SaveAnnouncementParam;
import com.chinamobile.iot.sc.pojo.vo.AnnouncementVO;
import com.chinamobile.iot.sc.service.AnnouncementService;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.common.BaseConstant.PRODUCT_APPLY_MANAGE_VERIFY_FIRST;
import static com.chinamobile.iot.sc.common.BaseConstant.SYSTEM_SETTING_ANNOUNCEMENT_AUDIT;

@Slf4j
@Service
public class AnnouncementServiceImpl implements AnnouncementService {

    @Resource
    private AnnouncementMapper announcementMapper;

    @Autowired
    private LogService logService;

    @Resource
    private AnnouncementMapperExt announcementMapperExt;

    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer saveAnnouncement(SaveAnnouncementParam param, LoginIfo4Redis loginIfo4Redis) {
        Date now = new Date();
            //新增
        Announcement  announcement = new Announcement();
            BeanUtils.copyProperties(param,announcement);
            announcement.setId(BaseServiceUtils.getId());
            announcement.setCreateTime(now);
            announcement.setUpdateTime(now);
            announcement.setStatus(AnnouncementStatusEnum.DRAFT.code);
            announcement.setCreatorId(loginIfo4Redis.getUserId());
            announcementMapper.insertSelective(announcement);

        //记录日志
        StringBuilder logContent = new StringBuilder();
            logContent.append("【新建】").append("\n");
            logContent.append("公告ID ").append(announcement.getId()).append("\n");
            logContent.append("主题 ").append(announcement.getTitle()).append("\n");
            logContent.append("创建时间 ").append(DateTimeUtil.formatDate(announcement.getCreateTime(),DateTimeUtil.DEFAULT_DATE_DEFAULT));
        logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code,
                SystemInstallEnum.ANNOUNCEMENT_MANAGE.code,
                logContent.toString(), LogResultEnum.LOG_SUCESS.code,null);
        return BaseAnswer.success(null);
    }

    @Override
    public BaseAnswer updateAnnouncement(SaveAnnouncementParam param, LoginIfo4Redis loginIfo4Redis) {
        Date now = new Date();
        String id = param.getId();
        String oldTitle = null;
        String oldContent = null;
        Boolean oldPopup = null;
        Date oldEndTime = null;
            //更新
        Announcement  announcement = announcementMapper.selectByPrimaryKey(id);
            if(announcement == null){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"公告不存在");
            }
            oldTitle = announcement.getTitle();
            oldContent = announcement.getContent();
            oldPopup = announcement.getPopup();
            oldEndTime = announcement.getEndTime();
            if(announcement.getStatus().intValue() != AnnouncementStatusEnum.REJECTED.code.intValue()){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"只有已驳回状态才可编辑");
            }
            BeanUtils.copyProperties(param,announcement);
            announcement.setUpdateTime(now);
            announcement.setStatus(AnnouncementStatusEnum.DRAFT.code);
            announcementMapper.updateByPrimaryKey(announcement);
        //记录日志
        StringBuilder logContent = new StringBuilder();
            logContent.append("【编辑】").append("\n");
            logContent.append("公告ID ").append(announcement.getId()).append("\n");
            logContent.append("主题由 ").append(oldTitle).append(" 修改为 ").append(announcement.getTitle()).append("\n");
            logContent.append("内容由 ").append(oldContent).append(" 修改为 ").append(announcement.getContent()).append("\n");
            logContent.append("是否弹窗提示由 ").append(oldPopup ? "是" : "否").append(" 修改为 ").append(announcement.getPopup() ? "是" : "否").append("\n");
            logContent.append("提示截止时间由 ").append(oldEndTime == null ? "空" : DateTimeUtil.formatDate(oldEndTime,DateTimeUtil.DEFAULT_DATE_DEFAULT)).append(" 修改为 ").append(announcement.getEndTime() == null ? "空" : DateTimeUtil.formatDate(announcement.getEndTime(),DateTimeUtil.DEFAULT_DATE_DEFAULT));
        logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code,
                SystemInstallEnum.ANNOUNCEMENT_MANAGE.code,
                logContent.toString(), LogResultEnum.LOG_SUCESS.code,null);
        return BaseAnswer.success(null);
    }

    @Override
    public BaseAnswer<PageData<AnnouncementVO>> announcementList(AnnouncementListParam param,LoginIfo4Redis loginIfo4Redis) {
        PageData<AnnouncementVO> data = new PageData<>();
        data.setPage(param.getPageNum());
        PageHelper.startPage(param.getPageNum(),param.getPageSize());
        String userId = loginIfo4Redis.getUserId();
        BaseAnswer<List<DataUserVO>> users = userFeignClient.getListUserByAuthCode(SYSTEM_SETTING_ANNOUNCEMENT_AUDIT);
        if (!users.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())){
            throw new BusinessException(BaseErrorConstant.AREA_ERROR.getStateCode(),"查询权限用户错误");
        }
        log.info("基础信息users：{}，userId：{}",users,userId);
        if ( users == null || users.getData() == null){
            //未查询到有审核权限的用户 当前用户查询自己创建的
            param.setUserId(userId);
        }else {
            List<DataUserVO> usersData = users.getData();
            List<String> collect = usersData.stream().map(DataUserVO::getUserId).collect(Collectors.toList());
            log.info("基础信息collect：{}",collect);
           //当前用户有审核权限 查询全部 不包含查询自己的
            if (!collect.contains(userId)){
                param.setUserId(userId);
            }
        }
        List<AnnouncementDO> list = announcementMapperExt.announcementList(param);
        if(CollectionUtils.isEmpty(list)){
            return BaseAnswer.success(data);
        }
        PageInfo<AnnouncementDO> pageInfo = new PageInfo<>(list);
        data.setCount(pageInfo.getTotal());
        data.setData(list.stream().map(p -> {
            AnnouncementVO vo = new AnnouncementVO();
            BeanUtils.copyProperties(p, vo);
            return vo;
        }).collect(Collectors.toList()));
        return BaseAnswer.success(data);
    }

    @Override
    public BaseAnswer<AnnouncementVO> announcementDetail(String id,String operate) {
        AnnouncementListParam param = new AnnouncementListParam();
        param.setId(id);
        List<AnnouncementDO> list = announcementMapperExt.announcementList(param);
        if(CollectionUtils.isEmpty(list)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"公告不存在");
        }
        AnnouncementVO announcementVO = new AnnouncementVO();
        AnnouncementDO announcementDO = list.get(0);
        BeanUtils.copyProperties(announcementDO,announcementVO);
        //记录日志
        StringBuilder logContent = new StringBuilder();
        logContent.append("【详情】").append("\n");
        logContent.append("公告ID ").append(announcementVO.getId()).append("\n");
        logContent.append("主题 ").append(announcementVO.getTitle()).append("\n");
        logContent.append("创建时间 ").append(DateTimeUtil.formatDate(announcementVO.getCreateTime(),DateTimeUtil.DEFAULT_DATE_DEFAULT));

        StringBuilder logContentHome = new StringBuilder();
        logContentHome.append("【查看】").append("\n");
        logContentHome.append("主题 ").append(announcementVO.getTitle()).append("\n");
        logContentHome.append("创建时间 ").append(DateTimeUtil.formatDate(announcementVO.getCreateTime(),DateTimeUtil.DEFAULT_DATE_DEFAULT));
        if ("0".equals(operate)){
            //系统设置
            logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code,
                    SystemInstallEnum.ANNOUNCEMENT_MANAGE.code,
                    logContent.toString(), LogResultEnum.LOG_SUCESS.code,null);
        }else if ("1".equals(operate)){
            //首页
            logService.recordOperateLog(ModuleEnum.HOME_PAGE.code,
                    HomePageEnum.ANNOUNCEMENT_OPERATE.code,
                    logContent.toString(), LogResultEnum.LOG_SUCESS.code,null);
        }else if ("2".equals(operate)){
            logService.recordOperateLog(ModuleEnum.HOME_PAGE.code,
                    HomePageEnum.MORE_ANNOUNCEMENT_OPERATE.code,
                    logContent.toString(), LogResultEnum.LOG_SUCESS.code,null);
        }


        return BaseAnswer.success(announcementVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer announcementVisible(AnnouncementVisibleParam param) {
        String id = param.getId();
        Boolean visible = param.getVisible();
        Announcement announcement = announcementMapper.selectByPrimaryKey(id);
        if(announcement == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"公告不存在");
        }
        Integer status = visible ? AnnouncementStatusEnum.PUBLISHED.code : AnnouncementStatusEnum.INVISIBLE.code;
        if(announcement.getStatus().intValue() == status.intValue()){
            return BaseAnswer.success(null);
        }
        announcement.setStatus(status);
        announcement.setUpdateTime(new Date());
        announcementMapper.updateByPrimaryKeySelective(announcement);
        //记录日志
        StringBuilder logContent = new StringBuilder();
        logContent.append(visible ? "【发布】" : "【隐藏】").append("\n");
        logContent.append("公告ID ").append(announcement.getId()).append("\n");
        logContent.append("主题 ").append(announcement.getTitle()).append("\n");
        logContent.append("创建时间 ").append(DateTimeUtil.formatDate(announcement.getCreateTime(),DateTimeUtil.DEFAULT_DATE_DEFAULT));
        logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code,
                SystemInstallEnum.ANNOUNCEMENT_MANAGE.code,
                logContent.toString(), LogResultEnum.LOG_SUCESS.code,null);
        return BaseAnswer.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer announcementAudit(AnnouncementAuditParam param) {
        Date now = new Date();
        String id = param.getId();
        Boolean pass = param.getPass();
        Announcement announcement = announcementMapper.selectByPrimaryKey(id);
        if(announcement == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"公告不存在");
        }
        if(announcement.getStatus().intValue() != AnnouncementStatusEnum.DRAFT.code.intValue()){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"只有【待审核】的才能审核");
        }
        announcement.setUpdateTime(now);
        announcement.setStatus(pass ? AnnouncementStatusEnum.PUBLISHED.code : AnnouncementStatusEnum.REJECTED.code);
        announcementMapper.updateByPrimaryKeySelective(announcement);

        //记录日志
        StringBuilder logContent = new StringBuilder();
        logContent.append("【审核】").append("\n");
        logContent.append("公告ID ").append(announcement.getId()).append("\n");
        logContent.append("主题 ").append(announcement.getTitle()).append("\n");
        logContent.append("创建时间 ").append(DateTimeUtil.formatDate(announcement.getCreateTime(),DateTimeUtil.DEFAULT_DATE_DEFAULT)).append("\n");
        logContent.append("审核时间 ").append(DateTimeUtil.formatDate(now,DateTimeUtil.DEFAULT_DATE_DEFAULT)).append("\n");
        logContent.append("审核结果 ").append(pass ? "通过" : "驳回");
        logService.recordOperateLog(ModuleEnum.SYSTEM_INSTALL.code,
                SystemInstallEnum.ANNOUNCEMENT_MANAGE.code,
                logContent.toString(), LogResultEnum.LOG_SUCESS.code,null);
        return BaseAnswer.success(null);
    }

    @Override
    public BaseAnswer<PageData<AnnouncementVO>> announcementPopupList(LoginIfo4Redis loginIfo4Redis) {
        PageData<AnnouncementVO> pageData = new PageData<>();
        String userId = loginIfo4Redis.getUserId();
        //获取用户今天已经弹窗的公告
        String o = stringRedisTemplate.opsForValue().get(CommonConstant.REDIS_ANNOUNCEMENT_POPUP+ userId);
        List<String> idList = new ArrayList<>();
        if(o != null){
            idList = JSONObject.parseObject(o, List.class);
        }
        log.info("已经读取的公告idList：{}",idList);
        Date now = new Date();
        AnnouncementExample announcementExample = new AnnouncementExample();
        AnnouncementExample.Criteria criteria = announcementExample.createCriteria()
                .andStatusEqualTo(AnnouncementStatusEnum.PUBLISHED.code)
                .andPopupEqualTo(true)
                .andEndTimeIsNotNull()
                .andEndTimeGreaterThanOrEqualTo(now);
        if(CollectionUtils.isNotEmpty(idList)){
            criteria.andIdNotIn(idList);
        }
        announcementExample = criteria.example();
        announcementExample.setOrderByClause("update_time DESC");
        List<Announcement> popupAnnouncementList = announcementMapper.selectByExampleWithBLOBs(announcementExample);
        log.info("查询的符合条件的公告信息：{}",popupAnnouncementList);
        if(CollectionUtils.isEmpty(popupAnnouncementList)){
            return BaseAnswer.success(pageData);
        }
        List<String> newIdList = popupAnnouncementList.stream().map(i -> {
            return i.getId();
        }).collect(Collectors.toList());
        idList.addAll(newIdList);

        Date dayEndDate = DateTimeUtil.getDayEndDate(now);
        long expiredMillSeconds = dayEndDate.getTime() - now.getTime();
        log.info("过期时间expiredMillSeconds：{}",expiredMillSeconds);
        //缓存redis弹出标志，有效期是当天剩余时间
        stringRedisTemplate.opsForValue().set(CommonConstant.REDIS_ANNOUNCEMENT_POPUP+userId, JSON.toJSONString(idList),expiredMillSeconds, TimeUnit.MILLISECONDS);
        List<AnnouncementVO> collect = popupAnnouncementList.stream().map(p -> {
            AnnouncementVO vo = new AnnouncementVO();
            BeanUtils.copyProperties(p, vo);
            return vo;
        }).collect(Collectors.toList());
        log.info("最后返回给前端的公告信息collectList：{}",collect);
        pageData.setCount(collect.size());
        pageData.setData(collect);
        return BaseAnswer.success(pageData);
    }

    @Override
    public BaseAnswer announcementRead(String id, LoginIfo4Redis loginIfo4Redis) {
        String userId = loginIfo4Redis.getUserId();
        Announcement announcement = announcementMapper.selectByPrimaryKey(id);
        if(announcement == null){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"公告不存在");
        }
        if(announcement.getStatus().intValue() != AnnouncementStatusEnum.PUBLISHED.code.intValue()){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"只有【已发布】的才能阅读");
        }
        //获取用户已读的公告id列表
        String s = stringRedisTemplate.opsForValue().get(CommonConstant.REDIS_ANNOUNCEMENT_READ+userId);
        List<String> idList = new ArrayList<>();
        if(s != null){
            idList = JSONObject.parseObject(s, List.class);
        }
        if(!idList.contains(id)){
            //更新用户已读的缓存id列表
            idList.add(id);
            stringRedisTemplate.opsForValue().set(CommonConstant.REDIS_ANNOUNCEMENT_READ+userId, JSON.toJSONString(idList));
        }
        return BaseAnswer.success(null);
    }

    @Override
    public BaseAnswer<PageData<AnnouncementVO>> announcementList4Home(AnnouncementListParam param, LoginIfo4Redis loginIfo4Redis) {
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        PageData<AnnouncementVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        String userId = loginIfo4Redis.getUserId();

        AnnouncementExample announcementExample = new AnnouncementExample();
        AnnouncementExample.Criteria criteria = announcementExample.createCriteria()
                .andStatusEqualTo(AnnouncementStatusEnum.PUBLISHED.code);
        announcementExample = criteria.example();
        announcementExample.setOrderByClause("update_time DESC");
        PageHelper.startPage(pageNum,pageSize);
        List<Announcement> announcementList = announcementMapper.selectByExampleWithBLOBs(announcementExample);
        if(CollectionUtils.isEmpty(announcementList)){
            return BaseAnswer.success(pageData);
        }
        PageInfo<Announcement> pageInfo = new PageInfo<>(announcementList);
        //获取用户已读的公告id列表
        String s = stringRedisTemplate.opsForValue().get(CommonConstant.REDIS_ANNOUNCEMENT_READ+userId);
        List<String> idList = new ArrayList<>();
        if(s != null){
            idList = JSONObject.parseObject(s, List.class);
        }

        List<String> finalIdList = idList;
        List<AnnouncementVO> collect = announcementList.stream().map(p -> {
            AnnouncementVO vo = new AnnouncementVO();
            BeanUtils.copyProperties(p, vo);
            vo.setRead(finalIdList.contains(p.getId()));
            return vo;
        }).collect(Collectors.toList());
        pageData.setCount(pageInfo.getTotal());
        pageData.setData(collect);
        return BaseAnswer.success(pageData);
    }

}

