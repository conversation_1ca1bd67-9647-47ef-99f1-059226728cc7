package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.param.EditFlowParam;
import com.chinamobile.iot.sc.pojo.param.ProductFlowListParam;
import com.chinamobile.iot.sc.pojo.vo.FlowRoleListVO;
import com.chinamobile.iot.sc.pojo.vo.FlowTypeListVO;
import com.chinamobile.iot.sc.pojo.vo.LimitListVO;
import com.chinamobile.iot.sc.pojo.vo.ProductFlowDetailVO;
import com.chinamobile.iot.sc.pojo.vo.ProductFlowListVO;
import com.chinamobile.iot.sc.service.ProductFlowInstanceService;
import com.chinamobile.iot.sc.service.ProductFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * created by liuxiang on 2024/2/26 16:22
 * 流程步骤定义，流程数据初始化，流程更新
 */
@RequestMapping("/osweb/product/flow")
@RestController
public class ProductFlowController {

    @Autowired
    private ProductFlowService productFlowService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 获取流程类型列表
     */
    @GetMapping("/typeList")
    public BaseAnswer<List<FlowTypeListVO>> getFlowTypeList(){
        return productFlowService.getFlowTypeList();
    }

    /**
     * 获取流程列表
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping("/list")
    public BaseAnswer<PageData<ProductFlowListVO>> getFlowList(ProductFlowListParam param,
                                                               @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return productFlowService.getFlowList(param,loginIfo4Redis);
    }

    /**
     * 查询流程详情
     */
    @GetMapping("/detail")
    public BaseAnswer<ProductFlowDetailVO> getFlowDetail(@RequestParam String flowId,
                                                                   @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return productFlowService.getFlowDetail(flowId,loginIfo4Redis,true);
    }


    /**
     * (后台接口)初始化流程数据，流程步骤
     */
    @PostMapping("/init")
    public BaseAnswer init(){
        try {
            return productFlowService.init();
        } catch (Exception e) {
            //如果初始化失败，编号需要清除掉
            stringRedisTemplate.delete("lcTaskCount");
            stringRedisTemplate.delete("lcTaskDay");
            throw e;
        }
    }

    /**
     * 获取限制列表
     */
    @GetMapping("/limitList")
    public BaseAnswer<List<LimitListVO>> getLimitList(){
        return productFlowService.getLimitList();
    }


    /**
     * 修改流程（实际上是将老流程变为失效，然后新增一个有效的流程。在途流程走老的，新发起流程走新的）
     */
    @PostMapping("/edit")
    public BaseAnswer editFlow(@Valid EditFlowParam param,
                               @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return productFlowService.editFlow(param,loginIfo4Redis);
    }

    /**
     * 获取产品流程相关的角色列表(不启用)
     */
    @GetMapping("/roleList")
    public BaseAnswer<List<FlowRoleListVO>> getFlowRoleList(){
        return productFlowService.getFlowRoleList();
    }

    /**
     * (查看流程审核步骤图使用)查看流程详情
     */
    @GetMapping("/inner/detail")
    public BaseAnswer<ProductFlowDetailVO> getInnerFlowDetail(@RequestParam String flowId,
                                                         @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return productFlowService.getFlowDetail(flowId,loginIfo4Redis,false);
    }

}
