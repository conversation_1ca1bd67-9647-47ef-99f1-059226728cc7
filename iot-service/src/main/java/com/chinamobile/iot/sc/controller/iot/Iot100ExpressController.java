package com.chinamobile.iot.sc.controller.iot;

import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.request.express.SubscribeExpressParam;
import com.chinamobile.iot.sc.request.express.SubscribeReq;
import com.chinamobile.iot.sc.response.iot.express.BaseKD100Resp;
import com.chinamobile.iot.sc.response.iot.express.KD100SyncParam;
import com.chinamobile.iot.sc.response.iot.express.KD100SyncParamWithSign;
import com.chinamobile.iot.sc.response.iot.express.QueryTrackResp;
import com.chinamobile.iot.sc.service.IOT100ExpressService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON>
 * @date : 2022/4/13 16:33
 * @description: Iot商城100快递使用接口
 **/
@RestController()
@RequestMapping("/os/express")
public class Iot100ExpressController {

    @Resource
    private IOT100ExpressService iot100ExpressService;

    /**
     * 快递100实时查询数据
     * @param baseRequest
     * @return
     */
    @PostMapping("/LogisticsQuery")
    public IOTAnswer<JSONObject> queryTrackRespMessage(@RequestBody IOTRequest baseRequest){
        return iot100ExpressService.queryRealTimeExpress(baseRequest);
    }

    /**
     * 快递100回调
     * @param sign
     * @param paramJson
     * @return
     */
    @PostMapping(path = "/syncByKD100",consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public BaseKD100Resp syncByKD100(@RequestParam(value = "sign",required = false) String sign,
                                     @RequestParam(value = "param") String paramJson){
        KD100SyncParamWithSign param = new KD100SyncParamWithSign();
        param.setSign(sign);
        param.setParam(JSONObject.parseObject(paramJson, KD100SyncParam.class));
        return iot100ExpressService.syncByKD100(param);
    }

    /**
     * 订阅
     * @param param
     * @return
     */
    @PostMapping("/subscribe")
    public BaseAnswer<Void> subscribe(@RequestBody SubscribeExpressParam param){
        return iot100ExpressService.subscribe(param);
    }

}
