package com.chinamobile.iot.sc.controller.newproduct;

import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.NewProductRequestManage;
import com.chinamobile.iot.sc.pojo.param.NewProductManageAuditParam;
import com.chinamobile.iot.sc.pojo.param.NewProductRequestManageAddParam;
import com.chinamobile.iot.sc.pojo.param.NewProductRequestManageUpdateParam;
import com.chinamobile.iot.sc.pojo.param.NewProductRequestManageWebParam;
import com.chinamobile.iot.sc.pojo.vo.NewProductManageDetailsVO;
import com.chinamobile.iot.sc.pojo.vo.NewProductOnlineOfflineVO;
import com.chinamobile.iot.sc.pojo.vo.NewProductOnlineRequestVO;
import com.chinamobile.iot.sc.pojo.vo.NewProductRequestManageListVO;
import com.chinamobile.iot.sc.request.product.ProvinceCityVO;
import com.chinamobile.iot.sc.service.NewProductRequestManageService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.chinamobile.iot.sc.common.BaseConstant.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/13
 * @description 新产品引入申请管理controller类
 */
@RestController
@RequestMapping(value = "/osweb/newProduct")
public class NewProductRequestManageController {

    @Resource
    private NewProductRequestManageService newProductRequestManageService;

    /**
     * 新产品可上架的申请列表
     * @return
     */
    @GetMapping(value = "/listReadyOnline")
    @Auth(authCode = {PRODUCT_UP_DOWN_FRAME})
    public BaseAnswer<List<NewProductOnlineRequestVO>> listNewProductReadyOnline(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        List<NewProductOnlineRequestVO> newProductOnlineRequestVOList
                = newProductRequestManageService.listFirstNewProductOnlineRequest(loginIfo4Redis);
        return new BaseAnswer<List<NewProductOnlineRequestVO>>().setData(newProductOnlineRequestVOList);
    }

    /**
     * 产品引入申请新增
     * @param addParam
     * @param loginIfo4Redis
     * @return
     */
    @Auth(authCode = {PRODUCT_APPLY_MANAGE})
    @PostMapping("/save")
    public BaseAnswer saveNewProductRequestManage(@RequestBody @Valid NewProductRequestManageAddParam addParam
        ,@RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){

        newProductRequestManageService.insetNewProductRequestManage(addParam,userId,loginIfo4Redis);

        return new BaseAnswer();
    }

    /**
     * 更新产品信息，重新提交申请审批
     * @param updateParam
     * @param loginIfo4Redis
     * @return
     */
    @Auth(authCode = {PRODUCT_APPLY_MANAGE})
    @PostMapping("/update")
    public BaseAnswer updateNewProductRequestManageMessage(@RequestBody @Valid NewProductRequestManageUpdateParam updateParam
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        newProductRequestManageService.updateNewProductRequestManage(updateParam,loginIfo4Redis);
        return new BaseAnswer();
    }

    /**
     * 批量导入产品信息
     * @param file
     * @param userId
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping("/file")
    @Auth(authCode = {PRODUCT_APPLY_MANAGE})
    public void saveImportProductRequest(@RequestParam("file") MultipartFile file
            , @RequestHeader(name = Constant.HEADER_KEY_USER_ID) String userId
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        newProductRequestManageService.importProductRequest(file,userId,loginIfo4Redis);

    }


    /**
     * 根据主键id 查询产品详情信息
     * @param id
     * @return
     */
    @GetMapping(value = "/getNewProductManageDetails")
    public BaseAnswer<NewProductManageDetailsVO> getProductManageDetailsMessage(@RequestParam("id") String id) {
        NewProductManageDetailsVO productManageDetails = newProductRequestManageService.getProductManageDetails(id);
        return new BaseAnswer<NewProductManageDetailsVO>().setData(productManageDetails);
    }


    /**
     * 分页查询产品申请信息
     * @param manageWebParam
     * @return
     */
    @Auth(authCode = {PRODUCT_APPLY_MANAGE})
    @GetMapping(value = "/pageNewProductManageList")
    public BaseAnswer<PageData<NewProductRequestManageListVO>> pageNewProductManageList(NewProductRequestManageWebParam manageWebParam
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        PageData<NewProductRequestManageListVO> pageData = newProductRequestManageService.getPageManageApplyList(manageWebParam,loginIfo4Redis);
        return new BaseAnswer<PageData<NewProductRequestManageListVO>>().setData(pageData);
    }

    /**
     * 审核产品
     * @param auditParam
     * @param loginIfo4Redis
     * @return
     */
    @Auth(authCode = {PRODUCT_APPLY_MANAGE})
    @PostMapping(value = "/saveProductAuditDispose")
    public BaseAnswer saveProductAuditDispose(@RequestBody @Valid NewProductManageAuditParam auditParam
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        newProductRequestManageService.insertProductAuditDispose(auditParam,loginIfo4Redis,ip);
        return new BaseAnswer();
    }


    /**
     * 查询还在审核中的商品（产品申请审核，上下架审核）
     * @param userId
     * @return
     */
    @GetMapping(value = "/getInReview")
    public BaseAnswer<Integer> getProductByRequestUserIdOrOnlineUserIdInReview(@RequestParam("userId") String userId) {
        Integer count = newProductRequestManageService.getProductByRequestUserIdOrOnlineUserId(userId);
        return new BaseAnswer<Integer>().setData(count);
    }

    /**
     * 查询省份城市编码列表
     * @return
     */
    @GetMapping(value = "/getProvinceCity")
    public BaseAnswer<List<ProvinceCityVO>> getProvinceCityListMessage(){
        List<ProvinceCityVO> provinceCityList = newProductRequestManageService.getProvinceCityList();
        return new BaseAnswer<List<ProvinceCityVO>>().setData(provinceCityList);
    }

}
