package com.chinamobile.iot.sc.controller.iot;

import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.param.OneLinkRealNameResultParam;
import com.chinamobile.iot.sc.pojo.vo.OneLinkRealNameResultVO;
import com.chinamobile.iot.sc.service.RealNameService;
import com.chinamobile.iot.sc.task.OneLinkTokenRefreshTask;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * created by l<PERSON>xia<PERSON> on 2023/9/25 14:19
 * 实名认证相关接口
 */
@RestController
@RequestMapping("/os/realname")
public class RealNameController {

    @Autowired
    private RealNameService realNameService;

    @Autowired
    private OneLinkTokenRefreshTask oneLinkTokenRefreshTask;

    /**
     * 通过oneLink,获取实名认证url(商城 -> OS)
     */
    @PostMapping("/videoUrlRealName")
    public IOTAnswer getVideoUrl(@RequestBody IOTRequest request){
        return realNameService.getVideoUrl(request);
    }

    /**
     * 接受oneLink的实名认证结果反馈并转发给商城(oneLink -> OS)
     */
    @PostMapping("/result")
    public OneLinkRealNameResultVO realNameResult(@RequestBody OneLinkRealNameResultParam param){
        return realNameService.realNameResult(param);
    }

    /**
     * 测试刷新token
     * @return
     */
    @PostMapping("/testToken")
    public String testToken(){
        oneLinkTokenRefreshTask.work();
        return "ok";
    }

}
