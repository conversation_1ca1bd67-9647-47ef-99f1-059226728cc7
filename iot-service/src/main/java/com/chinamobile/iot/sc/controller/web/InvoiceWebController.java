package com.chinamobile.iot.sc.controller.web;

import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.invoice.ApplyInvoiceInfo;
import com.chinamobile.iot.sc.pojo.invoice.InvoiceReverseInfo;
import com.chinamobile.iot.sc.request.invoice.InvoiceRevNewRequest;
import com.chinamobile.iot.sc.request.invoice.InvoiceRevRequest;
import com.chinamobile.iot.sc.request.invoice.Request4InvoRevPage;
import com.chinamobile.iot.sc.response.web.invoice.Data4ApplyInvoiceRec;
import com.chinamobile.iot.sc.request.invoice.Request4InvoRecPage;
import com.chinamobile.iot.sc.response.web.invoice.Data4InvoiceRevRec;
import com.chinamobile.iot.sc.service.IInvoiceService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * @package: com.chinamobile.iot.sc.controller.web
 * @ClassName: InvoiceWebController
 * @description: 发票管理Controller-对接前端页面
 * @author: zyj
 * @create: 2021/11/29 10:14
 * @Company: Copyright 2021-2099 CMIot Tech. Co. Ltd.  All Rights Reserved.
 **/
@RequestMapping("/osweb/invoice")
@RestController
public class InvoiceWebController {
    @Resource
    private IInvoiceService invoiceService;

    // 录入发票信息，并将发票文件上传至OS系统，OS将文件同步至IoT商城，OS上发票状态为开票成功，完成开票
    @Auth(authCode = {BaseConstant.IOT_INVOICE_HANDLE})
    @PostMapping("/sync/invoicingResult2IoTMall")
    public BaseAnswer<Void> invoicingResult2IOTMall(@RequestParam String invoiceEntryListJSON, String orderSeq
            , @RequestPart(value = "files", required = false)MultipartFile[] files, @RequestParam(required = false) String result,LoginIfo4Redis loginIfo4Redis){
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return invoiceService.invoicingResult2IOTMall(invoiceEntryListJSON, orderSeq, files, result,loginIfo4Redis.getUserId(),ip);
    }

    /**
     *@Description: 根据发票申请记录id或原子订单id模糊查询发票申请记录
     *@param request
     *@param userId
     *@param loginIfo4Redis
     *@return: ApplyInvoiceRec
     *@Author: zyj
     */
    @Auth(authCode = {BaseConstant.IOT_INVOICE_QUERY})
    @PostMapping("/findPage/byInvoiceOrOrder")
    BaseAnswer<PageData<Data4ApplyInvoiceRec>> findPageByInvoiceIdOrOrderId(@RequestBody Request4InvoRecPage request
            , @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return invoiceService.findPageByInvoiceIdOrOrderId(request, userId, loginIfo4Redis);
    }

    /**
     *@Description: 根据发票申请记录id或原子订单id模糊查询发票申请记录待办
     *@param request
     *@param userId
     *@param loginIfo4Redis
     *@return: ApplyInvoiceRec
     *@Author: zyj
     */
    @PostMapping("/findPage/backlog/byInvoiceOrOrder")
    BaseAnswer<PageData<Data4ApplyInvoiceRec>> findPageBacklogByInvoiceIdOrOrderId(@RequestBody Request4InvoRecPage request
            , @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return invoiceService.findPageByInvoiceIdOrOrderId(request, userId, loginIfo4Redis);
    }

    @GetMapping("/isSucc/invoice/{orderId}")
    BaseAnswer<Boolean> isSuccInvoiceByOrderId(@PathVariable String orderId){
        return invoiceService.isSuccInvoiceByOrderId(orderId);
    }

    // 根据orderId查询发票列表信息
    @Auth(authCode = {BaseConstant.IOT_INVOICE_DETAIL})
    @PostMapping("/find/invoices")
    public BaseAnswer<List<ApplyInvoiceInfo>> findInvoicesByOrderId(String orderId, String orderSeq
            ,@RequestHeader(Constant.HEADER_KEY_USER_ID) String userId
            ,@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return invoiceService.findInvoicesByOrderId(orderId, orderSeq, userId, loginIfo4Redis);
    }

    /**
     * 发票冲红录入请求-反馈至IOT商城
     */
    @Auth(authCode = {BaseConstant.IOT_REVINVO_HANDLE})
    @PostMapping("/sync/invoiceVoidCallback")
    public BaseAnswer<Void> invoiceVoidCallback(@RequestBody InvoiceRevNewRequest invoiceRevRequest
//            @RequestBody List<InvoiceRevRequest> invoiceRevRequests
            ,String orderSeq
            ,@RequestParam(required = false) String result
            ,@RequestParam(required = false)  String errorDesc,
                                                LoginIfo4Redis loginIfo4Redis) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        return invoiceService.invoiceVoidCallback(invoiceRevRequest, result, errorDesc,ip,loginIfo4Redis.getUserId());
    }

    /**
     * 发票冲红申请记录-分页查询
     */
    @Auth(authCode = {BaseConstant.IOT_REVINVO_QUERY})
    @PostMapping("/findPage/rev/byOrderOrId")
    BaseAnswer<PageData<Data4InvoiceRevRec>> findPageByRevIdOrOrderId(@RequestBody Request4InvoRevPage request
            , @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return invoiceService.findPageByRevIdOrOrderId(request, userId, loginIfo4Redis);
    }
    /**
     * 发票冲红-发票信息查询
     */
    @Auth(authCode = {BaseConstant.IOT_REVINVO_DETAIL})
    @PostMapping("/find/rev/invoices")
    public BaseAnswer<List<InvoiceReverseInfo>> findRevInfoByOrderId(String orderId, String orderSeq
            , @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        return invoiceService.findRevInfoByOrderId(orderId, orderSeq, userId, loginIfo4Redis);
    }

    /**
     * 发票冲红申请记录待办-分页查询
     */
    @PostMapping("/findPage/rev/backlog/byOrderOrId")
    BaseAnswer<PageData<Data4InvoiceRevRec>> findPageBacklogByRevIdOrOrderId(@RequestBody Request4InvoRevPage request
            , @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId
            , @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return invoiceService.findPageByRevIdOrOrderId(request, userId, loginIfo4Redis);
    }

    //未开票发票信息导出
    @GetMapping("/exportNotInvoiceExcel")
    public void exportNotInvoiceExcel(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                                      @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId) {
        invoiceService.exportNotInvoiceExcel(loginIfo4Redis, userId);
    }


    //未冲红发票信息导出
    @GetMapping("/exportNotRedFlushExcel")
    public void exportNotRedFlushExcel(@RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                                       @RequestHeader(Constant.HEADER_KEY_USER_ID) String userId) {
        invoiceService.exportNotRedFlushExcel(loginIfo4Redis, userId);
    }

}
