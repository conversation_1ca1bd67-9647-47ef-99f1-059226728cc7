package com.chinamobile.iot.sc.service.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/27
 * @description 账目项ID割接模板
 */
@Data
public class AtomChargeCodeExcel {

    @ExcelProperty(value = "原子商品编码（offeringCode）", index = 0)
    private String atomOfferingCode;

    @ExcelProperty(value = "账目项ID（chargeCode）", index = 1)
    private String chargeCode;
}
