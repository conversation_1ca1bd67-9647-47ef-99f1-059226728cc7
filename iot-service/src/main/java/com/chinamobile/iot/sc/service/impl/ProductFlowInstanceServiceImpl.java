package com.chinamobile.iot.sc.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.KafkaTopic;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.common.utils.BaseUtils;
import com.chinamobile.iot.sc.common.utils.RegexUtil;
import com.chinamobile.iot.sc.config.ProductFlowSmsConfig;
import com.chinamobile.iot.sc.config.ServiceConfig;
import com.chinamobile.iot.sc.constant.productflow.*;
import com.chinamobile.iot.sc.dao.*;
import com.chinamobile.iot.sc.dao.ext.ProductFlowInstanceMapperExt;
import com.chinamobile.iot.sc.dao.ext.ProductFlowInstanceSkuMapperExt;
import com.chinamobile.iot.sc.dao.ext.ProductFlowInstanceSpuMapperExt;
import com.chinamobile.iot.sc.dao.ext.ProductFlowInstanceTaskMapperExt;
import com.chinamobile.iot.sc.entity.ByteArrayUpload;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.entity.UpResult;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.ProductManageOperateEnum;
import com.chinamobile.iot.sc.excel.ExcelProductFlowMergeStrategy;
import com.chinamobile.iot.sc.exception.StatusConstant;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;
import com.chinamobile.iot.sc.feign.IotFeignClient;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.*;
import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.pojo.dto.*;
import com.chinamobile.iot.sc.pojo.entity.ProductFlowInstanceDirectoryOnline;
import com.chinamobile.iot.sc.pojo.entity.ProductFlowInstanceDirectoryOnlineExample;
import com.chinamobile.iot.sc.pojo.entity.ProductNavigationDirectoryCut;
import com.chinamobile.iot.sc.pojo.entity.ProductNavigationDirectoryOnline;
import com.chinamobile.iot.sc.pojo.mapper.*;
import com.chinamobile.iot.sc.pojo.mapper.productFlowInfo.ProductFlowAllExportDO;
import com.chinamobile.iot.sc.pojo.mapper.productFlowInfo.ShelfSpuDetailDO;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.*;
import com.chinamobile.iot.sc.pojo.vo.productFlowInfo.*;
import com.chinamobile.iot.sc.service.BaseSmsService;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.service.ProductFlowInstanceService;
import com.chinamobile.iot.sc.util.AuthCodeUtils;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import com.chinamobile.iot.sc.util.ExcelHeaderCheckUtil;
import com.chinamobile.iot.sc.util.ProductFlowUtil;
import com.chinamobile.iot.sc.util.excel.EasyExcelDTO;
import com.chinamobile.iot.sc.util.excel.EasyExcelUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.constant.RedisLockConstant.PRODUCT_FLOW_SHELF_LOCK;

/**
 * created by liuxiang on 2024/2/26 16:24
 */
@Service
@Slf4j
public class ProductFlowInstanceServiceImpl implements ProductFlowInstanceService {
    private ExecutorService executorService = new ThreadPoolExecutor(8, 16, 1, TimeUnit.MINUTES, new LinkedBlockingQueue<>(100000));

    @Autowired
    private KafkaTemplate<String, byte[]> kafkaTemplate;
    @Autowired
    private SmsFeignClient smsFeignClient;
    @Autowired
    private IotFeignClient iotFeignClient;
    @Autowired
    private ServiceConfig serviceConfig;
    @Autowired
    private RedisTemplate redisTemplate;
    @Resource
    private ProductFlowInstanceSpuMapperExt ProductFlowInstanceSpuMapperExt;
    @Resource
    private ProductShelfCategoryCostMapper productShelfCategoryCostMapper;

    @Resource
    private ProductNavigationDirectoryMapper productNavigationDirectoryMapper;

    @Autowired
    private OneNetObjectStorageService storageService;

    @Resource
    private ProductFlowInstanceAttachmentMapper productFlowInstanceAttachmentMapper;

    @Resource
    private ProductFlowMapper productFlowMapper;

    @Resource
    private ProductFlowInstanceMapper productFlowInstanceMapper;

    @Resource
    private ProductFlowStepMapper productFlowStepMapper;

    @Resource
    private ProductFlowInstanceTaskMapper productFlowInstanceTaskMapper;

    @Resource
    private ProductFlowInstanceSpuMapper productFlowInstanceSpuMapper;

    @Resource
    private ProductFlowInstanceSkuMapper productFlowInstanceSkuMapper;

    @Resource
    private ProductFlowInstanceAtomMapper productFlowInstanceAtomMapper;

    @Resource
    private ProductFlowInstanceConfigMapper productFlowInstanceConfigMapper;

    @Resource
    private ProductFlowInstanceSkuMapperExt productFlowInstanceSkuMapperExt;

    @Resource
    private ProductFlowInstanceMapperExt productFlowInstanceMapperExt;

    @Resource
    private ProductFlowInstanceTaskMapperExt productFlowInstanceTaskMapperExt;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private BaseSmsService baseSmsService;

    @Autowired
    private ProductFlowSmsConfig productFlowSmsConfig;
    @Autowired
    private RedisUtil redisUtil;
    @Resource
    private ProductFlowUtil productFlowUtil;

    @Autowired
    private LogService logService;

    @Autowired
    private ProductFlowInstanceServiceImpl productFlowInstanceServiceImpl;

    @Autowired
    private ProductFlowInstanceDirectoryMapper productFlowInstanceDirectoryMapper;


    @Resource
    private ProductNavigationDirectoryOnlineMapper productNavigationDirectoryOnlineMapper;

    @Resource
    private ProductNavigationDirectoryCutMapper productNavigationDirectoryCutMapper;

    @Resource
    private ProductFlowInstanceDirectoryOnlineMapper productFlowInstanceDirectoryOnlineMapper;


    ThreadFactory springThreadFactory = new CustomizableThreadFactory("springThread-pool-");
    ThreadPoolExecutor executor = new ThreadPoolExecutor(4, 10, 4L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(100000), springThreadFactory);

    @Override
    public BaseAnswer<PageData<ShelfSpuInfoVO>> getShelfSpuList(LoginIfo4Redis loginIfo4Redis, String userId, FlowInstanceSpuListParam param) {
        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(dataPermissionCodes) || (
                !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_QUERY_SYSTEM)
                        && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_QUERY_PERSONAL)
        )) {
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }
        Integer pageSize = param.getPageSize();
        Integer pageNum = param.getPageNum();
        PageData<ShelfSpuInfoVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        PageHelper.startPage(pageNum, pageSize);
        // 商品名称搜索支持反斜杠适配
        if (param.getSpuOfferingName() != null) {
            param.setSpuOfferingName(param.getSpuOfferingName().replaceAll("\\\\", "\\\\\\\\"));
        }
        if (param.getSkuOfferingName() != null) {
            param.setSkuOfferingName(param.getSkuOfferingName().replaceAll("\\\\", "\\\\\\\\"));
        }
        List<ShelfSpuInfoDO> list = null;
        //这里会对人员角色进行判断。如果是超管和运营管理员 客服管理员看到的应该是全部的库存信息，如果是合作伙伴看到的应该只有属于合作伙伴的信息
        List<String> userIdList = new ArrayList<>();
        //TODO  数据权限处理？
        //系统数据 如果是超管,产品运营管理员,产品运营负责人查询所有
        if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_QUERY_SYSTEM)) {
            //获取全部信息根据条件查询 数量
            list = ProductFlowInstanceSpuMapperExt.getFlowInstanceSpuList(param, userIdList);
        }
//        if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_QUERY_COMPANY)) {
//            BaseAnswer<List<String>> downUserIds = userFeignClient.getDownUserIds(userId);
//            if (CollectionUtil.isNotEmpty(downUserIds.getData())){
//                userIdList = downUserIds.getData();
//            }
//            userIdList.add(userId);
//            //获取全部信息根据条件查询 数量
//            list = ProductFlowInstanceSpuMapperExt.getFlowInstanceSpuList(param,userIdList);
//        }

        if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PRODUCT_QUERY_PERSONAL)) {
            //个人数据 如果当前产品经理,运营支撑管理员,备案管理员,配置专员 查询自己的商品配置
//            param.setUserId(userId);
            userIdList.add(userId);
            list = ProductFlowInstanceSpuMapperExt.getFlowInstanceSpuList(param, userIdList);

        }
        PageInfo<ShelfSpuInfoDO> pageInfo = new PageInfo<>(list);
        pageData.setCount(pageInfo.getTotal());
        List<ShelfSpuInfoVO> lst = Collections.emptyList();
        if (!org.springframework.util.CollectionUtils.isEmpty(list)) {
            lst = list.stream().map(ShelfSpuInfo -> {
                ShelfSpuInfoVO ShelfSpuInfoVO = new ShelfSpuInfoVO();
                BeanUtils.copyProperties(ShelfSpuInfo, ShelfSpuInfoVO);
                //设置是否可以二次编辑，只有存量导入的可以
                ProductFlowInstance productFlowInstance = productFlowInstanceMapper.selectByPrimaryKey(ShelfSpuInfoVO.getFlowInstanceId());
                if ("存量导入".equals(productFlowInstance.getCreatorName())) {
                    ShelfSpuInfoVO.setCanEdit(true);
                }
                return ShelfSpuInfoVO;
            }).collect(Collectors.toList());
        }
        pageData.setData(lst);
        return BaseAnswer.success(pageData);

    }


    @Override
    public BaseAnswer<ShelfSpuDetailVO> getShelfSpuDeatail(String flowInstanceId, String spuCode, String skuCode) {
        if (StringUtils.isBlank(flowInstanceId)) {
            logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code, "【查看】", LogResultEnum.LOG_FAIL.code, "流程ID不能为空");
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程ID不能为空");
        }

        //未上架流程id查询查询产品信息，上架后还需要根据spuCode和skuCode查询
        ShelfSpuDetailDO spuDeatail = ProductFlowInstanceSpuMapperExt.getFlowInstanceSpuDetail(flowInstanceId, spuCode, skuCode);
        if (spuDeatail == null) {
            logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code, "【查看】", LogResultEnum.LOG_FAIL.code, "未查到商品对应信息");
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未查到商品对应信息");
        }

        ProductFlowInstanceAttachmentExample example = new ProductFlowInstanceAttachmentExample().createCriteria().andFlowInstanceIdEqualTo(spuDeatail.getSkuItem().getFlowInstanceId()).example();
        //获取附件
        List<ProductFlowInstanceAttachment> attacmentList = productFlowInstanceAttachmentMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(attacmentList)) {
            List<AttachmentListVO> collect = attacmentList.stream().map(data -> {
                AttachmentListVO vo = new AttachmentListVO();
                BeanUtils.copyProperties(data, vo);
                return vo;
            }).collect(Collectors.toList());
            spuDeatail.setFileList(collect);

        }
        //获取原子信息
        ProductFlowInstanceAtomExample atomExample = new ProductFlowInstanceAtomExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
        List<ProductFlowInstanceAtom> productFlowInstanceAtoms = productFlowInstanceAtomMapper.selectByExample(atomExample);
        if (!productFlowInstanceAtoms.isEmpty()) {
            List<ShelfSpuDetailAtomVO> atomInfoList = productFlowInstanceAtoms.stream().map(a -> {
                ShelfSpuDetailAtomVO atomInfo = new ShelfSpuDetailAtomVO();
                BeanUtils.copyProperties(a, atomInfo);
                if (StringUtils.isNotEmpty(atomInfo.getCmiotCostProjectId())) {
                    atomInfo.setCmiotCostProjectName(productShelfCategoryCostMapper.selectByPrimaryKey(atomInfo.getCmiotCostProjectId()).getName());
                }
                return atomInfo;
            }).collect(Collectors.toList());
            spuDeatail.setAtomList(atomInfoList);
        }
        //如果未上架，根据流程id直接获取流程信息。如果已上架，需要根据spucode和skucode获取所有的流程，
        if ((StringUtils.isNotBlank(spuDeatail.getSkuItem().getSkuCode()) && StringUtils.isNotBlank(spuDeatail.getSpuItem().getSpuCode()))) {
            //先查询所有流程实例id
            ProductFlowInstanceSkuExample skuExample = new ProductFlowInstanceSkuExample().createCriteria().andSkuCodeEqualTo(spuDeatail.getSkuItem().getSkuCode()).andSpuCodeEqualTo(spuDeatail.getSpuItem().getSpuCode()).example();

            List<ProductFlowInstanceSku> skuList = productFlowInstanceSkuMapper.selectByExample(skuExample);
            if (skuList != null && skuList.size() > 0) {
                List<ProductFlowDO> collect = skuList.stream().map(data -> {
                    ProductFlow flow = productFlowMapper.selectByPrimaryKey(data.getFlowId());
                    ProductFlowDO flowInfo = new ProductFlowDO();
                    BeanUtils.copyProperties(flow, flowInfo);
                    flowInfo.setFlowInstanceId(data.getFlowInstanceId());
                    flowInfo.setNumber(productFlowInstanceMapper.selectByPrimaryKey(data.getFlowInstanceId()).getFlowInstanceNumber());
                    ProductFlowTypeEnum[] values = ProductFlowTypeEnum.values();
                    String flowName = spuDeatail.getSkuItem().getSkuName();
                    for (ProductFlowTypeEnum value : values) {
                        if (value.code.intValue() == flow.getFlowType()) {
                            flowName = flowName + value.name;
                        }
                    }
                    flowInfo.setName(flowName);

                    return flowInfo;
                }).collect(Collectors.toList());

                spuDeatail.setFlowInstanceList(collect);
            }

        } else {
            ProductFlow flow = productFlowMapper.selectByPrimaryKey(spuDeatail.getSkuItem().getFlowId());
            ProductFlowDO flowInfo = new ProductFlowDO();
            //设置流程实例编号
            BeanUtils.copyProperties(flow, flowInfo);
            flowInfo.setNumber(productFlowInstanceMapper.selectByPrimaryKey(flowInstanceId).getFlowInstanceNumber());
            ProductFlowTypeEnum[] values = ProductFlowTypeEnum.values();
            String flowName = spuDeatail.getSkuItem().getSkuName();
            for (ProductFlowTypeEnum value : values) {
                if (value.code.intValue() == flow.getFlowType()) {
                    flowName = flowName + value.name;
                }
            }
            productFlowInstanceMapper.selectByPrimaryKey(flowInstanceId).getFlowInstanceNumber();
            flowInfo.setName(flowName);
            flowInfo.setFlowInstanceId(flowInstanceId);
            List<ProductFlowDO> collect = new ArrayList<>();
            collect.add(flowInfo);
            spuDeatail.setFlowInstanceList(collect);
//            BeanUtils.copyProperties(collect, spuDeatail.getFileList());
        }

        //根据流程实例获取流程信息

        ShelfSpuDetailVO spuInfo = new ShelfSpuDetailVO();
        BeanUtils.copyProperties(spuDeatail, spuInfo);

        if (spuDeatail.getSpuItem() != null && spuDeatail.getSkuItem() != null) {
            ShelfSpuDetailSpuVO spuItem = new ShelfSpuDetailSpuVO();
            ShelfSpuDetailSkuVO skuItem = new ShelfSpuDetailSkuVO();
            //获取流程导航目录信息
            List<ProductFlowNavigationDirectoryDO> navigationDirectoryList =productFlowInstanceMapperExt.getNavigationDirectoryList(flowInstanceId);
            if(CollectionUtils.isNotEmpty(navigationDirectoryList)){
                List<ProductFlowInstanceDetailVO.NavigationDirectory> directoryList = getNavigationDirectoryList(navigationDirectoryList);
                spuItem.setNavigationDirectoryList(directoryList);
            }

            BeanUtils.copyProperties(spuDeatail.getSpuItem(), spuItem);
            BeanUtils.copyProperties(spuDeatail.getSkuItem(), skuItem);
            spuItem.setProductTypeName(ProductTypeEnum.getName(spuDeatail.getSpuItem().getProductType()));
            spuItem.setProductStandardName(ProductStandardEnum.getName(spuDeatail.getSpuItem().getProductStandard()));
            spuInfo.setSpuItem(spuItem);
            spuInfo.setSkuItem(skuItem);
        }
        //日志记录
        List<String> skuNameList = new ArrayList<>();

        String content = getLogContent("【查看】", null, spuInfo.getSpuItem().getSpuName(), spuInfo.getSkuItem().getSkuName(), null, null, null, null, null, null, null);
        if (StringUtils.isNotEmpty(content)) {
            logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code, content, LogResultEnum.LOG_SUCESS.code, null);
        }

        return BaseAnswer.success(spuInfo);

    }

    @Override
    public BaseAnswer shelfSpuDetailRecord(ShelfSpuDetailRecordParam param) {
        String downloadAttachmentList = CollectionUtils.isEmpty(param.getUrlList()) ? null : String.join(",", param.getUrlList());
        String content = getLogContent("【查看】", null, param.getSpuName(), param.getSkuName(), null, downloadAttachmentList, null, null, null, null, null);

        if (StringUtils.isNotEmpty(content)) {
            logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code, content, LogResultEnum.LOG_SUCESS.code, null);

        }
        return BaseAnswer.success(null);
    }


    private <T> T copyExportProperties(T info, ProductFlowInstanceSpu spuInfo, ProductFlowInstanceSku skuInfo,
                                       ProductFlowInstanceAtom atomInfo, ProductFlowInstanceConfig configInfo) {
        BeanUtils.copyProperties(spuInfo, info);
        BeanUtils.copyProperties(skuInfo, info);
        if (atomInfo != null) {
            BeanUtils.copyProperties(atomInfo, info);
        }
        if (configInfo != null) {
            BeanUtils.copyProperties(configInfo, info);
        }

        return info;
    }

    @Override
    public void skuExport(LoginIfo4Redis loginIfo4Redis, String flowInstanceId, String spuCode, String skuCode, String ip) {
        try {
            if (StringUtils.isBlank(flowInstanceId)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code,
                            "【导出】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "流程ID或原子ID不能为空");

                });
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程ID或原子ID不能为空");
            }
            ProductFlowInstanceSpu spuInfo;
            ProductFlowInstanceSku skuInfo;
            ProductFlowInstanceAtom atomInfo = null;
            ProductFlowInstanceConfig configInfo = null;
            //根据上架，未上架状况，获取信息
            if (StringUtils.isBlank(skuCode) && StringUtils.isNotBlank(flowInstanceId)) {
                //未上架  根据flowInstanceId,查询商品
                //获取spu信息

                ProductFlowInstanceSpuExample spuExample = new ProductFlowInstanceSpuExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
                List<ProductFlowInstanceSpu> spuList = productFlowInstanceSpuMapper.selectByExample(spuExample);
                if (spuList.isEmpty()) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code,
                                "【导出】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "未找到对应spu");
                    });
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到对应spu");
                }
                spuInfo = spuList.get(0);
                //获取sku信息
                ProductFlowInstanceSkuExample skuExample = new ProductFlowInstanceSkuExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
                List<ProductFlowInstanceSku> skuList = productFlowInstanceSkuMapper.selectByExample(skuExample);
                if (skuList.isEmpty()) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code,
                                "【导出】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "未找到对应sku");
                    });
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到对应sku");
                }
                skuInfo = skuList.get(0);
                if (skuInfo.getSalePrice() != null) {
                    skuInfo.setSalePrice(skuInfo.getSalePrice() / 1000);
                }
                if (skuInfo.getSaleMinPrice() != null) {
                    skuInfo.setSaleMinPrice(skuInfo.getSaleMinPrice() / 1000);
                }
                if (skuInfo.getSaleMaxPrice() != null) {
                    skuInfo.setSaleMaxPrice(skuInfo.getSaleMaxPrice() / 1000);
                }
                if (skuInfo.getProvincePrice() != null) {
                    skuInfo.setProvincePrice(skuInfo.getProvincePrice() / 1000);
                }

                //获取原子商品信息
                ProductFlowInstanceAtomExample atomExample = new ProductFlowInstanceAtomExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
                List<ProductFlowInstanceAtom> atomList = productFlowInstanceAtomMapper.selectByExample(atomExample);
                if (atomList.isEmpty()) {
//                         throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到对应原子商品信息");
                } else {
                    atomInfo = atomList.get(0);
                }

                if (atomInfo != null && atomInfo.getSettlePrice() != null) {
                    atomInfo.setSettlePrice(atomInfo.getSettlePrice() / 1000);
                }
                if (atomInfo != null && atomInfo.getSettlePriceCheck() != null) {
                    atomInfo.setSettlePriceCheck(atomInfo.getSettlePriceCheck() / 1000);
                }
                if (atomInfo != null && atomInfo.getHardwarePrice() != null) {
                    atomInfo.setHardwarePrice(atomInfo.getHardwarePrice() / 1000);
                }
                if (atomInfo != null && atomInfo.getSoftSettlePrice() != null) {
                    atomInfo.setSoftSettlePrice(atomInfo.getSoftSettlePrice() / 1000);
                }
                if (atomInfo != null && atomInfo.getSoftPrice() != null) {
                    atomInfo.setSoftPrice(atomInfo.getSoftPrice() / 1000);
                }
                if (atomInfo != null && atomInfo.getSoftTotalPrice() != null) {
                    atomInfo.setSoftTotalPrice(atomInfo.getSoftTotalPrice() / 1000);
                }
                if (atomInfo != null && atomInfo.getZhuanheSettlePrice() != null) {
                    atomInfo.setZhuanheSettlePrice(atomInfo.getZhuanheSettlePrice() / 1000);
                }
                if (atomInfo != null && atomInfo.getSalePrice() != null) {
                    atomInfo.setSalePrice(atomInfo.getSalePrice() / 1000);
                }
                if (atomInfo != null && atomInfo.getSaleMinPrice() != null) {
                    atomInfo.setSaleMinPrice(atomInfo.getSaleMinPrice() / 1000);
                }
                if (atomInfo != null && atomInfo.getSaleMaxPrice() != null) {
                    atomInfo.setSaleMaxPrice(atomInfo.getSaleMaxPrice() / 1000);
                }

                //获取商品配置信息
                ProductFlowInstanceConfigExample configExample = new ProductFlowInstanceConfigExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
                List<ProductFlowInstanceConfig> configList = productFlowInstanceConfigMapper.selectByExample(configExample);
                if (configList.isEmpty()) {
//                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到商品对应配置信息");
                } else {
                    configInfo = configList.get(0);
                }


            } else if (StringUtils.isNotBlank(skuCode) && StringUtils.isNotBlank(flowInstanceId)) {
                //已上架 根据spuCode,skuCode,flowInstanceId 查询商品

                ProductFlowInstanceSpuExample spuExample = new ProductFlowInstanceSpuExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).andSpuCodeEqualTo(spuCode).example();
                List<ProductFlowInstanceSpu> spuList = productFlowInstanceSpuMapper.selectByExample(spuExample);
                if (spuList.isEmpty()) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code,
                                "【导出】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "未找到对应spu");
                    });
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到对应spu");
                }
                spuInfo = spuList.get(0);
                //获取sku信息
                ProductFlowInstanceSkuExample skuExample = new ProductFlowInstanceSkuExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).andSpuCodeEqualTo(spuCode).andSkuCodeEqualTo(skuCode).example();
                List<ProductFlowInstanceSku> skuList = productFlowInstanceSkuMapper.selectByExample(skuExample);
                if (skuList.isEmpty()) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code,
                                "【导出】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "未找到对应sku");
                    });
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到对应sku");
                }
                skuInfo = skuList.get(0);
                if (skuInfo.getSalePrice() != null) {
                    skuInfo.setSalePrice(skuInfo.getSalePrice() / 1000);
                }
                if (skuInfo.getSaleMinPrice() != null) {
                    skuInfo.setSaleMinPrice(skuInfo.getSaleMinPrice() / 1000);
                }
                if (skuInfo.getSaleMaxPrice() != null) {
                    skuInfo.setSaleMaxPrice(skuInfo.getSaleMaxPrice() / 1000);
                }
                if (skuInfo.getProvincePrice() != null) {
                    skuInfo.setProvincePrice(skuInfo.getProvincePrice() / 1000);
                }
                //获取原子商品信息
                ProductFlowInstanceAtomExample atomExample = new ProductFlowInstanceAtomExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).andSpuCodeEqualTo(spuCode).andSkuCodeEqualTo(skuCode).example();
                List<ProductFlowInstanceAtom> atomList = productFlowInstanceAtomMapper.selectByExample(atomExample);
                if (atomList.isEmpty()) {
//                         throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到对应原子商品信息");
                } else {
                    atomInfo = atomList.get(0);
                }

                if (atomInfo != null && atomInfo.getSettlePrice() != null) {
                    atomInfo.setSettlePrice(atomInfo.getSettlePrice() / 1000);
                }
                if (atomInfo != null && atomInfo.getSettlePriceCheck() != null) {
                    atomInfo.setSettlePriceCheck(atomInfo.getSettlePriceCheck() / 1000);
                }
                if (atomInfo != null && atomInfo.getHardwarePrice() != null) {
                    atomInfo.setHardwarePrice(atomInfo.getHardwarePrice() / 1000);
                }
                if (atomInfo != null && atomInfo.getSoftSettlePrice() != null) {
                    atomInfo.setSoftSettlePrice(atomInfo.getSoftSettlePrice() / 1000);
                }
                if (atomInfo != null && atomInfo.getSoftPrice() != null) {
                    atomInfo.setSoftPrice(atomInfo.getSoftPrice() / 1000);
                }
                if (atomInfo != null && atomInfo.getSoftTotalPrice() != null) {
                    atomInfo.setSoftTotalPrice(atomInfo.getSoftTotalPrice() / 1000);
                }
                if (atomInfo != null && atomInfo.getZhuanheSettlePrice() != null) {
                    atomInfo.setZhuanheSettlePrice(atomInfo.getZhuanheSettlePrice() / 1000);
                }
                if (atomInfo != null && atomInfo.getSalePrice() != null) {
                    atomInfo.setSalePrice(atomInfo.getSalePrice() / 1000);
                }
                if (atomInfo != null && atomInfo.getSaleMinPrice() != null) {
                    atomInfo.setSaleMinPrice(atomInfo.getSaleMinPrice() / 1000);
                }
                if (atomInfo != null && atomInfo.getSaleMaxPrice() != null) {
                    atomInfo.setSaleMaxPrice(atomInfo.getSaleMaxPrice() / 1000);
                }
                //获取商品配置信息
                ProductFlowInstanceConfigExample configExample = new ProductFlowInstanceConfigExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
                List<ProductFlowInstanceConfig> configList = productFlowInstanceConfigMapper.selectByExample(configExample);
                if (configList.isEmpty()) {
//                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到商品对应配置信息");
                } else {
                    configInfo = configList.get(0);
                }

            } else {
                skuInfo = null;
                spuInfo = null;
            }

            ByteArrayOutputStream bytearrayOutputStream = new ByteArrayOutputStream();
            ClassPathResource classPathResource = null;
            InputStream inputStream = null;
            String fileName = "";
            //根据类型判断，导出不同表格
            List<ProductFlowInstanceDirectory> directoryList = productFlowInstanceDirectoryMapper.selectByExample(new ProductFlowInstanceDirectoryExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example());

            if (spuInfo.getProductStandard() == ProductStandardEnum.STANDARD.code.intValue()) {

                switch (spuInfo.getProductType()) {
                    case 1:
                    case 2:
                        List<ProductFlowStandWithinTheProvinceExportDTO> list = new ArrayList<>();
                        ProductFlowStandWithinTheProvinceExportDTO info = new ProductFlowStandWithinTheProvinceExportDTO();
                        info = copyExportProperties(info, spuInfo, skuInfo, atomInfo, configInfo);
                        //查询目录

                        if (info.getShelfCatagoryId() != null) {
                            info.setShelfCatagoryName(productShelfCategoryCostMapper.selectByPrimaryKey(info.getShelfCatagoryId()).getName());
                        }
                        if (!CollectionUtils.isEmpty(directoryList)) {
                            info.setFirstDirectoryId(directoryList.get(0).getFirstDirectoryId());

                            info.setFirstDirectoryName(productNavigationDirectoryMapper.selectByPrimaryKey(directoryList.get(0).getFirstDirectoryId())==null?"":productNavigationDirectoryMapper.selectByPrimaryKey(directoryList.get(0).getFirstDirectoryId()).getName());
                        }
                        if (info.getFirstDirectoryId() != null) {
                            Set<String> secondDirectoryNameSet = new HashSet<>();
                            Set<String> thirdDirectoryNameSet = new HashSet<>();
                            for (ProductFlowInstanceDirectory directory : directoryList) {
                                if(directory.getSecondDirectoryId()!=null){
                                    secondDirectoryNameSet.add(productNavigationDirectoryMapper.selectByPrimaryKey(directory.getSecondDirectoryId())==null?"":productNavigationDirectoryMapper.selectByPrimaryKey(directory.getSecondDirectoryId()).getName());
                                }
                                if(directory.getThirdDirectoryId()!=null){
                                    thirdDirectoryNameSet.add(productNavigationDirectoryMapper.selectByPrimaryKey(directory.getThirdDirectoryId())==null?"":productNavigationDirectoryMapper.selectByPrimaryKey(directory.getThirdDirectoryId()).getName());
                                }
                            }
                            info.setSecondDirectoryName(String.join(",",secondDirectoryNameSet));
                            info.setThirdDirectoryName(String.join(",",thirdDirectoryNameSet));
                        }
                        if (info.getCmiotCostProjectId() != null) {
                            info.setCmiotCostProjectName(productShelfCategoryCostMapper.selectByPrimaryKey(info.getCmiotCostProjectId()).getName());
                        }
                        //定义有重复，导致被覆盖
                        info.setSalePrice(skuInfo.getSalePrice());
                        info.setSaleMinPrice(skuInfo.getSaleMinPrice());
                        info.setSaleMaxPrice(skuInfo.getSaleMaxPrice());
                        info.setSaleOutOfPriceRange(skuInfo.getSaleOutOfPriceRange());
                        info.setSkuCode(skuInfo.getSkuCode());


                        list.add(info);
                        classPathResource = new ClassPathResource("template/product-information-stand-with-in-province-export-template.xlsx");
                        inputStream = classPathResource.getInputStream();
                        try {
                            log.info("单产品信息导出(标准类-省框、省内)准备excel");
                            EasyExcelUtils.exportExcel2OutputStream(bytearrayOutputStream, "list", list, null, inputStream, 0, "单产品信息导出(标准类-省框、省内)", null, null, null, false);
//                ExcelUtils.exportProductFlowSimpleExcelFast("产品信息导出", ProductFlowAllExportDTO.class, exportList, 0);
                            log.info("单产品信息导出(标准类-省框、省内)完毕");
                            fileName = "单产品信息导出(标准类-省框、省内)" + new Date().getTime() + ".xlsx";


                        } catch (Exception e) {
                            log.error("{}单产品信息导出(标准类-省框、省内)构建excel出错", e);
                            executorService.execute(() -> {
                                String content = getLogInquireContent("【导出】", null, ProductStandardEnum.getName(spuInfo.getProductStandard()), ProductTypeEnum.getName(spuInfo.getProductType()), spuInfo.getSpuName(), skuInfo.getSkuName(), null, null, null, null, null);
                                if (StringUtils.isNotEmpty(content)) {
                                    logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code,
                                            "构建excel出错,请联系管理员", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "构建excel出错,请联系管理员");

                                }
                            });
                            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "构建excel出错,请联系管理员");
                        }
//                        workbook = ExcelUtils.exportProductFlowSimpleExcelFast("产品信息导出", ProductFlowStandWithinTheProvinceExportDTO.class, list, 1);
//                        sheet = (SXSSFSheet) workbook.getSheet("产品信息导出");
//                        //冻结第二行表头
//                        sheet.createFreezePane(0, 2, 0, 2);
                        break;
                    case 3:
                        List<ProductFlowStandDICTExportDTO> list3 = new ArrayList<>();
                        ProductFlowStandDICTExportDTO info3 = new ProductFlowStandDICTExportDTO();
                        info3 = copyExportProperties(info3, spuInfo, skuInfo, atomInfo, configInfo);


                        if (info3.getShelfCatagoryId() != null) {
                            info3.setShelfCatagoryName(productShelfCategoryCostMapper.selectByPrimaryKey(info3.getShelfCatagoryId()).getName());
                        }
                        if (!CollectionUtils.isEmpty(directoryList)) {
                            info3.setFirstDirectoryId(directoryList.get(0).getFirstDirectoryId());
                            info3.setFirstDirectoryName(productNavigationDirectoryMapper.selectByPrimaryKey(
                                    directoryList.get(0).getFirstDirectoryId())==null?"":
                                    productNavigationDirectoryMapper.selectByPrimaryKey(directoryList.get(0).getFirstDirectoryId()).getName());
                        }
                        if (info3.getFirstDirectoryId() != null) {
                            Set<String> secondDirectoryNameSet = new HashSet<>();
                            Set<String> thirdDirectoryNameSet = new HashSet<>();
                            for (ProductFlowInstanceDirectory directory : directoryList) {
                                if(directory.getSecondDirectoryId()!=null){
                                    secondDirectoryNameSet.add(productNavigationDirectoryMapper.selectByPrimaryKey(directory.getSecondDirectoryId())==null?"": productNavigationDirectoryMapper.selectByPrimaryKey(directory.getSecondDirectoryId()).getName());
                                }
                                if(directory.getThirdDirectoryId()!=null){
                                    thirdDirectoryNameSet.add(productNavigationDirectoryMapper.selectByPrimaryKey(directory.getThirdDirectoryId())==null ?"": productNavigationDirectoryMapper.selectByPrimaryKey(directory.getThirdDirectoryId()).getName());
                                }
                            }
                            info3.setSecondDirectoryName(String.join(",",secondDirectoryNameSet));
                            info3.setThirdDirectoryName(String.join(",",thirdDirectoryNameSet));
                        }

                        if (info3.getCmiotCostProjectId() != null) {
                            info3.setCmiotCostProjectName(productShelfCategoryCostMapper.selectByPrimaryKey(info3.getCmiotCostProjectId()).getName());
                        }
                        //定义有重复，导致被覆盖
                        info3.setSendContactPerson(skuInfo.getSendContactPerson());
                        info3.setSalePrice(skuInfo.getSalePrice());
                        info3.setSaleMinPrice(skuInfo.getSaleMinPrice());
                        info3.setSaleMaxPrice(skuInfo.getSaleMaxPrice());
                        info3.setSaleOutOfPriceRange(skuInfo.getSaleOutOfPriceRange());
                        info3.setSkuCode(skuInfo.getSkuCode());
                        list3.add(info3);
                        classPathResource = new ClassPathResource("template/product-information-stand-dict-export-template.xlsx");
                        inputStream = classPathResource.getInputStream();
                        try {
                            log.info("单产品信息导出(标准类-DICT)准备excel");
                            EasyExcelUtils.exportExcel2OutputStream(bytearrayOutputStream, "list", list3, null, inputStream, 0, "单产品信息导出(标准类-DICT)", null, null, null, false);
//                ExcelUtils.exportProductFlowSimpleExcelFast("产品信息导出", ProductFlowAllExportDTO.class, exportList, 0);
                            log.info("单产品信息导出(标准类-DICT)完毕");
                            fileName = "单产品信息导出(标准类-DICT)" + new Date().getTime() + ".xlsx";

                        } catch (Exception e) {
                            log.error("{}单产品信息导出(标准类-DICT)构建excel出错", e);
                            executorService.execute(() -> {
                                String content = getLogInquireContent("【导出】", null, ProductStandardEnum.getName(spuInfo.getProductStandard()), ProductTypeEnum.getName(spuInfo.getProductType()), spuInfo.getSpuName(), skuInfo.getSkuName(), null, null, null, null, null);
                                if (StringUtils.isNotEmpty(content)) {
                                    logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code,
                                            "构建excel出错,请联系管理员", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "构建excel出错,请联系管理员");

                                }
                            });
                            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "构建excel出错,请联系管理员");
                        }
//                        workbook = ExcelUtils.exportProductFlowSimpleExcelFast("单产品信息导出(标准类-DICT)", ProductFlowStandDICTExportDTO.class, list3, 3);
//                        sheet = (SXSSFSheet) workbook.getSheet("单产品信息导出(标准类-DICT)");
//                        //冻结第二行表头
//                        sheet.createFreezePane(0, 2, 0, 2);
                        break;

                    case 4:
                        List<ProductFlowStandContractPerformanceExportDTO> list4 = new ArrayList<>();
                        ProductFlowStandContractPerformanceExportDTO info4 = new ProductFlowStandContractPerformanceExportDTO();
                        info4 = copyExportProperties(info4, spuInfo, skuInfo, atomInfo, configInfo);
                        if (info4.getShelfCatagoryId() != null) {
                            info4.setShelfCatagoryName(productShelfCategoryCostMapper.selectByPrimaryKey(info4.getShelfCatagoryId()).getName());
                        }
                        if (!CollectionUtils.isEmpty(directoryList)) {
                            info4.setFirstDirectoryId(directoryList.get(0).getFirstDirectoryId());
                            info4.setFirstDirectoryName(productNavigationDirectoryMapper.selectByPrimaryKey(directoryList.get(0).getFirstDirectoryId())==null?"":productNavigationDirectoryMapper.selectByPrimaryKey(directoryList.get(0).getFirstDirectoryId()).getName());
                        }
                        if (info4.getFirstDirectoryId() != null) {
                            Set<String> secondDirectoryNameSet = new HashSet<>();
                            Set<String> thirdDirectoryNameSet = new HashSet<>();
                            for (ProductFlowInstanceDirectory directory : directoryList) {
                                if(directory.getSecondDirectoryId()!=null){
                                    secondDirectoryNameSet.add(productNavigationDirectoryMapper.selectByPrimaryKey(directory.getSecondDirectoryId())==null?"":productNavigationDirectoryMapper.selectByPrimaryKey(directory.getSecondDirectoryId()).getName());
                                }
                                if(directory.getThirdDirectoryId()!=null){
                                    thirdDirectoryNameSet.add(productNavigationDirectoryMapper.selectByPrimaryKey(directory.getThirdDirectoryId())==null?"":productNavigationDirectoryMapper.selectByPrimaryKey(directory.getThirdDirectoryId()).getName());
                                }
                            }
                            info4.setSecondDirectoryName(String.join(",",secondDirectoryNameSet));
                            info4.setThirdDirectoryName(String.join(",",thirdDirectoryNameSet));
                        }

                        if (info4.getCmiotCostProjectId() != null) {
                            info4.setCmiotCostProjectName(productShelfCategoryCostMapper.selectByPrimaryKey(info4.getCmiotCostProjectId()).getName());
                        }
                        //定义有重复，导致被覆盖
                        info4.setSkuCode(skuInfo.getSkuCode());
                        list4.add(info4);
                        classPathResource = new ClassPathResource("template/product-information-stand-contract-performance-export-template.xlsx");
                        inputStream = classPathResource.getInputStream();
                        try {
                            log.info("单产品信息导出(标准类-合同履约)准备excel");
                            EasyExcelUtils.exportExcel2OutputStream(bytearrayOutputStream, "list", list4, null, inputStream, 0, "单产品信息导出(标准类-合同履约)", null, null, null, false);
//                ExcelUtils.exportProductFlowSimpleExcelFast("产品信息导出", ProductFlowAllExportDTO.class, exportList, 0);
                            log.info("单产品信息导出(标准类-合同履约)完毕");
                            fileName = "单产品信息导出(标准类-合同履约)" + new Date().getTime() + ".xlsx";
                        } catch (Exception e) {
                            log.error("{}单产品信息导出(标准类-合同履约)构建excel出错", e);
                            executorService.execute(() -> {
                                String content = getLogInquireContent("【导出】", null, ProductStandardEnum.getName(spuInfo.getProductStandard()), ProductTypeEnum.getName(spuInfo.getProductType()), spuInfo.getSpuName(), skuInfo.getSkuName(), null, null, null, null, null);
                                if (StringUtils.isNotEmpty(content)) {
                                    logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code,
                                            content, loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "构建excel出错,请联系管理员");

                                }
                            });
                            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "构建excel出错,请联系管理员");
                        }
//                        workbook = ExcelUtils.exportProductFlowSimpleExcelFast("单产品信息导出(标准类-DICT)", ProductFlowStandContractPerformanceExportDTO.class, list4, 4);
//                        sheet = (SXSSFSheet) workbook.getSheet("单产品信息导出(标准类-DICT)");
//                        //冻结第二行表头
//                        sheet.createFreezePane(0, 2, 0, 2);
                        break;
                    case 5:
                        List<ProductFlowStandJointSaleExportDTO> list5 = new ArrayList<>();
                        ProductFlowStandJointSaleExportDTO info5 = new ProductFlowStandJointSaleExportDTO();

                        info5 = copyExportProperties(info5, spuInfo, skuInfo, atomInfo, configInfo);
                        if (info5.getShelfCatagoryId() != null) {
                            info5.setShelfCatagoryName(productShelfCategoryCostMapper.selectByPrimaryKey(info5.getShelfCatagoryId()).getName());
                        }
                        if (!CollectionUtils.isEmpty(directoryList)) {
                            info5.setFirstDirectoryId(directoryList.get(0).getFirstDirectoryId());
                            info5.setFirstDirectoryName(productNavigationDirectoryMapper.selectByPrimaryKey(directoryList.get(0).getFirstDirectoryId())==null?"":productNavigationDirectoryMapper.selectByPrimaryKey(directoryList.get(0).getFirstDirectoryId()).getName());
                        }
                        if (info5.getFirstDirectoryId() != null) {
                            StringBuilder str = new StringBuilder();
                            StringBuilder str1 = new StringBuilder();
                            StringBuilder thirdDirectoryIdStr = new StringBuilder();
                            StringBuilder thirdDirectoryNameStr = new StringBuilder();
                            for (ProductFlowInstanceDirectory directory : directoryList) {
                                if(directory.getSecondDirectoryId()!=null){
                                    str.append(",").append(productNavigationDirectoryMapper.selectByPrimaryKey(directory.getSecondDirectoryId())==null?"":productNavigationDirectoryMapper.selectByPrimaryKey(directory.getSecondDirectoryId()).getName());
                                    str1.append(",").append(directory.getSecondDirectoryId());
                                }
                                if(directory.getThirdDirectoryId()!=null){
                                    thirdDirectoryNameStr.append(",").append(productNavigationDirectoryMapper.selectByPrimaryKey(directory.getThirdDirectoryId())==null?"":productNavigationDirectoryMapper.selectByPrimaryKey(directory.getThirdDirectoryId()).getName());
                                    thirdDirectoryIdStr.append(",").append(directory.getThirdDirectoryId());
                                }

                            }
                            if(str.length()>0){
                                str.deleteCharAt(0);
                                str1.deleteCharAt(0);
                            }
                            info5.setSecondDirectoryName(String.valueOf(str));
                            info5.setSecondDirectoryId(String.valueOf(str1));
                            if(thirdDirectoryIdStr.length()>0){
                                thirdDirectoryIdStr.deleteCharAt(0);
                                thirdDirectoryNameStr.deleteCharAt(0);
                            }
                            info5.setThirdDirectoryId(String.valueOf(thirdDirectoryIdStr));
                            info5.setThirdDirectoryName(String.valueOf(thirdDirectoryNameStr));
                        }

                        //定义有重复，导致被覆盖

                        info5.setSkuCode(skuInfo.getSkuCode());
                        info5.setSalePrice(skuInfo.getSalePrice());
                        info5.setMinPurchaseNum(skuInfo.getMinPurchaseNum());
                        //多个表字段相同，需要别名处理
//                             if(atomInfo!=null){
//                                 info5.setSalePriceAtom(atomInfo.getSalePrice());
//                             }
                        list5.add(info5);
                        classPathResource = new ClassPathResource("template/product-information-stand-joint-sale-export-template.xlsx");
                        inputStream = classPathResource.getInputStream();
                        try {
                            log.info("单产品信息导出(标准类-联合销售)准备excel");
                            EasyExcelUtils.exportExcel2OutputStream(bytearrayOutputStream, "list", list5, null, inputStream, 0, "单产品信息导出(标准类-联合销售)", null, null, null, false);
//                ExcelUtils.exportProductFlowSimpleExcelFast("产品信息导出", ProductFlowAllExportDTO.class, exportList, 0);
                            log.info("单产品信息导出(标准类-联合销售)完毕");
                            fileName = "单产品信息导出(标准类-联合销售)" + new Date().getTime() + ".xlsx";
                        } catch (Exception e) {
                            log.error("{}单产品信息导出(标准类-联合销售)构建excel出错", e);
                            executorService.execute(() -> {
                                String content = getLogInquireContent("【导出】", null, ProductStandardEnum.getName(spuInfo.getProductStandard()), ProductTypeEnum.getName(spuInfo.getProductType()), spuInfo.getSpuName(), skuInfo.getSkuName(), null, null, null, null, null);
                                if (StringUtils.isNotEmpty(content)) {
                                    logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code,
                                            content, loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "构建excel出错,请联系管理员");

                                }
                            });
                            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "构建excel出错,请联系管理员");
                        }
//                        workbook = ExcelUtils.exportProductFlowSimpleExcelFast("单产品信息导出(标准类-联合销售)", ProductFlowStandJointSaleExportDTO.class, list5, 5);
//                        sheet = (SXSSFSheet) workbook.getSheet("单产品信息导出(标准类-联合销售)");
//                        //冻结第二行表头
//                        sheet.createFreezePane(0, 2, 0, 2);

                        break;
                    default:
                        break;
                }

            } else if (spuInfo.getProductStandard() == ProductStandardEnum.PLAN.code.intValue()) {

                List<ProductFlowPlanExportDTO> list6 = new ArrayList<>();
                List<ProductFlowInstanceAtom> atomList;

                //获取原子商品信息
                if (StringUtils.isBlank(spuCode) && StringUtils.isBlank(skuCode) && StringUtils.isNotBlank(flowInstanceId)) {
                    ProductFlowInstanceAtomExample atomExample = new ProductFlowInstanceAtomExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).andSpuCodeEqualTo(spuCode).andSkuCodeEqualTo(skuCode).example();
                    atomList = productFlowInstanceAtomMapper.selectByExample(atomExample);
                } else {

                    ProductFlowInstanceAtomExample atomExample = new ProductFlowInstanceAtomExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
                    atomList = productFlowInstanceAtomMapper.selectByExample(atomExample);

                } 
                if (atomList != null && atomList.size() > 0) {
                    for (ProductFlowInstanceAtom atomItem : atomList) {
                        if (atomItem.getSettlePrice() != null) {
                            atomItem.setSettlePrice(atomItem.getSettlePrice() / 1000);
                        }
                        if (atomItem.getSettlePriceCheck() != null) {
                            atomItem.setSettlePriceCheck(atomItem.getSettlePriceCheck() / 1000);
                        }
                        if (atomItem.getHardwarePrice() != null) {
                            atomItem.setHardwarePrice(atomItem.getHardwarePrice() / 1000);
                        }
                        if (atomItem.getSoftSettlePrice() != null) {
                            atomItem.setSoftSettlePrice(atomItem.getSoftSettlePrice() / 1000);
                        }
                        if (atomItem.getSoftPrice() != null) {
                            atomItem.setSoftPrice(atomItem.getSoftPrice() / 1000);
                        }
                        if (atomItem.getSoftTotalPrice() != null) {
                            atomItem.setSoftTotalPrice(atomItem.getSoftTotalPrice() / 1000);
                        }
                        if (atomItem.getZhuanheSettlePrice() != null) {
                            atomItem.setZhuanheSettlePrice(atomItem.getZhuanheSettlePrice() / 1000);
                        }
                        if (atomItem.getSalePrice() != null) {
                            atomItem.setSalePrice(atomItem.getSalePrice() / 1000);
                        }
                        if (atomItem.getSaleMinPrice() != null) {
                            atomItem.setSaleMinPrice(atomItem.getSaleMinPrice() / 1000);
                        }
                        if (atomItem.getSaleMaxPrice() != null) {
                            atomItem.setSaleMaxPrice(atomItem.getSaleMaxPrice() / 1000);
                        }
                        ProductFlowPlanExportDTO info6 = new ProductFlowPlanExportDTO();
                        BeanUtils.copyProperties(spuInfo, info6);
                        BeanUtils.copyProperties(skuInfo, info6);
                        BeanUtils.copyProperties(atomItem, info6);
                        if (configInfo != null) {
                            BeanUtils.copyProperties(configInfo, info6);
                        }


                        info6 = copyExportProperties(info6, spuInfo, skuInfo, atomItem, configInfo);
                        if (info6.getShelfCatagoryId() != null) {
                            info6.setShelfCatagoryName(productShelfCategoryCostMapper.selectByPrimaryKey(info6.getShelfCatagoryId()).getName());
                        }
                        if (!CollectionUtils.isEmpty(directoryList)) {
                            info6.setFirstDirectoryId(directoryList.get(0).getFirstDirectoryId());
                            info6.setFirstDirectoryName(productNavigationDirectoryMapper.selectByPrimaryKey(directoryList.get(0).getFirstDirectoryId())==null?"":productNavigationDirectoryMapper.selectByPrimaryKey(directoryList.get(0).getFirstDirectoryId()).getName());
                        }
                        if (info6.getFirstDirectoryId() != null) {
                            StringBuilder str = new StringBuilder();
                            StringBuilder str1 = new StringBuilder();
                            StringBuilder thirdDirectoryIdStr = new StringBuilder();
                            StringBuilder thirdDirectoryNameStr = new StringBuilder();
                            for (ProductFlowInstanceDirectory directory : directoryList) {
                                if(directory.getSecondDirectoryId()!=null){
                                    str.append(",").append(productNavigationDirectoryMapper.selectByPrimaryKey(directory.getSecondDirectoryId())==null?"":productNavigationDirectoryMapper.selectByPrimaryKey(directory.getSecondDirectoryId()).getName());
                                    str1.append(",").append(directory.getSecondDirectoryId());
                                }
                                if(directory.getThirdDirectoryId()!=null){
                                    thirdDirectoryNameStr.append(",").append(productNavigationDirectoryMapper.selectByPrimaryKey(directory.getThirdDirectoryId())==null?"":productNavigationDirectoryMapper.selectByPrimaryKey(directory.getThirdDirectoryId()).getName());
                                    thirdDirectoryIdStr.append(",").append(directory.getThirdDirectoryId());
                                }

                            }
                            if(str.length()>0){
                                str.deleteCharAt(0);
                                str1.deleteCharAt(0);
                            }
                            info6.setSecondDirectoryName(String.valueOf(str));
                            info6.setSecondDirectoryId(String.valueOf(str1));
                            if(thirdDirectoryIdStr.length()>0){
                                thirdDirectoryIdStr.deleteCharAt(0);
                                thirdDirectoryNameStr.deleteCharAt(0);
                            }
                            info6.setThirdDirectoryId(String.valueOf(thirdDirectoryIdStr));
                            info6.setThirdDirectoryName(String.valueOf(thirdDirectoryNameStr));
                        }

                        if (info6.getCmiotCostProjectId() != null) {
                            info6.setCmiotCostProjectName(productShelfCategoryCostMapper.selectByPrimaryKey(info6.getCmiotCostProjectId()).getName());
                        }

                        //定义有重复，导致被覆盖
                        info6.setSendContactPerson(skuInfo.getSendContactPerson());
                        list6.add(info6);
                    }

                } else {
                    ProductFlowPlanExportDTO info6 = new ProductFlowPlanExportDTO();
                    BeanUtils.copyProperties(spuInfo, info6);
                    BeanUtils.copyProperties(skuInfo, info6);
                    if (configInfo != null) {
                        BeanUtils.copyProperties(configInfo, info6);
                    }


                    info6 = copyExportProperties(info6, spuInfo, skuInfo, null, configInfo);
                    if (info6.getShelfCatagoryId() != null) {
                        info6.setShelfCatagoryName(productShelfCategoryCostMapper.selectByPrimaryKey(info6.getShelfCatagoryId()).getName());
                    }
                    if (!CollectionUtils.isEmpty(directoryList)) {
                        info6.setFirstDirectoryId(directoryList.get(0).getFirstDirectoryId());
                        info6.setFirstDirectoryName(productNavigationDirectoryMapper.selectByPrimaryKey(directoryList.get(0).getFirstDirectoryId())==null?"":productNavigationDirectoryMapper.selectByPrimaryKey(directoryList.get(0).getFirstDirectoryId()).getName());
                    }
                    if (info6.getFirstDirectoryId() != null) {
                        StringBuilder str = new StringBuilder();
                        StringBuilder str1 = new StringBuilder();
                        StringBuilder thirdDirectoryIdStr = new StringBuilder();
                        StringBuilder thirdDirectoryNameStr = new StringBuilder();
                        for (ProductFlowInstanceDirectory directory : directoryList) {
                            if(directory.getSecondDirectoryId()!=null){
                                str.append(",").append(productNavigationDirectoryMapper.selectByPrimaryKey(directory.getSecondDirectoryId())==null?"":productNavigationDirectoryMapper.selectByPrimaryKey(directory.getSecondDirectoryId()).getName());
                                str1.append(",").append(directory.getSecondDirectoryId());
                            }
                            if(directory.getThirdDirectoryId()!=null){
                                thirdDirectoryNameStr.append(",").append(productNavigationDirectoryMapper.selectByPrimaryKey(directory.getThirdDirectoryId())==null?"":productNavigationDirectoryMapper.selectByPrimaryKey(directory.getThirdDirectoryId()).getName());
                                thirdDirectoryIdStr.append(",").append(directory.getThirdDirectoryId());
                            }

                        }
                        if(str.length()>0){
                            str.deleteCharAt(0);
                            str1.deleteCharAt(0);
                        }
                        info6.setSecondDirectoryName(String.valueOf(str));
                        info6.setSecondDirectoryId(String.valueOf(str1));
                        if(thirdDirectoryIdStr.length()>0){
                            thirdDirectoryIdStr.deleteCharAt(0);
                            thirdDirectoryNameStr.deleteCharAt(0);
                        }
                        info6.setThirdDirectoryId(String.valueOf(thirdDirectoryIdStr));
                        info6.setThirdDirectoryName(String.valueOf(thirdDirectoryNameStr));
                    }
                    //定义有重复，导致被覆盖
                    info6.setSendContactPerson(skuInfo.getSendContactPerson());
                    list6.add(info6);
                }

                try {
                    log.info("单产品信息导出(方案类)准备excel");
                    List<EasyExcelDTO> easyExcelDTOList = new ArrayList<>();
                    EasyExcelDTO easyExcelDTO = EasyExcelUtils.setEasyExcelDTO(0, "单产品信息导出(方案类)", "list",
                            list6, null);
                    easyExcelDTOList.add(easyExcelDTO);
                    String excelName = "对账单";
                    excelName = URLEncoder.encode(excelName, "UTF-8");
                    classPathResource = new ClassPathResource("template/product-information-plan-export-template.xlsx");
                    inputStream = classPathResource.getInputStream();
                    List<Integer> needCellWriteHandlerList = new ArrayList<>();
                    needCellWriteHandlerList.add(0);
                    List<CellWriteHandler> cellWriteHandlerList = new ArrayList<>();
                    //标准产品列合并
                    int[] mergeThridColumnIndex = new int[34];
                    //
                    for (int i = 0; i < 29; i++) {
                        mergeThridColumnIndex[i] = i;
                    }

                    mergeThridColumnIndex[30] = 47;
                    mergeThridColumnIndex[31] = 48;
                    mergeThridColumnIndex[32] = 49;
                    mergeThridColumnIndex[33] = 50;

                    //设置第几行开始合并3
                    int mergeRowIndex = 2;
//                         ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
//                         HttpServletResponse response = requestAttr.getResponse();
                    // Excel单元格行合并处理策略
                    ExcelProductFlowMergeStrategy productFlowColumnMergeStrategy = new ExcelProductFlowMergeStrategy(mergeRowIndex, mergeThridColumnIndex, list6);
                    cellWriteHandlerList.add(productFlowColumnMergeStrategy);

//                         EasyExcelUtils.exportManySheetExcel(response, easyExcelDTOList,
//                                 excelName, inputStream, needCellWriteHandlerList, cellWriteHandlerList,
//                                 BaseErrorConstant.SUCCESS.getStateCode(), BaseErrorConstant.SUCCESS.getMessage());
                    //EasyExcelUtils.exportExcel2OutputStream(bytearrayOutputStream, "list", list6, null, inputStream, 0, "单产品信息导出(方案类)", needCellWriteHandlerList, cellWriteHandlerList, null);
                    EasyExcelUtils.exportExcel2OutputStream(bytearrayOutputStream, "list", list6, null, inputStream, 0, "单产品信息导出(方案类)", needCellWriteHandlerList, cellWriteHandlerList, null, false);
                    fileName = "单产品信息导出(方案类)" + new Date().getTime() + ".xlsx";
                    log.info("单产品信息导出(方案类)完毕");
                } catch (Exception e) {
                    log.error("{}单产品信息导出(方案类)构建excel出错", e);
                    executorService.execute(() -> {
                        String content = getLogInquireContent("【导出】", null, ProductStandardEnum.getName(spuInfo.getProductStandard()), ProductTypeEnum.getName(spuInfo.getProductType()), spuInfo.getSpuName(), skuInfo.getSkuName(), null, null, null, null, null);
                        if (StringUtils.isNotEmpty(content)) {
                            logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code,
                                    content, loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "构建excel出错,请联系管理员");

                        }
                    });
                    throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "构建excel出错,请联系管理员");
                }


            } else {
                executorService.execute(() -> {
                    String content = getLogInquireContent("【导出】", null, ProductStandardEnum.getName(spuInfo.getProductStandard()), ProductTypeEnum.getName(spuInfo.getProductType()), spuInfo.getSpuName(), skuInfo.getSkuName(), null, null, null, null, null);
                    if (StringUtils.isNotEmpty(content)) {
                        logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code,
                                content, loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "参数错误");

                    }
                });
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "参数错误");
            }


            //上传文件到对象存储
//                 String fileName = "productInformationExport" + new Date().getTime() + ".xlsx";
            ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
            byteArrayUpload.setFileName(fileName);
            byteArrayUpload.setBytes(bytearrayOutputStream.toByteArray());
            BaseAnswer<UpResult> resultBaseAnswer = storageService.uploadByte(byteArrayUpload);
            if (!"00000".equals(resultBaseAnswer.getStateCode())) {
                executorService.execute(() -> {
                    String content = getLogInquireContent("【导出】", null, ProductStandardEnum.getName(spuInfo.getProductStandard()), ProductTypeEnum.getName(spuInfo.getProductType()), spuInfo.getSpuName(), skuInfo.getSkuName(), null, null, null, null, null);
                    if (StringUtils.isNotEmpty(content)) {
                        logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code,
                                content, loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "上传文件出错,请联系管理员");

                    }
                });
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "上传文件出错,请联系管理员");
            }
            log.info("SKU信息导出接口上传excel完毕");

            //消息中心提醒用户
            UpResult upResult = resultBaseAnswer.getData();
            String outerUrl = upResult.getOuterUrl();
            String fileKey = upResult.getKey();
            AddMessageParam messageParam = new AddMessageParam();
            messageParam.setContent("SKU信息导出成功，请点击下载\n下载有效期" + serviceConfig.getOrderExportExcelExpireDays() + "天，请尽快处理");
            messageParam.setType("SKU信息导出");
            messageParam.setUserId(loginIfo4Redis.getUserId());
            messageParam.setFileKey(fileKey);
            messageParam.setUrl(outerUrl);
            messageParam.setSource(1);
            messageParam.setModule("产品管理");
            iotFeignClient.addMessage(messageParam);

            //通过kafka发送websocket提示前端更新消息未读数
            ProducerRecord<String, byte[]> record = new ProducerRecord<>(KafkaTopic.SUPPLY_CHAIN_WEBSOCKET.getTopic(), "/message/monitor".getBytes());
            kafkaTemplate.send(record);

            //发送短信提示用户
            String phone = loginIfo4Redis.getPhone();
            if (StringUtils.isNotEmpty(phone) && RegexUtil.regexPhone(phone)) {
                Msg4Request msg4Request = new Msg4Request();
                msg4Request.setTemplateId(serviceConfig.getNewMessageSms());
                List<String> mobiles = new ArrayList<>();
                mobiles.add(phone);
                msg4Request.setMobiles(mobiles);
                smsFeignClient.asySendMessage(msg4Request);
            }
            //记录订单导出日志,异步进行
            executorService.execute(() -> {
                String content = getLogInquireContent("【导出】", null, ProductStandardEnum.getName(spuInfo.getProductStandard()), ProductTypeEnum.getName(spuInfo.getProductType()), spuInfo.getSpuName(), skuInfo.getSkuName(), null, null, null, null, null);
                if (StringUtils.isNotEmpty(content)) {
                    logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code,
                            content, loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_SUCESS.code, null);

                }
                log.info("SKU信息导出接口记录日志完毕");
            });
        } catch (Exception e) {
            String content = null;
            //出现异常时，保存到消息中心
            if (e instanceof BusinessException) {
                BusinessException businessException = (BusinessException) e;
                content = "SKU信息导出失败:" + businessException.getStatus().getMessage();
            } else {
                log.error("{}SKU信息导出发生异常,", e);
                content = "SKU信息导出失败,请联系管理员";
            }
            AddMessageParam messageParam = new AddMessageParam();
            messageParam.setContent(content);
            messageParam.setType("SKU信息导出");
            messageParam.setUserId(loginIfo4Redis.getUserId());
            messageParam.setModule("产品管理");
            messageParam.setSource(1);
            iotFeignClient.addMessage(messageParam);
        }

    }

    @Override
    public void skuExportAll(LoginIfo4Redis loginIfo4Redis, String ip) {
        try {
            //导出全部
            List<ProductFlowAllExportDO> list = ProductFlowInstanceSpuMapperExt.getFlowInstanceSpuDetailList();
            if (list == null || list.size() == 0) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code,
                            "【导出】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "无数据");

                });
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "无数据");
            }
            List<ProductFlowAllExportDTO> exportList = list.stream().map(d -> {
                ProductFlowAllExportDTO productFlowAllExportDTO = new ProductFlowAllExportDTO();
                BeanUtils.copyProperties(d, productFlowAllExportDTO);
                productFlowAllExportDTO.setProductStandard(ProductStandardEnum.getName(d.getProductStandard()));
                productFlowAllExportDTO.setProductType(ProductTypeEnum.getName(d.getProductType()));
                return productFlowAllExportDTO;
            }).collect(Collectors.toList());
            ByteArrayOutputStream bytearrayOutputStream = new ByteArrayOutputStream();
            ClassPathResource classPathResource = new ClassPathResource("template/product-information-export-template.xlsx");
            InputStream inputStream = classPathResource.getInputStream();
            try {
                log.info("产品信息导出准备excel");
                EasyExcelUtils.exportExcel2OutputStream(bytearrayOutputStream, "list", exportList, null, inputStream, 0, "产品信息导出", null, null, null, false);
//                ExcelUtils.exportProductFlowSimpleExcelFast("产品信息导出", ProductFlowAllExportDTO.class, exportList, 0);
                log.info("产品信息导出完毕");
            } catch (Exception e) {
                log.error("{}产品信息构建excel出错", e);
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code,
                            "【导出】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "构建excel出错,请联系管理员");

                });

                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "构建excel出错,请联系管理员");
            }
            //上传文件到对象存储
            String fileName = "产品信息导出" + new Date().getTime() + ".xlsx";
            ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
            byteArrayUpload.setFileName(fileName);
            byteArrayUpload.setBytes(bytearrayOutputStream.toByteArray());
            BaseAnswer<UpResult> resultBaseAnswer = storageService.uploadByte(byteArrayUpload);
            if (!"00000".equals(resultBaseAnswer.getStateCode())) {
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "上传文件出错,请联系管理员");
            }
            log.info("产品信息导出接口上传excel完毕");

            //消息中心提醒用户
            UpResult upResult = resultBaseAnswer.getData();
            String outerUrl = upResult.getOuterUrl();
            String fileKey = upResult.getKey();
            AddMessageParam messageParam = new AddMessageParam();
            messageParam.setContent("产品信息导出成功，请点击下载\n下载有效期" + serviceConfig.getOrderExportExcelExpireDays() + "天，请尽快处理");
            messageParam.setType("产品信息导出");
            messageParam.setUserId(loginIfo4Redis.getUserId());
            messageParam.setFileKey(fileKey);
            messageParam.setUrl(outerUrl);
            messageParam.setSource(1);
            messageParam.setModule("产品管理");
            iotFeignClient.addMessage(messageParam);

            //通过kafka发送websocket提示前端更新消息未读数
            ProducerRecord<String, byte[]> record = new ProducerRecord<>(KafkaTopic.SUPPLY_CHAIN_WEBSOCKET.getTopic(), "/message/monitor".getBytes());
            kafkaTemplate.send(record);

            //发送短信提示用户
            String phone = loginIfo4Redis.getPhone();
            if (StringUtils.isNotEmpty(phone) && RegexUtil.regexPhone(phone)) {
                Msg4Request msg4Request = new Msg4Request();
                msg4Request.setTemplateId(serviceConfig.getNewMessageSms());
                List<String> mobiles = new ArrayList<>();
                mobiles.add(phone);
                msg4Request.setMobiles(mobiles);
                smsFeignClient.asySendMessage(msg4Request);
            }
            //记录订单导出日志,异步进行
            executorService.execute(() -> {
                String content = getLogInquireContent("【导出】", null, null, null, null, null, null, null, null, null, null);
                if (StringUtils.isNotEmpty(content)) {
                    logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code,
                            content, loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_SUCESS.code, null);
                }
                log.info("产品信息导出接口记录日志完毕");
            });
        } catch (Exception e) {
            String content = null;
            //出现异常时，保存到消息中心
            if (e instanceof BusinessException) {
                BusinessException businessException = (BusinessException) e;
                content = "导出产品信息失败:" + businessException.getStatus().getMessage();
            } else {
                log.error("{}导出产品信息发生异常,", e);
                content = "导出产品信息失败,请联系管理员";
            }
            AddMessageParam messageParam = new AddMessageParam();
            messageParam.setContent(content);
            messageParam.setType("产品信息导出");
            messageParam.setUserId(loginIfo4Redis.getUserId());
            messageParam.setModule("产品管理");
            messageParam.setSource(1);
            iotFeignClient.addMessage(messageParam);
        }

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer editFlow(ProductFlowInstanceEditParam param, LoginIfo4Redis loginIfo4Redis, boolean addLog, ProductFlowInstanceSpu flowInstanceSpu, ProductFlowInstanceSku flowInstanceSku, ProductFlowInstance flowInstance, ProductFlow productFlow, String ip, Boolean specialEdit) {
        Date now = new Date();
        String flowInstanceId = param.getProductFlowInstanceId();
        String offShelfReason = param.getOffShelfReason();
        ProductFlowInstanceDetailVO.SpuInfo spuInfo = param.getSpuInfo();
        ProductFlowInstanceDetailVO.SkuInfo skuInfo = param.getSkuInfo();
        List<ProductFlowInstanceDetailVO.AtomInfo> atomInfoList = param.getAtomInfoList();
        ProductFlowInstanceDetailVO.ConfigInfo configInfo = param.getConfigInfo();

        String currentStepId = flowInstance.getCurrentStepId();
        ProductFlowInstanceTaskExample taskExample = new ProductFlowInstanceTaskExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).andStepIdEqualTo(currentStepId).andHandleStatusEqualTo(ProductFlowHandleStatusEnum.UNHANDLE.code).example();
        List<ProductFlowInstanceTask> taskList = productFlowInstanceTaskMapper.selectByExample(taskExample);
        if (taskList.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程单已处理，不能编辑");
        }
        Integer flowType = productFlow.getFlowType();
        //更新下架理由
        if (StringUtils.isNotEmpty(offShelfReason) && offShelfReason != flowInstance.getOffShelfReason()) {
            flowInstance.setOffShelfReason(offShelfReason);
            flowInstance.setUpdateTime(now);
            productFlowInstanceMapper.updateByPrimaryKey(flowInstance);
        }
        //更新spu信息
        if (spuInfo != null) {
            BeanUtils.copyProperties(spuInfo, flowInstanceSpu);
            flowInstanceSpu.setUpdateTime(now);
            productFlowInstanceSpuMapper.updateByPrimaryKeySelective(flowInstanceSpu);
            //更新导航目录信息,由于可能增减，所以统一清除现有数据，再插入新的数据
            ProductFlowInstanceDirectoryExample example = new ProductFlowInstanceDirectoryExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
            productFlowInstanceDirectoryMapper.deleteByExample(example);
            List<ProductFlowInstanceDetailVO.NavigationDirectory> navigationDirectoryList = spuInfo.getNavigationDirectoryList();
            if(CollectionUtils.isEmpty(navigationDirectoryList)){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"未传递导航目录信息");
            }
            List<ProductFlowInstanceDirectory> directoryList = new ArrayList<>();
            for (ProductFlowInstanceDetailVO.NavigationDirectory directory : navigationDirectoryList) {
                String firstId = directory.getId();
                String firstName = directory.getName();
                List<ProductFlowInstanceDetailVO.NavigationDirectory> secondList = directory.getChildren();
                if(CollectionUtils.isEmpty(secondList)){
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"未传递二级导航目录信息");
                }
                for (ProductFlowInstanceDetailVO.NavigationDirectory second : secondList) {
                    List<ProductFlowInstanceDetailVO.NavigationDirectory> thirdList = second.getChildren();
                    if(CollectionUtils.isEmpty(thirdList)){
                        ProductFlowInstanceDirectory productFlowInstanceDirectory = new ProductFlowInstanceDirectory();
                        productFlowInstanceDirectory.setId(BaseServiceUtils.getId());
                        productFlowInstanceDirectory.setFlowInstanceId(flowInstanceId);
                        productFlowInstanceDirectory.setFirstDirectoryId(firstId);
                        productFlowInstanceDirectory.setFirstDirectoryName(firstName);
                        productFlowInstanceDirectory.setSecondDirectoryId(second.getId());
                        productFlowInstanceDirectory.setSecondDirectoryName(second.getName());
                        productFlowInstanceDirectory.setCreateTime(now);
                        productFlowInstanceDirectory.setUpdateTime(now);
                        directoryList.add(productFlowInstanceDirectory);
                    }else {
                        for (ProductFlowInstanceDetailVO.NavigationDirectory third : thirdList) {
                            ProductFlowInstanceDirectory productFlowInstanceDirectory = new ProductFlowInstanceDirectory();
                            productFlowInstanceDirectory.setId(BaseServiceUtils.getId());
                            productFlowInstanceDirectory.setFlowInstanceId(flowInstanceId);
                            productFlowInstanceDirectory.setFirstDirectoryId(firstId);
                            productFlowInstanceDirectory.setFirstDirectoryName(firstName);
                            productFlowInstanceDirectory.setSecondDirectoryId(second.getId());
                            productFlowInstanceDirectory.setSecondDirectoryName(second.getName());
                            productFlowInstanceDirectory.setThirdDirectoryId(third.getId());
                            productFlowInstanceDirectory.setThirdDirectoryName(third.getName());
                            productFlowInstanceDirectory.setCreateTime(now);
                            productFlowInstanceDirectory.setUpdateTime(now);
                            directoryList.add(productFlowInstanceDirectory);
                        }
                    }

                }
            }
            if(CollectionUtils.isNotEmpty(directoryList)){
                productFlowInstanceDirectoryMapper.batchInsert(directoryList);
            }
        }
        //更新sku信息
        if (skuInfo != null) {
            if (flowType.intValue() == ProductFlowTypeEnum.PRODUCT_SHELF.code.intValue()) {
                String skuCode = skuInfo.getSkuCode();
                if (StringUtils.isNotEmpty(skuCode)) {
                    //校验sku编码唯一性
                    if (spuInfo == null || StringUtils.isEmpty(spuInfo.getSpuCode())) {
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "传递sku编码时必须传递spu信息");
                    }
                    if (!specialEdit) {
                        String spuCode = spuInfo.getSpuCode();
                        ProductFlowInstanceSkuExample example = new ProductFlowInstanceSkuExample().createCriteria().andSpuCodeEqualTo(spuCode).andSkuCodeEqualTo(skuCode).andShelfStatusEqualTo(ProductShelfStatusEnum.SHELF_SUCCESS.code).example();
                        List<ProductFlowInstanceSku> productFlowInstanceSkus = productFlowInstanceSkuMapper.selectByExample(example);
                        if (!productFlowInstanceSkus.isEmpty()) {
                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "相同的spu编码+sku编码的产品已上架,不能重复");
                        }
                    }
                }
            }

            BeanUtils.copyProperties(skuInfo, flowInstanceSku);
            flowInstanceSku.setUpdateTime(now);
            flowInstanceSku.setSpuCode(flowInstanceSpu.getSpuCode());
            productFlowInstanceSkuMapper.updateByPrimaryKeySelective(flowInstanceSku);
        }
        //更新原子信息
        if (CollectionUtils.isNotEmpty(atomInfoList)) {
            if (flowType.intValue() == ProductFlowTypeEnum.PRODUCT_SHELF.code.intValue() && flowInstanceSpu.getProductStandard().intValue() == ProductStandardEnum.PLAN.code.intValue()) {
                List<ProductFlowInstanceAtom> flowInstanceAtomList = getFlowInstanceAtomList(flowInstanceId);
                //方案类上架流程，由于可以增减atom,所以将atom删除后重新插入,创建时间不变，其他属性一致
                ProductFlowInstanceAtomExample atomExample = new ProductFlowInstanceAtomExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
                ProductFlowInstanceAtom originalAtom = flowInstanceAtomList.get(0);
                productFlowInstanceAtomMapper.deleteByExample(atomExample);
                List<ProductFlowInstanceAtom> instanceAtomList = atomInfoList.stream().map(a -> {
                    ProductFlowInstanceAtom instanceAtom = new ProductFlowInstanceAtom();
                    BeanUtils.copyProperties(a, instanceAtom);
                    instanceAtom.setCreateTime(originalAtom.getCreateTime());
                    instanceAtom.setUpdateTime(now);
                    instanceAtom.setId(BaseServiceUtils.getId());
                    instanceAtom.setFlowInstanceId(flowInstanceId);
                    instanceAtom.setFlowId(productFlow.getId());
                    instanceAtom.setSpuCode(flowInstanceSpu.getSpuCode());
                    instanceAtom.setSkuCode(flowInstanceSku.getSkuCode());
                    return instanceAtom;
                }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(instanceAtomList)) {
                    productFlowInstanceAtomMapper.batchInsert(instanceAtomList);
                }
            } else {
                //其他流程不能增加atom信息，直接更新
                for (ProductFlowInstanceDetailVO.AtomInfo atomInfo : atomInfoList) {
                    ProductFlowInstanceAtom flowInstanceAtom = productFlowInstanceAtomMapper.selectByPrimaryKey(atomInfo.getId());
                    if (flowInstanceAtom == null) {
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程单原子信息id:" + atomInfo.getId() + "不存在");
                    }
                    atomInfo.setId(flowInstanceAtom.getId());
                    BeanUtils.copyProperties(atomInfo, flowInstanceAtom);
                    flowInstanceAtom.setUpdateTime(now);
                    flowInstanceAtom.setSpuCode(flowInstanceSpu.getSpuCode());
                    flowInstanceAtom.setSkuCode(flowInstanceSku.getSkuCode());
                    productFlowInstanceAtomMapper.updateByPrimaryKeySelective(flowInstanceAtom);
                }
            }
        }

        //更新配置信息
        if (configInfo != null) {
            ProductFlowInstanceConfigExample configExample = new ProductFlowInstanceConfigExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
            List<ProductFlowInstanceConfig> flowInstanceConfigList = productFlowInstanceConfigMapper.selectByExample(configExample);
            if (flowInstanceConfigList.isEmpty()) {
                //首次配置
                ProductFlowInstanceConfig config = new ProductFlowInstanceConfig();
                BeanUtils.copyProperties(configInfo, config);
                config.setId(BaseServiceUtils.getId());
                config.setFlowId(flowInstance.getFlowId());
                config.setFlowInstanceId(flowInstanceId);
                config.setCreateTime(now);
                config.setUpdateTime(now);
                productFlowInstanceConfigMapper.insert(config);
            } else {
                ProductFlowInstanceConfig flowInstanceConfig = flowInstanceConfigList.get(0);
                configInfo.setId(flowInstanceConfig.getId());
                BeanUtils.copyProperties(configInfo, flowInstanceConfig);
                flowInstanceConfig.setUpdateTime(now);
                productFlowInstanceConfigMapper.updateByPrimaryKeySelective(flowInstanceConfig);
            }
        }

        if (addLog) {
            //记录日志
            Integer logSubModule = getLogSubModuleFromFlowType(flowType);
            String content = getLogContent("【编辑】", flowInstance.getFlowInstanceNumber(), spuInfo.getSpuName(), skuInfo.getSkuName(), null, null, null, null, null, null, null);
            if (StringUtils.isNotEmpty(content)) {
                logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, logSubModule, content, LogResultEnum.LOG_SUCESS.code, null);
            }
        }
        deleteAllRedisCache(Constant.REDIS_KEY_MINI_PRODUCT_SEARCH);
        deleteAllRedisCache(Constant.REDIS_KEY_MINI_PRODUCT_SEARCH_COUNT);
        return BaseAnswer.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer editDelFlow(ProductFlowInstanceEditParam param, LoginIfo4Redis loginIfo4Redis, boolean addLog) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        Date now = new Date();
        String flowInstanceId = param.getProductFlowInstanceId();
        String offShelfReason = param.getOffShelfReason();
        ProductFlowInstanceDetailVO.SpuInfo spuInfo = param.getSpuInfo();
        ProductFlowInstanceDetailVO.SkuInfo skuInfo = param.getSkuInfo();
        List<ProductFlowInstanceDetailVO.AtomInfo> atomInfoList = param.getAtomInfoList();
        ProductFlowInstanceDetailVO.ConfigInfo configInfo = param.getConfigInfo();
        ProductFlowInstance flowInstance = productFlowInstanceMapper.selectByPrimaryKey(flowInstanceId);

        if (flowInstance == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程实例不存在");
        }
        Boolean canEdit = flowInstance.getCanEdit();
        if (canEdit == null || !canEdit) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程实例不允许编辑");
        }
        ProductFlow productFlow = productFlowMapper.selectByPrimaryKey(flowInstance.getFlowId());
        if (productFlow == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程不存在");
        }
        String currentStepId = flowInstance.getCurrentStepId();
        ProductFlowInstanceTaskExample taskExample = new ProductFlowInstanceTaskExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).andStepIdEqualTo(currentStepId).andHandleStatusEqualTo(ProductFlowHandleStatusEnum.UNHANDLE.code).example();
        List<ProductFlowInstanceTask> taskList = productFlowInstanceTaskMapper.selectByExample(taskExample);
        if (taskList.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程单已处理，不能编辑");
        }
        ProductFlowInstanceTask flowInstanceTask = taskList.get(0);
        boolean isAdmin = loginIfo4Redis.getIsAdmin() == null ? false : loginIfo4Redis.getIsAdmin();
        if (!isAdmin && !flowInstanceTask.getAssigneeId().equals(loginIfo4Redis.getUserId())) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "无权限编辑");
        }
        Integer flowType = productFlow.getFlowType();

        //更新下架理由
        if (StringUtils.isNotEmpty(offShelfReason) && offShelfReason != flowInstance.getOffShelfReason()) {
            flowInstance.setOffShelfReason(offShelfReason);
            flowInstance.setUpdateTime(now);
            productFlowInstanceMapper.updateByPrimaryKey(flowInstance);
        }
        //更新spu信息
        ProductFlowInstanceSpu flowInstanceSpu = getFlowInstanceSpu(flowInstanceId);
        if (spuInfo != null) {
            BeanUtils.copyProperties(spuInfo, flowInstanceSpu);
            flowInstanceSpu.setUpdateTime(now);
            productFlowInstanceSpuMapper.updateByPrimaryKeySelective(flowInstanceSpu);
        }
        //更新sku信息(根据逻辑，一次只能更新一条sku)
        if (skuInfo != null) {
            ProductFlowInstanceSku flowInstanceSku = getFlowInstanceSkuList(flowInstanceId).get(0);
            BeanUtils.copyProperties(skuInfo, flowInstanceSku);
            flowInstanceSku.setUpdateTime(now);
            productFlowInstanceSkuMapper.updateByPrimaryKeySelective(flowInstanceSku);
        }
        //更新原子信息
        if (CollectionUtils.isNotEmpty(atomInfoList)) {
            if (flowType.intValue() == ProductFlowTypeEnum.PRODUCT_SHELF.code.intValue() && flowInstanceSpu.getProductStandard().intValue() == ProductStandardEnum.PLAN.code.intValue()) {
                List<ProductFlowInstanceAtom> flowInstanceAtomList = getFlowInstanceAtomList(flowInstanceId);
                //方案类上架流程，由于可以增减atom,所以将atom删除后重新插入,创建时间不变，其他属性一致
                ProductFlowInstanceAtomExample atomExample = new ProductFlowInstanceAtomExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
                ProductFlowInstanceAtom originalAtom = flowInstanceAtomList.get(0);
                productFlowInstanceAtomMapper.deleteByExample(atomExample);
                List<ProductFlowInstanceAtom> instanceAtomList = atomInfoList.stream().map(a -> {
                    ProductFlowInstanceAtom instanceAtom = new ProductFlowInstanceAtom();
                    BeanUtils.copyProperties(a, instanceAtom);
                    instanceAtom.setCreateTime(originalAtom.getCreateTime());
                    instanceAtom.setUpdateTime(now);
                    instanceAtom.setId(BaseServiceUtils.getId());
                    instanceAtom.setFlowInstanceId(flowInstanceId);
                    instanceAtom.setFlowId(productFlow.getId());
                    return instanceAtom;
                }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(instanceAtomList)) {
                    productFlowInstanceAtomMapper.batchInsert(instanceAtomList);
                }
            } else {
                //其他流程不能增加atom信息，直接更新
                for (ProductFlowInstanceDetailVO.AtomInfo atomInfo : atomInfoList) {
                    ProductFlowInstanceAtom flowInstanceAtom = productFlowInstanceAtomMapper.selectByPrimaryKey(atomInfo.getId());
                    if (flowInstanceAtom == null) {
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程单原子信息id:" + atomInfo.getId() + "不存在");
                    }
                    BeanUtils.copyProperties(atomInfo, flowInstanceAtom);
                    flowInstanceAtom.setUpdateTime(now);
                    productFlowInstanceAtomMapper.updateByPrimaryKeySelective(flowInstanceAtom);
                }
            }

        }

        //更新配置信息
        if (configInfo != null) {
            ProductFlowInstanceConfigExample configExample = new ProductFlowInstanceConfigExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
            List<ProductFlowInstanceConfig> flowInstanceConfigList = productFlowInstanceConfigMapper.selectByExample(configExample);
            if (flowInstanceConfigList.isEmpty()) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "没有流程配置信息");
            }
            ProductFlowInstanceConfig flowInstanceConfig = flowInstanceConfigList.get(0);
            BeanUtils.copyProperties(configInfo, flowInstanceConfig);
            flowInstanceConfig.setUpdateTime(now);
            productFlowInstanceConfigMapper.updateByPrimaryKeySelective(flowInstanceConfig);
        }

        if (addLog) {
            //记录日志
            Integer logSubModule = getLogSubModuleFromFlowType(flowType);
            String content = getLogContent("【流程编号】", flowInstance.getFlowInstanceNumber(), spuInfo.getSpuName(), skuInfo.getSkuName(), null, null, null, null, null, null, null);
            if (StringUtils.isNotEmpty(content)) {
                logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, logSubModule, content, LogResultEnum.LOG_SUCESS.code, null);
            }
        }
        deleteAllRedisCache(Constant.REDIS_KEY_MINI_PRODUCT_SEARCH);
        deleteAllRedisCache(Constant.REDIS_KEY_MINI_PRODUCT_SEARCH_COUNT);
        return BaseAnswer.success(null);
    }

    private ProductFlowInstanceSpu getFlowInstanceSpu(String flowInstanceId) {
        ProductFlowInstanceSpuExample spuExample = new ProductFlowInstanceSpuExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
        List<ProductFlowInstanceSpu> productFlowInstanceSpus = productFlowInstanceSpuMapper.selectByExample(spuExample);
        if (productFlowInstanceSpus.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "SPU上架流程不存在");
        }
        ProductFlowInstanceSpu flowInstanceSpu = productFlowInstanceSpus.get(0);
        return flowInstanceSpu;
    }

    @Override
    public BaseAnswer<List<ProductFlowInstanceSku>> getShelfSkuListBySpu(String spuCode) {
        ProductFlowInstanceSpuExample example = new ProductFlowInstanceSpuExample().createCriteria().andSpuCodeEqualTo(spuCode).example();
        List<ProductFlowInstanceSpu> productFlowInstanceSpus = productFlowInstanceSpuMapper.selectByExample(example);
        if (productFlowInstanceSpus.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "spu上架流程不存在");
        }
        ProductFlowInstanceSpu productFlowInstanceSpu = productFlowInstanceSpus.get(0);
        //选择已上架或已下架的
        List<Integer> shelfStatusList = new ArrayList<>();
        shelfStatusList.add(ProductShelfStatusEnum.SHELF_SUCCESS.code);
        ProductFlowInstanceSkuExample skuExample = new ProductFlowInstanceSkuExample().createCriteria().andSpuCodeEqualTo(productFlowInstanceSpu.getSpuCode()).andShelfStatusIn(shelfStatusList).example();
        List<ProductFlowInstanceSku> productFlowInstanceSkus = productFlowInstanceSkuMapper.selectByExample(skuExample);
        if (productFlowInstanceSkus.isEmpty()) {
            return BaseAnswer.success(null);
        }
        List<ShelfKindSkuListBySpuVO> data = new ArrayList<>();
        //去重复
        List<String> skuCodeList = new ArrayList<>();
        for (ProductFlowInstanceSku flowInstanceSku : productFlowInstanceSkus) {
            if (!skuCodeList.contains(flowInstanceSku.getSkuCode())) {
                skuCodeList.add(flowInstanceSku.getSkuCode());
                ShelfKindSkuListBySpuVO vo = new ShelfKindSkuListBySpuVO();
                vo.setFlowInstanceId(flowInstanceSku.getFlowInstanceId());
                vo.setSkuCode(flowInstanceSku.getSkuCode());
                vo.setSkuName(flowInstanceSku.getSkuName());
                data.add(vo);
            }
        }
        return BaseAnswer.success(data);
    }

    @Override
    public BaseAnswer<List<ProductFlowInstanceSku>> getShelfSkuNewListBySpu(String spuCode) {
        ProductFlowInstanceSpuExample example = new ProductFlowInstanceSpuExample().createCriteria().andSpuCodeEqualTo(spuCode).example();
        List<ProductFlowInstanceSpu> productFlowInstanceSpus = productFlowInstanceSpuMapper.selectByExample(example);
        if (productFlowInstanceSpus.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "spu上架流程不存在");
        }
        ProductFlowInstanceSpu productFlowInstanceSpu = productFlowInstanceSpus.get(0);
        //选择已上架或已下架的
        List<Integer> shelfStatusList = new ArrayList<>();
        shelfStatusList.add(ProductShelfStatusEnum.SHELF_SUCCESS.code);
        shelfStatusList.add(ProductShelfStatusEnum.OFF_SHELF_CANCEL.code);
        ProductFlowInstanceSkuExample skuExample = new ProductFlowInstanceSkuExample().createCriteria().andSpuCodeEqualTo(spuCode).andShelfStatusIn(shelfStatusList).example();
        List<ProductFlowInstanceSku> productFlowInstanceSkus = productFlowInstanceSkuMapper.selectByExample(skuExample);
        if (productFlowInstanceSkus.isEmpty()) {
            return BaseAnswer.success(null);
        }
        List<ShelfKindSkuListBySpuVO> data = new ArrayList<>();
        //去重复
        List<String> skuCodeList = new ArrayList<>();
        for (ProductFlowInstanceSku flowInstanceSku : productFlowInstanceSkus) {
            if (!skuCodeList.contains(flowInstanceSku.getSkuCode())) {
                //获取sku的最新状态，如果不是已上架状态，则不添加
                ProductFlowInstanceSku newSku = ProductFlowInstanceSpuMapperExt.getNewSku(flowInstanceSku.getSpuCode(), flowInstanceSku.getSkuCode());
                if (newSku.getShelfStatus() == ProductShelfStatusEnum.SHELF_SUCCESS.code || newSku.getShelfStatus() == ProductShelfStatusEnum.OFF_SHELF_CANCEL.code) {
                    skuCodeList.add(flowInstanceSku.getSkuCode());
                    ShelfKindSkuListBySpuVO vo = new ShelfKindSkuListBySpuVO();
                    vo.setFlowInstanceId(flowInstanceSku.getFlowInstanceId());
                    vo.setSkuCode(flowInstanceSku.getSkuCode());
                    vo.setSkuName(flowInstanceSku.getSkuName());
                    data.add(vo);
                }
//                ProductFlowInstanceSkuExample skuExample = new ProductFlowInstanceSkuExample().createCriteria().andSpuCodeEqualTo(productFlowInstanceSpu.getSpuCode()).andSkuCodeEqualTo(flowInstanceSku.getSkuCode()).example();
//                List<ProductFlowInstanceSku> productFlowInstanceSkus = productFlowInstanceSkuMapper.selectByExample(skuExample);

            }
        }
        return BaseAnswer.success(data);
    }

    @Override
    public BaseAnswer<ProductFlowInstanceDetailVO> getInstanceDetail(String flowInstanceId,
                                                                     LoginIfo4Redis loginIfo4Redis,
                                                                     Integer subModule) {
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        String ip = org.apache.commons.lang3.StringUtils.isNotEmpty(request.getHeader(Constant.IP)) ? request.getHeader(Constant.IP) : "127.0.0.1";
        ProductFlowInstanceDetailVO vo = new ProductFlowInstanceDetailVO();
        ProductFlowInstance productFlowInstance = productFlowInstanceMapper.selectByPrimaryKey(flowInstanceId);
        if (productFlowInstance == null) {
            executor.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule, "【查看】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "流程单不存在");
            });

            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程单不存在");
        }
        ProductFlowInstanceSpu flowInstanceSpu = getFlowInstanceSpu(flowInstanceId);
        List<ProductFlowInstanceSku> productFlowInstanceSkus = getFlowInstanceSkuList(flowInstanceId);
        ProductFlowInstanceDetailVO.BaseInfo baseInfo = new ProductFlowInstanceDetailVO.BaseInfo();
        BeanUtils.copyProperties(flowInstanceSpu, baseInfo);
        BeanUtils.copyProperties(productFlowInstance, baseInfo);
        baseInfo.setOperatorType(getOperateType(baseInfo.getProductType()));
        vo.setBaseInfo(baseInfo);

        ProductFlowInstanceDetailVO.SpuInfo spuInfo = new ProductFlowInstanceDetailVO.SpuInfo();
        BeanUtils.copyProperties(flowInstanceSpu, spuInfo);
        //获取流程导航目录信息
        List<ProductFlowNavigationDirectoryDO> navigationDirectoryList =productFlowInstanceMapperExt.getNavigationDirectoryList(flowInstanceId);
        if(CollectionUtils.isNotEmpty(navigationDirectoryList)){
            List<ProductFlowInstanceDetailVO.NavigationDirectory> directoryList = getNavigationDirectoryList(navigationDirectoryList);
            spuInfo.setNavigationDirectoryList(directoryList);
        }

        vo.setSpuInfo(spuInfo);

        List<ProductFlowInstanceDetailVO.SkuInfo> skuInfoList = productFlowInstanceSkus.stream().map(s -> {
            ProductFlowInstanceDetailVO.SkuInfo skuInfo = new ProductFlowInstanceDetailVO.SkuInfo();
            BeanUtils.copyProperties(s, skuInfo);
            return skuInfo;
        }).collect(Collectors.toList());
        vo.setSkuInfoList(skuInfoList);

        ProductFlowInstanceAtomExample atomExample = new ProductFlowInstanceAtomExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
        List<ProductFlowInstanceAtom> productFlowInstanceAtoms = productFlowInstanceAtomMapper.selectByExample(atomExample);
        if (!productFlowInstanceAtoms.isEmpty()) {
            List<ProductFlowInstanceDetailVO.AtomInfo> atomInfoList = productFlowInstanceAtoms.stream().map(a -> {
                ProductFlowInstanceDetailVO.AtomInfo atomInfo = new ProductFlowInstanceDetailVO.AtomInfo();
                BeanUtils.copyProperties(a, atomInfo);
                if (StringUtils.isNotEmpty(atomInfo.getCmiotCostProjectId())) {
                    atomInfo.setCmiotCostProjectName(productShelfCategoryCostMapper.selectByPrimaryKey(atomInfo.getCmiotCostProjectId()).getName());
                }
                return atomInfo;
            }).collect(Collectors.toList());
            vo.setAtomInfoList(atomInfoList);
        }

        ProductFlowInstanceAttachmentExample attachmentExample = new ProductFlowInstanceAttachmentExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
        List<ProductFlowInstanceAttachment> productFlowInstanceAttachments = productFlowInstanceAttachmentMapper.selectByExample(attachmentExample);
        if (!productFlowInstanceAttachments.isEmpty()) {
            List<ProductFlowInstanceDetailVO.AttachmentInfo> attachmentInfoList = productFlowInstanceAttachments.stream().map(p -> {
                ProductFlowInstanceDetailVO.AttachmentInfo attachmentInfo = new ProductFlowInstanceDetailVO.AttachmentInfo();
                BeanUtils.copyProperties(p, attachmentInfo);
                return attachmentInfo;
            }).collect(Collectors.toList());
            vo.setAttachmentInfoList(attachmentInfoList);
        }

        ProductFlowInstanceConfigExample configExample = new ProductFlowInstanceConfigExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
        List<ProductFlowInstanceConfig> productFlowInstanceConfigs = productFlowInstanceConfigMapper.selectByExample(configExample);
        if (!productFlowInstanceConfigs.isEmpty()) {
            List<ProductFlowInstanceDetailVO.ConfigInfo> configInfoList = productFlowInstanceConfigs.stream().map(p -> {
                ProductFlowInstanceDetailVO.ConfigInfo configInfo = new ProductFlowInstanceDetailVO.ConfigInfo();
                BeanUtils.copyProperties(p, configInfo);
                return configInfo;
            }).collect(Collectors.toList());
            vo.setConfigInfo(configInfoList.get(0));
        }

        //审核记录
        ProductFlowInstanceTaskExample example = new ProductFlowInstanceTaskExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
        example.orderBy("create_time ASC");
        List<ProductFlowInstanceTask> productFlowInstanceTasks = productFlowInstanceTaskMapper.selectByExample(example);
        if (!productFlowInstanceTasks.isEmpty()) {
            List<ProductFlowInstanceDetailVO.AuditInfo> auditInfoList = new ArrayList<>();
            int number = 1;
            for (int i = 0; i < productFlowInstanceTasks.size(); i++) {
                ProductFlowInstanceTask task = productFlowInstanceTasks.get(i);
                ProductFlowInstanceDetailVO.AuditInfo auditInfo = new ProductFlowInstanceDetailVO.AuditInfo();
                BeanUtils.copyProperties(task, auditInfo);
                //废止或结束的步骤，不显示时间
                if (auditInfo.getStepName().contains("结束")) {
                    auditInfo.setHandleTime(null);
                    auditInfo.setCreateTime(null);
                }
                if (i > 0) {
                    //判断前一个审核记录的stepId和当前审核记录是否一致，如果一致，则当前审核记录不设置序号
                    ProductFlowInstanceTask preTask = productFlowInstanceTasks.get(i - 1);
                    if (!preTask.getStepId().equals(task.getStepId())) {
                        auditInfo.setNumber(number);
                        number++;
                    }
                    auditInfoList.add(auditInfo);
                } else {
                    auditInfo.setNumber(number);
                    auditInfoList.add(auditInfo);
                    number++;
                }

            }

            vo.setAuditInfoList(auditInfoList);
        }

        //当前处理信息
        if (!"-1".equals(productFlowInstance.getCurrentStepId())) {
            ProductFlowInstanceTaskDO taskDO = productFlowInstanceMapperExt.getTaskInfo(flowInstanceId);
            ProductFlowInstanceDetailVO.TaskInfo taskInfo = new ProductFlowInstanceDetailVO.TaskInfo();
            BeanUtils.copyProperties(taskDO, taskInfo);
            if (taskInfo.getLimitId() != null) {
                taskInfo.setLimitName(ProductFlowLimitEnum.fromCode(taskInfo.getLimitId()).name);
            }
            Boolean canAudit = productFlowInstance.getCanAudit();
            taskInfo.setHasAuditButton((canAudit != null && canAudit) ? true : false);
            if (StringUtils.isEmpty(taskInfo.getPassNextStepName())) {
                taskInfo.setPassNextStepName("结束");
            }
            //转办任务的特殊处理
            /**
             * 转办人员不能再勾选转办（不应该显示转办）
             * 转办人员没有通过、不通过，只填写处理意见
             * 转办后的下一环节为原审批环节,不选审批人
             */
            String stepName = taskDO.getStepName();
            if (stepName.contains("(转办)")) {
                taskInfo.setRedirectRoleId(null);
                taskInfo.setHasAuditButton(false);
                taskInfo.setPassNextStepName(stepName.substring(0, stepName.indexOf("(转办)")));
                taskInfo.setPassNextStepRoleId(null);
                taskInfo.setRejectNextStepRoleId(null);
                taskInfo.setKnownRoleId(null);
            }
            vo.setTaskInfo(taskInfo);
        }

        //日志记录
        List<String> skuNameList = new ArrayList<>();
        skuInfoList.forEach(s -> {
            skuNameList.add(s.getSkuName());
        });
        String userId = loginIfo4Redis.getUserId();
        if (!"-1".equals(userId)) {
            String content = getLogContent("【查看】", productFlowInstance.getFlowInstanceNumber(), spuInfo.getSpuName(), String.join(",", skuNameList), null, null, null, null, null, null, null);
            if (StringUtils.isNotEmpty(content)) {
//                HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
//                String ip = org.apache.commons.lang3.StringUtils.isNotEmpty(request.getHeader(Constant.IP))? request.getHeader(Constant.IP) : "127.0.0.1";
                executor.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule, content, userId, ip, LogResultEnum.LOG_SUCESS.code, null);
                });
            }
        }
        return BaseAnswer.success(vo);
    }

    /**
     * 将导航目录数据列表转换为树形结构VO列表
     *
     * @param navigationDirectoryList 原始导航目录数据列表，包含三级目录结构数据
     * @return 组装完成的树形结构导航目录VO列表，包含三级嵌套结构
     */
    private static List<ProductFlowInstanceDetailVO.NavigationDirectory> getNavigationDirectoryList(List<ProductFlowNavigationDirectoryDO> navigationDirectoryList) {

        //一级目录
        Map<String, ProductFlowInstanceDetailVO.NavigationDirectory> firstMap = new LinkedHashMap<>();
        //二级目录
        Map<String, ProductFlowInstanceDetailVO.NavigationDirectory> secondMap = new LinkedHashMap<>();
        for (ProductFlowNavigationDirectoryDO directoryDO : navigationDirectoryList) {
            ProductFlowInstanceDetailVO.NavigationDirectory firstDir = null;
            if(StringUtils.isNotEmpty(directoryDO.getFirstDirectoryId())){
                if (firstMap.containsKey(directoryDO.getFirstDirectoryId())) {
                    firstDir = firstMap.get(directoryDO.getFirstDirectoryId());
                }else {
                    firstDir = new ProductFlowInstanceDetailVO.NavigationDirectory();
                    firstDir.setId(directoryDO.getFirstDirectoryId());
                    firstDir.setName(directoryDO.getFirstDirectoryName());
                    firstMap.put(firstDir.getId(), firstDir);
                }
            }

            ProductFlowInstanceDetailVO.NavigationDirectory secondDir = null;
            if(StringUtils.isNotEmpty(directoryDO.getSecondDirectoryId())){
                if (secondMap.containsKey(directoryDO.getSecondDirectoryId())) {
                    secondDir = secondMap.get(directoryDO.getSecondDirectoryId());
                }else {
                    secondDir = new ProductFlowInstanceDetailVO.NavigationDirectory();
                    secondDir.setId(directoryDO.getSecondDirectoryId());
                    secondDir.setName(directoryDO.getSecondDirectoryName());
                    secondMap.put(directoryDO.getSecondDirectoryId(), secondDir);
                    //首次出现二级目录，则添加到一级目录的child中
                    if (firstDir.getChildren() == null) {
                        firstDir.setChildren(new ArrayList<>());
                    }
                    firstDir.getChildren().add(secondDir);
                }
            }

            ProductFlowInstanceDetailVO.NavigationDirectory thirdDir = new ProductFlowInstanceDetailVO.NavigationDirectory();
            thirdDir.setId(directoryDO.getThirdDirectoryId());
            thirdDir.setName(directoryDO.getThirdDirectoryName());

            //三级目录如果有，就加到二级目录的child中
            if (directoryDO.getThirdDirectoryId() != null && secondDir != null) {
                if (secondDir.getChildren() == null) {
                    secondDir.setChildren(new ArrayList<>());
                }
                secondDir.getChildren().add(thirdDir);
            }

        }
        return new ArrayList<>(firstMap.values());
    }


    private List<ProductFlowInstanceSku> getFlowInstanceSkuList(String flowInstanceId) {
        ProductFlowInstanceSkuExample instanceSkuExample = new ProductFlowInstanceSkuExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
        List<ProductFlowInstanceSku> productFlowInstanceSkus = productFlowInstanceSkuMapper.selectByExample(instanceSkuExample);
        if (productFlowInstanceSkus.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程单SKU信息不存在");
        }
        return productFlowInstanceSkus;
    }

    @Override
    public BaseAnswer<List<ShelfCategoryListVO>> getShelfCategoryList() {
        ProductShelfCategoryCostExample example = new ProductShelfCategoryCostExample().createCriteria().andParentIdEqualTo("-1").example();
        List<ProductShelfCategoryCost> list = productShelfCategoryCostMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return BaseAnswer.success(null);
        }
        List<ShelfCategoryListVO> collect = list.stream().map(data -> {
            ShelfCategoryListVO vo = new ShelfCategoryListVO();
            BeanUtils.copyProperties(data, vo);
            return vo;
        }).collect(Collectors.toList());
        return BaseAnswer.success(collect);
    }

    @Override
    public BaseAnswer<List<ShelfCategoryListVO>> getCmiotCostListByCategoryId(String categoryId) {
        ProductShelfCategoryCostExample example = new ProductShelfCategoryCostExample().createCriteria().andParentIdEqualTo(categoryId).example();
        List<ProductShelfCategoryCost> list = productShelfCategoryCostMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return BaseAnswer.success(null);
        }
        List<ShelfCategoryListVO> collect = list.stream().map(data -> {
            ShelfCategoryListVO vo = new ShelfCategoryListVO();
            BeanUtils.copyProperties(data, vo);
            return vo;
        }).collect(Collectors.toList());
        return BaseAnswer.success(collect);
    }

    @Override
    public BaseAnswer<List<NavigationListVO>> getFirstNavigationList() {
        ProductNavigationDirectoryExample example = new ProductNavigationDirectoryExample().createCriteria()
                .andParentIdEqualTo("-1").andIsDeleteEqualTo(false).example();
        List<ProductNavigationDirectory> list = productNavigationDirectoryMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return BaseAnswer.success(null);
        }
        List<NavigationListVO> collect = list.stream().map(data -> {
            NavigationListVO vo = new NavigationListVO();
            BeanUtils.copyProperties(data, vo);
            return vo;
        }).collect(Collectors.toList());
        return BaseAnswer.success(collect);
    }

    @Override
    public BaseAnswer<List<NavigationListVO>> getSecondNavigationList(List<String> parentId) {
        ProductNavigationDirectoryExample example = new ProductNavigationDirectoryExample().createCriteria().andParentIdIn(parentId).andIsDeleteEqualTo(false).example();
        List<ProductNavigationDirectory> list = productNavigationDirectoryMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return BaseAnswer.success(null);
        }
        List<NavigationListVO> collect = list.stream().map(data -> {
            NavigationListVO vo = new NavigationListVO();
            BeanUtils.copyProperties(data, vo);
            return vo;
        }).collect(Collectors.toList());
        return BaseAnswer.success(collect);
    }

    @Override
    public BaseAnswer uploadAttachment(MultipartFile file, String flowInstanceId, Integer type) {
        if (file == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "文件不能为空");
        }
        if (!ProductFlowAttachmentTypeEnum.contains(type)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "文件类型错误");
        }
        String filename = file.getOriginalFilename();
        String suffix = filename.substring(filename.lastIndexOf(".") + 1).toUpperCase();
        long size = file.getSize();
        if (size > 100 * 1024 * 1024) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "文件最大限制100M");
        }
        if ((ProductFlowAttachmentTypeEnum.HEAD_IMAGE.code == type.intValue() || ProductFlowAttachmentTypeEnum.SLIDE_IMAGE.code == type.intValue())) {
            if (!"JPG".equals(suffix) && !"PNG".equals(suffix)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "图片格式只允许JPG和PNG");
            }
            if (size > 1024 * 1024) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "商品头图、商品轮播图 最大限制1M");
            }
        }
        if ((ProductFlowAttachmentTypeEnum.PRODUCT_DETAIL_MATERIAL.code == type.intValue()
                || ProductFlowAttachmentTypeEnum.REAL_PRODUCT_IMAGE.code == type.intValue()
                || ProductFlowAttachmentTypeEnum.AFTER_MARKET_IMAGE.code == type.intValue()
        )) {
            if (!"JPG".equals(suffix) && !"PNG".equals(suffix)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "图片格式只允许JPG和PNG");
            }
            if (size > 2 * 1024 * 1024) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "商品详情页素材、实质性产品图片、售后政策图片 最大限制2M");
            }
        }
        if (ProductFlowAttachmentTypeEnum.MOVIE.code == type.intValue()) {
            if (!"AVI".equals(suffix) && !"MPEG".equals(suffix) && !"MOV".equals(suffix)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "视频格式只允许AVI,MPEG和MOV");
            }
        }
        //流程的所有附件总尺寸限制100M
        List<ProductFlowInstanceAttachment> productFlowInstanceAttachments = productFlowInstanceAttachmentMapper.selectByExample(new ProductFlowInstanceAttachmentExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example());
        if(CollectionUtils.isNotEmpty(productFlowInstanceAttachments)){
            long totalSize = productFlowInstanceAttachments.stream().mapToLong(ProductFlowInstanceAttachment::getBytes).sum();
            if(totalSize + size > 100 * 1024 * 1024){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "所有附件加起来最大限制100M");
            }
        }

        InputStream inputStream = null;
        int width = 0;
        int height = 0;
        if (ProductFlowAttachmentTypeEnum.HEAD_IMAGE.code == type.intValue() ||
                ProductFlowAttachmentTypeEnum.SLIDE_IMAGE.code == type.intValue() ||
                ProductFlowAttachmentTypeEnum.PRODUCT_DETAIL_MATERIAL.code == type.intValue() ||
                ProductFlowAttachmentTypeEnum.REAL_PRODUCT_IMAGE.code == type.intValue() ||
                ProductFlowAttachmentTypeEnum.AFTER_MARKET_IMAGE.code == type.intValue()
        ) {
            try {
                inputStream = file.getInputStream();
                BufferedImage image = ImageIO.read(inputStream);
                width = image.getWidth();
                height = image.getHeight();
            } catch (IOException e) {
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "图片解析尺寸出错");
            }
        }

        if (ProductFlowAttachmentTypeEnum.HEAD_IMAGE.code == type.intValue() && (width != 800 || height != 528)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "头图尺寸限制为800*528");
        }
        if (ProductFlowAttachmentTypeEnum.SLIDE_IMAGE.code == type.intValue() && (width != 800 || height != 800)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "轮播图尺寸限制为800*800");
        }
        if ((ProductFlowAttachmentTypeEnum.PRODUCT_DETAIL_MATERIAL.code == type.intValue() ||
                ProductFlowAttachmentTypeEnum.REAL_PRODUCT_IMAGE.code == type.intValue() ||
                ProductFlowAttachmentTypeEnum.AFTER_MARKET_IMAGE.code == type.intValue()

        ) && (width != 750)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "商品详情页素材、实质性产品图片、售后政策图片宽度限制为750");
        }
        String snowId = BaseServiceUtils.getId();

        //处理同名文件覆盖问题,在文件名后加"_xxxxx"
        String[] nameArray = filename.split("\\.");
        filename = nameArray[0] + "_" + snowId + "." + nameArray[1];
        ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
        BaseAnswer<UpResult> uploadResult = null;
        try {
            byteArrayUpload.setBytes(file.getBytes());
            byteArrayUpload.setFileName(filename);
            uploadResult = storageService.uploadByte(byteArrayUpload);
        } catch (Exception e) {
            log.error("上传文件发生异常", e);
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR);
        }
        if (!uploadResult.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())) {
            log.error("上传文件响应失败:{}", JSON.toJSONString(uploadResult));
            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR);
        }
        Date now = new Date();
        //保存流程附件记录
        UpResult upResult = uploadResult.getData();
        ProductFlowInstanceAttachment attacment = new ProductFlowInstanceAttachment();
        attacment.setId(BaseServiceUtils.getId());
        attacment.setFlowInstanceId(flowInstanceId);
        attacment.setFileName(file.getOriginalFilename());
        attacment.setFileKey(upResult.getKey());
        attacment.setFileUrl(upResult.getOuterUrl());
        attacment.setType(type);
        attacment.setBytes(size);
        attacment.setCreateTime(now);
        attacment.setUpdateTime(now);
        productFlowInstanceAttachmentMapper.insert(attacment);
        redisTemplate.delete(Constant.REDIS_KEY_PRODUCT_FLOW_INSTANCE_ATTACHMENT + flowInstanceId);
        deleteAllRedisCache(Constant.REDIS_KEY_MINI_PRODUCT_SEARCH);
        deleteAllRedisCache(Constant.REDIS_KEY_MINI_PRODUCT_SEARCH_COUNT);
        return BaseAnswer.success(null);
    }


    @Override
    public BaseAnswer<List<AttachmentListVO>> getAttachmentList(String flowInstanceId) {
        ProductFlowInstanceAttachmentExample example = new ProductFlowInstanceAttachmentExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
        example.orderBy("create_time DESC");
        List<ProductFlowInstanceAttachment> attacmentList = productFlowInstanceAttachmentMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(attacmentList)) {
            return BaseAnswer.success(null);
        }
        List<AttachmentListVO> collect = attacmentList.stream().map(data -> {
            AttachmentListVO vo = new AttachmentListVO();
            BeanUtils.copyProperties(data, vo);
            return vo;
        }).collect(Collectors.toList());
        return BaseAnswer.success(collect);
    }

    @Override
    public BaseAnswer<PageData<ProductFlowInstanceListVO>> getFlowInstanceList(LoginIfo4Redis loginIfo4Redis, FlowInstanceListParam param) {
        List<String> dataPermissionCodes = (List<String>) redisTemplate.opsForValue().get(Constant.REDIS_KEY_ROLE_DATA_PERMISSION_CODE + loginIfo4Redis.getRoleId());
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(dataPermissionCodes) || (
                !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PUTAWAY_SYSTEM)
                        && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PUTAWAY_COMPANY)
                        && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PUTAWAY_PERSONAL)
                        && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_DESCEND_COMPANY)
                        && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_DESCEND_SYSTEM)
                        && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_DESCEND_PERSONAL)
                        && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ALTERATION_SYSTEM)
                        && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ALTERATION_COMPANY)
                        && !dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ALTERATION_PERSONAL)
        )) {
            throw new BusinessException(StatusConstant.UN_PERMISSION);
        }
        boolean isAdmin = loginIfo4Redis.getIsAdmin() == null ? false : loginIfo4Redis.getIsAdmin();
        String spuName = param.getSpuName();
        String skuName = param.getSkuName();
        Integer productType = param.getProductType();
        List<Integer> flowTypeList = param.getFlowTypeList();
        Integer productStandard = param.getProductStandard();
        Integer operatorType = param.getOperateType();
        String flowNumber = param.getFlowNumber();
        String creatorName = param.getCreatorName();
        String createTimeStart = param.getCreateTimeStart();
        String createTimeEnd = param.getCreateTimeEnd();
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        Date startTime = null;
        Date endTime = null;
        try {
            startTime = DateTimeUtil.getFormatDate(createTimeStart, DateTimeUtil.DEFAULT_DATE_DEFAULT);
            endTime = DateTimeUtil.getFormatDate(createTimeEnd, DateTimeUtil.DEFAULT_DATE_DEFAULT);
        } catch (ParseException e) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "日期格式错误");
        }
        PageData<ProductFlowInstanceListVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        PageHelper.startPage(pageNum, pageSize);
        List<FlowInstanceListDO> doList = new ArrayList<>();
        if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PUTAWAY_SYSTEM)
                || dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_DESCEND_SYSTEM)
                || dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ALTERATION_SYSTEM)
        ) {
            //查看系统数据
            doList = productFlowInstanceMapperExt.getFlowInstanceList(spuName, skuName, productType, flowTypeList, productStandard, operatorType, flowNumber, creatorName, startTime, endTime, loginIfo4Redis.getUserId(), isAdmin, false);
        }
        if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PUTAWAY_COMPANY)
                || dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_DESCEND_COMPANY)
                || dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ALTERATION_COMPANY)
        ) {
            //查看单位数据(省业管员，查看自己省发布范围的sku的流程)
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfo(loginIfo4Redis.getUserId());
            if (data4UserBaseAnswer == null || data4UserBaseAnswer.getData() == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "用户信息不存在");
            }
            String company = data4UserBaseAnswer.getData().getCompany();
            if (StringUtils.isNotEmpty(company)) {
                company = company.substring(0, company.lastIndexOf("移动"));
            } else {
                //避免SQL的like命中，所以加个空格
                company = " ";
            }
            doList = productFlowInstanceMapperExt.getFlowInstanceListArea(spuName, skuName, productType, flowTypeList, productStandard, operatorType, flowNumber, creatorName, startTime, endTime, loginIfo4Redis.getUserId(), isAdmin, company);


        }
        if (dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_PUTAWAY_PERSONAL)
                || dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_DESCEND_PERSONAL)
                || dataPermissionCodes.contains(BaseConstant.DATA_PERMISSION_ALTERATION_PERSONAL)
        ) {
            //查看个人数据
            doList = productFlowInstanceMapperExt.getFlowInstanceList(spuName, skuName, productType, flowTypeList, productStandard, operatorType, flowNumber, creatorName, startTime, endTime, loginIfo4Redis.getUserId(), isAdmin, true);
        }
        PageInfo<FlowInstanceListDO> pageInfo = new PageInfo<>(doList);
        pageData.setCount(pageInfo.getTotal());
        if (doList.isEmpty()) {
            return BaseAnswer.success(pageData);
        }
        List<AuthCode> authCodes = loginIfo4Redis.getAuthCodes();
        List<String> authCodeList = new ArrayList<>();
        AuthCodeUtils.getAllAuthCode(authCodeList, authCodes);
        List<ProductFlowInstanceListVO> collect = doList.stream().map(d -> {
            ProductFlowInstanceListVO vo = new ProductFlowInstanceListVO();
            BeanUtils.copyProperties(d, vo);
            vo.setOperateType(getOperateType(vo.getProductType()));
            int flowType = Integer.parseInt(vo.getFlowType());
            boolean changeFlow = flowType == ProductFlowTypeEnum.SALE_PRICE_UPDATE.code.intValue() || flowType == ProductFlowTypeEnum.SETTLE_PRICE_UPDATE.code.intValue() || flowType == ProductFlowTypeEnum.OTHER_INFO_UPDATE.code.intValue() || flowType == ProductFlowTypeEnum.ALL_INFO_UPDATE.code.intValue();
            //编辑，审核，废止，配置 的权限判定
            if (vo.getCanEdit() || vo.getCanCancel()) {
                if ((flowType == ProductFlowTypeEnum.PRODUCT_SHELF.code.intValue() && !authCodeList.contains("product_shelf_compile_abolish")) ||
                        (flowType == ProductFlowTypeEnum.PRODUCT_OFF_SHELF.code.intValue() && !authCodeList.contains("product_takedown_compile_abolish")) ||
                        (changeFlow && !authCodeList.contains("product_change_compile_abolish"))) {
                    vo.setCanCancel(false);
                    vo.setCanEdit(false);
                }
            }
            if (vo.getCanConfig()) {
                if ((flowType == ProductFlowTypeEnum.PRODUCT_SHELF.code.intValue() && !authCodeList.contains("product_shelf_details_configuration")) ||
                        (flowType == ProductFlowTypeEnum.PRODUCT_OFF_SHELF.code.intValue() && !authCodeList.contains("product_takedown_details_configuration")) ||
                        (changeFlow && !authCodeList.contains("product_change_details_configuration"))) {
                    vo.setCanConfig(false);
                }
            }
            if (vo.getCanAudit()) {
                if ((flowType == ProductFlowTypeEnum.PRODUCT_SHELF.code.intValue() && !authCodeList.contains("product_shelf_details_audit")) ||
                        (flowType == ProductFlowTypeEnum.PRODUCT_OFF_SHELF.code.intValue() && !authCodeList.contains("product_takedown_details_audit")) ||
                        (changeFlow && !authCodeList.contains("product_change_details_audit"))) {
                    vo.setCanAudit(false);
                }
            }
            return vo;
        }).collect(Collectors.toList());
        pageData.setData(collect);
        return BaseAnswer.success(pageData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer cancelFlow(String flowInstanceId, LoginIfo4Redis loginIfo4Redis, Integer subModule) {
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        String ip = org.apache.commons.lang3.StringUtils.isNotEmpty(request.getHeader(Constant.IP)) ? request.getHeader(Constant.IP) : "127.0.0.1";
        Date now = new Date();
        ProductFlowInstance productFlowInstance = productFlowInstanceMapper.selectByPrimaryKey(flowInstanceId);
        if (productFlowInstance == null) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                        "【作废】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "流程单不存在");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程单不存在");
        }
        Integer flowInstanceStatus = productFlowInstance.getStatus();
        if (flowInstanceStatus.intValue() == ProductFlowInstanceStatusEnum.CANCEL.code.intValue() ||
                flowInstanceStatus.intValue() == ProductFlowInstanceStatusEnum.OVER.ordinal()) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                        "【作废】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "流程单已结束");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程单已结束");
        }
        String flowId = productFlowInstance.getFlowId();
        ProductFlow productFlow = productFlowMapper.selectByPrimaryKey(flowId);
        if (productFlow == null) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                        "【作废】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "流程信息不存在");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程信息不存在");
        }
        if (!productFlowInstance.getCanCancel()) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                        "【作废】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "流程单目前不允许废止");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程单目前不允许废止");
        }
        productFlowInstance.setStatus(ProductFlowInstanceStatusEnum.CANCEL.code);
        productFlowInstance.setCurrentStepId("-1");
        productFlowInstance.setUpdateTime(now);
        productFlowInstance.setCanCancel(false);
        productFlowInstance.setCanEdit(false);
        productFlowInstance.setCanAudit(false);
        productFlowInstance.setCanConfig(false);
        productFlowInstanceMapper.updateByPrimaryKey(productFlowInstance);
        int flowType = productFlow.getFlowType().intValue();
        //更新流程sku上下架状态
        ProductFlowInstanceSku productFlowInstanceSku = new ProductFlowInstanceSku();
        productFlowInstanceSku.setUpdateTime(now);
        if (flowType == ProductFlowTypeEnum.PRODUCT_SHELF.code.intValue()) {
            productFlowInstanceSku.setShelfStatus(ProductShelfStatusEnum.SHELF_CANCEL.code);
        }
        if (flowType == ProductFlowTypeEnum.PRODUCT_OFF_SHELF.code.intValue()) {
            productFlowInstanceSku.setShelfStatus(ProductShelfStatusEnum.OFF_SHELF_CANCEL.code);
        }
        if (productFlowInstanceSku.getShelfStatus() != null) {
            ProductFlowInstanceSkuExample example = new ProductFlowInstanceSkuExample().createCriteria().andFlowIdEqualTo(flowId).andFlowInstanceIdEqualTo(flowInstanceId).example();
            productFlowInstanceSkuMapper.updateByExampleSelective(productFlowInstanceSku, example);
        }

        ProductFlowInstanceSpu flowInstanceSpu = getFlowInstanceSpu(flowInstanceId);
        List<ProductFlowInstanceSku> flowInstanceSkuList = getFlowInstanceSkuList(flowInstanceId);
        String skuNameList = flowInstanceSkuList.stream().map(f -> {
            return f.getSkuName();
        }).collect(Collectors.joining(","));

        //设置当前"发起流程"的处理时间
        ProductFlowInstanceTaskExample taskExample = new ProductFlowInstanceTaskExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).andHandleStatusEqualTo(ProductFlowHandleStatusEnum.UNHANDLE.code).example();
        List<ProductFlowInstanceTask> list = productFlowInstanceTaskMapper.selectByExample(taskExample);
        ProductFlowInstanceTask productFlowInstanceTask = list.get(0);
        productFlowInstanceTask.setHandleStatus(ProductFlowHandleStatusEnum.CANCEL.code);
        productFlowInstanceTask.setOptions("废止流程");
        productFlowInstanceTask.setHandleTime(now);
        productFlowInstanceTask.setUpdateTime(now);
        productFlowInstanceTaskMapper.updateByPrimaryKey(productFlowInstanceTask);

        //加一个空的流程任务，用来表示最新审核步骤"结束（废止）"
        ProductFlowInstanceTask flowInstanceTask = new ProductFlowInstanceTask();
        flowInstanceTask.setId(BaseServiceUtils.getId());
        flowInstanceTask.setFlowId(productFlowInstance.getFlowId());
        flowInstanceTask.setFlowInstanceId(flowInstanceId);
        flowInstanceTask.setStepId("-1");
        flowInstanceTask.setStepName("结束(废止)");
        flowInstanceTask.setHandleStatus(ProductFlowHandleStatusEnum.UNHANDLE.code);
        flowInstanceTask.setCreateTime(now);
        flowInstanceTask.setUpdateTime(now);
        productFlowInstanceTaskMapper.insertSelective(flowInstanceTask);

        //记录日志
        String content = getLogContent("【作废】", productFlowInstance.getFlowInstanceNumber(), flowInstanceSpu.getSpuName(), skuNameList, null, null, null, null, null, null, null);
        if (StringUtils.isNotEmpty(content)) {
            logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, subModule, content, LogResultEnum.LOG_SUCESS.code, null);
        }

        return BaseAnswer.success(null);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer auditProductFlow(AuditProductFlowParam param, LoginIfo4Redis loginIfo4Redis, Integer subModule, String ip) {
        boolean isAdmin = loginIfo4Redis.getIsAdmin() == null ? false : loginIfo4Redis.getIsAdmin();
        Date now = new Date();
        String userId = loginIfo4Redis.getUserId();
        String flowInstanceId = param.getFlowInstanceId();
        List<String> knownUserIdList = param.getKnownUserIdList();
        String nextAssigneeId = param.getNextAssigneeId();
        Data4User nextAssigneeUser = null;
        if (StringUtils.isNotEmpty(nextAssigneeId)) {
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfo(nextAssigneeId);
            if (data4UserBaseAnswer == null || data4UserBaseAnswer.getData() == null) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                            "-", userId, ip, LogResultEnum.LOG_FAIL.code, "下一处理人id:" + nextAssigneeId + "不存在");
                });
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "下一处理人id:" + nextAssigneeId + "不存在");
            }
            nextAssigneeUser = data4UserBaseAnswer.getData();
        }
        String nextNextAssignId = param.getNextNextAssignId();
        Data4User nextNextAssigneeUser = null;
        if (StringUtils.isNotEmpty(nextNextAssignId)) {
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfo(nextNextAssignId);
            if (data4UserBaseAnswer == null || data4UserBaseAnswer.getData() == null) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                            "-", userId, ip, LogResultEnum.LOG_FAIL.code, "下一处理人的下一处理人id:" + nextNextAssignId + "不存在");
                });
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "下一处理人的下一处理人id:" + nextNextAssignId + "不存在");
            }
            nextNextAssigneeUser = data4UserBaseAnswer.getData();
        }

        String option = param.getOptions();
        Boolean pass = param.getPass();
        Integer limitId = param.getLimitId();
        Boolean redirect = param.getRedirect();

        boolean config = param.isConfig();
        boolean edit = param.isEdit();
        ProductFlowInstanceDetailVO.SpuInfo spuInfo = param.getSpuInfo();
        ProductFlowInstanceDetailVO.SkuInfo skuInfo = param.getSkuInfo();
        List<ProductFlowInstanceDetailVO.AtomInfo> atomInfoList = param.getAtomInfoList();
        if (config && edit) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                        "-", userId, ip, LogResultEnum.LOG_FAIL.code, "不能同时为 编辑和配置");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "不能同时为 编辑和配置");
        }
        String title = config ? "【配置】" : (edit ? "【编辑】" : "【审核】");
        List<String> attachmentList = param.getAttachmentList();
        if (pass == null) {
            //界面没有通过和不通过。点击提交实际上就是通过
            pass = true;
        }
        if (limitId != null && !ProductFlowLimitEnum.contains(limitId)) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                        title, userId, ip, LogResultEnum.LOG_FAIL.code, "限制错误");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "限制错误");
        }
        ProductFlowInstance productFlowInstance = productFlowInstanceMapper.selectByPrimaryKey(flowInstanceId);
        if (productFlowInstance == null) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                        title, userId, ip, LogResultEnum.LOG_FAIL.code, "流程单不存在");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程单不存在");
        }
        ProductFlow productFlow = productFlowMapper.selectByPrimaryKey(productFlowInstance.getFlowId());
        if (productFlow == null) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                        title, userId, ip, LogResultEnum.LOG_FAIL.code, "流程单不存在");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程不存在");
        }
        ProductFlowInstanceTaskExample taskExample = new ProductFlowInstanceTaskExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).andStepIdEqualTo(productFlowInstance.getCurrentStepId()).andHandleStatusEqualTo(ProductFlowHandleStatusEnum.UNHANDLE.code).example();
        List<ProductFlowInstanceTask> productFlowTaskList = productFlowInstanceTaskMapper.selectByExample(taskExample);
        if (productFlowTaskList.isEmpty()) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                        title, userId, ip, LogResultEnum.LOG_FAIL.code, "未处理的审核任务不存在");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未处理的审核任务不存在");
        }
        ProductFlowInstanceTask productFlowTask = productFlowTaskList.get(0);
        String assigneeId = productFlowTask.getAssigneeId();
        if (!assigneeId.equals(userId) && !isAdmin) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                        title, userId, ip, LogResultEnum.LOG_FAIL.code, "当前登录用户无权限处理");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "当前登录用户无权限处理");
        }
        String existedNextAssigneeId = productFlowTask.getNextAssigneeId();
        Data4User existedNextAssigneeUser = null;
        if (StringUtils.isNotEmpty(existedNextAssigneeId)) {
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfo(existedNextAssigneeId);
            if (data4UserBaseAnswer == null || data4UserBaseAnswer.getData() == null) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                            title, userId, ip, LogResultEnum.LOG_FAIL.code, "转办的发起人" + existedNextAssigneeId + "不存在");
                });
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "转办的发起人" + existedNextAssigneeId + "不存在");
            }
            existedNextAssigneeUser = data4UserBaseAnswer.getData();
        }
        String creatorId = productFlowInstance.getCreatorId();
        Data4User creatorUserData = null;
        BaseAnswer<Data4User> creatorData = userFeignClient.userInfo(creatorId);
        if (creatorData == null || creatorData.getData() == null) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                        title, userId, ip, LogResultEnum.LOG_FAIL.code, "流程发起人:" + creatorId + "不存在");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程发起人:" + creatorId + "不存在");
        }
        creatorUserData = creatorData.getData();

        //判断当前步骤是否包含知悉
        ProductFlowStep productFlowStep = productFlowStepMapper.selectByPrimaryKey(productFlowInstance.getCurrentStepId());
        if (productFlowStep == null) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                        title, userId, ip, LogResultEnum.LOG_FAIL.code, "当前流程环节不存在");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "当前流程环节不存在");
        }
        Boolean allowKnown = productFlowStep.getAllowKnown();
        List<ProductFlowInstanceSku> productFlowInstanceSkus = getFlowInstanceSkuList(flowInstanceId);
        List<String> skuNameList = productFlowInstanceSkus.stream().map(f -> {
            return f.getSkuName();
        }).collect(Collectors.toList());
        ProductFlowInstanceSpu productFlowInstanceSpu = getFlowInstanceSpu(flowInstanceId);
        if (productFlow.getFlowType().intValue() == ProductFlowTypeEnum.PRODUCT_SHELF.code.intValue() && config) {
            String existedSpuCode = productFlowInstanceSpu.getSpuCode();
            if (StringUtils.isNotEmpty(existedSpuCode) && StringUtils.isEmpty(skuInfo.getSkuCode())) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                            title, userId, ip, LogResultEnum.LOG_FAIL.code, "SKU上架的专员配置必须传递SKU编码");
                });
                //SKU上架
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "SKU上架的专员配置必须传递SKU编码");
            }
            if (StringUtils.isEmpty(existedSpuCode) && (StringUtils.isEmpty(skuInfo.getSkuCode()) || StringUtils.isEmpty(spuInfo.getSpuCode()) || StringUtils.isEmpty(spuInfo.getUrl()))) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                            title, userId, ip, LogResultEnum.LOG_FAIL.code, "SPU上架的专员配置必须传递URL,SPU编码,SKU编码");
                });
                //SPU上架
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "SPU上架的专员配置必须传递URL,SPU编码,SKU编码");
            }
        }
        //更新流程审核过程涉及到的spu,sku,atom产品属性,(根据逻辑，一次只能更新一条sku)
        param.setProductFlowInstanceId(param.getFlowInstanceId());
        editFlow(param, loginIfo4Redis, false, productFlowInstanceSpu, productFlowInstanceSkus.get(0), productFlowInstance, productFlow, ip, false);

        //更新流程步骤审核结果
        productFlowTask.setHandleStatus((redirect != null && redirect) ? ProductFlowHandleStatusEnum.REDIRECT.code : (pass ? ProductFlowHandleStatusEnum.PASS.code : ProductFlowHandleStatusEnum.REJECT.code));
        productFlowTask.setHandleTime(now);
        productFlowTask.setOptions(option);
        productFlowTask.setUpdateTime(now);
        productFlowInstanceTaskMapper.updateByPrimaryKey(productFlowTask);
        if (allowKnown != null && allowKnown && CollectionUtils.isNotEmpty(knownUserIdList)) {
            for (String knownUserId : knownUserIdList) {
                BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfo(knownUserId);
                if (data4UserBaseAnswer == null || data4UserBaseAnswer.getData() == null) {
                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                                title, userId, ip, LogResultEnum.LOG_FAIL.code, "知悉人id:" + knownUserId + "不存在");
                    });
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "知悉人id:" + knownUserId + "不存在");
                }
                Data4User knownUser = data4UserBaseAnswer.getData();
                //发送知悉用户短信
                sendFlowMsg(knownUser.getPhone(), productFlowInstance, productFlow, String.join(",", skuNameList), productFlowSmsConfig.getProductFlowKnownSms());

                //添加知悉的审核记录
                ProductFlowInstanceTask knownFlowTask = new ProductFlowInstanceTask();
                knownFlowTask.setId(BaseServiceUtils.getId());
                knownFlowTask.setFlowInstanceId(flowInstanceId);
                knownFlowTask.setFlowId(productFlowInstance.getFlowId());
                knownFlowTask.setStepId(productFlowInstance.getCurrentStepId());
                knownFlowTask.setStepName("知悉");
                knownFlowTask.setAssigneeId(knownUserId);
                knownFlowTask.setAssigneeName(getNameWithCompany(knownUser));
                knownFlowTask.setOptions("系统短信通知");
                knownFlowTask.setHandleStatus(ProductFlowHandleStatusEnum.KNOWN.code);
                //这里使用new Date(),错开时间，避免排序问题
                knownFlowTask.setHandleTime(new Date());
                knownFlowTask.setCreateTime(new Date());
                knownFlowTask.setUpdateTime(new Date());
                productFlowInstanceTaskMapper.insertSelective(knownFlowTask);
            }
        }
        //审核后的当前步骤id
        String currentStepIdAfterAudit = null;
        Boolean canEdit = null;
        Boolean canCancel = null;
        Boolean canAudit = null;
        Boolean canConfig = null;

        if (redirect != null && redirect) {
            if (StringUtils.isEmpty(nextAssigneeId)) {
                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                            title, userId, ip, LogResultEnum.LOG_FAIL.code, "缺少下一任务处理人");
                });
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "缺少下一任务处理人");
            }
            //对于转办，无论转办人是否通过审核，流程节点回到转办前指定的人（而非实际处理人，如超管）
            ProductFlowInstanceTask redirectTask = new ProductFlowInstanceTask();
            redirectTask.setId(BaseServiceUtils.getId());
            redirectTask.setFlowInstanceId(flowInstanceId);
            redirectTask.setFlowId(productFlowInstance.getFlowId());
            redirectTask.setStepId(productFlowInstance.getCurrentStepId());
            redirectTask.setStepName(productFlowTask.getStepName() + "(转办)");
            redirectTask.setAssigneeId(nextAssigneeId);
            redirectTask.setAssigneeName(getNameWithCompany(nextAssigneeUser));
            redirectTask.setNextAssigneeId(assigneeId);
            redirectTask.setHandleStatus(ProductFlowHandleStatusEnum.UNHANDLE.code);
            redirectTask.setCreateTime(new Date());
            redirectTask.setUpdateTime(new Date());
            productFlowInstanceTaskMapper.insertSelective(redirectTask);

            //发送短信给转办人
            sendFlowMsg(nextAssigneeUser.getPhone(), productFlowInstance, productFlow, String.join(",", skuNameList), productFlowSmsConfig.getProductFlowAuditSms());

            currentStepIdAfterAudit = productFlowInstance.getCurrentStepId();
            //对于转办，可编辑可撤销可配置可审核 不用更新
        } else if (pass != null && !pass) {
            //驳回
            if (StringUtils.isNotEmpty(existedNextAssigneeId)) {
                //转办驳回：已经设置了下一环节处理人，表示当前任务是转办任务
                ProductFlowInstanceTask nextTask = new ProductFlowInstanceTask();
                nextTask.setId(BaseServiceUtils.getId());
                nextTask.setFlowInstanceId(flowInstanceId);
                nextTask.setFlowId(productFlowInstance.getFlowId());
                nextTask.setStepId(productFlowTask.getStepId());
                String stepName = productFlowTask.getStepName();
                //步骤名称去掉特殊说明
                nextTask.setStepName(stepName.substring(0, stepName.indexOf("(")));
                nextTask.setAssigneeId(existedNextAssigneeId);
                nextTask.setAssigneeName(getNameWithCompany(existedNextAssigneeUser));
                nextTask.setHandleStatus(ProductFlowHandleStatusEnum.UNHANDLE.code);
                nextTask.setCreateTime(new Date());
                nextTask.setUpdateTime(new Date());
                productFlowInstanceTaskMapper.insertSelective(nextTask);

                sendFlowMsg(existedNextAssigneeUser.getPhone(), productFlowInstance, productFlow, String.join(",", skuNameList), productFlowSmsConfig.getProductFlowRejectSms());

                currentStepIdAfterAudit = productFlowTask.getStepId();
                //如果转回去后是编辑（最小的index），需要：可编辑可废止，否则不可编辑不可废止
                ProductFlowStepExample previousStepExample = new ProductFlowStepExample().createCriteria().andFlowIdEqualTo(productFlowInstance.getFlowId()).andStepIndexEqualTo(productFlowStep.getStepIndex() - 1).example();
                List<ProductFlowStep> previousStepList = productFlowStepMapper.selectByExample(previousStepExample);
                boolean isFirstStep = previousStepList.isEmpty();
                canEdit = isFirstStep ? true : false;
                canCancel = isFirstStep ? true : false;

                //如果转回去后是专员配置（最大的index），需要：不可审核 可配置。否则： 可审核 不可配置。
                ProductFlowStepExample stepExample = new ProductFlowStepExample().createCriteria().andFlowIdEqualTo(productFlowInstance.getFlowId()).andStepIndexEqualTo(productFlowStep.getStepIndex() + 1).example();
                List<ProductFlowStep> nextStepList = productFlowStepMapper.selectByExample(stepExample);
                boolean isLastStep = nextStepList.isEmpty();
                //如果只有两步，那么最后一步是审核，如果是三步及以上，最后一步是配置
                canAudit = (!isFirstStep && !isLastStep) || (isLastStep && productFlowStep.getStepIndex() == 2) ? true : false;
                canConfig = isLastStep && productFlowStep.getStepIndex() > 2 ? true : false;

            } else {
                //直接驳回
                String rejectNextStepId = productFlowStep.getRejectNextStepId();
                ProductFlowStep rejectNextStep = productFlowStepMapper.selectByPrimaryKey(rejectNextStepId);
                if (rejectNextStep == null) {
                    log.info("驳回后步骤为空,不进行任何处理");
                } else {
                    ProductFlowInstanceTask nextTask = new ProductFlowInstanceTask();
                    nextTask.setId(BaseServiceUtils.getId());
                    nextTask.setFlowInstanceId(flowInstanceId);
                    nextTask.setFlowId(productFlowInstance.getFlowId());
                    nextTask.setStepId(rejectNextStep.getId());
                    nextTask.setStepName(rejectNextStep.getStepName() + "(驳回)");
                    nextTask.setAssigneeId(productFlowInstance.getCreatorId());
                    //避免用户改名，重新查询
                    BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfo(productFlowInstance.getCreatorId());
                    if (data4UserBaseAnswer == null || data4UserBaseAnswer.getData() == null) {
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "用户id" + productFlowInstance.getCreatorId() + "不存在");
                    }
                    String name = getNameWithCompany(data4UserBaseAnswer.getData());
                    nextTask.setAssigneeName(name);
                    nextTask.setHandleStatus(ProductFlowHandleStatusEnum.UNHANDLE.code);
                    nextTask.setCreateTime(new Date());
                    nextTask.setUpdateTime(new Date());
                    productFlowInstanceTaskMapper.insertSelective(nextTask);

                    //给流程发起人发送短信
                    sendFlowMsg(creatorUserData.getPhone(), productFlowInstance, productFlow, String.join(",", skuNameList), productFlowSmsConfig.getProductFlowRejectSms());

                    currentStepIdAfterAudit = rejectNextStepId;
                    canEdit = true;
                    canCancel = true;
                    canAudit = false;
                    canConfig = false;
                }
            }
            //驳回后清掉标红字段
            productFlowInstance.setChangeList("");
            productFlowInstance.setUpdateTime(now);
        } else {
            //通过
            if (StringUtils.isNotEmpty(existedNextAssigneeId)) {
                //转办通过：已经设置了下一环节处理人，表示当前任务是转办任务
                ProductFlowInstanceTask nextTask = new ProductFlowInstanceTask();
                nextTask.setId(BaseServiceUtils.getId());
                nextTask.setFlowInstanceId(flowInstanceId);
                nextTask.setFlowId(productFlowInstance.getFlowId());
                nextTask.setStepId(productFlowTask.getStepId());
                String stepName = productFlowTask.getStepName();
                //步骤名称去掉特殊说明
                nextTask.setStepName(stepName.substring(0, stepName.indexOf("(")));
                nextTask.setAssigneeId(existedNextAssigneeId);
                nextTask.setAssigneeName(getNameWithCompany(existedNextAssigneeUser));
                nextTask.setHandleStatus(ProductFlowHandleStatusEnum.UNHANDLE.code);
                nextTask.setCreateTime(new Date());
                nextTask.setUpdateTime(new Date());
                productFlowInstanceTaskMapper.insertSelective(nextTask);

                sendFlowMsg(existedNextAssigneeUser.getPhone(), productFlowInstance, productFlow, String.join(",", skuNameList), productFlowSmsConfig.getProductFlowAuditSms());

                currentStepIdAfterAudit = productFlowTask.getStepId();
                //如果转回去后是编辑（最小的index），需要：可编辑可废止，否则不可编辑不可废止
                ProductFlowStepExample previousStepExample = new ProductFlowStepExample().createCriteria().andFlowIdEqualTo(productFlowInstance.getFlowId()).andStepIndexEqualTo(productFlowStep.getStepIndex() - 1).example();
                List<ProductFlowStep> previousStepList = productFlowStepMapper.selectByExample(previousStepExample);
                boolean isFirstStep = previousStepList.isEmpty();
                canEdit = isFirstStep ? true : false;
                canCancel = isFirstStep ? true : false;

                //如果转回去后是专员配置（最大的index），需要：不可审核 可配置。否则： 可审核 不可配置。
                ProductFlowStepExample nextStepExample = new ProductFlowStepExample().createCriteria().andFlowIdEqualTo(productFlowInstance.getFlowId()).andStepIndexEqualTo(productFlowStep.getStepIndex() + 1).example();
                List<ProductFlowStep> nextStepList = productFlowStepMapper.selectByExample(nextStepExample);
                boolean isLastStep = nextStepList.isEmpty();
                //如果只有两步，那么最后一步是审核，如果是三步及以上，最后一步是配置
                canAudit = (!isFirstStep && !isLastStep) || (isLastStep && productFlowStep.getStepIndex() == 2) ? true : false;
                canConfig = isLastStep && productFlowStep.getStepIndex() > 2 ? true : false;

            } else {
                //正常通过，获取下一个流程步骤
                int nextStepIndex = productFlowStep.getStepIndex() + 1;
                ProductFlowStepExample stepExample = new ProductFlowStepExample().createCriteria().andFlowIdEqualTo(productFlowInstance.getFlowId()).andStepIndexEqualTo(nextStepIndex).example();
                List<ProductFlowStep> productFlowSteps = productFlowStepMapper.selectByExample(stepExample);
                if (!productFlowSteps.isEmpty()) {
                    ProductFlowStep flowStep = productFlowSteps.get(0);
                    if (limitId != null && limitId.intValue() == ProductFlowLimitEnum.OVER_INVENTORY.code.intValue()) {
                        if (StringUtils.isEmpty(nextAssigneeId)) {
                            executorService.execute(() -> {
                                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                                        title, userId, ip, LogResultEnum.LOG_FAIL.code, "缺少下一任务处理人(知悉)");
                            });
                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "缺少下一任务处理人(知悉)");
                        }
                        //对于"供应额度不足"限制，下一个步骤自动变为知悉，还需要保存再下一个步骤
                        ProductFlowInstanceTask knownFlowTask = new ProductFlowInstanceTask();
                        knownFlowTask.setId(BaseServiceUtils.getId());
                        knownFlowTask.setFlowInstanceId(flowInstanceId);
                        knownFlowTask.setFlowId(productFlowInstance.getFlowId());
                        knownFlowTask.setStepId(flowStep.getId());
                        knownFlowTask.setStepName(flowStep.getStepName() + "(知悉)");
                        knownFlowTask.setAssigneeId(nextAssigneeId);
                        knownFlowTask.setAssigneeName(getNameWithCompany(nextAssigneeUser));
                        knownFlowTask.setOptions("系统短信通知");
                        knownFlowTask.setHandleStatus(ProductFlowHandleStatusEnum.KNOWN.code);
                        knownFlowTask.setHandleTime(new Date());
                        knownFlowTask.setCreateTime(new Date());
                        knownFlowTask.setUpdateTime(new Date());
                        productFlowInstanceTaskMapper.insertSelective(knownFlowTask);
                        //发送知悉短信
                        sendFlowMsg(nextAssigneeUser.getPhone(), productFlowInstance, productFlow, String.join(",", skuNameList), productFlowSmsConfig.getProductFlowKnownSms());

                        nextStepIndex = flowStep.getStepIndex() + 1;
                        stepExample = new ProductFlowStepExample().createCriteria().andFlowIdEqualTo(productFlowInstance.getFlowId()).andStepIndexEqualTo(nextStepIndex).example();
                        productFlowSteps = productFlowStepMapper.selectByExample(stepExample);
                        if (!productFlowSteps.isEmpty()) {
                            if (StringUtils.isEmpty(nextNextAssignId)) {
                                executorService.execute(() -> {
                                    logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                                            title, userId, ip, LogResultEnum.LOG_FAIL.code, "缺少下一任务再下一任务处理人");
                                });
                                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "缺少下一任务再下一任务处理人");
                            }
                            flowStep = productFlowSteps.get(0);
                            ProductFlowInstanceTask nextTask = new ProductFlowInstanceTask();
                            nextTask.setId(BaseServiceUtils.getId());
                            nextTask.setFlowInstanceId(flowInstanceId);
                            nextTask.setFlowId(productFlowInstance.getFlowId());
                            nextTask.setStepId(flowStep.getId());
                            nextTask.setStepName(flowStep.getStepName());
                            nextTask.setAssigneeId(nextNextAssignId);
                            nextTask.setAssigneeName(getNameWithCompany(nextNextAssigneeUser));
                            nextTask.setHandleStatus(ProductFlowHandleStatusEnum.UNHANDLE.code);
                            nextTask.setCreateTime(new Date());
                            nextTask.setUpdateTime(new Date());
                            productFlowInstanceTaskMapper.insertSelective(nextTask);

                            sendFlowMsg(nextNextAssigneeUser.getPhone(), productFlowInstance, productFlow, String.join(",", skuNameList), productFlowSmsConfig.getProductFlowAuditSms());
                        }

                    } else {
                        if (StringUtils.isEmpty(nextAssigneeId)) {
                            executorService.execute(() -> {
                                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                                        title, userId, ip, LogResultEnum.LOG_FAIL.code, "缺少下一任务处理人");
                            });
                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "缺少下一任务处理人");
                        }
                        //保存下一步任务
                        ProductFlowInstanceTask nextTask = new ProductFlowInstanceTask();
                        nextTask.setId(BaseServiceUtils.getId());
                        nextTask.setFlowInstanceId(flowInstanceId);
                        nextTask.setFlowId(productFlowInstance.getFlowId());
                        nextTask.setStepId(flowStep.getId());
                        nextTask.setStepName(flowStep.getStepName());
                        nextTask.setAssigneeId(nextAssigneeId);
                        nextTask.setAssigneeName(getNameWithCompany(nextAssigneeUser));
                        nextTask.setHandleStatus(ProductFlowHandleStatusEnum.UNHANDLE.code);
                        nextTask.setCreateTime(new Date());
                        nextTask.setUpdateTime(new Date());
                        productFlowInstanceTaskMapper.insertSelective(nextTask);

                        sendFlowMsg(nextAssigneeUser.getPhone(), productFlowInstance, productFlow, String.join(",", skuNameList), productFlowSmsConfig.getProductFlowAuditSms());

                    }
                    currentStepIdAfterAudit = flowStep.getId();
                    canEdit = false;
                    canCancel = false;
                    //根据下一步是不是专员配置（最后一步），判断是否可审核，可配置
                    stepExample = new ProductFlowStepExample().createCriteria().andFlowIdEqualTo(productFlowInstance.getFlowId()).andStepIndexEqualTo(nextStepIndex + 1).example();
                    List<ProductFlowStep> nextStepList = productFlowStepMapper.selectByExample(stepExample);
                    boolean isLastStep = nextStepList.isEmpty();
                    //如果只有两步，那么最后一步是审核，如果是三步及以上，最后一步是配置
                    canAudit = isLastStep && nextStepIndex > 2 ? false : true;
                    canConfig = isLastStep && nextStepIndex > 2 ? true : false;
                } else {
                    //没有下一步，表示流程结束,发送短信通知流程所有参与人(上架发送短信:产品005，其他流程发送短信：产品002)
                    List<ProductFlowInstanceTask> taskList = productFlowInstanceTaskMapper.selectByExample(new ProductFlowInstanceTaskExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example());
                    Set<String> handleUserSet = taskList.stream().filter(t -> {
                        return !"-1".equals(t.getStepId());
                    }).map(t -> {
                        return t.getAssigneeId();
                    }).collect(Collectors.toSet());
                    Set<String> userPhoneSet = new HashSet<>();
                    for (String handledUserId : handleUserSet) {
                        BaseAnswer<Data4User> userData = userFeignClient.userInfo(handledUserId);
                        if (userData == null || userData.getData() == null) {
                            executorService.execute(() -> {
                                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, subModule,
                                        title, userId, ip, LogResultEnum.LOG_FAIL.code, "流程参与人:" + creatorId + "不存在");
                            });
                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程参与人:" + creatorId + "不存在");
                        }
                        userPhoneSet.add(userData.getData().getPhone());
                    }
                    Map<String, String> paramMap = new HashMap<>();
                    if(ProductFlowTypeEnum.PRODUCT_SHELF.code.intValue() == productFlow.getFlowType().intValue()){
                        paramMap.put("spuName", productFlowInstanceSpu.getSpuName());
                        paramMap.put("skuName", String.join(",", skuNameList));
                        paramMap.put("url", productFlowInstanceSpu.getUrl());
                        baseSmsService.sendManyMsg(new ArrayList<>(userPhoneSet), productFlowSmsConfig.getProductFlowShelfCompleteSms(), paramMap);
                    }else {
                        ProductFlowTypeEnum flowType = ProductFlowTypeEnum.fromCode(productFlow.getFlowType());
                        paramMap.put("flowType", flowType.name);
                        paramMap.put("flowInstanceNumber", productFlowInstance.getFlowInstanceNumber());
                        paramMap.put("skuName", String.join(",", skuNameList));
                        baseSmsService.sendManyMsg(new ArrayList<>(userPhoneSet), productFlowSmsConfig.getProductFlowPassSms(), paramMap);
                    }

                    canEdit = false;
                    canCancel = false;
                    canAudit = false;
                    canConfig = false;
                    productFlowInstance.setStatus(ProductFlowInstanceStatusEnum.OVER.code);
                    currentStepIdAfterAudit = "-1";
                    //加一个空的流程任务，用来表示最新审核步骤"结束"
                    ProductFlowInstanceTask flowInstanceTask = new ProductFlowInstanceTask();
                    flowInstanceTask.setId(BaseServiceUtils.getId());
                    flowInstanceTask.setFlowId(productFlowInstance.getFlowId());
                    flowInstanceTask.setFlowInstanceId(flowInstanceId);
                    flowInstanceTask.setStepId("-1");
                    flowInstanceTask.setStepName("结束");
                    flowInstanceTask.setHandleStatus(ProductFlowHandleStatusEnum.UNHANDLE.code);
                    flowInstanceTask.setCreateTime(new Date());
                    flowInstanceTask.setUpdateTime(new Date());
                    productFlowInstanceTaskMapper.insertSelective(flowInstanceTask);
                    //更新sku上下架状态
                    if (productFlow.getFlowType().intValue() == ProductFlowTypeEnum.PRODUCT_SHELF.code.intValue() ||
                            productFlow.getFlowType().intValue() == ProductFlowTypeEnum.PRODUCT_OFF_SHELF.code.intValue()) {
                        ProductFlowInstanceSkuExample example = new ProductFlowInstanceSkuExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceId).example();
                        ProductFlowInstanceSku flowInstanceSku = new ProductFlowInstanceSku();
                        flowInstanceSku.setShelfStatus(productFlow.getFlowType().intValue() == ProductFlowTypeEnum.PRODUCT_SHELF.code.intValue() ? ProductShelfStatusEnum.SHELF_SUCCESS.code : ProductShelfStatusEnum.OFF_SHELF_SUCCESS.code);
                        flowInstanceSku.setUpdateTime(now);
                        productFlowInstanceSkuMapper.updateByExampleSelective(flowInstanceSku, example);
                    }
                }
            }
        }

        //更新流程实例的可编辑，可审核，可取消，可配置属性
        if (currentStepIdAfterAudit != null) {
            productFlowInstance.setCurrentStepId(currentStepIdAfterAudit);
            productFlowInstance.setUpdateTime(now);
        }
        if (canAudit != null) {
            productFlowInstance.setCanAudit(canAudit);
        }
        if (canCancel != null) {
            productFlowInstance.setCanCancel(canCancel);
        }
        if (canConfig != null) {
            productFlowInstance.setCanConfig(canConfig);
        }
        if (canEdit != null) {
            productFlowInstance.setCanEdit(canEdit);
        }
        if (canAudit != null || canCancel != null || canConfig != null || canEdit != null) {
            productFlowInstance.setUpdateTime(now);
        }
        //变更流程，前端多次传递需要叠加（比如编辑环节进行了转办）
        String newChangeListStr = param.getChangeList();
        if (pass && StringUtils.isNotEmpty(newChangeListStr)) {
            JSONArray newArr = JSONObject.parseArray(newChangeListStr);
            String oldChangeListStr = productFlowInstance.getChangeList();
            if (StringUtils.isNotEmpty(oldChangeListStr)) {
                JSONArray arr = getChangeListArr(newArr, oldChangeListStr);
                productFlowInstance.setChangeList(JSON.toJSONString(arr));
            } else {
                //没有数据，直接保存
                productFlowInstance.setChangeList(newChangeListStr);
            }

        }
        productFlowInstanceMapper.updateByPrimaryKeySelective(productFlowInstance);

        //记录日志
        String spuCode = spuInfo == null ? null : spuInfo.getSpuCode();
        String url = spuInfo == null ? null : spuInfo.getUrl();
        String skuCode = skuInfo == null ? null : skuInfo.getSkuCode();
        Integer flowType = productFlow.getFlowType();

        String flowInstanceNumber = productFlowInstance.getFlowInstanceNumber();
        String spuName = productFlowInstanceSpu.getSpuName();
        String skuName = String.join(",", skuNameList);
        String resultDesc = (redirect != null && redirect) ? "转办" : ((pass != null && pass) ? "通过" : "驳回");
        String downloadAttachmentList = CollectionUtils.isEmpty(attachmentList) ? null : String.join(",", attachmentList);
        if (edit) {
            //编辑时，不记录审核结果和附件,编码,链接
            resultDesc = null;
            downloadAttachmentList = null;
            spuCode = null;
            skuCode = null;
            url = null;
        }
        if (config) {
            resultDesc = null;
            if (flowType.intValue() != ProductFlowTypeEnum.PRODUCT_SHELF.code.intValue()) {
                //非上架的配置，不显示编码和链接
                spuCode = null;
                skuCode = null;
                url = null;
            }
        }

        if (!edit && !config) {
            //审核时不展示商品编码和链接
            spuCode = null;
            skuCode = null;
            url = null;
        }

        String content = "";
        /*boolean salePriceUpdate = flowType.intValue() == ProductFlowTypeEnum.SALE_PRICE_UPDATE.code.intValue();
        boolean settlePriceUpdate = flowType.intValue() == ProductFlowTypeEnum.SETTLE_PRICE_UPDATE.code.intValue();
        boolean otherInfoUpdate = flowType.intValue() == ProductFlowTypeEnum.OTHER_INFO_UPDATE.code.intValue();
        boolean allInfoUpdate = flowType.intValue() == ProductFlowTypeEnum.ALL_INFO_UPDATE.code.intValue();
        if (salePriceUpdate || settlePriceUpdate
        ){
            if (salePriceUpdate) {
                title = "【销售价变更】";
            }else if (settlePriceUpdate){
                title = "【结算价变更】";
            }
            content = getSkuUpdatePriceLogContent(title,flowInstanceNumber,param);
        }else {
            if (otherInfoUpdate) {
                title = "【非价格信息变更】";
            }else if (allInfoUpdate){
                title = "【所有信息变更】";
            }
            content = getLogContent(title, flowInstanceNumber, spuName, skuName, resultDesc, downloadAttachmentList, url, spuCode, skuCode,null,null);
        }*/
        content = getLogContent(title, flowInstanceNumber, spuName, skuName, resultDesc, downloadAttachmentList, url, spuCode, skuCode, null, null);

        if (StringUtils.isNotEmpty(content)) {
            logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, subModule, content, LogResultEnum.LOG_SUCESS.code, null);
        }
        return BaseAnswer.success(null);
    }

    private static JSONArray getChangeListArr(JSONArray newArr, String oldChangeListStr) {
        Map<String, List<String>> nameListMap = new HashMap<>();
        JSONArray oldArr = JSONObject.parseArray(oldChangeListStr);
        for (Object o : newArr) {
            JSONObject newObject = (JSONObject) o;
            String newName = newObject.getString("name");
            if (newName == null) {
                continue;
            }
            List<String> newList = (List<String>) (newObject.get("list"));
            if (nameListMap.containsKey(newName)) {
                List<String> list = nameListMap.get(newName);
                list.addAll(newList);
                nameListMap.put(newName, new ArrayList<>(new HashSet<>(list)));
            } else {
                nameListMap.put(newName, newList);
            }
        }

        for (Object o : oldArr) {
            JSONObject oldObject = (JSONObject) o;
            String oldName = oldObject.getString("name");
            if (oldName == null) {
                continue;
            }
            List<String> oldList = (List<String>) (oldObject.get("list"));
            if (nameListMap.containsKey(oldName)) {
                List<String> list = nameListMap.get(oldName);
                list.addAll(oldList);
                nameListMap.put(oldName, new ArrayList<>(new HashSet<>(list)));
            } else {
                nameListMap.put(oldName, oldList);
            }
        }
        JSONArray arr = new JSONArray();
        for (Map.Entry<String, List<String>> entry : nameListMap.entrySet()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", entry.getKey());
            jsonObject.put("list", entry.getValue());
            arr.add(jsonObject);
        }
        return arr;
    }

    private String getNameWithCompany(Data4User data) {
        String name = data.getName();
        String company = data.getCompany();
        if (StringUtils.isNotEmpty(company)) {
            name = name + "(" + company + ")";
        }
        return name;
    }

    /**
     * 根据流程类型获取日志子模块
     */
    private Integer getLogSubModuleFromFlowType(Integer flowType) {
        Integer subModule = null;
        if (flowType.intValue() == ProductFlowTypeEnum.PRODUCT_SHELF.code.intValue()) {
            subModule = ProductManageOperateEnum.SHELF.code;
        }
        if (flowType.intValue() == ProductFlowTypeEnum.PRODUCT_OFF_SHELF.code.intValue()) {
            subModule = ProductManageOperateEnum.OFF_SHELF.code;
        }
        if (flowType.intValue() == ProductFlowTypeEnum.SALE_PRICE_UPDATE.code.intValue() ||
                flowType.intValue() == ProductFlowTypeEnum.SETTLE_PRICE_UPDATE.code.intValue() ||
                flowType.intValue() == ProductFlowTypeEnum.OTHER_INFO_UPDATE.code.intValue() ||
                flowType.intValue() == ProductFlowTypeEnum.ALL_INFO_UPDATE.code.intValue()
        ) {
            subModule = ProductManageOperateEnum.UPDATE.code;
        }
        return subModule;
    }

    @Override
    public String getLogContent(String title,
                                String flowInstanceNumber,
                                String spuName,
                                String skuName,
                                String resultDesc,
                                String downloadAttachmentList,
                                String url,
                                String spuCode,
                                String skuCode,
                                String oldFlowNumber,
                                String newFlowNumber
    ) {
        List<String> logList = new ArrayList<>();
        if (StringUtils.isNotEmpty(title)) {
            logList.add(title);
        }
        if (StringUtils.isNotEmpty(flowInstanceNumber)) {
            logList.add("流程编号 " + flowInstanceNumber);
        }
        if (StringUtils.isNotEmpty(spuName)) {
            logList.add("商品名称(SPU) " + spuName);
        }
        if (StringUtils.isNotEmpty(skuName)) {
            logList.add("商品名称(SKU) " + skuName);
        }
        if (StringUtils.isNotEmpty(resultDesc)) {
            logList.add("审核结果 " + resultDesc);
        }
        if (StringUtils.isNotEmpty(url)) {
            logList.add("商城链接 " + url);
        }
        if (StringUtils.isNotEmpty(spuCode)) {
            logList.add("商品编码(SPU) " + spuCode);
        }
        if (StringUtils.isNotEmpty(skuCode)) {
            logList.add("商品编码(SKU) " + skuCode);
        }
        if (StringUtils.isNotEmpty(downloadAttachmentList)) {
            logList.add("下载附件 " + downloadAttachmentList);
        }
        if (StringUtils.isNotEmpty(oldFlowNumber)) {
            logList.add("原流程编号 " + oldFlowNumber);
        }
        if (StringUtils.isNotEmpty(newFlowNumber)) {
            logList.add("新流程编号 " + newFlowNumber);
        }
        if (CollectionUtils.isNotEmpty(logList)) {
            return String.join(System.getProperty("line.separator"), logList);
        }
        return null;
    }

    public String getSkuUpdateCreateLogContent(String title,
                                               String flowInstanceNumber,
                                               String spuName,
                                               String skuName
    ) {
        List<String> logList = new ArrayList<>();
        if (StringUtils.isNotEmpty(title)) {
            logList.add(title);
        }
        if (StringUtils.isNotEmpty(flowInstanceNumber)) {
            logList.add("流程编号 " + flowInstanceNumber);
        }
        if (StringUtils.isNotEmpty(spuName)) {
            logList.add("商品名称(SPU) " + spuName);
        }
        if (StringUtils.isNotEmpty(skuName)) {
            logList.add("商品名称(SKU) " + skuName);
        }

        if (CollectionUtils.isNotEmpty(logList)) {
            return String.join(System.getProperty("line.separator"), logList);
        }
        return null;
    }


    public String getLogInquireContent(String title, String flowInstanceNumber, String productStandard, String productType, String spuName, String skuName, String resultDesc, String downloadAttachmentList, String url, String spuCode, String skuCode) {
        List<String> logList = new ArrayList<>();
        if (StringUtils.isNotEmpty(title)) {
            logList.add(title);
        }
        if (StringUtils.isNotEmpty(flowInstanceNumber)) {
            logList.add("流程编号 " + flowInstanceNumber);
        }
        if (StringUtils.isNotEmpty(productStandard)) {
            logList.add("产品标准" + productStandard);
        }
        if (StringUtils.isNotEmpty(productType)) {
            logList.add("产品类别 " + productType);
        }
        if (StringUtils.isNotEmpty(spuName)) {
            logList.add("商品名称(SPU) " + spuName);
        }
        if (StringUtils.isNotEmpty(skuName)) {
            logList.add("商品名称(SKU) " + skuName);
        }
        if (StringUtils.isNotEmpty(resultDesc)) {
            logList.add("审核结果 " + resultDesc);
        }
        if (StringUtils.isNotEmpty(url)) {
            logList.add("商城链接 " + url);
        }
        if (StringUtils.isNotEmpty(spuCode)) {
            logList.add("商品编码(SPU) " + spuCode);
        }
        if (StringUtils.isNotEmpty(skuCode)) {
            logList.add("商品编码(SKU) " + skuCode);
        }
        if (StringUtils.isNotEmpty(downloadAttachmentList)) {
            logList.add("下载附件 " + downloadAttachmentList);
        }
        if (CollectionUtils.isNotEmpty(logList)) {
            return String.join(System.getProperty("line.separator"), logList);
        }
        return null;
    }

    public String getSkuUpdatePriceLogContent(String title,
                                              String flowInstanceNumber,
                                              ProductFlowInstanceEditParam productFlowInstanceEditParam) {
        List<String> logList = new ArrayList<>();
        LoginIfo4Redis loginIfo4Redis = new LoginIfo4Redis();
        loginIfo4Redis.setUserId("-1");
        BaseAnswer<ProductFlowInstanceDetailVO> instanceDetail = getInstanceDetail(productFlowInstanceEditParam.getProductFlowInstanceId(),
                loginIfo4Redis,
                ProductManageOperateEnum.UPDATE.code);
        if (StringUtils.isNotEmpty(title)) {
            logList.add(title);
        }
        if (StringUtils.isNotEmpty(flowInstanceNumber)) {
            logList.add("流程编号 " + flowInstanceNumber);
        }
        String spuName = instanceDetail.getData().getSpuInfo().getSpuName();
        if (StringUtils.isNotEmpty(spuName)) {
            logList.add("商品名称(SPU) " + spuName);
        }
        if (!Optional.ofNullable(instanceDetail).isPresent()) {
            throw new BusinessException("10008", "未找到有效的流程相关数据信息");
        }
        List<ProductFlowInstanceDetailVO.SkuInfo> skuInfoList = instanceDetail.getData().getSkuInfoList();
        if (CollectionUtils.isEmpty(skuInfoList)) {
            throw new BusinessException("10008", "流程未配置商品sku相关信息");
        }
        ProductFlowInstanceDetailVO.SkuInfo oldSkuInfo = skuInfoList.get(0);
        ProductFlowInstanceDetailVO.SkuInfo skuInfo = productFlowInstanceEditParam.getSkuInfo();
        String skuName = skuInfo.getSkuName();
        logList.add("商品名称(SKU) " + skuName);

        Long skuOldSalePrice = oldSkuInfo.getSalePrice() == null ? -1 : oldSkuInfo.getSalePrice();
        Long skuSalePrice = skuInfo.getSalePrice() == null ? -1 : skuInfo.getSalePrice();
        if (skuSalePrice != null && skuOldSalePrice != null
                && !skuSalePrice.equals(skuOldSalePrice)) {
            logList.add("销售价格(单位厘)由 " + skuOldSalePrice + "调整为" + skuSalePrice);
        }

        Long oldSaleMinPrice = oldSkuInfo.getSaleMinPrice() == null ? -1 : oldSkuInfo.getSaleMinPrice();
        Long saleMinPrice = skuInfo.getSaleMinPrice() == null ? -1 : skuInfo.getSaleMinPrice();
        if (oldSaleMinPrice != null && saleMinPrice != null
                && !oldSaleMinPrice.equals(saleMinPrice)) {
            logList.add("销售最低价(单位厘)由 " + oldSaleMinPrice + "调整为" + saleMinPrice);
        }

        Long oldSaleMaxPrice = oldSkuInfo.getSaleMaxPrice() == null ? -1 : oldSkuInfo.getSaleMaxPrice();
        Long saleMaxPrice = skuInfo.getSaleMaxPrice() == null ? -1 : skuInfo.getSaleMaxPrice();
        if (oldSaleMaxPrice != null && saleMaxPrice != null
                && !oldSaleMaxPrice.equals(saleMaxPrice)) {
            logList.add("销售最高价(单位厘)由 " + oldSaleMaxPrice + "调整为" + saleMaxPrice);
        }

        Long oldProvincePrice = oldSkuInfo.getProvincePrice() == null ? -1 : oldSkuInfo.getProvincePrice();
        Long provincePrice = skuInfo.getProvincePrice() == null ? -1 : skuInfo.getProvincePrice();
        if (oldProvincePrice != null && provincePrice != null
                && !oldProvincePrice.equals(provincePrice)) {
            logList.add("省公司价格(单位厘)由 " + oldProvincePrice + "调整为" + provincePrice);
        }

        List<ProductFlowInstanceDetailVO.AtomInfo> atomInfoList = productFlowInstanceEditParam.getAtomInfoList();
        List<ProductFlowInstanceDetailVO.AtomInfo> oldAtomInfoList = instanceDetail.getData().getAtomInfoList();
        if (CollectionUtils.isNotEmpty(atomInfoList)
                && CollectionUtils.isNotEmpty(oldAtomInfoList)) {

            atomInfoList.forEach(atomInfo -> {
                oldAtomInfoList.forEach(oldAtomInfo -> {
                    String atomInfoId = atomInfo.getId();
                    String oldAtomInfoId = oldAtomInfo.getId();
                    if (atomInfoId.equals(oldAtomInfoId)) {

                        Long atomOldSalePrice = oldAtomInfo.getSalePrice() == null ? -1 : oldAtomInfo.getSalePrice();
                        Long atomSalePrice = atomInfo.getSalePrice() == null ? -1 : atomInfo.getSalePrice();
                        if (atomOldSalePrice != null && atomSalePrice != null
                                && !atomOldSalePrice.equals(atomSalePrice)) {
                            logList.add("原子销售价格(单位厘)由 " + atomOldSalePrice + "调整为" + atomSalePrice);
                        }

                        Long oldHardwarePrice = oldAtomInfo.getHardwarePrice() == null ? -1 : oldAtomInfo.getHardwarePrice();
                        Long hardwarePrice = atomInfo.getHardwarePrice() == null ? -1 : atomInfo.getHardwarePrice();
                        if (oldHardwarePrice != null && hardwarePrice != null
                                && !oldHardwarePrice.equals(hardwarePrice)) {
                            logList.add("硬件原子商品销售价格(单位厘)由 " + oldHardwarePrice + "调整为" + hardwarePrice);
                        }

                        Long oldSettlePrice = oldAtomInfo.getSettlePrice() == null ? -1 : oldAtomInfo.getSettlePrice();
                        Long settlePrice = atomInfo.getSettlePrice() == null ? -1 : atomInfo.getSettlePrice();
                        if (oldSettlePrice != null && settlePrice != null
                                && !oldSettlePrice.equals(settlePrice)) {
                            logList.add("结算单价或硬件结算单价或省-专结算单价价格(单位厘)由 " + oldSettlePrice + "调整为" + settlePrice);
                        }

                        Long oldSoftPrice = oldAtomInfo.getSoftPrice() == null ? -1 : oldAtomInfo.getSoftPrice();
                        Long softPrice = atomInfo.getSoftPrice() == null ? -1 : atomInfo.getSoftPrice();
                        if (oldSoftPrice != null && softPrice != null
                                && !oldSoftPrice.equals(softPrice)) {
                            logList.add("软件功能费销售单价价格(单位厘)由 " + oldSoftPrice + "调整为" + softPrice);
                        }

                        Long oldSoftSettlePrice = oldAtomInfo.getSoftSettlePrice() == null ? -1 : oldAtomInfo.getSoftSettlePrice();
                        Long softSettlePrice = atomInfo.getSoftSettlePrice() == null ? -1 : atomInfo.getSoftSettlePrice();
                        if (oldSoftSettlePrice != null && softSettlePrice != null
                                && !oldSoftSettlePrice.equals(softSettlePrice)) {
                            logList.add("软件结算单价价格(单位厘)由 " + oldSoftSettlePrice + "调整为" + softSettlePrice);
                        }

                        Long oldZhuanheSettlePrice = oldAtomInfo.getZhuanheSettlePrice() == null ? -1 : oldAtomInfo.getZhuanheSettlePrice();
                        Long zhuanheSettlePrice = atomInfo.getZhuanheSettlePrice() == null ? -1 : atomInfo.getZhuanheSettlePrice();
                        if (oldZhuanheSettlePrice != null && zhuanheSettlePrice != null
                                && !oldZhuanheSettlePrice.equals(zhuanheSettlePrice)) {
                            logList.add("（专-合）结算单价价格(单位厘)由 " + oldZhuanheSettlePrice + "调整为" + zhuanheSettlePrice);
                        }

                        Long oldSoftTotalPrice = oldAtomInfo.getSoftTotalPrice() == null ? -1 : oldAtomInfo.getSoftTotalPrice();
                        Long softTotalPrice = atomInfo.getSoftTotalPrice() == null ? -1 : atomInfo.getSoftTotalPrice();
                        if (oldSoftTotalPrice != null && softTotalPrice != null
                                && !oldSoftTotalPrice.equals(softTotalPrice)) {
                            logList.add("软件功能费商品销售由价格 " + oldSoftTotalPrice + "调整为" + softTotalPrice);
                        }

                        Long atomOldSaleMinPrice = oldAtomInfo.getSaleMinPrice() == null ? -1 : oldAtomInfo.getSaleMinPrice();
                        Long atomSaleMinPrice = atomInfo.getSaleMinPrice() == null ? -1 : atomInfo.getSaleMinPrice();
                        if (atomOldSaleMinPrice != null && atomSaleMinPrice != null
                                && !atomOldSaleMinPrice.equals(atomSaleMinPrice)) {
                            logList.add("销售最低价由价格 " + atomOldSaleMinPrice + "调整为" + atomSaleMinPrice);
                        }

                        Long oldAtomSaleMaxPrice = oldAtomInfo.getSaleMaxPrice() == null ? -1 : oldAtomInfo.getSaleMaxPrice();
                        Long atomSaleMaxPrice = atomInfo.getSaleMaxPrice() == null ? -1 : atomInfo.getSaleMaxPrice();
                        if (oldAtomSaleMaxPrice != null && atomSaleMaxPrice != null
                                && !oldAtomSaleMaxPrice.equals(atomSaleMaxPrice)) {
                            logList.add("销售最高价由价格 " + oldAtomSaleMaxPrice + "调整为" + atomSaleMaxPrice);
                        }
                    }
                });

            });
        }

        if (CollectionUtils.isNotEmpty(logList)) {
            return String.join(System.getProperty("line.separator"), logList);
        }
        return null;
    }

    @Override
    public BaseAnswer<Boolean> hasRunningFlowInstance(String userId) {
        Integer count = productFlowInstanceMapperExt.getUnhandldeTask(userId);
        return count == 0 ? BaseAnswer.success(false) : BaseAnswer.success(true);
    }

    @Override
    public BaseAnswer addDetaiAttachmentlLog(FlowInstanceAddDetaiAttachmentlLogParam param, LoginIfo4Redis loginIfo4Redis) {
        String flowInstanceId = param.getFlowInstanceId();
        List<String> attachmentNameList = param.getAttachmentNameList();
        ProductFlowInstance productFlowInstance = productFlowInstanceMapper.selectByPrimaryKey(flowInstanceId);
        if (productFlowInstance == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程单不存在");
        }
        ProductFlow productFlow = productFlowMapper.selectByPrimaryKey(productFlowInstance.getFlowId());
        if (productFlow == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程不存在");
        }
        Integer flowType = productFlow.getFlowType();
        ProductFlowInstanceSpu productFlowInstanceSpu = getFlowInstanceSpu(flowInstanceId);
        List<ProductFlowInstanceSku> flowInstanceSkuList = getFlowInstanceSkuList(flowInstanceId);
        List<String> skuNameList = flowInstanceSkuList.stream().map(f -> {
            return f.getSkuName();
        }).collect(Collectors.toList());

        //记录日志
        Integer subModule = getLogSubModuleFromFlowType(flowType);
        String content = getLogContent("【查看】", productFlowInstance.getFlowInstanceNumber(), productFlowInstanceSpu.getSpuName(), String.join(",", skuNameList), null, String.join(",", attachmentNameList), null, null, null, null, null);
        if (StringUtils.isNotEmpty(content)) {
            logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, subModule, content, LogResultEnum.LOG_SUCESS.code, null);
        }
        return BaseAnswer.success(null);
    }

    @Override
    public BaseAnswer<PageData<ShelfSpuSimpleListVO>> getShelfSpuSimpleList(LoginIfo4Redis loginIfo4Redis, ShelfSpuSimpleListParam param) {
        String spuNameCode = param.getSpuNameCode();
        Integer pageNum = param.getPageNum();
        Integer pageSize = param.getPageSize();
        PageData<ShelfSpuSimpleListVO> pageData = new PageData<>();
        pageData.setPage(pageNum);
        PageHelper.startPage(pageNum, pageSize);
        //分页
        List<ShelfSpuSimpleListDO> data = productFlowInstanceMapperExt.getShelfSpuSimpleList(spuNameCode);
        if (data.isEmpty()) {
            return BaseAnswer.success(pageData);
        }
        PageInfo<ShelfSpuSimpleListDO> pageInfo = new PageInfo<>(data);
        List<ShelfSpuSimpleListVO> collect = data.stream().map(d -> {
            ShelfSpuSimpleListVO vo = new ShelfSpuSimpleListVO();
            BeanUtils.copyProperties(d, vo);
            return vo;
        }).collect(Collectors.toList());
        pageData.setData(collect);
        pageData.setCount(pageInfo.getTotal());
        return BaseAnswer.success(pageData);
    }

    @Override
    public BaseAnswer deleteAttachment(String id) {
        ProductFlowInstanceAttachment productFlowInstanceAttachment = productFlowInstanceAttachmentMapper.selectByPrimaryKey(id);
        if (productFlowInstanceAttachment == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "附件不存在");
        }
        //文件系统删除
        try {
            storageService.delete(productFlowInstanceAttachment.getFileKey());
        } catch (Exception e) {
            log.error("删除文件出错", e);
        }
        productFlowInstanceAttachmentMapper.deleteByPrimaryKey(id);
        redisTemplate.delete(Constant.REDIS_KEY_PRODUCT_FLOW_INSTANCE_ATTACHMENT + productFlowInstanceAttachment.getFlowInstanceId());
        return BaseAnswer.success(null);
    }

    @Override
    public BaseAnswer<List<ProductFlowAuditStepListVO>> getAuditStepList(String flowInstanceId) {
        ProductFlowInstance productFlowInstance = productFlowInstanceMapper.selectByPrimaryKey(flowInstanceId);
        if (productFlowInstance == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程单不存在");
        }
        ProductFlowStepExample example = new ProductFlowStepExample().createCriteria().andFlowIdEqualTo(productFlowInstance.getFlowId()).example();
        example.orderBy("step_index ASC");
        List<ProductFlowStep> stepList = productFlowStepMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(stepList)) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程环节不存在");
        }
        List<ProductFlowAuditStepListVO> voList = stepList.stream().map(s -> {
            ProductFlowAuditStepListVO vo = new ProductFlowAuditStepListVO();
            BeanUtils.copyProperties(s, vo);
            if (vo.getLimitId() != null) {
                ProductFlowLimitEnum productFlowLimitEnum = ProductFlowLimitEnum.fromCode(vo.getLimitId());
                if (productFlowLimitEnum != null) {
                    vo.setLimitName(productFlowLimitEnum.getName());
                } else {
                    vo.setLimitId(null);
                }
            }
            return vo;
        }).collect(Collectors.toList());
        //增加“结束”的步骤，Index是最大index+1
        Integer maxStepIndex = stepList.get(stepList.size() - 1).getStepIndex();
        int endIndex = maxStepIndex + 1;
        ProductFlowAuditStepListVO endVo = new ProductFlowAuditStepListVO();
        endVo.setId("-1");
        endVo.setStepIndex(endIndex);
        endVo.setStepName("结束");
        voList.add(endVo);

        //查询拼装环节处理信息，包括转办知悉限制
        List<ProductFlowInstanceTask> taskList = productFlowInstanceTaskMapperExt.getTaskListByStepIndexDesc(flowInstanceId);
        if (CollectionUtils.isNotEmpty(taskList)) {
            //如果包含驳回，则舍弃最后一次驳回之前的那些步骤
            List<ProductFlowInstanceTask> rejectList = taskList.stream().filter(t -> {
                return t.getStepName().contains("(驳回)");
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(rejectList)) {
                ProductFlowInstanceTask rejectStep = rejectList.get(0);
                Date createTime = rejectStep.getCreateTime();
                taskList = taskList.stream().filter(t -> {
                    return t.getCreateTime().getTime() >= createTime.getTime();
                }).collect(Collectors.toList());
            }

            ProductFlowInstanceTask currentTask = taskList.get(0);
            if (currentTask.getStepName().contains("驳回")) {
                //驳回后，只高亮显示第一个步骤
                voList.get(0).setHighLight(true);
            } else {
                for (int i = 0; i < voList.size(); i++) {
                    ProductFlowAuditStepListVO vo = voList.get(i);
                    String id = vo.getId();
                    for (ProductFlowInstanceTask task : taskList) {
                        if (id.equals(task.getStepId())) {
                            //可能是环节本身，也可能是环节的转办知悉限制
                            if (ProductFlowHandleStatusEnum.CANCEL.code.intValue() == task.getHandleStatus().intValue()) {
                                vo.setHighLight(true);
                            }
                            if (ProductFlowHandleStatusEnum.PASS.code.intValue() == task.getHandleStatus().intValue()) {
                                vo.setHighLight(true);
                            }
                            if (ProductFlowHandleStatusEnum.REDIRECT.code.intValue() == task.getHandleStatus().intValue()) {
                                vo.setRedirected(true);
                                vo.setHighLight(true);
                            }
                            if (ProductFlowHandleStatusEnum.KNOWN.code.intValue() == task.getHandleStatus().intValue()) {
                                if (task.getStepName().equals(vo.getStepName() + "(知悉)")) {
                                    //限制条件：前一个步骤勾选了供应额度不足 导致的知悉
                                    ProductFlowAuditStepListVO limitStepVo = voList.get(i - 1);
                                    limitStepVo.setLimited(true);
                                    limitStepVo.setLimitId(ProductFlowLimitEnum.OVER_INVENTORY.code);
                                    limitStepVo.setLimitName(ProductFlowLimitEnum.OVER_INVENTORY.name);

                                    vo.setStepName(task.getStepName());
                                    vo.setHighLight(true);
                                } else {
                                    //本步骤勾选了知悉，需要高亮
                                    vo.setKnown(true);
                                    vo.setHighLight(true);
                                }
                            }

                            if ("-1".equals(task.getStepId())) {
                                //表示结束,也需要高亮步骤
                                vo.setHighLight(true);
                            }
                        }
                    }
                    //当前步骤也需要高亮
                    if (id.equals(currentTask.getStepId())) {
                        vo.setHighLight(true);
                    }
                }

            }
        }
        return BaseAnswer.success(voList);
    }

    /**
     * 发送产品流程相关短信
     *
     * @param productFlowInstance
     * @param productFlow
     * @param skuNameList
     */
    private void sendFlowMsg(String phone, ProductFlowInstance productFlowInstance, ProductFlow productFlow, String skuNameList, String smsTempId) {
        ProductFlowTypeEnum flowType = ProductFlowTypeEnum.fromCode(productFlow.getFlowType());
        if (flowType == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程类型不存在");
        }
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("flowType", flowType.name);
        paramMap.put("flowInstanceNumber", productFlowInstance.getFlowInstanceNumber());
        paramMap.put("skuName", skuNameList);
        baseSmsService.sendMsg(phone, smsTempId, paramMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer spuShelf(Integer productStandard, Integer productType, MultipartFile file, LoginIfo4Redis loginIfo4Redis) throws IOException {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        try {
            if (!ProductStandardEnum.contains(productStandard)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "产品标准错误");
            }
            if (!ProductTypeEnum.contains(productType)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "产品类别错误");
            }
            Integer operateType = getOperateType(productType);
            //判断走哪个上架流程
            ProductFlowExample example = new ProductFlowExample().createCriteria().andFlowTypeEqualTo(ProductFlowTypeEnum.PRODUCT_SHELF.code).andOperateTypeEqualTo(operateType).andStatusEqualTo(0).example();
            List<ProductFlow> flowList = productFlowMapper.selectByExample(example);
            if (flowList.isEmpty()) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "找不到对应流程定义");
            }
            ProductFlow productFlow = flowList.get(0);
            ProductFlowStepExample flowStepExample = new ProductFlowStepExample().createCriteria().andFlowIdEqualTo(productFlow.getId()).andStepIndexEqualTo(1).example();
            List<ProductFlowStep> productFlowSteps = productFlowStepMapper.selectByExample(flowStepExample);
            if (productFlowSteps.isEmpty()) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "请找管理员配置流程");
            }
            ProductFlowStep flowStep = productFlowSteps.get(0);
            InputStream inputStream = null;
            try {
                inputStream = file.getInputStream();
            } catch (IOException e) {
                log.error("获取输入流出错", e);
                throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "解析文件出错");
            }
            //按照不同的模板读取文件并上架
            Integer count = productShelfFromFile(productStandard, productType, loginIfo4Redis, productFlow, flowStep, inputStream, "SPU", null, null, file);
            response.addHeader("stateCode", BaseErrorConstant.SUCCESS.getStateCode());
            response.addHeader("message", "ok");
            response.addHeader("data", count + "");
            return BaseAnswer.success(null);
        } catch (Exception e) {
            //便于前端拿到响应，将异常信息放入header
            try {
                if (e instanceof BusinessException) {
                    BusinessException businessException = (BusinessException) e;
                    response.addHeader("stateCode", businessException.getStatus().getStateCode());
                    response.addHeader("message", URLEncoder.encode(businessException.getStatus().getMessage(), "UTF-8"));
                } else {
                    log.error("{}上架发生异常,", e);
                    response.addHeader("stateCode", BaseErrorConstant.INTERNAL_ERROR.getStateCode());
                    response.addHeader("message", URLEncoder.encode(BaseErrorConstant.INTERNAL_ERROR.getMessage(), "UTF-8"));
                }
            } catch (UnsupportedEncodingException ex) {
                ex.printStackTrace();
            }
            throw e;
        }
    }

    /**
     * 校验金额最小只能是分,返回厘
     */
    private long checkPriceAndGet(String price) {
        long l = (long) (Double.valueOf(price) * 1000L);
        if (l % 10 != 0) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "金额小数点后最多2位");
        }
        return l;
    }

    /**
     * SPU上架或SKU上架
     *
     * @param productStandard
     * @param productType
     * @param loginIfo4Redis
     * @param productFlow
     * @param flowStep
     * @param inputStream
     * @param type            SPU或SKU
     * @param spuCode         当SKU上架时，传递其对应的SPU编码
     * @param shelfSpu        已经上架的spu，最新数据
     * @param file
     * @return
     */
    private Integer productShelfFromFile(Integer productStandard, Integer productType, LoginIfo4Redis loginIfo4Redis, ProductFlow productFlow, ProductFlowStep flowStep, InputStream inputStream, String type, String spuCode, ProductFlowInstanceSpu shelfSpu, MultipartFile file) throws IOException {
        //上架数量
        Integer count = 0;
        Date now = new Date();
        try {
            if (productStandard.intValue() == ProductStandardEnum.PLAN.code.intValue()) {
                //校验excel表头
                if (!checkExcelHeader(file, ProductFlowShelfExcelHeaderConstant.planProductShelfExcelHeader, 1)) {
                    //记录日志
                    logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, null);
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "文件模板错误");
                }
                ;
                //方案类的各种类型产品通过唯一模板上传
                List<PlanProductSheflParam> list = EasyExcel.read(inputStream, PlanProductSheflParam.class, null)
                        .sheet(0).headRowNumber(2).doReadSync();

                if (CollectionUtils.isEmpty(list) || list.size() == 1) {
                    //第一行是注释，不是有效数据
                    logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, "无有效数据");

                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "无有效数据");
                }
                List<PlanProductSheflParam> planProductSheflParams = list.subList(1, list.size());
                //因为模板合并了单元格，所以第一行有全量SPU数据，后面几行只有SKU和原子数据
                List<String> spuNameList = new ArrayList<>();
                planProductSheflParams.forEach(p -> {
                    if (StringUtils.isNotEmpty(p.getSpuName())) {
                        spuNameList.add(p.getSpuName());
                    }
                });
                if ("SPU".equals(type) && (spuNameList.size() > 1 || spuNameList.size() == 0)) {

                    logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, "仅支持一个SPU多个SKU导入,不支持多SPU");

                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "仅支持一个SPU多个SKU导入,不支持多SPU");
                }
                //整理数据,适配原来的数据格式，一个sku弄成一条List数据，多个合并的sku就是多条List数据
                List<List<PlanProductSheflParam>> listData = dealData(planProductSheflParams,type);

                //遍历处理好的数据，按照之前逻辑循环处理即可
                for (List<PlanProductSheflParam> sheflParams : listData) {
                    //每个list的第一行包括spu,sku信息和原子信息，后面的行只有原子信息
                    PlanProductSheflParam planProductShelfParam = sheflParams.get(0);
                    if (BaseUtils.anyFiledHasValue(planProductShelfParam)) {
                        count++;
                        //新建流程实例，包括流程实例的可编辑，可审核，可取消，可配置属性
                        ProductFlowInstance productFlowInstance = addProductFlowInstance(loginIfo4Redis, now, productFlow, flowStep);
                        productFlowInstance.setShelfType("SPU".equals(type) ? 1 : 2);
                        productFlowInstanceMapper.updateByPrimaryKey(productFlowInstance);

                        //新建流程任务
                        addProductFlowTask(loginIfo4Redis, now, productFlow, flowStep, productFlowInstance);
                        //新建流程spu,sku,原子信息等

                        if ("SPU".equals(type)) {
                            //新SPU上架
                            ProductFlowInstanceSpu flowInstanceSpu = new ProductFlowInstanceSpu();
                            flowInstanceSpu.setId(BaseServiceUtils.getId());
                            flowInstanceSpu.setFlowId(productFlow.getId());
                            flowInstanceSpu.setFlowInstanceId(productFlowInstance.getId());
                            flowInstanceSpu.setProductStandard(productStandard);
                            flowInstanceSpu.setProductType(productType);
                            BeanUtils.copyProperties(planProductShelfParam, flowInstanceSpu);
                            setSpuCategoryAndNavigation(flowInstanceSpu, planProductShelfParam.getShelfCatagoryName(), planProductShelfParam.getFirstDirectoryName(), planProductShelfParam.getSecondDirectoryName(),planProductShelfParam.getThirdDirectoryName());
                            flowInstanceSpu.setCreateTime(now);
                            flowInstanceSpu.setUpdateTime(now);
                            productFlowInstanceSpuMapper.insert(flowInstanceSpu);
                        }
                        if ("SKU".equals(type)) {
                            //新SKU上架，复制保存已上架的spu
                            copyShelfSpuInfo(shelfSpu, now, productFlowInstance);
                        }

                        ProductFlowInstanceSku flowInstanceSku = new ProductFlowInstanceSku();
                        flowInstanceSku.setId(BaseServiceUtils.getId());
                        flowInstanceSku.setFlowId(productFlow.getId());
                        flowInstanceSku.setFlowInstanceId(productFlowInstance.getId());
                        BeanUtils.copyProperties(planProductShelfParam, flowInstanceSku);
                        if ("SKU".equals(type)) {
                            flowInstanceSku.setSpuCode(spuCode);
                        }
                        flowInstanceSku.setShelfStatus(ProductShelfStatusEnum.SHELF_IN_PROGRESS.code);
                        flowInstanceSku.setCreateTime(now);
                        flowInstanceSku.setUpdateTime(now);
                        productFlowInstanceSkuMapper.insert(flowInstanceSku);

                        ProductFlowInstanceAtom flowInstanceAtom = new ProductFlowInstanceAtom();
                        flowInstanceAtom.setId(BaseServiceUtils.getId());
                        flowInstanceAtom.setFlowId(productFlow.getId());
                        flowInstanceAtom.setFlowInstanceId(productFlowInstance.getId());
                        BeanUtils.copyProperties(planProductShelfParam, flowInstanceAtom);
                        if(StringUtils.isNotEmpty(planProductShelfParam.getInventoryStr())){
                            try {
                                flowInstanceAtom.setInventory(Integer.parseInt(planProductShelfParam.getInventoryStr()));
                            } catch (NumberFormatException e) {
                                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "库存数 请填写整数，不应许小数");
                            }
                        }
                        if ("SKU".equals(type)) {
                            flowInstanceAtom.setSpuCode(spuCode);
                        }
                        if (StringUtils.isNotEmpty(planProductShelfParam.getMinPurchaseNumStr())) {
                            try {
                                flowInstanceAtom.setMinPurchaseNum(Integer.parseInt(planProductShelfParam.getMinPurchaseNumStr()));
                            } catch (NumberFormatException e) {
                                String content = getLogContent("【新" + type + "上架】", productFlowInstance.getFlowInstanceNumber(), planProductShelfParam.getSpuName(), planProductShelfParam.getSkuName(), null, null, null, null, null, null, null);

                                logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, content, LogResultEnum.LOG_FAIL.code, "订购数量最小值 请填写整数，不应许小数");

                                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订购数量最小值 请填写整数，不应许小数");
                            }
                        }
                        if (StringUtils.isNotEmpty(planProductShelfParam.getSettlePriceYuan())) {
                            flowInstanceAtom.setSettlePrice(checkPriceAndGet(planProductShelfParam.getSettlePriceYuan()));
                        }
                        if (StringUtils.isNotEmpty(planProductShelfParam.getZhuanheSettlePriceYuan())) {
                            flowInstanceAtom.setZhuanheSettlePrice(checkPriceAndGet(planProductShelfParam.getZhuanheSettlePriceYuan()));
                        }
                        flowInstanceAtom.setCreateTime(now);
                        flowInstanceAtom.setUpdateTime(now);
                        productFlowInstanceAtomMapper.insert(flowInstanceAtom);

                        //截取后几行的原子信息并保存
                        List<PlanProductSheflParam> planProductAtomInfoList = sheflParams.subList(1, sheflParams.size());
                        for (PlanProductSheflParam productSheflParam : planProductAtomInfoList) {
                            if (!BaseUtils.anyFiledHasValue(productSheflParam)) {
                                continue;
                            }
                            flowInstanceAtom = new ProductFlowInstanceAtom();
                            flowInstanceAtom.setId(BaseServiceUtils.getId());
                            flowInstanceAtom.setFlowId(productFlow.getId());
                            flowInstanceAtom.setFlowInstanceId(productFlowInstance.getId());
                            BeanUtils.copyProperties(productSheflParam, flowInstanceAtom);
                            if(StringUtils.isNotEmpty(productSheflParam.getInventoryStr())){
                                try {
                                    flowInstanceAtom.setInventory(Integer.parseInt(productSheflParam.getInventoryStr()));
                                } catch (NumberFormatException e) {
                                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "库存数 请填写整数，不应许小数");
                                }
                            }
                            if (StringUtils.isNotEmpty(productSheflParam.getMinPurchaseNumStr())) {
                                try {
                                    flowInstanceAtom.setMinPurchaseNum(Integer.parseInt(productSheflParam.getMinPurchaseNumStr()));
                                } catch (NumberFormatException e) {
                                    String content = getLogContent("【新" + type + "上架】", productFlowInstance.getFlowInstanceNumber(), planProductShelfParam.getSpuName(), planProductShelfParam.getSkuName(), null, null, null, null, null, null, null);

                                    logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, content, LogResultEnum.LOG_FAIL.code, "订购数量最小值 请填写整数，不应许小数");

                                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订购数量最小值 请填写整数，不应许小数");
                                }
                            }
                            if ("SKU".equals(type)) {
                                flowInstanceAtom.setSpuCode(spuCode);
                            }
                            if (StringUtils.isNotEmpty(productSheflParam.getSettlePriceYuan())) {
                                flowInstanceAtom.setSettlePrice((long) (Double.valueOf(productSheflParam.getSettlePriceYuan()) * 1000L));
                            }
                            if (StringUtils.isNotEmpty(productSheflParam.getZhuanheSettlePriceYuan())) {
                                flowInstanceAtom.setZhuanheSettlePrice((long) (Double.valueOf(productSheflParam.getZhuanheSettlePriceYuan()) * 1000L));
                            }
                            flowInstanceAtom.setCreateTime(now);
                            flowInstanceAtom.setUpdateTime(now);
                            productFlowInstanceAtomMapper.insert(flowInstanceAtom);
                        }

                        //记录日志
                        String content = getLogContent("【新" + type + "上架】", productFlowInstance.getFlowInstanceNumber(), planProductShelfParam.getSpuName(), planProductShelfParam.getSkuName(), null, null, null, null, null, null, null);
                        if (StringUtils.isNotEmpty(content)) {
                            logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, content, LogResultEnum.LOG_SUCESS.code, null);
                        }
                    }
                }

            } else if (productStandard.intValue() == ProductStandardEnum.STANDARD.code.intValue() &&
                    (productType.intValue() == ProductTypeEnum.PROVINCE.code.intValue() || productType.intValue() == ProductTypeEnum.PROVINCE_RANGE.code.intValue())
            ) {
                //省框/省内标准类 模板
                if (!checkExcelHeader(file, ProductFlowShelfExcelHeaderConstant.standardProductSheflProvinceExcelHeader, 1)) {
                    //日志记录

                    logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, "文件模板错误");

                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "文件模板错误");
                }
                List<StandardProductSheflProvinceParam> list = EasyExcel.read(inputStream, StandardProductSheflProvinceParam.class, null)
                        .sheet(0).headRowNumber(2).doReadSync();

                if (CollectionUtils.isEmpty(list) || list.size() == 1) {
                    //第一行是注释，也不是有效数据
                    logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, "无有效数据");
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "无有效数据");
                }
                List<StandardProductSheflProvinceParam> standardProductSheflProvinceParams = list.subList(1, list.size());

                List<String> spuNameList = new ArrayList<>();
                standardProductSheflProvinceParams.forEach(p -> {
                    if (StringUtils.isNotEmpty(p.getSpuName())) {
                        spuNameList.add(p.getSpuName());
                    }
                });
                if ("SPU".equals(type) && (spuNameList.size() > 1 || spuNameList.size() == 0)) {
                    logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, "仅支持一个SPU多个SKU导入,不支持多SPU");
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "仅支持一个SPU多个SKU导入,不支持多SPU");
                }

                StandardProductSheflProvinceParam paramWithSpu = null;
                for (StandardProductSheflProvinceParam standardProductSheflProvinceParam : standardProductSheflProvinceParams) {
                    if (!BaseUtils.anyFiledHasValue(standardProductSheflProvinceParam)) {
                        continue;
                    }
                    count++;
                    //新建流程实例，包括流程实例的可编辑，可审核，可取消，可配置属性
                    ProductFlowInstance productFlowInstance = addProductFlowInstance(loginIfo4Redis, now, productFlow, flowStep);
                    productFlowInstance.setShelfType("SPU".equals(type) ? 1 : 2);
                    productFlowInstanceMapper.updateByPrimaryKey(productFlowInstance);
                    //新建流程任务
                    addProductFlowTask(loginIfo4Redis, now, productFlow, flowStep, productFlowInstance);
                    //新建流程spu,sku,原子信息等
                    if ("SPU".equals(type)) {
                        if(count == 1){
                            //如果SPU合并了单元格，则只有第一行有SPU信息
                            paramWithSpu = standardProductSheflProvinceParam;
                        }else if(StringUtils.isEmpty(standardProductSheflProvinceParam.getSpuName())){
                            //后面的行没有spu信息，需要手动设置
                            standardProductSheflProvinceParam.setShelfCatagoryName(paramWithSpu.getShelfCatagoryName());
                            standardProductSheflProvinceParam.setFirstDirectoryName(paramWithSpu.getFirstDirectoryName());
                            standardProductSheflProvinceParam.setSecondDirectoryName(paramWithSpu.getSecondDirectoryName());
                            standardProductSheflProvinceParam.setThirdDirectoryName(paramWithSpu.getThirdDirectoryName());
                            standardProductSheflProvinceParam.setSpuName(paramWithSpu.getSpuName());
                            standardProductSheflProvinceParam.setManager(paramWithSpu.getManager());
                            standardProductSheflProvinceParam.setProductDesc(paramWithSpu.getProductDesc());
                            standardProductSheflProvinceParam.setApplicationArea(paramWithSpu.getApplicationArea());
                            standardProductSheflProvinceParam.setIsHiddenShelf(paramWithSpu.getIsHiddenShelf());
                            standardProductSheflProvinceParam.setSpuServiceProvider(paramWithSpu.getSpuServiceProvider());
                            standardProductSheflProvinceParam.setSaleTag(paramWithSpu.getSaleTag());
                            standardProductSheflProvinceParam.setSearchWord(paramWithSpu.getSearchWord());
                        }

                        ProductFlowInstanceSpu flowInstanceSpu = new ProductFlowInstanceSpu();
                        flowInstanceSpu.setId(BaseServiceUtils.getId());
                        flowInstanceSpu.setFlowId(productFlow.getId());
                        flowInstanceSpu.setFlowInstanceId(productFlowInstance.getId());
                        flowInstanceSpu.setProductStandard(productStandard);
                        flowInstanceSpu.setProductType(productType);
                        BeanUtils.copyProperties(standardProductSheflProvinceParam, flowInstanceSpu);
                        setSpuCategoryAndNavigation(flowInstanceSpu, standardProductSheflProvinceParam.getShelfCatagoryName(), standardProductSheflProvinceParam.getFirstDirectoryName(), standardProductSheflProvinceParam.getSecondDirectoryName(), standardProductSheflProvinceParam.getThirdDirectoryName());
                        flowInstanceSpu.setCreateTime(now);
                        flowInstanceSpu.setUpdateTime(now);
                        productFlowInstanceSpuMapper.insert(flowInstanceSpu);
                    }
                    if ("SKU".equals(type)) {
                        //新SKU上架，复制保存已上架的spu
                        copyShelfSpuInfo(shelfSpu, now, productFlowInstance);
                    }

                    ProductFlowInstanceSku flowInstanceSku = new ProductFlowInstanceSku();
                    flowInstanceSku.setId(BaseServiceUtils.getId());
                    flowInstanceSku.setFlowId(productFlow.getId());
                    flowInstanceSku.setFlowInstanceId(productFlowInstance.getId());
                    BeanUtils.copyProperties(standardProductSheflProvinceParam, flowInstanceSku);
                    flowInstanceSku.setShelfStatus(ProductShelfStatusEnum.SHELF_IN_PROGRESS.code);
                    if ("SKU".equals(type)) {
                        flowInstanceSku.setSpuCode(spuCode);
                    }
                    flowInstanceSku.setCreateTime(now);
                    flowInstanceSku.setUpdateTime(now);
                    productFlowInstanceSkuMapper.insert(flowInstanceSku);

                    ProductFlowInstanceAtom flowInstanceAtom = new ProductFlowInstanceAtom();
                    flowInstanceAtom.setId(BaseServiceUtils.getId());
                    flowInstanceAtom.setFlowId(productFlow.getId());
                    flowInstanceAtom.setFlowInstanceId(productFlowInstance.getId());
                    if ("SKU".equals(type)) {
                        flowInstanceAtom.setSpuCode(spuCode);
                    }
                    BeanUtils.copyProperties(standardProductSheflProvinceParam, flowInstanceAtom);
                    if(StringUtils.isNotEmpty(standardProductSheflProvinceParam.getInventoryStr())){
                        try {
                            flowInstanceAtom.setInventory(Integer.parseInt(standardProductSheflProvinceParam.getInventoryStr()));
                        } catch (NumberFormatException e) {
                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "库存数 请填写整数，不应许小数");
                        }
                    }
                    if (StringUtils.isNotEmpty(standardProductSheflProvinceParam.getMinPurchaseNumStr())) {
                        try {
                            flowInstanceAtom.setMinPurchaseNum(Integer.parseInt(standardProductSheflProvinceParam.getMinPurchaseNumStr()));
                        } catch (NumberFormatException e) {

                            logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, "订购数量最小值 请填写整数，不应许小数");

                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订购数量最小值 请填写整数，不应许小数");

                        }
                    }
                    if (StringUtils.isNotEmpty(standardProductSheflProvinceParam.getSettlePriceYuan())) {
                        flowInstanceAtom.setSettlePrice(checkPriceAndGet(standardProductSheflProvinceParam.getSettlePriceYuan()));
                    }
                    if (StringUtils.isNotEmpty(standardProductSheflProvinceParam.getSettlePriceCheckYuan())) {
                        flowInstanceAtom.setSettlePriceCheck(checkPriceAndGet(standardProductSheflProvinceParam.getSettlePriceCheckYuan()));
                    }
                    flowInstanceAtom.setCreateTime(now);
                    flowInstanceAtom.setUpdateTime(now);
                    productFlowInstanceAtomMapper.insert(flowInstanceAtom);
                    //日志记录
                    String content = getLogContent("【新" + type + "上架】", productFlowInstance.getFlowInstanceNumber(), standardProductSheflProvinceParam.getSpuName(), standardProductSheflProvinceParam.getSkuName(), null, null, null, null, null, null, null);
                    if (StringUtils.isNotEmpty(content)) {
                        logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, content, LogResultEnum.LOG_SUCESS.code, null);
                    }
                }
            } else if (productStandard.intValue() == ProductStandardEnum.STANDARD.code.intValue() &&
                    productType.intValue() == ProductTypeEnum.DICT.code.intValue()
            ) {
                //DICT标准类 模板
                if (!checkExcelHeader(file, ProductFlowShelfExcelHeaderConstant.standardProductSheflDICTExcelHeader, 1)) {

                    logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, "文件模板错误");

                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "文件模板错误");
                }
                ;
                List<StandardProductSheflDICTParam> list = EasyExcel.read(inputStream, StandardProductSheflDICTParam.class, null)
                        .sheet(0).headRowNumber(2).doReadSync();

                if (CollectionUtils.isEmpty(list) || list.size() == 1) {
                    //第一行是注释，也不是有效数据
                    logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, "无有效数据");
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "无有效数据");
                }
                List<StandardProductSheflDICTParam> standardProductSheflDictParams = list.subList(1, list.size());

                List<String> spuNameList = new ArrayList<>();
                standardProductSheflDictParams.forEach(p -> {
                    if (StringUtils.isNotEmpty(p.getSpuName())) {
                        spuNameList.add(p.getSpuName());
                    }
                });
                if ("SPU".equals(type) && (spuNameList.size() > 1 || spuNameList.size() == 0)) {
                    logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, "仅支持一个SPU多个SKU导入,不支持多SPU");
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "仅支持一个SPU多个SKU导入,不支持多SPU");
                }
                StandardProductSheflDICTParam paramWithSpu = null;
                for (StandardProductSheflDICTParam standardProductSheflDictParam : standardProductSheflDictParams) {
                    if (!BaseUtils.anyFiledHasValue(standardProductSheflDictParam)) {
                        continue;
                    }
                    count++;
                    //新建流程实例，包括流程实例的可编辑，可审核，可取消，可配置属性
                    ProductFlowInstance productFlowInstance = addProductFlowInstance(loginIfo4Redis, now, productFlow, flowStep);
                    productFlowInstance.setShelfType("SPU".equals(type) ? 1 : 2);
                    productFlowInstanceMapper.updateByPrimaryKey(productFlowInstance);
                    //新建流程任务
                    addProductFlowTask(loginIfo4Redis, now, productFlow, flowStep, productFlowInstance);
                    //新建流程spu,sku,原子信息等
                    if ("SPU".equals(type)) {
                        if(count == 1){
                            //如果SPU合并了单元格，则只有第一行有SPU信息
                            paramWithSpu = standardProductSheflDictParam;
                        }else if(StringUtils.isEmpty(standardProductSheflDictParam.getSpuName())){
                            //后面的行没有spu信息，需要手动设置
                            standardProductSheflDictParam.setShelfCatagoryName(paramWithSpu.getShelfCatagoryName());
                            standardProductSheflDictParam.setFirstDirectoryName(paramWithSpu.getFirstDirectoryName());
                            standardProductSheflDictParam.setSecondDirectoryName(paramWithSpu.getSecondDirectoryName());
                            standardProductSheflDictParam.setThirdDirectoryName(paramWithSpu.getThirdDirectoryName());
                            standardProductSheflDictParam.setSpuName(paramWithSpu.getSpuName());
                            standardProductSheflDictParam.setManager(paramWithSpu.getManager());
                            standardProductSheflDictParam.setProductDesc(paramWithSpu.getProductDesc());
                            standardProductSheflDictParam.setApplicationArea(paramWithSpu.getApplicationArea());
                            standardProductSheflDictParam.setIsHiddenShelf(paramWithSpu.getIsHiddenShelf());
                            standardProductSheflDictParam.setSpuServiceProvider(paramWithSpu.getSpuServiceProvider());
                            standardProductSheflDictParam.setSaleTag(paramWithSpu.getSaleTag());
                            standardProductSheflDictParam.setSearchWord(paramWithSpu.getSearchWord());
                        }

                        ProductFlowInstanceSpu flowInstanceSpu = new ProductFlowInstanceSpu();
                        flowInstanceSpu.setId(BaseServiceUtils.getId());
                        flowInstanceSpu.setFlowId(productFlow.getId());
                        flowInstanceSpu.setFlowInstanceId(productFlowInstance.getId());
                        flowInstanceSpu.setProductStandard(productStandard);
                        flowInstanceSpu.setProductType(productType);
                        BeanUtils.copyProperties(standardProductSheflDictParam, flowInstanceSpu);
                        setSpuCategoryAndNavigation(flowInstanceSpu, standardProductSheflDictParam.getShelfCatagoryName(), standardProductSheflDictParam.getFirstDirectoryName(), standardProductSheflDictParam.getSecondDirectoryName(), standardProductSheflDictParam.getThirdDirectoryName());
                        flowInstanceSpu.setCreateTime(now);
                        flowInstanceSpu.setUpdateTime(now);
                        productFlowInstanceSpuMapper.insert(flowInstanceSpu);
                    }
                    if ("SKU".equals(type)) {
                        //新SKU上架，复制保存已上架的spu
                        copyShelfSpuInfo(shelfSpu, now, productFlowInstance);
                    }

                    ProductFlowInstanceSku flowInstanceSku = new ProductFlowInstanceSku();
                    flowInstanceSku.setId(BaseServiceUtils.getId());
                    flowInstanceSku.setFlowId(productFlow.getId());
                    flowInstanceSku.setFlowInstanceId(productFlowInstance.getId());
                    BeanUtils.copyProperties(standardProductSheflDictParam, flowInstanceSku);
                    if ("SKU".equals(type)) {
                        flowInstanceSku.setSpuCode(spuCode);
                    }
                    flowInstanceSku.setShelfStatus(ProductShelfStatusEnum.SHELF_IN_PROGRESS.code);
                    flowInstanceSku.setCreateTime(now);
                    flowInstanceSku.setUpdateTime(now);
                    productFlowInstanceSkuMapper.insert(flowInstanceSku);

                    ProductFlowInstanceAtom flowInstanceAtom = new ProductFlowInstanceAtom();
                    flowInstanceAtom.setId(BaseServiceUtils.getId());
                    flowInstanceAtom.setFlowId(productFlow.getId());
                    flowInstanceAtom.setFlowInstanceId(productFlowInstance.getId());
                    if ("SKU".equals(type)) {
                        flowInstanceAtom.setSpuCode(spuCode);
                    }
                    BeanUtils.copyProperties(standardProductSheflDictParam, flowInstanceAtom);
                    if(StringUtils.isNotEmpty(standardProductSheflDictParam.getInventoryStr())){
                        try {
                            flowInstanceAtom.setInventory(Integer.parseInt(standardProductSheflDictParam.getInventoryStr()));
                        } catch (NumberFormatException e) {
                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "库存数 请填写整数，不应许小数");
                        }
                    }
                    if (StringUtils.isNotEmpty(standardProductSheflDictParam.getMinPurchaseNumStr())) {
                        try {
                            flowInstanceAtom.setMinPurchaseNum(Integer.parseInt(standardProductSheflDictParam.getMinPurchaseNumStr()));
                        } catch (NumberFormatException e) {

                            logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, "订购数量最小值 请填写整数，不应许小数");

                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订购数量最小值 请填写整数，不应许小数");
                        }
                    }
                    if (StringUtils.isNotEmpty(standardProductSheflDictParam.getSettlePriceYuan())) {
                        flowInstanceAtom.setSettlePrice(checkPriceAndGet(standardProductSheflDictParam.getSettlePriceYuan()));
                    }
                    if (StringUtils.isNotEmpty(standardProductSheflDictParam.getZhuanheSettlePriceYuan())) {
                        flowInstanceAtom.setZhuanheSettlePrice(checkPriceAndGet(standardProductSheflDictParam.getZhuanheSettlePriceYuan()));
                    }
                    flowInstanceAtom.setCreateTime(now);
                    flowInstanceAtom.setUpdateTime(now);
                    productFlowInstanceAtomMapper.insert(flowInstanceAtom);
                    //日志记录
                    String content = getLogContent("【新" + type + "上架】", productFlowInstance.getFlowInstanceNumber(), standardProductSheflDictParam.getSpuName(), standardProductSheflDictParam.getSkuName(), null, null, null, null, null, null, null);
                    if (StringUtils.isNotEmpty(content)) {
                        logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, content, LogResultEnum.LOG_SUCESS.code, null);
                    }
                }
            } else if (productStandard.intValue() == ProductStandardEnum.STANDARD.code.intValue() &&
                    productType.intValue() == ProductTypeEnum.CONTRACT.code.intValue()
            ) {
                //合同履约标准类 模板
                if (!checkExcelHeader(file, ProductFlowShelfExcelHeaderConstant.standardProductSheflContractExcelHeader, 1)) {

                    logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, "文件模板错误");

                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "文件模板错误");
                }
                ;
                List<StandardProductSheflContractParam> list = EasyExcel.read(inputStream, StandardProductSheflContractParam.class, null)
                        .sheet(0).headRowNumber(2).doReadSync();

                if (CollectionUtils.isEmpty(list) || list.size() == 1) {
                    //第一行是注释，也不是有效数据
                    logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, "无有效数据");
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "无有效数据");
                }
                List<StandardProductSheflContractParam> standardProductSheflContractParams = list.subList(1, list.size());

                List<String> spuNameList = new ArrayList<>();
                standardProductSheflContractParams.forEach(p -> {
                    if (StringUtils.isNotEmpty(p.getSpuName())) {
                        spuNameList.add(p.getSpuName());
                    }
                });
                if ("SPU".equals(type) && (spuNameList.size() > 1 || spuNameList.size() == 0)) {
                    logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, "仅支持一个SPU多个SKU导入,不支持多SPU");
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "仅支持一个SPU多个SKU导入,不支持多SPU");
                }
                StandardProductSheflContractParam paramWithSpu = null;
                for (StandardProductSheflContractParam standardProductSheflContractParam : standardProductSheflContractParams) {
                    if (!BaseUtils.anyFiledHasValue(standardProductSheflContractParam)) {
                        continue;
                    }
                    count++;
                    //新建流程实例，包括流程实例的可编辑，可审核，可取消，可配置属性
                    ProductFlowInstance productFlowInstance = addProductFlowInstance(loginIfo4Redis, now, productFlow, flowStep);
                    productFlowInstance.setShelfType("SPU".equals(type) ? 1 : 2);
                    productFlowInstanceMapper.updateByPrimaryKey(productFlowInstance);
                    //新建流程任务
                    addProductFlowTask(loginIfo4Redis, now, productFlow, flowStep, productFlowInstance);
                    //新建流程spu,sku,原子信息等
                    ProductFlowInstanceSpu flowInstanceSpu = new ProductFlowInstanceSpu();
                    if ("SPU".equals(type)) {
                        if(count == 1){
                            //如果SPU合并了单元格，则只有第一行有SPU信息
                            paramWithSpu = standardProductSheflContractParam;
                        }else if(StringUtils.isEmpty(standardProductSheflContractParam.getSpuName())){
                            //后面的行没有spu信息，需要手动设置
                            standardProductSheflContractParam.setShelfCatagoryName(paramWithSpu.getShelfCatagoryName());
                            standardProductSheflContractParam.setFirstDirectoryName(paramWithSpu.getFirstDirectoryName());
                            standardProductSheflContractParam.setSecondDirectoryName(paramWithSpu.getSecondDirectoryName());
                            standardProductSheflContractParam.setThirdDirectoryName(paramWithSpu.getThirdDirectoryName());
                            standardProductSheflContractParam.setSpuName(paramWithSpu.getSpuName());
                            standardProductSheflContractParam.setManager(paramWithSpu.getManager());
                            standardProductSheflContractParam.setProductDesc(paramWithSpu.getProductDesc());
                            standardProductSheflContractParam.setApplicationArea(paramWithSpu.getApplicationArea());
                            standardProductSheflContractParam.setIsHiddenShelf(paramWithSpu.getIsHiddenShelf());
                            standardProductSheflContractParam.setSpuServiceProvider(paramWithSpu.getSpuServiceProvider());
                            standardProductSheflContractParam.setSaleTag(paramWithSpu.getSaleTag());
                            standardProductSheflContractParam.setSearchWord(paramWithSpu.getSearchWord());
                            standardProductSheflContractParam.setAftermarketAdminInfo(paramWithSpu.getAftermarketAdminInfo());
                        }

                        flowInstanceSpu.setId(BaseServiceUtils.getId());
                        flowInstanceSpu.setFlowId(productFlow.getId());
                        flowInstanceSpu.setFlowInstanceId(productFlowInstance.getId());
                        flowInstanceSpu.setProductStandard(productStandard);
                        flowInstanceSpu.setProductType(productType);
                        BeanUtils.copyProperties(standardProductSheflContractParam, flowInstanceSpu);
                        setSpuCategoryAndNavigation(flowInstanceSpu, standardProductSheflContractParam.getShelfCatagoryName(), standardProductSheflContractParam.getFirstDirectoryName(), standardProductSheflContractParam.getSecondDirectoryName(), standardProductSheflContractParam.getThirdDirectoryName());
                        flowInstanceSpu.setCreateTime(now);
                        flowInstanceSpu.setUpdateTime(now);
                        productFlowInstanceSpuMapper.insert(flowInstanceSpu);
                    }
                    if ("SKU".equals(type)) {
                        //新SKU上架，复制保存已上架的spu
                        copyShelfSpuInfo(shelfSpu, now, productFlowInstance);
                    }

                    ProductFlowInstanceSku flowInstanceSku = new ProductFlowInstanceSku();
                    flowInstanceSku.setId(BaseServiceUtils.getId());
                    flowInstanceSku.setFlowId(productFlow.getId());
                    flowInstanceSku.setFlowInstanceId(productFlowInstance.getId());
                    if ("SKU".equals(type)) {
                        flowInstanceSku.setSpuCode(spuCode);
                    }
                    BeanUtils.copyProperties(standardProductSheflContractParam, flowInstanceSku);
                    flowInstanceSku.setShelfStatus(ProductShelfStatusEnum.SHELF_IN_PROGRESS.code);
                    if (StringUtils.isNotEmpty(standardProductSheflContractParam.getProvincePriceYuan())) {
                        flowInstanceSku.setProvincePrice(checkPriceAndGet(standardProductSheflContractParam.getProvincePriceYuan()));
                    }
                    flowInstanceSku.setCreateTime(now);
                    flowInstanceSku.setUpdateTime(now);
                    productFlowInstanceSkuMapper.insert(flowInstanceSku);

                    ProductFlowInstanceAtom flowInstanceAtom = new ProductFlowInstanceAtom();
                    flowInstanceAtom.setId(BaseServiceUtils.getId());
                    flowInstanceAtom.setFlowId(productFlow.getId());
                    flowInstanceAtom.setFlowInstanceId(productFlowInstance.getId());
                    if ("SKU".equals(type)) {
                        flowInstanceAtom.setSpuCode(spuCode);
                    }
                    BeanUtils.copyProperties(standardProductSheflContractParam, flowInstanceAtom);
                    if(StringUtils.isNotEmpty(standardProductSheflContractParam.getInventoryStr())){
                        try {
                            flowInstanceAtom.setInventory(Integer.parseInt(standardProductSheflContractParam.getInventoryStr()));
                        } catch (NumberFormatException e) {
                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "库存数 请填写整数，不应许小数");
                        }
                    }
                    if (StringUtils.isNotEmpty(standardProductSheflContractParam.getSettlePriceYuan())) {
                        flowInstanceAtom.setSettlePrice(checkPriceAndGet(standardProductSheflContractParam.getSettlePriceYuan()));
                    }
                    if (StringUtils.isNotEmpty(standardProductSheflContractParam.getAtomQuantityStr())) {
                        try {
                            flowInstanceAtom.setAtomQuantity(Integer.parseInt(standardProductSheflContractParam.getAtomQuantityStr()));
                        } catch (NumberFormatException e) {

                            logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, "硬件原子数量 请填写整数，不允许小数");

                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "硬件原子数量 请填写整数，不允许小数");
                        }
                    }
                    if (StringUtils.isNotEmpty(standardProductSheflContractParam.getSoftSettlePriceYuan())) {
                        flowInstanceAtom.setSoftSettlePrice(checkPriceAndGet(standardProductSheflContractParam.getSoftSettlePriceYuan()));
                    }
                    if (StringUtils.isNotEmpty(standardProductSheflContractParam.getSoftQuantityStr())) {
                        try {
                            flowInstanceAtom.setSoftQuantity(Integer.parseInt(standardProductSheflContractParam.getSoftQuantityStr()));
                        } catch (NumberFormatException e) {

                            logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, "软件原子数量 请填写整数，不允许小数");

                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "软件原子数量 请填写整数，不允许小数");
                        }
                    }
                    //计算销售单价和销售价格
                    if (StringUtils.isNotEmpty(standardProductSheflContractParam.getProvincePriceYuan()) && StringUtils.isNotEmpty(standardProductSheflContractParam.getSoftQuantityStr())) {
                        long provincePrice = (long) (Double.valueOf(standardProductSheflContractParam.getProvincePriceYuan()) * 1000L);
                        int softQuantity = Integer.parseInt(standardProductSheflContractParam.getSoftQuantityStr());
                        //销售单价算出来不足1分钱的部分算1分钱
                        BigDecimal decimal = new BigDecimal(Double.valueOf(standardProductSheflContractParam.getProvincePriceYuan()) / softQuantity + "");
                        long softPrice = (long) (decimal.setScale(2, RoundingMode.CEILING).doubleValue() * 1000L);
                        flowInstanceAtom.setSoftPrice(softPrice);
                        flowInstanceAtom.setSoftTotalPrice(provincePrice);
                    }
                    flowInstanceAtom.setCreateTime(now);
                    flowInstanceAtom.setUpdateTime(now);
                    productFlowInstanceAtomMapper.insert(flowInstanceAtom);

                    //新建流程配置
                    ProductFlowInstanceConfig flowInstanceConfig = new ProductFlowInstanceConfig();
                    flowInstanceConfig.setId(BaseServiceUtils.getId());
                    flowInstanceConfig.setCreateTime(now);
                    flowInstanceConfig.setUpdateTime(now);
                    flowInstanceConfig.setFlowInstanceId(productFlowInstance.getId());
                    flowInstanceConfig.setFlowId(productFlow.getId());
                    BeanUtils.copyProperties(standardProductSheflContractParam, flowInstanceConfig);
                    productFlowInstanceConfigMapper.insert(flowInstanceConfig);

                    //日志记录
                    String content = getLogContent("【新" + type + "上架】", productFlowInstance.getFlowInstanceNumber(), standardProductSheflContractParam.getSpuName(), standardProductSheflContractParam.getSkuName(), null, null, null, null, null, null, null);
                    if (StringUtils.isNotEmpty(content)) {
                        logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, content, LogResultEnum.LOG_SUCESS.code, null);
                    }
                }

            } else if (productStandard.intValue() == ProductStandardEnum.STANDARD.code.intValue() &&
                    productType.intValue() == ProductTypeEnum.COOPERATE_SALE.code.intValue()
            ) {
                //联合销售标准类 模板
                if (!checkExcelHeader(file, ProductFlowShelfExcelHeaderConstant.standardProductSheflCooperateSaleExcelHeader, 1)) {

                    logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, "文件模板错误");

                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "文件模板错误");
                }
                ;
                List<StandardProductSheflCooperateSaleParam> list = EasyExcel.read(inputStream, StandardProductSheflCooperateSaleParam.class, null)
                        .sheet(0).headRowNumber(2).doReadSync();

                if (CollectionUtils.isEmpty(list) || list.size() == 1) {
                    //第一行是注释，也不是有效数据
                    logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, "无有效数据");
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "无有效数据");
                }
                List<StandardProductSheflCooperateSaleParam> standardProductSheflCooperateSaleParams = list.subList(1, list.size());

                List<String> spuNameList = new ArrayList<>();
                standardProductSheflCooperateSaleParams.forEach(p -> {
                    if (StringUtils.isNotEmpty(p.getSpuName())) {
                        spuNameList.add(p.getSpuName());
                    }
                });
                if ("SPU".equals(type) && (spuNameList.size() > 1 || spuNameList.size() == 0)) {
                    logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, "仅支持一个SPU多个SKU导入,不支持多SPU");
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "仅支持一个SPU多个SKU导入,不支持多SPU");
                }
                StandardProductSheflCooperateSaleParam paramWithSpu = null;
                for (StandardProductSheflCooperateSaleParam standardProductSheflCooperateSaleParam : standardProductSheflCooperateSaleParams) {
                    if (!BaseUtils.anyFiledHasValue(standardProductSheflCooperateSaleParam)) {
                        continue;
                    }
                    count++;
                    //新建流程实例，包括流程实例的可编辑，可审核，可取消，可配置属性
                    ProductFlowInstance productFlowInstance = addProductFlowInstance(loginIfo4Redis, now, productFlow, flowStep);
                    productFlowInstance.setShelfType("SPU".equals(type) ? 1 : 2);
                    productFlowInstanceMapper.updateByPrimaryKey(productFlowInstance);
                    //新建流程任务
                    addProductFlowTask(loginIfo4Redis, now, productFlow, flowStep, productFlowInstance);
                    //新建流程spu,sku,原子信息等
                    if ("SPU".equals(type)) {
                        if(count == 1){
                            //如果SPU合并了单元格，则只有第一行有SPU信息
                            paramWithSpu = standardProductSheflCooperateSaleParam;
                        }else if(StringUtils.isEmpty(standardProductSheflCooperateSaleParam.getSpuName())){
                            //后面的行没有spu信息，需要手动设置
                            standardProductSheflCooperateSaleParam.setShelfCatagoryName(paramWithSpu.getShelfCatagoryName());
                            standardProductSheflCooperateSaleParam.setFirstDirectoryName(paramWithSpu.getFirstDirectoryName());
                            standardProductSheflCooperateSaleParam.setSecondDirectoryName(paramWithSpu.getSecondDirectoryName());
                            standardProductSheflCooperateSaleParam.setThirdDirectoryName(paramWithSpu.getThirdDirectoryName());
                            standardProductSheflCooperateSaleParam.setSpuName(paramWithSpu.getSpuName());
                            standardProductSheflCooperateSaleParam.setManageDepartment(paramWithSpu.getManageDepartment());
                            standardProductSheflCooperateSaleParam.setManager(paramWithSpu.getManager());
                            standardProductSheflCooperateSaleParam.setProductDesc(paramWithSpu.getProductDesc());
                            standardProductSheflCooperateSaleParam.setApplicationArea(paramWithSpu.getApplicationArea());
                            standardProductSheflCooperateSaleParam.setIsHiddenShelf(paramWithSpu.getIsHiddenShelf());
                            standardProductSheflCooperateSaleParam.setSpuServiceProvider(paramWithSpu.getSpuServiceProvider());
                            standardProductSheflCooperateSaleParam.setSaleTag(paramWithSpu.getSaleTag());
                            standardProductSheflCooperateSaleParam.setSearchWord(paramWithSpu.getSearchWord());
                        }

                        ProductFlowInstanceSpu flowInstanceSpu = new ProductFlowInstanceSpu();
                        flowInstanceSpu.setId(BaseServiceUtils.getId());
                        flowInstanceSpu.setFlowId(productFlow.getId());
                        flowInstanceSpu.setFlowInstanceId(productFlowInstance.getId());
                        flowInstanceSpu.setProductStandard(productStandard);
                        flowInstanceSpu.setProductType(productType);
                        BeanUtils.copyProperties(standardProductSheflCooperateSaleParam, flowInstanceSpu);
                        setSpuCategoryAndNavigation(flowInstanceSpu, standardProductSheflCooperateSaleParam.getShelfCatagoryName(), standardProductSheflCooperateSaleParam.getFirstDirectoryName(), standardProductSheflCooperateSaleParam.getSecondDirectoryName(), standardProductSheflCooperateSaleParam.getThirdDirectoryName());
                        flowInstanceSpu.setCreateTime(now);
                        flowInstanceSpu.setUpdateTime(now);
                        productFlowInstanceSpuMapper.insert(flowInstanceSpu);
                    }
                    if ("SKU".equals(type)) {
                        //新SKU上架，复制保存已上架的spu
                        copyShelfSpuInfo(shelfSpu, now, productFlowInstance);
                    }

                    ProductFlowInstanceSku flowInstanceSku = new ProductFlowInstanceSku();
                    flowInstanceSku.setId(BaseServiceUtils.getId());
                    flowInstanceSku.setFlowId(productFlow.getId());
                    flowInstanceSku.setFlowInstanceId(productFlowInstance.getId());
                    if ("SKU".equals(type)) {
                        flowInstanceSku.setSpuCode(spuCode);
                    }
                    BeanUtils.copyProperties(standardProductSheflCooperateSaleParam, flowInstanceSku);
                    flowInstanceSku.setShelfStatus(ProductShelfStatusEnum.SHELF_IN_PROGRESS.code);
                    if (StringUtils.isNotEmpty(standardProductSheflCooperateSaleParam.getMinPurchaseNumStr())) {
                        flowInstanceSku.setMinPurchaseNum(Integer.parseInt(standardProductSheflCooperateSaleParam.getMinPurchaseNumStr()));
                    }
                    if (StringUtils.isNotEmpty(standardProductSheflCooperateSaleParam.getSalePriceYuan())) {
                        flowInstanceSku.setSalePrice(checkPriceAndGet(standardProductSheflCooperateSaleParam.getSalePriceYuan()));
                    }
                    flowInstanceSku.setCreateTime(now);
                    flowInstanceSku.setUpdateTime(now);
                    productFlowInstanceSkuMapper.insert(flowInstanceSku);

                    ProductFlowInstanceAtom flowInstanceAtom = new ProductFlowInstanceAtom();
                    flowInstanceAtom.setId(BaseServiceUtils.getId());
                    flowInstanceAtom.setFlowId(productFlow.getId());
                    flowInstanceAtom.setFlowInstanceId(productFlowInstance.getId());
                    if ("SKU".equals(type)) {
                        flowInstanceAtom.setSpuCode(spuCode);
                    }
                    BeanUtils.copyProperties(standardProductSheflCooperateSaleParam, flowInstanceAtom);
                    if(StringUtils.isNotEmpty(standardProductSheflCooperateSaleParam.getInventoryStr())){
                        try {
                            flowInstanceAtom.setInventory(Integer.parseInt(standardProductSheflCooperateSaleParam.getInventoryStr()));
                        } catch (NumberFormatException e) {
                            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "库存数 请填写整数，不应许小数");
                        }
                    }
                    if (StringUtils.isNotEmpty(standardProductSheflCooperateSaleParam.getSettlePriceYuan())) {
                        flowInstanceAtom.setSettlePrice(checkPriceAndGet(standardProductSheflCooperateSaleParam.getSettlePriceYuan()));
                    }
                    if (StringUtils.isNotEmpty(standardProductSheflCooperateSaleParam.getHardwarePriceYuan())) {
                        flowInstanceAtom.setHardwarePrice(checkPriceAndGet(standardProductSheflCooperateSaleParam.getHardwarePriceYuan()));
                    }
                    if (StringUtils.isNotEmpty(standardProductSheflCooperateSaleParam.getSoftPriceYuan())) {
                        flowInstanceAtom.setSoftPrice(checkPriceAndGet(standardProductSheflCooperateSaleParam.getSoftPriceYuan()));
                    }
                    flowInstanceAtom.setCreateTime(now);
                    flowInstanceAtom.setUpdateTime(now);
                    productFlowInstanceAtomMapper.insert(flowInstanceAtom);

                    //新建流程配置
                    ProductFlowInstanceConfig flowInstanceConfig = new ProductFlowInstanceConfig();
                    flowInstanceConfig.setId(BaseServiceUtils.getId());
                    flowInstanceConfig.setCreateTime(now);
                    flowInstanceConfig.setUpdateTime(now);
                    flowInstanceConfig.setFlowInstanceId(productFlowInstance.getId());
                    flowInstanceConfig.setFlowId(productFlow.getId());
                    BeanUtils.copyProperties(standardProductSheflCooperateSaleParam, flowInstanceConfig);
                    productFlowInstanceConfigMapper.insert(flowInstanceConfig);

                    //日志记录
                    String content = getLogContent("【新" + type + "上架】", productFlowInstance.getFlowInstanceNumber(), standardProductSheflCooperateSaleParam.getSpuName(), standardProductSheflCooperateSaleParam.getSkuName(), null, null, null, null, null, null, null);
                    if (StringUtils.isNotEmpty(content)) {
                        logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, content, LogResultEnum.LOG_SUCESS.code, null);
                    }
                }
            } else {

                logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新" + type + "上架】", LogResultEnum.LOG_FAIL.code, "标准类型或产品类型错误");

                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "标准类型或产品类型错误");
            }
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return count;
    }

    private List<List<PlanProductSheflParam>> dealData(List<PlanProductSheflParam> planProductSheflParams, String type) {
        List<List<PlanProductSheflParam>> list = new ArrayList<>();
        List<String> spuSkuNameList = new ArrayList<>();
        int skuCount = 0;
        for (PlanProductSheflParam param : planProductSheflParams) {
            String skuName = param.getSkuName();
            String spuName = param.getSpuName();
            if(StringUtils.isNotEmpty(skuName) && !spuSkuNameList.contains(spuName+skuName)){
                //如果spu和sku信息没有合并，也兼容支持
                spuSkuNameList.add(spuName+skuName);
                skuCount++;
                //一个新的sku,如果是SPU上架那么从第二个开始需要增加spu信息（因为SPU信息被合并了）
                List<PlanProductSheflParam> skuList = new ArrayList<>();
                list.add(skuList);
                if(skuCount >= 2 && "SPU".equals(type)){
                    PlanProductSheflParam paramWithSpuInfo = list.get(0).get(0);
                    //把spu信息设置到param
                    param.setShelfCatagoryName(paramWithSpuInfo.getShelfCatagoryName());
                    param.setFirstDirectoryName(paramWithSpuInfo.getFirstDirectoryName());
                    param.setSecondDirectoryName(paramWithSpuInfo.getSecondDirectoryName());
                    param.setThirdDirectoryName(paramWithSpuInfo.getThirdDirectoryName());
                    param.setSpuName(paramWithSpuInfo.getSpuName());
                    param.setManager(paramWithSpuInfo.getManager());
                    param.setProductDesc(paramWithSpuInfo.getProductDesc());
                    param.setApplicationArea(paramWithSpuInfo.getApplicationArea());
                    param.setIsHiddenShelf(paramWithSpuInfo.getIsHiddenShelf());
                    param.setSpuServiceProvider(paramWithSpuInfo.getSpuServiceProvider());
                    param.setSaleTag(paramWithSpuInfo.getSaleTag());
                    param.setSearchWord(paramWithSpuInfo.getSearchWord());
                }
                skuList.add(param);
            }else {
                //已经添加过的sku,直接添加即可
                List<PlanProductSheflParam> skuList = list.get(list.size() - 1);
                skuList.add(param);
            }
        }
        return list;
    }

    private boolean checkExcelHeader(MultipartFile file, List<String> header, Integer headerRowNum) throws IOException {
        return ExcelHeaderCheckUtil.checkExcelHeaders(file, header, headerRowNum);
    }

    public String getShelfCmiotCostIdByName(String costName, String shelfCatagoryId) {
        ProductShelfCategoryCostExample.Criteria criteria = new ProductShelfCategoryCostExample().createCriteria().andNameEqualTo(costName);
        if (StringUtils.isNotEmpty(shelfCatagoryId)) {
            criteria.andParentIdEqualTo(shelfCatagoryId);
        }
        ProductShelfCategoryCostExample costExample = criteria.example();
        List<ProductShelfCategoryCost> productShelfCategoryCosts = productShelfCategoryCostMapper.selectByExample(costExample);
        if (productShelfCategoryCosts.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "上架类目或cmiot账目项名称错误");
        }
        return productShelfCategoryCosts.get(0).getId();
    }

    private void copyShelfSpuInfo(ProductFlowInstanceSpu shelfSpu, Date now, ProductFlowInstance productFlowInstance) {
        String existedFlowInstanceId = shelfSpu.getFlowInstanceId();
        shelfSpu.setId(BaseServiceUtils.getId());
        shelfSpu.setCreateTime(now);
        shelfSpu.setUpdateTime(now);
        shelfSpu.setFlowInstanceId(productFlowInstance.getId());
        productFlowInstanceSpuMapper.insert(shelfSpu);

        //找出已上架老的spu的导航目录信息并复制到新的sku对应的spu
        ProductFlowInstanceDirectoryExample example = new ProductFlowInstanceDirectoryExample().createCriteria().andFlowInstanceIdEqualTo(existedFlowInstanceId).example();
        List<ProductFlowInstanceDirectory> productFlowInstanceDirectories = productFlowInstanceDirectoryMapper.selectByExample(example);
        if(CollectionUtils.isNotEmpty(productFlowInstanceDirectories)){
            //流程导航目录设置到product_flow_instance_directory
            for (ProductFlowInstanceDirectory productFlowInstanceDirectory : productFlowInstanceDirectories) {
                productFlowInstanceDirectory.setId(BaseServiceUtils.getId());
                productFlowInstanceDirectory.setFlowInstanceId(shelfSpu.getFlowInstanceId());
                productFlowInstanceDirectory.setCreateTime(now);
                productFlowInstanceDirectory.setUpdateTime(now);
                productFlowInstanceDirectoryMapper.insert(productFlowInstanceDirectory);
            }
        }
    }

    /**
     * 设置流程实例的spu 类目和导航信息
     */
    private void setSpuCategoryAndNavigation(ProductFlowInstanceSpu flowInstanceSpu, String shelfCatagoryName, String firstDirectoryName, String secondDirectoryName, String thirdDirectoryName) {
        if (StringUtils.isNotEmpty(shelfCatagoryName)) {
            flowInstanceSpu.setShelfCatagoryId(getShelfCmiotCostIdByName(shelfCatagoryName, "-1"));
        }
        //产品周文静确定：如果商城修改了导航目录名称，就会导致上架的时候找不到对应名称，这种情况让一二级目录为空，然后在编辑页面重新选择一二级目录
        String firstDirectoryId = null;
        String secondDirectoryId = null;
        String thirdDirectoryId = null;
        if (StringUtils.isNotEmpty(firstDirectoryName)) {
            firstDirectoryId = getNavigationIdByName(firstDirectoryName, "-1");
        }
        if (StringUtils.isNotEmpty(secondDirectoryName) && StringUtils.isNotEmpty(firstDirectoryId)) {
            secondDirectoryId = getNavigationIdByName(secondDirectoryName, firstDirectoryId);
        }
        if (StringUtils.isNotEmpty(thirdDirectoryName) && StringUtils.isNotEmpty(secondDirectoryId)) {
            thirdDirectoryId = getNavigationIdByName(thirdDirectoryName, secondDirectoryId);
        }
        //流程导航目录设置到product_flow_instance_directory表
        Date now = new Date();
        ProductFlowInstanceDirectory productFlowInstanceDirectory = new ProductFlowInstanceDirectory();
        productFlowInstanceDirectory.setId(BaseServiceUtils.getId());
        productFlowInstanceDirectory.setFlowInstanceId(flowInstanceSpu.getFlowInstanceId());
        productFlowInstanceDirectory.setFirstDirectoryId(firstDirectoryId);
        productFlowInstanceDirectory.setSecondDirectoryId(secondDirectoryId);
        productFlowInstanceDirectory.setThirdDirectoryId(thirdDirectoryId);
        productFlowInstanceDirectory.setFirstDirectoryName(firstDirectoryName);
        productFlowInstanceDirectory.setSecondDirectoryName(secondDirectoryName);
        productFlowInstanceDirectory.setThirdDirectoryName(thirdDirectoryName);
        productFlowInstanceDirectory.setCreateTime(now);
        productFlowInstanceDirectory.setUpdateTime(now);
        productFlowInstanceDirectoryMapper.insert(productFlowInstanceDirectory);

    }

    public String getNavigationIdByName(String firstDirectoryName, String parentId) {
        ProductNavigationDirectoryExample.Criteria criteria = new ProductNavigationDirectoryExample().createCriteria()
                .andNameEqualTo(firstDirectoryName).andIsDeleteEqualTo(false).andMenuEqualTo("1");
        if (StringUtils.isNotEmpty(parentId)) {
            criteria.andParentIdEqualTo(parentId);
        }
        ProductNavigationDirectoryExample firstNavigationExample = criteria.example();
        List<ProductNavigationDirectory> firstNavigationList = productNavigationDirectoryMapper.selectByExample(firstNavigationExample);
        if (firstNavigationList.isEmpty()) {
            String msg = "导航目录名称错误";
            if (!"-1".equals(parentId)) {
                msg = "二级或三级导航目录名称错误";
            }
            log.error(msg);
            return null;
        }
        return firstNavigationList.get(0).getId();
    }

    @Override
    public BaseAnswer dealProductFlowInstanceDirectory() {
        log.info("dealProductFlowInstanceDirectory start");
        Set<String> notExistedFirstDirectoryIdList = new HashSet<>();
        Set<String> notExistedSecondDirectoryIdList = new HashSet<>();
        List<String> errMsg = new ArrayList<>();

        //查找product_flow_instance_directory表没有的流程实例类目数据
        List<ProductFlowInstanceSpu> list = productFlowInstanceMapperExt.findNeedDealProductFlowInstanceSpu();
        log.info("dealProductFlowInstanceDirectory 未处理的流程数量");
        if (CollectionUtils.isNotEmpty(list)) {
            for (ProductFlowInstanceSpu flowInstanceSpu : list) {
                try {
                    //使用内部事务逐个处理，将类目数据存入product_flow_instance_directory表
                    productFlowInstanceServiceImpl.dealDirectory(flowInstanceSpu,notExistedFirstDirectoryIdList,notExistedSecondDirectoryIdList);
                } catch (Exception e) {
                    errMsg.add("流程实例id:"+flowInstanceSpu.getFlowInstanceId()+"导航目录处理失败:"+e.getMessage());
                }
            }
        }

        //返回不存在的一级类目id，二级类目id
        List<String> resultList = new ArrayList<>();
        if (!notExistedFirstDirectoryIdList.isEmpty()) {
            resultList.add("不存在的一级目录id:" + String.join(",", notExistedFirstDirectoryIdList));
        }
        if (!notExistedSecondDirectoryIdList.isEmpty()) {
            resultList.add("不存在的二级目录id:" + String.join(",", notExistedSecondDirectoryIdList));
        }
        if(!errMsg.isEmpty()){
            resultList.addAll(errMsg);
        }
        String answer = "成功";
        if (CollectionUtils.isNotEmpty(resultList)) {
            answer = String.join(System.getProperty("line.separator"), resultList);
            log.error("处理失败的结果:{}",answer);
        }
        log.info("dealProductFlowInstanceDirectory 处理完毕");

        return BaseAnswer.success(answer);

    }

    @Override
    public BaseAnswer<List<ProductFlowInstanceDetailVO.NavigationDirectory>> navigationData() {
        ProductNavigationDirectoryExample example = new ProductNavigationDirectoryExample().createCriteria().andIsDeleteEqualTo(false).example();
        example.orderBy("id");
        List<ProductFlowInstanceDetailVO.NavigationDirectory> list = new ArrayList<>();
        List<ProductNavigationDirectory> directoryList = productNavigationDirectoryMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(directoryList)){
            return BaseAnswer.success(list);
        }
        List<ProductNavigationDirectory> firstDirectoryList = directoryList.stream().filter(d -> {
            return "-1".equals(d.getParentId());
        }).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(firstDirectoryList)){
            return BaseAnswer.success(list);
        }
        Map<String, List<ProductNavigationDirectory>> parentIdAndDataListMap = directoryList.stream().filter(d -> {
            return !"-1".equals(d.getParentId());
        }).collect(Collectors.groupingBy(ProductNavigationDirectory::getParentId));
        list = getNavigationTreeData(firstDirectoryList,parentIdAndDataListMap);
        return BaseAnswer.success(list);
    }

    private List<ProductFlowInstanceDetailVO.NavigationDirectory> getNavigationTreeData(List<ProductNavigationDirectory> directoryList, Map<String, List<ProductNavigationDirectory>> parentIdAndDataListMap) {
        return directoryList.stream().map(d -> {
            ProductFlowInstanceDetailVO.NavigationDirectory directory = new ProductFlowInstanceDetailVO.NavigationDirectory();
            BeanUtils.copyProperties(d,directory);
            List<ProductNavigationDirectory> childList = parentIdAndDataListMap.get(d.getId());
            if(CollectionUtils.isNotEmpty(childList)){
                List<ProductFlowInstanceDetailVO.NavigationDirectory> childData = getNavigationTreeData(childList, parentIdAndDataListMap);
                directory.setChildren(childData);
            }else {
                directory.setChildren(new ArrayList<>());
            }
            return directory;
        }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public void dealDirectory(ProductFlowInstanceSpu flowInstanceSpu, Set<String> notExistedFirstDirectoryIdList, Set<String> notExistedSecondDirectoryIdList) {
        /*String firstDirectoryId = flowInstanceSpu.getFirstDirectoryId();
        String secondDirectoryId = flowInstanceSpu.getSecondDirectoryId();
        String flowInstanceId = flowInstanceSpu.getFlowInstanceId();

        ProductNavigationDirectoryExample firstExample = new ProductNavigationDirectoryExample().createCriteria().andIdEqualTo(firstDirectoryId).example();
        List<ProductNavigationDirectory> firstDirectoryList = productNavigationDirectoryMapper.selectByExample(firstExample);
        if(CollectionUtils.isEmpty(firstDirectoryList)){
            notExistedFirstDirectoryIdList.add(firstDirectoryId);
            return;
        }
        ProductNavigationDirectoryExample secondExample = new ProductNavigationDirectoryExample().createCriteria().andIdEqualTo(secondDirectoryId).example();
        List<ProductNavigationDirectory> secondDirectoryList = productNavigationDirectoryMapper.selectByExample(secondExample);
        if(CollectionUtils.isEmpty(secondDirectoryList)){
            notExistedSecondDirectoryIdList.add(secondDirectoryId);
            return;
        }
        Date now = new Date();
        ProductFlowInstanceDirectory productFlowInstanceDirectory = new ProductFlowInstanceDirectory();
        productFlowInstanceDirectory.setId(BaseServiceUtils.getId());
        productFlowInstanceDirectory.setFlowInstanceId(flowInstanceId);
        productFlowInstanceDirectory.setFirstDirectoryId(firstDirectoryId);
        productFlowInstanceDirectory.setSecondDirectoryId(secondDirectoryId);
        productFlowInstanceDirectory.setCreateTime(now);
        productFlowInstanceDirectory.setUpdateTime(now);

        productFlowInstanceDirectoryMapper.insert(productFlowInstanceDirectory);*/
    }

    private void addProductFlowTask(LoginIfo4Redis loginIfo4Redis, Date now, ProductFlow productFlow, ProductFlowStep flowStep, ProductFlowInstance productFlowInstance) {
        ProductFlowInstanceTask productFlowTask = new ProductFlowInstanceTask();
        productFlowTask.setId(BaseServiceUtils.getId());
        productFlowTask.setFlowInstanceId(productFlowInstance.getId());
        productFlowTask.setFlowId(productFlow.getId());
        productFlowTask.setStepId(flowStep.getId());
        productFlowTask.setStepName(flowStep.getStepName());
        productFlowTask.setAssigneeId(loginIfo4Redis.getUserId());
        productFlowTask.setAssigneeName(loginIfo4Redis.getUserName());
        productFlowTask.setHandleStatus(ProductFlowHandleStatusEnum.UNHANDLE.code);
        productFlowTask.setCreateTime(now);
        productFlowTask.setUpdateTime(now);
        productFlowInstanceTaskMapper.insert(productFlowTask);
    }

    private ProductFlowInstance addProductFlowInstance(LoginIfo4Redis loginIfo4Redis, Date now, ProductFlow productFlow, ProductFlowStep flowStep) {
        ProductFlowInstance productFlowInstance = new ProductFlowInstance();
        productFlowInstance.setId(BaseServiceUtils.getId());
        productFlowInstance.setFlowId(productFlow.getId());
        productFlowInstance.setFlowInstanceNumber(productFlowUtil.generateFlowInstanceNumber(productFlow.getFlowType()));
        productFlowInstance.setCreatorId(loginIfo4Redis.getUserId());
        productFlowInstance.setCreatorName(loginIfo4Redis.getUserName());
        productFlowInstance.setCurrentStepId(flowStep.getId());
        productFlowInstance.setStatus(ProductFlowInstanceStatusEnum.IN_PROGRESS.code);
        productFlowInstance.setCreateTime(now);
        productFlowInstance.setUpdateTime(now);
        productFlowInstance.setCanEdit(true);
        productFlowInstance.setCanCancel(true);
        productFlowInstance.setCanConfig(false);
        productFlowInstance.setCanAudit(false);
        productFlowInstanceMapper.insert(productFlowInstance);
        return productFlowInstance;
    }

    @Override
    public BaseAnswer skuShelf(String spuCode, String skuCode, MultipartFile file, LoginIfo4Redis loginIfo4Redis) throws IOException {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        try {
            Date now = new Date();
            if (StringUtils.isNotEmpty(skuCode) && file != null) {
                logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新SKU上架】", LogResultEnum.LOG_FAIL.code, "复制已有SKU或上传SKU文件，只能二选一");
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "复制已有SKU或上传SKU文件，只能二选一");
            }
            ProductFlowInstanceSpu productFlowInstanceSpu = productFlowInstanceMapperExt.getShelfSpu(spuCode);
            if (productFlowInstanceSpu == null) {
                logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新SKU上架】", LogResultEnum.LOG_FAIL.code, "spu流程不存在,无法上架sku");

                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "spu流程不存在,无法上架sku");
            }
            Integer productType = productFlowInstanceSpu.getProductType();
            Integer productStandard = productFlowInstanceSpu.getProductStandard();
            //根据spu产品标准，判断走哪个上架流程
            if (!ProductStandardEnum.contains(productStandard)) {
                logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新SKU上架】", LogResultEnum.LOG_FAIL.code, "产品标准错误");

                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "产品标准错误");
            }
            if (!ProductTypeEnum.contains(productType)) {
                logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新SKU上架】", LogResultEnum.LOG_FAIL.code, "产品类别错误");

                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "产品类别错误");
            }
            Integer operateType = getOperateType(productType);
            //判断走哪个上架流程
            ProductFlowExample example = new ProductFlowExample().createCriteria().andFlowTypeEqualTo(ProductFlowTypeEnum.PRODUCT_SHELF.code).andOperateTypeEqualTo(operateType).andStatusEqualTo(0).example();
            List<ProductFlow> flowList = productFlowMapper.selectByExample(example);
            if (flowList.isEmpty()) {
                logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新SKU上架】", LogResultEnum.LOG_FAIL.code, "找不到对应流程定义");

                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "找不到对应流程定义");
            }
            ProductFlow productFlow = flowList.get(0);
            productFlowInstanceSpu.setFlowId(productFlow.getId());
            ProductFlowStepExample flowStepExample = new ProductFlowStepExample().createCriteria().andFlowIdEqualTo(productFlow.getId()).andStepIndexEqualTo(1).example();
            List<ProductFlowStep> productFlowSteps = productFlowStepMapper.selectByExample(flowStepExample);
            if (productFlowSteps.isEmpty()) {
                logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新SKU上架】", LogResultEnum.LOG_FAIL.code, "请找管理员配置流程");
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "请找管理员配置流程");
            }
            ProductFlowStep flowStep = productFlowSteps.get(0);
            InputStream inputStream = null;
            Integer count = 0;
            if (file != null) {
                //通过文件上架sku
                try {
                    inputStream = file.getInputStream();
                } catch (IOException e) {
                    log.error("获取输入流出错", e);
                    throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR, "解析文件出错");
                }
                //按照不同的模板读取文件并上架
                count = productShelfFromFile(productStandard, productType, loginIfo4Redis, productFlow, flowStep, inputStream, "SKU", spuCode, productFlowInstanceSpu, file);
            }
            if (StringUtils.isNotEmpty(skuCode)) {
                count = 1;
                //通过曾经上架过的SKU信息上架
                ProductFlowInstanceSkuExample skuExample = new ProductFlowInstanceSkuExample().createCriteria().andSkuCodeEqualTo(skuCode).andSpuCodeEqualTo(spuCode).andShelfStatusEqualTo(ProductShelfStatusEnum.SHELF_SUCCESS.code).example();
                skuExample.orderBy("create_time ASC");
                List<ProductFlowInstanceSku> productFlowInstanceSkus = productFlowInstanceSkuMapper.selectByExample(skuExample);
                if (productFlowInstanceSkus.isEmpty()) {
                    logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, "【新SKU上架】", LogResultEnum.LOG_FAIL.code, "未找到该SKU的上架流程数据");

                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到该SKU的上架流程数据");
                }
                //新建流程实例，包括流程实例的可编辑，可审核，可取消，可配置属性
                ProductFlowInstance productFlowInstance = addProductFlowInstance(loginIfo4Redis, now, productFlow, flowStep);

                //新建流程任务
                addProductFlowTask(loginIfo4Redis, now, productFlow, flowStep, productFlowInstance);
                //复制保存已上架的spu
                copyShelfSpuInfo(productFlowInstanceSpu, now, productFlowInstance);
                //复制曾经上架的sku信息
                ProductFlowInstanceSku productFlowInstanceSku = productFlowInstanceSkus.get(0);
                String originFlowInstanceId = productFlowInstanceSku.getFlowInstanceId();
                productFlowInstanceSku.setId(BaseServiceUtils.getId());
                productFlowInstanceSku.setFlowInstanceId(productFlowInstance.getId());
                productFlowInstanceSku.setSkuCode(null);
                productFlowInstanceSku.setShelfStatus(ProductShelfStatusEnum.SHELF_IN_PROGRESS.code);
                productFlowInstanceSku.setCreateTime(now);
                productFlowInstanceSku.setUpdateTime(now);
                productFlowInstanceSkuMapper.insert(productFlowInstanceSku);
                //复制atom信息
                List<ProductFlowInstanceAtom> productFlowInstanceAtoms = getFlowInstanceAtomList(originFlowInstanceId);
                for (ProductFlowInstanceAtom productFlowInstanceAtom : productFlowInstanceAtoms) {
                    productFlowInstanceAtom.setId(BaseServiceUtils.getId());
                    productFlowInstanceAtom.setFlowInstanceId(productFlowInstance.getId());
                    productFlowInstanceAtom.setCreateTime(now);
                    productFlowInstanceAtom.setUpdateTime(now);
                    productFlowInstanceAtom.setSpuCode(spuCode);
                    //sku上架发起时，还没有skuCode
                    productFlowInstanceAtom.setSkuCode(null);
                    productFlowInstanceAtomMapper.insert(productFlowInstanceAtom);
                }

                //复制config信息
                ProductFlowInstanceConfigExample configExample = new ProductFlowInstanceConfigExample().createCriteria().andFlowInstanceIdEqualTo(originFlowInstanceId).example();
                List<ProductFlowInstanceConfig> productFlowInstanceConfigs = productFlowInstanceConfigMapper.selectByExample(configExample);
                if (CollectionUtils.isNotEmpty(productFlowInstanceConfigs)) {
                    for (ProductFlowInstanceConfig productFlowInstanceConfig : productFlowInstanceConfigs) {
                        productFlowInstanceConfig.setId(BaseServiceUtils.getId());
                        productFlowInstanceConfig.setFlowInstanceId(productFlowInstance.getId());
                        productFlowInstanceConfig.setCreateTime(now);
                        productFlowInstanceConfig.setUpdateTime(now);
                        productFlowInstanceConfigMapper.insert(productFlowInstanceConfig);
                    }
                }

                //记录日志
                String content = getLogContent("【新SKU上架】", productFlowInstance.getFlowInstanceNumber(), productFlowInstanceSpu.getSpuName(), productFlowInstanceSku.getSkuName(), null, null, null, null, null, null, null);
                if (StringUtils.isNotEmpty(content)) {

                    logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.SHELF.code, content, LogResultEnum.LOG_SUCESS.code, null);
                }
                productFlowInstance.setShelfType(2);
                productFlowInstanceMapper.updateByPrimaryKey(productFlowInstance);
            }

            response.addHeader("stateCode", BaseErrorConstant.SUCCESS.getStateCode());
            response.addHeader("message", "ok");
            response.addHeader("data", count + "");
            return BaseAnswer.success(null);
        } catch (Exception e) {
            //便于前端拿到响应，将异常信息放入header
            try {
                if (e instanceof BusinessException) {
                    BusinessException businessException = (BusinessException) e;
                    response.addHeader("stateCode", businessException.getStatus().getStateCode());
                    response.addHeader("message", URLEncoder.encode(businessException.getStatus().getMessage(), "UTF-8"));
                } else {
                    log.error("{}上架发生异常,", e);
                    response.addHeader("stateCode", BaseErrorConstant.INTERNAL_ERROR.getStateCode());
                    response.addHeader("message", URLEncoder.encode(BaseErrorConstant.INTERNAL_ERROR.getMessage(), "UTF-8"));
                }
            } catch (UnsupportedEncodingException ex) {
                ex.printStackTrace();
            }
            throw e;
        }
    }

    private List<ProductFlowInstanceAtom> getFlowInstanceAtomList(String originFlowInstanceId) {
        ProductFlowInstanceAtomExample flowAtomExample = new ProductFlowInstanceAtomExample().createCriteria().andFlowInstanceIdEqualTo(originFlowInstanceId).example();
        List<ProductFlowInstanceAtom> productFlowInstanceAtoms = productFlowInstanceAtomMapper.selectByExample(flowAtomExample);
        if (productFlowInstanceAtoms.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到该SKU对应的原子上架流程数据");
        }
        return productFlowInstanceAtoms;
    }

    public Integer getOperateType(Integer productType) {
        Integer operateType = null;
        //分省运营类包括省框、省内、DICT、合同履约四类产品；统一运营类包括联合销售产品
        if (productType.intValue() == ProductTypeEnum.PROVINCE_RANGE.code.intValue() ||
                productType.intValue() == ProductTypeEnum.PROVINCE.code.intValue() ||
                productType.intValue() == ProductTypeEnum.DICT.code.intValue() ||
                productType.intValue() == ProductTypeEnum.CONTRACT.code.intValue()
        ) {
            operateType = ProductOperateTypeEnum.PROVINCE.code;
        } else if (productType.intValue() == ProductTypeEnum.COOPERATE_SALE.code.intValue()) {
            operateType = ProductOperateTypeEnum.UNIFICATION.code;
        }
        return operateType;
    }

    @Override
    public BaseAnswer specialEdit(ProductFlowInstanceEditParam param, LoginIfo4Redis loginIfo4Redis) {
        String flowInstanceId = param.getProductFlowInstanceId();
        ProductFlowInstance productFlowInstance = productFlowInstanceMapper.selectByPrimaryKey(flowInstanceId);
        if (productFlowInstance == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程单不存在");
        }
        ProductFlow productFlow = productFlowMapper.selectByPrimaryKey(productFlowInstance.getFlowId());
        if (productFlow == null) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "流程不存在");
        }
        ProductFlowInstanceSpu productFlowInstanceSpu = getFlowInstanceSpu(flowInstanceId);
        List<ProductFlowInstanceSku> productFlowInstanceSkus = getFlowInstanceSkuList(flowInstanceId);
        editFlow(param, loginIfo4Redis, false, productFlowInstanceSpu, productFlowInstanceSkus.get(0), productFlowInstance, productFlow, null, true);
        //记录日志
        String content = getLogContent("【编辑】", productFlowInstance.getFlowInstanceNumber(), param.getSpuInfo().getSpuName(), param.getSkuInfo().getSkuName(), null, null, null, param.getSpuInfo().getSpuCode(), param.getSkuInfo().getSkuCode(), null, null);
        if (StringUtils.isNotEmpty(content)) {
            logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.INQUIRE.code, content, LogResultEnum.LOG_SUCESS.code, null);
        }
        return BaseAnswer.success(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer importAttachment(MultipartFile uploadFile) {
        String originalFilename = uploadFile.getOriginalFilename();
        if (!originalFilename.endsWith("zip")) {
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "仅支持ZIP格式压缩文件");
        }
        InputStream zipInputStream = null;
        try {
            zipInputStream = uploadFile.getInputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }
        Date now = new Date();
        List<ProductFlowInstanceAttachment> attachmentList = new ArrayList<>();
        List<Integer> flowTypeList = new ArrayList<>();
        flowTypeList.add(ProductFlowTypeEnum.PRODUCT_SHELF.code);
        List<Integer> shelfStatusList = new ArrayList<>();
        shelfStatusList.add(ProductShelfStatusEnum.SHELF_SUCCESS.code);
        //避免多次查询数据库，用map暂存
        //key - spuCode, value - 该spu下所有sku的上架流程sku实例
        Map<String, List<ProductFlowInstanceSkuDTO>> spuAndFlowInstanceMap = new HashMap<>();
        //key - spuCode_SkuCode, value - 该spu_sku的上架流程sku实例
        Map<String, List<ProductFlowInstanceSkuDTO>> spuSkuAndFlowInstanceMap = new HashMap<>();
        try (ZipArchiveInputStream zip = new ZipArchiveInputStream(zipInputStream)) {
            ZipArchiveEntry zipEntry = null;
            while ((zipEntry = zip.getNextZipEntry()) != null) {
                String fileName = zipEntry.getName();
                if (fileName.endsWith("/")) {
                    //文件夹,不处理
                    continue;
                } else {
                    //文件，名称有两种： spuCode/附件 ，或者 spuCode_skuCode/附件
                    String productCode = fileName.substring(0, fileName.lastIndexOf("/"));
                    String spuCode = null;
                    String skuCode = null;
                    String attachmentName = fileName.substring(fileName.lastIndexOf("/") + 1, fileName.lastIndexOf("."));
                    ProductFlowAttachmentTypeEnum typeEnum = ProductFlowAttachmentTypeEnum.fromContainName(attachmentName);
                    if (typeEnum == null) {
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "文件:" + fileName + "的附件名称错误");
                    }
                    try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                        //上传附件
                        byte[] byte_s = new byte[1024];
                        int num;
                        while ((num = zip.read(byte_s, 0, byte_s.length)) > 0) {
                            outputStream.write(byte_s, 0, num);
                        }
                        outputStream.close();
                        ByteArrayUpload byteArrayUpload = new ByteArrayUpload();
                        BaseAnswer<UpResult> uploadResult = null;
                        //处理同名文件覆盖问题,在文件名后加"_xxxxx"
                        String[] nameArray = fileName.split("\\.");
                        fileName = nameArray[0] + "_" + new Date().getTime() + "." + nameArray[1];
                        try {
                            byteArrayUpload.setBytes(outputStream.toByteArray());
                            byteArrayUpload.setFileName(fileName);
                            uploadResult = storageService.uploadByte(byteArrayUpload);
                        } catch (Exception e) {
                            log.error("上传文件" + fileName + "发生异常", e);
                            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR);
                        }
                        if (!uploadResult.getStateCode().equals(ExcepStatus.getSuccInstance().getStateCode())) {
                            log.error("上传文件" + fileName + "响应失败:{}", JSON.toJSONString(uploadResult));
                            throw new BusinessException(BaseErrorConstant.INTERNAL_ERROR);
                        }
                        if (!productCode.contains("_")) {
                            //spu下所有sku相同的附件,给每个sku的上架流程都增加附件
                            spuCode = productCode;
                            ProductFlowSkuParam skuParam = new ProductFlowSkuParam();
                            skuParam.setSpuCode(spuCode);
                            skuParam.setFlowTypeList(flowTypeList);
                            skuParam.setShelfStatusList(shelfStatusList);
                            List<ProductFlowInstanceSkuDTO> skuList = spuAndFlowInstanceMap.get(spuCode);
                            if (skuList == null) {
                                skuList = productFlowInstanceSkuMapperExt.listProductFlowInstanceSkuOnLine(skuParam);
                                spuAndFlowInstanceMap.put(spuCode, skuList);
                            }
                            if (CollectionUtils.isNotEmpty(skuList)) {
                                //填充流程附件数据
                                setAttachmentList(now, attachmentList, attachmentName, typeEnum, uploadResult, skuList);
                            } else {
                                log.error("spu:{}下没有完成上架的sku，不上传附件", spuCode);
                            }
                        } else {
                            //spu下sku有不同的附件，给指定的sku的上架流程都增加附件
                            String[] s = productCode.split("_");
                            spuCode = s[0];
                            skuCode = s[1];
                            ProductFlowSkuParam skuParam = new ProductFlowSkuParam();
                            skuParam.setSpuCode(spuCode);
                            skuParam.setSkuCode(skuCode);
                            skuParam.setFlowTypeList(flowTypeList);
                            skuParam.setShelfStatusList(shelfStatusList);
                            List<ProductFlowInstanceSkuDTO> skuList = spuSkuAndFlowInstanceMap.get(productCode);
                            if (skuList == null) {
                                skuList = productFlowInstanceSkuMapperExt.listProductFlowInstanceSkuOnLine(skuParam);
                                spuSkuAndFlowInstanceMap.put(productCode, skuList);
                            }
                            if (CollectionUtils.isNotEmpty(skuList)) {
                                //填充流程附件数据
                                setAttachmentList(now, attachmentList, attachmentName, typeEnum, uploadResult, skuList);
                            } else {
                                log.error("spu:{},sku:{}没有完成上架，不上传附件", spuCode, skuCode);
                            }
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(attachmentList)) {
                //批量插入附件
                int start = 0;
                int size = 1000;
                while (true) {
                    int end = (start + size) <= attachmentList.size() ? (start + size) : attachmentList.size();
                    List<ProductFlowInstanceAttachment> subList = attachmentList.subList(start, end);
                    if (CollectionUtils.isNotEmpty(subList)) {
                        productFlowInstanceAttachmentMapper.batchInsert(subList);
                    }
                    if (subList.size() < size) {
                        break;
                    }
                    start += size;
                }
            }
        } catch (Exception e) {
            log.error("导入文件报错", e);
        }
        return BaseAnswer.success(null);
    }

    private void setAttachmentList(Date now, List<ProductFlowInstanceAttachment> attachmentList, String attachmentName, ProductFlowAttachmentTypeEnum typeEnum, BaseAnswer<UpResult> uploadResult, List<ProductFlowInstanceSkuDTO> skuList) {
        List<String> flowInstanceIdList = skuList.stream().map(s -> {
            return s.getFlowInstanceId();
        }).collect(Collectors.toList());
        for (String flowInstanceId : flowInstanceIdList) {
            //保存流程附件记录
            UpResult upResult = uploadResult.getData();
            ProductFlowInstanceAttachment attacment = new ProductFlowInstanceAttachment();
            attacment.setId(BaseServiceUtils.getId());
            attacment.setFlowInstanceId(flowInstanceId);
            attacment.setFileName(attachmentName);
            attacment.setFileKey(upResult.getKey());
            attacment.setFileUrl(upResult.getOuterUrl());
            attacment.setType(typeEnum.code);
            attacment.setCreateTime(now);
            attacment.setUpdateTime(now);
            attachmentList.add(attacment);
            redisTemplate.delete(Constant.REDIS_KEY_PRODUCT_FLOW_INSTANCE_ATTACHMENT + flowInstanceId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer skuInfoUpdate(SkuInfoUpdateParam skuInfoUpdateParam, LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer baseAnswer = new BaseAnswer();
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        String ip = org.apache.commons.lang3.StringUtils.isNotEmpty(request.getHeader(Constant.IP)) ? request.getHeader(Constant.IP) : "127.0.0.1";
        String skuCode = skuInfoUpdateParam.getSkuCode();
        String spuCode = skuInfoUpdateParam.getSpuCode();
        Integer productFlowType = skuInfoUpdateParam.getProductFlowType();
        String flowInstanceIdParam = skuInfoUpdateParam.getFlowInstanceId();

        boolean hasFlowType = ProductFlowTypeEnum.containsCode(productFlowType);
        if (!hasFlowType) {

            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.UPDATE.code,
                        "-".toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "流程类型错误");
            });
            throw new BusinessException("10008", "流程类型错误");
        }

        ProductFlowInstanceSpuExample productFlowInstanceSpuExample = new ProductFlowInstanceSpuExample();
        productFlowInstanceSpuExample.createCriteria()
                .andSpuCodeEqualTo(spuCode)
                .andFlowInstanceIdEqualTo(flowInstanceIdParam);
        productFlowInstanceSpuExample.orderBy("create_time asc");
        List<ProductFlowInstanceSpu> productFlowInstanceSpuList = productFlowInstanceSpuMapper.selectByExample(productFlowInstanceSpuExample);
        if (CollectionUtils.isEmpty(productFlowInstanceSpuList)) {

            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.UPDATE.code,
                        "-", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "流程更新的spu信息不存在");
            });
            throw new BusinessException("10008", "流程更新的spu信息不存在");
        }

        ProductFlowSkuParam productFlowSkuParam = new ProductFlowSkuParam();
        productFlowSkuParam.setFlowInstanceStatus(ProductFlowInstanceStatusEnum.IN_PROGRESS.code);
        productFlowSkuParam.setSkuCode(skuCode);
        productFlowSkuParam.setSpuCode(spuCode);
        List<ProductFlowInstanceSkuDTO> flowingInstanceSkuList = productFlowInstanceSkuMapperExt.listProductFlowInstanceSkuOnLine(productFlowSkuParam);
        if (CollectionUtils.isNotEmpty(flowingInstanceSkuList)) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.UPDATE.code,
                        "-".toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "流程更新的sku还有处于其他流程中的步骤未完成");
            });
            throw new BusinessException("10008", "流程更新的sku还有处于其他流程中的步骤未完成");
        }

        ProductFlowInstanceSkuExample productFlowInstanceSkuExample = new ProductFlowInstanceSkuExample();
        productFlowInstanceSkuExample.createCriteria()
                .andShelfStatusEqualTo(ProductShelfStatusEnum.SHELF_SUCCESS.code)
                .andSkuCodeEqualTo(skuCode)
                .andSpuCodeEqualTo(spuCode);
//                .andFlowInstanceIdEqualTo(flowInstanceIdParam);
        productFlowInstanceSkuExample.orderBy("create_time asc");
        List<ProductFlowInstanceSku> productFlowInstanceSkuList = productFlowInstanceSkuMapper.selectByExample(productFlowInstanceSkuExample);
        if (CollectionUtils.isEmpty(productFlowInstanceSkuList)) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.UPDATE.code,
                        "-".toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "流程更新的sku信息不存在");
            });
            throw new BusinessException("10008", "流程更新的sku信息不存在");
        }

        /*if (productFlowInstanceSkuList.size() > 1) {
            throw new BusinessException("10008", "进行流程更新时相同sku同时生效信息不能有多个");
        }*/

        ProductFlowInstanceSkuExample flowInstanceSkuExample = new ProductFlowInstanceSkuExample();
        List shelfStatusParamList = new ArrayList();
        shelfStatusParamList.add(ProductShelfStatusEnum.SHELF_IN_PROGRESS.code);
        shelfStatusParamList.add(ProductShelfStatusEnum.OFF_SHELF_IN_PROGRESS.code);
        shelfStatusParamList.add(ProductShelfStatusEnum.OFF_SHELF_SUCCESS.code);
        flowInstanceSkuExample.createCriteria()
                .andShelfStatusIn(shelfStatusParamList)
                .andSkuCodeEqualTo(skuCode)
                .andSpuCodeEqualTo(spuCode);
        List<ProductFlowInstanceSku> flowInstanceSkuList = productFlowInstanceSkuMapper.selectByExample(flowInstanceSkuExample);
        if (CollectionUtils.isNotEmpty(flowInstanceSkuList)
                && flowInstanceSkuList.size() > 0) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.UPDATE.code,
                        "-".toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "流程更新的sku信息正处于上架中、下架中或已下架");
            });
            throw new BusinessException("10008", "流程更新的sku信息正处于上架中、下架中或已下架");
        }

        ProductFlowInstanceSku oldProductFlowInstanceSku = productFlowInstanceSkuList.get(0);
        String oldFlowId = oldProductFlowInstanceSku.getFlowId();
        String oldFlowInstanceId = oldProductFlowInstanceSku.getFlowInstanceId();

        if (!flowInstanceIdParam.equals(oldFlowInstanceId)) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.UPDATE.code,
                        "-".toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "流程更新的sku生效信息发生变化");
            });
            throw new BusinessException("10008", "流程更新的sku生效信息发生变化");
        }

        Date date = new Date();
        ProductFlow oldProductFlow = productFlowMapper.selectByPrimaryKey(oldFlowId);

        ProductFlowExample productFlowExample = new ProductFlowExample();
        productFlowExample.createCriteria()
                .andFlowTypeEqualTo(productFlowType)
                .andOperateTypeEqualTo(oldProductFlow.getOperateType())
                .andStatusEqualTo(0);
        List<ProductFlow> productFlowList = productFlowMapper.selectByExample(productFlowExample);
        if (CollectionUtils.isEmpty(productFlowList)) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.UPDATE.code,
                        "-".toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "流程信息不存在");
            });
            throw new BusinessException("10008", "流程信息不存在");
        }

        ProductFlow productFlow = productFlowList.get(0);
        String productFlowId = productFlow.getId();
        ProductFlowStepExample productFlowStepExample = new ProductFlowStepExample();
        productFlowStepExample.createCriteria()
                .andFlowIdEqualTo(productFlowId);
        productFlowStepExample.orderBy("step_index asc");
        List<ProductFlowStep> productFlowStepList = productFlowStepMapper.selectByExample(productFlowStepExample);
        if (CollectionUtils.isEmpty(productFlowStepList)) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.UPDATE.code,
                        "-".toString(), loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "请找管理员配置流程");
            });
            throw new BusinessException("10008", "请找管理员配置流程");
        }
        ProductFlowStep productFlowStep = productFlowStepList.get(0);

        // 添加流程实例
        ProductFlowInstance productFlowInstance = addProductFlowInstance(loginIfo4Redis, date, productFlow, productFlowStep);
        /*ProductFlowInstance productFlowInstance = new ProductFlowInstance();
        productFlowInstance.setId(BaseServiceUtils.getId());
        productFlowInstance.setFlowId(productFlowId);
        productFlowInstance.setFlowInstanceNumber(productFlowUtil.generateFlowInstanceNumber(productFlowType));
        productFlowInstance.setCreatorId(loginIfo4Redis.getUserId());
        productFlowInstance.setCreatorName(loginIfo4Redis.getUserName());
        productFlowInstance.setCurrentStepId(productFlowStep.getId());
        productFlowInstance.setStatus(0);
        productFlowInstance.setCreateTime(date);
        productFlowInstance.setUpdateTime(date);
        productFlowInstance.setCanEdit(true);
        productFlowInstance.setCanCancel(true);
        productFlowInstance.setCanAudit(true);
        productFlowInstance.setCanConfig(true);
        productFlowInstanceMapper.insert(productFlowInstance);*/

        String flowInstanceId = productFlowInstance.getId();
        // 添加流程原子实例
        ProductFlowInstanceAtomExample productFlowInstanceAtomExample = new ProductFlowInstanceAtomExample();
        productFlowInstanceAtomExample.createCriteria()
                .andSpuCodeEqualTo(spuCode)
                .andSkuCodeEqualTo(skuCode)
                .andFlowInstanceIdEqualTo(oldFlowInstanceId);
        List<ProductFlowInstanceAtom> productFlowInstanceAtomList = productFlowInstanceAtomMapper.selectByExample(productFlowInstanceAtomExample);
        if (CollectionUtils.isNotEmpty(productFlowInstanceAtomList)) {
            productFlowInstanceAtomList.stream().forEach(productFlowInstanceAtom -> {
                productFlowInstanceAtom.setId(BaseServiceUtils.getId());
                productFlowInstanceAtom.setFlowInstanceId(flowInstanceId);
                productFlowInstanceAtom.setFlowId(productFlowId);
                productFlowInstanceAtom.setCreateTime(date);
                productFlowInstanceAtom.setUpdateTime(date);
            });
            productFlowInstanceAtomMapper.batchInsert(productFlowInstanceAtomList);
        }

        // 添加产品流程附件
        ProductFlowInstanceAttachmentExample productFlowInstanceAttachmentExample = new ProductFlowInstanceAttachmentExample();
        productFlowInstanceAttachmentExample.createCriteria()
                .andFlowInstanceIdEqualTo(oldFlowInstanceId);
        List<ProductFlowInstanceAttachment> productFlowInstanceAttachmentList
                = productFlowInstanceAttachmentMapper.selectByExample(productFlowInstanceAttachmentExample);
        if (CollectionUtils.isNotEmpty(productFlowInstanceAttachmentList)) {
            productFlowInstanceAttachmentList.stream().forEach(productFlowInstanceAttachment -> {
                productFlowInstanceAttachment.setId(BaseServiceUtils.getId());
                productFlowInstanceAttachment.setFlowInstanceId(flowInstanceId);
                productFlowInstanceAttachment.setCreateTime(date);
                productFlowInstanceAttachment.setUpdateTime(date);
            });
            productFlowInstanceAttachmentMapper.batchInsert(productFlowInstanceAttachmentList);
        }

        // 添加产品流程配置
        ProductFlowInstanceConfigExample productFlowInstanceConfigExample = new ProductFlowInstanceConfigExample();
        productFlowInstanceConfigExample.createCriteria()
                .andFlowInstanceIdEqualTo(oldFlowInstanceId);
        List<ProductFlowInstanceConfig> productFlowInstanceConfigList = productFlowInstanceConfigMapper.selectByExample(productFlowInstanceConfigExample);
        if (CollectionUtils.isNotEmpty(productFlowInstanceConfigList)) {
            productFlowInstanceConfigList.stream().forEach(productFlowInstanceConfig -> {
                productFlowInstanceConfig.setId(BaseServiceUtils.getId());
                productFlowInstanceConfig.setFlowInstanceId(flowInstanceId);
                productFlowInstanceConfig.setFlowId(productFlowId);
                productFlowInstanceConfig.setCreateTime(date);
                productFlowInstanceConfig.setUpdateTime(date);
            });
            productFlowInstanceConfigMapper.batchInsert(productFlowInstanceConfigList);
        }

        // 添加产品流程sku
        ProductFlowInstanceSku productFlowInstanceSku = new ProductFlowInstanceSku();
        BeanUtils.copyProperties(oldProductFlowInstanceSku, productFlowInstanceSku);
        productFlowInstanceSku.setId(BaseServiceUtils.getId());
        productFlowInstanceSku.setFlowInstanceId(flowInstanceId);
        productFlowInstanceSku.setFlowId(productFlowId);
        productFlowInstanceSku.setShelfStatus(ProductShelfStatusEnum.SHELF_SUCCESS.code);
        productFlowInstanceSku.setCreateTime(date);
        productFlowInstanceSku.setUpdateTime(date);
        productFlowInstanceSkuMapper.insert(productFlowInstanceSku);

        // 添加产品流程spu
        ProductFlowInstanceSpu oldProductFlowInstanceSpu = productFlowInstanceSpuList.get(0);
        ProductFlowInstanceSpu productFlowInstanceSpu = new ProductFlowInstanceSpu();
        BeanUtils.copyProperties(oldProductFlowInstanceSpu, productFlowInstanceSpu);
        productFlowInstanceSpu.setId(BaseServiceUtils.getId());
        productFlowInstanceSpu.setFlowInstanceId(flowInstanceId);
        productFlowInstanceSpu.setFlowId(productFlowId);
        productFlowInstanceSpu.setCreateTime(date);
        productFlowInstanceSpu.setUpdateTime(date);
        productFlowInstanceSpuMapper.insert(productFlowInstanceSpu);

        //添加产品流程目录信息,找出已上架老的spu的导航目录信息并复制到新的sku对应的spu
        ProductFlowInstanceDirectoryExample example = new ProductFlowInstanceDirectoryExample().createCriteria().andFlowInstanceIdEqualTo(oldProductFlowInstanceSpu.getFlowInstanceId()).example();
        List<ProductFlowInstanceDirectory> productFlowInstanceDirectories = productFlowInstanceDirectoryMapper.selectByExample(example);
        if(CollectionUtils.isNotEmpty(productFlowInstanceDirectories)){
            //流程导航目录设置到product_flow_instance_directory
            for (ProductFlowInstanceDirectory productFlowInstanceDirectory : productFlowInstanceDirectories) {
                productFlowInstanceDirectory.setId(BaseServiceUtils.getId());
                productFlowInstanceDirectory.setFlowInstanceId(productFlowInstanceSpu.getFlowInstanceId());
                productFlowInstanceDirectory.setCreateTime(date);
                productFlowInstanceDirectory.setUpdateTime(date);
                productFlowInstanceDirectoryMapper.insert(productFlowInstanceDirectory);
            }
        }


        // 添加产品流程task
        addProductFlowTask(loginIfo4Redis, date, productFlow, productFlowStep, productFlowInstance);
// 添加日志
        String title = "";
        if ((int) productFlowType == ProductFlowTypeEnum.SALE_PRICE_UPDATE.code) {
            title = "【销售价变更】";
        } else if ((int) productFlowType == ProductFlowTypeEnum.SETTLE_PRICE_UPDATE.code) {
            title = "【结算价变更】";
        } else if ((int) productFlowType == ProductFlowTypeEnum.OTHER_INFO_UPDATE.code) {
            title = "【非价格信息变更】";
        } else if ((int) productFlowType == ProductFlowTypeEnum.ALL_INFO_UPDATE.code) {
            title = "【商品所有信息变更】";
        } else {
            title = "【无效的变更类型】";
        }

        String content = getSkuUpdateCreateLogContent(title, productFlowInstance.getFlowInstanceNumber(), oldProductFlowInstanceSpu.getSpuName(), oldProductFlowInstanceSku.getSkuName());
        if (StringUtils.isNotEmpty(content)) {
            logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.UPDATE.code, content, LogResultEnum.LOG_SUCESS.code, null);
        }

        deleteAllRedisCache(Constant.REDIS_KEY_MINI_PRODUCT_SEARCH);
        deleteAllRedisCache(Constant.REDIS_KEY_MINI_PRODUCT_SEARCH_COUNT);
        return baseAnswer;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseAnswer skuDelist(SkuInfoDelistParam skuInfoDelist, LoginIfo4Redis loginIfo4Redis, String ip) {
        Date now = new Date();
        BaseAnswer baseAnswer = new BaseAnswer();
        String spuCode = skuInfoDelist.getSpuCode();
        String flowInstanceIdParam = skuInfoDelist.getFlowInstanceId();
        ProductFlowInstanceSpuExample productFlowInstanceSpuExample = new ProductFlowInstanceSpuExample();
        productFlowInstanceSpuExample.createCriteria()
                .andSpuCodeEqualTo(spuCode)
                .andFlowInstanceIdEqualTo(flowInstanceIdParam);
        List<ProductFlowInstanceSpu> productFlowInstanceSpuList = productFlowInstanceSpuMapper.selectByExample(productFlowInstanceSpuExample);
        if (CollectionUtils.isEmpty(productFlowInstanceSpuList)) {

            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.OFF_SHELF.code,
                        "【sku下架】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "流程更新的spu信息不存在");
            });
            throw new BusinessException("10008", "流程更新的spu信息不存在");
        }
        ProductFlowInstanceSpu productFlowInstanceSpu = productFlowInstanceMapperExt.getShelfSpu(spuCode);
        if (productFlowInstanceSpu == null) {

            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.OFF_SHELF.code,
                        "【sku下架】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "spu流程不存在,无法下架sku");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "spu流程不存在,无法下架sku");
        }
        Integer productType = productFlowInstanceSpu.getProductType();
        Integer productStandard = productFlowInstanceSpu.getProductStandard();
        String flowId = productFlowInstanceSpu.getFlowId();
        //根据spu产品标准，判断走哪个下架流程
        if (!ProductStandardEnum.contains(productStandard)) {

            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.OFF_SHELF.code,
                        "【sku下架】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "产品标准错误");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "产品标准错误");
        }
        if (!ProductTypeEnum.contains(productType)) {

            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.OFF_SHELF.code,
                        "【sku下架】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "产品类别错误");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "产品类别错误");
        }
        Integer operateType = getOperateType(productType);
        //判断走哪个下架流程
        ProductFlowExample example = new ProductFlowExample().createCriteria().andFlowTypeEqualTo(ProductFlowTypeEnum.PRODUCT_OFF_SHELF.code).andOperateTypeEqualTo(operateType).andStatusEqualTo(0).example();
        List<ProductFlow> flowList = productFlowMapper.selectByExample(example);
        if (flowList.isEmpty()) {

            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.OFF_SHELF.code,
                        "【sku下架】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "找不到对应流程定义");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "找不到对应流程定义");
        }
        ProductFlow productFlow = flowList.get(0);
        ProductFlowStepExample flowStepExample = new ProductFlowStepExample().createCriteria().andFlowIdEqualTo(productFlow.getId()).andStepIndexEqualTo(1).example();
        List<ProductFlowStep> productFlowSteps = productFlowStepMapper.selectByExample(flowStepExample);
        if (productFlowSteps.isEmpty()) {
            executorService.execute(() -> {
                logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.OFF_SHELF.code,
                        "【sku下架】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "请找管理员配置流程");
            });
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "请找管理员配置流程");
        }

        ProductFlowStep flowStep = productFlowSteps.get(0);
        List lockList = new ArrayList();
        for (String skuParam : skuInfoDelist.getSkuCodeList()) {
            String skuCodeLock = PRODUCT_FLOW_SHELF_LOCK + skuParam;
            lockList.add(skuCodeLock);
        }
        return redisUtil.smartLock(lockList, () -> {
            ProductFlowInstanceSkuExample productFlowInstanceSkuExample = new ProductFlowInstanceSkuExample().createCriteria().andFlowInstanceIdEqualTo(flowInstanceIdParam).andSpuCodeEqualTo(spuCode).andSkuCodeIn(skuInfoDelist.getSkuCodeList()).andShelfStatusEqualTo(ProductShelfStatusEnum.OFF_SHELF_IN_PROGRESS.code).example();
            List<ProductFlowInstanceSku> skuList = productFlowInstanceSkuMapper.selectByExample(productFlowInstanceSkuExample);
            if (skuList != null && skuList.size() != 0) {

                executorService.execute(() -> {
                    logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.OFF_SHELF.code,
                            "【sku下架】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "sku已处于下架中");
                });
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "sku已处于下架中");
            }
            //使用分布式事务锁
//新建流程实例，包括流程实例的可编辑，可审核，可取消，可配置属性
            ProductFlowInstance productFlowInstance = addProductFlowInstance(loginIfo4Redis, now, productFlow, flowStep);
            String flowInstanceId = productFlowInstance.getId();
            //新建流程任务
            addProductFlowTask(loginIfo4Redis, now, productFlow, flowStep, productFlowInstance);
            // 添加产品流程附件
            ProductFlowInstanceAttachmentExample productFlowInstanceAttachmentExample = new ProductFlowInstanceAttachmentExample();
            productFlowInstanceAttachmentExample.createCriteria()
                    .andFlowInstanceIdEqualTo(flowInstanceIdParam);
            List<ProductFlowInstanceAttachment> productFlowInstanceAttachmentList
                    = productFlowInstanceAttachmentMapper.selectByExample(productFlowInstanceAttachmentExample);
            if (CollectionUtils.isNotEmpty(productFlowInstanceAttachmentList)) {
                productFlowInstanceAttachmentList.stream().forEach(productFlowInstanceAttachment -> {
                    productFlowInstanceAttachment.setId(BaseServiceUtils.getId());
                    productFlowInstanceAttachment.setFlowInstanceId(flowInstanceId);
                    productFlowInstanceAttachment.setCreateTime(now);
                    productFlowInstanceAttachment.setUpdateTime(now);
                });
                productFlowInstanceAttachmentMapper.batchInsert(productFlowInstanceAttachmentList);
            }

            // 添加产品流程配置
            ProductFlowInstanceConfigExample productFlowInstanceConfigExample = new ProductFlowInstanceConfigExample();
            productFlowInstanceConfigExample.createCriteria()
                    .andFlowInstanceIdEqualTo(flowInstanceIdParam);
            List<ProductFlowInstanceConfig> productFlowInstanceConfigList = productFlowInstanceConfigMapper.selectByExample(productFlowInstanceConfigExample);

            if (CollectionUtils.isNotEmpty(productFlowInstanceConfigList)) {
                productFlowInstanceConfigList.stream().forEach(productFlowInstanceConfig -> {
                    productFlowInstanceConfig.setId(BaseServiceUtils.getId());
                    productFlowInstanceConfig.setFlowInstanceId(flowInstanceId);
                    productFlowInstanceConfig.setFlowId(productFlow.getId());
                    productFlowInstanceConfig.setCreateTime(now);
                    productFlowInstanceConfig.setUpdateTime(now);
                });
                productFlowInstanceConfigMapper.batchInsert(productFlowInstanceConfigList);
            }
            productFlowInstanceSpu.setFlowId(productFlow.getId());
            //复制保存已上架的spu
            copyShelfSpuInfo(productFlowInstanceSpu, now, productFlowInstance);

            for (String skuParam : skuInfoDelist.getSkuCodeList()) {
                String skuCode = skuParam;

                //通过曾经上架过的SKU信息下架
                ProductFlowInstanceSkuExample skuExample = new ProductFlowInstanceSkuExample().createCriteria().andSkuCodeEqualTo(skuCode).andSpuCodeEqualTo(spuCode).andShelfStatusEqualTo(ProductShelfStatusEnum.SHELF_SUCCESS.code).example();
                List<ProductFlowInstanceSku> productFlowInstanceSkus = productFlowInstanceSkuMapper.selectByExample(skuExample);
                if (productFlowInstanceSkus.isEmpty()) {

                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.OFF_SHELF.code,
                                "【sku下架】", loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, "未找到该SKU的上架流程数据");
                    });
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "未找到该SKU的上架流程数据");
                }
                //复制曾经上架的sku信息
                ProductFlowInstanceSku productFlowInstanceSku = productFlowInstanceSkus.get(0);
                if (productFlowInstanceSku.getShelfStatus() != ProductShelfStatusEnum.SHELF_SUCCESS.code) {
                    String content = getLogContent("【sku下架】", productFlowInstance.getFlowInstanceNumber(), productFlowInstanceSpu.getSpuName(), productFlowInstanceSku.getSkuName(), null, null, null, null, null, null, null);

                    executorService.execute(() -> {
                        logService.recordOperateLogAsync(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.OFF_SHELF.code,
                                content, loginIfo4Redis.getUserId(), ip, LogResultEnum.LOG_FAIL.code, skuCode + "sku未上架");
                    });
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, skuCode + "sku未上架");
                }
                String originFlowInstanceId = productFlowInstanceSku.getFlowInstanceId();
                productFlowInstanceSku.setId(BaseServiceUtils.getId());
                productFlowInstanceSku.setFlowInstanceId(productFlowInstance.getId());
                productFlowInstanceSku.setSkuCode(skuCode);
                productFlowInstanceSku.setFlowId(productFlow.getId());
                productFlowInstanceSku.setShelfStatus(ProductShelfStatusEnum.OFF_SHELF_IN_PROGRESS.code);
                productFlowInstanceSku.setCreateTime(now);
                productFlowInstanceSku.setUpdateTime(now);
                productFlowInstanceSkuMapper.insert(productFlowInstanceSku);
                //复制atom信息
                List<ProductFlowInstanceAtom> productFlowInstanceAtoms = getFlowInstanceAtomList(originFlowInstanceId);
                for (ProductFlowInstanceAtom productFlowInstanceAtom : productFlowInstanceAtoms) {
                    productFlowInstanceAtom.setId(BaseServiceUtils.getId());
                    productFlowInstanceAtom.setFlowInstanceId(productFlowInstance.getId());
                    productFlowInstanceAtom.setFlowId(productFlow.getId());
                    productFlowInstanceAtom.setCreateTime(now);
                    productFlowInstanceAtom.setUpdateTime(now);
                    productFlowInstanceAtomMapper.insert(productFlowInstanceAtom);
                }

                //记录日志
                String content = getLogContent("【sku下架】", productFlowInstance.getFlowInstanceNumber(), productFlowInstanceSpu.getSpuName(), productFlowInstanceSku.getSkuName(), null, null, null, null, null, null, null);
                if (StringUtils.isNotEmpty(content)) {
                    logService.recordOperateLog(ModuleEnum.PRODUCT_MANAGE.code, ProductManageOperateEnum.OFF_SHELF.code, content, LogResultEnum.LOG_SUCESS.code, null);
                }
            }

            deleteAllRedisCache(Constant.REDIS_KEY_MINI_PRODUCT_SEARCH);
            deleteAllRedisCache(Constant.REDIS_KEY_MINI_PRODUCT_SEARCH_COUNT);
            return baseAnswer;
        });
    }


    @Override
    public BaseAnswer<List<ProductStandardListVO>> getProductStandardList() {
        ProductStandardEnum[] values = ProductStandardEnum.values();
        List<ProductStandardListVO> collect = Arrays.stream(values).map(data -> {
            ProductStandardListVO vo = new ProductStandardListVO();
            BeanUtils.copyProperties(data, vo);
            return vo;
        }).collect(Collectors.toList());
        return BaseAnswer.success(collect);
    }

    @Override
    public BaseAnswer<List<ProductTypeListVO>> getProductTypeList(Integer productStandardCode) {
        ProductTypeEnum[] typeEnums = null;
        if (productStandardCode.intValue() == ProductStandardEnum.PLAN.code.intValue()) {
            typeEnums = new ProductTypeEnum[3];
            //方案类只可选择 产品标准：省框、省内、DICT
            typeEnums[0] = ProductTypeEnum.PROVINCE_RANGE;
            typeEnums[1] = ProductTypeEnum.PROVINCE;
            typeEnums[2] = ProductTypeEnum.DICT;
        } else if (productStandardCode.intValue() == ProductStandardEnum.STANDARD.code.intValue()) {
            typeEnums = ProductTypeEnum.values();
        }
        List<ProductTypeListVO> collect = Arrays.stream(typeEnums).map(data -> {
            ProductTypeListVO vo = new ProductTypeListVO();
            BeanUtils.copyProperties(data, vo);
            return vo;
        }).collect(Collectors.toList());
        return BaseAnswer.success(collect);
    }

    @Override
    public BaseAnswer<List<ProductTypeListVO>> getProductOperateList() {
        ProductOperateTypeEnum[] typeEnums = null;
        typeEnums = ProductOperateTypeEnum.values();
        List<ProductTypeListVO> collect = Arrays.stream(typeEnums).map(data -> {
            ProductTypeListVO vo = new ProductTypeListVO();
            BeanUtils.copyProperties(data, vo);
            return vo;
        }).collect(Collectors.toList());
        return BaseAnswer.success(collect);
    }


    @Override
    public void updateProductFlowInstanceDirectoryName() {

        FileWriter writer = null;
        BufferedWriter bufferedWriter = null;
        try {
            writer = new FileWriter("E:\\newsql\\outDirectorySql.txt");
            bufferedWriter = new BufferedWriter(writer);
            //查询现有流程与目录关系信息
            List<ProductFlowInstanceDirectoryOnline> productFlowInstanceDirectoryOnlines = productFlowInstanceDirectoryOnlineMapper.selectByExample(new ProductFlowInstanceDirectoryOnlineExample());
            //查询到的流程目录遍历 在cut表查询相关目录名称信息
            for (ProductFlowInstanceDirectoryOnline productFlowInstanceDirectoryOnline : productFlowInstanceDirectoryOnlines) {
                String id = productFlowInstanceDirectoryOnline.getId();
                String firstDirectoryId = productFlowInstanceDirectoryOnline.getFirstDirectoryId();
                String secondDirectoryId = productFlowInstanceDirectoryOnline.getSecondDirectoryId();
                String thirdDirectoryId = productFlowInstanceDirectoryOnline.getThirdDirectoryId();
                String flowInstanceId = productFlowInstanceDirectoryOnline.getFlowInstanceId();
                String firstDirectoryName ="";
                String secondDirectoryName ="";
                String thirdDirectoryName ="";
                if (StringUtils.isNotEmpty(firstDirectoryId)){
                    //查询一级目录名称
                    ProductNavigationDirectoryCut productNavigationDirectoryCut = productNavigationDirectoryCutMapper.selectByPrimaryKey(firstDirectoryId);
                    if (Optional.ofNullable(productNavigationDirectoryCut).isPresent()){
                         firstDirectoryName = productNavigationDirectoryCut.getName();
                    }else {
                        ProductNavigationDirectoryOnline productNavigationDirectoryOnline = productNavigationDirectoryOnlineMapper.selectByPrimaryKey(firstDirectoryId);
                        if (Optional.ofNullable(productNavigationDirectoryOnline).isPresent()){
                             firstDirectoryName = productNavigationDirectoryOnline.getName();
                        }else {
                            log.info("流程目录关联的一级目录信息都没查询到flowInstanceId：{},firstDirectoryId：{}",flowInstanceId,firstDirectoryId);
                        }
                    }
                   //一级目录存在才有二级目录
                    if (StringUtils.isNotEmpty(secondDirectoryId)){
                        //查询二级目录名称
                        ProductNavigationDirectoryCut secondNavigationDirectoryCut = productNavigationDirectoryCutMapper.selectByPrimaryKey(secondDirectoryId);
                        if (Optional.ofNullable(secondNavigationDirectoryCut).isPresent()){
                            secondDirectoryName = secondNavigationDirectoryCut.getName();
                        }else {
                            ProductNavigationDirectoryOnline secondNavigationDirectoryOnline = productNavigationDirectoryOnlineMapper.selectByPrimaryKey(secondDirectoryId);
                            if (Optional.ofNullable(secondNavigationDirectoryOnline).isPresent()){
                                secondDirectoryName = secondNavigationDirectoryOnline.getName();
                            }else {
                                log.info("流程目录关联的二级目录信息都没查询到flowInstanceId：{},secondDirectoryId：{}",flowInstanceId,secondDirectoryId);
                            }
                        }
                        if (StringUtils.isNotEmpty(thirdDirectoryId)){
                            //查询三级目录名称
                            ProductNavigationDirectoryCut thirdNavigationDirectoryCut = productNavigationDirectoryCutMapper.selectByPrimaryKey(thirdDirectoryId);
                            if (Optional.ofNullable(thirdNavigationDirectoryCut).isPresent()){
                                thirdDirectoryName = thirdNavigationDirectoryCut.getName();
                            }else {
                                ProductNavigationDirectoryOnline thirdNavigationDirectoryOnline = productNavigationDirectoryOnlineMapper.selectByPrimaryKey(thirdDirectoryId);
                                if (Optional.ofNullable(thirdNavigationDirectoryOnline).isPresent()){
                                    thirdDirectoryName = thirdNavigationDirectoryOnline.getName();
                                }else {
                                    log.info("流程目录关联的三级目录信息都没查询到flowInstanceId：{},thirdDirectoryId：{}",flowInstanceId,thirdDirectoryId);
                                }
                            }
                        }
                    }
                }else {
                    log.info("流程还没关联目录，才开始发起流程getFlowInstanceId：{}",flowInstanceId);
                    continue;
                }
                if (StringUtils.isEmpty(firstDirectoryName) && StringUtils.isEmpty(secondDirectoryName) && StringUtils.isEmpty(thirdDirectoryName)){
                    log.info("流程目录，有目录信息单未查询到目录信息表的信息DirectoryIsNull：{}",flowInstanceId);
                    continue;
                }
                Map<String, String> map = new HashMap<>();
               if (StringUtils.isNotEmpty(firstDirectoryName)){
                   map.put("first_directory_name", firstDirectoryName);
               }
                if (StringUtils.isNotEmpty(secondDirectoryName)) {
                    map.put("second_directory_name", secondDirectoryName);
                }
                if (StringUtils.isNotEmpty(thirdDirectoryName)) {
                    map.put("third_directory_name", thirdDirectoryName);
                }
                String condition = "id = '" + id + "'";
                String updateQueryInfo = buildUpdateQuery("product_flow_instance_directory_online", map, condition);
                bufferedWriter.write(updateQueryInfo);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }finally {
            try {
                if (bufferedWriter != null) {
                    bufferedWriter.close();
                }
                if (writer != null) {
                    writer.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    @Override
    public void updateProductFlowInstanceDirectoryNameTest() {
        FileWriter writer = null;
        BufferedWriter bufferedWriter = null;
        try {
            writer = new FileWriter("E:\\newsql\\outDirectoryTestSql.txt");
            bufferedWriter = new BufferedWriter(writer);
            //查询现有流程与目录关系信息
            List<ProductFlowInstanceDirectory> productFlowInstanceDirectoryOnlines = productFlowInstanceDirectoryMapper.selectByExample(new ProductFlowInstanceDirectoryExample());
            //查询到的流程目录遍历 在cut表查询相关目录名称信息
            for (ProductFlowInstanceDirectory productFlowInstanceDirectory : productFlowInstanceDirectoryOnlines) {
                String id = productFlowInstanceDirectory.getId();
                String firstDirectoryId = productFlowInstanceDirectory.getFirstDirectoryId();
                String secondDirectoryId = productFlowInstanceDirectory.getSecondDirectoryId();
                String thirdDirectoryId = productFlowInstanceDirectory.getThirdDirectoryId();
                String flowInstanceId = productFlowInstanceDirectory.getFlowInstanceId();
                String firstDirectoryName ="";
                String secondDirectoryName ="";
                String thirdDirectoryName ="";
                if (StringUtils.isNotEmpty(firstDirectoryId)){
                    //查询一级目录名称
                    ProductNavigationDirectoryCut productNavigationDirectoryCut = productNavigationDirectoryCutMapper.selectByPrimaryKey(firstDirectoryId);
                    if (Optional.ofNullable(productNavigationDirectoryCut).isPresent()){
                        firstDirectoryName = productNavigationDirectoryCut.getName();
                    }else {
                        ProductNavigationDirectory productNavigationDirectoryOnline = productNavigationDirectoryMapper.selectByPrimaryKey(firstDirectoryId);
                        if (Optional.ofNullable(productNavigationDirectoryOnline).isPresent()){
                            firstDirectoryName = productNavigationDirectoryOnline.getName();
                        }else {
                            log.info("流程目录关联的一级目录信息都没查询到flowInstanceId：{},firstDirectoryId：{}",flowInstanceId,firstDirectoryId);
                        }
                    }
                    //一级目录存在才有二级目录
                    if (StringUtils.isNotEmpty(secondDirectoryId)){
                        //查询二级目录名称
                        ProductNavigationDirectoryCut secondNavigationDirectoryCut = productNavigationDirectoryCutMapper.selectByPrimaryKey(secondDirectoryId);
                        if (Optional.ofNullable(secondNavigationDirectoryCut).isPresent()){
                            secondDirectoryName = secondNavigationDirectoryCut.getName();
                        }else {
                            ProductNavigationDirectory secondNavigationDirectory = productNavigationDirectoryMapper.selectByPrimaryKey(secondDirectoryId);
                            if (Optional.ofNullable(secondNavigationDirectory).isPresent()){
                                secondDirectoryName = secondNavigationDirectory.getName();
                            }else {
                                log.info("流程目录关联的二级目录信息都没查询到flowInstanceId：{},secondDirectoryId：{}",flowInstanceId,secondDirectoryId);
                            }
                        }
                        if (StringUtils.isNotEmpty(thirdDirectoryId)){
                            //查询三级目录名称
                            ProductNavigationDirectoryCut thirdNavigationDirectoryCut = productNavigationDirectoryCutMapper.selectByPrimaryKey(thirdDirectoryId);
                            if (Optional.ofNullable(thirdNavigationDirectoryCut).isPresent()){
                                thirdDirectoryName = thirdNavigationDirectoryCut.getName();
                            }else {
                                ProductNavigationDirectory thirdNavigationDirectoryOnline = productNavigationDirectoryMapper.selectByPrimaryKey(thirdDirectoryId);
                                if (Optional.ofNullable(thirdNavigationDirectoryOnline).isPresent()){
                                    thirdDirectoryName = thirdNavigationDirectoryOnline.getName();
                                }else {
                                    log.info("流程目录关联的三级目录信息都没查询到flowInstanceId：{},thirdDirectoryId：{}",flowInstanceId,thirdDirectoryId);
                                }
                            }
                        }
                    }
                }else {
                    log.info("流程还没关联目录，才开始发起流程getFlowInstanceId：{}",flowInstanceId);
                    continue;
                }
                if (StringUtils.isEmpty(firstDirectoryName) && StringUtils.isEmpty(secondDirectoryName) && StringUtils.isEmpty(thirdDirectoryName)){
                    log.info("流程目录，有目录信息单未查询到目录信息表的信息DirectoryIsNull：{}",flowInstanceId);
                    continue;
                }
                Map<String, String> map = new HashMap<>();
                if (StringUtils.isNotEmpty(firstDirectoryName)){
                    map.put("first_directory_name", firstDirectoryName);
                }
                if (StringUtils.isNotEmpty(secondDirectoryName)) {
                    map.put("second_directory_name", secondDirectoryName);
                }
                if (StringUtils.isNotEmpty(thirdDirectoryName)) {
                    map.put("third_directory_name", thirdDirectoryName);
                }
                String condition = "id = '" + id + "'";
                String updateQueryInfo = buildUpdateQuery("product_flow_instance_directory", map, condition);
                bufferedWriter.write(updateQueryInfo);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }finally {
            try {
                if (bufferedWriter != null) {
                    bufferedWriter.close();
                }
                if (writer != null) {
                    writer.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 拼装update sql
     *
     * @param tableName
     * @param updateValues
     * @param condition
     * @return
     */
    public String buildUpdateQuery(String tableName, Map<String, String> updateValues, String condition) {
        StringBuilder builder = new StringBuilder();
        builder.append("update ").append(tableName).append(" set ");
        for (Map.Entry<String, String> entry : updateValues.entrySet()) {
            builder.append(entry.getKey()).append(" = '").append(entry.getValue()).append("', ");
        }
        builder.setLength(builder.length() - 2);
        builder.append(" where ").append(condition).append(";\n");
        return builder.toString();
    }



    public <K> void deleteAllRedisCache(K key) {
        Set<String> keys = redisTemplate.keys(key + "*");
        List<String> batchDeleteKeys = new ArrayList<>(keys);
        if (!batchDeleteKeys.isEmpty()) {
            redisTemplate.delete(batchDeleteKeys);
        }
    }

}

