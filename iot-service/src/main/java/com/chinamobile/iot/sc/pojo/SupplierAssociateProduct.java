package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 供应商关联商品表
 *
 * <AUTHOR>
public class SupplierAssociateProduct implements Serializable {
    /**
     * 主键ID
     *
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    private String id;

    /**
     * 供应商ID
     *
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    private String supplierId;

    /**
     * 商品ID（skuOfferingCode）
     *
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    private String productId;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    private Date createTime;

    /**
     * 删除时间
     *
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    private Date deleteTime;

    /**
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..supplier_associate_product.id
     *
     * @return the value of supply_chain..supplier_associate_product.id
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    public SupplierAssociateProduct withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..supplier_associate_product.id
     *
     * @param id the value for supply_chain..supplier_associate_product.id
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..supplier_associate_product.supplier_id
     *
     * @return the value of supply_chain..supplier_associate_product.supplier_id
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    public String getSupplierId() {
        return supplierId;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    public SupplierAssociateProduct withSupplierId(String supplierId) {
        this.setSupplierId(supplierId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..supplier_associate_product.supplier_id
     *
     * @param supplierId the value for supply_chain..supplier_associate_product.supplier_id
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    /**
     * This method returns the value of the database column supply_chain..supplier_associate_product.product_id
     *
     * @return the value of supply_chain..supplier_associate_product.product_id
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    public String getProductId() {
        return productId;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    public SupplierAssociateProduct withProductId(String productId) {
        this.setProductId(productId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..supplier_associate_product.product_id
     *
     * @param productId the value for supply_chain..supplier_associate_product.product_id
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    public void setProductId(String productId) {
        this.productId = productId;
    }

    /**
     * This method returns the value of the database column supply_chain..supplier_associate_product.create_time
     *
     * @return the value of supply_chain..supplier_associate_product.create_time
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    public SupplierAssociateProduct withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..supplier_associate_product.create_time
     *
     * @param createTime the value for supply_chain..supplier_associate_product.create_time
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..supplier_associate_product.delete_time
     *
     * @return the value of supply_chain..supplier_associate_product.delete_time
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    public Date getDeleteTime() {
        return deleteTime;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    public SupplierAssociateProduct withDeleteTime(Date deleteTime) {
        this.setDeleteTime(deleteTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..supplier_associate_product.delete_time
     *
     * @param deleteTime the value for supply_chain..supplier_associate_product.delete_time
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", supplierId=").append(supplierId);
        sb.append(", productId=").append(productId);
        sb.append(", createTime=").append(createTime);
        sb.append(", deleteTime=").append(deleteTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SupplierAssociateProduct other = (SupplierAssociateProduct) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSupplierId() == null ? other.getSupplierId() == null : this.getSupplierId().equals(other.getSupplierId()))
            && (this.getProductId() == null ? other.getProductId() == null : this.getProductId().equals(other.getProductId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getDeleteTime() == null ? other.getDeleteTime() == null : this.getDeleteTime().equals(other.getDeleteTime()));
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSupplierId() == null) ? 0 : getSupplierId().hashCode());
        result = prime * result + ((getProductId() == null) ? 0 : getProductId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getDeleteTime() == null) ? 0 : getDeleteTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Nov 22 16:41:53 CST 2022
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        supplierId("supplier_id", "supplierId", "VARCHAR", false),
        productId("product_id", "productId", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        deleteTime("delete_time", "deleteTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Tue Nov 22 16:41:53 CST 2022
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Nov 22 16:41:53 CST 2022
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Nov 22 16:41:53 CST 2022
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Nov 22 16:41:53 CST 2022
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Nov 22 16:41:53 CST 2022
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Nov 22 16:41:53 CST 2022
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Nov 22 16:41:53 CST 2022
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:41:53 CST 2022
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:41:53 CST 2022
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:41:53 CST 2022
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:41:53 CST 2022
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:41:53 CST 2022
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:41:53 CST 2022
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:41:53 CST 2022
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:41:53 CST 2022
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:41:53 CST 2022
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Nov 22 16:41:53 CST 2022
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}