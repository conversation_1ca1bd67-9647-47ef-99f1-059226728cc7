package com.chinamobile.iot.sc.controller.web;

import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.annotation.Auth;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.BaseConstant;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.config.ThreadExecutorConfig;
import com.chinamobile.iot.sc.constant.OrderStatusInnerEnum;
import com.chinamobile.iot.sc.dao.AtomOfferingInfoMapper;
import com.chinamobile.iot.sc.dao.Order2cAtomInfoMapper;
import com.chinamobile.iot.sc.dao.Order2cInfoMapper;
import com.chinamobile.iot.sc.dao.handle.OrderHandleMapper;
import com.chinamobile.iot.sc.enums.AtomOfferingClassEnum;
import com.chinamobile.iot.sc.enums.log.LogResultEnum;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.OrderManageOperateEnum;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.iot.sc.pojo.*;
import com.chinamobile.iot.sc.pojo.dto.CardRelationImportInfoDetailDTO;
import com.chinamobile.iot.sc.pojo.dto.ImportNumCardDetailLocationDTO;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryMainInfo;
import com.chinamobile.iot.sc.pojo.entity.DkcardxInventoryMainInfoExample;
import com.chinamobile.iot.sc.pojo.handle.OrderInfoDetailHandle;
import com.chinamobile.iot.sc.pojo.param.*;
import com.chinamobile.iot.sc.pojo.vo.*;
import com.chinamobile.iot.sc.service.CardRelationService;
import com.chinamobile.iot.sc.service.DkcardxInventoryMainInfoService;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.util.ExcelHeaderCheckUtil;
import com.chinamobile.iot.sc.util.ExcelUtils;
import com.chinamobile.iot.sc.util.SmsValidUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.chinamobile.iot.sc.common.BaseConstant.PARTNER_LORD_ROLE;
import static com.chinamobile.iot.sc.common.BaseConstant.PARTNER_ROLE;
import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.SUCCESS;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/1
 * @description 卡相关信息controller类
 */
@RestController
@Slf4j
@RequestMapping(value = "/osweb/card")
public class CardRelationController {

    @Resource
    private CardRelationService cardRelationService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private LogService logService;

    @Resource
    private DkcardxInventoryMainInfoService dkcardxInventoryMainInfoService;

    @Resource
    private Order2cAtomInfoMapper order2cAtomInfoMapper;

    @Resource
    private Order2cInfoMapper order2cInfoMapper;

    @Resource
    private AtomOfferingInfoMapper atomOfferingInfoMapper;

    @Resource
    private OrderHandleMapper orderHandleMapper;


    @Resource
    private RedisTemplate redisTemplate;

    /**
     * 导出卡相关信息
     *
     * @param cardRelationParam
     * @param response
     * @throws IOException
     */
    @GetMapping(value = "/exportCard")
    @Auth(authCode = {BaseConstant.IMPORT_EXPORT_CARD})
    public void exportCardRelation(CardRelationParam cardRelationParam,
                                   HttpServletResponse response) throws IOException {
        cardRelationService.exportCardRelation(cardRelationParam, response);
    }

    /**
     * 导出订单的卡相关信息
     * @param cardRelationParam
     * @param response
     * @throws Exception
     */
    @GetMapping(value = "/exportOrderCard")
    @Auth(authCode = {BaseConstant.IMPORT_EXPORT_CARD})
    public void exportOrderCard(@Valid CardRelationParam cardRelationParam,
                                @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                                   HttpServletResponse response) throws Exception {

        String exportPhone = cardRelationParam.getExportPhone();
        String exportMask = cardRelationParam.getExportMask() == null?"":cardRelationParam.getExportMask()+"";
        Boolean isAdmin = loginIfo4Redis.getIsAdmin();
        // 验证当前用户是否超管，及验证码是否正确
        SmsValidUtil.checkSmsValid(isAdmin,exportMask,exportPhone,redisTemplate);

        String roleType = loginIfo4Redis.getRoleType();
        String userId = loginIfo4Redis.getUserId();
        // 如果是省公司主合作伙伴、从合作伙伴及合作伙伴省管账号
        boolean isPartnerLordRole = PARTNER_LORD_ROLE.equals(roleType);
        boolean isPartnerRole = PARTNER_ROLE.equals(roleType);
        String isPartnerProvince = BaseConstant.PARTNER_PROVINCE;
        if (isPartnerLordRole || isPartnerRole || isPartnerProvince.equals(roleType)) {
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
            if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                log.warn("码号查询时主合作伙伴、从合作伙伴及合作伙伴省管账号错误:{}", JSONObject.toJSONString(data4UserBaseAnswer));
                logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.GOODS_ORDER.code,
                        "【导出】", LogResultEnum.LOG_FAIL.code, "主合作伙伴、从合作伙伴及合作伙伴省管账号错误");
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省管账号错误");
            }

            Data4User data4User = data4UserBaseAnswer.getData();
            String companyType = data4User.getCompanyType();
            boolean isProvinceUser = StringUtils.isNotEmpty(companyType) && "2".equals(companyType);
            String beId = cardRelationParam.getBeId();
            String location = cardRelationParam.getLocation();
            // 省公司合作伙伴主账号可查看、导出省级维度的码号信息
            if (isPartnerLordRole) {
                // 如果是省公司合作伙伴
                if (isProvinceUser){
                    if (StringUtils.isEmpty(beId)){
                        cardRelationParam.setBeId(data4User.getBeIdPartner());
                    }
                }else{ // 如果是普通主合作伙伴
                    BaseAnswer<List<String>> downUserIdsAnswer = userFeignClient.getDownUserIds(userId);
                    if (downUserIdsAnswer == null || !SUCCESS.getStateCode().equals(downUserIdsAnswer.getStateCode())) {
                        log.warn("码号查询时非省公司主合作伙伴账号错误:{}", JSONObject.toJSONString(downUserIdsAnswer));
                        logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                                OrderManageOperateEnum.GOODS_ORDER.code,
                                "【导出】", LogResultEnum.LOG_FAIL.code, "非省公司主合作伙伴账号错误");
                        throw new BusinessException("10004", "非省公司主合作伙伴账号错误");
                    }
                    List<String> cooperatorIdList = downUserIdsAnswer.getData();
                    if (CollectionUtils.isEmpty(cooperatorIdList)){
                        cooperatorIdList = new ArrayList<>();
                    }
                    cooperatorIdList.add(userId);
                    cardRelationParam.setCooperatorIdList(cooperatorIdList);
                }
            }

            // 合作伙伴省管账号、合作伙伴从账号可查看、导出地市维度的码号信息
            /*if ((isPartnerRole || isPartnerProvince.equals(roleType))
                    && isProvinceUser && StringUtils.isEmpty(location)) {*/
            if (isPartnerProvince.equals(roleType) && isProvinceUser && StringUtils.isEmpty(location)) {
                String userLocation = data4User.getLocationIdPartner();
                if (StringUtils.isEmpty(userLocation)){
                    throw new BusinessException("10004", "合作伙伴省管账号地市信息错误");
                }
                if ("all".equals(userLocation)) {
                    cardRelationParam.setBeId(data4User.getBeIdPartner());
                } else {
                    cardRelationParam.setLocation(userLocation);
                }

            }

            if (isPartnerRole){
                List<String> cooperatorIdList = new ArrayList<>();
                cooperatorIdList.add(userId);
                cardRelationParam.setCooperatorIdList(cooperatorIdList);
            }
        }else{
            Integer desensitizationStatus = cardRelationParam.getDesensitizationStatus();
            if (desensitizationStatus == null){
                throw new BusinessException("10004","脱敏选项不能为空");
            }

            if (desensitizationStatus != 0
                    && desensitizationStatus !=1){
                throw new BusinessException("10004","脱敏值只能0或1");
            }
        }
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String ip = requestAttr.getRequest().getHeader(Constant.IP);
        cardRelationParam.setIp(ip);
        ThreadExecutorConfig.executorService.execute(() -> {
            cardRelationService.exportOrderCard(cardRelationParam,loginIfo4Redis, response);
        });

        response.addHeader("stateCode", SUCCESS.getStateCode());
        response.addHeader("message", SUCCESS.getMessage());

    }

    /**
     * 获取导出订单的卡相关信息的数量
     * @param cardRelationParam
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping(value = "/getExportOrderCardCount")
    public BaseAnswer<Integer> getExportOrderCardCount(CardRelationParam cardRelationParam,
                                   @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        String roleType = loginIfo4Redis.getRoleType();
        String userId = loginIfo4Redis.getUserId();
        // 如果是省公司主合作伙伴、从合作伙伴及合作伙伴省管账号
        boolean isPartnerLordRole = PARTNER_LORD_ROLE.equals(roleType);
        boolean isPartnerRole = PARTNER_ROLE.equals(roleType);
        String isPartnerProvince = BaseConstant.PARTNER_PROVINCE;
        if (isPartnerLordRole || isPartnerRole || isPartnerProvince.equals(roleType)) {
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoById(userId);
            if (data4UserBaseAnswer == null || !SUCCESS.getStateCode().equals(data4UserBaseAnswer.getStateCode())) {
                log.warn("码号查询时主合作伙伴、从合作伙伴及合作伙伴省管账号错误:{}", JSONObject.toJSONString(data4UserBaseAnswer));
                logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                        OrderManageOperateEnum.GOODS_ORDER.code,
                        "【导出】", LogResultEnum.LOG_FAIL.code, "主合作伙伴、从合作伙伴及合作伙伴省管账号错误");
                throw new BusinessException("10004", "主合作伙伴、从合作伙伴及合作伙伴省管账号错误");
            }

            Data4User data4User = data4UserBaseAnswer.getData();
            String companyType = data4User.getCompanyType();
            boolean isProvinceUser = StringUtils.isNotEmpty(companyType) && "2".equals(companyType);
            String beId = cardRelationParam.getBeId();
            String location = cardRelationParam.getLocation();
            // 省公司合作伙伴主账号可查看、导出省级维度的码号信息
            if (isPartnerLordRole) {
                // 如果是省公司合作伙伴
                if (isProvinceUser){
                    if (StringUtils.isEmpty(beId)){
                        cardRelationParam.setBeId(data4User.getBeIdPartner());
                    }
                }else{ // 如果是普通主合作伙伴
                    BaseAnswer<List<String>> downUserIdsAnswer = userFeignClient.getDownUserIds(userId);
                    if (downUserIdsAnswer == null || !SUCCESS.getStateCode().equals(downUserIdsAnswer.getStateCode())) {
                        log.warn("码号查询时非省公司主合作伙伴账号错误:{}", JSONObject.toJSONString(downUserIdsAnswer));
                        logService.recordOperateLog(ModuleEnum.ORDER_MANAGE.code,
                                OrderManageOperateEnum.GOODS_ORDER.code,
                                "【导出】", LogResultEnum.LOG_FAIL.code, "非省公司主合作伙伴账号错误");
                        throw new BusinessException("10004", "非省公司主合作伙伴账号错误");
                    }
                    List<String> cooperatorIdList = downUserIdsAnswer.getData();
                    if (CollectionUtils.isEmpty(cooperatorIdList)){
                        cooperatorIdList = new ArrayList<>();
                    }
                    cooperatorIdList.add(userId);
                    cardRelationParam.setCooperatorIdList(cooperatorIdList);
                }
            }

            // 合作伙伴省管账号、合作伙伴从账号可查看、导出地市维度的码号信息
            /*if ((isPartnerRole || isPartnerProvince.equals(roleType))
                    && isProvinceUser && StringUtils.isEmpty(location)) {*/
            if (isPartnerProvince.equals(roleType) && isProvinceUser && StringUtils.isEmpty(location)) {
                String userLocation = data4User.getLocationIdPartner();
                if (StringUtils.isEmpty(userLocation)){
                    throw new BusinessException("10004", "合作伙伴省管账号地市信息错误");
                }
                if ("all".equals(userLocation)) {
                    cardRelationParam.setBeId(data4User.getBeIdPartner());
                } else {
                    cardRelationParam.setLocation(userLocation);
                }

            }

            if (isPartnerRole){
                List<String> cooperatorIdList = new ArrayList<>();
                cooperatorIdList.add(userId);
                cardRelationParam.setCooperatorIdList(cooperatorIdList);
            }
        }
        Integer exportOrderCardCount = null;
        try {
            exportOrderCardCount = cardRelationService.getExportOrderCardCount(cardRelationParam, loginIfo4Redis);
        } catch (Exception e) {
            log.info("获取导出订单的卡相关信息的数量:{}",e);
            throw new RuntimeException("获取导出订单的卡相关信息的数量异常");
        }
        return new BaseAnswer<Integer>().setData(exportOrderCardCount);
    }

    /**
     * 获取未使用过的卡信息
     * @return
     */
    @GetMapping(value = "/notUseCard")
    @Auth(authCode = {BaseConstant.ORDER_GET_ORDER})
    public BaseAnswer<List<NotUseCardVO>> listNotUseCard(@Valid NotUseCardParam notUseCardParam){
        BaseAnswer<List<NotUseCardVO>> baseAnswer = new BaseAnswer<>();
        List<NotUseCardVO> notUseCardVOList = cardRelationService.listNotUseCard(notUseCardParam);
        baseAnswer.setData(notUseCardVOList);
        return baseAnswer;
    }

    /**
     * 导入终端imei相关信息
     * @param file
     * @return
     */
    @PostMapping("/importCardRelationX")
    public void importCardRelationX(MultipartFile file,
                                    @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                                   HttpServletRequest request,
                                   HttpServletResponse response) {

        String userId = loginIfo4Redis.getUserId();
        String importRedisKey = "import_kx_".concat(userId);
        try {
            response.setHeader("content-type", "application/octet-stream");
            if (!ExcelUtils.suffixCheck(file.getOriginalFilename())) {
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("文件格式错误，只能是xlsx,xls类型", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }

            Boolean isUserImport = stringRedisTemplate.hasKey(importRedisKey);
            if (isUserImport != null && isUserImport){
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("您有一个任务执行中，请稍后再操作", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }

            InputStream fileInputStream = file.getInputStream();
            ThreadExecutorConfig.executorService.execute(() -> {
            try {
                cardRelationService.importCardRelationX(fileInputStream,loginIfo4Redis,request,response);
            } catch (Exception e) {
                stringRedisTemplate.delete(importRedisKey);
                log.error("批量导入x终端数据异常:{}",e);
                throw new BusinessException("10008","批量导入x终端数据异常");
            }
        });
            response.setHeader("statecode", "00000");
            response.setHeader("message", URLEncoder.encode("文件导入中，请稍后刷新查看", "UTF-8").replaceAll("\\+", "%20"));
        }catch (Exception e){
            stringRedisTemplate.delete(importRedisKey);
            log.error("批量导入x终端数据异常:{}",e);
            if (e instanceof BusinessException){
                throw new BusinessException("10008",((BusinessException)e).getMessage());
            }else{
                throw new BusinessException("10008","批量导入x终端数据异常");
            }
        }
    }

    /**
     * 导入终端imei相关信息到指定终端类型
     * @param importCardRelationToCardParam
     * @param file
     * @param loginIfo4Redis
     * @param request
     * @param response
     */
    @PostMapping("/importCardRelationXToCard")
    public void importCardRelationXToCard(@Valid ImportCardRelationToCardParam importCardRelationToCardParam,
                                    MultipartFile file,
                                    @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                                    HttpServletRequest request,
                                    HttpServletResponse response) {

        String userId = loginIfo4Redis.getUserId();
        String importRedisKey = "import_kx_card_".concat(userId);
        try {
            response.setHeader("content-type", "application/octet-stream");
            if (!ExcelUtils.suffixCheck(file.getOriginalFilename())) {
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("文件格式错误，只能是xlsx,xls类型", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }

            Boolean isUserImport = stringRedisTemplate.hasKey(importRedisKey);
            if (isUserImport != null && isUserImport){
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("有文件正在导入中，在该文件成功前请不要再次导入", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }

            String beId = importCardRelationToCardParam.getBeId();
            String deviceVersion = importCardRelationToCardParam.getDeviceVersion();
            String terminalType = importCardRelationToCardParam.getTerminalType();
            String custCode = importCardRelationToCardParam.getCustCode();
            String templateId = importCardRelationToCardParam.getTemplateId();
            DkcardxInventoryMainInfoExample dkcardxInventoryMainInfoExample = new DkcardxInventoryMainInfoExample();
            DkcardxInventoryMainInfoExample.Criteria mainInfoCriteria = dkcardxInventoryMainInfoExample.createCriteria()
                    .andBeIdEqualTo(beId)
                    .andDeviceVersionEqualTo(deviceVersion)
                    .andTerminalTypeEqualTo(terminalType);
            if (StringUtils.isEmpty(custCode)){
                mainInfoCriteria.andCustCodeIsNull();
            }else{
                mainInfoCriteria.andCustCodeEqualTo(custCode);
            }
            if (StringUtils.isEmpty(templateId)){
                mainInfoCriteria.andTemplateIdIsNull();
            }else{
                mainInfoCriteria.andTemplateIdEqualTo(templateId);
            }
            List<DkcardxInventoryMainInfo> mainInfoList = dkcardxInventoryMainInfoService.listDkcardxInventoryMainInfoByNeed(dkcardxInventoryMainInfoExample);
            if (CollectionUtils.isEmpty(mainInfoList)){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR.getStateCode(),"该终端类型的设备型号没有库存信息");
            }

            if (mainInfoList.size() > 1){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR.getStateCode(),"该终端类型的设备型号大于1个库存信息");
            }

            InputStream fileInputStream = file.getInputStream();
            ThreadExecutorConfig.executorService.execute(() -> {
                try {
                    cardRelationService.importCardRelationXToCard(importCardRelationToCardParam,
                            fileInputStream,mainInfoList.get(0),
                            loginIfo4Redis,request,response);
                } catch (Exception e) {
                    stringRedisTemplate.delete(importRedisKey);
                    log.error("批量导入x终端数据异常:{}",e);
                    throw new BusinessException("10008","批量导入x终端数据异常");
                }
            });
            response.setHeader("statecode", "00000");
            response.setHeader("message", URLEncoder.encode("文件导入中，请稍后刷新查看", "UTF-8").replaceAll("\\+", "%20"));
        }catch (Exception e){
            stringRedisTemplate.delete(importRedisKey);
            log.error("批量导入x终端数据异常:{}",e);
            if (e instanceof BusinessException){
                throw new BusinessException("10008",((BusinessException)e).getMessage());
            }else{
                throw new BusinessException("10008","批量导入x终端数据异常");
            }
        }
    }

    /**
     * 新增终端信息
     * @param addCardRelationParam
     * @param loginIfo4Redis
     * @return
     */
    @PostMapping(value = "/addCardRelation")
    public BaseAnswer addCardRelation(@Valid @RequestBody AddCardRelationParam addCardRelationParam,
                                      @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        BaseAnswer baseAnswer = cardRelationService.addCardRelationX(addCardRelationParam, loginIfo4Redis);
        return baseAnswer;
    }

    /**
     * 分页获取x终端信息
     * @param cardRelationXParam
     * @param loginIfo4Redis
     * @return
     */
    @GetMapping(value = "/pageCardRelationX")
    public BaseAnswer<PageData<CardRelationXVO>> pageCardRelationX(CardRelationXParam cardRelationXParam,
                                                         @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {

        BaseAnswer baseAnswer = new BaseAnswer();
        PageData<CardRelationXVO> pageData = cardRelationService.pageCardInfoX(cardRelationXParam, loginIfo4Redis);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }

    /**
     * 导出x终端信息
     * @param cardRelationXParam
     * @param loginIfo4Redis
     * @param response
     * @throws Exception
     */
    @GetMapping(value = "/exportCardRelationX")
    public void exportCardRelation(CardRelationXParam cardRelationXParam,
                                   @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                                   HttpServletResponse response) throws Exception {
        cardRelationService.exportCardRelationX(cardRelationXParam,loginIfo4Redis, response);
    }

    /**
     * 根据id删除未销售的x终端信息
     * @param id
     */
    @DeleteMapping(value = "/deleteNotSellCardRelationX")
    public BaseAnswer deleteNotSellCardRelationX(@RequestParam(value = "id")String id){
        cardRelationService.deleteNotSellCardRelationX(id);
        return new BaseAnswer();
    }

    /**
     * 批量导入删除未销售的x终端信息
     * @param file
     */
    @PostMapping(value = "/batchDeleteNotSellCardRelationX")
    public void batchDeleteNotSellCardRelationX(MultipartFile file,
                                                       @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                                                      HttpServletRequest request,
                                                      HttpServletResponse response) throws Exception{
        cardRelationService.batchDeleteNotSellCardRelationX(file,loginIfo4Redis,request,response);
    }

    /**
     * 获取x终端信息详情
     * @param id
     * @return
     */
    @GetMapping(value = "/getCardRelationXDetail")
    public BaseAnswer<CardRelationXDetailVO> getCardRelationXDetail(@RequestParam(value = "id")String id,
                                                                    @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis) {
        CardRelationXDetailVO cardRelationXDetail = cardRelationService.getCardRelationXDetail(id,loginIfo4Redis);
        return new BaseAnswer<CardRelationXDetailVO>().setData(cardRelationXDetail);
    }

    /**
     * (卡+X代客下单)根据imei或iccid或码号,查询纳入当前订单商品库存的x终端列表
     */
    @GetMapping("/orderProductCardRelationList")
    public BaseAnswer<List<OrderProductCardRelationListVO>> orderProductCardRelationList(@Valid OrderProductCardRelationListParam param){
        return cardRelationService.orderProductCardRelationList(param);
    }

    /**
     * （卡+X代客下单）交付号卡和终端
     */
    @PostMapping("/deliver")
    public BaseAnswer deliverCard(@Valid @RequestBody DeliverCardParam param,
                                  @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis){
        return cardRelationService.deliverCard(param,true,null,loginIfo4Redis);
    }

    /**
     * 导入代客下单卡+x订单的交付信息
     * @param file
     * @return
     */
    @PostMapping("/importCardXDeliver")
    public void importCardXDeliver(MultipartFile file,
                                    @RequestParam(value = "orderId")String orderId,
                                    @RequestParam(value = "cardType")String cardType,
                                    @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                                    HttpServletRequest request,
                                    HttpServletResponse response){
        String userId = loginIfo4Redis.getUserId();
        String importRedisKey = "import_kx_deliver_".concat(userId);

        try {
            response.setHeader("content-type", "application/octet-stream");
            if (!ExcelUtils.suffixCheck(file.getOriginalFilename())) {
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("文件格式错误，只能是xlsx,xls类型", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }


            Boolean isUserImport = stringRedisTemplate.hasKey(importRedisKey);
            if (isUserImport != null && isUserImport){
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("有文件正在导入中，在该文件成功前请不要再次导入", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }

            List<OrderInfoDetailHandle> orderInfoDetailHandleList = orderHandleMapper.selectOrderDetailByOrderId(orderId);
            if (CollectionUtils.isEmpty(orderInfoDetailHandleList)){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订单不存在");
            }

            OrderInfoDetailHandle orderInfoDetailHandle = orderInfoDetailHandleList.get(0);

            /*Order2cAtomInfo order2cAtomInfo = order2cAtomInfoMapper.selectByPrimaryKey(atomOrderId);
            if (order2cAtomInfo == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "原子订单不存在");
            }*/
            Integer orderStatus = orderInfoDetailHandle.getOrderStatus();
            String orderType = orderInfoDetailHandle.getOrderType();
            // 5类X产品类型支持个人客户预付费订购，包含：“合同履约”、“One NET独立服务”、
            // “标准产品(One NET）”、“One Park独立服务”和“标准产品（One Park）”
            if ("01".equals(orderType)) {
                if (OrderStatusInnerEnum.VALET_WAIT_DELIVER.getStatus().intValue() != orderStatus.intValue()) {
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "只有 " + OrderStatusInnerEnum.VALET_WAIT_DELIVER.getMessage() + " 状态的自主下单预付费订单才能交付");
                }
            } else {
                if (OrderStatusInnerEnum.VALET_WAIT_DELIVER.getStatus().intValue() != orderStatus.intValue()) {
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "只有 " + OrderStatusInnerEnum.VALET_WAIT_DELIVER.getMessage() + " 状态的卡+X代客下单订单才能交付");
                }
            }
            Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(orderId);
            if (order2cInfo == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订单不存在");
            }

            Order2cAtomInfoExample atomInfoExample = new Order2cAtomInfoExample();
            atomInfoExample.createCriteria()
                    .andOrderIdEqualTo(orderId)
                    .andAtomOfferingClassEqualTo(AtomOfferingClassEnum.X.getAtomOfferingClass());
            List<Order2cAtomInfo> order2cAtomInfoList = order2cAtomInfoMapper.selectByExample(atomInfoExample);
            if (CollectionUtils.isEmpty(order2cAtomInfoList)){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "原子订单不存在");
            }

            List<AtomOfferingInfo> atomOfferingInfoList = new ArrayList<>();
            order2cAtomInfoList.stream().forEach(order2cAtomInfo1 -> {
                AtomOfferingInfoExample atomExample = new AtomOfferingInfoExample().createCriteria()
                        .andSpuCodeEqualTo(order2cAtomInfo1.getSpuOfferingCode())
                        .andSkuCodeEqualTo(order2cAtomInfo1.getSkuOfferingCode())
                        .andOfferingCodeEqualTo(order2cAtomInfo1.getAtomOfferingCode())
                        .andDeleteTimeIsNull()
                        .example();
                List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper.selectByExample(atomExample);
                if (CollectionUtils.isEmpty(atomOfferingInfos)) {
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "原子商品不存在");
                }

                atomOfferingInfoList.add(atomOfferingInfos.get(0));
            });


            InputStream fileInputStream = file.getInputStream();
            ThreadExecutorConfig.executorService.execute(() -> {
            try {
                cardRelationService.importCardXDeliver(fileInputStream,orderId,cardType,
                        atomOfferingInfoList,order2cAtomInfoList,order2cInfo,loginIfo4Redis,
                        request,response);
            } catch (Exception e) {
                stringRedisTemplate.delete(importRedisKey);
                log.error("批量导入x终端数据交付异常:{}",e);
                throw new BusinessException("10008","批量导入x终端数据交付异常");
            }
        });

            response.setHeader("statecode", "00000");
            response.setHeader("message", URLEncoder.encode("文件导入中，请稍后刷新查看", "UTF-8").replaceAll("\\+", "%20"));
        }catch (Exception e){
            stringRedisTemplate.delete(importRedisKey);
            log.error("批量导入x终端交付数据异常:{}",e);
            if (e instanceof BusinessException){
                throw new BusinessException("10008",((BusinessException)e).getMessage());
            }else{
                throw new BusinessException("10008","批量导入x终端交付数据异常");
            }
        }

    }

    /**
     * 单个批量交付
     * @param file
     * @param orderId
     * @param cardType
     * @param loginIfo4Redis
     * @param request
     * @param response
     */
    @PostMapping("/importCardXDeliverSingle")
    public void importCardXDeliverSingle(MultipartFile file,
                                   @RequestParam(value = "orderId")String orderId,
                                   @RequestParam(value = "cardType")String cardType,
                                   @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                                   HttpServletRequest request,
                                   HttpServletResponse response){
        String userId = loginIfo4Redis.getUserId();
        String importRedisKey = "import_kx_deliver_".concat(userId);

        try {
            response.setHeader("content-type", "application/octet-stream");
            if (!ExcelUtils.suffixCheck(file.getOriginalFilename())) {
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("文件格式错误，只能是xlsx,xls类型", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }


            Boolean isUserImport = stringRedisTemplate.hasKey(importRedisKey);
            if (isUserImport != null && isUserImport){
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("有文件正在导入中，在该文件成功前请不要再次导入", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }

            List<OrderInfoDetailHandle> orderInfoDetailHandleList = orderHandleMapper.selectOrderDetailByOrderId(orderId);
            if (CollectionUtils.isEmpty(orderInfoDetailHandleList)){
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订单不存在");
            }

            OrderInfoDetailHandle orderInfoDetailHandle = orderInfoDetailHandleList.get(0);
            String atomOrderId = orderInfoDetailHandle.getId();

            Order2cAtomInfo order2cAtomInfo = order2cAtomInfoMapper.selectByPrimaryKey(atomOrderId);
            if (order2cAtomInfo == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "原子订单不存在");
            }
            Integer orderStatus = order2cAtomInfo.getOrderStatus();
            String orderType = order2cAtomInfo.getOrderType();
            // 5类X产品类型支持个人客户预付费订购，包含：“合同履约”、“One NET独立服务”、
            // “标准产品(One NET）”、“One Park独立服务”和“标准产品（One Park）”
            if ("01".equals(orderType)) {
                if (OrderStatusInnerEnum.VALET_WAIT_DELIVER.getStatus().intValue() != orderStatus.intValue()) {
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "只有 " + OrderStatusInnerEnum.VALET_WAIT_DELIVER.getMessage() + " 状态的自主下单预付费订单才能交付");
                }
            } else {
                if (OrderStatusInnerEnum.VALET_WAIT_DELIVER.getStatus().intValue() != orderStatus.intValue()) {
                    throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "只有 " + OrderStatusInnerEnum.VALET_WAIT_DELIVER.getMessage() + " 状态的卡+X代客下单订单才能交付");
                }
            }
            /*Order2cInfo order2cInfo = order2cInfoMapper.selectByPrimaryKey(order2cAtomInfo.getOrderId());
            if (order2cInfo == null) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "订单不存在");
            }*/

            AtomOfferingInfoExample atomExample = new AtomOfferingInfoExample().createCriteria()
                    .andSpuCodeEqualTo(order2cAtomInfo.getSpuOfferingCode())
                    .andSkuCodeEqualTo(order2cAtomInfo.getSkuOfferingCode())
                    .andOfferingCodeEqualTo(order2cAtomInfo.getAtomOfferingCode())
                    .andDeleteTimeIsNull()
                    .example();
            List<AtomOfferingInfo> atomOfferingInfos = atomOfferingInfoMapper.selectByExample(atomExample);
            if (CollectionUtils.isEmpty(atomOfferingInfos)) {
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR, "原子商品不存在");
            }

            AtomOfferingInfo atomOfferingInfo = atomOfferingInfos.get(0);

            stringRedisTemplate.opsForValue().set(importRedisKey,importRedisKey,12,TimeUnit.HOURS);

            InputStream fileInputStream = file.getInputStream();
            ThreadExecutorConfig.executorService.execute(() -> {
            try {
                cardRelationService.importCardXDeliverSingle(fileInputStream,orderId,cardType,
                        atomOfferingInfo,order2cAtomInfo,loginIfo4Redis,
                        request,response,importRedisKey);
            } catch (Exception e) {
                stringRedisTemplate.delete(importRedisKey);
                log.error("批量导入x终端数据交付异常:{}",e);
                throw new BusinessException("10008","批量导入x终端数据交付异常");
            }
        });

            response.setHeader("statecode", "00000");
            response.setHeader("message", URLEncoder.encode("文件导入中，请稍后刷新查看", "UTF-8").replaceAll("\\+", "%20"));
        }catch (Exception e){
            stringRedisTemplate.delete(importRedisKey);
            log.error("批量导入x终端交付数据异常:{}",e);
            if (e instanceof BusinessException){
                throw new BusinessException("10008",((BusinessException)e).getMessage());
            }else{
                throw new BusinessException("10008","批量导入x终端交付数据异常");
            }
        }

    }

    /**
     * （卡+X代客下单）获取应该交付的数量及对应区域
     */
    @GetMapping("/cardRalationCountByArea")
    public BaseAnswer<List<CardRalationCountByAreaVO>> cardRalationCountByArea(@RequestParam String orderId){
        return cardRelationService.cardRalationCountByArea(orderId);
    }

    /**
     * 获取导入批次终端明细
     * @param cardRelationImportInfoDetailParam
     * @return
     */
    @GetMapping(value = "/listCardRelationImportInfoDetail")
    public BaseAnswer<PageData<CardRelationImportInfoDetailDTO>> listCardRelationImportInfoDetail(@Valid CardRelationImportInfoDetailParam cardRelationImportInfoDetailParam){
        BaseAnswer<PageData<CardRelationImportInfoDetailDTO>> baseAnswer = new BaseAnswer<>();
        PageData<CardRelationImportInfoDetailDTO> pageData  = cardRelationService.listCardRelationImportInfoDetail(cardRelationImportInfoDetailParam);
        baseAnswer.setData(pageData);
        return baseAnswer;
    }

    /**
     * 获取导入批次x终端详情地市下拉列表
     * @param cardRelationImportInfoDetailParam
     * @return
     */
    @GetMapping(value = "/listImportNumXDetailLocation")
    public BaseAnswer<List<ImportNumCardDetailLocationDTO>> listImportNumXDetailLocation(@Valid CardRelationImportInfoDetailParam cardRelationImportInfoDetailParam){
        BaseAnswer<List<ImportNumCardDetailLocationDTO>> baseAnswer = new BaseAnswer<>();
        List<ImportNumCardDetailLocationDTO> detailLocationDTOList = cardRelationService.listImportNumXDetailLocation(cardRelationImportInfoDetailParam);
        baseAnswer.setData(detailLocationDTOList);
        return baseAnswer;
    }

    /**
     * 卡状态（生命周期）实时查询
     * @param queryTempIccidLifeStatusParam
     * @return
     */
    @GetMapping(value = "/queryTempIccidLifeStatus")
    public BaseAnswer queryTempIccidLifeStatus(@Valid QueryTempIccidLifeStatusParam queryTempIccidLifeStatusParam){
        return cardRelationService.queryTempIccidLifeStatus(queryTempIccidLifeStatusParam);
    }


    /**
     * 手动处理卡+X未销售存量数据到库存相关表中
     * @param id
     * @return
     */
    @GetMapping(value = "/handleExistCardRelationToInventory")
    public BaseAnswer handleExistCardRelationToInventory(@RequestParam(value = "id",required = false) String id){
        cardRelationService.handleExistCardRelationToInventory(id);
        return new BaseAnswer();
    }

    /**
     * 手动处理卡+X已销售存量数据到库存相关表中
     * @param id
     * @return
     */
    @GetMapping(value = "/handleExistSellOutCardRelationToInventory")
    public BaseAnswer handleExistSellOutCardRelationToInventory(@RequestParam(value = "id",required = false) String id){
        cardRelationService.handleExistSellOutCardRelationToInventory(id);
        return new BaseAnswer();
    }

    /**
     * 手动处理卡+X存量预占相关信息到库存相关表中
     * @param inventoryId
     * @return
     */
    @GetMapping(value = "/handleExistInventoryToNewInventory")
    public BaseAnswer handleExistInventoryToNewInventory(@RequestParam(value = "inventoryId",required = false) String inventoryId){
        cardRelationService.handleExistInventoryToNewInventory(inventoryId);
        return new BaseAnswer();
    }


    @PostMapping(value = "/handleInventoryMisError")
    public BaseAnswer handleInventoryMisError(@RequestParam(value = "dk_detail_id",required = false) String dkDetailId,
                                              @RequestParam(value = "dk_atom_id",required = false) String dkAtomId,
                                              @RequestParam(value = "reserve_num", required = false) Long reserveNum){
        cardRelationService.handleInventoryMisError(dkDetailId, dkAtomId, reserveNum);
        return new BaseAnswer();
    }

    /**
     * 处理没有导入批次的卡+X终端
     * @return
     */
    @PostMapping(value = "/handleNoImportNumCard")
    public BaseAnswer handleNoImportNumCard(){
        cardRelationService.handleNoImportNumCard();
        return new BaseAnswer();
    }

    /*
    * 处理原子商品信息库存
     * @param atomId
     * @param reserveNum
     */
    @PostMapping(value = "/handleInventoryMisErrorAtomInfo")
    public BaseAnswer handleInventoryMisErrorAtomInfoMessage(@RequestParam(value = "atomId") String atomId,
                                                             @RequestParam(value = "reserveNum", required = false) Long reserveNum) {
        cardRelationService.handleInventoryMisErrorAtomInfo(atomId, reserveNum);
        return new BaseAnswer();
    }

    /**
     * 处理终端原子库存信息表预占
     * @param dkAtomId
     * @param reserveNum
     */
    @PostMapping(value = "/handleInventoryMisErrorAtomCard")
    public BaseAnswer handleInventoryMisErrorAtomCardMessage(@RequestParam(value = "dkAtomId") String dkAtomId,
                                              @RequestParam(value = "reserveNum") Long reserveNum){
        cardRelationService.handleInventoryMisErrorAtomCard(dkAtomId, reserveNum);
        return new BaseAnswer();
    }

    /**
     * 处理终端详情信息表预占
     * @param dkDetailId
     * @param reserveNum
     */
    @PostMapping(value = "/handleInventoryMisErrorCardDetail")
    public BaseAnswer handleInventoryMisErrorCardDetailMessage(@RequestParam(value = "dkDetailId") String dkDetailId,
                                              @RequestParam(value = "reserveNum") Long reserveNum){
        cardRelationService.handleInventoryMisErrorCardDetail(dkDetailId, reserveNum);
        return new BaseAnswer();
    }

    /**
     *
     * 处理错误导入的imei
     * @param file
     * @param request
     * @param response
     * @throws Exception
     */
    @PostMapping("/handleErrorImei")
    public void handleErrorImei(@RequestParam MultipartFile file,
                             HttpServletRequest request,
                             HttpServletResponse response) throws Exception{

            InputStream fileInputStream = file.getInputStream();
            cardRelationService.handleErrorImei(fileInputStream,request,response);
            response.setHeader("statecode", "00000");
            response.setHeader("message", URLEncoder.encode("文件导入中，请稍后刷新查看", "UTF-8").replaceAll("\\+", "%20"));

    }

    /**
     * 处理库存详情不在库存原子表的数据
     * @param dkcardxInventoryAtomInfo
     * @return
     */
    @PostMapping(value = "/handleInventoryNotInAtomInfo")
    public BaseAnswer handleInventoryNotInAtomInfo(@RequestBody DkcardxInventoryAtomInfo dkcardxInventoryAtomInfo) {
        cardRelationService.handleInventoryNotInAtomInfo(dkcardxInventoryAtomInfo);
        return new BaseAnswer();
    }


    /**
     * 批量交付
     * @param file
     * @param response
     */
    @PostMapping("/batchDeliver")
    public void batchDeliver(@RequestParam MultipartFile file,
                             @RequestAttribute(Constant.ATTR_KEY_AUTH_INFO) LoginIfo4Redis loginIfo4Redis,
                             HttpServletResponse response){
        String userId = loginIfo4Redis.getUserId();

        String importRedisKey = "import_kx_order_deliver_".concat(userId);

        List<String> batchDeliverHeaderList = Arrays.asList("订单号,设备型号,imei/sn,码号".split(","));


        try {
            if (!ExcelUtils.suffixCheck(file.getOriginalFilename())) {
                //用于前端区分99999，88888是不下载文件，只展示message
                response.setHeader("statecode", "88888");
                response.setHeader("message", URLEncoder.encode("文件格式错误，只能是xlsx,xls类型", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }
            //校验表头
            if(!ExcelHeaderCheckUtil.checkExcelHeaders(file,batchDeliverHeaderList,0)){
                response.setHeader("message", URLEncoder.encode("文件模板错误", "UTF-8").replaceAll("\\+", "%20"));
                response.setHeader("statecode", "88888");
                return;
            }

            Boolean isUserImport = stringRedisTemplate.hasKey(importRedisKey);
            if (isUserImport != null && isUserImport){
                response.setHeader("statecode", "99999");
                response.setHeader("message", URLEncoder.encode("有文件正在导入中，在该文件成功前请不要再次导入", "UTF-8").replaceAll("\\+", "%20"));
                return;
            }

            stringRedisTemplate.opsForValue().set(importRedisKey,importRedisKey,12, TimeUnit.HOURS);

            InputStream fileInputStream = file.getInputStream();
            ThreadExecutorConfig.executorService.execute(() -> {
                try {
                    cardRelationService.batchDeliver(fileInputStream,loginIfo4Redis,response,importRedisKey);
                } catch (Exception e) {
                    stringRedisTemplate.delete(importRedisKey);
                    log.error("批量导入x终端数据交付异常:{}",e);
                    throw new BusinessException("10008","批量导入x终端数据交付异常");
                }
            });
            response.setHeader("statecode", "00000");
            response.setHeader("message", URLEncoder.encode("文件导入中，请稍后刷新查看", "UTF-8").replaceAll("\\+", "%20"));
        } catch (Exception e) {
            stringRedisTemplate.delete(importRedisKey);
            log.error("批量导入订单交付异常:{}",e);
            if (e instanceof BusinessException){
                throw new BusinessException("10008",((BusinessException)e).getMessage());
            }else{
                throw new BusinessException("10008","批量导入订单交付异常");
            }
        }
    }

}
