package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 售后订单关联服务商品表
 *
 * <AUTHOR>
public class AfterMarketOrder2cOfferingInfo implements Serializable {
    /**
     * 售后订单关联服务商品表主键
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String id;

    /**
     * 售后订单ID
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String serviceOrderId;

    /**
     * 售后服务类型,1、属地化服务；2、铁通增值服务；3、铁通增值服务（卡+X专用）
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private Integer afterMarketType;

    /**
     * 售后商品编码
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String afterMarketCode;

    /**
     * 订购数量
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private Long quantity;

    /**
     * 当前派单人员姓名
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String presentSendOrderName;

    /**
     * 当前派单人员电话
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String presentSendOrderPhone;

    /**
     * 派单人员所属公司
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String sendOrderCompany;

    /**
     * 关联派单人员id
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String userId;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private Date updateTime;

    /**
     * 售后服务商品名称
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String afterMarketName;

    /**
     * 售后服务商品结算单价
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String afterMarketSettlePrice;

    /**
     * 接单方式 1--OS接单；2--省内接单
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private Integer orderTakeType;

    /**
     * 合作伙伴主账号用户id
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String adminCooperatorId;

    /**
     * 装维管理员用户id
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String installManagerId;

    /**
     * 省侧装维平台
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String provinceInstallPlatform;

    /**
     * 派单时间
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private Date sendOrderTime;

    /**
     * 交付时间
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private Date deliverTime;

    /**
     * 规格商品编码
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String skuOfferingCode;

    /**
     * 原子商品编码
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String atomOfferingCode;

    /**
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String spuOfferingCode;

    /**
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String spuOfferingClass;

    /**
     * 规格商品名称
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String skuOfferingName;

    /**
     * 原子商品名称
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String atomOfferingName;

    /**
     * 合作伙伴主账号名称
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String adminCooperatorName;

    /**
     * 交付失败信息
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String deliverFailedMsg;

    /**
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String afterMarketVersion;

    /**
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String spuOfferingVersion;

    /**
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String skuOfferingVersion;

    /**
     *
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private String atomOfferingVersion;

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.id
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.id
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.id
     *
     * @param id the value for supply_chain..after_market_order_2c_offering_info.id
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.service_order_id
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.service_order_id
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getServiceOrderId() {
        return serviceOrderId;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withServiceOrderId(String serviceOrderId) {
        this.setServiceOrderId(serviceOrderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.service_order_id
     *
     * @param serviceOrderId the value for supply_chain..after_market_order_2c_offering_info.service_order_id
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setServiceOrderId(String serviceOrderId) {
        this.serviceOrderId = serviceOrderId;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.after_market_type
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.after_market_type
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public Integer getAfterMarketType() {
        return afterMarketType;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withAfterMarketType(Integer afterMarketType) {
        this.setAfterMarketType(afterMarketType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.after_market_type
     *
     * @param afterMarketType the value for supply_chain..after_market_order_2c_offering_info.after_market_type
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setAfterMarketType(Integer afterMarketType) {
        this.afterMarketType = afterMarketType;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.after_market_code
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.after_market_code
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getAfterMarketCode() {
        return afterMarketCode;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withAfterMarketCode(String afterMarketCode) {
        this.setAfterMarketCode(afterMarketCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.after_market_code
     *
     * @param afterMarketCode the value for supply_chain..after_market_order_2c_offering_info.after_market_code
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setAfterMarketCode(String afterMarketCode) {
        this.afterMarketCode = afterMarketCode;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.quantity
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.quantity
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public Long getQuantity() {
        return quantity;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withQuantity(Long quantity) {
        this.setQuantity(quantity);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.quantity
     *
     * @param quantity the value for supply_chain..after_market_order_2c_offering_info.quantity
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.present_send_order_name
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.present_send_order_name
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getPresentSendOrderName() {
        return presentSendOrderName;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withPresentSendOrderName(String presentSendOrderName) {
        this.setPresentSendOrderName(presentSendOrderName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.present_send_order_name
     *
     * @param presentSendOrderName the value for supply_chain..after_market_order_2c_offering_info.present_send_order_name
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setPresentSendOrderName(String presentSendOrderName) {
        this.presentSendOrderName = presentSendOrderName;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.present_send_order_phone
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.present_send_order_phone
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getPresentSendOrderPhone() {
        return presentSendOrderPhone;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withPresentSendOrderPhone(String presentSendOrderPhone) {
        this.setPresentSendOrderPhone(presentSendOrderPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.present_send_order_phone
     *
     * @param presentSendOrderPhone the value for supply_chain..after_market_order_2c_offering_info.present_send_order_phone
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setPresentSendOrderPhone(String presentSendOrderPhone) {
        this.presentSendOrderPhone = presentSendOrderPhone;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.send_order_company
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.send_order_company
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getSendOrderCompany() {
        return sendOrderCompany;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withSendOrderCompany(String sendOrderCompany) {
        this.setSendOrderCompany(sendOrderCompany);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.send_order_company
     *
     * @param sendOrderCompany the value for supply_chain..after_market_order_2c_offering_info.send_order_company
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setSendOrderCompany(String sendOrderCompany) {
        this.sendOrderCompany = sendOrderCompany;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.user_id
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.user_id
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withUserId(String userId) {
        this.setUserId(userId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.user_id
     *
     * @param userId the value for supply_chain..after_market_order_2c_offering_info.user_id
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.create_time
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.create_time
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.create_time
     *
     * @param createTime the value for supply_chain..after_market_order_2c_offering_info.create_time
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.update_time
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.update_time
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.update_time
     *
     * @param updateTime the value for supply_chain..after_market_order_2c_offering_info.update_time
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.after_market_name
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.after_market_name
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getAfterMarketName() {
        return afterMarketName;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withAfterMarketName(String afterMarketName) {
        this.setAfterMarketName(afterMarketName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.after_market_name
     *
     * @param afterMarketName the value for supply_chain..after_market_order_2c_offering_info.after_market_name
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setAfterMarketName(String afterMarketName) {
        this.afterMarketName = afterMarketName;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.after_market_settle_price
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.after_market_settle_price
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getAfterMarketSettlePrice() {
        return afterMarketSettlePrice;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withAfterMarketSettlePrice(String afterMarketSettlePrice) {
        this.setAfterMarketSettlePrice(afterMarketSettlePrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.after_market_settle_price
     *
     * @param afterMarketSettlePrice the value for supply_chain..after_market_order_2c_offering_info.after_market_settle_price
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setAfterMarketSettlePrice(String afterMarketSettlePrice) {
        this.afterMarketSettlePrice = afterMarketSettlePrice;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.order_take_type
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.order_take_type
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public Integer getOrderTakeType() {
        return orderTakeType;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withOrderTakeType(Integer orderTakeType) {
        this.setOrderTakeType(orderTakeType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.order_take_type
     *
     * @param orderTakeType the value for supply_chain..after_market_order_2c_offering_info.order_take_type
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setOrderTakeType(Integer orderTakeType) {
        this.orderTakeType = orderTakeType;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.admin_cooperator_id
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.admin_cooperator_id
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getAdminCooperatorId() {
        return adminCooperatorId;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withAdminCooperatorId(String adminCooperatorId) {
        this.setAdminCooperatorId(adminCooperatorId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.admin_cooperator_id
     *
     * @param adminCooperatorId the value for supply_chain..after_market_order_2c_offering_info.admin_cooperator_id
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setAdminCooperatorId(String adminCooperatorId) {
        this.adminCooperatorId = adminCooperatorId;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.install_manager_id
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.install_manager_id
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getInstallManagerId() {
        return installManagerId;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withInstallManagerId(String installManagerId) {
        this.setInstallManagerId(installManagerId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.install_manager_id
     *
     * @param installManagerId the value for supply_chain..after_market_order_2c_offering_info.install_manager_id
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setInstallManagerId(String installManagerId) {
        this.installManagerId = installManagerId;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.province_install_platform
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.province_install_platform
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getProvinceInstallPlatform() {
        return provinceInstallPlatform;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withProvinceInstallPlatform(String provinceInstallPlatform) {
        this.setProvinceInstallPlatform(provinceInstallPlatform);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.province_install_platform
     *
     * @param provinceInstallPlatform the value for supply_chain..after_market_order_2c_offering_info.province_install_platform
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setProvinceInstallPlatform(String provinceInstallPlatform) {
        this.provinceInstallPlatform = provinceInstallPlatform;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.send_order_time
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.send_order_time
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public Date getSendOrderTime() {
        return sendOrderTime;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withSendOrderTime(Date sendOrderTime) {
        this.setSendOrderTime(sendOrderTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.send_order_time
     *
     * @param sendOrderTime the value for supply_chain..after_market_order_2c_offering_info.send_order_time
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setSendOrderTime(Date sendOrderTime) {
        this.sendOrderTime = sendOrderTime;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.deliver_time
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.deliver_time
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public Date getDeliverTime() {
        return deliverTime;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withDeliverTime(Date deliverTime) {
        this.setDeliverTime(deliverTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.deliver_time
     *
     * @param deliverTime the value for supply_chain..after_market_order_2c_offering_info.deliver_time
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setDeliverTime(Date deliverTime) {
        this.deliverTime = deliverTime;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.sku_offering_code
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.sku_offering_code
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getSkuOfferingCode() {
        return skuOfferingCode;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withSkuOfferingCode(String skuOfferingCode) {
        this.setSkuOfferingCode(skuOfferingCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.sku_offering_code
     *
     * @param skuOfferingCode the value for supply_chain..after_market_order_2c_offering_info.sku_offering_code
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setSkuOfferingCode(String skuOfferingCode) {
        this.skuOfferingCode = skuOfferingCode;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.atom_offering_code
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.atom_offering_code
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getAtomOfferingCode() {
        return atomOfferingCode;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withAtomOfferingCode(String atomOfferingCode) {
        this.setAtomOfferingCode(atomOfferingCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.atom_offering_code
     *
     * @param atomOfferingCode the value for supply_chain..after_market_order_2c_offering_info.atom_offering_code
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setAtomOfferingCode(String atomOfferingCode) {
        this.atomOfferingCode = atomOfferingCode;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.spu_offering_code
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.spu_offering_code
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getSpuOfferingCode() {
        return spuOfferingCode;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withSpuOfferingCode(String spuOfferingCode) {
        this.setSpuOfferingCode(spuOfferingCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.spu_offering_code
     *
     * @param spuOfferingCode the value for supply_chain..after_market_order_2c_offering_info.spu_offering_code
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setSpuOfferingCode(String spuOfferingCode) {
        this.spuOfferingCode = spuOfferingCode;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.spu_offering_class
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.spu_offering_class
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getSpuOfferingClass() {
        return spuOfferingClass;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withSpuOfferingClass(String spuOfferingClass) {
        this.setSpuOfferingClass(spuOfferingClass);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.spu_offering_class
     *
     * @param spuOfferingClass the value for supply_chain..after_market_order_2c_offering_info.spu_offering_class
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setSpuOfferingClass(String spuOfferingClass) {
        this.spuOfferingClass = spuOfferingClass;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.sku_offering_name
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.sku_offering_name
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getSkuOfferingName() {
        return skuOfferingName;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withSkuOfferingName(String skuOfferingName) {
        this.setSkuOfferingName(skuOfferingName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.sku_offering_name
     *
     * @param skuOfferingName the value for supply_chain..after_market_order_2c_offering_info.sku_offering_name
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setSkuOfferingName(String skuOfferingName) {
        this.skuOfferingName = skuOfferingName;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.atom_offering_name
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.atom_offering_name
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getAtomOfferingName() {
        return atomOfferingName;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withAtomOfferingName(String atomOfferingName) {
        this.setAtomOfferingName(atomOfferingName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.atom_offering_name
     *
     * @param atomOfferingName the value for supply_chain..after_market_order_2c_offering_info.atom_offering_name
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setAtomOfferingName(String atomOfferingName) {
        this.atomOfferingName = atomOfferingName;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.admin_cooperator_name
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.admin_cooperator_name
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getAdminCooperatorName() {
        return adminCooperatorName;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withAdminCooperatorName(String adminCooperatorName) {
        this.setAdminCooperatorName(adminCooperatorName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.admin_cooperator_name
     *
     * @param adminCooperatorName the value for supply_chain..after_market_order_2c_offering_info.admin_cooperator_name
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setAdminCooperatorName(String adminCooperatorName) {
        this.adminCooperatorName = adminCooperatorName;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.deliver_failed_msg
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.deliver_failed_msg
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getDeliverFailedMsg() {
        return deliverFailedMsg;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withDeliverFailedMsg(String deliverFailedMsg) {
        this.setDeliverFailedMsg(deliverFailedMsg);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.deliver_failed_msg
     *
     * @param deliverFailedMsg the value for supply_chain..after_market_order_2c_offering_info.deliver_failed_msg
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setDeliverFailedMsg(String deliverFailedMsg) {
        this.deliverFailedMsg = deliverFailedMsg;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.after_market_version
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.after_market_version
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getAfterMarketVersion() {
        return afterMarketVersion;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withAfterMarketVersion(String afterMarketVersion) {
        this.setAfterMarketVersion(afterMarketVersion);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.after_market_version
     *
     * @param afterMarketVersion the value for supply_chain..after_market_order_2c_offering_info.after_market_version
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setAfterMarketVersion(String afterMarketVersion) {
        this.afterMarketVersion = afterMarketVersion;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.spu_offering_version
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.spu_offering_version
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getSpuOfferingVersion() {
        return spuOfferingVersion;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withSpuOfferingVersion(String spuOfferingVersion) {
        this.setSpuOfferingVersion(spuOfferingVersion);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.spu_offering_version
     *
     * @param spuOfferingVersion the value for supply_chain..after_market_order_2c_offering_info.spu_offering_version
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setSpuOfferingVersion(String spuOfferingVersion) {
        this.spuOfferingVersion = spuOfferingVersion;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.sku_offering_version
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.sku_offering_version
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getSkuOfferingVersion() {
        return skuOfferingVersion;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withSkuOfferingVersion(String skuOfferingVersion) {
        this.setSkuOfferingVersion(skuOfferingVersion);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.sku_offering_version
     *
     * @param skuOfferingVersion the value for supply_chain..after_market_order_2c_offering_info.sku_offering_version
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setSkuOfferingVersion(String skuOfferingVersion) {
        this.skuOfferingVersion = skuOfferingVersion;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_offering_info.atom_offering_version
     *
     * @return the value of supply_chain..after_market_order_2c_offering_info.atom_offering_version
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public String getAtomOfferingVersion() {
        return atomOfferingVersion;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public AfterMarketOrder2cOfferingInfo withAtomOfferingVersion(String atomOfferingVersion) {
        this.setAtomOfferingVersion(atomOfferingVersion);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_offering_info.atom_offering_version
     *
     * @param atomOfferingVersion the value for supply_chain..after_market_order_2c_offering_info.atom_offering_version
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public void setAtomOfferingVersion(String atomOfferingVersion) {
        this.atomOfferingVersion = atomOfferingVersion;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", serviceOrderId=").append(serviceOrderId);
        sb.append(", afterMarketType=").append(afterMarketType);
        sb.append(", afterMarketCode=").append(afterMarketCode);
        sb.append(", quantity=").append(quantity);
        sb.append(", presentSendOrderName=").append(presentSendOrderName);
        sb.append(", presentSendOrderPhone=").append(presentSendOrderPhone);
        sb.append(", sendOrderCompany=").append(sendOrderCompany);
        sb.append(", userId=").append(userId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", afterMarketName=").append(afterMarketName);
        sb.append(", afterMarketSettlePrice=").append(afterMarketSettlePrice);
        sb.append(", orderTakeType=").append(orderTakeType);
        sb.append(", adminCooperatorId=").append(adminCooperatorId);
        sb.append(", installManagerId=").append(installManagerId);
        sb.append(", provinceInstallPlatform=").append(provinceInstallPlatform);
        sb.append(", sendOrderTime=").append(sendOrderTime);
        sb.append(", deliverTime=").append(deliverTime);
        sb.append(", skuOfferingCode=").append(skuOfferingCode);
        sb.append(", atomOfferingCode=").append(atomOfferingCode);
        sb.append(", spuOfferingCode=").append(spuOfferingCode);
        sb.append(", spuOfferingClass=").append(spuOfferingClass);
        sb.append(", skuOfferingName=").append(skuOfferingName);
        sb.append(", atomOfferingName=").append(atomOfferingName);
        sb.append(", adminCooperatorName=").append(adminCooperatorName);
        sb.append(", deliverFailedMsg=").append(deliverFailedMsg);
        sb.append(", afterMarketVersion=").append(afterMarketVersion);
        sb.append(", spuOfferingVersion=").append(spuOfferingVersion);
        sb.append(", skuOfferingVersion=").append(skuOfferingVersion);
        sb.append(", atomOfferingVersion=").append(atomOfferingVersion);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        AfterMarketOrder2cOfferingInfo other = (AfterMarketOrder2cOfferingInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getServiceOrderId() == null ? other.getServiceOrderId() == null : this.getServiceOrderId().equals(other.getServiceOrderId()))
            && (this.getAfterMarketType() == null ? other.getAfterMarketType() == null : this.getAfterMarketType().equals(other.getAfterMarketType()))
            && (this.getAfterMarketCode() == null ? other.getAfterMarketCode() == null : this.getAfterMarketCode().equals(other.getAfterMarketCode()))
            && (this.getQuantity() == null ? other.getQuantity() == null : this.getQuantity().equals(other.getQuantity()))
            && (this.getPresentSendOrderName() == null ? other.getPresentSendOrderName() == null : this.getPresentSendOrderName().equals(other.getPresentSendOrderName()))
            && (this.getPresentSendOrderPhone() == null ? other.getPresentSendOrderPhone() == null : this.getPresentSendOrderPhone().equals(other.getPresentSendOrderPhone()))
            && (this.getSendOrderCompany() == null ? other.getSendOrderCompany() == null : this.getSendOrderCompany().equals(other.getSendOrderCompany()))
            && (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getAfterMarketName() == null ? other.getAfterMarketName() == null : this.getAfterMarketName().equals(other.getAfterMarketName()))
            && (this.getAfterMarketSettlePrice() == null ? other.getAfterMarketSettlePrice() == null : this.getAfterMarketSettlePrice().equals(other.getAfterMarketSettlePrice()))
            && (this.getOrderTakeType() == null ? other.getOrderTakeType() == null : this.getOrderTakeType().equals(other.getOrderTakeType()))
            && (this.getAdminCooperatorId() == null ? other.getAdminCooperatorId() == null : this.getAdminCooperatorId().equals(other.getAdminCooperatorId()))
            && (this.getInstallManagerId() == null ? other.getInstallManagerId() == null : this.getInstallManagerId().equals(other.getInstallManagerId()))
            && (this.getProvinceInstallPlatform() == null ? other.getProvinceInstallPlatform() == null : this.getProvinceInstallPlatform().equals(other.getProvinceInstallPlatform()))
            && (this.getSendOrderTime() == null ? other.getSendOrderTime() == null : this.getSendOrderTime().equals(other.getSendOrderTime()))
            && (this.getDeliverTime() == null ? other.getDeliverTime() == null : this.getDeliverTime().equals(other.getDeliverTime()))
            && (this.getSkuOfferingCode() == null ? other.getSkuOfferingCode() == null : this.getSkuOfferingCode().equals(other.getSkuOfferingCode()))
            && (this.getAtomOfferingCode() == null ? other.getAtomOfferingCode() == null : this.getAtomOfferingCode().equals(other.getAtomOfferingCode()))
            && (this.getSpuOfferingCode() == null ? other.getSpuOfferingCode() == null : this.getSpuOfferingCode().equals(other.getSpuOfferingCode()))
            && (this.getSpuOfferingClass() == null ? other.getSpuOfferingClass() == null : this.getSpuOfferingClass().equals(other.getSpuOfferingClass()))
            && (this.getSkuOfferingName() == null ? other.getSkuOfferingName() == null : this.getSkuOfferingName().equals(other.getSkuOfferingName()))
            && (this.getAtomOfferingName() == null ? other.getAtomOfferingName() == null : this.getAtomOfferingName().equals(other.getAtomOfferingName()))
            && (this.getAdminCooperatorName() == null ? other.getAdminCooperatorName() == null : this.getAdminCooperatorName().equals(other.getAdminCooperatorName()))
            && (this.getDeliverFailedMsg() == null ? other.getDeliverFailedMsg() == null : this.getDeliverFailedMsg().equals(other.getDeliverFailedMsg()))
            && (this.getAfterMarketVersion() == null ? other.getAfterMarketVersion() == null : this.getAfterMarketVersion().equals(other.getAfterMarketVersion()))
            && (this.getSpuOfferingVersion() == null ? other.getSpuOfferingVersion() == null : this.getSpuOfferingVersion().equals(other.getSpuOfferingVersion()))
            && (this.getSkuOfferingVersion() == null ? other.getSkuOfferingVersion() == null : this.getSkuOfferingVersion().equals(other.getSkuOfferingVersion()))
            && (this.getAtomOfferingVersion() == null ? other.getAtomOfferingVersion() == null : this.getAtomOfferingVersion().equals(other.getAtomOfferingVersion()));
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getServiceOrderId() == null) ? 0 : getServiceOrderId().hashCode());
        result = prime * result + ((getAfterMarketType() == null) ? 0 : getAfterMarketType().hashCode());
        result = prime * result + ((getAfterMarketCode() == null) ? 0 : getAfterMarketCode().hashCode());
        result = prime * result + ((getQuantity() == null) ? 0 : getQuantity().hashCode());
        result = prime * result + ((getPresentSendOrderName() == null) ? 0 : getPresentSendOrderName().hashCode());
        result = prime * result + ((getPresentSendOrderPhone() == null) ? 0 : getPresentSendOrderPhone().hashCode());
        result = prime * result + ((getSendOrderCompany() == null) ? 0 : getSendOrderCompany().hashCode());
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getAfterMarketName() == null) ? 0 : getAfterMarketName().hashCode());
        result = prime * result + ((getAfterMarketSettlePrice() == null) ? 0 : getAfterMarketSettlePrice().hashCode());
        result = prime * result + ((getOrderTakeType() == null) ? 0 : getOrderTakeType().hashCode());
        result = prime * result + ((getAdminCooperatorId() == null) ? 0 : getAdminCooperatorId().hashCode());
        result = prime * result + ((getInstallManagerId() == null) ? 0 : getInstallManagerId().hashCode());
        result = prime * result + ((getProvinceInstallPlatform() == null) ? 0 : getProvinceInstallPlatform().hashCode());
        result = prime * result + ((getSendOrderTime() == null) ? 0 : getSendOrderTime().hashCode());
        result = prime * result + ((getDeliverTime() == null) ? 0 : getDeliverTime().hashCode());
        result = prime * result + ((getSkuOfferingCode() == null) ? 0 : getSkuOfferingCode().hashCode());
        result = prime * result + ((getAtomOfferingCode() == null) ? 0 : getAtomOfferingCode().hashCode());
        result = prime * result + ((getSpuOfferingCode() == null) ? 0 : getSpuOfferingCode().hashCode());
        result = prime * result + ((getSpuOfferingClass() == null) ? 0 : getSpuOfferingClass().hashCode());
        result = prime * result + ((getSkuOfferingName() == null) ? 0 : getSkuOfferingName().hashCode());
        result = prime * result + ((getAtomOfferingName() == null) ? 0 : getAtomOfferingName().hashCode());
        result = prime * result + ((getAdminCooperatorName() == null) ? 0 : getAdminCooperatorName().hashCode());
        result = prime * result + ((getDeliverFailedMsg() == null) ? 0 : getDeliverFailedMsg().hashCode());
        result = prime * result + ((getAfterMarketVersion() == null) ? 0 : getAfterMarketVersion().hashCode());
        result = prime * result + ((getSpuOfferingVersion() == null) ? 0 : getSpuOfferingVersion().hashCode());
        result = prime * result + ((getSkuOfferingVersion() == null) ? 0 : getSkuOfferingVersion().hashCode());
        result = prime * result + ((getAtomOfferingVersion() == null) ? 0 : getAtomOfferingVersion().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:25:45 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        serviceOrderId("service_order_id", "serviceOrderId", "VARCHAR", false),
        afterMarketType("after_market_type", "afterMarketType", "INTEGER", false),
        afterMarketCode("after_market_code", "afterMarketCode", "VARCHAR", false),
        quantity("quantity", "quantity", "BIGINT", false),
        presentSendOrderName("present_send_order_name", "presentSendOrderName", "VARCHAR", false),
        presentSendOrderPhone("present_send_order_phone", "presentSendOrderPhone", "VARCHAR", false),
        sendOrderCompany("send_order_company", "sendOrderCompany", "VARCHAR", false),
        userId("user_id", "userId", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        afterMarketName("after_market_name", "afterMarketName", "VARCHAR", false),
        afterMarketSettlePrice("after_market_settle_price", "afterMarketSettlePrice", "VARCHAR", false),
        orderTakeType("order_take_type", "orderTakeType", "INTEGER", false),
        adminCooperatorId("admin_cooperator_id", "adminCooperatorId", "VARCHAR", false),
        installManagerId("install_manager_id", "installManagerId", "VARCHAR", false),
        provinceInstallPlatform("province_install_platform", "provinceInstallPlatform", "VARCHAR", false),
        sendOrderTime("send_order_time", "sendOrderTime", "TIMESTAMP", false),
        deliverTime("deliver_time", "deliverTime", "TIMESTAMP", false),
        skuOfferingCode("sku_offering_code", "skuOfferingCode", "VARCHAR", false),
        atomOfferingCode("atom_offering_code", "atomOfferingCode", "VARCHAR", false),
        spuOfferingCode("spu_offering_code", "spuOfferingCode", "VARCHAR", false),
        spuOfferingClass("spu_offering_class", "spuOfferingClass", "VARCHAR", false),
        skuOfferingName("sku_offering_name", "skuOfferingName", "VARCHAR", false),
        atomOfferingName("atom_offering_name", "atomOfferingName", "VARCHAR", false),
        adminCooperatorName("admin_cooperator_name", "adminCooperatorName", "VARCHAR", false),
        deliverFailedMsg("deliver_failed_msg", "deliverFailedMsg", "VARCHAR", false),
        afterMarketVersion("after_market_version", "afterMarketVersion", "VARCHAR", false),
        spuOfferingVersion("spu_offering_version", "spuOfferingVersion", "VARCHAR", false),
        skuOfferingVersion("sku_offering_version", "skuOfferingVersion", "VARCHAR", false),
        atomOfferingVersion("atom_offering_version", "atomOfferingVersion", "VARCHAR", false);

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Thu May 29 15:25:45 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}