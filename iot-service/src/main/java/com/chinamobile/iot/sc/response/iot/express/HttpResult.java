package com.chinamobile.iot.sc.response.iot.express;

import lombok.Data;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date : 2022/4/14 9:24
 * @description: http请求返回结果
 **/
@Data
public class HttpResult {

    /**
     * HTTP状态码
     */
    private int status;
    /**
     * HTTP响应正文
     */
    private String body;
    /**
     * 错误信息
     */
    private String error;

    public HttpResult() {
    }

    public HttpResult(int status, String body, String error) {
        this.status = status;
        this.body = body;
        this.error = error;
    }
}
