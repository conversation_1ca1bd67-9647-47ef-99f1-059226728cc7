package com.chinamobile.iot.sc.pojo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class NewProductRequestManageExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public NewProductRequestManageExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public NewProductRequestManageExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    public NewProductRequestManageExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    public static Criteria newAndCreateCriteria() {
        NewProductRequestManageExample example = new NewProductRequestManageExample();
        return example.createCriteria();
    }

    public NewProductRequestManageExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    public NewProductRequestManageExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andRequestNoIsNull() {
            addCriterion("request_no is null");
            return (Criteria) this;
        }

        public Criteria andRequestNoIsNotNull() {
            addCriterion("request_no is not null");
            return (Criteria) this;
        }

        public Criteria andRequestNoEqualTo(String value) {
            addCriterion("request_no =", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("request_no = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestNoNotEqualTo(String value) {
            addCriterion("request_no <>", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("request_no <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestNoGreaterThan(String value) {
            addCriterion("request_no >", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("request_no > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestNoGreaterThanOrEqualTo(String value) {
            addCriterion("request_no >=", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("request_no >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestNoLessThan(String value) {
            addCriterion("request_no <", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("request_no < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestNoLessThanOrEqualTo(String value) {
            addCriterion("request_no <=", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("request_no <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestNoLike(String value) {
            addCriterion("request_no like", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoNotLike(String value) {
            addCriterion("request_no not like", value, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoIn(List<String> values) {
            addCriterion("request_no in", values, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoNotIn(List<String> values) {
            addCriterion("request_no not in", values, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoBetween(String value1, String value2) {
            addCriterion("request_no between", value1, value2, "requestNo");
            return (Criteria) this;
        }

        public Criteria andRequestNoNotBetween(String value1, String value2) {
            addCriterion("request_no not between", value1, value2, "requestNo");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameIsNull() {
            addCriterion("spu_offering_name is null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameIsNotNull() {
            addCriterion("spu_offering_name is not null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameEqualTo(String value) {
            addCriterion("spu_offering_name =", value, "spuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("spu_offering_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameNotEqualTo(String value) {
            addCriterion("spu_offering_name <>", value, "spuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("spu_offering_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameGreaterThan(String value) {
            addCriterion("spu_offering_name >", value, "spuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("spu_offering_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameGreaterThanOrEqualTo(String value) {
            addCriterion("spu_offering_name >=", value, "spuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("spu_offering_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameLessThan(String value) {
            addCriterion("spu_offering_name <", value, "spuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("spu_offering_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameLessThanOrEqualTo(String value) {
            addCriterion("spu_offering_name <=", value, "spuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("spu_offering_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameLike(String value) {
            addCriterion("spu_offering_name like", value, "spuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameNotLike(String value) {
            addCriterion("spu_offering_name not like", value, "spuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameIn(List<String> values) {
            addCriterion("spu_offering_name in", values, "spuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameNotIn(List<String> values) {
            addCriterion("spu_offering_name not in", values, "spuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameBetween(String value1, String value2) {
            addCriterion("spu_offering_name between", value1, value2, "spuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameNotBetween(String value1, String value2) {
            addCriterion("spu_offering_name not between", value1, value2, "spuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeIsNull() {
            addCriterion("spu_offering_code is null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeIsNotNull() {
            addCriterion("spu_offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeEqualTo(String value) {
            addCriterion("spu_offering_code =", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("spu_offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotEqualTo(String value) {
            addCriterion("spu_offering_code <>", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("spu_offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThan(String value) {
            addCriterion("spu_offering_code >", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("spu_offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("spu_offering_code >=", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("spu_offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThan(String value) {
            addCriterion("spu_offering_code <", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("spu_offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("spu_offering_code <=", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("spu_offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLike(String value) {
            addCriterion("spu_offering_code like", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotLike(String value) {
            addCriterion("spu_offering_code not like", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeIn(List<String> values) {
            addCriterion("spu_offering_code in", values, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotIn(List<String> values) {
            addCriterion("spu_offering_code not in", values, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeBetween(String value1, String value2) {
            addCriterion("spu_offering_code between", value1, value2, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("spu_offering_code not between", value1, value2, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameIsNull() {
            addCriterion("sku_offering_name is null");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameIsNotNull() {
            addCriterion("sku_offering_name is not null");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameEqualTo(String value) {
            addCriterion("sku_offering_name =", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("sku_offering_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameNotEqualTo(String value) {
            addCriterion("sku_offering_name <>", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("sku_offering_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameGreaterThan(String value) {
            addCriterion("sku_offering_name >", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("sku_offering_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameGreaterThanOrEqualTo(String value) {
            addCriterion("sku_offering_name >=", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("sku_offering_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameLessThan(String value) {
            addCriterion("sku_offering_name <", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("sku_offering_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameLessThanOrEqualTo(String value) {
            addCriterion("sku_offering_name <=", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("sku_offering_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameLike(String value) {
            addCriterion("sku_offering_name like", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameNotLike(String value) {
            addCriterion("sku_offering_name not like", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameIn(List<String> values) {
            addCriterion("sku_offering_name in", values, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameNotIn(List<String> values) {
            addCriterion("sku_offering_name not in", values, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameBetween(String value1, String value2) {
            addCriterion("sku_offering_name between", value1, value2, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameNotBetween(String value1, String value2) {
            addCriterion("sku_offering_name not between", value1, value2, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeIsNull() {
            addCriterion("sku_offering_code is null");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeIsNotNull() {
            addCriterion("sku_offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeEqualTo(String value) {
            addCriterion("sku_offering_code =", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("sku_offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeNotEqualTo(String value) {
            addCriterion("sku_offering_code <>", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("sku_offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeGreaterThan(String value) {
            addCriterion("sku_offering_code >", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("sku_offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sku_offering_code >=", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("sku_offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeLessThan(String value) {
            addCriterion("sku_offering_code <", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("sku_offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("sku_offering_code <=", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("sku_offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeLike(String value) {
            addCriterion("sku_offering_code like", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeNotLike(String value) {
            addCriterion("sku_offering_code not like", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeIn(List<String> values) {
            addCriterion("sku_offering_code in", values, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeNotIn(List<String> values) {
            addCriterion("sku_offering_code not in", values, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeBetween(String value1, String value2) {
            addCriterion("sku_offering_code between", value1, value2, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("sku_offering_code not between", value1, value2, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andRequestStatusIsNull() {
            addCriterion("request_status is null");
            return (Criteria) this;
        }

        public Criteria andRequestStatusIsNotNull() {
            addCriterion("request_status is not null");
            return (Criteria) this;
        }

        public Criteria andRequestStatusEqualTo(Integer value) {
            addCriterion("request_status =", value, "requestStatus");
            return (Criteria) this;
        }

        public Criteria andRequestStatusEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("request_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestStatusNotEqualTo(Integer value) {
            addCriterion("request_status <>", value, "requestStatus");
            return (Criteria) this;
        }

        public Criteria andRequestStatusNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("request_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestStatusGreaterThan(Integer value) {
            addCriterion("request_status >", value, "requestStatus");
            return (Criteria) this;
        }

        public Criteria andRequestStatusGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("request_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("request_status >=", value, "requestStatus");
            return (Criteria) this;
        }

        public Criteria andRequestStatusGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("request_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestStatusLessThan(Integer value) {
            addCriterion("request_status <", value, "requestStatus");
            return (Criteria) this;
        }

        public Criteria andRequestStatusLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("request_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestStatusLessThanOrEqualTo(Integer value) {
            addCriterion("request_status <=", value, "requestStatus");
            return (Criteria) this;
        }

        public Criteria andRequestStatusLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("request_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestStatusIn(List<Integer> values) {
            addCriterion("request_status in", values, "requestStatus");
            return (Criteria) this;
        }

        public Criteria andRequestStatusNotIn(List<Integer> values) {
            addCriterion("request_status not in", values, "requestStatus");
            return (Criteria) this;
        }

        public Criteria andRequestStatusBetween(Integer value1, Integer value2) {
            addCriterion("request_status between", value1, value2, "requestStatus");
            return (Criteria) this;
        }

        public Criteria andRequestStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("request_status not between", value1, value2, "requestStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineStatusIsNull() {
            addCriterion("online_status is null");
            return (Criteria) this;
        }

        public Criteria andOnlineStatusIsNotNull() {
            addCriterion("online_status is not null");
            return (Criteria) this;
        }

        public Criteria andOnlineStatusEqualTo(String value) {
            addCriterion("online_status =", value, "onlineStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineStatusEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("online_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineStatusNotEqualTo(String value) {
            addCriterion("online_status <>", value, "onlineStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineStatusNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("online_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineStatusGreaterThan(String value) {
            addCriterion("online_status >", value, "onlineStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineStatusGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("online_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineStatusGreaterThanOrEqualTo(String value) {
            addCriterion("online_status >=", value, "onlineStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineStatusGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("online_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineStatusLessThan(String value) {
            addCriterion("online_status <", value, "onlineStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineStatusLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("online_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineStatusLessThanOrEqualTo(String value) {
            addCriterion("online_status <=", value, "onlineStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineStatusLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("online_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineStatusLike(String value) {
            addCriterion("online_status like", value, "onlineStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineStatusNotLike(String value) {
            addCriterion("online_status not like", value, "onlineStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineStatusIn(List<String> values) {
            addCriterion("online_status in", values, "onlineStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineStatusNotIn(List<String> values) {
            addCriterion("online_status not in", values, "onlineStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineStatusBetween(String value1, String value2) {
            addCriterion("online_status between", value1, value2, "onlineStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineStatusNotBetween(String value1, String value2) {
            addCriterion("online_status not between", value1, value2, "onlineStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineRequestStatusIsNull() {
            addCriterion("online_offline_request_status is null");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineRequestStatusIsNotNull() {
            addCriterion("online_offline_request_status is not null");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineRequestStatusEqualTo(Integer value) {
            addCriterion("online_offline_request_status =", value, "onlineOfflineRequestStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineRequestStatusEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("online_offline_request_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineRequestStatusNotEqualTo(Integer value) {
            addCriterion("online_offline_request_status <>", value, "onlineOfflineRequestStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineRequestStatusNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("online_offline_request_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineRequestStatusGreaterThan(Integer value) {
            addCriterion("online_offline_request_status >", value, "onlineOfflineRequestStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineRequestStatusGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("online_offline_request_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineRequestStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("online_offline_request_status >=", value, "onlineOfflineRequestStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineRequestStatusGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("online_offline_request_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineRequestStatusLessThan(Integer value) {
            addCriterion("online_offline_request_status <", value, "onlineOfflineRequestStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineRequestStatusLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("online_offline_request_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineRequestStatusLessThanOrEqualTo(Integer value) {
            addCriterion("online_offline_request_status <=", value, "onlineOfflineRequestStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineRequestStatusLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("online_offline_request_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineRequestStatusIn(List<Integer> values) {
            addCriterion("online_offline_request_status in", values, "onlineOfflineRequestStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineRequestStatusNotIn(List<Integer> values) {
            addCriterion("online_offline_request_status not in", values, "onlineOfflineRequestStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineRequestStatusBetween(Integer value1, Integer value2) {
            addCriterion("online_offline_request_status between", value1, value2, "onlineOfflineRequestStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineRequestStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("online_offline_request_status not between", value1, value2, "onlineOfflineRequestStatus");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIsNull() {
            addCriterion("cooperator_id is null");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIsNotNull() {
            addCriterion("cooperator_id is not null");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdEqualTo(String value) {
            addCriterion("cooperator_id =", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("cooperator_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotEqualTo(String value) {
            addCriterion("cooperator_id <>", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("cooperator_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThan(String value) {
            addCriterion("cooperator_id >", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("cooperator_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanOrEqualTo(String value) {
            addCriterion("cooperator_id >=", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("cooperator_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThan(String value) {
            addCriterion("cooperator_id <", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("cooperator_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanOrEqualTo(String value) {
            addCriterion("cooperator_id <=", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("cooperator_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLike(String value) {
            addCriterion("cooperator_id like", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotLike(String value) {
            addCriterion("cooperator_id not like", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIn(List<String> values) {
            addCriterion("cooperator_id in", values, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotIn(List<String> values) {
            addCriterion("cooperator_id not in", values, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdBetween(String value1, String value2) {
            addCriterion("cooperator_id between", value1, value2, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotBetween(String value1, String value2) {
            addCriterion("cooperator_id not between", value1, value2, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("brand = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("brand <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("brand > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("brand >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("brand < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("brand <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("model = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("model <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("model > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("model >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("model < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("model <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andColorIsNull() {
            addCriterion("color is null");
            return (Criteria) this;
        }

        public Criteria andColorIsNotNull() {
            addCriterion("color is not null");
            return (Criteria) this;
        }

        public Criteria andColorEqualTo(String value) {
            addCriterion("color =", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("color = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorNotEqualTo(String value) {
            addCriterion("color <>", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("color <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorGreaterThan(String value) {
            addCriterion("color >", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("color > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorGreaterThanOrEqualTo(String value) {
            addCriterion("color >=", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("color >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorLessThan(String value) {
            addCriterion("color <", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("color < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorLessThanOrEqualTo(String value) {
            addCriterion("color <=", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("color <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorLike(String value) {
            addCriterion("color like", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotLike(String value) {
            addCriterion("color not like", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorIn(List<String> values) {
            addCriterion("color in", values, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotIn(List<String> values) {
            addCriterion("color not in", values, "color");
            return (Criteria) this;
        }

        public Criteria andColorBetween(String value1, String value2) {
            addCriterion("color between", value1, value2, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotBetween(String value1, String value2) {
            addCriterion("color not between", value1, value2, "color");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeIsNull() {
            addCriterion("material_code is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeIsNotNull() {
            addCriterion("material_code is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeEqualTo(String value) {
            addCriterion("material_code =", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("material_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotEqualTo(String value) {
            addCriterion("material_code <>", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("material_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCodeGreaterThan(String value) {
            addCriterion("material_code >", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("material_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCodeGreaterThanOrEqualTo(String value) {
            addCriterion("material_code >=", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("material_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLessThan(String value) {
            addCriterion("material_code <", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("material_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLessThanOrEqualTo(String value) {
            addCriterion("material_code <=", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("material_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLike(String value) {
            addCriterion("material_code like", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotLike(String value) {
            addCriterion("material_code not like", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeIn(List<String> values) {
            addCriterion("material_code in", values, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotIn(List<String> values) {
            addCriterion("material_code not in", values, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeBetween(String value1, String value2) {
            addCriterion("material_code between", value1, value2, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotBetween(String value1, String value2) {
            addCriterion("material_code not between", value1, value2, "materialCode");
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyIsNull() {
            addCriterion("network_property is null");
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyIsNotNull() {
            addCriterion("network_property is not null");
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyEqualTo(String value) {
            addCriterion("network_property =", value, "networkProperty");
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("network_property = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyNotEqualTo(String value) {
            addCriterion("network_property <>", value, "networkProperty");
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("network_property <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyGreaterThan(String value) {
            addCriterion("network_property >", value, "networkProperty");
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("network_property > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyGreaterThanOrEqualTo(String value) {
            addCriterion("network_property >=", value, "networkProperty");
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("network_property >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyLessThan(String value) {
            addCriterion("network_property <", value, "networkProperty");
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("network_property < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyLessThanOrEqualTo(String value) {
            addCriterion("network_property <=", value, "networkProperty");
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("network_property <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyLike(String value) {
            addCriterion("network_property like", value, "networkProperty");
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyNotLike(String value) {
            addCriterion("network_property not like", value, "networkProperty");
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyIn(List<String> values) {
            addCriterion("network_property in", values, "networkProperty");
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyNotIn(List<String> values) {
            addCriterion("network_property not in", values, "networkProperty");
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyBetween(String value1, String value2) {
            addCriterion("network_property between", value1, value2, "networkProperty");
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyNotBetween(String value1, String value2) {
            addCriterion("network_property not between", value1, value2, "networkProperty");
            return (Criteria) this;
        }

        public Criteria andApplicationDomainIsNull() {
            addCriterion("application_domain is null");
            return (Criteria) this;
        }

        public Criteria andApplicationDomainIsNotNull() {
            addCriterion("application_domain is not null");
            return (Criteria) this;
        }

        public Criteria andApplicationDomainEqualTo(String value) {
            addCriterion("application_domain =", value, "applicationDomain");
            return (Criteria) this;
        }

        public Criteria andApplicationDomainEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("application_domain = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApplicationDomainNotEqualTo(String value) {
            addCriterion("application_domain <>", value, "applicationDomain");
            return (Criteria) this;
        }

        public Criteria andApplicationDomainNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("application_domain <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApplicationDomainGreaterThan(String value) {
            addCriterion("application_domain >", value, "applicationDomain");
            return (Criteria) this;
        }

        public Criteria andApplicationDomainGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("application_domain > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApplicationDomainGreaterThanOrEqualTo(String value) {
            addCriterion("application_domain >=", value, "applicationDomain");
            return (Criteria) this;
        }

        public Criteria andApplicationDomainGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("application_domain >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApplicationDomainLessThan(String value) {
            addCriterion("application_domain <", value, "applicationDomain");
            return (Criteria) this;
        }

        public Criteria andApplicationDomainLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("application_domain < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApplicationDomainLessThanOrEqualTo(String value) {
            addCriterion("application_domain <=", value, "applicationDomain");
            return (Criteria) this;
        }

        public Criteria andApplicationDomainLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("application_domain <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andApplicationDomainLike(String value) {
            addCriterion("application_domain like", value, "applicationDomain");
            return (Criteria) this;
        }

        public Criteria andApplicationDomainNotLike(String value) {
            addCriterion("application_domain not like", value, "applicationDomain");
            return (Criteria) this;
        }

        public Criteria andApplicationDomainIn(List<String> values) {
            addCriterion("application_domain in", values, "applicationDomain");
            return (Criteria) this;
        }

        public Criteria andApplicationDomainNotIn(List<String> values) {
            addCriterion("application_domain not in", values, "applicationDomain");
            return (Criteria) this;
        }

        public Criteria andApplicationDomainBetween(String value1, String value2) {
            addCriterion("application_domain between", value1, value2, "applicationDomain");
            return (Criteria) this;
        }

        public Criteria andApplicationDomainNotBetween(String value1, String value2) {
            addCriterion("application_domain not between", value1, value2, "applicationDomain");
            return (Criteria) this;
        }

        public Criteria andProductIntroductionIsNull() {
            addCriterion("product_introduction is null");
            return (Criteria) this;
        }

        public Criteria andProductIntroductionIsNotNull() {
            addCriterion("product_introduction is not null");
            return (Criteria) this;
        }

        public Criteria andProductIntroductionEqualTo(String value) {
            addCriterion("product_introduction =", value, "productIntroduction");
            return (Criteria) this;
        }

        public Criteria andProductIntroductionEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_introduction = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductIntroductionNotEqualTo(String value) {
            addCriterion("product_introduction <>", value, "productIntroduction");
            return (Criteria) this;
        }

        public Criteria andProductIntroductionNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_introduction <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductIntroductionGreaterThan(String value) {
            addCriterion("product_introduction >", value, "productIntroduction");
            return (Criteria) this;
        }

        public Criteria andProductIntroductionGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_introduction > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductIntroductionGreaterThanOrEqualTo(String value) {
            addCriterion("product_introduction >=", value, "productIntroduction");
            return (Criteria) this;
        }

        public Criteria andProductIntroductionGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_introduction >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductIntroductionLessThan(String value) {
            addCriterion("product_introduction <", value, "productIntroduction");
            return (Criteria) this;
        }

        public Criteria andProductIntroductionLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_introduction < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductIntroductionLessThanOrEqualTo(String value) {
            addCriterion("product_introduction <=", value, "productIntroduction");
            return (Criteria) this;
        }

        public Criteria andProductIntroductionLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_introduction <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductIntroductionLike(String value) {
            addCriterion("product_introduction like", value, "productIntroduction");
            return (Criteria) this;
        }

        public Criteria andProductIntroductionNotLike(String value) {
            addCriterion("product_introduction not like", value, "productIntroduction");
            return (Criteria) this;
        }

        public Criteria andProductIntroductionIn(List<String> values) {
            addCriterion("product_introduction in", values, "productIntroduction");
            return (Criteria) this;
        }

        public Criteria andProductIntroductionNotIn(List<String> values) {
            addCriterion("product_introduction not in", values, "productIntroduction");
            return (Criteria) this;
        }

        public Criteria andProductIntroductionBetween(String value1, String value2) {
            addCriterion("product_introduction between", value1, value2, "productIntroduction");
            return (Criteria) this;
        }

        public Criteria andProductIntroductionNotBetween(String value1, String value2) {
            addCriterion("product_introduction not between", value1, value2, "productIntroduction");
            return (Criteria) this;
        }

        public Criteria andProductSaleContentIsNull() {
            addCriterion("product_sale_content is null");
            return (Criteria) this;
        }

        public Criteria andProductSaleContentIsNotNull() {
            addCriterion("product_sale_content is not null");
            return (Criteria) this;
        }

        public Criteria andProductSaleContentEqualTo(String value) {
            addCriterion("product_sale_content =", value, "productSaleContent");
            return (Criteria) this;
        }

        public Criteria andProductSaleContentEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_sale_content = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleContentNotEqualTo(String value) {
            addCriterion("product_sale_content <>", value, "productSaleContent");
            return (Criteria) this;
        }

        public Criteria andProductSaleContentNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_sale_content <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleContentGreaterThan(String value) {
            addCriterion("product_sale_content >", value, "productSaleContent");
            return (Criteria) this;
        }

        public Criteria andProductSaleContentGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_sale_content > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleContentGreaterThanOrEqualTo(String value) {
            addCriterion("product_sale_content >=", value, "productSaleContent");
            return (Criteria) this;
        }

        public Criteria andProductSaleContentGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_sale_content >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleContentLessThan(String value) {
            addCriterion("product_sale_content <", value, "productSaleContent");
            return (Criteria) this;
        }

        public Criteria andProductSaleContentLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_sale_content < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleContentLessThanOrEqualTo(String value) {
            addCriterion("product_sale_content <=", value, "productSaleContent");
            return (Criteria) this;
        }

        public Criteria andProductSaleContentLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_sale_content <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleContentLike(String value) {
            addCriterion("product_sale_content like", value, "productSaleContent");
            return (Criteria) this;
        }

        public Criteria andProductSaleContentNotLike(String value) {
            addCriterion("product_sale_content not like", value, "productSaleContent");
            return (Criteria) this;
        }

        public Criteria andProductSaleContentIn(List<String> values) {
            addCriterion("product_sale_content in", values, "productSaleContent");
            return (Criteria) this;
        }

        public Criteria andProductSaleContentNotIn(List<String> values) {
            addCriterion("product_sale_content not in", values, "productSaleContent");
            return (Criteria) this;
        }

        public Criteria andProductSaleContentBetween(String value1, String value2) {
            addCriterion("product_sale_content between", value1, value2, "productSaleContent");
            return (Criteria) this;
        }

        public Criteria andProductSaleContentNotBetween(String value1, String value2) {
            addCriterion("product_sale_content not between", value1, value2, "productSaleContent");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeIsNull() {
            addCriterion("product_sale_area_code is null");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeIsNotNull() {
            addCriterion("product_sale_area_code is not null");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeEqualTo(String value) {
            addCriterion("product_sale_area_code =", value, "productSaleAreaCode");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_sale_area_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeNotEqualTo(String value) {
            addCriterion("product_sale_area_code <>", value, "productSaleAreaCode");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_sale_area_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeGreaterThan(String value) {
            addCriterion("product_sale_area_code >", value, "productSaleAreaCode");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_sale_area_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeGreaterThanOrEqualTo(String value) {
            addCriterion("product_sale_area_code >=", value, "productSaleAreaCode");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_sale_area_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeLessThan(String value) {
            addCriterion("product_sale_area_code <", value, "productSaleAreaCode");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_sale_area_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeLessThanOrEqualTo(String value) {
            addCriterion("product_sale_area_code <=", value, "productSaleAreaCode");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_sale_area_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeLike(String value) {
            addCriterion("product_sale_area_code like", value, "productSaleAreaCode");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeNotLike(String value) {
            addCriterion("product_sale_area_code not like", value, "productSaleAreaCode");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeIn(List<String> values) {
            addCriterion("product_sale_area_code in", values, "productSaleAreaCode");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeNotIn(List<String> values) {
            addCriterion("product_sale_area_code not in", values, "productSaleAreaCode");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeBetween(String value1, String value2) {
            addCriterion("product_sale_area_code between", value1, value2, "productSaleAreaCode");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeNotBetween(String value1, String value2) {
            addCriterion("product_sale_area_code not between", value1, value2, "productSaleAreaCode");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaIsNull() {
            addCriterion("product_sale_area is null");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaIsNotNull() {
            addCriterion("product_sale_area is not null");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaEqualTo(String value) {
            addCriterion("product_sale_area =", value, "productSaleArea");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_sale_area = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaNotEqualTo(String value) {
            addCriterion("product_sale_area <>", value, "productSaleArea");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_sale_area <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaGreaterThan(String value) {
            addCriterion("product_sale_area >", value, "productSaleArea");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_sale_area > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaGreaterThanOrEqualTo(String value) {
            addCriterion("product_sale_area >=", value, "productSaleArea");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_sale_area >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaLessThan(String value) {
            addCriterion("product_sale_area <", value, "productSaleArea");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_sale_area < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaLessThanOrEqualTo(String value) {
            addCriterion("product_sale_area <=", value, "productSaleArea");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_sale_area <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaLike(String value) {
            addCriterion("product_sale_area like", value, "productSaleArea");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaNotLike(String value) {
            addCriterion("product_sale_area not like", value, "productSaleArea");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaIn(List<String> values) {
            addCriterion("product_sale_area in", values, "productSaleArea");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaNotIn(List<String> values) {
            addCriterion("product_sale_area not in", values, "productSaleArea");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaBetween(String value1, String value2) {
            addCriterion("product_sale_area between", value1, value2, "productSaleArea");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaNotBetween(String value1, String value2) {
            addCriterion("product_sale_area not between", value1, value2, "productSaleArea");
            return (Criteria) this;
        }

        public Criteria andSupplyPriceIsNull() {
            addCriterion("supply_price is null");
            return (Criteria) this;
        }

        public Criteria andSupplyPriceIsNotNull() {
            addCriterion("supply_price is not null");
            return (Criteria) this;
        }

        public Criteria andSupplyPriceEqualTo(BigDecimal value) {
            addCriterion("supply_price =", value, "supplyPrice");
            return (Criteria) this;
        }

        public Criteria andSupplyPriceEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("supply_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplyPriceNotEqualTo(BigDecimal value) {
            addCriterion("supply_price <>", value, "supplyPrice");
            return (Criteria) this;
        }

        public Criteria andSupplyPriceNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("supply_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplyPriceGreaterThan(BigDecimal value) {
            addCriterion("supply_price >", value, "supplyPrice");
            return (Criteria) this;
        }

        public Criteria andSupplyPriceGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("supply_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplyPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("supply_price >=", value, "supplyPrice");
            return (Criteria) this;
        }

        public Criteria andSupplyPriceGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("supply_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplyPriceLessThan(BigDecimal value) {
            addCriterion("supply_price <", value, "supplyPrice");
            return (Criteria) this;
        }

        public Criteria andSupplyPriceLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("supply_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplyPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("supply_price <=", value, "supplyPrice");
            return (Criteria) this;
        }

        public Criteria andSupplyPriceLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("supply_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplyPriceIn(List<BigDecimal> values) {
            addCriterion("supply_price in", values, "supplyPrice");
            return (Criteria) this;
        }

        public Criteria andSupplyPriceNotIn(List<BigDecimal> values) {
            addCriterion("supply_price not in", values, "supplyPrice");
            return (Criteria) this;
        }

        public Criteria andSupplyPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("supply_price between", value1, value2, "supplyPrice");
            return (Criteria) this;
        }

        public Criteria andSupplyPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("supply_price not between", value1, value2, "supplyPrice");
            return (Criteria) this;
        }

        public Criteria andMarketPriceIsNull() {
            addCriterion("market_price is null");
            return (Criteria) this;
        }

        public Criteria andMarketPriceIsNotNull() {
            addCriterion("market_price is not null");
            return (Criteria) this;
        }

        public Criteria andMarketPriceEqualTo(BigDecimal value) {
            addCriterion("market_price =", value, "marketPrice");
            return (Criteria) this;
        }

        public Criteria andMarketPriceEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("market_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketPriceNotEqualTo(BigDecimal value) {
            addCriterion("market_price <>", value, "marketPrice");
            return (Criteria) this;
        }

        public Criteria andMarketPriceNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("market_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketPriceGreaterThan(BigDecimal value) {
            addCriterion("market_price >", value, "marketPrice");
            return (Criteria) this;
        }

        public Criteria andMarketPriceGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("market_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("market_price >=", value, "marketPrice");
            return (Criteria) this;
        }

        public Criteria andMarketPriceGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("market_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketPriceLessThan(BigDecimal value) {
            addCriterion("market_price <", value, "marketPrice");
            return (Criteria) this;
        }

        public Criteria andMarketPriceLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("market_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("market_price <=", value, "marketPrice");
            return (Criteria) this;
        }

        public Criteria andMarketPriceLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("market_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketPriceIn(List<BigDecimal> values) {
            addCriterion("market_price in", values, "marketPrice");
            return (Criteria) this;
        }

        public Criteria andMarketPriceNotIn(List<BigDecimal> values) {
            addCriterion("market_price not in", values, "marketPrice");
            return (Criteria) this;
        }

        public Criteria andMarketPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("market_price between", value1, value2, "marketPrice");
            return (Criteria) this;
        }

        public Criteria andMarketPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("market_price not between", value1, value2, "marketPrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceIsNull() {
            addCriterion("sale_price is null");
            return (Criteria) this;
        }

        public Criteria andSalePriceIsNotNull() {
            addCriterion("sale_price is not null");
            return (Criteria) this;
        }

        public Criteria andSalePriceEqualTo(BigDecimal value) {
            addCriterion("sale_price =", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("sale_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalePriceNotEqualTo(BigDecimal value) {
            addCriterion("sale_price <>", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("sale_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalePriceGreaterThan(BigDecimal value) {
            addCriterion("sale_price >", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("sale_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalePriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("sale_price >=", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("sale_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalePriceLessThan(BigDecimal value) {
            addCriterion("sale_price <", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("sale_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalePriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("sale_price <=", value, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("sale_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSalePriceIn(List<BigDecimal> values) {
            addCriterion("sale_price in", values, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceNotIn(List<BigDecimal> values) {
            addCriterion("sale_price not in", values, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sale_price between", value1, value2, "salePrice");
            return (Criteria) this;
        }

        public Criteria andSalePriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("sale_price not between", value1, value2, "salePrice");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameIsNull() {
            addCriterion("product_manager_name is null");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameIsNotNull() {
            addCriterion("product_manager_name is not null");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameEqualTo(String value) {
            addCriterion("product_manager_name =", value, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_manager_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductManagerNameNotEqualTo(String value) {
            addCriterion("product_manager_name <>", value, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_manager_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductManagerNameGreaterThan(String value) {
            addCriterion("product_manager_name >", value, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_manager_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductManagerNameGreaterThanOrEqualTo(String value) {
            addCriterion("product_manager_name >=", value, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_manager_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductManagerNameLessThan(String value) {
            addCriterion("product_manager_name <", value, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_manager_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductManagerNameLessThanOrEqualTo(String value) {
            addCriterion("product_manager_name <=", value, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_manager_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductManagerNameLike(String value) {
            addCriterion("product_manager_name like", value, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameNotLike(String value) {
            addCriterion("product_manager_name not like", value, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameIn(List<String> values) {
            addCriterion("product_manager_name in", values, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameNotIn(List<String> values) {
            addCriterion("product_manager_name not in", values, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameBetween(String value1, String value2) {
            addCriterion("product_manager_name between", value1, value2, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameNotBetween(String value1, String value2) {
            addCriterion("product_manager_name not between", value1, value2, "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneIsNull() {
            addCriterion("product_manager_phone is null");
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneIsNotNull() {
            addCriterion("product_manager_phone is not null");
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneEqualTo(String value) {
            addCriterion("product_manager_phone =", value, "productManagerPhone");
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_manager_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneNotEqualTo(String value) {
            addCriterion("product_manager_phone <>", value, "productManagerPhone");
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_manager_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneGreaterThan(String value) {
            addCriterion("product_manager_phone >", value, "productManagerPhone");
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_manager_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("product_manager_phone >=", value, "productManagerPhone");
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_manager_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneLessThan(String value) {
            addCriterion("product_manager_phone <", value, "productManagerPhone");
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_manager_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneLessThanOrEqualTo(String value) {
            addCriterion("product_manager_phone <=", value, "productManagerPhone");
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_manager_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneLike(String value) {
            addCriterion("product_manager_phone like", value, "productManagerPhone");
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneNotLike(String value) {
            addCriterion("product_manager_phone not like", value, "productManagerPhone");
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneIn(List<String> values) {
            addCriterion("product_manager_phone in", values, "productManagerPhone");
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneNotIn(List<String> values) {
            addCriterion("product_manager_phone not in", values, "productManagerPhone");
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneBetween(String value1, String value2) {
            addCriterion("product_manager_phone between", value1, value2, "productManagerPhone");
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneNotBetween(String value1, String value2) {
            addCriterion("product_manager_phone not between", value1, value2, "productManagerPhone");
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailIsNull() {
            addCriterion("product_manager_email is null");
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailIsNotNull() {
            addCriterion("product_manager_email is not null");
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailEqualTo(String value) {
            addCriterion("product_manager_email =", value, "productManagerEmail");
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_manager_email = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailNotEqualTo(String value) {
            addCriterion("product_manager_email <>", value, "productManagerEmail");
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_manager_email <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailGreaterThan(String value) {
            addCriterion("product_manager_email >", value, "productManagerEmail");
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_manager_email > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailGreaterThanOrEqualTo(String value) {
            addCriterion("product_manager_email >=", value, "productManagerEmail");
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_manager_email >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailLessThan(String value) {
            addCriterion("product_manager_email <", value, "productManagerEmail");
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_manager_email < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailLessThanOrEqualTo(String value) {
            addCriterion("product_manager_email <=", value, "productManagerEmail");
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("product_manager_email <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailLike(String value) {
            addCriterion("product_manager_email like", value, "productManagerEmail");
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailNotLike(String value) {
            addCriterion("product_manager_email not like", value, "productManagerEmail");
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailIn(List<String> values) {
            addCriterion("product_manager_email in", values, "productManagerEmail");
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailNotIn(List<String> values) {
            addCriterion("product_manager_email not in", values, "productManagerEmail");
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailBetween(String value1, String value2) {
            addCriterion("product_manager_email between", value1, value2, "productManagerEmail");
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailNotBetween(String value1, String value2) {
            addCriterion("product_manager_email not between", value1, value2, "productManagerEmail");
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdIsNull() {
            addCriterion("request_current_handler_user_id is null");
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdIsNotNull() {
            addCriterion("request_current_handler_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdEqualTo(String value) {
            addCriterion("request_current_handler_user_id =", value, "requestCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("request_current_handler_user_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdNotEqualTo(String value) {
            addCriterion("request_current_handler_user_id <>", value, "requestCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("request_current_handler_user_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdGreaterThan(String value) {
            addCriterion("request_current_handler_user_id >", value, "requestCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("request_current_handler_user_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("request_current_handler_user_id >=", value, "requestCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("request_current_handler_user_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdLessThan(String value) {
            addCriterion("request_current_handler_user_id <", value, "requestCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("request_current_handler_user_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdLessThanOrEqualTo(String value) {
            addCriterion("request_current_handler_user_id <=", value, "requestCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("request_current_handler_user_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdLike(String value) {
            addCriterion("request_current_handler_user_id like", value, "requestCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdNotLike(String value) {
            addCriterion("request_current_handler_user_id not like", value, "requestCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdIn(List<String> values) {
            addCriterion("request_current_handler_user_id in", values, "requestCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdNotIn(List<String> values) {
            addCriterion("request_current_handler_user_id not in", values, "requestCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdBetween(String value1, String value2) {
            addCriterion("request_current_handler_user_id between", value1, value2, "requestCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdNotBetween(String value1, String value2) {
            addCriterion("request_current_handler_user_id not between", value1, value2, "requestCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdIsNull() {
            addCriterion("online_offline_current_handler_user_id is null");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdIsNotNull() {
            addCriterion("online_offline_current_handler_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdEqualTo(String value) {
            addCriterion("online_offline_current_handler_user_id =", value, "onlineOfflineCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("online_offline_current_handler_user_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdNotEqualTo(String value) {
            addCriterion("online_offline_current_handler_user_id <>", value, "onlineOfflineCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("online_offline_current_handler_user_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdGreaterThan(String value) {
            addCriterion("online_offline_current_handler_user_id >", value, "onlineOfflineCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("online_offline_current_handler_user_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("online_offline_current_handler_user_id >=", value, "onlineOfflineCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("online_offline_current_handler_user_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdLessThan(String value) {
            addCriterion("online_offline_current_handler_user_id <", value, "onlineOfflineCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("online_offline_current_handler_user_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdLessThanOrEqualTo(String value) {
            addCriterion("online_offline_current_handler_user_id <=", value, "onlineOfflineCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("online_offline_current_handler_user_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdLike(String value) {
            addCriterion("online_offline_current_handler_user_id like", value, "onlineOfflineCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdNotLike(String value) {
            addCriterion("online_offline_current_handler_user_id not like", value, "onlineOfflineCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdIn(List<String> values) {
            addCriterion("online_offline_current_handler_user_id in", values, "onlineOfflineCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdNotIn(List<String> values) {
            addCriterion("online_offline_current_handler_user_id not in", values, "onlineOfflineCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdBetween(String value1, String value2) {
            addCriterion("online_offline_current_handler_user_id between", value1, value2, "onlineOfflineCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdNotBetween(String value1, String value2) {
            addCriterion("online_offline_current_handler_user_id not between", value1, value2, "onlineOfflineCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("creator = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("creator <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("creator > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("creator >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("creator < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("creator <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(NewProductRequestManage.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andRequestNoLikeInsensitive(String value) {
            addCriterion("upper(request_no) like", value.toUpperCase(), "requestNo");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingNameLikeInsensitive(String value) {
            addCriterion("upper(spu_offering_name) like", value.toUpperCase(), "spuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(spu_offering_code) like", value.toUpperCase(), "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameLikeInsensitive(String value) {
            addCriterion("upper(sku_offering_name) like", value.toUpperCase(), "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(sku_offering_code) like", value.toUpperCase(), "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andOnlineStatusLikeInsensitive(String value) {
            addCriterion("upper(online_status) like", value.toUpperCase(), "onlineStatus");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLikeInsensitive(String value) {
            addCriterion("upper(cooperator_id) like", value.toUpperCase(), "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andBrandLikeInsensitive(String value) {
            addCriterion("upper(brand) like", value.toUpperCase(), "brand");
            return (Criteria) this;
        }

        public Criteria andModelLikeInsensitive(String value) {
            addCriterion("upper(model) like", value.toUpperCase(), "model");
            return (Criteria) this;
        }

        public Criteria andColorLikeInsensitive(String value) {
            addCriterion("upper(color) like", value.toUpperCase(), "color");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLikeInsensitive(String value) {
            addCriterion("upper(material_code) like", value.toUpperCase(), "materialCode");
            return (Criteria) this;
        }

        public Criteria andNetworkPropertyLikeInsensitive(String value) {
            addCriterion("upper(network_property) like", value.toUpperCase(), "networkProperty");
            return (Criteria) this;
        }

        public Criteria andApplicationDomainLikeInsensitive(String value) {
            addCriterion("upper(application_domain) like", value.toUpperCase(), "applicationDomain");
            return (Criteria) this;
        }

        public Criteria andProductIntroductionLikeInsensitive(String value) {
            addCriterion("upper(product_introduction) like", value.toUpperCase(), "productIntroduction");
            return (Criteria) this;
        }

        public Criteria andProductSaleContentLikeInsensitive(String value) {
            addCriterion("upper(product_sale_content) like", value.toUpperCase(), "productSaleContent");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaCodeLikeInsensitive(String value) {
            addCriterion("upper(product_sale_area_code) like", value.toUpperCase(), "productSaleAreaCode");
            return (Criteria) this;
        }

        public Criteria andProductSaleAreaLikeInsensitive(String value) {
            addCriterion("upper(product_sale_area) like", value.toUpperCase(), "productSaleArea");
            return (Criteria) this;
        }

        public Criteria andProductManagerNameLikeInsensitive(String value) {
            addCriterion("upper(product_manager_name) like", value.toUpperCase(), "productManagerName");
            return (Criteria) this;
        }

        public Criteria andProductManagerPhoneLikeInsensitive(String value) {
            addCriterion("upper(product_manager_phone) like", value.toUpperCase(), "productManagerPhone");
            return (Criteria) this;
        }

        public Criteria andProductManagerEmailLikeInsensitive(String value) {
            addCriterion("upper(product_manager_email) like", value.toUpperCase(), "productManagerEmail");
            return (Criteria) this;
        }

        public Criteria andRequestCurrentHandlerUserIdLikeInsensitive(String value) {
            addCriterion("upper(request_current_handler_user_id) like", value.toUpperCase(), "requestCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andOnlineOfflineCurrentHandlerUserIdLikeInsensitive(String value) {
            addCriterion("upper(online_offline_current_handler_user_id) like", value.toUpperCase(), "onlineOfflineCurrentHandlerUserId");
            return (Criteria) this;
        }

        public Criteria andCreatorLikeInsensitive(String value) {
            addCriterion("upper(creator) like", value.toUpperCase(), "creator");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        private NewProductRequestManageExample example;

        protected Criteria(NewProductRequestManageExample example) {
            super();
            this.example = example;
        }

        public NewProductRequestManageExample example() {
            return this.example;
        }

        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            Criteria add(Criteria add);
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        void example(com.chinamobile.iot.sc.pojo.NewProductRequestManageExample example);
    }
}