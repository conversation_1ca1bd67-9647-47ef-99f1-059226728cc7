package com.chinamobile.iot.sc.response.iot;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/29
 * @description 卡状态（生命周期）返回类
 */
@Data
public class QuerySubscriberStatusResponse {

    /**
     * 号码状态
     * 1: 待激活
     * 2: 已激活
     * 4: 停机
     * 6: 可测试
     * 7: 库存
     * 8: 预销户
     */
    private String status;

    /**
     * 状态变更时间
     * 最近一次状态变更的时间，格式：yyyyMMddhhmmss
     */
    private String statusTime;

}
