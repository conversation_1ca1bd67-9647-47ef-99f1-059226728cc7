package com.chinamobile.iot.sc.pojo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 服务包
 *
 * <AUTHOR>
public class ServicePack implements Serializable {
    /**
     * 主键
     *
     *
     * @mbg.generated Fri Nov 10 17:05:21 CST 2023
     */
    private String id;

    /**
     * 服务包名称
     *
     *
     * @mbg.generated Fri Nov 10 17:05:21 CST 2023
     */
    private String name;

    /**
     *
     * @mbg.generated Fri Nov 10 17:05:21 CST 2023
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..service_pack.id
     *
     * @return the value of supply_chain..service_pack.id
     *
     * @mbg.generated Fri Nov 10 17:05:21 CST 2023
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Fri Nov 10 17:05:21 CST 2023
     */
    public ServicePack withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..service_pack.id
     *
     * @param id the value for supply_chain..service_pack.id
     *
     * @mbg.generated Fri Nov 10 17:05:21 CST 2023
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..service_pack.name
     *
     * @return the value of supply_chain..service_pack.name
     *
     * @mbg.generated Fri Nov 10 17:05:21 CST 2023
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Fri Nov 10 17:05:21 CST 2023
     */
    public ServicePack withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..service_pack.name
     *
     * @param name the value for supply_chain..service_pack.name
     *
     * @mbg.generated Fri Nov 10 17:05:21 CST 2023
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     *
     * @mbg.generated Fri Nov 10 17:05:21 CST 2023
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Fri Nov 10 17:05:21 CST 2023
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ServicePack other = (ServicePack) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()));
    }

    /**
     *
     * @mbg.generated Fri Nov 10 17:05:21 CST 2023
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Fri Nov 10 17:05:21 CST 2023
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        name("name", "name", "VARCHAR", false);

        /**
         *
         * @mbg.generated Fri Nov 10 17:05:21 CST 2023
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Fri Nov 10 17:05:21 CST 2023
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Fri Nov 10 17:05:21 CST 2023
         */
        private final String column;

        /**
         *
         * @mbg.generated Fri Nov 10 17:05:21 CST 2023
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Fri Nov 10 17:05:21 CST 2023
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Fri Nov 10 17:05:21 CST 2023
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Fri Nov 10 17:05:21 CST 2023
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri Nov 10 17:05:21 CST 2023
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Fri Nov 10 17:05:21 CST 2023
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Fri Nov 10 17:05:21 CST 2023
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Fri Nov 10 17:05:21 CST 2023
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Fri Nov 10 17:05:21 CST 2023
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Fri Nov 10 17:05:21 CST 2023
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Fri Nov 10 17:05:21 CST 2023
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Fri Nov 10 17:05:21 CST 2023
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Fri Nov 10 17:05:21 CST 2023
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Fri Nov 10 17:05:21 CST 2023
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}