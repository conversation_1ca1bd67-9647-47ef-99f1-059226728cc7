package com.chinamobile.iot.sc.service;

import com.chinamobile.iot.sc.mode.IOTAnswer;
import com.chinamobile.iot.sc.mode.IOTRequest;
import com.chinamobile.iot.sc.pojo.param.LoginStatusQueryParam;
import com.chinamobile.iot.sc.pojo.vo.LoginStatusQueryVO;
import com.chinamobile.iot.sc.response.iot.SsoLoginResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/26 15:19
 * @description 商城单点登录服务接口
 */
public interface SsoService {

    /**
     * 商城免认证登录接口
     * @param request
     * @return
     */
    IOTAnswer<SsoLoginResponse> authenticationExemptLogin(IOTRequest request);

    LoginStatusQueryVO loginStatusQuery(LoginStatusQueryParam param);
}
