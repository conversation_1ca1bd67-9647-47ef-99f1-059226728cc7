package com.chinamobile.iot.sc.pojo.vo;

import com.chinamobile.iot.sc.pojo.ProductFlow;
import com.chinamobile.iot.sc.pojo.ProductFlowStep;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * created by l<PERSON><PERSON><PERSON> on 2024/3/8 11:00
 */
@Data
public class ProductFlowDetailVO {

    @NotEmpty(message = "id不能为空")
    private String id;

    /**
     * 流程编号
     */
    private String number;

    /**
     * 流程类型 1-产品上架 2-产品下架 3-销售价变更 4-结算价变更 5-非价格信息变更 6-商品所有信息变更(仅限统一运营类)
     */
    private Integer flowType;

    /**
     * 运营类型 1-分省运营类 2-统一运营类
     */
    private Integer operateType;

    /**
     * 流程状态 0-生效 1-失效
     */
    private Integer status;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 流程图
     */
    private byte[] image;

    /**
     * 流程环节
     */
    @Valid
    @NotEmpty
    @Size(min = 3,message = "流程环节最少3个")
    private List<StepInfo> stepInfoList;


    @Data
    public static class StepInfo{

        /**
         * 步骤id
         */
        private String id;


        /**
         * 步骤序号，从1开始叠加
         */
        @NotNull(message = "环节序号不能为空")
        private Integer stepIndex;

        /**
         * 步骤名称
         */
        @NotEmpty(message = "环节名称不能为空")
        private String stepName;

        /**
         * 提示语
         */
        @NotEmpty(message = "提示语不能为空")
        private String tip;

        /**
         * 本环节处理人角色id
         */
        private String assigneeRoleId;

        /**
         * 本环节处理人角色名称
         */
        private String assigneeRoleName;


        /**
         * 驳回当前步骤后的下一步id
         */
        private String rejectNextStepId;

        /**
         * 驳回当前步骤后的下一步名称
         */
        private String rejectNextStepName;

        /**
         * 可否转办
         */
        private Boolean allowRedirect;

        /**
         * 接收转办的角色id
         */
        private String redirectRoleId;

        /**
         * 接收转办的角名称
         */
        private String redirectRoleName;

        /**
         * 可否知悉
         */
        private Boolean allowKnown;

        /**
         * 接收知悉的角色id
         */
        private String knownRoleId;

        /**
         * 接收知悉的角色名称
         */
        private String knownRoleName;

        /**
         * 是否新增限制
         */
        private Boolean allowLimit;

        /**
         * 限制条件枚举值: 1.供应额度不足
         */
        private Integer limitId;

        /**
         * 限制条件名称
         */
        private String limitName;

        /**
         * 限制条件逻辑:下一环节审批角色转为知悉（供应额度不足）
         */
        private String limitResult;
    }

}
