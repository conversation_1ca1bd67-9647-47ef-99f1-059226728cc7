package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ContractMaterialDetailItemVO {

    /**
     * 商品绑定物料表id
     */
    private String kpmId;

    /**
     * 不含税单价
     */
    private BigDecimal unitPrice;

    /**
     * 含税单价
     */
    private BigDecimal discountedUnitPrice;

    /**
     * 税率
     */
    private String taxRate;

    /**
     * 物料编码
     */
    private String materialsCode;

    /**
     * 物料描述
     */
    private String materialsDesc;

    /**
     * 数量
     */
    private BigDecimal materialsNumber;

    /**
     * 物料单位
     */
    private String materialsUnit;

    /**
     * 套编码
     */
    private String offerSetCode;

    /**
     * 配件编码
     */
    private String offerItemCode;

    /**
     * 配件名称
     */
    private String offerItemName;

    /**
     * 创建日期
     */
    private String createDate;

    /**
     * 更新日期
     */
    private String lastUpdateDate;

    /**
     * OU编码 省市编码
     */
    private String ouCode;
    /**
     * OU名称 省市名称
     */
    private String ouName;

}
