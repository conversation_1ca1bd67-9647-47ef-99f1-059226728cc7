package com.chinamobile.iot.sc.pojo;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SyncPaymentStatusExample {
    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    public SyncPaymentStatusExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    public SyncPaymentStatusExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    public SyncPaymentStatusExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    public static Criteria newAndCreateCriteria() {
        SyncPaymentStatusExample example = new SyncPaymentStatusExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    public SyncPaymentStatusExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    public SyncPaymentStatusExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSegment1IsNull() {
            addCriterion("segment1 is null");
            return (Criteria) this;
        }

        public Criteria andSegment1IsNotNull() {
            addCriterion("segment1 is not null");
            return (Criteria) this;
        }

        public Criteria andSegment1EqualTo(String value) {
            addCriterion("segment1 =", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1EqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("segment1 = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSegment1NotEqualTo(String value) {
            addCriterion("segment1 <>", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1NotEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("segment1 <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSegment1GreaterThan(String value) {
            addCriterion("segment1 >", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1GreaterThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("segment1 > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSegment1GreaterThanOrEqualTo(String value) {
            addCriterion("segment1 >=", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1GreaterThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("segment1 >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSegment1LessThan(String value) {
            addCriterion("segment1 <", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1LessThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("segment1 < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSegment1LessThanOrEqualTo(String value) {
            addCriterion("segment1 <=", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1LessThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("segment1 <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSegment1Like(String value) {
            addCriterion("segment1 like", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1NotLike(String value) {
            addCriterion("segment1 not like", value, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1In(List<String> values) {
            addCriterion("segment1 in", values, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1NotIn(List<String> values) {
            addCriterion("segment1 not in", values, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1Between(String value1, String value2) {
            addCriterion("segment1 between", value1, value2, "segment1");
            return (Criteria) this;
        }

        public Criteria andSegment1NotBetween(String value1, String value2) {
            addCriterion("segment1 not between", value1, value2, "segment1");
            return (Criteria) this;
        }

        public Criteria andIotMallNumberIsNull() {
            addCriterion("iot_mall_number is null");
            return (Criteria) this;
        }

        public Criteria andIotMallNumberIsNotNull() {
            addCriterion("iot_mall_number is not null");
            return (Criteria) this;
        }

        public Criteria andIotMallNumberEqualTo(String value) {
            addCriterion("iot_mall_number =", value, "iotMallNumber");
            return (Criteria) this;
        }

        public Criteria andIotMallNumberEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("iot_mall_number = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotMallNumberNotEqualTo(String value) {
            addCriterion("iot_mall_number <>", value, "iotMallNumber");
            return (Criteria) this;
        }

        public Criteria andIotMallNumberNotEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("iot_mall_number <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotMallNumberGreaterThan(String value) {
            addCriterion("iot_mall_number >", value, "iotMallNumber");
            return (Criteria) this;
        }

        public Criteria andIotMallNumberGreaterThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("iot_mall_number > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotMallNumberGreaterThanOrEqualTo(String value) {
            addCriterion("iot_mall_number >=", value, "iotMallNumber");
            return (Criteria) this;
        }

        public Criteria andIotMallNumberGreaterThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("iot_mall_number >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotMallNumberLessThan(String value) {
            addCriterion("iot_mall_number <", value, "iotMallNumber");
            return (Criteria) this;
        }

        public Criteria andIotMallNumberLessThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("iot_mall_number < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotMallNumberLessThanOrEqualTo(String value) {
            addCriterion("iot_mall_number <=", value, "iotMallNumber");
            return (Criteria) this;
        }

        public Criteria andIotMallNumberLessThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("iot_mall_number <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIotMallNumberLike(String value) {
            addCriterion("iot_mall_number like", value, "iotMallNumber");
            return (Criteria) this;
        }

        public Criteria andIotMallNumberNotLike(String value) {
            addCriterion("iot_mall_number not like", value, "iotMallNumber");
            return (Criteria) this;
        }

        public Criteria andIotMallNumberIn(List<String> values) {
            addCriterion("iot_mall_number in", values, "iotMallNumber");
            return (Criteria) this;
        }

        public Criteria andIotMallNumberNotIn(List<String> values) {
            addCriterion("iot_mall_number not in", values, "iotMallNumber");
            return (Criteria) this;
        }

        public Criteria andIotMallNumberBetween(String value1, String value2) {
            addCriterion("iot_mall_number between", value1, value2, "iotMallNumber");
            return (Criteria) this;
        }

        public Criteria andIotMallNumberNotBetween(String value1, String value2) {
            addCriterion("iot_mall_number not between", value1, value2, "iotMallNumber");
            return (Criteria) this;
        }

        public Criteria andPoSegment1IsNull() {
            addCriterion("po_segment1 is null");
            return (Criteria) this;
        }

        public Criteria andPoSegment1IsNotNull() {
            addCriterion("po_segment1 is not null");
            return (Criteria) this;
        }

        public Criteria andPoSegment1EqualTo(String value) {
            addCriterion("po_segment1 =", value, "poSegment1");
            return (Criteria) this;
        }

        public Criteria andPoSegment1EqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("po_segment1 = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPoSegment1NotEqualTo(String value) {
            addCriterion("po_segment1 <>", value, "poSegment1");
            return (Criteria) this;
        }

        public Criteria andPoSegment1NotEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("po_segment1 <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPoSegment1GreaterThan(String value) {
            addCriterion("po_segment1 >", value, "poSegment1");
            return (Criteria) this;
        }

        public Criteria andPoSegment1GreaterThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("po_segment1 > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPoSegment1GreaterThanOrEqualTo(String value) {
            addCriterion("po_segment1 >=", value, "poSegment1");
            return (Criteria) this;
        }

        public Criteria andPoSegment1GreaterThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("po_segment1 >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPoSegment1LessThan(String value) {
            addCriterion("po_segment1 <", value, "poSegment1");
            return (Criteria) this;
        }

        public Criteria andPoSegment1LessThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("po_segment1 < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPoSegment1LessThanOrEqualTo(String value) {
            addCriterion("po_segment1 <=", value, "poSegment1");
            return (Criteria) this;
        }

        public Criteria andPoSegment1LessThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("po_segment1 <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPoSegment1Like(String value) {
            addCriterion("po_segment1 like", value, "poSegment1");
            return (Criteria) this;
        }

        public Criteria andPoSegment1NotLike(String value) {
            addCriterion("po_segment1 not like", value, "poSegment1");
            return (Criteria) this;
        }

        public Criteria andPoSegment1In(List<String> values) {
            addCriterion("po_segment1 in", values, "poSegment1");
            return (Criteria) this;
        }

        public Criteria andPoSegment1NotIn(List<String> values) {
            addCriterion("po_segment1 not in", values, "poSegment1");
            return (Criteria) this;
        }

        public Criteria andPoSegment1Between(String value1, String value2) {
            addCriterion("po_segment1 between", value1, value2, "poSegment1");
            return (Criteria) this;
        }

        public Criteria andPoSegment1NotBetween(String value1, String value2) {
            addCriterion("po_segment1 not between", value1, value2, "poSegment1");
            return (Criteria) this;
        }

        public Criteria andReimNoIsNull() {
            addCriterion("reim_no is null");
            return (Criteria) this;
        }

        public Criteria andReimNoIsNotNull() {
            addCriterion("reim_no is not null");
            return (Criteria) this;
        }

        public Criteria andReimNoEqualTo(String value) {
            addCriterion("reim_no =", value, "reimNo");
            return (Criteria) this;
        }

        public Criteria andReimNoEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("reim_no = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReimNoNotEqualTo(String value) {
            addCriterion("reim_no <>", value, "reimNo");
            return (Criteria) this;
        }

        public Criteria andReimNoNotEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("reim_no <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReimNoGreaterThan(String value) {
            addCriterion("reim_no >", value, "reimNo");
            return (Criteria) this;
        }

        public Criteria andReimNoGreaterThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("reim_no > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReimNoGreaterThanOrEqualTo(String value) {
            addCriterion("reim_no >=", value, "reimNo");
            return (Criteria) this;
        }

        public Criteria andReimNoGreaterThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("reim_no >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReimNoLessThan(String value) {
            addCriterion("reim_no <", value, "reimNo");
            return (Criteria) this;
        }

        public Criteria andReimNoLessThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("reim_no < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReimNoLessThanOrEqualTo(String value) {
            addCriterion("reim_no <=", value, "reimNo");
            return (Criteria) this;
        }

        public Criteria andReimNoLessThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("reim_no <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReimNoLike(String value) {
            addCriterion("reim_no like", value, "reimNo");
            return (Criteria) this;
        }

        public Criteria andReimNoNotLike(String value) {
            addCriterion("reim_no not like", value, "reimNo");
            return (Criteria) this;
        }

        public Criteria andReimNoIn(List<String> values) {
            addCriterion("reim_no in", values, "reimNo");
            return (Criteria) this;
        }

        public Criteria andReimNoNotIn(List<String> values) {
            addCriterion("reim_no not in", values, "reimNo");
            return (Criteria) this;
        }

        public Criteria andReimNoBetween(String value1, String value2) {
            addCriterion("reim_no between", value1, value2, "reimNo");
            return (Criteria) this;
        }

        public Criteria andReimNoNotBetween(String value1, String value2) {
            addCriterion("reim_no not between", value1, value2, "reimNo");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountIsNull() {
            addCriterion("payment_amount is null");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountIsNotNull() {
            addCriterion("payment_amount is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountEqualTo(BigDecimal value) {
            addCriterion("payment_amount =", value, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("payment_amount = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPaymentAmountNotEqualTo(BigDecimal value) {
            addCriterion("payment_amount <>", value, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountNotEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("payment_amount <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPaymentAmountGreaterThan(BigDecimal value) {
            addCriterion("payment_amount >", value, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountGreaterThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("payment_amount > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPaymentAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("payment_amount >=", value, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountGreaterThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("payment_amount >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPaymentAmountLessThan(BigDecimal value) {
            addCriterion("payment_amount <", value, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountLessThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("payment_amount < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPaymentAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("payment_amount <=", value, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountLessThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("payment_amount <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPaymentAmountIn(List<BigDecimal> values) {
            addCriterion("payment_amount in", values, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountNotIn(List<BigDecimal> values) {
            addCriterion("payment_amount not in", values, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("payment_amount between", value1, value2, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andPaymentAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("payment_amount not between", value1, value2, "paymentAmount");
            return (Criteria) this;
        }

        public Criteria andStatusCodeIsNull() {
            addCriterion("status_code is null");
            return (Criteria) this;
        }

        public Criteria andStatusCodeIsNotNull() {
            addCriterion("status_code is not null");
            return (Criteria) this;
        }

        public Criteria andStatusCodeEqualTo(String value) {
            addCriterion("status_code =", value, "statusCode");
            return (Criteria) this;
        }

        public Criteria andStatusCodeEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("status_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusCodeNotEqualTo(String value) {
            addCriterion("status_code <>", value, "statusCode");
            return (Criteria) this;
        }

        public Criteria andStatusCodeNotEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("status_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusCodeGreaterThan(String value) {
            addCriterion("status_code >", value, "statusCode");
            return (Criteria) this;
        }

        public Criteria andStatusCodeGreaterThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("status_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusCodeGreaterThanOrEqualTo(String value) {
            addCriterion("status_code >=", value, "statusCode");
            return (Criteria) this;
        }

        public Criteria andStatusCodeGreaterThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("status_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusCodeLessThan(String value) {
            addCriterion("status_code <", value, "statusCode");
            return (Criteria) this;
        }

        public Criteria andStatusCodeLessThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("status_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusCodeLessThanOrEqualTo(String value) {
            addCriterion("status_code <=", value, "statusCode");
            return (Criteria) this;
        }

        public Criteria andStatusCodeLessThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("status_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusCodeLike(String value) {
            addCriterion("status_code like", value, "statusCode");
            return (Criteria) this;
        }

        public Criteria andStatusCodeNotLike(String value) {
            addCriterion("status_code not like", value, "statusCode");
            return (Criteria) this;
        }

        public Criteria andStatusCodeIn(List<String> values) {
            addCriterion("status_code in", values, "statusCode");
            return (Criteria) this;
        }

        public Criteria andStatusCodeNotIn(List<String> values) {
            addCriterion("status_code not in", values, "statusCode");
            return (Criteria) this;
        }

        public Criteria andStatusCodeBetween(String value1, String value2) {
            addCriterion("status_code between", value1, value2, "statusCode");
            return (Criteria) this;
        }

        public Criteria andStatusCodeNotBetween(String value1, String value2) {
            addCriterion("status_code not between", value1, value2, "statusCode");
            return (Criteria) this;
        }

        public Criteria andStatusNameIsNull() {
            addCriterion("status_name is null");
            return (Criteria) this;
        }

        public Criteria andStatusNameIsNotNull() {
            addCriterion("status_name is not null");
            return (Criteria) this;
        }

        public Criteria andStatusNameEqualTo(String value) {
            addCriterion("status_name =", value, "statusName");
            return (Criteria) this;
        }

        public Criteria andStatusNameEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("status_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusNameNotEqualTo(String value) {
            addCriterion("status_name <>", value, "statusName");
            return (Criteria) this;
        }

        public Criteria andStatusNameNotEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("status_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusNameGreaterThan(String value) {
            addCriterion("status_name >", value, "statusName");
            return (Criteria) this;
        }

        public Criteria andStatusNameGreaterThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("status_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusNameGreaterThanOrEqualTo(String value) {
            addCriterion("status_name >=", value, "statusName");
            return (Criteria) this;
        }

        public Criteria andStatusNameGreaterThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("status_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusNameLessThan(String value) {
            addCriterion("status_name <", value, "statusName");
            return (Criteria) this;
        }

        public Criteria andStatusNameLessThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("status_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusNameLessThanOrEqualTo(String value) {
            addCriterion("status_name <=", value, "statusName");
            return (Criteria) this;
        }

        public Criteria andStatusNameLessThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("status_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andStatusNameLike(String value) {
            addCriterion("status_name like", value, "statusName");
            return (Criteria) this;
        }

        public Criteria andStatusNameNotLike(String value) {
            addCriterion("status_name not like", value, "statusName");
            return (Criteria) this;
        }

        public Criteria andStatusNameIn(List<String> values) {
            addCriterion("status_name in", values, "statusName");
            return (Criteria) this;
        }

        public Criteria andStatusNameNotIn(List<String> values) {
            addCriterion("status_name not in", values, "statusName");
            return (Criteria) this;
        }

        public Criteria andStatusNameBetween(String value1, String value2) {
            addCriterion("status_name between", value1, value2, "statusName");
            return (Criteria) this;
        }

        public Criteria andStatusNameNotBetween(String value1, String value2) {
            addCriterion("status_name not between", value1, value2, "statusName");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNull() {
            addCriterion("province is null");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNotNull() {
            addCriterion("province is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceEqualTo(String value) {
            addCriterion("province =", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("province = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceNotEqualTo(String value) {
            addCriterion("province <>", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("province <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThan(String value) {
            addCriterion("province >", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("province > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("province >=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("province >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceLessThan(String value) {
            addCriterion("province <", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("province < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceLessThanOrEqualTo(String value) {
            addCriterion("province <=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("province <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceLike(String value) {
            addCriterion("province like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotLike(String value) {
            addCriterion("province not like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceIn(List<String> values) {
            addCriterion("province in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotIn(List<String> values) {
            addCriterion("province not in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceBetween(String value1, String value2) {
            addCriterion("province between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotBetween(String value1, String value2) {
            addCriterion("province not between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andSubscriberIsNull() {
            addCriterion("subscriber is null");
            return (Criteria) this;
        }

        public Criteria andSubscriberIsNotNull() {
            addCriterion("subscriber is not null");
            return (Criteria) this;
        }

        public Criteria andSubscriberEqualTo(String value) {
            addCriterion("subscriber =", value, "subscriber");
            return (Criteria) this;
        }

        public Criteria andSubscriberEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("subscriber = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSubscriberNotEqualTo(String value) {
            addCriterion("subscriber <>", value, "subscriber");
            return (Criteria) this;
        }

        public Criteria andSubscriberNotEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("subscriber <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSubscriberGreaterThan(String value) {
            addCriterion("subscriber >", value, "subscriber");
            return (Criteria) this;
        }

        public Criteria andSubscriberGreaterThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("subscriber > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSubscriberGreaterThanOrEqualTo(String value) {
            addCriterion("subscriber >=", value, "subscriber");
            return (Criteria) this;
        }

        public Criteria andSubscriberGreaterThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("subscriber >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSubscriberLessThan(String value) {
            addCriterion("subscriber <", value, "subscriber");
            return (Criteria) this;
        }

        public Criteria andSubscriberLessThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("subscriber < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSubscriberLessThanOrEqualTo(String value) {
            addCriterion("subscriber <=", value, "subscriber");
            return (Criteria) this;
        }

        public Criteria andSubscriberLessThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("subscriber <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSubscriberLike(String value) {
            addCriterion("subscriber like", value, "subscriber");
            return (Criteria) this;
        }

        public Criteria andSubscriberNotLike(String value) {
            addCriterion("subscriber not like", value, "subscriber");
            return (Criteria) this;
        }

        public Criteria andSubscriberIn(List<String> values) {
            addCriterion("subscriber in", values, "subscriber");
            return (Criteria) this;
        }

        public Criteria andSubscriberNotIn(List<String> values) {
            addCriterion("subscriber not in", values, "subscriber");
            return (Criteria) this;
        }

        public Criteria andSubscriberBetween(String value1, String value2) {
            addCriterion("subscriber between", value1, value2, "subscriber");
            return (Criteria) this;
        }

        public Criteria andSubscriberNotBetween(String value1, String value2) {
            addCriterion("subscriber not between", value1, value2, "subscriber");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(SyncPaymentStatus.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andSegment1LikeInsensitive(String value) {
            addCriterion("upper(segment1) like", value.toUpperCase(), "segment1");
            return (Criteria) this;
        }

        public Criteria andIotMallNumberLikeInsensitive(String value) {
            addCriterion("upper(iot_mall_number) like", value.toUpperCase(), "iotMallNumber");
            return (Criteria) this;
        }

        public Criteria andPoSegment1LikeInsensitive(String value) {
            addCriterion("upper(po_segment1) like", value.toUpperCase(), "poSegment1");
            return (Criteria) this;
        }

        public Criteria andReimNoLikeInsensitive(String value) {
            addCriterion("upper(reim_no) like", value.toUpperCase(), "reimNo");
            return (Criteria) this;
        }

        public Criteria andStatusCodeLikeInsensitive(String value) {
            addCriterion("upper(status_code) like", value.toUpperCase(), "statusCode");
            return (Criteria) this;
        }

        public Criteria andStatusNameLikeInsensitive(String value) {
            addCriterion("upper(status_name) like", value.toUpperCase(), "statusName");
            return (Criteria) this;
        }

        public Criteria andProvinceLikeInsensitive(String value) {
            addCriterion("upper(province) like", value.toUpperCase(), "province");
            return (Criteria) this;
        }

        public Criteria andSubscriberLikeInsensitive(String value) {
            addCriterion("upper(subscriber) like", value.toUpperCase(), "subscriber");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Thu Mar 23 10:27:27 CST 2023
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Thu Mar 23 10:27:27 CST 2023
         */
        private SyncPaymentStatusExample example;

        /**
         *
         * @mbg.generated Thu Mar 23 10:27:27 CST 2023
         */
        protected Criteria(SyncPaymentStatusExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Thu Mar 23 10:27:27 CST 2023
         */
        public SyncPaymentStatusExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Thu Mar 23 10:27:27 CST 2023
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Mar 23 10:27:27 CST 2023
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Mar 23 10:27:27 CST 2023
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Thu Mar 23 10:27:27 CST 2023
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Thu Mar 23 10:27:27 CST 2023
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Thu Mar 23 10:27:27 CST 2023
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Thu Mar 23 10:27:27 CST 2023
         */
        void example(com.chinamobile.iot.sc.pojo.SyncPaymentStatusExample example);
    }
}