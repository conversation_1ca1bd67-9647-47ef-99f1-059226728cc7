package com.chinamobile.iot.sc.controller.iot;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.pojo.param.IOPUploadParam;
import com.chinamobile.iot.sc.service.IOPService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/os/iop")
public class IOPController {

    @Resource
    private IOPService iopService;

    /**
     * 获取iop校验报告
     *
     * @return
     */
    @PostMapping("/test/report")
    public BaseAnswer testSftpIOPReport() {
        iopService.sftpIOPReport();
        return new BaseAnswer();
    }

    /**
     * 上传iop
     *
     * @return
     */
    @PostMapping("/test/upload")
    public BaseAnswer testSftpUploadIOP(@RequestBody IOPUploadParam param) {
        iopService.sftpUploadIOP(param);
        return new BaseAnswer();
    }
}
