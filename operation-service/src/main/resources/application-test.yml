#iot:
#  encodeKey: 3D88F1C1AAE7
## mybatis-plus:
##   configuration:
##     log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#spring:
#  cloud:
#    nacos:
#      discovery:
#        namespace: kubesphere
#        service: supply-chain-data-svc
#        server-addr: 10.12.57.2:8848
#  datasource:
#    hikari:
#      idle-timeout: 180000
#      max-lifetime: 30000
#      maximum-pool-size: 8
#      minimum-idle: 4
#    dynamic:
#      primary: query
#      strict: false
#      datasource:
#        query:
#          driver-class-name: com.mysql.cj.jdbc.Driver
#          url: **************************************************************************************************************************************
#          password: nxnQcZZwK@e6MQ
#          username: root
#        save:
#          driver-class-name: com.mysql.cj.jdbc.Driver
#          url: ************************************************************************************************************************************
#          password: app_!QAZxsw2
#          username: supply
#  redis:
#    cluster:
#      nodes: 10.12.57.3:6379,10.12.57.4:6379,10.12.57.5:6379
#    password: hK@JY2YCamWPNvg
#    pool:
#      max-active: 8
#      max-idle: 8
#      max-wait: -1
#      min-idle: 0
#    timeout: 5000
#  kafka:
#    bootstrap-servers: **********:9092,**********:9092,**********0:9092
#    consumer:
#      auto-offset-reset: latest
#      group-id: supplyChainDataDorisGroups
#      value-deserializer: org.apache.kafka.common.serialization.ByteArrayDeserializer
#    listener:
#      concurrency: 5
#    producer:
#      retries: 3
#      value-serializer: org.apache.kafka.common.serialization.ByteArraySerializer
#  jackson:
#    date-format: yyyy-MM-dd HH:mm:ss
#    time-zone: GMT+8
#  servlet:
#    multipart:
#      max-file-size: 20MB
#      max-request-size: 30MB
#onenet-storage:
#  queryHttpInner: http://*************:9092/mallos/oss/ #访问代理地址
#  queryHttpOuter: http://**********/mallos/oss/
#  endpoint: http://s3-qos.iot-st-armtest.qiniu-solutions.com
#  bucketName: mallos-test
#  accessKey: W1aowUWsredwHbsuCeLUbI_wXI8_eNJtSWelhbxD
#  secretKey: zht2qc8vrIdCrL50PB5EdFrZSNAApdlOxQ7wZIFD
#sms:
#  newMessage: 107024
##gio埋点配置
#gio:
#  # gio埋点地址
#  gioOpenUrl: https://gio-collect.zhipinmall.com/
#  #gio埋点AccountId
#  gioAccountId: 8f529222143a4f61
#  #gio埋点DataSourceId
#  gioDataSourceId: a3d3403cad6bf830
##省代码配置
#province:
#  code:
#    provinceCodes:
#      - provinceName: 北京
#        provinceCode: Beijing
#      - provinceName: 上海
#        provinceCode: Shanghai
#      - provinceName: 天津
#        provinceCode: Tianjin
#      - provinceName: 重庆
#        provinceCode: Chongqing
#      - provinceName: 河南
#        provinceCode: Henan
#      - provinceName: 河北
#        provinceCode: Hebei
#      - provinceName: 青海
#        provinceCode: Qinghai
#      - provinceName: 安徽
#        provinceCode: Anhui
#      - provinceName: 福建
#        provinceCode: Fujian
#      - provinceName: 广东
#        provinceCode: Guangdong
#      - provinceName: 广西
#        provinceCode: Guangxi
#      - provinceName: 贵州
#        provinceCode: Guizhou
#      - provinceName: 甘肃
#        provinceCode: Gansu
#      - provinceName: 海南
#        provinceCode: Hainan
#      - provinceName: 黑龙江
#        provinceCode: Heilongjiang
#      - provinceName: 湖北
#        provinceCode: Hubei
#      - provinceName: 湖南
#        provinceCode: Hunan
#      - provinceName: 吉林
#        provinceCode: Jilin
#      - provinceName: 江苏
#        provinceCode: Jiangsu
#      - provinceName: 江西
#        provinceCode: Jiangxi
#      - provinceName: 辽宁
#        provinceCode: Liaoning
#      - provinceName: 内蒙古
#        provinceCode: Neimenggu
#      - provinceName: 宁夏
#        provinceCode: Ningxia
#      - provinceName: 陕西
#        provinceCode: Shanxi1
#      - provinceName: 山西
#        provinceCode: Shanxi
#      - provinceName: 山东
#        provinceCode: Shandong
#      - provinceName: 四川
#        provinceCode: Sichuan
#      - provinceName: 西藏
#        provinceCode: Xizang
#      - provinceName: 新疆
#        provinceCode: Xinjiang
#      - provinceName: 云南
#        provinceCode: Yunnan
#      - provinceName: 浙江
#        provinceCode: Zhejiang