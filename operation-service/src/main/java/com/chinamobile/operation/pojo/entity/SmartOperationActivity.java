package com.chinamobile.operation.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 智慧运营-活动表
 *
 * <AUTHOR>
public class SmartOperationActivity implements Serializable {
    /**
     *
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    private String id;

    /**
     * 活动名称
     *
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    private String name;

    /**
     * 触达方式 1-短信 2-公众号 3-小程序
     *
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    private Integer type;

    /**
     * 触发条件 1-单次触发 2-周期触发
     *
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    private Integer startCondition;

    /**
     * 触发内容，对于短信是短信模板ID
     *
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    private String content;

    /**
     * 开始时间,对于单次触发就是触发事件
     *
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    private Date startTime;

    /**
     * 结束时间(单次触发没有结束时间)
     *
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    private Date endTime;

    /**
     * 每周的哪几天触发（如1,2代表每周一和周二触发，-1代表每一天；单次触发和每天触发没有值）
     *
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    private String weekDays;

    /**
     * 每月的哪几天触发（如1,2代表每月的1日和2日触发，-1代表每一天；单次触发和每天触发没有值）
     *
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    private String monthDays;

    /**
     * 触发的具体时间,HH:mm
     *
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    private String taskTime;

    /**
     * 创建人id
     *
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    private String createUserId;

    /**
     * 状态（注意：已开始和已结束通过时间来判断） 0-设计中 1-审批中 2-被驳回 3-待发布（审批通过） 4-未开始(已发布) 6-暂停中 8-关闭
     *
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    private Integer status;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..smart_operation_activity.id
     *
     * @return the value of supply_chain..smart_operation_activity.id
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public SmartOperationActivity withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_activity.id
     *
     * @param id the value for supply_chain..smart_operation_activity.id
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_activity.name
     *
     * @return the value of supply_chain..smart_operation_activity.name
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public SmartOperationActivity withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_activity.name
     *
     * @param name the value for supply_chain..smart_operation_activity.name
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_activity.type
     *
     * @return the value of supply_chain..smart_operation_activity.type
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public Integer getType() {
        return type;
    }

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public SmartOperationActivity withType(Integer type) {
        this.setType(type);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_activity.type
     *
     * @param type the value for supply_chain..smart_operation_activity.type
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_activity.start_condition
     *
     * @return the value of supply_chain..smart_operation_activity.start_condition
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public Integer getStartCondition() {
        return startCondition;
    }

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public SmartOperationActivity withStartCondition(Integer startCondition) {
        this.setStartCondition(startCondition);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_activity.start_condition
     *
     * @param startCondition the value for supply_chain..smart_operation_activity.start_condition
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public void setStartCondition(Integer startCondition) {
        this.startCondition = startCondition;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_activity.content
     *
     * @return the value of supply_chain..smart_operation_activity.content
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public String getContent() {
        return content;
    }

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public SmartOperationActivity withContent(String content) {
        this.setContent(content);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_activity.content
     *
     * @param content the value for supply_chain..smart_operation_activity.content
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_activity.start_time
     *
     * @return the value of supply_chain..smart_operation_activity.start_time
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public SmartOperationActivity withStartTime(Date startTime) {
        this.setStartTime(startTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_activity.start_time
     *
     * @param startTime the value for supply_chain..smart_operation_activity.start_time
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_activity.end_time
     *
     * @return the value of supply_chain..smart_operation_activity.end_time
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public Date getEndTime() {
        return endTime;
    }

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public SmartOperationActivity withEndTime(Date endTime) {
        this.setEndTime(endTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_activity.end_time
     *
     * @param endTime the value for supply_chain..smart_operation_activity.end_time
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_activity.week_days
     *
     * @return the value of supply_chain..smart_operation_activity.week_days
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public String getWeekDays() {
        return weekDays;
    }

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public SmartOperationActivity withWeekDays(String weekDays) {
        this.setWeekDays(weekDays);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_activity.week_days
     *
     * @param weekDays the value for supply_chain..smart_operation_activity.week_days
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public void setWeekDays(String weekDays) {
        this.weekDays = weekDays;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_activity.month_days
     *
     * @return the value of supply_chain..smart_operation_activity.month_days
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public String getMonthDays() {
        return monthDays;
    }

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public SmartOperationActivity withMonthDays(String monthDays) {
        this.setMonthDays(monthDays);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_activity.month_days
     *
     * @param monthDays the value for supply_chain..smart_operation_activity.month_days
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public void setMonthDays(String monthDays) {
        this.monthDays = monthDays;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_activity.task_time
     *
     * @return the value of supply_chain..smart_operation_activity.task_time
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public String getTaskTime() {
        return taskTime;
    }

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public SmartOperationActivity withTaskTime(String taskTime) {
        this.setTaskTime(taskTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_activity.task_time
     *
     * @param taskTime the value for supply_chain..smart_operation_activity.task_time
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public void setTaskTime(String taskTime) {
        this.taskTime = taskTime;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_activity.create_user_id
     *
     * @return the value of supply_chain..smart_operation_activity.create_user_id
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public SmartOperationActivity withCreateUserId(String createUserId) {
        this.setCreateUserId(createUserId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_activity.create_user_id
     *
     * @param createUserId the value for supply_chain..smart_operation_activity.create_user_id
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_activity.status
     *
     * @return the value of supply_chain..smart_operation_activity.status
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public Integer getStatus() {
        return status;
    }

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public SmartOperationActivity withStatus(Integer status) {
        this.setStatus(status);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_activity.status
     *
     * @param status the value for supply_chain..smart_operation_activity.status
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_activity.create_time
     *
     * @return the value of supply_chain..smart_operation_activity.create_time
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public SmartOperationActivity withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_activity.create_time
     *
     * @param createTime the value for supply_chain..smart_operation_activity.create_time
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..smart_operation_activity.update_time
     *
     * @return the value of supply_chain..smart_operation_activity.update_time
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public SmartOperationActivity withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..smart_operation_activity.update_time
     *
     * @param updateTime the value for supply_chain..smart_operation_activity.update_time
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", type=").append(type);
        sb.append(", startCondition=").append(startCondition);
        sb.append(", content=").append(content);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", weekDays=").append(weekDays);
        sb.append(", monthDays=").append(monthDays);
        sb.append(", taskTime=").append(taskTime);
        sb.append(", createUserId=").append(createUserId);
        sb.append(", status=").append(status);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SmartOperationActivity other = (SmartOperationActivity) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getStartCondition() == null ? other.getStartCondition() == null : this.getStartCondition().equals(other.getStartCondition()))
            && (this.getContent() == null ? other.getContent() == null : this.getContent().equals(other.getContent()))
            && (this.getStartTime() == null ? other.getStartTime() == null : this.getStartTime().equals(other.getStartTime()))
            && (this.getEndTime() == null ? other.getEndTime() == null : this.getEndTime().equals(other.getEndTime()))
            && (this.getWeekDays() == null ? other.getWeekDays() == null : this.getWeekDays().equals(other.getWeekDays()))
            && (this.getMonthDays() == null ? other.getMonthDays() == null : this.getMonthDays().equals(other.getMonthDays()))
            && (this.getTaskTime() == null ? other.getTaskTime() == null : this.getTaskTime().equals(other.getTaskTime()))
            && (this.getCreateUserId() == null ? other.getCreateUserId() == null : this.getCreateUserId().equals(other.getCreateUserId()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getStartCondition() == null) ? 0 : getStartCondition().hashCode());
        result = prime * result + ((getContent() == null) ? 0 : getContent().hashCode());
        result = prime * result + ((getStartTime() == null) ? 0 : getStartTime().hashCode());
        result = prime * result + ((getEndTime() == null) ? 0 : getEndTime().hashCode());
        result = prime * result + ((getWeekDays() == null) ? 0 : getWeekDays().hashCode());
        result = prime * result + ((getMonthDays() == null) ? 0 : getMonthDays().hashCode());
        result = prime * result + ((getTaskTime() == null) ? 0 : getTaskTime().hashCode());
        result = prime * result + ((getCreateUserId() == null) ? 0 : getCreateUserId().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Thu Feb 27 16:34:27 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        type("type", "type", "INTEGER", false),
        startCondition("start_condition", "startCondition", "INTEGER", false),
        content("content", "content", "VARCHAR", false),
        startTime("start_time", "startTime", "TIMESTAMP", false),
        endTime("end_time", "endTime", "TIMESTAMP", false),
        weekDays("week_days", "weekDays", "VARCHAR", false),
        monthDays("month_days", "monthDays", "VARCHAR", false),
        taskTime("task_time", "taskTime", "VARCHAR", false),
        createUserId("create_user_id", "createUserId", "VARCHAR", false),
        status("status", "status", "INTEGER", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Thu Feb 27 16:34:27 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Feb 27 16:34:27 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Feb 27 16:34:27 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Thu Feb 27 16:34:27 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Thu Feb 27 16:34:27 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Thu Feb 27 16:34:27 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Thu Feb 27 16:34:27 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Feb 27 16:34:27 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Feb 27 16:34:27 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Thu Feb 27 16:34:27 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Thu Feb 27 16:34:27 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Thu Feb 27 16:34:27 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Thu Feb 27 16:34:27 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Thu Feb 27 16:34:27 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Thu Feb 27 16:34:27 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Thu Feb 27 16:34:27 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Thu Feb 27 16:34:27 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}