package com.chinamobile.activity.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 活动专区的模板
 *
 * <AUTHOR>
public class ActivityTemplate implements Serializable {
    /**
     *
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    private String id;

    /**
     * 模板名称
     *
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    private String name;

    /**
     * 渠道类型，1 --PC; 2-- 移动
     *
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    private Integer channel;

    /**
     * 模板图片的key
     *
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    private String fileKey;

    /**
     * 模板图片url
     *
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    private String url;

    /**
     * 是否可用
     *
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    private Boolean available;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..activity_template.id
     *
     * @return the value of supply_chain..activity_template.id
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public ActivityTemplate withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_template.id
     *
     * @param id the value for supply_chain..activity_template.id
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_template.name
     *
     * @return the value of supply_chain..activity_template.name
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public ActivityTemplate withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_template.name
     *
     * @param name the value for supply_chain..activity_template.name
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_template.channel
     *
     * @return the value of supply_chain..activity_template.channel
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public Integer getChannel() {
        return channel;
    }

    /**
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public ActivityTemplate withChannel(Integer channel) {
        this.setChannel(channel);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_template.channel
     *
     * @param channel the value for supply_chain..activity_template.channel
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_template.file_key
     *
     * @return the value of supply_chain..activity_template.file_key
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public String getFileKey() {
        return fileKey;
    }

    /**
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public ActivityTemplate withFileKey(String fileKey) {
        this.setFileKey(fileKey);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_template.file_key
     *
     * @param fileKey the value for supply_chain..activity_template.file_key
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public void setFileKey(String fileKey) {
        this.fileKey = fileKey;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_template.url
     *
     * @return the value of supply_chain..activity_template.url
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public String getUrl() {
        return url;
    }

    /**
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public ActivityTemplate withUrl(String url) {
        this.setUrl(url);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_template.url
     *
     * @param url the value for supply_chain..activity_template.url
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_template.available
     *
     * @return the value of supply_chain..activity_template.available
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public Boolean getAvailable() {
        return available;
    }

    /**
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public ActivityTemplate withAvailable(Boolean available) {
        this.setAvailable(available);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_template.available
     *
     * @param available the value for supply_chain..activity_template.available
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public void setAvailable(Boolean available) {
        this.available = available;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_template.create_time
     *
     * @return the value of supply_chain..activity_template.create_time
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public ActivityTemplate withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_template.create_time
     *
     * @param createTime the value for supply_chain..activity_template.create_time
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_template.update_time
     *
     * @return the value of supply_chain..activity_template.update_time
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public ActivityTemplate withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_template.update_time
     *
     * @param updateTime the value for supply_chain..activity_template.update_time
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", channel=").append(channel);
        sb.append(", fileKey=").append(fileKey);
        sb.append(", url=").append(url);
        sb.append(", available=").append(available);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ActivityTemplate other = (ActivityTemplate) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getChannel() == null ? other.getChannel() == null : this.getChannel().equals(other.getChannel()))
            && (this.getFileKey() == null ? other.getFileKey() == null : this.getFileKey().equals(other.getFileKey()))
            && (this.getUrl() == null ? other.getUrl() == null : this.getUrl().equals(other.getUrl()))
            && (this.getAvailable() == null ? other.getAvailable() == null : this.getAvailable().equals(other.getAvailable()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getChannel() == null) ? 0 : getChannel().hashCode());
        result = prime * result + ((getFileKey() == null) ? 0 : getFileKey().hashCode());
        result = prime * result + ((getUrl() == null) ? 0 : getUrl().hashCode());
        result = prime * result + ((getAvailable() == null) ? 0 : getAvailable().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Aug 17 14:55:48 CST 2022
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        channel("channel", "channel", "INTEGER", false),
        fileKey("file_key", "fileKey", "VARCHAR", false),
        url("url", "url", "VARCHAR", false),
        available("available", "available", "BIT", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Wed Aug 17 14:55:48 CST 2022
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Aug 17 14:55:48 CST 2022
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Aug 17 14:55:48 CST 2022
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Aug 17 14:55:48 CST 2022
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Aug 17 14:55:48 CST 2022
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Aug 17 14:55:48 CST 2022
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Aug 17 14:55:48 CST 2022
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Aug 17 14:55:48 CST 2022
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Aug 17 14:55:48 CST 2022
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Aug 17 14:55:48 CST 2022
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Aug 17 14:55:48 CST 2022
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Aug 17 14:55:48 CST 2022
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Aug 17 14:55:48 CST 2022
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Aug 17 14:55:48 CST 2022
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Aug 17 14:55:48 CST 2022
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Aug 17 14:55:48 CST 2022
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Aug 17 14:55:48 CST 2022
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}