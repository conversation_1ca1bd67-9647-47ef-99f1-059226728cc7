package com.chinamobile.activity.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 活动的楼层
 *
 * <AUTHOR>
public class ActivityFloor implements Serializable {
    /**
     *
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    private String id;

    /**
     * 活动专区Id
     *
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    private String activityId;

    /**
     * 楼层名称
     *
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    private String name;

    /**
     * 是否展示该楼层
     *
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    private Boolean isShow;

    /**
     * 是否展示二级页面
     *
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    private Boolean isShowSecond;

    /**
     * 是否展示商品标签
     *
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    private Boolean isShowTag;

    /**
     * 商品展示个数,-1表示全部
     *
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    private Integer showNum;

    /**
     * 每行展示个数
     *
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    private Integer numInALine;

    /**
     * 排序，数字越小越靠前
     *
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    private Integer sort;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..activity_floor.id
     *
     * @return the value of supply_chain..activity_floor.id
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public ActivityFloor withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_floor.id
     *
     * @param id the value for supply_chain..activity_floor.id
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_floor.activity_id
     *
     * @return the value of supply_chain..activity_floor.activity_id
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public String getActivityId() {
        return activityId;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public ActivityFloor withActivityId(String activityId) {
        this.setActivityId(activityId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_floor.activity_id
     *
     * @param activityId the value for supply_chain..activity_floor.activity_id
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_floor.name
     *
     * @return the value of supply_chain..activity_floor.name
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public ActivityFloor withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_floor.name
     *
     * @param name the value for supply_chain..activity_floor.name
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_floor.is_show
     *
     * @return the value of supply_chain..activity_floor.is_show
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public Boolean getIsShow() {
        return isShow;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public ActivityFloor withIsShow(Boolean isShow) {
        this.setIsShow(isShow);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_floor.is_show
     *
     * @param isShow the value for supply_chain..activity_floor.is_show
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public void setIsShow(Boolean isShow) {
        this.isShow = isShow;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_floor.is_show_second
     *
     * @return the value of supply_chain..activity_floor.is_show_second
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public Boolean getIsShowSecond() {
        return isShowSecond;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public ActivityFloor withIsShowSecond(Boolean isShowSecond) {
        this.setIsShowSecond(isShowSecond);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_floor.is_show_second
     *
     * @param isShowSecond the value for supply_chain..activity_floor.is_show_second
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public void setIsShowSecond(Boolean isShowSecond) {
        this.isShowSecond = isShowSecond;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_floor.is_show_tag
     *
     * @return the value of supply_chain..activity_floor.is_show_tag
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public Boolean getIsShowTag() {
        return isShowTag;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public ActivityFloor withIsShowTag(Boolean isShowTag) {
        this.setIsShowTag(isShowTag);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_floor.is_show_tag
     *
     * @param isShowTag the value for supply_chain..activity_floor.is_show_tag
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public void setIsShowTag(Boolean isShowTag) {
        this.isShowTag = isShowTag;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_floor.show_num
     *
     * @return the value of supply_chain..activity_floor.show_num
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public Integer getShowNum() {
        return showNum;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public ActivityFloor withShowNum(Integer showNum) {
        this.setShowNum(showNum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_floor.show_num
     *
     * @param showNum the value for supply_chain..activity_floor.show_num
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public void setShowNum(Integer showNum) {
        this.showNum = showNum;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_floor.num_in_a_line
     *
     * @return the value of supply_chain..activity_floor.num_in_a_line
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public Integer getNumInALine() {
        return numInALine;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public ActivityFloor withNumInALine(Integer numInALine) {
        this.setNumInALine(numInALine);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_floor.num_in_a_line
     *
     * @param numInALine the value for supply_chain..activity_floor.num_in_a_line
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public void setNumInALine(Integer numInALine) {
        this.numInALine = numInALine;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_floor.sort
     *
     * @return the value of supply_chain..activity_floor.sort
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public Integer getSort() {
        return sort;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public ActivityFloor withSort(Integer sort) {
        this.setSort(sort);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_floor.sort
     *
     * @param sort the value for supply_chain..activity_floor.sort
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_floor.create_time
     *
     * @return the value of supply_chain..activity_floor.create_time
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public ActivityFloor withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_floor.create_time
     *
     * @param createTime the value for supply_chain..activity_floor.create_time
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..activity_floor.update_time
     *
     * @return the value of supply_chain..activity_floor.update_time
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public ActivityFloor withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..activity_floor.update_time
     *
     * @param updateTime the value for supply_chain..activity_floor.update_time
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", activityId=").append(activityId);
        sb.append(", name=").append(name);
        sb.append(", isShow=").append(isShow);
        sb.append(", isShowSecond=").append(isShowSecond);
        sb.append(", isShowTag=").append(isShowTag);
        sb.append(", showNum=").append(showNum);
        sb.append(", numInALine=").append(numInALine);
        sb.append(", sort=").append(sort);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ActivityFloor other = (ActivityFloor) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getActivityId() == null ? other.getActivityId() == null : this.getActivityId().equals(other.getActivityId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getIsShow() == null ? other.getIsShow() == null : this.getIsShow().equals(other.getIsShow()))
            && (this.getIsShowSecond() == null ? other.getIsShowSecond() == null : this.getIsShowSecond().equals(other.getIsShowSecond()))
            && (this.getIsShowTag() == null ? other.getIsShowTag() == null : this.getIsShowTag().equals(other.getIsShowTag()))
            && (this.getShowNum() == null ? other.getShowNum() == null : this.getShowNum().equals(other.getShowNum()))
            && (this.getNumInALine() == null ? other.getNumInALine() == null : this.getNumInALine().equals(other.getNumInALine()))
            && (this.getSort() == null ? other.getSort() == null : this.getSort().equals(other.getSort()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getActivityId() == null) ? 0 : getActivityId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getIsShow() == null) ? 0 : getIsShow().hashCode());
        result = prime * result + ((getIsShowSecond() == null) ? 0 : getIsShowSecond().hashCode());
        result = prime * result + ((getIsShowTag() == null) ? 0 : getIsShowTag().hashCode());
        result = prime * result + ((getShowNum() == null) ? 0 : getShowNum().hashCode());
        result = prime * result + ((getNumInALine() == null) ? 0 : getNumInALine().hashCode());
        result = prime * result + ((getSort() == null) ? 0 : getSort().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Aug 08 17:44:57 CST 2022
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        activityId("activity_id", "activityId", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        isShow("is_show", "isShow", "BIT", false),
        isShowSecond("is_show_second", "isShowSecond", "BIT", false),
        isShowTag("is_show_tag", "isShowTag", "BIT", false),
        showNum("show_num", "showNum", "INTEGER", false),
        numInALine("num_in_a_line", "numInALine", "INTEGER", false),
        sort("sort", "sort", "INTEGER", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Mon Aug 08 17:44:57 CST 2022
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Aug 08 17:44:57 CST 2022
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Aug 08 17:44:57 CST 2022
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Aug 08 17:44:57 CST 2022
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Aug 08 17:44:57 CST 2022
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Aug 08 17:44:57 CST 2022
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Aug 08 17:44:57 CST 2022
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:44:57 CST 2022
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:44:57 CST 2022
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:44:57 CST 2022
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:44:57 CST 2022
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:44:57 CST 2022
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:44:57 CST 2022
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:44:57 CST 2022
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:44:57 CST 2022
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:44:57 CST 2022
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Aug 08 17:44:57 CST 2022
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}