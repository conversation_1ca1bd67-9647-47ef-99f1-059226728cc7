package com.chinamobile.iot.sc.service.excel;


import cn.hutool.core.util.NumberUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.chinamobile.iot.sc.constant.OrgIdConstant;
import com.chinamobile.iot.sc.dto.excel.OrderExcel;
import com.chinamobile.iot.sc.exception.ServicePowerException;
import com.chinamobile.iot.sc.utils.GeneralUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by liang
 * 2021/8/31 20:24
 */
@Slf4j
public class OrderExcelListener extends AnalysisEventListener<OrderExcel> {


    @Override
    public void invoke(OrderExcel data, AnalysisContext analysisContext) {
        int currentRow = analysisContext.readRowHolder().getRowIndex() + 1;
        //订单号50字符以内或为空都抛出异常
        if (StringUtils.isBlank(data.getOrderNum())||data.getOrderNum().length() > 50) {
            throw new ServicePowerException(500, "订单号录入错误,错误行:" + currentRow);
        }
        String activityCode = data.getActivityCode();
        //营销活动代码10位数字。即为空或不为10位或不是数字时报错
        if (StringUtils.isBlank(activityCode) || activityCode.length() != 10 || !NumberUtil.isNumber(activityCode)) {
            throw new ServicePowerException(500, "营销活动代码错误,错误行:" + currentRow);
        }
        if(StringUtils.isBlank(data.getActivityName())){
            throw new ServicePowerException(500,"营销活动名称不能为空,错误行:"+currentRow);
        }
        if(StringUtils.isBlank(data.getRcvContact())){
            throw new ServicePowerException(500,"收货人不能为空,错误行:"+currentRow);
        }
        //收货人电话11位
        String phone =data.getRcvContactPhone();
        if(StringUtils.isBlank(phone)||!GeneralUtils.checkPhone(phone)){
            throw new ServicePowerException(500,"手机号不合法,错误行:"+currentRow);
        }
        String rcvAddress=data.getRcvContactAddress();
        if(StringUtils.isBlank(rcvAddress)||!rcvAddress.contains("市")||!rcvAddress.contains("省")){
            throw new ServicePowerException(500,"收获地址缺少省、市级描述,错误行:"+currentRow);
        }
        String city = rcvAddress.substring(rcvAddress.indexOf("省") + 1, rcvAddress.indexOf("市"));
        String orgId= OrgIdConstant.orgIdMap.get(city);
        String province = rcvAddress.substring(0,rcvAddress.indexOf("省"));
        //青海没确定，暂时取消验证
        if("河南".equals(province)){
            if(StringUtils.isBlank(orgId)){
                throw new ServicePowerException(500,"收货地址信息无法匹配，请确认收货地址信息是否正确，错误行:"+currentRow);
            }
        }else if("青海".equals(province)){
           //do nothing currently
        }
        String itemCode=data.getItemCode();
        String regexCode="[a-zA-Z0-9]*";
        Matcher matcher=Pattern.compile(regexCode).matcher(itemCode);
        //产品编码不为空且由英文及数字组成
        if(StringUtils.isBlank(itemCode)||!matcher.find()||!matcher.group(0).equals(itemCode)){
            throw new ServicePowerException(500,"产品编码错误,错误行:"+currentRow);
        }
        String colorRegex="[\\u4e00-\\u9fa5a-zA-Z]*";
        Matcher colorMatcher=Pattern.compile(colorRegex).matcher(data.getSku());
        if(StringUtils.isBlank(data.getSku())||!colorMatcher.find()||!colorMatcher.group(0).equals(data.getSku())){
            throw new ServicePowerException(500,"颜色描述错误,错误行:"+currentRow);
        }
        String qty=data.getQty();
        if(!NumberUtil.isInteger(qty)||NumberUtil.parseInt(qty)<=0){
            throw new ServicePowerException(500,"产品数量错误,错误行:"+currentRow);
        }

        log.info("解析到一条数据:{},当前行:{}", data, currentRow);

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("所有数据解析完成！");
    }


}
