package com.chinamobile.iot.sc.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 河南装维售后工单
 *
 * <AUTHOR>
public class OrderHenanZw implements Serializable {
    /**
     * 工单编号
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String sheetNo;

    /**
     * 订单编号
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String orderNo;

    /**
     * 商品组/销售商品名称
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String spuName;

    /**
     * 商品组编码
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String spuCode;

    /**
     * 商品类型
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String spuOfferingClass;

    /**
     * 商品名称（规格）
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String skuName;

    /**
     * 商品编码（规格）
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String skuCode;

    /**
     * 原子商品名称
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String atomName;

    /**
     * 原子商品编码
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String atomCode;

    /**
     * 原子商品类型
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String atomOfferingClass;

    /**
     * 型号
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String model;

    /**
     * 序列号
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String sn;

    /**
     * 订购数量
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private Long quantity;

    /**
     * 订购金额
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private Long price;

    /**
     * 工单状态 0-待派发，1-已派发，2-已受理，3-开通成功，4-开通失败
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private Integer status;

    /**
     * 派发时间
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private Date sendTime;

    /**
     * 合作伙伴id
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String cooperatorId;

    /**
     * 合作伙伴名称
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String cooperatorName;

    /**
     * 装维类型 0：只装不维 1：既装又维
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private Integer businessType;

    /**
     * 客户联系人
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String customContact;

    /**
     * 客户联系电话
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String customContactPhone;

    /**
     * 安装地址所属省份代码
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String provinceCode;

    /**
     * 安装地址所属省份名称
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String provinceName;

    /**
     * 安装地址所属地市代码
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String cityCode;

    /**
     * 安装地址所属地市名称
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String cityName;

    /**
     * 安装地址所属区县代码
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String areaCode;

    /**
     * 安装地址所属区县名称
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String areaName;

    /**
     * 安装详细地址
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private String address;

    /**
     * 工单创建时间
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private Date createTime;

    /**
     * 工单更新时间
     *
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.sheet_no
     *
     * @return the value of supply_chain..order_henan_zw.sheet_no
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getSheetNo() {
        return sheetNo;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withSheetNo(String sheetNo) {
        this.setSheetNo(sheetNo);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.sheet_no
     *
     * @param sheetNo the value for supply_chain..order_henan_zw.sheet_no
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setSheetNo(String sheetNo) {
        this.sheetNo = sheetNo;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.order_no
     *
     * @return the value of supply_chain..order_henan_zw.order_no
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withOrderNo(String orderNo) {
        this.setOrderNo(orderNo);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.order_no
     *
     * @param orderNo the value for supply_chain..order_henan_zw.order_no
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.spu_name
     *
     * @return the value of supply_chain..order_henan_zw.spu_name
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getSpuName() {
        return spuName;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withSpuName(String spuName) {
        this.setSpuName(spuName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.spu_name
     *
     * @param spuName the value for supply_chain..order_henan_zw.spu_name
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setSpuName(String spuName) {
        this.spuName = spuName;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.spu_code
     *
     * @return the value of supply_chain..order_henan_zw.spu_code
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getSpuCode() {
        return spuCode;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withSpuCode(String spuCode) {
        this.setSpuCode(spuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.spu_code
     *
     * @param spuCode the value for supply_chain..order_henan_zw.spu_code
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setSpuCode(String spuCode) {
        this.spuCode = spuCode;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.spu_offering_class
     *
     * @return the value of supply_chain..order_henan_zw.spu_offering_class
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getSpuOfferingClass() {
        return spuOfferingClass;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withSpuOfferingClass(String spuOfferingClass) {
        this.setSpuOfferingClass(spuOfferingClass);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.spu_offering_class
     *
     * @param spuOfferingClass the value for supply_chain..order_henan_zw.spu_offering_class
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setSpuOfferingClass(String spuOfferingClass) {
        this.spuOfferingClass = spuOfferingClass;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.sku_name
     *
     * @return the value of supply_chain..order_henan_zw.sku_name
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getSkuName() {
        return skuName;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withSkuName(String skuName) {
        this.setSkuName(skuName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.sku_name
     *
     * @param skuName the value for supply_chain..order_henan_zw.sku_name
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.sku_code
     *
     * @return the value of supply_chain..order_henan_zw.sku_code
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getSkuCode() {
        return skuCode;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withSkuCode(String skuCode) {
        this.setSkuCode(skuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.sku_code
     *
     * @param skuCode the value for supply_chain..order_henan_zw.sku_code
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.atom_name
     *
     * @return the value of supply_chain..order_henan_zw.atom_name
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getAtomName() {
        return atomName;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withAtomName(String atomName) {
        this.setAtomName(atomName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.atom_name
     *
     * @param atomName the value for supply_chain..order_henan_zw.atom_name
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setAtomName(String atomName) {
        this.atomName = atomName;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.atom_code
     *
     * @return the value of supply_chain..order_henan_zw.atom_code
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getAtomCode() {
        return atomCode;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withAtomCode(String atomCode) {
        this.setAtomCode(atomCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.atom_code
     *
     * @param atomCode the value for supply_chain..order_henan_zw.atom_code
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setAtomCode(String atomCode) {
        this.atomCode = atomCode;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.atom_offering_class
     *
     * @return the value of supply_chain..order_henan_zw.atom_offering_class
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getAtomOfferingClass() {
        return atomOfferingClass;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withAtomOfferingClass(String atomOfferingClass) {
        this.setAtomOfferingClass(atomOfferingClass);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.atom_offering_class
     *
     * @param atomOfferingClass the value for supply_chain..order_henan_zw.atom_offering_class
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setAtomOfferingClass(String atomOfferingClass) {
        this.atomOfferingClass = atomOfferingClass;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.model
     *
     * @return the value of supply_chain..order_henan_zw.model
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getModel() {
        return model;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withModel(String model) {
        this.setModel(model);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.model
     *
     * @param model the value for supply_chain..order_henan_zw.model
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setModel(String model) {
        this.model = model;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.sn
     *
     * @return the value of supply_chain..order_henan_zw.sn
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getSn() {
        return sn;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withSn(String sn) {
        this.setSn(sn);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.sn
     *
     * @param sn the value for supply_chain..order_henan_zw.sn
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setSn(String sn) {
        this.sn = sn;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.quantity
     *
     * @return the value of supply_chain..order_henan_zw.quantity
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public Long getQuantity() {
        return quantity;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withQuantity(Long quantity) {
        this.setQuantity(quantity);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.quantity
     *
     * @param quantity the value for supply_chain..order_henan_zw.quantity
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.price
     *
     * @return the value of supply_chain..order_henan_zw.price
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public Long getPrice() {
        return price;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withPrice(Long price) {
        this.setPrice(price);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.price
     *
     * @param price the value for supply_chain..order_henan_zw.price
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setPrice(Long price) {
        this.price = price;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.status
     *
     * @return the value of supply_chain..order_henan_zw.status
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public Integer getStatus() {
        return status;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withStatus(Integer status) {
        this.setStatus(status);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.status
     *
     * @param status the value for supply_chain..order_henan_zw.status
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.send_time
     *
     * @return the value of supply_chain..order_henan_zw.send_time
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public Date getSendTime() {
        return sendTime;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withSendTime(Date sendTime) {
        this.setSendTime(sendTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.send_time
     *
     * @param sendTime the value for supply_chain..order_henan_zw.send_time
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.cooperator_id
     *
     * @return the value of supply_chain..order_henan_zw.cooperator_id
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getCooperatorId() {
        return cooperatorId;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withCooperatorId(String cooperatorId) {
        this.setCooperatorId(cooperatorId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.cooperator_id
     *
     * @param cooperatorId the value for supply_chain..order_henan_zw.cooperator_id
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setCooperatorId(String cooperatorId) {
        this.cooperatorId = cooperatorId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.cooperator_name
     *
     * @return the value of supply_chain..order_henan_zw.cooperator_name
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getCooperatorName() {
        return cooperatorName;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withCooperatorName(String cooperatorName) {
        this.setCooperatorName(cooperatorName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.cooperator_name
     *
     * @param cooperatorName the value for supply_chain..order_henan_zw.cooperator_name
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setCooperatorName(String cooperatorName) {
        this.cooperatorName = cooperatorName;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.business_type
     *
     * @return the value of supply_chain..order_henan_zw.business_type
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public Integer getBusinessType() {
        return businessType;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withBusinessType(Integer businessType) {
        this.setBusinessType(businessType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.business_type
     *
     * @param businessType the value for supply_chain..order_henan_zw.business_type
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.custom_contact
     *
     * @return the value of supply_chain..order_henan_zw.custom_contact
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getCustomContact() {
        return customContact;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withCustomContact(String customContact) {
        this.setCustomContact(customContact);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.custom_contact
     *
     * @param customContact the value for supply_chain..order_henan_zw.custom_contact
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setCustomContact(String customContact) {
        this.customContact = customContact;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.custom_contact_phone
     *
     * @return the value of supply_chain..order_henan_zw.custom_contact_phone
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getCustomContactPhone() {
        return customContactPhone;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withCustomContactPhone(String customContactPhone) {
        this.setCustomContactPhone(customContactPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.custom_contact_phone
     *
     * @param customContactPhone the value for supply_chain..order_henan_zw.custom_contact_phone
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setCustomContactPhone(String customContactPhone) {
        this.customContactPhone = customContactPhone;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.province_code
     *
     * @return the value of supply_chain..order_henan_zw.province_code
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withProvinceCode(String provinceCode) {
        this.setProvinceCode(provinceCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.province_code
     *
     * @param provinceCode the value for supply_chain..order_henan_zw.province_code
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.province_name
     *
     * @return the value of supply_chain..order_henan_zw.province_name
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getProvinceName() {
        return provinceName;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withProvinceName(String provinceName) {
        this.setProvinceName(provinceName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.province_name
     *
     * @param provinceName the value for supply_chain..order_henan_zw.province_name
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.city_code
     *
     * @return the value of supply_chain..order_henan_zw.city_code
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withCityCode(String cityCode) {
        this.setCityCode(cityCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.city_code
     *
     * @param cityCode the value for supply_chain..order_henan_zw.city_code
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.city_name
     *
     * @return the value of supply_chain..order_henan_zw.city_name
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getCityName() {
        return cityName;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withCityName(String cityName) {
        this.setCityName(cityName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.city_name
     *
     * @param cityName the value for supply_chain..order_henan_zw.city_name
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.area_code
     *
     * @return the value of supply_chain..order_henan_zw.area_code
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getAreaCode() {
        return areaCode;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withAreaCode(String areaCode) {
        this.setAreaCode(areaCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.area_code
     *
     * @param areaCode the value for supply_chain..order_henan_zw.area_code
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.area_name
     *
     * @return the value of supply_chain..order_henan_zw.area_name
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getAreaName() {
        return areaName;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withAreaName(String areaName) {
        this.setAreaName(areaName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.area_name
     *
     * @param areaName the value for supply_chain..order_henan_zw.area_name
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.address
     *
     * @return the value of supply_chain..order_henan_zw.address
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public String getAddress() {
        return address;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withAddress(String address) {
        this.setAddress(address);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.address
     *
     * @param address the value for supply_chain..order_henan_zw.address
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.create_time
     *
     * @return the value of supply_chain..order_henan_zw.create_time
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.create_time
     *
     * @param createTime the value for supply_chain..order_henan_zw.create_time
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..order_henan_zw.update_time
     *
     * @return the value of supply_chain..order_henan_zw.update_time
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public OrderHenanZw withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_henan_zw.update_time
     *
     * @param updateTime the value for supply_chain..order_henan_zw.update_time
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", sheetNo=").append(sheetNo);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", spuName=").append(spuName);
        sb.append(", spuCode=").append(spuCode);
        sb.append(", spuOfferingClass=").append(spuOfferingClass);
        sb.append(", skuName=").append(skuName);
        sb.append(", skuCode=").append(skuCode);
        sb.append(", atomName=").append(atomName);
        sb.append(", atomCode=").append(atomCode);
        sb.append(", atomOfferingClass=").append(atomOfferingClass);
        sb.append(", model=").append(model);
        sb.append(", sn=").append(sn);
        sb.append(", quantity=").append(quantity);
        sb.append(", price=").append(price);
        sb.append(", status=").append(status);
        sb.append(", sendTime=").append(sendTime);
        sb.append(", cooperatorId=").append(cooperatorId);
        sb.append(", cooperatorName=").append(cooperatorName);
        sb.append(", businessType=").append(businessType);
        sb.append(", customContact=").append(customContact);
        sb.append(", customContactPhone=").append(customContactPhone);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", provinceName=").append(provinceName);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", cityName=").append(cityName);
        sb.append(", areaCode=").append(areaCode);
        sb.append(", areaName=").append(areaName);
        sb.append(", address=").append(address);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OrderHenanZw other = (OrderHenanZw) that;
        return (this.getSheetNo() == null ? other.getSheetNo() == null : this.getSheetNo().equals(other.getSheetNo()))
            && (this.getOrderNo() == null ? other.getOrderNo() == null : this.getOrderNo().equals(other.getOrderNo()))
            && (this.getSpuName() == null ? other.getSpuName() == null : this.getSpuName().equals(other.getSpuName()))
            && (this.getSpuCode() == null ? other.getSpuCode() == null : this.getSpuCode().equals(other.getSpuCode()))
            && (this.getSpuOfferingClass() == null ? other.getSpuOfferingClass() == null : this.getSpuOfferingClass().equals(other.getSpuOfferingClass()))
            && (this.getSkuName() == null ? other.getSkuName() == null : this.getSkuName().equals(other.getSkuName()))
            && (this.getSkuCode() == null ? other.getSkuCode() == null : this.getSkuCode().equals(other.getSkuCode()))
            && (this.getAtomName() == null ? other.getAtomName() == null : this.getAtomName().equals(other.getAtomName()))
            && (this.getAtomCode() == null ? other.getAtomCode() == null : this.getAtomCode().equals(other.getAtomCode()))
            && (this.getAtomOfferingClass() == null ? other.getAtomOfferingClass() == null : this.getAtomOfferingClass().equals(other.getAtomOfferingClass()))
            && (this.getModel() == null ? other.getModel() == null : this.getModel().equals(other.getModel()))
            && (this.getSn() == null ? other.getSn() == null : this.getSn().equals(other.getSn()))
            && (this.getQuantity() == null ? other.getQuantity() == null : this.getQuantity().equals(other.getQuantity()))
            && (this.getPrice() == null ? other.getPrice() == null : this.getPrice().equals(other.getPrice()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getSendTime() == null ? other.getSendTime() == null : this.getSendTime().equals(other.getSendTime()))
            && (this.getCooperatorId() == null ? other.getCooperatorId() == null : this.getCooperatorId().equals(other.getCooperatorId()))
            && (this.getCooperatorName() == null ? other.getCooperatorName() == null : this.getCooperatorName().equals(other.getCooperatorName()))
            && (this.getBusinessType() == null ? other.getBusinessType() == null : this.getBusinessType().equals(other.getBusinessType()))
            && (this.getCustomContact() == null ? other.getCustomContact() == null : this.getCustomContact().equals(other.getCustomContact()))
            && (this.getCustomContactPhone() == null ? other.getCustomContactPhone() == null : this.getCustomContactPhone().equals(other.getCustomContactPhone()))
            && (this.getProvinceCode() == null ? other.getProvinceCode() == null : this.getProvinceCode().equals(other.getProvinceCode()))
            && (this.getProvinceName() == null ? other.getProvinceName() == null : this.getProvinceName().equals(other.getProvinceName()))
            && (this.getCityCode() == null ? other.getCityCode() == null : this.getCityCode().equals(other.getCityCode()))
            && (this.getCityName() == null ? other.getCityName() == null : this.getCityName().equals(other.getCityName()))
            && (this.getAreaCode() == null ? other.getAreaCode() == null : this.getAreaCode().equals(other.getAreaCode()))
            && (this.getAreaName() == null ? other.getAreaName() == null : this.getAreaName().equals(other.getAreaName()))
            && (this.getAddress() == null ? other.getAddress() == null : this.getAddress().equals(other.getAddress()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getSheetNo() == null) ? 0 : getSheetNo().hashCode());
        result = prime * result + ((getOrderNo() == null) ? 0 : getOrderNo().hashCode());
        result = prime * result + ((getSpuName() == null) ? 0 : getSpuName().hashCode());
        result = prime * result + ((getSpuCode() == null) ? 0 : getSpuCode().hashCode());
        result = prime * result + ((getSpuOfferingClass() == null) ? 0 : getSpuOfferingClass().hashCode());
        result = prime * result + ((getSkuName() == null) ? 0 : getSkuName().hashCode());
        result = prime * result + ((getSkuCode() == null) ? 0 : getSkuCode().hashCode());
        result = prime * result + ((getAtomName() == null) ? 0 : getAtomName().hashCode());
        result = prime * result + ((getAtomCode() == null) ? 0 : getAtomCode().hashCode());
        result = prime * result + ((getAtomOfferingClass() == null) ? 0 : getAtomOfferingClass().hashCode());
        result = prime * result + ((getModel() == null) ? 0 : getModel().hashCode());
        result = prime * result + ((getSn() == null) ? 0 : getSn().hashCode());
        result = prime * result + ((getQuantity() == null) ? 0 : getQuantity().hashCode());
        result = prime * result + ((getPrice() == null) ? 0 : getPrice().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getSendTime() == null) ? 0 : getSendTime().hashCode());
        result = prime * result + ((getCooperatorId() == null) ? 0 : getCooperatorId().hashCode());
        result = prime * result + ((getCooperatorName() == null) ? 0 : getCooperatorName().hashCode());
        result = prime * result + ((getBusinessType() == null) ? 0 : getBusinessType().hashCode());
        result = prime * result + ((getCustomContact() == null) ? 0 : getCustomContact().hashCode());
        result = prime * result + ((getCustomContactPhone() == null) ? 0 : getCustomContactPhone().hashCode());
        result = prime * result + ((getProvinceCode() == null) ? 0 : getProvinceCode().hashCode());
        result = prime * result + ((getProvinceName() == null) ? 0 : getProvinceName().hashCode());
        result = prime * result + ((getCityCode() == null) ? 0 : getCityCode().hashCode());
        result = prime * result + ((getCityName() == null) ? 0 : getCityName().hashCode());
        result = prime * result + ((getAreaCode() == null) ? 0 : getAreaCode().hashCode());
        result = prime * result + ((getAreaName() == null) ? 0 : getAreaName().hashCode());
        result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon Oct 24 14:51:18 CST 2022
     */
    public enum Column {
        sheetNo("sheet_no", "sheetNo", "VARCHAR", false),
        orderNo("order_no", "orderNo", "VARCHAR", false),
        spuName("spu_name", "spuName", "VARCHAR", false),
        spuCode("spu_code", "spuCode", "VARCHAR", false),
        spuOfferingClass("spu_offering_class", "spuOfferingClass", "VARCHAR", false),
        skuName("sku_name", "skuName", "VARCHAR", false),
        skuCode("sku_code", "skuCode", "VARCHAR", false),
        atomName("atom_name", "atomName", "VARCHAR", false),
        atomCode("atom_code", "atomCode", "VARCHAR", false),
        atomOfferingClass("atom_offering_class", "atomOfferingClass", "VARCHAR", false),
        model("model", "model", "VARCHAR", false),
        sn("sn", "sn", "VARCHAR", false),
        quantity("quantity", "quantity", "BIGINT", false),
        price("price", "price", "BIGINT", false),
        status("status", "status", "INTEGER", false),
        sendTime("send_time", "sendTime", "TIMESTAMP", false),
        cooperatorId("cooperator_id", "cooperatorId", "VARCHAR", false),
        cooperatorName("cooperator_name", "cooperatorName", "VARCHAR", false),
        businessType("business_type", "businessType", "INTEGER", false),
        customContact("custom_contact", "customContact", "VARCHAR", false),
        customContactPhone("custom_contact_phone", "customContactPhone", "VARCHAR", false),
        provinceCode("province_code", "provinceCode", "VARCHAR", false),
        provinceName("province_name", "provinceName", "VARCHAR", false),
        cityCode("city_code", "cityCode", "VARCHAR", false),
        cityName("city_name", "cityName", "VARCHAR", false),
        areaCode("area_code", "areaCode", "VARCHAR", false),
        areaName("area_name", "areaName", "VARCHAR", false),
        address("address", "address", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Mon Oct 24 14:51:18 CST 2022
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Oct 24 14:51:18 CST 2022
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon Oct 24 14:51:18 CST 2022
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon Oct 24 14:51:18 CST 2022
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon Oct 24 14:51:18 CST 2022
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon Oct 24 14:51:18 CST 2022
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Oct 24 14:51:18 CST 2022
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Oct 24 14:51:18 CST 2022
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Oct 24 14:51:18 CST 2022
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Oct 24 14:51:18 CST 2022
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Oct 24 14:51:18 CST 2022
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Oct 24 14:51:18 CST 2022
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Oct 24 14:51:18 CST 2022
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Oct 24 14:51:18 CST 2022
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Oct 24 14:51:18 CST 2022
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Oct 24 14:51:18 CST 2022
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Oct 24 14:51:18 CST 2022
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}