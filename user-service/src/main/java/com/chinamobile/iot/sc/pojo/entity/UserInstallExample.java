package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class UserInstallExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public UserInstallExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public UserInstallExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    public UserInstallExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    public static Criteria newAndCreateCriteria() {
        UserInstallExample example = new UserInstallExample();
        return example.createCriteria();
    }

    public UserInstallExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    public UserInstallExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andInstallNameIsNull() {
            addCriterion("install_name is null");
            return (Criteria) this;
        }

        public Criteria andInstallNameIsNotNull() {
            addCriterion("install_name is not null");
            return (Criteria) this;
        }

        public Criteria andInstallNameEqualTo(String value) {
            addCriterion("install_name =", value, "installName");
            return (Criteria) this;
        }

        public Criteria andInstallNameEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallNameNotEqualTo(String value) {
            addCriterion("install_name <>", value, "installName");
            return (Criteria) this;
        }

        public Criteria andInstallNameNotEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallNameGreaterThan(String value) {
            addCriterion("install_name >", value, "installName");
            return (Criteria) this;
        }

        public Criteria andInstallNameGreaterThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallNameGreaterThanOrEqualTo(String value) {
            addCriterion("install_name >=", value, "installName");
            return (Criteria) this;
        }

        public Criteria andInstallNameGreaterThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallNameLessThan(String value) {
            addCriterion("install_name <", value, "installName");
            return (Criteria) this;
        }

        public Criteria andInstallNameLessThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallNameLessThanOrEqualTo(String value) {
            addCriterion("install_name <=", value, "installName");
            return (Criteria) this;
        }

        public Criteria andInstallNameLessThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallNameLike(String value) {
            addCriterion("install_name like", value, "installName");
            return (Criteria) this;
        }

        public Criteria andInstallNameNotLike(String value) {
            addCriterion("install_name not like", value, "installName");
            return (Criteria) this;
        }

        public Criteria andInstallNameIn(List<String> values) {
            addCriterion("install_name in", values, "installName");
            return (Criteria) this;
        }

        public Criteria andInstallNameNotIn(List<String> values) {
            addCriterion("install_name not in", values, "installName");
            return (Criteria) this;
        }

        public Criteria andInstallNameBetween(String value1, String value2) {
            addCriterion("install_name between", value1, value2, "installName");
            return (Criteria) this;
        }

        public Criteria andInstallNameNotBetween(String value1, String value2) {
            addCriterion("install_name not between", value1, value2, "installName");
            return (Criteria) this;
        }

        public Criteria andInstallPhoneIsNull() {
            addCriterion("install_phone is null");
            return (Criteria) this;
        }

        public Criteria andInstallPhoneIsNotNull() {
            addCriterion("install_phone is not null");
            return (Criteria) this;
        }

        public Criteria andInstallPhoneEqualTo(String value) {
            addCriterion("install_phone =", value, "installPhone");
            return (Criteria) this;
        }

        public Criteria andInstallPhoneEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallPhoneNotEqualTo(String value) {
            addCriterion("install_phone <>", value, "installPhone");
            return (Criteria) this;
        }

        public Criteria andInstallPhoneNotEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallPhoneGreaterThan(String value) {
            addCriterion("install_phone >", value, "installPhone");
            return (Criteria) this;
        }

        public Criteria andInstallPhoneGreaterThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("install_phone >=", value, "installPhone");
            return (Criteria) this;
        }

        public Criteria andInstallPhoneGreaterThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallPhoneLessThan(String value) {
            addCriterion("install_phone <", value, "installPhone");
            return (Criteria) this;
        }

        public Criteria andInstallPhoneLessThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallPhoneLessThanOrEqualTo(String value) {
            addCriterion("install_phone <=", value, "installPhone");
            return (Criteria) this;
        }

        public Criteria andInstallPhoneLessThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallPhoneLike(String value) {
            addCriterion("install_phone like", value, "installPhone");
            return (Criteria) this;
        }

        public Criteria andInstallPhoneNotLike(String value) {
            addCriterion("install_phone not like", value, "installPhone");
            return (Criteria) this;
        }

        public Criteria andInstallPhoneIn(List<String> values) {
            addCriterion("install_phone in", values, "installPhone");
            return (Criteria) this;
        }

        public Criteria andInstallPhoneNotIn(List<String> values) {
            addCriterion("install_phone not in", values, "installPhone");
            return (Criteria) this;
        }

        public Criteria andInstallPhoneBetween(String value1, String value2) {
            addCriterion("install_phone between", value1, value2, "installPhone");
            return (Criteria) this;
        }

        public Criteria andInstallPhoneNotBetween(String value1, String value2) {
            addCriterion("install_phone not between", value1, value2, "installPhone");
            return (Criteria) this;
        }

        public Criteria andInstallEmailIsNull() {
            addCriterion("install_email is null");
            return (Criteria) this;
        }

        public Criteria andInstallEmailIsNotNull() {
            addCriterion("install_email is not null");
            return (Criteria) this;
        }

        public Criteria andInstallEmailEqualTo(String value) {
            addCriterion("install_email =", value, "installEmail");
            return (Criteria) this;
        }

        public Criteria andInstallEmailEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_email = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallEmailNotEqualTo(String value) {
            addCriterion("install_email <>", value, "installEmail");
            return (Criteria) this;
        }

        public Criteria andInstallEmailNotEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_email <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallEmailGreaterThan(String value) {
            addCriterion("install_email >", value, "installEmail");
            return (Criteria) this;
        }

        public Criteria andInstallEmailGreaterThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_email > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallEmailGreaterThanOrEqualTo(String value) {
            addCriterion("install_email >=", value, "installEmail");
            return (Criteria) this;
        }

        public Criteria andInstallEmailGreaterThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_email >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallEmailLessThan(String value) {
            addCriterion("install_email <", value, "installEmail");
            return (Criteria) this;
        }

        public Criteria andInstallEmailLessThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_email < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallEmailLessThanOrEqualTo(String value) {
            addCriterion("install_email <=", value, "installEmail");
            return (Criteria) this;
        }

        public Criteria andInstallEmailLessThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_email <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallEmailLike(String value) {
            addCriterion("install_email like", value, "installEmail");
            return (Criteria) this;
        }

        public Criteria andInstallEmailNotLike(String value) {
            addCriterion("install_email not like", value, "installEmail");
            return (Criteria) this;
        }

        public Criteria andInstallEmailIn(List<String> values) {
            addCriterion("install_email in", values, "installEmail");
            return (Criteria) this;
        }

        public Criteria andInstallEmailNotIn(List<String> values) {
            addCriterion("install_email not in", values, "installEmail");
            return (Criteria) this;
        }

        public Criteria andInstallEmailBetween(String value1, String value2) {
            addCriterion("install_email between", value1, value2, "installEmail");
            return (Criteria) this;
        }

        public Criteria andInstallEmailNotBetween(String value1, String value2) {
            addCriterion("install_email not between", value1, value2, "installEmail");
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameIsNull() {
            addCriterion("install_partner_name is null");
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameIsNotNull() {
            addCriterion("install_partner_name is not null");
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameEqualTo(String value) {
            addCriterion("install_partner_name =", value, "installPartnerName");
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_partner_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameNotEqualTo(String value) {
            addCriterion("install_partner_name <>", value, "installPartnerName");
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameNotEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_partner_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameGreaterThan(String value) {
            addCriterion("install_partner_name >", value, "installPartnerName");
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameGreaterThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_partner_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameGreaterThanOrEqualTo(String value) {
            addCriterion("install_partner_name >=", value, "installPartnerName");
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameGreaterThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_partner_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameLessThan(String value) {
            addCriterion("install_partner_name <", value, "installPartnerName");
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameLessThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_partner_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameLessThanOrEqualTo(String value) {
            addCriterion("install_partner_name <=", value, "installPartnerName");
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameLessThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("install_partner_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameLike(String value) {
            addCriterion("install_partner_name like", value, "installPartnerName");
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameNotLike(String value) {
            addCriterion("install_partner_name not like", value, "installPartnerName");
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameIn(List<String> values) {
            addCriterion("install_partner_name in", values, "installPartnerName");
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameNotIn(List<String> values) {
            addCriterion("install_partner_name not in", values, "installPartnerName");
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameBetween(String value1, String value2) {
            addCriterion("install_partner_name between", value1, value2, "installPartnerName");
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameNotBetween(String value1, String value2) {
            addCriterion("install_partner_name not between", value1, value2, "installPartnerName");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdIsNull() {
            addCriterion("primary_user_id is null");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdIsNotNull() {
            addCriterion("primary_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdEqualTo(String value) {
            addCriterion("primary_user_id =", value, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("primary_user_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdNotEqualTo(String value) {
            addCriterion("primary_user_id <>", value, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdNotEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("primary_user_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdGreaterThan(String value) {
            addCriterion("primary_user_id >", value, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdGreaterThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("primary_user_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("primary_user_id >=", value, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdGreaterThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("primary_user_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdLessThan(String value) {
            addCriterion("primary_user_id <", value, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdLessThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("primary_user_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdLessThanOrEqualTo(String value) {
            addCriterion("primary_user_id <=", value, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdLessThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("primary_user_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdLike(String value) {
            addCriterion("primary_user_id like", value, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdNotLike(String value) {
            addCriterion("primary_user_id not like", value, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdIn(List<String> values) {
            addCriterion("primary_user_id in", values, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdNotIn(List<String> values) {
            addCriterion("primary_user_id not in", values, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdBetween(String value1, String value2) {
            addCriterion("primary_user_id between", value1, value2, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdNotBetween(String value1, String value2) {
            addCriterion("primary_user_id not between", value1, value2, "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andIsCancelIsNull() {
            addCriterion("is_cancel is null");
            return (Criteria) this;
        }

        public Criteria andIsCancelIsNotNull() {
            addCriterion("is_cancel is not null");
            return (Criteria) this;
        }

        public Criteria andIsCancelEqualTo(Boolean value) {
            addCriterion("is_cancel =", value, "isCancel");
            return (Criteria) this;
        }

        public Criteria andIsCancelEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("is_cancel = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsCancelNotEqualTo(Boolean value) {
            addCriterion("is_cancel <>", value, "isCancel");
            return (Criteria) this;
        }

        public Criteria andIsCancelNotEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("is_cancel <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsCancelGreaterThan(Boolean value) {
            addCriterion("is_cancel >", value, "isCancel");
            return (Criteria) this;
        }

        public Criteria andIsCancelGreaterThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("is_cancel > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsCancelGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_cancel >=", value, "isCancel");
            return (Criteria) this;
        }

        public Criteria andIsCancelGreaterThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("is_cancel >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsCancelLessThan(Boolean value) {
            addCriterion("is_cancel <", value, "isCancel");
            return (Criteria) this;
        }

        public Criteria andIsCancelLessThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("is_cancel < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsCancelLessThanOrEqualTo(Boolean value) {
            addCriterion("is_cancel <=", value, "isCancel");
            return (Criteria) this;
        }

        public Criteria andIsCancelLessThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("is_cancel <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsCancelIn(List<Boolean> values) {
            addCriterion("is_cancel in", values, "isCancel");
            return (Criteria) this;
        }

        public Criteria andIsCancelNotIn(List<Boolean> values) {
            addCriterion("is_cancel not in", values, "isCancel");
            return (Criteria) this;
        }

        public Criteria andIsCancelBetween(Boolean value1, Boolean value2) {
            addCriterion("is_cancel between", value1, value2, "isCancel");
            return (Criteria) this;
        }

        public Criteria andIsCancelNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_cancel not between", value1, value2, "isCancel");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNull() {
            addCriterion("create_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIsNotNull() {
            addCriterion("create_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualTo(String value) {
            addCriterion("create_user_id =", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("create_user_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualTo(String value) {
            addCriterion("create_user_id <>", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("create_user_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThan(String value) {
            addCriterion("create_user_id >", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("create_user_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_user_id >=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdGreaterThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("create_user_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThan(String value) {
            addCriterion("create_user_id <", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("create_user_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualTo(String value) {
            addCriterion("create_user_id <=", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLessThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("create_user_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLike(String value) {
            addCriterion("create_user_id like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotLike(String value) {
            addCriterion("create_user_id not like", value, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdIn(List<String> values) {
            addCriterion("create_user_id in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotIn(List<String> values) {
            addCriterion("create_user_id not in", values, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdBetween(String value1, String value2) {
            addCriterion("create_user_id between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdNotBetween(String value1, String value2) {
            addCriterion("create_user_id not between", value1, value2, "createUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(UserInstall.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andInstallNameLikeInsensitive(String value) {
            addCriterion("upper(install_name) like", value.toUpperCase(), "installName");
            return (Criteria) this;
        }

        public Criteria andInstallPhoneLikeInsensitive(String value) {
            addCriterion("upper(install_phone) like", value.toUpperCase(), "installPhone");
            return (Criteria) this;
        }

        public Criteria andInstallEmailLikeInsensitive(String value) {
            addCriterion("upper(install_email) like", value.toUpperCase(), "installEmail");
            return (Criteria) this;
        }

        public Criteria andInstallPartnerNameLikeInsensitive(String value) {
            addCriterion("upper(install_partner_name) like", value.toUpperCase(), "installPartnerName");
            return (Criteria) this;
        }

        public Criteria andPrimaryUserIdLikeInsensitive(String value) {
            addCriterion("upper(primary_user_id) like", value.toUpperCase(), "primaryUserId");
            return (Criteria) this;
        }

        public Criteria andCreateUserIdLikeInsensitive(String value) {
            addCriterion("upper(create_user_id) like", value.toUpperCase(), "createUserId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        private UserInstallExample example;

        protected Criteria(UserInstallExample example) {
            super();
            this.example = example;
        }

        public UserInstallExample example() {
            return this.example;
        }

        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            Criteria add(Criteria add);
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        void example(com.chinamobile.iot.sc.pojo.entity.UserInstallExample example);
    }
}