package com.chinamobile.iot.sc.dao.ext;

import com.chinamobile.iot.sc.pojo.entity.UserProvinceBusiness;
import com.chinamobile.iot.sc.vo.request.RequestUserListParam;
import com.chinamobile.iot.sc.vo.response.UserListItemVO;
import com.chinamobile.iot.sc.vo.response.UserPipeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> xiemaohua
 * @date : 2024/3/4 14:48
 * @description: 省业管员用户拓展类
 **/
@Mapper
public interface UserProvinceBusinessMapperExt {

    /**
     * 查询用户信息通过查询参数
     * @param param
     * @param roleIds
     * @return
     */
    List<UserListItemVO> getProvinceBusinessUserList(@Param("param") RequestUserListParam param, @Param("roleIds") List<String> roleIds);

    /**
     * 根据角色类型查询省业管员信息
     * @param roleTypes
     * @return
     */
    List<UserPipeDO>  getProvinceBusinessUserByRoleTypeList(List<String> roleTypes);

    /**
     * 根据角色类型或 省份 地市查询省业中心用户
     * @param roleId
     * @param beIds
     * @param locations
     * @return
     */
    List<UserProvinceBusiness>  getProvinceBusinessUserListByRoleTypes(@Param("roleId") String roleId,@Param("beIds") List<String> beIds,@Param("locations") List<String> locations);
}
