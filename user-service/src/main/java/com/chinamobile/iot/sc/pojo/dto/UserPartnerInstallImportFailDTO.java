package com.chinamobile.iot.sc.pojo.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR> x<PERSON><PERSON><PERSON><PERSON>
 * @date : 2025/6/4 10:01
 * @description:
 **/
@Data
public class UserPartnerInstallImportFailDTO {


    /**
     * 角色名称
     */
    @ExcelProperty(value = "角色名称",index = 0)
    private String roleName;

    /**
     * 账号类型
     */
    @ExcelProperty(value = "账号类型",index = 1)
    private String userTypeName;

    /**
     * 姓名
     */
    @ExcelProperty(value = "联系人姓名",index = 2)
    private String account;

    /**
     * 手机号
     */
    @ExcelProperty(value = "联系人手机号",index = 3)
    private String phone;

    /**
     * 邮箱
     */
    @ExcelProperty(value = "联系人邮箱",index = 4)
    private String email;

    /**
     * 是否为外部人员
     */
    @ExcelProperty(value = "是否为外部人员",index = 5)
    private String isExternal;

    /**
     * 地市域
     */
    @ExcelProperty(value = "省域/地市域",index = 6)
    private String provinceLocation;

    @ExcelProperty(value="失败原因",index = 7)
    private String failedReason;

}
