package com.chinamobile.iot.sc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2024/12/26 15:16
 */
@Configuration
@ConfigurationProperties(prefix = "gio")
@Data
public class GioConfig {

    //根据企业id获取gio用户列表的链接
    private String usersUrlFormat;

    //更新gio看板行级权限的地址
    private String updateRowLevelPermissionUrlFormat;

    //企业id
    private String enterpriseId;

    //gio平台企业概览中获取的token
    private String authorization;

    //gio空间id
    private String spaceId;

    //项目id
    private String projectId = "xx";

}
