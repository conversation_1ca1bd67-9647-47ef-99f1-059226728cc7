<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ext.UserMapperExt">

    <select id="page4User" resultType="com.chinamobile.iot.sc.pojo.mapper.UserDO">
        select
        user_id,name,pwd,phone,remark,is_admin,role_id,create_time,update_time,is_cancel,email,company,partner_name,creator,is_primary,is_send
        from user u where 1=1 and u.is_cancel = false and (u.unified_status is null or u.unified_status=0)
        <if test=' queryInfo !=null and queryInfo != &apos;&apos;'>
            and (user_id like concat('%',#{queryInfo},'%') or partner_name like concat('%',#{queryInfo},'%') or name
            like concat('%',#{queryInfo},'%') or phone like concat('%',#{queryInfo},'%'))
        </if>
        <if test="userId != null and userId !=''">
            and u.user_id like concat('%', #{userId},'%')
        </if>
        <if test="userName != null and userName != ''">
            and u.name like concat ('%',#{userName},'%')
        </if>
        <if test="partnerName != null and partnerName != ''">
            and u.partner_name like concat ('%',#{partnerName},'%')
        </if>
        <if test=' queryRoleIds !=null and queryRoleIds.size() != 0'>
            and u.role_id in
            <foreach item='item' index='index' collection='queryRoleIds' open='(' separator=',' close=')'>
                #{item}
            </foreach>
        </if>
        <if test=' queryRoleId !=null and queryRoleId != &apos;&apos;'>
            and u.role_id = #{queryRoleId}
        </if>
        <if test=' queryUserIds !=null and queryUserIds.size() != 0'>
            and u.user_id in
            <foreach item='item' index='index' collection='queryUserIds' open='(' separator=',' close=')'>
                #{item}
            </foreach>
        </if>
        order by update_time desc
        limit ${pageIndex}, ${num}
    </select>

    <select id="pageCount4User" resultType="java.lang.Long">
        select count(*)
        from user u where 1=1 and u.is_cancel = false and (u.unified_status is null or u.unified_status=0)
        <if test=' queryInfo !=null and queryInfo != &apos;&apos;'>
            and (user_id like concat('%',#{queryInfo},'%') or partner_name like concat('%',#{queryInfo},'%') or name
            like concat('%',#{queryInfo},'%') or phone like concat('%',#{queryInfo},'%'))
        </if>
        <if test="userId != null and userId !=''">
            and u.user_id like concat('%', #{userId},'%')
        </if>
        <if test="userName != null and userName != ''">
            and u.name like concat ('%',#{userName},'%')
        </if>
        <if test="partnerName != null and partnerName != ''">
            and u.partner_name like concat ('%',#{partnerName},'%')
        </if>
        <if test=' queryRoleIds !=null and queryRoleIds.size() != 0'>
            and u.role_id in
            <foreach item='item' index='index' collection='queryRoleIds' open='(' separator=',' close=')'>
                #{item}
            </foreach>
        </if>
        <if test=' queryRoleId !=null and queryRoleId != &apos;&apos;'>
            and u.role_id = #{queryRoleId}
        </if>
        <if test=' queryUserIds !=null and queryUserIds.size() != 0'>
            and u.user_id in
            <foreach item='item' index='index' collection='queryUserIds' open='(' separator=',' close=')'>
                #{item}
            </foreach>
        </if>
    </select>

    <delete id="batchDeleteUser">
        update user set is_cancel = true, update_time = now() where user_id in
        <foreach item='item' index='index' collection='userIds' open='(' separator=',' close=')'>
            #{item}
        </foreach>
    </delete>

    <select id="selectBatchUser" resultType="com.chinamobile.iot.sc.pojo.mapper.UserDO">
        select
        user_id,name,pwd,phone,remark,is_admin,role_id,create_time,update_time,is_cancel,email,company,partner_name,creator,is_primary,is_send
        from user where user_id in
        <foreach item='item' index='index' collection='userIds' open='(' separator=',' close=')'>
            #{item}
        </foreach>
    </select>

    <select id="getPartnerUserByRoleId" resultType="com.chinamobile.iot.sc.pojo.mapper.UserDO">
        select
        user_id,name,pwd,phone,remark,is_admin,role_id,create_time,update_time,is_cancel,email,company,partner_name,creator,is_primary,is_send
        from user u where 1=1 and u.is_cancel = false and partner_name is not null and (u.unified_status is null or u.unified_status=0)
        <if test=' roleId !=null and roleId != &apos;&apos;'>
            and u.role_id = #{roleId}
        </if>
        GROUP BY partner_name HAVING COUNT(partner_name)=1
    </select>

    <select id="getPageUserPipe" resultType="com.chinamobile.iot.sc.vo.response.UserPipeDO">
        SELECT
        u.user_id userId,
        u.`name`,
        u.account,
        u.phone,
        u.email,
        u.company,
        u.department_name departmentName,
        u.new_job_number newJobNumber,
        u.old_job_number oldJobNumber,
        r.role_type roleType,
        r.name roleName
        FROM
        `user` u,
        role r
        WHERE
        u.is_cancel = FALSE and u.role_id = r.id and (u.unified_status is null or u.unified_status=0)
        AND r.role_type in
        ('productOperatorImport','productOperatorFrame','productOperatorRecheck','productOperatorUltimately')
        <if test="name != null and name != ''">
            and u.name like concat ('%',#{name},'%')
        </if>
        <if test="account != null and account != ''">
            and u.account like concat ('%',#{account},'%')
        </if>
        <if test="phone != null and phone != ''">
            and u.phone like concat ('%',#{phone},'%')
        </if>
        <if test="roleType != null and roleType != ''">
            and r.role_type =#{roleType}
        </if>
        <if test="departmentName != null and departmentName != ''">
            and u.department_name =#{departmentName}
        </if>
        ORDER BY
        u.update_time DESC
        limit ${pageIndex}, ${num}
    </select>

    <select id="getPageCountUserPipe" resultType="java.lang.Long">
        select count(*)
        from
        user u, role r
        where u.is_cancel = FALSE and u.role_id = r.id and (u.unified_status is null or u.unified_status=0)
        AND r.role_type in
        ('productOperatorImport','productOperatorFrame','productOperatorRecheck','productOperatorUltimately')
        <if test="name != null and name != ''">
            and u.name like concat ('%',#{name},'%')
        </if>
        <if test="account != null and account != ''">
            and u.account like concat ('%',#{account},'%')
        </if>
        <if test="phone != null and phone != ''">
            and u.phone like concat ('%',#{phone},'%')
        </if>
        <if test="roleType != null and roleType != ''">
            and r.role_type =#{roleType}
        </if>
        <if test="departmentName != null and departmentName != ''">
            and u.department_name =#{departmentName}
        </if>
        ORDER BY
        u.update_time DESC
    </select>
    <select id="getAllUserDepartment" resultType="java.lang.String">
        select distinct u.department_name departmentName
        from user u,
             role r
        where u.is_cancel = FALSE and (u.unified_status is null or u.unified_status=0)
          and u.role_id = r.id
          AND r.role_type in
              ('productOperatorImport', 'productOperatorFrame', 'productOperatorRecheck', 'productOperatorUltimately')
    </select>

    <select id="getUserByRoleTypeList" parameterType="java.util.List"
            resultType="com.chinamobile.iot.sc.vo.response.UserPipeDO">
        SELECT
        u.user_id userId,
        u.`name`,
        u.account,
        u.phone,
        u.email,
        u.company,
        u.department_name departmentName,
        u.new_job_number newJobNumber,
        u.old_job_number oldJobNumber,
        r.role_type roleType
        FROM
        `user` u,
        role_info r
        WHERE
        u.is_cancel = FALSE and u.role_id = r.id and (u.unified_status is null or u.unified_status=0)
        <if test="roleTypes != null">
            and r.role_type in
            <foreach item='item' index='index' collection='roleTypes' open='(' separator=',' close=')'>
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getUserByAuthCodeList" parameterType="java.util.List"
            resultType="com.chinamobile.iot.sc.vo.response.UserPipeDO">
        SELECT
        u.user_id userId,
        u.`name`,
        u.account,
        u.phone,
        u.email,
        u.company,
        u.department_name departmentName,
        u.new_job_number newJobNumber,
        u.old_job_number oldJobNumber,
        u.is_admin isAdmin
        FROM
        `user` u
        left join role_auth ra on u.role_id = ra.role_id
        left join auth a on a.id = ra.auth_id
        WHERE
        u.is_cancel = FALSE and (u.unified_status is null or u.unified_status=0)
        <if test="authCodes != null">
            and a.auth_code in
            <foreach item='item' index='index' collection='authCodes' open='(' separator=',' close=')'>
                #{item}
            </foreach>
        </if>
        group by u.user_id
    </select>

    <select id="getUserList" resultType="com.chinamobile.iot.sc.vo.response.UserListItemVO">
        select u.user_id userId,
        u.name name,
        r.name roleName,
        r.system system,
        r.id roleId,
        u.company company,
        u.department_name departmentName,
        CASE
        WHEN u.iot_type = 1 THEN '内部用户'
        WHEN u.iot_type = 2 THEN '外部用户'
        ELSE '-'
        END iotTypeStr,
        CASE
        WHEN u.user_type = 'normal' THEN '正式账号'
        WHEN u.user_type = 'test' THEN '测试账号'
        ELSE '-'
        END userTypeStr,
        u.creator creatorName,
        u.create_time createTime,
        u.update_time updateTime,
        CASE
        WHEN u.unified_status = 0 THEN '正常'
        WHEN u.unified_status = 1 THEN '锁定'
        WHEN u.unified_status = 2 THEN '未启用'
        WHEN u.unified_status = 3 THEN '注销'
        ELSE '-'
        END uniStatusStr,
        CASE
        WHEN u.is_cancel = 1 THEN '停用'
        WHEN u.is_cancel = 0 THEN '正常'
        ELSE '-'
        END localStatusStr,
        u.is_admin isAdmin
        from user u left join role_info r on u.role_id = r.id
        where 1=1
            and u.is_logoff = 0
        <if test="param.userId != null and param.userId !=''">
            and u.user_id like concat('%', #{param.userId},'%')
        </if>
        <if test="param.name != null and param.name != ''">
            and u.name like concat ('%',#{param.name},'%')
        </if>
        <if test="param.phone != null and param.phone != ''">
            and u.phone = #{param.phone}
        </if>
        <if test="param.partnerName != null and param.partnerName != ''">
            and u.partner_name like concat ('%',#{param.partnerName},'%')
        </if>
        <if test=" param.roleId !=null and param.roleId != ''">
            and u.role_id = #{param.roleId}
        </if>

        <if test=" param.roleId == null and roleIds !=null and roleIds.size() != 0">
            and u.role_id in
            <foreach item='item' index='index' collection='roleIds' open='(' separator=',' close=')'>
                #{item}
            </foreach>
        </if>

        <if test="param.departmentId != null and param.departmentId != ''">
            and u.department_id = #{param.departmentId}
        </if>
        <if test="param.departmentName != null and param.departmentName != ''">
            and u.department_name  like concat ('%',#{param.departmentName},'%')
        </if>
        <if test="param.iotType != null ">
            and u.iot_type = #{param.iotType}
        </if>
        <if test="param.userType != null and param.userType != ''">
            and u.user_type = #{param.userType}
        </if>
        <if test="param.uniqueStatus != null ">
            and u.unified_status = #{param.uniqueStatus}
        </if>
        <if test="param.isCancel != null ">
            and u.is_cancel = #{param.isCancel}
        </if>
        <if test="param.queryInfo !=null and param.queryInfo != ''">
            and (u.user_id like concat('%',#{param.queryInfo},'%') or u.partner_name like concat('%',#{param.queryInfo},'%') or u.name
            like concat('%',#{param.queryInfo},'%') )
        </if>
        order by u.update_time desc
    </select>
    <select id="getRoleMessageByAuthId" resultType="com.chinamobile.iot.sc.pojo.mapper.UserAndRoleByAuthDO">
        select distinct ri.id roleId, ri.name roleName from
        role_info ri
        left join role_auth ra on ri.id = ra.role_id
        left join auth au on au.id = ra.auth_id
        where 1=1
        and au.auth_code in ('product_shelf_process','product_takedown_process','product_change_process') and ri.role_type != 'admin'
    </select>
    <select id="queryRoleMessageByAuthCode" resultType="com.chinamobile.iot.sc.pojo.mapper.RoleMessageByAuthCodeDO">
        select distinct ri.id roleId, ri.name roleName from
            role_info ri
                left join role_auth ra on ri.id = ra.role_id
                left join auth au on au.id = ra.auth_id
        where 1=1
          and ri.role_type != 'admin'
        <if test=" authCodes !=null and authCodes.size() != 0">
            and au.auth_code in
            <foreach item='item' index='index' collection='authCodes' open='(' separator=',' close=')'>
                #{item}
            </foreach>
        </if>
    </select>
</mapper>