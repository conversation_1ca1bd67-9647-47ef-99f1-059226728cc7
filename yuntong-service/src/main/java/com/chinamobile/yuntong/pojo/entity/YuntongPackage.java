package com.chinamobile.yuntong.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 云瞳套餐信息表
 *
 * <AUTHOR>
public class YuntongPackage implements Serializable {
    /**
     *
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    private String id;

    /**
     * sku编码
     *
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    private String skuCode;

    /**
     * 原子编码
     *
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    private String atomCode;

    /**
     * 原子名称
     *
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    private String atomName;

    /**
     * 套餐名称,新订购套餐取sku名称
     *
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    private String packageName;

    /**
     * 套餐编码
     *
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    private String packageNumber;

    /**
     * 存储属性 1-全天 2-事件
     *
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    private Integer storageType;

    /**
     * 存储时长,天数：1，3，7，30
     *
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    private Integer storageDays;

    /**
     * 套餐属性 1-标清 2-高清
     *
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    private Integer packageProperty;

    /**
     * 资费类型 1-免费 2-付费
     *
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    private Integer packageCost;

    /**
     * 服务时长,月数: 1,2,12,24,-1(连续包月)
     *
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    private Integer serviceMonths;

    /**
     * 套餐类型 1- 新订购一次性套餐  2-存量客户一次性套餐 3-存量客户连续包月套餐 4-电商渠道订购套餐
     *
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    private Integer packageType;

    /**
     *
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    private Date createTime;

    /**
     *
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..yuntong_package.id
     *
     * @return the value of supply_chain..yuntong_package.id
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public YuntongPackage withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_package.id
     *
     * @param id the value for supply_chain..yuntong_package.id
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_package.sku_code
     *
     * @return the value of supply_chain..yuntong_package.sku_code
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public String getSkuCode() {
        return skuCode;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public YuntongPackage withSkuCode(String skuCode) {
        this.setSkuCode(skuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_package.sku_code
     *
     * @param skuCode the value for supply_chain..yuntong_package.sku_code
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_package.atom_code
     *
     * @return the value of supply_chain..yuntong_package.atom_code
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public String getAtomCode() {
        return atomCode;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public YuntongPackage withAtomCode(String atomCode) {
        this.setAtomCode(atomCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_package.atom_code
     *
     * @param atomCode the value for supply_chain..yuntong_package.atom_code
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public void setAtomCode(String atomCode) {
        this.atomCode = atomCode;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_package.atom_name
     *
     * @return the value of supply_chain..yuntong_package.atom_name
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public String getAtomName() {
        return atomName;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public YuntongPackage withAtomName(String atomName) {
        this.setAtomName(atomName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_package.atom_name
     *
     * @param atomName the value for supply_chain..yuntong_package.atom_name
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public void setAtomName(String atomName) {
        this.atomName = atomName;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_package.package_name
     *
     * @return the value of supply_chain..yuntong_package.package_name
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public String getPackageName() {
        return packageName;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public YuntongPackage withPackageName(String packageName) {
        this.setPackageName(packageName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_package.package_name
     *
     * @param packageName the value for supply_chain..yuntong_package.package_name
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_package.package_number
     *
     * @return the value of supply_chain..yuntong_package.package_number
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public String getPackageNumber() {
        return packageNumber;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public YuntongPackage withPackageNumber(String packageNumber) {
        this.setPackageNumber(packageNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_package.package_number
     *
     * @param packageNumber the value for supply_chain..yuntong_package.package_number
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public void setPackageNumber(String packageNumber) {
        this.packageNumber = packageNumber;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_package.storage_type
     *
     * @return the value of supply_chain..yuntong_package.storage_type
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public Integer getStorageType() {
        return storageType;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public YuntongPackage withStorageType(Integer storageType) {
        this.setStorageType(storageType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_package.storage_type
     *
     * @param storageType the value for supply_chain..yuntong_package.storage_type
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public void setStorageType(Integer storageType) {
        this.storageType = storageType;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_package.storage_days
     *
     * @return the value of supply_chain..yuntong_package.storage_days
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public Integer getStorageDays() {
        return storageDays;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public YuntongPackage withStorageDays(Integer storageDays) {
        this.setStorageDays(storageDays);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_package.storage_days
     *
     * @param storageDays the value for supply_chain..yuntong_package.storage_days
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public void setStorageDays(Integer storageDays) {
        this.storageDays = storageDays;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_package.package_property
     *
     * @return the value of supply_chain..yuntong_package.package_property
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public Integer getPackageProperty() {
        return packageProperty;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public YuntongPackage withPackageProperty(Integer packageProperty) {
        this.setPackageProperty(packageProperty);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_package.package_property
     *
     * @param packageProperty the value for supply_chain..yuntong_package.package_property
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public void setPackageProperty(Integer packageProperty) {
        this.packageProperty = packageProperty;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_package.package_cost
     *
     * @return the value of supply_chain..yuntong_package.package_cost
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public Integer getPackageCost() {
        return packageCost;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public YuntongPackage withPackageCost(Integer packageCost) {
        this.setPackageCost(packageCost);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_package.package_cost
     *
     * @param packageCost the value for supply_chain..yuntong_package.package_cost
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public void setPackageCost(Integer packageCost) {
        this.packageCost = packageCost;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_package.service_months
     *
     * @return the value of supply_chain..yuntong_package.service_months
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public Integer getServiceMonths() {
        return serviceMonths;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public YuntongPackage withServiceMonths(Integer serviceMonths) {
        this.setServiceMonths(serviceMonths);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_package.service_months
     *
     * @param serviceMonths the value for supply_chain..yuntong_package.service_months
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public void setServiceMonths(Integer serviceMonths) {
        this.serviceMonths = serviceMonths;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_package.package_type
     *
     * @return the value of supply_chain..yuntong_package.package_type
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public Integer getPackageType() {
        return packageType;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public YuntongPackage withPackageType(Integer packageType) {
        this.setPackageType(packageType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_package.package_type
     *
     * @param packageType the value for supply_chain..yuntong_package.package_type
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public void setPackageType(Integer packageType) {
        this.packageType = packageType;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_package.create_time
     *
     * @return the value of supply_chain..yuntong_package.create_time
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public YuntongPackage withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_package.create_time
     *
     * @param createTime the value for supply_chain..yuntong_package.create_time
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..yuntong_package.update_time
     *
     * @return the value of supply_chain..yuntong_package.update_time
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public YuntongPackage withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..yuntong_package.update_time
     *
     * @param updateTime the value for supply_chain..yuntong_package.update_time
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", skuCode=").append(skuCode);
        sb.append(", atomCode=").append(atomCode);
        sb.append(", atomName=").append(atomName);
        sb.append(", packageName=").append(packageName);
        sb.append(", packageNumber=").append(packageNumber);
        sb.append(", storageType=").append(storageType);
        sb.append(", storageDays=").append(storageDays);
        sb.append(", packageProperty=").append(packageProperty);
        sb.append(", packageCost=").append(packageCost);
        sb.append(", serviceMonths=").append(serviceMonths);
        sb.append(", packageType=").append(packageType);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        YuntongPackage other = (YuntongPackage) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSkuCode() == null ? other.getSkuCode() == null : this.getSkuCode().equals(other.getSkuCode()))
            && (this.getAtomCode() == null ? other.getAtomCode() == null : this.getAtomCode().equals(other.getAtomCode()))
            && (this.getAtomName() == null ? other.getAtomName() == null : this.getAtomName().equals(other.getAtomName()))
            && (this.getPackageName() == null ? other.getPackageName() == null : this.getPackageName().equals(other.getPackageName()))
            && (this.getPackageNumber() == null ? other.getPackageNumber() == null : this.getPackageNumber().equals(other.getPackageNumber()))
            && (this.getStorageType() == null ? other.getStorageType() == null : this.getStorageType().equals(other.getStorageType()))
            && (this.getStorageDays() == null ? other.getStorageDays() == null : this.getStorageDays().equals(other.getStorageDays()))
            && (this.getPackageProperty() == null ? other.getPackageProperty() == null : this.getPackageProperty().equals(other.getPackageProperty()))
            && (this.getPackageCost() == null ? other.getPackageCost() == null : this.getPackageCost().equals(other.getPackageCost()))
            && (this.getServiceMonths() == null ? other.getServiceMonths() == null : this.getServiceMonths().equals(other.getServiceMonths()))
            && (this.getPackageType() == null ? other.getPackageType() == null : this.getPackageType().equals(other.getPackageType()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSkuCode() == null) ? 0 : getSkuCode().hashCode());
        result = prime * result + ((getAtomCode() == null) ? 0 : getAtomCode().hashCode());
        result = prime * result + ((getAtomName() == null) ? 0 : getAtomName().hashCode());
        result = prime * result + ((getPackageName() == null) ? 0 : getPackageName().hashCode());
        result = prime * result + ((getPackageNumber() == null) ? 0 : getPackageNumber().hashCode());
        result = prime * result + ((getStorageType() == null) ? 0 : getStorageType().hashCode());
        result = prime * result + ((getStorageDays() == null) ? 0 : getStorageDays().hashCode());
        result = prime * result + ((getPackageProperty() == null) ? 0 : getPackageProperty().hashCode());
        result = prime * result + ((getPackageCost() == null) ? 0 : getPackageCost().hashCode());
        result = prime * result + ((getServiceMonths() == null) ? 0 : getServiceMonths().hashCode());
        result = prime * result + ((getPackageType() == null) ? 0 : getPackageType().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Jan 15 10:25:32 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        skuCode("sku_code", "skuCode", "VARCHAR", false),
        atomCode("atom_code", "atomCode", "VARCHAR", false),
        atomName("atom_name", "atomName", "VARCHAR", false),
        packageName("package_name", "packageName", "VARCHAR", false),
        packageNumber("package_number", "packageNumber", "VARCHAR", false),
        storageType("storage_type", "storageType", "INTEGER", false),
        storageDays("storage_days", "storageDays", "INTEGER", false),
        packageProperty("package_property", "packageProperty", "INTEGER", false),
        packageCost("package_cost", "packageCost", "INTEGER", false),
        serviceMonths("service_months", "serviceMonths", "INTEGER", false),
        packageType("package_type", "packageType", "INTEGER", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Wed Jan 15 10:25:32 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Jan 15 10:25:32 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Jan 15 10:25:32 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Jan 15 10:25:32 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Jan 15 10:25:32 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Jan 15 10:25:32 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Jan 15 10:25:32 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Jan 15 10:25:32 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Jan 15 10:25:32 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Jan 15 10:25:32 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Jan 15 10:25:32 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Jan 15 10:25:32 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Jan 15 10:25:32 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Jan 15 10:25:32 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Jan 15 10:25:32 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Jan 15 10:25:32 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Jan 15 10:25:32 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}