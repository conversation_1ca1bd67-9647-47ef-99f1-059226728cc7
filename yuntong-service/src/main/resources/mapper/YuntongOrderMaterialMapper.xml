<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.yuntong.dao.YuntongOrderMaterialMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.yuntong.pojo.entity.YuntongOrderMaterial">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 10 14:51:38 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_number" jdbcType="VARCHAR" property="orderNumber" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="product_number" jdbcType="VARCHAR" property="productNumber" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="taxprice" jdbcType="VARCHAR" property="taxprice" />
    <result column="amount" jdbcType="DOUBLE" property="amount" />
    <result column="notaxprice" jdbcType="VARCHAR" property="notaxprice" />
    <result column="taxrate" jdbcType="DOUBLE" property="taxrate" />
    <result column="tax" jdbcType="VARCHAR" property="tax" />
    <result column="valorem_total" jdbcType="VARCHAR" property="valoremTotal" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="price" jdbcType="VARCHAR" property="price" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 10 14:51:38 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 10 14:51:38 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 10 14:51:38 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, order_number, product_name, product_number, model, taxprice, amount, notaxprice, 
    taxrate, tax, valorem_total, unit, price, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrderMaterialExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 10 14:51:38 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from yuntong_order_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 10 14:51:38 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from yuntong_order_material
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 10 14:51:38 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from yuntong_order_material
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrderMaterialExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 10 14:51:38 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from yuntong_order_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrderMaterial">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 10 14:51:38 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into yuntong_order_material (id, order_number, product_name, 
      product_number, model, taxprice, 
      amount, notaxprice, taxrate, 
      tax, valorem_total, unit, 
      price, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{orderNumber,jdbcType=VARCHAR}, #{productName,jdbcType=VARCHAR}, 
      #{productNumber,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, #{taxprice,jdbcType=VARCHAR}, 
      #{amount,jdbcType=DOUBLE}, #{notaxprice,jdbcType=VARCHAR}, #{taxrate,jdbcType=DOUBLE}, 
      #{tax,jdbcType=VARCHAR}, #{valoremTotal,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, 
      #{price,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrderMaterial">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 10 14:51:38 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into yuntong_order_material
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderNumber != null">
        order_number,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="productNumber != null">
        product_number,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="taxprice != null">
        taxprice,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="notaxprice != null">
        notaxprice,
      </if>
      <if test="taxrate != null">
        taxrate,
      </if>
      <if test="tax != null">
        tax,
      </if>
      <if test="valoremTotal != null">
        valorem_total,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderNumber != null">
        #{orderNumber,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="productNumber != null">
        #{productNumber,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="taxprice != null">
        #{taxprice,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DOUBLE},
      </if>
      <if test="notaxprice != null">
        #{notaxprice,jdbcType=VARCHAR},
      </if>
      <if test="taxrate != null">
        #{taxrate,jdbcType=DOUBLE},
      </if>
      <if test="tax != null">
        #{tax,jdbcType=VARCHAR},
      </if>
      <if test="valoremTotal != null">
        #{valoremTotal,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrderMaterialExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 10 14:51:38 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from yuntong_order_material
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 10 14:51:38 CST 2025. by MyBatis Generator, do not modify.
    -->
    update yuntong_order_material
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNumber != null">
        order_number = #{record.orderNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=VARCHAR},
      </if>
      <if test="record.productNumber != null">
        product_number = #{record.productNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.model != null">
        model = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.taxprice != null">
        taxprice = #{record.taxprice,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=DOUBLE},
      </if>
      <if test="record.notaxprice != null">
        notaxprice = #{record.notaxprice,jdbcType=VARCHAR},
      </if>
      <if test="record.taxrate != null">
        taxrate = #{record.taxrate,jdbcType=DOUBLE},
      </if>
      <if test="record.tax != null">
        tax = #{record.tax,jdbcType=VARCHAR},
      </if>
      <if test="record.valoremTotal != null">
        valorem_total = #{record.valoremTotal,jdbcType=VARCHAR},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 10 14:51:38 CST 2025. by MyBatis Generator, do not modify.
    -->
    update yuntong_order_material
    set id = #{record.id,jdbcType=VARCHAR},
      order_number = #{record.orderNumber,jdbcType=VARCHAR},
      product_name = #{record.productName,jdbcType=VARCHAR},
      product_number = #{record.productNumber,jdbcType=VARCHAR},
      model = #{record.model,jdbcType=VARCHAR},
      taxprice = #{record.taxprice,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=DOUBLE},
      notaxprice = #{record.notaxprice,jdbcType=VARCHAR},
      taxrate = #{record.taxrate,jdbcType=DOUBLE},
      tax = #{record.tax,jdbcType=VARCHAR},
      valorem_total = #{record.valoremTotal,jdbcType=VARCHAR},
      unit = #{record.unit,jdbcType=VARCHAR},
      price = #{record.price,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrderMaterial">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 10 14:51:38 CST 2025. by MyBatis Generator, do not modify.
    -->
    update yuntong_order_material
    <set>
      <if test="orderNumber != null">
        order_number = #{orderNumber,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="productNumber != null">
        product_number = #{productNumber,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="taxprice != null">
        taxprice = #{taxprice,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DOUBLE},
      </if>
      <if test="notaxprice != null">
        notaxprice = #{notaxprice,jdbcType=VARCHAR},
      </if>
      <if test="taxrate != null">
        taxrate = #{taxrate,jdbcType=DOUBLE},
      </if>
      <if test="tax != null">
        tax = #{tax,jdbcType=VARCHAR},
      </if>
      <if test="valoremTotal != null">
        valorem_total = #{valoremTotal,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.yuntong.pojo.entity.YuntongOrderMaterial">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 10 14:51:38 CST 2025. by MyBatis Generator, do not modify.
    -->
    update yuntong_order_material
    set order_number = #{orderNumber,jdbcType=VARCHAR},
      product_name = #{productName,jdbcType=VARCHAR},
      product_number = #{productNumber,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      taxprice = #{taxprice,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=DOUBLE},
      notaxprice = #{notaxprice,jdbcType=VARCHAR},
      taxrate = #{taxrate,jdbcType=DOUBLE},
      tax = #{tax,jdbcType=VARCHAR},
      valorem_total = #{valoremTotal,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      price = #{price,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 10 14:51:38 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into yuntong_order_material
    (id, order_number, product_name, product_number, model, taxprice, amount, notaxprice, 
      taxrate, tax, valorem_total, unit, price, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.orderNumber,jdbcType=VARCHAR}, #{item.productName,jdbcType=VARCHAR}, 
        #{item.productNumber,jdbcType=VARCHAR}, #{item.model,jdbcType=VARCHAR}, #{item.taxprice,jdbcType=VARCHAR}, 
        #{item.amount,jdbcType=DOUBLE}, #{item.notaxprice,jdbcType=VARCHAR}, #{item.taxrate,jdbcType=DOUBLE}, 
        #{item.tax,jdbcType=VARCHAR}, #{item.valoremTotal,jdbcType=VARCHAR}, #{item.unit,jdbcType=VARCHAR}, 
        #{item.price,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 10 14:51:38 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into yuntong_order_material (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'order_number'.toString() == column.value">
          #{item.orderNumber,jdbcType=VARCHAR}
        </if>
        <if test="'product_name'.toString() == column.value">
          #{item.productName,jdbcType=VARCHAR}
        </if>
        <if test="'product_number'.toString() == column.value">
          #{item.productNumber,jdbcType=VARCHAR}
        </if>
        <if test="'model'.toString() == column.value">
          #{item.model,jdbcType=VARCHAR}
        </if>
        <if test="'taxprice'.toString() == column.value">
          #{item.taxprice,jdbcType=VARCHAR}
        </if>
        <if test="'amount'.toString() == column.value">
          #{item.amount,jdbcType=DOUBLE}
        </if>
        <if test="'notaxprice'.toString() == column.value">
          #{item.notaxprice,jdbcType=VARCHAR}
        </if>
        <if test="'taxrate'.toString() == column.value">
          #{item.taxrate,jdbcType=DOUBLE}
        </if>
        <if test="'tax'.toString() == column.value">
          #{item.tax,jdbcType=VARCHAR}
        </if>
        <if test="'valorem_total'.toString() == column.value">
          #{item.valoremTotal,jdbcType=VARCHAR}
        </if>
        <if test="'unit'.toString() == column.value">
          #{item.unit,jdbcType=VARCHAR}
        </if>
        <if test="'price'.toString() == column.value">
          #{item.price,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>