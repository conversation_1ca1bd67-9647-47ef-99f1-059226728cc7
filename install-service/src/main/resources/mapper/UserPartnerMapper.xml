<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.install.dao.UserPartnerMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.install.pojo.entity.UserPartner">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:50 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="pwd" jdbcType="VARCHAR" property="pwd" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="role_id" jdbcType="VARCHAR" property="roleId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_cancel" jdbcType="BIT" property="isCancel" />
    <result column="is_logoff" jdbcType="BIT" property="isLogoff" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="partner_name" jdbcType="VARCHAR" property="partnerName" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="is_primary" jdbcType="BIT" property="isPrimary" />
    <result column="is_send" jdbcType="BIT" property="isSend" />
    <result column="user_type" jdbcType="VARCHAR" property="userType" />
    <result column="can_enable" jdbcType="BIT" property="canEnable" />
    <result column="cj_status" jdbcType="VARCHAR" property="cjStatus" />
    <result column="cj_apply_time" jdbcType="TIMESTAMP" property="cjApplyTime" />
    <result column="cj_advice" jdbcType="VARCHAR" property="cjAdvice" />
    <result column="unified_code" jdbcType="VARCHAR" property="unifiedCode" />
    <result column="company_type" jdbcType="VARCHAR" property="companyType" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="location_id" jdbcType="VARCHAR" property="locationId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:50 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:50 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:50 CST 2025. by MyBatis Generator, do not modify.
    -->
    user_id, name, pwd, phone, remark, role_id, create_time, update_time, is_cancel, 
    is_logoff, email, partner_name, creator, is_primary, is_send, user_type, can_enable, 
    cj_status, cj_apply_time, cj_advice, unified_code, company_type, province, be_id, 
    location, location_id
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.install.pojo.entity.UserPartnerExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:50 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_partner
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:50 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from user_partner
    where user_id = #{userId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:50 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from user_partner
    where user_id = #{userId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.install.pojo.entity.UserPartnerExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:50 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from user_partner
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.install.pojo.entity.UserPartner">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:50 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into user_partner (user_id, name, pwd, 
      phone, remark, role_id, 
      create_time, update_time, is_cancel, 
      is_logoff, email, partner_name, 
      creator, is_primary, is_send, 
      user_type, can_enable, cj_status, 
      cj_apply_time, cj_advice, unified_code, 
      company_type, province, be_id, 
      location, location_id)
    values (#{userId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{pwd,jdbcType=VARCHAR}, 
      #{phone,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{roleId,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{isCancel,jdbcType=BIT}, 
      #{isLogoff,jdbcType=BIT}, #{email,jdbcType=VARCHAR}, #{partnerName,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{isPrimary,jdbcType=BIT}, #{isSend,jdbcType=BIT}, 
      #{userType,jdbcType=VARCHAR}, #{canEnable,jdbcType=BIT}, #{cjStatus,jdbcType=VARCHAR}, 
      #{cjApplyTime,jdbcType=TIMESTAMP}, #{cjAdvice,jdbcType=VARCHAR}, #{unifiedCode,jdbcType=VARCHAR}, 
      #{companyType,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, #{beId,jdbcType=VARCHAR}, 
      #{location,jdbcType=VARCHAR}, #{locationId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.install.pojo.entity.UserPartner">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:50 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into user_partner
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="pwd != null">
        pwd,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isCancel != null">
        is_cancel,
      </if>
      <if test="isLogoff != null">
        is_logoff,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="partnerName != null">
        partner_name,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="isPrimary != null">
        is_primary,
      </if>
      <if test="isSend != null">
        is_send,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="canEnable != null">
        can_enable,
      </if>
      <if test="cjStatus != null">
        cj_status,
      </if>
      <if test="cjApplyTime != null">
        cj_apply_time,
      </if>
      <if test="cjAdvice != null">
        cj_advice,
      </if>
      <if test="unifiedCode != null">
        unified_code,
      </if>
      <if test="companyType != null">
        company_type,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="locationId != null">
        location_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="pwd != null">
        #{pwd,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isCancel != null">
        #{isCancel,jdbcType=BIT},
      </if>
      <if test="isLogoff != null">
        #{isLogoff,jdbcType=BIT},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="partnerName != null">
        #{partnerName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="isPrimary != null">
        #{isPrimary,jdbcType=BIT},
      </if>
      <if test="isSend != null">
        #{isSend,jdbcType=BIT},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=VARCHAR},
      </if>
      <if test="canEnable != null">
        #{canEnable,jdbcType=BIT},
      </if>
      <if test="cjStatus != null">
        #{cjStatus,jdbcType=VARCHAR},
      </if>
      <if test="cjApplyTime != null">
        #{cjApplyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cjAdvice != null">
        #{cjAdvice,jdbcType=VARCHAR},
      </if>
      <if test="unifiedCode != null">
        #{unifiedCode,jdbcType=VARCHAR},
      </if>
      <if test="companyType != null">
        #{companyType,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="locationId != null">
        #{locationId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.install.pojo.entity.UserPartnerExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:50 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from user_partner
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:50 CST 2025. by MyBatis Generator, do not modify.
    -->
    update user_partner
    <set>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.pwd != null">
        pwd = #{record.pwd,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.roleId != null">
        role_id = #{record.roleId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isCancel != null">
        is_cancel = #{record.isCancel,jdbcType=BIT},
      </if>
      <if test="record.isLogoff != null">
        is_logoff = #{record.isLogoff,jdbcType=BIT},
      </if>
      <if test="record.email != null">
        email = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.partnerName != null">
        partner_name = #{record.partnerName,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.isPrimary != null">
        is_primary = #{record.isPrimary,jdbcType=BIT},
      </if>
      <if test="record.isSend != null">
        is_send = #{record.isSend,jdbcType=BIT},
      </if>
      <if test="record.userType != null">
        user_type = #{record.userType,jdbcType=VARCHAR},
      </if>
      <if test="record.canEnable != null">
        can_enable = #{record.canEnable,jdbcType=BIT},
      </if>
      <if test="record.cjStatus != null">
        cj_status = #{record.cjStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.cjApplyTime != null">
        cj_apply_time = #{record.cjApplyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cjAdvice != null">
        cj_advice = #{record.cjAdvice,jdbcType=VARCHAR},
      </if>
      <if test="record.unifiedCode != null">
        unified_code = #{record.unifiedCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyType != null">
        company_type = #{record.companyType,jdbcType=VARCHAR},
      </if>
      <if test="record.province != null">
        province = #{record.province,jdbcType=VARCHAR},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.location != null">
        location = #{record.location,jdbcType=VARCHAR},
      </if>
      <if test="record.locationId != null">
        location_id = #{record.locationId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:50 CST 2025. by MyBatis Generator, do not modify.
    -->
    update user_partner
    set user_id = #{record.userId,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      pwd = #{record.pwd,jdbcType=VARCHAR},
      phone = #{record.phone,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      role_id = #{record.roleId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_cancel = #{record.isCancel,jdbcType=BIT},
      is_logoff = #{record.isLogoff,jdbcType=BIT},
      email = #{record.email,jdbcType=VARCHAR},
      partner_name = #{record.partnerName,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      is_primary = #{record.isPrimary,jdbcType=BIT},
      is_send = #{record.isSend,jdbcType=BIT},
      user_type = #{record.userType,jdbcType=VARCHAR},
      can_enable = #{record.canEnable,jdbcType=BIT},
      cj_status = #{record.cjStatus,jdbcType=VARCHAR},
      cj_apply_time = #{record.cjApplyTime,jdbcType=TIMESTAMP},
      cj_advice = #{record.cjAdvice,jdbcType=VARCHAR},
      unified_code = #{record.unifiedCode,jdbcType=VARCHAR},
      company_type = #{record.companyType,jdbcType=VARCHAR},
      province = #{record.province,jdbcType=VARCHAR},
      be_id = #{record.beId,jdbcType=VARCHAR},
      location = #{record.location,jdbcType=VARCHAR},
      location_id = #{record.locationId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.install.pojo.entity.UserPartner">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:50 CST 2025. by MyBatis Generator, do not modify.
    -->
    update user_partner
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="pwd != null">
        pwd = #{pwd,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isCancel != null">
        is_cancel = #{isCancel,jdbcType=BIT},
      </if>
      <if test="isLogoff != null">
        is_logoff = #{isLogoff,jdbcType=BIT},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="partnerName != null">
        partner_name = #{partnerName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="isPrimary != null">
        is_primary = #{isPrimary,jdbcType=BIT},
      </if>
      <if test="isSend != null">
        is_send = #{isSend,jdbcType=BIT},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=VARCHAR},
      </if>
      <if test="canEnable != null">
        can_enable = #{canEnable,jdbcType=BIT},
      </if>
      <if test="cjStatus != null">
        cj_status = #{cjStatus,jdbcType=VARCHAR},
      </if>
      <if test="cjApplyTime != null">
        cj_apply_time = #{cjApplyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cjAdvice != null">
        cj_advice = #{cjAdvice,jdbcType=VARCHAR},
      </if>
      <if test="unifiedCode != null">
        unified_code = #{unifiedCode,jdbcType=VARCHAR},
      </if>
      <if test="companyType != null">
        company_type = #{companyType,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="locationId != null">
        location_id = #{locationId,jdbcType=VARCHAR},
      </if>
    </set>
    where user_id = #{userId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.install.pojo.entity.UserPartner">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:50 CST 2025. by MyBatis Generator, do not modify.
    -->
    update user_partner
    set name = #{name,jdbcType=VARCHAR},
      pwd = #{pwd,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      role_id = #{roleId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_cancel = #{isCancel,jdbcType=BIT},
      is_logoff = #{isLogoff,jdbcType=BIT},
      email = #{email,jdbcType=VARCHAR},
      partner_name = #{partnerName,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      is_primary = #{isPrimary,jdbcType=BIT},
      is_send = #{isSend,jdbcType=BIT},
      user_type = #{userType,jdbcType=VARCHAR},
      can_enable = #{canEnable,jdbcType=BIT},
      cj_status = #{cjStatus,jdbcType=VARCHAR},
      cj_apply_time = #{cjApplyTime,jdbcType=TIMESTAMP},
      cj_advice = #{cjAdvice,jdbcType=VARCHAR},
      unified_code = #{unifiedCode,jdbcType=VARCHAR},
      company_type = #{companyType,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      be_id = #{beId,jdbcType=VARCHAR},
      location = #{location,jdbcType=VARCHAR},
      location_id = #{locationId,jdbcType=VARCHAR}
    where user_id = #{userId,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:50 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into user_partner
    (user_id, name, pwd, phone, remark, role_id, create_time, update_time, is_cancel, 
      is_logoff, email, partner_name, creator, is_primary, is_send, user_type, can_enable, 
      cj_status, cj_apply_time, cj_advice, unified_code, company_type, province, be_id, 
      location, location_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.userId,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.pwd,jdbcType=VARCHAR}, 
        #{item.phone,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.roleId,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.isCancel,jdbcType=BIT}, 
        #{item.isLogoff,jdbcType=BIT}, #{item.email,jdbcType=VARCHAR}, #{item.partnerName,jdbcType=VARCHAR}, 
        #{item.creator,jdbcType=VARCHAR}, #{item.isPrimary,jdbcType=BIT}, #{item.isSend,jdbcType=BIT}, 
        #{item.userType,jdbcType=VARCHAR}, #{item.canEnable,jdbcType=BIT}, #{item.cjStatus,jdbcType=VARCHAR}, 
        #{item.cjApplyTime,jdbcType=TIMESTAMP}, #{item.cjAdvice,jdbcType=VARCHAR}, #{item.unifiedCode,jdbcType=VARCHAR}, 
        #{item.companyType,jdbcType=VARCHAR}, #{item.province,jdbcType=VARCHAR}, #{item.beId,jdbcType=VARCHAR}, 
        #{item.location,jdbcType=VARCHAR}, #{item.locationId,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon May 26 16:36:50 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into user_partner (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'pwd'.toString() == column.value">
          #{item.pwd,jdbcType=VARCHAR}
        </if>
        <if test="'phone'.toString() == column.value">
          #{item.phone,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'role_id'.toString() == column.value">
          #{item.roleId,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_cancel'.toString() == column.value">
          #{item.isCancel,jdbcType=BIT}
        </if>
        <if test="'is_logoff'.toString() == column.value">
          #{item.isLogoff,jdbcType=BIT}
        </if>
        <if test="'email'.toString() == column.value">
          #{item.email,jdbcType=VARCHAR}
        </if>
        <if test="'partner_name'.toString() == column.value">
          #{item.partnerName,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'is_primary'.toString() == column.value">
          #{item.isPrimary,jdbcType=BIT}
        </if>
        <if test="'is_send'.toString() == column.value">
          #{item.isSend,jdbcType=BIT}
        </if>
        <if test="'user_type'.toString() == column.value">
          #{item.userType,jdbcType=VARCHAR}
        </if>
        <if test="'can_enable'.toString() == column.value">
          #{item.canEnable,jdbcType=BIT}
        </if>
        <if test="'cj_status'.toString() == column.value">
          #{item.cjStatus,jdbcType=VARCHAR}
        </if>
        <if test="'cj_apply_time'.toString() == column.value">
          #{item.cjApplyTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'cj_advice'.toString() == column.value">
          #{item.cjAdvice,jdbcType=VARCHAR}
        </if>
        <if test="'unified_code'.toString() == column.value">
          #{item.unifiedCode,jdbcType=VARCHAR}
        </if>
        <if test="'company_type'.toString() == column.value">
          #{item.companyType,jdbcType=VARCHAR}
        </if>
        <if test="'province'.toString() == column.value">
          #{item.province,jdbcType=VARCHAR}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'location'.toString() == column.value">
          #{item.location,jdbcType=VARCHAR}
        </if>
        <if test="'location_id'.toString() == column.value">
          #{item.locationId,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>