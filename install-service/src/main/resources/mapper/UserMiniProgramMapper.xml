<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.install.dao.UserMiniProgramMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.install.pojo.entity.UserMiniProgram">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="number" jdbcType="VARCHAR" property="number" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="region_id" jdbcType="VARCHAR" property="regionId" />
    <result column="region_name" jdbcType="VARCHAR" property="regionName" />
    <result column="role_type" jdbcType="VARCHAR" property="roleType" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="header_img_url" jdbcType="VARCHAR" property="headerImgUrl" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="audit_reason" jdbcType="VARCHAR" property="auditReason" />
    <result column="file_key" jdbcType="VARCHAR" property="fileKey" />
    <result column="audit_header_notice" jdbcType="INTEGER" property="auditHeaderNotice" />
    <result column="sign_status" jdbcType="INTEGER" property="signStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="latest_login_time" jdbcType="TIMESTAMP" property="latestLoginTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, code, number, name, phone, be_id, province_name, location, city_name, 
    region_id, region_name, role_type, status, header_img_url, audit_status, audit_reason, 
    file_key, audit_header_notice, sign_status, create_time, update_time, latest_login_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.install.pojo.entity.UserMiniProgramExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user_mini_program
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_mini_program
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from user_mini_program
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.install.pojo.entity.UserMiniProgramExample">
    delete from user_mini_program
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.install.pojo.entity.UserMiniProgram">
    insert into user_mini_program (id, user_id, code, 
      number, name, phone, 
      be_id, province_name, location, 
      city_name, region_id, region_name, 
      role_type, status, header_img_url, 
      audit_status, audit_reason, file_key, 
      audit_header_notice, sign_status, create_time, 
      update_time, latest_login_time)
    values (#{id,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR}, 
      #{number,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, 
      #{beId,jdbcType=VARCHAR}, #{provinceName,jdbcType=VARCHAR}, #{location,jdbcType=VARCHAR}, 
      #{cityName,jdbcType=VARCHAR}, #{regionId,jdbcType=VARCHAR}, #{regionName,jdbcType=VARCHAR}, 
      #{roleType,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{headerImgUrl,jdbcType=VARCHAR}, 
      #{auditStatus,jdbcType=INTEGER}, #{auditReason,jdbcType=VARCHAR}, #{fileKey,jdbcType=VARCHAR}, 
      #{auditHeaderNotice,jdbcType=INTEGER}, #{signStatus,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{latestLoginTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.install.pojo.entity.UserMiniProgram">
    insert into user_mini_program
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="number != null">
        number,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="regionId != null">
        region_id,
      </if>
      <if test="regionName != null">
        region_name,
      </if>
      <if test="roleType != null">
        role_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="headerImgUrl != null">
        header_img_url,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="auditReason != null">
        audit_reason,
      </if>
      <if test="fileKey != null">
        file_key,
      </if>
      <if test="auditHeaderNotice != null">
        audit_header_notice,
      </if>
      <if test="signStatus != null">
        sign_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="latestLoginTime != null">
        latest_login_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        #{number,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null">
        #{regionId,jdbcType=VARCHAR},
      </if>
      <if test="regionName != null">
        #{regionName,jdbcType=VARCHAR},
      </if>
      <if test="roleType != null">
        #{roleType,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="headerImgUrl != null">
        #{headerImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditReason != null">
        #{auditReason,jdbcType=VARCHAR},
      </if>
      <if test="fileKey != null">
        #{fileKey,jdbcType=VARCHAR},
      </if>
      <if test="auditHeaderNotice != null">
        #{auditHeaderNotice,jdbcType=INTEGER},
      </if>
      <if test="signStatus != null">
        #{signStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="latestLoginTime != null">
        #{latestLoginTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.install.pojo.entity.UserMiniProgramExample" resultType="java.lang.Long">
    select count(*) from user_mini_program
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update user_mini_program
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.number != null">
        number = #{record.number,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceName != null">
        province_name = #{record.provinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.location != null">
        location = #{record.location,jdbcType=VARCHAR},
      </if>
      <if test="record.cityName != null">
        city_name = #{record.cityName,jdbcType=VARCHAR},
      </if>
      <if test="record.regionId != null">
        region_id = #{record.regionId,jdbcType=VARCHAR},
      </if>
      <if test="record.regionName != null">
        region_name = #{record.regionName,jdbcType=VARCHAR},
      </if>
      <if test="record.roleType != null">
        role_type = #{record.roleType,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.headerImgUrl != null">
        header_img_url = #{record.headerImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=INTEGER},
      </if>
      <if test="record.auditReason != null">
        audit_reason = #{record.auditReason,jdbcType=VARCHAR},
      </if>
      <if test="record.fileKey != null">
        file_key = #{record.fileKey,jdbcType=VARCHAR},
      </if>
      <if test="record.auditHeaderNotice != null">
        audit_header_notice = #{record.auditHeaderNotice,jdbcType=INTEGER},
      </if>
      <if test="record.signStatus != null">
        sign_status = #{record.signStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.latestLoginTime != null">
        latest_login_time = #{record.latestLoginTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update user_mini_program
    set id = #{record.id,jdbcType=VARCHAR},
      user_id = #{record.userId,jdbcType=VARCHAR},
      code = #{record.code,jdbcType=VARCHAR},
      number = #{record.number,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      phone = #{record.phone,jdbcType=VARCHAR},
      be_id = #{record.beId,jdbcType=VARCHAR},
      province_name = #{record.provinceName,jdbcType=VARCHAR},
      location = #{record.location,jdbcType=VARCHAR},
      city_name = #{record.cityName,jdbcType=VARCHAR},
      region_id = #{record.regionId,jdbcType=VARCHAR},
      region_name = #{record.regionName,jdbcType=VARCHAR},
      role_type = #{record.roleType,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      header_img_url = #{record.headerImgUrl,jdbcType=VARCHAR},
      audit_status = #{record.auditStatus,jdbcType=INTEGER},
      audit_reason = #{record.auditReason,jdbcType=VARCHAR},
      file_key = #{record.fileKey,jdbcType=VARCHAR},
      audit_header_notice = #{record.auditHeaderNotice,jdbcType=INTEGER},
      sign_status = #{record.signStatus,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      latest_login_time = #{record.latestLoginTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.install.pojo.entity.UserMiniProgram">
    update user_mini_program
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="number != null">
        number = #{number,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="regionId != null">
        region_id = #{regionId,jdbcType=VARCHAR},
      </if>
      <if test="regionName != null">
        region_name = #{regionName,jdbcType=VARCHAR},
      </if>
      <if test="roleType != null">
        role_type = #{roleType,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="headerImgUrl != null">
        header_img_url = #{headerImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditReason != null">
        audit_reason = #{auditReason,jdbcType=VARCHAR},
      </if>
      <if test="fileKey != null">
        file_key = #{fileKey,jdbcType=VARCHAR},
      </if>
      <if test="auditHeaderNotice != null">
        audit_header_notice = #{auditHeaderNotice,jdbcType=INTEGER},
      </if>
      <if test="signStatus != null">
        sign_status = #{signStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="latestLoginTime != null">
        latest_login_time = #{latestLoginTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.install.pojo.entity.UserMiniProgram">
    update user_mini_program
    set user_id = #{userId,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      number = #{number,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      be_id = #{beId,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR},
      location = #{location,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      region_id = #{regionId,jdbcType=VARCHAR},
      region_name = #{regionName,jdbcType=VARCHAR},
      role_type = #{roleType,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      header_img_url = #{headerImgUrl,jdbcType=VARCHAR},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      audit_reason = #{auditReason,jdbcType=VARCHAR},
      file_key = #{fileKey,jdbcType=VARCHAR},
      audit_header_notice = #{auditHeaderNotice,jdbcType=INTEGER},
      sign_status = #{signStatus,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      latest_login_time = #{latestLoginTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    insert into user_mini_program
    (id, user_id, code, number, name, phone, be_id, province_name, location, city_name, 
      region_id, region_name, role_type, status, header_img_url, audit_status, audit_reason, 
      file_key, audit_header_notice, sign_status, create_time, update_time, latest_login_time
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, #{item.code,jdbcType=VARCHAR}, 
        #{item.number,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.phone,jdbcType=VARCHAR}, 
        #{item.beId,jdbcType=VARCHAR}, #{item.provinceName,jdbcType=VARCHAR}, #{item.location,jdbcType=VARCHAR}, 
        #{item.cityName,jdbcType=VARCHAR}, #{item.regionId,jdbcType=VARCHAR}, #{item.regionName,jdbcType=VARCHAR}, 
        #{item.roleType,jdbcType=VARCHAR}, #{item.status,jdbcType=VARCHAR}, #{item.headerImgUrl,jdbcType=VARCHAR}, 
        #{item.auditStatus,jdbcType=INTEGER}, #{item.auditReason,jdbcType=VARCHAR}, #{item.fileKey,jdbcType=VARCHAR}, 
        #{item.auditHeaderNotice,jdbcType=INTEGER}, #{item.signStatus,jdbcType=INTEGER}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.latestLoginTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    insert into user_mini_program (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
        <if test="'code'.toString() == column.value">
          #{item.code,jdbcType=VARCHAR}
        </if>
        <if test="'number'.toString() == column.value">
          #{item.number,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'phone'.toString() == column.value">
          #{item.phone,jdbcType=VARCHAR}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'province_name'.toString() == column.value">
          #{item.provinceName,jdbcType=VARCHAR}
        </if>
        <if test="'location'.toString() == column.value">
          #{item.location,jdbcType=VARCHAR}
        </if>
        <if test="'city_name'.toString() == column.value">
          #{item.cityName,jdbcType=VARCHAR}
        </if>
        <if test="'region_id'.toString() == column.value">
          #{item.regionId,jdbcType=VARCHAR}
        </if>
        <if test="'region_name'.toString() == column.value">
          #{item.regionName,jdbcType=VARCHAR}
        </if>
        <if test="'role_type'.toString() == column.value">
          #{item.roleType,jdbcType=VARCHAR}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=VARCHAR}
        </if>
        <if test="'header_img_url'.toString() == column.value">
          #{item.headerImgUrl,jdbcType=VARCHAR}
        </if>
        <if test="'audit_status'.toString() == column.value">
          #{item.auditStatus,jdbcType=INTEGER}
        </if>
        <if test="'audit_reason'.toString() == column.value">
          #{item.auditReason,jdbcType=VARCHAR}
        </if>
        <if test="'file_key'.toString() == column.value">
          #{item.fileKey,jdbcType=VARCHAR}
        </if>
        <if test="'audit_header_notice'.toString() == column.value">
          #{item.auditHeaderNotice,jdbcType=INTEGER}
        </if>
        <if test="'sign_status'.toString() == column.value">
          #{item.signStatus,jdbcType=INTEGER}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'latest_login_time'.toString() == column.value">
          #{item.latestLoginTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>