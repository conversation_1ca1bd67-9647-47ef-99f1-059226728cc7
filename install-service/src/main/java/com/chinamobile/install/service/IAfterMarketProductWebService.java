package com.chinamobile.install.service;

import com.chinamobile.install.pojo.param.AfterMarketProductConfigPartnerParam;
import com.chinamobile.install.pojo.param.AfterMarketProductExportParam;
import com.chinamobile.install.pojo.param.AfterMarketProductListParam;
import com.chinamobile.install.pojo.vo.AfterMarketProductSimpleInfoVO;
import com.chinamobile.install.pojo.vo.AfterMarketProductVO;
import com.chinamobile.install.pojo.vo.ProvinceInstallPlatformVO;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;

import java.util.List;


public interface IAfterMarketProductWebService {
    BaseAnswer<PageData<AfterMarketProductVO>> getList(AfterMarketProductListParam param);

    BaseAnswer<AfterMarketProductSimpleInfoVO> getSimpleInfo(String aftermarketOfferingCodeId, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<Void> configPartner(AfterMarketProductConfigPartnerParam param, LoginIfo4Redis loginIfo4Redis);

    void productExport(AfterMarketProductExportParam param, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer dealAftermarketStdService();

    BaseAnswer<List<ProvinceInstallPlatformVO>> provinceInstallPlatformList();
}
