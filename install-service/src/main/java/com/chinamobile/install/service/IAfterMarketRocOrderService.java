package com.chinamobile.install.service;

import com.chinamobile.install.pojo.param.AfterMarketOrderQueryParam;
import com.chinamobile.install.pojo.param.AuditRefundRequest;
import com.chinamobile.install.pojo.vo.AfterMarketRocOrderDetailVO;
import com.chinamobile.install.pojo.vo.AfterMarketRocOrderItemVO;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;

/**
 * <AUTHOR>
 * @Date 2022/12/22 16:28
 **/
public interface IAfterMarketRocOrderService {

    /**
     * 售后订单退换货列表
     * @param param
     * @param loginIfo4Redis
     * @return
     */
    BaseAnswer<PageData<AfterMarketRocOrderItemVO>> getAfterMarketRocOrderList(AfterMarketOrderQueryParam param, LoginIfo4Redis loginIfo4Redis);

    /**
     *
     * @param serviceRocOrderId
     * @param loginIfo4Redis
     * @return
     */
    BaseAnswer<AfterMarketRocOrderDetailVO> getAfterMarketRocOrderDetail(String serviceRocOrderId, LoginIfo4Redis loginIfo4Redis);

    /**
     * 售后服务订单退单审批
     * @param request
     * @param userId
     * @return
     */
    BaseAnswer<Void> refundAudit(AuditRefundRequest request, String userId, String ip);

}
