package com.chinamobile.install.schedule;

import com.chinamobile.install.dao.AfterMarketOrder2cInfoMapper;
import com.chinamobile.install.dao.AfterMarketOrder2cOfferingInfoMapper;
import com.chinamobile.install.dao.Order2cInfoMapper;
import com.chinamobile.install.dao.UserMiniProgramMapper;
import com.chinamobile.install.enums.AfterMarketOrderStatusEnum;
import com.chinamobile.install.pojo.entity.*;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.redis.RedisUtil;
import com.chinamobile.iot.sc.entity.Msg4Request;
import com.chinamobile.iot.sc.feign.SmsFeignClient;
import com.chinamobile.iot.sc.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.SUCCESS;

/**
 * created by wangqiqi on 2025/6/5 15:31
 * 每天发送预约提醒短信
 */
@Component
@EnableScheduling
@Slf4j
public class NoReservationSendMsgTask {

    private String lockKey = "NoReservationSendMsgTask";
    @Resource
    private UserMiniProgramMapper userMiniProgramMapper;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private AfterMarketOrder2cInfoMapper afterMarketOrder2cInfoMapper;

    @Resource
    private AfterMarketOrder2cOfferingInfoMapper afterMarketOrder2cOfferingInfoMapper;

    @Resource
    private SmsFeignClient smsFeignClient;

    /**
     * 预约提醒短信模板ID
     */
    @Value("${sms.AfterMarketOrderReservationReminderTemplateId:108551}")
    private String afterMarketOrderReservationReminderTemplateId;
    @Autowired
    private Order2cInfoMapper order2cInfoMapper;
    @Value("${install.isTest}")
    private String isTest;
    //每天早上9点30分执行
//    @Scheduled(cron = "0 */10 * * * ?")
    @Scheduled(cron = "0 30 9 * * ?")
    public void work() {
        Boolean getLock = null;
        try {
            getLock = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1", 10, TimeUnit.MINUTES);
            log.info("NoReservationSendMsgTask获取锁结果:{}", getLock);
            if (getLock) {
                sendReservationReminderSms();
            }
        } catch (Exception e) {
            log.error("NoReservationSendMsgTask发生异常", e);
        } finally {
            if (getLock != null && getLock) {
                stringRedisTemplate.delete(lockKey);
                log.info("NoReservationSendMsgTask释放锁");
            }
        }
    }

    /**
     * 发送预约提醒短信
     */
    private void sendReservationReminderSms() {
        log.info("开始执行预约提醒短信发送任务");

        try {
            // 获取今天0点的时间
            Date today = new Date();
            Date todayBegin = DateTimeUtil.getDayBeginDate(today);

            log.info("查询创建时间小于今天0点的待预约订单，截止时间：{}",
                DateTimeUtil.formatDate(todayBegin, DateTimeUtil.DEFAULT_DATE_DEFAULT));

            // 查询状态为1（待预约）且创建时间小于今天0点的订单
            AfterMarketOrder2cInfoExample example = new AfterMarketOrder2cInfoExample();
            AfterMarketOrder2cInfoExample.Criteria criteria = example.createCriteria();
            criteria.andStatusEqualTo(AfterMarketOrderStatusEnum.TO_APPOINTMENT.getStatus()); // 状态为1：待预约
            criteria.andCreateTimeLessThan(todayBegin); // 创建时间小于今天0点

            List<AfterMarketOrder2cInfo> orderList = afterMarketOrder2cInfoMapper.selectByExample(example);

            if (CollectionUtils.isEmpty(orderList)) {
                log.info("没有找到创建时间小于今天0点的待预约订单，任务结束");
                return;
            }

            log.info("找到{}条创建时间小于今天0点的待预约订单，开始发送短信提醒", orderList.size());

            int successCount = 0;
            int failCount = 0;

            for (AfterMarketOrder2cInfo order : orderList) {
                try {
                    if (sendSmsToCustomer(order)) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    log.error("发送短信失败，订单ID：{}，异常：", order.getServiceOrderId(), e);
                    failCount++;
                }
            }

            log.info("预约提醒短信发送完成，成功：{}条，失败：{}条", successCount, failCount);

        } catch (Exception e) {
            log.error("执行预约提醒短信发送任务异常", e);
        }
    }

    /**
     * 向客户发送预约提醒短信
     * @param order 订单信息
     * @return 发送是否成功
     */
    private boolean sendSmsToCustomer(AfterMarketOrder2cInfo order) {
        String phone = "";
        Order2cInfo info = order2cInfoMapper.selectByPrimaryKey(order.getOfferingOrderId());
        if (info == null) {
            log.warn("订单{}对应的预约订单{}不存在，跳过发送短信", order.getServiceOrderId(), order.getOfferingOrderId());
            return false;
        }
        if(info.getBillLadderType()!=null){
            phone = info.getCustMgPhone();
        }else if(info.getBillLadderType()==null){
            if(info.getCustomerType().equals("0")){
                List<UserMiniProgram> userMiniPrograms = userMiniProgramMapper.selectByExample(new UserMiniProgramExample().createCriteria().andCodeEqualTo(info.getCustCode()).example());
                if(CollectionUtils.isEmpty(userMiniPrograms)){
                    log.warn("订单{}的预约手机号为空，跳过发送短信", order.getServiceOrderId());
                    return false;
                }else{
                    phone = userMiniPrograms.get(0).getPhone();
                }
            }else{
                phone =info.getContactPhone();
            }

        }
        if(phone==""){
            log.warn("订单{}的预约手机号为空，跳过发送短信", order.getServiceOrderId());
            return false;
        }
        try {
            // 构建短信请求
            Msg4Request smsRequest = new Msg4Request();
            List<String> mobiles = new ArrayList<>();
            mobiles.add(phone);
            smsRequest.setMobiles(mobiles);
            smsRequest.setTemplateId(afterMarketOrderReservationReminderTemplateId);

            // 构建短信参数
            Map<String, String> messageParams = new HashMap<>();

            // 查询售后服务包商品名称
            AfterMarketOrder2cOfferingInfoExample example = new AfterMarketOrder2cOfferingInfoExample();
            AfterMarketOrder2cOfferingInfoExample.Criteria criteria = example.createCriteria();
            criteria.andServiceOrderIdEqualTo(order.getServiceOrderId());

            List<AfterMarketOrder2cOfferingInfo> offeringInfoList = afterMarketOrder2cOfferingInfoMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(offeringInfoList)){
                log.warn("订单{}的售后服务包商品名称为空，跳过发送短信", order.getServiceOrderId());
                return false;
            }
            String productName = offeringInfoList.get(0).getAfterMarketName();
            messageParams.put("productName", productName);
            smsRequest.setMessage(messageParams);

            // 发送短信
            if(isTest.equals("true")){
                log.info("预约提醒短信发送成功，订单ID：{}，手机号：{}", order.getServiceOrderId(), phone);
                return true;
            }else{
                BaseAnswer<Void> result = smsFeignClient.asySendMessage(smsRequest);
                if (!SUCCESS.getStateCode().equals(result.getStateCode())) {
                    log.warn("预约提醒短信发送失败，订单ID：{}，手机号：{}，返回结果：{}",
                            order.getServiceOrderId(), phone, result != null ? result.getMessage() : "null");
                    return false;
                }else{
                    log.info("预约提醒短信发送成功，订单ID：{}，手机号：{}", order.getServiceOrderId(), phone);
                    return true;
                }
            }



        } catch (Exception e) {
            log.error("发送预约提醒短信异常，订单ID：{}，手机号：{}，异常：",
                order.getServiceOrderId(), phone, e);
            return false;
        }
    }


}
