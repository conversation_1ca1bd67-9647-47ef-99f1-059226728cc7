package com.chinamobile.install.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 用户表
 *
 * <AUTHOR>
public class User implements Serializable {
    /**
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private String userId;

    /**
     * 姓名
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private String name;

    /**
     * 密码
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private String pwd;

    /**
     * 电话
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private String phone;

    /**
     * 备注
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private String remark;

    /**
     * 是否是超级管理员
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private Boolean isAdmin;

    /**
     * 角色id
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private String roleId;

    /**
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private Date createTime;

    /**
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private Date updateTime;

    /**
     * 是否无效
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private Boolean isCancel;

    /**
     * 是否注销
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private Boolean isLogoff;

    /**
     * 邮箱
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private String email;

    /**
     * 公司
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private String company;

    /**
     * 合作伙伴名
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private String partnerName;

    /**
     * 创建人
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private String creator;

    /**
     * 是否是主合作伙伴
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private Boolean isPrimary;

    /**
     * 是否发送短信
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private Boolean isSend;

    /**
     * 用户账号
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private String account;

    /**
     * 部门id
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private String departmentId;

    /**
     * 部门名称
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private String departmentName;

    /**
     * 旧工号
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private String oldJobNumber;

    /**
     * 新工号
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private String newJobNumber;

    /**
     * 自己编的固定值，用于关联4A视图
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private String orgId;

    /**
     * 账号来源（用户对象），iot-物联网公司（含外协），other-非物联网公司
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private String userFrom;

    /**
     * 账号类型，normal-正式账号，test-测试账号
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private String userType;

    /**
     * 用户种类，用于物联网公司用户区分内部用户跟外协，1 内部用户 2 外部用户
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private Integer iotType;

    /**
     * 统一用户平台用户状态，0：正常，1：锁定（账号不可用，但可恢复），2：未启用（账号信息不完整 不可用），3：注销（此账号从此不可恢复）
     *
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private Integer unifiedStatus;

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..user.user_id
     *
     * @return the value of supply_chain..user.user_id
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withUserId(String userId) {
        this.setUserId(userId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.user_id
     *
     * @param userId the value for supply_chain..user.user_id
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * This method returns the value of the database column supply_chain..user.name
     *
     * @return the value of supply_chain..user.name
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.name
     *
     * @param name the value for supply_chain..user.name
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method returns the value of the database column supply_chain..user.pwd
     *
     * @return the value of supply_chain..user.pwd
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public String getPwd() {
        return pwd;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withPwd(String pwd) {
        this.setPwd(pwd);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.pwd
     *
     * @param pwd the value for supply_chain..user.pwd
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    /**
     * This method returns the value of the database column supply_chain..user.phone
     *
     * @return the value of supply_chain..user.phone
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public String getPhone() {
        return phone;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withPhone(String phone) {
        this.setPhone(phone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.phone
     *
     * @param phone the value for supply_chain..user.phone
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * This method returns the value of the database column supply_chain..user.remark
     *
     * @return the value of supply_chain..user.remark
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public String getRemark() {
        return remark;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withRemark(String remark) {
        this.setRemark(remark);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.remark
     *
     * @param remark the value for supply_chain..user.remark
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * This method returns the value of the database column supply_chain..user.is_admin
     *
     * @return the value of supply_chain..user.is_admin
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public Boolean getIsAdmin() {
        return isAdmin;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withIsAdmin(Boolean isAdmin) {
        this.setIsAdmin(isAdmin);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.is_admin
     *
     * @param isAdmin the value for supply_chain..user.is_admin
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setIsAdmin(Boolean isAdmin) {
        this.isAdmin = isAdmin;
    }

    /**
     * This method returns the value of the database column supply_chain..user.role_id
     *
     * @return the value of supply_chain..user.role_id
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public String getRoleId() {
        return roleId;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withRoleId(String roleId) {
        this.setRoleId(roleId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.role_id
     *
     * @param roleId the value for supply_chain..user.role_id
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    /**
     * This method returns the value of the database column supply_chain..user.create_time
     *
     * @return the value of supply_chain..user.create_time
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.create_time
     *
     * @param createTime the value for supply_chain..user.create_time
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..user.update_time
     *
     * @return the value of supply_chain..user.update_time
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.update_time
     *
     * @param updateTime the value for supply_chain..user.update_time
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..user.is_cancel
     *
     * @return the value of supply_chain..user.is_cancel
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public Boolean getIsCancel() {
        return isCancel;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withIsCancel(Boolean isCancel) {
        this.setIsCancel(isCancel);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.is_cancel
     *
     * @param isCancel the value for supply_chain..user.is_cancel
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setIsCancel(Boolean isCancel) {
        this.isCancel = isCancel;
    }

    /**
     * This method returns the value of the database column supply_chain..user.is_logoff
     *
     * @return the value of supply_chain..user.is_logoff
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public Boolean getIsLogoff() {
        return isLogoff;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withIsLogoff(Boolean isLogoff) {
        this.setIsLogoff(isLogoff);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.is_logoff
     *
     * @param isLogoff the value for supply_chain..user.is_logoff
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setIsLogoff(Boolean isLogoff) {
        this.isLogoff = isLogoff;
    }

    /**
     * This method returns the value of the database column supply_chain..user.email
     *
     * @return the value of supply_chain..user.email
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public String getEmail() {
        return email;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withEmail(String email) {
        this.setEmail(email);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.email
     *
     * @param email the value for supply_chain..user.email
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * This method returns the value of the database column supply_chain..user.company
     *
     * @return the value of supply_chain..user.company
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public String getCompany() {
        return company;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withCompany(String company) {
        this.setCompany(company);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.company
     *
     * @param company the value for supply_chain..user.company
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setCompany(String company) {
        this.company = company;
    }

    /**
     * This method returns the value of the database column supply_chain..user.partner_name
     *
     * @return the value of supply_chain..user.partner_name
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public String getPartnerName() {
        return partnerName;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withPartnerName(String partnerName) {
        this.setPartnerName(partnerName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.partner_name
     *
     * @param partnerName the value for supply_chain..user.partner_name
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    /**
     * This method returns the value of the database column supply_chain..user.creator
     *
     * @return the value of supply_chain..user.creator
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public String getCreator() {
        return creator;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withCreator(String creator) {
        this.setCreator(creator);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.creator
     *
     * @param creator the value for supply_chain..user.creator
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setCreator(String creator) {
        this.creator = creator;
    }

    /**
     * This method returns the value of the database column supply_chain..user.is_primary
     *
     * @return the value of supply_chain..user.is_primary
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public Boolean getIsPrimary() {
        return isPrimary;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withIsPrimary(Boolean isPrimary) {
        this.setIsPrimary(isPrimary);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.is_primary
     *
     * @param isPrimary the value for supply_chain..user.is_primary
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setIsPrimary(Boolean isPrimary) {
        this.isPrimary = isPrimary;
    }

    /**
     * This method returns the value of the database column supply_chain..user.is_send
     *
     * @return the value of supply_chain..user.is_send
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public Boolean getIsSend() {
        return isSend;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withIsSend(Boolean isSend) {
        this.setIsSend(isSend);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.is_send
     *
     * @param isSend the value for supply_chain..user.is_send
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setIsSend(Boolean isSend) {
        this.isSend = isSend;
    }

    /**
     * This method returns the value of the database column supply_chain..user.account
     *
     * @return the value of supply_chain..user.account
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public String getAccount() {
        return account;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withAccount(String account) {
        this.setAccount(account);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.account
     *
     * @param account the value for supply_chain..user.account
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setAccount(String account) {
        this.account = account;
    }

    /**
     * This method returns the value of the database column supply_chain..user.department_id
     *
     * @return the value of supply_chain..user.department_id
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public String getDepartmentId() {
        return departmentId;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withDepartmentId(String departmentId) {
        this.setDepartmentId(departmentId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.department_id
     *
     * @param departmentId the value for supply_chain..user.department_id
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId;
    }

    /**
     * This method returns the value of the database column supply_chain..user.department_name
     *
     * @return the value of supply_chain..user.department_name
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public String getDepartmentName() {
        return departmentName;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withDepartmentName(String departmentName) {
        this.setDepartmentName(departmentName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.department_name
     *
     * @param departmentName the value for supply_chain..user.department_name
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    /**
     * This method returns the value of the database column supply_chain..user.old_job_number
     *
     * @return the value of supply_chain..user.old_job_number
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public String getOldJobNumber() {
        return oldJobNumber;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withOldJobNumber(String oldJobNumber) {
        this.setOldJobNumber(oldJobNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.old_job_number
     *
     * @param oldJobNumber the value for supply_chain..user.old_job_number
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setOldJobNumber(String oldJobNumber) {
        this.oldJobNumber = oldJobNumber;
    }

    /**
     * This method returns the value of the database column supply_chain..user.new_job_number
     *
     * @return the value of supply_chain..user.new_job_number
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public String getNewJobNumber() {
        return newJobNumber;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withNewJobNumber(String newJobNumber) {
        this.setNewJobNumber(newJobNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.new_job_number
     *
     * @param newJobNumber the value for supply_chain..user.new_job_number
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setNewJobNumber(String newJobNumber) {
        this.newJobNumber = newJobNumber;
    }

    /**
     * This method returns the value of the database column supply_chain..user.org_id
     *
     * @return the value of supply_chain..user.org_id
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public String getOrgId() {
        return orgId;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withOrgId(String orgId) {
        this.setOrgId(orgId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.org_id
     *
     * @param orgId the value for supply_chain..user.org_id
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    /**
     * This method returns the value of the database column supply_chain..user.user_from
     *
     * @return the value of supply_chain..user.user_from
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public String getUserFrom() {
        return userFrom;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withUserFrom(String userFrom) {
        this.setUserFrom(userFrom);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.user_from
     *
     * @param userFrom the value for supply_chain..user.user_from
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setUserFrom(String userFrom) {
        this.userFrom = userFrom;
    }

    /**
     * This method returns the value of the database column supply_chain..user.user_type
     *
     * @return the value of supply_chain..user.user_type
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public String getUserType() {
        return userType;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withUserType(String userType) {
        this.setUserType(userType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.user_type
     *
     * @param userType the value for supply_chain..user.user_type
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setUserType(String userType) {
        this.userType = userType;
    }

    /**
     * This method returns the value of the database column supply_chain..user.iot_type
     *
     * @return the value of supply_chain..user.iot_type
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public Integer getIotType() {
        return iotType;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withIotType(Integer iotType) {
        this.setIotType(iotType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.iot_type
     *
     * @param iotType the value for supply_chain..user.iot_type
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setIotType(Integer iotType) {
        this.iotType = iotType;
    }

    /**
     * This method returns the value of the database column supply_chain..user.unified_status
     *
     * @return the value of supply_chain..user.unified_status
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public Integer getUnifiedStatus() {
        return unifiedStatus;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public User withUnifiedStatus(Integer unifiedStatus) {
        this.setUnifiedStatus(unifiedStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user.unified_status
     *
     * @param unifiedStatus the value for supply_chain..user.unified_status
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public void setUnifiedStatus(Integer unifiedStatus) {
        this.unifiedStatus = unifiedStatus;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", userId=").append(userId);
        sb.append(", name=").append(name);
        sb.append(", pwd=").append(pwd);
        sb.append(", phone=").append(phone);
        sb.append(", remark=").append(remark);
        sb.append(", isAdmin=").append(isAdmin);
        sb.append(", roleId=").append(roleId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", isCancel=").append(isCancel);
        sb.append(", isLogoff=").append(isLogoff);
        sb.append(", email=").append(email);
        sb.append(", company=").append(company);
        sb.append(", partnerName=").append(partnerName);
        sb.append(", creator=").append(creator);
        sb.append(", isPrimary=").append(isPrimary);
        sb.append(", isSend=").append(isSend);
        sb.append(", account=").append(account);
        sb.append(", departmentId=").append(departmentId);
        sb.append(", departmentName=").append(departmentName);
        sb.append(", oldJobNumber=").append(oldJobNumber);
        sb.append(", newJobNumber=").append(newJobNumber);
        sb.append(", orgId=").append(orgId);
        sb.append(", userFrom=").append(userFrom);
        sb.append(", userType=").append(userType);
        sb.append(", iotType=").append(iotType);
        sb.append(", unifiedStatus=").append(unifiedStatus);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        User other = (User) that;
        return (this.getUserId() == null ? other.getUserId() == null : this.getUserId().equals(other.getUserId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getPwd() == null ? other.getPwd() == null : this.getPwd().equals(other.getPwd()))
            && (this.getPhone() == null ? other.getPhone() == null : this.getPhone().equals(other.getPhone()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getIsAdmin() == null ? other.getIsAdmin() == null : this.getIsAdmin().equals(other.getIsAdmin()))
            && (this.getRoleId() == null ? other.getRoleId() == null : this.getRoleId().equals(other.getRoleId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getIsCancel() == null ? other.getIsCancel() == null : this.getIsCancel().equals(other.getIsCancel()))
            && (this.getIsLogoff() == null ? other.getIsLogoff() == null : this.getIsLogoff().equals(other.getIsLogoff()))
            && (this.getEmail() == null ? other.getEmail() == null : this.getEmail().equals(other.getEmail()))
            && (this.getCompany() == null ? other.getCompany() == null : this.getCompany().equals(other.getCompany()))
            && (this.getPartnerName() == null ? other.getPartnerName() == null : this.getPartnerName().equals(other.getPartnerName()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getIsPrimary() == null ? other.getIsPrimary() == null : this.getIsPrimary().equals(other.getIsPrimary()))
            && (this.getIsSend() == null ? other.getIsSend() == null : this.getIsSend().equals(other.getIsSend()))
            && (this.getAccount() == null ? other.getAccount() == null : this.getAccount().equals(other.getAccount()))
            && (this.getDepartmentId() == null ? other.getDepartmentId() == null : this.getDepartmentId().equals(other.getDepartmentId()))
            && (this.getDepartmentName() == null ? other.getDepartmentName() == null : this.getDepartmentName().equals(other.getDepartmentName()))
            && (this.getOldJobNumber() == null ? other.getOldJobNumber() == null : this.getOldJobNumber().equals(other.getOldJobNumber()))
            && (this.getNewJobNumber() == null ? other.getNewJobNumber() == null : this.getNewJobNumber().equals(other.getNewJobNumber()))
            && (this.getOrgId() == null ? other.getOrgId() == null : this.getOrgId().equals(other.getOrgId()))
            && (this.getUserFrom() == null ? other.getUserFrom() == null : this.getUserFrom().equals(other.getUserFrom()))
            && (this.getUserType() == null ? other.getUserType() == null : this.getUserType().equals(other.getUserType()))
            && (this.getIotType() == null ? other.getIotType() == null : this.getIotType().equals(other.getIotType()))
            && (this.getUnifiedStatus() == null ? other.getUnifiedStatus() == null : this.getUnifiedStatus().equals(other.getUnifiedStatus()));
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getUserId() == null) ? 0 : getUserId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getPwd() == null) ? 0 : getPwd().hashCode());
        result = prime * result + ((getPhone() == null) ? 0 : getPhone().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getIsAdmin() == null) ? 0 : getIsAdmin().hashCode());
        result = prime * result + ((getRoleId() == null) ? 0 : getRoleId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getIsCancel() == null) ? 0 : getIsCancel().hashCode());
        result = prime * result + ((getIsLogoff() == null) ? 0 : getIsLogoff().hashCode());
        result = prime * result + ((getEmail() == null) ? 0 : getEmail().hashCode());
        result = prime * result + ((getCompany() == null) ? 0 : getCompany().hashCode());
        result = prime * result + ((getPartnerName() == null) ? 0 : getPartnerName().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getIsPrimary() == null) ? 0 : getIsPrimary().hashCode());
        result = prime * result + ((getIsSend() == null) ? 0 : getIsSend().hashCode());
        result = prime * result + ((getAccount() == null) ? 0 : getAccount().hashCode());
        result = prime * result + ((getDepartmentId() == null) ? 0 : getDepartmentId().hashCode());
        result = prime * result + ((getDepartmentName() == null) ? 0 : getDepartmentName().hashCode());
        result = prime * result + ((getOldJobNumber() == null) ? 0 : getOldJobNumber().hashCode());
        result = prime * result + ((getNewJobNumber() == null) ? 0 : getNewJobNumber().hashCode());
        result = prime * result + ((getOrgId() == null) ? 0 : getOrgId().hashCode());
        result = prime * result + ((getUserFrom() == null) ? 0 : getUserFrom().hashCode());
        result = prime * result + ((getUserType() == null) ? 0 : getUserType().hashCode());
        result = prime * result + ((getIotType() == null) ? 0 : getIotType().hashCode());
        result = prime * result + ((getUnifiedStatus() == null) ? 0 : getUnifiedStatus().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:36:37 CST 2025
     */
    public enum Column {
        userId("user_id", "userId", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        pwd("pwd", "pwd", "VARCHAR", false),
        phone("phone", "phone", "VARCHAR", false),
        remark("remark", "remark", "VARCHAR", false),
        isAdmin("is_admin", "isAdmin", "BIT", false),
        roleId("role_id", "roleId", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        isCancel("is_cancel", "isCancel", "BIT", false),
        isLogoff("is_logoff", "isLogoff", "BIT", false),
        email("email", "email", "VARCHAR", false),
        company("company", "company", "VARCHAR", false),
        partnerName("partner_name", "partnerName", "VARCHAR", false),
        creator("creator", "creator", "VARCHAR", false),
        isPrimary("is_primary", "isPrimary", "BIT", false),
        isSend("is_send", "isSend", "BIT", false),
        account("account", "account", "VARCHAR", false),
        departmentId("department_id", "departmentId", "VARCHAR", false),
        departmentName("department_name", "departmentName", "VARCHAR", false),
        oldJobNumber("old_job_number", "oldJobNumber", "VARCHAR", false),
        newJobNumber("new_job_number", "newJobNumber", "VARCHAR", false),
        orgId("org_id", "orgId", "VARCHAR", false),
        userFrom("user_from", "userFrom", "VARCHAR", false),
        userType("user_type", "userType", "VARCHAR", false),
        iotType("iot_type", "iotType", "INTEGER", false),
        unifiedStatus("unified_status", "unifiedStatus", "INTEGER", false);

        /**
         *
         * @mbg.generated Mon May 26 16:36:37 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon May 26 16:36:37 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon May 26 16:36:37 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon May 26 16:36:37 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon May 26 16:36:37 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon May 26 16:36:37 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon May 26 16:36:37 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon May 26 16:36:37 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon May 26 16:36:37 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon May 26 16:36:37 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon May 26 16:36:37 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon May 26 16:36:37 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon May 26 16:36:37 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon May 26 16:36:37 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon May 26 16:36:37 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon May 26 16:36:37 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon May 26 16:36:37 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}