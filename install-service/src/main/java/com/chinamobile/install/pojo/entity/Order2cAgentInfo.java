package com.chinamobile.install.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 订单渠道商信息表
 *
 * <AUTHOR>
public class Order2cAgentInfo implements Serializable {
    /**
     *
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    private String id;

    /**
     * 订单编号
     *
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    private String orderId;

    /**
     * 渠道商全称
     *
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    private String agentName;

    /**
     * 渠道商全称清洗
     *
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    private String agentNameWash;

    /**
     * 渠道商编号
     *
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    private String agentNumber;

    /**
     * 渠道商编号清洗
     *
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    private String agentNumberWash;

    /**
     * 渠道商电话
     *
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    private String agentPhone;

    /**
     * 与渠道商手机号（agentPhone）搭配使用，传渠道商的UserID信息，省侧渠道商不传
     *
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    private String agentUserId;

    /**
     * 渠道商标签清洗
     *
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    private String agentLabelWash;

    /**
     * 渠道商类别清洗
     *
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    private String agentCategoryWash;

    /**
     * 是否清洗标识  0未清洗，1已清洗
     *
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    private String isWash;

    /**
     * 清洗操作人
     *
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    private String operatorWash;

    /**
     *
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    private Date createTime;

    /**
     *
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..order_2c_agent_info.id
     *
     * @return the value of supply_chain..order_2c_agent_info.id
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public Order2cAgentInfo withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_agent_info.id
     *
     * @param id the value for supply_chain..order_2c_agent_info.id
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_agent_info.order_id
     *
     * @return the value of supply_chain..order_2c_agent_info.order_id
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public Order2cAgentInfo withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_agent_info.order_id
     *
     * @param orderId the value for supply_chain..order_2c_agent_info.order_id
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_agent_info.agent_name
     *
     * @return the value of supply_chain..order_2c_agent_info.agent_name
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public String getAgentName() {
        return agentName;
    }

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public Order2cAgentInfo withAgentName(String agentName) {
        this.setAgentName(agentName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_agent_info.agent_name
     *
     * @param agentName the value for supply_chain..order_2c_agent_info.agent_name
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_agent_info.agent_name_wash
     *
     * @return the value of supply_chain..order_2c_agent_info.agent_name_wash
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public String getAgentNameWash() {
        return agentNameWash;
    }

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public Order2cAgentInfo withAgentNameWash(String agentNameWash) {
        this.setAgentNameWash(agentNameWash);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_agent_info.agent_name_wash
     *
     * @param agentNameWash the value for supply_chain..order_2c_agent_info.agent_name_wash
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public void setAgentNameWash(String agentNameWash) {
        this.agentNameWash = agentNameWash;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_agent_info.agent_number
     *
     * @return the value of supply_chain..order_2c_agent_info.agent_number
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public String getAgentNumber() {
        return agentNumber;
    }

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public Order2cAgentInfo withAgentNumber(String agentNumber) {
        this.setAgentNumber(agentNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_agent_info.agent_number
     *
     * @param agentNumber the value for supply_chain..order_2c_agent_info.agent_number
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public void setAgentNumber(String agentNumber) {
        this.agentNumber = agentNumber;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_agent_info.agent_number_wash
     *
     * @return the value of supply_chain..order_2c_agent_info.agent_number_wash
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public String getAgentNumberWash() {
        return agentNumberWash;
    }

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public Order2cAgentInfo withAgentNumberWash(String agentNumberWash) {
        this.setAgentNumberWash(agentNumberWash);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_agent_info.agent_number_wash
     *
     * @param agentNumberWash the value for supply_chain..order_2c_agent_info.agent_number_wash
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public void setAgentNumberWash(String agentNumberWash) {
        this.agentNumberWash = agentNumberWash;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_agent_info.agent_phone
     *
     * @return the value of supply_chain..order_2c_agent_info.agent_phone
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public String getAgentPhone() {
        return agentPhone;
    }

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public Order2cAgentInfo withAgentPhone(String agentPhone) {
        this.setAgentPhone(agentPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_agent_info.agent_phone
     *
     * @param agentPhone the value for supply_chain..order_2c_agent_info.agent_phone
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public void setAgentPhone(String agentPhone) {
        this.agentPhone = agentPhone;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_agent_info.agent_user_id
     *
     * @return the value of supply_chain..order_2c_agent_info.agent_user_id
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public String getAgentUserId() {
        return agentUserId;
    }

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public Order2cAgentInfo withAgentUserId(String agentUserId) {
        this.setAgentUserId(agentUserId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_agent_info.agent_user_id
     *
     * @param agentUserId the value for supply_chain..order_2c_agent_info.agent_user_id
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public void setAgentUserId(String agentUserId) {
        this.agentUserId = agentUserId;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_agent_info.agent_label_wash
     *
     * @return the value of supply_chain..order_2c_agent_info.agent_label_wash
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public String getAgentLabelWash() {
        return agentLabelWash;
    }

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public Order2cAgentInfo withAgentLabelWash(String agentLabelWash) {
        this.setAgentLabelWash(agentLabelWash);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_agent_info.agent_label_wash
     *
     * @param agentLabelWash the value for supply_chain..order_2c_agent_info.agent_label_wash
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public void setAgentLabelWash(String agentLabelWash) {
        this.agentLabelWash = agentLabelWash;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_agent_info.agent_category_wash
     *
     * @return the value of supply_chain..order_2c_agent_info.agent_category_wash
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public String getAgentCategoryWash() {
        return agentCategoryWash;
    }

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public Order2cAgentInfo withAgentCategoryWash(String agentCategoryWash) {
        this.setAgentCategoryWash(agentCategoryWash);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_agent_info.agent_category_wash
     *
     * @param agentCategoryWash the value for supply_chain..order_2c_agent_info.agent_category_wash
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public void setAgentCategoryWash(String agentCategoryWash) {
        this.agentCategoryWash = agentCategoryWash;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_agent_info.is_wash
     *
     * @return the value of supply_chain..order_2c_agent_info.is_wash
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public String getIsWash() {
        return isWash;
    }

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public Order2cAgentInfo withIsWash(String isWash) {
        this.setIsWash(isWash);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_agent_info.is_wash
     *
     * @param isWash the value for supply_chain..order_2c_agent_info.is_wash
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public void setIsWash(String isWash) {
        this.isWash = isWash;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_agent_info.operator_wash
     *
     * @return the value of supply_chain..order_2c_agent_info.operator_wash
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public String getOperatorWash() {
        return operatorWash;
    }

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public Order2cAgentInfo withOperatorWash(String operatorWash) {
        this.setOperatorWash(operatorWash);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_agent_info.operator_wash
     *
     * @param operatorWash the value for supply_chain..order_2c_agent_info.operator_wash
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public void setOperatorWash(String operatorWash) {
        this.operatorWash = operatorWash;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_agent_info.create_time
     *
     * @return the value of supply_chain..order_2c_agent_info.create_time
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public Order2cAgentInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_agent_info.create_time
     *
     * @param createTime the value for supply_chain..order_2c_agent_info.create_time
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..order_2c_agent_info.update_time
     *
     * @return the value of supply_chain..order_2c_agent_info.update_time
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public Order2cAgentInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_2c_agent_info.update_time
     *
     * @param updateTime the value for supply_chain..order_2c_agent_info.update_time
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderId=").append(orderId);
        sb.append(", agentName=").append(agentName);
        sb.append(", agentNameWash=").append(agentNameWash);
        sb.append(", agentNumber=").append(agentNumber);
        sb.append(", agentNumberWash=").append(agentNumberWash);
        sb.append(", agentPhone=").append(agentPhone);
        sb.append(", agentUserId=").append(agentUserId);
        sb.append(", agentLabelWash=").append(agentLabelWash);
        sb.append(", agentCategoryWash=").append(agentCategoryWash);
        sb.append(", isWash=").append(isWash);
        sb.append(", operatorWash=").append(operatorWash);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Order2cAgentInfo other = (Order2cAgentInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getAgentName() == null ? other.getAgentName() == null : this.getAgentName().equals(other.getAgentName()))
            && (this.getAgentNameWash() == null ? other.getAgentNameWash() == null : this.getAgentNameWash().equals(other.getAgentNameWash()))
            && (this.getAgentNumber() == null ? other.getAgentNumber() == null : this.getAgentNumber().equals(other.getAgentNumber()))
            && (this.getAgentNumberWash() == null ? other.getAgentNumberWash() == null : this.getAgentNumberWash().equals(other.getAgentNumberWash()))
            && (this.getAgentPhone() == null ? other.getAgentPhone() == null : this.getAgentPhone().equals(other.getAgentPhone()))
            && (this.getAgentUserId() == null ? other.getAgentUserId() == null : this.getAgentUserId().equals(other.getAgentUserId()))
            && (this.getAgentLabelWash() == null ? other.getAgentLabelWash() == null : this.getAgentLabelWash().equals(other.getAgentLabelWash()))
            && (this.getAgentCategoryWash() == null ? other.getAgentCategoryWash() == null : this.getAgentCategoryWash().equals(other.getAgentCategoryWash()))
            && (this.getIsWash() == null ? other.getIsWash() == null : this.getIsWash().equals(other.getIsWash()))
            && (this.getOperatorWash() == null ? other.getOperatorWash() == null : this.getOperatorWash().equals(other.getOperatorWash()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getAgentName() == null) ? 0 : getAgentName().hashCode());
        result = prime * result + ((getAgentNameWash() == null) ? 0 : getAgentNameWash().hashCode());
        result = prime * result + ((getAgentNumber() == null) ? 0 : getAgentNumber().hashCode());
        result = prime * result + ((getAgentNumberWash() == null) ? 0 : getAgentNumberWash().hashCode());
        result = prime * result + ((getAgentPhone() == null) ? 0 : getAgentPhone().hashCode());
        result = prime * result + ((getAgentUserId() == null) ? 0 : getAgentUserId().hashCode());
        result = prime * result + ((getAgentLabelWash() == null) ? 0 : getAgentLabelWash().hashCode());
        result = prime * result + ((getAgentCategoryWash() == null) ? 0 : getAgentCategoryWash().hashCode());
        result = prime * result + ((getIsWash() == null) ? 0 : getIsWash().hashCode());
        result = prime * result + ((getOperatorWash() == null) ? 0 : getOperatorWash().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed May 28 09:51:52 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        agentName("agent_name", "agentName", "VARCHAR", false),
        agentNameWash("agent_name_wash", "agentNameWash", "VARCHAR", false),
        agentNumber("agent_number", "agentNumber", "VARCHAR", false),
        agentNumberWash("agent_number_wash", "agentNumberWash", "VARCHAR", false),
        agentPhone("agent_phone", "agentPhone", "VARCHAR", false),
        agentUserId("agent_user_id", "agentUserId", "VARCHAR", false),
        agentLabelWash("agent_label_wash", "agentLabelWash", "VARCHAR", false),
        agentCategoryWash("agent_category_wash", "agentCategoryWash", "VARCHAR", false),
        isWash("is_wash", "isWash", "VARCHAR", false),
        operatorWash("operator_wash", "operatorWash", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Wed May 28 09:51:52 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed May 28 09:51:52 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed May 28 09:51:52 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed May 28 09:51:52 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed May 28 09:51:52 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed May 28 09:51:52 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed May 28 09:51:52 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed May 28 09:51:52 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed May 28 09:51:52 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed May 28 09:51:52 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed May 28 09:51:52 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed May 28 09:51:52 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed May 28 09:51:52 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed May 28 09:51:52 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed May 28 09:51:52 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed May 28 09:51:52 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed May 28 09:51:52 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}