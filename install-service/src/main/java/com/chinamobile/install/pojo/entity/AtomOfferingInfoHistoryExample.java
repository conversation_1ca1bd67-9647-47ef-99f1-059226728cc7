package com.chinamobile.install.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AtomOfferingInfoHistoryExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public AtomOfferingInfoHistoryExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public AtomOfferingInfoHistoryExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    public AtomOfferingInfoHistoryExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    public static Criteria newAndCreateCriteria() {
        AtomOfferingInfoHistoryExample example = new AtomOfferingInfoHistoryExample();
        return example.createCriteria();
    }

    public AtomOfferingInfoHistoryExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    public AtomOfferingInfoHistoryExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    public AtomOfferingInfoHistoryExample distinct(boolean distinct) {
        this.setDistinct(distinct);
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSpuIdIsNull() {
            addCriterion("spu_id is null");
            return (Criteria) this;
        }

        public Criteria andSpuIdIsNotNull() {
            addCriterion("spu_id is not null");
            return (Criteria) this;
        }

        public Criteria andSpuIdEqualTo(String value) {
            addCriterion("spu_id =", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("spu_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuIdNotEqualTo(String value) {
            addCriterion("spu_id <>", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("spu_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThan(String value) {
            addCriterion("spu_id >", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("spu_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThanOrEqualTo(String value) {
            addCriterion("spu_id >=", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("spu_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThan(String value) {
            addCriterion("spu_id <", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("spu_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThanOrEqualTo(String value) {
            addCriterion("spu_id <=", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("spu_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuIdLike(String value) {
            addCriterion("spu_id like", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotLike(String value) {
            addCriterion("spu_id not like", value, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdIn(List<String> values) {
            addCriterion("spu_id in", values, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotIn(List<String> values) {
            addCriterion("spu_id not in", values, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdBetween(String value1, String value2) {
            addCriterion("spu_id between", value1, value2, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuIdNotBetween(String value1, String value2) {
            addCriterion("spu_id not between", value1, value2, "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNull() {
            addCriterion("spu_code is null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNotNull() {
            addCriterion("spu_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualTo(String value) {
            addCriterion("spu_code =", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("spu_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualTo(String value) {
            addCriterion("spu_code <>", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("spu_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThan(String value) {
            addCriterion("spu_code >", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("spu_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("spu_code >=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("spu_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThan(String value) {
            addCriterion("spu_code <", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("spu_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualTo(String value) {
            addCriterion("spu_code <=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("spu_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLike(String value) {
            addCriterion("spu_code like", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotLike(String value) {
            addCriterion("spu_code not like", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIn(List<String> values) {
            addCriterion("spu_code in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotIn(List<String> values) {
            addCriterion("spu_code not in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeBetween(String value1, String value2) {
            addCriterion("spu_code between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotBetween(String value1, String value2) {
            addCriterion("spu_code not between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSkuIdIsNull() {
            addCriterion("sku_id is null");
            return (Criteria) this;
        }

        public Criteria andSkuIdIsNotNull() {
            addCriterion("sku_id is not null");
            return (Criteria) this;
        }

        public Criteria andSkuIdEqualTo(String value) {
            addCriterion("sku_id =", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("sku_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuIdNotEqualTo(String value) {
            addCriterion("sku_id <>", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("sku_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuIdGreaterThan(String value) {
            addCriterion("sku_id >", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("sku_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuIdGreaterThanOrEqualTo(String value) {
            addCriterion("sku_id >=", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("sku_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuIdLessThan(String value) {
            addCriterion("sku_id <", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("sku_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuIdLessThanOrEqualTo(String value) {
            addCriterion("sku_id <=", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("sku_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuIdLike(String value) {
            addCriterion("sku_id like", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotLike(String value) {
            addCriterion("sku_id not like", value, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdIn(List<String> values) {
            addCriterion("sku_id in", values, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotIn(List<String> values) {
            addCriterion("sku_id not in", values, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdBetween(String value1, String value2) {
            addCriterion("sku_id between", value1, value2, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuIdNotBetween(String value1, String value2) {
            addCriterion("sku_id not between", value1, value2, "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNull() {
            addCriterion("sku_code is null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNotNull() {
            addCriterion("sku_code is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeEqualTo(String value) {
            addCriterion("sku_code =", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("sku_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotEqualTo(String value) {
            addCriterion("sku_code <>", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("sku_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThan(String value) {
            addCriterion("sku_code >", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("sku_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sku_code >=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("sku_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThan(String value) {
            addCriterion("sku_code <", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("sku_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanOrEqualTo(String value) {
            addCriterion("sku_code <=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("sku_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeLike(String value) {
            addCriterion("sku_code like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotLike(String value) {
            addCriterion("sku_code not like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIn(List<String> values) {
            addCriterion("sku_code in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotIn(List<String> values) {
            addCriterion("sku_code not in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeBetween(String value1, String value2) {
            addCriterion("sku_code between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotBetween(String value1, String value2) {
            addCriterion("sku_code not between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeIsNull() {
            addCriterion("offering_code is null");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeIsNotNull() {
            addCriterion("offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeEqualTo(String value) {
            addCriterion("offering_code =", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotEqualTo(String value) {
            addCriterion("offering_code <>", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThan(String value) {
            addCriterion("offering_code >", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("offering_code >=", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThan(String value) {
            addCriterion("offering_code <", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("offering_code <=", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLike(String value) {
            addCriterion("offering_code like", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotLike(String value) {
            addCriterion("offering_code not like", value, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeIn(List<String> values) {
            addCriterion("offering_code in", values, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotIn(List<String> values) {
            addCriterion("offering_code not in", values, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeBetween(String value1, String value2) {
            addCriterion("offering_code between", value1, value2, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("offering_code not between", value1, value2, "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingNameIsNull() {
            addCriterion("offering_name is null");
            return (Criteria) this;
        }

        public Criteria andOfferingNameIsNotNull() {
            addCriterion("offering_name is not null");
            return (Criteria) this;
        }

        public Criteria andOfferingNameEqualTo(String value) {
            addCriterion("offering_name =", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offering_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotEqualTo(String value) {
            addCriterion("offering_name <>", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offering_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThan(String value) {
            addCriterion("offering_name >", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offering_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThanOrEqualTo(String value) {
            addCriterion("offering_name >=", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offering_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThan(String value) {
            addCriterion("offering_name <", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offering_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThanOrEqualTo(String value) {
            addCriterion("offering_name <=", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offering_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingNameLike(String value) {
            addCriterion("offering_name like", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotLike(String value) {
            addCriterion("offering_name not like", value, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameIn(List<String> values) {
            addCriterion("offering_name in", values, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotIn(List<String> values) {
            addCriterion("offering_name not in", values, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameBetween(String value1, String value2) {
            addCriterion("offering_name between", value1, value2, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingNameNotBetween(String value1, String value2) {
            addCriterion("offering_name not between", value1, value2, "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingClassIsNull() {
            addCriterion("offering_class is null");
            return (Criteria) this;
        }

        public Criteria andOfferingClassIsNotNull() {
            addCriterion("offering_class is not null");
            return (Criteria) this;
        }

        public Criteria andOfferingClassEqualTo(String value) {
            addCriterion("offering_class =", value, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offering_class = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingClassNotEqualTo(String value) {
            addCriterion("offering_class <>", value, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offering_class <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingClassGreaterThan(String value) {
            addCriterion("offering_class >", value, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offering_class > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingClassGreaterThanOrEqualTo(String value) {
            addCriterion("offering_class >=", value, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offering_class >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingClassLessThan(String value) {
            addCriterion("offering_class <", value, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offering_class < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingClassLessThanOrEqualTo(String value) {
            addCriterion("offering_class <=", value, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offering_class <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingClassLike(String value) {
            addCriterion("offering_class like", value, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassNotLike(String value) {
            addCriterion("offering_class not like", value, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassIn(List<String> values) {
            addCriterion("offering_class in", values, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassNotIn(List<String> values) {
            addCriterion("offering_class not in", values, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassBetween(String value1, String value2) {
            addCriterion("offering_class between", value1, value2, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andOfferingClassNotBetween(String value1, String value2) {
            addCriterion("offering_class not between", value1, value2, "offeringClass");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNull() {
            addCriterion("quantity is null");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNotNull() {
            addCriterion("quantity is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualTo(Long value) {
            addCriterion("quantity =", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("quantity = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualTo(Long value) {
            addCriterion("quantity <>", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("quantity <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThan(Long value) {
            addCriterion("quantity >", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("quantity > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualTo(Long value) {
            addCriterion("quantity >=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("quantity >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityLessThan(Long value) {
            addCriterion("quantity <", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("quantity < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualTo(Long value) {
            addCriterion("quantity <=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("quantity <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuantityIn(List<Long> values) {
            addCriterion("quantity in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotIn(List<Long> values) {
            addCriterion("quantity not in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityBetween(Long value1, Long value2) {
            addCriterion("quantity between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotBetween(Long value1, Long value2) {
            addCriterion("quantity not between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeIsNull() {
            addCriterion("ext_soft_offering_code is null");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeIsNotNull() {
            addCriterion("ext_soft_offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeEqualTo(String value) {
            addCriterion("ext_soft_offering_code =", value, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("ext_soft_offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeNotEqualTo(String value) {
            addCriterion("ext_soft_offering_code <>", value, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("ext_soft_offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeGreaterThan(String value) {
            addCriterion("ext_soft_offering_code >", value, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("ext_soft_offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("ext_soft_offering_code >=", value, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("ext_soft_offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeLessThan(String value) {
            addCriterion("ext_soft_offering_code <", value, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("ext_soft_offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("ext_soft_offering_code <=", value, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("ext_soft_offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeLike(String value) {
            addCriterion("ext_soft_offering_code like", value, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeNotLike(String value) {
            addCriterion("ext_soft_offering_code not like", value, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeIn(List<String> values) {
            addCriterion("ext_soft_offering_code in", values, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeNotIn(List<String> values) {
            addCriterion("ext_soft_offering_code not in", values, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeBetween(String value1, String value2) {
            addCriterion("ext_soft_offering_code between", value1, value2, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("ext_soft_offering_code not between", value1, value2, "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeIsNull() {
            addCriterion("ext_hard_offering_code is null");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeIsNotNull() {
            addCriterion("ext_hard_offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeEqualTo(String value) {
            addCriterion("ext_hard_offering_code =", value, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("ext_hard_offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeNotEqualTo(String value) {
            addCriterion("ext_hard_offering_code <>", value, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("ext_hard_offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeGreaterThan(String value) {
            addCriterion("ext_hard_offering_code >", value, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("ext_hard_offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("ext_hard_offering_code >=", value, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("ext_hard_offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeLessThan(String value) {
            addCriterion("ext_hard_offering_code <", value, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("ext_hard_offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("ext_hard_offering_code <=", value, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("ext_hard_offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeLike(String value) {
            addCriterion("ext_hard_offering_code like", value, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeNotLike(String value) {
            addCriterion("ext_hard_offering_code not like", value, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeIn(List<String> values) {
            addCriterion("ext_hard_offering_code in", values, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeNotIn(List<String> values) {
            addCriterion("ext_hard_offering_code not in", values, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeBetween(String value1, String value2) {
            addCriterion("ext_hard_offering_code between", value1, value2, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("ext_hard_offering_code not between", value1, value2, "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSettlePriceIsNull() {
            addCriterion("settle_price is null");
            return (Criteria) this;
        }

        public Criteria andSettlePriceIsNotNull() {
            addCriterion("settle_price is not null");
            return (Criteria) this;
        }

        public Criteria andSettlePriceEqualTo(Long value) {
            addCriterion("settle_price =", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("settle_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceNotEqualTo(Long value) {
            addCriterion("settle_price <>", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("settle_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceGreaterThan(Long value) {
            addCriterion("settle_price >", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("settle_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceGreaterThanOrEqualTo(Long value) {
            addCriterion("settle_price >=", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("settle_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceLessThan(Long value) {
            addCriterion("settle_price <", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("settle_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceLessThanOrEqualTo(Long value) {
            addCriterion("settle_price <=", value, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("settle_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlePriceIn(List<Long> values) {
            addCriterion("settle_price in", values, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceNotIn(List<Long> values) {
            addCriterion("settle_price not in", values, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceBetween(Long value1, Long value2) {
            addCriterion("settle_price between", value1, value2, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andSettlePriceNotBetween(Long value1, Long value2) {
            addCriterion("settle_price not between", value1, value2, "settlePrice");
            return (Criteria) this;
        }

        public Criteria andChargeCodeIsNull() {
            addCriterion("charge_code is null");
            return (Criteria) this;
        }

        public Criteria andChargeCodeIsNotNull() {
            addCriterion("charge_code is not null");
            return (Criteria) this;
        }

        public Criteria andChargeCodeEqualTo(String value) {
            addCriterion("charge_code =", value, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("charge_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeCodeNotEqualTo(String value) {
            addCriterion("charge_code <>", value, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("charge_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeCodeGreaterThan(String value) {
            addCriterion("charge_code >", value, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("charge_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeCodeGreaterThanOrEqualTo(String value) {
            addCriterion("charge_code >=", value, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("charge_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeCodeLessThan(String value) {
            addCriterion("charge_code <", value, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("charge_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeCodeLessThanOrEqualTo(String value) {
            addCriterion("charge_code <=", value, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("charge_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeCodeLike(String value) {
            addCriterion("charge_code like", value, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeNotLike(String value) {
            addCriterion("charge_code not like", value, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeIn(List<String> values) {
            addCriterion("charge_code in", values, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeNotIn(List<String> values) {
            addCriterion("charge_code not in", values, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeBetween(String value1, String value2) {
            addCriterion("charge_code between", value1, value2, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeNotBetween(String value1, String value2) {
            addCriterion("charge_code not between", value1, value2, "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeIdIsNull() {
            addCriterion("charge_id is null");
            return (Criteria) this;
        }

        public Criteria andChargeIdIsNotNull() {
            addCriterion("charge_id is not null");
            return (Criteria) this;
        }

        public Criteria andChargeIdEqualTo(String value) {
            addCriterion("charge_id =", value, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("charge_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeIdNotEqualTo(String value) {
            addCriterion("charge_id <>", value, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("charge_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeIdGreaterThan(String value) {
            addCriterion("charge_id >", value, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("charge_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeIdGreaterThanOrEqualTo(String value) {
            addCriterion("charge_id >=", value, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("charge_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeIdLessThan(String value) {
            addCriterion("charge_id <", value, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("charge_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeIdLessThanOrEqualTo(String value) {
            addCriterion("charge_id <=", value, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("charge_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andChargeIdLike(String value) {
            addCriterion("charge_id like", value, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdNotLike(String value) {
            addCriterion("charge_id not like", value, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdIn(List<String> values) {
            addCriterion("charge_id in", values, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdNotIn(List<String> values) {
            addCriterion("charge_id not in", values, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdBetween(String value1, String value2) {
            addCriterion("charge_id between", value1, value2, "chargeId");
            return (Criteria) this;
        }

        public Criteria andChargeIdNotBetween(String value1, String value2) {
            addCriterion("charge_id not between", value1, value2, "chargeId");
            return (Criteria) this;
        }

        public Criteria andColorIsNull() {
            addCriterion("color is null");
            return (Criteria) this;
        }

        public Criteria andColorIsNotNull() {
            addCriterion("color is not null");
            return (Criteria) this;
        }

        public Criteria andColorEqualTo(String value) {
            addCriterion("color =", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("color = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorNotEqualTo(String value) {
            addCriterion("color <>", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("color <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorGreaterThan(String value) {
            addCriterion("color >", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("color > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorGreaterThanOrEqualTo(String value) {
            addCriterion("color >=", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("color >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorLessThan(String value) {
            addCriterion("color <", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("color < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorLessThanOrEqualTo(String value) {
            addCriterion("color <=", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("color <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorLike(String value) {
            addCriterion("color like", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotLike(String value) {
            addCriterion("color not like", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorIn(List<String> values) {
            addCriterion("color in", values, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotIn(List<String> values) {
            addCriterion("color not in", values, "color");
            return (Criteria) this;
        }

        public Criteria andColorBetween(String value1, String value2) {
            addCriterion("color between", value1, value2, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotBetween(String value1, String value2) {
            addCriterion("color not between", value1, value2, "color");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("model = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("model <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("model > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("model >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("model < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("model <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("unit = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("unit <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("unit > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("unit >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("unit < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("unit <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceIsNull() {
            addCriterion("atom_sale_price is null");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceIsNotNull() {
            addCriterion("atom_sale_price is not null");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceEqualTo(Long value) {
            addCriterion("atom_sale_price =", value, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("atom_sale_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceNotEqualTo(Long value) {
            addCriterion("atom_sale_price <>", value, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("atom_sale_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceGreaterThan(Long value) {
            addCriterion("atom_sale_price >", value, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("atom_sale_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceGreaterThanOrEqualTo(Long value) {
            addCriterion("atom_sale_price >=", value, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("atom_sale_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceLessThan(Long value) {
            addCriterion("atom_sale_price <", value, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("atom_sale_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceLessThanOrEqualTo(Long value) {
            addCriterion("atom_sale_price <=", value, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("atom_sale_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceIn(List<Long> values) {
            addCriterion("atom_sale_price in", values, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceNotIn(List<Long> values) {
            addCriterion("atom_sale_price not in", values, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceBetween(Long value1, Long value2) {
            addCriterion("atom_sale_price between", value1, value2, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSalePriceNotBetween(Long value1, Long value2) {
            addCriterion("atom_sale_price not between", value1, value2, "atomSalePrice");
            return (Criteria) this;
        }

        public Criteria andInventoryIsNull() {
            addCriterion("inventory is null");
            return (Criteria) this;
        }

        public Criteria andInventoryIsNotNull() {
            addCriterion("inventory is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryEqualTo(Long value) {
            addCriterion("inventory =", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("inventory = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryNotEqualTo(Long value) {
            addCriterion("inventory <>", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("inventory <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryGreaterThan(Long value) {
            addCriterion("inventory >", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("inventory > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryGreaterThanOrEqualTo(Long value) {
            addCriterion("inventory >=", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("inventory >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryLessThan(Long value) {
            addCriterion("inventory <", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("inventory < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryLessThanOrEqualTo(Long value) {
            addCriterion("inventory <=", value, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("inventory <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryIn(List<Long> values) {
            addCriterion("inventory in", values, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryNotIn(List<Long> values) {
            addCriterion("inventory not in", values, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryBetween(Long value1, Long value2) {
            addCriterion("inventory between", value1, value2, "inventory");
            return (Criteria) this;
        }

        public Criteria andInventoryNotBetween(Long value1, Long value2) {
            addCriterion("inventory not between", value1, value2, "inventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryIsNull() {
            addCriterion("reserve_inventory is null");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryIsNotNull() {
            addCriterion("reserve_inventory is not null");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryEqualTo(Long value) {
            addCriterion("reserve_inventory =", value, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("reserve_inventory = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveInventoryNotEqualTo(Long value) {
            addCriterion("reserve_inventory <>", value, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("reserve_inventory <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveInventoryGreaterThan(Long value) {
            addCriterion("reserve_inventory >", value, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("reserve_inventory > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveInventoryGreaterThanOrEqualTo(Long value) {
            addCriterion("reserve_inventory >=", value, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("reserve_inventory >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveInventoryLessThan(Long value) {
            addCriterion("reserve_inventory <", value, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("reserve_inventory < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveInventoryLessThanOrEqualTo(Long value) {
            addCriterion("reserve_inventory <=", value, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("reserve_inventory <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReserveInventoryIn(List<Long> values) {
            addCriterion("reserve_inventory in", values, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryNotIn(List<Long> values) {
            addCriterion("reserve_inventory not in", values, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryBetween(Long value1, Long value2) {
            addCriterion("reserve_inventory between", value1, value2, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andReserveInventoryNotBetween(Long value1, Long value2) {
            addCriterion("reserve_inventory not between", value1, value2, "reserveInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryIsNull() {
            addCriterion("is_inventory is null");
            return (Criteria) this;
        }

        public Criteria andIsInventoryIsNotNull() {
            addCriterion("is_inventory is not null");
            return (Criteria) this;
        }

        public Criteria andIsInventoryEqualTo(Boolean value) {
            addCriterion("is_inventory =", value, "isInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("is_inventory = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsInventoryNotEqualTo(Boolean value) {
            addCriterion("is_inventory <>", value, "isInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("is_inventory <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsInventoryGreaterThan(Boolean value) {
            addCriterion("is_inventory >", value, "isInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("is_inventory > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsInventoryGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_inventory >=", value, "isInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("is_inventory >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsInventoryLessThan(Boolean value) {
            addCriterion("is_inventory <", value, "isInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("is_inventory < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsInventoryLessThanOrEqualTo(Boolean value) {
            addCriterion("is_inventory <=", value, "isInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("is_inventory <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsInventoryIn(List<Boolean> values) {
            addCriterion("is_inventory in", values, "isInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryNotIn(List<Boolean> values) {
            addCriterion("is_inventory not in", values, "isInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryBetween(Boolean value1, Boolean value2) {
            addCriterion("is_inventory between", value1, value2, "isInventory");
            return (Criteria) this;
        }

        public Criteria andIsInventoryNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_inventory not between", value1, value2, "isInventory");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIsNull() {
            addCriterion("cooperator_id is null");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIsNotNull() {
            addCriterion("cooperator_id is not null");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdEqualTo(String value) {
            addCriterion("cooperator_id =", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("cooperator_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotEqualTo(String value) {
            addCriterion("cooperator_id <>", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("cooperator_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThan(String value) {
            addCriterion("cooperator_id >", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("cooperator_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanOrEqualTo(String value) {
            addCriterion("cooperator_id >=", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("cooperator_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThan(String value) {
            addCriterion("cooperator_id <", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("cooperator_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanOrEqualTo(String value) {
            addCriterion("cooperator_id <=", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("cooperator_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLike(String value) {
            addCriterion("cooperator_id like", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotLike(String value) {
            addCriterion("cooperator_id not like", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIn(List<String> values) {
            addCriterion("cooperator_id in", values, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotIn(List<String> values) {
            addCriterion("cooperator_id not in", values, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdBetween(String value1, String value2) {
            addCriterion("cooperator_id between", value1, value2, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotBetween(String value1, String value2) {
            addCriterion("cooperator_id not between", value1, value2, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdIsNull() {
            addCriterion("inventory_threshold is null");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdIsNotNull() {
            addCriterion("inventory_threshold is not null");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdEqualTo(Long value) {
            addCriterion("inventory_threshold =", value, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("inventory_threshold = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdNotEqualTo(Long value) {
            addCriterion("inventory_threshold <>", value, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("inventory_threshold <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdGreaterThan(Long value) {
            addCriterion("inventory_threshold >", value, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("inventory_threshold > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdGreaterThanOrEqualTo(Long value) {
            addCriterion("inventory_threshold >=", value, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("inventory_threshold >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdLessThan(Long value) {
            addCriterion("inventory_threshold <", value, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("inventory_threshold < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdLessThanOrEqualTo(Long value) {
            addCriterion("inventory_threshold <=", value, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("inventory_threshold <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdIn(List<Long> values) {
            addCriterion("inventory_threshold in", values, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdNotIn(List<Long> values) {
            addCriterion("inventory_threshold not in", values, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdBetween(Long value1, Long value2) {
            addCriterion("inventory_threshold between", value1, value2, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andInventoryThresholdNotBetween(Long value1, Long value2) {
            addCriterion("inventory_threshold not between", value1, value2, "inventoryThreshold");
            return (Criteria) this;
        }

        public Criteria andIsNoticeIsNull() {
            addCriterion("is_notice is null");
            return (Criteria) this;
        }

        public Criteria andIsNoticeIsNotNull() {
            addCriterion("is_notice is not null");
            return (Criteria) this;
        }

        public Criteria andIsNoticeEqualTo(Boolean value) {
            addCriterion("is_notice =", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("is_notice = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsNoticeNotEqualTo(Boolean value) {
            addCriterion("is_notice <>", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("is_notice <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsNoticeGreaterThan(Boolean value) {
            addCriterion("is_notice >", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("is_notice > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsNoticeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_notice >=", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("is_notice >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsNoticeLessThan(Boolean value) {
            addCriterion("is_notice <", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("is_notice < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsNoticeLessThanOrEqualTo(Boolean value) {
            addCriterion("is_notice <=", value, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("is_notice <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIsNoticeIn(List<Boolean> values) {
            addCriterion("is_notice in", values, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeNotIn(List<Boolean> values) {
            addCriterion("is_notice not in", values, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeBetween(Boolean value1, Boolean value2) {
            addCriterion("is_notice between", value1, value2, "isNotice");
            return (Criteria) this;
        }

        public Criteria andIsNoticeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_notice not between", value1, value2, "isNotice");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeIsNull() {
            addCriterion("config_all_time is null");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeIsNotNull() {
            addCriterion("config_all_time is not null");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeEqualTo(Date value) {
            addCriterion("config_all_time =", value, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("config_all_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeNotEqualTo(Date value) {
            addCriterion("config_all_time <>", value, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("config_all_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeGreaterThan(Date value) {
            addCriterion("config_all_time >", value, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("config_all_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("config_all_time >=", value, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("config_all_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeLessThan(Date value) {
            addCriterion("config_all_time <", value, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("config_all_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeLessThanOrEqualTo(Date value) {
            addCriterion("config_all_time <=", value, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("config_all_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeIn(List<Date> values) {
            addCriterion("config_all_time in", values, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeNotIn(List<Date> values) {
            addCriterion("config_all_time not in", values, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeBetween(Date value1, Date value2) {
            addCriterion("config_all_time between", value1, value2, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigAllTimeNotBetween(Date value1, Date value2) {
            addCriterion("config_all_time not between", value1, value2, "configAllTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeIsNull() {
            addCriterion("config_time is null");
            return (Criteria) this;
        }

        public Criteria andConfigTimeIsNotNull() {
            addCriterion("config_time is not null");
            return (Criteria) this;
        }

        public Criteria andConfigTimeEqualTo(Date value) {
            addCriterion("config_time =", value, "configTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("config_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigTimeNotEqualTo(Date value) {
            addCriterion("config_time <>", value, "configTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("config_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigTimeGreaterThan(Date value) {
            addCriterion("config_time >", value, "configTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("config_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("config_time >=", value, "configTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("config_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigTimeLessThan(Date value) {
            addCriterion("config_time <", value, "configTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("config_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigTimeLessThanOrEqualTo(Date value) {
            addCriterion("config_time <=", value, "configTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("config_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andConfigTimeIn(List<Date> values) {
            addCriterion("config_time in", values, "configTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeNotIn(List<Date> values) {
            addCriterion("config_time not in", values, "configTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeBetween(Date value1, Date value2) {
            addCriterion("config_time between", value1, value2, "configTime");
            return (Criteria) this;
        }

        public Criteria andConfigTimeNotBetween(Date value1, Date value2) {
            addCriterion("config_time not between", value1, value2, "configTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andNotifiedIsNull() {
            addCriterion("notified is null");
            return (Criteria) this;
        }

        public Criteria andNotifiedIsNotNull() {
            addCriterion("notified is not null");
            return (Criteria) this;
        }

        public Criteria andNotifiedEqualTo(Boolean value) {
            addCriterion("notified =", value, "notified");
            return (Criteria) this;
        }

        public Criteria andNotifiedEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("notified = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNotifiedNotEqualTo(Boolean value) {
            addCriterion("notified <>", value, "notified");
            return (Criteria) this;
        }

        public Criteria andNotifiedNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("notified <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNotifiedGreaterThan(Boolean value) {
            addCriterion("notified >", value, "notified");
            return (Criteria) this;
        }

        public Criteria andNotifiedGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("notified > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNotifiedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("notified >=", value, "notified");
            return (Criteria) this;
        }

        public Criteria andNotifiedGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("notified >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNotifiedLessThan(Boolean value) {
            addCriterion("notified <", value, "notified");
            return (Criteria) this;
        }

        public Criteria andNotifiedLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("notified < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNotifiedLessThanOrEqualTo(Boolean value) {
            addCriterion("notified <=", value, "notified");
            return (Criteria) this;
        }

        public Criteria andNotifiedLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("notified <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNotifiedIn(List<Boolean> values) {
            addCriterion("notified in", values, "notified");
            return (Criteria) this;
        }

        public Criteria andNotifiedNotIn(List<Boolean> values) {
            addCriterion("notified not in", values, "notified");
            return (Criteria) this;
        }

        public Criteria andNotifiedBetween(Boolean value1, Boolean value2) {
            addCriterion("notified between", value1, value2, "notified");
            return (Criteria) this;
        }

        public Criteria andNotifiedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("notified not between", value1, value2, "notified");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionIsNull() {
            addCriterion("offeringSaleRegion is null");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionIsNotNull() {
            addCriterion("offeringSaleRegion is not null");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionEqualTo(String value) {
            addCriterion("offeringSaleRegion =", value, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offeringSaleRegion = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionNotEqualTo(String value) {
            addCriterion("offeringSaleRegion <>", value, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offeringSaleRegion <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionGreaterThan(String value) {
            addCriterion("offeringSaleRegion >", value, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offeringSaleRegion > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionGreaterThanOrEqualTo(String value) {
            addCriterion("offeringSaleRegion >=", value, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offeringSaleRegion >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionLessThan(String value) {
            addCriterion("offeringSaleRegion <", value, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offeringSaleRegion < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionLessThanOrEqualTo(String value) {
            addCriterion("offeringSaleRegion <=", value, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("offeringSaleRegion <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionLike(String value) {
            addCriterion("offeringSaleRegion like", value, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionNotLike(String value) {
            addCriterion("offeringSaleRegion not like", value, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionIn(List<String> values) {
            addCriterion("offeringSaleRegion in", values, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionNotIn(List<String> values) {
            addCriterion("offeringSaleRegion not in", values, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionBetween(String value1, String value2) {
            addCriterion("offeringSaleRegion between", value1, value2, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionNotBetween(String value1, String value2) {
            addCriterion("offeringSaleRegion not between", value1, value2, "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerIsNull() {
            addCriterion("settlePricePartner is null");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerIsNotNull() {
            addCriterion("settlePricePartner is not null");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerEqualTo(String value) {
            addCriterion("settlePricePartner =", value, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("settlePricePartner = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerNotEqualTo(String value) {
            addCriterion("settlePricePartner <>", value, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("settlePricePartner <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerGreaterThan(String value) {
            addCriterion("settlePricePartner >", value, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("settlePricePartner > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerGreaterThanOrEqualTo(String value) {
            addCriterion("settlePricePartner >=", value, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("settlePricePartner >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerLessThan(String value) {
            addCriterion("settlePricePartner <", value, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("settlePricePartner < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerLessThanOrEqualTo(String value) {
            addCriterion("settlePricePartner <=", value, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("settlePricePartner <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerLike(String value) {
            addCriterion("settlePricePartner like", value, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerNotLike(String value) {
            addCriterion("settlePricePartner not like", value, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerIn(List<String> values) {
            addCriterion("settlePricePartner in", values, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerNotIn(List<String> values) {
            addCriterion("settlePricePartner not in", values, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerBetween(String value1, String value2) {
            addCriterion("settlePricePartner between", value1, value2, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerNotBetween(String value1, String value2) {
            addCriterion("settlePricePartner not between", value1, value2, "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameIsNull() {
            addCriterion("settleServiceName is null");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameIsNotNull() {
            addCriterion("settleServiceName is not null");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameEqualTo(String value) {
            addCriterion("settleServiceName =", value, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("settleServiceName = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleservicenameNotEqualTo(String value) {
            addCriterion("settleServiceName <>", value, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("settleServiceName <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleservicenameGreaterThan(String value) {
            addCriterion("settleServiceName >", value, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("settleServiceName > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleservicenameGreaterThanOrEqualTo(String value) {
            addCriterion("settleServiceName >=", value, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("settleServiceName >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleservicenameLessThan(String value) {
            addCriterion("settleServiceName <", value, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("settleServiceName < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleservicenameLessThanOrEqualTo(String value) {
            addCriterion("settleServiceName <=", value, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("settleServiceName <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleservicenameLike(String value) {
            addCriterion("settleServiceName like", value, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameNotLike(String value) {
            addCriterion("settleServiceName not like", value, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameIn(List<String> values) {
            addCriterion("settleServiceName in", values, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameNotIn(List<String> values) {
            addCriterion("settleServiceName not in", values, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameBetween(String value1, String value2) {
            addCriterion("settleServiceName between", value1, value2, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameNotBetween(String value1, String value2) {
            addCriterion("settleServiceName not between", value1, value2, "settleservicename");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferIsNull() {
            addCriterion("associated_offer is null");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferIsNotNull() {
            addCriterion("associated_offer is not null");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferEqualTo(String value) {
            addCriterion("associated_offer =", value, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("associated_offer = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferNotEqualTo(String value) {
            addCriterion("associated_offer <>", value, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("associated_offer <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferGreaterThan(String value) {
            addCriterion("associated_offer >", value, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("associated_offer > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferGreaterThanOrEqualTo(String value) {
            addCriterion("associated_offer >=", value, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("associated_offer >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferLessThan(String value) {
            addCriterion("associated_offer <", value, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("associated_offer < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferLessThanOrEqualTo(String value) {
            addCriterion("associated_offer <=", value, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("associated_offer <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferLike(String value) {
            addCriterion("associated_offer like", value, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferNotLike(String value) {
            addCriterion("associated_offer not like", value, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferIn(List<String> values) {
            addCriterion("associated_offer in", values, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferNotIn(List<String> values) {
            addCriterion("associated_offer not in", values, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferBetween(String value1, String value2) {
            addCriterion("associated_offer between", value1, value2, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferNotBetween(String value1, String value2) {
            addCriterion("associated_offer not between", value1, value2, "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodIsNull() {
            addCriterion("validity_period is null");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodIsNotNull() {
            addCriterion("validity_period is not null");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodEqualTo(String value) {
            addCriterion("validity_period =", value, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("validity_period = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValidityPeriodNotEqualTo(String value) {
            addCriterion("validity_period <>", value, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("validity_period <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValidityPeriodGreaterThan(String value) {
            addCriterion("validity_period >", value, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("validity_period > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValidityPeriodGreaterThanOrEqualTo(String value) {
            addCriterion("validity_period >=", value, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("validity_period >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValidityPeriodLessThan(String value) {
            addCriterion("validity_period <", value, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("validity_period < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValidityPeriodLessThanOrEqualTo(String value) {
            addCriterion("validity_period <=", value, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("validity_period <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValidityPeriodLike(String value) {
            addCriterion("validity_period like", value, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodNotLike(String value) {
            addCriterion("validity_period not like", value, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodIn(List<String> values) {
            addCriterion("validity_period in", values, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodNotIn(List<String> values) {
            addCriterion("validity_period not in", values, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodBetween(String value1, String value2) {
            addCriterion("validity_period between", value1, value2, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodNotBetween(String value1, String value2) {
            addCriterion("validity_period not between", value1, value2, "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeIsNull() {
            addCriterion("delete_time is null");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeIsNotNull() {
            addCriterion("delete_time is not null");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeEqualTo(Date value) {
            addCriterion("delete_time =", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("delete_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotEqualTo(Date value) {
            addCriterion("delete_time <>", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("delete_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThan(Date value) {
            addCriterion("delete_time >", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("delete_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("delete_time >=", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("delete_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThan(Date value) {
            addCriterion("delete_time <", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("delete_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThanOrEqualTo(Date value) {
            addCriterion("delete_time <=", value, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("delete_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeleteTimeIn(List<Date> values) {
            addCriterion("delete_time in", values, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotIn(List<Date> values) {
            addCriterion("delete_time not in", values, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeBetween(Date value1, Date value2) {
            addCriterion("delete_time between", value1, value2, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andDeleteTimeNotBetween(Date value1, Date value2) {
            addCriterion("delete_time not between", value1, value2, "deleteTime");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayIsNull() {
            addCriterion("get_order_way is null");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayIsNotNull() {
            addCriterion("get_order_way is not null");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayEqualTo(Integer value) {
            addCriterion("get_order_way =", value, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("get_order_way = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andGetOrderWayNotEqualTo(Integer value) {
            addCriterion("get_order_way <>", value, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("get_order_way <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andGetOrderWayGreaterThan(Integer value) {
            addCriterion("get_order_way >", value, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("get_order_way > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andGetOrderWayGreaterThanOrEqualTo(Integer value) {
            addCriterion("get_order_way >=", value, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("get_order_way >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andGetOrderWayLessThan(Integer value) {
            addCriterion("get_order_way <", value, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("get_order_way < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andGetOrderWayLessThanOrEqualTo(Integer value) {
            addCriterion("get_order_way <=", value, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("get_order_way <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andGetOrderWayIn(List<Integer> values) {
            addCriterion("get_order_way in", values, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayNotIn(List<Integer> values) {
            addCriterion("get_order_way not in", values, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayBetween(Integer value1, Integer value2) {
            addCriterion("get_order_way between", value1, value2, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andGetOrderWayNotBetween(Integer value1, Integer value2) {
            addCriterion("get_order_way not between", value1, value2, "getOrderWay");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionIsNull() {
            addCriterion("atom_offering_version is null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionIsNotNull() {
            addCriterion("atom_offering_version is not null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionEqualTo(String value) {
            addCriterion("atom_offering_version =", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("atom_offering_version = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionNotEqualTo(String value) {
            addCriterion("atom_offering_version <>", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("atom_offering_version <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionGreaterThan(String value) {
            addCriterion("atom_offering_version >", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("atom_offering_version > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionGreaterThanOrEqualTo(String value) {
            addCriterion("atom_offering_version >=", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("atom_offering_version >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionLessThan(String value) {
            addCriterion("atom_offering_version <", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("atom_offering_version < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionLessThanOrEqualTo(String value) {
            addCriterion("atom_offering_version <=", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("atom_offering_version <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionLike(String value) {
            addCriterion("atom_offering_version like", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionNotLike(String value) {
            addCriterion("atom_offering_version not like", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionIn(List<String> values) {
            addCriterion("atom_offering_version in", values, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionNotIn(List<String> values) {
            addCriterion("atom_offering_version not in", values, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionBetween(String value1, String value2) {
            addCriterion("atom_offering_version between", value1, value2, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionNotBetween(String value1, String value2) {
            addCriterion("atom_offering_version not between", value1, value2, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionIsNull() {
            addCriterion("sku_offering_version is null");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionIsNotNull() {
            addCriterion("sku_offering_version is not null");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionEqualTo(String value) {
            addCriterion("sku_offering_version =", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("sku_offering_version = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionNotEqualTo(String value) {
            addCriterion("sku_offering_version <>", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("sku_offering_version <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionGreaterThan(String value) {
            addCriterion("sku_offering_version >", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("sku_offering_version > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionGreaterThanOrEqualTo(String value) {
            addCriterion("sku_offering_version >=", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("sku_offering_version >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionLessThan(String value) {
            addCriterion("sku_offering_version <", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("sku_offering_version < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionLessThanOrEqualTo(String value) {
            addCriterion("sku_offering_version <=", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("sku_offering_version <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionLike(String value) {
            addCriterion("sku_offering_version like", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionNotLike(String value) {
            addCriterion("sku_offering_version not like", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionIn(List<String> values) {
            addCriterion("sku_offering_version in", values, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionNotIn(List<String> values) {
            addCriterion("sku_offering_version not in", values, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionBetween(String value1, String value2) {
            addCriterion("sku_offering_version between", value1, value2, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionNotBetween(String value1, String value2) {
            addCriterion("sku_offering_version not between", value1, value2, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionIsNull() {
            addCriterion("spu_offering_version is null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionIsNotNull() {
            addCriterion("spu_offering_version is not null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionEqualTo(String value) {
            addCriterion("spu_offering_version =", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("spu_offering_version = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotEqualTo(String value) {
            addCriterion("spu_offering_version <>", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("spu_offering_version <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionGreaterThan(String value) {
            addCriterion("spu_offering_version >", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("spu_offering_version > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionGreaterThanOrEqualTo(String value) {
            addCriterion("spu_offering_version >=", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("spu_offering_version >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLessThan(String value) {
            addCriterion("spu_offering_version <", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("spu_offering_version < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLessThanOrEqualTo(String value) {
            addCriterion("spu_offering_version <=", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("spu_offering_version <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLike(String value) {
            addCriterion("spu_offering_version like", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotLike(String value) {
            addCriterion("spu_offering_version not like", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionIn(List<String> values) {
            addCriterion("spu_offering_version in", values, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotIn(List<String> values) {
            addCriterion("spu_offering_version not in", values, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionBetween(String value1, String value2) {
            addCriterion("spu_offering_version between", value1, value2, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotBetween(String value1, String value2) {
            addCriterion("spu_offering_version not between", value1, value2, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomIdIsNull() {
            addCriterion("atom_id is null");
            return (Criteria) this;
        }

        public Criteria andAtomIdIsNotNull() {
            addCriterion("atom_id is not null");
            return (Criteria) this;
        }

        public Criteria andAtomIdEqualTo(String value) {
            addCriterion("atom_id =", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("atom_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomIdNotEqualTo(String value) {
            addCriterion("atom_id <>", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("atom_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomIdGreaterThan(String value) {
            addCriterion("atom_id >", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("atom_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomIdGreaterThanOrEqualTo(String value) {
            addCriterion("atom_id >=", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("atom_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomIdLessThan(String value) {
            addCriterion("atom_id <", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("atom_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomIdLessThanOrEqualTo(String value) {
            addCriterion("atom_id <=", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("atom_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomIdLike(String value) {
            addCriterion("atom_id like", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdNotLike(String value) {
            addCriterion("atom_id not like", value, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdIn(List<String> values) {
            addCriterion("atom_id in", values, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdNotIn(List<String> values) {
            addCriterion("atom_id not in", values, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdBetween(String value1, String value2) {
            addCriterion("atom_id between", value1, value2, "atomId");
            return (Criteria) this;
        }

        public Criteria andAtomIdNotBetween(String value1, String value2) {
            addCriterion("atom_id not between", value1, value2, "atomId");
            return (Criteria) this;
        }

        public Criteria andProductTypeIsNull() {
            addCriterion("product_type is null");
            return (Criteria) this;
        }

        public Criteria andProductTypeIsNotNull() {
            addCriterion("product_type is not null");
            return (Criteria) this;
        }

        public Criteria andProductTypeEqualTo(String value) {
            addCriterion("product_type =", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("product_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeNotEqualTo(String value) {
            addCriterion("product_type <>", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("product_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThan(String value) {
            addCriterion("product_type >", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("product_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThanOrEqualTo(String value) {
            addCriterion("product_type >=", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("product_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThan(String value) {
            addCriterion("product_type <", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("product_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThanOrEqualTo(String value) {
            addCriterion("product_type <=", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("product_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductTypeLike(String value) {
            addCriterion("product_type like", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotLike(String value) {
            addCriterion("product_type not like", value, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeIn(List<String> values) {
            addCriterion("product_type in", values, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotIn(List<String> values) {
            addCriterion("product_type not in", values, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeBetween(String value1, String value2) {
            addCriterion("product_type between", value1, value2, "productType");
            return (Criteria) this;
        }

        public Criteria andProductTypeNotBetween(String value1, String value2) {
            addCriterion("product_type not between", value1, value2, "productType");
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalIsNull() {
            addCriterion("card_containing_terminal is null");
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalIsNotNull() {
            addCriterion("card_containing_terminal is not null");
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalEqualTo(String value) {
            addCriterion("card_containing_terminal =", value, "cardContainingTerminal");
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("card_containing_terminal = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalNotEqualTo(String value) {
            addCriterion("card_containing_terminal <>", value, "cardContainingTerminal");
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalNotEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("card_containing_terminal <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalGreaterThan(String value) {
            addCriterion("card_containing_terminal >", value, "cardContainingTerminal");
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalGreaterThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("card_containing_terminal > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalGreaterThanOrEqualTo(String value) {
            addCriterion("card_containing_terminal >=", value, "cardContainingTerminal");
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalGreaterThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("card_containing_terminal >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalLessThan(String value) {
            addCriterion("card_containing_terminal <", value, "cardContainingTerminal");
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalLessThanColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("card_containing_terminal < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalLessThanOrEqualTo(String value) {
            addCriterion("card_containing_terminal <=", value, "cardContainingTerminal");
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalLessThanOrEqualToColumn(AtomOfferingInfoHistory.Column column) {
            addCriterion(new StringBuilder("card_containing_terminal <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalLike(String value) {
            addCriterion("card_containing_terminal like", value, "cardContainingTerminal");
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalNotLike(String value) {
            addCriterion("card_containing_terminal not like", value, "cardContainingTerminal");
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalIn(List<String> values) {
            addCriterion("card_containing_terminal in", values, "cardContainingTerminal");
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalNotIn(List<String> values) {
            addCriterion("card_containing_terminal not in", values, "cardContainingTerminal");
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalBetween(String value1, String value2) {
            addCriterion("card_containing_terminal between", value1, value2, "cardContainingTerminal");
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalNotBetween(String value1, String value2) {
            addCriterion("card_containing_terminal not between", value1, value2, "cardContainingTerminal");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andSpuIdLikeInsensitive(String value) {
            addCriterion("upper(spu_id) like", value.toUpperCase(), "spuId");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLikeInsensitive(String value) {
            addCriterion("upper(spu_code) like", value.toUpperCase(), "spuCode");
            return (Criteria) this;
        }

        public Criteria andSkuIdLikeInsensitive(String value) {
            addCriterion("upper(sku_id) like", value.toUpperCase(), "skuId");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLikeInsensitive(String value) {
            addCriterion("upper(sku_code) like", value.toUpperCase(), "skuCode");
            return (Criteria) this;
        }

        public Criteria andOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(offering_code) like", value.toUpperCase(), "offeringCode");
            return (Criteria) this;
        }

        public Criteria andOfferingNameLikeInsensitive(String value) {
            addCriterion("upper(offering_name) like", value.toUpperCase(), "offeringName");
            return (Criteria) this;
        }

        public Criteria andOfferingClassLikeInsensitive(String value) {
            addCriterion("upper(offering_class) like", value.toUpperCase(), "offeringClass");
            return (Criteria) this;
        }

        public Criteria andExtSoftOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(ext_soft_offering_code) like", value.toUpperCase(), "extSoftOfferingCode");
            return (Criteria) this;
        }

        public Criteria andExtHardOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(ext_hard_offering_code) like", value.toUpperCase(), "extHardOfferingCode");
            return (Criteria) this;
        }

        public Criteria andChargeCodeLikeInsensitive(String value) {
            addCriterion("upper(charge_code) like", value.toUpperCase(), "chargeCode");
            return (Criteria) this;
        }

        public Criteria andChargeIdLikeInsensitive(String value) {
            addCriterion("upper(charge_id) like", value.toUpperCase(), "chargeId");
            return (Criteria) this;
        }

        public Criteria andColorLikeInsensitive(String value) {
            addCriterion("upper(color) like", value.toUpperCase(), "color");
            return (Criteria) this;
        }

        public Criteria andModelLikeInsensitive(String value) {
            addCriterion("upper(model) like", value.toUpperCase(), "model");
            return (Criteria) this;
        }

        public Criteria andUnitLikeInsensitive(String value) {
            addCriterion("upper(unit) like", value.toUpperCase(), "unit");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLikeInsensitive(String value) {
            addCriterion("upper(cooperator_id) like", value.toUpperCase(), "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andOfferingsaleregionLikeInsensitive(String value) {
            addCriterion("upper(offeringSaleRegion) like", value.toUpperCase(), "offeringsaleregion");
            return (Criteria) this;
        }

        public Criteria andSettlepricepartnerLikeInsensitive(String value) {
            addCriterion("upper(settlePricePartner) like", value.toUpperCase(), "settlepricepartner");
            return (Criteria) this;
        }

        public Criteria andSettleservicenameLikeInsensitive(String value) {
            addCriterion("upper(settleServiceName) like", value.toUpperCase(), "settleservicename");
            return (Criteria) this;
        }

        public Criteria andAssociatedOfferLikeInsensitive(String value) {
            addCriterion("upper(associated_offer) like", value.toUpperCase(), "associatedOffer");
            return (Criteria) this;
        }

        public Criteria andValidityPeriodLikeInsensitive(String value) {
            addCriterion("upper(validity_period) like", value.toUpperCase(), "validityPeriod");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionLikeInsensitive(String value) {
            addCriterion("upper(atom_offering_version) like", value.toUpperCase(), "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionLikeInsensitive(String value) {
            addCriterion("upper(sku_offering_version) like", value.toUpperCase(), "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLikeInsensitive(String value) {
            addCriterion("upper(spu_offering_version) like", value.toUpperCase(), "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomIdLikeInsensitive(String value) {
            addCriterion("upper(atom_id) like", value.toUpperCase(), "atomId");
            return (Criteria) this;
        }

        public Criteria andProductTypeLikeInsensitive(String value) {
            addCriterion("upper(product_type) like", value.toUpperCase(), "productType");
            return (Criteria) this;
        }

        public Criteria andCardContainingTerminalLikeInsensitive(String value) {
            addCriterion("upper(card_containing_terminal) like", value.toUpperCase(), "cardContainingTerminal");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        private AtomOfferingInfoHistoryExample example;

        protected Criteria(AtomOfferingInfoHistoryExample example) {
            super();
            this.example = example;
        }

        public AtomOfferingInfoHistoryExample example() {
            return this.example;
        }

        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            Criteria add(Criteria add);
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        void example(com.chinamobile.install.pojo.entity.AtomOfferingInfoHistoryExample example);
    }
}