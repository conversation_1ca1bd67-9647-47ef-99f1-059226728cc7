package com.chinamobile.install.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 售后订单同步信息
 *
 * <AUTHOR>
public class AfterMarketOrder2cInfo implements Serializable {
    /**
     * 售后订单ID
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private String serviceOrderId;

    /**
     * 关联商品订单号
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private String offeringOrderId;

    /**
     * 售后服务订单状态: 
1:待预约 -对应商城同步状态【1待预约（付款完成）】11:待分派-装维管理员接单时，用户提交预约申请后，售后服务订单状态由“待预约”变为“待分派”，装维管理员完成订单分派后，订单状态由“待分派”变为“待派单” 
2:派单中-对应商城同步状态【2待派单（预约完成）】
3:已派单
 31：已签到 4.已完结（成功）
5.已完成（失败）
6.已撤销
7.交易完成-对应商城同步状态【3订单计收（订单同步至CMIoT成功后，同步本状态）】
8.交易失败-对应商城同步状态【4退款完成】 9.同步省侧
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private Integer status;

    /**
     * 最新状态的变更时间
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private Date statusTime;

    /**
     * 订单总金额
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private String totalPrice;

    /**
     * 预约提交类型，1：客户自主填入，2：超时系统自动提交
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private Integer appointmentSubmitType;

    /**
     * 预约人姓名
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private String appointmentName;

    /**
     * 预约人电话
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private String appointmentPhone;

    /**
     * 预约地址
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private String appointmentAddress;

    /**
     * 预约时间
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private Date appointmentTime;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private Date createTime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private Date updateTime;

    /**
     * 省编码
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private String beId;

    /**
     * 区域编码，参考4.2地市编码或4.7区县编码
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private String regionId;

    /**
     * 省份
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private String addr1;

    /**
     * 地市
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private String addr2;

    /**
     * 区县
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private String addr3;

    /**
     * 乡镇
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private String addr4;

    /**
     * 非结构地址
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private String usaddr;

    /**
     * 是否OS内部订单（直接导入的，不和商城交互）
     *
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private Boolean isInner;

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.service_order_id
     *
     * @return the value of supply_chain..after_market_order_2c_info.service_order_id
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public String getServiceOrderId() {
        return serviceOrderId;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withServiceOrderId(String serviceOrderId) {
        this.setServiceOrderId(serviceOrderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.service_order_id
     *
     * @param serviceOrderId the value for supply_chain..after_market_order_2c_info.service_order_id
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setServiceOrderId(String serviceOrderId) {
        this.serviceOrderId = serviceOrderId;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.offering_order_id
     *
     * @return the value of supply_chain..after_market_order_2c_info.offering_order_id
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public String getOfferingOrderId() {
        return offeringOrderId;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withOfferingOrderId(String offeringOrderId) {
        this.setOfferingOrderId(offeringOrderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.offering_order_id
     *
     * @param offeringOrderId the value for supply_chain..after_market_order_2c_info.offering_order_id
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setOfferingOrderId(String offeringOrderId) {
        this.offeringOrderId = offeringOrderId;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.status
     *
     * @return the value of supply_chain..after_market_order_2c_info.status
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public Integer getStatus() {
        return status;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withStatus(Integer status) {
        this.setStatus(status);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.status
     *
     * @param status the value for supply_chain..after_market_order_2c_info.status
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.status_time
     *
     * @return the value of supply_chain..after_market_order_2c_info.status_time
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public Date getStatusTime() {
        return statusTime;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withStatusTime(Date statusTime) {
        this.setStatusTime(statusTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.status_time
     *
     * @param statusTime the value for supply_chain..after_market_order_2c_info.status_time
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setStatusTime(Date statusTime) {
        this.statusTime = statusTime;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.total_price
     *
     * @return the value of supply_chain..after_market_order_2c_info.total_price
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public String getTotalPrice() {
        return totalPrice;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withTotalPrice(String totalPrice) {
        this.setTotalPrice(totalPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.total_price
     *
     * @param totalPrice the value for supply_chain..after_market_order_2c_info.total_price
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setTotalPrice(String totalPrice) {
        this.totalPrice = totalPrice;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.appointment_submit_type
     *
     * @return the value of supply_chain..after_market_order_2c_info.appointment_submit_type
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public Integer getAppointmentSubmitType() {
        return appointmentSubmitType;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withAppointmentSubmitType(Integer appointmentSubmitType) {
        this.setAppointmentSubmitType(appointmentSubmitType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.appointment_submit_type
     *
     * @param appointmentSubmitType the value for supply_chain..after_market_order_2c_info.appointment_submit_type
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setAppointmentSubmitType(Integer appointmentSubmitType) {
        this.appointmentSubmitType = appointmentSubmitType;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.appointment_name
     *
     * @return the value of supply_chain..after_market_order_2c_info.appointment_name
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public String getAppointmentName() {
        return appointmentName;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withAppointmentName(String appointmentName) {
        this.setAppointmentName(appointmentName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.appointment_name
     *
     * @param appointmentName the value for supply_chain..after_market_order_2c_info.appointment_name
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setAppointmentName(String appointmentName) {
        this.appointmentName = appointmentName;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.appointment_phone
     *
     * @return the value of supply_chain..after_market_order_2c_info.appointment_phone
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public String getAppointmentPhone() {
        return appointmentPhone;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withAppointmentPhone(String appointmentPhone) {
        this.setAppointmentPhone(appointmentPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.appointment_phone
     *
     * @param appointmentPhone the value for supply_chain..after_market_order_2c_info.appointment_phone
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setAppointmentPhone(String appointmentPhone) {
        this.appointmentPhone = appointmentPhone;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.appointment_address
     *
     * @return the value of supply_chain..after_market_order_2c_info.appointment_address
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public String getAppointmentAddress() {
        return appointmentAddress;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withAppointmentAddress(String appointmentAddress) {
        this.setAppointmentAddress(appointmentAddress);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.appointment_address
     *
     * @param appointmentAddress the value for supply_chain..after_market_order_2c_info.appointment_address
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setAppointmentAddress(String appointmentAddress) {
        this.appointmentAddress = appointmentAddress;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.appointment_time
     *
     * @return the value of supply_chain..after_market_order_2c_info.appointment_time
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public Date getAppointmentTime() {
        return appointmentTime;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withAppointmentTime(Date appointmentTime) {
        this.setAppointmentTime(appointmentTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.appointment_time
     *
     * @param appointmentTime the value for supply_chain..after_market_order_2c_info.appointment_time
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setAppointmentTime(Date appointmentTime) {
        this.appointmentTime = appointmentTime;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.create_time
     *
     * @return the value of supply_chain..after_market_order_2c_info.create_time
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.create_time
     *
     * @param createTime the value for supply_chain..after_market_order_2c_info.create_time
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.update_time
     *
     * @return the value of supply_chain..after_market_order_2c_info.update_time
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.update_time
     *
     * @param updateTime the value for supply_chain..after_market_order_2c_info.update_time
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.be_id
     *
     * @return the value of supply_chain..after_market_order_2c_info.be_id
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public String getBeId() {
        return beId;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withBeId(String beId) {
        this.setBeId(beId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.be_id
     *
     * @param beId the value for supply_chain..after_market_order_2c_info.be_id
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setBeId(String beId) {
        this.beId = beId;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.region_id
     *
     * @return the value of supply_chain..after_market_order_2c_info.region_id
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public String getRegionId() {
        return regionId;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withRegionId(String regionId) {
        this.setRegionId(regionId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.region_id
     *
     * @param regionId the value for supply_chain..after_market_order_2c_info.region_id
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.addr1
     *
     * @return the value of supply_chain..after_market_order_2c_info.addr1
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public String getAddr1() {
        return addr1;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withAddr1(String addr1) {
        this.setAddr1(addr1);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.addr1
     *
     * @param addr1 the value for supply_chain..after_market_order_2c_info.addr1
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setAddr1(String addr1) {
        this.addr1 = addr1;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.addr2
     *
     * @return the value of supply_chain..after_market_order_2c_info.addr2
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public String getAddr2() {
        return addr2;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withAddr2(String addr2) {
        this.setAddr2(addr2);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.addr2
     *
     * @param addr2 the value for supply_chain..after_market_order_2c_info.addr2
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setAddr2(String addr2) {
        this.addr2 = addr2;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.addr3
     *
     * @return the value of supply_chain..after_market_order_2c_info.addr3
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public String getAddr3() {
        return addr3;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withAddr3(String addr3) {
        this.setAddr3(addr3);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.addr3
     *
     * @param addr3 the value for supply_chain..after_market_order_2c_info.addr3
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setAddr3(String addr3) {
        this.addr3 = addr3;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.addr4
     *
     * @return the value of supply_chain..after_market_order_2c_info.addr4
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public String getAddr4() {
        return addr4;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withAddr4(String addr4) {
        this.setAddr4(addr4);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.addr4
     *
     * @param addr4 the value for supply_chain..after_market_order_2c_info.addr4
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setAddr4(String addr4) {
        this.addr4 = addr4;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.usaddr
     *
     * @return the value of supply_chain..after_market_order_2c_info.usaddr
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public String getUsaddr() {
        return usaddr;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withUsaddr(String usaddr) {
        this.setUsaddr(usaddr);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.usaddr
     *
     * @param usaddr the value for supply_chain..after_market_order_2c_info.usaddr
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setUsaddr(String usaddr) {
        this.usaddr = usaddr;
    }

    /**
     * This method returns the value of the database column supply_chain..after_market_order_2c_info.is_inner
     *
     * @return the value of supply_chain..after_market_order_2c_info.is_inner
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public Boolean getIsInner() {
        return isInner;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public AfterMarketOrder2cInfo withIsInner(Boolean isInner) {
        this.setIsInner(isInner);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..after_market_order_2c_info.is_inner
     *
     * @param isInner the value for supply_chain..after_market_order_2c_info.is_inner
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public void setIsInner(Boolean isInner) {
        this.isInner = isInner;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", serviceOrderId=").append(serviceOrderId);
        sb.append(", offeringOrderId=").append(offeringOrderId);
        sb.append(", status=").append(status);
        sb.append(", statusTime=").append(statusTime);
        sb.append(", totalPrice=").append(totalPrice);
        sb.append(", appointmentSubmitType=").append(appointmentSubmitType);
        sb.append(", appointmentName=").append(appointmentName);
        sb.append(", appointmentPhone=").append(appointmentPhone);
        sb.append(", appointmentAddress=").append(appointmentAddress);
        sb.append(", appointmentTime=").append(appointmentTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", beId=").append(beId);
        sb.append(", regionId=").append(regionId);
        sb.append(", addr1=").append(addr1);
        sb.append(", addr2=").append(addr2);
        sb.append(", addr3=").append(addr3);
        sb.append(", addr4=").append(addr4);
        sb.append(", usaddr=").append(usaddr);
        sb.append(", isInner=").append(isInner);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        AfterMarketOrder2cInfo other = (AfterMarketOrder2cInfo) that;
        return (this.getServiceOrderId() == null ? other.getServiceOrderId() == null : this.getServiceOrderId().equals(other.getServiceOrderId()))
            && (this.getOfferingOrderId() == null ? other.getOfferingOrderId() == null : this.getOfferingOrderId().equals(other.getOfferingOrderId()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getStatusTime() == null ? other.getStatusTime() == null : this.getStatusTime().equals(other.getStatusTime()))
            && (this.getTotalPrice() == null ? other.getTotalPrice() == null : this.getTotalPrice().equals(other.getTotalPrice()))
            && (this.getAppointmentSubmitType() == null ? other.getAppointmentSubmitType() == null : this.getAppointmentSubmitType().equals(other.getAppointmentSubmitType()))
            && (this.getAppointmentName() == null ? other.getAppointmentName() == null : this.getAppointmentName().equals(other.getAppointmentName()))
            && (this.getAppointmentPhone() == null ? other.getAppointmentPhone() == null : this.getAppointmentPhone().equals(other.getAppointmentPhone()))
            && (this.getAppointmentAddress() == null ? other.getAppointmentAddress() == null : this.getAppointmentAddress().equals(other.getAppointmentAddress()))
            && (this.getAppointmentTime() == null ? other.getAppointmentTime() == null : this.getAppointmentTime().equals(other.getAppointmentTime()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getBeId() == null ? other.getBeId() == null : this.getBeId().equals(other.getBeId()))
            && (this.getRegionId() == null ? other.getRegionId() == null : this.getRegionId().equals(other.getRegionId()))
            && (this.getAddr1() == null ? other.getAddr1() == null : this.getAddr1().equals(other.getAddr1()))
            && (this.getAddr2() == null ? other.getAddr2() == null : this.getAddr2().equals(other.getAddr2()))
            && (this.getAddr3() == null ? other.getAddr3() == null : this.getAddr3().equals(other.getAddr3()))
            && (this.getAddr4() == null ? other.getAddr4() == null : this.getAddr4().equals(other.getAddr4()))
            && (this.getUsaddr() == null ? other.getUsaddr() == null : this.getUsaddr().equals(other.getUsaddr()))
            && (this.getIsInner() == null ? other.getIsInner() == null : this.getIsInner().equals(other.getIsInner()));
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getServiceOrderId() == null) ? 0 : getServiceOrderId().hashCode());
        result = prime * result + ((getOfferingOrderId() == null) ? 0 : getOfferingOrderId().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getStatusTime() == null) ? 0 : getStatusTime().hashCode());
        result = prime * result + ((getTotalPrice() == null) ? 0 : getTotalPrice().hashCode());
        result = prime * result + ((getAppointmentSubmitType() == null) ? 0 : getAppointmentSubmitType().hashCode());
        result = prime * result + ((getAppointmentName() == null) ? 0 : getAppointmentName().hashCode());
        result = prime * result + ((getAppointmentPhone() == null) ? 0 : getAppointmentPhone().hashCode());
        result = prime * result + ((getAppointmentAddress() == null) ? 0 : getAppointmentAddress().hashCode());
        result = prime * result + ((getAppointmentTime() == null) ? 0 : getAppointmentTime().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getBeId() == null) ? 0 : getBeId().hashCode());
        result = prime * result + ((getRegionId() == null) ? 0 : getRegionId().hashCode());
        result = prime * result + ((getAddr1() == null) ? 0 : getAddr1().hashCode());
        result = prime * result + ((getAddr2() == null) ? 0 : getAddr2().hashCode());
        result = prime * result + ((getAddr3() == null) ? 0 : getAddr3().hashCode());
        result = prime * result + ((getAddr4() == null) ? 0 : getAddr4().hashCode());
        result = prime * result + ((getUsaddr() == null) ? 0 : getUsaddr().hashCode());
        result = prime * result + ((getIsInner() == null) ? 0 : getIsInner().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Mon May 26 16:27:52 CST 2025
     */
    public enum Column {
        serviceOrderId("service_order_id", "serviceOrderId", "VARCHAR", false),
        offeringOrderId("offering_order_id", "offeringOrderId", "VARCHAR", false),
        status("status", "status", "INTEGER", false),
        statusTime("status_time", "statusTime", "TIMESTAMP", false),
        totalPrice("total_price", "totalPrice", "VARCHAR", false),
        appointmentSubmitType("appointment_submit_type", "appointmentSubmitType", "INTEGER", false),
        appointmentName("appointment_name", "appointmentName", "VARCHAR", false),
        appointmentPhone("appointment_phone", "appointmentPhone", "VARCHAR", false),
        appointmentAddress("appointment_address", "appointmentAddress", "VARCHAR", false),
        appointmentTime("appointment_time", "appointmentTime", "TIMESTAMP", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        beId("be_id", "beId", "VARCHAR", false),
        regionId("region_id", "regionId", "VARCHAR", false),
        addr1("addr1", "addr1", "VARCHAR", false),
        addr2("addr2", "addr2", "VARCHAR", false),
        addr3("addr3", "addr3", "VARCHAR", false),
        addr4("addr4", "addr4", "VARCHAR", false),
        usaddr("usaddr", "usaddr", "VARCHAR", false),
        isInner("is_inner", "isInner", "BIT", false);

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon May 26 16:27:52 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}