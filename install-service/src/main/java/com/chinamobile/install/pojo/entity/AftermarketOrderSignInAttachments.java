package com.chinamobile.install.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 装维售后服务签到附件表
 *
 * <AUTHOR>
public class AftermarketOrderSignInAttachments implements Serializable {
    /**
     *
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    private String id;

    /**
     * 售后服务订单id
     *
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    private String serviceOrderId;

    /**
     * 附件文件名
     *
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    private String fileName;

    /**
     * 附件文件在对象存储的key
     *
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    private String fileKey;

    /**
     * 附件文件下载地址
     *
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    private String fileUrl;

    /**
     *
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    private Date createTime;

    /**
     *
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    private Date updateTime;

    /**
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..aftermarket_order_sign_in_attachments.id
     *
     * @return the value of supply_chain..aftermarket_order_sign_in_attachments.id
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public AftermarketOrderSignInAttachments withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..aftermarket_order_sign_in_attachments.id
     *
     * @param id the value for supply_chain..aftermarket_order_sign_in_attachments.id
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..aftermarket_order_sign_in_attachments.service_order_id
     *
     * @return the value of supply_chain..aftermarket_order_sign_in_attachments.service_order_id
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public String getServiceOrderId() {
        return serviceOrderId;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public AftermarketOrderSignInAttachments withServiceOrderId(String serviceOrderId) {
        this.setServiceOrderId(serviceOrderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..aftermarket_order_sign_in_attachments.service_order_id
     *
     * @param serviceOrderId the value for supply_chain..aftermarket_order_sign_in_attachments.service_order_id
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public void setServiceOrderId(String serviceOrderId) {
        this.serviceOrderId = serviceOrderId;
    }

    /**
     * This method returns the value of the database column supply_chain..aftermarket_order_sign_in_attachments.file_name
     *
     * @return the value of supply_chain..aftermarket_order_sign_in_attachments.file_name
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public String getFileName() {
        return fileName;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public AftermarketOrderSignInAttachments withFileName(String fileName) {
        this.setFileName(fileName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..aftermarket_order_sign_in_attachments.file_name
     *
     * @param fileName the value for supply_chain..aftermarket_order_sign_in_attachments.file_name
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * This method returns the value of the database column supply_chain..aftermarket_order_sign_in_attachments.file_key
     *
     * @return the value of supply_chain..aftermarket_order_sign_in_attachments.file_key
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public String getFileKey() {
        return fileKey;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public AftermarketOrderSignInAttachments withFileKey(String fileKey) {
        this.setFileKey(fileKey);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..aftermarket_order_sign_in_attachments.file_key
     *
     * @param fileKey the value for supply_chain..aftermarket_order_sign_in_attachments.file_key
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public void setFileKey(String fileKey) {
        this.fileKey = fileKey;
    }

    /**
     * This method returns the value of the database column supply_chain..aftermarket_order_sign_in_attachments.file_url
     *
     * @return the value of supply_chain..aftermarket_order_sign_in_attachments.file_url
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public String getFileUrl() {
        return fileUrl;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public AftermarketOrderSignInAttachments withFileUrl(String fileUrl) {
        this.setFileUrl(fileUrl);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..aftermarket_order_sign_in_attachments.file_url
     *
     * @param fileUrl the value for supply_chain..aftermarket_order_sign_in_attachments.file_url
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    /**
     * This method returns the value of the database column supply_chain..aftermarket_order_sign_in_attachments.create_time
     *
     * @return the value of supply_chain..aftermarket_order_sign_in_attachments.create_time
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public AftermarketOrderSignInAttachments withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..aftermarket_order_sign_in_attachments.create_time
     *
     * @param createTime the value for supply_chain..aftermarket_order_sign_in_attachments.create_time
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..aftermarket_order_sign_in_attachments.update_time
     *
     * @return the value of supply_chain..aftermarket_order_sign_in_attachments.update_time
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public AftermarketOrderSignInAttachments withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..aftermarket_order_sign_in_attachments.update_time
     *
     * @param updateTime the value for supply_chain..aftermarket_order_sign_in_attachments.update_time
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", serviceOrderId=").append(serviceOrderId);
        sb.append(", fileName=").append(fileName);
        sb.append(", fileKey=").append(fileKey);
        sb.append(", fileUrl=").append(fileUrl);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        AftermarketOrderSignInAttachments other = (AftermarketOrderSignInAttachments) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getServiceOrderId() == null ? other.getServiceOrderId() == null : this.getServiceOrderId().equals(other.getServiceOrderId()))
            && (this.getFileName() == null ? other.getFileName() == null : this.getFileName().equals(other.getFileName()))
            && (this.getFileKey() == null ? other.getFileKey() == null : this.getFileKey().equals(other.getFileKey()))
            && (this.getFileUrl() == null ? other.getFileUrl() == null : this.getFileUrl().equals(other.getFileUrl()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getServiceOrderId() == null) ? 0 : getServiceOrderId().hashCode());
        result = prime * result + ((getFileName() == null) ? 0 : getFileName().hashCode());
        result = prime * result + ((getFileKey() == null) ? 0 : getFileKey().hashCode());
        result = prime * result + ((getFileUrl() == null) ? 0 : getFileUrl().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Thu May 29 15:20:30 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        serviceOrderId("service_order_id", "serviceOrderId", "VARCHAR", false),
        fileName("file_name", "fileName", "VARCHAR", false),
        fileKey("file_key", "fileKey", "VARCHAR", false),
        fileUrl("file_url", "fileUrl", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Thu May 29 15:20:30 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu May 29 15:20:30 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu May 29 15:20:30 CST 2025
         */
        private final String column;

        /**
         *
         * @mbg.generated Thu May 29 15:20:30 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Thu May 29 15:20:30 CST 2025
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Thu May 29 15:20:30 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Thu May 29 15:20:30 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu May 29 15:20:30 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu May 29 15:20:30 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Thu May 29 15:20:30 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Thu May 29 15:20:30 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Thu May 29 15:20:30 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Thu May 29 15:20:30 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Thu May 29 15:20:30 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Thu May 29 15:20:30 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Thu May 29 15:20:30 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Thu May 29 15:20:30 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}