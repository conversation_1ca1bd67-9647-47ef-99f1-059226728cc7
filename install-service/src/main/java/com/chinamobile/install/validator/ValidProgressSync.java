package com.chinamobile.install.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 进度同步请求参数验证注解
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ProgressSyncValidator.class)
@Documented
public @interface ValidProgressSync {

    String message() default "进度同步请求参数验证失败";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
