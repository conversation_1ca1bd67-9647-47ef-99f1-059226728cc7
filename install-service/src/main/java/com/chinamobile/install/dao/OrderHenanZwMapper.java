package com.chinamobile.install.dao;

import com.chinamobile.install.pojo.entity.OrderHenanZw;
import com.chinamobile.install.pojo.entity.OrderHenanZwExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface OrderHenanZwMapper {
    long countByExample(OrderHenanZwExample example);

    int deleteByExample(OrderHenanZwExample example);

    int deleteByPrimaryKey(String sheetNo);

    int insert(OrderHenanZw record);

    int insertSelective(OrderHenanZw record);

    List<OrderHenanZw> selectByExample(OrderHenanZwExample example);

    OrderHenanZw selectByPrimaryKey(String sheetNo);

    int updateByExampleSelective(@Param("record") OrderHenanZw record, @Param("example") OrderHenanZwExample example);

    int updateByExample(@Param("record") OrderHenanZw record, @Param("example") OrderHenanZwExample example);

    int updateByPrimaryKeySelective(OrderHenanZw record);

    int updateByPrimaryKey(OrderHenanZw record);

    int batchInsert(@Param("list") List<OrderHenanZw> list);

    int batchInsertSelective(@Param("list") List<OrderHenanZw> list, @Param("selective") OrderHenanZw.Column ... selective);
}