package com.chinamobile.install.dao;

import com.chinamobile.install.pojo.entity.HenanZwCity;
import com.chinamobile.install.pojo.entity.HenanZwCityExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface HenanZwCityMapper {
    long countByExample(HenanZwCityExample example);

    int deleteByExample(HenanZwCityExample example);

    int insert(HenanZwCity record);

    int insertSelective(HenanZwCity record);

    List<HenanZwCity> selectByExample(HenanZwCityExample example);

    int updateByExampleSelective(@Param("record") HenanZwCity record, @Param("example") HenanZwCityExample example);

    int updateByExample(@Param("record") HenanZwCity record, @Param("example") HenanZwCityExample example);

    int batchInsert(@Param("list") List<HenanZwCity> list);

    int batchInsertSelective(@Param("list") List<HenanZwCity> list, @Param("selective") HenanZwCity.Column ... selective);
}