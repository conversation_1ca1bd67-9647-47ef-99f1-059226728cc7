package com.chinamobile.install.response;

import lombok.Data;

import java.util.Collections;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/24 10:40
 * @description TODO
 */
@Data
public class BaseZwResponse<T> {


    private String state;

    private T body;

    private String errorCode;

    private String errorMessage;

    private String requestId;

    public static BaseZwResponse success() {
        return success(UUID.randomUUID().toString());
    }

    public static BaseZwResponse success(String requestId) {
        return success(Collections.EMPTY_MAP, requestId);
    }

    public static BaseZwResponse success(Object body, String requestId) {
        BaseZwResponse baseZwResponse = new BaseZwResponse();
        baseZwResponse.setState(StateCode.OK);
        baseZwResponse.setBody(body);
        baseZwResponse.setRequestId(requestId);
        return baseZwResponse;
    }

    public static BaseZwResponse error(String errorCode, String errorMessage) {
        return error(errorCode, errorMessage, UUID.randomUUID().toString());
    }

    public static BaseZwResponse error(String errorCode, String errorMessage, String requestId) {
        BaseZwResponse baseZwResponse = new BaseZwResponse();
        baseZwResponse.setState(StateCode.ERROR);
        baseZwResponse.setErrorCode(errorCode);
        baseZwResponse.setErrorMessage(errorMessage);
        baseZwResponse.setRequestId(requestId);
        return baseZwResponse;
    }

    public static class StateCode {
        public static final String OK = "OK";
        public static final String ERROR = "ERROR";
        public static final String EXCEPTION = "EXCEPTION";
        public static final String FORBIDDEN = "FORBIDDEN";
    }

    public static class ErrorCode {
        public static final String INTERNAL_SERVER_EXCEPTION = "INTERNAL_SERVER_EXCEPTION";
        public static final String AUTH_EXCEPTION = "AUTH_EXCEPTION";
        public static final String BAD_REQUEST_EXCEPTION = "BAD_REQUEST_EXCEPTION";
        public static final String VALIDATE_EXCEPTION = "VALIDATE_EXCEPTION";
        public static final String FILE_NOT_EXIST_EXCEPTION = "FILE_NOT_EXIST_EXCEPTION";
        public static final String FILE_UPLOAD_EXCEPTION = "FILE_UPLOAD_EXCEPTION";
    }

}
