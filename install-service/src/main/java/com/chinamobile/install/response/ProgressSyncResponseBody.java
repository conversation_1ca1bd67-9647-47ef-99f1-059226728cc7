package com.chinamobile.install.response;

import lombok.Data;

/**
 * 单进度接口（透明化）响应体
 *
 * <AUTHOR>
 * @date 2024/01/01
 */
@Data
public class ProgressSyncResponseBody {

    /**
     * 状态码
     * 00000-成功，00001-失败
     */
    private String stateCode;

    /**
     * 成功失败消息
     * 成功；[失败原因描述]；
     */
    private String message;

    public static ProgressSyncResponseBody success() {
        ProgressSyncResponseBody body = new ProgressSyncResponseBody();
        body.setStateCode("00000");
        body.setMessage("成功");
        return body;
    }

    public static ProgressSyncResponseBody success(String message) {
        ProgressSyncResponseBody body = new ProgressSyncResponseBody();
        body.setStateCode("00000");
        body.setMessage(message);
        return body;
    }

    public static ProgressSyncResponseBody error(String message) {
        ProgressSyncResponseBody body = new ProgressSyncResponseBody();
        body.setStateCode("00001");
        body.setMessage(message);
        return body;
    }
}
