package com.chinamobile.install.exception;


import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.ExcepStatus;

import static com.chinamobile.iot.sc.exceptions.ExcepStatus.createInstance;

/**
 * @Author: YSC
 * @Date: 2021/11/10 15:54
 * @Description: 异常返回映射
 */
public class B2BConstant extends BaseErrorConstant {
    private static final  String PREF = "40";

    /**
     * 非独立履约联系人
     */
    public static final ExcepStatus PHONE_ERROR = createInstance(PREF+"300", "手机号格式有误！");
    public static final ExcepStatus EMAIL_FORMAT_ERROR = createInstance(PREF+"301", "邮箱格式有误！");
    public static final ExcepStatus NAME_FORMAT_ERROR = createInstance(PREF+"302", "姓名必填，2-6个汉字");
    public static final ExcepStatus COMPANY_ERROR = createInstance(PREF+"303", "所属公司必填，2-15个汉字！");
    public static final ExcepStatus CONTACT_IS_NULL = createInstance(PREF+"304", "非独立履约联系人不存在！");

    public static final ExcepStatus HANAN_ZW_ORDER_NO_EMPTY = createInstance(PREF+"305", "订单号为空！");
}
