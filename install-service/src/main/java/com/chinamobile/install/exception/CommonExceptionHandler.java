package com.chinamobile.install.exception;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.exceptions.ExceptionEnums;
import com.chinamobile.iot.sc.exceptions.ResponseCode;
import com.chinamobile.iot.sc.util.CommonResponseUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.sql.SQLException;



@RestControllerAdvice
@Slf4j
public class CommonExceptionHandler {


    /**
     * 验证字段时按照自定义返回格式处理
     *
     * @param exception
     * @return
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    public CommonResponseUtils handleValidException(MethodArgumentNotValidException exception) {
        String defaultMessage = exception.getBindingResult().getFieldError().getDefaultMessage();
        return returnError(defaultMessage);
    }

    /**
     * 验证非@RequestBody
     *
     * @param exception
     * @return
     */
    @ExceptionHandler(value = BindException.class)
    public CommonResponseUtils handleBindException(BindException exception) {
        FieldError fieldError =  exception.getBindingResult().getFieldError();
        String field = fieldError.getField();
        String defaultMessage = fieldError.getDefaultMessage();
        return returnError(field+defaultMessage);
    }

    //自定义抛出异常
    @ExceptionHandler(CommonException.class)
    public CommonResponseUtils handleException(CommonException e) {
        ExceptionEnums enums = e.getExceptionEnums();
        return CommonResponseUtils.error(enums.getCode(), enums.getMsg());
    }
    @ExceptionHandler(BusinessException.class)
    public BaseAnswer<Void> handBusinessException(BusinessException ex){
        return new BaseAnswer<>().setStatus(ex.getStatus());
    }

    @ExceptionHandler(SQLException.class)
    public CommonResponseUtils handSQLException(SQLException ex){
        log.error("系统异常:{}", ex);
        return CommonResponseUtils.error(ResponseCode.FAILURE, ex.getSQLState());
    }

    /**
     * 捕获授权错误
     *
     * @param e
     * @return
     */
//    @ExceptionHandler(AccessDeniedException.class)
//    public CommonResponseUtils handleAccessDeniedException(AccessDeniedException e) {
//        if (e.getMessage().equals("不允许访问")) {
//            return CommonResponseUtils.error(AUTHOR_NEED, "您没有权限进行该操作");
//        }
//
//        log.error("授权异常:{}", e);
//        return CommonResponseUtils.error(FAILURE, e.getMessage());
//    }


    //全局异常捕捉
    @ExceptionHandler(Exception.class)
    public CommonResponseUtils errorHandler(Exception e) {
        log.error("系统异常:{}", e);
        return CommonResponseUtils.error(ResponseCode.FAILURE, e.getMessage());
    }

    private CommonResponseUtils returnError(String defaultMessage) {
        return CommonResponseUtils.error(ResponseCode.PARAM_INVALID, defaultMessage);
    }

}
