package com.chinamobile.iot.sc.service;

import com.alibaba.fastjson.JSON;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.entity.iot.CreateOperateRecordParam;
import com.chinamobile.iot.sc.enums.log.ModuleEnum;
import com.chinamobile.iot.sc.enums.log.RoleEnum;
import com.chinamobile.iot.sc.feign.IotFeignClient;
import com.chinamobile.iot.sc.feign.UserFeignClient;
import com.chinamobile.iot.sc.mode.Data4User;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.util.LogRoleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.regex.Pattern;

import static com.chinamobile.iot.sc.common.BaseConstant.H5_LOGIN;

/**
 * 订单管理模块日志记录service
 * */
@Service
@Slf4j
public class LogService {
    @Resource
    private IotFeignClient iotFeignClient;

    @Resource
    private UserFeignClient userFeignClient;

    @Resource
    private RedisTemplate redisTemplate;

    // \b 是单词边界(连着的两个(字母字符 与 非字母字符) 之间的逻辑上的间隔),
    // 字符串在编译时会被转码一次,所以是 "\\b"
    // \B 是单词内部逻辑间隔(连着的两个字母字符之间的逻辑上的间隔)
    private static final String phoneReg = "\\b(ip(hone|od)|android|opera m(ob|in)i"
            +"|windows (phone|ce)|blackberry"
            +"|s(ymbian|eries60|amsung)|p(laybook|alm|rofile/midp"
            +"|laystation portable)|nokia|fennec|htc[-_]"
            +"|mobile|up.browser|[1-4][0-9]{2}x[1-4][0-9]{2})\\b";

    private static final String tabletReg = "\\b(ipad|tablet|(Nexus 7)|up.browser|[1-4][0-9]{2}x[1-4][0-9]{2})\\b";

    //移动设备正则匹配：手机端、平板
    private static final Pattern phonePat = Pattern.compile(phoneReg, Pattern.CASE_INSENSITIVE);
    private static final Pattern tabletPat = Pattern.compile(tabletReg, Pattern.CASE_INSENSITIVE);

    /**
     * 检测是否是移动设备访问
     *
     * @param userAgent 浏览器标识
     * @return true:移动设备接入，false:pc端接入
     */
    public static boolean isMobile(String userAgent){
        if(null == userAgent){
            userAgent = "";
        }
        return phonePat.matcher(userAgent).find() || tabletPat.matcher(userAgent).find();
    }

    public static String replaceWithStar(String phone) {
        if (StringUtils.isBlank(phone) || phone.length() != 11) {
            return StringUtils.defaultString(phone);
        } else {
            return phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        }
    }

    /**
     * 名称脱敏处理
     * @param custName
     * @return
     */
    public static String custNameDesensitization(String custName) {
        // 规则说明：
        // 姓名：字符长度小于5位；企业名称：字符长度大于等于5位。
        // 姓名规则
        // 规则一：1个字则不脱敏，如"张"-->"张"
        // 规则二：2个字则脱敏第二个字，如"张三"-->"张*"
        // 规则三：3个字则脱敏第二个字，如"张三丰"-->"张*丰"
        // 规则四：4个字则脱敏中间两个字，如"易烊千玺"-->"易**玺"
        // 企业名称规则：
        // 从第4位开始隐藏，最多隐藏6位。

        if (StringUtils.isNotEmpty(custName)) {
            char[] chars = custName.toCharArray();
            if (chars.length < 5) {// 表示姓名
                if (chars.length > 1) {
                    StringBuffer sb = new StringBuffer();
                    for (int i = 0; i < chars.length - 2; i++) {
                        sb.append("*");
                    }
                    custName = custName.replaceAll(custName.substring(1, chars.length - 1), sb.toString());
                }
            } else {// 企业名称
                int start = 4;
                // 第一部分
                String str1 = custName.substring(0, start);
                // 第二部分
                String str2 = "";
                if (chars.length == 5) {
                    str2 = "*";
                } else if (chars.length == 6) {
                    str2 = "**";
                } else if (chars.length == 7) {
                    str2 = "***";
                } else if (chars.length == 8) {
                    str2 = "****";
                } else if (chars.length == 9) {
                    str2 = "*****";
                } else {
                    str2 = "******";
                }
                // 通过计算得到第三部分需要从第几个字符截取
                int subIndex = start + str2.length();
                // 第三部分
                String str3 = custName.substring(subIndex);
                StringBuffer sb = new StringBuffer();
                sb.append(str1);
                sb.append(str2);
                sb.append(str3);
                custName = sb.toString();
            }
        }
        return custName;
    }


    /**
     * 对于没有校验token的接口，userId不存在request header里面，需要手动将操作人userId传入
     */
    public void recordOperateLog(Integer module, Integer subModule,String content,String userId) {
        log.debug("操作日志记录,{}-{}-{} -{},",ModuleEnum.moduleName(module),
                ModuleEnum.subModuleName(module,subModule),content,userId);
        doRecordOperateLog(module, subModule, content,userId,0);
    }

    public void recordOperateLog(Integer module, Integer subModule,String content) {
        log.debug("操作日志记录,{}-{}-{},",ModuleEnum.moduleName(module),
                ModuleEnum.subModuleName(module,subModule),content);
        log.info("hwf test recordOperateLog no userid mode-subModule-content: {}-{}-{}",ModuleEnum.moduleName(module),ModuleEnum.subModuleName(module,subModule),content);
        doRecordOperateLog(module, subModule, content,null,0);
    }
    public void recordOperateLog(Integer module, Integer subModule,String content, Integer result, String failReason) {
        log.debug("操作日志记录,{}-{}-{},",ModuleEnum.moduleName(module),
                ModuleEnum.subModuleName(module,subModule),content);
        log.info("hwf test recordOperateLog no userid mode-subModule-content: {}-{}-{}",ModuleEnum.moduleName(module),ModuleEnum.subModuleName(module,subModule),content);
        doRecordOperateLog(module, subModule, content,null,0,result,failReason);
    }
    public void recordOperateLog(Integer module,
                                 Integer subModule,
                                 String content,
                                 String userId,
                                 Integer dataFrom) {
        log.debug("操作日志记录,{}-{}-{} -{},",ModuleEnum.moduleName(module),
                ModuleEnum.subModuleName(module,subModule),content,userId);
        doRecordOperateLog(module, subModule, content,userId,dataFrom);
    }
    //新日志需求，添加结果和失败原因
    public void recordOperateLog(Integer module,
                                 Integer subModule,
                                 String content,
                                 String userId,
                                 Integer dataFrom,
                                 Integer result,
                                 String failReason
                                 ) {
        log.debug("操作日志记录,{}-{}-{} -{}-{}-{},",ModuleEnum.moduleName(module),
                ModuleEnum.subModuleName(module,subModule),content,userId,result,failReason);
        doRecordOperateLog(module, subModule, content,userId,dataFrom,result,failReason);
    }
    //无法获取用户信息，添加的日志
    public void recordOperateLog(Integer module,
                                 Integer subModule,
                                 String content,
                                 Integer dataFrom,
                                 Integer result,
                                 String failReason,
                                 String account
    ) {
        log.debug("操作日志记录,{}-{}-{} -{}-{}-{},",ModuleEnum.moduleName(module),
                ModuleEnum.subModuleName(module,subModule),content,account,result,failReason);
        doRecordOperateLog(module, subModule, content,dataFrom,result,failReason,account);
    }
    public void recordOperateLogAsync(Integer module,
                                      Integer subModule,
                                      String content,
                                      String userId,
                                      String ip) {
        log.debug("操作日志记录,{}-{}-{},",ModuleEnum.moduleName(module),
                ModuleEnum.subModuleName(module,subModule),content);
        doRecordOperateLogAsync(module, subModule, content,userId,ip,0);
    }
    //新日志添加字段
    public void recordOperateLogAsync(Integer module,
                                      Integer subModule,
                                      String content,
                                      String userId,
                                      String ip,
                                      Integer result,
                                      String failReason
                                      ) {
        log.debug("操作日志记录,{}-{}-{},",ModuleEnum.moduleName(module),
                ModuleEnum.subModuleName(module,subModule),content);
        doRecordOperateLogAsync(module, subModule, content,userId,ip,0,result,failReason);
    }

    public void recordOperateLogAsync(Integer module,
                                      Integer subModule,
                                      String content,
                                      String userId,
                                      String ip,
                                      Integer dataFrom) {
        log.debug("操作日志记录,{}-{}-{},",ModuleEnum.moduleName(module),
                ModuleEnum.subModuleName(module,subModule),content);
        doRecordOperateLogAsync(module, subModule, content,userId,ip,dataFrom);
    }

    /**
     * 对于有校验token的接口，只传入日志内容及模块信息即可
     */
    private void doRecordOperateLog(Integer module,
                                    Integer subModule,
                                    String content,
                                    String userId,
                                    Integer dataFrom) {
        log.info("hwf test doRecordOperateLog userId = {}",userId);
        try {
            HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
            //获取用户基本信息
            userId = StringUtils.isNotEmpty(userId)? userId: request.getHeader(Constant.HEADER_KEY_USER_ID);
            log.info("hwf test userId = {}",userId);
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoByIdNoLog(userId);
            if (dataFrom == 0) {
                dataFrom = isMobile(request.getHeader("User-Agent")) ? 1 : 0;
            }
            CreateOperateRecordParam param = new CreateOperateRecordParam();
            if (data4UserBaseAnswer != null && data4UserBaseAnswer.getData() != null) {
                Data4User data4User = data4UserBaseAnswer.getData();
                if(LogRoleUtil.getLogRole(StringUtils.isNotEmpty(data4User.getRoleType())? data4User.getRoleType():H5_LOGIN)!= null){
                    param.setRole(LogRoleUtil.getLogRole(StringUtils.isNotEmpty(data4User.getRoleType())? data4User.getRoleType():H5_LOGIN));
                }
                if(StringUtils.isNotEmpty(data4User.getRoleName())){
                    param.setRoleName(data4User.getRoleName());
                }

                //目前OS系统中，除了系统管理员超管，其他都是有手机号的
                if (RoleEnum.SYSTEM_ADMIN.code.equals(param.getRole()) || RoleEnum.KANBAN_SUPER_ADMIN.code.equals(param.getRole())) {
                    //产品确认固定位"admin"
                    param.setOperatorAccount("systemAdmin");
                } else {
                    param.setOperatorName(data4User.getName());
                    param.setOperatorAccount(replaceWithStar(data4User.getPhone()));
                }
            }else{
                param.setOperatorAccount("系统默认");
            }
            param.setTime(new Date());
            param.setIp(StringUtils.isNotEmpty(request.getHeader(Constant.IP))? request.getHeader(Constant.IP) : "127.0.0.1");
            param.setContent(content);
            param.setModule(module);
            param.setSubModule(subModule);
            param.setDataFrom(dataFrom);

            try{
                LoginIfo4Redis loginInfo4Redis = null;
                if (StringUtils.isNotBlank(userId)) {
                    loginInfo4Redis = (LoginIfo4Redis) redisTemplate.opsForValue().get(Constant.REDIS_KEY_USER_TOKEN + userId);
                    param.setMainAcctId(loginInfo4Redis.getMainAcctId());
                }
            }catch (Exception e){
                log.error("获取MainAcctId失败: {}", e.getMessage());
            }

            log.debug("操作日志记录,{}-{}-json:{},",ModuleEnum.moduleName(module),
                    ModuleEnum.subModuleName(module,subModule), JSON.toJSONString(param));
            iotFeignClient.createLog(param);
        } catch (Exception exception) {
            log.error("操作日志记录失败,{}-{}-{},原因：{}",ModuleEnum.moduleName(module),
                    ModuleEnum.subModuleName(module,subModule),content,exception.getMessage());
            exception.printStackTrace();
        }
    }

    private void doRecordOperateLog(Integer module,
                                    Integer subModule,
                                    String content,
                                    String userId,
                                    Integer dataFrom,
                                    Integer result,
                                    String failReason) {
        log.info("hwf test doRecordOperateLog userId = {}",userId);
        try {
            HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
            userId = StringUtils.isNotEmpty(userId)? userId: request.getHeader(Constant.HEADER_KEY_USER_ID);
            log.info("hwf test userId = {}",userId);
            BaseAnswer<Data4User> data4UserBaseAnswer = userFeignClient.userInfoByIdNoLog(userId);
            if (dataFrom == 0) {
                dataFrom = isMobile(request.getHeader("User-Agent")) ? 1 : 0;
            }
            CreateOperateRecordParam param = new CreateOperateRecordParam();
            if (data4UserBaseAnswer != null && data4UserBaseAnswer.getData() != null) {
                Data4User data4User = data4UserBaseAnswer.getData();
                if(LogRoleUtil.getLogRole(StringUtils.isNotEmpty(data4User.getRoleType())? data4User.getRoleType():H5_LOGIN)!= null){
                    param.setRole(LogRoleUtil.getLogRole(StringUtils.isNotEmpty(data4User.getRoleType())? data4User.getRoleType():H5_LOGIN));
                }
                if(StringUtils.isNotEmpty(data4User.getRoleName())){
                    param.setRoleName(data4User.getRoleName());
                }

                if (RoleEnum.SYSTEM_ADMIN.code.equals(param.getRole()) || RoleEnum.KANBAN_SUPER_ADMIN.code.equals(param.getRole())) {
                    param.setOperatorAccount("systemAdmin");
                } else {
                    param.setOperatorName(data4User.getName());
                    param.setOperatorAccount(replaceWithStar(data4User.getPhone()));
                }
            }else{
                param.setOperatorAccount("系统默认");
            }
            param.setTime(new Date());
            param.setIp(StringUtils.isNotEmpty(request.getHeader(Constant.IP))? request.getHeader(Constant.IP) : "127.0.0.1");
            param.setContent(content);
            param.setModule(module);
            param.setSubModule(subModule);
            param.setDataFrom(dataFrom);
            param.setResult(result);
            param.setFailReason(failReason);

            try{
                LoginIfo4Redis loginInfo4Redis = null;
                if (StringUtils.isNotBlank(userId)) {
                    loginInfo4Redis = (LoginIfo4Redis) redisTemplate.opsForValue().get(Constant.REDIS_KEY_USER_TOKEN + userId);
                    param.setMainAcctId(loginInfo4Redis.getMainAcctId());
                }
            }catch (Exception e){
                log.error("获取MainAcctId失败: {}", e.getMessage());
            }

            log.debug("操作日志记录,{}-{}-json:{},",ModuleEnum.moduleName(module),
                    ModuleEnum.subModuleName(module,subModule), JSON.toJSONString(param));
            iotFeignClient.createLog(param);
        } catch (Exception exception) {
            log.error("操作日志记录失败,{}-{}-{},原因：{}",ModuleEnum.moduleName(module),
                    ModuleEnum.subModuleName(module,subModule),content,exception.getMessage());
            exception.printStackTrace();
        }
    }

    private void doRecordOperateLog(Integer module,
                                    Integer subModule,
                                    String content,
                                    Integer dataFrom,
                                    Integer result,
                                    String failReason,
                                    String account) {
        try {
            HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();

            if (dataFrom == 0) {
                dataFrom = isMobile(request.getHeader("User-Agent")) ? 1 : 0;
            }
            CreateOperateRecordParam param = new CreateOperateRecordParam();
            param.setOperatorAccount(account);
            param.setTime(new Date());
            param.setIp(StringUtils.isNotEmpty(request.getHeader(Constant.IP))? request.getHeader(Constant.IP) : "127.0.0.1");
            param.setContent(content);
            param.setModule(module);
            param.setSubModule(subModule);
            param.setDataFrom(dataFrom);
            param.setResult(result);
            param.setFailReason(failReason);

            try{
                LoginIfo4Redis loginInfo4Redis = null;
                if (StringUtils.isNotBlank(account)) {
                    loginInfo4Redis = (LoginIfo4Redis) redisTemplate.opsForValue().get(Constant.REDIS_KEY_USER_TOKEN + account);
                    param.setMainAcctId(loginInfo4Redis.getMainAcctId());
                }
            }catch (Exception e){
                log.error("获取MainAcctId失败: {}", e.getMessage());
            }

            log.debug("操作日志记录,{}-{}-json:{},",ModuleEnum.moduleName(module),
                    ModuleEnum.subModuleName(module,subModule), JSON.toJSONString(param));
            iotFeignClient.createLog(param);
        } catch (Exception exception) {
            log.error("操作日志记录失败,{}-{}-{},原因：{}",ModuleEnum.moduleName(module),
                    ModuleEnum.subModuleName(module,subModule),content,exception.getMessage());
            exception.printStackTrace();
        }
    }

    private void doRecordOperateLogAsync(Integer module,
                                         Integer subModule,
                                         String content,
                                         String userId,
                                         String ip,
                                         Integer dataFrom) {
        try {
            LoginIfo4Redis loginIfo4Redis = (LoginIfo4Redis) redisTemplate.opsForValue().get(Constant.REDIS_KEY_USER_TOKEN
                    + userId);

            CreateOperateRecordParam param = new CreateOperateRecordParam();
            param.setTime(new Date());
            param.setIp(StringUtils.isNotEmpty(ip)?ip : "127.0.0.1");

            param.setContent(content);
            param.setModule(module);
            param.setSubModule(subModule);
            if(LogRoleUtil.getLogRole(StringUtils.isNotEmpty(loginIfo4Redis.getRoleType())? loginIfo4Redis.getRoleType():H5_LOGIN)!= null){
                param.setRole(LogRoleUtil.getLogRole(StringUtils.isNotEmpty(loginIfo4Redis.getRoleType())? loginIfo4Redis.getRoleType():H5_LOGIN));
            }
            if(StringUtils.isNotEmpty(loginIfo4Redis.getRoleName())){
                param.setRoleName(loginIfo4Redis.getRoleName());
            }
            if (RoleEnum.SYSTEM_ADMIN.code.equals(param.getRole()) || RoleEnum.KANBAN_SUPER_ADMIN.code.equals(param.getRole())) {
                param.setOperatorAccount("systemAdmin");
            } else {
                param.setOperatorName(loginIfo4Redis.getUserName());
                param.setOperatorAccount(replaceWithStar(loginIfo4Redis.getPhone()));
            }

            param.setDataFrom(dataFrom);

            try{
                if (StringUtils.isNotBlank(userId)) {
                    param.setMainAcctId(loginIfo4Redis.getMainAcctId());
                }
            }catch (Exception e){
                log.error("获取MainAcctId失败: {}", e.getMessage());
            }

            log.debug("操作日志记录,{}-{}-json:{},",ModuleEnum.moduleName(module),
                    ModuleEnum.subModuleName(module,subModule), JSON.toJSONString(param));
            iotFeignClient.createLog(param);
        } catch (Exception exception) {
            log.error("操作日志记录失败,{}-{}-{},原因：{}",ModuleEnum.moduleName(module),
                    ModuleEnum.subModuleName(module,subModule),content,exception.getMessage());
        }
    }

    private void doRecordOperateLogAsync(Integer module,
                                         Integer subModule,
                                         String content,
                                         String userId,
                                         String ip,
                                         Integer dataFrom,
                                         Integer result,
                                         String failReason) {
        try {
            LoginIfo4Redis loginIfo4Redis = (LoginIfo4Redis) redisTemplate.opsForValue().get(Constant.REDIS_KEY_USER_TOKEN
                    + userId);

            CreateOperateRecordParam param = new CreateOperateRecordParam();
            param.setTime(new Date());
            param.setIp(StringUtils.isNotEmpty(ip)?ip : "127.0.0.1");

            param.setContent(content);
            param.setModule(module);
            param.setSubModule(subModule);
            param.setResult(result);
            param.setFailReason(failReason);
            if(LogRoleUtil.getLogRole(StringUtils.isNotEmpty(loginIfo4Redis.getRoleType())? loginIfo4Redis.getRoleType():H5_LOGIN)!= null){
                param.setRole(LogRoleUtil.getLogRole(StringUtils.isNotEmpty(loginIfo4Redis.getRoleType())? loginIfo4Redis.getRoleType():H5_LOGIN));
            }
            if(StringUtils.isNotEmpty(loginIfo4Redis.getRoleName())){
                param.setRoleName(loginIfo4Redis.getRoleName());
            }
            if (RoleEnum.SYSTEM_ADMIN.code.equals(param.getRole()) || RoleEnum.KANBAN_SUPER_ADMIN.code.equals(param.getRole())) {
                param.setOperatorAccount("systemAdmin");
            } else {
                param.setOperatorName(loginIfo4Redis.getUserName());
                param.setOperatorAccount(replaceWithStar(loginIfo4Redis.getPhone()));
            }

            param.setDataFrom(dataFrom);

            try{
                if (StringUtils.isNotBlank(userId)) {
                    param.setMainAcctId(loginIfo4Redis.getMainAcctId());
                }
            }catch (Exception e){
                log.error("获取MainAcctId失败: {}", e.getMessage());
            }

            log.debug("操作日志记录,{}-{}-json:{},",ModuleEnum.moduleName(module),
                    ModuleEnum.subModuleName(module,subModule), JSON.toJSONString(param));
            iotFeignClient.createLog(param);
        } catch (Exception exception) {
            log.error("操作日志记录失败,{}-{}-{},原因：{}",ModuleEnum.moduleName(module),
                    ModuleEnum.subModuleName(module,subModule),content,exception.getMessage());
        }
    }

    public void recordPushOperateLog(Integer module, Integer subModule, String content, String name, String phone, String roleName) {
        try {
            HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();

            CreateOperateRecordParam param = new CreateOperateRecordParam();
            param.setRoleName(roleName);
            param.setOperatorName(name);
            param.setOperatorAccount(replaceWithStar(phone));

            param.setTime(new Date());
            param.setIp(StringUtils.isNotEmpty(request.getHeader(Constant.IP))? request.getHeader(Constant.IP) : "127.0.0.1");
            param.setContent(content);
            param.setModule(module);
            param.setSubModule(subModule);

            try{
                LoginIfo4Redis loginInfo4Redis = null;
                if (StringUtils.isNotBlank(phone)) {
                    loginInfo4Redis = (LoginIfo4Redis) redisTemplate.opsForValue().get(Constant.REDIS_KEY_USER_TOKEN + phone);
                    param.setMainAcctId(loginInfo4Redis.getMainAcctId());
                }
            }catch (Exception e){
                log.error("获取MainAcctId失败: {}", e.getMessage());
            }

            log.debug("操作日志记录,{}-{}-json:{},",ModuleEnum.moduleName(module),
                    ModuleEnum.subModuleName(module,subModule), JSON.toJSONString(param));
            iotFeignClient.createLog(param);
        } catch (Exception exception) {
            log.error("操作日志记录失败,{}-{}-{},原因：{}",ModuleEnum.moduleName(module),
                    ModuleEnum.subModuleName(module,subModule),content,exception.getMessage());
            exception.printStackTrace();
        }
    }
}
