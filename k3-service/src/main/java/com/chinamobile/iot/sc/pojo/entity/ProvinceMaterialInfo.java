package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;

public class ProvinceMaterialInfo implements Serializable {
    private String id;

    private String provinceContractId;

    private String productCode;

    private String productName;

    private String materialCode;

    private String materialDescribe;

    private String partCode;

    private String partName;

    private String nestedCode;

    private String internetMaterialCode;

    private String unit;

    private BigDecimal taxExclusiveUnivalence;

    private String taxRate;

    private BigDecimal taxInclusiveUnivalence;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public ProvinceMaterialInfo withId(String id) {
        this.setId(id);
        return this;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getProvinceContractId() {
        return provinceContractId;
    }

    public ProvinceMaterialInfo withProvinceContractId(String provinceContractId) {
        this.setProvinceContractId(provinceContractId);
        return this;
    }

    public void setProvinceContractId(String provinceContractId) {
        this.provinceContractId = provinceContractId == null ? null : provinceContractId.trim();
    }

    public String getProductCode() {
        return productCode;
    }

    public ProvinceMaterialInfo withProductCode(String productCode) {
        this.setProductCode(productCode);
        return this;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode == null ? null : productCode.trim();
    }

    public String getProductName() {
        return productName;
    }

    public ProvinceMaterialInfo withProductName(String productName) {
        this.setProductName(productName);
        return this;
    }

    public void setProductName(String productName) {
        this.productName = productName == null ? null : productName.trim();
    }

    public String getMaterialCode() {
        return materialCode;
    }

    public ProvinceMaterialInfo withMaterialCode(String materialCode) {
        this.setMaterialCode(materialCode);
        return this;
    }

    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode == null ? null : materialCode.trim();
    }

    public String getMaterialDescribe() {
        return materialDescribe;
    }

    public ProvinceMaterialInfo withMaterialDescribe(String materialDescribe) {
        this.setMaterialDescribe(materialDescribe);
        return this;
    }

    public void setMaterialDescribe(String materialDescribe) {
        this.materialDescribe = materialDescribe == null ? null : materialDescribe.trim();
    }

    public String getPartCode() {
        return partCode;
    }

    public ProvinceMaterialInfo withPartCode(String partCode) {
        this.setPartCode(partCode);
        return this;
    }

    public void setPartCode(String partCode) {
        this.partCode = partCode == null ? null : partCode.trim();
    }

    public String getPartName() {
        return partName;
    }

    public ProvinceMaterialInfo withPartName(String partName) {
        this.setPartName(partName);
        return this;
    }

    public void setPartName(String partName) {
        this.partName = partName == null ? null : partName.trim();
    }

    public String getNestedCode() {
        return nestedCode;
    }

    public ProvinceMaterialInfo withNestedCode(String nestedCode) {
        this.setNestedCode(nestedCode);
        return this;
    }

    public void setNestedCode(String nestedCode) {
        this.nestedCode = nestedCode == null ? null : nestedCode.trim();
    }

    public String getInternetMaterialCode() {
        return internetMaterialCode;
    }

    public ProvinceMaterialInfo withInternetMaterialCode(String internetMaterialCode) {
        this.setInternetMaterialCode(internetMaterialCode);
        return this;
    }

    public void setInternetMaterialCode(String internetMaterialCode) {
        this.internetMaterialCode = internetMaterialCode == null ? null : internetMaterialCode.trim();
    }

    public String getUnit() {
        return unit;
    }

    public ProvinceMaterialInfo withUnit(String unit) {
        this.setUnit(unit);
        return this;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public BigDecimal getTaxExclusiveUnivalence() {
        return taxExclusiveUnivalence;
    }

    public ProvinceMaterialInfo withTaxExclusiveUnivalence(BigDecimal taxExclusiveUnivalence) {
        this.setTaxExclusiveUnivalence(taxExclusiveUnivalence);
        return this;
    }

    public void setTaxExclusiveUnivalence(BigDecimal taxExclusiveUnivalence) {
        this.taxExclusiveUnivalence = taxExclusiveUnivalence;
    }

    public String getTaxRate() {
        return taxRate;
    }

    public ProvinceMaterialInfo withTaxRate(String taxRate) {
        this.setTaxRate(taxRate);
        return this;
    }

    public void setTaxRate(String taxRate) {
        this.taxRate = taxRate == null ? null : taxRate.trim();
    }

    public BigDecimal getTaxInclusiveUnivalence() {
        return taxInclusiveUnivalence;
    }

    public ProvinceMaterialInfo withTaxInclusiveUnivalence(BigDecimal taxInclusiveUnivalence) {
        this.setTaxInclusiveUnivalence(taxInclusiveUnivalence);
        return this;
    }

    public void setTaxInclusiveUnivalence(BigDecimal taxInclusiveUnivalence) {
        this.taxInclusiveUnivalence = taxInclusiveUnivalence;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", provinceContractId=").append(provinceContractId);
        sb.append(", productCode=").append(productCode);
        sb.append(", productName=").append(productName);
        sb.append(", materialCode=").append(materialCode);
        sb.append(", materialDescribe=").append(materialDescribe);
        sb.append(", partCode=").append(partCode);
        sb.append(", partName=").append(partName);
        sb.append(", nestedCode=").append(nestedCode);
        sb.append(", internetMaterialCode=").append(internetMaterialCode);
        sb.append(", unit=").append(unit);
        sb.append(", taxExclusiveUnivalence=").append(taxExclusiveUnivalence);
        sb.append(", taxRate=").append(taxRate);
        sb.append(", taxInclusiveUnivalence=").append(taxInclusiveUnivalence);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ProvinceMaterialInfo other = (ProvinceMaterialInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getProvinceContractId() == null ? other.getProvinceContractId() == null : this.getProvinceContractId().equals(other.getProvinceContractId()))
            && (this.getProductCode() == null ? other.getProductCode() == null : this.getProductCode().equals(other.getProductCode()))
            && (this.getProductName() == null ? other.getProductName() == null : this.getProductName().equals(other.getProductName()))
            && (this.getMaterialCode() == null ? other.getMaterialCode() == null : this.getMaterialCode().equals(other.getMaterialCode()))
            && (this.getMaterialDescribe() == null ? other.getMaterialDescribe() == null : this.getMaterialDescribe().equals(other.getMaterialDescribe()))
            && (this.getPartCode() == null ? other.getPartCode() == null : this.getPartCode().equals(other.getPartCode()))
            && (this.getPartName() == null ? other.getPartName() == null : this.getPartName().equals(other.getPartName()))
            && (this.getNestedCode() == null ? other.getNestedCode() == null : this.getNestedCode().equals(other.getNestedCode()))
            && (this.getInternetMaterialCode() == null ? other.getInternetMaterialCode() == null : this.getInternetMaterialCode().equals(other.getInternetMaterialCode()))
            && (this.getUnit() == null ? other.getUnit() == null : this.getUnit().equals(other.getUnit()))
            && (this.getTaxExclusiveUnivalence() == null ? other.getTaxExclusiveUnivalence() == null : this.getTaxExclusiveUnivalence().equals(other.getTaxExclusiveUnivalence()))
            && (this.getTaxRate() == null ? other.getTaxRate() == null : this.getTaxRate().equals(other.getTaxRate()))
            && (this.getTaxInclusiveUnivalence() == null ? other.getTaxInclusiveUnivalence() == null : this.getTaxInclusiveUnivalence().equals(other.getTaxInclusiveUnivalence()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getProvinceContractId() == null) ? 0 : getProvinceContractId().hashCode());
        result = prime * result + ((getProductCode() == null) ? 0 : getProductCode().hashCode());
        result = prime * result + ((getProductName() == null) ? 0 : getProductName().hashCode());
        result = prime * result + ((getMaterialCode() == null) ? 0 : getMaterialCode().hashCode());
        result = prime * result + ((getMaterialDescribe() == null) ? 0 : getMaterialDescribe().hashCode());
        result = prime * result + ((getPartCode() == null) ? 0 : getPartCode().hashCode());
        result = prime * result + ((getPartName() == null) ? 0 : getPartName().hashCode());
        result = prime * result + ((getNestedCode() == null) ? 0 : getNestedCode().hashCode());
        result = prime * result + ((getInternetMaterialCode() == null) ? 0 : getInternetMaterialCode().hashCode());
        result = prime * result + ((getUnit() == null) ? 0 : getUnit().hashCode());
        result = prime * result + ((getTaxExclusiveUnivalence() == null) ? 0 : getTaxExclusiveUnivalence().hashCode());
        result = prime * result + ((getTaxRate() == null) ? 0 : getTaxRate().hashCode());
        result = prime * result + ((getTaxInclusiveUnivalence() == null) ? 0 : getTaxInclusiveUnivalence().hashCode());
        return result;
    }

    public enum Column {
        id("id", "id", "VARCHAR", false),
        provinceContractId("province_contract_id", "provinceContractId", "VARCHAR", false),
        productCode("product_code", "productCode", "VARCHAR", false),
        productName("product_name", "productName", "VARCHAR", false),
        materialCode("material_code", "materialCode", "VARCHAR", false),
        materialDescribe("material_describe", "materialDescribe", "VARCHAR", false),
        partCode("part_code", "partCode", "VARCHAR", false),
        partName("part_name", "partName", "VARCHAR", false),
        nestedCode("nested_code", "nestedCode", "VARCHAR", false),
        internetMaterialCode("internet_material_code", "internetMaterialCode", "VARCHAR", false),
        unit("unit", "unit", "VARCHAR", false),
        taxExclusiveUnivalence("tax_exclusive_univalence", "taxExclusiveUnivalence", "DECIMAL", false),
        taxRate("tax_rate", "taxRate", "VARCHAR", false),
        taxInclusiveUnivalence("tax_inclusive_univalence", "taxInclusiveUnivalence", "DECIMAL", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}