package com.chinamobile.iot.sc.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.pojo.mapper.*;
import com.chinamobile.iot.sc.pojo.param.K3ProBillParam;
import com.chinamobile.iot.sc.pojo.query.K3SynOrderQuery;
import com.chinamobile.iot.sc.pojo.query.K3SynQuery;
import com.chinamobile.iot.sc.pojo.query.K3syncStatisQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/18
 * @description 月数据按省统计合同信息mapper扩展类
 */
public interface K3syncStatisProvinceMapperExt {
    /**
     * 获取合同月数据集合
     *
     * @param page
     * @param k3SyncStatisQuery
     * @return
     */
    Page<K3syncStatisDO> listK3syncStatisProvince(@Param("page") Page page, @Param("k3SyncStatisQuery") K3syncStatisQuery k3SyncStatisQuery);

    /**
     * 获取合同省销售数据集合
     *
     * @param page
     * @param k3SyncStatisQuery
     * @return
     */
    Page<K3syncStatisProDO> listProK3syncStatisProvince(@Param("page") Page page, @Param("k3SyncStatisQuery") K3syncStatisQuery k3SyncStatisQuery);

    /**
     * 获取同步数据集合
     * @param k3SynQuery
     * @return
     */
    List<K3SynDO> listSynProvince(@Param("k3SynQuery") K3SynQuery k3SynQuery);

    /**
     * 获取同步提交数据集合
     * @param k3SynQuery
     * @return
     */
    List<K3SynCommitDO> listSynCommitProvince(@Param("k3SynQuery") K3SynQuery k3SynQuery);

    /**
     * 获取k3省侧导出相关信息
     * @param k3ProBillParam
     * @return
     */
    K3ProBillContractDO getK3ProBillRelatedProvince(@Param("k3ProBillParam") K3ProBillParam k3ProBillParam);

    /**
     * 获取用于生成k3销售订单的相关数据
     * @param proDataCode
     * @return
     */
    List<GenK3DO> listK3ProvinceByProDataCode(String proDataCode);

    /**
     * 获取同步订单相关信息集合
     * @param k3SynOrderQuery
     * @return
     */
    List<K3SynOrderDO> listSynOrderProvince(@Param("k3SynOrderQuery") K3SynOrderQuery k3SynOrderQuery);
}
