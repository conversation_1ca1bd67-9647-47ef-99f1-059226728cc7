package com.chinamobile.iot.sc.pojo.vo;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/6/17 18:08
 */
@Data
public class PromaterialVO {

    /**
     * 商品物料对应表ID
     */
    private String id;

    /**
     * 原子表记录ID
     */
    private String atomId;

    /**
     *商品组/销售商品名称
     */
    private String  spuOfferingName;

    /**
     * 商品组/销售商品编码
     */
    private String  spuOfferingCode;

    /**
     * 销售商品状态（0：测试，1：发布，2：下架）
     */
    private String  spuOfferingStatus;


    /**
     * 商品类型
     */
    private String spuOfferingClass;

    /**
     * 商品名称（规格）
     */
    private String skuOfferingName;

    /**
     * 规格商品状态（0：测试，1：发布，2：下架）
     */
    private String  skuOfferingStatus;

    /**
     *商品编码（规格）
     */
    private String  skuOfferingCode;

    /**
     * 原子商品名称
     */
    private String atomOfferingName;

    /**
     * 原子商品类型
     */
    private String atomOfferingClass;

    /**
     * 原子商品编码
     */
    private String atomOfferingCode;

    /**
     * 型号
     */
    private String model;

    /**
     * 颜色
     */
    private String color;

    /**
     * 所属合作伙伴名
     */
    private String partnerName;

    /**
     * 物料配置状态 0--未配置  1--已配置
     */
    private Integer configStatus;

    /**
     * 物料编码
     */
    private String materialNum;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 配置时间
     */
    private Date configTime;










}
