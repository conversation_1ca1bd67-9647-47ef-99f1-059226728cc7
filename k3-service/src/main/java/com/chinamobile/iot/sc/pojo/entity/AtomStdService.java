package com.chinamobile.iot.sc.pojo.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/16 11:39
 * @description 商品组-标准服务关联信息实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AtomStdService {

    /**
     * 主键
     */
    private String id;

    /**
     * 商品组id
     */
    private String atomId;

    /**
     * 标准服务id
     */
    private String stdServiceId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
