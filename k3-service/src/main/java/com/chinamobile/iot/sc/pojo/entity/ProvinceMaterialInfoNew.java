package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 省物料信息新表
 *
 * <AUTHOR>
public class ProvinceMaterialInfoNew implements Serializable {
    /**
     * 主键id
     *
     * Corresponding to the database column supply_chain..province_material_info_new.id
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private String id;

    /**
     * 物联网公司合同编码
     *
     * Corresponding to the database column supply_chain..province_material_info_new.internet_contract_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private String internetContractCode;

    /**
     * 合同类型 ：A08--OneNET、A09--OnePark、A10-OneTraffic、A15--千里眼独立服务、A16--和对讲独立服务、A17--云视讯独立服务',
     *
     * Corresponding to the database column supply_chain..province_material_info_new.contract_type
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private String contractType;

    /**
     * 省份编码
     *
     * Corresponding to the database column supply_chain..province_material_info_new.province_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private String provinceCode;

    /**
     * 省份名称
     *
     * Corresponding to the database column supply_chain..province_material_info_new.province_name
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private String provinceName;

    /**
     * 地市名称
     *
     * Corresponding to the database column supply_chain..province_material_info_new.city_name
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private String cityName;

    /**
     * 地市编码
     *
     * Corresponding to the database column supply_chain..province_material_info_new.city_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private String cityCode;

    /**
     * 合同编号
     *
     * Corresponding to the database column supply_chain..province_material_info_new.contract_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private String contractCode;

    /**
     * 主物料编码
     *
     * Corresponding to the database column supply_chain..province_material_info_new.material_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private String materialCode;

    /**
     *
     * Corresponding to the database column supply_chain..province_material_info_new.material_name
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private String materialName;

    /**
     * 扩展物料编码  8位集团物料+省代码+3位顺序码
     *
     * Corresponding to the database column supply_chain..province_material_info_new.attr2
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private String attr2;

    /**
     * 扩展物料描述
     *
     * Corresponding to the database column supply_chain..province_material_info_new.attr3
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private String attr3;

    /**
     * 单位(项)
     *
     * Corresponding to the database column supply_chain..province_material_info_new.unit
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private String unit;

    /**
     * 不含税单价\\n
     *
     * Corresponding to the database column supply_chain..province_material_info_new.unit_price
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private String unitPrice;

    /**
     * 含税单价
     *
     * Corresponding to the database column supply_chain..province_material_info_new.tax_inclusive_price
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private String taxInclusivePrice;

    /**
     * 税码 固定值
     *
     * Corresponding to the database column supply_chain..province_material_info_new.tax_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private String taxCode;

    /**
     * 税率 固定值
     *
     * Corresponding to the database column supply_chain..province_material_info_new.tax_rate
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private BigDecimal taxRate;

    /**
     * 是否直发  N/Y
     *
     * Corresponding to the database column supply_chain..province_material_info_new.sd_project_flag
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private String sdProjectFlag;

    /**
     * 物联网物料编码
     *
     * Corresponding to the database column supply_chain..province_material_info_new.internet_material_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private String internetMaterialCode;

    /**
     * 物联网物料名称
     *
     * Corresponding to the database column supply_chain..province_material_info_new.internet_material_name
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private String internetMaterialName;

    /**
     *
     * Corresponding to the database column supply_chain..province_material_info_new.create_time
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private Date createTime;

    /**
     *
     * Corresponding to the database column supply_chain..province_material_info_new.update_time
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private Date updateTime;

    /**
     * Corresponding to the database table supply_chain..province_material_info_new
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.id
     *
     * @return the value of supply_chain..province_material_info_new.id
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.id
     *
     * @param id the value for supply_chain..province_material_info_new.id
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.internet_contract_code
     *
     * @return the value of supply_chain..province_material_info_new.internet_contract_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getInternetContractCode() {
        return internetContractCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withInternetContractCode(String internetContractCode) {
        this.setInternetContractCode(internetContractCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.internet_contract_code
     *
     * @param internetContractCode the value for supply_chain..province_material_info_new.internet_contract_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setInternetContractCode(String internetContractCode) {
        this.internetContractCode = internetContractCode == null ? null : internetContractCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.contract_type
     *
     * @return the value of supply_chain..province_material_info_new.contract_type
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getContractType() {
        return contractType;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withContractType(String contractType) {
        this.setContractType(contractType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.contract_type
     *
     * @param contractType the value for supply_chain..province_material_info_new.contract_type
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setContractType(String contractType) {
        this.contractType = contractType == null ? null : contractType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.province_code
     *
     * @return the value of supply_chain..province_material_info_new.province_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withProvinceCode(String provinceCode) {
        this.setProvinceCode(provinceCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.province_code
     *
     * @param provinceCode the value for supply_chain..province_material_info_new.province_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.province_name
     *
     * @return the value of supply_chain..province_material_info_new.province_name
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getProvinceName() {
        return provinceName;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withProvinceName(String provinceName) {
        this.setProvinceName(provinceName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.province_name
     *
     * @param provinceName the value for supply_chain..province_material_info_new.province_name
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName == null ? null : provinceName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.city_name
     *
     * @return the value of supply_chain..province_material_info_new.city_name
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getCityName() {
        return cityName;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withCityName(String cityName) {
        this.setCityName(cityName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.city_name
     *
     * @param cityName the value for supply_chain..province_material_info_new.city_name
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.city_code
     *
     * @return the value of supply_chain..province_material_info_new.city_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withCityCode(String cityCode) {
        this.setCityCode(cityCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.city_code
     *
     * @param cityCode the value for supply_chain..province_material_info_new.city_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.contract_code
     *
     * @return the value of supply_chain..province_material_info_new.contract_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getContractCode() {
        return contractCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withContractCode(String contractCode) {
        this.setContractCode(contractCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.contract_code
     *
     * @param contractCode the value for supply_chain..province_material_info_new.contract_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setContractCode(String contractCode) {
        this.contractCode = contractCode == null ? null : contractCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.material_code
     *
     * @return the value of supply_chain..province_material_info_new.material_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getMaterialCode() {
        return materialCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withMaterialCode(String materialCode) {
        this.setMaterialCode(materialCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.material_code
     *
     * @param materialCode the value for supply_chain..province_material_info_new.material_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode == null ? null : materialCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.material_name
     *
     * @return the value of supply_chain..province_material_info_new.material_name
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getMaterialName() {
        return materialName;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withMaterialName(String materialName) {
        this.setMaterialName(materialName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.material_name
     *
     * @param materialName the value for supply_chain..province_material_info_new.material_name
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setMaterialName(String materialName) {
        this.materialName = materialName == null ? null : materialName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.attr2
     *
     * @return the value of supply_chain..province_material_info_new.attr2
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getAttr2() {
        return attr2;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withAttr2(String attr2) {
        this.setAttr2(attr2);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.attr2
     *
     * @param attr2 the value for supply_chain..province_material_info_new.attr2
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setAttr2(String attr2) {
        this.attr2 = attr2 == null ? null : attr2.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.attr3
     *
     * @return the value of supply_chain..province_material_info_new.attr3
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getAttr3() {
        return attr3;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withAttr3(String attr3) {
        this.setAttr3(attr3);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.attr3
     *
     * @param attr3 the value for supply_chain..province_material_info_new.attr3
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setAttr3(String attr3) {
        this.attr3 = attr3 == null ? null : attr3.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.unit
     *
     * @return the value of supply_chain..province_material_info_new.unit
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getUnit() {
        return unit;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withUnit(String unit) {
        this.setUnit(unit);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.unit
     *
     * @param unit the value for supply_chain..province_material_info_new.unit
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.unit_price
     *
     * @return the value of supply_chain..province_material_info_new.unit_price
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getUnitPrice() {
        return unitPrice;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withUnitPrice(String unitPrice) {
        this.setUnitPrice(unitPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.unit_price
     *
     * @param unitPrice the value for supply_chain..province_material_info_new.unit_price
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setUnitPrice(String unitPrice) {
        this.unitPrice = unitPrice == null ? null : unitPrice.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.tax_inclusive_price
     *
     * @return the value of supply_chain..province_material_info_new.tax_inclusive_price
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getTaxInclusivePrice() {
        return taxInclusivePrice;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withTaxInclusivePrice(String taxInclusivePrice) {
        this.setTaxInclusivePrice(taxInclusivePrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.tax_inclusive_price
     *
     * @param taxInclusivePrice the value for supply_chain..province_material_info_new.tax_inclusive_price
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setTaxInclusivePrice(String taxInclusivePrice) {
        this.taxInclusivePrice = taxInclusivePrice == null ? null : taxInclusivePrice.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.tax_code
     *
     * @return the value of supply_chain..province_material_info_new.tax_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getTaxCode() {
        return taxCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withTaxCode(String taxCode) {
        this.setTaxCode(taxCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.tax_code
     *
     * @param taxCode the value for supply_chain..province_material_info_new.tax_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode == null ? null : taxCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.tax_rate
     *
     * @return the value of supply_chain..province_material_info_new.tax_rate
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public BigDecimal getTaxRate() {
        return taxRate;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withTaxRate(BigDecimal taxRate) {
        this.setTaxRate(taxRate);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.tax_rate
     *
     * @param taxRate the value for supply_chain..province_material_info_new.tax_rate
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.sd_project_flag
     *
     * @return the value of supply_chain..province_material_info_new.sd_project_flag
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getSdProjectFlag() {
        return sdProjectFlag;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withSdProjectFlag(String sdProjectFlag) {
        this.setSdProjectFlag(sdProjectFlag);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.sd_project_flag
     *
     * @param sdProjectFlag the value for supply_chain..province_material_info_new.sd_project_flag
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setSdProjectFlag(String sdProjectFlag) {
        this.sdProjectFlag = sdProjectFlag == null ? null : sdProjectFlag.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.internet_material_code
     *
     * @return the value of supply_chain..province_material_info_new.internet_material_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getInternetMaterialCode() {
        return internetMaterialCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withInternetMaterialCode(String internetMaterialCode) {
        this.setInternetMaterialCode(internetMaterialCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.internet_material_code
     *
     * @param internetMaterialCode the value for supply_chain..province_material_info_new.internet_material_code
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setInternetMaterialCode(String internetMaterialCode) {
        this.internetMaterialCode = internetMaterialCode == null ? null : internetMaterialCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.internet_material_name
     *
     * @return the value of supply_chain..province_material_info_new.internet_material_name
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public String getInternetMaterialName() {
        return internetMaterialName;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withInternetMaterialName(String internetMaterialName) {
        this.setInternetMaterialName(internetMaterialName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.internet_material_name
     *
     * @param internetMaterialName the value for supply_chain..province_material_info_new.internet_material_name
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setInternetMaterialName(String internetMaterialName) {
        this.internetMaterialName = internetMaterialName == null ? null : internetMaterialName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.create_time
     *
     * @return the value of supply_chain..province_material_info_new.create_time
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.create_time
     *
     * @param createTime the value for supply_chain..province_material_info_new.create_time
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..province_material_info_new.update_time
     *
     * @return the value of supply_chain..province_material_info_new.update_time
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public ProvinceMaterialInfoNew withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_material_info_new.update_time
     *
     * @param updateTime the value for supply_chain..province_material_info_new.update_time
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", internetContractCode=").append(internetContractCode);
        sb.append(", contractType=").append(contractType);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", provinceName=").append(provinceName);
        sb.append(", cityName=").append(cityName);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", contractCode=").append(contractCode);
        sb.append(", materialCode=").append(materialCode);
        sb.append(", materialName=").append(materialName);
        sb.append(", attr2=").append(attr2);
        sb.append(", attr3=").append(attr3);
        sb.append(", unit=").append(unit);
        sb.append(", unitPrice=").append(unitPrice);
        sb.append(", taxInclusivePrice=").append(taxInclusivePrice);
        sb.append(", taxCode=").append(taxCode);
        sb.append(", taxRate=").append(taxRate);
        sb.append(", sdProjectFlag=").append(sdProjectFlag);
        sb.append(", internetMaterialCode=").append(internetMaterialCode);
        sb.append(", internetMaterialName=").append(internetMaterialName);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ProvinceMaterialInfoNew other = (ProvinceMaterialInfoNew) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getInternetContractCode() == null ? other.getInternetContractCode() == null : this.getInternetContractCode().equals(other.getInternetContractCode()))
            && (this.getContractType() == null ? other.getContractType() == null : this.getContractType().equals(other.getContractType()))
            && (this.getProvinceCode() == null ? other.getProvinceCode() == null : this.getProvinceCode().equals(other.getProvinceCode()))
            && (this.getProvinceName() == null ? other.getProvinceName() == null : this.getProvinceName().equals(other.getProvinceName()))
            && (this.getCityName() == null ? other.getCityName() == null : this.getCityName().equals(other.getCityName()))
            && (this.getCityCode() == null ? other.getCityCode() == null : this.getCityCode().equals(other.getCityCode()))
            && (this.getContractCode() == null ? other.getContractCode() == null : this.getContractCode().equals(other.getContractCode()))
            && (this.getMaterialCode() == null ? other.getMaterialCode() == null : this.getMaterialCode().equals(other.getMaterialCode()))
            && (this.getMaterialName() == null ? other.getMaterialName() == null : this.getMaterialName().equals(other.getMaterialName()))
            && (this.getAttr2() == null ? other.getAttr2() == null : this.getAttr2().equals(other.getAttr2()))
            && (this.getAttr3() == null ? other.getAttr3() == null : this.getAttr3().equals(other.getAttr3()))
            && (this.getUnit() == null ? other.getUnit() == null : this.getUnit().equals(other.getUnit()))
            && (this.getUnitPrice() == null ? other.getUnitPrice() == null : this.getUnitPrice().equals(other.getUnitPrice()))
            && (this.getTaxInclusivePrice() == null ? other.getTaxInclusivePrice() == null : this.getTaxInclusivePrice().equals(other.getTaxInclusivePrice()))
            && (this.getTaxCode() == null ? other.getTaxCode() == null : this.getTaxCode().equals(other.getTaxCode()))
            && (this.getTaxRate() == null ? other.getTaxRate() == null : this.getTaxRate().equals(other.getTaxRate()))
            && (this.getSdProjectFlag() == null ? other.getSdProjectFlag() == null : this.getSdProjectFlag().equals(other.getSdProjectFlag()))
            && (this.getInternetMaterialCode() == null ? other.getInternetMaterialCode() == null : this.getInternetMaterialCode().equals(other.getInternetMaterialCode()))
            && (this.getInternetMaterialName() == null ? other.getInternetMaterialName() == null : this.getInternetMaterialName().equals(other.getInternetMaterialName()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getInternetContractCode() == null) ? 0 : getInternetContractCode().hashCode());
        result = prime * result + ((getContractType() == null) ? 0 : getContractType().hashCode());
        result = prime * result + ((getProvinceCode() == null) ? 0 : getProvinceCode().hashCode());
        result = prime * result + ((getProvinceName() == null) ? 0 : getProvinceName().hashCode());
        result = prime * result + ((getCityName() == null) ? 0 : getCityName().hashCode());
        result = prime * result + ((getCityCode() == null) ? 0 : getCityCode().hashCode());
        result = prime * result + ((getContractCode() == null) ? 0 : getContractCode().hashCode());
        result = prime * result + ((getMaterialCode() == null) ? 0 : getMaterialCode().hashCode());
        result = prime * result + ((getMaterialName() == null) ? 0 : getMaterialName().hashCode());
        result = prime * result + ((getAttr2() == null) ? 0 : getAttr2().hashCode());
        result = prime * result + ((getAttr3() == null) ? 0 : getAttr3().hashCode());
        result = prime * result + ((getUnit() == null) ? 0 : getUnit().hashCode());
        result = prime * result + ((getUnitPrice() == null) ? 0 : getUnitPrice().hashCode());
        result = prime * result + ((getTaxInclusivePrice() == null) ? 0 : getTaxInclusivePrice().hashCode());
        result = prime * result + ((getTaxCode() == null) ? 0 : getTaxCode().hashCode());
        result = prime * result + ((getTaxRate() == null) ? 0 : getTaxRate().hashCode());
        result = prime * result + ((getSdProjectFlag() == null) ? 0 : getSdProjectFlag().hashCode());
        result = prime * result + ((getInternetMaterialCode() == null) ? 0 : getInternetMaterialCode().hashCode());
        result = prime * result + ((getInternetMaterialName() == null) ? 0 : getInternetMaterialName().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     * corresponding to the database table supply_chain..province_material_info_new
     *
     * @mbg.generated Mon Mar 10 15:14:39 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        internetContractCode("internet_contract_code", "internetContractCode", "VARCHAR", false),
        contractType("contract_type", "contractType", "VARCHAR", false),
        provinceCode("province_code", "provinceCode", "VARCHAR", false),
        provinceName("province_name", "provinceName", "VARCHAR", false),
        cityName("city_name", "cityName", "VARCHAR", false),
        cityCode("city_code", "cityCode", "VARCHAR", false),
        contractCode("contract_code", "contractCode", "VARCHAR", false),
        materialCode("material_code", "materialCode", "VARCHAR", false),
        materialName("material_name", "materialName", "VARCHAR", false),
        attr2("attr2", "attr2", "VARCHAR", false),
        attr3("attr3", "attr3", "VARCHAR", false),
        unit("unit", "unit", "VARCHAR", false),
        unitPrice("unit_price", "unitPrice", "VARCHAR", false),
        taxInclusivePrice("tax_inclusive_price", "taxInclusivePrice", "VARCHAR", false),
        taxCode("tax_code", "taxCode", "VARCHAR", false),
        taxRate("tax_rate", "taxRate", "DECIMAL", false),
        sdProjectFlag("sd_project_flag", "sdProjectFlag", "VARCHAR", false),
        internetMaterialCode("internet_material_code", "internetMaterialCode", "VARCHAR", false),
        internetMaterialName("internet_material_name", "internetMaterialName", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         * Corresponding to the database table supply_chain..province_material_info_new
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..province_material_info_new
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..province_material_info_new
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        private final String column;

        /**
         * Corresponding to the database table supply_chain..province_material_info_new
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         * Corresponding to the database table supply_chain..province_material_info_new
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        private final String javaProperty;

        /**
         * Corresponding to the database table supply_chain..province_material_info_new
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Mar 10 15:14:39 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}