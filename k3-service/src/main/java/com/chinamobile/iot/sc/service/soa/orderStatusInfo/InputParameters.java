
package com.chinamobile.iot.sc.service.soa.orderStatusInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for InputParameters complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="InputParameters"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="MSGHEADER" type="{http://soa.cmcc.com/MsgHeader}MSGHEADER"/&gt;
 *         &lt;element name="PROVINCE_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SOURCE_FROM" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SOURCE_FROM_NO" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SCM_PO_NUM" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="QUERY_EXT" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="LAST_UPDATE_START" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="LAST_UPDATE_END" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "InputParameters", propOrder = {
    "msgheader",
    "provincecode",
    "sourcefrom",
    "sourcefromno",
    "scmponum",
    "queryext",
    "lastupdatestart",
    "lastupdateend"
})
public class InputParameters {

    @XmlElement(name = "MSGHEADER", required = true)
    protected MSGHEADER msgheader;
    @XmlElement(name = "PROVINCE_CODE", required = true)
    protected String provincecode;
    @XmlElement(name = "SOURCE_FROM", required = true, nillable = true)
    protected String sourcefrom;
    @XmlElement(name = "SOURCE_FROM_NO", required = true, nillable = true)
    protected String sourcefromno;
    @XmlElement(name = "SCM_PO_NUM", required = true, nillable = true)
    protected String scmponum;
    @XmlElement(name = "QUERY_EXT", required = true, nillable = true)
    protected String queryext;
    @XmlElement(name = "LAST_UPDATE_START", required = true, nillable = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar lastupdatestart;
    @XmlElement(name = "LAST_UPDATE_END", required = true, nillable = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar lastupdateend;

    /**
     * Gets the value of the msgheader property.
     * 
     * @return
     *     possible object is
     *     {@link MSGHEADER }
     *     
     */
    public MSGHEADER getMSGHEADER() {
        return msgheader;
    }

    /**
     * Sets the value of the msgheader property.
     * 
     * @param value
     *     allowed object is
     *     {@link MSGHEADER }
     *     
     */
    public void setMSGHEADER(MSGHEADER value) {
        this.msgheader = value;
    }

    /**
     * Gets the value of the provincecode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPROVINCECODE() {
        return provincecode;
    }

    /**
     * Sets the value of the provincecode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPROVINCECODE(String value) {
        this.provincecode = value;
    }

    /**
     * Gets the value of the sourcefrom property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSOURCEFROM() {
        return sourcefrom;
    }

    /**
     * Sets the value of the sourcefrom property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSOURCEFROM(String value) {
        this.sourcefrom = value;
    }

    /**
     * Gets the value of the sourcefromno property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSOURCEFROMNO() {
        return sourcefromno;
    }

    /**
     * Sets the value of the sourcefromno property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSOURCEFROMNO(String value) {
        this.sourcefromno = value;
    }

    /**
     * Gets the value of the scmponum property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSCMPONUM() {
        return scmponum;
    }

    /**
     * Sets the value of the scmponum property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSCMPONUM(String value) {
        this.scmponum = value;
    }

    /**
     * Gets the value of the queryext property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getQUERYEXT() {
        return queryext;
    }

    /**
     * Sets the value of the queryext property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setQUERYEXT(String value) {
        this.queryext = value;
    }

    /**
     * Gets the value of the lastupdatestart property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLASTUPDATESTART() {
        return lastupdatestart;
    }

    /**
     * Sets the value of the lastupdatestart property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLASTUPDATESTART(XMLGregorianCalendar value) {
        this.lastupdatestart = value;
    }

    /**
     * Gets the value of the lastupdateend property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getLASTUPDATEEND() {
        return lastupdateend;
    }

    /**
     * Sets the value of the lastupdateend property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setLASTUPDATEEND(XMLGregorianCalendar value) {
        this.lastupdateend = value;
    }

}
