package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 保理订单信息
 *
 * <AUTHOR>
public class OrderBaoli implements Serializable {
    /**
     *
     * Corresponding to the database column supply_chain..order_baoli.id
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    private String id;

    /**
     * 原子订单表id
     *
     * Corresponding to the database column supply_chain..order_baoli.order_atom_info_id
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    private String orderAtomInfoId;

    /**
     * 订单id
     *
     * Corresponding to the database column supply_chain..order_baoli.order_id
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    private String orderId;

    /**
     * 商品规格编码
     *
     * Corresponding to the database column supply_chain..order_baoli.sku_offering_name
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    private String skuOfferingName;

    /**
     * 结算金额(厘)
     *
     * Corresponding to the database column supply_chain..order_baoli.total_price
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    private Long totalPrice;

    /**
     * 合同编号(移动省专协议编号)
     *
     * Corresponding to the database column supply_chain..order_baoli.contract_num
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    private String contractNum;

    /**
     * 合同相对方名称
     *
     * Corresponding to the database column supply_chain..order_baoli.buyer_name
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    private String buyerName;

    /**
     * 承建方名称(移动省专公司名称)
     *
     * Corresponding to the database column supply_chain..order_baoli.seller_name
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    private String sellerName;

    /**
     * 保理状态  0--未申请 1--待申请 2--省专审核中  3--省专已拒绝  4--方案制作中 5--方案确认中 6--方案已拒绝 7--方案已退回 8--方案审核中 9--银行审核中 10--银行已拒绝 11--银行已放款 12--已撤销
     *
     * Corresponding to the database column supply_chain..order_baoli.baoli_status
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    private Integer baoliStatus;

    /**
     * 合作伙伴ID
     *
     * Corresponding to the database column supply_chain..order_baoli.cooperator_id
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    private String cooperatorId;

    /**
     * 贸易编号
     *
     * Corresponding to the database column supply_chain..order_baoli.trade_no
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    private String tradeNo;

    /**
     * 已还金额(厘)
     *
     * Corresponding to the database column supply_chain..order_baoli.return_price
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    private Long returnPrice;

    /**
     * 订单完成时间
     *
     * Corresponding to the database column supply_chain..order_baoli.order_finish_time
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    private Date orderFinishTime;

    /**
     * 创建时间
     *
     * Corresponding to the database column supply_chain..order_baoli.create_time
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    private Date createTime;

    /**
     * 更新时间
     *
     * Corresponding to the database column supply_chain..order_baoli.update_time
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    private Date updateTime;

    /**
     * Corresponding to the database table supply_chain..order_baoli
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..order_baoli.id
     *
     * @return the value of supply_chain..order_baoli.id
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public OrderBaoli withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_baoli.id
     *
     * @param id the value for supply_chain..order_baoli.id
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_baoli.order_atom_info_id
     *
     * @return the value of supply_chain..order_baoli.order_atom_info_id
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public String getOrderAtomInfoId() {
        return orderAtomInfoId;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public OrderBaoli withOrderAtomInfoId(String orderAtomInfoId) {
        this.setOrderAtomInfoId(orderAtomInfoId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_baoli.order_atom_info_id
     *
     * @param orderAtomInfoId the value for supply_chain..order_baoli.order_atom_info_id
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public void setOrderAtomInfoId(String orderAtomInfoId) {
        this.orderAtomInfoId = orderAtomInfoId == null ? null : orderAtomInfoId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_baoli.order_id
     *
     * @return the value of supply_chain..order_baoli.order_id
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public OrderBaoli withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_baoli.order_id
     *
     * @param orderId the value for supply_chain..order_baoli.order_id
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_baoli.sku_offering_name
     *
     * @return the value of supply_chain..order_baoli.sku_offering_name
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public String getSkuOfferingName() {
        return skuOfferingName;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public OrderBaoli withSkuOfferingName(String skuOfferingName) {
        this.setSkuOfferingName(skuOfferingName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_baoli.sku_offering_name
     *
     * @param skuOfferingName the value for supply_chain..order_baoli.sku_offering_name
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public void setSkuOfferingName(String skuOfferingName) {
        this.skuOfferingName = skuOfferingName == null ? null : skuOfferingName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_baoli.total_price
     *
     * @return the value of supply_chain..order_baoli.total_price
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public Long getTotalPrice() {
        return totalPrice;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public OrderBaoli withTotalPrice(Long totalPrice) {
        this.setTotalPrice(totalPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_baoli.total_price
     *
     * @param totalPrice the value for supply_chain..order_baoli.total_price
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public void setTotalPrice(Long totalPrice) {
        this.totalPrice = totalPrice;
    }

    /**
     * This method returns the value of the database column supply_chain..order_baoli.contract_num
     *
     * @return the value of supply_chain..order_baoli.contract_num
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public String getContractNum() {
        return contractNum;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public OrderBaoli withContractNum(String contractNum) {
        this.setContractNum(contractNum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_baoli.contract_num
     *
     * @param contractNum the value for supply_chain..order_baoli.contract_num
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public void setContractNum(String contractNum) {
        this.contractNum = contractNum == null ? null : contractNum.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_baoli.buyer_name
     *
     * @return the value of supply_chain..order_baoli.buyer_name
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public String getBuyerName() {
        return buyerName;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public OrderBaoli withBuyerName(String buyerName) {
        this.setBuyerName(buyerName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_baoli.buyer_name
     *
     * @param buyerName the value for supply_chain..order_baoli.buyer_name
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName == null ? null : buyerName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_baoli.seller_name
     *
     * @return the value of supply_chain..order_baoli.seller_name
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public String getSellerName() {
        return sellerName;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public OrderBaoli withSellerName(String sellerName) {
        this.setSellerName(sellerName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_baoli.seller_name
     *
     * @param sellerName the value for supply_chain..order_baoli.seller_name
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public void setSellerName(String sellerName) {
        this.sellerName = sellerName == null ? null : sellerName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_baoli.baoli_status
     *
     * @return the value of supply_chain..order_baoli.baoli_status
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public Integer getBaoliStatus() {
        return baoliStatus;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public OrderBaoli withBaoliStatus(Integer baoliStatus) {
        this.setBaoliStatus(baoliStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_baoli.baoli_status
     *
     * @param baoliStatus the value for supply_chain..order_baoli.baoli_status
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public void setBaoliStatus(Integer baoliStatus) {
        this.baoliStatus = baoliStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..order_baoli.cooperator_id
     *
     * @return the value of supply_chain..order_baoli.cooperator_id
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public String getCooperatorId() {
        return cooperatorId;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public OrderBaoli withCooperatorId(String cooperatorId) {
        this.setCooperatorId(cooperatorId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_baoli.cooperator_id
     *
     * @param cooperatorId the value for supply_chain..order_baoli.cooperator_id
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public void setCooperatorId(String cooperatorId) {
        this.cooperatorId = cooperatorId == null ? null : cooperatorId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_baoli.trade_no
     *
     * @return the value of supply_chain..order_baoli.trade_no
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public String getTradeNo() {
        return tradeNo;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public OrderBaoli withTradeNo(String tradeNo) {
        this.setTradeNo(tradeNo);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_baoli.trade_no
     *
     * @param tradeNo the value for supply_chain..order_baoli.trade_no
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo == null ? null : tradeNo.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..order_baoli.return_price
     *
     * @return the value of supply_chain..order_baoli.return_price
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public Long getReturnPrice() {
        return returnPrice;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public OrderBaoli withReturnPrice(Long returnPrice) {
        this.setReturnPrice(returnPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_baoli.return_price
     *
     * @param returnPrice the value for supply_chain..order_baoli.return_price
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public void setReturnPrice(Long returnPrice) {
        this.returnPrice = returnPrice;
    }

    /**
     * This method returns the value of the database column supply_chain..order_baoli.order_finish_time
     *
     * @return the value of supply_chain..order_baoli.order_finish_time
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public Date getOrderFinishTime() {
        return orderFinishTime;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public OrderBaoli withOrderFinishTime(Date orderFinishTime) {
        this.setOrderFinishTime(orderFinishTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_baoli.order_finish_time
     *
     * @param orderFinishTime the value for supply_chain..order_baoli.order_finish_time
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public void setOrderFinishTime(Date orderFinishTime) {
        this.orderFinishTime = orderFinishTime;
    }

    /**
     * This method returns the value of the database column supply_chain..order_baoli.create_time
     *
     * @return the value of supply_chain..order_baoli.create_time
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public OrderBaoli withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_baoli.create_time
     *
     * @param createTime the value for supply_chain..order_baoli.create_time
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..order_baoli.update_time
     *
     * @return the value of supply_chain..order_baoli.update_time
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public OrderBaoli withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..order_baoli.update_time
     *
     * @param updateTime the value for supply_chain..order_baoli.update_time
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderAtomInfoId=").append(orderAtomInfoId);
        sb.append(", orderId=").append(orderId);
        sb.append(", skuOfferingName=").append(skuOfferingName);
        sb.append(", totalPrice=").append(totalPrice);
        sb.append(", contractNum=").append(contractNum);
        sb.append(", buyerName=").append(buyerName);
        sb.append(", sellerName=").append(sellerName);
        sb.append(", baoliStatus=").append(baoliStatus);
        sb.append(", cooperatorId=").append(cooperatorId);
        sb.append(", tradeNo=").append(tradeNo);
        sb.append(", returnPrice=").append(returnPrice);
        sb.append(", orderFinishTime=").append(orderFinishTime);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OrderBaoli other = (OrderBaoli) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOrderAtomInfoId() == null ? other.getOrderAtomInfoId() == null : this.getOrderAtomInfoId().equals(other.getOrderAtomInfoId()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getSkuOfferingName() == null ? other.getSkuOfferingName() == null : this.getSkuOfferingName().equals(other.getSkuOfferingName()))
            && (this.getTotalPrice() == null ? other.getTotalPrice() == null : this.getTotalPrice().equals(other.getTotalPrice()))
            && (this.getContractNum() == null ? other.getContractNum() == null : this.getContractNum().equals(other.getContractNum()))
            && (this.getBuyerName() == null ? other.getBuyerName() == null : this.getBuyerName().equals(other.getBuyerName()))
            && (this.getSellerName() == null ? other.getSellerName() == null : this.getSellerName().equals(other.getSellerName()))
            && (this.getBaoliStatus() == null ? other.getBaoliStatus() == null : this.getBaoliStatus().equals(other.getBaoliStatus()))
            && (this.getCooperatorId() == null ? other.getCooperatorId() == null : this.getCooperatorId().equals(other.getCooperatorId()))
            && (this.getTradeNo() == null ? other.getTradeNo() == null : this.getTradeNo().equals(other.getTradeNo()))
            && (this.getReturnPrice() == null ? other.getReturnPrice() == null : this.getReturnPrice().equals(other.getReturnPrice()))
            && (this.getOrderFinishTime() == null ? other.getOrderFinishTime() == null : this.getOrderFinishTime().equals(other.getOrderFinishTime()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOrderAtomInfoId() == null) ? 0 : getOrderAtomInfoId().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getSkuOfferingName() == null) ? 0 : getSkuOfferingName().hashCode());
        result = prime * result + ((getTotalPrice() == null) ? 0 : getTotalPrice().hashCode());
        result = prime * result + ((getContractNum() == null) ? 0 : getContractNum().hashCode());
        result = prime * result + ((getBuyerName() == null) ? 0 : getBuyerName().hashCode());
        result = prime * result + ((getSellerName() == null) ? 0 : getSellerName().hashCode());
        result = prime * result + ((getBaoliStatus() == null) ? 0 : getBaoliStatus().hashCode());
        result = prime * result + ((getCooperatorId() == null) ? 0 : getCooperatorId().hashCode());
        result = prime * result + ((getTradeNo() == null) ? 0 : getTradeNo().hashCode());
        result = prime * result + ((getReturnPrice() == null) ? 0 : getReturnPrice().hashCode());
        result = prime * result + ((getOrderFinishTime() == null) ? 0 : getOrderFinishTime().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     * corresponding to the database table supply_chain..order_baoli
     *
     * @mbg.generated Mon Sep 18 16:48:48 CST 2023
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        orderAtomInfoId("order_atom_info_id", "orderAtomInfoId", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        skuOfferingName("sku_offering_name", "skuOfferingName", "VARCHAR", false),
        totalPrice("total_price", "totalPrice", "BIGINT", false),
        contractNum("contract_num", "contractNum", "VARCHAR", false),
        buyerName("buyer_name", "buyerName", "VARCHAR", false),
        sellerName("seller_name", "sellerName", "VARCHAR", false),
        baoliStatus("baoli_status", "baoliStatus", "INTEGER", false),
        cooperatorId("cooperator_id", "cooperatorId", "VARCHAR", false),
        tradeNo("trade_no", "tradeNo", "VARCHAR", false),
        returnPrice("return_price", "returnPrice", "BIGINT", false),
        orderFinishTime("order_finish_time", "orderFinishTime", "TIMESTAMP", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         * Corresponding to the database table supply_chain..order_baoli
         *
         * @mbg.generated Mon Sep 18 16:48:48 CST 2023
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..order_baoli
         *
         * @mbg.generated Mon Sep 18 16:48:48 CST 2023
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..order_baoli
         *
         * @mbg.generated Mon Sep 18 16:48:48 CST 2023
         */
        private final String column;

        /**
         * Corresponding to the database table supply_chain..order_baoli
         *
         * @mbg.generated Mon Sep 18 16:48:48 CST 2023
         */
        private final boolean isColumnNameDelimited;

        /**
         * Corresponding to the database table supply_chain..order_baoli
         *
         * @mbg.generated Mon Sep 18 16:48:48 CST 2023
         */
        private final String javaProperty;

        /**
         * Corresponding to the database table supply_chain..order_baoli
         *
         * @mbg.generated Mon Sep 18 16:48:48 CST 2023
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Sep 18 16:48:48 CST 2023
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:48:48 CST 2023
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:48:48 CST 2023
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:48:48 CST 2023
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:48:48 CST 2023
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:48:48 CST 2023
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:48:48 CST 2023
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:48:48 CST 2023
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:48:48 CST 2023
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:48:48 CST 2023
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:48:48 CST 2023
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}