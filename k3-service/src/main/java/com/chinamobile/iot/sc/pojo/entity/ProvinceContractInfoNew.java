package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 省份合同信息新表
 *
 * <AUTHOR>
public class ProvinceContractInfoNew implements Serializable {
    /**
     * 合同主键id
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.id
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String id;

    /**
     * 合同类型 ：A08--OneNET、A09--OnePark、A10-OneTraffic、A15--千里眼独立服务、A16--和对讲独立服务、A17--云视讯独立服务
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.contract_type
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String contractType;

    /**
     * 省份编码
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.province_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String provinceCode;

    /**
     * 省份名称
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.province_name
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String provinceName;

    /**
     * 地市编码
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.city_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String cityCode;

    /**
     * 地市名称
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.city_name
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String cityName;

    /**
     * 物联网公司合同编码
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.internet_contract_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String internetContractCode;

    /**
     * 申请部门代码
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.dept_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String deptCode;

    /**
     * 申请部门名称
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.dept_name
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String deptName;

    /**
     * 申请人ID
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.created_id
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String createdId;

    /**
     * 申请人姓名
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.created_name
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String createdName;

    /**
     * 物资类别 字典值： E：工程物资 C：业务用品 P：备品备件 O：杂品  "当“开支类型”为“资本类”时
，类别必需为“E-工程物资”；当
“开支类型”为“成本类”时，可
以为“C-业务用品，P-备品备件
，O-杂品” 。产品库上架的物资
类别需与订单上的物资类别一致
当为零购订单时不校验产品库的物资类别"
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.mtl_type_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String mtlTypeCode;

    /**
     * 物资小类
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.small_item
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String smallItem;

    /**
     * 合同编号
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.contract_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String contractCode;

    /**
     * 供应商编码  固定值 MDM_106201379

     *
     * Corresponding to the database column supply_chain..province_contract_info_new.vendor_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String vendorCode;

    /**
     * 需求类型  字典值： 1：个人市场业务需求 2：家庭市场业务需求 3：政企市场业务需求 4：新兴市场业务需求 5：支撑多种业务的产品类采购 6：其他业务需求\n
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.req_type
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String reqType;

    /**
     * 二级需求类型  当“需求类型”为“政企市场业务 求”时必填 字典值： 1：ICT业务需求 2：其他业务需

     *
     * Corresponding to the database column supply_chain..province_contract_info_new.req_second_type
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String reqSecondType;

    /**
     * 接收人员工编号  新员工编号，11位

     *
     * Corresponding to the database column supply_chain..province_contract_info_new.rcv_user_num
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String rcvUserNum;

    /**
     * 接收人姓名
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.rcv_user
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String rcvUser;

    /**
     * 接收人电话
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.rcv_contact_phone
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String rcvContactPhone;

    /**
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.rcv_site_address
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String rcvSiteAddress;

    /**
     * MIS主体

     *
     * Corresponding to the database column supply_chain..province_contract_info_new.mis_body
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String misBody;

    /**
     * OU标识
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.org_id
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String orgId;

    /**
     * 字典值： Capex：资本类 Opex：成本类

     *
     * Corresponding to the database column supply_chain..province_contract_info_new.exp_type
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String expType;

    /**
     * 报账模式 "字典值： 1：ERP采购订单 2：费用池订单


     *
     * Corresponding to the database column supply_chain..province_contract_info_new.reimbursement_mode
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String reimbursementMode;

    /**
     * 要求到货时间模式 "字典值：
DATE：时间点
RANGE：时间段
EFFECT：订单生效后X自然日默认
“时间点”
当为零购订单时，仅可传
“DATE”和”RANGE”"

     *
     * Corresponding to the database column supply_chain..province_contract_info_new.arrival_time_model
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String arrivalTimeModel;

    /**
     * 要求到货天数\\\\\\\\\\\\\\\\n
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.arrival_days
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private Integer arrivalDays;

    /**
     * 发运组织代码\\n
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.organization_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String organizationCode;

    /**
     * INVENTORY：库存 EXPENSE：费用

     *
     * Corresponding to the database column supply_chain..province_contract_info_new.item_type
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String itemType;

    /**
     * 预算组合ID "当“开支类型”为“成本类”时必\n填"
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.budget_id
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String budgetId;

    /**
     * 预算年份 "当“开支类型”为“成本类”时必\n填"\n
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.budget_year
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String budgetYear;

    /**
     * 业务活动编码  "当“开支类型”为“成本类”时必填默认预算组织中的业务活动"\n\n
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.activity_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String activityCode;

    /**
     * 成本中心  "仅适用“报账模式”为“ERP采购订单”当“开支类型”为“成本类”时并且“分配类型”为“费用”时必填"

     *
     * Corresponding to the database column supply_chain..province_contract_info_new.cost_center
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String costCenter;

    /**
     * 费用科目  "仅适用“报账模式”为“ERP采购订单”当“开支类型”为“成本类”时并且“分配类型”为“费用”时必填"

     *
     * Corresponding to the database column supply_chain..province_contract_info_new.expense_account
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String expenseAccount;

    /**
     * 子目"仅适用“报账模式”为“ERP采购
订单”
当“开支类型”为“成本类
”时并且“分配类型”为“费用
”时必填"

     *
     * Corresponding to the database column supply_chain..province_contract_info_new.cost_subject
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String costSubject;

    /**
     * 管会业务活动 1、当“开支类型”为“成本类 ”时并且“分配类型”为“费用 ”时必填
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.manage_activity
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String manageActivity;

    /**
     * 管会市场维度 1、当“开支类型”为“成本类 ”时并且“分配类型”为“费用 ”时必填\n
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.manage_market
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String manageMarket;

    /**
     * 管会产品维度 1、当“开支类型”为“成本类 ”时并且“分配类型”为“费用 ”时必填\n
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.manage_product
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private String manageProduct;

    /**
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.create_time
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private Date createTime;

    /**
     *
     * Corresponding to the database column supply_chain..province_contract_info_new.update_time
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private Date updateTime;

    /**
     * Corresponding to the database table supply_chain..province_contract_info_new
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.id
     *
     * @return the value of supply_chain..province_contract_info_new.id
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.id
     *
     * @param id the value for supply_chain..province_contract_info_new.id
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.contract_type
     *
     * @return the value of supply_chain..province_contract_info_new.contract_type
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getContractType() {
        return contractType;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withContractType(String contractType) {
        this.setContractType(contractType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.contract_type
     *
     * @param contractType the value for supply_chain..province_contract_info_new.contract_type
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setContractType(String contractType) {
        this.contractType = contractType == null ? null : contractType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.province_code
     *
     * @return the value of supply_chain..province_contract_info_new.province_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withProvinceCode(String provinceCode) {
        this.setProvinceCode(provinceCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.province_code
     *
     * @param provinceCode the value for supply_chain..province_contract_info_new.province_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.province_name
     *
     * @return the value of supply_chain..province_contract_info_new.province_name
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getProvinceName() {
        return provinceName;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withProvinceName(String provinceName) {
        this.setProvinceName(provinceName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.province_name
     *
     * @param provinceName the value for supply_chain..province_contract_info_new.province_name
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName == null ? null : provinceName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.city_code
     *
     * @return the value of supply_chain..province_contract_info_new.city_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getCityCode() {
        return cityCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withCityCode(String cityCode) {
        this.setCityCode(cityCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.city_code
     *
     * @param cityCode the value for supply_chain..province_contract_info_new.city_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setCityCode(String cityCode) {
        this.cityCode = cityCode == null ? null : cityCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.city_name
     *
     * @return the value of supply_chain..province_contract_info_new.city_name
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getCityName() {
        return cityName;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withCityName(String cityName) {
        this.setCityName(cityName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.city_name
     *
     * @param cityName the value for supply_chain..province_contract_info_new.city_name
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.internet_contract_code
     *
     * @return the value of supply_chain..province_contract_info_new.internet_contract_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getInternetContractCode() {
        return internetContractCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withInternetContractCode(String internetContractCode) {
        this.setInternetContractCode(internetContractCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.internet_contract_code
     *
     * @param internetContractCode the value for supply_chain..province_contract_info_new.internet_contract_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setInternetContractCode(String internetContractCode) {
        this.internetContractCode = internetContractCode == null ? null : internetContractCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.dept_code
     *
     * @return the value of supply_chain..province_contract_info_new.dept_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getDeptCode() {
        return deptCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withDeptCode(String deptCode) {
        this.setDeptCode(deptCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.dept_code
     *
     * @param deptCode the value for supply_chain..province_contract_info_new.dept_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode == null ? null : deptCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.dept_name
     *
     * @return the value of supply_chain..province_contract_info_new.dept_name
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getDeptName() {
        return deptName;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withDeptName(String deptName) {
        this.setDeptName(deptName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.dept_name
     *
     * @param deptName the value for supply_chain..province_contract_info_new.dept_name
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setDeptName(String deptName) {
        this.deptName = deptName == null ? null : deptName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.created_id
     *
     * @return the value of supply_chain..province_contract_info_new.created_id
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getCreatedId() {
        return createdId;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withCreatedId(String createdId) {
        this.setCreatedId(createdId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.created_id
     *
     * @param createdId the value for supply_chain..province_contract_info_new.created_id
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setCreatedId(String createdId) {
        this.createdId = createdId == null ? null : createdId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.created_name
     *
     * @return the value of supply_chain..province_contract_info_new.created_name
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getCreatedName() {
        return createdName;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withCreatedName(String createdName) {
        this.setCreatedName(createdName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.created_name
     *
     * @param createdName the value for supply_chain..province_contract_info_new.created_name
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setCreatedName(String createdName) {
        this.createdName = createdName == null ? null : createdName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.mtl_type_code
     *
     * @return the value of supply_chain..province_contract_info_new.mtl_type_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getMtlTypeCode() {
        return mtlTypeCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withMtlTypeCode(String mtlTypeCode) {
        this.setMtlTypeCode(mtlTypeCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.mtl_type_code
     *
     * @param mtlTypeCode the value for supply_chain..province_contract_info_new.mtl_type_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setMtlTypeCode(String mtlTypeCode) {
        this.mtlTypeCode = mtlTypeCode == null ? null : mtlTypeCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.small_item
     *
     * @return the value of supply_chain..province_contract_info_new.small_item
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getSmallItem() {
        return smallItem;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withSmallItem(String smallItem) {
        this.setSmallItem(smallItem);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.small_item
     *
     * @param smallItem the value for supply_chain..province_contract_info_new.small_item
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setSmallItem(String smallItem) {
        this.smallItem = smallItem == null ? null : smallItem.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.contract_code
     *
     * @return the value of supply_chain..province_contract_info_new.contract_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getContractCode() {
        return contractCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withContractCode(String contractCode) {
        this.setContractCode(contractCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.contract_code
     *
     * @param contractCode the value for supply_chain..province_contract_info_new.contract_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setContractCode(String contractCode) {
        this.contractCode = contractCode == null ? null : contractCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.vendor_code
     *
     * @return the value of supply_chain..province_contract_info_new.vendor_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getVendorCode() {
        return vendorCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withVendorCode(String vendorCode) {
        this.setVendorCode(vendorCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.vendor_code
     *
     * @param vendorCode the value for supply_chain..province_contract_info_new.vendor_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode == null ? null : vendorCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.req_type
     *
     * @return the value of supply_chain..province_contract_info_new.req_type
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getReqType() {
        return reqType;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withReqType(String reqType) {
        this.setReqType(reqType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.req_type
     *
     * @param reqType the value for supply_chain..province_contract_info_new.req_type
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setReqType(String reqType) {
        this.reqType = reqType == null ? null : reqType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.req_second_type
     *
     * @return the value of supply_chain..province_contract_info_new.req_second_type
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getReqSecondType() {
        return reqSecondType;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withReqSecondType(String reqSecondType) {
        this.setReqSecondType(reqSecondType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.req_second_type
     *
     * @param reqSecondType the value for supply_chain..province_contract_info_new.req_second_type
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setReqSecondType(String reqSecondType) {
        this.reqSecondType = reqSecondType == null ? null : reqSecondType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.rcv_user_num
     *
     * @return the value of supply_chain..province_contract_info_new.rcv_user_num
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getRcvUserNum() {
        return rcvUserNum;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withRcvUserNum(String rcvUserNum) {
        this.setRcvUserNum(rcvUserNum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.rcv_user_num
     *
     * @param rcvUserNum the value for supply_chain..province_contract_info_new.rcv_user_num
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setRcvUserNum(String rcvUserNum) {
        this.rcvUserNum = rcvUserNum == null ? null : rcvUserNum.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.rcv_user
     *
     * @return the value of supply_chain..province_contract_info_new.rcv_user
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getRcvUser() {
        return rcvUser;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withRcvUser(String rcvUser) {
        this.setRcvUser(rcvUser);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.rcv_user
     *
     * @param rcvUser the value for supply_chain..province_contract_info_new.rcv_user
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setRcvUser(String rcvUser) {
        this.rcvUser = rcvUser == null ? null : rcvUser.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.rcv_contact_phone
     *
     * @return the value of supply_chain..province_contract_info_new.rcv_contact_phone
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getRcvContactPhone() {
        return rcvContactPhone;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withRcvContactPhone(String rcvContactPhone) {
        this.setRcvContactPhone(rcvContactPhone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.rcv_contact_phone
     *
     * @param rcvContactPhone the value for supply_chain..province_contract_info_new.rcv_contact_phone
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setRcvContactPhone(String rcvContactPhone) {
        this.rcvContactPhone = rcvContactPhone == null ? null : rcvContactPhone.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.rcv_site_address
     *
     * @return the value of supply_chain..province_contract_info_new.rcv_site_address
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getRcvSiteAddress() {
        return rcvSiteAddress;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withRcvSiteAddress(String rcvSiteAddress) {
        this.setRcvSiteAddress(rcvSiteAddress);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.rcv_site_address
     *
     * @param rcvSiteAddress the value for supply_chain..province_contract_info_new.rcv_site_address
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setRcvSiteAddress(String rcvSiteAddress) {
        this.rcvSiteAddress = rcvSiteAddress == null ? null : rcvSiteAddress.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.mis_body
     *
     * @return the value of supply_chain..province_contract_info_new.mis_body
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getMisBody() {
        return misBody;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withMisBody(String misBody) {
        this.setMisBody(misBody);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.mis_body
     *
     * @param misBody the value for supply_chain..province_contract_info_new.mis_body
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setMisBody(String misBody) {
        this.misBody = misBody == null ? null : misBody.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.org_id
     *
     * @return the value of supply_chain..province_contract_info_new.org_id
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getOrgId() {
        return orgId;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withOrgId(String orgId) {
        this.setOrgId(orgId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.org_id
     *
     * @param orgId the value for supply_chain..province_contract_info_new.org_id
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setOrgId(String orgId) {
        this.orgId = orgId == null ? null : orgId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.exp_type
     *
     * @return the value of supply_chain..province_contract_info_new.exp_type
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getExpType() {
        return expType;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withExpType(String expType) {
        this.setExpType(expType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.exp_type
     *
     * @param expType the value for supply_chain..province_contract_info_new.exp_type
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setExpType(String expType) {
        this.expType = expType == null ? null : expType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.reimbursement_mode
     *
     * @return the value of supply_chain..province_contract_info_new.reimbursement_mode
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getReimbursementMode() {
        return reimbursementMode;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withReimbursementMode(String reimbursementMode) {
        this.setReimbursementMode(reimbursementMode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.reimbursement_mode
     *
     * @param reimbursementMode the value for supply_chain..province_contract_info_new.reimbursement_mode
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setReimbursementMode(String reimbursementMode) {
        this.reimbursementMode = reimbursementMode == null ? null : reimbursementMode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.arrival_time_model
     *
     * @return the value of supply_chain..province_contract_info_new.arrival_time_model
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getArrivalTimeModel() {
        return arrivalTimeModel;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withArrivalTimeModel(String arrivalTimeModel) {
        this.setArrivalTimeModel(arrivalTimeModel);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.arrival_time_model
     *
     * @param arrivalTimeModel the value for supply_chain..province_contract_info_new.arrival_time_model
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setArrivalTimeModel(String arrivalTimeModel) {
        this.arrivalTimeModel = arrivalTimeModel == null ? null : arrivalTimeModel.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.arrival_days
     *
     * @return the value of supply_chain..province_contract_info_new.arrival_days
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public Integer getArrivalDays() {
        return arrivalDays;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withArrivalDays(Integer arrivalDays) {
        this.setArrivalDays(arrivalDays);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.arrival_days
     *
     * @param arrivalDays the value for supply_chain..province_contract_info_new.arrival_days
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setArrivalDays(Integer arrivalDays) {
        this.arrivalDays = arrivalDays;
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.organization_code
     *
     * @return the value of supply_chain..province_contract_info_new.organization_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getOrganizationCode() {
        return organizationCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withOrganizationCode(String organizationCode) {
        this.setOrganizationCode(organizationCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.organization_code
     *
     * @param organizationCode the value for supply_chain..province_contract_info_new.organization_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode == null ? null : organizationCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.item_type
     *
     * @return the value of supply_chain..province_contract_info_new.item_type
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getItemType() {
        return itemType;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withItemType(String itemType) {
        this.setItemType(itemType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.item_type
     *
     * @param itemType the value for supply_chain..province_contract_info_new.item_type
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setItemType(String itemType) {
        this.itemType = itemType == null ? null : itemType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.budget_id
     *
     * @return the value of supply_chain..province_contract_info_new.budget_id
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getBudgetId() {
        return budgetId;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withBudgetId(String budgetId) {
        this.setBudgetId(budgetId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.budget_id
     *
     * @param budgetId the value for supply_chain..province_contract_info_new.budget_id
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setBudgetId(String budgetId) {
        this.budgetId = budgetId == null ? null : budgetId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.budget_year
     *
     * @return the value of supply_chain..province_contract_info_new.budget_year
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getBudgetYear() {
        return budgetYear;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withBudgetYear(String budgetYear) {
        this.setBudgetYear(budgetYear);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.budget_year
     *
     * @param budgetYear the value for supply_chain..province_contract_info_new.budget_year
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setBudgetYear(String budgetYear) {
        this.budgetYear = budgetYear == null ? null : budgetYear.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.activity_code
     *
     * @return the value of supply_chain..province_contract_info_new.activity_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getActivityCode() {
        return activityCode;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withActivityCode(String activityCode) {
        this.setActivityCode(activityCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.activity_code
     *
     * @param activityCode the value for supply_chain..province_contract_info_new.activity_code
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode == null ? null : activityCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.cost_center
     *
     * @return the value of supply_chain..province_contract_info_new.cost_center
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getCostCenter() {
        return costCenter;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withCostCenter(String costCenter) {
        this.setCostCenter(costCenter);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.cost_center
     *
     * @param costCenter the value for supply_chain..province_contract_info_new.cost_center
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setCostCenter(String costCenter) {
        this.costCenter = costCenter == null ? null : costCenter.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.expense_account
     *
     * @return the value of supply_chain..province_contract_info_new.expense_account
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getExpenseAccount() {
        return expenseAccount;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withExpenseAccount(String expenseAccount) {
        this.setExpenseAccount(expenseAccount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.expense_account
     *
     * @param expenseAccount the value for supply_chain..province_contract_info_new.expense_account
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setExpenseAccount(String expenseAccount) {
        this.expenseAccount = expenseAccount == null ? null : expenseAccount.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.cost_subject
     *
     * @return the value of supply_chain..province_contract_info_new.cost_subject
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getCostSubject() {
        return costSubject;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withCostSubject(String costSubject) {
        this.setCostSubject(costSubject);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.cost_subject
     *
     * @param costSubject the value for supply_chain..province_contract_info_new.cost_subject
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setCostSubject(String costSubject) {
        this.costSubject = costSubject == null ? null : costSubject.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.manage_activity
     *
     * @return the value of supply_chain..province_contract_info_new.manage_activity
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getManageActivity() {
        return manageActivity;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withManageActivity(String manageActivity) {
        this.setManageActivity(manageActivity);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.manage_activity
     *
     * @param manageActivity the value for supply_chain..province_contract_info_new.manage_activity
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setManageActivity(String manageActivity) {
        this.manageActivity = manageActivity == null ? null : manageActivity.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.manage_market
     *
     * @return the value of supply_chain..province_contract_info_new.manage_market
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getManageMarket() {
        return manageMarket;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withManageMarket(String manageMarket) {
        this.setManageMarket(manageMarket);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.manage_market
     *
     * @param manageMarket the value for supply_chain..province_contract_info_new.manage_market
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setManageMarket(String manageMarket) {
        this.manageMarket = manageMarket == null ? null : manageMarket.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.manage_product
     *
     * @return the value of supply_chain..province_contract_info_new.manage_product
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public String getManageProduct() {
        return manageProduct;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withManageProduct(String manageProduct) {
        this.setManageProduct(manageProduct);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.manage_product
     *
     * @param manageProduct the value for supply_chain..province_contract_info_new.manage_product
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setManageProduct(String manageProduct) {
        this.manageProduct = manageProduct == null ? null : manageProduct.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.create_time
     *
     * @return the value of supply_chain..province_contract_info_new.create_time
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.create_time
     *
     * @param createTime the value for supply_chain..province_contract_info_new.create_time
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info_new.update_time
     *
     * @return the value of supply_chain..province_contract_info_new.update_time
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public ProvinceContractInfoNew withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info_new.update_time
     *
     * @param updateTime the value for supply_chain..province_contract_info_new.update_time
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", contractType=").append(contractType);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", provinceName=").append(provinceName);
        sb.append(", cityCode=").append(cityCode);
        sb.append(", cityName=").append(cityName);
        sb.append(", internetContractCode=").append(internetContractCode);
        sb.append(", deptCode=").append(deptCode);
        sb.append(", deptName=").append(deptName);
        sb.append(", createdId=").append(createdId);
        sb.append(", createdName=").append(createdName);
        sb.append(", mtlTypeCode=").append(mtlTypeCode);
        sb.append(", smallItem=").append(smallItem);
        sb.append(", contractCode=").append(contractCode);
        sb.append(", vendorCode=").append(vendorCode);
        sb.append(", reqType=").append(reqType);
        sb.append(", reqSecondType=").append(reqSecondType);
        sb.append(", rcvUserNum=").append(rcvUserNum);
        sb.append(", rcvUser=").append(rcvUser);
        sb.append(", rcvContactPhone=").append(rcvContactPhone);
        sb.append(", rcvSiteAddress=").append(rcvSiteAddress);
        sb.append(", misBody=").append(misBody);
        sb.append(", orgId=").append(orgId);
        sb.append(", expType=").append(expType);
        sb.append(", reimbursementMode=").append(reimbursementMode);
        sb.append(", arrivalTimeModel=").append(arrivalTimeModel);
        sb.append(", arrivalDays=").append(arrivalDays);
        sb.append(", organizationCode=").append(organizationCode);
        sb.append(", itemType=").append(itemType);
        sb.append(", budgetId=").append(budgetId);
        sb.append(", budgetYear=").append(budgetYear);
        sb.append(", activityCode=").append(activityCode);
        sb.append(", costCenter=").append(costCenter);
        sb.append(", expenseAccount=").append(expenseAccount);
        sb.append(", costSubject=").append(costSubject);
        sb.append(", manageActivity=").append(manageActivity);
        sb.append(", manageMarket=").append(manageMarket);
        sb.append(", manageProduct=").append(manageProduct);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ProvinceContractInfoNew other = (ProvinceContractInfoNew) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getContractType() == null ? other.getContractType() == null : this.getContractType().equals(other.getContractType()))
            && (this.getProvinceCode() == null ? other.getProvinceCode() == null : this.getProvinceCode().equals(other.getProvinceCode()))
            && (this.getProvinceName() == null ? other.getProvinceName() == null : this.getProvinceName().equals(other.getProvinceName()))
            && (this.getCityCode() == null ? other.getCityCode() == null : this.getCityCode().equals(other.getCityCode()))
            && (this.getCityName() == null ? other.getCityName() == null : this.getCityName().equals(other.getCityName()))
            && (this.getInternetContractCode() == null ? other.getInternetContractCode() == null : this.getInternetContractCode().equals(other.getInternetContractCode()))
            && (this.getDeptCode() == null ? other.getDeptCode() == null : this.getDeptCode().equals(other.getDeptCode()))
            && (this.getDeptName() == null ? other.getDeptName() == null : this.getDeptName().equals(other.getDeptName()))
            && (this.getCreatedId() == null ? other.getCreatedId() == null : this.getCreatedId().equals(other.getCreatedId()))
            && (this.getCreatedName() == null ? other.getCreatedName() == null : this.getCreatedName().equals(other.getCreatedName()))
            && (this.getMtlTypeCode() == null ? other.getMtlTypeCode() == null : this.getMtlTypeCode().equals(other.getMtlTypeCode()))
            && (this.getSmallItem() == null ? other.getSmallItem() == null : this.getSmallItem().equals(other.getSmallItem()))
            && (this.getContractCode() == null ? other.getContractCode() == null : this.getContractCode().equals(other.getContractCode()))
            && (this.getVendorCode() == null ? other.getVendorCode() == null : this.getVendorCode().equals(other.getVendorCode()))
            && (this.getReqType() == null ? other.getReqType() == null : this.getReqType().equals(other.getReqType()))
            && (this.getReqSecondType() == null ? other.getReqSecondType() == null : this.getReqSecondType().equals(other.getReqSecondType()))
            && (this.getRcvUserNum() == null ? other.getRcvUserNum() == null : this.getRcvUserNum().equals(other.getRcvUserNum()))
            && (this.getRcvUser() == null ? other.getRcvUser() == null : this.getRcvUser().equals(other.getRcvUser()))
            && (this.getRcvContactPhone() == null ? other.getRcvContactPhone() == null : this.getRcvContactPhone().equals(other.getRcvContactPhone()))
            && (this.getRcvSiteAddress() == null ? other.getRcvSiteAddress() == null : this.getRcvSiteAddress().equals(other.getRcvSiteAddress()))
            && (this.getMisBody() == null ? other.getMisBody() == null : this.getMisBody().equals(other.getMisBody()))
            && (this.getOrgId() == null ? other.getOrgId() == null : this.getOrgId().equals(other.getOrgId()))
            && (this.getExpType() == null ? other.getExpType() == null : this.getExpType().equals(other.getExpType()))
            && (this.getReimbursementMode() == null ? other.getReimbursementMode() == null : this.getReimbursementMode().equals(other.getReimbursementMode()))
            && (this.getArrivalTimeModel() == null ? other.getArrivalTimeModel() == null : this.getArrivalTimeModel().equals(other.getArrivalTimeModel()))
            && (this.getArrivalDays() == null ? other.getArrivalDays() == null : this.getArrivalDays().equals(other.getArrivalDays()))
            && (this.getOrganizationCode() == null ? other.getOrganizationCode() == null : this.getOrganizationCode().equals(other.getOrganizationCode()))
            && (this.getItemType() == null ? other.getItemType() == null : this.getItemType().equals(other.getItemType()))
            && (this.getBudgetId() == null ? other.getBudgetId() == null : this.getBudgetId().equals(other.getBudgetId()))
            && (this.getBudgetYear() == null ? other.getBudgetYear() == null : this.getBudgetYear().equals(other.getBudgetYear()))
            && (this.getActivityCode() == null ? other.getActivityCode() == null : this.getActivityCode().equals(other.getActivityCode()))
            && (this.getCostCenter() == null ? other.getCostCenter() == null : this.getCostCenter().equals(other.getCostCenter()))
            && (this.getExpenseAccount() == null ? other.getExpenseAccount() == null : this.getExpenseAccount().equals(other.getExpenseAccount()))
            && (this.getCostSubject() == null ? other.getCostSubject() == null : this.getCostSubject().equals(other.getCostSubject()))
            && (this.getManageActivity() == null ? other.getManageActivity() == null : this.getManageActivity().equals(other.getManageActivity()))
            && (this.getManageMarket() == null ? other.getManageMarket() == null : this.getManageMarket().equals(other.getManageMarket()))
            && (this.getManageProduct() == null ? other.getManageProduct() == null : this.getManageProduct().equals(other.getManageProduct()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getContractType() == null) ? 0 : getContractType().hashCode());
        result = prime * result + ((getProvinceCode() == null) ? 0 : getProvinceCode().hashCode());
        result = prime * result + ((getProvinceName() == null) ? 0 : getProvinceName().hashCode());
        result = prime * result + ((getCityCode() == null) ? 0 : getCityCode().hashCode());
        result = prime * result + ((getCityName() == null) ? 0 : getCityName().hashCode());
        result = prime * result + ((getInternetContractCode() == null) ? 0 : getInternetContractCode().hashCode());
        result = prime * result + ((getDeptCode() == null) ? 0 : getDeptCode().hashCode());
        result = prime * result + ((getDeptName() == null) ? 0 : getDeptName().hashCode());
        result = prime * result + ((getCreatedId() == null) ? 0 : getCreatedId().hashCode());
        result = prime * result + ((getCreatedName() == null) ? 0 : getCreatedName().hashCode());
        result = prime * result + ((getMtlTypeCode() == null) ? 0 : getMtlTypeCode().hashCode());
        result = prime * result + ((getSmallItem() == null) ? 0 : getSmallItem().hashCode());
        result = prime * result + ((getContractCode() == null) ? 0 : getContractCode().hashCode());
        result = prime * result + ((getVendorCode() == null) ? 0 : getVendorCode().hashCode());
        result = prime * result + ((getReqType() == null) ? 0 : getReqType().hashCode());
        result = prime * result + ((getReqSecondType() == null) ? 0 : getReqSecondType().hashCode());
        result = prime * result + ((getRcvUserNum() == null) ? 0 : getRcvUserNum().hashCode());
        result = prime * result + ((getRcvUser() == null) ? 0 : getRcvUser().hashCode());
        result = prime * result + ((getRcvContactPhone() == null) ? 0 : getRcvContactPhone().hashCode());
        result = prime * result + ((getRcvSiteAddress() == null) ? 0 : getRcvSiteAddress().hashCode());
        result = prime * result + ((getMisBody() == null) ? 0 : getMisBody().hashCode());
        result = prime * result + ((getOrgId() == null) ? 0 : getOrgId().hashCode());
        result = prime * result + ((getExpType() == null) ? 0 : getExpType().hashCode());
        result = prime * result + ((getReimbursementMode() == null) ? 0 : getReimbursementMode().hashCode());
        result = prime * result + ((getArrivalTimeModel() == null) ? 0 : getArrivalTimeModel().hashCode());
        result = prime * result + ((getArrivalDays() == null) ? 0 : getArrivalDays().hashCode());
        result = prime * result + ((getOrganizationCode() == null) ? 0 : getOrganizationCode().hashCode());
        result = prime * result + ((getItemType() == null) ? 0 : getItemType().hashCode());
        result = prime * result + ((getBudgetId() == null) ? 0 : getBudgetId().hashCode());
        result = prime * result + ((getBudgetYear() == null) ? 0 : getBudgetYear().hashCode());
        result = prime * result + ((getActivityCode() == null) ? 0 : getActivityCode().hashCode());
        result = prime * result + ((getCostCenter() == null) ? 0 : getCostCenter().hashCode());
        result = prime * result + ((getExpenseAccount() == null) ? 0 : getExpenseAccount().hashCode());
        result = prime * result + ((getCostSubject() == null) ? 0 : getCostSubject().hashCode());
        result = prime * result + ((getManageActivity() == null) ? 0 : getManageActivity().hashCode());
        result = prime * result + ((getManageMarket() == null) ? 0 : getManageMarket().hashCode());
        result = prime * result + ((getManageProduct() == null) ? 0 : getManageProduct().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     * corresponding to the database table supply_chain..province_contract_info_new
     *
     * @mbg.generated Mon Mar 17 11:12:31 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        contractType("contract_type", "contractType", "VARCHAR", false),
        provinceCode("province_code", "provinceCode", "VARCHAR", false),
        provinceName("province_name", "provinceName", "VARCHAR", false),
        cityCode("city_code", "cityCode", "VARCHAR", false),
        cityName("city_name", "cityName", "VARCHAR", false),
        internetContractCode("internet_contract_code", "internetContractCode", "VARCHAR", false),
        deptCode("dept_code", "deptCode", "VARCHAR", false),
        deptName("dept_name", "deptName", "VARCHAR", false),
        createdId("created_id", "createdId", "VARCHAR", false),
        createdName("created_name", "createdName", "VARCHAR", false),
        mtlTypeCode("mtl_type_code", "mtlTypeCode", "VARCHAR", false),
        smallItem("small_item", "smallItem", "VARCHAR", false),
        contractCode("contract_code", "contractCode", "VARCHAR", false),
        vendorCode("vendor_code", "vendorCode", "VARCHAR", false),
        reqType("req_type", "reqType", "VARCHAR", false),
        reqSecondType("req_second_type", "reqSecondType", "VARCHAR", false),
        rcvUserNum("rcv_user_num", "rcvUserNum", "VARCHAR", false),
        rcvUser("rcv_user", "rcvUser", "VARCHAR", false),
        rcvContactPhone("rcv_contact_phone", "rcvContactPhone", "VARCHAR", false),
        rcvSiteAddress("rcv_site_address", "rcvSiteAddress", "VARCHAR", false),
        misBody("mis_body", "misBody", "VARCHAR", false),
        orgId("org_id", "orgId", "VARCHAR", false),
        expType("exp_type", "expType", "VARCHAR", false),
        reimbursementMode("reimbursement_mode", "reimbursementMode", "VARCHAR", false),
        arrivalTimeModel("arrival_time_model", "arrivalTimeModel", "VARCHAR", false),
        arrivalDays("arrival_days", "arrivalDays", "INTEGER", false),
        organizationCode("organization_code", "organizationCode", "VARCHAR", false),
        itemType("item_type", "itemType", "VARCHAR", false),
        budgetId("budget_id", "budgetId", "VARCHAR", false),
        budgetYear("budget_year", "budgetYear", "VARCHAR", false),
        activityCode("activity_code", "activityCode", "VARCHAR", false),
        costCenter("cost_center", "costCenter", "VARCHAR", false),
        expenseAccount("expense_account", "expenseAccount", "VARCHAR", false),
        costSubject("cost_subject", "costSubject", "VARCHAR", false),
        manageActivity("manage_activity", "manageActivity", "VARCHAR", false),
        manageMarket("manage_market", "manageMarket", "VARCHAR", false),
        manageProduct("manage_product", "manageProduct", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         * Corresponding to the database table supply_chain..province_contract_info_new
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..province_contract_info_new
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..province_contract_info_new
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        private final String column;

        /**
         * Corresponding to the database table supply_chain..province_contract_info_new
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         * Corresponding to the database table supply_chain..province_contract_info_new
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        private final String javaProperty;

        /**
         * Corresponding to the database table supply_chain..province_contract_info_new
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Mar 17 11:12:31 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}