package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class K3syncStatisProvince implements Serializable {
    private String id;

    private String contractNum;

    private String contractName;

    private String contractDept;

    private String contractStatisType;

    private String contractSeller;

    private String contractType;

    private Integer contractSettleMode;

    private String moneyUnit;

    private String sellUnit;

    private String productType;

    private String orderProvinceName;

    private String orderProvinceCode;

    private String orderCount;

    private Long totalPrice;

    private String k3RetNum;

    private String k3SyncStatus;

    private String k3CommitStatus;

    private String sellerOrgId;

    private String sellerTeamId;

    private String sellerDeptId;

    private String sellerPhone;

    private String project;

    private String subProject;

    private String costCenter;

    private Date syncSucTime;

    private Date commitSucTime;

    private String customCode;

    private String buyerProvince;

    private String buyerProvinceCode;

    private String buyerCity;

    private String buyerCityCode;

    private Date createTime;

    private String relatedOrderIds;

    private String relatedK3OrderIds;

    private String proRetNum;

    private String proSyncStatus;

    private String proSubmitAccountStatus;

    private String syncK3UserName;

    private String commitK3UserName;

    private String materialDept;

    private String proDataCode;

    private Integer k3Status;

    private Integer proMaterialStatus;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public K3syncStatisProvince withId(String id) {
        this.setId(id);
        return this;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getContractNum() {
        return contractNum;
    }

    public K3syncStatisProvince withContractNum(String contractNum) {
        this.setContractNum(contractNum);
        return this;
    }

    public void setContractNum(String contractNum) {
        this.contractNum = contractNum == null ? null : contractNum.trim();
    }

    public String getContractName() {
        return contractName;
    }

    public K3syncStatisProvince withContractName(String contractName) {
        this.setContractName(contractName);
        return this;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName == null ? null : contractName.trim();
    }

    public String getContractDept() {
        return contractDept;
    }

    public K3syncStatisProvince withContractDept(String contractDept) {
        this.setContractDept(contractDept);
        return this;
    }

    public void setContractDept(String contractDept) {
        this.contractDept = contractDept == null ? null : contractDept.trim();
    }

    public String getContractStatisType() {
        return contractStatisType;
    }

    public K3syncStatisProvince withContractStatisType(String contractStatisType) {
        this.setContractStatisType(contractStatisType);
        return this;
    }

    public void setContractStatisType(String contractStatisType) {
        this.contractStatisType = contractStatisType == null ? null : contractStatisType.trim();
    }

    public String getContractSeller() {
        return contractSeller;
    }

    public K3syncStatisProvince withContractSeller(String contractSeller) {
        this.setContractSeller(contractSeller);
        return this;
    }

    public void setContractSeller(String contractSeller) {
        this.contractSeller = contractSeller == null ? null : contractSeller.trim();
    }

    public String getContractType() {
        return contractType;
    }

    public K3syncStatisProvince withContractType(String contractType) {
        this.setContractType(contractType);
        return this;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType == null ? null : contractType.trim();
    }

    public Integer getContractSettleMode() {
        return contractSettleMode;
    }

    public K3syncStatisProvince withContractSettleMode(Integer contractSettleMode) {
        this.setContractSettleMode(contractSettleMode);
        return this;
    }

    public void setContractSettleMode(Integer contractSettleMode) {
        this.contractSettleMode = contractSettleMode;
    }

    public String getMoneyUnit() {
        return moneyUnit;
    }

    public K3syncStatisProvince withMoneyUnit(String moneyUnit) {
        this.setMoneyUnit(moneyUnit);
        return this;
    }

    public void setMoneyUnit(String moneyUnit) {
        this.moneyUnit = moneyUnit == null ? null : moneyUnit.trim();
    }

    public String getSellUnit() {
        return sellUnit;
    }

    public K3syncStatisProvince withSellUnit(String sellUnit) {
        this.setSellUnit(sellUnit);
        return this;
    }

    public void setSellUnit(String sellUnit) {
        this.sellUnit = sellUnit == null ? null : sellUnit.trim();
    }

    public String getProductType() {
        return productType;
    }

    public K3syncStatisProvince withProductType(String productType) {
        this.setProductType(productType);
        return this;
    }

    public void setProductType(String productType) {
        this.productType = productType == null ? null : productType.trim();
    }

    public String getOrderProvinceName() {
        return orderProvinceName;
    }

    public K3syncStatisProvince withOrderProvinceName(String orderProvinceName) {
        this.setOrderProvinceName(orderProvinceName);
        return this;
    }

    public void setOrderProvinceName(String orderProvinceName) {
        this.orderProvinceName = orderProvinceName == null ? null : orderProvinceName.trim();
    }

    public String getOrderProvinceCode() {
        return orderProvinceCode;
    }

    public K3syncStatisProvince withOrderProvinceCode(String orderProvinceCode) {
        this.setOrderProvinceCode(orderProvinceCode);
        return this;
    }

    public void setOrderProvinceCode(String orderProvinceCode) {
        this.orderProvinceCode = orderProvinceCode == null ? null : orderProvinceCode.trim();
    }

    public String getOrderCount() {
        return orderCount;
    }

    public K3syncStatisProvince withOrderCount(String orderCount) {
        this.setOrderCount(orderCount);
        return this;
    }

    public void setOrderCount(String orderCount) {
        this.orderCount = orderCount == null ? null : orderCount.trim();
    }

    public Long getTotalPrice() {
        return totalPrice;
    }

    public K3syncStatisProvince withTotalPrice(Long totalPrice) {
        this.setTotalPrice(totalPrice);
        return this;
    }

    public void setTotalPrice(Long totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getK3RetNum() {
        return k3RetNum;
    }

    public K3syncStatisProvince withK3RetNum(String k3RetNum) {
        this.setK3RetNum(k3RetNum);
        return this;
    }

    public void setK3RetNum(String k3RetNum) {
        this.k3RetNum = k3RetNum == null ? null : k3RetNum.trim();
    }

    public String getK3SyncStatus() {
        return k3SyncStatus;
    }

    public K3syncStatisProvince withK3SyncStatus(String k3SyncStatus) {
        this.setK3SyncStatus(k3SyncStatus);
        return this;
    }

    public void setK3SyncStatus(String k3SyncStatus) {
        this.k3SyncStatus = k3SyncStatus == null ? null : k3SyncStatus.trim();
    }

    public String getK3CommitStatus() {
        return k3CommitStatus;
    }

    public K3syncStatisProvince withK3CommitStatus(String k3CommitStatus) {
        this.setK3CommitStatus(k3CommitStatus);
        return this;
    }

    public void setK3CommitStatus(String k3CommitStatus) {
        this.k3CommitStatus = k3CommitStatus == null ? null : k3CommitStatus.trim();
    }

    public String getSellerOrgId() {
        return sellerOrgId;
    }

    public K3syncStatisProvince withSellerOrgId(String sellerOrgId) {
        this.setSellerOrgId(sellerOrgId);
        return this;
    }

    public void setSellerOrgId(String sellerOrgId) {
        this.sellerOrgId = sellerOrgId == null ? null : sellerOrgId.trim();
    }

    public String getSellerTeamId() {
        return sellerTeamId;
    }

    public K3syncStatisProvince withSellerTeamId(String sellerTeamId) {
        this.setSellerTeamId(sellerTeamId);
        return this;
    }

    public void setSellerTeamId(String sellerTeamId) {
        this.sellerTeamId = sellerTeamId == null ? null : sellerTeamId.trim();
    }

    public String getSellerDeptId() {
        return sellerDeptId;
    }

    public K3syncStatisProvince withSellerDeptId(String sellerDeptId) {
        this.setSellerDeptId(sellerDeptId);
        return this;
    }

    public void setSellerDeptId(String sellerDeptId) {
        this.sellerDeptId = sellerDeptId == null ? null : sellerDeptId.trim();
    }

    public String getSellerPhone() {
        return sellerPhone;
    }

    public K3syncStatisProvince withSellerPhone(String sellerPhone) {
        this.setSellerPhone(sellerPhone);
        return this;
    }

    public void setSellerPhone(String sellerPhone) {
        this.sellerPhone = sellerPhone == null ? null : sellerPhone.trim();
    }

    public String getProject() {
        return project;
    }

    public K3syncStatisProvince withProject(String project) {
        this.setProject(project);
        return this;
    }

    public void setProject(String project) {
        this.project = project == null ? null : project.trim();
    }

    public String getSubProject() {
        return subProject;
    }

    public K3syncStatisProvince withSubProject(String subProject) {
        this.setSubProject(subProject);
        return this;
    }

    public void setSubProject(String subProject) {
        this.subProject = subProject == null ? null : subProject.trim();
    }

    public String getCostCenter() {
        return costCenter;
    }

    public K3syncStatisProvince withCostCenter(String costCenter) {
        this.setCostCenter(costCenter);
        return this;
    }

    public void setCostCenter(String costCenter) {
        this.costCenter = costCenter == null ? null : costCenter.trim();
    }

    public Date getSyncSucTime() {
        return syncSucTime;
    }

    public K3syncStatisProvince withSyncSucTime(Date syncSucTime) {
        this.setSyncSucTime(syncSucTime);
        return this;
    }

    public void setSyncSucTime(Date syncSucTime) {
        this.syncSucTime = syncSucTime;
    }

    public Date getCommitSucTime() {
        return commitSucTime;
    }

    public K3syncStatisProvince withCommitSucTime(Date commitSucTime) {
        this.setCommitSucTime(commitSucTime);
        return this;
    }

    public void setCommitSucTime(Date commitSucTime) {
        this.commitSucTime = commitSucTime;
    }

    public String getCustomCode() {
        return customCode;
    }

    public K3syncStatisProvince withCustomCode(String customCode) {
        this.setCustomCode(customCode);
        return this;
    }

    public void setCustomCode(String customCode) {
        this.customCode = customCode == null ? null : customCode.trim();
    }

    public String getBuyerProvince() {
        return buyerProvince;
    }

    public K3syncStatisProvince withBuyerProvince(String buyerProvince) {
        this.setBuyerProvince(buyerProvince);
        return this;
    }

    public void setBuyerProvince(String buyerProvince) {
        this.buyerProvince = buyerProvince == null ? null : buyerProvince.trim();
    }

    public String getBuyerProvinceCode() {
        return buyerProvinceCode;
    }

    public K3syncStatisProvince withBuyerProvinceCode(String buyerProvinceCode) {
        this.setBuyerProvinceCode(buyerProvinceCode);
        return this;
    }

    public void setBuyerProvinceCode(String buyerProvinceCode) {
        this.buyerProvinceCode = buyerProvinceCode == null ? null : buyerProvinceCode.trim();
    }

    public String getBuyerCity() {
        return buyerCity;
    }

    public K3syncStatisProvince withBuyerCity(String buyerCity) {
        this.setBuyerCity(buyerCity);
        return this;
    }

    public void setBuyerCity(String buyerCity) {
        this.buyerCity = buyerCity == null ? null : buyerCity.trim();
    }

    public String getBuyerCityCode() {
        return buyerCityCode;
    }

    public K3syncStatisProvince withBuyerCityCode(String buyerCityCode) {
        this.setBuyerCityCode(buyerCityCode);
        return this;
    }

    public void setBuyerCityCode(String buyerCityCode) {
        this.buyerCityCode = buyerCityCode == null ? null : buyerCityCode.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public K3syncStatisProvince withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getRelatedOrderIds() {
        return relatedOrderIds;
    }

    public K3syncStatisProvince withRelatedOrderIds(String relatedOrderIds) {
        this.setRelatedOrderIds(relatedOrderIds);
        return this;
    }

    public void setRelatedOrderIds(String relatedOrderIds) {
        this.relatedOrderIds = relatedOrderIds == null ? null : relatedOrderIds.trim();
    }

    public String getRelatedK3OrderIds() {
        return relatedK3OrderIds;
    }

    public K3syncStatisProvince withRelatedK3OrderIds(String relatedK3OrderIds) {
        this.setRelatedK3OrderIds(relatedK3OrderIds);
        return this;
    }

    public void setRelatedK3OrderIds(String relatedK3OrderIds) {
        this.relatedK3OrderIds = relatedK3OrderIds == null ? null : relatedK3OrderIds.trim();
    }

    public String getProRetNum() {
        return proRetNum;
    }

    public K3syncStatisProvince withProRetNum(String proRetNum) {
        this.setProRetNum(proRetNum);
        return this;
    }

    public void setProRetNum(String proRetNum) {
        this.proRetNum = proRetNum == null ? null : proRetNum.trim();
    }

    public String getProSyncStatus() {
        return proSyncStatus;
    }

    public K3syncStatisProvince withProSyncStatus(String proSyncStatus) {
        this.setProSyncStatus(proSyncStatus);
        return this;
    }

    public void setProSyncStatus(String proSyncStatus) {
        this.proSyncStatus = proSyncStatus == null ? null : proSyncStatus.trim();
    }

    public String getProSubmitAccountStatus() {
        return proSubmitAccountStatus;
    }

    public K3syncStatisProvince withProSubmitAccountStatus(String proSubmitAccountStatus) {
        this.setProSubmitAccountStatus(proSubmitAccountStatus);
        return this;
    }

    public void setProSubmitAccountStatus(String proSubmitAccountStatus) {
        this.proSubmitAccountStatus = proSubmitAccountStatus == null ? null : proSubmitAccountStatus.trim();
    }

    public String getSyncK3UserName() {
        return syncK3UserName;
    }

    public K3syncStatisProvince withSyncK3UserName(String syncK3UserName) {
        this.setSyncK3UserName(syncK3UserName);
        return this;
    }

    public void setSyncK3UserName(String syncK3UserName) {
        this.syncK3UserName = syncK3UserName == null ? null : syncK3UserName.trim();
    }

    public String getCommitK3UserName() {
        return commitK3UserName;
    }

    public K3syncStatisProvince withCommitK3UserName(String commitK3UserName) {
        this.setCommitK3UserName(commitK3UserName);
        return this;
    }

    public void setCommitK3UserName(String commitK3UserName) {
        this.commitK3UserName = commitK3UserName == null ? null : commitK3UserName.trim();
    }

    public String getMaterialDept() {
        return materialDept;
    }

    public K3syncStatisProvince withMaterialDept(String materialDept) {
        this.setMaterialDept(materialDept);
        return this;
    }

    public void setMaterialDept(String materialDept) {
        this.materialDept = materialDept == null ? null : materialDept.trim();
    }

    public String getProDataCode() {
        return proDataCode;
    }

    public K3syncStatisProvince withProDataCode(String proDataCode) {
        this.setProDataCode(proDataCode);
        return this;
    }

    public void setProDataCode(String proDataCode) {
        this.proDataCode = proDataCode == null ? null : proDataCode.trim();
    }

    public Integer getK3Status() {
        return k3Status;
    }

    public K3syncStatisProvince withK3Status(Integer k3Status) {
        this.setK3Status(k3Status);
        return this;
    }

    public void setK3Status(Integer k3Status) {
        this.k3Status = k3Status;
    }

    public Integer getProMaterialStatus() {
        return proMaterialStatus;
    }

    public K3syncStatisProvince withProMaterialStatus(Integer proMaterialStatus) {
        this.setProMaterialStatus(proMaterialStatus);
        return this;
    }

    public void setProMaterialStatus(Integer proMaterialStatus) {
        this.proMaterialStatus = proMaterialStatus;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", contractNum=").append(contractNum);
        sb.append(", contractName=").append(contractName);
        sb.append(", contractDept=").append(contractDept);
        sb.append(", contractStatisType=").append(contractStatisType);
        sb.append(", contractSeller=").append(contractSeller);
        sb.append(", contractType=").append(contractType);
        sb.append(", contractSettleMode=").append(contractSettleMode);
        sb.append(", moneyUnit=").append(moneyUnit);
        sb.append(", sellUnit=").append(sellUnit);
        sb.append(", productType=").append(productType);
        sb.append(", orderProvinceName=").append(orderProvinceName);
        sb.append(", orderProvinceCode=").append(orderProvinceCode);
        sb.append(", orderCount=").append(orderCount);
        sb.append(", totalPrice=").append(totalPrice);
        sb.append(", k3RetNum=").append(k3RetNum);
        sb.append(", k3SyncStatus=").append(k3SyncStatus);
        sb.append(", k3CommitStatus=").append(k3CommitStatus);
        sb.append(", sellerOrgId=").append(sellerOrgId);
        sb.append(", sellerTeamId=").append(sellerTeamId);
        sb.append(", sellerDeptId=").append(sellerDeptId);
        sb.append(", sellerPhone=").append(sellerPhone);
        sb.append(", project=").append(project);
        sb.append(", subProject=").append(subProject);
        sb.append(", costCenter=").append(costCenter);
        sb.append(", syncSucTime=").append(syncSucTime);
        sb.append(", commitSucTime=").append(commitSucTime);
        sb.append(", customCode=").append(customCode);
        sb.append(", buyerProvince=").append(buyerProvince);
        sb.append(", buyerProvinceCode=").append(buyerProvinceCode);
        sb.append(", buyerCity=").append(buyerCity);
        sb.append(", buyerCityCode=").append(buyerCityCode);
        sb.append(", createTime=").append(createTime);
        sb.append(", relatedOrderIds=").append(relatedOrderIds);
        sb.append(", relatedK3OrderIds=").append(relatedK3OrderIds);
        sb.append(", proRetNum=").append(proRetNum);
        sb.append(", proSyncStatus=").append(proSyncStatus);
        sb.append(", proSubmitAccountStatus=").append(proSubmitAccountStatus);
        sb.append(", syncK3UserName=").append(syncK3UserName);
        sb.append(", commitK3UserName=").append(commitK3UserName);
        sb.append(", materialDept=").append(materialDept);
        sb.append(", proDataCode=").append(proDataCode);
        sb.append(", k3Status=").append(k3Status);
        sb.append(", proMaterialStatus=").append(proMaterialStatus);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        K3syncStatisProvince other = (K3syncStatisProvince) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getContractNum() == null ? other.getContractNum() == null : this.getContractNum().equals(other.getContractNum()))
            && (this.getContractName() == null ? other.getContractName() == null : this.getContractName().equals(other.getContractName()))
            && (this.getContractDept() == null ? other.getContractDept() == null : this.getContractDept().equals(other.getContractDept()))
            && (this.getContractStatisType() == null ? other.getContractStatisType() == null : this.getContractStatisType().equals(other.getContractStatisType()))
            && (this.getContractSeller() == null ? other.getContractSeller() == null : this.getContractSeller().equals(other.getContractSeller()))
            && (this.getContractType() == null ? other.getContractType() == null : this.getContractType().equals(other.getContractType()))
            && (this.getContractSettleMode() == null ? other.getContractSettleMode() == null : this.getContractSettleMode().equals(other.getContractSettleMode()))
            && (this.getMoneyUnit() == null ? other.getMoneyUnit() == null : this.getMoneyUnit().equals(other.getMoneyUnit()))
            && (this.getSellUnit() == null ? other.getSellUnit() == null : this.getSellUnit().equals(other.getSellUnit()))
            && (this.getProductType() == null ? other.getProductType() == null : this.getProductType().equals(other.getProductType()))
            && (this.getOrderProvinceName() == null ? other.getOrderProvinceName() == null : this.getOrderProvinceName().equals(other.getOrderProvinceName()))
            && (this.getOrderProvinceCode() == null ? other.getOrderProvinceCode() == null : this.getOrderProvinceCode().equals(other.getOrderProvinceCode()))
            && (this.getOrderCount() == null ? other.getOrderCount() == null : this.getOrderCount().equals(other.getOrderCount()))
            && (this.getTotalPrice() == null ? other.getTotalPrice() == null : this.getTotalPrice().equals(other.getTotalPrice()))
            && (this.getK3RetNum() == null ? other.getK3RetNum() == null : this.getK3RetNum().equals(other.getK3RetNum()))
            && (this.getK3SyncStatus() == null ? other.getK3SyncStatus() == null : this.getK3SyncStatus().equals(other.getK3SyncStatus()))
            && (this.getK3CommitStatus() == null ? other.getK3CommitStatus() == null : this.getK3CommitStatus().equals(other.getK3CommitStatus()))
            && (this.getSellerOrgId() == null ? other.getSellerOrgId() == null : this.getSellerOrgId().equals(other.getSellerOrgId()))
            && (this.getSellerTeamId() == null ? other.getSellerTeamId() == null : this.getSellerTeamId().equals(other.getSellerTeamId()))
            && (this.getSellerDeptId() == null ? other.getSellerDeptId() == null : this.getSellerDeptId().equals(other.getSellerDeptId()))
            && (this.getSellerPhone() == null ? other.getSellerPhone() == null : this.getSellerPhone().equals(other.getSellerPhone()))
            && (this.getProject() == null ? other.getProject() == null : this.getProject().equals(other.getProject()))
            && (this.getSubProject() == null ? other.getSubProject() == null : this.getSubProject().equals(other.getSubProject()))
            && (this.getCostCenter() == null ? other.getCostCenter() == null : this.getCostCenter().equals(other.getCostCenter()))
            && (this.getSyncSucTime() == null ? other.getSyncSucTime() == null : this.getSyncSucTime().equals(other.getSyncSucTime()))
            && (this.getCommitSucTime() == null ? other.getCommitSucTime() == null : this.getCommitSucTime().equals(other.getCommitSucTime()))
            && (this.getCustomCode() == null ? other.getCustomCode() == null : this.getCustomCode().equals(other.getCustomCode()))
            && (this.getBuyerProvince() == null ? other.getBuyerProvince() == null : this.getBuyerProvince().equals(other.getBuyerProvince()))
            && (this.getBuyerProvinceCode() == null ? other.getBuyerProvinceCode() == null : this.getBuyerProvinceCode().equals(other.getBuyerProvinceCode()))
            && (this.getBuyerCity() == null ? other.getBuyerCity() == null : this.getBuyerCity().equals(other.getBuyerCity()))
            && (this.getBuyerCityCode() == null ? other.getBuyerCityCode() == null : this.getBuyerCityCode().equals(other.getBuyerCityCode()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getRelatedOrderIds() == null ? other.getRelatedOrderIds() == null : this.getRelatedOrderIds().equals(other.getRelatedOrderIds()))
            && (this.getRelatedK3OrderIds() == null ? other.getRelatedK3OrderIds() == null : this.getRelatedK3OrderIds().equals(other.getRelatedK3OrderIds()))
            && (this.getProRetNum() == null ? other.getProRetNum() == null : this.getProRetNum().equals(other.getProRetNum()))
            && (this.getProSyncStatus() == null ? other.getProSyncStatus() == null : this.getProSyncStatus().equals(other.getProSyncStatus()))
            && (this.getProSubmitAccountStatus() == null ? other.getProSubmitAccountStatus() == null : this.getProSubmitAccountStatus().equals(other.getProSubmitAccountStatus()))
            && (this.getSyncK3UserName() == null ? other.getSyncK3UserName() == null : this.getSyncK3UserName().equals(other.getSyncK3UserName()))
            && (this.getCommitK3UserName() == null ? other.getCommitK3UserName() == null : this.getCommitK3UserName().equals(other.getCommitK3UserName()))
            && (this.getMaterialDept() == null ? other.getMaterialDept() == null : this.getMaterialDept().equals(other.getMaterialDept()))
            && (this.getProDataCode() == null ? other.getProDataCode() == null : this.getProDataCode().equals(other.getProDataCode()))
            && (this.getK3Status() == null ? other.getK3Status() == null : this.getK3Status().equals(other.getK3Status()))
            && (this.getProMaterialStatus() == null ? other.getProMaterialStatus() == null : this.getProMaterialStatus().equals(other.getProMaterialStatus()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getContractNum() == null) ? 0 : getContractNum().hashCode());
        result = prime * result + ((getContractName() == null) ? 0 : getContractName().hashCode());
        result = prime * result + ((getContractDept() == null) ? 0 : getContractDept().hashCode());
        result = prime * result + ((getContractStatisType() == null) ? 0 : getContractStatisType().hashCode());
        result = prime * result + ((getContractSeller() == null) ? 0 : getContractSeller().hashCode());
        result = prime * result + ((getContractType() == null) ? 0 : getContractType().hashCode());
        result = prime * result + ((getContractSettleMode() == null) ? 0 : getContractSettleMode().hashCode());
        result = prime * result + ((getMoneyUnit() == null) ? 0 : getMoneyUnit().hashCode());
        result = prime * result + ((getSellUnit() == null) ? 0 : getSellUnit().hashCode());
        result = prime * result + ((getProductType() == null) ? 0 : getProductType().hashCode());
        result = prime * result + ((getOrderProvinceName() == null) ? 0 : getOrderProvinceName().hashCode());
        result = prime * result + ((getOrderProvinceCode() == null) ? 0 : getOrderProvinceCode().hashCode());
        result = prime * result + ((getOrderCount() == null) ? 0 : getOrderCount().hashCode());
        result = prime * result + ((getTotalPrice() == null) ? 0 : getTotalPrice().hashCode());
        result = prime * result + ((getK3RetNum() == null) ? 0 : getK3RetNum().hashCode());
        result = prime * result + ((getK3SyncStatus() == null) ? 0 : getK3SyncStatus().hashCode());
        result = prime * result + ((getK3CommitStatus() == null) ? 0 : getK3CommitStatus().hashCode());
        result = prime * result + ((getSellerOrgId() == null) ? 0 : getSellerOrgId().hashCode());
        result = prime * result + ((getSellerTeamId() == null) ? 0 : getSellerTeamId().hashCode());
        result = prime * result + ((getSellerDeptId() == null) ? 0 : getSellerDeptId().hashCode());
        result = prime * result + ((getSellerPhone() == null) ? 0 : getSellerPhone().hashCode());
        result = prime * result + ((getProject() == null) ? 0 : getProject().hashCode());
        result = prime * result + ((getSubProject() == null) ? 0 : getSubProject().hashCode());
        result = prime * result + ((getCostCenter() == null) ? 0 : getCostCenter().hashCode());
        result = prime * result + ((getSyncSucTime() == null) ? 0 : getSyncSucTime().hashCode());
        result = prime * result + ((getCommitSucTime() == null) ? 0 : getCommitSucTime().hashCode());
        result = prime * result + ((getCustomCode() == null) ? 0 : getCustomCode().hashCode());
        result = prime * result + ((getBuyerProvince() == null) ? 0 : getBuyerProvince().hashCode());
        result = prime * result + ((getBuyerProvinceCode() == null) ? 0 : getBuyerProvinceCode().hashCode());
        result = prime * result + ((getBuyerCity() == null) ? 0 : getBuyerCity().hashCode());
        result = prime * result + ((getBuyerCityCode() == null) ? 0 : getBuyerCityCode().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getRelatedOrderIds() == null) ? 0 : getRelatedOrderIds().hashCode());
        result = prime * result + ((getRelatedK3OrderIds() == null) ? 0 : getRelatedK3OrderIds().hashCode());
        result = prime * result + ((getProRetNum() == null) ? 0 : getProRetNum().hashCode());
        result = prime * result + ((getProSyncStatus() == null) ? 0 : getProSyncStatus().hashCode());
        result = prime * result + ((getProSubmitAccountStatus() == null) ? 0 : getProSubmitAccountStatus().hashCode());
        result = prime * result + ((getSyncK3UserName() == null) ? 0 : getSyncK3UserName().hashCode());
        result = prime * result + ((getCommitK3UserName() == null) ? 0 : getCommitK3UserName().hashCode());
        result = prime * result + ((getMaterialDept() == null) ? 0 : getMaterialDept().hashCode());
        result = prime * result + ((getProDataCode() == null) ? 0 : getProDataCode().hashCode());
        result = prime * result + ((getK3Status() == null) ? 0 : getK3Status().hashCode());
        result = prime * result + ((getProMaterialStatus() == null) ? 0 : getProMaterialStatus().hashCode());
        return result;
    }

    public enum Column {
        id("id", "id", "VARCHAR", false),
        contractNum("contract_num", "contractNum", "VARCHAR", false),
        contractName("contract_name", "contractName", "VARCHAR", false),
        contractDept("contract_dept", "contractDept", "VARCHAR", false),
        contractStatisType("contract_statis_type", "contractStatisType", "VARCHAR", false),
        contractSeller("contract_seller", "contractSeller", "VARCHAR", false),
        contractType("contract_type", "contractType", "VARCHAR", false),
        contractSettleMode("contract_settle_mode", "contractSettleMode", "INTEGER", false),
        moneyUnit("money_unit", "moneyUnit", "VARCHAR", false),
        sellUnit("sell_unit", "sellUnit", "VARCHAR", false),
        productType("product_type", "productType", "VARCHAR", false),
        orderProvinceName("order_province_name", "orderProvinceName", "VARCHAR", false),
        orderProvinceCode("order_province_code", "orderProvinceCode", "VARCHAR", false),
        orderCount("order_count", "orderCount", "VARCHAR", false),
        totalPrice("total_price", "totalPrice", "BIGINT", false),
        k3RetNum("k3_ret_num", "k3RetNum", "VARCHAR", false),
        k3SyncStatus("k3_sync_status", "k3SyncStatus", "VARCHAR", false),
        k3CommitStatus("k3_commit_status", "k3CommitStatus", "VARCHAR", false),
        sellerOrgId("seller_org_id", "sellerOrgId", "VARCHAR", false),
        sellerTeamId("seller_team_id", "sellerTeamId", "VARCHAR", false),
        sellerDeptId("seller_dept_id", "sellerDeptId", "VARCHAR", false),
        sellerPhone("seller_phone", "sellerPhone", "VARCHAR", false),
        project("project", "project", "VARCHAR", false),
        subProject("sub_project", "subProject", "VARCHAR", false),
        costCenter("cost_center", "costCenter", "VARCHAR", false),
        syncSucTime("sync_suc_time", "syncSucTime", "TIMESTAMP", false),
        commitSucTime("commit_suc_time", "commitSucTime", "TIMESTAMP", false),
        customCode("custom_code", "customCode", "VARCHAR", false),
        buyerProvince("buyer_province", "buyerProvince", "VARCHAR", false),
        buyerProvinceCode("buyer_province_code", "buyerProvinceCode", "VARCHAR", false),
        buyerCity("buyer_city", "buyerCity", "VARCHAR", false),
        buyerCityCode("buyer_city_code", "buyerCityCode", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        relatedOrderIds("related_order_ids", "relatedOrderIds", "VARCHAR", false),
        relatedK3OrderIds("related_k3_order_ids", "relatedK3OrderIds", "VARCHAR", false),
        proRetNum("pro_ret_num", "proRetNum", "VARCHAR", false),
        proSyncStatus("pro_sync_status", "proSyncStatus", "VARCHAR", false),
        proSubmitAccountStatus("pro_submit_account_status", "proSubmitAccountStatus", "VARCHAR", false),
        syncK3UserName("sync_k3_user_name", "syncK3UserName", "VARCHAR", false),
        commitK3UserName("commit_k3_user_name", "commitK3UserName", "VARCHAR", false),
        materialDept("material_dept", "materialDept", "VARCHAR", false),
        proDataCode("pro_data_code", "proDataCode", "VARCHAR", false),
        k3Status("k3_status", "k3Status", "INTEGER", false),
        proMaterialStatus("pro_material_status", "proMaterialStatus", "INTEGER", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}