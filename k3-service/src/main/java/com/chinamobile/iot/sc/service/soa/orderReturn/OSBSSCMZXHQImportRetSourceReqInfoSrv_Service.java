package com.chinamobile.iot.sc.service.soa.orderReturn;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

/**
 * This class was generated by Apache CXF 3.0.4
 * 2024-08-20T15:48:45.795+08:00
 * Generated source version: 3.0.4
 * 
 */
@WebServiceClient(name = "OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrv", 
                  wsdlLocation = "file:/cmcc/mcsshare/soa/resources/example-vm/webservice-example-1724140118679/webservice-example/OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrv/src/main/webapp/META-INF/wsdl/OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrv/OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrv.wsdl",
                  targetNamespace = "http://soa.cmcc.com/OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrv") 
public class OSBSSCMZXHQImportRetSourceReqInfoSrv_Service extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://soa.cmcc.com/OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrv", "OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrv");
    public final static QName OSBSSCMZXHQImportRetSourceReqInfoSrvPort = new QName("http://soa.cmcc.com/OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrv", "OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrvPort");
    static {
        URL url = null;
        try {
            url = new URL("file:/cmcc/mcsshare/soa/resources/example-vm/webservice-example-1724140118679/webservice-example/OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrv/src/main/webapp/META-INF/wsdl/OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrv/OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrv.wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(OSBSSCMZXHQImportRetSourceReqInfoSrv_Service.class.getName())
                .log(java.util.logging.Level.INFO, 
                     "Can not initialize the default wsdl from {0}", "file:/cmcc/mcsshare/soa/resources/example-vm/webservice-example-1724140118679/webservice-example/OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrv/src/main/webapp/META-INF/wsdl/OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrv/OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrv.wsdl");
        }
        WSDL_LOCATION = url;
    }

    public OSBSSCMZXHQImportRetSourceReqInfoSrv_Service(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public OSBSSCMZXHQImportRetSourceReqInfoSrv_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public OSBSSCMZXHQImportRetSourceReqInfoSrv_Service() {
        super(WSDL_LOCATION, SERVICE);
    }
    
    //This constructor requires JAX-WS API 2.2. You will need to endorse the 2.2
    //API jar or re-run wsdl2java with "-frontend jaxws21" to generate JAX-WS 2.1
    //compliant code instead.
    public OSBSSCMZXHQImportRetSourceReqInfoSrv_Service(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    //This constructor requires JAX-WS API 2.2. You will need to endorse the 2.2
    //API jar or re-run wsdl2java with "-frontend jaxws21" to generate JAX-WS 2.1
    //compliant code instead.
    public OSBSSCMZXHQImportRetSourceReqInfoSrv_Service(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    //This constructor requires JAX-WS API 2.2. You will need to endorse the 2.2
    //API jar or re-run wsdl2java with "-frontend jaxws21" to generate JAX-WS 2.1
    //compliant code instead.
    public OSBSSCMZXHQImportRetSourceReqInfoSrv_Service(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }    

    /**
     *
     * @return
     *     returns OSBSSCMZXHQImportRetSourceReqInfoSrv
     */
    @WebEndpoint(name = "OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrvPort")
    public OSBSSCMZXHQImportRetSourceReqInfoSrv getOSBSSCMZXHQImportRetSourceReqInfoSrvPort() {
        return super.getPort(OSBSSCMZXHQImportRetSourceReqInfoSrvPort, OSBSSCMZXHQImportRetSourceReqInfoSrv.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns OSBSSCMZXHQImportRetSourceReqInfoSrv
     */
    @WebEndpoint(name = "OSB_SSCM_ZX_HQ_ImportRetSourceReqInfoSrvPort")
    public OSBSSCMZXHQImportRetSourceReqInfoSrv getOSBSSCMZXHQImportRetSourceReqInfoSrvPort(WebServiceFeature... features) {
        return super.getPort(OSBSSCMZXHQImportRetSourceReqInfoSrvPort, OSBSSCMZXHQImportRetSourceReqInfoSrv.class, features);
    }

}
