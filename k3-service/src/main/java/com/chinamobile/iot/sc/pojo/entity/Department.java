package com.chinamobile.iot.sc.pojo.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/13 17:51
 * @description 产品部门实体类
 */
@Data
@NoArgsConstructor
public class Department {
    /**
     * 组织ID
     */
    private Integer id;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改时间
     */
    private Date updatedDt;

    /**
     * 0 有效,   1 失效
     */
    private Integer status;

    /**
     * 组织编码
     */
    private String orgCode;

    /**
     * 组织全称
     */
    private String fullName;

    /**
     * 组织简称
     */
    private String shortName;

    /**
     * 删除时间
     */
    private Date deleteDt;

}
