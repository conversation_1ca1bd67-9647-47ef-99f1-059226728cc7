package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class OnlineSettlementOsOrder implements Serializable {
    private String id;

    private String orderType;

    private String orderId;

    private String spuOfferingCode;

    private String skuOfferingCode;

    private String skuOfferingName;

    private Long skuQuantity;

    private String spuOfferingClass;

    private Long totalPrice;

    private String beId;

    private String location;

    private Date orderCreateTime;

    private Date orderSuccessTime;

    private Integer purchaseStatus;

    private Integer onlinePurchaseStatus;

    private String onlineSettlementPurchaseOrderId;

    private Date createTime;

    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public OnlineSettlementOsOrder withId(String id) {
        this.setId(id);
        return this;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getOrderType() {
        return orderType;
    }

    public OnlineSettlementOsOrder withOrderType(String orderType) {
        this.setOrderType(orderType);
        return this;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType == null ? null : orderType.trim();
    }

    public String getOrderId() {
        return orderId;
    }

    public OnlineSettlementOsOrder withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    public String getSpuOfferingCode() {
        return spuOfferingCode;
    }

    public OnlineSettlementOsOrder withSpuOfferingCode(String spuOfferingCode) {
        this.setSpuOfferingCode(spuOfferingCode);
        return this;
    }

    public void setSpuOfferingCode(String spuOfferingCode) {
        this.spuOfferingCode = spuOfferingCode == null ? null : spuOfferingCode.trim();
    }

    public String getSkuOfferingCode() {
        return skuOfferingCode;
    }

    public OnlineSettlementOsOrder withSkuOfferingCode(String skuOfferingCode) {
        this.setSkuOfferingCode(skuOfferingCode);
        return this;
    }

    public void setSkuOfferingCode(String skuOfferingCode) {
        this.skuOfferingCode = skuOfferingCode == null ? null : skuOfferingCode.trim();
    }

    public String getSkuOfferingName() {
        return skuOfferingName;
    }

    public OnlineSettlementOsOrder withSkuOfferingName(String skuOfferingName) {
        this.setSkuOfferingName(skuOfferingName);
        return this;
    }

    public void setSkuOfferingName(String skuOfferingName) {
        this.skuOfferingName = skuOfferingName == null ? null : skuOfferingName.trim();
    }

    public Long getSkuQuantity() {
        return skuQuantity;
    }

    public OnlineSettlementOsOrder withSkuQuantity(Long skuQuantity) {
        this.setSkuQuantity(skuQuantity);
        return this;
    }

    public void setSkuQuantity(Long skuQuantity) {
        this.skuQuantity = skuQuantity;
    }

    public String getSpuOfferingClass() {
        return spuOfferingClass;
    }

    public OnlineSettlementOsOrder withSpuOfferingClass(String spuOfferingClass) {
        this.setSpuOfferingClass(spuOfferingClass);
        return this;
    }

    public void setSpuOfferingClass(String spuOfferingClass) {
        this.spuOfferingClass = spuOfferingClass == null ? null : spuOfferingClass.trim();
    }

    public Long getTotalPrice() {
        return totalPrice;
    }

    public OnlineSettlementOsOrder withTotalPrice(Long totalPrice) {
        this.setTotalPrice(totalPrice);
        return this;
    }

    public void setTotalPrice(Long totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getBeId() {
        return beId;
    }

    public OnlineSettlementOsOrder withBeId(String beId) {
        this.setBeId(beId);
        return this;
    }

    public void setBeId(String beId) {
        this.beId = beId == null ? null : beId.trim();
    }

    public String getLocation() {
        return location;
    }

    public OnlineSettlementOsOrder withLocation(String location) {
        this.setLocation(location);
        return this;
    }

    public void setLocation(String location) {
        this.location = location == null ? null : location.trim();
    }

    public Date getOrderCreateTime() {
        return orderCreateTime;
    }

    public OnlineSettlementOsOrder withOrderCreateTime(Date orderCreateTime) {
        this.setOrderCreateTime(orderCreateTime);
        return this;
    }

    public void setOrderCreateTime(Date orderCreateTime) {
        this.orderCreateTime = orderCreateTime;
    }

    public Date getOrderSuccessTime() {
        return orderSuccessTime;
    }

    public OnlineSettlementOsOrder withOrderSuccessTime(Date orderSuccessTime) {
        this.setOrderSuccessTime(orderSuccessTime);
        return this;
    }

    public void setOrderSuccessTime(Date orderSuccessTime) {
        this.orderSuccessTime = orderSuccessTime;
    }

    public Integer getPurchaseStatus() {
        return purchaseStatus;
    }

    public OnlineSettlementOsOrder withPurchaseStatus(Integer purchaseStatus) {
        this.setPurchaseStatus(purchaseStatus);
        return this;
    }

    public void setPurchaseStatus(Integer purchaseStatus) {
        this.purchaseStatus = purchaseStatus;
    }

    public Integer getOnlinePurchaseStatus() {
        return onlinePurchaseStatus;
    }

    public OnlineSettlementOsOrder withOnlinePurchaseStatus(Integer onlinePurchaseStatus) {
        this.setOnlinePurchaseStatus(onlinePurchaseStatus);
        return this;
    }

    public void setOnlinePurchaseStatus(Integer onlinePurchaseStatus) {
        this.onlinePurchaseStatus = onlinePurchaseStatus;
    }

    public String getOnlineSettlementPurchaseOrderId() {
        return onlineSettlementPurchaseOrderId;
    }

    public OnlineSettlementOsOrder withOnlineSettlementPurchaseOrderId(String onlineSettlementPurchaseOrderId) {
        this.setOnlineSettlementPurchaseOrderId(onlineSettlementPurchaseOrderId);
        return this;
    }

    public void setOnlineSettlementPurchaseOrderId(String onlineSettlementPurchaseOrderId) {
        this.onlineSettlementPurchaseOrderId = onlineSettlementPurchaseOrderId == null ? null : onlineSettlementPurchaseOrderId.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public OnlineSettlementOsOrder withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public OnlineSettlementOsOrder withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderType=").append(orderType);
        sb.append(", orderId=").append(orderId);
        sb.append(", spuOfferingCode=").append(spuOfferingCode);
        sb.append(", skuOfferingCode=").append(skuOfferingCode);
        sb.append(", skuOfferingName=").append(skuOfferingName);
        sb.append(", skuQuantity=").append(skuQuantity);
        sb.append(", spuOfferingClass=").append(spuOfferingClass);
        sb.append(", totalPrice=").append(totalPrice);
        sb.append(", beId=").append(beId);
        sb.append(", location=").append(location);
        sb.append(", orderCreateTime=").append(orderCreateTime);
        sb.append(", orderSuccessTime=").append(orderSuccessTime);
        sb.append(", purchaseStatus=").append(purchaseStatus);
        sb.append(", onlinePurchaseStatus=").append(onlinePurchaseStatus);
        sb.append(", onlineSettlementPurchaseOrderId=").append(onlineSettlementPurchaseOrderId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        OnlineSettlementOsOrder other = (OnlineSettlementOsOrder) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOrderType() == null ? other.getOrderType() == null : this.getOrderType().equals(other.getOrderType()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getSpuOfferingCode() == null ? other.getSpuOfferingCode() == null : this.getSpuOfferingCode().equals(other.getSpuOfferingCode()))
            && (this.getSkuOfferingCode() == null ? other.getSkuOfferingCode() == null : this.getSkuOfferingCode().equals(other.getSkuOfferingCode()))
            && (this.getSkuOfferingName() == null ? other.getSkuOfferingName() == null : this.getSkuOfferingName().equals(other.getSkuOfferingName()))
            && (this.getSkuQuantity() == null ? other.getSkuQuantity() == null : this.getSkuQuantity().equals(other.getSkuQuantity()))
            && (this.getSpuOfferingClass() == null ? other.getSpuOfferingClass() == null : this.getSpuOfferingClass().equals(other.getSpuOfferingClass()))
            && (this.getTotalPrice() == null ? other.getTotalPrice() == null : this.getTotalPrice().equals(other.getTotalPrice()))
            && (this.getBeId() == null ? other.getBeId() == null : this.getBeId().equals(other.getBeId()))
            && (this.getLocation() == null ? other.getLocation() == null : this.getLocation().equals(other.getLocation()))
            && (this.getOrderCreateTime() == null ? other.getOrderCreateTime() == null : this.getOrderCreateTime().equals(other.getOrderCreateTime()))
            && (this.getOrderSuccessTime() == null ? other.getOrderSuccessTime() == null : this.getOrderSuccessTime().equals(other.getOrderSuccessTime()))
            && (this.getPurchaseStatus() == null ? other.getPurchaseStatus() == null : this.getPurchaseStatus().equals(other.getPurchaseStatus()))
            && (this.getOnlinePurchaseStatus() == null ? other.getOnlinePurchaseStatus() == null : this.getOnlinePurchaseStatus().equals(other.getOnlinePurchaseStatus()))
            && (this.getOnlineSettlementPurchaseOrderId() == null ? other.getOnlineSettlementPurchaseOrderId() == null : this.getOnlineSettlementPurchaseOrderId().equals(other.getOnlineSettlementPurchaseOrderId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOrderType() == null) ? 0 : getOrderType().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getSpuOfferingCode() == null) ? 0 : getSpuOfferingCode().hashCode());
        result = prime * result + ((getSkuOfferingCode() == null) ? 0 : getSkuOfferingCode().hashCode());
        result = prime * result + ((getSkuOfferingName() == null) ? 0 : getSkuOfferingName().hashCode());
        result = prime * result + ((getSkuQuantity() == null) ? 0 : getSkuQuantity().hashCode());
        result = prime * result + ((getSpuOfferingClass() == null) ? 0 : getSpuOfferingClass().hashCode());
        result = prime * result + ((getTotalPrice() == null) ? 0 : getTotalPrice().hashCode());
        result = prime * result + ((getBeId() == null) ? 0 : getBeId().hashCode());
        result = prime * result + ((getLocation() == null) ? 0 : getLocation().hashCode());
        result = prime * result + ((getOrderCreateTime() == null) ? 0 : getOrderCreateTime().hashCode());
        result = prime * result + ((getOrderSuccessTime() == null) ? 0 : getOrderSuccessTime().hashCode());
        result = prime * result + ((getPurchaseStatus() == null) ? 0 : getPurchaseStatus().hashCode());
        result = prime * result + ((getOnlinePurchaseStatus() == null) ? 0 : getOnlinePurchaseStatus().hashCode());
        result = prime * result + ((getOnlineSettlementPurchaseOrderId() == null) ? 0 : getOnlineSettlementPurchaseOrderId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    public enum Column {
        id("id", "id", "VARCHAR", false),
        orderType("order_type", "orderType", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        spuOfferingCode("spu_offering_code", "spuOfferingCode", "VARCHAR", false),
        skuOfferingCode("sku_offering_code", "skuOfferingCode", "VARCHAR", false),
        skuOfferingName("sku_offering_name", "skuOfferingName", "VARCHAR", false),
        skuQuantity("sku_quantity", "skuQuantity", "BIGINT", false),
        spuOfferingClass("spu_offering_class", "spuOfferingClass", "VARCHAR", false),
        totalPrice("total_price", "totalPrice", "BIGINT", false),
        beId("be_id", "beId", "VARCHAR", false),
        location("location", "location", "VARCHAR", false),
        orderCreateTime("order_create_time", "orderCreateTime", "TIMESTAMP", false),
        orderSuccessTime("order_success_time", "orderSuccessTime", "TIMESTAMP", false),
        purchaseStatus("purchase_status", "purchaseStatus", "INTEGER", false),
        onlinePurchaseStatus("online_purchase_status", "onlinePurchaseStatus", "INTEGER", false),
        onlineSettlementPurchaseOrderId("online_settlement_purchase_order_id", "onlineSettlementPurchaseOrderId", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}