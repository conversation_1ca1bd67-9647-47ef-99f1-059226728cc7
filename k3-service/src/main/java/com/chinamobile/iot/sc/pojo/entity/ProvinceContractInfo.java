package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 省份合同信息表
 *
 * <AUTHOR>
public class ProvinceContractInfo implements Serializable {
    /**
     * 省合同主键id
     *
     * Corresponding to the database column supply_chain..province_contract_info.id
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    private String id;

    /**
     * 省合同编号
     *
     * Corresponding to the database column supply_chain..province_contract_info.province_contract_no
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    private String provinceContractNo;

    /**
     * 省合同名称
     *
     * Corresponding to the database column supply_chain..province_contract_info.province_contract_name
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    private String provinceContractName;

    /**
     * 物联网公司合同编号
     *
     * Corresponding to the database column supply_chain..province_contract_info.Internet_contract_code
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    private String internetContractCode;

    /**
     * 采购员名称
     *
     * Corresponding to the database column supply_chain..province_contract_info.buyer_name
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    private String buyerName;

    /**
     * 采购员编码
     *
     * Corresponding to the database column supply_chain..province_contract_info.buyer_no
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    private String buyerNo;

    /**
     * 申请部门
     *
     * Corresponding to the database column supply_chain..province_contract_info.apply_department
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    private String applyDepartment;

    /**
     * 申请部门编码
     *
     * Corresponding to the database column supply_chain..province_contract_info.apply_department_no
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    private String applyDepartmentNo;

    /**
     * 公司名称
     *
     * Corresponding to the database column supply_chain..province_contract_info.company_name
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    private String companyName;

    /**
     * 公司代码
     *
     * Corresponding to the database column supply_chain..province_contract_info.company_code
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    private String companyCode;

    /**
     * 省份名称
     *
     * Corresponding to the database column supply_chain..province_contract_info.province_name
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    private String provinceName;

    /**
     * 地市名称
     *
     * Corresponding to the database column supply_chain..province_contract_info.city_name
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    private String cityName;

    /**
     * 省份编码
     *
     * Corresponding to the database column supply_chain..province_contract_info.province_code
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    private String provinceCode;

    /**
     * 省份合同类型（0：OneNet，1:OnePark）
     *
     * Corresponding to the database column supply_chain..province_contract_info.contract_type
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    private String contractType;

    /**
     * 创建时间
     *
     * Corresponding to the database column supply_chain..province_contract_info.create_time
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    private Date createTime;

    /**
     * 更新时间
     *
     * Corresponding to the database column supply_chain..province_contract_info.update_time
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    private Date updateTime;

    /**
     * Corresponding to the database table supply_chain..province_contract_info
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..province_contract_info.id
     *
     * @return the value of supply_chain..province_contract_info.id
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public ProvinceContractInfo withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info.id
     *
     * @param id the value for supply_chain..province_contract_info.id
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info.province_contract_no
     *
     * @return the value of supply_chain..province_contract_info.province_contract_no
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public String getProvinceContractNo() {
        return provinceContractNo;
    }

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public ProvinceContractInfo withProvinceContractNo(String provinceContractNo) {
        this.setProvinceContractNo(provinceContractNo);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info.province_contract_no
     *
     * @param provinceContractNo the value for supply_chain..province_contract_info.province_contract_no
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public void setProvinceContractNo(String provinceContractNo) {
        this.provinceContractNo = provinceContractNo == null ? null : provinceContractNo.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info.province_contract_name
     *
     * @return the value of supply_chain..province_contract_info.province_contract_name
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public String getProvinceContractName() {
        return provinceContractName;
    }

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public ProvinceContractInfo withProvinceContractName(String provinceContractName) {
        this.setProvinceContractName(provinceContractName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info.province_contract_name
     *
     * @param provinceContractName the value for supply_chain..province_contract_info.province_contract_name
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public void setProvinceContractName(String provinceContractName) {
        this.provinceContractName = provinceContractName == null ? null : provinceContractName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info.Internet_contract_code
     *
     * @return the value of supply_chain..province_contract_info.Internet_contract_code
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public String getInternetContractCode() {
        return internetContractCode;
    }

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public ProvinceContractInfo withInternetContractCode(String internetContractCode) {
        this.setInternetContractCode(internetContractCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info.Internet_contract_code
     *
     * @param internetContractCode the value for supply_chain..province_contract_info.Internet_contract_code
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public void setInternetContractCode(String internetContractCode) {
        this.internetContractCode = internetContractCode == null ? null : internetContractCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info.buyer_name
     *
     * @return the value of supply_chain..province_contract_info.buyer_name
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public String getBuyerName() {
        return buyerName;
    }

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public ProvinceContractInfo withBuyerName(String buyerName) {
        this.setBuyerName(buyerName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info.buyer_name
     *
     * @param buyerName the value for supply_chain..province_contract_info.buyer_name
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName == null ? null : buyerName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info.buyer_no
     *
     * @return the value of supply_chain..province_contract_info.buyer_no
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public String getBuyerNo() {
        return buyerNo;
    }

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public ProvinceContractInfo withBuyerNo(String buyerNo) {
        this.setBuyerNo(buyerNo);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info.buyer_no
     *
     * @param buyerNo the value for supply_chain..province_contract_info.buyer_no
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public void setBuyerNo(String buyerNo) {
        this.buyerNo = buyerNo == null ? null : buyerNo.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info.apply_department
     *
     * @return the value of supply_chain..province_contract_info.apply_department
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public String getApplyDepartment() {
        return applyDepartment;
    }

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public ProvinceContractInfo withApplyDepartment(String applyDepartment) {
        this.setApplyDepartment(applyDepartment);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info.apply_department
     *
     * @param applyDepartment the value for supply_chain..province_contract_info.apply_department
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public void setApplyDepartment(String applyDepartment) {
        this.applyDepartment = applyDepartment == null ? null : applyDepartment.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info.apply_department_no
     *
     * @return the value of supply_chain..province_contract_info.apply_department_no
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public String getApplyDepartmentNo() {
        return applyDepartmentNo;
    }

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public ProvinceContractInfo withApplyDepartmentNo(String applyDepartmentNo) {
        this.setApplyDepartmentNo(applyDepartmentNo);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info.apply_department_no
     *
     * @param applyDepartmentNo the value for supply_chain..province_contract_info.apply_department_no
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public void setApplyDepartmentNo(String applyDepartmentNo) {
        this.applyDepartmentNo = applyDepartmentNo == null ? null : applyDepartmentNo.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info.company_name
     *
     * @return the value of supply_chain..province_contract_info.company_name
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public String getCompanyName() {
        return companyName;
    }

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public ProvinceContractInfo withCompanyName(String companyName) {
        this.setCompanyName(companyName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info.company_name
     *
     * @param companyName the value for supply_chain..province_contract_info.company_name
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public void setCompanyName(String companyName) {
        this.companyName = companyName == null ? null : companyName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info.company_code
     *
     * @return the value of supply_chain..province_contract_info.company_code
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public String getCompanyCode() {
        return companyCode;
    }

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public ProvinceContractInfo withCompanyCode(String companyCode) {
        this.setCompanyCode(companyCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info.company_code
     *
     * @param companyCode the value for supply_chain..province_contract_info.company_code
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode == null ? null : companyCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info.province_name
     *
     * @return the value of supply_chain..province_contract_info.province_name
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public String getProvinceName() {
        return provinceName;
    }

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public ProvinceContractInfo withProvinceName(String provinceName) {
        this.setProvinceName(provinceName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info.province_name
     *
     * @param provinceName the value for supply_chain..province_contract_info.province_name
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName == null ? null : provinceName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info.city_name
     *
     * @return the value of supply_chain..province_contract_info.city_name
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public String getCityName() {
        return cityName;
    }

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public ProvinceContractInfo withCityName(String cityName) {
        this.setCityName(cityName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info.city_name
     *
     * @param cityName the value for supply_chain..province_contract_info.city_name
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info.province_code
     *
     * @return the value of supply_chain..province_contract_info.province_code
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public String getProvinceCode() {
        return provinceCode;
    }

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public ProvinceContractInfo withProvinceCode(String provinceCode) {
        this.setProvinceCode(provinceCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info.province_code
     *
     * @param provinceCode the value for supply_chain..province_contract_info.province_code
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode == null ? null : provinceCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info.contract_type
     *
     * @return the value of supply_chain..province_contract_info.contract_type
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public String getContractType() {
        return contractType;
    }

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public ProvinceContractInfo withContractType(String contractType) {
        this.setContractType(contractType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info.contract_type
     *
     * @param contractType the value for supply_chain..province_contract_info.contract_type
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public void setContractType(String contractType) {
        this.contractType = contractType == null ? null : contractType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info.create_time
     *
     * @return the value of supply_chain..province_contract_info.create_time
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public ProvinceContractInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info.create_time
     *
     * @param createTime the value for supply_chain..province_contract_info.create_time
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..province_contract_info.update_time
     *
     * @return the value of supply_chain..province_contract_info.update_time
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public ProvinceContractInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..province_contract_info.update_time
     *
     * @param updateTime the value for supply_chain..province_contract_info.update_time
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", provinceContractNo=").append(provinceContractNo);
        sb.append(", provinceContractName=").append(provinceContractName);
        sb.append(", internetContractCode=").append(internetContractCode);
        sb.append(", buyerName=").append(buyerName);
        sb.append(", buyerNo=").append(buyerNo);
        sb.append(", applyDepartment=").append(applyDepartment);
        sb.append(", applyDepartmentNo=").append(applyDepartmentNo);
        sb.append(", companyName=").append(companyName);
        sb.append(", companyCode=").append(companyCode);
        sb.append(", provinceName=").append(provinceName);
        sb.append(", cityName=").append(cityName);
        sb.append(", provinceCode=").append(provinceCode);
        sb.append(", contractType=").append(contractType);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ProvinceContractInfo other = (ProvinceContractInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getProvinceContractNo() == null ? other.getProvinceContractNo() == null : this.getProvinceContractNo().equals(other.getProvinceContractNo()))
            && (this.getProvinceContractName() == null ? other.getProvinceContractName() == null : this.getProvinceContractName().equals(other.getProvinceContractName()))
            && (this.getInternetContractCode() == null ? other.getInternetContractCode() == null : this.getInternetContractCode().equals(other.getInternetContractCode()))
            && (this.getBuyerName() == null ? other.getBuyerName() == null : this.getBuyerName().equals(other.getBuyerName()))
            && (this.getBuyerNo() == null ? other.getBuyerNo() == null : this.getBuyerNo().equals(other.getBuyerNo()))
            && (this.getApplyDepartment() == null ? other.getApplyDepartment() == null : this.getApplyDepartment().equals(other.getApplyDepartment()))
            && (this.getApplyDepartmentNo() == null ? other.getApplyDepartmentNo() == null : this.getApplyDepartmentNo().equals(other.getApplyDepartmentNo()))
            && (this.getCompanyName() == null ? other.getCompanyName() == null : this.getCompanyName().equals(other.getCompanyName()))
            && (this.getCompanyCode() == null ? other.getCompanyCode() == null : this.getCompanyCode().equals(other.getCompanyCode()))
            && (this.getProvinceName() == null ? other.getProvinceName() == null : this.getProvinceName().equals(other.getProvinceName()))
            && (this.getCityName() == null ? other.getCityName() == null : this.getCityName().equals(other.getCityName()))
            && (this.getProvinceCode() == null ? other.getProvinceCode() == null : this.getProvinceCode().equals(other.getProvinceCode()))
            && (this.getContractType() == null ? other.getContractType() == null : this.getContractType().equals(other.getContractType()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getProvinceContractNo() == null) ? 0 : getProvinceContractNo().hashCode());
        result = prime * result + ((getProvinceContractName() == null) ? 0 : getProvinceContractName().hashCode());
        result = prime * result + ((getInternetContractCode() == null) ? 0 : getInternetContractCode().hashCode());
        result = prime * result + ((getBuyerName() == null) ? 0 : getBuyerName().hashCode());
        result = prime * result + ((getBuyerNo() == null) ? 0 : getBuyerNo().hashCode());
        result = prime * result + ((getApplyDepartment() == null) ? 0 : getApplyDepartment().hashCode());
        result = prime * result + ((getApplyDepartmentNo() == null) ? 0 : getApplyDepartmentNo().hashCode());
        result = prime * result + ((getCompanyName() == null) ? 0 : getCompanyName().hashCode());
        result = prime * result + ((getCompanyCode() == null) ? 0 : getCompanyCode().hashCode());
        result = prime * result + ((getProvinceName() == null) ? 0 : getProvinceName().hashCode());
        result = prime * result + ((getCityName() == null) ? 0 : getCityName().hashCode());
        result = prime * result + ((getProvinceCode() == null) ? 0 : getProvinceCode().hashCode());
        result = prime * result + ((getContractType() == null) ? 0 : getContractType().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     * corresponding to the database table supply_chain..province_contract_info
     *
     * @mbg.generated Wed Sep 06 08:46:25 CST 2023
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        provinceContractNo("province_contract_no", "provinceContractNo", "VARCHAR", false),
        provinceContractName("province_contract_name", "provinceContractName", "VARCHAR", false),
        internetContractCode("Internet_contract_code", "internetContractCode", "VARCHAR", false),
        buyerName("buyer_name", "buyerName", "VARCHAR", false),
        buyerNo("buyer_no", "buyerNo", "VARCHAR", false),
        applyDepartment("apply_department", "applyDepartment", "VARCHAR", false),
        applyDepartmentNo("apply_department_no", "applyDepartmentNo", "VARCHAR", false),
        companyName("company_name", "companyName", "VARCHAR", false),
        companyCode("company_code", "companyCode", "VARCHAR", false),
        provinceName("province_name", "provinceName", "VARCHAR", false),
        cityName("city_name", "cityName", "VARCHAR", false),
        provinceCode("province_code", "provinceCode", "VARCHAR", false),
        contractType("contract_type", "contractType", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         * Corresponding to the database table supply_chain..province_contract_info
         *
         * @mbg.generated Wed Sep 06 08:46:25 CST 2023
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..province_contract_info
         *
         * @mbg.generated Wed Sep 06 08:46:25 CST 2023
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..province_contract_info
         *
         * @mbg.generated Wed Sep 06 08:46:25 CST 2023
         */
        private final String column;

        /**
         * Corresponding to the database table supply_chain..province_contract_info
         *
         * @mbg.generated Wed Sep 06 08:46:25 CST 2023
         */
        private final boolean isColumnNameDelimited;

        /**
         * Corresponding to the database table supply_chain..province_contract_info
         *
         * @mbg.generated Wed Sep 06 08:46:25 CST 2023
         */
        private final String javaProperty;

        /**
         * Corresponding to the database table supply_chain..province_contract_info
         *
         * @mbg.generated Wed Sep 06 08:46:25 CST 2023
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Sep 06 08:46:25 CST 2023
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Sep 06 08:46:25 CST 2023
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Sep 06 08:46:25 CST 2023
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Sep 06 08:46:25 CST 2023
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Sep 06 08:46:25 CST 2023
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Sep 06 08:46:25 CST 2023
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Sep 06 08:46:25 CST 2023
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Sep 06 08:46:25 CST 2023
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Sep 06 08:46:25 CST 2023
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Sep 06 08:46:25 CST 2023
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Sep 06 08:46:25 CST 2023
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}