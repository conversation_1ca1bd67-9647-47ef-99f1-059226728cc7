package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 
 *
 * <AUTHOR>
public class ContractClientInfo implements Serializable {
    /**
     * 客户编码
     *
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    private String code;

    /**
     * 客户名称
     *
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    private String name;

    /**
     * 简称
     *
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    private String shortName;

    /**
     * 单据状态
     *
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    private String verifyStatus;

    /**
     * 禁用状态
     *
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    private String disableStatus;

    /**
     * 使用组织
     *
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    private String user;

    /**
     * 审核人
     *
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    private String verifierName;

    /**
     * 审核日期
     *
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    private Date verifyDate;

    /**
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..contract_client_info.code
     *
     * @return the value of supply_chain..contract_client_info.code
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public String getCode() {
        return code;
    }

    /**
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public ContractClientInfo withCode(String code) {
        this.setCode(code);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_client_info.code
     *
     * @param code the value for supply_chain..contract_client_info.code
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_client_info.name
     *
     * @return the value of supply_chain..contract_client_info.name
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public ContractClientInfo withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_client_info.name
     *
     * @param name the value for supply_chain..contract_client_info.name
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_client_info.short_name
     *
     * @return the value of supply_chain..contract_client_info.short_name
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public String getShortName() {
        return shortName;
    }

    /**
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public ContractClientInfo withShortName(String shortName) {
        this.setShortName(shortName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_client_info.short_name
     *
     * @param shortName the value for supply_chain..contract_client_info.short_name
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public void setShortName(String shortName) {
        this.shortName = shortName;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_client_info.verify_status
     *
     * @return the value of supply_chain..contract_client_info.verify_status
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public String getVerifyStatus() {
        return verifyStatus;
    }

    /**
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public ContractClientInfo withVerifyStatus(String verifyStatus) {
        this.setVerifyStatus(verifyStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_client_info.verify_status
     *
     * @param verifyStatus the value for supply_chain..contract_client_info.verify_status
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public void setVerifyStatus(String verifyStatus) {
        this.verifyStatus = verifyStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_client_info.disable_status
     *
     * @return the value of supply_chain..contract_client_info.disable_status
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public String getDisableStatus() {
        return disableStatus;
    }

    /**
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public ContractClientInfo withDisableStatus(String disableStatus) {
        this.setDisableStatus(disableStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_client_info.disable_status
     *
     * @param disableStatus the value for supply_chain..contract_client_info.disable_status
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public void setDisableStatus(String disableStatus) {
        this.disableStatus = disableStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_client_info.user
     *
     * @return the value of supply_chain..contract_client_info.user
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public String getUser() {
        return user;
    }

    /**
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public ContractClientInfo withUser(String user) {
        this.setUser(user);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_client_info.user
     *
     * @param user the value for supply_chain..contract_client_info.user
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public void setUser(String user) {
        this.user = user;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_client_info.verifier_name
     *
     * @return the value of supply_chain..contract_client_info.verifier_name
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public String getVerifierName() {
        return verifierName;
    }

    /**
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public ContractClientInfo withVerifierName(String verifierName) {
        this.setVerifierName(verifierName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_client_info.verifier_name
     *
     * @param verifierName the value for supply_chain..contract_client_info.verifier_name
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public void setVerifierName(String verifierName) {
        this.verifierName = verifierName;
    }

    /**
     * This method returns the value of the database column supply_chain..contract_client_info.verify_date
     *
     * @return the value of supply_chain..contract_client_info.verify_date
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public Date getVerifyDate() {
        return verifyDate;
    }

    /**
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public ContractClientInfo withVerifyDate(Date verifyDate) {
        this.setVerifyDate(verifyDate);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract_client_info.verify_date
     *
     * @param verifyDate the value for supply_chain..contract_client_info.verify_date
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public void setVerifyDate(Date verifyDate) {
        this.verifyDate = verifyDate;
    }

    /**
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", code=").append(code);
        sb.append(", name=").append(name);
        sb.append(", shortName=").append(shortName);
        sb.append(", verifyStatus=").append(verifyStatus);
        sb.append(", disableStatus=").append(disableStatus);
        sb.append(", user=").append(user);
        sb.append(", verifierName=").append(verifierName);
        sb.append(", verifyDate=").append(verifyDate);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ContractClientInfo other = (ContractClientInfo) that;
        return (this.getCode() == null ? other.getCode() == null : this.getCode().equals(other.getCode()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getShortName() == null ? other.getShortName() == null : this.getShortName().equals(other.getShortName()))
            && (this.getVerifyStatus() == null ? other.getVerifyStatus() == null : this.getVerifyStatus().equals(other.getVerifyStatus()))
            && (this.getDisableStatus() == null ? other.getDisableStatus() == null : this.getDisableStatus().equals(other.getDisableStatus()))
            && (this.getUser() == null ? other.getUser() == null : this.getUser().equals(other.getUser()))
            && (this.getVerifierName() == null ? other.getVerifierName() == null : this.getVerifierName().equals(other.getVerifierName()))
            && (this.getVerifyDate() == null ? other.getVerifyDate() == null : this.getVerifyDate().equals(other.getVerifyDate()));
    }

    /**
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getCode() == null) ? 0 : getCode().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getShortName() == null) ? 0 : getShortName().hashCode());
        result = prime * result + ((getVerifyStatus() == null) ? 0 : getVerifyStatus().hashCode());
        result = prime * result + ((getDisableStatus() == null) ? 0 : getDisableStatus().hashCode());
        result = prime * result + ((getUser() == null) ? 0 : getUser().hashCode());
        result = prime * result + ((getVerifierName() == null) ? 0 : getVerifierName().hashCode());
        result = prime * result + ((getVerifyDate() == null) ? 0 : getVerifyDate().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Tue Jun 28 10:11:58 CST 2022
     */
    public enum Column {
        code("code", "code", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        shortName("short_name", "shortName", "VARCHAR", false),
        verifyStatus("verify_status", "verifyStatus", "VARCHAR", false),
        disableStatus("disable_status", "disableStatus", "VARCHAR", false),
        user("user", "user", "VARCHAR", false),
        verifierName("verifier_name", "verifierName", "VARCHAR", false),
        verifyDate("verify_date", "verifyDate", "TIMESTAMP", false);

        /**
         *
         * @mbg.generated Tue Jun 28 10:11:58 CST 2022
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Jun 28 10:11:58 CST 2022
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Tue Jun 28 10:11:58 CST 2022
         */
        private final String column;

        /**
         *
         * @mbg.generated Tue Jun 28 10:11:58 CST 2022
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Tue Jun 28 10:11:58 CST 2022
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Tue Jun 28 10:11:58 CST 2022
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue Jun 28 10:11:58 CST 2022
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Jun 28 10:11:58 CST 2022
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue Jun 28 10:11:58 CST 2022
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue Jun 28 10:11:58 CST 2022
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue Jun 28 10:11:58 CST 2022
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue Jun 28 10:11:58 CST 2022
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue Jun 28 10:11:58 CST 2022
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue Jun 28 10:11:58 CST 2022
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue Jun 28 10:11:58 CST 2022
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue Jun 28 10:11:58 CST 2022
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue Jun 28 10:11:58 CST 2022
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}