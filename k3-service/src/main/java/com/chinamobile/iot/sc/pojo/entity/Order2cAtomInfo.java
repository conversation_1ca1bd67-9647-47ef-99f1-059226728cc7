package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class Order2cAtomInfo implements Serializable {
    private String id;

    private String orderId;

    private String orderType;

    private String spuOfferingCode;

    private String skuOfferingCode;

    private String skuOfferingName;

    private Long skuQuantity;

    private Long skuPrice;

    private String marketName;

    private String marketCode;

    private String supplierName;

    private String color;

    private String model;

    private String atomOfferingClass;

    private String atomOfferingCode;

    private String atomOfferingName;

    private String deductPrice;

    private Long atomPrice;

    private Long atomSettlePrice;

    private Long atomQuantity;

    private Integer orderStatus;

    private String cooperatorId;

    private String finishCooperatorId;

    private String beId;

    private String regionId;

    private String exHandleId;

    private String skuCardName;

    private String skuMsisdn;

    private Integer allowOrderStatus;

    private String allowOrderFailureReason;

    private Integer partReturn;

    private Integer baoliStatus;

    private String createTime;

    private Date updateTime;

    private String valetOrderCompleteTime;

    private Integer carOpenStatus;

    private String spuOfferingVersion;

    private String skuOfferingVersion;

    private String atomOfferingVersion;

    private Integer softServiceStatus;

    private String scmOrderNum;

    private Integer settleStatus;

    private Integer onlineSettleStatus;

    private Date billNoTime;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public Order2cAtomInfo withId(String id) {
        this.setId(id);
        return this;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getOrderId() {
        return orderId;
    }

    public Order2cAtomInfo withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    public String getOrderType() {
        return orderType;
    }

    public Order2cAtomInfo withOrderType(String orderType) {
        this.setOrderType(orderType);
        return this;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType == null ? null : orderType.trim();
    }

    public String getSpuOfferingCode() {
        return spuOfferingCode;
    }

    public Order2cAtomInfo withSpuOfferingCode(String spuOfferingCode) {
        this.setSpuOfferingCode(spuOfferingCode);
        return this;
    }

    public void setSpuOfferingCode(String spuOfferingCode) {
        this.spuOfferingCode = spuOfferingCode == null ? null : spuOfferingCode.trim();
    }

    public String getSkuOfferingCode() {
        return skuOfferingCode;
    }

    public Order2cAtomInfo withSkuOfferingCode(String skuOfferingCode) {
        this.setSkuOfferingCode(skuOfferingCode);
        return this;
    }

    public void setSkuOfferingCode(String skuOfferingCode) {
        this.skuOfferingCode = skuOfferingCode == null ? null : skuOfferingCode.trim();
    }

    public String getSkuOfferingName() {
        return skuOfferingName;
    }

    public Order2cAtomInfo withSkuOfferingName(String skuOfferingName) {
        this.setSkuOfferingName(skuOfferingName);
        return this;
    }

    public void setSkuOfferingName(String skuOfferingName) {
        this.skuOfferingName = skuOfferingName == null ? null : skuOfferingName.trim();
    }

    public Long getSkuQuantity() {
        return skuQuantity;
    }

    public Order2cAtomInfo withSkuQuantity(Long skuQuantity) {
        this.setSkuQuantity(skuQuantity);
        return this;
    }

    public void setSkuQuantity(Long skuQuantity) {
        this.skuQuantity = skuQuantity;
    }

    public Long getSkuPrice() {
        return skuPrice;
    }

    public Order2cAtomInfo withSkuPrice(Long skuPrice) {
        this.setSkuPrice(skuPrice);
        return this;
    }

    public void setSkuPrice(Long skuPrice) {
        this.skuPrice = skuPrice;
    }

    public String getMarketName() {
        return marketName;
    }

    public Order2cAtomInfo withMarketName(String marketName) {
        this.setMarketName(marketName);
        return this;
    }

    public void setMarketName(String marketName) {
        this.marketName = marketName == null ? null : marketName.trim();
    }

    public String getMarketCode() {
        return marketCode;
    }

    public Order2cAtomInfo withMarketCode(String marketCode) {
        this.setMarketCode(marketCode);
        return this;
    }

    public void setMarketCode(String marketCode) {
        this.marketCode = marketCode == null ? null : marketCode.trim();
    }

    public String getSupplierName() {
        return supplierName;
    }

    public Order2cAtomInfo withSupplierName(String supplierName) {
        this.setSupplierName(supplierName);
        return this;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName == null ? null : supplierName.trim();
    }

    public String getColor() {
        return color;
    }

    public Order2cAtomInfo withColor(String color) {
        this.setColor(color);
        return this;
    }

    public void setColor(String color) {
        this.color = color == null ? null : color.trim();
    }

    public String getModel() {
        return model;
    }

    public Order2cAtomInfo withModel(String model) {
        this.setModel(model);
        return this;
    }

    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    public String getAtomOfferingClass() {
        return atomOfferingClass;
    }

    public Order2cAtomInfo withAtomOfferingClass(String atomOfferingClass) {
        this.setAtomOfferingClass(atomOfferingClass);
        return this;
    }

    public void setAtomOfferingClass(String atomOfferingClass) {
        this.atomOfferingClass = atomOfferingClass == null ? null : atomOfferingClass.trim();
    }

    public String getAtomOfferingCode() {
        return atomOfferingCode;
    }

    public Order2cAtomInfo withAtomOfferingCode(String atomOfferingCode) {
        this.setAtomOfferingCode(atomOfferingCode);
        return this;
    }

    public void setAtomOfferingCode(String atomOfferingCode) {
        this.atomOfferingCode = atomOfferingCode == null ? null : atomOfferingCode.trim();
    }

    public String getAtomOfferingName() {
        return atomOfferingName;
    }

    public Order2cAtomInfo withAtomOfferingName(String atomOfferingName) {
        this.setAtomOfferingName(atomOfferingName);
        return this;
    }

    public void setAtomOfferingName(String atomOfferingName) {
        this.atomOfferingName = atomOfferingName == null ? null : atomOfferingName.trim();
    }

    public String getDeductPrice() {
        return deductPrice;
    }

    public Order2cAtomInfo withDeductPrice(String deductPrice) {
        this.setDeductPrice(deductPrice);
        return this;
    }

    public void setDeductPrice(String deductPrice) {
        this.deductPrice = deductPrice == null ? null : deductPrice.trim();
    }

    public Long getAtomPrice() {
        return atomPrice;
    }

    public Order2cAtomInfo withAtomPrice(Long atomPrice) {
        this.setAtomPrice(atomPrice);
        return this;
    }

    public void setAtomPrice(Long atomPrice) {
        this.atomPrice = atomPrice;
    }

    public Long getAtomSettlePrice() {
        return atomSettlePrice;
    }

    public Order2cAtomInfo withAtomSettlePrice(Long atomSettlePrice) {
        this.setAtomSettlePrice(atomSettlePrice);
        return this;
    }

    public void setAtomSettlePrice(Long atomSettlePrice) {
        this.atomSettlePrice = atomSettlePrice;
    }

    public Long getAtomQuantity() {
        return atomQuantity;
    }

    public Order2cAtomInfo withAtomQuantity(Long atomQuantity) {
        this.setAtomQuantity(atomQuantity);
        return this;
    }

    public void setAtomQuantity(Long atomQuantity) {
        this.atomQuantity = atomQuantity;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public Order2cAtomInfo withOrderStatus(Integer orderStatus) {
        this.setOrderStatus(orderStatus);
        return this;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getCooperatorId() {
        return cooperatorId;
    }

    public Order2cAtomInfo withCooperatorId(String cooperatorId) {
        this.setCooperatorId(cooperatorId);
        return this;
    }

    public void setCooperatorId(String cooperatorId) {
        this.cooperatorId = cooperatorId == null ? null : cooperatorId.trim();
    }

    public String getFinishCooperatorId() {
        return finishCooperatorId;
    }

    public Order2cAtomInfo withFinishCooperatorId(String finishCooperatorId) {
        this.setFinishCooperatorId(finishCooperatorId);
        return this;
    }

    public void setFinishCooperatorId(String finishCooperatorId) {
        this.finishCooperatorId = finishCooperatorId == null ? null : finishCooperatorId.trim();
    }

    public String getBeId() {
        return beId;
    }

    public Order2cAtomInfo withBeId(String beId) {
        this.setBeId(beId);
        return this;
    }

    public void setBeId(String beId) {
        this.beId = beId == null ? null : beId.trim();
    }

    public String getRegionId() {
        return regionId;
    }

    public Order2cAtomInfo withRegionId(String regionId) {
        this.setRegionId(regionId);
        return this;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId == null ? null : regionId.trim();
    }

    public String getExHandleId() {
        return exHandleId;
    }

    public Order2cAtomInfo withExHandleId(String exHandleId) {
        this.setExHandleId(exHandleId);
        return this;
    }

    public void setExHandleId(String exHandleId) {
        this.exHandleId = exHandleId == null ? null : exHandleId.trim();
    }

    public String getSkuCardName() {
        return skuCardName;
    }

    public Order2cAtomInfo withSkuCardName(String skuCardName) {
        this.setSkuCardName(skuCardName);
        return this;
    }

    public void setSkuCardName(String skuCardName) {
        this.skuCardName = skuCardName == null ? null : skuCardName.trim();
    }

    public String getSkuMsisdn() {
        return skuMsisdn;
    }

    public Order2cAtomInfo withSkuMsisdn(String skuMsisdn) {
        this.setSkuMsisdn(skuMsisdn);
        return this;
    }

    public void setSkuMsisdn(String skuMsisdn) {
        this.skuMsisdn = skuMsisdn == null ? null : skuMsisdn.trim();
    }

    public Integer getAllowOrderStatus() {
        return allowOrderStatus;
    }

    public Order2cAtomInfo withAllowOrderStatus(Integer allowOrderStatus) {
        this.setAllowOrderStatus(allowOrderStatus);
        return this;
    }

    public void setAllowOrderStatus(Integer allowOrderStatus) {
        this.allowOrderStatus = allowOrderStatus;
    }

    public String getAllowOrderFailureReason() {
        return allowOrderFailureReason;
    }

    public Order2cAtomInfo withAllowOrderFailureReason(String allowOrderFailureReason) {
        this.setAllowOrderFailureReason(allowOrderFailureReason);
        return this;
    }

    public void setAllowOrderFailureReason(String allowOrderFailureReason) {
        this.allowOrderFailureReason = allowOrderFailureReason == null ? null : allowOrderFailureReason.trim();
    }

    public Integer getPartReturn() {
        return partReturn;
    }

    public Order2cAtomInfo withPartReturn(Integer partReturn) {
        this.setPartReturn(partReturn);
        return this;
    }

    public void setPartReturn(Integer partReturn) {
        this.partReturn = partReturn;
    }

    public Integer getBaoliStatus() {
        return baoliStatus;
    }

    public Order2cAtomInfo withBaoliStatus(Integer baoliStatus) {
        this.setBaoliStatus(baoliStatus);
        return this;
    }

    public void setBaoliStatus(Integer baoliStatus) {
        this.baoliStatus = baoliStatus;
    }

    public String getCreateTime() {
        return createTime;
    }

    public Order2cAtomInfo withCreateTime(String createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime == null ? null : createTime.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public Order2cAtomInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getValetOrderCompleteTime() {
        return valetOrderCompleteTime;
    }

    public Order2cAtomInfo withValetOrderCompleteTime(String valetOrderCompleteTime) {
        this.setValetOrderCompleteTime(valetOrderCompleteTime);
        return this;
    }

    public void setValetOrderCompleteTime(String valetOrderCompleteTime) {
        this.valetOrderCompleteTime = valetOrderCompleteTime == null ? null : valetOrderCompleteTime.trim();
    }

    public Integer getCarOpenStatus() {
        return carOpenStatus;
    }

    public Order2cAtomInfo withCarOpenStatus(Integer carOpenStatus) {
        this.setCarOpenStatus(carOpenStatus);
        return this;
    }

    public void setCarOpenStatus(Integer carOpenStatus) {
        this.carOpenStatus = carOpenStatus;
    }

    public String getSpuOfferingVersion() {
        return spuOfferingVersion;
    }

    public Order2cAtomInfo withSpuOfferingVersion(String spuOfferingVersion) {
        this.setSpuOfferingVersion(spuOfferingVersion);
        return this;
    }

    public void setSpuOfferingVersion(String spuOfferingVersion) {
        this.spuOfferingVersion = spuOfferingVersion == null ? null : spuOfferingVersion.trim();
    }

    public String getSkuOfferingVersion() {
        return skuOfferingVersion;
    }

    public Order2cAtomInfo withSkuOfferingVersion(String skuOfferingVersion) {
        this.setSkuOfferingVersion(skuOfferingVersion);
        return this;
    }

    public void setSkuOfferingVersion(String skuOfferingVersion) {
        this.skuOfferingVersion = skuOfferingVersion == null ? null : skuOfferingVersion.trim();
    }

    public String getAtomOfferingVersion() {
        return atomOfferingVersion;
    }

    public Order2cAtomInfo withAtomOfferingVersion(String atomOfferingVersion) {
        this.setAtomOfferingVersion(atomOfferingVersion);
        return this;
    }

    public void setAtomOfferingVersion(String atomOfferingVersion) {
        this.atomOfferingVersion = atomOfferingVersion == null ? null : atomOfferingVersion.trim();
    }

    public Integer getSoftServiceStatus() {
        return softServiceStatus;
    }

    public Order2cAtomInfo withSoftServiceStatus(Integer softServiceStatus) {
        this.setSoftServiceStatus(softServiceStatus);
        return this;
    }

    public void setSoftServiceStatus(Integer softServiceStatus) {
        this.softServiceStatus = softServiceStatus;
    }

    public String getScmOrderNum() {
        return scmOrderNum;
    }

    public Order2cAtomInfo withScmOrderNum(String scmOrderNum) {
        this.setScmOrderNum(scmOrderNum);
        return this;
    }

    public void setScmOrderNum(String scmOrderNum) {
        this.scmOrderNum = scmOrderNum == null ? null : scmOrderNum.trim();
    }

    public Integer getSettleStatus() {
        return settleStatus;
    }

    public Order2cAtomInfo withSettleStatus(Integer settleStatus) {
        this.setSettleStatus(settleStatus);
        return this;
    }

    public void setSettleStatus(Integer settleStatus) {
        this.settleStatus = settleStatus;
    }

    public Integer getOnlineSettleStatus() {
        return onlineSettleStatus;
    }

    public Order2cAtomInfo withOnlineSettleStatus(Integer onlineSettleStatus) {
        this.setOnlineSettleStatus(onlineSettleStatus);
        return this;
    }

    public void setOnlineSettleStatus(Integer onlineSettleStatus) {
        this.onlineSettleStatus = onlineSettleStatus;
    }

    public Date getBillNoTime() {
        return billNoTime;
    }

    public Order2cAtomInfo withBillNoTime(Date billNoTime) {
        this.setBillNoTime(billNoTime);
        return this;
    }

    public void setBillNoTime(Date billNoTime) {
        this.billNoTime = billNoTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderId=").append(orderId);
        sb.append(", orderType=").append(orderType);
        sb.append(", spuOfferingCode=").append(spuOfferingCode);
        sb.append(", skuOfferingCode=").append(skuOfferingCode);
        sb.append(", skuOfferingName=").append(skuOfferingName);
        sb.append(", skuQuantity=").append(skuQuantity);
        sb.append(", skuPrice=").append(skuPrice);
        sb.append(", marketName=").append(marketName);
        sb.append(", marketCode=").append(marketCode);
        sb.append(", supplierName=").append(supplierName);
        sb.append(", color=").append(color);
        sb.append(", model=").append(model);
        sb.append(", atomOfferingClass=").append(atomOfferingClass);
        sb.append(", atomOfferingCode=").append(atomOfferingCode);
        sb.append(", atomOfferingName=").append(atomOfferingName);
        sb.append(", deductPrice=").append(deductPrice);
        sb.append(", atomPrice=").append(atomPrice);
        sb.append(", atomSettlePrice=").append(atomSettlePrice);
        sb.append(", atomQuantity=").append(atomQuantity);
        sb.append(", orderStatus=").append(orderStatus);
        sb.append(", cooperatorId=").append(cooperatorId);
        sb.append(", finishCooperatorId=").append(finishCooperatorId);
        sb.append(", beId=").append(beId);
        sb.append(", regionId=").append(regionId);
        sb.append(", exHandleId=").append(exHandleId);
        sb.append(", skuCardName=").append(skuCardName);
        sb.append(", skuMsisdn=").append(skuMsisdn);
        sb.append(", allowOrderStatus=").append(allowOrderStatus);
        sb.append(", allowOrderFailureReason=").append(allowOrderFailureReason);
        sb.append(", partReturn=").append(partReturn);
        sb.append(", baoliStatus=").append(baoliStatus);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", valetOrderCompleteTime=").append(valetOrderCompleteTime);
        sb.append(", carOpenStatus=").append(carOpenStatus);
        sb.append(", spuOfferingVersion=").append(spuOfferingVersion);
        sb.append(", skuOfferingVersion=").append(skuOfferingVersion);
        sb.append(", atomOfferingVersion=").append(atomOfferingVersion);
        sb.append(", softServiceStatus=").append(softServiceStatus);
        sb.append(", scmOrderNum=").append(scmOrderNum);
        sb.append(", settleStatus=").append(settleStatus);
        sb.append(", onlineSettleStatus=").append(onlineSettleStatus);
        sb.append(", billNoTime=").append(billNoTime);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Order2cAtomInfo other = (Order2cAtomInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getOrderType() == null ? other.getOrderType() == null : this.getOrderType().equals(other.getOrderType()))
            && (this.getSpuOfferingCode() == null ? other.getSpuOfferingCode() == null : this.getSpuOfferingCode().equals(other.getSpuOfferingCode()))
            && (this.getSkuOfferingCode() == null ? other.getSkuOfferingCode() == null : this.getSkuOfferingCode().equals(other.getSkuOfferingCode()))
            && (this.getSkuOfferingName() == null ? other.getSkuOfferingName() == null : this.getSkuOfferingName().equals(other.getSkuOfferingName()))
            && (this.getSkuQuantity() == null ? other.getSkuQuantity() == null : this.getSkuQuantity().equals(other.getSkuQuantity()))
            && (this.getSkuPrice() == null ? other.getSkuPrice() == null : this.getSkuPrice().equals(other.getSkuPrice()))
            && (this.getMarketName() == null ? other.getMarketName() == null : this.getMarketName().equals(other.getMarketName()))
            && (this.getMarketCode() == null ? other.getMarketCode() == null : this.getMarketCode().equals(other.getMarketCode()))
            && (this.getSupplierName() == null ? other.getSupplierName() == null : this.getSupplierName().equals(other.getSupplierName()))
            && (this.getColor() == null ? other.getColor() == null : this.getColor().equals(other.getColor()))
            && (this.getModel() == null ? other.getModel() == null : this.getModel().equals(other.getModel()))
            && (this.getAtomOfferingClass() == null ? other.getAtomOfferingClass() == null : this.getAtomOfferingClass().equals(other.getAtomOfferingClass()))
            && (this.getAtomOfferingCode() == null ? other.getAtomOfferingCode() == null : this.getAtomOfferingCode().equals(other.getAtomOfferingCode()))
            && (this.getAtomOfferingName() == null ? other.getAtomOfferingName() == null : this.getAtomOfferingName().equals(other.getAtomOfferingName()))
            && (this.getDeductPrice() == null ? other.getDeductPrice() == null : this.getDeductPrice().equals(other.getDeductPrice()))
            && (this.getAtomPrice() == null ? other.getAtomPrice() == null : this.getAtomPrice().equals(other.getAtomPrice()))
            && (this.getAtomSettlePrice() == null ? other.getAtomSettlePrice() == null : this.getAtomSettlePrice().equals(other.getAtomSettlePrice()))
            && (this.getAtomQuantity() == null ? other.getAtomQuantity() == null : this.getAtomQuantity().equals(other.getAtomQuantity()))
            && (this.getOrderStatus() == null ? other.getOrderStatus() == null : this.getOrderStatus().equals(other.getOrderStatus()))
            && (this.getCooperatorId() == null ? other.getCooperatorId() == null : this.getCooperatorId().equals(other.getCooperatorId()))
            && (this.getFinishCooperatorId() == null ? other.getFinishCooperatorId() == null : this.getFinishCooperatorId().equals(other.getFinishCooperatorId()))
            && (this.getBeId() == null ? other.getBeId() == null : this.getBeId().equals(other.getBeId()))
            && (this.getRegionId() == null ? other.getRegionId() == null : this.getRegionId().equals(other.getRegionId()))
            && (this.getExHandleId() == null ? other.getExHandleId() == null : this.getExHandleId().equals(other.getExHandleId()))
            && (this.getSkuCardName() == null ? other.getSkuCardName() == null : this.getSkuCardName().equals(other.getSkuCardName()))
            && (this.getSkuMsisdn() == null ? other.getSkuMsisdn() == null : this.getSkuMsisdn().equals(other.getSkuMsisdn()))
            && (this.getAllowOrderStatus() == null ? other.getAllowOrderStatus() == null : this.getAllowOrderStatus().equals(other.getAllowOrderStatus()))
            && (this.getAllowOrderFailureReason() == null ? other.getAllowOrderFailureReason() == null : this.getAllowOrderFailureReason().equals(other.getAllowOrderFailureReason()))
            && (this.getPartReturn() == null ? other.getPartReturn() == null : this.getPartReturn().equals(other.getPartReturn()))
            && (this.getBaoliStatus() == null ? other.getBaoliStatus() == null : this.getBaoliStatus().equals(other.getBaoliStatus()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getValetOrderCompleteTime() == null ? other.getValetOrderCompleteTime() == null : this.getValetOrderCompleteTime().equals(other.getValetOrderCompleteTime()))
            && (this.getCarOpenStatus() == null ? other.getCarOpenStatus() == null : this.getCarOpenStatus().equals(other.getCarOpenStatus()))
            && (this.getSpuOfferingVersion() == null ? other.getSpuOfferingVersion() == null : this.getSpuOfferingVersion().equals(other.getSpuOfferingVersion()))
            && (this.getSkuOfferingVersion() == null ? other.getSkuOfferingVersion() == null : this.getSkuOfferingVersion().equals(other.getSkuOfferingVersion()))
            && (this.getAtomOfferingVersion() == null ? other.getAtomOfferingVersion() == null : this.getAtomOfferingVersion().equals(other.getAtomOfferingVersion()))
            && (this.getSoftServiceStatus() == null ? other.getSoftServiceStatus() == null : this.getSoftServiceStatus().equals(other.getSoftServiceStatus()))
            && (this.getScmOrderNum() == null ? other.getScmOrderNum() == null : this.getScmOrderNum().equals(other.getScmOrderNum()))
            && (this.getSettleStatus() == null ? other.getSettleStatus() == null : this.getSettleStatus().equals(other.getSettleStatus()))
            && (this.getOnlineSettleStatus() == null ? other.getOnlineSettleStatus() == null : this.getOnlineSettleStatus().equals(other.getOnlineSettleStatus()))
            && (this.getBillNoTime() == null ? other.getBillNoTime() == null : this.getBillNoTime().equals(other.getBillNoTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getOrderType() == null) ? 0 : getOrderType().hashCode());
        result = prime * result + ((getSpuOfferingCode() == null) ? 0 : getSpuOfferingCode().hashCode());
        result = prime * result + ((getSkuOfferingCode() == null) ? 0 : getSkuOfferingCode().hashCode());
        result = prime * result + ((getSkuOfferingName() == null) ? 0 : getSkuOfferingName().hashCode());
        result = prime * result + ((getSkuQuantity() == null) ? 0 : getSkuQuantity().hashCode());
        result = prime * result + ((getSkuPrice() == null) ? 0 : getSkuPrice().hashCode());
        result = prime * result + ((getMarketName() == null) ? 0 : getMarketName().hashCode());
        result = prime * result + ((getMarketCode() == null) ? 0 : getMarketCode().hashCode());
        result = prime * result + ((getSupplierName() == null) ? 0 : getSupplierName().hashCode());
        result = prime * result + ((getColor() == null) ? 0 : getColor().hashCode());
        result = prime * result + ((getModel() == null) ? 0 : getModel().hashCode());
        result = prime * result + ((getAtomOfferingClass() == null) ? 0 : getAtomOfferingClass().hashCode());
        result = prime * result + ((getAtomOfferingCode() == null) ? 0 : getAtomOfferingCode().hashCode());
        result = prime * result + ((getAtomOfferingName() == null) ? 0 : getAtomOfferingName().hashCode());
        result = prime * result + ((getDeductPrice() == null) ? 0 : getDeductPrice().hashCode());
        result = prime * result + ((getAtomPrice() == null) ? 0 : getAtomPrice().hashCode());
        result = prime * result + ((getAtomSettlePrice() == null) ? 0 : getAtomSettlePrice().hashCode());
        result = prime * result + ((getAtomQuantity() == null) ? 0 : getAtomQuantity().hashCode());
        result = prime * result + ((getOrderStatus() == null) ? 0 : getOrderStatus().hashCode());
        result = prime * result + ((getCooperatorId() == null) ? 0 : getCooperatorId().hashCode());
        result = prime * result + ((getFinishCooperatorId() == null) ? 0 : getFinishCooperatorId().hashCode());
        result = prime * result + ((getBeId() == null) ? 0 : getBeId().hashCode());
        result = prime * result + ((getRegionId() == null) ? 0 : getRegionId().hashCode());
        result = prime * result + ((getExHandleId() == null) ? 0 : getExHandleId().hashCode());
        result = prime * result + ((getSkuCardName() == null) ? 0 : getSkuCardName().hashCode());
        result = prime * result + ((getSkuMsisdn() == null) ? 0 : getSkuMsisdn().hashCode());
        result = prime * result + ((getAllowOrderStatus() == null) ? 0 : getAllowOrderStatus().hashCode());
        result = prime * result + ((getAllowOrderFailureReason() == null) ? 0 : getAllowOrderFailureReason().hashCode());
        result = prime * result + ((getPartReturn() == null) ? 0 : getPartReturn().hashCode());
        result = prime * result + ((getBaoliStatus() == null) ? 0 : getBaoliStatus().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getValetOrderCompleteTime() == null) ? 0 : getValetOrderCompleteTime().hashCode());
        result = prime * result + ((getCarOpenStatus() == null) ? 0 : getCarOpenStatus().hashCode());
        result = prime * result + ((getSpuOfferingVersion() == null) ? 0 : getSpuOfferingVersion().hashCode());
        result = prime * result + ((getSkuOfferingVersion() == null) ? 0 : getSkuOfferingVersion().hashCode());
        result = prime * result + ((getAtomOfferingVersion() == null) ? 0 : getAtomOfferingVersion().hashCode());
        result = prime * result + ((getSoftServiceStatus() == null) ? 0 : getSoftServiceStatus().hashCode());
        result = prime * result + ((getScmOrderNum() == null) ? 0 : getScmOrderNum().hashCode());
        result = prime * result + ((getSettleStatus() == null) ? 0 : getSettleStatus().hashCode());
        result = prime * result + ((getOnlineSettleStatus() == null) ? 0 : getOnlineSettleStatus().hashCode());
        result = prime * result + ((getBillNoTime() == null) ? 0 : getBillNoTime().hashCode());
        return result;
    }

    public enum Column {
        id("id", "id", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        orderType("order_type", "orderType", "VARCHAR", false),
        spuOfferingCode("spu_offering_code", "spuOfferingCode", "VARCHAR", false),
        skuOfferingCode("sku_offering_code", "skuOfferingCode", "VARCHAR", false),
        skuOfferingName("sku_offering_name", "skuOfferingName", "VARCHAR", false),
        skuQuantity("sku_quantity", "skuQuantity", "BIGINT", false),
        skuPrice("sku_price", "skuPrice", "BIGINT", false),
        marketName("market_name", "marketName", "VARCHAR", false),
        marketCode("market_code", "marketCode", "VARCHAR", false),
        supplierName("supplier_name", "supplierName", "VARCHAR", false),
        color("color", "color", "VARCHAR", false),
        model("model", "model", "VARCHAR", false),
        atomOfferingClass("atom_offering_class", "atomOfferingClass", "VARCHAR", false),
        atomOfferingCode("atom_offering_code", "atomOfferingCode", "VARCHAR", false),
        atomOfferingName("atom_offering_name", "atomOfferingName", "VARCHAR", false),
        deductPrice("deduct_price", "deductPrice", "VARCHAR", false),
        atomPrice("atom_price", "atomPrice", "BIGINT", false),
        atomSettlePrice("atom_settle_price", "atomSettlePrice", "BIGINT", false),
        atomQuantity("atom_quantity", "atomQuantity", "BIGINT", false),
        orderStatus("order_status", "orderStatus", "INTEGER", false),
        cooperatorId("cooperator_id", "cooperatorId", "VARCHAR", false),
        finishCooperatorId("finish_cooperator_id", "finishCooperatorId", "VARCHAR", false),
        beId("be_id", "beId", "VARCHAR", false),
        regionId("region_id", "regionId", "VARCHAR", false),
        exHandleId("ex_handle_id", "exHandleId", "VARCHAR", false),
        skuCardName("sku_card_name", "skuCardName", "VARCHAR", false),
        skuMsisdn("sku_msisdn", "skuMsisdn", "VARCHAR", false),
        allowOrderStatus("allow_order_status", "allowOrderStatus", "INTEGER", false),
        allowOrderFailureReason("allow_order_failure_reason", "allowOrderFailureReason", "VARCHAR", false),
        partReturn("part_return", "partReturn", "INTEGER", false),
        baoliStatus("baoli_status", "baoliStatus", "INTEGER", false),
        createTime("create_time", "createTime", "VARCHAR", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        valetOrderCompleteTime("valet_order_complete_time", "valetOrderCompleteTime", "VARCHAR", false),
        carOpenStatus("car_open_status", "carOpenStatus", "INTEGER", false),
        spuOfferingVersion("spu_offering_version", "spuOfferingVersion", "VARCHAR", false),
        skuOfferingVersion("sku_offering_version", "skuOfferingVersion", "VARCHAR", false),
        atomOfferingVersion("atom_offering_version", "atomOfferingVersion", "VARCHAR", false),
        softServiceStatus("soft_service_status", "softServiceStatus", "INTEGER", false),
        scmOrderNum("scm_order_num", "scmOrderNum", "VARCHAR", false),
        settleStatus("settle_status", "settleStatus", "INTEGER", false),
        onlineSettleStatus("online_settle_status", "onlineSettleStatus", "INTEGER", false),
        billNoTime("bill_no_time", "billNoTime", "TIMESTAMP", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}