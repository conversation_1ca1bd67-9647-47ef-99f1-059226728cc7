package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.List;

public class ContractCityInfoExample {
    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    public ContractCityInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    public ContractCityInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    public ContractCityInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    public static Criteria newAndCreateCriteria() {
        ContractCityInfoExample example = new ContractCityInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    public ContractCityInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    public ContractCityInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andMallCodeIsNull() {
            addCriterion("mall_code is null");
            return (Criteria) this;
        }

        public Criteria andMallCodeIsNotNull() {
            addCriterion("mall_code is not null");
            return (Criteria) this;
        }

        public Criteria andMallCodeEqualTo(String value) {
            addCriterion("mall_code =", value, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("mall_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallCodeNotEqualTo(String value) {
            addCriterion("mall_code <>", value, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeNotEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("mall_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallCodeGreaterThan(String value) {
            addCriterion("mall_code >", value, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeGreaterThanColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("mall_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallCodeGreaterThanOrEqualTo(String value) {
            addCriterion("mall_code >=", value, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeGreaterThanOrEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("mall_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallCodeLessThan(String value) {
            addCriterion("mall_code <", value, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeLessThanColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("mall_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallCodeLessThanOrEqualTo(String value) {
            addCriterion("mall_code <=", value, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeLessThanOrEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("mall_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallCodeLike(String value) {
            addCriterion("mall_code like", value, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeNotLike(String value) {
            addCriterion("mall_code not like", value, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeIn(List<String> values) {
            addCriterion("mall_code in", values, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeNotIn(List<String> values) {
            addCriterion("mall_code not in", values, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeBetween(String value1, String value2) {
            addCriterion("mall_code between", value1, value2, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallCodeNotBetween(String value1, String value2) {
            addCriterion("mall_code not between", value1, value2, "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallNameIsNull() {
            addCriterion("mall_name is null");
            return (Criteria) this;
        }

        public Criteria andMallNameIsNotNull() {
            addCriterion("mall_name is not null");
            return (Criteria) this;
        }

        public Criteria andMallNameEqualTo(String value) {
            addCriterion("mall_name =", value, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("mall_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallNameNotEqualTo(String value) {
            addCriterion("mall_name <>", value, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameNotEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("mall_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallNameGreaterThan(String value) {
            addCriterion("mall_name >", value, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameGreaterThanColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("mall_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallNameGreaterThanOrEqualTo(String value) {
            addCriterion("mall_name >=", value, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameGreaterThanOrEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("mall_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallNameLessThan(String value) {
            addCriterion("mall_name <", value, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameLessThanColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("mall_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallNameLessThanOrEqualTo(String value) {
            addCriterion("mall_name <=", value, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameLessThanOrEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("mall_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMallNameLike(String value) {
            addCriterion("mall_name like", value, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameNotLike(String value) {
            addCriterion("mall_name not like", value, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameIn(List<String> values) {
            addCriterion("mall_name in", values, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameNotIn(List<String> values) {
            addCriterion("mall_name not in", values, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameBetween(String value1, String value2) {
            addCriterion("mall_name between", value1, value2, "mallName");
            return (Criteria) this;
        }

        public Criteria andMallNameNotBetween(String value1, String value2) {
            addCriterion("mall_name not between", value1, value2, "mallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeIsNull() {
            addCriterion("province_mall_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeIsNotNull() {
            addCriterion("province_mall_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeEqualTo(String value) {
            addCriterion("province_mall_code =", value, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("province_mall_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeNotEqualTo(String value) {
            addCriterion("province_mall_code <>", value, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeNotEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("province_mall_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeGreaterThan(String value) {
            addCriterion("province_mall_code >", value, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeGreaterThanColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("province_mall_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_mall_code >=", value, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeGreaterThanOrEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("province_mall_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeLessThan(String value) {
            addCriterion("province_mall_code <", value, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeLessThanColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("province_mall_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeLessThanOrEqualTo(String value) {
            addCriterion("province_mall_code <=", value, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeLessThanOrEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("province_mall_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeLike(String value) {
            addCriterion("province_mall_code like", value, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeNotLike(String value) {
            addCriterion("province_mall_code not like", value, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeIn(List<String> values) {
            addCriterion("province_mall_code in", values, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeNotIn(List<String> values) {
            addCriterion("province_mall_code not in", values, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeBetween(String value1, String value2) {
            addCriterion("province_mall_code between", value1, value2, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeNotBetween(String value1, String value2) {
            addCriterion("province_mall_code not between", value1, value2, "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameIsNull() {
            addCriterion("province_mall_name is null");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameIsNotNull() {
            addCriterion("province_mall_name is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameEqualTo(String value) {
            addCriterion("province_mall_name =", value, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("province_mall_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameNotEqualTo(String value) {
            addCriterion("province_mall_name <>", value, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameNotEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("province_mall_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameGreaterThan(String value) {
            addCriterion("province_mall_name >", value, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameGreaterThanColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("province_mall_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameGreaterThanOrEqualTo(String value) {
            addCriterion("province_mall_name >=", value, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameGreaterThanOrEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("province_mall_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameLessThan(String value) {
            addCriterion("province_mall_name <", value, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameLessThanColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("province_mall_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameLessThanOrEqualTo(String value) {
            addCriterion("province_mall_name <=", value, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameLessThanOrEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("province_mall_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameLike(String value) {
            addCriterion("province_mall_name like", value, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameNotLike(String value) {
            addCriterion("province_mall_name not like", value, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameIn(List<String> values) {
            addCriterion("province_mall_name in", values, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameNotIn(List<String> values) {
            addCriterion("province_mall_name not in", values, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameBetween(String value1, String value2) {
            addCriterion("province_mall_name between", value1, value2, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameNotBetween(String value1, String value2) {
            addCriterion("province_mall_name not between", value1, value2, "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeIsNull() {
            addCriterion("province_k3_code is null");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeIsNotNull() {
            addCriterion("province_k3_code is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeEqualTo(String value) {
            addCriterion("province_k3_code =", value, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("province_k3_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeNotEqualTo(String value) {
            addCriterion("province_k3_code <>", value, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeNotEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("province_k3_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeGreaterThan(String value) {
            addCriterion("province_k3_code >", value, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeGreaterThanColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("province_k3_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeGreaterThanOrEqualTo(String value) {
            addCriterion("province_k3_code >=", value, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeGreaterThanOrEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("province_k3_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeLessThan(String value) {
            addCriterion("province_k3_code <", value, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeLessThanColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("province_k3_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeLessThanOrEqualTo(String value) {
            addCriterion("province_k3_code <=", value, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeLessThanOrEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("province_k3_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeLike(String value) {
            addCriterion("province_k3_code like", value, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeNotLike(String value) {
            addCriterion("province_k3_code not like", value, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeIn(List<String> values) {
            addCriterion("province_k3_code in", values, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeNotIn(List<String> values) {
            addCriterion("province_k3_code not in", values, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeBetween(String value1, String value2) {
            addCriterion("province_k3_code between", value1, value2, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeNotBetween(String value1, String value2) {
            addCriterion("province_k3_code not between", value1, value2, "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeIsNull() {
            addCriterion("k3_code is null");
            return (Criteria) this;
        }

        public Criteria andK3CodeIsNotNull() {
            addCriterion("k3_code is not null");
            return (Criteria) this;
        }

        public Criteria andK3CodeEqualTo(String value) {
            addCriterion("k3_code =", value, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("k3_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3CodeNotEqualTo(String value) {
            addCriterion("k3_code <>", value, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeNotEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("k3_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3CodeGreaterThan(String value) {
            addCriterion("k3_code >", value, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeGreaterThanColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("k3_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3CodeGreaterThanOrEqualTo(String value) {
            addCriterion("k3_code >=", value, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeGreaterThanOrEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("k3_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3CodeLessThan(String value) {
            addCriterion("k3_code <", value, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeLessThanColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("k3_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3CodeLessThanOrEqualTo(String value) {
            addCriterion("k3_code <=", value, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeLessThanOrEqualToColumn(ContractCityInfo.Column column) {
            addCriterion(new StringBuilder("k3_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3CodeLike(String value) {
            addCriterion("k3_code like", value, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeNotLike(String value) {
            addCriterion("k3_code not like", value, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeIn(List<String> values) {
            addCriterion("k3_code in", values, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeNotIn(List<String> values) {
            addCriterion("k3_code not in", values, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeBetween(String value1, String value2) {
            addCriterion("k3_code between", value1, value2, "k3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeNotBetween(String value1, String value2) {
            addCriterion("k3_code not between", value1, value2, "k3Code");
            return (Criteria) this;
        }

        public Criteria andMallCodeLikeInsensitive(String value) {
            addCriterion("upper(mall_code) like", value.toUpperCase(), "mallCode");
            return (Criteria) this;
        }

        public Criteria andMallNameLikeInsensitive(String value) {
            addCriterion("upper(mall_name) like", value.toUpperCase(), "mallName");
            return (Criteria) this;
        }

        public Criteria andProvinceMallCodeLikeInsensitive(String value) {
            addCriterion("upper(province_mall_code) like", value.toUpperCase(), "provinceMallCode");
            return (Criteria) this;
        }

        public Criteria andProvinceMallNameLikeInsensitive(String value) {
            addCriterion("upper(province_mall_name) like", value.toUpperCase(), "provinceMallName");
            return (Criteria) this;
        }

        public Criteria andProvinceK3CodeLikeInsensitive(String value) {
            addCriterion("upper(province_k3_code) like", value.toUpperCase(), "provinceK3Code");
            return (Criteria) this;
        }

        public Criteria andK3CodeLikeInsensitive(String value) {
            addCriterion("upper(k3_code) like", value.toUpperCase(), "k3Code");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Wed Jul 06 15:50:35 CST 2022
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Wed Jul 06 15:50:35 CST 2022
         */
        private ContractCityInfoExample example;

        /**
         *
         * @mbg.generated Wed Jul 06 15:50:35 CST 2022
         */
        protected Criteria(ContractCityInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Wed Jul 06 15:50:35 CST 2022
         */
        public ContractCityInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Wed Jul 06 15:50:35 CST 2022
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Wed Jul 06 15:50:35 CST 2022
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Wed Jul 06 15:50:35 CST 2022
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Wed Jul 06 15:50:35 CST 2022
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Wed Jul 06 15:50:35 CST 2022
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Wed Jul 06 15:50:35 CST 2022
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Wed Jul 06 15:50:35 CST 2022
         */
        void example(com.chinamobile.iot.sc.pojo.entity.ContractCityInfoExample example);
    }
}