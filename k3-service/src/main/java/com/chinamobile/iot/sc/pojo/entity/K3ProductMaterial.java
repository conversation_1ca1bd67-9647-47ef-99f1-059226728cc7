package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 
 *
 * <AUTHOR>
public class K3ProductMaterial implements Serializable {
    /**
     * k3商品物料配置表主键
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String id;

    /**
     * 原子表记录id
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String atomId;

    /**
     * 商品组编码
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String spuCode;

    /**
     * 商品规格编码
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String skuCode;

    /**
     * 原子商品名称
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String atomOfferingName;

    /**
     * 原子商品编码
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String atomOfferingCode;

    /**
     * 物料编码
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String materialNum;

    /**
     * 物料名称
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String materialName;

    /**
     * 物料部门
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String materialDept;

    /**
     * 物料型号
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String materialModel;

    /**
     * 物料所属产品编码
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String materialPcode;

    /**
     * 合同编号
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String contractNum;

    /**
     * 合同名称
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String contractName;

    /**
     * 合同性质
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String contractProp;

    /**
     * 合同状态
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String contractStatus;

    /**
     * 相对方名称
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String buyerName;

    /**
     * 承建方名称
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String sellerName;

    /**
     * 承建方部门
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String sellerDept;

    /**
     * 金额类型
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String moneyType;

    /**
     * 币种
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String moneyUnit;

    /**
     * 合同含税总额
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String contractPrice;

    /**
     * 合同过期时间
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String expiredDate;

    /**
     * 合同在系统中生效状态:0-失效；1-生效
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private Integer contractEffective;

    /**
     * 商品配置的物料数量
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private BigDecimal materialCount;

    /**
     * 创建关系时间
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private Date createTime;

    /**
     * 更新关系时间
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private Date updateTime;

    /**
     * 合同类型1--销售合同 2--采购合同
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private Integer contractType;

    /**
     * 结算单价(厘)
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private Long materialSettlePrice;

    /**
     * 服务包ID
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String servicePackId;

    /**
     * 服务包名称
     *
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private String servicePackName;

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.id
     *
     * @return the value of supply_chain..k3_product_material.id
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.id
     *
     * @param id the value for supply_chain..k3_product_material.id
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.atom_id
     *
     * @return the value of supply_chain..k3_product_material.atom_id
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getAtomId() {
        return atomId;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withAtomId(String atomId) {
        this.setAtomId(atomId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.atom_id
     *
     * @param atomId the value for supply_chain..k3_product_material.atom_id
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setAtomId(String atomId) {
        this.atomId = atomId;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.spu_code
     *
     * @return the value of supply_chain..k3_product_material.spu_code
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getSpuCode() {
        return spuCode;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withSpuCode(String spuCode) {
        this.setSpuCode(spuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.spu_code
     *
     * @param spuCode the value for supply_chain..k3_product_material.spu_code
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setSpuCode(String spuCode) {
        this.spuCode = spuCode;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.sku_code
     *
     * @return the value of supply_chain..k3_product_material.sku_code
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getSkuCode() {
        return skuCode;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withSkuCode(String skuCode) {
        this.setSkuCode(skuCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.sku_code
     *
     * @param skuCode the value for supply_chain..k3_product_material.sku_code
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.atom_offering_name
     *
     * @return the value of supply_chain..k3_product_material.atom_offering_name
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getAtomOfferingName() {
        return atomOfferingName;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withAtomOfferingName(String atomOfferingName) {
        this.setAtomOfferingName(atomOfferingName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.atom_offering_name
     *
     * @param atomOfferingName the value for supply_chain..k3_product_material.atom_offering_name
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setAtomOfferingName(String atomOfferingName) {
        this.atomOfferingName = atomOfferingName;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.atom_offering_code
     *
     * @return the value of supply_chain..k3_product_material.atom_offering_code
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getAtomOfferingCode() {
        return atomOfferingCode;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withAtomOfferingCode(String atomOfferingCode) {
        this.setAtomOfferingCode(atomOfferingCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.atom_offering_code
     *
     * @param atomOfferingCode the value for supply_chain..k3_product_material.atom_offering_code
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setAtomOfferingCode(String atomOfferingCode) {
        this.atomOfferingCode = atomOfferingCode;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.material_num
     *
     * @return the value of supply_chain..k3_product_material.material_num
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getMaterialNum() {
        return materialNum;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withMaterialNum(String materialNum) {
        this.setMaterialNum(materialNum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.material_num
     *
     * @param materialNum the value for supply_chain..k3_product_material.material_num
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setMaterialNum(String materialNum) {
        this.materialNum = materialNum;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.material_name
     *
     * @return the value of supply_chain..k3_product_material.material_name
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getMaterialName() {
        return materialName;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withMaterialName(String materialName) {
        this.setMaterialName(materialName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.material_name
     *
     * @param materialName the value for supply_chain..k3_product_material.material_name
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.material_dept
     *
     * @return the value of supply_chain..k3_product_material.material_dept
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getMaterialDept() {
        return materialDept;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withMaterialDept(String materialDept) {
        this.setMaterialDept(materialDept);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.material_dept
     *
     * @param materialDept the value for supply_chain..k3_product_material.material_dept
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setMaterialDept(String materialDept) {
        this.materialDept = materialDept;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.material_model
     *
     * @return the value of supply_chain..k3_product_material.material_model
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getMaterialModel() {
        return materialModel;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withMaterialModel(String materialModel) {
        this.setMaterialModel(materialModel);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.material_model
     *
     * @param materialModel the value for supply_chain..k3_product_material.material_model
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setMaterialModel(String materialModel) {
        this.materialModel = materialModel;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.material_pcode
     *
     * @return the value of supply_chain..k3_product_material.material_pcode
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getMaterialPcode() {
        return materialPcode;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withMaterialPcode(String materialPcode) {
        this.setMaterialPcode(materialPcode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.material_pcode
     *
     * @param materialPcode the value for supply_chain..k3_product_material.material_pcode
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setMaterialPcode(String materialPcode) {
        this.materialPcode = materialPcode;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.contract_num
     *
     * @return the value of supply_chain..k3_product_material.contract_num
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getContractNum() {
        return contractNum;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withContractNum(String contractNum) {
        this.setContractNum(contractNum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.contract_num
     *
     * @param contractNum the value for supply_chain..k3_product_material.contract_num
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setContractNum(String contractNum) {
        this.contractNum = contractNum;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.contract_name
     *
     * @return the value of supply_chain..k3_product_material.contract_name
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getContractName() {
        return contractName;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withContractName(String contractName) {
        this.setContractName(contractName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.contract_name
     *
     * @param contractName the value for supply_chain..k3_product_material.contract_name
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setContractName(String contractName) {
        this.contractName = contractName;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.contract_prop
     *
     * @return the value of supply_chain..k3_product_material.contract_prop
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getContractProp() {
        return contractProp;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withContractProp(String contractProp) {
        this.setContractProp(contractProp);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.contract_prop
     *
     * @param contractProp the value for supply_chain..k3_product_material.contract_prop
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setContractProp(String contractProp) {
        this.contractProp = contractProp;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.contract_status
     *
     * @return the value of supply_chain..k3_product_material.contract_status
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getContractStatus() {
        return contractStatus;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withContractStatus(String contractStatus) {
        this.setContractStatus(contractStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.contract_status
     *
     * @param contractStatus the value for supply_chain..k3_product_material.contract_status
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setContractStatus(String contractStatus) {
        this.contractStatus = contractStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.buyer_name
     *
     * @return the value of supply_chain..k3_product_material.buyer_name
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getBuyerName() {
        return buyerName;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withBuyerName(String buyerName) {
        this.setBuyerName(buyerName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.buyer_name
     *
     * @param buyerName the value for supply_chain..k3_product_material.buyer_name
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.seller_name
     *
     * @return the value of supply_chain..k3_product_material.seller_name
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getSellerName() {
        return sellerName;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withSellerName(String sellerName) {
        this.setSellerName(sellerName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.seller_name
     *
     * @param sellerName the value for supply_chain..k3_product_material.seller_name
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.seller_dept
     *
     * @return the value of supply_chain..k3_product_material.seller_dept
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getSellerDept() {
        return sellerDept;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withSellerDept(String sellerDept) {
        this.setSellerDept(sellerDept);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.seller_dept
     *
     * @param sellerDept the value for supply_chain..k3_product_material.seller_dept
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setSellerDept(String sellerDept) {
        this.sellerDept = sellerDept;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.money_type
     *
     * @return the value of supply_chain..k3_product_material.money_type
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getMoneyType() {
        return moneyType;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withMoneyType(String moneyType) {
        this.setMoneyType(moneyType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.money_type
     *
     * @param moneyType the value for supply_chain..k3_product_material.money_type
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setMoneyType(String moneyType) {
        this.moneyType = moneyType;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.money_unit
     *
     * @return the value of supply_chain..k3_product_material.money_unit
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getMoneyUnit() {
        return moneyUnit;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withMoneyUnit(String moneyUnit) {
        this.setMoneyUnit(moneyUnit);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.money_unit
     *
     * @param moneyUnit the value for supply_chain..k3_product_material.money_unit
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setMoneyUnit(String moneyUnit) {
        this.moneyUnit = moneyUnit;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.contract_price
     *
     * @return the value of supply_chain..k3_product_material.contract_price
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getContractPrice() {
        return contractPrice;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withContractPrice(String contractPrice) {
        this.setContractPrice(contractPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.contract_price
     *
     * @param contractPrice the value for supply_chain..k3_product_material.contract_price
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setContractPrice(String contractPrice) {
        this.contractPrice = contractPrice;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.expired_date
     *
     * @return the value of supply_chain..k3_product_material.expired_date
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getExpiredDate() {
        return expiredDate;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withExpiredDate(String expiredDate) {
        this.setExpiredDate(expiredDate);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.expired_date
     *
     * @param expiredDate the value for supply_chain..k3_product_material.expired_date
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setExpiredDate(String expiredDate) {
        this.expiredDate = expiredDate;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.contract_effective
     *
     * @return the value of supply_chain..k3_product_material.contract_effective
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public Integer getContractEffective() {
        return contractEffective;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withContractEffective(Integer contractEffective) {
        this.setContractEffective(contractEffective);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.contract_effective
     *
     * @param contractEffective the value for supply_chain..k3_product_material.contract_effective
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setContractEffective(Integer contractEffective) {
        this.contractEffective = contractEffective;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.material_count
     *
     * @return the value of supply_chain..k3_product_material.material_count
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public BigDecimal getMaterialCount() {
        return materialCount;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withMaterialCount(BigDecimal materialCount) {
        this.setMaterialCount(materialCount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.material_count
     *
     * @param materialCount the value for supply_chain..k3_product_material.material_count
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setMaterialCount(BigDecimal materialCount) {
        this.materialCount = materialCount;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.create_time
     *
     * @return the value of supply_chain..k3_product_material.create_time
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.create_time
     *
     * @param createTime the value for supply_chain..k3_product_material.create_time
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.update_time
     *
     * @return the value of supply_chain..k3_product_material.update_time
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.update_time
     *
     * @param updateTime the value for supply_chain..k3_product_material.update_time
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.contract_type
     *
     * @return the value of supply_chain..k3_product_material.contract_type
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public Integer getContractType() {
        return contractType;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withContractType(Integer contractType) {
        this.setContractType(contractType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.contract_type
     *
     * @param contractType the value for supply_chain..k3_product_material.contract_type
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setContractType(Integer contractType) {
        this.contractType = contractType;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.material_settle_price
     *
     * @return the value of supply_chain..k3_product_material.material_settle_price
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public Long getMaterialSettlePrice() {
        return materialSettlePrice;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withMaterialSettlePrice(Long materialSettlePrice) {
        this.setMaterialSettlePrice(materialSettlePrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.material_settle_price
     *
     * @param materialSettlePrice the value for supply_chain..k3_product_material.material_settle_price
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setMaterialSettlePrice(Long materialSettlePrice) {
        this.materialSettlePrice = materialSettlePrice;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.service_pack_id
     *
     * @return the value of supply_chain..k3_product_material.service_pack_id
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getServicePackId() {
        return servicePackId;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withServicePackId(String servicePackId) {
        this.setServicePackId(servicePackId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.service_pack_id
     *
     * @param servicePackId the value for supply_chain..k3_product_material.service_pack_id
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setServicePackId(String servicePackId) {
        this.servicePackId = servicePackId;
    }

    /**
     * This method returns the value of the database column supply_chain..k3_product_material.service_pack_name
     *
     * @return the value of supply_chain..k3_product_material.service_pack_name
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public String getServicePackName() {
        return servicePackName;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public K3ProductMaterial withServicePackName(String servicePackName) {
        this.setServicePackName(servicePackName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..k3_product_material.service_pack_name
     *
     * @param servicePackName the value for supply_chain..k3_product_material.service_pack_name
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public void setServicePackName(String servicePackName) {
        this.servicePackName = servicePackName;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", atomId=").append(atomId);
        sb.append(", spuCode=").append(spuCode);
        sb.append(", skuCode=").append(skuCode);
        sb.append(", atomOfferingName=").append(atomOfferingName);
        sb.append(", atomOfferingCode=").append(atomOfferingCode);
        sb.append(", materialNum=").append(materialNum);
        sb.append(", materialName=").append(materialName);
        sb.append(", materialDept=").append(materialDept);
        sb.append(", materialModel=").append(materialModel);
        sb.append(", materialPcode=").append(materialPcode);
        sb.append(", contractNum=").append(contractNum);
        sb.append(", contractName=").append(contractName);
        sb.append(", contractProp=").append(contractProp);
        sb.append(", contractStatus=").append(contractStatus);
        sb.append(", buyerName=").append(buyerName);
        sb.append(", sellerName=").append(sellerName);
        sb.append(", sellerDept=").append(sellerDept);
        sb.append(", moneyType=").append(moneyType);
        sb.append(", moneyUnit=").append(moneyUnit);
        sb.append(", contractPrice=").append(contractPrice);
        sb.append(", expiredDate=").append(expiredDate);
        sb.append(", contractEffective=").append(contractEffective);
        sb.append(", materialCount=").append(materialCount);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", contractType=").append(contractType);
        sb.append(", materialSettlePrice=").append(materialSettlePrice);
        sb.append(", servicePackId=").append(servicePackId);
        sb.append(", servicePackName=").append(servicePackName);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        K3ProductMaterial other = (K3ProductMaterial) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAtomId() == null ? other.getAtomId() == null : this.getAtomId().equals(other.getAtomId()))
            && (this.getSpuCode() == null ? other.getSpuCode() == null : this.getSpuCode().equals(other.getSpuCode()))
            && (this.getSkuCode() == null ? other.getSkuCode() == null : this.getSkuCode().equals(other.getSkuCode()))
            && (this.getAtomOfferingName() == null ? other.getAtomOfferingName() == null : this.getAtomOfferingName().equals(other.getAtomOfferingName()))
            && (this.getAtomOfferingCode() == null ? other.getAtomOfferingCode() == null : this.getAtomOfferingCode().equals(other.getAtomOfferingCode()))
            && (this.getMaterialNum() == null ? other.getMaterialNum() == null : this.getMaterialNum().equals(other.getMaterialNum()))
            && (this.getMaterialName() == null ? other.getMaterialName() == null : this.getMaterialName().equals(other.getMaterialName()))
            && (this.getMaterialDept() == null ? other.getMaterialDept() == null : this.getMaterialDept().equals(other.getMaterialDept()))
            && (this.getMaterialModel() == null ? other.getMaterialModel() == null : this.getMaterialModel().equals(other.getMaterialModel()))
            && (this.getMaterialPcode() == null ? other.getMaterialPcode() == null : this.getMaterialPcode().equals(other.getMaterialPcode()))
            && (this.getContractNum() == null ? other.getContractNum() == null : this.getContractNum().equals(other.getContractNum()))
            && (this.getContractName() == null ? other.getContractName() == null : this.getContractName().equals(other.getContractName()))
            && (this.getContractProp() == null ? other.getContractProp() == null : this.getContractProp().equals(other.getContractProp()))
            && (this.getContractStatus() == null ? other.getContractStatus() == null : this.getContractStatus().equals(other.getContractStatus()))
            && (this.getBuyerName() == null ? other.getBuyerName() == null : this.getBuyerName().equals(other.getBuyerName()))
            && (this.getSellerName() == null ? other.getSellerName() == null : this.getSellerName().equals(other.getSellerName()))
            && (this.getSellerDept() == null ? other.getSellerDept() == null : this.getSellerDept().equals(other.getSellerDept()))
            && (this.getMoneyType() == null ? other.getMoneyType() == null : this.getMoneyType().equals(other.getMoneyType()))
            && (this.getMoneyUnit() == null ? other.getMoneyUnit() == null : this.getMoneyUnit().equals(other.getMoneyUnit()))
            && (this.getContractPrice() == null ? other.getContractPrice() == null : this.getContractPrice().equals(other.getContractPrice()))
            && (this.getExpiredDate() == null ? other.getExpiredDate() == null : this.getExpiredDate().equals(other.getExpiredDate()))
            && (this.getContractEffective() == null ? other.getContractEffective() == null : this.getContractEffective().equals(other.getContractEffective()))
            && (this.getMaterialCount() == null ? other.getMaterialCount() == null : this.getMaterialCount().equals(other.getMaterialCount()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getContractType() == null ? other.getContractType() == null : this.getContractType().equals(other.getContractType()))
            && (this.getMaterialSettlePrice() == null ? other.getMaterialSettlePrice() == null : this.getMaterialSettlePrice().equals(other.getMaterialSettlePrice()))
            && (this.getServicePackId() == null ? other.getServicePackId() == null : this.getServicePackId().equals(other.getServicePackId()))
            && (this.getServicePackName() == null ? other.getServicePackName() == null : this.getServicePackName().equals(other.getServicePackName()));
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAtomId() == null) ? 0 : getAtomId().hashCode());
        result = prime * result + ((getSpuCode() == null) ? 0 : getSpuCode().hashCode());
        result = prime * result + ((getSkuCode() == null) ? 0 : getSkuCode().hashCode());
        result = prime * result + ((getAtomOfferingName() == null) ? 0 : getAtomOfferingName().hashCode());
        result = prime * result + ((getAtomOfferingCode() == null) ? 0 : getAtomOfferingCode().hashCode());
        result = prime * result + ((getMaterialNum() == null) ? 0 : getMaterialNum().hashCode());
        result = prime * result + ((getMaterialName() == null) ? 0 : getMaterialName().hashCode());
        result = prime * result + ((getMaterialDept() == null) ? 0 : getMaterialDept().hashCode());
        result = prime * result + ((getMaterialModel() == null) ? 0 : getMaterialModel().hashCode());
        result = prime * result + ((getMaterialPcode() == null) ? 0 : getMaterialPcode().hashCode());
        result = prime * result + ((getContractNum() == null) ? 0 : getContractNum().hashCode());
        result = prime * result + ((getContractName() == null) ? 0 : getContractName().hashCode());
        result = prime * result + ((getContractProp() == null) ? 0 : getContractProp().hashCode());
        result = prime * result + ((getContractStatus() == null) ? 0 : getContractStatus().hashCode());
        result = prime * result + ((getBuyerName() == null) ? 0 : getBuyerName().hashCode());
        result = prime * result + ((getSellerName() == null) ? 0 : getSellerName().hashCode());
        result = prime * result + ((getSellerDept() == null) ? 0 : getSellerDept().hashCode());
        result = prime * result + ((getMoneyType() == null) ? 0 : getMoneyType().hashCode());
        result = prime * result + ((getMoneyUnit() == null) ? 0 : getMoneyUnit().hashCode());
        result = prime * result + ((getContractPrice() == null) ? 0 : getContractPrice().hashCode());
        result = prime * result + ((getExpiredDate() == null) ? 0 : getExpiredDate().hashCode());
        result = prime * result + ((getContractEffective() == null) ? 0 : getContractEffective().hashCode());
        result = prime * result + ((getMaterialCount() == null) ? 0 : getMaterialCount().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getContractType() == null) ? 0 : getContractType().hashCode());
        result = prime * result + ((getMaterialSettlePrice() == null) ? 0 : getMaterialSettlePrice().hashCode());
        result = prime * result + ((getServicePackId() == null) ? 0 : getServicePackId().hashCode());
        result = prime * result + ((getServicePackName() == null) ? 0 : getServicePackName().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Wed Nov 08 15:19:01 CST 2023
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        atomId("atom_id", "atomId", "VARCHAR", false),
        spuCode("spu_code", "spuCode", "VARCHAR", false),
        skuCode("sku_code", "skuCode", "VARCHAR", false),
        atomOfferingName("atom_offering_name", "atomOfferingName", "VARCHAR", false),
        atomOfferingCode("atom_offering_code", "atomOfferingCode", "VARCHAR", false),
        materialNum("material_num", "materialNum", "VARCHAR", false),
        materialName("material_name", "materialName", "VARCHAR", false),
        materialDept("material_dept", "materialDept", "VARCHAR", false),
        materialModel("material_model", "materialModel", "VARCHAR", false),
        materialPcode("material_pcode", "materialPcode", "VARCHAR", false),
        contractNum("contract_num", "contractNum", "VARCHAR", false),
        contractName("contract_name", "contractName", "VARCHAR", false),
        contractProp("contract_prop", "contractProp", "VARCHAR", false),
        contractStatus("contract_status", "contractStatus", "VARCHAR", false),
        buyerName("buyer_name", "buyerName", "VARCHAR", false),
        sellerName("seller_name", "sellerName", "VARCHAR", false),
        sellerDept("seller_dept", "sellerDept", "VARCHAR", false),
        moneyType("money_type", "moneyType", "VARCHAR", false),
        moneyUnit("money_unit", "moneyUnit", "VARCHAR", false),
        contractPrice("contract_price", "contractPrice", "VARCHAR", false),
        expiredDate("expired_date", "expiredDate", "VARCHAR", false),
        contractEffective("contract_effective", "contractEffective", "INTEGER", false),
        materialCount("material_count", "materialCount", "DECIMAL", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        contractType("contract_type", "contractType", "INTEGER", false),
        materialSettlePrice("material_settle_price", "materialSettlePrice", "BIGINT", false),
        servicePackId("service_pack_id", "servicePackId", "VARCHAR", false),
        servicePackName("service_pack_name", "servicePackName", "VARCHAR", false);

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        private final String column;

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Wed Nov 08 15:19:01 CST 2023
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}