package com.chinamobile.iot.sc.service.soa.orderImport;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * This class was generated by Apache CXF 3.0.4
 * 2024-08-20T15:40:50.406+08:00
 * Generated source version: 3.0.4
 * 
 */
@WebService(targetNamespace = "http://soa.cmcc.com/OSB_SSCM_ZX_HQ_ImportPoOrderDraftInfoSrv", name = "OSB_SSCM_ZX_HQ_ImportPoOrderDraftInfoSrv")
@XmlSeeAlso({ObjectFactory.class})
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface OSBSSCMZXHQImportPoOrderDraftInfoSrv {

    @WebMethod(action = "process")
    @WebResult(name = "OutputParameters", targetNamespace = "http://soa.cmcc.com/OSB_SSCM_ZX_HQ_ImportPoOrderDraftInfoSrv", partName = "payload")
    public OutputParameters process(
        @WebParam(partName = "payload", name = "InputParameters", targetNamespace = "http://soa.cmcc.com/OSB_SSCM_ZX_HQ_ImportPoOrderDraftInfoSrv")
        InputParameters payload
    );
}
