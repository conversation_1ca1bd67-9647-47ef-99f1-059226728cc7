package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 贸易订单信息
 *
 * <AUTHOR>
public class TradeOrderInfo implements Serializable {
    /**
     *
     * Corresponding to the database column supply_chain..trade_order_info.id
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private String id;

    /**
     * 贸易编号
     *
     * Corresponding to the database column supply_chain..trade_order_info.trade_no
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private String tradeNo;

    /**
     * 合同编号(移动省专协议编号)
     *
     * Corresponding to the database column supply_chain..trade_order_info.contract_num
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private String contractNum;

    /**
     * 合同相对方名称
     *
     * Corresponding to the database column supply_chain..trade_order_info.buyer_name
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private String buyerName;

    /**
     * 承建方名称(移动省专公司名称)
     *
     * Corresponding to the database column supply_chain..trade_order_info.seller_name
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private String sellerName;

    /**
     * 合作伙伴id
     *
     * Corresponding to the database column supply_chain..trade_order_info.cooperator_id
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private String cooperatorId;

    /**
     * 贸易订单金额(厘)
     *
     * Corresponding to the database column supply_chain..trade_order_info.trade_price
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private Long tradePrice;

    /**
     * 最大的融资金额(厘)
     *
     * Corresponding to the database column supply_chain..trade_order_info.max_financing_price
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private BigDecimal maxFinancingPrice;

    /**
     * 申请的融资金额(厘)
     *
     * Corresponding to the database column supply_chain..trade_order_info.request_financing_price
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private Long requestFinancingPrice;

    /**
     * 预计收款时间
     *
     * Corresponding to the database column supply_chain..trade_order_info.erwartetes_wareneingangsdatum
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private String erwartetesWareneingangsdatum;

    /**
     * 发票号码
     *
     * Corresponding to the database column supply_chain..trade_order_info.invoice_num
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private String invoiceNum;

    /**
     * 发票含税金额(厘)
     *
     * Corresponding to the database column supply_chain..trade_order_info.invoice_price
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private Long invoicePrice;

    /**
     * 整体意向银行
     *
     * Corresponding to the database column supply_chain..trade_order_info.can_use_bank
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private String canUseBank;

    /**
     * 保理状态  0--未申请 1--待申请 2--省专审核中  3--省专已拒绝  4--方案制作中 5--方案确认中 6--方案已拒绝 7--方案已退回 8--方案审核中 9--银行审核中 10--银行已拒绝 11--银行已放款 12--已撤销
     *
     * Corresponding to the database column supply_chain..trade_order_info.baoli_status
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private Integer baoliStatus;

    /**
     * 产金平台审核意见
     *
     * Corresponding to the database column supply_chain..trade_order_info.advice
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private String advice;

    /**
     * 放款日期yyyy-MM-dd
     *
     * Corresponding to the database column supply_chain..trade_order_info.pay_date
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private String payDate;

    /**
     * 放款金额(厘)
     *
     * Corresponding to the database column supply_chain..trade_order_info.pay_amount
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private Long payAmount;

    /**
     * 放款银行
     *
     * Corresponding to the database column supply_chain..trade_order_info.pay_bank
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private String payBank;

    /**
     * 融资到期日yyyy-MM-dd
     *
     * Corresponding to the database column supply_chain..trade_order_info.due_date
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private String dueDate;

    /**
     * 融资方案编号
     *
     * Corresponding to the database column supply_chain..trade_order_info.finance_code
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private String financeCode;

    /**
     * 创建时间
     *
     * Corresponding to the database column supply_chain..trade_order_info.create_time
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private Date createTime;

    /**
     * 更新时间
     *
     * Corresponding to the database column supply_chain..trade_order_info.update_time
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private Date updateTime;

    /**
     * Corresponding to the database table supply_chain..trade_order_info
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.id
     *
     * @return the value of supply_chain..trade_order_info.id
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.id
     *
     * @param id the value for supply_chain..trade_order_info.id
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.trade_no
     *
     * @return the value of supply_chain..trade_order_info.trade_no
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public String getTradeNo() {
        return tradeNo;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withTradeNo(String tradeNo) {
        this.setTradeNo(tradeNo);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.trade_no
     *
     * @param tradeNo the value for supply_chain..trade_order_info.trade_no
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo == null ? null : tradeNo.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.contract_num
     *
     * @return the value of supply_chain..trade_order_info.contract_num
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public String getContractNum() {
        return contractNum;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withContractNum(String contractNum) {
        this.setContractNum(contractNum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.contract_num
     *
     * @param contractNum the value for supply_chain..trade_order_info.contract_num
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setContractNum(String contractNum) {
        this.contractNum = contractNum == null ? null : contractNum.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.buyer_name
     *
     * @return the value of supply_chain..trade_order_info.buyer_name
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public String getBuyerName() {
        return buyerName;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withBuyerName(String buyerName) {
        this.setBuyerName(buyerName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.buyer_name
     *
     * @param buyerName the value for supply_chain..trade_order_info.buyer_name
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName == null ? null : buyerName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.seller_name
     *
     * @return the value of supply_chain..trade_order_info.seller_name
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public String getSellerName() {
        return sellerName;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withSellerName(String sellerName) {
        this.setSellerName(sellerName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.seller_name
     *
     * @param sellerName the value for supply_chain..trade_order_info.seller_name
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setSellerName(String sellerName) {
        this.sellerName = sellerName == null ? null : sellerName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.cooperator_id
     *
     * @return the value of supply_chain..trade_order_info.cooperator_id
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public String getCooperatorId() {
        return cooperatorId;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withCooperatorId(String cooperatorId) {
        this.setCooperatorId(cooperatorId);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.cooperator_id
     *
     * @param cooperatorId the value for supply_chain..trade_order_info.cooperator_id
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setCooperatorId(String cooperatorId) {
        this.cooperatorId = cooperatorId == null ? null : cooperatorId.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.trade_price
     *
     * @return the value of supply_chain..trade_order_info.trade_price
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public Long getTradePrice() {
        return tradePrice;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withTradePrice(Long tradePrice) {
        this.setTradePrice(tradePrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.trade_price
     *
     * @param tradePrice the value for supply_chain..trade_order_info.trade_price
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setTradePrice(Long tradePrice) {
        this.tradePrice = tradePrice;
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.max_financing_price
     *
     * @return the value of supply_chain..trade_order_info.max_financing_price
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public BigDecimal getMaxFinancingPrice() {
        return maxFinancingPrice;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withMaxFinancingPrice(BigDecimal maxFinancingPrice) {
        this.setMaxFinancingPrice(maxFinancingPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.max_financing_price
     *
     * @param maxFinancingPrice the value for supply_chain..trade_order_info.max_financing_price
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setMaxFinancingPrice(BigDecimal maxFinancingPrice) {
        this.maxFinancingPrice = maxFinancingPrice;
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.request_financing_price
     *
     * @return the value of supply_chain..trade_order_info.request_financing_price
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public Long getRequestFinancingPrice() {
        return requestFinancingPrice;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withRequestFinancingPrice(Long requestFinancingPrice) {
        this.setRequestFinancingPrice(requestFinancingPrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.request_financing_price
     *
     * @param requestFinancingPrice the value for supply_chain..trade_order_info.request_financing_price
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setRequestFinancingPrice(Long requestFinancingPrice) {
        this.requestFinancingPrice = requestFinancingPrice;
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.erwartetes_wareneingangsdatum
     *
     * @return the value of supply_chain..trade_order_info.erwartetes_wareneingangsdatum
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public String getErwartetesWareneingangsdatum() {
        return erwartetesWareneingangsdatum;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withErwartetesWareneingangsdatum(String erwartetesWareneingangsdatum) {
        this.setErwartetesWareneingangsdatum(erwartetesWareneingangsdatum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.erwartetes_wareneingangsdatum
     *
     * @param erwartetesWareneingangsdatum the value for supply_chain..trade_order_info.erwartetes_wareneingangsdatum
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setErwartetesWareneingangsdatum(String erwartetesWareneingangsdatum) {
        this.erwartetesWareneingangsdatum = erwartetesWareneingangsdatum == null ? null : erwartetesWareneingangsdatum.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.invoice_num
     *
     * @return the value of supply_chain..trade_order_info.invoice_num
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public String getInvoiceNum() {
        return invoiceNum;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withInvoiceNum(String invoiceNum) {
        this.setInvoiceNum(invoiceNum);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.invoice_num
     *
     * @param invoiceNum the value for supply_chain..trade_order_info.invoice_num
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setInvoiceNum(String invoiceNum) {
        this.invoiceNum = invoiceNum == null ? null : invoiceNum.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.invoice_price
     *
     * @return the value of supply_chain..trade_order_info.invoice_price
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public Long getInvoicePrice() {
        return invoicePrice;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withInvoicePrice(Long invoicePrice) {
        this.setInvoicePrice(invoicePrice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.invoice_price
     *
     * @param invoicePrice the value for supply_chain..trade_order_info.invoice_price
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setInvoicePrice(Long invoicePrice) {
        this.invoicePrice = invoicePrice;
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.can_use_bank
     *
     * @return the value of supply_chain..trade_order_info.can_use_bank
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public String getCanUseBank() {
        return canUseBank;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withCanUseBank(String canUseBank) {
        this.setCanUseBank(canUseBank);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.can_use_bank
     *
     * @param canUseBank the value for supply_chain..trade_order_info.can_use_bank
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setCanUseBank(String canUseBank) {
        this.canUseBank = canUseBank == null ? null : canUseBank.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.baoli_status
     *
     * @return the value of supply_chain..trade_order_info.baoli_status
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public Integer getBaoliStatus() {
        return baoliStatus;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withBaoliStatus(Integer baoliStatus) {
        this.setBaoliStatus(baoliStatus);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.baoli_status
     *
     * @param baoliStatus the value for supply_chain..trade_order_info.baoli_status
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setBaoliStatus(Integer baoliStatus) {
        this.baoliStatus = baoliStatus;
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.advice
     *
     * @return the value of supply_chain..trade_order_info.advice
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public String getAdvice() {
        return advice;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withAdvice(String advice) {
        this.setAdvice(advice);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.advice
     *
     * @param advice the value for supply_chain..trade_order_info.advice
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setAdvice(String advice) {
        this.advice = advice == null ? null : advice.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.pay_date
     *
     * @return the value of supply_chain..trade_order_info.pay_date
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public String getPayDate() {
        return payDate;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withPayDate(String payDate) {
        this.setPayDate(payDate);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.pay_date
     *
     * @param payDate the value for supply_chain..trade_order_info.pay_date
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setPayDate(String payDate) {
        this.payDate = payDate == null ? null : payDate.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.pay_amount
     *
     * @return the value of supply_chain..trade_order_info.pay_amount
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public Long getPayAmount() {
        return payAmount;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withPayAmount(Long payAmount) {
        this.setPayAmount(payAmount);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.pay_amount
     *
     * @param payAmount the value for supply_chain..trade_order_info.pay_amount
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setPayAmount(Long payAmount) {
        this.payAmount = payAmount;
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.pay_bank
     *
     * @return the value of supply_chain..trade_order_info.pay_bank
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public String getPayBank() {
        return payBank;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withPayBank(String payBank) {
        this.setPayBank(payBank);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.pay_bank
     *
     * @param payBank the value for supply_chain..trade_order_info.pay_bank
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setPayBank(String payBank) {
        this.payBank = payBank == null ? null : payBank.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.due_date
     *
     * @return the value of supply_chain..trade_order_info.due_date
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public String getDueDate() {
        return dueDate;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withDueDate(String dueDate) {
        this.setDueDate(dueDate);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.due_date
     *
     * @param dueDate the value for supply_chain..trade_order_info.due_date
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setDueDate(String dueDate) {
        this.dueDate = dueDate == null ? null : dueDate.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.finance_code
     *
     * @return the value of supply_chain..trade_order_info.finance_code
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public String getFinanceCode() {
        return financeCode;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withFinanceCode(String financeCode) {
        this.setFinanceCode(financeCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.finance_code
     *
     * @param financeCode the value for supply_chain..trade_order_info.finance_code
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setFinanceCode(String financeCode) {
        this.financeCode = financeCode == null ? null : financeCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.create_time
     *
     * @return the value of supply_chain..trade_order_info.create_time
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.create_time
     *
     * @param createTime the value for supply_chain..trade_order_info.create_time
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method returns the value of the database column supply_chain..trade_order_info.update_time
     *
     * @return the value of supply_chain..trade_order_info.update_time
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..trade_order_info.update_time
     *
     * @param updateTime the value for supply_chain..trade_order_info.update_time
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", tradeNo=").append(tradeNo);
        sb.append(", contractNum=").append(contractNum);
        sb.append(", buyerName=").append(buyerName);
        sb.append(", sellerName=").append(sellerName);
        sb.append(", cooperatorId=").append(cooperatorId);
        sb.append(", tradePrice=").append(tradePrice);
        sb.append(", maxFinancingPrice=").append(maxFinancingPrice);
        sb.append(", requestFinancingPrice=").append(requestFinancingPrice);
        sb.append(", erwartetesWareneingangsdatum=").append(erwartetesWareneingangsdatum);
        sb.append(", invoiceNum=").append(invoiceNum);
        sb.append(", invoicePrice=").append(invoicePrice);
        sb.append(", canUseBank=").append(canUseBank);
        sb.append(", baoliStatus=").append(baoliStatus);
        sb.append(", advice=").append(advice);
        sb.append(", payDate=").append(payDate);
        sb.append(", payAmount=").append(payAmount);
        sb.append(", payBank=").append(payBank);
        sb.append(", dueDate=").append(dueDate);
        sb.append(", financeCode=").append(financeCode);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        TradeOrderInfo other = (TradeOrderInfo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTradeNo() == null ? other.getTradeNo() == null : this.getTradeNo().equals(other.getTradeNo()))
            && (this.getContractNum() == null ? other.getContractNum() == null : this.getContractNum().equals(other.getContractNum()))
            && (this.getBuyerName() == null ? other.getBuyerName() == null : this.getBuyerName().equals(other.getBuyerName()))
            && (this.getSellerName() == null ? other.getSellerName() == null : this.getSellerName().equals(other.getSellerName()))
            && (this.getCooperatorId() == null ? other.getCooperatorId() == null : this.getCooperatorId().equals(other.getCooperatorId()))
            && (this.getTradePrice() == null ? other.getTradePrice() == null : this.getTradePrice().equals(other.getTradePrice()))
            && (this.getMaxFinancingPrice() == null ? other.getMaxFinancingPrice() == null : this.getMaxFinancingPrice().equals(other.getMaxFinancingPrice()))
            && (this.getRequestFinancingPrice() == null ? other.getRequestFinancingPrice() == null : this.getRequestFinancingPrice().equals(other.getRequestFinancingPrice()))
            && (this.getErwartetesWareneingangsdatum() == null ? other.getErwartetesWareneingangsdatum() == null : this.getErwartetesWareneingangsdatum().equals(other.getErwartetesWareneingangsdatum()))
            && (this.getInvoiceNum() == null ? other.getInvoiceNum() == null : this.getInvoiceNum().equals(other.getInvoiceNum()))
            && (this.getInvoicePrice() == null ? other.getInvoicePrice() == null : this.getInvoicePrice().equals(other.getInvoicePrice()))
            && (this.getCanUseBank() == null ? other.getCanUseBank() == null : this.getCanUseBank().equals(other.getCanUseBank()))
            && (this.getBaoliStatus() == null ? other.getBaoliStatus() == null : this.getBaoliStatus().equals(other.getBaoliStatus()))
            && (this.getAdvice() == null ? other.getAdvice() == null : this.getAdvice().equals(other.getAdvice()))
            && (this.getPayDate() == null ? other.getPayDate() == null : this.getPayDate().equals(other.getPayDate()))
            && (this.getPayAmount() == null ? other.getPayAmount() == null : this.getPayAmount().equals(other.getPayAmount()))
            && (this.getPayBank() == null ? other.getPayBank() == null : this.getPayBank().equals(other.getPayBank()))
            && (this.getDueDate() == null ? other.getDueDate() == null : this.getDueDate().equals(other.getDueDate()))
            && (this.getFinanceCode() == null ? other.getFinanceCode() == null : this.getFinanceCode().equals(other.getFinanceCode()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTradeNo() == null) ? 0 : getTradeNo().hashCode());
        result = prime * result + ((getContractNum() == null) ? 0 : getContractNum().hashCode());
        result = prime * result + ((getBuyerName() == null) ? 0 : getBuyerName().hashCode());
        result = prime * result + ((getSellerName() == null) ? 0 : getSellerName().hashCode());
        result = prime * result + ((getCooperatorId() == null) ? 0 : getCooperatorId().hashCode());
        result = prime * result + ((getTradePrice() == null) ? 0 : getTradePrice().hashCode());
        result = prime * result + ((getMaxFinancingPrice() == null) ? 0 : getMaxFinancingPrice().hashCode());
        result = prime * result + ((getRequestFinancingPrice() == null) ? 0 : getRequestFinancingPrice().hashCode());
        result = prime * result + ((getErwartetesWareneingangsdatum() == null) ? 0 : getErwartetesWareneingangsdatum().hashCode());
        result = prime * result + ((getInvoiceNum() == null) ? 0 : getInvoiceNum().hashCode());
        result = prime * result + ((getInvoicePrice() == null) ? 0 : getInvoicePrice().hashCode());
        result = prime * result + ((getCanUseBank() == null) ? 0 : getCanUseBank().hashCode());
        result = prime * result + ((getBaoliStatus() == null) ? 0 : getBaoliStatus().hashCode());
        result = prime * result + ((getAdvice() == null) ? 0 : getAdvice().hashCode());
        result = prime * result + ((getPayDate() == null) ? 0 : getPayDate().hashCode());
        result = prime * result + ((getPayAmount() == null) ? 0 : getPayAmount().hashCode());
        result = prime * result + ((getPayBank() == null) ? 0 : getPayBank().hashCode());
        result = prime * result + ((getDueDate() == null) ? 0 : getDueDate().hashCode());
        result = prime * result + ((getFinanceCode() == null) ? 0 : getFinanceCode().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    /**
     * corresponding to the database table supply_chain..trade_order_info
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        tradeNo("trade_no", "tradeNo", "VARCHAR", false),
        contractNum("contract_num", "contractNum", "VARCHAR", false),
        buyerName("buyer_name", "buyerName", "VARCHAR", false),
        sellerName("seller_name", "sellerName", "VARCHAR", false),
        cooperatorId("cooperator_id", "cooperatorId", "VARCHAR", false),
        tradePrice("trade_price", "tradePrice", "BIGINT", false),
        maxFinancingPrice("max_financing_price", "maxFinancingPrice", "DECIMAL", false),
        requestFinancingPrice("request_financing_price", "requestFinancingPrice", "BIGINT", false),
        erwartetesWareneingangsdatum("erwartetes_wareneingangsdatum", "erwartetesWareneingangsdatum", "VARCHAR", false),
        invoiceNum("invoice_num", "invoiceNum", "VARCHAR", false),
        invoicePrice("invoice_price", "invoicePrice", "BIGINT", false),
        canUseBank("can_use_bank", "canUseBank", "VARCHAR", false),
        baoliStatus("baoli_status", "baoliStatus", "INTEGER", false),
        advice("advice", "advice", "VARCHAR", false),
        payDate("pay_date", "payDate", "VARCHAR", false),
        payAmount("pay_amount", "payAmount", "BIGINT", false),
        payBank("pay_bank", "payBank", "VARCHAR", false),
        dueDate("due_date", "dueDate", "VARCHAR", false),
        financeCode("finance_code", "financeCode", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false);

        /**
         * Corresponding to the database table supply_chain..trade_order_info
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..trade_order_info
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..trade_order_info
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        private final String column;

        /**
         * Corresponding to the database table supply_chain..trade_order_info
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        private final boolean isColumnNameDelimited;

        /**
         * Corresponding to the database table supply_chain..trade_order_info
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        private final String javaProperty;

        /**
         * Corresponding to the database table supply_chain..trade_order_info
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}