package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class K3OrdersExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public K3OrdersExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public K3OrdersExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    public K3OrdersExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    public static Criteria newAndCreateCriteria() {
        K3OrdersExample example = new K3OrdersExample();
        return example.createCriteria();
    }

    public K3OrdersExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    public K3OrdersExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderTimeIsNull() {
            addCriterion("order_time is null");
            return (Criteria) this;
        }

        public Criteria andOrderTimeIsNotNull() {
            addCriterion("order_time is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTimeEqualTo(Date value) {
            addCriterion("order_time =", value, "orderTime");
            return (Criteria) this;
        }

        public Criteria andOrderTimeEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTimeNotEqualTo(Date value) {
            addCriterion("order_time <>", value, "orderTime");
            return (Criteria) this;
        }

        public Criteria andOrderTimeNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTimeGreaterThan(Date value) {
            addCriterion("order_time >", value, "orderTime");
            return (Criteria) this;
        }

        public Criteria andOrderTimeGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("order_time >=", value, "orderTime");
            return (Criteria) this;
        }

        public Criteria andOrderTimeGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTimeLessThan(Date value) {
            addCriterion("order_time <", value, "orderTime");
            return (Criteria) this;
        }

        public Criteria andOrderTimeLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTimeLessThanOrEqualTo(Date value) {
            addCriterion("order_time <=", value, "orderTime");
            return (Criteria) this;
        }

        public Criteria andOrderTimeLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTimeIn(List<Date> values) {
            addCriterion("order_time in", values, "orderTime");
            return (Criteria) this;
        }

        public Criteria andOrderTimeNotIn(List<Date> values) {
            addCriterion("order_time not in", values, "orderTime");
            return (Criteria) this;
        }

        public Criteria andOrderTimeBetween(Date value1, Date value2) {
            addCriterion("order_time between", value1, value2, "orderTime");
            return (Criteria) this;
        }

        public Criteria andOrderTimeNotBetween(Date value1, Date value2) {
            addCriterion("order_time not between", value1, value2, "orderTime");
            return (Criteria) this;
        }

        public Criteria andOrderFinishTimeIsNull() {
            addCriterion("order_finish_time is null");
            return (Criteria) this;
        }

        public Criteria andOrderFinishTimeIsNotNull() {
            addCriterion("order_finish_time is not null");
            return (Criteria) this;
        }

        public Criteria andOrderFinishTimeEqualTo(Date value) {
            addCriterion("order_finish_time =", value, "orderFinishTime");
            return (Criteria) this;
        }

        public Criteria andOrderFinishTimeEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_finish_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderFinishTimeNotEqualTo(Date value) {
            addCriterion("order_finish_time <>", value, "orderFinishTime");
            return (Criteria) this;
        }

        public Criteria andOrderFinishTimeNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_finish_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderFinishTimeGreaterThan(Date value) {
            addCriterion("order_finish_time >", value, "orderFinishTime");
            return (Criteria) this;
        }

        public Criteria andOrderFinishTimeGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_finish_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderFinishTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("order_finish_time >=", value, "orderFinishTime");
            return (Criteria) this;
        }

        public Criteria andOrderFinishTimeGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_finish_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderFinishTimeLessThan(Date value) {
            addCriterion("order_finish_time <", value, "orderFinishTime");
            return (Criteria) this;
        }

        public Criteria andOrderFinishTimeLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_finish_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderFinishTimeLessThanOrEqualTo(Date value) {
            addCriterion("order_finish_time <=", value, "orderFinishTime");
            return (Criteria) this;
        }

        public Criteria andOrderFinishTimeLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_finish_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderFinishTimeIn(List<Date> values) {
            addCriterion("order_finish_time in", values, "orderFinishTime");
            return (Criteria) this;
        }

        public Criteria andOrderFinishTimeNotIn(List<Date> values) {
            addCriterion("order_finish_time not in", values, "orderFinishTime");
            return (Criteria) this;
        }

        public Criteria andOrderFinishTimeBetween(Date value1, Date value2) {
            addCriterion("order_finish_time between", value1, value2, "orderFinishTime");
            return (Criteria) this;
        }

        public Criteria andOrderFinishTimeNotBetween(Date value1, Date value2) {
            addCriterion("order_finish_time not between", value1, value2, "orderFinishTime");
            return (Criteria) this;
        }

        public Criteria andSpuNameIsNull() {
            addCriterion("spu_name is null");
            return (Criteria) this;
        }

        public Criteria andSpuNameIsNotNull() {
            addCriterion("spu_name is not null");
            return (Criteria) this;
        }

        public Criteria andSpuNameEqualTo(String value) {
            addCriterion("spu_name =", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuNameNotEqualTo(String value) {
            addCriterion("spu_name <>", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuNameGreaterThan(String value) {
            addCriterion("spu_name >", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuNameGreaterThanOrEqualTo(String value) {
            addCriterion("spu_name >=", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuNameLessThan(String value) {
            addCriterion("spu_name <", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuNameLessThanOrEqualTo(String value) {
            addCriterion("spu_name <=", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuNameLike(String value) {
            addCriterion("spu_name like", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameNotLike(String value) {
            addCriterion("spu_name not like", value, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameIn(List<String> values) {
            addCriterion("spu_name in", values, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameNotIn(List<String> values) {
            addCriterion("spu_name not in", values, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameBetween(String value1, String value2) {
            addCriterion("spu_name between", value1, value2, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuNameNotBetween(String value1, String value2) {
            addCriterion("spu_name not between", value1, value2, "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNull() {
            addCriterion("spu_code is null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNotNull() {
            addCriterion("spu_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualTo(String value) {
            addCriterion("spu_code =", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualTo(String value) {
            addCriterion("spu_code <>", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThan(String value) {
            addCriterion("spu_code >", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("spu_code >=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThan(String value) {
            addCriterion("spu_code <", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualTo(String value) {
            addCriterion("spu_code <=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuCodeLike(String value) {
            addCriterion("spu_code like", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotLike(String value) {
            addCriterion("spu_code not like", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIn(List<String> values) {
            addCriterion("spu_code in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotIn(List<String> values) {
            addCriterion("spu_code not in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeBetween(String value1, String value2) {
            addCriterion("spu_code between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotBetween(String value1, String value2) {
            addCriterion("spu_code not between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuTypeIsNull() {
            addCriterion("spu_type is null");
            return (Criteria) this;
        }

        public Criteria andSpuTypeIsNotNull() {
            addCriterion("spu_type is not null");
            return (Criteria) this;
        }

        public Criteria andSpuTypeEqualTo(String value) {
            addCriterion("spu_type =", value, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuTypeNotEqualTo(String value) {
            addCriterion("spu_type <>", value, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuTypeGreaterThan(String value) {
            addCriterion("spu_type >", value, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuTypeGreaterThanOrEqualTo(String value) {
            addCriterion("spu_type >=", value, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuTypeLessThan(String value) {
            addCriterion("spu_type <", value, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuTypeLessThanOrEqualTo(String value) {
            addCriterion("spu_type <=", value, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuTypeLike(String value) {
            addCriterion("spu_type like", value, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeNotLike(String value) {
            addCriterion("spu_type not like", value, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeIn(List<String> values) {
            addCriterion("spu_type in", values, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeNotIn(List<String> values) {
            addCriterion("spu_type not in", values, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeBetween(String value1, String value2) {
            addCriterion("spu_type between", value1, value2, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuTypeNotBetween(String value1, String value2) {
            addCriterion("spu_type not between", value1, value2, "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuClassIsNull() {
            addCriterion("spu_class is null");
            return (Criteria) this;
        }

        public Criteria andSpuClassIsNotNull() {
            addCriterion("spu_class is not null");
            return (Criteria) this;
        }

        public Criteria andSpuClassEqualTo(String value) {
            addCriterion("spu_class =", value, "spuClass");
            return (Criteria) this;
        }

        public Criteria andSpuClassEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_class = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuClassNotEqualTo(String value) {
            addCriterion("spu_class <>", value, "spuClass");
            return (Criteria) this;
        }

        public Criteria andSpuClassNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_class <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuClassGreaterThan(String value) {
            addCriterion("spu_class >", value, "spuClass");
            return (Criteria) this;
        }

        public Criteria andSpuClassGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_class > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuClassGreaterThanOrEqualTo(String value) {
            addCriterion("spu_class >=", value, "spuClass");
            return (Criteria) this;
        }

        public Criteria andSpuClassGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_class >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuClassLessThan(String value) {
            addCriterion("spu_class <", value, "spuClass");
            return (Criteria) this;
        }

        public Criteria andSpuClassLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_class < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuClassLessThanOrEqualTo(String value) {
            addCriterion("spu_class <=", value, "spuClass");
            return (Criteria) this;
        }

        public Criteria andSpuClassLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("spu_class <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuClassLike(String value) {
            addCriterion("spu_class like", value, "spuClass");
            return (Criteria) this;
        }

        public Criteria andSpuClassNotLike(String value) {
            addCriterion("spu_class not like", value, "spuClass");
            return (Criteria) this;
        }

        public Criteria andSpuClassIn(List<String> values) {
            addCriterion("spu_class in", values, "spuClass");
            return (Criteria) this;
        }

        public Criteria andSpuClassNotIn(List<String> values) {
            addCriterion("spu_class not in", values, "spuClass");
            return (Criteria) this;
        }

        public Criteria andSpuClassBetween(String value1, String value2) {
            addCriterion("spu_class between", value1, value2, "spuClass");
            return (Criteria) this;
        }

        public Criteria andSpuClassNotBetween(String value1, String value2) {
            addCriterion("spu_class not between", value1, value2, "spuClass");
            return (Criteria) this;
        }

        public Criteria andSkuNameIsNull() {
            addCriterion("sku_name is null");
            return (Criteria) this;
        }

        public Criteria andSkuNameIsNotNull() {
            addCriterion("sku_name is not null");
            return (Criteria) this;
        }

        public Criteria andSkuNameEqualTo(String value) {
            addCriterion("sku_name =", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("sku_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuNameNotEqualTo(String value) {
            addCriterion("sku_name <>", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("sku_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuNameGreaterThan(String value) {
            addCriterion("sku_name >", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("sku_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuNameGreaterThanOrEqualTo(String value) {
            addCriterion("sku_name >=", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("sku_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuNameLessThan(String value) {
            addCriterion("sku_name <", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("sku_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuNameLessThanOrEqualTo(String value) {
            addCriterion("sku_name <=", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("sku_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuNameLike(String value) {
            addCriterion("sku_name like", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameNotLike(String value) {
            addCriterion("sku_name not like", value, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameIn(List<String> values) {
            addCriterion("sku_name in", values, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameNotIn(List<String> values) {
            addCriterion("sku_name not in", values, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameBetween(String value1, String value2) {
            addCriterion("sku_name between", value1, value2, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuNameNotBetween(String value1, String value2) {
            addCriterion("sku_name not between", value1, value2, "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNull() {
            addCriterion("sku_code is null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNotNull() {
            addCriterion("sku_code is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeEqualTo(String value) {
            addCriterion("sku_code =", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("sku_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotEqualTo(String value) {
            addCriterion("sku_code <>", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("sku_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThan(String value) {
            addCriterion("sku_code >", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("sku_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sku_code >=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("sku_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThan(String value) {
            addCriterion("sku_code <", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("sku_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanOrEqualTo(String value) {
            addCriterion("sku_code <=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("sku_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCodeLike(String value) {
            addCriterion("sku_code like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotLike(String value) {
            addCriterion("sku_code not like", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIn(List<String> values) {
            addCriterion("sku_code in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotIn(List<String> values) {
            addCriterion("sku_code not in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeBetween(String value1, String value2) {
            addCriterion("sku_code between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotBetween(String value1, String value2) {
            addCriterion("sku_code not between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andAtomNameIsNull() {
            addCriterion("atom_name is null");
            return (Criteria) this;
        }

        public Criteria andAtomNameIsNotNull() {
            addCriterion("atom_name is not null");
            return (Criteria) this;
        }

        public Criteria andAtomNameEqualTo(String value) {
            addCriterion("atom_name =", value, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("atom_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomNameNotEqualTo(String value) {
            addCriterion("atom_name <>", value, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("atom_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomNameGreaterThan(String value) {
            addCriterion("atom_name >", value, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("atom_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomNameGreaterThanOrEqualTo(String value) {
            addCriterion("atom_name >=", value, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("atom_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomNameLessThan(String value) {
            addCriterion("atom_name <", value, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("atom_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomNameLessThanOrEqualTo(String value) {
            addCriterion("atom_name <=", value, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("atom_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomNameLike(String value) {
            addCriterion("atom_name like", value, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameNotLike(String value) {
            addCriterion("atom_name not like", value, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameIn(List<String> values) {
            addCriterion("atom_name in", values, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameNotIn(List<String> values) {
            addCriterion("atom_name not in", values, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameBetween(String value1, String value2) {
            addCriterion("atom_name between", value1, value2, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomNameNotBetween(String value1, String value2) {
            addCriterion("atom_name not between", value1, value2, "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomCodeIsNull() {
            addCriterion("atom_code is null");
            return (Criteria) this;
        }

        public Criteria andAtomCodeIsNotNull() {
            addCriterion("atom_code is not null");
            return (Criteria) this;
        }

        public Criteria andAtomCodeEqualTo(String value) {
            addCriterion("atom_code =", value, "atomCode");
            return (Criteria) this;
        }

        public Criteria andAtomCodeEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("atom_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomCodeNotEqualTo(String value) {
            addCriterion("atom_code <>", value, "atomCode");
            return (Criteria) this;
        }

        public Criteria andAtomCodeNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("atom_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomCodeGreaterThan(String value) {
            addCriterion("atom_code >", value, "atomCode");
            return (Criteria) this;
        }

        public Criteria andAtomCodeGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("atom_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomCodeGreaterThanOrEqualTo(String value) {
            addCriterion("atom_code >=", value, "atomCode");
            return (Criteria) this;
        }

        public Criteria andAtomCodeGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("atom_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomCodeLessThan(String value) {
            addCriterion("atom_code <", value, "atomCode");
            return (Criteria) this;
        }

        public Criteria andAtomCodeLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("atom_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomCodeLessThanOrEqualTo(String value) {
            addCriterion("atom_code <=", value, "atomCode");
            return (Criteria) this;
        }

        public Criteria andAtomCodeLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("atom_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomCodeLike(String value) {
            addCriterion("atom_code like", value, "atomCode");
            return (Criteria) this;
        }

        public Criteria andAtomCodeNotLike(String value) {
            addCriterion("atom_code not like", value, "atomCode");
            return (Criteria) this;
        }

        public Criteria andAtomCodeIn(List<String> values) {
            addCriterion("atom_code in", values, "atomCode");
            return (Criteria) this;
        }

        public Criteria andAtomCodeNotIn(List<String> values) {
            addCriterion("atom_code not in", values, "atomCode");
            return (Criteria) this;
        }

        public Criteria andAtomCodeBetween(String value1, String value2) {
            addCriterion("atom_code between", value1, value2, "atomCode");
            return (Criteria) this;
        }

        public Criteria andAtomCodeNotBetween(String value1, String value2) {
            addCriterion("atom_code not between", value1, value2, "atomCode");
            return (Criteria) this;
        }

        public Criteria andUnitPriceIsNull() {
            addCriterion("unit_price is null");
            return (Criteria) this;
        }

        public Criteria andUnitPriceIsNotNull() {
            addCriterion("unit_price is not null");
            return (Criteria) this;
        }

        public Criteria andUnitPriceEqualTo(String value) {
            addCriterion("unit_price =", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("unit_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitPriceNotEqualTo(String value) {
            addCriterion("unit_price <>", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("unit_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitPriceGreaterThan(String value) {
            addCriterion("unit_price >", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("unit_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitPriceGreaterThanOrEqualTo(String value) {
            addCriterion("unit_price >=", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("unit_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitPriceLessThan(String value) {
            addCriterion("unit_price <", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("unit_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitPriceLessThanOrEqualTo(String value) {
            addCriterion("unit_price <=", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("unit_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitPriceLike(String value) {
            addCriterion("unit_price like", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceNotLike(String value) {
            addCriterion("unit_price not like", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceIn(List<String> values) {
            addCriterion("unit_price in", values, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceNotIn(List<String> values) {
            addCriterion("unit_price not in", values, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceBetween(String value1, String value2) {
            addCriterion("unit_price between", value1, value2, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceNotBetween(String value1, String value2) {
            addCriterion("unit_price not between", value1, value2, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andQuatityIsNull() {
            addCriterion("quatity is null");
            return (Criteria) this;
        }

        public Criteria andQuatityIsNotNull() {
            addCriterion("quatity is not null");
            return (Criteria) this;
        }

        public Criteria andQuatityEqualTo(Long value) {
            addCriterion("quatity =", value, "quatity");
            return (Criteria) this;
        }

        public Criteria andQuatityEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("quatity = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuatityNotEqualTo(Long value) {
            addCriterion("quatity <>", value, "quatity");
            return (Criteria) this;
        }

        public Criteria andQuatityNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("quatity <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuatityGreaterThan(Long value) {
            addCriterion("quatity >", value, "quatity");
            return (Criteria) this;
        }

        public Criteria andQuatityGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("quatity > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuatityGreaterThanOrEqualTo(Long value) {
            addCriterion("quatity >=", value, "quatity");
            return (Criteria) this;
        }

        public Criteria andQuatityGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("quatity >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuatityLessThan(Long value) {
            addCriterion("quatity <", value, "quatity");
            return (Criteria) this;
        }

        public Criteria andQuatityLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("quatity < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuatityLessThanOrEqualTo(Long value) {
            addCriterion("quatity <=", value, "quatity");
            return (Criteria) this;
        }

        public Criteria andQuatityLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("quatity <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andQuatityIn(List<Long> values) {
            addCriterion("quatity in", values, "quatity");
            return (Criteria) this;
        }

        public Criteria andQuatityNotIn(List<Long> values) {
            addCriterion("quatity not in", values, "quatity");
            return (Criteria) this;
        }

        public Criteria andQuatityBetween(Long value1, Long value2) {
            addCriterion("quatity between", value1, value2, "quatity");
            return (Criteria) this;
        }

        public Criteria andQuatityNotBetween(Long value1, Long value2) {
            addCriterion("quatity not between", value1, value2, "quatity");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNull() {
            addCriterion("total_price is null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceIsNotNull() {
            addCriterion("total_price is not null");
            return (Criteria) this;
        }

        public Criteria andTotalPriceEqualTo(Long value) {
            addCriterion("total_price =", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("total_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotEqualTo(Long value) {
            addCriterion("total_price <>", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("total_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThan(Long value) {
            addCriterion("total_price >", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("total_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThanOrEqualTo(Long value) {
            addCriterion("total_price >=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("total_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThan(Long value) {
            addCriterion("total_price <", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("total_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThanOrEqualTo(Long value) {
            addCriterion("total_price <=", value, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("total_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTotalPriceIn(List<Long> values) {
            addCriterion("total_price in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotIn(List<Long> values) {
            addCriterion("total_price not in", values, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceBetween(Long value1, Long value2) {
            addCriterion("total_price between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andTotalPriceNotBetween(Long value1, Long value2) {
            addCriterion("total_price not between", value1, value2, "totalPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceIsNull() {
            addCriterion("deduct_price is null");
            return (Criteria) this;
        }

        public Criteria andDeductPriceIsNotNull() {
            addCriterion("deduct_price is not null");
            return (Criteria) this;
        }

        public Criteria andDeductPriceEqualTo(String value) {
            addCriterion("deduct_price =", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("deduct_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeductPriceNotEqualTo(String value) {
            addCriterion("deduct_price <>", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("deduct_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeductPriceGreaterThan(String value) {
            addCriterion("deduct_price >", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("deduct_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeductPriceGreaterThanOrEqualTo(String value) {
            addCriterion("deduct_price >=", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("deduct_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeductPriceLessThan(String value) {
            addCriterion("deduct_price <", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("deduct_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeductPriceLessThanOrEqualTo(String value) {
            addCriterion("deduct_price <=", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("deduct_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeductPriceLike(String value) {
            addCriterion("deduct_price like", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceNotLike(String value) {
            addCriterion("deduct_price not like", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceIn(List<String> values) {
            addCriterion("deduct_price in", values, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceNotIn(List<String> values) {
            addCriterion("deduct_price not in", values, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceBetween(String value1, String value2) {
            addCriterion("deduct_price between", value1, value2, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceNotBetween(String value1, String value2) {
            addCriterion("deduct_price not between", value1, value2, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneIsNull() {
            addCriterion("receiver_phone is null");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneIsNotNull() {
            addCriterion("receiver_phone is not null");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneEqualTo(String value) {
            addCriterion("receiver_phone =", value, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("receiver_phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneNotEqualTo(String value) {
            addCriterion("receiver_phone <>", value, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("receiver_phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneGreaterThan(String value) {
            addCriterion("receiver_phone >", value, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("receiver_phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("receiver_phone >=", value, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("receiver_phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneLessThan(String value) {
            addCriterion("receiver_phone <", value, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("receiver_phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneLessThanOrEqualTo(String value) {
            addCriterion("receiver_phone <=", value, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("receiver_phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneLike(String value) {
            addCriterion("receiver_phone like", value, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneNotLike(String value) {
            addCriterion("receiver_phone not like", value, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneIn(List<String> values) {
            addCriterion("receiver_phone in", values, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneNotIn(List<String> values) {
            addCriterion("receiver_phone not in", values, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneBetween(String value1, String value2) {
            addCriterion("receiver_phone between", value1, value2, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneNotBetween(String value1, String value2) {
            addCriterion("receiver_phone not between", value1, value2, "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceIsNull() {
            addCriterion("order_province is null");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceIsNotNull() {
            addCriterion("order_province is not null");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceEqualTo(String value) {
            addCriterion("order_province =", value, "orderProvince");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_province = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNotEqualTo(String value) {
            addCriterion("order_province <>", value, "orderProvince");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_province <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceGreaterThan(String value) {
            addCriterion("order_province >", value, "orderProvince");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_province > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("order_province >=", value, "orderProvince");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_province >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceLessThan(String value) {
            addCriterion("order_province <", value, "orderProvince");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_province < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceLessThanOrEqualTo(String value) {
            addCriterion("order_province <=", value, "orderProvince");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_province <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceLike(String value) {
            addCriterion("order_province like", value, "orderProvince");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNotLike(String value) {
            addCriterion("order_province not like", value, "orderProvince");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceIn(List<String> values) {
            addCriterion("order_province in", values, "orderProvince");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNotIn(List<String> values) {
            addCriterion("order_province not in", values, "orderProvince");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceBetween(String value1, String value2) {
            addCriterion("order_province between", value1, value2, "orderProvince");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceNotBetween(String value1, String value2) {
            addCriterion("order_province not between", value1, value2, "orderProvince");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeIsNull() {
            addCriterion("order_province_code is null");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeIsNotNull() {
            addCriterion("order_province_code is not null");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeEqualTo(String value) {
            addCriterion("order_province_code =", value, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_province_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeNotEqualTo(String value) {
            addCriterion("order_province_code <>", value, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_province_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeGreaterThan(String value) {
            addCriterion("order_province_code >", value, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_province_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("order_province_code >=", value, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_province_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeLessThan(String value) {
            addCriterion("order_province_code <", value, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_province_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("order_province_code <=", value, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_province_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeLike(String value) {
            addCriterion("order_province_code like", value, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeNotLike(String value) {
            addCriterion("order_province_code not like", value, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeIn(List<String> values) {
            addCriterion("order_province_code in", values, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeNotIn(List<String> values) {
            addCriterion("order_province_code not in", values, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeBetween(String value1, String value2) {
            addCriterion("order_province_code between", value1, value2, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("order_province_code not between", value1, value2, "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityIsNull() {
            addCriterion("order_city is null");
            return (Criteria) this;
        }

        public Criteria andOrderCityIsNotNull() {
            addCriterion("order_city is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCityEqualTo(String value) {
            addCriterion("order_city =", value, "orderCity");
            return (Criteria) this;
        }

        public Criteria andOrderCityEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_city = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityNotEqualTo(String value) {
            addCriterion("order_city <>", value, "orderCity");
            return (Criteria) this;
        }

        public Criteria andOrderCityNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_city <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityGreaterThan(String value) {
            addCriterion("order_city >", value, "orderCity");
            return (Criteria) this;
        }

        public Criteria andOrderCityGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_city > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityGreaterThanOrEqualTo(String value) {
            addCriterion("order_city >=", value, "orderCity");
            return (Criteria) this;
        }

        public Criteria andOrderCityGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_city >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityLessThan(String value) {
            addCriterion("order_city <", value, "orderCity");
            return (Criteria) this;
        }

        public Criteria andOrderCityLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_city < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityLessThanOrEqualTo(String value) {
            addCriterion("order_city <=", value, "orderCity");
            return (Criteria) this;
        }

        public Criteria andOrderCityLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_city <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityLike(String value) {
            addCriterion("order_city like", value, "orderCity");
            return (Criteria) this;
        }

        public Criteria andOrderCityNotLike(String value) {
            addCriterion("order_city not like", value, "orderCity");
            return (Criteria) this;
        }

        public Criteria andOrderCityIn(List<String> values) {
            addCriterion("order_city in", values, "orderCity");
            return (Criteria) this;
        }

        public Criteria andOrderCityNotIn(List<String> values) {
            addCriterion("order_city not in", values, "orderCity");
            return (Criteria) this;
        }

        public Criteria andOrderCityBetween(String value1, String value2) {
            addCriterion("order_city between", value1, value2, "orderCity");
            return (Criteria) this;
        }

        public Criteria andOrderCityNotBetween(String value1, String value2) {
            addCriterion("order_city not between", value1, value2, "orderCity");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeIsNull() {
            addCriterion("order_city_code is null");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeIsNotNull() {
            addCriterion("order_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeEqualTo(String value) {
            addCriterion("order_city_code =", value, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_city_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeNotEqualTo(String value) {
            addCriterion("order_city_code <>", value, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_city_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeGreaterThan(String value) {
            addCriterion("order_city_code >", value, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_city_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("order_city_code >=", value, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_city_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeLessThan(String value) {
            addCriterion("order_city_code <", value, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_city_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeLessThanOrEqualTo(String value) {
            addCriterion("order_city_code <=", value, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("order_city_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeLike(String value) {
            addCriterion("order_city_code like", value, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeNotLike(String value) {
            addCriterion("order_city_code not like", value, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeIn(List<String> values) {
            addCriterion("order_city_code in", values, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeNotIn(List<String> values) {
            addCriterion("order_city_code not in", values, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeBetween(String value1, String value2) {
            addCriterion("order_city_code between", value1, value2, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeNotBetween(String value1, String value2) {
            addCriterion("order_city_code not between", value1, value2, "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeIsNull() {
            addCriterion("create_oper_code is null");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeIsNotNull() {
            addCriterion("create_oper_code is not null");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeEqualTo(String value) {
            addCriterion("create_oper_code =", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("create_oper_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeNotEqualTo(String value) {
            addCriterion("create_oper_code <>", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("create_oper_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeGreaterThan(String value) {
            addCriterion("create_oper_code >", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("create_oper_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeGreaterThanOrEqualTo(String value) {
            addCriterion("create_oper_code >=", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("create_oper_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeLessThan(String value) {
            addCriterion("create_oper_code <", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("create_oper_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeLessThanOrEqualTo(String value) {
            addCriterion("create_oper_code <=", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("create_oper_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeLike(String value) {
            addCriterion("create_oper_code like", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeNotLike(String value) {
            addCriterion("create_oper_code not like", value, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeIn(List<String> values) {
            addCriterion("create_oper_code in", values, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeNotIn(List<String> values) {
            addCriterion("create_oper_code not in", values, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeBetween(String value1, String value2) {
            addCriterion("create_oper_code between", value1, value2, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeNotBetween(String value1, String value2) {
            addCriterion("create_oper_code not between", value1, value2, "createOperCode");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumIsNull() {
            addCriterion("employee_num is null");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumIsNotNull() {
            addCriterion("employee_num is not null");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumEqualTo(String value) {
            addCriterion("employee_num =", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("employee_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeNumNotEqualTo(String value) {
            addCriterion("employee_num <>", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("employee_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeNumGreaterThan(String value) {
            addCriterion("employee_num >", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("employee_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeNumGreaterThanOrEqualTo(String value) {
            addCriterion("employee_num >=", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("employee_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeNumLessThan(String value) {
            addCriterion("employee_num <", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("employee_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeNumLessThanOrEqualTo(String value) {
            addCriterion("employee_num <=", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("employee_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmployeeNumLike(String value) {
            addCriterion("employee_num like", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumNotLike(String value) {
            addCriterion("employee_num not like", value, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumIn(List<String> values) {
            addCriterion("employee_num in", values, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumNotIn(List<String> values) {
            addCriterion("employee_num not in", values, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumBetween(String value1, String value2) {
            addCriterion("employee_num between", value1, value2, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumNotBetween(String value1, String value2) {
            addCriterion("employee_num not between", value1, value2, "employeeNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumIsNull() {
            addCriterion("material_num is null");
            return (Criteria) this;
        }

        public Criteria andMaterialNumIsNotNull() {
            addCriterion("material_num is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialNumEqualTo(String value) {
            addCriterion("material_num =", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumNotEqualTo(String value) {
            addCriterion("material_num <>", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumGreaterThan(String value) {
            addCriterion("material_num >", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumGreaterThanOrEqualTo(String value) {
            addCriterion("material_num >=", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumLessThan(String value) {
            addCriterion("material_num <", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumLessThanOrEqualTo(String value) {
            addCriterion("material_num <=", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNumLike(String value) {
            addCriterion("material_num like", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumNotLike(String value) {
            addCriterion("material_num not like", value, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumIn(List<String> values) {
            addCriterion("material_num in", values, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumNotIn(List<String> values) {
            addCriterion("material_num not in", values, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumBetween(String value1, String value2) {
            addCriterion("material_num between", value1, value2, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumNotBetween(String value1, String value2) {
            addCriterion("material_num not between", value1, value2, "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNameIsNull() {
            addCriterion("material_name is null");
            return (Criteria) this;
        }

        public Criteria andMaterialNameIsNotNull() {
            addCriterion("material_name is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialNameEqualTo(String value) {
            addCriterion("material_name =", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNameNotEqualTo(String value) {
            addCriterion("material_name <>", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNameGreaterThan(String value) {
            addCriterion("material_name >", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNameGreaterThanOrEqualTo(String value) {
            addCriterion("material_name >=", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNameLessThan(String value) {
            addCriterion("material_name <", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNameLessThanOrEqualTo(String value) {
            addCriterion("material_name <=", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialNameLike(String value) {
            addCriterion("material_name like", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameNotLike(String value) {
            addCriterion("material_name not like", value, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameIn(List<String> values) {
            addCriterion("material_name in", values, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameNotIn(List<String> values) {
            addCriterion("material_name not in", values, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameBetween(String value1, String value2) {
            addCriterion("material_name between", value1, value2, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialNameNotBetween(String value1, String value2) {
            addCriterion("material_name not between", value1, value2, "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptIsNull() {
            addCriterion("material_dept is null");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptIsNotNull() {
            addCriterion("material_dept is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptEqualTo(String value) {
            addCriterion("material_dept =", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_dept = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDeptNotEqualTo(String value) {
            addCriterion("material_dept <>", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_dept <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDeptGreaterThan(String value) {
            addCriterion("material_dept >", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_dept > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDeptGreaterThanOrEqualTo(String value) {
            addCriterion("material_dept >=", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_dept >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDeptLessThan(String value) {
            addCriterion("material_dept <", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_dept < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDeptLessThanOrEqualTo(String value) {
            addCriterion("material_dept <=", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_dept <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDeptLike(String value) {
            addCriterion("material_dept like", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptNotLike(String value) {
            addCriterion("material_dept not like", value, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptIn(List<String> values) {
            addCriterion("material_dept in", values, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptNotIn(List<String> values) {
            addCriterion("material_dept not in", values, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptBetween(String value1, String value2) {
            addCriterion("material_dept between", value1, value2, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptNotBetween(String value1, String value2) {
            addCriterion("material_dept not between", value1, value2, "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitIsNull() {
            addCriterion("material_unit is null");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitIsNotNull() {
            addCriterion("material_unit is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitEqualTo(String value) {
            addCriterion("material_unit =", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_unit = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialUnitNotEqualTo(String value) {
            addCriterion("material_unit <>", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_unit <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialUnitGreaterThan(String value) {
            addCriterion("material_unit >", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_unit > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialUnitGreaterThanOrEqualTo(String value) {
            addCriterion("material_unit >=", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_unit >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialUnitLessThan(String value) {
            addCriterion("material_unit <", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_unit < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialUnitLessThanOrEqualTo(String value) {
            addCriterion("material_unit <=", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("material_unit <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialUnitLike(String value) {
            addCriterion("material_unit like", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitNotLike(String value) {
            addCriterion("material_unit not like", value, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitIn(List<String> values) {
            addCriterion("material_unit in", values, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitNotIn(List<String> values) {
            addCriterion("material_unit not in", values, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitBetween(String value1, String value2) {
            addCriterion("material_unit between", value1, value2, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitNotBetween(String value1, String value2) {
            addCriterion("material_unit not between", value1, value2, "materialUnit");
            return (Criteria) this;
        }

        public Criteria andContractNumIsNull() {
            addCriterion("contract_num is null");
            return (Criteria) this;
        }

        public Criteria andContractNumIsNotNull() {
            addCriterion("contract_num is not null");
            return (Criteria) this;
        }

        public Criteria andContractNumEqualTo(String value) {
            addCriterion("contract_num =", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumNotEqualTo(String value) {
            addCriterion("contract_num <>", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumGreaterThan(String value) {
            addCriterion("contract_num >", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumGreaterThanOrEqualTo(String value) {
            addCriterion("contract_num >=", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumLessThan(String value) {
            addCriterion("contract_num <", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumLessThanOrEqualTo(String value) {
            addCriterion("contract_num <=", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumLike(String value) {
            addCriterion("contract_num like", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumNotLike(String value) {
            addCriterion("contract_num not like", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumIn(List<String> values) {
            addCriterion("contract_num in", values, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumNotIn(List<String> values) {
            addCriterion("contract_num not in", values, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumBetween(String value1, String value2) {
            addCriterion("contract_num between", value1, value2, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumNotBetween(String value1, String value2) {
            addCriterion("contract_num not between", value1, value2, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNameIsNull() {
            addCriterion("contract_name is null");
            return (Criteria) this;
        }

        public Criteria andContractNameIsNotNull() {
            addCriterion("contract_name is not null");
            return (Criteria) this;
        }

        public Criteria andContractNameEqualTo(String value) {
            addCriterion("contract_name =", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNameNotEqualTo(String value) {
            addCriterion("contract_name <>", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNameGreaterThan(String value) {
            addCriterion("contract_name >", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNameGreaterThanOrEqualTo(String value) {
            addCriterion("contract_name >=", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNameLessThan(String value) {
            addCriterion("contract_name <", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNameLessThanOrEqualTo(String value) {
            addCriterion("contract_name <=", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNameLike(String value) {
            addCriterion("contract_name like", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotLike(String value) {
            addCriterion("contract_name not like", value, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameIn(List<String> values) {
            addCriterion("contract_name in", values, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotIn(List<String> values) {
            addCriterion("contract_name not in", values, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameBetween(String value1, String value2) {
            addCriterion("contract_name between", value1, value2, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractNameNotBetween(String value1, String value2) {
            addCriterion("contract_name not between", value1, value2, "contractName");
            return (Criteria) this;
        }

        public Criteria andContractDeptIsNull() {
            addCriterion("contract_dept is null");
            return (Criteria) this;
        }

        public Criteria andContractDeptIsNotNull() {
            addCriterion("contract_dept is not null");
            return (Criteria) this;
        }

        public Criteria andContractDeptEqualTo(String value) {
            addCriterion("contract_dept =", value, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_dept = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractDeptNotEqualTo(String value) {
            addCriterion("contract_dept <>", value, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_dept <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractDeptGreaterThan(String value) {
            addCriterion("contract_dept >", value, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_dept > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractDeptGreaterThanOrEqualTo(String value) {
            addCriterion("contract_dept >=", value, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_dept >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractDeptLessThan(String value) {
            addCriterion("contract_dept <", value, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_dept < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractDeptLessThanOrEqualTo(String value) {
            addCriterion("contract_dept <=", value, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_dept <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractDeptLike(String value) {
            addCriterion("contract_dept like", value, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptNotLike(String value) {
            addCriterion("contract_dept not like", value, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptIn(List<String> values) {
            addCriterion("contract_dept in", values, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptNotIn(List<String> values) {
            addCriterion("contract_dept not in", values, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptBetween(String value1, String value2) {
            addCriterion("contract_dept between", value1, value2, "contractDept");
            return (Criteria) this;
        }

        public Criteria andContractDeptNotBetween(String value1, String value2) {
            addCriterion("contract_dept not between", value1, value2, "contractDept");
            return (Criteria) this;
        }

        public Criteria andCustomCodeIsNull() {
            addCriterion("custom_code is null");
            return (Criteria) this;
        }

        public Criteria andCustomCodeIsNotNull() {
            addCriterion("custom_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustomCodeEqualTo(String value) {
            addCriterion("custom_code =", value, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("custom_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomCodeNotEqualTo(String value) {
            addCriterion("custom_code <>", value, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("custom_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomCodeGreaterThan(String value) {
            addCriterion("custom_code >", value, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("custom_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomCodeGreaterThanOrEqualTo(String value) {
            addCriterion("custom_code >=", value, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("custom_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomCodeLessThan(String value) {
            addCriterion("custom_code <", value, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("custom_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomCodeLessThanOrEqualTo(String value) {
            addCriterion("custom_code <=", value, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("custom_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCustomCodeLike(String value) {
            addCriterion("custom_code like", value, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeNotLike(String value) {
            addCriterion("custom_code not like", value, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeIn(List<String> values) {
            addCriterion("custom_code in", values, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeNotIn(List<String> values) {
            addCriterion("custom_code not in", values, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeBetween(String value1, String value2) {
            addCriterion("custom_code between", value1, value2, "customCode");
            return (Criteria) this;
        }

        public Criteria andCustomCodeNotBetween(String value1, String value2) {
            addCriterion("custom_code not between", value1, value2, "customCode");
            return (Criteria) this;
        }

        public Criteria andContractSellUnitIsNull() {
            addCriterion("contract_sell_unit is null");
            return (Criteria) this;
        }

        public Criteria andContractSellUnitIsNotNull() {
            addCriterion("contract_sell_unit is not null");
            return (Criteria) this;
        }

        public Criteria andContractSellUnitEqualTo(String value) {
            addCriterion("contract_sell_unit =", value, "contractSellUnit");
            return (Criteria) this;
        }

        public Criteria andContractSellUnitEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_sell_unit = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSellUnitNotEqualTo(String value) {
            addCriterion("contract_sell_unit <>", value, "contractSellUnit");
            return (Criteria) this;
        }

        public Criteria andContractSellUnitNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_sell_unit <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSellUnitGreaterThan(String value) {
            addCriterion("contract_sell_unit >", value, "contractSellUnit");
            return (Criteria) this;
        }

        public Criteria andContractSellUnitGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_sell_unit > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSellUnitGreaterThanOrEqualTo(String value) {
            addCriterion("contract_sell_unit >=", value, "contractSellUnit");
            return (Criteria) this;
        }

        public Criteria andContractSellUnitGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_sell_unit >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSellUnitLessThan(String value) {
            addCriterion("contract_sell_unit <", value, "contractSellUnit");
            return (Criteria) this;
        }

        public Criteria andContractSellUnitLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_sell_unit < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSellUnitLessThanOrEqualTo(String value) {
            addCriterion("contract_sell_unit <=", value, "contractSellUnit");
            return (Criteria) this;
        }

        public Criteria andContractSellUnitLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_sell_unit <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSellUnitLike(String value) {
            addCriterion("contract_sell_unit like", value, "contractSellUnit");
            return (Criteria) this;
        }

        public Criteria andContractSellUnitNotLike(String value) {
            addCriterion("contract_sell_unit not like", value, "contractSellUnit");
            return (Criteria) this;
        }

        public Criteria andContractSellUnitIn(List<String> values) {
            addCriterion("contract_sell_unit in", values, "contractSellUnit");
            return (Criteria) this;
        }

        public Criteria andContractSellUnitNotIn(List<String> values) {
            addCriterion("contract_sell_unit not in", values, "contractSellUnit");
            return (Criteria) this;
        }

        public Criteria andContractSellUnitBetween(String value1, String value2) {
            addCriterion("contract_sell_unit between", value1, value2, "contractSellUnit");
            return (Criteria) this;
        }

        public Criteria andContractSellUnitNotBetween(String value1, String value2) {
            addCriterion("contract_sell_unit not between", value1, value2, "contractSellUnit");
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitIsNull() {
            addCriterion("contract_money_unit is null");
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitIsNotNull() {
            addCriterion("contract_money_unit is not null");
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitEqualTo(String value) {
            addCriterion("contract_money_unit =", value, "contractMoneyUnit");
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_money_unit = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitNotEqualTo(String value) {
            addCriterion("contract_money_unit <>", value, "contractMoneyUnit");
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_money_unit <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitGreaterThan(String value) {
            addCriterion("contract_money_unit >", value, "contractMoneyUnit");
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_money_unit > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitGreaterThanOrEqualTo(String value) {
            addCriterion("contract_money_unit >=", value, "contractMoneyUnit");
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_money_unit >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitLessThan(String value) {
            addCriterion("contract_money_unit <", value, "contractMoneyUnit");
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_money_unit < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitLessThanOrEqualTo(String value) {
            addCriterion("contract_money_unit <=", value, "contractMoneyUnit");
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_money_unit <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitLike(String value) {
            addCriterion("contract_money_unit like", value, "contractMoneyUnit");
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitNotLike(String value) {
            addCriterion("contract_money_unit not like", value, "contractMoneyUnit");
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitIn(List<String> values) {
            addCriterion("contract_money_unit in", values, "contractMoneyUnit");
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitNotIn(List<String> values) {
            addCriterion("contract_money_unit not in", values, "contractMoneyUnit");
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitBetween(String value1, String value2) {
            addCriterion("contract_money_unit between", value1, value2, "contractMoneyUnit");
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitNotBetween(String value1, String value2) {
            addCriterion("contract_money_unit not between", value1, value2, "contractMoneyUnit");
            return (Criteria) this;
        }

        public Criteria andContractTypeIsNull() {
            addCriterion("contract_type is null");
            return (Criteria) this;
        }

        public Criteria andContractTypeIsNotNull() {
            addCriterion("contract_type is not null");
            return (Criteria) this;
        }

        public Criteria andContractTypeEqualTo(String value) {
            addCriterion("contract_type =", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeNotEqualTo(String value) {
            addCriterion("contract_type <>", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThan(String value) {
            addCriterion("contract_type >", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanOrEqualTo(String value) {
            addCriterion("contract_type >=", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThan(String value) {
            addCriterion("contract_type <", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanOrEqualTo(String value) {
            addCriterion("contract_type <=", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTypeLike(String value) {
            addCriterion("contract_type like", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotLike(String value) {
            addCriterion("contract_type not like", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeIn(List<String> values) {
            addCriterion("contract_type in", values, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotIn(List<String> values) {
            addCriterion("contract_type not in", values, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeBetween(String value1, String value2) {
            addCriterion("contract_type between", value1, value2, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotBetween(String value1, String value2) {
            addCriterion("contract_type not between", value1, value2, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractProjectIsNull() {
            addCriterion("contract_project is null");
            return (Criteria) this;
        }

        public Criteria andContractProjectIsNotNull() {
            addCriterion("contract_project is not null");
            return (Criteria) this;
        }

        public Criteria andContractProjectEqualTo(String value) {
            addCriterion("contract_project =", value, "contractProject");
            return (Criteria) this;
        }

        public Criteria andContractProjectEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_project = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractProjectNotEqualTo(String value) {
            addCriterion("contract_project <>", value, "contractProject");
            return (Criteria) this;
        }

        public Criteria andContractProjectNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_project <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractProjectGreaterThan(String value) {
            addCriterion("contract_project >", value, "contractProject");
            return (Criteria) this;
        }

        public Criteria andContractProjectGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_project > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractProjectGreaterThanOrEqualTo(String value) {
            addCriterion("contract_project >=", value, "contractProject");
            return (Criteria) this;
        }

        public Criteria andContractProjectGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_project >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractProjectLessThan(String value) {
            addCriterion("contract_project <", value, "contractProject");
            return (Criteria) this;
        }

        public Criteria andContractProjectLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_project < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractProjectLessThanOrEqualTo(String value) {
            addCriterion("contract_project <=", value, "contractProject");
            return (Criteria) this;
        }

        public Criteria andContractProjectLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_project <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractProjectLike(String value) {
            addCriterion("contract_project like", value, "contractProject");
            return (Criteria) this;
        }

        public Criteria andContractProjectNotLike(String value) {
            addCriterion("contract_project not like", value, "contractProject");
            return (Criteria) this;
        }

        public Criteria andContractProjectIn(List<String> values) {
            addCriterion("contract_project in", values, "contractProject");
            return (Criteria) this;
        }

        public Criteria andContractProjectNotIn(List<String> values) {
            addCriterion("contract_project not in", values, "contractProject");
            return (Criteria) this;
        }

        public Criteria andContractProjectBetween(String value1, String value2) {
            addCriterion("contract_project between", value1, value2, "contractProject");
            return (Criteria) this;
        }

        public Criteria andContractProjectNotBetween(String value1, String value2) {
            addCriterion("contract_project not between", value1, value2, "contractProject");
            return (Criteria) this;
        }

        public Criteria andContractSubProjectIsNull() {
            addCriterion("contract_sub_project is null");
            return (Criteria) this;
        }

        public Criteria andContractSubProjectIsNotNull() {
            addCriterion("contract_sub_project is not null");
            return (Criteria) this;
        }

        public Criteria andContractSubProjectEqualTo(String value) {
            addCriterion("contract_sub_project =", value, "contractSubProject");
            return (Criteria) this;
        }

        public Criteria andContractSubProjectEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_sub_project = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSubProjectNotEqualTo(String value) {
            addCriterion("contract_sub_project <>", value, "contractSubProject");
            return (Criteria) this;
        }

        public Criteria andContractSubProjectNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_sub_project <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSubProjectGreaterThan(String value) {
            addCriterion("contract_sub_project >", value, "contractSubProject");
            return (Criteria) this;
        }

        public Criteria andContractSubProjectGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_sub_project > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSubProjectGreaterThanOrEqualTo(String value) {
            addCriterion("contract_sub_project >=", value, "contractSubProject");
            return (Criteria) this;
        }

        public Criteria andContractSubProjectGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_sub_project >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSubProjectLessThan(String value) {
            addCriterion("contract_sub_project <", value, "contractSubProject");
            return (Criteria) this;
        }

        public Criteria andContractSubProjectLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_sub_project < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSubProjectLessThanOrEqualTo(String value) {
            addCriterion("contract_sub_project <=", value, "contractSubProject");
            return (Criteria) this;
        }

        public Criteria andContractSubProjectLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_sub_project <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSubProjectLike(String value) {
            addCriterion("contract_sub_project like", value, "contractSubProject");
            return (Criteria) this;
        }

        public Criteria andContractSubProjectNotLike(String value) {
            addCriterion("contract_sub_project not like", value, "contractSubProject");
            return (Criteria) this;
        }

        public Criteria andContractSubProjectIn(List<String> values) {
            addCriterion("contract_sub_project in", values, "contractSubProject");
            return (Criteria) this;
        }

        public Criteria andContractSubProjectNotIn(List<String> values) {
            addCriterion("contract_sub_project not in", values, "contractSubProject");
            return (Criteria) this;
        }

        public Criteria andContractSubProjectBetween(String value1, String value2) {
            addCriterion("contract_sub_project between", value1, value2, "contractSubProject");
            return (Criteria) this;
        }

        public Criteria andContractSubProjectNotBetween(String value1, String value2) {
            addCriterion("contract_sub_project not between", value1, value2, "contractSubProject");
            return (Criteria) this;
        }

        public Criteria andContractTaxIsNull() {
            addCriterion("contract_tax is null");
            return (Criteria) this;
        }

        public Criteria andContractTaxIsNotNull() {
            addCriterion("contract_tax is not null");
            return (Criteria) this;
        }

        public Criteria andContractTaxEqualTo(String value) {
            addCriterion("contract_tax =", value, "contractTax");
            return (Criteria) this;
        }

        public Criteria andContractTaxEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_tax = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTaxNotEqualTo(String value) {
            addCriterion("contract_tax <>", value, "contractTax");
            return (Criteria) this;
        }

        public Criteria andContractTaxNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_tax <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTaxGreaterThan(String value) {
            addCriterion("contract_tax >", value, "contractTax");
            return (Criteria) this;
        }

        public Criteria andContractTaxGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_tax > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTaxGreaterThanOrEqualTo(String value) {
            addCriterion("contract_tax >=", value, "contractTax");
            return (Criteria) this;
        }

        public Criteria andContractTaxGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_tax >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTaxLessThan(String value) {
            addCriterion("contract_tax <", value, "contractTax");
            return (Criteria) this;
        }

        public Criteria andContractTaxLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_tax < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTaxLessThanOrEqualTo(String value) {
            addCriterion("contract_tax <=", value, "contractTax");
            return (Criteria) this;
        }

        public Criteria andContractTaxLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_tax <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractTaxLike(String value) {
            addCriterion("contract_tax like", value, "contractTax");
            return (Criteria) this;
        }

        public Criteria andContractTaxNotLike(String value) {
            addCriterion("contract_tax not like", value, "contractTax");
            return (Criteria) this;
        }

        public Criteria andContractTaxIn(List<String> values) {
            addCriterion("contract_tax in", values, "contractTax");
            return (Criteria) this;
        }

        public Criteria andContractTaxNotIn(List<String> values) {
            addCriterion("contract_tax not in", values, "contractTax");
            return (Criteria) this;
        }

        public Criteria andContractTaxBetween(String value1, String value2) {
            addCriterion("contract_tax between", value1, value2, "contractTax");
            return (Criteria) this;
        }

        public Criteria andContractTaxNotBetween(String value1, String value2) {
            addCriterion("contract_tax not between", value1, value2, "contractTax");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeIsNull() {
            addCriterion("contract_settle_mode is null");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeIsNotNull() {
            addCriterion("contract_settle_mode is not null");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeEqualTo(Integer value) {
            addCriterion("contract_settle_mode =", value, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_settle_mode = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSettleModeNotEqualTo(Integer value) {
            addCriterion("contract_settle_mode <>", value, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_settle_mode <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSettleModeGreaterThan(Integer value) {
            addCriterion("contract_settle_mode >", value, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_settle_mode > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSettleModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("contract_settle_mode >=", value, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_settle_mode >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSettleModeLessThan(Integer value) {
            addCriterion("contract_settle_mode <", value, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_settle_mode < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSettleModeLessThanOrEqualTo(Integer value) {
            addCriterion("contract_settle_mode <=", value, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_settle_mode <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSettleModeIn(List<Integer> values) {
            addCriterion("contract_settle_mode in", values, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeNotIn(List<Integer> values) {
            addCriterion("contract_settle_mode not in", values, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeBetween(Integer value1, Integer value2) {
            addCriterion("contract_settle_mode between", value1, value2, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andContractSettleModeNotBetween(Integer value1, Integer value2) {
            addCriterion("contract_settle_mode not between", value1, value2, "contractSettleMode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceIsNull() {
            addCriterion("buyer_province is null");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceIsNotNull() {
            addCriterion("buyer_province is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceEqualTo(String value) {
            addCriterion("buyer_province =", value, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_province = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceNotEqualTo(String value) {
            addCriterion("buyer_province <>", value, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_province <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceGreaterThan(String value) {
            addCriterion("buyer_province >", value, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_province > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("buyer_province >=", value, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_province >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceLessThan(String value) {
            addCriterion("buyer_province <", value, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_province < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceLessThanOrEqualTo(String value) {
            addCriterion("buyer_province <=", value, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_province <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceLike(String value) {
            addCriterion("buyer_province like", value, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceNotLike(String value) {
            addCriterion("buyer_province not like", value, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceIn(List<String> values) {
            addCriterion("buyer_province in", values, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceNotIn(List<String> values) {
            addCriterion("buyer_province not in", values, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceBetween(String value1, String value2) {
            addCriterion("buyer_province between", value1, value2, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceNotBetween(String value1, String value2) {
            addCriterion("buyer_province not between", value1, value2, "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeIsNull() {
            addCriterion("buyer_province_mall_code is null");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeIsNotNull() {
            addCriterion("buyer_province_mall_code is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeEqualTo(String value) {
            addCriterion("buyer_province_mall_code =", value, "buyerProvinceMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_province_mall_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeNotEqualTo(String value) {
            addCriterion("buyer_province_mall_code <>", value, "buyerProvinceMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_province_mall_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeGreaterThan(String value) {
            addCriterion("buyer_province_mall_code >", value, "buyerProvinceMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_province_mall_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeGreaterThanOrEqualTo(String value) {
            addCriterion("buyer_province_mall_code >=", value, "buyerProvinceMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_province_mall_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeLessThan(String value) {
            addCriterion("buyer_province_mall_code <", value, "buyerProvinceMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_province_mall_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeLessThanOrEqualTo(String value) {
            addCriterion("buyer_province_mall_code <=", value, "buyerProvinceMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_province_mall_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeLike(String value) {
            addCriterion("buyer_province_mall_code like", value, "buyerProvinceMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeNotLike(String value) {
            addCriterion("buyer_province_mall_code not like", value, "buyerProvinceMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeIn(List<String> values) {
            addCriterion("buyer_province_mall_code in", values, "buyerProvinceMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeNotIn(List<String> values) {
            addCriterion("buyer_province_mall_code not in", values, "buyerProvinceMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeBetween(String value1, String value2) {
            addCriterion("buyer_province_mall_code between", value1, value2, "buyerProvinceMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeNotBetween(String value1, String value2) {
            addCriterion("buyer_province_mall_code not between", value1, value2, "buyerProvinceMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeIsNull() {
            addCriterion("buyer_province_code is null");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeIsNotNull() {
            addCriterion("buyer_province_code is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeEqualTo(String value) {
            addCriterion("buyer_province_code =", value, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_province_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeNotEqualTo(String value) {
            addCriterion("buyer_province_code <>", value, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_province_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeGreaterThan(String value) {
            addCriterion("buyer_province_code >", value, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_province_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("buyer_province_code >=", value, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_province_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeLessThan(String value) {
            addCriterion("buyer_province_code <", value, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_province_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("buyer_province_code <=", value, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_province_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeLike(String value) {
            addCriterion("buyer_province_code like", value, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeNotLike(String value) {
            addCriterion("buyer_province_code not like", value, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeIn(List<String> values) {
            addCriterion("buyer_province_code in", values, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeNotIn(List<String> values) {
            addCriterion("buyer_province_code not in", values, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeBetween(String value1, String value2) {
            addCriterion("buyer_province_code between", value1, value2, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("buyer_province_code not between", value1, value2, "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityIsNull() {
            addCriterion("buyer_city is null");
            return (Criteria) this;
        }

        public Criteria andBuyerCityIsNotNull() {
            addCriterion("buyer_city is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerCityEqualTo(String value) {
            addCriterion("buyer_city =", value, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_city = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityNotEqualTo(String value) {
            addCriterion("buyer_city <>", value, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_city <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityGreaterThan(String value) {
            addCriterion("buyer_city >", value, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_city > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityGreaterThanOrEqualTo(String value) {
            addCriterion("buyer_city >=", value, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_city >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityLessThan(String value) {
            addCriterion("buyer_city <", value, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_city < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityLessThanOrEqualTo(String value) {
            addCriterion("buyer_city <=", value, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_city <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityLike(String value) {
            addCriterion("buyer_city like", value, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityNotLike(String value) {
            addCriterion("buyer_city not like", value, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityIn(List<String> values) {
            addCriterion("buyer_city in", values, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityNotIn(List<String> values) {
            addCriterion("buyer_city not in", values, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityBetween(String value1, String value2) {
            addCriterion("buyer_city between", value1, value2, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityNotBetween(String value1, String value2) {
            addCriterion("buyer_city not between", value1, value2, "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeIsNull() {
            addCriterion("buyer_city_mall_code is null");
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeIsNotNull() {
            addCriterion("buyer_city_mall_code is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeEqualTo(String value) {
            addCriterion("buyer_city_mall_code =", value, "buyerCityMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_city_mall_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeNotEqualTo(String value) {
            addCriterion("buyer_city_mall_code <>", value, "buyerCityMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_city_mall_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeGreaterThan(String value) {
            addCriterion("buyer_city_mall_code >", value, "buyerCityMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_city_mall_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeGreaterThanOrEqualTo(String value) {
            addCriterion("buyer_city_mall_code >=", value, "buyerCityMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_city_mall_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeLessThan(String value) {
            addCriterion("buyer_city_mall_code <", value, "buyerCityMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_city_mall_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeLessThanOrEqualTo(String value) {
            addCriterion("buyer_city_mall_code <=", value, "buyerCityMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_city_mall_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeLike(String value) {
            addCriterion("buyer_city_mall_code like", value, "buyerCityMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeNotLike(String value) {
            addCriterion("buyer_city_mall_code not like", value, "buyerCityMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeIn(List<String> values) {
            addCriterion("buyer_city_mall_code in", values, "buyerCityMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeNotIn(List<String> values) {
            addCriterion("buyer_city_mall_code not in", values, "buyerCityMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeBetween(String value1, String value2) {
            addCriterion("buyer_city_mall_code between", value1, value2, "buyerCityMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeNotBetween(String value1, String value2) {
            addCriterion("buyer_city_mall_code not between", value1, value2, "buyerCityMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeIsNull() {
            addCriterion("buyer_city_code is null");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeIsNotNull() {
            addCriterion("buyer_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeEqualTo(String value) {
            addCriterion("buyer_city_code =", value, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_city_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeNotEqualTo(String value) {
            addCriterion("buyer_city_code <>", value, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_city_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeGreaterThan(String value) {
            addCriterion("buyer_city_code >", value, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_city_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("buyer_city_code >=", value, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_city_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeLessThan(String value) {
            addCriterion("buyer_city_code <", value, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_city_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeLessThanOrEqualTo(String value) {
            addCriterion("buyer_city_code <=", value, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("buyer_city_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeLike(String value) {
            addCriterion("buyer_city_code like", value, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeNotLike(String value) {
            addCriterion("buyer_city_code not like", value, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeIn(List<String> values) {
            addCriterion("buyer_city_code in", values, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeNotIn(List<String> values) {
            addCriterion("buyer_city_code not in", values, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeBetween(String value1, String value2) {
            addCriterion("buyer_city_code between", value1, value2, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeNotBetween(String value1, String value2) {
            addCriterion("buyer_city_code not between", value1, value2, "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeIsNull() {
            addCriterion("contract_satis_type is null");
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeIsNotNull() {
            addCriterion("contract_satis_type is not null");
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeEqualTo(String value) {
            addCriterion("contract_satis_type =", value, "contractSatisType");
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_satis_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeNotEqualTo(String value) {
            addCriterion("contract_satis_type <>", value, "contractSatisType");
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_satis_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeGreaterThan(String value) {
            addCriterion("contract_satis_type >", value, "contractSatisType");
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_satis_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeGreaterThanOrEqualTo(String value) {
            addCriterion("contract_satis_type >=", value, "contractSatisType");
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_satis_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeLessThan(String value) {
            addCriterion("contract_satis_type <", value, "contractSatisType");
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_satis_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeLessThanOrEqualTo(String value) {
            addCriterion("contract_satis_type <=", value, "contractSatisType");
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_satis_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeLike(String value) {
            addCriterion("contract_satis_type like", value, "contractSatisType");
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeNotLike(String value) {
            addCriterion("contract_satis_type not like", value, "contractSatisType");
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeIn(List<String> values) {
            addCriterion("contract_satis_type in", values, "contractSatisType");
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeNotIn(List<String> values) {
            addCriterion("contract_satis_type not in", values, "contractSatisType");
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeBetween(String value1, String value2) {
            addCriterion("contract_satis_type between", value1, value2, "contractSatisType");
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeNotBetween(String value1, String value2) {
            addCriterion("contract_satis_type not between", value1, value2, "contractSatisType");
            return (Criteria) this;
        }

        public Criteria andContractStatisEnumIsNull() {
            addCriterion("contract_statis_enum is null");
            return (Criteria) this;
        }

        public Criteria andContractStatisEnumIsNotNull() {
            addCriterion("contract_statis_enum is not null");
            return (Criteria) this;
        }

        public Criteria andContractStatisEnumEqualTo(Integer value) {
            addCriterion("contract_statis_enum =", value, "contractStatisEnum");
            return (Criteria) this;
        }

        public Criteria andContractStatisEnumEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_statis_enum = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractStatisEnumNotEqualTo(Integer value) {
            addCriterion("contract_statis_enum <>", value, "contractStatisEnum");
            return (Criteria) this;
        }

        public Criteria andContractStatisEnumNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_statis_enum <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractStatisEnumGreaterThan(Integer value) {
            addCriterion("contract_statis_enum >", value, "contractStatisEnum");
            return (Criteria) this;
        }

        public Criteria andContractStatisEnumGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_statis_enum > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractStatisEnumGreaterThanOrEqualTo(Integer value) {
            addCriterion("contract_statis_enum >=", value, "contractStatisEnum");
            return (Criteria) this;
        }

        public Criteria andContractStatisEnumGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_statis_enum >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractStatisEnumLessThan(Integer value) {
            addCriterion("contract_statis_enum <", value, "contractStatisEnum");
            return (Criteria) this;
        }

        public Criteria andContractStatisEnumLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_statis_enum < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractStatisEnumLessThanOrEqualTo(Integer value) {
            addCriterion("contract_statis_enum <=", value, "contractStatisEnum");
            return (Criteria) this;
        }

        public Criteria andContractStatisEnumLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("contract_statis_enum <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractStatisEnumIn(List<Integer> values) {
            addCriterion("contract_statis_enum in", values, "contractStatisEnum");
            return (Criteria) this;
        }

        public Criteria andContractStatisEnumNotIn(List<Integer> values) {
            addCriterion("contract_statis_enum not in", values, "contractStatisEnum");
            return (Criteria) this;
        }

        public Criteria andContractStatisEnumBetween(Integer value1, Integer value2) {
            addCriterion("contract_statis_enum between", value1, value2, "contractStatisEnum");
            return (Criteria) this;
        }

        public Criteria andContractStatisEnumNotBetween(Integer value1, Integer value2) {
            addCriterion("contract_statis_enum not between", value1, value2, "contractStatisEnum");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andK3NumIsNull() {
            addCriterion("k3_num is null");
            return (Criteria) this;
        }

        public Criteria andK3NumIsNotNull() {
            addCriterion("k3_num is not null");
            return (Criteria) this;
        }

        public Criteria andK3NumEqualTo(String value) {
            addCriterion("k3_num =", value, "k3Num");
            return (Criteria) this;
        }

        public Criteria andK3NumEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("k3_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3NumNotEqualTo(String value) {
            addCriterion("k3_num <>", value, "k3Num");
            return (Criteria) this;
        }

        public Criteria andK3NumNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("k3_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3NumGreaterThan(String value) {
            addCriterion("k3_num >", value, "k3Num");
            return (Criteria) this;
        }

        public Criteria andK3NumGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("k3_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3NumGreaterThanOrEqualTo(String value) {
            addCriterion("k3_num >=", value, "k3Num");
            return (Criteria) this;
        }

        public Criteria andK3NumGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("k3_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3NumLessThan(String value) {
            addCriterion("k3_num <", value, "k3Num");
            return (Criteria) this;
        }

        public Criteria andK3NumLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("k3_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3NumLessThanOrEqualTo(String value) {
            addCriterion("k3_num <=", value, "k3Num");
            return (Criteria) this;
        }

        public Criteria andK3NumLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("k3_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3NumLike(String value) {
            addCriterion("k3_num like", value, "k3Num");
            return (Criteria) this;
        }

        public Criteria andK3NumNotLike(String value) {
            addCriterion("k3_num not like", value, "k3Num");
            return (Criteria) this;
        }

        public Criteria andK3NumIn(List<String> values) {
            addCriterion("k3_num in", values, "k3Num");
            return (Criteria) this;
        }

        public Criteria andK3NumNotIn(List<String> values) {
            addCriterion("k3_num not in", values, "k3Num");
            return (Criteria) this;
        }

        public Criteria andK3NumBetween(String value1, String value2) {
            addCriterion("k3_num between", value1, value2, "k3Num");
            return (Criteria) this;
        }

        public Criteria andK3NumNotBetween(String value1, String value2) {
            addCriterion("k3_num not between", value1, value2, "k3Num");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdIsNull() {
            addCriterion("product_department_id is null");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdIsNotNull() {
            addCriterion("product_department_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdEqualTo(String value) {
            addCriterion("product_department_id =", value, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("product_department_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdNotEqualTo(String value) {
            addCriterion("product_department_id <>", value, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("product_department_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdGreaterThan(String value) {
            addCriterion("product_department_id >", value, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("product_department_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdGreaterThanOrEqualTo(String value) {
            addCriterion("product_department_id >=", value, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("product_department_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdLessThan(String value) {
            addCriterion("product_department_id <", value, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("product_department_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdLessThanOrEqualTo(String value) {
            addCriterion("product_department_id <=", value, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("product_department_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdLike(String value) {
            addCriterion("product_department_id like", value, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdNotLike(String value) {
            addCriterion("product_department_id not like", value, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdIn(List<String> values) {
            addCriterion("product_department_id in", values, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdNotIn(List<String> values) {
            addCriterion("product_department_id not in", values, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdBetween(String value1, String value2) {
            addCriterion("product_department_id between", value1, value2, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdNotBetween(String value1, String value2) {
            addCriterion("product_department_id not between", value1, value2, "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameIsNull() {
            addCriterion("product_department_name is null");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameIsNotNull() {
            addCriterion("product_department_name is not null");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameEqualTo(String value) {
            addCriterion("product_department_name =", value, "productDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("product_department_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameNotEqualTo(String value) {
            addCriterion("product_department_name <>", value, "productDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("product_department_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameGreaterThan(String value) {
            addCriterion("product_department_name >", value, "productDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("product_department_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameGreaterThanOrEqualTo(String value) {
            addCriterion("product_department_name >=", value, "productDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("product_department_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameLessThan(String value) {
            addCriterion("product_department_name <", value, "productDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("product_department_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameLessThanOrEqualTo(String value) {
            addCriterion("product_department_name <=", value, "productDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("product_department_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameLike(String value) {
            addCriterion("product_department_name like", value, "productDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameNotLike(String value) {
            addCriterion("product_department_name not like", value, "productDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameIn(List<String> values) {
            addCriterion("product_department_name in", values, "productDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameNotIn(List<String> values) {
            addCriterion("product_department_name not in", values, "productDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameBetween(String value1, String value2) {
            addCriterion("product_department_name between", value1, value2, "productDepartmentName");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameNotBetween(String value1, String value2) {
            addCriterion("product_department_name not between", value1, value2, "productDepartmentName");
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdIsNull() {
            addCriterion("k3_sync_department_id is null");
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdIsNotNull() {
            addCriterion("k3_sync_department_id is not null");
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdEqualTo(String value) {
            addCriterion("k3_sync_department_id =", value, "k3SyncDepartmentId");
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("k3_sync_department_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdNotEqualTo(String value) {
            addCriterion("k3_sync_department_id <>", value, "k3SyncDepartmentId");
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdNotEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("k3_sync_department_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdGreaterThan(String value) {
            addCriterion("k3_sync_department_id >", value, "k3SyncDepartmentId");
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdGreaterThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("k3_sync_department_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdGreaterThanOrEqualTo(String value) {
            addCriterion("k3_sync_department_id >=", value, "k3SyncDepartmentId");
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdGreaterThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("k3_sync_department_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdLessThan(String value) {
            addCriterion("k3_sync_department_id <", value, "k3SyncDepartmentId");
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdLessThanColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("k3_sync_department_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdLessThanOrEqualTo(String value) {
            addCriterion("k3_sync_department_id <=", value, "k3SyncDepartmentId");
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdLessThanOrEqualToColumn(K3Orders.Column column) {
            addCriterion(new StringBuilder("k3_sync_department_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdLike(String value) {
            addCriterion("k3_sync_department_id like", value, "k3SyncDepartmentId");
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdNotLike(String value) {
            addCriterion("k3_sync_department_id not like", value, "k3SyncDepartmentId");
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdIn(List<String> values) {
            addCriterion("k3_sync_department_id in", values, "k3SyncDepartmentId");
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdNotIn(List<String> values) {
            addCriterion("k3_sync_department_id not in", values, "k3SyncDepartmentId");
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdBetween(String value1, String value2) {
            addCriterion("k3_sync_department_id between", value1, value2, "k3SyncDepartmentId");
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdNotBetween(String value1, String value2) {
            addCriterion("k3_sync_department_id not between", value1, value2, "k3SyncDepartmentId");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andOrderIdLikeInsensitive(String value) {
            addCriterion("upper(order_id) like", value.toUpperCase(), "orderId");
            return (Criteria) this;
        }

        public Criteria andSpuNameLikeInsensitive(String value) {
            addCriterion("upper(spu_name) like", value.toUpperCase(), "spuName");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLikeInsensitive(String value) {
            addCriterion("upper(spu_code) like", value.toUpperCase(), "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuTypeLikeInsensitive(String value) {
            addCriterion("upper(spu_type) like", value.toUpperCase(), "spuType");
            return (Criteria) this;
        }

        public Criteria andSpuClassLikeInsensitive(String value) {
            addCriterion("upper(spu_class) like", value.toUpperCase(), "spuClass");
            return (Criteria) this;
        }

        public Criteria andSkuNameLikeInsensitive(String value) {
            addCriterion("upper(sku_name) like", value.toUpperCase(), "skuName");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLikeInsensitive(String value) {
            addCriterion("upper(sku_code) like", value.toUpperCase(), "skuCode");
            return (Criteria) this;
        }

        public Criteria andAtomNameLikeInsensitive(String value) {
            addCriterion("upper(atom_name) like", value.toUpperCase(), "atomName");
            return (Criteria) this;
        }

        public Criteria andAtomCodeLikeInsensitive(String value) {
            addCriterion("upper(atom_code) like", value.toUpperCase(), "atomCode");
            return (Criteria) this;
        }

        public Criteria andUnitPriceLikeInsensitive(String value) {
            addCriterion("upper(unit_price) like", value.toUpperCase(), "unitPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceLikeInsensitive(String value) {
            addCriterion("upper(deduct_price) like", value.toUpperCase(), "deductPrice");
            return (Criteria) this;
        }

        public Criteria andReceiverPhoneLikeInsensitive(String value) {
            addCriterion("upper(receiver_phone) like", value.toUpperCase(), "receiverPhone");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceLikeInsensitive(String value) {
            addCriterion("upper(order_province) like", value.toUpperCase(), "orderProvince");
            return (Criteria) this;
        }

        public Criteria andOrderProvinceCodeLikeInsensitive(String value) {
            addCriterion("upper(order_province_code) like", value.toUpperCase(), "orderProvinceCode");
            return (Criteria) this;
        }

        public Criteria andOrderCityLikeInsensitive(String value) {
            addCriterion("upper(order_city) like", value.toUpperCase(), "orderCity");
            return (Criteria) this;
        }

        public Criteria andOrderCityCodeLikeInsensitive(String value) {
            addCriterion("upper(order_city_code) like", value.toUpperCase(), "orderCityCode");
            return (Criteria) this;
        }

        public Criteria andCreateOperCodeLikeInsensitive(String value) {
            addCriterion("upper(create_oper_code) like", value.toUpperCase(), "createOperCode");
            return (Criteria) this;
        }

        public Criteria andEmployeeNumLikeInsensitive(String value) {
            addCriterion("upper(employee_num) like", value.toUpperCase(), "employeeNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNumLikeInsensitive(String value) {
            addCriterion("upper(material_num) like", value.toUpperCase(), "materialNum");
            return (Criteria) this;
        }

        public Criteria andMaterialNameLikeInsensitive(String value) {
            addCriterion("upper(material_name) like", value.toUpperCase(), "materialName");
            return (Criteria) this;
        }

        public Criteria andMaterialDeptLikeInsensitive(String value) {
            addCriterion("upper(material_dept) like", value.toUpperCase(), "materialDept");
            return (Criteria) this;
        }

        public Criteria andMaterialUnitLikeInsensitive(String value) {
            addCriterion("upper(material_unit) like", value.toUpperCase(), "materialUnit");
            return (Criteria) this;
        }

        public Criteria andContractNumLikeInsensitive(String value) {
            addCriterion("upper(contract_num) like", value.toUpperCase(), "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNameLikeInsensitive(String value) {
            addCriterion("upper(contract_name) like", value.toUpperCase(), "contractName");
            return (Criteria) this;
        }

        public Criteria andContractDeptLikeInsensitive(String value) {
            addCriterion("upper(contract_dept) like", value.toUpperCase(), "contractDept");
            return (Criteria) this;
        }

        public Criteria andCustomCodeLikeInsensitive(String value) {
            addCriterion("upper(custom_code) like", value.toUpperCase(), "customCode");
            return (Criteria) this;
        }

        public Criteria andContractSellUnitLikeInsensitive(String value) {
            addCriterion("upper(contract_sell_unit) like", value.toUpperCase(), "contractSellUnit");
            return (Criteria) this;
        }

        public Criteria andContractMoneyUnitLikeInsensitive(String value) {
            addCriterion("upper(contract_money_unit) like", value.toUpperCase(), "contractMoneyUnit");
            return (Criteria) this;
        }

        public Criteria andContractTypeLikeInsensitive(String value) {
            addCriterion("upper(contract_type) like", value.toUpperCase(), "contractType");
            return (Criteria) this;
        }

        public Criteria andContractProjectLikeInsensitive(String value) {
            addCriterion("upper(contract_project) like", value.toUpperCase(), "contractProject");
            return (Criteria) this;
        }

        public Criteria andContractSubProjectLikeInsensitive(String value) {
            addCriterion("upper(contract_sub_project) like", value.toUpperCase(), "contractSubProject");
            return (Criteria) this;
        }

        public Criteria andContractTaxLikeInsensitive(String value) {
            addCriterion("upper(contract_tax) like", value.toUpperCase(), "contractTax");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceLikeInsensitive(String value) {
            addCriterion("upper(buyer_province) like", value.toUpperCase(), "buyerProvince");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceMallCodeLikeInsensitive(String value) {
            addCriterion("upper(buyer_province_mall_code) like", value.toUpperCase(), "buyerProvinceMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerProvinceCodeLikeInsensitive(String value) {
            addCriterion("upper(buyer_province_code) like", value.toUpperCase(), "buyerProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityLikeInsensitive(String value) {
            addCriterion("upper(buyer_city) like", value.toUpperCase(), "buyerCity");
            return (Criteria) this;
        }

        public Criteria andBuyerCityMallCodeLikeInsensitive(String value) {
            addCriterion("upper(buyer_city_mall_code) like", value.toUpperCase(), "buyerCityMallCode");
            return (Criteria) this;
        }

        public Criteria andBuyerCityCodeLikeInsensitive(String value) {
            addCriterion("upper(buyer_city_code) like", value.toUpperCase(), "buyerCityCode");
            return (Criteria) this;
        }

        public Criteria andContractSatisTypeLikeInsensitive(String value) {
            addCriterion("upper(contract_satis_type) like", value.toUpperCase(), "contractSatisType");
            return (Criteria) this;
        }

        public Criteria andK3NumLikeInsensitive(String value) {
            addCriterion("upper(k3_num) like", value.toUpperCase(), "k3Num");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentIdLikeInsensitive(String value) {
            addCriterion("upper(product_department_id) like", value.toUpperCase(), "productDepartmentId");
            return (Criteria) this;
        }

        public Criteria andProductDepartmentNameLikeInsensitive(String value) {
            addCriterion("upper(product_department_name) like", value.toUpperCase(), "productDepartmentName");
            return (Criteria) this;
        }

        public Criteria andK3SyncDepartmentIdLikeInsensitive(String value) {
            addCriterion("upper(k3_sync_department_id) like", value.toUpperCase(), "k3SyncDepartmentId");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        private K3OrdersExample example;

        protected Criteria(K3OrdersExample example) {
            super();
            this.example = example;
        }

        public K3OrdersExample example() {
            return this.example;
        }

        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            Criteria add(Criteria add);
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        void example(com.chinamobile.iot.sc.pojo.entity.K3OrdersExample example);
    }
}