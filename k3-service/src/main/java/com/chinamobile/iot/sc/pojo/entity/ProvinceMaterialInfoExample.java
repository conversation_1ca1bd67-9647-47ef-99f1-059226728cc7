package com.chinamobile.iot.sc.pojo.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class ProvinceMaterialInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public ProvinceMaterialInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public ProvinceMaterialInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    public ProvinceMaterialInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    public static Criteria newAndCreateCriteria() {
        ProvinceMaterialInfoExample example = new ProvinceMaterialInfoExample();
        return example.createCriteria();
    }

    public ProvinceMaterialInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    public ProvinceMaterialInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdIsNull() {
            addCriterion("province_contract_id is null");
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdIsNotNull() {
            addCriterion("province_contract_id is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdEqualTo(String value) {
            addCriterion("province_contract_id =", value, "provinceContractId");
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("province_contract_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdNotEqualTo(String value) {
            addCriterion("province_contract_id <>", value, "provinceContractId");
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdNotEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("province_contract_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdGreaterThan(String value) {
            addCriterion("province_contract_id >", value, "provinceContractId");
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdGreaterThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("province_contract_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdGreaterThanOrEqualTo(String value) {
            addCriterion("province_contract_id >=", value, "provinceContractId");
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdGreaterThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("province_contract_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdLessThan(String value) {
            addCriterion("province_contract_id <", value, "provinceContractId");
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdLessThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("province_contract_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdLessThanOrEqualTo(String value) {
            addCriterion("province_contract_id <=", value, "provinceContractId");
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdLessThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("province_contract_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdLike(String value) {
            addCriterion("province_contract_id like", value, "provinceContractId");
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdNotLike(String value) {
            addCriterion("province_contract_id not like", value, "provinceContractId");
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdIn(List<String> values) {
            addCriterion("province_contract_id in", values, "provinceContractId");
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdNotIn(List<String> values) {
            addCriterion("province_contract_id not in", values, "provinceContractId");
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdBetween(String value1, String value2) {
            addCriterion("province_contract_id between", value1, value2, "provinceContractId");
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdNotBetween(String value1, String value2) {
            addCriterion("province_contract_id not between", value1, value2, "provinceContractId");
            return (Criteria) this;
        }

        public Criteria andProductCodeIsNull() {
            addCriterion("product_code is null");
            return (Criteria) this;
        }

        public Criteria andProductCodeIsNotNull() {
            addCriterion("product_code is not null");
            return (Criteria) this;
        }

        public Criteria andProductCodeEqualTo(String value) {
            addCriterion("product_code =", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("product_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCodeNotEqualTo(String value) {
            addCriterion("product_code <>", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeNotEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("product_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCodeGreaterThan(String value) {
            addCriterion("product_code >", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeGreaterThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("product_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCodeGreaterThanOrEqualTo(String value) {
            addCriterion("product_code >=", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeGreaterThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("product_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCodeLessThan(String value) {
            addCriterion("product_code <", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeLessThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("product_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCodeLessThanOrEqualTo(String value) {
            addCriterion("product_code <=", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeLessThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("product_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductCodeLike(String value) {
            addCriterion("product_code like", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeNotLike(String value) {
            addCriterion("product_code not like", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeIn(List<String> values) {
            addCriterion("product_code in", values, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeNotIn(List<String> values) {
            addCriterion("product_code not in", values, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeBetween(String value1, String value2) {
            addCriterion("product_code between", value1, value2, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeNotBetween(String value1, String value2) {
            addCriterion("product_code not between", value1, value2, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductNameIsNull() {
            addCriterion("product_name is null");
            return (Criteria) this;
        }

        public Criteria andProductNameIsNotNull() {
            addCriterion("product_name is not null");
            return (Criteria) this;
        }

        public Criteria andProductNameEqualTo(String value) {
            addCriterion("product_name =", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("product_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductNameNotEqualTo(String value) {
            addCriterion("product_name <>", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("product_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThan(String value) {
            addCriterion("product_name >", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("product_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThanOrEqualTo(String value) {
            addCriterion("product_name >=", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("product_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductNameLessThan(String value) {
            addCriterion("product_name <", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLessThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("product_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductNameLessThanOrEqualTo(String value) {
            addCriterion("product_name <=", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLessThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("product_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProductNameLike(String value) {
            addCriterion("product_name like", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotLike(String value) {
            addCriterion("product_name not like", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameIn(List<String> values) {
            addCriterion("product_name in", values, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotIn(List<String> values) {
            addCriterion("product_name not in", values, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameBetween(String value1, String value2) {
            addCriterion("product_name between", value1, value2, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotBetween(String value1, String value2) {
            addCriterion("product_name not between", value1, value2, "productName");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeIsNull() {
            addCriterion("material_code is null");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeIsNotNull() {
            addCriterion("material_code is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeEqualTo(String value) {
            addCriterion("material_code =", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("material_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotEqualTo(String value) {
            addCriterion("material_code <>", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("material_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCodeGreaterThan(String value) {
            addCriterion("material_code >", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeGreaterThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("material_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCodeGreaterThanOrEqualTo(String value) {
            addCriterion("material_code >=", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeGreaterThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("material_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLessThan(String value) {
            addCriterion("material_code <", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLessThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("material_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLessThanOrEqualTo(String value) {
            addCriterion("material_code <=", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLessThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("material_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLike(String value) {
            addCriterion("material_code like", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotLike(String value) {
            addCriterion("material_code not like", value, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeIn(List<String> values) {
            addCriterion("material_code in", values, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotIn(List<String> values) {
            addCriterion("material_code not in", values, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeBetween(String value1, String value2) {
            addCriterion("material_code between", value1, value2, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeNotBetween(String value1, String value2) {
            addCriterion("material_code not between", value1, value2, "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeIsNull() {
            addCriterion("material_describe is null");
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeIsNotNull() {
            addCriterion("material_describe is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeEqualTo(String value) {
            addCriterion("material_describe =", value, "materialDescribe");
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("material_describe = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeNotEqualTo(String value) {
            addCriterion("material_describe <>", value, "materialDescribe");
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeNotEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("material_describe <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeGreaterThan(String value) {
            addCriterion("material_describe >", value, "materialDescribe");
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeGreaterThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("material_describe > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeGreaterThanOrEqualTo(String value) {
            addCriterion("material_describe >=", value, "materialDescribe");
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeGreaterThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("material_describe >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeLessThan(String value) {
            addCriterion("material_describe <", value, "materialDescribe");
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeLessThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("material_describe < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeLessThanOrEqualTo(String value) {
            addCriterion("material_describe <=", value, "materialDescribe");
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeLessThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("material_describe <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeLike(String value) {
            addCriterion("material_describe like", value, "materialDescribe");
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeNotLike(String value) {
            addCriterion("material_describe not like", value, "materialDescribe");
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeIn(List<String> values) {
            addCriterion("material_describe in", values, "materialDescribe");
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeNotIn(List<String> values) {
            addCriterion("material_describe not in", values, "materialDescribe");
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeBetween(String value1, String value2) {
            addCriterion("material_describe between", value1, value2, "materialDescribe");
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeNotBetween(String value1, String value2) {
            addCriterion("material_describe not between", value1, value2, "materialDescribe");
            return (Criteria) this;
        }

        public Criteria andPartCodeIsNull() {
            addCriterion("part_code is null");
            return (Criteria) this;
        }

        public Criteria andPartCodeIsNotNull() {
            addCriterion("part_code is not null");
            return (Criteria) this;
        }

        public Criteria andPartCodeEqualTo(String value) {
            addCriterion("part_code =", value, "partCode");
            return (Criteria) this;
        }

        public Criteria andPartCodeEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("part_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartCodeNotEqualTo(String value) {
            addCriterion("part_code <>", value, "partCode");
            return (Criteria) this;
        }

        public Criteria andPartCodeNotEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("part_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartCodeGreaterThan(String value) {
            addCriterion("part_code >", value, "partCode");
            return (Criteria) this;
        }

        public Criteria andPartCodeGreaterThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("part_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartCodeGreaterThanOrEqualTo(String value) {
            addCriterion("part_code >=", value, "partCode");
            return (Criteria) this;
        }

        public Criteria andPartCodeGreaterThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("part_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartCodeLessThan(String value) {
            addCriterion("part_code <", value, "partCode");
            return (Criteria) this;
        }

        public Criteria andPartCodeLessThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("part_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartCodeLessThanOrEqualTo(String value) {
            addCriterion("part_code <=", value, "partCode");
            return (Criteria) this;
        }

        public Criteria andPartCodeLessThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("part_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartCodeLike(String value) {
            addCriterion("part_code like", value, "partCode");
            return (Criteria) this;
        }

        public Criteria andPartCodeNotLike(String value) {
            addCriterion("part_code not like", value, "partCode");
            return (Criteria) this;
        }

        public Criteria andPartCodeIn(List<String> values) {
            addCriterion("part_code in", values, "partCode");
            return (Criteria) this;
        }

        public Criteria andPartCodeNotIn(List<String> values) {
            addCriterion("part_code not in", values, "partCode");
            return (Criteria) this;
        }

        public Criteria andPartCodeBetween(String value1, String value2) {
            addCriterion("part_code between", value1, value2, "partCode");
            return (Criteria) this;
        }

        public Criteria andPartCodeNotBetween(String value1, String value2) {
            addCriterion("part_code not between", value1, value2, "partCode");
            return (Criteria) this;
        }

        public Criteria andPartNameIsNull() {
            addCriterion("part_name is null");
            return (Criteria) this;
        }

        public Criteria andPartNameIsNotNull() {
            addCriterion("part_name is not null");
            return (Criteria) this;
        }

        public Criteria andPartNameEqualTo(String value) {
            addCriterion("part_name =", value, "partName");
            return (Criteria) this;
        }

        public Criteria andPartNameEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("part_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartNameNotEqualTo(String value) {
            addCriterion("part_name <>", value, "partName");
            return (Criteria) this;
        }

        public Criteria andPartNameNotEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("part_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartNameGreaterThan(String value) {
            addCriterion("part_name >", value, "partName");
            return (Criteria) this;
        }

        public Criteria andPartNameGreaterThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("part_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartNameGreaterThanOrEqualTo(String value) {
            addCriterion("part_name >=", value, "partName");
            return (Criteria) this;
        }

        public Criteria andPartNameGreaterThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("part_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartNameLessThan(String value) {
            addCriterion("part_name <", value, "partName");
            return (Criteria) this;
        }

        public Criteria andPartNameLessThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("part_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartNameLessThanOrEqualTo(String value) {
            addCriterion("part_name <=", value, "partName");
            return (Criteria) this;
        }

        public Criteria andPartNameLessThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("part_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartNameLike(String value) {
            addCriterion("part_name like", value, "partName");
            return (Criteria) this;
        }

        public Criteria andPartNameNotLike(String value) {
            addCriterion("part_name not like", value, "partName");
            return (Criteria) this;
        }

        public Criteria andPartNameIn(List<String> values) {
            addCriterion("part_name in", values, "partName");
            return (Criteria) this;
        }

        public Criteria andPartNameNotIn(List<String> values) {
            addCriterion("part_name not in", values, "partName");
            return (Criteria) this;
        }

        public Criteria andPartNameBetween(String value1, String value2) {
            addCriterion("part_name between", value1, value2, "partName");
            return (Criteria) this;
        }

        public Criteria andPartNameNotBetween(String value1, String value2) {
            addCriterion("part_name not between", value1, value2, "partName");
            return (Criteria) this;
        }

        public Criteria andNestedCodeIsNull() {
            addCriterion("nested_code is null");
            return (Criteria) this;
        }

        public Criteria andNestedCodeIsNotNull() {
            addCriterion("nested_code is not null");
            return (Criteria) this;
        }

        public Criteria andNestedCodeEqualTo(String value) {
            addCriterion("nested_code =", value, "nestedCode");
            return (Criteria) this;
        }

        public Criteria andNestedCodeEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("nested_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNestedCodeNotEqualTo(String value) {
            addCriterion("nested_code <>", value, "nestedCode");
            return (Criteria) this;
        }

        public Criteria andNestedCodeNotEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("nested_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNestedCodeGreaterThan(String value) {
            addCriterion("nested_code >", value, "nestedCode");
            return (Criteria) this;
        }

        public Criteria andNestedCodeGreaterThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("nested_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNestedCodeGreaterThanOrEqualTo(String value) {
            addCriterion("nested_code >=", value, "nestedCode");
            return (Criteria) this;
        }

        public Criteria andNestedCodeGreaterThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("nested_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNestedCodeLessThan(String value) {
            addCriterion("nested_code <", value, "nestedCode");
            return (Criteria) this;
        }

        public Criteria andNestedCodeLessThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("nested_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNestedCodeLessThanOrEqualTo(String value) {
            addCriterion("nested_code <=", value, "nestedCode");
            return (Criteria) this;
        }

        public Criteria andNestedCodeLessThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("nested_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNestedCodeLike(String value) {
            addCriterion("nested_code like", value, "nestedCode");
            return (Criteria) this;
        }

        public Criteria andNestedCodeNotLike(String value) {
            addCriterion("nested_code not like", value, "nestedCode");
            return (Criteria) this;
        }

        public Criteria andNestedCodeIn(List<String> values) {
            addCriterion("nested_code in", values, "nestedCode");
            return (Criteria) this;
        }

        public Criteria andNestedCodeNotIn(List<String> values) {
            addCriterion("nested_code not in", values, "nestedCode");
            return (Criteria) this;
        }

        public Criteria andNestedCodeBetween(String value1, String value2) {
            addCriterion("nested_code between", value1, value2, "nestedCode");
            return (Criteria) this;
        }

        public Criteria andNestedCodeNotBetween(String value1, String value2) {
            addCriterion("nested_code not between", value1, value2, "nestedCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeIsNull() {
            addCriterion("internet_material_code is null");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeIsNotNull() {
            addCriterion("internet_material_code is not null");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeEqualTo(String value) {
            addCriterion("internet_material_code =", value, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("internet_material_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeNotEqualTo(String value) {
            addCriterion("internet_material_code <>", value, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeNotEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("internet_material_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeGreaterThan(String value) {
            addCriterion("internet_material_code >", value, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeGreaterThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("internet_material_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeGreaterThanOrEqualTo(String value) {
            addCriterion("internet_material_code >=", value, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeGreaterThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("internet_material_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeLessThan(String value) {
            addCriterion("internet_material_code <", value, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeLessThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("internet_material_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeLessThanOrEqualTo(String value) {
            addCriterion("internet_material_code <=", value, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeLessThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("internet_material_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeLike(String value) {
            addCriterion("internet_material_code like", value, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeNotLike(String value) {
            addCriterion("internet_material_code not like", value, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeIn(List<String> values) {
            addCriterion("internet_material_code in", values, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeNotIn(List<String> values) {
            addCriterion("internet_material_code not in", values, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeBetween(String value1, String value2) {
            addCriterion("internet_material_code between", value1, value2, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeNotBetween(String value1, String value2) {
            addCriterion("internet_material_code not between", value1, value2, "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andUnitIsNull() {
            addCriterion("unit is null");
            return (Criteria) this;
        }

        public Criteria andUnitIsNotNull() {
            addCriterion("unit is not null");
            return (Criteria) this;
        }

        public Criteria andUnitEqualTo(String value) {
            addCriterion("unit =", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("unit = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualTo(String value) {
            addCriterion("unit <>", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("unit <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThan(String value) {
            addCriterion("unit >", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("unit > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualTo(String value) {
            addCriterion("unit >=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitGreaterThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("unit >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitLessThan(String value) {
            addCriterion("unit <", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("unit < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualTo(String value) {
            addCriterion("unit <=", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitLessThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("unit <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUnitLike(String value) {
            addCriterion("unit like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotLike(String value) {
            addCriterion("unit not like", value, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitIn(List<String> values) {
            addCriterion("unit in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotIn(List<String> values) {
            addCriterion("unit not in", values, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitBetween(String value1, String value2) {
            addCriterion("unit between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andUnitNotBetween(String value1, String value2) {
            addCriterion("unit not between", value1, value2, "unit");
            return (Criteria) this;
        }

        public Criteria andTaxExclusiveUnivalenceIsNull() {
            addCriterion("tax_exclusive_univalence is null");
            return (Criteria) this;
        }

        public Criteria andTaxExclusiveUnivalenceIsNotNull() {
            addCriterion("tax_exclusive_univalence is not null");
            return (Criteria) this;
        }

        public Criteria andTaxExclusiveUnivalenceEqualTo(BigDecimal value) {
            addCriterion("tax_exclusive_univalence =", value, "taxExclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andTaxExclusiveUnivalenceEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("tax_exclusive_univalence = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxExclusiveUnivalenceNotEqualTo(BigDecimal value) {
            addCriterion("tax_exclusive_univalence <>", value, "taxExclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andTaxExclusiveUnivalenceNotEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("tax_exclusive_univalence <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxExclusiveUnivalenceGreaterThan(BigDecimal value) {
            addCriterion("tax_exclusive_univalence >", value, "taxExclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andTaxExclusiveUnivalenceGreaterThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("tax_exclusive_univalence > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxExclusiveUnivalenceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_exclusive_univalence >=", value, "taxExclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andTaxExclusiveUnivalenceGreaterThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("tax_exclusive_univalence >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxExclusiveUnivalenceLessThan(BigDecimal value) {
            addCriterion("tax_exclusive_univalence <", value, "taxExclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andTaxExclusiveUnivalenceLessThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("tax_exclusive_univalence < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxExclusiveUnivalenceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_exclusive_univalence <=", value, "taxExclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andTaxExclusiveUnivalenceLessThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("tax_exclusive_univalence <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxExclusiveUnivalenceIn(List<BigDecimal> values) {
            addCriterion("tax_exclusive_univalence in", values, "taxExclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andTaxExclusiveUnivalenceNotIn(List<BigDecimal> values) {
            addCriterion("tax_exclusive_univalence not in", values, "taxExclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andTaxExclusiveUnivalenceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_exclusive_univalence between", value1, value2, "taxExclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andTaxExclusiveUnivalenceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_exclusive_univalence not between", value1, value2, "taxExclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andTaxRateIsNull() {
            addCriterion("tax_rate is null");
            return (Criteria) this;
        }

        public Criteria andTaxRateIsNotNull() {
            addCriterion("tax_rate is not null");
            return (Criteria) this;
        }

        public Criteria andTaxRateEqualTo(String value) {
            addCriterion("tax_rate =", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("tax_rate = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxRateNotEqualTo(String value) {
            addCriterion("tax_rate <>", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("tax_rate <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThan(String value) {
            addCriterion("tax_rate >", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("tax_rate > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThanOrEqualTo(String value) {
            addCriterion("tax_rate >=", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("tax_rate >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThan(String value) {
            addCriterion("tax_rate <", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("tax_rate < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThanOrEqualTo(String value) {
            addCriterion("tax_rate <=", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("tax_rate <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxRateLike(String value) {
            addCriterion("tax_rate like", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotLike(String value) {
            addCriterion("tax_rate not like", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateIn(List<String> values) {
            addCriterion("tax_rate in", values, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotIn(List<String> values) {
            addCriterion("tax_rate not in", values, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateBetween(String value1, String value2) {
            addCriterion("tax_rate between", value1, value2, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotBetween(String value1, String value2) {
            addCriterion("tax_rate not between", value1, value2, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxInclusiveUnivalenceIsNull() {
            addCriterion("tax_inclusive_univalence is null");
            return (Criteria) this;
        }

        public Criteria andTaxInclusiveUnivalenceIsNotNull() {
            addCriterion("tax_inclusive_univalence is not null");
            return (Criteria) this;
        }

        public Criteria andTaxInclusiveUnivalenceEqualTo(BigDecimal value) {
            addCriterion("tax_inclusive_univalence =", value, "taxInclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andTaxInclusiveUnivalenceEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("tax_inclusive_univalence = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxInclusiveUnivalenceNotEqualTo(BigDecimal value) {
            addCriterion("tax_inclusive_univalence <>", value, "taxInclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andTaxInclusiveUnivalenceNotEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("tax_inclusive_univalence <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxInclusiveUnivalenceGreaterThan(BigDecimal value) {
            addCriterion("tax_inclusive_univalence >", value, "taxInclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andTaxInclusiveUnivalenceGreaterThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("tax_inclusive_univalence > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxInclusiveUnivalenceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_inclusive_univalence >=", value, "taxInclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andTaxInclusiveUnivalenceGreaterThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("tax_inclusive_univalence >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxInclusiveUnivalenceLessThan(BigDecimal value) {
            addCriterion("tax_inclusive_univalence <", value, "taxInclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andTaxInclusiveUnivalenceLessThanColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("tax_inclusive_univalence < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxInclusiveUnivalenceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_inclusive_univalence <=", value, "taxInclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andTaxInclusiveUnivalenceLessThanOrEqualToColumn(ProvinceMaterialInfo.Column column) {
            addCriterion(new StringBuilder("tax_inclusive_univalence <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTaxInclusiveUnivalenceIn(List<BigDecimal> values) {
            addCriterion("tax_inclusive_univalence in", values, "taxInclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andTaxInclusiveUnivalenceNotIn(List<BigDecimal> values) {
            addCriterion("tax_inclusive_univalence not in", values, "taxInclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andTaxInclusiveUnivalenceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_inclusive_univalence between", value1, value2, "taxInclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andTaxInclusiveUnivalenceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_inclusive_univalence not between", value1, value2, "taxInclusiveUnivalence");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andProvinceContractIdLikeInsensitive(String value) {
            addCriterion("upper(province_contract_id) like", value.toUpperCase(), "provinceContractId");
            return (Criteria) this;
        }

        public Criteria andProductCodeLikeInsensitive(String value) {
            addCriterion("upper(product_code) like", value.toUpperCase(), "productCode");
            return (Criteria) this;
        }

        public Criteria andProductNameLikeInsensitive(String value) {
            addCriterion("upper(product_name) like", value.toUpperCase(), "productName");
            return (Criteria) this;
        }

        public Criteria andMaterialCodeLikeInsensitive(String value) {
            addCriterion("upper(material_code) like", value.toUpperCase(), "materialCode");
            return (Criteria) this;
        }

        public Criteria andMaterialDescribeLikeInsensitive(String value) {
            addCriterion("upper(material_describe) like", value.toUpperCase(), "materialDescribe");
            return (Criteria) this;
        }

        public Criteria andPartCodeLikeInsensitive(String value) {
            addCriterion("upper(part_code) like", value.toUpperCase(), "partCode");
            return (Criteria) this;
        }

        public Criteria andPartNameLikeInsensitive(String value) {
            addCriterion("upper(part_name) like", value.toUpperCase(), "partName");
            return (Criteria) this;
        }

        public Criteria andNestedCodeLikeInsensitive(String value) {
            addCriterion("upper(nested_code) like", value.toUpperCase(), "nestedCode");
            return (Criteria) this;
        }

        public Criteria andInternetMaterialCodeLikeInsensitive(String value) {
            addCriterion("upper(internet_material_code) like", value.toUpperCase(), "internetMaterialCode");
            return (Criteria) this;
        }

        public Criteria andUnitLikeInsensitive(String value) {
            addCriterion("upper(unit) like", value.toUpperCase(), "unit");
            return (Criteria) this;
        }

        public Criteria andTaxRateLikeInsensitive(String value) {
            addCriterion("upper(tax_rate) like", value.toUpperCase(), "taxRate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        private ProvinceMaterialInfoExample example;

        protected Criteria(ProvinceMaterialInfoExample example) {
            super();
            this.example = example;
        }

        public ProvinceMaterialInfoExample example() {
            return this.example;
        }

        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            Criteria add(Criteria add);
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        void example(com.chinamobile.iot.sc.pojo.entity.ProvinceMaterialInfoExample example);
    }
}