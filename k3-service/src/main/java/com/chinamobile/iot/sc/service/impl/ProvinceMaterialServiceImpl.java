package com.chinamobile.iot.sc.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.WriteDirectionEnum;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.common.utils.RegexUtil;
import com.chinamobile.iot.sc.config.ProvinceCityConfig;
import com.chinamobile.iot.sc.dao.ProvinceContractInfoMapper;
import com.chinamobile.iot.sc.dao.ProvinceContractInfoNewMapper;
import com.chinamobile.iot.sc.dao.ProvinceMaterialInfoMapper;
import com.chinamobile.iot.sc.dao.ProvinceMaterialInfoNewMapper;
import com.chinamobile.iot.sc.dao.ext.ProvinceContractInfoMapperExt;
import com.chinamobile.iot.sc.enums.*;
import com.chinamobile.iot.sc.enums.log.*;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.pojo.dto.*;
import com.chinamobile.iot.sc.pojo.entity.*;
import com.chinamobile.iot.sc.pojo.param.ProvinceContractUpdateParam;
import com.chinamobile.iot.sc.pojo.param.ProvincialContractMaterialParam;
import com.chinamobile.iot.sc.pojo.vo.PageVO;
import com.chinamobile.iot.sc.pojo.vo.ProvinceMaterialVO;
import com.chinamobile.iot.sc.service.LogService;
import com.chinamobile.iot.sc.service.ProvinceMaterialService;
import com.chinamobile.iot.sc.service.excel.*;
import com.chinamobile.iot.sc.util.DateUtils;
import com.chinamobile.iot.sc.util.excel.EasyExcelDTO;
import com.chinamobile.iot.sc.util.excel.EasyExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.constant.K3SyncStatusConstant.*;

/**
 * <AUTHOR> xiemaohua
 * @date : 2023/3/23 9:49
 * @description: 省物料配置接口实现类
 **/
@Service
@Slf4j
public class ProvinceMaterialServiceImpl implements ProvinceMaterialService {


    @Resource
    private ProvinceContractInfoMapper provinceContractInfoMapper;

    @Resource
    private ProvinceMaterialInfoMapper provinceMaterialInfoMapper;


    @Resource
    private ProvinceContractInfoNewMapper provinceContractInfoNewMapper;

    @Resource
    private ProvinceMaterialInfoNewMapper provinceMaterialInfoNewMapper;

    @Resource
    private ProvinceContractInfoMapperExt provinceContractInfoMapperExt;

    @Resource
    private ProvinceCityConfig provinceCityConfig;

    @Resource
    private LogService logService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importOneNetMaterialRequest(MultipartFile file) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        if (file.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.FILE_NOT_EXIST);
        }
        String oldName = file.getOriginalFilename();
        boolean fileTypeBool = oldName == null || (!oldName.endsWith(".xlsx") && !oldName.endsWith(".xls"));
        if (fileTypeBool) {
            throw new BusinessException(BaseErrorConstant.FILE_TYPE_ERROR);
        }

        //物料清单监听类
        BatchOneNetMaterialRequestExcelListener listenerMaterial = new BatchOneNetMaterialRequestExcelListener();

        //采购信息监听类
        BatchPurchaseRequestExcelListener listenerPurchase = new BatchPurchaseRequestExcelListener();
        listenerPurchase.setProvinceCityConfig(provinceCityConfig);

        try {
            //读取表sheet1 采购信息
            List<Object> purchaseList = EasyExcel.read(file.getInputStream(), ProvinceContractRequestExcel.class, listenerPurchase)
                    .sheet(0).headRowNumber(1).doReadSync();

            //读取sheet2 物料清单
            List<Object> materialList = EasyExcel.read(file.getInputStream(), BatchOneNetMaterialRequestExcel.class, listenerMaterial)
                    .sheet(1).headRowNumber(1).doReadSync();

          /*  List<Map<Integer, String>> headerMapsData = listenerMaterial.getHeaderMapsData();
            log.info("获取头信息：{}", headerMapsData);*/
            //物料成功条数
            List<ProvinceMaterialSucceedDTO> succeedListDataExcel = listenerMaterial.getSucceedListDataExcel();
            //失败物料导入
            List<ProvinceContractMaterialFailDTO> provinceContractMaterialFailDTO = listenerMaterial.getProvinceContractMaterialFailDTO();


            //合同成功条数对象
            List<ProvinceContractSucceedDTO> provinceContractSucceedDTO = listenerPurchase.getSucceedContractExcel();
            //合同失败条数对象
            List<ProvinceContractSucceedDTO> provinceContractFailDTO = listenerPurchase.getProvinceContractFailDTO();

            long count = provinceContractSucceedDTO.stream().map(ProvinceContractSucceedDTO::getProvinceContractNo).distinct().count();
            if (count>1){
                log.info("导入的采购信息合同编号不一致：ProvinceContractNo;{}",count);
                throw new BusinessException(BaseErrorConstant.FILE_PARSE_ERROR);
            }
            //判断是否有失败的
        //    boolean fieldsIsNotNull = checkObjAllFieldsIsNotNull(provinceContractFailDTO);

            if (CollectionUtils.isNotEmpty(provinceContractMaterialFailDTO) || CollectionUtils.isNotEmpty(provinceContractFailDTO)) {
                String excelName = "批量导入物料映射信息失败";
                excelName = URLEncoder.encode(excelName, "UTF-8");
                ClassPathResource classPathResource = new ClassPathResource("template/material_mapping_error.xlsx");
                InputStream templateFileName = classPathResource.getInputStream();
                String stateCode = BaseErrorConstant.IMPORT_MATERIAL_MAPPINGS_BATCHES_FAIL.getStateCode();
                String message = BaseErrorConstant.IMPORT_MATERIAL_MAPPINGS_BATCHES_FAIL.getMessage();

                //构建填充excel参数
      /*          Map<String, Object> mapPurchase = new HashMap<String, Object>();
                mapPurchase.put("contractType", provinceContractFailDTO.getContractType());
                mapPurchase.put("provinceName", provinceContractFailDTO.getProvinceName());
                mapPurchase.put("provinceContractNo", provinceContractFailDTO.getProvinceContractNo());
                mapPurchase.put("provinceContractName", provinceContractFailDTO.getProvinceContractName());
                mapPurchase.put("internetContractCode",provinceContractFailDTO.getInternetContractCode());
                mapPurchase.put("buyerName", provinceContractFailDTO.getBuyerName());
                mapPurchase.put("buyerNo", provinceContractFailDTO.getBuyerNo());
                mapPurchase.put("applyDepartmentNo", provinceContractFailDTO.getApplyDepartmentNo());
                mapPurchase.put("applyDepartment", provinceContractFailDTO.getApplyDepartment());
                mapPurchase.put("companyCode", provinceContractFailDTO.getCompanyCode());
                mapPurchase.put("companyName", provinceContractFailDTO.getCompanyName());
                EasyExcelUtils.exportExcel(response,"list",provinceContractMaterialFailDTO,map,excelName,templateFileName,
                   0,"失败描述"  ,stateCode,message);*/
                List<EasyExcelDTO> easyExcelDTOList = new ArrayList<>();
                //采购信息失败信息
                EasyExcelDTO easyExcelDTO = EasyExcelUtils.setEasyExcelDTO(0, "采购信息", "list",
                        provinceContractFailDTO, null);
                easyExcelDTOList.add(easyExcelDTO);

                // 省侧订单明细数据
                easyExcelDTO = EasyExcelUtils.setEasyExcelDTO(1, "物料清单", "list",
                        provinceContractMaterialFailDTO, null);
                easyExcelDTOList.add(easyExcelDTO);
                  //导出失败信息
                EasyExcelUtils.exportManySheetExcel(response, easyExcelDTOList,
                        excelName, templateFileName,
                        stateCode,message);
            } else {
                //判断当前合同是否已经导入过，导入过删除之前关联物料重新新增
                mappingMaterialOrContractPut(succeedListDataExcel,provinceContractSucceedDTO);
                response.addHeader("stateCode", BaseErrorConstant.SUCCESS.getStateCode());
                response.addHeader("message", URLEncoder.encode("批量导入物料信息成功", "UTF-8"));
            }
        } catch (IOException e) {
            log.error("读取文件异常，文件名: {},异常描述：{}", oldName, e);
            throw new BusinessException(BaseErrorConstant.FILE_PARSE_ERROR);
        } catch (ExcelAnalysisException e) {
            e.printStackTrace();
            throw new BusinessException(BaseErrorConstant.RULE_NOT_ALLOW, e.getCause().getMessage());
        } catch (BusinessException e) {
            e.printStackTrace();
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(BaseErrorConstant.FILE_PARSE_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importOneNetMaterialNewRequest(MultipartFile file) {
        ServletRequestAttributes requestAttr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        HttpServletResponse response = requestAttr.getResponse();
        if (file.isEmpty()) {
            throw new BusinessException(BaseErrorConstant.FILE_NOT_EXIST);
        }
        String oldName = file.getOriginalFilename();
        boolean fileTypeBool = oldName == null || (!oldName.endsWith(".xlsx") && !oldName.endsWith(".xls"));
        if (fileTypeBool) {
            throw new BusinessException(BaseErrorConstant.FILE_TYPE_ERROR);
        }

        //合同监听类
        ProvinceContractRequestNewExcelListener listenerContract = new ProvinceContractRequestNewExcelListener();
        listenerContract.setProvinceCityConfig(provinceCityConfig);
        //物料监听类
        ProvinceMaterialRequestNewExcelListener listenerMaterial = new ProvinceMaterialRequestNewExcelListener();
        listenerMaterial.setProvinceCityConfig(provinceCityConfig);
        try {
            //读取表sheet1 合同
            List<Object> purchaseList = EasyExcel.read(file.getInputStream(), ProvinceContractRequestNewExcel.class, listenerContract)
                    .sheet(0).headRowNumber(4).doReadSync();

            //读取sheet2 物料
            List<Object> materialList = EasyExcel.read(file.getInputStream(), ProvinceMaterialRequestNewExcel.class, listenerMaterial)
                    .sheet(1).headRowNumber(2).doReadSync();
            //物料成功条数
            List<ProvinceMaterialRequestNewDTO> succeedProvinceMaterialList = listenerMaterial.getSucceedProvinceMaterialExcel();
            //失败物料导入
            List<ProvinceMaterialRequestNewFailDTO> provinceMaterialFailList = listenerMaterial.getProvinceMaterialFailDTO();


            //合同成功条数对象
            List<ProvinceContractImportNewDTO> provinceContractSucceedList = listenerContract.getSucceedContractExcel();
            //合同失败条数对象
            List<ProvinceContractImportNewDTO> provinceContractFailList = listenerContract.getProvinceContractFailDTO();
            long count = provinceContractSucceedList.stream().map(ProvinceContractImportNewDTO::getVendorCode).distinct().count();
            if (count>1){
                log.info("导入的供应商编码不一致：ProvinceContractNo;{}",count);
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"导入的供应商编码不一致");
            }


            long countContract = provinceContractSucceedList.stream().map(x ->x.getContractType()+"_"+ x.getInternetContractCode() + "_" + x.getProvinceName() + "_" + x.getCityName()).collect(Collectors.toList()).stream().distinct().count();
            if (countContract != provinceContractSucceedList.size()){
                log.info("导入的物联网合同编码存在相同：ProvinceContractNo;{}",count);
                throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"导入的合同类型+物联网合同编码+省份+地市存在相同合同不唯一，相同数据");
            }
            if (CollectionUtils.isNotEmpty(provinceMaterialFailList) || CollectionUtils.isNotEmpty(provinceContractFailList)) {
                String excelName = "批量导入物料映射信息失败";
                excelName = URLEncoder.encode(excelName, "UTF-8");
                ClassPathResource classPathResource = new ClassPathResource("template/material_mapping_error.xlsx");
                InputStream templateFileName = classPathResource.getInputStream();
                String stateCode = BaseErrorConstant.IMPORT_MATERIAL_MAPPINGS_BATCHES_FAIL.getStateCode();
                String message = BaseErrorConstant.IMPORT_MATERIAL_MAPPINGS_BATCHES_FAIL.getMessage();
                List<EasyExcelDTO> easyExcelDTOList = new ArrayList<>();
                // 省合同
                if (CollectionUtils.isEmpty(provinceMaterialFailList)){
                    provinceContractFailList.add(new ProvinceContractImportNewDTO());
                }
                EasyExcelDTO easyExcelDTO = EasyExcelUtils.setEasyExcelDTO(0, "基础信息", "list",
                        provinceContractFailList, null);
                easyExcelDTOList.add(easyExcelDTO);
                if (CollectionUtils.isEmpty(provinceMaterialFailList)){
                    provinceMaterialFailList.add(new ProvinceMaterialRequestNewFailDTO());
                }
                //采物料
                 easyExcelDTO = EasyExcelUtils.setEasyExcelDTO(1, "物料信息", "list",
                        provinceMaterialFailList, null);
                easyExcelDTOList.add(easyExcelDTO);

                //导出失败信息
                EasyExcelUtils.exportManySheetExcel(response, easyExcelDTOList,
                        excelName, templateFileName,
                        stateCode,message);
            } else {
                //判断导入的合同 是否有对应的物料信息
                Map<String, List<ProvinceContractImportNewDTO>> provinceContractCollect = provinceContractSucceedList.stream().collect(Collectors.groupingBy(x -> x.getContractType() + "_" + x.getInternetContractCode() + "_" + x.getProvinceName() + "_" + x.getCityName()));
                Map<String, List<ProvinceMaterialRequestNewDTO>> provinceMaterialCollect = succeedProvinceMaterialList.stream().collect(Collectors.groupingBy(x -> x.getContractType() + "_" + x.getInternetContractCode() + "_" + x.getProvinceName() + "_" + x.getCityName()));
                Iterator<String> iterator = provinceContractCollect.keySet().iterator();
                while (iterator.hasNext()){
                    String key = iterator.next();
                    List<ProvinceMaterialRequestNewDTO> dtoList = provinceMaterialCollect.get(key);
                    if (CollectionUtils.isEmpty(dtoList)){
                        log.info("导入的合同类型+物联网合同编码+省份+地市的合同信息不存在对应的唯一物料信息：ProvinceMaterialNo;{}",key);
                        throw new BusinessException(BaseErrorConstant.PARAM_ERROR,"导入的合同类型+物联网合同编码+省份+地市的合同信息不存在对应的唯一物料信息"+key);
                    }
                }
                //判断当前合同是否已经导入过，导入过删除之前关联物料重新新增
                mappingMaterialOrContractPutNew(succeedProvinceMaterialList,provinceContractSucceedList);
                response.addHeader("stateCode", BaseErrorConstant.SUCCESS.getStateCode());
                response.addHeader("message", URLEncoder.encode("批量导入物料信息成功", "UTF-8"));
            }
        } catch (IOException e) {
            log.error("读取文件异常，文件名: {},异常描述：{}", oldName, e);
            throw new BusinessException(BaseErrorConstant.FILE_PARSE_ERROR);
        } catch (ExcelAnalysisException e) {
            e.printStackTrace();
            throw new BusinessException(BaseErrorConstant.RULE_NOT_ALLOW, e.getCause().getMessage());
        } catch (BusinessException e) {
            e.printStackTrace();
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(BaseErrorConstant.FILE_PARSE_ERROR);
        }
    }

    @Override
    public BaseAnswer<PageVO<ProvinceContractInfoNew>> queryListProvinceContracts(ProvincialContractMaterialParam contractMaterialParam) {
        PageVO<ProvinceContractInfoNew> pageVO = new PageVO<>();
        Integer pageIndex = contractMaterialParam.getPageIndex();
        Integer pageCount = contractMaterialParam.getPageCount();
        if (pageIndex == null || pageIndex == 0) {
            pageIndex = 1;
        }
        if (pageCount == null || pageCount == 0) {
            pageCount = 5;
        }
        Page<ProvinceContractInfoNew> page = new Page<>(pageIndex, pageCount);
        Page<ProvinceContractInfoNew> provinceContractInfoPage = provinceContractInfoMapperExt.listProvinceContractInfo(page, contractMaterialParam);
        pageVO.setCurrentPage(provinceContractInfoPage.getCurrent());
        pageVO.setPageCount(provinceContractInfoPage.getSize());
        pageVO.setTotalCount(provinceContractInfoPage.getTotal());
        pageVO.setList(provinceContractInfoPage.getRecords());
        return new BaseAnswer<PageVO<ProvinceContractInfoNew>>().setData(pageVO);
    }

    @Override
    public BaseAnswer<List<ProvinceMaterialVO>> getProvinceMaterialList(String internetContractCode,String provinceName,
                                                                        String cityName,String contractType) {
        ProvinceMaterialInfoNewExample example = new ProvinceMaterialInfoNewExample();
        ProvinceMaterialInfoNewExample.Criteria criteria = example.createCriteria();
        criteria.andInternetContractCodeEqualTo(internetContractCode)
                .andContractTypeEqualTo(contractType).andProvinceNameEqualTo(provinceName);
        if (StringUtils.isNotEmpty(cityName)){
            criteria.andCityNameEqualTo(cityName);
        }
        List<ProvinceMaterialInfoNew> provinceMaterialInfoNews = provinceMaterialInfoNewMapper.selectByExample(example);
        List<ProvinceMaterialVO> collect = provinceMaterialInfoNews.stream().map(provinceMaterialInfoNew -> {
            ProvinceMaterialVO provinceMaterialVO = new ProvinceMaterialVO();
            BeanUtils.copyProperties(provinceMaterialInfoNew, provinceMaterialVO);
            return provinceMaterialVO;
        }).collect(Collectors.toList());
        //添加成功日志
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("【查看物料表】\n").append("查看").append(provinceName).append("省");
        if (StringUtils.isNotEmpty(cityName)){
            stringBuilder.append(cityName).append("地市");
        }
        String display = SPUOfferingClassEnum.getDisplay(contractType);
        stringBuilder.append(",").append(display).append("合同类型，物料表").append("合同编码").append(internetContractCode);
        logService.recordOperateLog(ModuleEnum.ONLINE_SETTLE_MANAGE.code,
                OnlineSettleOperateEnum.PROVINCE_INFO_OPERATE.code,stringBuilder.toString());
        return new BaseAnswer<List<ProvinceMaterialVO>>().setData(collect);
    }

    @Override
    public BaseAnswer<Void> deleteProvinceContracts(String id) {
        ProvinceContractInfoNew provinceContractInfoNew = provinceContractInfoNewMapper.selectByPrimaryKey(id);

        provinceContractInfoNewMapper.deleteByPrimaryKey(id);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("【删除物料表】\n").append("删除").append(provinceContractInfoNew.getProvinceName()).append("省");
        if (StringUtils.isNotEmpty(provinceContractInfoNew.getCityName())){
            stringBuilder.append(provinceContractInfoNew.getCityName()).append("地市");
        }
        String display = SPUOfferingClassEnum.getDisplay(provinceContractInfoNew.getContractType());
        stringBuilder.append(",").append(display).append("合同类型，省侧信息表和物料表").append("合同编码").append(provinceContractInfoNew.getInternetContractCode());
        logService.recordOperateLog(ModuleEnum.ONLINE_SETTLE_MANAGE.code,
                OnlineSettleOperateEnum.PROVINCE_INFO_OPERATE.code,stringBuilder.toString());
        return new BaseAnswer<>();
    }

    @Override
    public BaseAnswer<Void> updateProvinceContracts(ProvinceContractUpdateParam param) {

        ProvinceContractInfoNew provinceContractInfoNew = new ProvinceContractInfoNew();
        BeanUtils.copyProperties(param,provinceContractInfoNew);
        provinceContractInfoNew.setArrivalDays(Integer.valueOf(param.getArrivalDays()));
        String id = param.getId();
        ProvinceContractInfoNew provinceContractInfoOld = provinceContractInfoNewMapper.selectByPrimaryKey(id);

        //校验数据
        String failedReason = packageFailedPurchaseReason(provinceContractInfoNew);
        if (StringUtils.isNotEmpty(failedReason)){
            throw new BusinessException(BaseErrorConstant.PARAM_ERROR,failedReason);
        }
        provinceContractInfoNewMapper.updateByPrimaryKeySelective(provinceContractInfoNew);
        //添加成功日志
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("【修改】\n").append("修改").append(provinceContractInfoOld.getProvinceName());
        String cityName = provinceContractInfoNew.getCityName();
        if (StringUtils.isNotEmpty(cityName)){
            stringBuilder.append(cityName).append("地市");
        }
        String display = SPUOfferingClassEnum.getDisplay(provinceContractInfoNew.getContractType());
        stringBuilder.append(",").append(display).append("合同类型，省侧信息表").append("合同编码").append(provinceContractInfoNew.getInternetContractCode()).append("\n");
        String contractTypeUp = param.getContractType();
        String contractTypeOld = provinceContractInfoOld.getContractType();
        if (!contractTypeUp.equals(contractTypeOld)){
            stringBuilder.append("合同类型字段由").append(display).append("修改为").append(SPUOfferingClassEnum.getDisplay(contractTypeUp)).append("\n");
        }
        String contractCodeUp = param.getContractCode();
        String contractCode = provinceContractInfoOld.getContractCode();
        if (!contractCodeUp.equals(contractCode)){
            stringBuilder.append("合同编码字段由").append(contractCode).append("修改为").append(contractCodeUp).append("\n");
        }
        String vendorCodeUp = param.getVendorCode();
        String vendorCode = provinceContractInfoOld.getVendorCode();
        if (!vendorCodeUp.equals(vendorCode)){
            stringBuilder.append("供应商编码字段由").append(vendorCode).append("修改为").append(vendorCodeUp).append("\n");
        }
        String mtlTypeCodeUp = param.getMtlTypeCode();
        String mtlTypeCode = provinceContractInfoOld.getMtlTypeCode();
        if (!mtlTypeCodeUp.equals(mtlTypeCode)){
            stringBuilder.append("物资类别字段由").append(MtlTypeCodeEnum.getDescByStatus(mtlTypeCode))
                    .append("修改").append(MtlTypeCodeEnum.getDescByStatus(mtlTypeCodeUp)).append("\n");
        }
        String reqTypeUp = param.getReqType();
        String reqType = provinceContractInfoOld.getReqType();
        if (!reqTypeUp.equals(reqType)){
            stringBuilder.append("需求类型字段由").append(ReqTypeEnum.getDescByStatus(reqType))
                    .append("修改为").append(ReqTypeEnum.getDescByStatus(reqTypeUp)).append("\n");
        }
        String reqSecondTypeUp = param.getReqSecondType();
        String reqSecondType = provinceContractInfoOld.getReqSecondType();
        if (StringUtils.isNotEmpty(param.getReqType()) && !"3".equals(param.getReqType())){
            if (StringUtils.isEmpty(param.getReqSecondType())){
                stringBuilder.append("二级需求类型字段由");
                if (StringUtils.isNotEmpty(reqSecondType)){
                    stringBuilder.append(ReqSecondTypeEnum.getDescByStatus(reqSecondType));
                }else {
                    stringBuilder.append("null");
                }
                stringBuilder.append("修改为").append("null").append("\n");
            }else {
                stringBuilder.append("二级需求类型字段由");
                if (StringUtils.isNotEmpty(reqSecondType)){
                    stringBuilder.append(ReqSecondTypeEnum.getDescByStatus(reqSecondType));
                }else {
                    stringBuilder.append("null");
                }
                stringBuilder.append("修改为").append(ReqSecondTypeEnum.getDescByStatus(reqSecondTypeUp)).append("\n");
            }
        }else {
            if (StringUtils.isNotEmpty(reqSecondTypeUp) && StringUtils.isNotEmpty(reqSecondType)){
                if (!reqSecondTypeUp.equals(reqSecondType)){
                    stringBuilder.append("二级需求类型字段由").append(ReqSecondTypeEnum.getDescByStatus(reqSecondType))
                            .append("修改为").append(ReqSecondTypeEnum.getDescByStatus(reqSecondTypeUp)).append("\n");
                }
            }else {
                stringBuilder.append("二级需求类型字段由");
                        if (StringUtils.isNotEmpty(reqSecondType)){
                            stringBuilder.append(ReqSecondTypeEnum.getDescByStatus(reqSecondType));
                        }else {
                            stringBuilder.append("null");
                        }

                stringBuilder.append("修改为").append(ReqSecondTypeEnum.getDescByStatus(reqSecondTypeUp)).append("\n");
            }
        }

        String expTypeUp = param.getExpType();
        String expType = provinceContractInfoOld.getExpType();
        if (!expTypeUp.equals(expType)){
            stringBuilder.append("开支类型字段由").append(ExpTypeEnum.getDescByStatus(expType))
                    .append("修改为").append(ExpTypeEnum.getDescByStatus(expTypeUp)).append("\n");
        }
        String createdNameUp = param.getCreatedName();
        String createdName = provinceContractInfoOld.getCreatedName();
        if (!createdNameUp.equals(createdName)){
            stringBuilder.append("申请人姓名字段由").append(createdName)
                    .append("修改为").append(createdNameUp).append("\n");
        }
        String createdIdUp = param.getCreatedId();
        String createdId = provinceContractInfoOld.getCreatedId();
        if (!createdIdUp.equals(createdId)){
            stringBuilder.append("申请人ID字段由").append(createdId)
                    .append("修改为").append(createdIdUp).append("\n");
        }
        String deptNameUp = param.getDeptName();
        String deptName = provinceContractInfoOld.getDeptName();
        if (!deptNameUp.equals(deptName)){
            stringBuilder.append("申请部门名称字段由").append(deptName)
                    .append("修改为").append(deptNameUp).append("\n");
        }
        String deptCodeUp = param.getDeptCode();
        String deptCode = provinceContractInfoOld.getDeptCode();
        if (!deptCodeUp.equals(deptCode)){
            stringBuilder.append("申请部门代码字段由").append(deptCode)
                    .append("修改为").append(deptCodeUp).append("\n");
        }
        String rcvUserUp = param.getRcvUser();
        String rcvUser = provinceContractInfoOld.getRcvUser();
        if (!rcvUserUp.equals(rcvUser)){
            stringBuilder.append("接收人姓名字段由").append(rcvUser)
                    .append("修改为").append(rcvUserUp).append("\n");
        }
        String rcvUserNumUp = param.getRcvUserNum();
        String rcvUserNum = provinceContractInfoOld.getRcvUserNum();
        if (!rcvUserNumUp.equals(rcvUserNum)){
            stringBuilder.append("接收人员工编号字段由").append(rcvUserNum)
                    .append("修改为").append(rcvUserNumUp).append("\n");
        }
        String rcvContactPhoneUp = param.getRcvContactPhone();
        String rcvContactPhone = provinceContractInfoOld.getRcvContactPhone();
        if (!rcvContactPhoneUp.equals(rcvContactPhone)){
            stringBuilder.append("接收人电话字段由").append(rcvContactPhone)
                    .append("修改为").append(rcvContactPhoneUp).append("\n");
        }
        String rcvSiteAddressUp = param.getRcvSiteAddress();
        String rcvSiteAddress = provinceContractInfoOld.getRcvSiteAddress();
        if (!rcvSiteAddressUp.equals(rcvSiteAddress)){
            stringBuilder.append("接收人地址字段由").append(rcvSiteAddress)
                    .append("修改为").append(rcvSiteAddressUp).append("\n");
        }
        String misBodyUp = param.getMisBody();
        String misBody = provinceContractInfoOld.getMisBody();
        if (!misBodyUp.equals(misBody)){
            stringBuilder.append("MIS主体字段由").append(misBody)
                    .append("修改为").append(misBodyUp).append("\n");
        }
        String orgIdUp = param.getOrgId();
        String orgId = provinceContractInfoOld.getOrgId();
        if (!orgIdUp.equals(orgId)){
            stringBuilder.append("OU标识字段由").append(orgId)
                    .append("修改为").append(orgIdUp).append("\n");
        }
        String organizationCodeUp = param.getOrganizationCode();
        String organizationCode = provinceContractInfoOld.getOrganizationCode();
        if (!organizationCodeUp.equals(organizationCode)){
            stringBuilder.append("发运组织代码字段由").append(organizationCode)
                    .append("修改为").append(organizationCodeUp).append("\n");
        }
        String activityCodeUp = param.getActivityCode();
        String activityCode = provinceContractInfoOld.getActivityCode();
        if (!activityCodeUp.equals(activityCode)){
            stringBuilder.append("业务活动编码字段由").append(activityCode)
                    .append("修改为").append(activityCodeUp).append("\n");
        }
        String reimbursementModeUp = param.getReimbursementMode();
        String reimbursementMode = provinceContractInfoOld.getReimbursementMode();
        if (!reimbursementModeUp.equals(reimbursementMode)){
            stringBuilder.append("报账模式字段由").append(ReimbursementModeEnum.getDescByStatus(reimbursementMode))
                    .append("修改为").append(ReimbursementModeEnum.getDescByStatus(reimbursementModeUp)).append("\n");
        }
        String arrivalTimeModelUp = param.getArrivalTimeModel();
        String arrivalTimeModel = provinceContractInfoOld.getArrivalTimeModel();
        if (!arrivalTimeModelUp.equals(arrivalTimeModel)){
            stringBuilder.append("要求到货时间模式字段由").append(ArrivalTimeModelEnum.getDescByStatus(arrivalTimeModel))
                    .append("修改为").append(ArrivalTimeModelEnum.getDescByStatus(arrivalTimeModelUp)).append("\n");
        }
        String arrivalDaysUp = param.getArrivalDays();
        Integer arrivalDays= provinceContractInfoOld.getArrivalDays();
        if (!Integer.valueOf(arrivalDaysUp).equals(arrivalDays)){
            stringBuilder.append("要求到货天数字段由").append(arrivalDays)
                    .append("修改为").append(arrivalDaysUp).append("\n");
        }
        String itemTypeUp = param.getItemType();
        String itemType= provinceContractInfoOld.getItemType();
        if (!itemTypeUp.equals(itemType)){
            stringBuilder.append("分配类型字段由").append(ItemTypeEnum.getDescByStatus(itemType))
                    .append("修改为").append(ItemTypeEnum.getDescByStatus(itemTypeUp)).append("\n");
        }
        String budgetIdUp = param.getBudgetId();
        String budgetId= provinceContractInfoOld.getBudgetId();
        if (!budgetIdUp.equals(budgetId)){
            stringBuilder.append("预算组合ID字段由").append(budgetId)
                    .append("修改为").append(budgetIdUp).append("\n");
        }
        String budgetYearUp = param.getBudgetYear();
        String budgetYearId= provinceContractInfoOld.getBudgetYear();
        if (!budgetYearUp.equals(budgetYearId)){
            stringBuilder.append("预算年份字段由").append(budgetYearId)
                    .append("修改为").append(budgetYearUp).append("\n");
        }
        String manageActivityUp = param.getManageActivity();
        String manageActivity= provinceContractInfoOld.getManageActivity();
        if (!manageActivityUp.equals(manageActivity)){
            stringBuilder.append("管会业务活动字段由").append(manageActivity)
                    .append("修改为").append(manageActivityUp).append("\n");
        }
        String manageMarketUp = param.getManageMarket();
        String manageMarket= provinceContractInfoOld.getManageMarket();
        if (!manageMarketUp.equals(manageMarket)){
            stringBuilder.append("管会市场维度字段由").append(manageMarket)
                    .append("修改为").append(manageMarketUp).append("\n");
        }
        String manageProductUp = param.getManageProduct();
        String manageProduct= provinceContractInfoOld.getManageProduct();
        if (!manageProductUp.equals(manageProduct)){
            stringBuilder.append("管会产品维度字段由").append(manageProduct)
                    .append("修改为").append(manageProductUp).append("\n");
        }

        logService.recordOperateLog(ModuleEnum.ONLINE_SETTLE_MANAGE.code,
                OnlineSettleOperateEnum.PROVINCE_INFO_OPERATE.code,stringBuilder.toString());

        return new BaseAnswer<>();
    }



    /**
     * 合同信息及物料信息入库
     *
     * @param succeedListDataExcel
     * @param provinceContractSucceedDTO
     */
    public void mappingMaterialOrContractPut(List<ProvinceMaterialSucceedDTO> succeedListDataExcel, List<ProvinceContractSucceedDTO> provinceContractSucceedDTO) {
        //查询当前导入合同物料信息是否已经导入过，未导入直接新增，导入过删除之前，重新导入
        List<ProvinceContractInfo> provinceContractInfos = provinceContractInfoMapper.selectByExample(new ProvinceContractInfoExample().createCriteria()
                .andProvinceContractNoIn(provinceContractSucceedDTO.stream().
                        map(ProvinceContractSucceedDTO::getProvinceContractNo).distinct().collect(Collectors.toList())).example());
        if (CollectionUtils.isNotEmpty(provinceContractInfos)) {
            // 新优化的是多个合同 或者相同合同不同地市 对应物料信息 删除所有合同关联的物料信息
            provinceContractInfos.forEach(provinceContractInfo -> {
                int deleteCount = provinceMaterialInfoMapper.deleteByExample(new ProvinceMaterialInfoExample().createCriteria()
                        .andProvinceContractIdEqualTo(provinceContractInfo.getId()).example());
                log.info("删除合同相关物料信息条数：ProvinceContractId,ProvinceContractNo,deleteCount:{},{},{}",provinceContractInfo.getId()
                        ,provinceContractInfo.getProvinceContractNo(), deleteCount);
                provinceContractInfoMapper.deleteByPrimaryKey(provinceContractInfo.getId());
            });
            ProvinceContractInfo provinceContractInfo = new ProvinceContractInfo();
            packagingUpdateProvinceContractInfo(succeedListDataExcel,provinceContractSucceedDTO, provinceContractInfo, "2");
        }else {
            ProvinceContractInfo provinceContractInfo = new ProvinceContractInfo();
            packagingUpdateProvinceContractInfo(succeedListDataExcel,provinceContractSucceedDTO, provinceContractInfo, "2");
        }

    }

    /**
     * 封装合同基础信息,插入合同 及物料信息入库
     *
     * @param succeedListDataExcel
     * @param provinceContractSucceedDTO
     * @param provinceContractInfo
     * @param operate
     */
    private void packagingUpdateProvinceContractInfo(List<ProvinceMaterialSucceedDTO> succeedListDataExcel, List<ProvinceContractSucceedDTO> provinceContractSucceedDTO, ProvinceContractInfo provinceContractInfo, String operate) {
        //更新  //新优化  直接删除原来的合同 及合同关联的物料信息 直接全都新增
        provinceContractSucceedDTO.forEach(provinceContractSucceedDTO1 -> {
            BeanUtils.copyProperties(provinceContractSucceedDTO1, provinceContractInfo);
            Date date = new Date();
            if ("1".equals(operate)) {
                provinceContractInfo.setUpdateTime(date);
                provinceContractInfoMapper.updateByPrimaryKeySelective(provinceContractInfo);
                packagingProvinceMaterialInfo(succeedListDataExcel, provinceContractInfo.getId());
            } else if ("2".equals(operate)) {
                //新增
                String id = BaseServiceUtils.getId();
                provinceContractInfo.setId(id);
                provinceContractInfo.setCreateTime(date);
                provinceContractInfo.setUpdateTime(date);
                provinceContractInfoMapper.insert(provinceContractInfo);
                packagingProvinceMaterialInfo(succeedListDataExcel, id);
            }
        });
    }

    /**
     * 封装省物料信息
     * @param succeedListDataExcel
     * @param provinceContractId
     */
    private void packagingProvinceMaterialInfo(List<ProvinceMaterialSucceedDTO> succeedListDataExcel, String provinceContractId) {
        List<ProvinceMaterialInfo> collect = succeedListDataExcel.stream().map(provinceMaterialSucceedDTO -> {
            ProvinceMaterialInfo provinceMaterialInfo = new ProvinceMaterialInfo();
            BeanUtils.copyProperties(provinceMaterialSucceedDTO,provinceMaterialInfo);
            provinceMaterialInfo.setId(BaseServiceUtils.getId());
            provinceMaterialInfo.setProvinceContractId(provinceContractId);
            return provinceMaterialInfo;
        }).collect(Collectors.toList());
        provinceMaterialInfoMapper.batchInsert(collect);
    }


    /**
     * 新合同物料新增
     * @param succeedProvinceMaterialList
     * @param provinceContractSucceedList
     */
    public void mappingMaterialOrContractPutNew(List<ProvinceMaterialRequestNewDTO> succeedProvinceMaterialList, List<ProvinceContractImportNewDTO> provinceContractSucceedList) {
        //查询当前导入合同物料信息是否已经导入过，未导入直接新增，导入过删除之前，重新导入(对比若有相同地市且物联网公司合同编码相同的)
        for (ProvinceContractImportNewDTO provinceContractImportNewDTO : provinceContractSucceedList) {
            String cityName = provinceContractImportNewDTO.getCityName();
            String provinceName = provinceContractImportNewDTO.getProvinceName();
            String internetContractCode = provinceContractImportNewDTO.getInternetContractCode();
            String contractType = provinceContractImportNewDTO.getContractType();
            ProvinceContractInfoNewExample provinceContractInfoNewExample = new ProvinceContractInfoNewExample();
            ProvinceContractInfoNewExample.Criteria criteria = provinceContractInfoNewExample.createCriteria();
            criteria.andInternetContractCodeEqualTo(internetContractCode)
                    .andContractTypeEqualTo(contractType);
            if (StringUtils.isNotEmpty(provinceName)){
                criteria.andProvinceNameEqualTo(provinceName);
            }
            if (StringUtils.isNotEmpty(cityName)){
                criteria.andCityNameEqualTo(cityName);
            }
            //正常来说地市和物联网合同编号可以确定唯一性  确定要覆盖的合同信息
            List<ProvinceContractInfoNew> provinceContractInfoNews = provinceContractInfoNewMapper.selectByExample(provinceContractInfoNewExample);
            if (CollectionUtils.isNotEmpty(provinceContractInfoNews)){
                provinceContractInfoNews.forEach(provinceContractInfoNew -> {
                    //删除已存在的合同信息，后再进行新增
                    provinceContractInfoNewMapper.deleteByPrimaryKey(provinceContractInfoNew.getId());
                });
            }
        }

        if (CollectionUtils.isNotEmpty(succeedProvinceMaterialList)){
            //物料不为空，就删除，在重新新增  也通过合同类型  省 地市 物联网编码
            for (ProvinceMaterialRequestNewDTO provinceMaterialRequestNewDTO : succeedProvinceMaterialList) {
                String internetContractCodeMaterial = provinceMaterialRequestNewDTO.getInternetContractCode();
                ProvinceMaterialInfoNewExample provinceMaterialInfoNewExample = new ProvinceMaterialInfoNewExample();
                ProvinceMaterialInfoNewExample.Criteria criteria = provinceMaterialInfoNewExample.createCriteria();
                criteria.andInternetContractCodeEqualTo(internetContractCodeMaterial)
                        .andContractTypeEqualTo(provinceMaterialRequestNewDTO.getContractType());
                if (StringUtils.isNotEmpty(provinceMaterialRequestNewDTO.getProvinceName())){
                    criteria.andProvinceNameEqualTo(provinceMaterialRequestNewDTO.getProvinceName());
                }
                if (StringUtils.isNotEmpty(provinceMaterialRequestNewDTO.getCityName())){
                    criteria.andCityNameEqualTo(provinceMaterialRequestNewDTO.getCityName());
                }
                List<ProvinceMaterialInfoNew> provinceMaterialInfoNews = provinceMaterialInfoNewMapper.selectByExample(provinceMaterialInfoNewExample);
                if (CollectionUtils.isNotEmpty(provinceMaterialInfoNews)){
                    //当前要导入的物料已存在，也删除,后重新覆盖
                    provinceMaterialInfoNews.forEach(provinceMaterialInfoNew -> {
                        provinceMaterialInfoNewMapper.deleteByPrimaryKey(provinceMaterialInfoNew.getId());
                    });
                }
            }
        }
        //删除了历史的 就直接新增吧
        packagingUpdateProvinceContractInfoNew(succeedProvinceMaterialList,provinceContractSucceedList);
    }


    /**
     * 新的新增合同物料信息
     * @param succeedProvinceMaterialList
     * @param provinceContractSucceedList
     */
    private void packagingUpdateProvinceContractInfoNew(List<ProvinceMaterialRequestNewDTO> succeedProvinceMaterialList, List<ProvinceContractImportNewDTO> provinceContractSucceedList) {
        //更新  //新优化  直接删除原来的合同 及合同关联的物料信息 直接全都新增
        Date date = new Date();
        List<ProvinceContractInfoNew> provinceContractInfoNews = new ArrayList<>();
        provinceContractSucceedList.forEach(provinceContractInfoSucceed -> {
            ProvinceContractInfoNew provinceContractInfoNew = new ProvinceContractInfoNew();
            BeanUtils.copyProperties(provinceContractInfoSucceed, provinceContractInfoNew);
                //新增
                String id = BaseServiceUtils.getId();
                provinceContractInfoNew.setId(id);
                provinceContractInfoNew.setCreateTime(date);
                provinceContractInfoNew.setUpdateTime(date);
                if(StringUtils.isEmpty(provinceContractInfoSucceed.getCityCode())){
                    provinceContractInfoNew.setCityCode("");
                }
                if(StringUtils.isEmpty(provinceContractInfoSucceed.getProvinceCode())){
                    provinceContractInfoNew.setProvinceCode("");
                }
            provinceContractInfoNews.add(provinceContractInfoNew);
        });
        provinceContractInfoNewMapper.batchInsert(provinceContractInfoNews);
        packagingProvinceMaterialInfoNew(succeedProvinceMaterialList,date);
        provinceContractInfoNews.forEach(x->{
            //添加成功日志
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("【物料表映射导入】\n").append("新增").append(x.getProvinceName());
            String cityName = x.getCityName();
            if (StringUtils.isNotEmpty(cityName)){
                stringBuilder.append(cityName).append("地市");
            }
            String display = SPUOfferingClassEnum.getDisplay(x.getContractType());
            stringBuilder.append(",").append(display).append("合同类型，省侧信息表和物料表").append("合同编码").append(x.getInternetContractCode());

            String content = stringBuilder.toString();
            logService.recordOperateLog(ModuleEnum.ONLINE_SETTLE_MANAGE.code,
                    OnlineSettleOperateEnum.PROVINCE_INFO_OPERATE.code,content);
        });
    }


    /**
     * 新封装省物料信息
     * @param succeedProvinceMaterialList
     */
    private void packagingProvinceMaterialInfoNew(List<ProvinceMaterialRequestNewDTO> succeedProvinceMaterialList,Date date) {
        List<ProvinceMaterialInfoNew> collect = succeedProvinceMaterialList.stream().map(provinceMaterialSucceedDTO -> {
            ProvinceMaterialInfoNew provinceMaterialInfoNew = new ProvinceMaterialInfoNew();
            BeanUtils.copyProperties(provinceMaterialSucceedDTO,provinceMaterialInfoNew);
            if(StringUtils.isEmpty(provinceMaterialSucceedDTO.getCityCode())){
                provinceMaterialInfoNew.setCityCode("");
            }
            if(StringUtils.isEmpty(provinceMaterialSucceedDTO.getProvinceCode())){
                provinceMaterialInfoNew.setProvinceCode("");
            }
            provinceMaterialInfoNew.setId(BaseServiceUtils.getId());
            provinceMaterialInfoNew.setCreateTime(date);
            provinceMaterialInfoNew.setUpdateTime(date);
            return provinceMaterialInfoNew;
        }).collect(Collectors.toList());
        provinceMaterialInfoNewMapper.batchInsert(collect);
    }


    /**
     *修改校验数据
     * @param data
     * @return
     */
    public String packageFailedPurchaseReason(ProvinceContractInfoNew data){
        String failedReason="";
       /* if (StringUtils.isNotEmpty(data.getProvinceName())){
            HashMap<String, String> provinceNameCodeMap = provinceCityConfig.getProvinceNameCodeMap();
            String provinceName = data.getProvinceName();
            String provinceCode = provinceNameCodeMap.get(provinceName);
            if (StringUtils.isEmpty(provinceCode)){
                failedReason = failedReason.concat(",合同省份请按照商城省份填").concat("_");
            }
        }else {
            failedReason = failedReason.concat(",省份不能为空").concat("_");
        }
        if (StringUtils.isNotEmpty(data.getCityName())){
            HashMap<String, String> cityNameCodeMap = provinceCityConfig.getCityNameCodeMap();
            String cityName = data.getCityName();
            String cityCode = cityNameCodeMap.get(cityName);
            if (StringUtils.isEmpty(cityCode)){
                failedReason = failedReason.concat(",合同地市请按照商城地市名称填").concat("_");
            }
        }*/
        if (StringUtils.isEmpty(data.getContractType())){
            failedReason = failedReason.concat(",合同类型不能为空").concat("_");
        }else {
            String contractType = data.getContractType();
            boolean contains = SPUOfferingClassEnum.contains(contractType);
            if (!contains){
                failedReason = failedReason.concat(",合同类型必须是OneNET独立服务/OnePark独立服务/OneTraffic独立服务/千里眼独立服务/和对讲独立服务/云视讯独立服务").concat("_");
            }

          /*  if (!SPUOfferingClassEnum.A08.getDisplay().equals(contractType) && !SPUOfferingClassEnum.A09.getDisplay().equals(contractType)
                    && !SPUOfferingClassEnum.A10.getDisplay().equals(contractType) && !SPUOfferingClassEnum.A15.getDisplay().equals(contractType)
                    &&!SPUOfferingClassEnum.A16.getDisplay().equals(contractType) && !SPUOfferingClassEnum.A17.getDisplay().equals(contractType)){
                failedReason = failedReason.concat(",合同类型必须是onenet独立服务/onepark独立服务/onetraffic独立服务/千里眼独立服务/和对讲独立服务/云视讯独立服务").concat("_");
            }*/
        }
        if (StringUtils.isEmpty(data.getInternetContractCode())){
            failedReason = failedReason.concat(",物联网公司合同编码不能为空").concat("_");
        }
        if (StringUtils.isEmpty(data.getDeptCode())){
            failedReason = failedReason.concat(",申请部门代码不能为空").concat("_");
        }
        if (StringUtils.isEmpty(data.getDeptName())){
            failedReason = failedReason.concat(",申请部门名称不能为空").concat("_");
        }
        if (StringUtils.isEmpty(data.getCreatedId())){
            failedReason = failedReason.concat(",申请人ID不能为空").concat("_");
        }
        if (StringUtils.isEmpty(data.getCreatedName())){
            failedReason = failedReason.concat(",申请人姓名不能为空").concat("_");
        }
        if (StringUtils.isNotEmpty(data.getMtlTypeCode())){
            if (!MTL_TYPE_CODE_C.equals(data.getMtlTypeCode()) &&  !MTL_TYPE_CODE_P.equals(data.getMtlTypeCode()) && !MTL_TYPE_CODE_O.equals(data.getMtlTypeCode())){
                failedReason = failedReason.concat(",物资类别需为C或P或O").concat("_");
            }else {
                String expType = data.getExpType();
                if (StringUtils.isEmpty(expType)){
                    failedReason = failedReason.concat(",开支类型不能为空").concat("_");
                }else {
                    //开支类型写死了
                    if (EXP_TYPE_OPEX.equals(expType)){
                        if (!MTL_TYPE_CODE_C.equals(data.getMtlTypeCode()) &&  !MTL_TYPE_CODE_P.equals(data.getMtlTypeCode()) && !MTL_TYPE_CODE_O.equals(data.getMtlTypeCode())){
                            failedReason = failedReason.concat(",物资类别需为C或P或O").concat("_");
                        }
                    }else {
                        failedReason = failedReason.concat(",开支类型只能是Opex成本类").concat("_");
                    }
                }
            }
        }else {
            failedReason = failedReason.concat(",物资类别不能为空").concat("_");
        }
        if (StringUtils.isEmpty(data.getContractCode())){
            failedReason = failedReason.concat(",合同编码不能为空").concat("_");
        }

        if (StringUtils.isEmpty(data.getVendorCode())){
            failedReason = failedReason.concat(",供应商编码不能为空").concat("_");
        }
        if (StringUtils.isEmpty(data.getReqType())){
            failedReason = failedReason.concat(",需求类型不能为空").concat("_");
        }
        if (StringUtils.isNotEmpty(data.getReqType()) && "3".equals(data.getReqType())){
            if (StringUtils.isEmpty(data.getReqSecondType())){
                failedReason = failedReason.concat(",二级需求类型不能为空").concat("_");
            }
        }

        if (StringUtils.isEmpty(data.getRcvUserNum())){
            failedReason = failedReason.concat(",接收人员工编号不能为空").concat("_");
        }else {
        /*    if (!"E".equals(data.getRcvUserNum().substring(0,1))){
                failedReason = failedReason.concat(",接收人员工编号E开头").concat("_");
            }
            if (data.getRcvUserNum().substring(1).length()>10){
                failedReason = failedReason.concat(",接收人员工编号E开头+10位数").concat("_");
            }*/
        }
        if (StringUtils.isEmpty(data.getRcvContactPhone())){
            failedReason = failedReason.concat(",接收人电话不能为空").concat("_");
        }else {
            if (!RegexUtil.regexPhone(data.getRcvContactPhone())){
                failedReason = failedReason.concat(",接收人电话格式错误").concat("_");
            }
        }
        if (StringUtils.isEmpty(data.getRcvSiteAddress())){
            failedReason = failedReason.concat(",接收人地址不能为空").concat("_");
        }
        if (StringUtils.isEmpty(data.getMisBody())){
            failedReason = failedReason.concat(",MIS主体不能为空").concat("_");
        }
        if (StringUtils.isEmpty(data.getOrgId())){
            failedReason = failedReason.concat(",OU标识不能为空").concat("_");
        }
        if (StringUtils.isEmpty(data.getExpType())){
            failedReason = failedReason.concat(",开支类型不能为空").concat("_");
        }else {
            if (!EXP_TYPE_OPEX.equals(data.getExpType())){
                failedReason = failedReason.concat(",开支类型为Opex：成本类").concat("_");
            }
        }
        if (StringUtils.isEmpty(data.getReimbursementMode())){
            failedReason = failedReason.concat(",报账模式不能为空").concat("_");
        }else {
            if ( !PROVINCE_MATERIAL_UNIVERSAL_TWO.equals(data.getReimbursementMode())){
                failedReason = failedReason.concat(",报账模式为2：费用池订单").concat("_");
            }
        }
        if (StringUtils.isEmpty(data.getArrivalTimeModel())){
            failedReason = failedReason.concat(",要求到货时间模式不能为空").concat("_");
        }else {
            if (!"EFFECT".equals(data.getArrivalTimeModel())){
                failedReason = failedReason.concat(",要求到货时间模式为： EFFECT：订单生效后X自然日默认").concat("_");
            }
        }
        if (data.getArrivalDays() ==null){
            failedReason = failedReason.concat(",要求到货天数不能为空").concat("_");
        }else {
            Integer arrivalDays = data.getArrivalDays();
            Boolean regexNumber = RegexUtil.regexNumber(String.valueOf(arrivalDays));
            if (!regexNumber){
                failedReason = failedReason.concat(",要求到货天数为数字").concat("_");
            }
        }
        if (StringUtils.isEmpty(data.getOrganizationCode())){
            failedReason = failedReason.concat(",发运组织代码不能为空").concat("_");
        }
        if (StringUtils.isEmpty(data.getItemType())){
            failedReason = failedReason.concat(",分配类型不能为空").concat("_");
        }else {
            if (!PROVINCE_MATERIAL_INVENTORY.equals(data.getItemType()) && !PROVINCE_MATERIAL_EXPENSE.equals(data.getItemType())){
                failedReason = failedReason.concat(",分配类型为INVENTORY或EXPENSE").concat("_");
            }
        }
        if (StringUtils.isNotEmpty(data.getExpType()) && EXP_TYPE_OPEX.equals(data.getExpType())){
            if (StringUtils.isEmpty(data.getBudgetId())){
                failedReason = failedReason.concat(",预算组合ID不能为空").concat("_");
            }
        }
        if (StringUtils.isNotEmpty(data.getExpType()) && EXP_TYPE_OPEX.equals(data.getExpType())){
            if (StringUtils.isEmpty(data.getBudgetYear())){
                failedReason = failedReason.concat(",预算年份不能为空").concat("_");
            }
        }
        if (StringUtils.isNotEmpty(data.getExpType()) && EXP_TYPE_OPEX.equals(data.getExpType())){
            if (StringUtils.isEmpty(data.getActivityCode())){
                failedReason = failedReason.concat(",业务活动编码不能为空").concat("_");
            }
        }
        if ((StringUtils.isNotEmpty(data.getReimbursementMode()) && PROVINCE_MATERIAL_UNIVERSAL_ONE.equals(data.getReimbursementMode()))
                && (StringUtils.isNotEmpty(data.getExpType()) && EXP_TYPE_OPEX.equals(data.getExpType()))
                && (StringUtils.isNotEmpty(data.getItemType()) && PROVINCE_MATERIAL_EXPENSE.equals(data.getItemType()))){
          /*  if (StringUtils.isEmpty(data.getCostCenter())){
                failedReason = failedReason.concat(",成本中心不能为空").concat("_");
            }
            if (StringUtils.isEmpty(data.getExpenseAccount())){
                failedReason = failedReason.concat(",费用科目不能为空").concat("_");
            }
            if (StringUtils.isEmpty(data.getCostSubject())){
                failedReason = failedReason.concat(",子目不能为空").concat("_");
            }*/
        }
        if ((StringUtils.isNotEmpty(data.getExpType()) && EXP_TYPE_OPEX.equals(data.getExpType()))
                && (StringUtils.isNotEmpty(data.getItemType()) && PROVINCE_MATERIAL_EXPENSE.equals(data.getItemType()))){
            if (StringUtils.isEmpty(data.getManageActivity())){
                failedReason = failedReason.concat(",管会业务活动不能为空").concat("_");
            }
            if (StringUtils.isEmpty(data.getManageMarket())){
                failedReason = failedReason.concat(",管会市场维度不能为空").concat("_");
            }
            if (StringUtils.isEmpty(data.getManageProduct())){
                failedReason = failedReason.concat(",管会产品维度不能为空").concat("_");
            }
        }
        return failedReason;
    }

    /**
     * 判断实体对象属性值是否不为空
     *
     * @param object
     * @return
     */
    private boolean checkObjAllFieldsIsNotNull(Object object) {
        try {
            // 挨个获取对象属性值
            for (Field f : object.getClass().getDeclaredFields()) {
                f.setAccessible(true);
                // 有一个属性值不为null，且值不是空字符串，就返回true
                if (f.get(object) != null && StringUtils.isNotBlank(f.get(object).toString())) {
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
