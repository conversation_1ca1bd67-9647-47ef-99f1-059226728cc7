package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class StandardService implements Serializable {
    private String id;

    private String name;

    private Integer productDepartmentId;

    private String realProductName;

    private String productPropertyId;

    private Date createTime;

    private Date updateTime;

    private String remark1;

    private String remark2;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public StandardService withId(String id) {
        this.setId(id);
        return this;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getName() {
        return name;
    }

    public StandardService withName(String name) {
        this.setName(name);
        return this;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Integer getProductDepartmentId() {
        return productDepartmentId;
    }

    public StandardService withProductDepartmentId(Integer productDepartmentId) {
        this.setProductDepartmentId(productDepartmentId);
        return this;
    }

    public void setProductDepartmentId(Integer productDepartmentId) {
        this.productDepartmentId = productDepartmentId;
    }

    public String getRealProductName() {
        return realProductName;
    }

    public StandardService withRealProductName(String realProductName) {
        this.setRealProductName(realProductName);
        return this;
    }

    public void setRealProductName(String realProductName) {
        this.realProductName = realProductName == null ? null : realProductName.trim();
    }

    public String getProductPropertyId() {
        return productPropertyId;
    }

    public StandardService withProductPropertyId(String productPropertyId) {
        this.setProductPropertyId(productPropertyId);
        return this;
    }

    public void setProductPropertyId(String productPropertyId) {
        this.productPropertyId = productPropertyId == null ? null : productPropertyId.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public StandardService withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public StandardService withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark1() {
        return remark1;
    }

    public StandardService withRemark1(String remark1) {
        this.setRemark1(remark1);
        return this;
    }

    public void setRemark1(String remark1) {
        this.remark1 = remark1 == null ? null : remark1.trim();
    }

    public String getRemark2() {
        return remark2;
    }

    public StandardService withRemark2(String remark2) {
        this.setRemark2(remark2);
        return this;
    }

    public void setRemark2(String remark2) {
        this.remark2 = remark2 == null ? null : remark2.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", productDepartmentId=").append(productDepartmentId);
        sb.append(", realProductName=").append(realProductName);
        sb.append(", productPropertyId=").append(productPropertyId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", remark1=").append(remark1);
        sb.append(", remark2=").append(remark2);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        StandardService other = (StandardService) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getProductDepartmentId() == null ? other.getProductDepartmentId() == null : this.getProductDepartmentId().equals(other.getProductDepartmentId()))
            && (this.getRealProductName() == null ? other.getRealProductName() == null : this.getRealProductName().equals(other.getRealProductName()))
            && (this.getProductPropertyId() == null ? other.getProductPropertyId() == null : this.getProductPropertyId().equals(other.getProductPropertyId()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getRemark1() == null ? other.getRemark1() == null : this.getRemark1().equals(other.getRemark1()))
            && (this.getRemark2() == null ? other.getRemark2() == null : this.getRemark2().equals(other.getRemark2()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getProductDepartmentId() == null) ? 0 : getProductDepartmentId().hashCode());
        result = prime * result + ((getRealProductName() == null) ? 0 : getRealProductName().hashCode());
        result = prime * result + ((getProductPropertyId() == null) ? 0 : getProductPropertyId().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getRemark1() == null) ? 0 : getRemark1().hashCode());
        result = prime * result + ((getRemark2() == null) ? 0 : getRemark2().hashCode());
        return result;
    }

    public enum Column {
        id("id", "id", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        productDepartmentId("product_department_id", "productDepartmentId", "INTEGER", false),
        realProductName("real_product_name", "realProductName", "VARCHAR", false),
        productPropertyId("product_property_id", "productPropertyId", "VARCHAR", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        remark1("remark1", "remark1", "VARCHAR", false),
        remark2("remark2", "remark2", "VARCHAR", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}