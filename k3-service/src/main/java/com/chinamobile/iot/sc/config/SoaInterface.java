package com.chinamobile.iot.sc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @AUTHOR: HWF
 * @DATE: 2025/3/24
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "soa.interface")
public class SoaInterface {

    private Integer connectionTimeout;

    private Integer receiveTimeout;
}
