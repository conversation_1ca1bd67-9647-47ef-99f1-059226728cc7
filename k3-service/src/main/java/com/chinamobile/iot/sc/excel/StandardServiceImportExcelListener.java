package com.chinamobile.iot.sc.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.chinamobile.iot.sc.dao.DepartmentMapper;
import com.chinamobile.iot.sc.dao.StandardServiceMapper;
import com.chinamobile.iot.sc.dao.StandardServiceMapperExt;
import com.chinamobile.iot.sc.exceptions.BaseErrorConstant;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.pojo.dto.StandardServiceFailDTO;
import com.chinamobile.iot.sc.pojo.entity.Department;
import com.chinamobile.iot.sc.pojo.entity.StandardService;
import com.chinamobile.iot.sc.pojo.param.EditStandardServiceParam;
import com.chinamobile.iot.sc.service.StandardServiceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.IMPORT_TEMPLATE_ERROR;

/**
 * <AUTHOR> xiemaohua
 * @date : 2024/11/4 14:31
 * @description: StandardServiceImportExcelListenne
 **/
@Slf4j
public class StandardServiceImportExcelListener extends AnalysisEventListener<StandardServiceImportExcel> {

    private StandardServiceMapperExt standardServiceMapperExt;

    private StandardServiceMapper standardServiceMapper;

    private DepartmentMapper departmentMapper;

    public StandardServiceImportExcelListener(StandardServiceMapperExt standardServiceMapperExt, StandardServiceMapper standardServiceMapper, DepartmentMapper departmentMapper) {
        this.standardServiceMapperExt = standardServiceMapperExt;
        this.standardServiceMapper = standardServiceMapper;
        this.departmentMapper = departmentMapper;
    }

    private List<EditStandardServiceParam> editStandardServiceSuccessList = new ArrayList<>();

    private List<StandardServiceFailDTO> standardServiceFailDTOList = new ArrayList<>();

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        if (!"标准服务编码".equals(headMap.get(0)) || !"标准服务名称".equals(headMap.get(1)) || !"产品部门".equals(headMap.get(2)) || !"实质性产品名称".equals(headMap.get(3))
                || !"产品属性".equals(headMap.get(4)) || !"备注1".equals(headMap.get(5)) || !"备注2".equals(headMap.get(6))){
            throw new BusinessException(IMPORT_TEMPLATE_ERROR);
        }
    }

    @Override
    public void invoke(StandardServiceImportExcel data, AnalysisContext context) {
        int currentRow = context.readRowHolder().getRowIndex()+1;
        String failedReason="";
        String id = data.getId();
        String name = data.getName();
        String departmentName = data.getDepartmentName();
        String productProperty = data.getProductProperty();
        String realProductName = data.getRealProductName();
        String remark1 = data.getRemark1();
        String remark2 = data.getRemark2();
        Integer departmentId = null;
        if (StringUtils.isEmpty(id)){
            failedReason = failedReason+ "标准服务编码不能为空！".concat("错误行"+currentRow);
        }else {
            StandardService standardService = standardServiceMapper.selectByPrimaryKey(id);
            if (!Optional.ofNullable(standardService).isPresent()){
                failedReason = failedReason+ "标准服务编码不存在！".concat("错误行"+currentRow);
            }
        }

        if (StringUtils.isEmpty(name)){
            failedReason = failedReason+ "标准服务名称不能为空！".concat("错误行"+currentRow);
        }

        if (StringUtils.isEmpty(departmentName)){
            failedReason = failedReason+ "产品部门不能为空！".concat("错误行"+currentRow);
        }else {
            Department department= departmentMapper.selectOne(new QueryWrapper<Department>().lambda().eq(Department::getShortName, departmentName));
            if (department == null) {
                failedReason = failedReason+ "产品部门不存在！".concat("错误行"+currentRow);
            }else {
                departmentId = department.getId();
            }
        }

        if (StringUtils.isEmpty(realProductName)){
            failedReason = failedReason+ "实质性产品名称不能为空！".concat("错误行"+currentRow);
        }else {
            List<StandardService> standardServiceDTOS = standardServiceMapperExt.selectList(new QueryWrapper<StandardService>().lambda().eq(StandardService::getRealProductName, realProductName).eq(StandardService::getName, name).ne(StandardService::getId, id));
            //根据产品要求：标准服务名称可以重复，但是同一个标准服务下  实质产品名称不能重复
            if (!standardServiceDTOS.isEmpty()) {
                failedReason = failedReason+ "标准产品[" + name + "]对应的实质产品名称[" + realProductName + "]已存在".concat("错误行"+currentRow);
            }
        }

        if (StringUtils.isEmpty(productProperty)){
            failedReason = failedReason+ "产品属性不能为空！".concat("错误行"+currentRow);
        }else {
            if (!"自研".equals(productProperty) && !"生态".equals(productProperty)){
                failedReason = failedReason+ "产品属性为生态或自研！".concat("错误行"+currentRow);
            }else {
                if ("自研".equals(productProperty)){
                    productProperty ="101";
                }else {
                    productProperty ="102";
                }
            }
        }


        if (StringUtils.isEmpty(failedReason)){
            EditStandardServiceParam param =new EditStandardServiceParam();
            param.setId(id);
            param.setName(name);
            param.setDepartmentId(departmentId);
            param.setRealProductName(realProductName);
            param.setProductPropertyId(productProperty);
            param.setRemark1(remark1);
            param.setRemark2(remark2);
            editStandardServiceSuccessList.add(param);
        }else {
            StandardServiceFailDTO failDTO = new StandardServiceFailDTO();
             BeanUtils.copyProperties(data,failDTO);
            failDTO.setFailedReason(failedReason);
            standardServiceFailDTOList.add(failDTO);
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("批量导入标准用户修改导入所有数据解析完成！");
    }

    public List<EditStandardServiceParam> getEditStandardServiceSuccessList() {
        return editStandardServiceSuccessList;
    }

    public List<StandardServiceFailDTO> getStandardServiceFailDTOList() {
        return standardServiceFailDTOList;
    }
}
