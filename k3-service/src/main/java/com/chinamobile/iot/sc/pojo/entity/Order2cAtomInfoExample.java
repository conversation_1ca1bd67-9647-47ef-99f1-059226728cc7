package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Order2cAtomInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public Order2cAtomInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Order2cAtomInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    public Order2cAtomInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    public static Criteria newAndCreateCriteria() {
        Order2cAtomInfoExample example = new Order2cAtomInfoExample();
        return example.createCriteria();
    }

    public Order2cAtomInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    public Order2cAtomInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("order_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("order_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("order_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("order_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("order_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("order_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNull() {
            addCriterion("order_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNotNull() {
            addCriterion("order_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeEqualTo(String value) {
            addCriterion("order_type =", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("order_type = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotEqualTo(String value) {
            addCriterion("order_type <>", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("order_type <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThan(String value) {
            addCriterion("order_type >", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("order_type > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanOrEqualTo(String value) {
            addCriterion("order_type >=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("order_type >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThan(String value) {
            addCriterion("order_type <", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("order_type < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanOrEqualTo(String value) {
            addCriterion("order_type <=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("order_type <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderTypeLike(String value) {
            addCriterion("order_type like", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotLike(String value) {
            addCriterion("order_type not like", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIn(List<String> values) {
            addCriterion("order_type in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotIn(List<String> values) {
            addCriterion("order_type not in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeBetween(String value1, String value2) {
            addCriterion("order_type between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotBetween(String value1, String value2) {
            addCriterion("order_type not between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeIsNull() {
            addCriterion("spu_offering_code is null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeIsNotNull() {
            addCriterion("spu_offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeEqualTo(String value) {
            addCriterion("spu_offering_code =", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotEqualTo(String value) {
            addCriterion("spu_offering_code <>", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThan(String value) {
            addCriterion("spu_offering_code >", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("spu_offering_code >=", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThan(String value) {
            addCriterion("spu_offering_code <", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("spu_offering_code <=", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLike(String value) {
            addCriterion("spu_offering_code like", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotLike(String value) {
            addCriterion("spu_offering_code not like", value, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeIn(List<String> values) {
            addCriterion("spu_offering_code in", values, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotIn(List<String> values) {
            addCriterion("spu_offering_code not in", values, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeBetween(String value1, String value2) {
            addCriterion("spu_offering_code between", value1, value2, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("spu_offering_code not between", value1, value2, "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeIsNull() {
            addCriterion("sku_offering_code is null");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeIsNotNull() {
            addCriterion("sku_offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeEqualTo(String value) {
            addCriterion("sku_offering_code =", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeNotEqualTo(String value) {
            addCriterion("sku_offering_code <>", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeGreaterThan(String value) {
            addCriterion("sku_offering_code >", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sku_offering_code >=", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeLessThan(String value) {
            addCriterion("sku_offering_code <", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("sku_offering_code <=", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeLike(String value) {
            addCriterion("sku_offering_code like", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeNotLike(String value) {
            addCriterion("sku_offering_code not like", value, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeIn(List<String> values) {
            addCriterion("sku_offering_code in", values, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeNotIn(List<String> values) {
            addCriterion("sku_offering_code not in", values, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeBetween(String value1, String value2) {
            addCriterion("sku_offering_code between", value1, value2, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("sku_offering_code not between", value1, value2, "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameIsNull() {
            addCriterion("sku_offering_name is null");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameIsNotNull() {
            addCriterion("sku_offering_name is not null");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameEqualTo(String value) {
            addCriterion("sku_offering_name =", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameNotEqualTo(String value) {
            addCriterion("sku_offering_name <>", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameGreaterThan(String value) {
            addCriterion("sku_offering_name >", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameGreaterThanOrEqualTo(String value) {
            addCriterion("sku_offering_name >=", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameLessThan(String value) {
            addCriterion("sku_offering_name <", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameLessThanOrEqualTo(String value) {
            addCriterion("sku_offering_name <=", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameLike(String value) {
            addCriterion("sku_offering_name like", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameNotLike(String value) {
            addCriterion("sku_offering_name not like", value, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameIn(List<String> values) {
            addCriterion("sku_offering_name in", values, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameNotIn(List<String> values) {
            addCriterion("sku_offering_name not in", values, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameBetween(String value1, String value2) {
            addCriterion("sku_offering_name between", value1, value2, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameNotBetween(String value1, String value2) {
            addCriterion("sku_offering_name not between", value1, value2, "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andSkuQuantityIsNull() {
            addCriterion("sku_quantity is null");
            return (Criteria) this;
        }

        public Criteria andSkuQuantityIsNotNull() {
            addCriterion("sku_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andSkuQuantityEqualTo(Long value) {
            addCriterion("sku_quantity =", value, "skuQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuQuantityEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_quantity = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuQuantityNotEqualTo(Long value) {
            addCriterion("sku_quantity <>", value, "skuQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuQuantityNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_quantity <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuQuantityGreaterThan(Long value) {
            addCriterion("sku_quantity >", value, "skuQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuQuantityGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_quantity > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuQuantityGreaterThanOrEqualTo(Long value) {
            addCriterion("sku_quantity >=", value, "skuQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuQuantityGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_quantity >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuQuantityLessThan(Long value) {
            addCriterion("sku_quantity <", value, "skuQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuQuantityLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_quantity < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuQuantityLessThanOrEqualTo(Long value) {
            addCriterion("sku_quantity <=", value, "skuQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuQuantityLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_quantity <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuQuantityIn(List<Long> values) {
            addCriterion("sku_quantity in", values, "skuQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuQuantityNotIn(List<Long> values) {
            addCriterion("sku_quantity not in", values, "skuQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuQuantityBetween(Long value1, Long value2) {
            addCriterion("sku_quantity between", value1, value2, "skuQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuQuantityNotBetween(Long value1, Long value2) {
            addCriterion("sku_quantity not between", value1, value2, "skuQuantity");
            return (Criteria) this;
        }

        public Criteria andSkuPriceIsNull() {
            addCriterion("sku_price is null");
            return (Criteria) this;
        }

        public Criteria andSkuPriceIsNotNull() {
            addCriterion("sku_price is not null");
            return (Criteria) this;
        }

        public Criteria andSkuPriceEqualTo(Long value) {
            addCriterion("sku_price =", value, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andSkuPriceEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuPriceNotEqualTo(Long value) {
            addCriterion("sku_price <>", value, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andSkuPriceNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuPriceGreaterThan(Long value) {
            addCriterion("sku_price >", value, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andSkuPriceGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuPriceGreaterThanOrEqualTo(Long value) {
            addCriterion("sku_price >=", value, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andSkuPriceGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuPriceLessThan(Long value) {
            addCriterion("sku_price <", value, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andSkuPriceLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuPriceLessThanOrEqualTo(Long value) {
            addCriterion("sku_price <=", value, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andSkuPriceLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuPriceIn(List<Long> values) {
            addCriterion("sku_price in", values, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andSkuPriceNotIn(List<Long> values) {
            addCriterion("sku_price not in", values, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andSkuPriceBetween(Long value1, Long value2) {
            addCriterion("sku_price between", value1, value2, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andSkuPriceNotBetween(Long value1, Long value2) {
            addCriterion("sku_price not between", value1, value2, "skuPrice");
            return (Criteria) this;
        }

        public Criteria andMarketNameIsNull() {
            addCriterion("market_name is null");
            return (Criteria) this;
        }

        public Criteria andMarketNameIsNotNull() {
            addCriterion("market_name is not null");
            return (Criteria) this;
        }

        public Criteria andMarketNameEqualTo(String value) {
            addCriterion("market_name =", value, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("market_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketNameNotEqualTo(String value) {
            addCriterion("market_name <>", value, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("market_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketNameGreaterThan(String value) {
            addCriterion("market_name >", value, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("market_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketNameGreaterThanOrEqualTo(String value) {
            addCriterion("market_name >=", value, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("market_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketNameLessThan(String value) {
            addCriterion("market_name <", value, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("market_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketNameLessThanOrEqualTo(String value) {
            addCriterion("market_name <=", value, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("market_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketNameLike(String value) {
            addCriterion("market_name like", value, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameNotLike(String value) {
            addCriterion("market_name not like", value, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameIn(List<String> values) {
            addCriterion("market_name in", values, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameNotIn(List<String> values) {
            addCriterion("market_name not in", values, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameBetween(String value1, String value2) {
            addCriterion("market_name between", value1, value2, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketNameNotBetween(String value1, String value2) {
            addCriterion("market_name not between", value1, value2, "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketCodeIsNull() {
            addCriterion("market_code is null");
            return (Criteria) this;
        }

        public Criteria andMarketCodeIsNotNull() {
            addCriterion("market_code is not null");
            return (Criteria) this;
        }

        public Criteria andMarketCodeEqualTo(String value) {
            addCriterion("market_code =", value, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("market_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketCodeNotEqualTo(String value) {
            addCriterion("market_code <>", value, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("market_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketCodeGreaterThan(String value) {
            addCriterion("market_code >", value, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("market_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketCodeGreaterThanOrEqualTo(String value) {
            addCriterion("market_code >=", value, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("market_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketCodeLessThan(String value) {
            addCriterion("market_code <", value, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("market_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketCodeLessThanOrEqualTo(String value) {
            addCriterion("market_code <=", value, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("market_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMarketCodeLike(String value) {
            addCriterion("market_code like", value, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeNotLike(String value) {
            addCriterion("market_code not like", value, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeIn(List<String> values) {
            addCriterion("market_code in", values, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeNotIn(List<String> values) {
            addCriterion("market_code not in", values, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeBetween(String value1, String value2) {
            addCriterion("market_code between", value1, value2, "marketCode");
            return (Criteria) this;
        }

        public Criteria andMarketCodeNotBetween(String value1, String value2) {
            addCriterion("market_code not between", value1, value2, "marketCode");
            return (Criteria) this;
        }

        public Criteria andSupplierNameIsNull() {
            addCriterion("supplier_name is null");
            return (Criteria) this;
        }

        public Criteria andSupplierNameIsNotNull() {
            addCriterion("supplier_name is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierNameEqualTo(String value) {
            addCriterion("supplier_name =", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("supplier_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotEqualTo(String value) {
            addCriterion("supplier_name <>", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("supplier_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierNameGreaterThan(String value) {
            addCriterion("supplier_name >", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("supplier_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierNameGreaterThanOrEqualTo(String value) {
            addCriterion("supplier_name >=", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("supplier_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierNameLessThan(String value) {
            addCriterion("supplier_name <", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("supplier_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierNameLessThanOrEqualTo(String value) {
            addCriterion("supplier_name <=", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("supplier_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSupplierNameLike(String value) {
            addCriterion("supplier_name like", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotLike(String value) {
            addCriterion("supplier_name not like", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameIn(List<String> values) {
            addCriterion("supplier_name in", values, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotIn(List<String> values) {
            addCriterion("supplier_name not in", values, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameBetween(String value1, String value2) {
            addCriterion("supplier_name between", value1, value2, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotBetween(String value1, String value2) {
            addCriterion("supplier_name not between", value1, value2, "supplierName");
            return (Criteria) this;
        }

        public Criteria andColorIsNull() {
            addCriterion("color is null");
            return (Criteria) this;
        }

        public Criteria andColorIsNotNull() {
            addCriterion("color is not null");
            return (Criteria) this;
        }

        public Criteria andColorEqualTo(String value) {
            addCriterion("color =", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("color = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorNotEqualTo(String value) {
            addCriterion("color <>", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("color <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorGreaterThan(String value) {
            addCriterion("color >", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("color > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorGreaterThanOrEqualTo(String value) {
            addCriterion("color >=", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("color >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorLessThan(String value) {
            addCriterion("color <", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("color < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorLessThanOrEqualTo(String value) {
            addCriterion("color <=", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("color <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andColorLike(String value) {
            addCriterion("color like", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotLike(String value) {
            addCriterion("color not like", value, "color");
            return (Criteria) this;
        }

        public Criteria andColorIn(List<String> values) {
            addCriterion("color in", values, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotIn(List<String> values) {
            addCriterion("color not in", values, "color");
            return (Criteria) this;
        }

        public Criteria andColorBetween(String value1, String value2) {
            addCriterion("color between", value1, value2, "color");
            return (Criteria) this;
        }

        public Criteria andColorNotBetween(String value1, String value2) {
            addCriterion("color not between", value1, value2, "color");
            return (Criteria) this;
        }

        public Criteria andModelIsNull() {
            addCriterion("model is null");
            return (Criteria) this;
        }

        public Criteria andModelIsNotNull() {
            addCriterion("model is not null");
            return (Criteria) this;
        }

        public Criteria andModelEqualTo(String value) {
            addCriterion("model =", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("model = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelNotEqualTo(String value) {
            addCriterion("model <>", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("model <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelGreaterThan(String value) {
            addCriterion("model >", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("model > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualTo(String value) {
            addCriterion("model >=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("model >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLessThan(String value) {
            addCriterion("model <", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("model < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualTo(String value) {
            addCriterion("model <=", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("model <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andModelLike(String value) {
            addCriterion("model like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotLike(String value) {
            addCriterion("model not like", value, "model");
            return (Criteria) this;
        }

        public Criteria andModelIn(List<String> values) {
            addCriterion("model in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotIn(List<String> values) {
            addCriterion("model not in", values, "model");
            return (Criteria) this;
        }

        public Criteria andModelBetween(String value1, String value2) {
            addCriterion("model between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andModelNotBetween(String value1, String value2) {
            addCriterion("model not between", value1, value2, "model");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassIsNull() {
            addCriterion("atom_offering_class is null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassIsNotNull() {
            addCriterion("atom_offering_class is not null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassEqualTo(String value) {
            addCriterion("atom_offering_class =", value, "atomOfferingClass");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_class = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassNotEqualTo(String value) {
            addCriterion("atom_offering_class <>", value, "atomOfferingClass");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_class <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassGreaterThan(String value) {
            addCriterion("atom_offering_class >", value, "atomOfferingClass");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_class > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassGreaterThanOrEqualTo(String value) {
            addCriterion("atom_offering_class >=", value, "atomOfferingClass");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_class >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassLessThan(String value) {
            addCriterion("atom_offering_class <", value, "atomOfferingClass");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_class < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassLessThanOrEqualTo(String value) {
            addCriterion("atom_offering_class <=", value, "atomOfferingClass");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_class <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassLike(String value) {
            addCriterion("atom_offering_class like", value, "atomOfferingClass");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassNotLike(String value) {
            addCriterion("atom_offering_class not like", value, "atomOfferingClass");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassIn(List<String> values) {
            addCriterion("atom_offering_class in", values, "atomOfferingClass");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassNotIn(List<String> values) {
            addCriterion("atom_offering_class not in", values, "atomOfferingClass");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassBetween(String value1, String value2) {
            addCriterion("atom_offering_class between", value1, value2, "atomOfferingClass");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassNotBetween(String value1, String value2) {
            addCriterion("atom_offering_class not between", value1, value2, "atomOfferingClass");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeIsNull() {
            addCriterion("atom_offering_code is null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeIsNotNull() {
            addCriterion("atom_offering_code is not null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeEqualTo(String value) {
            addCriterion("atom_offering_code =", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeNotEqualTo(String value) {
            addCriterion("atom_offering_code <>", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeGreaterThan(String value) {
            addCriterion("atom_offering_code >", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeGreaterThanOrEqualTo(String value) {
            addCriterion("atom_offering_code >=", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeLessThan(String value) {
            addCriterion("atom_offering_code <", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeLessThanOrEqualTo(String value) {
            addCriterion("atom_offering_code <=", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeLike(String value) {
            addCriterion("atom_offering_code like", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeNotLike(String value) {
            addCriterion("atom_offering_code not like", value, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeIn(List<String> values) {
            addCriterion("atom_offering_code in", values, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeNotIn(List<String> values) {
            addCriterion("atom_offering_code not in", values, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeBetween(String value1, String value2) {
            addCriterion("atom_offering_code between", value1, value2, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeNotBetween(String value1, String value2) {
            addCriterion("atom_offering_code not between", value1, value2, "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameIsNull() {
            addCriterion("atom_offering_name is null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameIsNotNull() {
            addCriterion("atom_offering_name is not null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameEqualTo(String value) {
            addCriterion("atom_offering_name =", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameNotEqualTo(String value) {
            addCriterion("atom_offering_name <>", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameGreaterThan(String value) {
            addCriterion("atom_offering_name >", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameGreaterThanOrEqualTo(String value) {
            addCriterion("atom_offering_name >=", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameLessThan(String value) {
            addCriterion("atom_offering_name <", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameLessThanOrEqualTo(String value) {
            addCriterion("atom_offering_name <=", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameLike(String value) {
            addCriterion("atom_offering_name like", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameNotLike(String value) {
            addCriterion("atom_offering_name not like", value, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameIn(List<String> values) {
            addCriterion("atom_offering_name in", values, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameNotIn(List<String> values) {
            addCriterion("atom_offering_name not in", values, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameBetween(String value1, String value2) {
            addCriterion("atom_offering_name between", value1, value2, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameNotBetween(String value1, String value2) {
            addCriterion("atom_offering_name not between", value1, value2, "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andDeductPriceIsNull() {
            addCriterion("deduct_price is null");
            return (Criteria) this;
        }

        public Criteria andDeductPriceIsNotNull() {
            addCriterion("deduct_price is not null");
            return (Criteria) this;
        }

        public Criteria andDeductPriceEqualTo(String value) {
            addCriterion("deduct_price =", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("deduct_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeductPriceNotEqualTo(String value) {
            addCriterion("deduct_price <>", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("deduct_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeductPriceGreaterThan(String value) {
            addCriterion("deduct_price >", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("deduct_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeductPriceGreaterThanOrEqualTo(String value) {
            addCriterion("deduct_price >=", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("deduct_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeductPriceLessThan(String value) {
            addCriterion("deduct_price <", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("deduct_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeductPriceLessThanOrEqualTo(String value) {
            addCriterion("deduct_price <=", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("deduct_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDeductPriceLike(String value) {
            addCriterion("deduct_price like", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceNotLike(String value) {
            addCriterion("deduct_price not like", value, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceIn(List<String> values) {
            addCriterion("deduct_price in", values, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceNotIn(List<String> values) {
            addCriterion("deduct_price not in", values, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceBetween(String value1, String value2) {
            addCriterion("deduct_price between", value1, value2, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andDeductPriceNotBetween(String value1, String value2) {
            addCriterion("deduct_price not between", value1, value2, "deductPrice");
            return (Criteria) this;
        }

        public Criteria andAtomPriceIsNull() {
            addCriterion("atom_price is null");
            return (Criteria) this;
        }

        public Criteria andAtomPriceIsNotNull() {
            addCriterion("atom_price is not null");
            return (Criteria) this;
        }

        public Criteria andAtomPriceEqualTo(Long value) {
            addCriterion("atom_price =", value, "atomPrice");
            return (Criteria) this;
        }

        public Criteria andAtomPriceEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomPriceNotEqualTo(Long value) {
            addCriterion("atom_price <>", value, "atomPrice");
            return (Criteria) this;
        }

        public Criteria andAtomPriceNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomPriceGreaterThan(Long value) {
            addCriterion("atom_price >", value, "atomPrice");
            return (Criteria) this;
        }

        public Criteria andAtomPriceGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomPriceGreaterThanOrEqualTo(Long value) {
            addCriterion("atom_price >=", value, "atomPrice");
            return (Criteria) this;
        }

        public Criteria andAtomPriceGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomPriceLessThan(Long value) {
            addCriterion("atom_price <", value, "atomPrice");
            return (Criteria) this;
        }

        public Criteria andAtomPriceLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomPriceLessThanOrEqualTo(Long value) {
            addCriterion("atom_price <=", value, "atomPrice");
            return (Criteria) this;
        }

        public Criteria andAtomPriceLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomPriceIn(List<Long> values) {
            addCriterion("atom_price in", values, "atomPrice");
            return (Criteria) this;
        }

        public Criteria andAtomPriceNotIn(List<Long> values) {
            addCriterion("atom_price not in", values, "atomPrice");
            return (Criteria) this;
        }

        public Criteria andAtomPriceBetween(Long value1, Long value2) {
            addCriterion("atom_price between", value1, value2, "atomPrice");
            return (Criteria) this;
        }

        public Criteria andAtomPriceNotBetween(Long value1, Long value2) {
            addCriterion("atom_price not between", value1, value2, "atomPrice");
            return (Criteria) this;
        }

        public Criteria andAtomSettlePriceIsNull() {
            addCriterion("atom_settle_price is null");
            return (Criteria) this;
        }

        public Criteria andAtomSettlePriceIsNotNull() {
            addCriterion("atom_settle_price is not null");
            return (Criteria) this;
        }

        public Criteria andAtomSettlePriceEqualTo(Long value) {
            addCriterion("atom_settle_price =", value, "atomSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSettlePriceEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_settle_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomSettlePriceNotEqualTo(Long value) {
            addCriterion("atom_settle_price <>", value, "atomSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSettlePriceNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_settle_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomSettlePriceGreaterThan(Long value) {
            addCriterion("atom_settle_price >", value, "atomSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSettlePriceGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_settle_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomSettlePriceGreaterThanOrEqualTo(Long value) {
            addCriterion("atom_settle_price >=", value, "atomSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSettlePriceGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_settle_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomSettlePriceLessThan(Long value) {
            addCriterion("atom_settle_price <", value, "atomSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSettlePriceLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_settle_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomSettlePriceLessThanOrEqualTo(Long value) {
            addCriterion("atom_settle_price <=", value, "atomSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSettlePriceLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_settle_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomSettlePriceIn(List<Long> values) {
            addCriterion("atom_settle_price in", values, "atomSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSettlePriceNotIn(List<Long> values) {
            addCriterion("atom_settle_price not in", values, "atomSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSettlePriceBetween(Long value1, Long value2) {
            addCriterion("atom_settle_price between", value1, value2, "atomSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAtomSettlePriceNotBetween(Long value1, Long value2) {
            addCriterion("atom_settle_price not between", value1, value2, "atomSettlePrice");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityIsNull() {
            addCriterion("atom_quantity is null");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityIsNotNull() {
            addCriterion("atom_quantity is not null");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityEqualTo(Long value) {
            addCriterion("atom_quantity =", value, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_quantity = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomQuantityNotEqualTo(Long value) {
            addCriterion("atom_quantity <>", value, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_quantity <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomQuantityGreaterThan(Long value) {
            addCriterion("atom_quantity >", value, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_quantity > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomQuantityGreaterThanOrEqualTo(Long value) {
            addCriterion("atom_quantity >=", value, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_quantity >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomQuantityLessThan(Long value) {
            addCriterion("atom_quantity <", value, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_quantity < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomQuantityLessThanOrEqualTo(Long value) {
            addCriterion("atom_quantity <=", value, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_quantity <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomQuantityIn(List<Long> values) {
            addCriterion("atom_quantity in", values, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityNotIn(List<Long> values) {
            addCriterion("atom_quantity not in", values, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityBetween(Long value1, Long value2) {
            addCriterion("atom_quantity between", value1, value2, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andAtomQuantityNotBetween(Long value1, Long value2) {
            addCriterion("atom_quantity not between", value1, value2, "atomQuantity");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNull() {
            addCriterion("order_status is null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNotNull() {
            addCriterion("order_status is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusEqualTo(Integer value) {
            addCriterion("order_status =", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("order_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotEqualTo(Integer value) {
            addCriterion("order_status <>", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("order_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThan(Integer value) {
            addCriterion("order_status >", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("order_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_status >=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("order_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThan(Integer value) {
            addCriterion("order_status <", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("order_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThanOrEqualTo(Integer value) {
            addCriterion("order_status <=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("order_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOrderStatusIn(List<Integer> values) {
            addCriterion("order_status in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotIn(List<Integer> values) {
            addCriterion("order_status not in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusBetween(Integer value1, Integer value2) {
            addCriterion("order_status between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("order_status not between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIsNull() {
            addCriterion("cooperator_id is null");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIsNotNull() {
            addCriterion("cooperator_id is not null");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdEqualTo(String value) {
            addCriterion("cooperator_id =", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotEqualTo(String value) {
            addCriterion("cooperator_id <>", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThan(String value) {
            addCriterion("cooperator_id >", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanOrEqualTo(String value) {
            addCriterion("cooperator_id >=", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThan(String value) {
            addCriterion("cooperator_id <", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanOrEqualTo(String value) {
            addCriterion("cooperator_id <=", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLike(String value) {
            addCriterion("cooperator_id like", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotLike(String value) {
            addCriterion("cooperator_id not like", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIn(List<String> values) {
            addCriterion("cooperator_id in", values, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotIn(List<String> values) {
            addCriterion("cooperator_id not in", values, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdBetween(String value1, String value2) {
            addCriterion("cooperator_id between", value1, value2, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotBetween(String value1, String value2) {
            addCriterion("cooperator_id not between", value1, value2, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdIsNull() {
            addCriterion("finish_cooperator_id is null");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdIsNotNull() {
            addCriterion("finish_cooperator_id is not null");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdEqualTo(String value) {
            addCriterion("finish_cooperator_id =", value, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("finish_cooperator_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdNotEqualTo(String value) {
            addCriterion("finish_cooperator_id <>", value, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("finish_cooperator_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdGreaterThan(String value) {
            addCriterion("finish_cooperator_id >", value, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("finish_cooperator_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdGreaterThanOrEqualTo(String value) {
            addCriterion("finish_cooperator_id >=", value, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("finish_cooperator_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdLessThan(String value) {
            addCriterion("finish_cooperator_id <", value, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("finish_cooperator_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdLessThanOrEqualTo(String value) {
            addCriterion("finish_cooperator_id <=", value, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("finish_cooperator_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdLike(String value) {
            addCriterion("finish_cooperator_id like", value, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdNotLike(String value) {
            addCriterion("finish_cooperator_id not like", value, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdIn(List<String> values) {
            addCriterion("finish_cooperator_id in", values, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdNotIn(List<String> values) {
            addCriterion("finish_cooperator_id not in", values, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdBetween(String value1, String value2) {
            addCriterion("finish_cooperator_id between", value1, value2, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdNotBetween(String value1, String value2) {
            addCriterion("finish_cooperator_id not between", value1, value2, "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andBeIdIsNull() {
            addCriterion("be_id is null");
            return (Criteria) this;
        }

        public Criteria andBeIdIsNotNull() {
            addCriterion("be_id is not null");
            return (Criteria) this;
        }

        public Criteria andBeIdEqualTo(String value) {
            addCriterion("be_id =", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("be_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdNotEqualTo(String value) {
            addCriterion("be_id <>", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("be_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThan(String value) {
            addCriterion("be_id >", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("be_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanOrEqualTo(String value) {
            addCriterion("be_id >=", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("be_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLessThan(String value) {
            addCriterion("be_id <", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("be_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanOrEqualTo(String value) {
            addCriterion("be_id <=", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("be_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBeIdLike(String value) {
            addCriterion("be_id like", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotLike(String value) {
            addCriterion("be_id not like", value, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdIn(List<String> values) {
            addCriterion("be_id in", values, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotIn(List<String> values) {
            addCriterion("be_id not in", values, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdBetween(String value1, String value2) {
            addCriterion("be_id between", value1, value2, "beId");
            return (Criteria) this;
        }

        public Criteria andBeIdNotBetween(String value1, String value2) {
            addCriterion("be_id not between", value1, value2, "beId");
            return (Criteria) this;
        }

        public Criteria andRegionIdIsNull() {
            addCriterion("region_id is null");
            return (Criteria) this;
        }

        public Criteria andRegionIdIsNotNull() {
            addCriterion("region_id is not null");
            return (Criteria) this;
        }

        public Criteria andRegionIdEqualTo(String value) {
            addCriterion("region_id =", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("region_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdNotEqualTo(String value) {
            addCriterion("region_id <>", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("region_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThan(String value) {
            addCriterion("region_id >", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("region_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanOrEqualTo(String value) {
            addCriterion("region_id >=", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("region_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThan(String value) {
            addCriterion("region_id <", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("region_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanOrEqualTo(String value) {
            addCriterion("region_id <=", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("region_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRegionIdLike(String value) {
            addCriterion("region_id like", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotLike(String value) {
            addCriterion("region_id not like", value, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdIn(List<String> values) {
            addCriterion("region_id in", values, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotIn(List<String> values) {
            addCriterion("region_id not in", values, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdBetween(String value1, String value2) {
            addCriterion("region_id between", value1, value2, "regionId");
            return (Criteria) this;
        }

        public Criteria andRegionIdNotBetween(String value1, String value2) {
            addCriterion("region_id not between", value1, value2, "regionId");
            return (Criteria) this;
        }

        public Criteria andExHandleIdIsNull() {
            addCriterion("ex_handle_id is null");
            return (Criteria) this;
        }

        public Criteria andExHandleIdIsNotNull() {
            addCriterion("ex_handle_id is not null");
            return (Criteria) this;
        }

        public Criteria andExHandleIdEqualTo(String value) {
            addCriterion("ex_handle_id =", value, "exHandleId");
            return (Criteria) this;
        }

        public Criteria andExHandleIdEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("ex_handle_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExHandleIdNotEqualTo(String value) {
            addCriterion("ex_handle_id <>", value, "exHandleId");
            return (Criteria) this;
        }

        public Criteria andExHandleIdNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("ex_handle_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExHandleIdGreaterThan(String value) {
            addCriterion("ex_handle_id >", value, "exHandleId");
            return (Criteria) this;
        }

        public Criteria andExHandleIdGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("ex_handle_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExHandleIdGreaterThanOrEqualTo(String value) {
            addCriterion("ex_handle_id >=", value, "exHandleId");
            return (Criteria) this;
        }

        public Criteria andExHandleIdGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("ex_handle_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExHandleIdLessThan(String value) {
            addCriterion("ex_handle_id <", value, "exHandleId");
            return (Criteria) this;
        }

        public Criteria andExHandleIdLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("ex_handle_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExHandleIdLessThanOrEqualTo(String value) {
            addCriterion("ex_handle_id <=", value, "exHandleId");
            return (Criteria) this;
        }

        public Criteria andExHandleIdLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("ex_handle_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andExHandleIdLike(String value) {
            addCriterion("ex_handle_id like", value, "exHandleId");
            return (Criteria) this;
        }

        public Criteria andExHandleIdNotLike(String value) {
            addCriterion("ex_handle_id not like", value, "exHandleId");
            return (Criteria) this;
        }

        public Criteria andExHandleIdIn(List<String> values) {
            addCriterion("ex_handle_id in", values, "exHandleId");
            return (Criteria) this;
        }

        public Criteria andExHandleIdNotIn(List<String> values) {
            addCriterion("ex_handle_id not in", values, "exHandleId");
            return (Criteria) this;
        }

        public Criteria andExHandleIdBetween(String value1, String value2) {
            addCriterion("ex_handle_id between", value1, value2, "exHandleId");
            return (Criteria) this;
        }

        public Criteria andExHandleIdNotBetween(String value1, String value2) {
            addCriterion("ex_handle_id not between", value1, value2, "exHandleId");
            return (Criteria) this;
        }

        public Criteria andSkuCardNameIsNull() {
            addCriterion("sku_card_name is null");
            return (Criteria) this;
        }

        public Criteria andSkuCardNameIsNotNull() {
            addCriterion("sku_card_name is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCardNameEqualTo(String value) {
            addCriterion("sku_card_name =", value, "skuCardName");
            return (Criteria) this;
        }

        public Criteria andSkuCardNameEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_card_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCardNameNotEqualTo(String value) {
            addCriterion("sku_card_name <>", value, "skuCardName");
            return (Criteria) this;
        }

        public Criteria andSkuCardNameNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_card_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCardNameGreaterThan(String value) {
            addCriterion("sku_card_name >", value, "skuCardName");
            return (Criteria) this;
        }

        public Criteria andSkuCardNameGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_card_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCardNameGreaterThanOrEqualTo(String value) {
            addCriterion("sku_card_name >=", value, "skuCardName");
            return (Criteria) this;
        }

        public Criteria andSkuCardNameGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_card_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCardNameLessThan(String value) {
            addCriterion("sku_card_name <", value, "skuCardName");
            return (Criteria) this;
        }

        public Criteria andSkuCardNameLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_card_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCardNameLessThanOrEqualTo(String value) {
            addCriterion("sku_card_name <=", value, "skuCardName");
            return (Criteria) this;
        }

        public Criteria andSkuCardNameLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_card_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuCardNameLike(String value) {
            addCriterion("sku_card_name like", value, "skuCardName");
            return (Criteria) this;
        }

        public Criteria andSkuCardNameNotLike(String value) {
            addCriterion("sku_card_name not like", value, "skuCardName");
            return (Criteria) this;
        }

        public Criteria andSkuCardNameIn(List<String> values) {
            addCriterion("sku_card_name in", values, "skuCardName");
            return (Criteria) this;
        }

        public Criteria andSkuCardNameNotIn(List<String> values) {
            addCriterion("sku_card_name not in", values, "skuCardName");
            return (Criteria) this;
        }

        public Criteria andSkuCardNameBetween(String value1, String value2) {
            addCriterion("sku_card_name between", value1, value2, "skuCardName");
            return (Criteria) this;
        }

        public Criteria andSkuCardNameNotBetween(String value1, String value2) {
            addCriterion("sku_card_name not between", value1, value2, "skuCardName");
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnIsNull() {
            addCriterion("sku_msisdn is null");
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnIsNotNull() {
            addCriterion("sku_msisdn is not null");
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnEqualTo(String value) {
            addCriterion("sku_msisdn =", value, "skuMsisdn");
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_msisdn = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnNotEqualTo(String value) {
            addCriterion("sku_msisdn <>", value, "skuMsisdn");
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_msisdn <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnGreaterThan(String value) {
            addCriterion("sku_msisdn >", value, "skuMsisdn");
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_msisdn > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnGreaterThanOrEqualTo(String value) {
            addCriterion("sku_msisdn >=", value, "skuMsisdn");
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_msisdn >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnLessThan(String value) {
            addCriterion("sku_msisdn <", value, "skuMsisdn");
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_msisdn < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnLessThanOrEqualTo(String value) {
            addCriterion("sku_msisdn <=", value, "skuMsisdn");
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_msisdn <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnLike(String value) {
            addCriterion("sku_msisdn like", value, "skuMsisdn");
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnNotLike(String value) {
            addCriterion("sku_msisdn not like", value, "skuMsisdn");
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnIn(List<String> values) {
            addCriterion("sku_msisdn in", values, "skuMsisdn");
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnNotIn(List<String> values) {
            addCriterion("sku_msisdn not in", values, "skuMsisdn");
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnBetween(String value1, String value2) {
            addCriterion("sku_msisdn between", value1, value2, "skuMsisdn");
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnNotBetween(String value1, String value2) {
            addCriterion("sku_msisdn not between", value1, value2, "skuMsisdn");
            return (Criteria) this;
        }

        public Criteria andAllowOrderStatusIsNull() {
            addCriterion("allow_order_status is null");
            return (Criteria) this;
        }

        public Criteria andAllowOrderStatusIsNotNull() {
            addCriterion("allow_order_status is not null");
            return (Criteria) this;
        }

        public Criteria andAllowOrderStatusEqualTo(Integer value) {
            addCriterion("allow_order_status =", value, "allowOrderStatus");
            return (Criteria) this;
        }

        public Criteria andAllowOrderStatusEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("allow_order_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowOrderStatusNotEqualTo(Integer value) {
            addCriterion("allow_order_status <>", value, "allowOrderStatus");
            return (Criteria) this;
        }

        public Criteria andAllowOrderStatusNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("allow_order_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowOrderStatusGreaterThan(Integer value) {
            addCriterion("allow_order_status >", value, "allowOrderStatus");
            return (Criteria) this;
        }

        public Criteria andAllowOrderStatusGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("allow_order_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowOrderStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("allow_order_status >=", value, "allowOrderStatus");
            return (Criteria) this;
        }

        public Criteria andAllowOrderStatusGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("allow_order_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowOrderStatusLessThan(Integer value) {
            addCriterion("allow_order_status <", value, "allowOrderStatus");
            return (Criteria) this;
        }

        public Criteria andAllowOrderStatusLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("allow_order_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowOrderStatusLessThanOrEqualTo(Integer value) {
            addCriterion("allow_order_status <=", value, "allowOrderStatus");
            return (Criteria) this;
        }

        public Criteria andAllowOrderStatusLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("allow_order_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowOrderStatusIn(List<Integer> values) {
            addCriterion("allow_order_status in", values, "allowOrderStatus");
            return (Criteria) this;
        }

        public Criteria andAllowOrderStatusNotIn(List<Integer> values) {
            addCriterion("allow_order_status not in", values, "allowOrderStatus");
            return (Criteria) this;
        }

        public Criteria andAllowOrderStatusBetween(Integer value1, Integer value2) {
            addCriterion("allow_order_status between", value1, value2, "allowOrderStatus");
            return (Criteria) this;
        }

        public Criteria andAllowOrderStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("allow_order_status not between", value1, value2, "allowOrderStatus");
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonIsNull() {
            addCriterion("allow_order_failure_reason is null");
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonIsNotNull() {
            addCriterion("allow_order_failure_reason is not null");
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonEqualTo(String value) {
            addCriterion("allow_order_failure_reason =", value, "allowOrderFailureReason");
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("allow_order_failure_reason = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonNotEqualTo(String value) {
            addCriterion("allow_order_failure_reason <>", value, "allowOrderFailureReason");
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("allow_order_failure_reason <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonGreaterThan(String value) {
            addCriterion("allow_order_failure_reason >", value, "allowOrderFailureReason");
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("allow_order_failure_reason > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonGreaterThanOrEqualTo(String value) {
            addCriterion("allow_order_failure_reason >=", value, "allowOrderFailureReason");
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("allow_order_failure_reason >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonLessThan(String value) {
            addCriterion("allow_order_failure_reason <", value, "allowOrderFailureReason");
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("allow_order_failure_reason < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonLessThanOrEqualTo(String value) {
            addCriterion("allow_order_failure_reason <=", value, "allowOrderFailureReason");
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("allow_order_failure_reason <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonLike(String value) {
            addCriterion("allow_order_failure_reason like", value, "allowOrderFailureReason");
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonNotLike(String value) {
            addCriterion("allow_order_failure_reason not like", value, "allowOrderFailureReason");
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonIn(List<String> values) {
            addCriterion("allow_order_failure_reason in", values, "allowOrderFailureReason");
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonNotIn(List<String> values) {
            addCriterion("allow_order_failure_reason not in", values, "allowOrderFailureReason");
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonBetween(String value1, String value2) {
            addCriterion("allow_order_failure_reason between", value1, value2, "allowOrderFailureReason");
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonNotBetween(String value1, String value2) {
            addCriterion("allow_order_failure_reason not between", value1, value2, "allowOrderFailureReason");
            return (Criteria) this;
        }

        public Criteria andPartReturnIsNull() {
            addCriterion("part_return is null");
            return (Criteria) this;
        }

        public Criteria andPartReturnIsNotNull() {
            addCriterion("part_return is not null");
            return (Criteria) this;
        }

        public Criteria andPartReturnEqualTo(Integer value) {
            addCriterion("part_return =", value, "partReturn");
            return (Criteria) this;
        }

        public Criteria andPartReturnEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("part_return = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartReturnNotEqualTo(Integer value) {
            addCriterion("part_return <>", value, "partReturn");
            return (Criteria) this;
        }

        public Criteria andPartReturnNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("part_return <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartReturnGreaterThan(Integer value) {
            addCriterion("part_return >", value, "partReturn");
            return (Criteria) this;
        }

        public Criteria andPartReturnGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("part_return > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartReturnGreaterThanOrEqualTo(Integer value) {
            addCriterion("part_return >=", value, "partReturn");
            return (Criteria) this;
        }

        public Criteria andPartReturnGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("part_return >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartReturnLessThan(Integer value) {
            addCriterion("part_return <", value, "partReturn");
            return (Criteria) this;
        }

        public Criteria andPartReturnLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("part_return < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartReturnLessThanOrEqualTo(Integer value) {
            addCriterion("part_return <=", value, "partReturn");
            return (Criteria) this;
        }

        public Criteria andPartReturnLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("part_return <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPartReturnIn(List<Integer> values) {
            addCriterion("part_return in", values, "partReturn");
            return (Criteria) this;
        }

        public Criteria andPartReturnNotIn(List<Integer> values) {
            addCriterion("part_return not in", values, "partReturn");
            return (Criteria) this;
        }

        public Criteria andPartReturnBetween(Integer value1, Integer value2) {
            addCriterion("part_return between", value1, value2, "partReturn");
            return (Criteria) this;
        }

        public Criteria andPartReturnNotBetween(Integer value1, Integer value2) {
            addCriterion("part_return not between", value1, value2, "partReturn");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusIsNull() {
            addCriterion("baoli_status is null");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusIsNotNull() {
            addCriterion("baoli_status is not null");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusEqualTo(Integer value) {
            addCriterion("baoli_status =", value, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("baoli_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBaoliStatusNotEqualTo(Integer value) {
            addCriterion("baoli_status <>", value, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("baoli_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBaoliStatusGreaterThan(Integer value) {
            addCriterion("baoli_status >", value, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("baoli_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBaoliStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("baoli_status >=", value, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("baoli_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBaoliStatusLessThan(Integer value) {
            addCriterion("baoli_status <", value, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("baoli_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBaoliStatusLessThanOrEqualTo(Integer value) {
            addCriterion("baoli_status <=", value, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("baoli_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBaoliStatusIn(List<Integer> values) {
            addCriterion("baoli_status in", values, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusNotIn(List<Integer> values) {
            addCriterion("baoli_status not in", values, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusBetween(Integer value1, Integer value2) {
            addCriterion("baoli_status between", value1, value2, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("baoli_status not between", value1, value2, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(String value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(String value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(String value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(String value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(String value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(String value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLike(String value) {
            addCriterion("create_time like", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotLike(String value) {
            addCriterion("create_time not like", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<String> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<String> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(String value1, String value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(String value1, String value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeIsNull() {
            addCriterion("valet_order_complete_time is null");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeIsNotNull() {
            addCriterion("valet_order_complete_time is not null");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeEqualTo(String value) {
            addCriterion("valet_order_complete_time =", value, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_complete_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeNotEqualTo(String value) {
            addCriterion("valet_order_complete_time <>", value, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_complete_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeGreaterThan(String value) {
            addCriterion("valet_order_complete_time >", value, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_complete_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeGreaterThanOrEqualTo(String value) {
            addCriterion("valet_order_complete_time >=", value, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_complete_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeLessThan(String value) {
            addCriterion("valet_order_complete_time <", value, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_complete_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeLessThanOrEqualTo(String value) {
            addCriterion("valet_order_complete_time <=", value, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("valet_order_complete_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeLike(String value) {
            addCriterion("valet_order_complete_time like", value, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeNotLike(String value) {
            addCriterion("valet_order_complete_time not like", value, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeIn(List<String> values) {
            addCriterion("valet_order_complete_time in", values, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeNotIn(List<String> values) {
            addCriterion("valet_order_complete_time not in", values, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeBetween(String value1, String value2) {
            addCriterion("valet_order_complete_time between", value1, value2, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeNotBetween(String value1, String value2) {
            addCriterion("valet_order_complete_time not between", value1, value2, "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andCarOpenStatusIsNull() {
            addCriterion("car_open_status is null");
            return (Criteria) this;
        }

        public Criteria andCarOpenStatusIsNotNull() {
            addCriterion("car_open_status is not null");
            return (Criteria) this;
        }

        public Criteria andCarOpenStatusEqualTo(Integer value) {
            addCriterion("car_open_status =", value, "carOpenStatus");
            return (Criteria) this;
        }

        public Criteria andCarOpenStatusEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("car_open_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCarOpenStatusNotEqualTo(Integer value) {
            addCriterion("car_open_status <>", value, "carOpenStatus");
            return (Criteria) this;
        }

        public Criteria andCarOpenStatusNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("car_open_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCarOpenStatusGreaterThan(Integer value) {
            addCriterion("car_open_status >", value, "carOpenStatus");
            return (Criteria) this;
        }

        public Criteria andCarOpenStatusGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("car_open_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCarOpenStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("car_open_status >=", value, "carOpenStatus");
            return (Criteria) this;
        }

        public Criteria andCarOpenStatusGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("car_open_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCarOpenStatusLessThan(Integer value) {
            addCriterion("car_open_status <", value, "carOpenStatus");
            return (Criteria) this;
        }

        public Criteria andCarOpenStatusLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("car_open_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCarOpenStatusLessThanOrEqualTo(Integer value) {
            addCriterion("car_open_status <=", value, "carOpenStatus");
            return (Criteria) this;
        }

        public Criteria andCarOpenStatusLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("car_open_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCarOpenStatusIn(List<Integer> values) {
            addCriterion("car_open_status in", values, "carOpenStatus");
            return (Criteria) this;
        }

        public Criteria andCarOpenStatusNotIn(List<Integer> values) {
            addCriterion("car_open_status not in", values, "carOpenStatus");
            return (Criteria) this;
        }

        public Criteria andCarOpenStatusBetween(Integer value1, Integer value2) {
            addCriterion("car_open_status between", value1, value2, "carOpenStatus");
            return (Criteria) this;
        }

        public Criteria andCarOpenStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("car_open_status not between", value1, value2, "carOpenStatus");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionIsNull() {
            addCriterion("spu_offering_version is null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionIsNotNull() {
            addCriterion("spu_offering_version is not null");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionEqualTo(String value) {
            addCriterion("spu_offering_version =", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_version = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotEqualTo(String value) {
            addCriterion("spu_offering_version <>", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_version <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionGreaterThan(String value) {
            addCriterion("spu_offering_version >", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_version > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionGreaterThanOrEqualTo(String value) {
            addCriterion("spu_offering_version >=", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_version >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLessThan(String value) {
            addCriterion("spu_offering_version <", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_version < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLessThanOrEqualTo(String value) {
            addCriterion("spu_offering_version <=", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("spu_offering_version <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLike(String value) {
            addCriterion("spu_offering_version like", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotLike(String value) {
            addCriterion("spu_offering_version not like", value, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionIn(List<String> values) {
            addCriterion("spu_offering_version in", values, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotIn(List<String> values) {
            addCriterion("spu_offering_version not in", values, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionBetween(String value1, String value2) {
            addCriterion("spu_offering_version between", value1, value2, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionNotBetween(String value1, String value2) {
            addCriterion("spu_offering_version not between", value1, value2, "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionIsNull() {
            addCriterion("sku_offering_version is null");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionIsNotNull() {
            addCriterion("sku_offering_version is not null");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionEqualTo(String value) {
            addCriterion("sku_offering_version =", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_version = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionNotEqualTo(String value) {
            addCriterion("sku_offering_version <>", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_version <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionGreaterThan(String value) {
            addCriterion("sku_offering_version >", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_version > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionGreaterThanOrEqualTo(String value) {
            addCriterion("sku_offering_version >=", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_version >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionLessThan(String value) {
            addCriterion("sku_offering_version <", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_version < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionLessThanOrEqualTo(String value) {
            addCriterion("sku_offering_version <=", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("sku_offering_version <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionLike(String value) {
            addCriterion("sku_offering_version like", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionNotLike(String value) {
            addCriterion("sku_offering_version not like", value, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionIn(List<String> values) {
            addCriterion("sku_offering_version in", values, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionNotIn(List<String> values) {
            addCriterion("sku_offering_version not in", values, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionBetween(String value1, String value2) {
            addCriterion("sku_offering_version between", value1, value2, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionNotBetween(String value1, String value2) {
            addCriterion("sku_offering_version not between", value1, value2, "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionIsNull() {
            addCriterion("atom_offering_version is null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionIsNotNull() {
            addCriterion("atom_offering_version is not null");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionEqualTo(String value) {
            addCriterion("atom_offering_version =", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_version = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionNotEqualTo(String value) {
            addCriterion("atom_offering_version <>", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_version <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionGreaterThan(String value) {
            addCriterion("atom_offering_version >", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_version > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionGreaterThanOrEqualTo(String value) {
            addCriterion("atom_offering_version >=", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_version >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionLessThan(String value) {
            addCriterion("atom_offering_version <", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_version < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionLessThanOrEqualTo(String value) {
            addCriterion("atom_offering_version <=", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("atom_offering_version <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionLike(String value) {
            addCriterion("atom_offering_version like", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionNotLike(String value) {
            addCriterion("atom_offering_version not like", value, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionIn(List<String> values) {
            addCriterion("atom_offering_version in", values, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionNotIn(List<String> values) {
            addCriterion("atom_offering_version not in", values, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionBetween(String value1, String value2) {
            addCriterion("atom_offering_version between", value1, value2, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionNotBetween(String value1, String value2) {
            addCriterion("atom_offering_version not between", value1, value2, "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSoftServiceStatusIsNull() {
            addCriterion("soft_service_status is null");
            return (Criteria) this;
        }

        public Criteria andSoftServiceStatusIsNotNull() {
            addCriterion("soft_service_status is not null");
            return (Criteria) this;
        }

        public Criteria andSoftServiceStatusEqualTo(Integer value) {
            addCriterion("soft_service_status =", value, "softServiceStatus");
            return (Criteria) this;
        }

        public Criteria andSoftServiceStatusEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("soft_service_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftServiceStatusNotEqualTo(Integer value) {
            addCriterion("soft_service_status <>", value, "softServiceStatus");
            return (Criteria) this;
        }

        public Criteria andSoftServiceStatusNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("soft_service_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftServiceStatusGreaterThan(Integer value) {
            addCriterion("soft_service_status >", value, "softServiceStatus");
            return (Criteria) this;
        }

        public Criteria andSoftServiceStatusGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("soft_service_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftServiceStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("soft_service_status >=", value, "softServiceStatus");
            return (Criteria) this;
        }

        public Criteria andSoftServiceStatusGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("soft_service_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftServiceStatusLessThan(Integer value) {
            addCriterion("soft_service_status <", value, "softServiceStatus");
            return (Criteria) this;
        }

        public Criteria andSoftServiceStatusLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("soft_service_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftServiceStatusLessThanOrEqualTo(Integer value) {
            addCriterion("soft_service_status <=", value, "softServiceStatus");
            return (Criteria) this;
        }

        public Criteria andSoftServiceStatusLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("soft_service_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSoftServiceStatusIn(List<Integer> values) {
            addCriterion("soft_service_status in", values, "softServiceStatus");
            return (Criteria) this;
        }

        public Criteria andSoftServiceStatusNotIn(List<Integer> values) {
            addCriterion("soft_service_status not in", values, "softServiceStatus");
            return (Criteria) this;
        }

        public Criteria andSoftServiceStatusBetween(Integer value1, Integer value2) {
            addCriterion("soft_service_status between", value1, value2, "softServiceStatus");
            return (Criteria) this;
        }

        public Criteria andSoftServiceStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("soft_service_status not between", value1, value2, "softServiceStatus");
            return (Criteria) this;
        }

        public Criteria andScmOrderNumIsNull() {
            addCriterion("scm_order_num is null");
            return (Criteria) this;
        }

        public Criteria andScmOrderNumIsNotNull() {
            addCriterion("scm_order_num is not null");
            return (Criteria) this;
        }

        public Criteria andScmOrderNumEqualTo(String value) {
            addCriterion("scm_order_num =", value, "scmOrderNum");
            return (Criteria) this;
        }

        public Criteria andScmOrderNumEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("scm_order_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andScmOrderNumNotEqualTo(String value) {
            addCriterion("scm_order_num <>", value, "scmOrderNum");
            return (Criteria) this;
        }

        public Criteria andScmOrderNumNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("scm_order_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andScmOrderNumGreaterThan(String value) {
            addCriterion("scm_order_num >", value, "scmOrderNum");
            return (Criteria) this;
        }

        public Criteria andScmOrderNumGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("scm_order_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andScmOrderNumGreaterThanOrEqualTo(String value) {
            addCriterion("scm_order_num >=", value, "scmOrderNum");
            return (Criteria) this;
        }

        public Criteria andScmOrderNumGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("scm_order_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andScmOrderNumLessThan(String value) {
            addCriterion("scm_order_num <", value, "scmOrderNum");
            return (Criteria) this;
        }

        public Criteria andScmOrderNumLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("scm_order_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andScmOrderNumLessThanOrEqualTo(String value) {
            addCriterion("scm_order_num <=", value, "scmOrderNum");
            return (Criteria) this;
        }

        public Criteria andScmOrderNumLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("scm_order_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andScmOrderNumLike(String value) {
            addCriterion("scm_order_num like", value, "scmOrderNum");
            return (Criteria) this;
        }

        public Criteria andScmOrderNumNotLike(String value) {
            addCriterion("scm_order_num not like", value, "scmOrderNum");
            return (Criteria) this;
        }

        public Criteria andScmOrderNumIn(List<String> values) {
            addCriterion("scm_order_num in", values, "scmOrderNum");
            return (Criteria) this;
        }

        public Criteria andScmOrderNumNotIn(List<String> values) {
            addCriterion("scm_order_num not in", values, "scmOrderNum");
            return (Criteria) this;
        }

        public Criteria andScmOrderNumBetween(String value1, String value2) {
            addCriterion("scm_order_num between", value1, value2, "scmOrderNum");
            return (Criteria) this;
        }

        public Criteria andScmOrderNumNotBetween(String value1, String value2) {
            addCriterion("scm_order_num not between", value1, value2, "scmOrderNum");
            return (Criteria) this;
        }

        public Criteria andSettleStatusIsNull() {
            addCriterion("settle_status is null");
            return (Criteria) this;
        }

        public Criteria andSettleStatusIsNotNull() {
            addCriterion("settle_status is not null");
            return (Criteria) this;
        }

        public Criteria andSettleStatusEqualTo(Integer value) {
            addCriterion("settle_status =", value, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("settle_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleStatusNotEqualTo(Integer value) {
            addCriterion("settle_status <>", value, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("settle_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleStatusGreaterThan(Integer value) {
            addCriterion("settle_status >", value, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("settle_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("settle_status >=", value, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("settle_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleStatusLessThan(Integer value) {
            addCriterion("settle_status <", value, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("settle_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleStatusLessThanOrEqualTo(Integer value) {
            addCriterion("settle_status <=", value, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("settle_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSettleStatusIn(List<Integer> values) {
            addCriterion("settle_status in", values, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusNotIn(List<Integer> values) {
            addCriterion("settle_status not in", values, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusBetween(Integer value1, Integer value2) {
            addCriterion("settle_status between", value1, value2, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andSettleStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("settle_status not between", value1, value2, "settleStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineSettleStatusIsNull() {
            addCriterion("online_settle_status is null");
            return (Criteria) this;
        }

        public Criteria andOnlineSettleStatusIsNotNull() {
            addCriterion("online_settle_status is not null");
            return (Criteria) this;
        }

        public Criteria andOnlineSettleStatusEqualTo(Integer value) {
            addCriterion("online_settle_status =", value, "onlineSettleStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineSettleStatusEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("online_settle_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineSettleStatusNotEqualTo(Integer value) {
            addCriterion("online_settle_status <>", value, "onlineSettleStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineSettleStatusNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("online_settle_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineSettleStatusGreaterThan(Integer value) {
            addCriterion("online_settle_status >", value, "onlineSettleStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineSettleStatusGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("online_settle_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineSettleStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("online_settle_status >=", value, "onlineSettleStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineSettleStatusGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("online_settle_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineSettleStatusLessThan(Integer value) {
            addCriterion("online_settle_status <", value, "onlineSettleStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineSettleStatusLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("online_settle_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineSettleStatusLessThanOrEqualTo(Integer value) {
            addCriterion("online_settle_status <=", value, "onlineSettleStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineSettleStatusLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("online_settle_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andOnlineSettleStatusIn(List<Integer> values) {
            addCriterion("online_settle_status in", values, "onlineSettleStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineSettleStatusNotIn(List<Integer> values) {
            addCriterion("online_settle_status not in", values, "onlineSettleStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineSettleStatusBetween(Integer value1, Integer value2) {
            addCriterion("online_settle_status between", value1, value2, "onlineSettleStatus");
            return (Criteria) this;
        }

        public Criteria andOnlineSettleStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("online_settle_status not between", value1, value2, "onlineSettleStatus");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeIsNull() {
            addCriterion("bill_no_time is null");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeIsNotNull() {
            addCriterion("bill_no_time is not null");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeEqualTo(Date value) {
            addCriterion("bill_no_time =", value, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("bill_no_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillNoTimeNotEqualTo(Date value) {
            addCriterion("bill_no_time <>", value, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeNotEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("bill_no_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillNoTimeGreaterThan(Date value) {
            addCriterion("bill_no_time >", value, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeGreaterThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("bill_no_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillNoTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("bill_no_time >=", value, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeGreaterThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("bill_no_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillNoTimeLessThan(Date value) {
            addCriterion("bill_no_time <", value, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeLessThanColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("bill_no_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillNoTimeLessThanOrEqualTo(Date value) {
            addCriterion("bill_no_time <=", value, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeLessThanOrEqualToColumn(Order2cAtomInfo.Column column) {
            addCriterion(new StringBuilder("bill_no_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBillNoTimeIn(List<Date> values) {
            addCriterion("bill_no_time in", values, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeNotIn(List<Date> values) {
            addCriterion("bill_no_time not in", values, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeBetween(Date value1, Date value2) {
            addCriterion("bill_no_time between", value1, value2, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andBillNoTimeNotBetween(Date value1, Date value2) {
            addCriterion("bill_no_time not between", value1, value2, "billNoTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andOrderIdLikeInsensitive(String value) {
            addCriterion("upper(order_id) like", value.toUpperCase(), "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLikeInsensitive(String value) {
            addCriterion("upper(order_type) like", value.toUpperCase(), "orderType");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(spu_offering_code) like", value.toUpperCase(), "spuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(sku_offering_code) like", value.toUpperCase(), "skuOfferingCode");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingNameLikeInsensitive(String value) {
            addCriterion("upper(sku_offering_name) like", value.toUpperCase(), "skuOfferingName");
            return (Criteria) this;
        }

        public Criteria andMarketNameLikeInsensitive(String value) {
            addCriterion("upper(market_name) like", value.toUpperCase(), "marketName");
            return (Criteria) this;
        }

        public Criteria andMarketCodeLikeInsensitive(String value) {
            addCriterion("upper(market_code) like", value.toUpperCase(), "marketCode");
            return (Criteria) this;
        }

        public Criteria andSupplierNameLikeInsensitive(String value) {
            addCriterion("upper(supplier_name) like", value.toUpperCase(), "supplierName");
            return (Criteria) this;
        }

        public Criteria andColorLikeInsensitive(String value) {
            addCriterion("upper(color) like", value.toUpperCase(), "color");
            return (Criteria) this;
        }

        public Criteria andModelLikeInsensitive(String value) {
            addCriterion("upper(model) like", value.toUpperCase(), "model");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingClassLikeInsensitive(String value) {
            addCriterion("upper(atom_offering_class) like", value.toUpperCase(), "atomOfferingClass");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingCodeLikeInsensitive(String value) {
            addCriterion("upper(atom_offering_code) like", value.toUpperCase(), "atomOfferingCode");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingNameLikeInsensitive(String value) {
            addCriterion("upper(atom_offering_name) like", value.toUpperCase(), "atomOfferingName");
            return (Criteria) this;
        }

        public Criteria andDeductPriceLikeInsensitive(String value) {
            addCriterion("upper(deduct_price) like", value.toUpperCase(), "deductPrice");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLikeInsensitive(String value) {
            addCriterion("upper(cooperator_id) like", value.toUpperCase(), "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andFinishCooperatorIdLikeInsensitive(String value) {
            addCriterion("upper(finish_cooperator_id) like", value.toUpperCase(), "finishCooperatorId");
            return (Criteria) this;
        }

        public Criteria andBeIdLikeInsensitive(String value) {
            addCriterion("upper(be_id) like", value.toUpperCase(), "beId");
            return (Criteria) this;
        }

        public Criteria andRegionIdLikeInsensitive(String value) {
            addCriterion("upper(region_id) like", value.toUpperCase(), "regionId");
            return (Criteria) this;
        }

        public Criteria andExHandleIdLikeInsensitive(String value) {
            addCriterion("upper(ex_handle_id) like", value.toUpperCase(), "exHandleId");
            return (Criteria) this;
        }

        public Criteria andSkuCardNameLikeInsensitive(String value) {
            addCriterion("upper(sku_card_name) like", value.toUpperCase(), "skuCardName");
            return (Criteria) this;
        }

        public Criteria andSkuMsisdnLikeInsensitive(String value) {
            addCriterion("upper(sku_msisdn) like", value.toUpperCase(), "skuMsisdn");
            return (Criteria) this;
        }

        public Criteria andAllowOrderFailureReasonLikeInsensitive(String value) {
            addCriterion("upper(allow_order_failure_reason) like", value.toUpperCase(), "allowOrderFailureReason");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLikeInsensitive(String value) {
            addCriterion("upper(create_time) like", value.toUpperCase(), "createTime");
            return (Criteria) this;
        }

        public Criteria andValetOrderCompleteTimeLikeInsensitive(String value) {
            addCriterion("upper(valet_order_complete_time) like", value.toUpperCase(), "valetOrderCompleteTime");
            return (Criteria) this;
        }

        public Criteria andSpuOfferingVersionLikeInsensitive(String value) {
            addCriterion("upper(spu_offering_version) like", value.toUpperCase(), "spuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andSkuOfferingVersionLikeInsensitive(String value) {
            addCriterion("upper(sku_offering_version) like", value.toUpperCase(), "skuOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andAtomOfferingVersionLikeInsensitive(String value) {
            addCriterion("upper(atom_offering_version) like", value.toUpperCase(), "atomOfferingVersion");
            return (Criteria) this;
        }

        public Criteria andScmOrderNumLikeInsensitive(String value) {
            addCriterion("upper(scm_order_num) like", value.toUpperCase(), "scmOrderNum");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        private Order2cAtomInfoExample example;

        protected Criteria(Order2cAtomInfoExample example) {
            super();
            this.example = example;
        }

        public Order2cAtomInfoExample example() {
            return this.example;
        }

        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            Criteria add(Criteria add);
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        void example(com.chinamobile.iot.sc.pojo.entity.Order2cAtomInfoExample example);
    }
}