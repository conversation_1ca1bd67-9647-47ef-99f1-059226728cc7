package com.chinamobile.iot.sc.pojo.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TradeOrderInfoExample {
    /**
     * Corresponding to the database table supply_chain..trade_order_info
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    protected String orderByClause;

    /**
     * Corresponding to the database table supply_chain..trade_order_info
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    protected boolean distinct;

    /**
     * Corresponding to the database table supply_chain..trade_order_info
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfoExample orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfoExample orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public static Criteria newAndCreateCriteria() {
        TradeOrderInfoExample example = new TradeOrderInfoExample();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfoExample when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public TradeOrderInfoExample when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     * Corresponding to the database table supply_chain..trade_order_info
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTradeNoIsNull() {
            addCriterion("trade_no is null");
            return (Criteria) this;
        }

        public Criteria andTradeNoIsNotNull() {
            addCriterion("trade_no is not null");
            return (Criteria) this;
        }

        public Criteria andTradeNoEqualTo(String value) {
            addCriterion("trade_no =", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("trade_no = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTradeNoNotEqualTo(String value) {
            addCriterion("trade_no <>", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("trade_no <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTradeNoGreaterThan(String value) {
            addCriterion("trade_no >", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("trade_no > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTradeNoGreaterThanOrEqualTo(String value) {
            addCriterion("trade_no >=", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("trade_no >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTradeNoLessThan(String value) {
            addCriterion("trade_no <", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("trade_no < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTradeNoLessThanOrEqualTo(String value) {
            addCriterion("trade_no <=", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("trade_no <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTradeNoLike(String value) {
            addCriterion("trade_no like", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoNotLike(String value) {
            addCriterion("trade_no not like", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoIn(List<String> values) {
            addCriterion("trade_no in", values, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoNotIn(List<String> values) {
            addCriterion("trade_no not in", values, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoBetween(String value1, String value2) {
            addCriterion("trade_no between", value1, value2, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoNotBetween(String value1, String value2) {
            addCriterion("trade_no not between", value1, value2, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andContractNumIsNull() {
            addCriterion("contract_num is null");
            return (Criteria) this;
        }

        public Criteria andContractNumIsNotNull() {
            addCriterion("contract_num is not null");
            return (Criteria) this;
        }

        public Criteria andContractNumEqualTo(String value) {
            addCriterion("contract_num =", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("contract_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumNotEqualTo(String value) {
            addCriterion("contract_num <>", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("contract_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumGreaterThan(String value) {
            addCriterion("contract_num >", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("contract_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumGreaterThanOrEqualTo(String value) {
            addCriterion("contract_num >=", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("contract_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumLessThan(String value) {
            addCriterion("contract_num <", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("contract_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumLessThanOrEqualTo(String value) {
            addCriterion("contract_num <=", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("contract_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andContractNumLike(String value) {
            addCriterion("contract_num like", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumNotLike(String value) {
            addCriterion("contract_num not like", value, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumIn(List<String> values) {
            addCriterion("contract_num in", values, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumNotIn(List<String> values) {
            addCriterion("contract_num not in", values, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumBetween(String value1, String value2) {
            addCriterion("contract_num between", value1, value2, "contractNum");
            return (Criteria) this;
        }

        public Criteria andContractNumNotBetween(String value1, String value2) {
            addCriterion("contract_num not between", value1, value2, "contractNum");
            return (Criteria) this;
        }

        public Criteria andBuyerNameIsNull() {
            addCriterion("buyer_name is null");
            return (Criteria) this;
        }

        public Criteria andBuyerNameIsNotNull() {
            addCriterion("buyer_name is not null");
            return (Criteria) this;
        }

        public Criteria andBuyerNameEqualTo(String value) {
            addCriterion("buyer_name =", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("buyer_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerNameNotEqualTo(String value) {
            addCriterion("buyer_name <>", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("buyer_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerNameGreaterThan(String value) {
            addCriterion("buyer_name >", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("buyer_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerNameGreaterThanOrEqualTo(String value) {
            addCriterion("buyer_name >=", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("buyer_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerNameLessThan(String value) {
            addCriterion("buyer_name <", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("buyer_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerNameLessThanOrEqualTo(String value) {
            addCriterion("buyer_name <=", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("buyer_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBuyerNameLike(String value) {
            addCriterion("buyer_name like", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameNotLike(String value) {
            addCriterion("buyer_name not like", value, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameIn(List<String> values) {
            addCriterion("buyer_name in", values, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameNotIn(List<String> values) {
            addCriterion("buyer_name not in", values, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameBetween(String value1, String value2) {
            addCriterion("buyer_name between", value1, value2, "buyerName");
            return (Criteria) this;
        }

        public Criteria andBuyerNameNotBetween(String value1, String value2) {
            addCriterion("buyer_name not between", value1, value2, "buyerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameIsNull() {
            addCriterion("seller_name is null");
            return (Criteria) this;
        }

        public Criteria andSellerNameIsNotNull() {
            addCriterion("seller_name is not null");
            return (Criteria) this;
        }

        public Criteria andSellerNameEqualTo(String value) {
            addCriterion("seller_name =", value, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("seller_name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerNameNotEqualTo(String value) {
            addCriterion("seller_name <>", value, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("seller_name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerNameGreaterThan(String value) {
            addCriterion("seller_name >", value, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("seller_name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerNameGreaterThanOrEqualTo(String value) {
            addCriterion("seller_name >=", value, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("seller_name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerNameLessThan(String value) {
            addCriterion("seller_name <", value, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("seller_name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerNameLessThanOrEqualTo(String value) {
            addCriterion("seller_name <=", value, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("seller_name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerNameLike(String value) {
            addCriterion("seller_name like", value, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameNotLike(String value) {
            addCriterion("seller_name not like", value, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameIn(List<String> values) {
            addCriterion("seller_name in", values, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameNotIn(List<String> values) {
            addCriterion("seller_name not in", values, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameBetween(String value1, String value2) {
            addCriterion("seller_name between", value1, value2, "sellerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameNotBetween(String value1, String value2) {
            addCriterion("seller_name not between", value1, value2, "sellerName");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIsNull() {
            addCriterion("cooperator_id is null");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIsNotNull() {
            addCriterion("cooperator_id is not null");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdEqualTo(String value) {
            addCriterion("cooperator_id =", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotEqualTo(String value) {
            addCriterion("cooperator_id <>", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThan(String value) {
            addCriterion("cooperator_id >", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanOrEqualTo(String value) {
            addCriterion("cooperator_id >=", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThan(String value) {
            addCriterion("cooperator_id <", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanOrEqualTo(String value) {
            addCriterion("cooperator_id <=", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("cooperator_id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLike(String value) {
            addCriterion("cooperator_id like", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotLike(String value) {
            addCriterion("cooperator_id not like", value, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdIn(List<String> values) {
            addCriterion("cooperator_id in", values, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotIn(List<String> values) {
            addCriterion("cooperator_id not in", values, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdBetween(String value1, String value2) {
            addCriterion("cooperator_id between", value1, value2, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdNotBetween(String value1, String value2) {
            addCriterion("cooperator_id not between", value1, value2, "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andTradePriceIsNull() {
            addCriterion("trade_price is null");
            return (Criteria) this;
        }

        public Criteria andTradePriceIsNotNull() {
            addCriterion("trade_price is not null");
            return (Criteria) this;
        }

        public Criteria andTradePriceEqualTo(Long value) {
            addCriterion("trade_price =", value, "tradePrice");
            return (Criteria) this;
        }

        public Criteria andTradePriceEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("trade_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTradePriceNotEqualTo(Long value) {
            addCriterion("trade_price <>", value, "tradePrice");
            return (Criteria) this;
        }

        public Criteria andTradePriceNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("trade_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTradePriceGreaterThan(Long value) {
            addCriterion("trade_price >", value, "tradePrice");
            return (Criteria) this;
        }

        public Criteria andTradePriceGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("trade_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTradePriceGreaterThanOrEqualTo(Long value) {
            addCriterion("trade_price >=", value, "tradePrice");
            return (Criteria) this;
        }

        public Criteria andTradePriceGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("trade_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTradePriceLessThan(Long value) {
            addCriterion("trade_price <", value, "tradePrice");
            return (Criteria) this;
        }

        public Criteria andTradePriceLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("trade_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTradePriceLessThanOrEqualTo(Long value) {
            addCriterion("trade_price <=", value, "tradePrice");
            return (Criteria) this;
        }

        public Criteria andTradePriceLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("trade_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTradePriceIn(List<Long> values) {
            addCriterion("trade_price in", values, "tradePrice");
            return (Criteria) this;
        }

        public Criteria andTradePriceNotIn(List<Long> values) {
            addCriterion("trade_price not in", values, "tradePrice");
            return (Criteria) this;
        }

        public Criteria andTradePriceBetween(Long value1, Long value2) {
            addCriterion("trade_price between", value1, value2, "tradePrice");
            return (Criteria) this;
        }

        public Criteria andTradePriceNotBetween(Long value1, Long value2) {
            addCriterion("trade_price not between", value1, value2, "tradePrice");
            return (Criteria) this;
        }

        public Criteria andMaxFinancingPriceIsNull() {
            addCriterion("max_financing_price is null");
            return (Criteria) this;
        }

        public Criteria andMaxFinancingPriceIsNotNull() {
            addCriterion("max_financing_price is not null");
            return (Criteria) this;
        }

        public Criteria andMaxFinancingPriceEqualTo(BigDecimal value) {
            addCriterion("max_financing_price =", value, "maxFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andMaxFinancingPriceEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("max_financing_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaxFinancingPriceNotEqualTo(BigDecimal value) {
            addCriterion("max_financing_price <>", value, "maxFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andMaxFinancingPriceNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("max_financing_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaxFinancingPriceGreaterThan(BigDecimal value) {
            addCriterion("max_financing_price >", value, "maxFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andMaxFinancingPriceGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("max_financing_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaxFinancingPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("max_financing_price >=", value, "maxFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andMaxFinancingPriceGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("max_financing_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaxFinancingPriceLessThan(BigDecimal value) {
            addCriterion("max_financing_price <", value, "maxFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andMaxFinancingPriceLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("max_financing_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaxFinancingPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("max_financing_price <=", value, "maxFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andMaxFinancingPriceLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("max_financing_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andMaxFinancingPriceIn(List<BigDecimal> values) {
            addCriterion("max_financing_price in", values, "maxFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andMaxFinancingPriceNotIn(List<BigDecimal> values) {
            addCriterion("max_financing_price not in", values, "maxFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andMaxFinancingPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("max_financing_price between", value1, value2, "maxFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andMaxFinancingPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("max_financing_price not between", value1, value2, "maxFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andRequestFinancingPriceIsNull() {
            addCriterion("request_financing_price is null");
            return (Criteria) this;
        }

        public Criteria andRequestFinancingPriceIsNotNull() {
            addCriterion("request_financing_price is not null");
            return (Criteria) this;
        }

        public Criteria andRequestFinancingPriceEqualTo(Long value) {
            addCriterion("request_financing_price =", value, "requestFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andRequestFinancingPriceEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("request_financing_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestFinancingPriceNotEqualTo(Long value) {
            addCriterion("request_financing_price <>", value, "requestFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andRequestFinancingPriceNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("request_financing_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestFinancingPriceGreaterThan(Long value) {
            addCriterion("request_financing_price >", value, "requestFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andRequestFinancingPriceGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("request_financing_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestFinancingPriceGreaterThanOrEqualTo(Long value) {
            addCriterion("request_financing_price >=", value, "requestFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andRequestFinancingPriceGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("request_financing_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestFinancingPriceLessThan(Long value) {
            addCriterion("request_financing_price <", value, "requestFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andRequestFinancingPriceLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("request_financing_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestFinancingPriceLessThanOrEqualTo(Long value) {
            addCriterion("request_financing_price <=", value, "requestFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andRequestFinancingPriceLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("request_financing_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andRequestFinancingPriceIn(List<Long> values) {
            addCriterion("request_financing_price in", values, "requestFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andRequestFinancingPriceNotIn(List<Long> values) {
            addCriterion("request_financing_price not in", values, "requestFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andRequestFinancingPriceBetween(Long value1, Long value2) {
            addCriterion("request_financing_price between", value1, value2, "requestFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andRequestFinancingPriceNotBetween(Long value1, Long value2) {
            addCriterion("request_financing_price not between", value1, value2, "requestFinancingPrice");
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumIsNull() {
            addCriterion("erwartetes_wareneingangsdatum is null");
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumIsNotNull() {
            addCriterion("erwartetes_wareneingangsdatum is not null");
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumEqualTo(String value) {
            addCriterion("erwartetes_wareneingangsdatum =", value, "erwartetesWareneingangsdatum");
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("erwartetes_wareneingangsdatum = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumNotEqualTo(String value) {
            addCriterion("erwartetes_wareneingangsdatum <>", value, "erwartetesWareneingangsdatum");
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("erwartetes_wareneingangsdatum <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumGreaterThan(String value) {
            addCriterion("erwartetes_wareneingangsdatum >", value, "erwartetesWareneingangsdatum");
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("erwartetes_wareneingangsdatum > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumGreaterThanOrEqualTo(String value) {
            addCriterion("erwartetes_wareneingangsdatum >=", value, "erwartetesWareneingangsdatum");
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("erwartetes_wareneingangsdatum >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumLessThan(String value) {
            addCriterion("erwartetes_wareneingangsdatum <", value, "erwartetesWareneingangsdatum");
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("erwartetes_wareneingangsdatum < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumLessThanOrEqualTo(String value) {
            addCriterion("erwartetes_wareneingangsdatum <=", value, "erwartetesWareneingangsdatum");
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("erwartetes_wareneingangsdatum <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumLike(String value) {
            addCriterion("erwartetes_wareneingangsdatum like", value, "erwartetesWareneingangsdatum");
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumNotLike(String value) {
            addCriterion("erwartetes_wareneingangsdatum not like", value, "erwartetesWareneingangsdatum");
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumIn(List<String> values) {
            addCriterion("erwartetes_wareneingangsdatum in", values, "erwartetesWareneingangsdatum");
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumNotIn(List<String> values) {
            addCriterion("erwartetes_wareneingangsdatum not in", values, "erwartetesWareneingangsdatum");
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumBetween(String value1, String value2) {
            addCriterion("erwartetes_wareneingangsdatum between", value1, value2, "erwartetesWareneingangsdatum");
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumNotBetween(String value1, String value2) {
            addCriterion("erwartetes_wareneingangsdatum not between", value1, value2, "erwartetesWareneingangsdatum");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumIsNull() {
            addCriterion("invoice_num is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumIsNotNull() {
            addCriterion("invoice_num is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumEqualTo(String value) {
            addCriterion("invoice_num =", value, "invoiceNum");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("invoice_num = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInvoiceNumNotEqualTo(String value) {
            addCriterion("invoice_num <>", value, "invoiceNum");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("invoice_num <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInvoiceNumGreaterThan(String value) {
            addCriterion("invoice_num >", value, "invoiceNum");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("invoice_num > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInvoiceNumGreaterThanOrEqualTo(String value) {
            addCriterion("invoice_num >=", value, "invoiceNum");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("invoice_num >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInvoiceNumLessThan(String value) {
            addCriterion("invoice_num <", value, "invoiceNum");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("invoice_num < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInvoiceNumLessThanOrEqualTo(String value) {
            addCriterion("invoice_num <=", value, "invoiceNum");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("invoice_num <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInvoiceNumLike(String value) {
            addCriterion("invoice_num like", value, "invoiceNum");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumNotLike(String value) {
            addCriterion("invoice_num not like", value, "invoiceNum");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumIn(List<String> values) {
            addCriterion("invoice_num in", values, "invoiceNum");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumNotIn(List<String> values) {
            addCriterion("invoice_num not in", values, "invoiceNum");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumBetween(String value1, String value2) {
            addCriterion("invoice_num between", value1, value2, "invoiceNum");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumNotBetween(String value1, String value2) {
            addCriterion("invoice_num not between", value1, value2, "invoiceNum");
            return (Criteria) this;
        }

        public Criteria andInvoicePriceIsNull() {
            addCriterion("invoice_price is null");
            return (Criteria) this;
        }

        public Criteria andInvoicePriceIsNotNull() {
            addCriterion("invoice_price is not null");
            return (Criteria) this;
        }

        public Criteria andInvoicePriceEqualTo(Long value) {
            addCriterion("invoice_price =", value, "invoicePrice");
            return (Criteria) this;
        }

        public Criteria andInvoicePriceEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("invoice_price = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInvoicePriceNotEqualTo(Long value) {
            addCriterion("invoice_price <>", value, "invoicePrice");
            return (Criteria) this;
        }

        public Criteria andInvoicePriceNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("invoice_price <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInvoicePriceGreaterThan(Long value) {
            addCriterion("invoice_price >", value, "invoicePrice");
            return (Criteria) this;
        }

        public Criteria andInvoicePriceGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("invoice_price > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInvoicePriceGreaterThanOrEqualTo(Long value) {
            addCriterion("invoice_price >=", value, "invoicePrice");
            return (Criteria) this;
        }

        public Criteria andInvoicePriceGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("invoice_price >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInvoicePriceLessThan(Long value) {
            addCriterion("invoice_price <", value, "invoicePrice");
            return (Criteria) this;
        }

        public Criteria andInvoicePriceLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("invoice_price < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInvoicePriceLessThanOrEqualTo(Long value) {
            addCriterion("invoice_price <=", value, "invoicePrice");
            return (Criteria) this;
        }

        public Criteria andInvoicePriceLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("invoice_price <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andInvoicePriceIn(List<Long> values) {
            addCriterion("invoice_price in", values, "invoicePrice");
            return (Criteria) this;
        }

        public Criteria andInvoicePriceNotIn(List<Long> values) {
            addCriterion("invoice_price not in", values, "invoicePrice");
            return (Criteria) this;
        }

        public Criteria andInvoicePriceBetween(Long value1, Long value2) {
            addCriterion("invoice_price between", value1, value2, "invoicePrice");
            return (Criteria) this;
        }

        public Criteria andInvoicePriceNotBetween(Long value1, Long value2) {
            addCriterion("invoice_price not between", value1, value2, "invoicePrice");
            return (Criteria) this;
        }

        public Criteria andCanUseBankIsNull() {
            addCriterion("can_use_bank is null");
            return (Criteria) this;
        }

        public Criteria andCanUseBankIsNotNull() {
            addCriterion("can_use_bank is not null");
            return (Criteria) this;
        }

        public Criteria andCanUseBankEqualTo(String value) {
            addCriterion("can_use_bank =", value, "canUseBank");
            return (Criteria) this;
        }

        public Criteria andCanUseBankEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("can_use_bank = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCanUseBankNotEqualTo(String value) {
            addCriterion("can_use_bank <>", value, "canUseBank");
            return (Criteria) this;
        }

        public Criteria andCanUseBankNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("can_use_bank <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCanUseBankGreaterThan(String value) {
            addCriterion("can_use_bank >", value, "canUseBank");
            return (Criteria) this;
        }

        public Criteria andCanUseBankGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("can_use_bank > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCanUseBankGreaterThanOrEqualTo(String value) {
            addCriterion("can_use_bank >=", value, "canUseBank");
            return (Criteria) this;
        }

        public Criteria andCanUseBankGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("can_use_bank >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCanUseBankLessThan(String value) {
            addCriterion("can_use_bank <", value, "canUseBank");
            return (Criteria) this;
        }

        public Criteria andCanUseBankLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("can_use_bank < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCanUseBankLessThanOrEqualTo(String value) {
            addCriterion("can_use_bank <=", value, "canUseBank");
            return (Criteria) this;
        }

        public Criteria andCanUseBankLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("can_use_bank <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCanUseBankLike(String value) {
            addCriterion("can_use_bank like", value, "canUseBank");
            return (Criteria) this;
        }

        public Criteria andCanUseBankNotLike(String value) {
            addCriterion("can_use_bank not like", value, "canUseBank");
            return (Criteria) this;
        }

        public Criteria andCanUseBankIn(List<String> values) {
            addCriterion("can_use_bank in", values, "canUseBank");
            return (Criteria) this;
        }

        public Criteria andCanUseBankNotIn(List<String> values) {
            addCriterion("can_use_bank not in", values, "canUseBank");
            return (Criteria) this;
        }

        public Criteria andCanUseBankBetween(String value1, String value2) {
            addCriterion("can_use_bank between", value1, value2, "canUseBank");
            return (Criteria) this;
        }

        public Criteria andCanUseBankNotBetween(String value1, String value2) {
            addCriterion("can_use_bank not between", value1, value2, "canUseBank");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusIsNull() {
            addCriterion("baoli_status is null");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusIsNotNull() {
            addCriterion("baoli_status is not null");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusEqualTo(Integer value) {
            addCriterion("baoli_status =", value, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("baoli_status = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBaoliStatusNotEqualTo(Integer value) {
            addCriterion("baoli_status <>", value, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("baoli_status <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBaoliStatusGreaterThan(Integer value) {
            addCriterion("baoli_status >", value, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("baoli_status > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBaoliStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("baoli_status >=", value, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("baoli_status >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBaoliStatusLessThan(Integer value) {
            addCriterion("baoli_status <", value, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("baoli_status < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBaoliStatusLessThanOrEqualTo(Integer value) {
            addCriterion("baoli_status <=", value, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("baoli_status <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andBaoliStatusIn(List<Integer> values) {
            addCriterion("baoli_status in", values, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusNotIn(List<Integer> values) {
            addCriterion("baoli_status not in", values, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusBetween(Integer value1, Integer value2) {
            addCriterion("baoli_status between", value1, value2, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andBaoliStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("baoli_status not between", value1, value2, "baoliStatus");
            return (Criteria) this;
        }

        public Criteria andAdviceIsNull() {
            addCriterion("advice is null");
            return (Criteria) this;
        }

        public Criteria andAdviceIsNotNull() {
            addCriterion("advice is not null");
            return (Criteria) this;
        }

        public Criteria andAdviceEqualTo(String value) {
            addCriterion("advice =", value, "advice");
            return (Criteria) this;
        }

        public Criteria andAdviceEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("advice = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdviceNotEqualTo(String value) {
            addCriterion("advice <>", value, "advice");
            return (Criteria) this;
        }

        public Criteria andAdviceNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("advice <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdviceGreaterThan(String value) {
            addCriterion("advice >", value, "advice");
            return (Criteria) this;
        }

        public Criteria andAdviceGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("advice > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdviceGreaterThanOrEqualTo(String value) {
            addCriterion("advice >=", value, "advice");
            return (Criteria) this;
        }

        public Criteria andAdviceGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("advice >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdviceLessThan(String value) {
            addCriterion("advice <", value, "advice");
            return (Criteria) this;
        }

        public Criteria andAdviceLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("advice < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdviceLessThanOrEqualTo(String value) {
            addCriterion("advice <=", value, "advice");
            return (Criteria) this;
        }

        public Criteria andAdviceLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("advice <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAdviceLike(String value) {
            addCriterion("advice like", value, "advice");
            return (Criteria) this;
        }

        public Criteria andAdviceNotLike(String value) {
            addCriterion("advice not like", value, "advice");
            return (Criteria) this;
        }

        public Criteria andAdviceIn(List<String> values) {
            addCriterion("advice in", values, "advice");
            return (Criteria) this;
        }

        public Criteria andAdviceNotIn(List<String> values) {
            addCriterion("advice not in", values, "advice");
            return (Criteria) this;
        }

        public Criteria andAdviceBetween(String value1, String value2) {
            addCriterion("advice between", value1, value2, "advice");
            return (Criteria) this;
        }

        public Criteria andAdviceNotBetween(String value1, String value2) {
            addCriterion("advice not between", value1, value2, "advice");
            return (Criteria) this;
        }

        public Criteria andPayDateIsNull() {
            addCriterion("pay_date is null");
            return (Criteria) this;
        }

        public Criteria andPayDateIsNotNull() {
            addCriterion("pay_date is not null");
            return (Criteria) this;
        }

        public Criteria andPayDateEqualTo(String value) {
            addCriterion("pay_date =", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_date = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayDateNotEqualTo(String value) {
            addCriterion("pay_date <>", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_date <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayDateGreaterThan(String value) {
            addCriterion("pay_date >", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_date > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayDateGreaterThanOrEqualTo(String value) {
            addCriterion("pay_date >=", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_date >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayDateLessThan(String value) {
            addCriterion("pay_date <", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_date < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayDateLessThanOrEqualTo(String value) {
            addCriterion("pay_date <=", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_date <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayDateLike(String value) {
            addCriterion("pay_date like", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateNotLike(String value) {
            addCriterion("pay_date not like", value, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateIn(List<String> values) {
            addCriterion("pay_date in", values, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateNotIn(List<String> values) {
            addCriterion("pay_date not in", values, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateBetween(String value1, String value2) {
            addCriterion("pay_date between", value1, value2, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayDateNotBetween(String value1, String value2) {
            addCriterion("pay_date not between", value1, value2, "payDate");
            return (Criteria) this;
        }

        public Criteria andPayAmountIsNull() {
            addCriterion("pay_amount is null");
            return (Criteria) this;
        }

        public Criteria andPayAmountIsNotNull() {
            addCriterion("pay_amount is not null");
            return (Criteria) this;
        }

        public Criteria andPayAmountEqualTo(Long value) {
            addCriterion("pay_amount =", value, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_amount = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayAmountNotEqualTo(Long value) {
            addCriterion("pay_amount <>", value, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_amount <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayAmountGreaterThan(Long value) {
            addCriterion("pay_amount >", value, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_amount > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("pay_amount >=", value, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_amount >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayAmountLessThan(Long value) {
            addCriterion("pay_amount <", value, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_amount < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayAmountLessThanOrEqualTo(Long value) {
            addCriterion("pay_amount <=", value, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_amount <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayAmountIn(List<Long> values) {
            addCriterion("pay_amount in", values, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountNotIn(List<Long> values) {
            addCriterion("pay_amount not in", values, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountBetween(Long value1, Long value2) {
            addCriterion("pay_amount between", value1, value2, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayAmountNotBetween(Long value1, Long value2) {
            addCriterion("pay_amount not between", value1, value2, "payAmount");
            return (Criteria) this;
        }

        public Criteria andPayBankIsNull() {
            addCriterion("pay_bank is null");
            return (Criteria) this;
        }

        public Criteria andPayBankIsNotNull() {
            addCriterion("pay_bank is not null");
            return (Criteria) this;
        }

        public Criteria andPayBankEqualTo(String value) {
            addCriterion("pay_bank =", value, "payBank");
            return (Criteria) this;
        }

        public Criteria andPayBankEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_bank = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayBankNotEqualTo(String value) {
            addCriterion("pay_bank <>", value, "payBank");
            return (Criteria) this;
        }

        public Criteria andPayBankNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_bank <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayBankGreaterThan(String value) {
            addCriterion("pay_bank >", value, "payBank");
            return (Criteria) this;
        }

        public Criteria andPayBankGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_bank > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayBankGreaterThanOrEqualTo(String value) {
            addCriterion("pay_bank >=", value, "payBank");
            return (Criteria) this;
        }

        public Criteria andPayBankGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_bank >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayBankLessThan(String value) {
            addCriterion("pay_bank <", value, "payBank");
            return (Criteria) this;
        }

        public Criteria andPayBankLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_bank < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayBankLessThanOrEqualTo(String value) {
            addCriterion("pay_bank <=", value, "payBank");
            return (Criteria) this;
        }

        public Criteria andPayBankLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("pay_bank <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPayBankLike(String value) {
            addCriterion("pay_bank like", value, "payBank");
            return (Criteria) this;
        }

        public Criteria andPayBankNotLike(String value) {
            addCriterion("pay_bank not like", value, "payBank");
            return (Criteria) this;
        }

        public Criteria andPayBankIn(List<String> values) {
            addCriterion("pay_bank in", values, "payBank");
            return (Criteria) this;
        }

        public Criteria andPayBankNotIn(List<String> values) {
            addCriterion("pay_bank not in", values, "payBank");
            return (Criteria) this;
        }

        public Criteria andPayBankBetween(String value1, String value2) {
            addCriterion("pay_bank between", value1, value2, "payBank");
            return (Criteria) this;
        }

        public Criteria andPayBankNotBetween(String value1, String value2) {
            addCriterion("pay_bank not between", value1, value2, "payBank");
            return (Criteria) this;
        }

        public Criteria andDueDateIsNull() {
            addCriterion("due_date is null");
            return (Criteria) this;
        }

        public Criteria andDueDateIsNotNull() {
            addCriterion("due_date is not null");
            return (Criteria) this;
        }

        public Criteria andDueDateEqualTo(String value) {
            addCriterion("due_date =", value, "dueDate");
            return (Criteria) this;
        }

        public Criteria andDueDateEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("due_date = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDueDateNotEqualTo(String value) {
            addCriterion("due_date <>", value, "dueDate");
            return (Criteria) this;
        }

        public Criteria andDueDateNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("due_date <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDueDateGreaterThan(String value) {
            addCriterion("due_date >", value, "dueDate");
            return (Criteria) this;
        }

        public Criteria andDueDateGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("due_date > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDueDateGreaterThanOrEqualTo(String value) {
            addCriterion("due_date >=", value, "dueDate");
            return (Criteria) this;
        }

        public Criteria andDueDateGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("due_date >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDueDateLessThan(String value) {
            addCriterion("due_date <", value, "dueDate");
            return (Criteria) this;
        }

        public Criteria andDueDateLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("due_date < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDueDateLessThanOrEqualTo(String value) {
            addCriterion("due_date <=", value, "dueDate");
            return (Criteria) this;
        }

        public Criteria andDueDateLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("due_date <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDueDateLike(String value) {
            addCriterion("due_date like", value, "dueDate");
            return (Criteria) this;
        }

        public Criteria andDueDateNotLike(String value) {
            addCriterion("due_date not like", value, "dueDate");
            return (Criteria) this;
        }

        public Criteria andDueDateIn(List<String> values) {
            addCriterion("due_date in", values, "dueDate");
            return (Criteria) this;
        }

        public Criteria andDueDateNotIn(List<String> values) {
            addCriterion("due_date not in", values, "dueDate");
            return (Criteria) this;
        }

        public Criteria andDueDateBetween(String value1, String value2) {
            addCriterion("due_date between", value1, value2, "dueDate");
            return (Criteria) this;
        }

        public Criteria andDueDateNotBetween(String value1, String value2) {
            addCriterion("due_date not between", value1, value2, "dueDate");
            return (Criteria) this;
        }

        public Criteria andFinanceCodeIsNull() {
            addCriterion("finance_code is null");
            return (Criteria) this;
        }

        public Criteria andFinanceCodeIsNotNull() {
            addCriterion("finance_code is not null");
            return (Criteria) this;
        }

        public Criteria andFinanceCodeEqualTo(String value) {
            addCriterion("finance_code =", value, "financeCode");
            return (Criteria) this;
        }

        public Criteria andFinanceCodeEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("finance_code = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFinanceCodeNotEqualTo(String value) {
            addCriterion("finance_code <>", value, "financeCode");
            return (Criteria) this;
        }

        public Criteria andFinanceCodeNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("finance_code <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFinanceCodeGreaterThan(String value) {
            addCriterion("finance_code >", value, "financeCode");
            return (Criteria) this;
        }

        public Criteria andFinanceCodeGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("finance_code > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFinanceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("finance_code >=", value, "financeCode");
            return (Criteria) this;
        }

        public Criteria andFinanceCodeGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("finance_code >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFinanceCodeLessThan(String value) {
            addCriterion("finance_code <", value, "financeCode");
            return (Criteria) this;
        }

        public Criteria andFinanceCodeLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("finance_code < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFinanceCodeLessThanOrEqualTo(String value) {
            addCriterion("finance_code <=", value, "financeCode");
            return (Criteria) this;
        }

        public Criteria andFinanceCodeLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("finance_code <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andFinanceCodeLike(String value) {
            addCriterion("finance_code like", value, "financeCode");
            return (Criteria) this;
        }

        public Criteria andFinanceCodeNotLike(String value) {
            addCriterion("finance_code not like", value, "financeCode");
            return (Criteria) this;
        }

        public Criteria andFinanceCodeIn(List<String> values) {
            addCriterion("finance_code in", values, "financeCode");
            return (Criteria) this;
        }

        public Criteria andFinanceCodeNotIn(List<String> values) {
            addCriterion("finance_code not in", values, "financeCode");
            return (Criteria) this;
        }

        public Criteria andFinanceCodeBetween(String value1, String value2) {
            addCriterion("finance_code between", value1, value2, "financeCode");
            return (Criteria) this;
        }

        public Criteria andFinanceCodeNotBetween(String value1, String value2) {
            addCriterion("finance_code not between", value1, value2, "financeCode");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("create_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("create_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("create_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("create_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("create_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("create_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("update_time = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("update_time <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("update_time > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("update_time >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("update_time < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualToColumn(TradeOrderInfo.Column column) {
            addCriterion(new StringBuilder("update_time <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andTradeNoLikeInsensitive(String value) {
            addCriterion("upper(trade_no) like", value.toUpperCase(), "tradeNo");
            return (Criteria) this;
        }

        public Criteria andContractNumLikeInsensitive(String value) {
            addCriterion("upper(contract_num) like", value.toUpperCase(), "contractNum");
            return (Criteria) this;
        }

        public Criteria andBuyerNameLikeInsensitive(String value) {
            addCriterion("upper(buyer_name) like", value.toUpperCase(), "buyerName");
            return (Criteria) this;
        }

        public Criteria andSellerNameLikeInsensitive(String value) {
            addCriterion("upper(seller_name) like", value.toUpperCase(), "sellerName");
            return (Criteria) this;
        }

        public Criteria andCooperatorIdLikeInsensitive(String value) {
            addCriterion("upper(cooperator_id) like", value.toUpperCase(), "cooperatorId");
            return (Criteria) this;
        }

        public Criteria andErwartetesWareneingangsdatumLikeInsensitive(String value) {
            addCriterion("upper(erwartetes_wareneingangsdatum) like", value.toUpperCase(), "erwartetesWareneingangsdatum");
            return (Criteria) this;
        }

        public Criteria andInvoiceNumLikeInsensitive(String value) {
            addCriterion("upper(invoice_num) like", value.toUpperCase(), "invoiceNum");
            return (Criteria) this;
        }

        public Criteria andCanUseBankLikeInsensitive(String value) {
            addCriterion("upper(can_use_bank) like", value.toUpperCase(), "canUseBank");
            return (Criteria) this;
        }

        public Criteria andAdviceLikeInsensitive(String value) {
            addCriterion("upper(advice) like", value.toUpperCase(), "advice");
            return (Criteria) this;
        }

        public Criteria andPayDateLikeInsensitive(String value) {
            addCriterion("upper(pay_date) like", value.toUpperCase(), "payDate");
            return (Criteria) this;
        }

        public Criteria andPayBankLikeInsensitive(String value) {
            addCriterion("upper(pay_bank) like", value.toUpperCase(), "payBank");
            return (Criteria) this;
        }

        public Criteria andDueDateLikeInsensitive(String value) {
            addCriterion("upper(due_date) like", value.toUpperCase(), "dueDate");
            return (Criteria) this;
        }

        public Criteria andFinanceCodeLikeInsensitive(String value) {
            addCriterion("upper(finance_code) like", value.toUpperCase(), "financeCode");
            return (Criteria) this;
        }
    }

    /**
     * Corresponding to the database table supply_chain..trade_order_info
     *
     * @mbg.generated do_not_delete_during_merge Mon Sep 18 16:49:08 CST 2023
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         * Corresponding to the database table supply_chain..trade_order_info
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        private TradeOrderInfoExample example;

        /**
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        protected Criteria(TradeOrderInfoExample example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        public TradeOrderInfoExample example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Mon Sep 18 16:49:08 CST 2023
             */
            Criteria add(Criteria add);
        }
    }

    /**
     * Corresponding to the database table supply_chain..trade_order_info
     *
     * @mbg.generated Mon Sep 18 16:49:08 CST 2023
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Mon Sep 18 16:49:08 CST 2023
         */
        void example(com.chinamobile.iot.sc.pojo.entity.TradeOrderInfoExample example);
    }
}