package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 
 *
 * <AUTHOR>
public class Contract implements Serializable {
    /**
     * 主键
     *
     * Corresponding to the database column supply_chain..contract.id
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String id;

    /**
     * 合同编号
     *
     * Corresponding to the database column supply_chain..contract.number
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String number;

    /**
     * 合同名称
     *
     * Corresponding to the database column supply_chain..contract.name
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String name;

    /**
     * 合同性质，{FRAMEWORK：框架协议，SINGLE_CONTRACT：单项合同，OUTER_CONTRACT：外部合同}
     *
     * Corresponding to the database column supply_chain..contract.property
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String property;

    /**
     * 合同状态，{履行中，已签订，已解除，履行完毕}
     *
     * Corresponding to the database column supply_chain..contract.status
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String status;

    /**
     * 相对方编码
     *
     * Corresponding to the database column supply_chain..contract.vendor_code
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String vendorCode;

    /**
     * 相对方名称
     *
     * Corresponding to the database column supply_chain..contract.vendor_name
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String vendorName;

    /**
     * 承建方名称
     *
     * Corresponding to the database column supply_chain..contract.create_company_name
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String createCompanyName;

    /**
     * 承建方部门名称
     *
     * Corresponding to the database column supply_chain..contract.create_dept_name
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String createDeptName;

    /**
     * 金额类型，{ESTIMATE_AMOUNT：预估金额，UPPER_LIMIT_AMOUNT：上限金额，CONTRACT_AMOUNT：固定金额}
     *
     * Corresponding to the database column supply_chain..contract.amount_type
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String amountType;

    /**
     * 币种
     *
     * Corresponding to the database column supply_chain..contract.currency
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String currency;

    /**
     * 合同含税总额
     *
     * Corresponding to the database column supply_chain..contract.amount_including_tax
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private BigDecimal amountIncludingTax;

    /**
     * 合同失效时间
     *
     * Corresponding to the database column supply_chain..contract.end_date
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private Date endDate;

    /**
     * 合同相对方所属省k3编码
     *
     * Corresponding to the database column supply_chain..contract.province_k3_code
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String provinceK3Code;

    /**
     * 合同相对方所属省商城编码
     *
     * Corresponding to the database column supply_chain..contract.province_mall_code
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String provinceMallCode;

    /**
     * 合同相对方所属省商城名称
     *
     * Corresponding to the database column supply_chain..contract.province_mall_name
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String provinceMallName;

    /**
     * 合同相对方所属地市k3编码
     *
     * Corresponding to the database column supply_chain..contract.city_k3_code
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String cityK3Code;

    /**
     * 合同相对方所属地市商城编码
     *
     * Corresponding to the database column supply_chain..contract.city_mall_code
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String cityMallCode;

    /**
     * 合同相对方所属地市商城名称
     *
     * Corresponding to the database column supply_chain..contract.city_mall_name
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String cityMallName;

    /**
     * 统计方式,{0-按地市，1-按省份，2-按合同}
     *
     * Corresponding to the database column supply_chain..contract.count_type
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private Integer countType;

    /**
     * 项目ID
     *
     * Corresponding to the database column supply_chain..contract.project
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String project;

    /**
     * 子项目ID
     *
     * Corresponding to the database column supply_chain..contract.sub_project
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String subProject;

    /**
     * 税率
     *
     * Corresponding to the database column supply_chain..contract.f_number
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String fNumber;

    /**
     * 结算方式（0-按地市，1-按省份）
     *
     * Corresponding to the database column supply_chain..contract.settlement_mode
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private Integer settlementMode;

    /**
     * 合同是否生效
     *
     * Corresponding to the database column supply_chain..contract.active
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private Boolean active;

    /**
     * 合同类型1--销售合同 2--采购合同 3--外部合同
     *
     * Corresponding to the database column supply_chain..contract.contract_type
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private Integer contractType;

    /**
     * 合同来源 1--系统获取  2--人工录入
     *
     * Corresponding to the database column supply_chain..contract.contract_source
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private Integer contractSource;

    /**
     * 库存配置，0-未配置（未全部配置），1-已配置（全部配置）
     *
     * Corresponding to the database column supply_chain..contract.quota_config
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private Integer quotaConfig;

    /**
     * 销售订单类型，0-标准产品省框，1-DICT服务包，2-统结标准产品
     *
     * Corresponding to the database column supply_chain..contract.sale_order_type
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private Integer saleOrderType;

    /**
     * 关联的历史合同
     *
     * Corresponding to the database column supply_chain..contract.relevancy_history_number
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private String relevancyHistoryNumber;

    /**
     * Corresponding to the database table supply_chain..contract
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..contract.id
     *
     * @return the value of supply_chain..contract.id
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.id
     *
     * @param id the value for supply_chain..contract.id
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.number
     *
     * @return the value of supply_chain..contract.number
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getNumber() {
        return number;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withNumber(String number) {
        this.setNumber(number);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.number
     *
     * @param number the value for supply_chain..contract.number
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setNumber(String number) {
        this.number = number == null ? null : number.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.name
     *
     * @return the value of supply_chain..contract.name
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.name
     *
     * @param name the value for supply_chain..contract.name
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.property
     *
     * @return the value of supply_chain..contract.property
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getProperty() {
        return property;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withProperty(String property) {
        this.setProperty(property);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.property
     *
     * @param property the value for supply_chain..contract.property
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setProperty(String property) {
        this.property = property == null ? null : property.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.status
     *
     * @return the value of supply_chain..contract.status
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getStatus() {
        return status;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withStatus(String status) {
        this.setStatus(status);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.status
     *
     * @param status the value for supply_chain..contract.status
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.vendor_code
     *
     * @return the value of supply_chain..contract.vendor_code
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getVendorCode() {
        return vendorCode;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withVendorCode(String vendorCode) {
        this.setVendorCode(vendorCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.vendor_code
     *
     * @param vendorCode the value for supply_chain..contract.vendor_code
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode == null ? null : vendorCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.vendor_name
     *
     * @return the value of supply_chain..contract.vendor_name
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getVendorName() {
        return vendorName;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withVendorName(String vendorName) {
        this.setVendorName(vendorName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.vendor_name
     *
     * @param vendorName the value for supply_chain..contract.vendor_name
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setVendorName(String vendorName) {
        this.vendorName = vendorName == null ? null : vendorName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.create_company_name
     *
     * @return the value of supply_chain..contract.create_company_name
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getCreateCompanyName() {
        return createCompanyName;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withCreateCompanyName(String createCompanyName) {
        this.setCreateCompanyName(createCompanyName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.create_company_name
     *
     * @param createCompanyName the value for supply_chain..contract.create_company_name
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setCreateCompanyName(String createCompanyName) {
        this.createCompanyName = createCompanyName == null ? null : createCompanyName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.create_dept_name
     *
     * @return the value of supply_chain..contract.create_dept_name
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getCreateDeptName() {
        return createDeptName;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withCreateDeptName(String createDeptName) {
        this.setCreateDeptName(createDeptName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.create_dept_name
     *
     * @param createDeptName the value for supply_chain..contract.create_dept_name
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setCreateDeptName(String createDeptName) {
        this.createDeptName = createDeptName == null ? null : createDeptName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.amount_type
     *
     * @return the value of supply_chain..contract.amount_type
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getAmountType() {
        return amountType;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withAmountType(String amountType) {
        this.setAmountType(amountType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.amount_type
     *
     * @param amountType the value for supply_chain..contract.amount_type
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setAmountType(String amountType) {
        this.amountType = amountType == null ? null : amountType.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.currency
     *
     * @return the value of supply_chain..contract.currency
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getCurrency() {
        return currency;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withCurrency(String currency) {
        this.setCurrency(currency);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.currency
     *
     * @param currency the value for supply_chain..contract.currency
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.amount_including_tax
     *
     * @return the value of supply_chain..contract.amount_including_tax
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public BigDecimal getAmountIncludingTax() {
        return amountIncludingTax;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withAmountIncludingTax(BigDecimal amountIncludingTax) {
        this.setAmountIncludingTax(amountIncludingTax);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.amount_including_tax
     *
     * @param amountIncludingTax the value for supply_chain..contract.amount_including_tax
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setAmountIncludingTax(BigDecimal amountIncludingTax) {
        this.amountIncludingTax = amountIncludingTax;
    }

    /**
     * This method returns the value of the database column supply_chain..contract.end_date
     *
     * @return the value of supply_chain..contract.end_date
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Date getEndDate() {
        return endDate;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withEndDate(Date endDate) {
        this.setEndDate(endDate);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.end_date
     *
     * @param endDate the value for supply_chain..contract.end_date
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    /**
     * This method returns the value of the database column supply_chain..contract.province_k3_code
     *
     * @return the value of supply_chain..contract.province_k3_code
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getProvinceK3Code() {
        return provinceK3Code;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withProvinceK3Code(String provinceK3Code) {
        this.setProvinceK3Code(provinceK3Code);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.province_k3_code
     *
     * @param provinceK3Code the value for supply_chain..contract.province_k3_code
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setProvinceK3Code(String provinceK3Code) {
        this.provinceK3Code = provinceK3Code == null ? null : provinceK3Code.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.province_mall_code
     *
     * @return the value of supply_chain..contract.province_mall_code
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getProvinceMallCode() {
        return provinceMallCode;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withProvinceMallCode(String provinceMallCode) {
        this.setProvinceMallCode(provinceMallCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.province_mall_code
     *
     * @param provinceMallCode the value for supply_chain..contract.province_mall_code
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setProvinceMallCode(String provinceMallCode) {
        this.provinceMallCode = provinceMallCode == null ? null : provinceMallCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.province_mall_name
     *
     * @return the value of supply_chain..contract.province_mall_name
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getProvinceMallName() {
        return provinceMallName;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withProvinceMallName(String provinceMallName) {
        this.setProvinceMallName(provinceMallName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.province_mall_name
     *
     * @param provinceMallName the value for supply_chain..contract.province_mall_name
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setProvinceMallName(String provinceMallName) {
        this.provinceMallName = provinceMallName == null ? null : provinceMallName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.city_k3_code
     *
     * @return the value of supply_chain..contract.city_k3_code
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getCityK3Code() {
        return cityK3Code;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withCityK3Code(String cityK3Code) {
        this.setCityK3Code(cityK3Code);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.city_k3_code
     *
     * @param cityK3Code the value for supply_chain..contract.city_k3_code
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setCityK3Code(String cityK3Code) {
        this.cityK3Code = cityK3Code == null ? null : cityK3Code.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.city_mall_code
     *
     * @return the value of supply_chain..contract.city_mall_code
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getCityMallCode() {
        return cityMallCode;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withCityMallCode(String cityMallCode) {
        this.setCityMallCode(cityMallCode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.city_mall_code
     *
     * @param cityMallCode the value for supply_chain..contract.city_mall_code
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setCityMallCode(String cityMallCode) {
        this.cityMallCode = cityMallCode == null ? null : cityMallCode.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.city_mall_name
     *
     * @return the value of supply_chain..contract.city_mall_name
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getCityMallName() {
        return cityMallName;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withCityMallName(String cityMallName) {
        this.setCityMallName(cityMallName);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.city_mall_name
     *
     * @param cityMallName the value for supply_chain..contract.city_mall_name
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setCityMallName(String cityMallName) {
        this.cityMallName = cityMallName == null ? null : cityMallName.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.count_type
     *
     * @return the value of supply_chain..contract.count_type
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Integer getCountType() {
        return countType;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withCountType(Integer countType) {
        this.setCountType(countType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.count_type
     *
     * @param countType the value for supply_chain..contract.count_type
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setCountType(Integer countType) {
        this.countType = countType;
    }

    /**
     * This method returns the value of the database column supply_chain..contract.project
     *
     * @return the value of supply_chain..contract.project
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getProject() {
        return project;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withProject(String project) {
        this.setProject(project);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.project
     *
     * @param project the value for supply_chain..contract.project
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setProject(String project) {
        this.project = project == null ? null : project.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.sub_project
     *
     * @return the value of supply_chain..contract.sub_project
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getSubProject() {
        return subProject;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withSubProject(String subProject) {
        this.setSubProject(subProject);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.sub_project
     *
     * @param subProject the value for supply_chain..contract.sub_project
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setSubProject(String subProject) {
        this.subProject = subProject == null ? null : subProject.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.f_number
     *
     * @return the value of supply_chain..contract.f_number
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getfNumber() {
        return fNumber;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withfNumber(String fNumber) {
        this.setfNumber(fNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.f_number
     *
     * @param fNumber the value for supply_chain..contract.f_number
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setfNumber(String fNumber) {
        this.fNumber = fNumber == null ? null : fNumber.trim();
    }

    /**
     * This method returns the value of the database column supply_chain..contract.settlement_mode
     *
     * @return the value of supply_chain..contract.settlement_mode
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Integer getSettlementMode() {
        return settlementMode;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withSettlementMode(Integer settlementMode) {
        this.setSettlementMode(settlementMode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.settlement_mode
     *
     * @param settlementMode the value for supply_chain..contract.settlement_mode
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setSettlementMode(Integer settlementMode) {
        this.settlementMode = settlementMode;
    }

    /**
     * This method returns the value of the database column supply_chain..contract.active
     *
     * @return the value of supply_chain..contract.active
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Boolean getActive() {
        return active;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withActive(Boolean active) {
        this.setActive(active);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.active
     *
     * @param active the value for supply_chain..contract.active
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setActive(Boolean active) {
        this.active = active;
    }

    /**
     * This method returns the value of the database column supply_chain..contract.contract_type
     *
     * @return the value of supply_chain..contract.contract_type
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Integer getContractType() {
        return contractType;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withContractType(Integer contractType) {
        this.setContractType(contractType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.contract_type
     *
     * @param contractType the value for supply_chain..contract.contract_type
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setContractType(Integer contractType) {
        this.contractType = contractType;
    }

    /**
     * This method returns the value of the database column supply_chain..contract.contract_source
     *
     * @return the value of supply_chain..contract.contract_source
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Integer getContractSource() {
        return contractSource;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withContractSource(Integer contractSource) {
        this.setContractSource(contractSource);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.contract_source
     *
     * @param contractSource the value for supply_chain..contract.contract_source
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setContractSource(Integer contractSource) {
        this.contractSource = contractSource;
    }

    /**
     * This method returns the value of the database column supply_chain..contract.quota_config
     *
     * @return the value of supply_chain..contract.quota_config
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Integer getQuotaConfig() {
        return quotaConfig;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withQuotaConfig(Integer quotaConfig) {
        this.setQuotaConfig(quotaConfig);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.quota_config
     *
     * @param quotaConfig the value for supply_chain..contract.quota_config
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setQuotaConfig(Integer quotaConfig) {
        this.quotaConfig = quotaConfig;
    }

    /**
     * This method returns the value of the database column supply_chain..contract.sale_order_type
     *
     * @return the value of supply_chain..contract.sale_order_type
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Integer getSaleOrderType() {
        return saleOrderType;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withSaleOrderType(Integer saleOrderType) {
        this.setSaleOrderType(saleOrderType);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.sale_order_type
     *
     * @param saleOrderType the value for supply_chain..contract.sale_order_type
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setSaleOrderType(Integer saleOrderType) {
        this.saleOrderType = saleOrderType;
    }

    /**
     * This method returns the value of the database column supply_chain..contract.relevancy_history_number
     *
     * @return the value of supply_chain..contract.relevancy_history_number
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public String getRelevancyHistoryNumber() {
        return relevancyHistoryNumber;
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public Contract withRelevancyHistoryNumber(String relevancyHistoryNumber) {
        this.setRelevancyHistoryNumber(relevancyHistoryNumber);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..contract.relevancy_history_number
     *
     * @param relevancyHistoryNumber the value for supply_chain..contract.relevancy_history_number
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public void setRelevancyHistoryNumber(String relevancyHistoryNumber) {
        this.relevancyHistoryNumber = relevancyHistoryNumber == null ? null : relevancyHistoryNumber.trim();
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", number=").append(number);
        sb.append(", name=").append(name);
        sb.append(", property=").append(property);
        sb.append(", status=").append(status);
        sb.append(", vendorCode=").append(vendorCode);
        sb.append(", vendorName=").append(vendorName);
        sb.append(", createCompanyName=").append(createCompanyName);
        sb.append(", createDeptName=").append(createDeptName);
        sb.append(", amountType=").append(amountType);
        sb.append(", currency=").append(currency);
        sb.append(", amountIncludingTax=").append(amountIncludingTax);
        sb.append(", endDate=").append(endDate);
        sb.append(", provinceK3Code=").append(provinceK3Code);
        sb.append(", provinceMallCode=").append(provinceMallCode);
        sb.append(", provinceMallName=").append(provinceMallName);
        sb.append(", cityK3Code=").append(cityK3Code);
        sb.append(", cityMallCode=").append(cityMallCode);
        sb.append(", cityMallName=").append(cityMallName);
        sb.append(", countType=").append(countType);
        sb.append(", project=").append(project);
        sb.append(", subProject=").append(subProject);
        sb.append(", fNumber=").append(fNumber);
        sb.append(", settlementMode=").append(settlementMode);
        sb.append(", active=").append(active);
        sb.append(", contractType=").append(contractType);
        sb.append(", contractSource=").append(contractSource);
        sb.append(", quotaConfig=").append(quotaConfig);
        sb.append(", saleOrderType=").append(saleOrderType);
        sb.append(", relevancyHistoryNumber=").append(relevancyHistoryNumber);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Contract other = (Contract) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getNumber() == null ? other.getNumber() == null : this.getNumber().equals(other.getNumber()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getProperty() == null ? other.getProperty() == null : this.getProperty().equals(other.getProperty()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getVendorCode() == null ? other.getVendorCode() == null : this.getVendorCode().equals(other.getVendorCode()))
            && (this.getVendorName() == null ? other.getVendorName() == null : this.getVendorName().equals(other.getVendorName()))
            && (this.getCreateCompanyName() == null ? other.getCreateCompanyName() == null : this.getCreateCompanyName().equals(other.getCreateCompanyName()))
            && (this.getCreateDeptName() == null ? other.getCreateDeptName() == null : this.getCreateDeptName().equals(other.getCreateDeptName()))
            && (this.getAmountType() == null ? other.getAmountType() == null : this.getAmountType().equals(other.getAmountType()))
            && (this.getCurrency() == null ? other.getCurrency() == null : this.getCurrency().equals(other.getCurrency()))
            && (this.getAmountIncludingTax() == null ? other.getAmountIncludingTax() == null : this.getAmountIncludingTax().equals(other.getAmountIncludingTax()))
            && (this.getEndDate() == null ? other.getEndDate() == null : this.getEndDate().equals(other.getEndDate()))
            && (this.getProvinceK3Code() == null ? other.getProvinceK3Code() == null : this.getProvinceK3Code().equals(other.getProvinceK3Code()))
            && (this.getProvinceMallCode() == null ? other.getProvinceMallCode() == null : this.getProvinceMallCode().equals(other.getProvinceMallCode()))
            && (this.getProvinceMallName() == null ? other.getProvinceMallName() == null : this.getProvinceMallName().equals(other.getProvinceMallName()))
            && (this.getCityK3Code() == null ? other.getCityK3Code() == null : this.getCityK3Code().equals(other.getCityK3Code()))
            && (this.getCityMallCode() == null ? other.getCityMallCode() == null : this.getCityMallCode().equals(other.getCityMallCode()))
            && (this.getCityMallName() == null ? other.getCityMallName() == null : this.getCityMallName().equals(other.getCityMallName()))
            && (this.getCountType() == null ? other.getCountType() == null : this.getCountType().equals(other.getCountType()))
            && (this.getProject() == null ? other.getProject() == null : this.getProject().equals(other.getProject()))
            && (this.getSubProject() == null ? other.getSubProject() == null : this.getSubProject().equals(other.getSubProject()))
            && (this.getfNumber() == null ? other.getfNumber() == null : this.getfNumber().equals(other.getfNumber()))
            && (this.getSettlementMode() == null ? other.getSettlementMode() == null : this.getSettlementMode().equals(other.getSettlementMode()))
            && (this.getActive() == null ? other.getActive() == null : this.getActive().equals(other.getActive()))
            && (this.getContractType() == null ? other.getContractType() == null : this.getContractType().equals(other.getContractType()))
            && (this.getContractSource() == null ? other.getContractSource() == null : this.getContractSource().equals(other.getContractSource()))
            && (this.getQuotaConfig() == null ? other.getQuotaConfig() == null : this.getQuotaConfig().equals(other.getQuotaConfig()))
            && (this.getSaleOrderType() == null ? other.getSaleOrderType() == null : this.getSaleOrderType().equals(other.getSaleOrderType()))
            && (this.getRelevancyHistoryNumber() == null ? other.getRelevancyHistoryNumber() == null : this.getRelevancyHistoryNumber().equals(other.getRelevancyHistoryNumber()));
    }

    /**
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getNumber() == null) ? 0 : getNumber().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getProperty() == null) ? 0 : getProperty().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getVendorCode() == null) ? 0 : getVendorCode().hashCode());
        result = prime * result + ((getVendorName() == null) ? 0 : getVendorName().hashCode());
        result = prime * result + ((getCreateCompanyName() == null) ? 0 : getCreateCompanyName().hashCode());
        result = prime * result + ((getCreateDeptName() == null) ? 0 : getCreateDeptName().hashCode());
        result = prime * result + ((getAmountType() == null) ? 0 : getAmountType().hashCode());
        result = prime * result + ((getCurrency() == null) ? 0 : getCurrency().hashCode());
        result = prime * result + ((getAmountIncludingTax() == null) ? 0 : getAmountIncludingTax().hashCode());
        result = prime * result + ((getEndDate() == null) ? 0 : getEndDate().hashCode());
        result = prime * result + ((getProvinceK3Code() == null) ? 0 : getProvinceK3Code().hashCode());
        result = prime * result + ((getProvinceMallCode() == null) ? 0 : getProvinceMallCode().hashCode());
        result = prime * result + ((getProvinceMallName() == null) ? 0 : getProvinceMallName().hashCode());
        result = prime * result + ((getCityK3Code() == null) ? 0 : getCityK3Code().hashCode());
        result = prime * result + ((getCityMallCode() == null) ? 0 : getCityMallCode().hashCode());
        result = prime * result + ((getCityMallName() == null) ? 0 : getCityMallName().hashCode());
        result = prime * result + ((getCountType() == null) ? 0 : getCountType().hashCode());
        result = prime * result + ((getProject() == null) ? 0 : getProject().hashCode());
        result = prime * result + ((getSubProject() == null) ? 0 : getSubProject().hashCode());
        result = prime * result + ((getfNumber() == null) ? 0 : getfNumber().hashCode());
        result = prime * result + ((getSettlementMode() == null) ? 0 : getSettlementMode().hashCode());
        result = prime * result + ((getActive() == null) ? 0 : getActive().hashCode());
        result = prime * result + ((getContractType() == null) ? 0 : getContractType().hashCode());
        result = prime * result + ((getContractSource() == null) ? 0 : getContractSource().hashCode());
        result = prime * result + ((getQuotaConfig() == null) ? 0 : getQuotaConfig().hashCode());
        result = prime * result + ((getSaleOrderType() == null) ? 0 : getSaleOrderType().hashCode());
        result = prime * result + ((getRelevancyHistoryNumber() == null) ? 0 : getRelevancyHistoryNumber().hashCode());
        return result;
    }

    /**
     * corresponding to the database table supply_chain..contract
     *
     * @mbg.generated Tue May 13 11:05:38 CST 2025
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        number("number", "number", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        property("property", "property", "VARCHAR", false),
        status("status", "status", "VARCHAR", false),
        vendorCode("vendor_code", "vendorCode", "VARCHAR", false),
        vendorName("vendor_name", "vendorName", "VARCHAR", false),
        createCompanyName("create_company_name", "createCompanyName", "VARCHAR", false),
        createDeptName("create_dept_name", "createDeptName", "VARCHAR", false),
        amountType("amount_type", "amountType", "VARCHAR", false),
        currency("currency", "currency", "VARCHAR", false),
        amountIncludingTax("amount_including_tax", "amountIncludingTax", "DECIMAL", false),
        endDate("end_date", "endDate", "TIMESTAMP", false),
        provinceK3Code("province_k3_code", "provinceK3Code", "VARCHAR", false),
        provinceMallCode("province_mall_code", "provinceMallCode", "VARCHAR", false),
        provinceMallName("province_mall_name", "provinceMallName", "VARCHAR", false),
        cityK3Code("city_k3_code", "cityK3Code", "VARCHAR", false),
        cityMallCode("city_mall_code", "cityMallCode", "VARCHAR", false),
        cityMallName("city_mall_name", "cityMallName", "VARCHAR", false),
        countType("count_type", "countType", "INTEGER", false),
        project("project", "project", "VARCHAR", false),
        subProject("sub_project", "subProject", "VARCHAR", false),
        fNumber("f_number", "fNumber", "VARCHAR", false),
        settlementMode("settlement_mode", "settlementMode", "INTEGER", false),
        active("active", "active", "BIT", false),
        contractType("contract_type", "contractType", "INTEGER", false),
        contractSource("contract_source", "contractSource", "INTEGER", false),
        quotaConfig("quota_config", "quotaConfig", "INTEGER", false),
        saleOrderType("sale_order_type", "saleOrderType", "INTEGER", false),
        relevancyHistoryNumber("relevancy_history_number", "relevancyHistoryNumber", "VARCHAR", false);

        /**
         * Corresponding to the database table supply_chain..contract
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..contract
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         * Corresponding to the database table supply_chain..contract
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        private final String column;

        /**
         * Corresponding to the database table supply_chain..contract
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        private final boolean isColumnNameDelimited;

        /**
         * Corresponding to the database table supply_chain..contract
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        private final String javaProperty;

        /**
         * Corresponding to the database table supply_chain..contract
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Tue May 13 11:05:38 CST 2025
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}