package com.chinamobile.iot.sc.controller;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.service.SOAService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * @AUTHOR: HWF
 * @DATE: 2025/1/17
 */
@RestController
@RequestMapping("/k3serv")
public class SOAController {

    @Resource
    private SOAService soaService;

    @PostMapping("/soa/test/orderDraftImport")
    public BaseAnswer<Void> testOrderDraftImport(){
        soaService.testOrderDraftImport();
        return new BaseAnswer<>();
    }


    @PostMapping("/soa/test/orderDraftReturn")
    public BaseAnswer<Void> testOrderDraftReturn(){
        soaService.testOrderDraftReturn();
        return new BaseAnswer<>();
    }

    @PostMapping("/soa/test/orderUpdate")
    public BaseAnswer<Void> testOrderUpdate(){
        soaService.testOrderUpdate();
        return new BaseAnswer<>();
    }

    @GetMapping("/soa/test/orderStatus")
    public BaseAnswer<Void> testOrderStatus(){
        soaService.testOrderStatus();
        return new BaseAnswer<>();
    }

    @GetMapping("/soa/test/scmOrder")
    public BaseAnswer<Void> testScmOrderQuery(){
        soaService.testScmOrderQuery();
        return new BaseAnswer<>();
    }


    @PostMapping("/soa/test/uploadFtp")
    public BaseAnswer<Void> testUploadFTP(@RequestParam("file") MultipartFile file){
        soaService.uploadSftpFile(file);
        return new BaseAnswer<>();
    }


    @PostMapping("/soa/test/downlaodFtp")
    public BaseAnswer<Void> testDownloadFTP(){
        soaService.downloadSftpFile();
        return new BaseAnswer<>();
    }


    @PostMapping("/soa/test/downloadStream")
    public BaseAnswer<Void> testdownloadStream(String url, String filename){
        soaService.testDownloadStream(url, filename);
        return new BaseAnswer<>();
    }


    @PostMapping("soa/test/fileexist")
    public BaseAnswer<Void> isFtpFileExist(String url, String filename){
        soaService.testFtpFileExist(url, filename);
        return new BaseAnswer<>();
    }


    @PostMapping("soa/test/handle/exception")
    public BaseAnswer<Void> testExceptionRollback(){
        soaService.testExceptionRollback();
        return new BaseAnswer<>();
    }


    @GetMapping("/soa/sftp/filelist")
    public BaseAnswer<Void> getSftpFiles(String dir){
        soaService.listSftpFiles();
        return new BaseAnswer<>();
    }


    @DeleteMapping("/soa/sftp/filedelete")
    public BaseAnswer<Void> deleteSftpFile(String type, String fileName){
        soaService.deleteSftpFile(type, fileName);
        return new BaseAnswer<>();
    }

    //todo 增加手动上传附件接口

}
