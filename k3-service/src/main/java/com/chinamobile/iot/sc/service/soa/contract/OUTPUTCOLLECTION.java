
package com.chinamobile.iot.sc.service.soa.contract;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>Java class for OUTPUTCOLLECTION complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="OUTPUTCOLLECTION"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="OUTPUTCOLLECTION_ITEM" type="{http://soa.cmcc.com/OSB_CMS_CMS_HQ_PageInquiryContractBaseConfigSrv}OUTPUTCOLLECTION_ITEM" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OUTPUTCOLLECTION", propOrder = {
    "outputcollectionitem"
})
public class OUTPUTCOLLECTION {

    @XmlElement(name = "OUTPUTCOLLECTION_ITEM")
    protected List<OUTPUTCOLLECTIONITEM> outputcollectionitem;

    /**
     * Gets the value of the outputcollectionitem property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the outputcollectionitem property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getOUTPUTCOLLECTIONITEM().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link OUTPUTCOLLECTIONITEM }
     * 
     * 
     */
    public List<OUTPUTCOLLECTIONITEM> getOUTPUTCOLLECTIONITEM() {
        if (outputcollectionitem == null) {
            outputcollectionitem = new ArrayList<OUTPUTCOLLECTIONITEM>();
        }
        return this.outputcollectionitem;
    }

}
