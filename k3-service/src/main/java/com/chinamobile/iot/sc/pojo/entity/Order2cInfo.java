package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class Order2cInfo {
    private String orderId;

    private String orderType;

    private String businessCode;

    private String createOperCode;

    private String createOperUserId;

    private String employeeNum;

    private String custMgName;

    private String custMgPhone;

    private Date orderStatusTime;

    private String custCode;

    private String custUserId;

    private String custName;

    private String beId;

    private String location;

    private String regionId;

    private String orderOrgBizCode;

    private String orgLevel;

    private String orgName;

    private String provinceOrgName;

    private String remarks;

    private String bookid;

    private Integer status;

    private String totalPrice;

    private String createTime;

    private String contactPersonName;

    private String contactPhone;

    private String addr1;

    private String addr2;

    private String addr3;

    private String addr4;

    private String usaddr;

    private String spuOfferingClass;

    private String spuOfferingCode;

    private String supplierCode;

    private Date updateTime;

    private Integer orderStatus;

    private String deductPrice;

    private String orderingChannelSource;

    private String orderingChannelName;

    private String ssoTerminalType;

    private Integer toK3;

    private Integer specialAfterMarketHandle;

    private String specialAfterStatus;

    private String specialAfterStatusTime;

    private String specialAfterLatestTime;

    private String syncK3Id;

    private Date payTime;

    private Date refundTime;

    private String valetOrderCompleteTime;

    private String billLadderType;

    private Integer qlyStatus;

    private Integer ysxStatus;

    private Integer kxRefundStatus;

    private String kxRefundReason;

    private String spuOfferingVersion;

    private String reserveBeId;

    private String reserveLocation;

    private String specialAfterRefundsNumber;

    private String spuListPlatform;

    private String billNoNumber;

    public String getOrderId() {
        return orderId;
    }

    public Order2cInfo withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    public String getOrderType() {
        return orderType;
    }

    public Order2cInfo withOrderType(String orderType) {
        this.setOrderType(orderType);
        return this;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType == null ? null : orderType.trim();
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public Order2cInfo withBusinessCode(String businessCode) {
        this.setBusinessCode(businessCode);
        return this;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode == null ? null : businessCode.trim();
    }

    public String getCreateOperCode() {
        return createOperCode;
    }

    public Order2cInfo withCreateOperCode(String createOperCode) {
        this.setCreateOperCode(createOperCode);
        return this;
    }

    public void setCreateOperCode(String createOperCode) {
        this.createOperCode = createOperCode == null ? null : createOperCode.trim();
    }

    public String getCreateOperUserId() {
        return createOperUserId;
    }

    public Order2cInfo withCreateOperUserId(String createOperUserId) {
        this.setCreateOperUserId(createOperUserId);
        return this;
    }

    public void setCreateOperUserId(String createOperUserId) {
        this.createOperUserId = createOperUserId == null ? null : createOperUserId.trim();
    }

    public String getEmployeeNum() {
        return employeeNum;
    }

    public Order2cInfo withEmployeeNum(String employeeNum) {
        this.setEmployeeNum(employeeNum);
        return this;
    }

    public void setEmployeeNum(String employeeNum) {
        this.employeeNum = employeeNum == null ? null : employeeNum.trim();
    }

    public String getCustMgName() {
        return custMgName;
    }

    public Order2cInfo withCustMgName(String custMgName) {
        this.setCustMgName(custMgName);
        return this;
    }

    public void setCustMgName(String custMgName) {
        this.custMgName = custMgName == null ? null : custMgName.trim();
    }

    public String getCustMgPhone() {
        return custMgPhone;
    }

    public Order2cInfo withCustMgPhone(String custMgPhone) {
        this.setCustMgPhone(custMgPhone);
        return this;
    }

    public void setCustMgPhone(String custMgPhone) {
        this.custMgPhone = custMgPhone == null ? null : custMgPhone.trim();
    }

    public Date getOrderStatusTime() {
        return orderStatusTime;
    }

    public Order2cInfo withOrderStatusTime(Date orderStatusTime) {
        this.setOrderStatusTime(orderStatusTime);
        return this;
    }

    public void setOrderStatusTime(Date orderStatusTime) {
        this.orderStatusTime = orderStatusTime;
    }

    public String getCustCode() {
        return custCode;
    }

    public Order2cInfo withCustCode(String custCode) {
        this.setCustCode(custCode);
        return this;
    }

    public void setCustCode(String custCode) {
        this.custCode = custCode == null ? null : custCode.trim();
    }

    public String getCustUserId() {
        return custUserId;
    }

    public Order2cInfo withCustUserId(String custUserId) {
        this.setCustUserId(custUserId);
        return this;
    }

    public void setCustUserId(String custUserId) {
        this.custUserId = custUserId == null ? null : custUserId.trim();
    }

    public String getCustName() {
        return custName;
    }

    public Order2cInfo withCustName(String custName) {
        this.setCustName(custName);
        return this;
    }

    public void setCustName(String custName) {
        this.custName = custName == null ? null : custName.trim();
    }

    public String getBeId() {
        return beId;
    }

    public Order2cInfo withBeId(String beId) {
        this.setBeId(beId);
        return this;
    }

    public void setBeId(String beId) {
        this.beId = beId == null ? null : beId.trim();
    }

    public String getLocation() {
        return location;
    }

    public Order2cInfo withLocation(String location) {
        this.setLocation(location);
        return this;
    }

    public void setLocation(String location) {
        this.location = location == null ? null : location.trim();
    }

    public String getRegionId() {
        return regionId;
    }

    public Order2cInfo withRegionId(String regionId) {
        this.setRegionId(regionId);
        return this;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId == null ? null : regionId.trim();
    }

    public String getOrderOrgBizCode() {
        return orderOrgBizCode;
    }

    public Order2cInfo withOrderOrgBizCode(String orderOrgBizCode) {
        this.setOrderOrgBizCode(orderOrgBizCode);
        return this;
    }

    public void setOrderOrgBizCode(String orderOrgBizCode) {
        this.orderOrgBizCode = orderOrgBizCode == null ? null : orderOrgBizCode.trim();
    }

    public String getOrgLevel() {
        return orgLevel;
    }

    public Order2cInfo withOrgLevel(String orgLevel) {
        this.setOrgLevel(orgLevel);
        return this;
    }

    public void setOrgLevel(String orgLevel) {
        this.orgLevel = orgLevel == null ? null : orgLevel.trim();
    }

    public String getOrgName() {
        return orgName;
    }

    public Order2cInfo withOrgName(String orgName) {
        this.setOrgName(orgName);
        return this;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName == null ? null : orgName.trim();
    }

    public String getProvinceOrgName() {
        return provinceOrgName;
    }

    public Order2cInfo withProvinceOrgName(String provinceOrgName) {
        this.setProvinceOrgName(provinceOrgName);
        return this;
    }

    public void setProvinceOrgName(String provinceOrgName) {
        this.provinceOrgName = provinceOrgName == null ? null : provinceOrgName.trim();
    }

    public String getRemarks() {
        return remarks;
    }

    public Order2cInfo withRemarks(String remarks) {
        this.setRemarks(remarks);
        return this;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks == null ? null : remarks.trim();
    }

    public String getBookid() {
        return bookid;
    }

    public Order2cInfo withBookid(String bookid) {
        this.setBookid(bookid);
        return this;
    }

    public void setBookid(String bookid) {
        this.bookid = bookid == null ? null : bookid.trim();
    }

    public Integer getStatus() {
        return status;
    }

    public Order2cInfo withStatus(Integer status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getTotalPrice() {
        return totalPrice;
    }

    public Order2cInfo withTotalPrice(String totalPrice) {
        this.setTotalPrice(totalPrice);
        return this;
    }

    public void setTotalPrice(String totalPrice) {
        this.totalPrice = totalPrice == null ? null : totalPrice.trim();
    }

    public String getCreateTime() {
        return createTime;
    }

    public Order2cInfo withCreateTime(String createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime == null ? null : createTime.trim();
    }

    public String getContactPersonName() {
        return contactPersonName;
    }

    public Order2cInfo withContactPersonName(String contactPersonName) {
        this.setContactPersonName(contactPersonName);
        return this;
    }

    public void setContactPersonName(String contactPersonName) {
        this.contactPersonName = contactPersonName == null ? null : contactPersonName.trim();
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public Order2cInfo withContactPhone(String contactPhone) {
        this.setContactPhone(contactPhone);
        return this;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone == null ? null : contactPhone.trim();
    }

    public String getAddr1() {
        return addr1;
    }

    public Order2cInfo withAddr1(String addr1) {
        this.setAddr1(addr1);
        return this;
    }

    public void setAddr1(String addr1) {
        this.addr1 = addr1 == null ? null : addr1.trim();
    }

    public String getAddr2() {
        return addr2;
    }

    public Order2cInfo withAddr2(String addr2) {
        this.setAddr2(addr2);
        return this;
    }

    public void setAddr2(String addr2) {
        this.addr2 = addr2 == null ? null : addr2.trim();
    }

    public String getAddr3() {
        return addr3;
    }

    public Order2cInfo withAddr3(String addr3) {
        this.setAddr3(addr3);
        return this;
    }

    public void setAddr3(String addr3) {
        this.addr3 = addr3 == null ? null : addr3.trim();
    }

    public String getAddr4() {
        return addr4;
    }

    public Order2cInfo withAddr4(String addr4) {
        this.setAddr4(addr4);
        return this;
    }

    public void setAddr4(String addr4) {
        this.addr4 = addr4 == null ? null : addr4.trim();
    }

    public String getUsaddr() {
        return usaddr;
    }

    public Order2cInfo withUsaddr(String usaddr) {
        this.setUsaddr(usaddr);
        return this;
    }

    public void setUsaddr(String usaddr) {
        this.usaddr = usaddr == null ? null : usaddr.trim();
    }

    public String getSpuOfferingClass() {
        return spuOfferingClass;
    }

    public Order2cInfo withSpuOfferingClass(String spuOfferingClass) {
        this.setSpuOfferingClass(spuOfferingClass);
        return this;
    }

    public void setSpuOfferingClass(String spuOfferingClass) {
        this.spuOfferingClass = spuOfferingClass == null ? null : spuOfferingClass.trim();
    }

    public String getSpuOfferingCode() {
        return spuOfferingCode;
    }

    public Order2cInfo withSpuOfferingCode(String spuOfferingCode) {
        this.setSpuOfferingCode(spuOfferingCode);
        return this;
    }

    public void setSpuOfferingCode(String spuOfferingCode) {
        this.spuOfferingCode = spuOfferingCode == null ? null : spuOfferingCode.trim();
    }

    public String getSupplierCode() {
        return supplierCode;
    }

    public Order2cInfo withSupplierCode(String supplierCode) {
        this.setSupplierCode(supplierCode);
        return this;
    }

    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode == null ? null : supplierCode.trim();
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public Order2cInfo withUpdateTime(Date updateTime) {
        this.setUpdateTime(updateTime);
        return this;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public Order2cInfo withOrderStatus(Integer orderStatus) {
        this.setOrderStatus(orderStatus);
        return this;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getDeductPrice() {
        return deductPrice;
    }

    public Order2cInfo withDeductPrice(String deductPrice) {
        this.setDeductPrice(deductPrice);
        return this;
    }

    public void setDeductPrice(String deductPrice) {
        this.deductPrice = deductPrice == null ? null : deductPrice.trim();
    }

    public String getOrderingChannelSource() {
        return orderingChannelSource;
    }

    public Order2cInfo withOrderingChannelSource(String orderingChannelSource) {
        this.setOrderingChannelSource(orderingChannelSource);
        return this;
    }

    public void setOrderingChannelSource(String orderingChannelSource) {
        this.orderingChannelSource = orderingChannelSource == null ? null : orderingChannelSource.trim();
    }

    public String getOrderingChannelName() {
        return orderingChannelName;
    }

    public Order2cInfo withOrderingChannelName(String orderingChannelName) {
        this.setOrderingChannelName(orderingChannelName);
        return this;
    }

    public void setOrderingChannelName(String orderingChannelName) {
        this.orderingChannelName = orderingChannelName == null ? null : orderingChannelName.trim();
    }

    public String getSsoTerminalType() {
        return ssoTerminalType;
    }

    public Order2cInfo withSsoTerminalType(String ssoTerminalType) {
        this.setSsoTerminalType(ssoTerminalType);
        return this;
    }

    public void setSsoTerminalType(String ssoTerminalType) {
        this.ssoTerminalType = ssoTerminalType == null ? null : ssoTerminalType.trim();
    }

    public Integer getToK3() {
        return toK3;
    }

    public Order2cInfo withToK3(Integer toK3) {
        this.setToK3(toK3);
        return this;
    }

    public void setToK3(Integer toK3) {
        this.toK3 = toK3;
    }

    public Integer getSpecialAfterMarketHandle() {
        return specialAfterMarketHandle;
    }

    public Order2cInfo withSpecialAfterMarketHandle(Integer specialAfterMarketHandle) {
        this.setSpecialAfterMarketHandle(specialAfterMarketHandle);
        return this;
    }

    public void setSpecialAfterMarketHandle(Integer specialAfterMarketHandle) {
        this.specialAfterMarketHandle = specialAfterMarketHandle;
    }

    public String getSpecialAfterStatus() {
        return specialAfterStatus;
    }

    public Order2cInfo withSpecialAfterStatus(String specialAfterStatus) {
        this.setSpecialAfterStatus(specialAfterStatus);
        return this;
    }

    public void setSpecialAfterStatus(String specialAfterStatus) {
        this.specialAfterStatus = specialAfterStatus == null ? null : specialAfterStatus.trim();
    }

    public String getSpecialAfterStatusTime() {
        return specialAfterStatusTime;
    }

    public Order2cInfo withSpecialAfterStatusTime(String specialAfterStatusTime) {
        this.setSpecialAfterStatusTime(specialAfterStatusTime);
        return this;
    }

    public void setSpecialAfterStatusTime(String specialAfterStatusTime) {
        this.specialAfterStatusTime = specialAfterStatusTime == null ? null : specialAfterStatusTime.trim();
    }

    public String getSpecialAfterLatestTime() {
        return specialAfterLatestTime;
    }

    public Order2cInfo withSpecialAfterLatestTime(String specialAfterLatestTime) {
        this.setSpecialAfterLatestTime(specialAfterLatestTime);
        return this;
    }

    public void setSpecialAfterLatestTime(String specialAfterLatestTime) {
        this.specialAfterLatestTime = specialAfterLatestTime == null ? null : specialAfterLatestTime.trim();
    }

    public String getSyncK3Id() {
        return syncK3Id;
    }

    public Order2cInfo withSyncK3Id(String syncK3Id) {
        this.setSyncK3Id(syncK3Id);
        return this;
    }

    public void setSyncK3Id(String syncK3Id) {
        this.syncK3Id = syncK3Id == null ? null : syncK3Id.trim();
    }

    public Date getPayTime() {
        return payTime;
    }

    public Order2cInfo withPayTime(Date payTime) {
        this.setPayTime(payTime);
        return this;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Date getRefundTime() {
        return refundTime;
    }

    public Order2cInfo withRefundTime(Date refundTime) {
        this.setRefundTime(refundTime);
        return this;
    }

    public void setRefundTime(Date refundTime) {
        this.refundTime = refundTime;
    }

    public String getValetOrderCompleteTime() {
        return valetOrderCompleteTime;
    }

    public Order2cInfo withValetOrderCompleteTime(String valetOrderCompleteTime) {
        this.setValetOrderCompleteTime(valetOrderCompleteTime);
        return this;
    }

    public void setValetOrderCompleteTime(String valetOrderCompleteTime) {
        this.valetOrderCompleteTime = valetOrderCompleteTime == null ? null : valetOrderCompleteTime.trim();
    }

    public String getBillLadderType() {
        return billLadderType;
    }

    public Order2cInfo withBillLadderType(String billLadderType) {
        this.setBillLadderType(billLadderType);
        return this;
    }

    public void setBillLadderType(String billLadderType) {
        this.billLadderType = billLadderType == null ? null : billLadderType.trim();
    }

    public Integer getQlyStatus() {
        return qlyStatus;
    }

    public Order2cInfo withQlyStatus(Integer qlyStatus) {
        this.setQlyStatus(qlyStatus);
        return this;
    }

    public void setQlyStatus(Integer qlyStatus) {
        this.qlyStatus = qlyStatus;
    }

    public Integer getYsxStatus() {
        return ysxStatus;
    }

    public Order2cInfo withYsxStatus(Integer ysxStatus) {
        this.setYsxStatus(ysxStatus);
        return this;
    }

    public void setYsxStatus(Integer ysxStatus) {
        this.ysxStatus = ysxStatus;
    }

    public Integer getKxRefundStatus() {
        return kxRefundStatus;
    }

    public Order2cInfo withKxRefundStatus(Integer kxRefundStatus) {
        this.setKxRefundStatus(kxRefundStatus);
        return this;
    }

    public void setKxRefundStatus(Integer kxRefundStatus) {
        this.kxRefundStatus = kxRefundStatus;
    }

    public String getKxRefundReason() {
        return kxRefundReason;
    }

    public Order2cInfo withKxRefundReason(String kxRefundReason) {
        this.setKxRefundReason(kxRefundReason);
        return this;
    }

    public void setKxRefundReason(String kxRefundReason) {
        this.kxRefundReason = kxRefundReason == null ? null : kxRefundReason.trim();
    }

    public String getSpuOfferingVersion() {
        return spuOfferingVersion;
    }

    public Order2cInfo withSpuOfferingVersion(String spuOfferingVersion) {
        this.setSpuOfferingVersion(spuOfferingVersion);
        return this;
    }

    public void setSpuOfferingVersion(String spuOfferingVersion) {
        this.spuOfferingVersion = spuOfferingVersion == null ? null : spuOfferingVersion.trim();
    }

    public String getReserveBeId() {
        return reserveBeId;
    }

    public Order2cInfo withReserveBeId(String reserveBeId) {
        this.setReserveBeId(reserveBeId);
        return this;
    }

    public void setReserveBeId(String reserveBeId) {
        this.reserveBeId = reserveBeId == null ? null : reserveBeId.trim();
    }

    public String getReserveLocation() {
        return reserveLocation;
    }

    public Order2cInfo withReserveLocation(String reserveLocation) {
        this.setReserveLocation(reserveLocation);
        return this;
    }

    public void setReserveLocation(String reserveLocation) {
        this.reserveLocation = reserveLocation == null ? null : reserveLocation.trim();
    }

    public String getSpecialAfterRefundsNumber() {
        return specialAfterRefundsNumber;
    }

    public Order2cInfo withSpecialAfterRefundsNumber(String specialAfterRefundsNumber) {
        this.setSpecialAfterRefundsNumber(specialAfterRefundsNumber);
        return this;
    }

    public void setSpecialAfterRefundsNumber(String specialAfterRefundsNumber) {
        this.specialAfterRefundsNumber = specialAfterRefundsNumber == null ? null : specialAfterRefundsNumber.trim();
    }

    public String getSpuListPlatform() {
        return spuListPlatform;
    }

    public Order2cInfo withSpuListPlatform(String spuListPlatform) {
        this.setSpuListPlatform(spuListPlatform);
        return this;
    }

    public void setSpuListPlatform(String spuListPlatform) {
        this.spuListPlatform = spuListPlatform == null ? null : spuListPlatform.trim();
    }

    public String getBillNoNumber() {
        return billNoNumber;
    }

    public Order2cInfo withBillNoNumber(String billNoNumber) {
        this.setBillNoNumber(billNoNumber);
        return this;
    }

    public void setBillNoNumber(String billNoNumber) {
        this.billNoNumber = billNoNumber == null ? null : billNoNumber.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", orderId=").append(orderId);
        sb.append(", orderType=").append(orderType);
        sb.append(", businessCode=").append(businessCode);
        sb.append(", createOperCode=").append(createOperCode);
        sb.append(", createOperUserId=").append(createOperUserId);
        sb.append(", employeeNum=").append(employeeNum);
        sb.append(", custMgName=").append(custMgName);
        sb.append(", custMgPhone=").append(custMgPhone);
        sb.append(", orderStatusTime=").append(orderStatusTime);
        sb.append(", custCode=").append(custCode);
        sb.append(", custUserId=").append(custUserId);
        sb.append(", custName=").append(custName);
        sb.append(", beId=").append(beId);
        sb.append(", location=").append(location);
        sb.append(", regionId=").append(regionId);
        sb.append(", orderOrgBizCode=").append(orderOrgBizCode);
        sb.append(", orgLevel=").append(orgLevel);
        sb.append(", orgName=").append(orgName);
        sb.append(", provinceOrgName=").append(provinceOrgName);
        sb.append(", remarks=").append(remarks);
        sb.append(", bookid=").append(bookid);
        sb.append(", status=").append(status);
        sb.append(", totalPrice=").append(totalPrice);
        sb.append(", createTime=").append(createTime);
        sb.append(", contactPersonName=").append(contactPersonName);
        sb.append(", contactPhone=").append(contactPhone);
        sb.append(", addr1=").append(addr1);
        sb.append(", addr2=").append(addr2);
        sb.append(", addr3=").append(addr3);
        sb.append(", addr4=").append(addr4);
        sb.append(", usaddr=").append(usaddr);
        sb.append(", spuOfferingClass=").append(spuOfferingClass);
        sb.append(", spuOfferingCode=").append(spuOfferingCode);
        sb.append(", supplierCode=").append(supplierCode);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", orderStatus=").append(orderStatus);
        sb.append(", deductPrice=").append(deductPrice);
        sb.append(", orderingChannelSource=").append(orderingChannelSource);
        sb.append(", orderingChannelName=").append(orderingChannelName);
        sb.append(", ssoTerminalType=").append(ssoTerminalType);
        sb.append(", toK3=").append(toK3);
        sb.append(", specialAfterMarketHandle=").append(specialAfterMarketHandle);
        sb.append(", specialAfterStatus=").append(specialAfterStatus);
        sb.append(", specialAfterStatusTime=").append(specialAfterStatusTime);
        sb.append(", specialAfterLatestTime=").append(specialAfterLatestTime);
        sb.append(", syncK3Id=").append(syncK3Id);
        sb.append(", payTime=").append(payTime);
        sb.append(", refundTime=").append(refundTime);
        sb.append(", valetOrderCompleteTime=").append(valetOrderCompleteTime);
        sb.append(", billLadderType=").append(billLadderType);
        sb.append(", qlyStatus=").append(qlyStatus);
        sb.append(", ysxStatus=").append(ysxStatus);
        sb.append(", kxRefundStatus=").append(kxRefundStatus);
        sb.append(", kxRefundReason=").append(kxRefundReason);
        sb.append(", spuOfferingVersion=").append(spuOfferingVersion);
        sb.append(", reserveBeId=").append(reserveBeId);
        sb.append(", reserveLocation=").append(reserveLocation);
        sb.append(", specialAfterRefundsNumber=").append(specialAfterRefundsNumber);
        sb.append(", spuListPlatform=").append(spuListPlatform);
        sb.append(", billNoNumber=").append(billNoNumber);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Order2cInfo other = (Order2cInfo) that;
        return (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getOrderType() == null ? other.getOrderType() == null : this.getOrderType().equals(other.getOrderType()))
            && (this.getBusinessCode() == null ? other.getBusinessCode() == null : this.getBusinessCode().equals(other.getBusinessCode()))
            && (this.getCreateOperCode() == null ? other.getCreateOperCode() == null : this.getCreateOperCode().equals(other.getCreateOperCode()))
            && (this.getCreateOperUserId() == null ? other.getCreateOperUserId() == null : this.getCreateOperUserId().equals(other.getCreateOperUserId()))
            && (this.getEmployeeNum() == null ? other.getEmployeeNum() == null : this.getEmployeeNum().equals(other.getEmployeeNum()))
            && (this.getCustMgName() == null ? other.getCustMgName() == null : this.getCustMgName().equals(other.getCustMgName()))
            && (this.getCustMgPhone() == null ? other.getCustMgPhone() == null : this.getCustMgPhone().equals(other.getCustMgPhone()))
            && (this.getOrderStatusTime() == null ? other.getOrderStatusTime() == null : this.getOrderStatusTime().equals(other.getOrderStatusTime()))
            && (this.getCustCode() == null ? other.getCustCode() == null : this.getCustCode().equals(other.getCustCode()))
            && (this.getCustUserId() == null ? other.getCustUserId() == null : this.getCustUserId().equals(other.getCustUserId()))
            && (this.getCustName() == null ? other.getCustName() == null : this.getCustName().equals(other.getCustName()))
            && (this.getBeId() == null ? other.getBeId() == null : this.getBeId().equals(other.getBeId()))
            && (this.getLocation() == null ? other.getLocation() == null : this.getLocation().equals(other.getLocation()))
            && (this.getRegionId() == null ? other.getRegionId() == null : this.getRegionId().equals(other.getRegionId()))
            && (this.getOrderOrgBizCode() == null ? other.getOrderOrgBizCode() == null : this.getOrderOrgBizCode().equals(other.getOrderOrgBizCode()))
            && (this.getOrgLevel() == null ? other.getOrgLevel() == null : this.getOrgLevel().equals(other.getOrgLevel()))
            && (this.getOrgName() == null ? other.getOrgName() == null : this.getOrgName().equals(other.getOrgName()))
            && (this.getProvinceOrgName() == null ? other.getProvinceOrgName() == null : this.getProvinceOrgName().equals(other.getProvinceOrgName()))
            && (this.getRemarks() == null ? other.getRemarks() == null : this.getRemarks().equals(other.getRemarks()))
            && (this.getBookid() == null ? other.getBookid() == null : this.getBookid().equals(other.getBookid()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getTotalPrice() == null ? other.getTotalPrice() == null : this.getTotalPrice().equals(other.getTotalPrice()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getContactPersonName() == null ? other.getContactPersonName() == null : this.getContactPersonName().equals(other.getContactPersonName()))
            && (this.getContactPhone() == null ? other.getContactPhone() == null : this.getContactPhone().equals(other.getContactPhone()))
            && (this.getAddr1() == null ? other.getAddr1() == null : this.getAddr1().equals(other.getAddr1()))
            && (this.getAddr2() == null ? other.getAddr2() == null : this.getAddr2().equals(other.getAddr2()))
            && (this.getAddr3() == null ? other.getAddr3() == null : this.getAddr3().equals(other.getAddr3()))
            && (this.getAddr4() == null ? other.getAddr4() == null : this.getAddr4().equals(other.getAddr4()))
            && (this.getUsaddr() == null ? other.getUsaddr() == null : this.getUsaddr().equals(other.getUsaddr()))
            && (this.getSpuOfferingClass() == null ? other.getSpuOfferingClass() == null : this.getSpuOfferingClass().equals(other.getSpuOfferingClass()))
            && (this.getSpuOfferingCode() == null ? other.getSpuOfferingCode() == null : this.getSpuOfferingCode().equals(other.getSpuOfferingCode()))
            && (this.getSupplierCode() == null ? other.getSupplierCode() == null : this.getSupplierCode().equals(other.getSupplierCode()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getOrderStatus() == null ? other.getOrderStatus() == null : this.getOrderStatus().equals(other.getOrderStatus()))
            && (this.getDeductPrice() == null ? other.getDeductPrice() == null : this.getDeductPrice().equals(other.getDeductPrice()))
            && (this.getOrderingChannelSource() == null ? other.getOrderingChannelSource() == null : this.getOrderingChannelSource().equals(other.getOrderingChannelSource()))
            && (this.getOrderingChannelName() == null ? other.getOrderingChannelName() == null : this.getOrderingChannelName().equals(other.getOrderingChannelName()))
            && (this.getSsoTerminalType() == null ? other.getSsoTerminalType() == null : this.getSsoTerminalType().equals(other.getSsoTerminalType()))
            && (this.getToK3() == null ? other.getToK3() == null : this.getToK3().equals(other.getToK3()))
            && (this.getSpecialAfterMarketHandle() == null ? other.getSpecialAfterMarketHandle() == null : this.getSpecialAfterMarketHandle().equals(other.getSpecialAfterMarketHandle()))
            && (this.getSpecialAfterStatus() == null ? other.getSpecialAfterStatus() == null : this.getSpecialAfterStatus().equals(other.getSpecialAfterStatus()))
            && (this.getSpecialAfterStatusTime() == null ? other.getSpecialAfterStatusTime() == null : this.getSpecialAfterStatusTime().equals(other.getSpecialAfterStatusTime()))
            && (this.getSpecialAfterLatestTime() == null ? other.getSpecialAfterLatestTime() == null : this.getSpecialAfterLatestTime().equals(other.getSpecialAfterLatestTime()))
            && (this.getSyncK3Id() == null ? other.getSyncK3Id() == null : this.getSyncK3Id().equals(other.getSyncK3Id()))
            && (this.getPayTime() == null ? other.getPayTime() == null : this.getPayTime().equals(other.getPayTime()))
            && (this.getRefundTime() == null ? other.getRefundTime() == null : this.getRefundTime().equals(other.getRefundTime()))
            && (this.getValetOrderCompleteTime() == null ? other.getValetOrderCompleteTime() == null : this.getValetOrderCompleteTime().equals(other.getValetOrderCompleteTime()))
            && (this.getBillLadderType() == null ? other.getBillLadderType() == null : this.getBillLadderType().equals(other.getBillLadderType()))
            && (this.getQlyStatus() == null ? other.getQlyStatus() == null : this.getQlyStatus().equals(other.getQlyStatus()))
            && (this.getYsxStatus() == null ? other.getYsxStatus() == null : this.getYsxStatus().equals(other.getYsxStatus()))
            && (this.getKxRefundStatus() == null ? other.getKxRefundStatus() == null : this.getKxRefundStatus().equals(other.getKxRefundStatus()))
            && (this.getKxRefundReason() == null ? other.getKxRefundReason() == null : this.getKxRefundReason().equals(other.getKxRefundReason()))
            && (this.getSpuOfferingVersion() == null ? other.getSpuOfferingVersion() == null : this.getSpuOfferingVersion().equals(other.getSpuOfferingVersion()))
            && (this.getReserveBeId() == null ? other.getReserveBeId() == null : this.getReserveBeId().equals(other.getReserveBeId()))
            && (this.getReserveLocation() == null ? other.getReserveLocation() == null : this.getReserveLocation().equals(other.getReserveLocation()))
            && (this.getSpecialAfterRefundsNumber() == null ? other.getSpecialAfterRefundsNumber() == null : this.getSpecialAfterRefundsNumber().equals(other.getSpecialAfterRefundsNumber()))
            && (this.getSpuListPlatform() == null ? other.getSpuListPlatform() == null : this.getSpuListPlatform().equals(other.getSpuListPlatform()))
            && (this.getBillNoNumber() == null ? other.getBillNoNumber() == null : this.getBillNoNumber().equals(other.getBillNoNumber()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getOrderType() == null) ? 0 : getOrderType().hashCode());
        result = prime * result + ((getBusinessCode() == null) ? 0 : getBusinessCode().hashCode());
        result = prime * result + ((getCreateOperCode() == null) ? 0 : getCreateOperCode().hashCode());
        result = prime * result + ((getCreateOperUserId() == null) ? 0 : getCreateOperUserId().hashCode());
        result = prime * result + ((getEmployeeNum() == null) ? 0 : getEmployeeNum().hashCode());
        result = prime * result + ((getCustMgName() == null) ? 0 : getCustMgName().hashCode());
        result = prime * result + ((getCustMgPhone() == null) ? 0 : getCustMgPhone().hashCode());
        result = prime * result + ((getOrderStatusTime() == null) ? 0 : getOrderStatusTime().hashCode());
        result = prime * result + ((getCustCode() == null) ? 0 : getCustCode().hashCode());
        result = prime * result + ((getCustUserId() == null) ? 0 : getCustUserId().hashCode());
        result = prime * result + ((getCustName() == null) ? 0 : getCustName().hashCode());
        result = prime * result + ((getBeId() == null) ? 0 : getBeId().hashCode());
        result = prime * result + ((getLocation() == null) ? 0 : getLocation().hashCode());
        result = prime * result + ((getRegionId() == null) ? 0 : getRegionId().hashCode());
        result = prime * result + ((getOrderOrgBizCode() == null) ? 0 : getOrderOrgBizCode().hashCode());
        result = prime * result + ((getOrgLevel() == null) ? 0 : getOrgLevel().hashCode());
        result = prime * result + ((getOrgName() == null) ? 0 : getOrgName().hashCode());
        result = prime * result + ((getProvinceOrgName() == null) ? 0 : getProvinceOrgName().hashCode());
        result = prime * result + ((getRemarks() == null) ? 0 : getRemarks().hashCode());
        result = prime * result + ((getBookid() == null) ? 0 : getBookid().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getTotalPrice() == null) ? 0 : getTotalPrice().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getContactPersonName() == null) ? 0 : getContactPersonName().hashCode());
        result = prime * result + ((getContactPhone() == null) ? 0 : getContactPhone().hashCode());
        result = prime * result + ((getAddr1() == null) ? 0 : getAddr1().hashCode());
        result = prime * result + ((getAddr2() == null) ? 0 : getAddr2().hashCode());
        result = prime * result + ((getAddr3() == null) ? 0 : getAddr3().hashCode());
        result = prime * result + ((getAddr4() == null) ? 0 : getAddr4().hashCode());
        result = prime * result + ((getUsaddr() == null) ? 0 : getUsaddr().hashCode());
        result = prime * result + ((getSpuOfferingClass() == null) ? 0 : getSpuOfferingClass().hashCode());
        result = prime * result + ((getSpuOfferingCode() == null) ? 0 : getSpuOfferingCode().hashCode());
        result = prime * result + ((getSupplierCode() == null) ? 0 : getSupplierCode().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getOrderStatus() == null) ? 0 : getOrderStatus().hashCode());
        result = prime * result + ((getDeductPrice() == null) ? 0 : getDeductPrice().hashCode());
        result = prime * result + ((getOrderingChannelSource() == null) ? 0 : getOrderingChannelSource().hashCode());
        result = prime * result + ((getOrderingChannelName() == null) ? 0 : getOrderingChannelName().hashCode());
        result = prime * result + ((getSsoTerminalType() == null) ? 0 : getSsoTerminalType().hashCode());
        result = prime * result + ((getToK3() == null) ? 0 : getToK3().hashCode());
        result = prime * result + ((getSpecialAfterMarketHandle() == null) ? 0 : getSpecialAfterMarketHandle().hashCode());
        result = prime * result + ((getSpecialAfterStatus() == null) ? 0 : getSpecialAfterStatus().hashCode());
        result = prime * result + ((getSpecialAfterStatusTime() == null) ? 0 : getSpecialAfterStatusTime().hashCode());
        result = prime * result + ((getSpecialAfterLatestTime() == null) ? 0 : getSpecialAfterLatestTime().hashCode());
        result = prime * result + ((getSyncK3Id() == null) ? 0 : getSyncK3Id().hashCode());
        result = prime * result + ((getPayTime() == null) ? 0 : getPayTime().hashCode());
        result = prime * result + ((getRefundTime() == null) ? 0 : getRefundTime().hashCode());
        result = prime * result + ((getValetOrderCompleteTime() == null) ? 0 : getValetOrderCompleteTime().hashCode());
        result = prime * result + ((getBillLadderType() == null) ? 0 : getBillLadderType().hashCode());
        result = prime * result + ((getQlyStatus() == null) ? 0 : getQlyStatus().hashCode());
        result = prime * result + ((getYsxStatus() == null) ? 0 : getYsxStatus().hashCode());
        result = prime * result + ((getKxRefundStatus() == null) ? 0 : getKxRefundStatus().hashCode());
        result = prime * result + ((getKxRefundReason() == null) ? 0 : getKxRefundReason().hashCode());
        result = prime * result + ((getSpuOfferingVersion() == null) ? 0 : getSpuOfferingVersion().hashCode());
        result = prime * result + ((getReserveBeId() == null) ? 0 : getReserveBeId().hashCode());
        result = prime * result + ((getReserveLocation() == null) ? 0 : getReserveLocation().hashCode());
        result = prime * result + ((getSpecialAfterRefundsNumber() == null) ? 0 : getSpecialAfterRefundsNumber().hashCode());
        result = prime * result + ((getSpuListPlatform() == null) ? 0 : getSpuListPlatform().hashCode());
        result = prime * result + ((getBillNoNumber() == null) ? 0 : getBillNoNumber().hashCode());
        return result;
    }

    public enum Column {
        orderId("order_id", "orderId", "VARCHAR", false),
        orderType("order_type", "orderType", "VARCHAR", false),
        businessCode("business_code", "businessCode", "VARCHAR", false),
        createOperCode("create_oper_code", "createOperCode", "VARCHAR", false),
        createOperUserId("create_oper_user_id", "createOperUserId", "VARCHAR", false),
        employeeNum("employee_num", "employeeNum", "VARCHAR", false),
        custMgName("cust_mg_name", "custMgName", "VARCHAR", false),
        custMgPhone("cust_mg_phone", "custMgPhone", "VARCHAR", false),
        orderStatusTime("order_status_time", "orderStatusTime", "TIMESTAMP", false),
        custCode("cust_code", "custCode", "VARCHAR", false),
        custUserId("cust_user_id", "custUserId", "VARCHAR", false),
        custName("cust_name", "custName", "VARCHAR", false),
        beId("be_id", "beId", "VARCHAR", false),
        location("location", "location", "VARCHAR", false),
        regionId("region_ID", "regionId", "VARCHAR", false),
        orderOrgBizCode("order_org_biz_code", "orderOrgBizCode", "VARCHAR", false),
        orgLevel("org_level", "orgLevel", "VARCHAR", false),
        orgName("org_name", "orgName", "VARCHAR", false),
        provinceOrgName("province_org_name", "provinceOrgName", "VARCHAR", false),
        remarks("remarks", "remarks", "VARCHAR", false),
        bookid("bookId", "bookid", "VARCHAR", false),
        status("status", "status", "INTEGER", false),
        totalPrice("total_price", "totalPrice", "VARCHAR", false),
        createTime("create_time", "createTime", "VARCHAR", false),
        contactPersonName("contact_person_name", "contactPersonName", "VARCHAR", false),
        contactPhone("contact_phone", "contactPhone", "VARCHAR", false),
        addr1("addr1", "addr1", "VARCHAR", false),
        addr2("addr2", "addr2", "VARCHAR", false),
        addr3("addr3", "addr3", "VARCHAR", false),
        addr4("addr4", "addr4", "VARCHAR", false),
        usaddr("usaddr", "usaddr", "VARCHAR", false),
        spuOfferingClass("spu_offering_class", "spuOfferingClass", "VARCHAR", false),
        spuOfferingCode("spu_offering_code", "spuOfferingCode", "VARCHAR", false),
        supplierCode("supplier_code", "supplierCode", "VARCHAR", false),
        updateTime("update_time", "updateTime", "TIMESTAMP", false),
        orderStatus("order_status", "orderStatus", "INTEGER", false),
        deductPrice("deduct_price", "deductPrice", "VARCHAR", false),
        orderingChannelSource("ordering_channel_source", "orderingChannelSource", "VARCHAR", false),
        orderingChannelName("ordering_channel_name", "orderingChannelName", "VARCHAR", false),
        ssoTerminalType("sso_terminal_type", "ssoTerminalType", "VARCHAR", false),
        toK3("to_k3", "toK3", "INTEGER", false),
        specialAfterMarketHandle("special_after_market_handle", "specialAfterMarketHandle", "INTEGER", false),
        specialAfterStatus("special_after_status", "specialAfterStatus", "VARCHAR", false),
        specialAfterStatusTime("special_after_status_time", "specialAfterStatusTime", "VARCHAR", false),
        specialAfterLatestTime("special_after_latest_time", "specialAfterLatestTime", "VARCHAR", false),
        syncK3Id("sync_k3_id", "syncK3Id", "VARCHAR", false),
        payTime("pay_time", "payTime", "TIMESTAMP", false),
        refundTime("refund_time", "refundTime", "TIMESTAMP", false),
        valetOrderCompleteTime("valet_order_complete_time", "valetOrderCompleteTime", "VARCHAR", false),
        billLadderType("bill_ladder_type", "billLadderType", "VARCHAR", false),
        qlyStatus("qly_status", "qlyStatus", "INTEGER", false),
        ysxStatus("ysx_status", "ysxStatus", "INTEGER", false),
        kxRefundStatus("kx_refund_status", "kxRefundStatus", "INTEGER", false),
        kxRefundReason("kx_refund_reason", "kxRefundReason", "VARCHAR", false),
        spuOfferingVersion("spu_offering_version", "spuOfferingVersion", "VARCHAR", false),
        reserveBeId("reserve_be_id", "reserveBeId", "VARCHAR", false),
        reserveLocation("reserve_location", "reserveLocation", "VARCHAR", false),
        specialAfterRefundsNumber("special_after_refunds_number", "specialAfterRefundsNumber", "VARCHAR", false),
        spuListPlatform("spu_list_platform", "spuListPlatform", "VARCHAR", false),
        billNoNumber("bill_no_number", "billNoNumber", "VARCHAR", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}