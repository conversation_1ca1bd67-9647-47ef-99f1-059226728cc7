package com.chinamobile.iot.sc.service.soa.token;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * This class was generated by Apache CXF 3.5.2
 * 2022-06-20T15:46:38.723+08:00
 * Generated source version: 3.5.2
 *
 */
@WebService(targetNamespace = "http://soa.cmcc.com/OSB_BP_SOA_HQ_InquiryServiceTokenSrv", name = "OSB_BP_SOA_HQ_InquiryServiceTokenSrv")
@XmlSeeAlso({com.chinamobile.iot.sc.service.soa.token.header.ObjectFactory.class, ObjectFactory.class})
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
public interface OSBBPSOAHQInquiryServiceTokenSrv {

    @WebMethod(action = "process")
    @WebResult(name = "OutputParameters", targetNamespace = "http://soa.cmcc.com/OSB_BP_SOA_HQ_InquiryServiceTokenSrv", partName = "payload")
    public OutputParameters process(

        @WebParam(partName = "payload", name = "InputParameters", targetNamespace = "http://soa.cmcc.com/OSB_BP_SOA_HQ_InquiryServiceTokenSrv")
        InputParameters payload
    );
}
