package com.chinamobile.iot.sc.pojo.mapper;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/18
 * @description k3同步信息
 */
@Data
public class K3SynDO {

    private String id;

    /**
     * 合同编码
     */
    private String contractNum;

    /**
     * 销售单位
     */
    private String sellUnit;

    /**
     * 合同销售员
     */
    private String contractSeller;

    /**
     * 订单收入归属省份代码
     */
    private String orderProvinceCode;

    /**
     * 订单收入归属地市代码
     */
    private String orderCityCode;

    /**
     * 客户
     */
    private String customCode;


    /**
     * 合同类型
     */
    private String contractType;

    /**
     * 销售员组织Id
     */
    private String sellerOrgId;

    /**
     * 销售员团队Id
     */
    private String sellerTeamId;

    /**
     * 销售员部门Id
     */
    private String sellerDeptId;

    /**
     * 销售员电话
     */
    private String sellerPhone;

    /**
     * 成本中心
     */
    private String costCenter;

    /**
     * 合同相对方省份代码
     */
    private String buyerProvinceCode;

    /**
     * 合同相对方城市代码
     */
    private String buyerCityCode;

    /**
     * 项目
     */
    private String project;

    /**
     * 子项目
     */
    private String subProject;

    /**
     * 币种
     */
    private String moneyUnit;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 省销售数据编码
     */
    private String proDataCode;

    /**
     * 物料部门
     */
    private String materialDept;
}
