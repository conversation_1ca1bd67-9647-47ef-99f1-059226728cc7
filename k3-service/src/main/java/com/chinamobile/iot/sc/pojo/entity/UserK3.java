package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

/**
 * Created By MBG-GUI-EXTENSION https://github.com/spawpaw/mybatis-generator-gui-extension
 * Description:
 * 
 *
 * <AUTHOR>
public class UserK3 implements Serializable {
    /**
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private String id;

    /**
     * 用户账号
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private String account;

    /**
     * 用户姓名
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private String name;

    /**
     * 部门
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private String department;

    /**
     * 团队
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private String team;

    /**
     * 手机
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private String phone;

    /**
     * 邮箱
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private String email;

    /**
     * 老工号
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private String usercode;

    /**
     * 新工号
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private String ihrusercode;

    /**
     * 创建人
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private String createor;

    /**
     * 项目
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private String project;

    /**
     * 成本中心编码
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private String costcenter;

    /**
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private String costcentername;

    /**
     * 销售员部门
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private String sellerdept;

    /**
     * 销售员团队
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private String sellerteam;

    /**
     * 创建时间
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private Date createtime;

    /**
     * 更新时间
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private Date updatetime;

    /**
     * 省名称
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private String provincename;

    /**
     * 省编码
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private String provincecode;

    /**
     * 用户类型1 :销售员
     *
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private String usertype;

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method returns the value of the database column supply_chain..user_k3.id
     *
     * @return the value of supply_chain..user_k3.id
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public String getId() {
        return id;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withId(String id) {
        this.setId(id);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.id
     *
     * @param id the value for supply_chain..user_k3.id
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * This method returns the value of the database column supply_chain..user_k3.account
     *
     * @return the value of supply_chain..user_k3.account
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public String getAccount() {
        return account;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withAccount(String account) {
        this.setAccount(account);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.account
     *
     * @param account the value for supply_chain..user_k3.account
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setAccount(String account) {
        this.account = account;
    }

    /**
     * This method returns the value of the database column supply_chain..user_k3.name
     *
     * @return the value of supply_chain..user_k3.name
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withName(String name) {
        this.setName(name);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.name
     *
     * @param name the value for supply_chain..user_k3.name
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method returns the value of the database column supply_chain..user_k3.department
     *
     * @return the value of supply_chain..user_k3.department
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public String getDepartment() {
        return department;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withDepartment(String department) {
        this.setDepartment(department);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.department
     *
     * @param department the value for supply_chain..user_k3.department
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setDepartment(String department) {
        this.department = department;
    }

    /**
     * This method returns the value of the database column supply_chain..user_k3.team
     *
     * @return the value of supply_chain..user_k3.team
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public String getTeam() {
        return team;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withTeam(String team) {
        this.setTeam(team);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.team
     *
     * @param team the value for supply_chain..user_k3.team
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setTeam(String team) {
        this.team = team;
    }

    /**
     * This method returns the value of the database column supply_chain..user_k3.phone
     *
     * @return the value of supply_chain..user_k3.phone
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public String getPhone() {
        return phone;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withPhone(String phone) {
        this.setPhone(phone);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.phone
     *
     * @param phone the value for supply_chain..user_k3.phone
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * This method returns the value of the database column supply_chain..user_k3.email
     *
     * @return the value of supply_chain..user_k3.email
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public String getEmail() {
        return email;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withEmail(String email) {
        this.setEmail(email);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.email
     *
     * @param email the value for supply_chain..user_k3.email
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * This method returns the value of the database column supply_chain..user_k3.usercode
     *
     * @return the value of supply_chain..user_k3.usercode
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public String getUsercode() {
        return usercode;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withUsercode(String usercode) {
        this.setUsercode(usercode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.usercode
     *
     * @param usercode the value for supply_chain..user_k3.usercode
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setUsercode(String usercode) {
        this.usercode = usercode;
    }

    /**
     * This method returns the value of the database column supply_chain..user_k3.ihrusercode
     *
     * @return the value of supply_chain..user_k3.ihrusercode
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public String getIhrusercode() {
        return ihrusercode;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withIhrusercode(String ihrusercode) {
        this.setIhrusercode(ihrusercode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.ihrusercode
     *
     * @param ihrusercode the value for supply_chain..user_k3.ihrusercode
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setIhrusercode(String ihrusercode) {
        this.ihrusercode = ihrusercode;
    }

    /**
     * This method returns the value of the database column supply_chain..user_k3.createor
     *
     * @return the value of supply_chain..user_k3.createor
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public String getCreateor() {
        return createor;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withCreateor(String createor) {
        this.setCreateor(createor);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.createor
     *
     * @param createor the value for supply_chain..user_k3.createor
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setCreateor(String createor) {
        this.createor = createor;
    }

    /**
     * This method returns the value of the database column supply_chain..user_k3.project
     *
     * @return the value of supply_chain..user_k3.project
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public String getProject() {
        return project;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withProject(String project) {
        this.setProject(project);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.project
     *
     * @param project the value for supply_chain..user_k3.project
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setProject(String project) {
        this.project = project;
    }

    /**
     * This method returns the value of the database column supply_chain..user_k3.costcenter
     *
     * @return the value of supply_chain..user_k3.costcenter
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public String getCostcenter() {
        return costcenter;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withCostcenter(String costcenter) {
        this.setCostcenter(costcenter);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.costcenter
     *
     * @param costcenter the value for supply_chain..user_k3.costcenter
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setCostcenter(String costcenter) {
        this.costcenter = costcenter;
    }

    /**
     * This method returns the value of the database column supply_chain..user_k3.costcenterName
     *
     * @return the value of supply_chain..user_k3.costcenterName
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public String getCostcentername() {
        return costcentername;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withCostcentername(String costcentername) {
        this.setCostcentername(costcentername);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.costcenterName
     *
     * @param costcentername the value for supply_chain..user_k3.costcenterName
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setCostcentername(String costcentername) {
        this.costcentername = costcentername;
    }

    /**
     * This method returns the value of the database column supply_chain..user_k3.sellerdept
     *
     * @return the value of supply_chain..user_k3.sellerdept
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public String getSellerdept() {
        return sellerdept;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withSellerdept(String sellerdept) {
        this.setSellerdept(sellerdept);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.sellerdept
     *
     * @param sellerdept the value for supply_chain..user_k3.sellerdept
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setSellerdept(String sellerdept) {
        this.sellerdept = sellerdept;
    }

    /**
     * This method returns the value of the database column supply_chain..user_k3.sellerteam
     *
     * @return the value of supply_chain..user_k3.sellerteam
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public String getSellerteam() {
        return sellerteam;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withSellerteam(String sellerteam) {
        this.setSellerteam(sellerteam);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.sellerteam
     *
     * @param sellerteam the value for supply_chain..user_k3.sellerteam
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setSellerteam(String sellerteam) {
        this.sellerteam = sellerteam;
    }

    /**
     * This method returns the value of the database column supply_chain..user_k3.createtime
     *
     * @return the value of supply_chain..user_k3.createtime
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public Date getCreatetime() {
        return createtime;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withCreatetime(Date createtime) {
        this.setCreatetime(createtime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.createtime
     *
     * @param createtime the value for supply_chain..user_k3.createtime
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    /**
     * This method returns the value of the database column supply_chain..user_k3.updatetime
     *
     * @return the value of supply_chain..user_k3.updatetime
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public Date getUpdatetime() {
        return updatetime;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withUpdatetime(Date updatetime) {
        this.setUpdatetime(updatetime);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.updatetime
     *
     * @param updatetime the value for supply_chain..user_k3.updatetime
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setUpdatetime(Date updatetime) {
        this.updatetime = updatetime;
    }

    /**
     * This method returns the value of the database column supply_chain..user_k3.provincename
     *
     * @return the value of supply_chain..user_k3.provincename
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public String getProvincename() {
        return provincename;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withProvincename(String provincename) {
        this.setProvincename(provincename);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.provincename
     *
     * @param provincename the value for supply_chain..user_k3.provincename
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setProvincename(String provincename) {
        this.provincename = provincename;
    }

    /**
     * This method returns the value of the database column supply_chain..user_k3.provincecode
     *
     * @return the value of supply_chain..user_k3.provincecode
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public String getProvincecode() {
        return provincecode;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withProvincecode(String provincecode) {
        this.setProvincecode(provincecode);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.provincecode
     *
     * @param provincecode the value for supply_chain..user_k3.provincecode
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setProvincecode(String provincecode) {
        this.provincecode = provincecode;
    }

    /**
     * This method returns the value of the database column supply_chain..user_k3.usertype
     *
     * @return the value of supply_chain..user_k3.usertype
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public String getUsertype() {
        return usertype;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3 withUsertype(String usertype) {
        this.setUsertype(usertype);
        return this;
    }

    /**
     * This method sets the value of the database column supply_chain..user_k3.usertype
     *
     * @param usertype the value for supply_chain..user_k3.usertype
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setUsertype(String usertype) {
        this.usertype = usertype;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", account=").append(account);
        sb.append(", name=").append(name);
        sb.append(", department=").append(department);
        sb.append(", team=").append(team);
        sb.append(", phone=").append(phone);
        sb.append(", email=").append(email);
        sb.append(", usercode=").append(usercode);
        sb.append(", ihrusercode=").append(ihrusercode);
        sb.append(", createor=").append(createor);
        sb.append(", project=").append(project);
        sb.append(", costcenter=").append(costcenter);
        sb.append(", costcentername=").append(costcentername);
        sb.append(", sellerdept=").append(sellerdept);
        sb.append(", sellerteam=").append(sellerteam);
        sb.append(", createtime=").append(createtime);
        sb.append(", updatetime=").append(updatetime);
        sb.append(", provincename=").append(provincename);
        sb.append(", provincecode=").append(provincecode);
        sb.append(", usertype=").append(usertype);
        sb.append("]");
        return sb.toString();
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UserK3 other = (UserK3) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getAccount() == null ? other.getAccount() == null : this.getAccount().equals(other.getAccount()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getDepartment() == null ? other.getDepartment() == null : this.getDepartment().equals(other.getDepartment()))
            && (this.getTeam() == null ? other.getTeam() == null : this.getTeam().equals(other.getTeam()))
            && (this.getPhone() == null ? other.getPhone() == null : this.getPhone().equals(other.getPhone()))
            && (this.getEmail() == null ? other.getEmail() == null : this.getEmail().equals(other.getEmail()))
            && (this.getUsercode() == null ? other.getUsercode() == null : this.getUsercode().equals(other.getUsercode()))
            && (this.getIhrusercode() == null ? other.getIhrusercode() == null : this.getIhrusercode().equals(other.getIhrusercode()))
            && (this.getCreateor() == null ? other.getCreateor() == null : this.getCreateor().equals(other.getCreateor()))
            && (this.getProject() == null ? other.getProject() == null : this.getProject().equals(other.getProject()))
            && (this.getCostcenter() == null ? other.getCostcenter() == null : this.getCostcenter().equals(other.getCostcenter()))
            && (this.getCostcentername() == null ? other.getCostcentername() == null : this.getCostcentername().equals(other.getCostcentername()))
            && (this.getSellerdept() == null ? other.getSellerdept() == null : this.getSellerdept().equals(other.getSellerdept()))
            && (this.getSellerteam() == null ? other.getSellerteam() == null : this.getSellerteam().equals(other.getSellerteam()))
            && (this.getCreatetime() == null ? other.getCreatetime() == null : this.getCreatetime().equals(other.getCreatetime()))
            && (this.getUpdatetime() == null ? other.getUpdatetime() == null : this.getUpdatetime().equals(other.getUpdatetime()))
            && (this.getProvincename() == null ? other.getProvincename() == null : this.getProvincename().equals(other.getProvincename()))
            && (this.getProvincecode() == null ? other.getProvincecode() == null : this.getProvincecode().equals(other.getProvincecode()))
            && (this.getUsertype() == null ? other.getUsertype() == null : this.getUsertype().equals(other.getUsertype()));
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getAccount() == null) ? 0 : getAccount().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getDepartment() == null) ? 0 : getDepartment().hashCode());
        result = prime * result + ((getTeam() == null) ? 0 : getTeam().hashCode());
        result = prime * result + ((getPhone() == null) ? 0 : getPhone().hashCode());
        result = prime * result + ((getEmail() == null) ? 0 : getEmail().hashCode());
        result = prime * result + ((getUsercode() == null) ? 0 : getUsercode().hashCode());
        result = prime * result + ((getIhrusercode() == null) ? 0 : getIhrusercode().hashCode());
        result = prime * result + ((getCreateor() == null) ? 0 : getCreateor().hashCode());
        result = prime * result + ((getProject() == null) ? 0 : getProject().hashCode());
        result = prime * result + ((getCostcenter() == null) ? 0 : getCostcenter().hashCode());
        result = prime * result + ((getCostcentername() == null) ? 0 : getCostcentername().hashCode());
        result = prime * result + ((getSellerdept() == null) ? 0 : getSellerdept().hashCode());
        result = prime * result + ((getSellerteam() == null) ? 0 : getSellerteam().hashCode());
        result = prime * result + ((getCreatetime() == null) ? 0 : getCreatetime().hashCode());
        result = prime * result + ((getUpdatetime() == null) ? 0 : getUpdatetime().hashCode());
        result = prime * result + ((getProvincename() == null) ? 0 : getProvincename().hashCode());
        result = prime * result + ((getProvincecode() == null) ? 0 : getProvincecode().hashCode());
        result = prime * result + ((getUsertype() == null) ? 0 : getUsertype().hashCode());
        return result;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public enum Column {
        id("id", "id", "VARCHAR", false),
        account("account", "account", "VARCHAR", false),
        name("name", "name", "VARCHAR", false),
        department("department", "department", "VARCHAR", false),
        team("team", "team", "VARCHAR", false),
        phone("phone", "phone", "VARCHAR", false),
        email("email", "email", "VARCHAR", false),
        usercode("usercode", "usercode", "VARCHAR", false),
        ihrusercode("ihrusercode", "ihrusercode", "VARCHAR", false),
        createor("createor", "createor", "VARCHAR", false),
        project("project", "project", "VARCHAR", false),
        costcenter("costcenter", "costcenter", "VARCHAR", false),
        costcentername("costcenterName", "costcentername", "VARCHAR", false),
        sellerdept("sellerdept", "sellerdept", "VARCHAR", false),
        sellerteam("sellerteam", "sellerteam", "VARCHAR", false),
        createtime("createtime", "createtime", "TIMESTAMP", false),
        updatetime("updatetime", "updatetime", "TIMESTAMP", false),
        provincename("provincename", "provincename", "VARCHAR", false),
        provincecode("provincecode", "provincecode", "VARCHAR", false),
        usertype("usertype", "usertype", "VARCHAR", false);

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        private static final String BEGINNING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        private static final String ENDING_DELIMITER = "\"";

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        private final String column;

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        private final boolean isColumnNameDelimited;

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        private final String javaProperty;

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        private final String jdbcType;

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        public String value() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        public String getValue() {
            return this.column;
        }

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        public String getJavaProperty() {
            return this.javaProperty;
        }

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        public String getJdbcType() {
            return this.jdbcType;
        }

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        public static Column[] all() {
            return Column.values();
        }

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}