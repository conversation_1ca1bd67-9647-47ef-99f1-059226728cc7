package com.chinamobile.iot.sc.pojo.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

public class K3Orders implements Serializable {
    private String id;

    private String orderId;

    private Date orderTime;

    private Date orderFinishTime;

    private String spuName;

    private String spuCode;

    private String spuType;

    private String spuClass;

    private String skuName;

    private String skuCode;

    private String atomName;

    private String atomCode;

    private String unitPrice;

    private Long quatity;

    private Long totalPrice;

    private String deductPrice;

    private String receiverPhone;

    private String orderProvince;

    private String orderProvinceCode;

    private String orderCity;

    private String orderCityCode;

    private String createOperCode;

    private String employeeNum;

    private String materialNum;

    private String materialName;

    private String materialDept;

    private String materialUnit;

    private String contractNum;

    private String contractName;

    private String contractDept;

    private String customCode;

    private String contractSellUnit;

    private String contractMoneyUnit;

    private String contractType;

    private String contractProject;

    private String contractSubProject;

    private String contractTax;

    private Integer contractSettleMode;

    private String buyerProvince;

    private String buyerProvinceMallCode;

    private String buyerProvinceCode;

    private String buyerCity;

    private String buyerCityMallCode;

    private String buyerCityCode;

    private String contractSatisType;

    private Integer contractStatisEnum;

    private Date createTime;

    private String k3Num;

    private String productDepartmentId;

    private String productDepartmentName;

    private String k3SyncDepartmentId;

    private static final long serialVersionUID = 1L;

    public String getId() {
        return id;
    }

    public K3Orders withId(String id) {
        this.setId(id);
        return this;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getOrderId() {
        return orderId;
    }

    public K3Orders withOrderId(String orderId) {
        this.setOrderId(orderId);
        return this;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public K3Orders withOrderTime(Date orderTime) {
        this.setOrderTime(orderTime);
        return this;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public Date getOrderFinishTime() {
        return orderFinishTime;
    }

    public K3Orders withOrderFinishTime(Date orderFinishTime) {
        this.setOrderFinishTime(orderFinishTime);
        return this;
    }

    public void setOrderFinishTime(Date orderFinishTime) {
        this.orderFinishTime = orderFinishTime;
    }

    public String getSpuName() {
        return spuName;
    }

    public K3Orders withSpuName(String spuName) {
        this.setSpuName(spuName);
        return this;
    }

    public void setSpuName(String spuName) {
        this.spuName = spuName == null ? null : spuName.trim();
    }

    public String getSpuCode() {
        return spuCode;
    }

    public K3Orders withSpuCode(String spuCode) {
        this.setSpuCode(spuCode);
        return this;
    }

    public void setSpuCode(String spuCode) {
        this.spuCode = spuCode == null ? null : spuCode.trim();
    }

    public String getSpuType() {
        return spuType;
    }

    public K3Orders withSpuType(String spuType) {
        this.setSpuType(spuType);
        return this;
    }

    public void setSpuType(String spuType) {
        this.spuType = spuType == null ? null : spuType.trim();
    }

    public String getSpuClass() {
        return spuClass;
    }

    public K3Orders withSpuClass(String spuClass) {
        this.setSpuClass(spuClass);
        return this;
    }

    public void setSpuClass(String spuClass) {
        this.spuClass = spuClass == null ? null : spuClass.trim();
    }

    public String getSkuName() {
        return skuName;
    }

    public K3Orders withSkuName(String skuName) {
        this.setSkuName(skuName);
        return this;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName == null ? null : skuName.trim();
    }

    public String getSkuCode() {
        return skuCode;
    }

    public K3Orders withSkuCode(String skuCode) {
        this.setSkuCode(skuCode);
        return this;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode == null ? null : skuCode.trim();
    }

    public String getAtomName() {
        return atomName;
    }

    public K3Orders withAtomName(String atomName) {
        this.setAtomName(atomName);
        return this;
    }

    public void setAtomName(String atomName) {
        this.atomName = atomName == null ? null : atomName.trim();
    }

    public String getAtomCode() {
        return atomCode;
    }

    public K3Orders withAtomCode(String atomCode) {
        this.setAtomCode(atomCode);
        return this;
    }

    public void setAtomCode(String atomCode) {
        this.atomCode = atomCode == null ? null : atomCode.trim();
    }

    public String getUnitPrice() {
        return unitPrice;
    }

    public K3Orders withUnitPrice(String unitPrice) {
        this.setUnitPrice(unitPrice);
        return this;
    }

    public void setUnitPrice(String unitPrice) {
        this.unitPrice = unitPrice == null ? null : unitPrice.trim();
    }

    public Long getQuatity() {
        return quatity;
    }

    public K3Orders withQuatity(Long quatity) {
        this.setQuatity(quatity);
        return this;
    }

    public void setQuatity(Long quatity) {
        this.quatity = quatity;
    }

    public Long getTotalPrice() {
        return totalPrice;
    }

    public K3Orders withTotalPrice(Long totalPrice) {
        this.setTotalPrice(totalPrice);
        return this;
    }

    public void setTotalPrice(Long totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getDeductPrice() {
        return deductPrice;
    }

    public K3Orders withDeductPrice(String deductPrice) {
        this.setDeductPrice(deductPrice);
        return this;
    }

    public void setDeductPrice(String deductPrice) {
        this.deductPrice = deductPrice == null ? null : deductPrice.trim();
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }

    public K3Orders withReceiverPhone(String receiverPhone) {
        this.setReceiverPhone(receiverPhone);
        return this;
    }

    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone == null ? null : receiverPhone.trim();
    }

    public String getOrderProvince() {
        return orderProvince;
    }

    public K3Orders withOrderProvince(String orderProvince) {
        this.setOrderProvince(orderProvince);
        return this;
    }

    public void setOrderProvince(String orderProvince) {
        this.orderProvince = orderProvince == null ? null : orderProvince.trim();
    }

    public String getOrderProvinceCode() {
        return orderProvinceCode;
    }

    public K3Orders withOrderProvinceCode(String orderProvinceCode) {
        this.setOrderProvinceCode(orderProvinceCode);
        return this;
    }

    public void setOrderProvinceCode(String orderProvinceCode) {
        this.orderProvinceCode = orderProvinceCode == null ? null : orderProvinceCode.trim();
    }

    public String getOrderCity() {
        return orderCity;
    }

    public K3Orders withOrderCity(String orderCity) {
        this.setOrderCity(orderCity);
        return this;
    }

    public void setOrderCity(String orderCity) {
        this.orderCity = orderCity == null ? null : orderCity.trim();
    }

    public String getOrderCityCode() {
        return orderCityCode;
    }

    public K3Orders withOrderCityCode(String orderCityCode) {
        this.setOrderCityCode(orderCityCode);
        return this;
    }

    public void setOrderCityCode(String orderCityCode) {
        this.orderCityCode = orderCityCode == null ? null : orderCityCode.trim();
    }

    public String getCreateOperCode() {
        return createOperCode;
    }

    public K3Orders withCreateOperCode(String createOperCode) {
        this.setCreateOperCode(createOperCode);
        return this;
    }

    public void setCreateOperCode(String createOperCode) {
        this.createOperCode = createOperCode == null ? null : createOperCode.trim();
    }

    public String getEmployeeNum() {
        return employeeNum;
    }

    public K3Orders withEmployeeNum(String employeeNum) {
        this.setEmployeeNum(employeeNum);
        return this;
    }

    public void setEmployeeNum(String employeeNum) {
        this.employeeNum = employeeNum == null ? null : employeeNum.trim();
    }

    public String getMaterialNum() {
        return materialNum;
    }

    public K3Orders withMaterialNum(String materialNum) {
        this.setMaterialNum(materialNum);
        return this;
    }

    public void setMaterialNum(String materialNum) {
        this.materialNum = materialNum == null ? null : materialNum.trim();
    }

    public String getMaterialName() {
        return materialName;
    }

    public K3Orders withMaterialName(String materialName) {
        this.setMaterialName(materialName);
        return this;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName == null ? null : materialName.trim();
    }

    public String getMaterialDept() {
        return materialDept;
    }

    public K3Orders withMaterialDept(String materialDept) {
        this.setMaterialDept(materialDept);
        return this;
    }

    public void setMaterialDept(String materialDept) {
        this.materialDept = materialDept == null ? null : materialDept.trim();
    }

    public String getMaterialUnit() {
        return materialUnit;
    }

    public K3Orders withMaterialUnit(String materialUnit) {
        this.setMaterialUnit(materialUnit);
        return this;
    }

    public void setMaterialUnit(String materialUnit) {
        this.materialUnit = materialUnit == null ? null : materialUnit.trim();
    }

    public String getContractNum() {
        return contractNum;
    }

    public K3Orders withContractNum(String contractNum) {
        this.setContractNum(contractNum);
        return this;
    }

    public void setContractNum(String contractNum) {
        this.contractNum = contractNum == null ? null : contractNum.trim();
    }

    public String getContractName() {
        return contractName;
    }

    public K3Orders withContractName(String contractName) {
        this.setContractName(contractName);
        return this;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName == null ? null : contractName.trim();
    }

    public String getContractDept() {
        return contractDept;
    }

    public K3Orders withContractDept(String contractDept) {
        this.setContractDept(contractDept);
        return this;
    }

    public void setContractDept(String contractDept) {
        this.contractDept = contractDept == null ? null : contractDept.trim();
    }

    public String getCustomCode() {
        return customCode;
    }

    public K3Orders withCustomCode(String customCode) {
        this.setCustomCode(customCode);
        return this;
    }

    public void setCustomCode(String customCode) {
        this.customCode = customCode == null ? null : customCode.trim();
    }

    public String getContractSellUnit() {
        return contractSellUnit;
    }

    public K3Orders withContractSellUnit(String contractSellUnit) {
        this.setContractSellUnit(contractSellUnit);
        return this;
    }

    public void setContractSellUnit(String contractSellUnit) {
        this.contractSellUnit = contractSellUnit == null ? null : contractSellUnit.trim();
    }

    public String getContractMoneyUnit() {
        return contractMoneyUnit;
    }

    public K3Orders withContractMoneyUnit(String contractMoneyUnit) {
        this.setContractMoneyUnit(contractMoneyUnit);
        return this;
    }

    public void setContractMoneyUnit(String contractMoneyUnit) {
        this.contractMoneyUnit = contractMoneyUnit == null ? null : contractMoneyUnit.trim();
    }

    public String getContractType() {
        return contractType;
    }

    public K3Orders withContractType(String contractType) {
        this.setContractType(contractType);
        return this;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType == null ? null : contractType.trim();
    }

    public String getContractProject() {
        return contractProject;
    }

    public K3Orders withContractProject(String contractProject) {
        this.setContractProject(contractProject);
        return this;
    }

    public void setContractProject(String contractProject) {
        this.contractProject = contractProject == null ? null : contractProject.trim();
    }

    public String getContractSubProject() {
        return contractSubProject;
    }

    public K3Orders withContractSubProject(String contractSubProject) {
        this.setContractSubProject(contractSubProject);
        return this;
    }

    public void setContractSubProject(String contractSubProject) {
        this.contractSubProject = contractSubProject == null ? null : contractSubProject.trim();
    }

    public String getContractTax() {
        return contractTax;
    }

    public K3Orders withContractTax(String contractTax) {
        this.setContractTax(contractTax);
        return this;
    }

    public void setContractTax(String contractTax) {
        this.contractTax = contractTax == null ? null : contractTax.trim();
    }

    public Integer getContractSettleMode() {
        return contractSettleMode;
    }

    public K3Orders withContractSettleMode(Integer contractSettleMode) {
        this.setContractSettleMode(contractSettleMode);
        return this;
    }

    public void setContractSettleMode(Integer contractSettleMode) {
        this.contractSettleMode = contractSettleMode;
    }

    public String getBuyerProvince() {
        return buyerProvince;
    }

    public K3Orders withBuyerProvince(String buyerProvince) {
        this.setBuyerProvince(buyerProvince);
        return this;
    }

    public void setBuyerProvince(String buyerProvince) {
        this.buyerProvince = buyerProvince == null ? null : buyerProvince.trim();
    }

    public String getBuyerProvinceMallCode() {
        return buyerProvinceMallCode;
    }

    public K3Orders withBuyerProvinceMallCode(String buyerProvinceMallCode) {
        this.setBuyerProvinceMallCode(buyerProvinceMallCode);
        return this;
    }

    public void setBuyerProvinceMallCode(String buyerProvinceMallCode) {
        this.buyerProvinceMallCode = buyerProvinceMallCode == null ? null : buyerProvinceMallCode.trim();
    }

    public String getBuyerProvinceCode() {
        return buyerProvinceCode;
    }

    public K3Orders withBuyerProvinceCode(String buyerProvinceCode) {
        this.setBuyerProvinceCode(buyerProvinceCode);
        return this;
    }

    public void setBuyerProvinceCode(String buyerProvinceCode) {
        this.buyerProvinceCode = buyerProvinceCode == null ? null : buyerProvinceCode.trim();
    }

    public String getBuyerCity() {
        return buyerCity;
    }

    public K3Orders withBuyerCity(String buyerCity) {
        this.setBuyerCity(buyerCity);
        return this;
    }

    public void setBuyerCity(String buyerCity) {
        this.buyerCity = buyerCity == null ? null : buyerCity.trim();
    }

    public String getBuyerCityMallCode() {
        return buyerCityMallCode;
    }

    public K3Orders withBuyerCityMallCode(String buyerCityMallCode) {
        this.setBuyerCityMallCode(buyerCityMallCode);
        return this;
    }

    public void setBuyerCityMallCode(String buyerCityMallCode) {
        this.buyerCityMallCode = buyerCityMallCode == null ? null : buyerCityMallCode.trim();
    }

    public String getBuyerCityCode() {
        return buyerCityCode;
    }

    public K3Orders withBuyerCityCode(String buyerCityCode) {
        this.setBuyerCityCode(buyerCityCode);
        return this;
    }

    public void setBuyerCityCode(String buyerCityCode) {
        this.buyerCityCode = buyerCityCode == null ? null : buyerCityCode.trim();
    }

    public String getContractSatisType() {
        return contractSatisType;
    }

    public K3Orders withContractSatisType(String contractSatisType) {
        this.setContractSatisType(contractSatisType);
        return this;
    }

    public void setContractSatisType(String contractSatisType) {
        this.contractSatisType = contractSatisType == null ? null : contractSatisType.trim();
    }

    public Integer getContractStatisEnum() {
        return contractStatisEnum;
    }

    public K3Orders withContractStatisEnum(Integer contractStatisEnum) {
        this.setContractStatisEnum(contractStatisEnum);
        return this;
    }

    public void setContractStatisEnum(Integer contractStatisEnum) {
        this.contractStatisEnum = contractStatisEnum;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public K3Orders withCreateTime(Date createTime) {
        this.setCreateTime(createTime);
        return this;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getK3Num() {
        return k3Num;
    }

    public K3Orders withK3Num(String k3Num) {
        this.setK3Num(k3Num);
        return this;
    }

    public void setK3Num(String k3Num) {
        this.k3Num = k3Num == null ? null : k3Num.trim();
    }

    public String getProductDepartmentId() {
        return productDepartmentId;
    }

    public K3Orders withProductDepartmentId(String productDepartmentId) {
        this.setProductDepartmentId(productDepartmentId);
        return this;
    }

    public void setProductDepartmentId(String productDepartmentId) {
        this.productDepartmentId = productDepartmentId == null ? null : productDepartmentId.trim();
    }

    public String getProductDepartmentName() {
        return productDepartmentName;
    }

    public K3Orders withProductDepartmentName(String productDepartmentName) {
        this.setProductDepartmentName(productDepartmentName);
        return this;
    }

    public void setProductDepartmentName(String productDepartmentName) {
        this.productDepartmentName = productDepartmentName == null ? null : productDepartmentName.trim();
    }

    public String getK3SyncDepartmentId() {
        return k3SyncDepartmentId;
    }

    public K3Orders withK3SyncDepartmentId(String k3SyncDepartmentId) {
        this.setK3SyncDepartmentId(k3SyncDepartmentId);
        return this;
    }

    public void setK3SyncDepartmentId(String k3SyncDepartmentId) {
        this.k3SyncDepartmentId = k3SyncDepartmentId == null ? null : k3SyncDepartmentId.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", orderId=").append(orderId);
        sb.append(", orderTime=").append(orderTime);
        sb.append(", orderFinishTime=").append(orderFinishTime);
        sb.append(", spuName=").append(spuName);
        sb.append(", spuCode=").append(spuCode);
        sb.append(", spuType=").append(spuType);
        sb.append(", spuClass=").append(spuClass);
        sb.append(", skuName=").append(skuName);
        sb.append(", skuCode=").append(skuCode);
        sb.append(", atomName=").append(atomName);
        sb.append(", atomCode=").append(atomCode);
        sb.append(", unitPrice=").append(unitPrice);
        sb.append(", quatity=").append(quatity);
        sb.append(", totalPrice=").append(totalPrice);
        sb.append(", deductPrice=").append(deductPrice);
        sb.append(", receiverPhone=").append(receiverPhone);
        sb.append(", orderProvince=").append(orderProvince);
        sb.append(", orderProvinceCode=").append(orderProvinceCode);
        sb.append(", orderCity=").append(orderCity);
        sb.append(", orderCityCode=").append(orderCityCode);
        sb.append(", createOperCode=").append(createOperCode);
        sb.append(", employeeNum=").append(employeeNum);
        sb.append(", materialNum=").append(materialNum);
        sb.append(", materialName=").append(materialName);
        sb.append(", materialDept=").append(materialDept);
        sb.append(", materialUnit=").append(materialUnit);
        sb.append(", contractNum=").append(contractNum);
        sb.append(", contractName=").append(contractName);
        sb.append(", contractDept=").append(contractDept);
        sb.append(", customCode=").append(customCode);
        sb.append(", contractSellUnit=").append(contractSellUnit);
        sb.append(", contractMoneyUnit=").append(contractMoneyUnit);
        sb.append(", contractType=").append(contractType);
        sb.append(", contractProject=").append(contractProject);
        sb.append(", contractSubProject=").append(contractSubProject);
        sb.append(", contractTax=").append(contractTax);
        sb.append(", contractSettleMode=").append(contractSettleMode);
        sb.append(", buyerProvince=").append(buyerProvince);
        sb.append(", buyerProvinceMallCode=").append(buyerProvinceMallCode);
        sb.append(", buyerProvinceCode=").append(buyerProvinceCode);
        sb.append(", buyerCity=").append(buyerCity);
        sb.append(", buyerCityMallCode=").append(buyerCityMallCode);
        sb.append(", buyerCityCode=").append(buyerCityCode);
        sb.append(", contractSatisType=").append(contractSatisType);
        sb.append(", contractStatisEnum=").append(contractStatisEnum);
        sb.append(", createTime=").append(createTime);
        sb.append(", k3Num=").append(k3Num);
        sb.append(", productDepartmentId=").append(productDepartmentId);
        sb.append(", productDepartmentName=").append(productDepartmentName);
        sb.append(", k3SyncDepartmentId=").append(k3SyncDepartmentId);
        sb.append("]");
        return sb.toString();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        K3Orders other = (K3Orders) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()))
            && (this.getOrderTime() == null ? other.getOrderTime() == null : this.getOrderTime().equals(other.getOrderTime()))
            && (this.getOrderFinishTime() == null ? other.getOrderFinishTime() == null : this.getOrderFinishTime().equals(other.getOrderFinishTime()))
            && (this.getSpuName() == null ? other.getSpuName() == null : this.getSpuName().equals(other.getSpuName()))
            && (this.getSpuCode() == null ? other.getSpuCode() == null : this.getSpuCode().equals(other.getSpuCode()))
            && (this.getSpuType() == null ? other.getSpuType() == null : this.getSpuType().equals(other.getSpuType()))
            && (this.getSpuClass() == null ? other.getSpuClass() == null : this.getSpuClass().equals(other.getSpuClass()))
            && (this.getSkuName() == null ? other.getSkuName() == null : this.getSkuName().equals(other.getSkuName()))
            && (this.getSkuCode() == null ? other.getSkuCode() == null : this.getSkuCode().equals(other.getSkuCode()))
            && (this.getAtomName() == null ? other.getAtomName() == null : this.getAtomName().equals(other.getAtomName()))
            && (this.getAtomCode() == null ? other.getAtomCode() == null : this.getAtomCode().equals(other.getAtomCode()))
            && (this.getUnitPrice() == null ? other.getUnitPrice() == null : this.getUnitPrice().equals(other.getUnitPrice()))
            && (this.getQuatity() == null ? other.getQuatity() == null : this.getQuatity().equals(other.getQuatity()))
            && (this.getTotalPrice() == null ? other.getTotalPrice() == null : this.getTotalPrice().equals(other.getTotalPrice()))
            && (this.getDeductPrice() == null ? other.getDeductPrice() == null : this.getDeductPrice().equals(other.getDeductPrice()))
            && (this.getReceiverPhone() == null ? other.getReceiverPhone() == null : this.getReceiverPhone().equals(other.getReceiverPhone()))
            && (this.getOrderProvince() == null ? other.getOrderProvince() == null : this.getOrderProvince().equals(other.getOrderProvince()))
            && (this.getOrderProvinceCode() == null ? other.getOrderProvinceCode() == null : this.getOrderProvinceCode().equals(other.getOrderProvinceCode()))
            && (this.getOrderCity() == null ? other.getOrderCity() == null : this.getOrderCity().equals(other.getOrderCity()))
            && (this.getOrderCityCode() == null ? other.getOrderCityCode() == null : this.getOrderCityCode().equals(other.getOrderCityCode()))
            && (this.getCreateOperCode() == null ? other.getCreateOperCode() == null : this.getCreateOperCode().equals(other.getCreateOperCode()))
            && (this.getEmployeeNum() == null ? other.getEmployeeNum() == null : this.getEmployeeNum().equals(other.getEmployeeNum()))
            && (this.getMaterialNum() == null ? other.getMaterialNum() == null : this.getMaterialNum().equals(other.getMaterialNum()))
            && (this.getMaterialName() == null ? other.getMaterialName() == null : this.getMaterialName().equals(other.getMaterialName()))
            && (this.getMaterialDept() == null ? other.getMaterialDept() == null : this.getMaterialDept().equals(other.getMaterialDept()))
            && (this.getMaterialUnit() == null ? other.getMaterialUnit() == null : this.getMaterialUnit().equals(other.getMaterialUnit()))
            && (this.getContractNum() == null ? other.getContractNum() == null : this.getContractNum().equals(other.getContractNum()))
            && (this.getContractName() == null ? other.getContractName() == null : this.getContractName().equals(other.getContractName()))
            && (this.getContractDept() == null ? other.getContractDept() == null : this.getContractDept().equals(other.getContractDept()))
            && (this.getCustomCode() == null ? other.getCustomCode() == null : this.getCustomCode().equals(other.getCustomCode()))
            && (this.getContractSellUnit() == null ? other.getContractSellUnit() == null : this.getContractSellUnit().equals(other.getContractSellUnit()))
            && (this.getContractMoneyUnit() == null ? other.getContractMoneyUnit() == null : this.getContractMoneyUnit().equals(other.getContractMoneyUnit()))
            && (this.getContractType() == null ? other.getContractType() == null : this.getContractType().equals(other.getContractType()))
            && (this.getContractProject() == null ? other.getContractProject() == null : this.getContractProject().equals(other.getContractProject()))
            && (this.getContractSubProject() == null ? other.getContractSubProject() == null : this.getContractSubProject().equals(other.getContractSubProject()))
            && (this.getContractTax() == null ? other.getContractTax() == null : this.getContractTax().equals(other.getContractTax()))
            && (this.getContractSettleMode() == null ? other.getContractSettleMode() == null : this.getContractSettleMode().equals(other.getContractSettleMode()))
            && (this.getBuyerProvince() == null ? other.getBuyerProvince() == null : this.getBuyerProvince().equals(other.getBuyerProvince()))
            && (this.getBuyerProvinceMallCode() == null ? other.getBuyerProvinceMallCode() == null : this.getBuyerProvinceMallCode().equals(other.getBuyerProvinceMallCode()))
            && (this.getBuyerProvinceCode() == null ? other.getBuyerProvinceCode() == null : this.getBuyerProvinceCode().equals(other.getBuyerProvinceCode()))
            && (this.getBuyerCity() == null ? other.getBuyerCity() == null : this.getBuyerCity().equals(other.getBuyerCity()))
            && (this.getBuyerCityMallCode() == null ? other.getBuyerCityMallCode() == null : this.getBuyerCityMallCode().equals(other.getBuyerCityMallCode()))
            && (this.getBuyerCityCode() == null ? other.getBuyerCityCode() == null : this.getBuyerCityCode().equals(other.getBuyerCityCode()))
            && (this.getContractSatisType() == null ? other.getContractSatisType() == null : this.getContractSatisType().equals(other.getContractSatisType()))
            && (this.getContractStatisEnum() == null ? other.getContractStatisEnum() == null : this.getContractStatisEnum().equals(other.getContractStatisEnum()))
            && (this.getCreateTime() == null ? other.getCreateTime() == null : this.getCreateTime().equals(other.getCreateTime()))
            && (this.getK3Num() == null ? other.getK3Num() == null : this.getK3Num().equals(other.getK3Num()))
            && (this.getProductDepartmentId() == null ? other.getProductDepartmentId() == null : this.getProductDepartmentId().equals(other.getProductDepartmentId()))
            && (this.getProductDepartmentName() == null ? other.getProductDepartmentName() == null : this.getProductDepartmentName().equals(other.getProductDepartmentName()))
            && (this.getK3SyncDepartmentId() == null ? other.getK3SyncDepartmentId() == null : this.getK3SyncDepartmentId().equals(other.getK3SyncDepartmentId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        result = prime * result + ((getOrderTime() == null) ? 0 : getOrderTime().hashCode());
        result = prime * result + ((getOrderFinishTime() == null) ? 0 : getOrderFinishTime().hashCode());
        result = prime * result + ((getSpuName() == null) ? 0 : getSpuName().hashCode());
        result = prime * result + ((getSpuCode() == null) ? 0 : getSpuCode().hashCode());
        result = prime * result + ((getSpuType() == null) ? 0 : getSpuType().hashCode());
        result = prime * result + ((getSpuClass() == null) ? 0 : getSpuClass().hashCode());
        result = prime * result + ((getSkuName() == null) ? 0 : getSkuName().hashCode());
        result = prime * result + ((getSkuCode() == null) ? 0 : getSkuCode().hashCode());
        result = prime * result + ((getAtomName() == null) ? 0 : getAtomName().hashCode());
        result = prime * result + ((getAtomCode() == null) ? 0 : getAtomCode().hashCode());
        result = prime * result + ((getUnitPrice() == null) ? 0 : getUnitPrice().hashCode());
        result = prime * result + ((getQuatity() == null) ? 0 : getQuatity().hashCode());
        result = prime * result + ((getTotalPrice() == null) ? 0 : getTotalPrice().hashCode());
        result = prime * result + ((getDeductPrice() == null) ? 0 : getDeductPrice().hashCode());
        result = prime * result + ((getReceiverPhone() == null) ? 0 : getReceiverPhone().hashCode());
        result = prime * result + ((getOrderProvince() == null) ? 0 : getOrderProvince().hashCode());
        result = prime * result + ((getOrderProvinceCode() == null) ? 0 : getOrderProvinceCode().hashCode());
        result = prime * result + ((getOrderCity() == null) ? 0 : getOrderCity().hashCode());
        result = prime * result + ((getOrderCityCode() == null) ? 0 : getOrderCityCode().hashCode());
        result = prime * result + ((getCreateOperCode() == null) ? 0 : getCreateOperCode().hashCode());
        result = prime * result + ((getEmployeeNum() == null) ? 0 : getEmployeeNum().hashCode());
        result = prime * result + ((getMaterialNum() == null) ? 0 : getMaterialNum().hashCode());
        result = prime * result + ((getMaterialName() == null) ? 0 : getMaterialName().hashCode());
        result = prime * result + ((getMaterialDept() == null) ? 0 : getMaterialDept().hashCode());
        result = prime * result + ((getMaterialUnit() == null) ? 0 : getMaterialUnit().hashCode());
        result = prime * result + ((getContractNum() == null) ? 0 : getContractNum().hashCode());
        result = prime * result + ((getContractName() == null) ? 0 : getContractName().hashCode());
        result = prime * result + ((getContractDept() == null) ? 0 : getContractDept().hashCode());
        result = prime * result + ((getCustomCode() == null) ? 0 : getCustomCode().hashCode());
        result = prime * result + ((getContractSellUnit() == null) ? 0 : getContractSellUnit().hashCode());
        result = prime * result + ((getContractMoneyUnit() == null) ? 0 : getContractMoneyUnit().hashCode());
        result = prime * result + ((getContractType() == null) ? 0 : getContractType().hashCode());
        result = prime * result + ((getContractProject() == null) ? 0 : getContractProject().hashCode());
        result = prime * result + ((getContractSubProject() == null) ? 0 : getContractSubProject().hashCode());
        result = prime * result + ((getContractTax() == null) ? 0 : getContractTax().hashCode());
        result = prime * result + ((getContractSettleMode() == null) ? 0 : getContractSettleMode().hashCode());
        result = prime * result + ((getBuyerProvince() == null) ? 0 : getBuyerProvince().hashCode());
        result = prime * result + ((getBuyerProvinceMallCode() == null) ? 0 : getBuyerProvinceMallCode().hashCode());
        result = prime * result + ((getBuyerProvinceCode() == null) ? 0 : getBuyerProvinceCode().hashCode());
        result = prime * result + ((getBuyerCity() == null) ? 0 : getBuyerCity().hashCode());
        result = prime * result + ((getBuyerCityMallCode() == null) ? 0 : getBuyerCityMallCode().hashCode());
        result = prime * result + ((getBuyerCityCode() == null) ? 0 : getBuyerCityCode().hashCode());
        result = prime * result + ((getContractSatisType() == null) ? 0 : getContractSatisType().hashCode());
        result = prime * result + ((getContractStatisEnum() == null) ? 0 : getContractStatisEnum().hashCode());
        result = prime * result + ((getCreateTime() == null) ? 0 : getCreateTime().hashCode());
        result = prime * result + ((getK3Num() == null) ? 0 : getK3Num().hashCode());
        result = prime * result + ((getProductDepartmentId() == null) ? 0 : getProductDepartmentId().hashCode());
        result = prime * result + ((getProductDepartmentName() == null) ? 0 : getProductDepartmentName().hashCode());
        result = prime * result + ((getK3SyncDepartmentId() == null) ? 0 : getK3SyncDepartmentId().hashCode());
        return result;
    }

    public enum Column {
        id("id", "id", "VARCHAR", false),
        orderId("order_id", "orderId", "VARCHAR", false),
        orderTime("order_time", "orderTime", "TIMESTAMP", false),
        orderFinishTime("order_finish_time", "orderFinishTime", "TIMESTAMP", false),
        spuName("spu_name", "spuName", "VARCHAR", false),
        spuCode("spu_code", "spuCode", "VARCHAR", false),
        spuType("spu_type", "spuType", "VARCHAR", false),
        spuClass("spu_class", "spuClass", "VARCHAR", false),
        skuName("sku_name", "skuName", "VARCHAR", false),
        skuCode("sku_code", "skuCode", "VARCHAR", false),
        atomName("atom_name", "atomName", "VARCHAR", false),
        atomCode("atom_code", "atomCode", "VARCHAR", false),
        unitPrice("unit_price", "unitPrice", "VARCHAR", false),
        quatity("quatity", "quatity", "BIGINT", false),
        totalPrice("total_price", "totalPrice", "BIGINT", false),
        deductPrice("deduct_price", "deductPrice", "VARCHAR", false),
        receiverPhone("receiver_phone", "receiverPhone", "VARCHAR", false),
        orderProvince("order_province", "orderProvince", "VARCHAR", false),
        orderProvinceCode("order_province_code", "orderProvinceCode", "VARCHAR", false),
        orderCity("order_city", "orderCity", "VARCHAR", false),
        orderCityCode("order_city_code", "orderCityCode", "VARCHAR", false),
        createOperCode("create_oper_code", "createOperCode", "VARCHAR", false),
        employeeNum("employee_num", "employeeNum", "VARCHAR", false),
        materialNum("material_num", "materialNum", "VARCHAR", false),
        materialName("material_name", "materialName", "VARCHAR", false),
        materialDept("material_dept", "materialDept", "VARCHAR", false),
        materialUnit("material_unit", "materialUnit", "VARCHAR", false),
        contractNum("contract_num", "contractNum", "VARCHAR", false),
        contractName("contract_name", "contractName", "VARCHAR", false),
        contractDept("contract_dept", "contractDept", "VARCHAR", false),
        customCode("custom_code", "customCode", "VARCHAR", false),
        contractSellUnit("contract_sell_unit", "contractSellUnit", "VARCHAR", false),
        contractMoneyUnit("contract_money_unit", "contractMoneyUnit", "VARCHAR", false),
        contractType("contract_type", "contractType", "VARCHAR", false),
        contractProject("contract_project", "contractProject", "VARCHAR", false),
        contractSubProject("contract_sub_project", "contractSubProject", "VARCHAR", false),
        contractTax("contract_tax", "contractTax", "VARCHAR", false),
        contractSettleMode("contract_settle_mode", "contractSettleMode", "INTEGER", false),
        buyerProvince("buyer_province", "buyerProvince", "VARCHAR", false),
        buyerProvinceMallCode("buyer_province_mall_code", "buyerProvinceMallCode", "VARCHAR", false),
        buyerProvinceCode("buyer_province_code", "buyerProvinceCode", "VARCHAR", false),
        buyerCity("buyer_city", "buyerCity", "VARCHAR", false),
        buyerCityMallCode("buyer_city_mall_code", "buyerCityMallCode", "VARCHAR", false),
        buyerCityCode("buyer_city_code", "buyerCityCode", "VARCHAR", false),
        contractSatisType("contract_satis_type", "contractSatisType", "VARCHAR", false),
        contractStatisEnum("contract_statis_enum", "contractStatisEnum", "INTEGER", false),
        createTime("create_time", "createTime", "TIMESTAMP", false),
        k3Num("k3_num", "k3Num", "VARCHAR", false),
        productDepartmentId("product_department_id", "productDepartmentId", "VARCHAR", false),
        productDepartmentName("product_department_name", "productDepartmentName", "VARCHAR", false),
        k3SyncDepartmentId("k3_sync_department_id", "k3SyncDepartmentId", "VARCHAR", false);

        private static final String BEGINNING_DELIMITER = "\"";

        private static final String ENDING_DELIMITER = "\"";

        private final String column;

        private final boolean isColumnNameDelimited;

        private final String javaProperty;

        private final String jdbcType;

        public String value() {
            return this.column;
        }

        public String getValue() {
            return this.column;
        }

        public String getJavaProperty() {
            return this.javaProperty;
        }

        public String getJdbcType() {
            return this.jdbcType;
        }

        Column(String column, String javaProperty, String jdbcType, boolean isColumnNameDelimited) {
            this.column = column;
            this.javaProperty = javaProperty;
            this.jdbcType = jdbcType;
            this.isColumnNameDelimited = isColumnNameDelimited;
        }

        public String desc() {
            return this.getEscapedColumnName() + " DESC";
        }

        public String asc() {
            return this.getEscapedColumnName() + " ASC";
        }

        public static Column[] excludes(Column ... excludes) {
            ArrayList<Column> columns = new ArrayList<>(Arrays.asList(Column.values()));
            if (excludes != null && excludes.length > 0) {
                columns.removeAll(new ArrayList<>(Arrays.asList(excludes)));
            }
            return columns.toArray(new Column[]{});
        }

        public static Column[] all() {
            return Column.values();
        }

        public String getEscapedColumnName() {
            if (this.isColumnNameDelimited) {
                return new StringBuilder().append(BEGINNING_DELIMITER).append(this.column).append(ENDING_DELIMITER).toString();
            } else {
                return this.column;
            }
        }

        public String getAliasedEscapedColumnName() {
            return this.getEscapedColumnName();
        }
    }
}