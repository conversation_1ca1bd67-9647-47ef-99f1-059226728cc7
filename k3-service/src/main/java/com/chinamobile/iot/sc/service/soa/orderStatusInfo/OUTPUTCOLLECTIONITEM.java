
package com.chinamobile.iot.sc.service.soa.orderStatusInfo;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for OUTPUTCOLLECTION_ITEM complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="OUTPUTCOLLECTION_ITEM"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="OU_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SOURCE_FROM" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SOURCE_FROM_NO" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="SCM_PO_NUM" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="ORDER_STATUS" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="OUTPUT_EXT" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OUTPUTCOLLECTION_ITEM", propOrder = {
    "ouname",
    "sourcefrom",
    "sourcefromno",
    "scmponum",
    "orderstatus",
    "outputext"
})
public class OUTPUTCOLLECTIONITEM {

    @XmlElement(name = "OU_NAME", required = true, nillable = true)
    protected String ouname;
    @XmlElement(name = "SOURCE_FROM", required = true, nillable = true)
    protected String sourcefrom;
    @XmlElement(name = "SOURCE_FROM_NO", required = true, nillable = true)
    protected String sourcefromno;
    @XmlElement(name = "SCM_PO_NUM", required = true, nillable = true)
    protected String scmponum;
    @XmlElement(name = "ORDER_STATUS", required = true, nillable = true)
    protected String orderstatus;
    @XmlElement(name = "OUTPUT_EXT", required = true, nillable = true)
    protected String outputext;

    /**
     * Gets the value of the ouname property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOUNAME() {
        return ouname;
    }

    /**
     * Sets the value of the ouname property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOUNAME(String value) {
        this.ouname = value;
    }

    /**
     * Gets the value of the sourcefrom property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSOURCEFROM() {
        return sourcefrom;
    }

    /**
     * Sets the value of the sourcefrom property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSOURCEFROM(String value) {
        this.sourcefrom = value;
    }

    /**
     * Gets the value of the sourcefromno property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSOURCEFROMNO() {
        return sourcefromno;
    }

    /**
     * Sets the value of the sourcefromno property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSOURCEFROMNO(String value) {
        this.sourcefromno = value;
    }

    /**
     * Gets the value of the scmponum property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSCMPONUM() {
        return scmponum;
    }

    /**
     * Sets the value of the scmponum property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSCMPONUM(String value) {
        this.scmponum = value;
    }

    /**
     * Gets the value of the orderstatus property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getORDERSTATUS() {
        return orderstatus;
    }

    /**
     * Sets the value of the orderstatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setORDERSTATUS(String value) {
        this.orderstatus = value;
    }

    /**
     * Gets the value of the outputext property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOUTPUTEXT() {
        return outputext;
    }

    /**
     * Sets the value of the outputext property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOUTPUTEXT(String value) {
        this.outputext = value;
    }

}
