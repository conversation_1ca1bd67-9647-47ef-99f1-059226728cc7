package com.chinamobile.iot.sc.pojo.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class UserK3Example {
    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    protected boolean distinct;

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3Example() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3Example orderBy(String orderByClause) {
        this.setOrderByClause(orderByClause);
        return this;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3Example orderBy(String ... orderByClauses) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < orderByClauses.length; i++) {
            sb.append(orderByClauses[i]);
            if (i < orderByClauses.length - 1) {
                sb.append(" , ");
            }
        }
        this.setOrderByClause(sb.toString());
        return this;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria(this);
        return criteria;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setPageInfo(Integer currentPage, Integer pageSize) {
        if(pageSize<1) throw new IllegalArgumentException("页大小不能小于1！");
        this.limit=pageSize;
        if(currentPage<1) throw new IllegalArgumentException("页数不能小于1！");
        this.offset=(currentPage-1)*pageSize;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public static Criteria newAndCreateCriteria() {
        UserK3Example example = new UserK3Example();
        return example.createCriteria();
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3Example when(boolean condition, IExampleWhen then) {
        if (condition) {
            then.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public UserK3Example when(boolean condition, IExampleWhen then, IExampleWhen otherwise) {
        if (condition) {
            then.example(this);
        } else {
            otherwise.example(this);
        }
        return this;
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("id = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("id <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("id > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("id >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("id < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("id <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountIsNull() {
            addCriterion("account is null");
            return (Criteria) this;
        }

        public Criteria andAccountIsNotNull() {
            addCriterion("account is not null");
            return (Criteria) this;
        }

        public Criteria andAccountEqualTo(String value) {
            addCriterion("account =", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("account = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAccountNotEqualTo(String value) {
            addCriterion("account <>", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("account <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThan(String value) {
            addCriterion("account >", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("account > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThanOrEqualTo(String value) {
            addCriterion("account >=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("account >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAccountLessThan(String value) {
            addCriterion("account <", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("account < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAccountLessThanOrEqualTo(String value) {
            addCriterion("account <=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("account <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andAccountLike(String value) {
            addCriterion("account like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotLike(String value) {
            addCriterion("account not like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountIn(List<String> values) {
            addCriterion("account in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotIn(List<String> values) {
            addCriterion("account not in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountBetween(String value1, String value2) {
            addCriterion("account between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotBetween(String value1, String value2) {
            addCriterion("account not between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("name = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("name <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("name > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("name >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("name < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("name <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andDepartmentIsNull() {
            addCriterion("department is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIsNotNull() {
            addCriterion("department is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentEqualTo(String value) {
            addCriterion("department =", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("department = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDepartmentNotEqualTo(String value) {
            addCriterion("department <>", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("department <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDepartmentGreaterThan(String value) {
            addCriterion("department >", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("department > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDepartmentGreaterThanOrEqualTo(String value) {
            addCriterion("department >=", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("department >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDepartmentLessThan(String value) {
            addCriterion("department <", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("department < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDepartmentLessThanOrEqualTo(String value) {
            addCriterion("department <=", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("department <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andDepartmentLike(String value) {
            addCriterion("department like", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotLike(String value) {
            addCriterion("department not like", value, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentIn(List<String> values) {
            addCriterion("department in", values, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotIn(List<String> values) {
            addCriterion("department not in", values, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentBetween(String value1, String value2) {
            addCriterion("department between", value1, value2, "department");
            return (Criteria) this;
        }

        public Criteria andDepartmentNotBetween(String value1, String value2) {
            addCriterion("department not between", value1, value2, "department");
            return (Criteria) this;
        }

        public Criteria andTeamIsNull() {
            addCriterion("team is null");
            return (Criteria) this;
        }

        public Criteria andTeamIsNotNull() {
            addCriterion("team is not null");
            return (Criteria) this;
        }

        public Criteria andTeamEqualTo(String value) {
            addCriterion("team =", value, "team");
            return (Criteria) this;
        }

        public Criteria andTeamEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("team = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTeamNotEqualTo(String value) {
            addCriterion("team <>", value, "team");
            return (Criteria) this;
        }

        public Criteria andTeamNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("team <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTeamGreaterThan(String value) {
            addCriterion("team >", value, "team");
            return (Criteria) this;
        }

        public Criteria andTeamGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("team > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTeamGreaterThanOrEqualTo(String value) {
            addCriterion("team >=", value, "team");
            return (Criteria) this;
        }

        public Criteria andTeamGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("team >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTeamLessThan(String value) {
            addCriterion("team <", value, "team");
            return (Criteria) this;
        }

        public Criteria andTeamLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("team < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTeamLessThanOrEqualTo(String value) {
            addCriterion("team <=", value, "team");
            return (Criteria) this;
        }

        public Criteria andTeamLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("team <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andTeamLike(String value) {
            addCriterion("team like", value, "team");
            return (Criteria) this;
        }

        public Criteria andTeamNotLike(String value) {
            addCriterion("team not like", value, "team");
            return (Criteria) this;
        }

        public Criteria andTeamIn(List<String> values) {
            addCriterion("team in", values, "team");
            return (Criteria) this;
        }

        public Criteria andTeamNotIn(List<String> values) {
            addCriterion("team not in", values, "team");
            return (Criteria) this;
        }

        public Criteria andTeamBetween(String value1, String value2) {
            addCriterion("team between", value1, value2, "team");
            return (Criteria) this;
        }

        public Criteria andTeamNotBetween(String value1, String value2) {
            addCriterion("team not between", value1, value2, "team");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNull() {
            addCriterion("phone is null");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNotNull() {
            addCriterion("phone is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneEqualTo(String value) {
            addCriterion("phone =", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("phone = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhoneNotEqualTo(String value) {
            addCriterion("phone <>", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("phone <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThan(String value) {
            addCriterion("phone >", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("phone > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("phone >=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("phone >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhoneLessThan(String value) {
            addCriterion("phone <", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("phone < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanOrEqualTo(String value) {
            addCriterion("phone <=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("phone <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andPhoneLike(String value) {
            addCriterion("phone like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotLike(String value) {
            addCriterion("phone not like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneIn(List<String> values) {
            addCriterion("phone in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotIn(List<String> values) {
            addCriterion("phone not in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneBetween(String value1, String value2) {
            addCriterion("phone between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotBetween(String value1, String value2) {
            addCriterion("phone not between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andEmailIsNull() {
            addCriterion("email is null");
            return (Criteria) this;
        }

        public Criteria andEmailIsNotNull() {
            addCriterion("email is not null");
            return (Criteria) this;
        }

        public Criteria andEmailEqualTo(String value) {
            addCriterion("email =", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("email = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmailNotEqualTo(String value) {
            addCriterion("email <>", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("email <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThan(String value) {
            addCriterion("email >", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("email > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThanOrEqualTo(String value) {
            addCriterion("email >=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("email >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmailLessThan(String value) {
            addCriterion("email <", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("email < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmailLessThanOrEqualTo(String value) {
            addCriterion("email <=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("email <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andEmailLike(String value) {
            addCriterion("email like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotLike(String value) {
            addCriterion("email not like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailIn(List<String> values) {
            addCriterion("email in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotIn(List<String> values) {
            addCriterion("email not in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailBetween(String value1, String value2) {
            addCriterion("email between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotBetween(String value1, String value2) {
            addCriterion("email not between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andUsercodeIsNull() {
            addCriterion("usercode is null");
            return (Criteria) this;
        }

        public Criteria andUsercodeIsNotNull() {
            addCriterion("usercode is not null");
            return (Criteria) this;
        }

        public Criteria andUsercodeEqualTo(String value) {
            addCriterion("usercode =", value, "usercode");
            return (Criteria) this;
        }

        public Criteria andUsercodeEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("usercode = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsercodeNotEqualTo(String value) {
            addCriterion("usercode <>", value, "usercode");
            return (Criteria) this;
        }

        public Criteria andUsercodeNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("usercode <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsercodeGreaterThan(String value) {
            addCriterion("usercode >", value, "usercode");
            return (Criteria) this;
        }

        public Criteria andUsercodeGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("usercode > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsercodeGreaterThanOrEqualTo(String value) {
            addCriterion("usercode >=", value, "usercode");
            return (Criteria) this;
        }

        public Criteria andUsercodeGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("usercode >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsercodeLessThan(String value) {
            addCriterion("usercode <", value, "usercode");
            return (Criteria) this;
        }

        public Criteria andUsercodeLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("usercode < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsercodeLessThanOrEqualTo(String value) {
            addCriterion("usercode <=", value, "usercode");
            return (Criteria) this;
        }

        public Criteria andUsercodeLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("usercode <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsercodeLike(String value) {
            addCriterion("usercode like", value, "usercode");
            return (Criteria) this;
        }

        public Criteria andUsercodeNotLike(String value) {
            addCriterion("usercode not like", value, "usercode");
            return (Criteria) this;
        }

        public Criteria andUsercodeIn(List<String> values) {
            addCriterion("usercode in", values, "usercode");
            return (Criteria) this;
        }

        public Criteria andUsercodeNotIn(List<String> values) {
            addCriterion("usercode not in", values, "usercode");
            return (Criteria) this;
        }

        public Criteria andUsercodeBetween(String value1, String value2) {
            addCriterion("usercode between", value1, value2, "usercode");
            return (Criteria) this;
        }

        public Criteria andUsercodeNotBetween(String value1, String value2) {
            addCriterion("usercode not between", value1, value2, "usercode");
            return (Criteria) this;
        }

        public Criteria andIhrusercodeIsNull() {
            addCriterion("ihrusercode is null");
            return (Criteria) this;
        }

        public Criteria andIhrusercodeIsNotNull() {
            addCriterion("ihrusercode is not null");
            return (Criteria) this;
        }

        public Criteria andIhrusercodeEqualTo(String value) {
            addCriterion("ihrusercode =", value, "ihrusercode");
            return (Criteria) this;
        }

        public Criteria andIhrusercodeEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("ihrusercode = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIhrusercodeNotEqualTo(String value) {
            addCriterion("ihrusercode <>", value, "ihrusercode");
            return (Criteria) this;
        }

        public Criteria andIhrusercodeNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("ihrusercode <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIhrusercodeGreaterThan(String value) {
            addCriterion("ihrusercode >", value, "ihrusercode");
            return (Criteria) this;
        }

        public Criteria andIhrusercodeGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("ihrusercode > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIhrusercodeGreaterThanOrEqualTo(String value) {
            addCriterion("ihrusercode >=", value, "ihrusercode");
            return (Criteria) this;
        }

        public Criteria andIhrusercodeGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("ihrusercode >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIhrusercodeLessThan(String value) {
            addCriterion("ihrusercode <", value, "ihrusercode");
            return (Criteria) this;
        }

        public Criteria andIhrusercodeLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("ihrusercode < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIhrusercodeLessThanOrEqualTo(String value) {
            addCriterion("ihrusercode <=", value, "ihrusercode");
            return (Criteria) this;
        }

        public Criteria andIhrusercodeLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("ihrusercode <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andIhrusercodeLike(String value) {
            addCriterion("ihrusercode like", value, "ihrusercode");
            return (Criteria) this;
        }

        public Criteria andIhrusercodeNotLike(String value) {
            addCriterion("ihrusercode not like", value, "ihrusercode");
            return (Criteria) this;
        }

        public Criteria andIhrusercodeIn(List<String> values) {
            addCriterion("ihrusercode in", values, "ihrusercode");
            return (Criteria) this;
        }

        public Criteria andIhrusercodeNotIn(List<String> values) {
            addCriterion("ihrusercode not in", values, "ihrusercode");
            return (Criteria) this;
        }

        public Criteria andIhrusercodeBetween(String value1, String value2) {
            addCriterion("ihrusercode between", value1, value2, "ihrusercode");
            return (Criteria) this;
        }

        public Criteria andIhrusercodeNotBetween(String value1, String value2) {
            addCriterion("ihrusercode not between", value1, value2, "ihrusercode");
            return (Criteria) this;
        }

        public Criteria andCreateorIsNull() {
            addCriterion("createor is null");
            return (Criteria) this;
        }

        public Criteria andCreateorIsNotNull() {
            addCriterion("createor is not null");
            return (Criteria) this;
        }

        public Criteria andCreateorEqualTo(String value) {
            addCriterion("createor =", value, "createor");
            return (Criteria) this;
        }

        public Criteria andCreateorEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("createor = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateorNotEqualTo(String value) {
            addCriterion("createor <>", value, "createor");
            return (Criteria) this;
        }

        public Criteria andCreateorNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("createor <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateorGreaterThan(String value) {
            addCriterion("createor >", value, "createor");
            return (Criteria) this;
        }

        public Criteria andCreateorGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("createor > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateorGreaterThanOrEqualTo(String value) {
            addCriterion("createor >=", value, "createor");
            return (Criteria) this;
        }

        public Criteria andCreateorGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("createor >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateorLessThan(String value) {
            addCriterion("createor <", value, "createor");
            return (Criteria) this;
        }

        public Criteria andCreateorLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("createor < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateorLessThanOrEqualTo(String value) {
            addCriterion("createor <=", value, "createor");
            return (Criteria) this;
        }

        public Criteria andCreateorLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("createor <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreateorLike(String value) {
            addCriterion("createor like", value, "createor");
            return (Criteria) this;
        }

        public Criteria andCreateorNotLike(String value) {
            addCriterion("createor not like", value, "createor");
            return (Criteria) this;
        }

        public Criteria andCreateorIn(List<String> values) {
            addCriterion("createor in", values, "createor");
            return (Criteria) this;
        }

        public Criteria andCreateorNotIn(List<String> values) {
            addCriterion("createor not in", values, "createor");
            return (Criteria) this;
        }

        public Criteria andCreateorBetween(String value1, String value2) {
            addCriterion("createor between", value1, value2, "createor");
            return (Criteria) this;
        }

        public Criteria andCreateorNotBetween(String value1, String value2) {
            addCriterion("createor not between", value1, value2, "createor");
            return (Criteria) this;
        }

        public Criteria andProjectIsNull() {
            addCriterion("project is null");
            return (Criteria) this;
        }

        public Criteria andProjectIsNotNull() {
            addCriterion("project is not null");
            return (Criteria) this;
        }

        public Criteria andProjectEqualTo(String value) {
            addCriterion("project =", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("project = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectNotEqualTo(String value) {
            addCriterion("project <>", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("project <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThan(String value) {
            addCriterion("project >", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("project > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThanOrEqualTo(String value) {
            addCriterion("project >=", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("project >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectLessThan(String value) {
            addCriterion("project <", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("project < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectLessThanOrEqualTo(String value) {
            addCriterion("project <=", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("project <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProjectLike(String value) {
            addCriterion("project like", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotLike(String value) {
            addCriterion("project not like", value, "project");
            return (Criteria) this;
        }

        public Criteria andProjectIn(List<String> values) {
            addCriterion("project in", values, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotIn(List<String> values) {
            addCriterion("project not in", values, "project");
            return (Criteria) this;
        }

        public Criteria andProjectBetween(String value1, String value2) {
            addCriterion("project between", value1, value2, "project");
            return (Criteria) this;
        }

        public Criteria andProjectNotBetween(String value1, String value2) {
            addCriterion("project not between", value1, value2, "project");
            return (Criteria) this;
        }

        public Criteria andCostcenterIsNull() {
            addCriterion("costcenter is null");
            return (Criteria) this;
        }

        public Criteria andCostcenterIsNotNull() {
            addCriterion("costcenter is not null");
            return (Criteria) this;
        }

        public Criteria andCostcenterEqualTo(String value) {
            addCriterion("costcenter =", value, "costcenter");
            return (Criteria) this;
        }

        public Criteria andCostcenterEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("costcenter = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostcenterNotEqualTo(String value) {
            addCriterion("costcenter <>", value, "costcenter");
            return (Criteria) this;
        }

        public Criteria andCostcenterNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("costcenter <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostcenterGreaterThan(String value) {
            addCriterion("costcenter >", value, "costcenter");
            return (Criteria) this;
        }

        public Criteria andCostcenterGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("costcenter > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostcenterGreaterThanOrEqualTo(String value) {
            addCriterion("costcenter >=", value, "costcenter");
            return (Criteria) this;
        }

        public Criteria andCostcenterGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("costcenter >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostcenterLessThan(String value) {
            addCriterion("costcenter <", value, "costcenter");
            return (Criteria) this;
        }

        public Criteria andCostcenterLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("costcenter < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostcenterLessThanOrEqualTo(String value) {
            addCriterion("costcenter <=", value, "costcenter");
            return (Criteria) this;
        }

        public Criteria andCostcenterLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("costcenter <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostcenterLike(String value) {
            addCriterion("costcenter like", value, "costcenter");
            return (Criteria) this;
        }

        public Criteria andCostcenterNotLike(String value) {
            addCriterion("costcenter not like", value, "costcenter");
            return (Criteria) this;
        }

        public Criteria andCostcenterIn(List<String> values) {
            addCriterion("costcenter in", values, "costcenter");
            return (Criteria) this;
        }

        public Criteria andCostcenterNotIn(List<String> values) {
            addCriterion("costcenter not in", values, "costcenter");
            return (Criteria) this;
        }

        public Criteria andCostcenterBetween(String value1, String value2) {
            addCriterion("costcenter between", value1, value2, "costcenter");
            return (Criteria) this;
        }

        public Criteria andCostcenterNotBetween(String value1, String value2) {
            addCriterion("costcenter not between", value1, value2, "costcenter");
            return (Criteria) this;
        }

        public Criteria andCostcenternameIsNull() {
            addCriterion("costcenterName is null");
            return (Criteria) this;
        }

        public Criteria andCostcenternameIsNotNull() {
            addCriterion("costcenterName is not null");
            return (Criteria) this;
        }

        public Criteria andCostcenternameEqualTo(String value) {
            addCriterion("costcenterName =", value, "costcentername");
            return (Criteria) this;
        }

        public Criteria andCostcenternameEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("costcenterName = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostcenternameNotEqualTo(String value) {
            addCriterion("costcenterName <>", value, "costcentername");
            return (Criteria) this;
        }

        public Criteria andCostcenternameNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("costcenterName <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostcenternameGreaterThan(String value) {
            addCriterion("costcenterName >", value, "costcentername");
            return (Criteria) this;
        }

        public Criteria andCostcenternameGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("costcenterName > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostcenternameGreaterThanOrEqualTo(String value) {
            addCriterion("costcenterName >=", value, "costcentername");
            return (Criteria) this;
        }

        public Criteria andCostcenternameGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("costcenterName >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostcenternameLessThan(String value) {
            addCriterion("costcenterName <", value, "costcentername");
            return (Criteria) this;
        }

        public Criteria andCostcenternameLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("costcenterName < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostcenternameLessThanOrEqualTo(String value) {
            addCriterion("costcenterName <=", value, "costcentername");
            return (Criteria) this;
        }

        public Criteria andCostcenternameLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("costcenterName <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCostcenternameLike(String value) {
            addCriterion("costcenterName like", value, "costcentername");
            return (Criteria) this;
        }

        public Criteria andCostcenternameNotLike(String value) {
            addCriterion("costcenterName not like", value, "costcentername");
            return (Criteria) this;
        }

        public Criteria andCostcenternameIn(List<String> values) {
            addCriterion("costcenterName in", values, "costcentername");
            return (Criteria) this;
        }

        public Criteria andCostcenternameNotIn(List<String> values) {
            addCriterion("costcenterName not in", values, "costcentername");
            return (Criteria) this;
        }

        public Criteria andCostcenternameBetween(String value1, String value2) {
            addCriterion("costcenterName between", value1, value2, "costcentername");
            return (Criteria) this;
        }

        public Criteria andCostcenternameNotBetween(String value1, String value2) {
            addCriterion("costcenterName not between", value1, value2, "costcentername");
            return (Criteria) this;
        }

        public Criteria andSellerdeptIsNull() {
            addCriterion("sellerdept is null");
            return (Criteria) this;
        }

        public Criteria andSellerdeptIsNotNull() {
            addCriterion("sellerdept is not null");
            return (Criteria) this;
        }

        public Criteria andSellerdeptEqualTo(String value) {
            addCriterion("sellerdept =", value, "sellerdept");
            return (Criteria) this;
        }

        public Criteria andSellerdeptEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("sellerdept = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerdeptNotEqualTo(String value) {
            addCriterion("sellerdept <>", value, "sellerdept");
            return (Criteria) this;
        }

        public Criteria andSellerdeptNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("sellerdept <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerdeptGreaterThan(String value) {
            addCriterion("sellerdept >", value, "sellerdept");
            return (Criteria) this;
        }

        public Criteria andSellerdeptGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("sellerdept > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerdeptGreaterThanOrEqualTo(String value) {
            addCriterion("sellerdept >=", value, "sellerdept");
            return (Criteria) this;
        }

        public Criteria andSellerdeptGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("sellerdept >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerdeptLessThan(String value) {
            addCriterion("sellerdept <", value, "sellerdept");
            return (Criteria) this;
        }

        public Criteria andSellerdeptLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("sellerdept < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerdeptLessThanOrEqualTo(String value) {
            addCriterion("sellerdept <=", value, "sellerdept");
            return (Criteria) this;
        }

        public Criteria andSellerdeptLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("sellerdept <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerdeptLike(String value) {
            addCriterion("sellerdept like", value, "sellerdept");
            return (Criteria) this;
        }

        public Criteria andSellerdeptNotLike(String value) {
            addCriterion("sellerdept not like", value, "sellerdept");
            return (Criteria) this;
        }

        public Criteria andSellerdeptIn(List<String> values) {
            addCriterion("sellerdept in", values, "sellerdept");
            return (Criteria) this;
        }

        public Criteria andSellerdeptNotIn(List<String> values) {
            addCriterion("sellerdept not in", values, "sellerdept");
            return (Criteria) this;
        }

        public Criteria andSellerdeptBetween(String value1, String value2) {
            addCriterion("sellerdept between", value1, value2, "sellerdept");
            return (Criteria) this;
        }

        public Criteria andSellerdeptNotBetween(String value1, String value2) {
            addCriterion("sellerdept not between", value1, value2, "sellerdept");
            return (Criteria) this;
        }

        public Criteria andSellerteamIsNull() {
            addCriterion("sellerteam is null");
            return (Criteria) this;
        }

        public Criteria andSellerteamIsNotNull() {
            addCriterion("sellerteam is not null");
            return (Criteria) this;
        }

        public Criteria andSellerteamEqualTo(String value) {
            addCriterion("sellerteam =", value, "sellerteam");
            return (Criteria) this;
        }

        public Criteria andSellerteamEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("sellerteam = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerteamNotEqualTo(String value) {
            addCriterion("sellerteam <>", value, "sellerteam");
            return (Criteria) this;
        }

        public Criteria andSellerteamNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("sellerteam <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerteamGreaterThan(String value) {
            addCriterion("sellerteam >", value, "sellerteam");
            return (Criteria) this;
        }

        public Criteria andSellerteamGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("sellerteam > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerteamGreaterThanOrEqualTo(String value) {
            addCriterion("sellerteam >=", value, "sellerteam");
            return (Criteria) this;
        }

        public Criteria andSellerteamGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("sellerteam >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerteamLessThan(String value) {
            addCriterion("sellerteam <", value, "sellerteam");
            return (Criteria) this;
        }

        public Criteria andSellerteamLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("sellerteam < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerteamLessThanOrEqualTo(String value) {
            addCriterion("sellerteam <=", value, "sellerteam");
            return (Criteria) this;
        }

        public Criteria andSellerteamLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("sellerteam <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andSellerteamLike(String value) {
            addCriterion("sellerteam like", value, "sellerteam");
            return (Criteria) this;
        }

        public Criteria andSellerteamNotLike(String value) {
            addCriterion("sellerteam not like", value, "sellerteam");
            return (Criteria) this;
        }

        public Criteria andSellerteamIn(List<String> values) {
            addCriterion("sellerteam in", values, "sellerteam");
            return (Criteria) this;
        }

        public Criteria andSellerteamNotIn(List<String> values) {
            addCriterion("sellerteam not in", values, "sellerteam");
            return (Criteria) this;
        }

        public Criteria andSellerteamBetween(String value1, String value2) {
            addCriterion("sellerteam between", value1, value2, "sellerteam");
            return (Criteria) this;
        }

        public Criteria andSellerteamNotBetween(String value1, String value2) {
            addCriterion("sellerteam not between", value1, value2, "sellerteam");
            return (Criteria) this;
        }

        public Criteria andCreatetimeIsNull() {
            addCriterion("createtime is null");
            return (Criteria) this;
        }

        public Criteria andCreatetimeIsNotNull() {
            addCriterion("createtime is not null");
            return (Criteria) this;
        }

        public Criteria andCreatetimeEqualTo(Date value) {
            addCriterion("createtime =", value, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("createtime = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatetimeNotEqualTo(Date value) {
            addCriterion("createtime <>", value, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("createtime <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatetimeGreaterThan(Date value) {
            addCriterion("createtime >", value, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("createtime > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("createtime >=", value, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("createtime >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatetimeLessThan(Date value) {
            addCriterion("createtime <", value, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("createtime < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatetimeLessThanOrEqualTo(Date value) {
            addCriterion("createtime <=", value, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("createtime <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andCreatetimeIn(List<Date> values) {
            addCriterion("createtime in", values, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeNotIn(List<Date> values) {
            addCriterion("createtime not in", values, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeBetween(Date value1, Date value2) {
            addCriterion("createtime between", value1, value2, "createtime");
            return (Criteria) this;
        }

        public Criteria andCreatetimeNotBetween(Date value1, Date value2) {
            addCriterion("createtime not between", value1, value2, "createtime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeIsNull() {
            addCriterion("updatetime is null");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeIsNotNull() {
            addCriterion("updatetime is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeEqualTo(Date value) {
            addCriterion("updatetime =", value, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("updatetime = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdatetimeNotEqualTo(Date value) {
            addCriterion("updatetime <>", value, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("updatetime <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdatetimeGreaterThan(Date value) {
            addCriterion("updatetime >", value, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("updatetime > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdatetimeGreaterThanOrEqualTo(Date value) {
            addCriterion("updatetime >=", value, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("updatetime >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdatetimeLessThan(Date value) {
            addCriterion("updatetime <", value, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("updatetime < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdatetimeLessThanOrEqualTo(Date value) {
            addCriterion("updatetime <=", value, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("updatetime <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUpdatetimeIn(List<Date> values) {
            addCriterion("updatetime in", values, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeNotIn(List<Date> values) {
            addCriterion("updatetime not in", values, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeBetween(Date value1, Date value2) {
            addCriterion("updatetime between", value1, value2, "updatetime");
            return (Criteria) this;
        }

        public Criteria andUpdatetimeNotBetween(Date value1, Date value2) {
            addCriterion("updatetime not between", value1, value2, "updatetime");
            return (Criteria) this;
        }

        public Criteria andProvincenameIsNull() {
            addCriterion("provincename is null");
            return (Criteria) this;
        }

        public Criteria andProvincenameIsNotNull() {
            addCriterion("provincename is not null");
            return (Criteria) this;
        }

        public Criteria andProvincenameEqualTo(String value) {
            addCriterion("provincename =", value, "provincename");
            return (Criteria) this;
        }

        public Criteria andProvincenameEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("provincename = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvincenameNotEqualTo(String value) {
            addCriterion("provincename <>", value, "provincename");
            return (Criteria) this;
        }

        public Criteria andProvincenameNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("provincename <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvincenameGreaterThan(String value) {
            addCriterion("provincename >", value, "provincename");
            return (Criteria) this;
        }

        public Criteria andProvincenameGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("provincename > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvincenameGreaterThanOrEqualTo(String value) {
            addCriterion("provincename >=", value, "provincename");
            return (Criteria) this;
        }

        public Criteria andProvincenameGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("provincename >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvincenameLessThan(String value) {
            addCriterion("provincename <", value, "provincename");
            return (Criteria) this;
        }

        public Criteria andProvincenameLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("provincename < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvincenameLessThanOrEqualTo(String value) {
            addCriterion("provincename <=", value, "provincename");
            return (Criteria) this;
        }

        public Criteria andProvincenameLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("provincename <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvincenameLike(String value) {
            addCriterion("provincename like", value, "provincename");
            return (Criteria) this;
        }

        public Criteria andProvincenameNotLike(String value) {
            addCriterion("provincename not like", value, "provincename");
            return (Criteria) this;
        }

        public Criteria andProvincenameIn(List<String> values) {
            addCriterion("provincename in", values, "provincename");
            return (Criteria) this;
        }

        public Criteria andProvincenameNotIn(List<String> values) {
            addCriterion("provincename not in", values, "provincename");
            return (Criteria) this;
        }

        public Criteria andProvincenameBetween(String value1, String value2) {
            addCriterion("provincename between", value1, value2, "provincename");
            return (Criteria) this;
        }

        public Criteria andProvincenameNotBetween(String value1, String value2) {
            addCriterion("provincename not between", value1, value2, "provincename");
            return (Criteria) this;
        }

        public Criteria andProvincecodeIsNull() {
            addCriterion("provincecode is null");
            return (Criteria) this;
        }

        public Criteria andProvincecodeIsNotNull() {
            addCriterion("provincecode is not null");
            return (Criteria) this;
        }

        public Criteria andProvincecodeEqualTo(String value) {
            addCriterion("provincecode =", value, "provincecode");
            return (Criteria) this;
        }

        public Criteria andProvincecodeEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("provincecode = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvincecodeNotEqualTo(String value) {
            addCriterion("provincecode <>", value, "provincecode");
            return (Criteria) this;
        }

        public Criteria andProvincecodeNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("provincecode <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvincecodeGreaterThan(String value) {
            addCriterion("provincecode >", value, "provincecode");
            return (Criteria) this;
        }

        public Criteria andProvincecodeGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("provincecode > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvincecodeGreaterThanOrEqualTo(String value) {
            addCriterion("provincecode >=", value, "provincecode");
            return (Criteria) this;
        }

        public Criteria andProvincecodeGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("provincecode >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvincecodeLessThan(String value) {
            addCriterion("provincecode <", value, "provincecode");
            return (Criteria) this;
        }

        public Criteria andProvincecodeLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("provincecode < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvincecodeLessThanOrEqualTo(String value) {
            addCriterion("provincecode <=", value, "provincecode");
            return (Criteria) this;
        }

        public Criteria andProvincecodeLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("provincecode <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andProvincecodeLike(String value) {
            addCriterion("provincecode like", value, "provincecode");
            return (Criteria) this;
        }

        public Criteria andProvincecodeNotLike(String value) {
            addCriterion("provincecode not like", value, "provincecode");
            return (Criteria) this;
        }

        public Criteria andProvincecodeIn(List<String> values) {
            addCriterion("provincecode in", values, "provincecode");
            return (Criteria) this;
        }

        public Criteria andProvincecodeNotIn(List<String> values) {
            addCriterion("provincecode not in", values, "provincecode");
            return (Criteria) this;
        }

        public Criteria andProvincecodeBetween(String value1, String value2) {
            addCriterion("provincecode between", value1, value2, "provincecode");
            return (Criteria) this;
        }

        public Criteria andProvincecodeNotBetween(String value1, String value2) {
            addCriterion("provincecode not between", value1, value2, "provincecode");
            return (Criteria) this;
        }

        public Criteria andUsertypeIsNull() {
            addCriterion("usertype is null");
            return (Criteria) this;
        }

        public Criteria andUsertypeIsNotNull() {
            addCriterion("usertype is not null");
            return (Criteria) this;
        }

        public Criteria andUsertypeEqualTo(String value) {
            addCriterion("usertype =", value, "usertype");
            return (Criteria) this;
        }

        public Criteria andUsertypeEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("usertype = ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsertypeNotEqualTo(String value) {
            addCriterion("usertype <>", value, "usertype");
            return (Criteria) this;
        }

        public Criteria andUsertypeNotEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("usertype <> ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsertypeGreaterThan(String value) {
            addCriterion("usertype >", value, "usertype");
            return (Criteria) this;
        }

        public Criteria andUsertypeGreaterThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("usertype > ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsertypeGreaterThanOrEqualTo(String value) {
            addCriterion("usertype >=", value, "usertype");
            return (Criteria) this;
        }

        public Criteria andUsertypeGreaterThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("usertype >= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsertypeLessThan(String value) {
            addCriterion("usertype <", value, "usertype");
            return (Criteria) this;
        }

        public Criteria andUsertypeLessThanColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("usertype < ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsertypeLessThanOrEqualTo(String value) {
            addCriterion("usertype <=", value, "usertype");
            return (Criteria) this;
        }

        public Criteria andUsertypeLessThanOrEqualToColumn(UserK3.Column column) {
            addCriterion(new StringBuilder("usertype <= ").append(column.getEscapedColumnName()).toString());
            return (Criteria) this;
        }

        public Criteria andUsertypeLike(String value) {
            addCriterion("usertype like", value, "usertype");
            return (Criteria) this;
        }

        public Criteria andUsertypeNotLike(String value) {
            addCriterion("usertype not like", value, "usertype");
            return (Criteria) this;
        }

        public Criteria andUsertypeIn(List<String> values) {
            addCriterion("usertype in", values, "usertype");
            return (Criteria) this;
        }

        public Criteria andUsertypeNotIn(List<String> values) {
            addCriterion("usertype not in", values, "usertype");
            return (Criteria) this;
        }

        public Criteria andUsertypeBetween(String value1, String value2) {
            addCriterion("usertype between", value1, value2, "usertype");
            return (Criteria) this;
        }

        public Criteria andUsertypeNotBetween(String value1, String value2) {
            addCriterion("usertype not between", value1, value2, "usertype");
            return (Criteria) this;
        }

        public Criteria andIdLikeInsensitive(String value) {
            addCriterion("upper(id) like", value.toUpperCase(), "id");
            return (Criteria) this;
        }

        public Criteria andAccountLikeInsensitive(String value) {
            addCriterion("upper(account) like", value.toUpperCase(), "account");
            return (Criteria) this;
        }

        public Criteria andNameLikeInsensitive(String value) {
            addCriterion("upper(name) like", value.toUpperCase(), "name");
            return (Criteria) this;
        }

        public Criteria andDepartmentLikeInsensitive(String value) {
            addCriterion("upper(department) like", value.toUpperCase(), "department");
            return (Criteria) this;
        }

        public Criteria andTeamLikeInsensitive(String value) {
            addCriterion("upper(team) like", value.toUpperCase(), "team");
            return (Criteria) this;
        }

        public Criteria andPhoneLikeInsensitive(String value) {
            addCriterion("upper(phone) like", value.toUpperCase(), "phone");
            return (Criteria) this;
        }

        public Criteria andEmailLikeInsensitive(String value) {
            addCriterion("upper(email) like", value.toUpperCase(), "email");
            return (Criteria) this;
        }

        public Criteria andUsercodeLikeInsensitive(String value) {
            addCriterion("upper(usercode) like", value.toUpperCase(), "usercode");
            return (Criteria) this;
        }

        public Criteria andIhrusercodeLikeInsensitive(String value) {
            addCriterion("upper(ihrusercode) like", value.toUpperCase(), "ihrusercode");
            return (Criteria) this;
        }

        public Criteria andCreateorLikeInsensitive(String value) {
            addCriterion("upper(createor) like", value.toUpperCase(), "createor");
            return (Criteria) this;
        }

        public Criteria andProjectLikeInsensitive(String value) {
            addCriterion("upper(project) like", value.toUpperCase(), "project");
            return (Criteria) this;
        }

        public Criteria andCostcenterLikeInsensitive(String value) {
            addCriterion("upper(costcenter) like", value.toUpperCase(), "costcenter");
            return (Criteria) this;
        }

        public Criteria andCostcenternameLikeInsensitive(String value) {
            addCriterion("upper(costcenterName) like", value.toUpperCase(), "costcentername");
            return (Criteria) this;
        }

        public Criteria andSellerdeptLikeInsensitive(String value) {
            addCriterion("upper(sellerdept) like", value.toUpperCase(), "sellerdept");
            return (Criteria) this;
        }

        public Criteria andSellerteamLikeInsensitive(String value) {
            addCriterion("upper(sellerteam) like", value.toUpperCase(), "sellerteam");
            return (Criteria) this;
        }

        public Criteria andProvincenameLikeInsensitive(String value) {
            addCriterion("upper(provincename) like", value.toUpperCase(), "provincename");
            return (Criteria) this;
        }

        public Criteria andProvincecodeLikeInsensitive(String value) {
            addCriterion("upper(provincecode) like", value.toUpperCase(), "provincecode");
            return (Criteria) this;
        }

        public Criteria andUsertypeLikeInsensitive(String value) {
            addCriterion("upper(usertype) like", value.toUpperCase(), "usertype");
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge Thu Nov 21 23:08:51 CST 2024
     */
    public static class Criteria extends GeneratedCriteria {
        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        private UserK3Example example;

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        protected Criteria(UserK3Example example) {
            super();
            this.example = example;
        }

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        public UserK3Example example() {
            return this.example;
        }

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        @Deprecated
        public Criteria andIf(boolean ifAdd, ICriteriaAdd add) {
            if (ifAdd) {
                add.add(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then) {
            if (condition) {
                then.criteria(this);
            }
            return this;
        }

        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        public Criteria when(boolean condition, ICriteriaWhen then, ICriteriaWhen otherwise) {
            if (condition) {
                then.criteria(this);
            } else {
                otherwise.criteria(this);
            }
            return this;
        }

        @Deprecated
        public interface ICriteriaAdd {
            /**
             *
             * @mbg.generated Thu Nov 21 23:08:51 CST 2024
             */
            Criteria add(Criteria add);
        }
    }

    /**
     *
     * @mbg.generated Thu Nov 21 23:08:51 CST 2024
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    public interface ICriteriaWhen {
        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        void criteria(Criteria criteria);
    }

    public interface IExampleWhen {
        /**
         *
         * @mbg.generated Thu Nov 21 23:08:51 CST 2024
         */
        void example(com.chinamobile.iot.sc.pojo.entity.UserK3Example example);
    }
}