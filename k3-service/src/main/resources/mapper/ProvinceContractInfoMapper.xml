<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ProvinceContractInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 06 08:46:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="province_contract_no" jdbcType="VARCHAR" property="provinceContractNo" />
    <result column="province_contract_name" jdbcType="VARCHAR" property="provinceContractName" />
    <result column="Internet_contract_code" jdbcType="VARCHAR" property="internetContractCode" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="buyer_no" jdbcType="VARCHAR" property="buyerNo" />
    <result column="apply_department" jdbcType="VARCHAR" property="applyDepartment" />
    <result column="apply_department_no" jdbcType="VARCHAR" property="applyDepartmentNo" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="contract_type" jdbcType="VARCHAR" property="contractType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 06 08:46:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 06 08:46:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 06 08:46:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    id, province_contract_no, province_contract_name, Internet_contract_code, buyer_name, 
    buyer_no, apply_department, apply_department_no, company_name, company_code, province_name, 
    city_name, province_code, contract_type, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 06 08:46:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from province_contract_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 06 08:46:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from province_contract_info
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 06 08:46:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from province_contract_info
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 06 08:46:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from province_contract_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 06 08:46:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into province_contract_info (id, province_contract_no, province_contract_name, 
      Internet_contract_code, buyer_name, buyer_no, 
      apply_department, apply_department_no, company_name, 
      company_code, province_name, city_name, 
      province_code, contract_type, create_time, 
      update_time)
    values (#{id,jdbcType=VARCHAR}, #{provinceContractNo,jdbcType=VARCHAR}, #{provinceContractName,jdbcType=VARCHAR}, 
      #{internetContractCode,jdbcType=VARCHAR}, #{buyerName,jdbcType=VARCHAR}, #{buyerNo,jdbcType=VARCHAR}, 
      #{applyDepartment,jdbcType=VARCHAR}, #{applyDepartmentNo,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, 
      #{companyCode,jdbcType=VARCHAR}, #{provinceName,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR}, 
      #{provinceCode,jdbcType=VARCHAR}, #{contractType,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 06 08:46:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into province_contract_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="provinceContractNo != null">
        province_contract_no,
      </if>
      <if test="provinceContractName != null">
        province_contract_name,
      </if>
      <if test="internetContractCode != null">
        Internet_contract_code,
      </if>
      <if test="buyerName != null">
        buyer_name,
      </if>
      <if test="buyerNo != null">
        buyer_no,
      </if>
      <if test="applyDepartment != null">
        apply_department,
      </if>
      <if test="applyDepartmentNo != null">
        apply_department_no,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="contractType != null">
        contract_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="provinceContractNo != null">
        #{provinceContractNo,jdbcType=VARCHAR},
      </if>
      <if test="provinceContractName != null">
        #{provinceContractName,jdbcType=VARCHAR},
      </if>
      <if test="internetContractCode != null">
        #{internetContractCode,jdbcType=VARCHAR},
      </if>
      <if test="buyerName != null">
        #{buyerName,jdbcType=VARCHAR},
      </if>
      <if test="buyerNo != null">
        #{buyerNo,jdbcType=VARCHAR},
      </if>
      <if test="applyDepartment != null">
        #{applyDepartment,jdbcType=VARCHAR},
      </if>
      <if test="applyDepartmentNo != null">
        #{applyDepartmentNo,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="contractType != null">
        #{contractType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 06 08:46:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    select count(*) from province_contract_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 06 08:46:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    update province_contract_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceContractNo != null">
        province_contract_no = #{record.provinceContractNo,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceContractName != null">
        province_contract_name = #{record.provinceContractName,jdbcType=VARCHAR},
      </if>
      <if test="record.internetContractCode != null">
        Internet_contract_code = #{record.internetContractCode,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerName != null">
        buyer_name = #{record.buyerName,jdbcType=VARCHAR},
      </if>
      <if test="record.buyerNo != null">
        buyer_no = #{record.buyerNo,jdbcType=VARCHAR},
      </if>
      <if test="record.applyDepartment != null">
        apply_department = #{record.applyDepartment,jdbcType=VARCHAR},
      </if>
      <if test="record.applyDepartmentNo != null">
        apply_department_no = #{record.applyDepartmentNo,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceName != null">
        province_name = #{record.provinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.cityName != null">
        city_name = #{record.cityName,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.contractType != null">
        contract_type = #{record.contractType,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 06 08:46:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    update province_contract_info
    set id = #{record.id,jdbcType=VARCHAR},
      province_contract_no = #{record.provinceContractNo,jdbcType=VARCHAR},
      province_contract_name = #{record.provinceContractName,jdbcType=VARCHAR},
      Internet_contract_code = #{record.internetContractCode,jdbcType=VARCHAR},
      buyer_name = #{record.buyerName,jdbcType=VARCHAR},
      buyer_no = #{record.buyerNo,jdbcType=VARCHAR},
      apply_department = #{record.applyDepartment,jdbcType=VARCHAR},
      apply_department_no = #{record.applyDepartmentNo,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      province_name = #{record.provinceName,jdbcType=VARCHAR},
      city_name = #{record.cityName,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      contract_type = #{record.contractType,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 06 08:46:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    update province_contract_info
    <set>
      <if test="provinceContractNo != null">
        province_contract_no = #{provinceContractNo,jdbcType=VARCHAR},
      </if>
      <if test="provinceContractName != null">
        province_contract_name = #{provinceContractName,jdbcType=VARCHAR},
      </if>
      <if test="internetContractCode != null">
        Internet_contract_code = #{internetContractCode,jdbcType=VARCHAR},
      </if>
      <if test="buyerName != null">
        buyer_name = #{buyerName,jdbcType=VARCHAR},
      </if>
      <if test="buyerNo != null">
        buyer_no = #{buyerNo,jdbcType=VARCHAR},
      </if>
      <if test="applyDepartment != null">
        apply_department = #{applyDepartment,jdbcType=VARCHAR},
      </if>
      <if test="applyDepartmentNo != null">
        apply_department_no = #{applyDepartmentNo,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        province_code = #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="contractType != null">
        contract_type = #{contractType,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 06 08:46:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    update province_contract_info
    set province_contract_no = #{provinceContractNo,jdbcType=VARCHAR},
      province_contract_name = #{provinceContractName,jdbcType=VARCHAR},
      Internet_contract_code = #{internetContractCode,jdbcType=VARCHAR},
      buyer_name = #{buyerName,jdbcType=VARCHAR},
      buyer_no = #{buyerNo,jdbcType=VARCHAR},
      apply_department = #{applyDepartment,jdbcType=VARCHAR},
      apply_department_no = #{applyDepartmentNo,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      province_code = #{provinceCode,jdbcType=VARCHAR},
      contract_type = #{contractType,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 06 08:46:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into province_contract_info
    (id, province_contract_no, province_contract_name, Internet_contract_code, buyer_name, 
      buyer_no, apply_department, apply_department_no, company_name, company_code, province_name, 
      city_name, province_code, contract_type, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.provinceContractNo,jdbcType=VARCHAR}, #{item.provinceContractName,jdbcType=VARCHAR}, 
        #{item.internetContractCode,jdbcType=VARCHAR}, #{item.buyerName,jdbcType=VARCHAR}, 
        #{item.buyerNo,jdbcType=VARCHAR}, #{item.applyDepartment,jdbcType=VARCHAR}, #{item.applyDepartmentNo,jdbcType=VARCHAR}, 
        #{item.companyName,jdbcType=VARCHAR}, #{item.companyCode,jdbcType=VARCHAR}, #{item.provinceName,jdbcType=VARCHAR}, 
        #{item.cityName,jdbcType=VARCHAR}, #{item.provinceCode,jdbcType=VARCHAR}, #{item.contractType,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Sep 06 08:46:25 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into province_contract_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'province_contract_no'.toString() == column.value">
          #{item.provinceContractNo,jdbcType=VARCHAR}
        </if>
        <if test="'province_contract_name'.toString() == column.value">
          #{item.provinceContractName,jdbcType=VARCHAR}
        </if>
        <if test="'Internet_contract_code'.toString() == column.value">
          #{item.internetContractCode,jdbcType=VARCHAR}
        </if>
        <if test="'buyer_name'.toString() == column.value">
          #{item.buyerName,jdbcType=VARCHAR}
        </if>
        <if test="'buyer_no'.toString() == column.value">
          #{item.buyerNo,jdbcType=VARCHAR}
        </if>
        <if test="'apply_department'.toString() == column.value">
          #{item.applyDepartment,jdbcType=VARCHAR}
        </if>
        <if test="'apply_department_no'.toString() == column.value">
          #{item.applyDepartmentNo,jdbcType=VARCHAR}
        </if>
        <if test="'company_name'.toString() == column.value">
          #{item.companyName,jdbcType=VARCHAR}
        </if>
        <if test="'company_code'.toString() == column.value">
          #{item.companyCode,jdbcType=VARCHAR}
        </if>
        <if test="'province_name'.toString() == column.value">
          #{item.provinceName,jdbcType=VARCHAR}
        </if>
        <if test="'city_name'.toString() == column.value">
          #{item.cityName,jdbcType=VARCHAR}
        </if>
        <if test="'province_code'.toString() == column.value">
          #{item.provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="'contract_type'.toString() == column.value">
          #{item.contractType,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>