<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.OnlineSettlementPurchaseOrderMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementPurchaseOrder">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 15:31:26 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="prikey" jdbcType="VARCHAR" property="prikey" />
    <result column="source_type" jdbcType="VARCHAR" property="sourceType" />
    <result column="scm_order_num" jdbcType="VARCHAR" property="scmOrderNum" />
    <result column="po_order_num" jdbcType="VARCHAR" property="poOrderNum" />
    <result column="settle_type" jdbcType="VARCHAR" property="settleType" />
    <result column="contract_number" jdbcType="VARCHAR" property="contractNumber" />
    <result column="contract_name" jdbcType="VARCHAR" property="contractName" />
    <result column="contract_type" jdbcType="VARCHAR" property="contractType" />
    <result column="be_id" jdbcType="VARCHAR" property="beId" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="settle_price" jdbcType="BIGINT" property="settlePrice" />
    <result column="settle_status" jdbcType="VARCHAR" property="settleStatus" />
    <result column="tax_inclusive_total_settle_price" jdbcType="DECIMAL" property="taxInclusiveTotalSettlePrice" />
    <result column="syn_status" jdbcType="INTEGER" property="synStatus" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 15:31:26 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 15:31:26 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 15:31:26 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, prikey, source_type, scm_order_num, po_order_num, settle_type, contract_number, 
    contract_name, contract_type, be_id, location, settle_price, settle_status, tax_inclusive_total_settle_price, 
    syn_status, province_code, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementPurchaseOrderExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 15:31:26 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from online_settlement_purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 15:31:26 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from online_settlement_purchase_order
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 15:31:26 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from online_settlement_purchase_order
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementPurchaseOrderExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 15:31:26 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from online_settlement_purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementPurchaseOrder">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 15:31:26 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into online_settlement_purchase_order (id, prikey, source_type, 
      scm_order_num, po_order_num, settle_type, 
      contract_number, contract_name, contract_type, 
      be_id, location, settle_price, 
      settle_status, tax_inclusive_total_settle_price, 
      syn_status, province_code, create_time, 
      update_time)
    values (#{id,jdbcType=VARCHAR}, #{prikey,jdbcType=VARCHAR}, #{sourceType,jdbcType=VARCHAR}, 
      #{scmOrderNum,jdbcType=VARCHAR}, #{poOrderNum,jdbcType=VARCHAR}, #{settleType,jdbcType=VARCHAR}, 
      #{contractNumber,jdbcType=VARCHAR}, #{contractName,jdbcType=VARCHAR}, #{contractType,jdbcType=VARCHAR}, 
      #{beId,jdbcType=VARCHAR}, #{location,jdbcType=VARCHAR}, #{settlePrice,jdbcType=BIGINT}, 
      #{settleStatus,jdbcType=VARCHAR}, #{taxInclusiveTotalSettlePrice,jdbcType=DECIMAL}, 
      #{synStatus,jdbcType=INTEGER}, #{provinceCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementPurchaseOrder">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 15:31:26 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into online_settlement_purchase_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="prikey != null">
        prikey,
      </if>
      <if test="sourceType != null">
        source_type,
      </if>
      <if test="scmOrderNum != null">
        scm_order_num,
      </if>
      <if test="poOrderNum != null">
        po_order_num,
      </if>
      <if test="settleType != null">
        settle_type,
      </if>
      <if test="contractNumber != null">
        contract_number,
      </if>
      <if test="contractName != null">
        contract_name,
      </if>
      <if test="contractType != null">
        contract_type,
      </if>
      <if test="beId != null">
        be_id,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="settlePrice != null">
        settle_price,
      </if>
      <if test="settleStatus != null">
        settle_status,
      </if>
      <if test="taxInclusiveTotalSettlePrice != null">
        tax_inclusive_total_settle_price,
      </if>
      <if test="synStatus != null">
        syn_status,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="prikey != null">
        #{prikey,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="scmOrderNum != null">
        #{scmOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="poOrderNum != null">
        #{poOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="settleType != null">
        #{settleType,jdbcType=VARCHAR},
      </if>
      <if test="contractNumber != null">
        #{contractNumber,jdbcType=VARCHAR},
      </if>
      <if test="contractName != null">
        #{contractName,jdbcType=VARCHAR},
      </if>
      <if test="contractType != null">
        #{contractType,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        #{beId,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="settlePrice != null">
        #{settlePrice,jdbcType=BIGINT},
      </if>
      <if test="settleStatus != null">
        #{settleStatus,jdbcType=VARCHAR},
      </if>
      <if test="taxInclusiveTotalSettlePrice != null">
        #{taxInclusiveTotalSettlePrice,jdbcType=DECIMAL},
      </if>
      <if test="synStatus != null">
        #{synStatus,jdbcType=INTEGER},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementPurchaseOrderExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 15:31:26 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from online_settlement_purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 15:31:26 CST 2025. by MyBatis Generator, do not modify.
    -->
    update online_settlement_purchase_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.prikey != null">
        prikey = #{record.prikey,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceType != null">
        source_type = #{record.sourceType,jdbcType=VARCHAR},
      </if>
      <if test="record.scmOrderNum != null">
        scm_order_num = #{record.scmOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="record.poOrderNum != null">
        po_order_num = #{record.poOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="record.settleType != null">
        settle_type = #{record.settleType,jdbcType=VARCHAR},
      </if>
      <if test="record.contractNumber != null">
        contract_number = #{record.contractNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.contractName != null">
        contract_name = #{record.contractName,jdbcType=VARCHAR},
      </if>
      <if test="record.contractType != null">
        contract_type = #{record.contractType,jdbcType=VARCHAR},
      </if>
      <if test="record.beId != null">
        be_id = #{record.beId,jdbcType=VARCHAR},
      </if>
      <if test="record.location != null">
        location = #{record.location,jdbcType=VARCHAR},
      </if>
      <if test="record.settlePrice != null">
        settle_price = #{record.settlePrice,jdbcType=BIGINT},
      </if>
      <if test="record.settleStatus != null">
        settle_status = #{record.settleStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.taxInclusiveTotalSettlePrice != null">
        tax_inclusive_total_settle_price = #{record.taxInclusiveTotalSettlePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.synStatus != null">
        syn_status = #{record.synStatus,jdbcType=INTEGER},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 15:31:26 CST 2025. by MyBatis Generator, do not modify.
    -->
    update online_settlement_purchase_order
    set id = #{record.id,jdbcType=VARCHAR},
      prikey = #{record.prikey,jdbcType=VARCHAR},
      source_type = #{record.sourceType,jdbcType=VARCHAR},
      scm_order_num = #{record.scmOrderNum,jdbcType=VARCHAR},
      po_order_num = #{record.poOrderNum,jdbcType=VARCHAR},
      settle_type = #{record.settleType,jdbcType=VARCHAR},
      contract_number = #{record.contractNumber,jdbcType=VARCHAR},
      contract_name = #{record.contractName,jdbcType=VARCHAR},
      contract_type = #{record.contractType,jdbcType=VARCHAR},
      be_id = #{record.beId,jdbcType=VARCHAR},
      location = #{record.location,jdbcType=VARCHAR},
      settle_price = #{record.settlePrice,jdbcType=BIGINT},
      settle_status = #{record.settleStatus,jdbcType=VARCHAR},
      tax_inclusive_total_settle_price = #{record.taxInclusiveTotalSettlePrice,jdbcType=DECIMAL},
      syn_status = #{record.synStatus,jdbcType=INTEGER},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementPurchaseOrder">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 15:31:26 CST 2025. by MyBatis Generator, do not modify.
    -->
    update online_settlement_purchase_order
    <set>
      <if test="prikey != null">
        prikey = #{prikey,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="scmOrderNum != null">
        scm_order_num = #{scmOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="poOrderNum != null">
        po_order_num = #{poOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="settleType != null">
        settle_type = #{settleType,jdbcType=VARCHAR},
      </if>
      <if test="contractNumber != null">
        contract_number = #{contractNumber,jdbcType=VARCHAR},
      </if>
      <if test="contractName != null">
        contract_name = #{contractName,jdbcType=VARCHAR},
      </if>
      <if test="contractType != null">
        contract_type = #{contractType,jdbcType=VARCHAR},
      </if>
      <if test="beId != null">
        be_id = #{beId,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=VARCHAR},
      </if>
      <if test="settlePrice != null">
        settle_price = #{settlePrice,jdbcType=BIGINT},
      </if>
      <if test="settleStatus != null">
        settle_status = #{settleStatus,jdbcType=VARCHAR},
      </if>
      <if test="taxInclusiveTotalSettlePrice != null">
        tax_inclusive_total_settle_price = #{taxInclusiveTotalSettlePrice,jdbcType=DECIMAL},
      </if>
      <if test="synStatus != null">
        syn_status = #{synStatus,jdbcType=INTEGER},
      </if>
      <if test="provinceCode != null">
        province_code = #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.OnlineSettlementPurchaseOrder">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 15:31:26 CST 2025. by MyBatis Generator, do not modify.
    -->
    update online_settlement_purchase_order
    set prikey = #{prikey,jdbcType=VARCHAR},
      source_type = #{sourceType,jdbcType=VARCHAR},
      scm_order_num = #{scmOrderNum,jdbcType=VARCHAR},
      po_order_num = #{poOrderNum,jdbcType=VARCHAR},
      settle_type = #{settleType,jdbcType=VARCHAR},
      contract_number = #{contractNumber,jdbcType=VARCHAR},
      contract_name = #{contractName,jdbcType=VARCHAR},
      contract_type = #{contractType,jdbcType=VARCHAR},
      be_id = #{beId,jdbcType=VARCHAR},
      location = #{location,jdbcType=VARCHAR},
      settle_price = #{settlePrice,jdbcType=BIGINT},
      settle_status = #{settleStatus,jdbcType=VARCHAR},
      tax_inclusive_total_settle_price = #{taxInclusiveTotalSettlePrice,jdbcType=DECIMAL},
      syn_status = #{synStatus,jdbcType=INTEGER},
      province_code = #{provinceCode,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 15:31:26 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into online_settlement_purchase_order
    (id, prikey, source_type, scm_order_num, po_order_num, settle_type, contract_number, 
      contract_name, contract_type, be_id, location, settle_price, settle_status, tax_inclusive_total_settle_price, 
      syn_status, province_code, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.prikey,jdbcType=VARCHAR}, #{item.sourceType,jdbcType=VARCHAR}, 
        #{item.scmOrderNum,jdbcType=VARCHAR}, #{item.poOrderNum,jdbcType=VARCHAR}, #{item.settleType,jdbcType=VARCHAR}, 
        #{item.contractNumber,jdbcType=VARCHAR}, #{item.contractName,jdbcType=VARCHAR}, 
        #{item.contractType,jdbcType=VARCHAR}, #{item.beId,jdbcType=VARCHAR}, #{item.location,jdbcType=VARCHAR}, 
        #{item.settlePrice,jdbcType=BIGINT}, #{item.settleStatus,jdbcType=VARCHAR}, #{item.taxInclusiveTotalSettlePrice,jdbcType=DECIMAL}, 
        #{item.synStatus,jdbcType=INTEGER}, #{item.provinceCode,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Thu May 22 15:31:26 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into online_settlement_purchase_order (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'prikey'.toString() == column.value">
          #{item.prikey,jdbcType=VARCHAR}
        </if>
        <if test="'source_type'.toString() == column.value">
          #{item.sourceType,jdbcType=VARCHAR}
        </if>
        <if test="'scm_order_num'.toString() == column.value">
          #{item.scmOrderNum,jdbcType=VARCHAR}
        </if>
        <if test="'po_order_num'.toString() == column.value">
          #{item.poOrderNum,jdbcType=VARCHAR}
        </if>
        <if test="'settle_type'.toString() == column.value">
          #{item.settleType,jdbcType=VARCHAR}
        </if>
        <if test="'contract_number'.toString() == column.value">
          #{item.contractNumber,jdbcType=VARCHAR}
        </if>
        <if test="'contract_name'.toString() == column.value">
          #{item.contractName,jdbcType=VARCHAR}
        </if>
        <if test="'contract_type'.toString() == column.value">
          #{item.contractType,jdbcType=VARCHAR}
        </if>
        <if test="'be_id'.toString() == column.value">
          #{item.beId,jdbcType=VARCHAR}
        </if>
        <if test="'location'.toString() == column.value">
          #{item.location,jdbcType=VARCHAR}
        </if>
        <if test="'settle_price'.toString() == column.value">
          #{item.settlePrice,jdbcType=BIGINT}
        </if>
        <if test="'settle_status'.toString() == column.value">
          #{item.settleStatus,jdbcType=VARCHAR}
        </if>
        <if test="'tax_inclusive_total_settle_price'.toString() == column.value">
          #{item.taxInclusiveTotalSettlePrice,jdbcType=DECIMAL}
        </if>
        <if test="'syn_status'.toString() == column.value">
          #{item.synStatus,jdbcType=INTEGER}
        </if>
        <if test="'province_code'.toString() == column.value">
          #{item.provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>