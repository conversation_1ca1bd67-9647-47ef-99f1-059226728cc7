<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.iot.sc.dao.ProvinceContractInfoNewMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfoNew">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 11:12:31 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="contract_type" jdbcType="VARCHAR" property="contractType" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="internet_contract_code" jdbcType="VARCHAR" property="internetContractCode" />
    <result column="dept_code" jdbcType="VARCHAR" property="deptCode" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="created_id" jdbcType="VARCHAR" property="createdId" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="mtl_type_code" jdbcType="VARCHAR" property="mtlTypeCode" />
    <result column="small_item" jdbcType="VARCHAR" property="smallItem" />
    <result column="contract_code" jdbcType="VARCHAR" property="contractCode" />
    <result column="vendor_code" jdbcType="VARCHAR" property="vendorCode" />
    <result column="req_type" jdbcType="VARCHAR" property="reqType" />
    <result column="req_second_type" jdbcType="VARCHAR" property="reqSecondType" />
    <result column="rcv_user_num" jdbcType="VARCHAR" property="rcvUserNum" />
    <result column="rcv_user" jdbcType="VARCHAR" property="rcvUser" />
    <result column="rcv_contact_phone" jdbcType="VARCHAR" property="rcvContactPhone" />
    <result column="rcv_site_address" jdbcType="VARCHAR" property="rcvSiteAddress" />
    <result column="mis_body" jdbcType="VARCHAR" property="misBody" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="exp_type" jdbcType="VARCHAR" property="expType" />
    <result column="reimbursement_mode" jdbcType="VARCHAR" property="reimbursementMode" />
    <result column="arrival_time_model" jdbcType="VARCHAR" property="arrivalTimeModel" />
    <result column="arrival_days" jdbcType="INTEGER" property="arrivalDays" />
    <result column="organization_code" jdbcType="VARCHAR" property="organizationCode" />
    <result column="item_type" jdbcType="VARCHAR" property="itemType" />
    <result column="budget_id" jdbcType="VARCHAR" property="budgetId" />
    <result column="budget_year" jdbcType="VARCHAR" property="budgetYear" />
    <result column="activity_code" jdbcType="VARCHAR" property="activityCode" />
    <result column="cost_center" jdbcType="VARCHAR" property="costCenter" />
    <result column="expense_account" jdbcType="VARCHAR" property="expenseAccount" />
    <result column="cost_subject" jdbcType="VARCHAR" property="costSubject" />
    <result column="manage_activity" jdbcType="VARCHAR" property="manageActivity" />
    <result column="manage_market" jdbcType="VARCHAR" property="manageMarket" />
    <result column="manage_product" jdbcType="VARCHAR" property="manageProduct" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 11:12:31 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 11:12:31 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 11:12:31 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, contract_type, province_code, province_name, city_code, city_name, internet_contract_code, 
    dept_code, dept_name, created_id, created_name, mtl_type_code, small_item, contract_code, 
    vendor_code, req_type, req_second_type, rcv_user_num, rcv_user, rcv_contact_phone, 
    rcv_site_address, mis_body, org_id, exp_type, reimbursement_mode, arrival_time_model, 
    arrival_days, organization_code, item_type, budget_id, budget_year, activity_code, 
    cost_center, expense_account, cost_subject, manage_activity, manage_market, manage_product, 
    create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfoNewExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 11:12:31 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from province_contract_info_new
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 11:12:31 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from province_contract_info_new
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 11:12:31 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from province_contract_info_new
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfoNewExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 11:12:31 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from province_contract_info_new
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfoNew">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 11:12:31 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into province_contract_info_new (id, contract_type, province_code, 
      province_name, city_code, city_name, 
      internet_contract_code, dept_code, dept_name, 
      created_id, created_name, mtl_type_code, 
      small_item, contract_code, vendor_code, 
      req_type, req_second_type, rcv_user_num, 
      rcv_user, rcv_contact_phone, rcv_site_address, 
      mis_body, org_id, exp_type, 
      reimbursement_mode, arrival_time_model, arrival_days, 
      organization_code, item_type, budget_id, 
      budget_year, activity_code, cost_center, 
      expense_account, cost_subject, manage_activity, 
      manage_market, manage_product, create_time, 
      update_time)
    values (#{id,jdbcType=VARCHAR}, #{contractType,jdbcType=VARCHAR}, #{provinceCode,jdbcType=VARCHAR}, 
      #{provinceName,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR}, 
      #{internetContractCode,jdbcType=VARCHAR}, #{deptCode,jdbcType=VARCHAR}, #{deptName,jdbcType=VARCHAR}, 
      #{createdId,jdbcType=VARCHAR}, #{createdName,jdbcType=VARCHAR}, #{mtlTypeCode,jdbcType=VARCHAR}, 
      #{smallItem,jdbcType=VARCHAR}, #{contractCode,jdbcType=VARCHAR}, #{vendorCode,jdbcType=VARCHAR}, 
      #{reqType,jdbcType=VARCHAR}, #{reqSecondType,jdbcType=VARCHAR}, #{rcvUserNum,jdbcType=VARCHAR}, 
      #{rcvUser,jdbcType=VARCHAR}, #{rcvContactPhone,jdbcType=VARCHAR}, #{rcvSiteAddress,jdbcType=VARCHAR}, 
      #{misBody,jdbcType=VARCHAR}, #{orgId,jdbcType=VARCHAR}, #{expType,jdbcType=VARCHAR}, 
      #{reimbursementMode,jdbcType=VARCHAR}, #{arrivalTimeModel,jdbcType=VARCHAR}, #{arrivalDays,jdbcType=INTEGER}, 
      #{organizationCode,jdbcType=VARCHAR}, #{itemType,jdbcType=VARCHAR}, #{budgetId,jdbcType=VARCHAR}, 
      #{budgetYear,jdbcType=VARCHAR}, #{activityCode,jdbcType=VARCHAR}, #{costCenter,jdbcType=VARCHAR}, 
      #{expenseAccount,jdbcType=VARCHAR}, #{costSubject,jdbcType=VARCHAR}, #{manageActivity,jdbcType=VARCHAR}, 
      #{manageMarket,jdbcType=VARCHAR}, #{manageProduct,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfoNew">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 11:12:31 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into province_contract_info_new
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="contractType != null">
        contract_type,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="internetContractCode != null">
        internet_contract_code,
      </if>
      <if test="deptCode != null">
        dept_code,
      </if>
      <if test="deptName != null">
        dept_name,
      </if>
      <if test="createdId != null">
        created_id,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="mtlTypeCode != null">
        mtl_type_code,
      </if>
      <if test="smallItem != null">
        small_item,
      </if>
      <if test="contractCode != null">
        contract_code,
      </if>
      <if test="vendorCode != null">
        vendor_code,
      </if>
      <if test="reqType != null">
        req_type,
      </if>
      <if test="reqSecondType != null">
        req_second_type,
      </if>
      <if test="rcvUserNum != null">
        rcv_user_num,
      </if>
      <if test="rcvUser != null">
        rcv_user,
      </if>
      <if test="rcvContactPhone != null">
        rcv_contact_phone,
      </if>
      <if test="rcvSiteAddress != null">
        rcv_site_address,
      </if>
      <if test="misBody != null">
        mis_body,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="expType != null">
        exp_type,
      </if>
      <if test="reimbursementMode != null">
        reimbursement_mode,
      </if>
      <if test="arrivalTimeModel != null">
        arrival_time_model,
      </if>
      <if test="arrivalDays != null">
        arrival_days,
      </if>
      <if test="organizationCode != null">
        organization_code,
      </if>
      <if test="itemType != null">
        item_type,
      </if>
      <if test="budgetId != null">
        budget_id,
      </if>
      <if test="budgetYear != null">
        budget_year,
      </if>
      <if test="activityCode != null">
        activity_code,
      </if>
      <if test="costCenter != null">
        cost_center,
      </if>
      <if test="expenseAccount != null">
        expense_account,
      </if>
      <if test="costSubject != null">
        cost_subject,
      </if>
      <if test="manageActivity != null">
        manage_activity,
      </if>
      <if test="manageMarket != null">
        manage_market,
      </if>
      <if test="manageProduct != null">
        manage_product,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="contractType != null">
        #{contractType,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="internetContractCode != null">
        #{internetContractCode,jdbcType=VARCHAR},
      </if>
      <if test="deptCode != null">
        #{deptCode,jdbcType=VARCHAR},
      </if>
      <if test="deptName != null">
        #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="createdId != null">
        #{createdId,jdbcType=VARCHAR},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="mtlTypeCode != null">
        #{mtlTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="smallItem != null">
        #{smallItem,jdbcType=VARCHAR},
      </if>
      <if test="contractCode != null">
        #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="vendorCode != null">
        #{vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="reqType != null">
        #{reqType,jdbcType=VARCHAR},
      </if>
      <if test="reqSecondType != null">
        #{reqSecondType,jdbcType=VARCHAR},
      </if>
      <if test="rcvUserNum != null">
        #{rcvUserNum,jdbcType=VARCHAR},
      </if>
      <if test="rcvUser != null">
        #{rcvUser,jdbcType=VARCHAR},
      </if>
      <if test="rcvContactPhone != null">
        #{rcvContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="rcvSiteAddress != null">
        #{rcvSiteAddress,jdbcType=VARCHAR},
      </if>
      <if test="misBody != null">
        #{misBody,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="expType != null">
        #{expType,jdbcType=VARCHAR},
      </if>
      <if test="reimbursementMode != null">
        #{reimbursementMode,jdbcType=VARCHAR},
      </if>
      <if test="arrivalTimeModel != null">
        #{arrivalTimeModel,jdbcType=VARCHAR},
      </if>
      <if test="arrivalDays != null">
        #{arrivalDays,jdbcType=INTEGER},
      </if>
      <if test="organizationCode != null">
        #{organizationCode,jdbcType=VARCHAR},
      </if>
      <if test="itemType != null">
        #{itemType,jdbcType=VARCHAR},
      </if>
      <if test="budgetId != null">
        #{budgetId,jdbcType=VARCHAR},
      </if>
      <if test="budgetYear != null">
        #{budgetYear,jdbcType=VARCHAR},
      </if>
      <if test="activityCode != null">
        #{activityCode,jdbcType=VARCHAR},
      </if>
      <if test="costCenter != null">
        #{costCenter,jdbcType=VARCHAR},
      </if>
      <if test="expenseAccount != null">
        #{expenseAccount,jdbcType=VARCHAR},
      </if>
      <if test="costSubject != null">
        #{costSubject,jdbcType=VARCHAR},
      </if>
      <if test="manageActivity != null">
        #{manageActivity,jdbcType=VARCHAR},
      </if>
      <if test="manageMarket != null">
        #{manageMarket,jdbcType=VARCHAR},
      </if>
      <if test="manageProduct != null">
        #{manageProduct,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfoNewExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 11:12:31 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from province_contract_info_new
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 11:12:31 CST 2025. by MyBatis Generator, do not modify.
    -->
    update province_contract_info_new
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.contractType != null">
        contract_type = #{record.contractType,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceName != null">
        province_name = #{record.provinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityName != null">
        city_name = #{record.cityName,jdbcType=VARCHAR},
      </if>
      <if test="record.internetContractCode != null">
        internet_contract_code = #{record.internetContractCode,jdbcType=VARCHAR},
      </if>
      <if test="record.deptCode != null">
        dept_code = #{record.deptCode,jdbcType=VARCHAR},
      </if>
      <if test="record.deptName != null">
        dept_name = #{record.deptName,jdbcType=VARCHAR},
      </if>
      <if test="record.createdId != null">
        created_id = #{record.createdId,jdbcType=VARCHAR},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.mtlTypeCode != null">
        mtl_type_code = #{record.mtlTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.smallItem != null">
        small_item = #{record.smallItem,jdbcType=VARCHAR},
      </if>
      <if test="record.contractCode != null">
        contract_code = #{record.contractCode,jdbcType=VARCHAR},
      </if>
      <if test="record.vendorCode != null">
        vendor_code = #{record.vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="record.reqType != null">
        req_type = #{record.reqType,jdbcType=VARCHAR},
      </if>
      <if test="record.reqSecondType != null">
        req_second_type = #{record.reqSecondType,jdbcType=VARCHAR},
      </if>
      <if test="record.rcvUserNum != null">
        rcv_user_num = #{record.rcvUserNum,jdbcType=VARCHAR},
      </if>
      <if test="record.rcvUser != null">
        rcv_user = #{record.rcvUser,jdbcType=VARCHAR},
      </if>
      <if test="record.rcvContactPhone != null">
        rcv_contact_phone = #{record.rcvContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.rcvSiteAddress != null">
        rcv_site_address = #{record.rcvSiteAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.misBody != null">
        mis_body = #{record.misBody,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=VARCHAR},
      </if>
      <if test="record.expType != null">
        exp_type = #{record.expType,jdbcType=VARCHAR},
      </if>
      <if test="record.reimbursementMode != null">
        reimbursement_mode = #{record.reimbursementMode,jdbcType=VARCHAR},
      </if>
      <if test="record.arrivalTimeModel != null">
        arrival_time_model = #{record.arrivalTimeModel,jdbcType=VARCHAR},
      </if>
      <if test="record.arrivalDays != null">
        arrival_days = #{record.arrivalDays,jdbcType=INTEGER},
      </if>
      <if test="record.organizationCode != null">
        organization_code = #{record.organizationCode,jdbcType=VARCHAR},
      </if>
      <if test="record.itemType != null">
        item_type = #{record.itemType,jdbcType=VARCHAR},
      </if>
      <if test="record.budgetId != null">
        budget_id = #{record.budgetId,jdbcType=VARCHAR},
      </if>
      <if test="record.budgetYear != null">
        budget_year = #{record.budgetYear,jdbcType=VARCHAR},
      </if>
      <if test="record.activityCode != null">
        activity_code = #{record.activityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.costCenter != null">
        cost_center = #{record.costCenter,jdbcType=VARCHAR},
      </if>
      <if test="record.expenseAccount != null">
        expense_account = #{record.expenseAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.costSubject != null">
        cost_subject = #{record.costSubject,jdbcType=VARCHAR},
      </if>
      <if test="record.manageActivity != null">
        manage_activity = #{record.manageActivity,jdbcType=VARCHAR},
      </if>
      <if test="record.manageMarket != null">
        manage_market = #{record.manageMarket,jdbcType=VARCHAR},
      </if>
      <if test="record.manageProduct != null">
        manage_product = #{record.manageProduct,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 11:12:31 CST 2025. by MyBatis Generator, do not modify.
    -->
    update province_contract_info_new
    set id = #{record.id,jdbcType=VARCHAR},
      contract_type = #{record.contractType,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      province_name = #{record.provinceName,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      city_name = #{record.cityName,jdbcType=VARCHAR},
      internet_contract_code = #{record.internetContractCode,jdbcType=VARCHAR},
      dept_code = #{record.deptCode,jdbcType=VARCHAR},
      dept_name = #{record.deptName,jdbcType=VARCHAR},
      created_id = #{record.createdId,jdbcType=VARCHAR},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      mtl_type_code = #{record.mtlTypeCode,jdbcType=VARCHAR},
      small_item = #{record.smallItem,jdbcType=VARCHAR},
      contract_code = #{record.contractCode,jdbcType=VARCHAR},
      vendor_code = #{record.vendorCode,jdbcType=VARCHAR},
      req_type = #{record.reqType,jdbcType=VARCHAR},
      req_second_type = #{record.reqSecondType,jdbcType=VARCHAR},
      rcv_user_num = #{record.rcvUserNum,jdbcType=VARCHAR},
      rcv_user = #{record.rcvUser,jdbcType=VARCHAR},
      rcv_contact_phone = #{record.rcvContactPhone,jdbcType=VARCHAR},
      rcv_site_address = #{record.rcvSiteAddress,jdbcType=VARCHAR},
      mis_body = #{record.misBody,jdbcType=VARCHAR},
      org_id = #{record.orgId,jdbcType=VARCHAR},
      exp_type = #{record.expType,jdbcType=VARCHAR},
      reimbursement_mode = #{record.reimbursementMode,jdbcType=VARCHAR},
      arrival_time_model = #{record.arrivalTimeModel,jdbcType=VARCHAR},
      arrival_days = #{record.arrivalDays,jdbcType=INTEGER},
      organization_code = #{record.organizationCode,jdbcType=VARCHAR},
      item_type = #{record.itemType,jdbcType=VARCHAR},
      budget_id = #{record.budgetId,jdbcType=VARCHAR},
      budget_year = #{record.budgetYear,jdbcType=VARCHAR},
      activity_code = #{record.activityCode,jdbcType=VARCHAR},
      cost_center = #{record.costCenter,jdbcType=VARCHAR},
      expense_account = #{record.expenseAccount,jdbcType=VARCHAR},
      cost_subject = #{record.costSubject,jdbcType=VARCHAR},
      manage_activity = #{record.manageActivity,jdbcType=VARCHAR},
      manage_market = #{record.manageMarket,jdbcType=VARCHAR},
      manage_product = #{record.manageProduct,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfoNew">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 11:12:31 CST 2025. by MyBatis Generator, do not modify.
    -->
    update province_contract_info_new
    <set>
      <if test="contractType != null">
        contract_type = #{contractType,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        province_code = #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="internetContractCode != null">
        internet_contract_code = #{internetContractCode,jdbcType=VARCHAR},
      </if>
      <if test="deptCode != null">
        dept_code = #{deptCode,jdbcType=VARCHAR},
      </if>
      <if test="deptName != null">
        dept_name = #{deptName,jdbcType=VARCHAR},
      </if>
      <if test="createdId != null">
        created_id = #{createdId,jdbcType=VARCHAR},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="mtlTypeCode != null">
        mtl_type_code = #{mtlTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="smallItem != null">
        small_item = #{smallItem,jdbcType=VARCHAR},
      </if>
      <if test="contractCode != null">
        contract_code = #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="vendorCode != null">
        vendor_code = #{vendorCode,jdbcType=VARCHAR},
      </if>
      <if test="reqType != null">
        req_type = #{reqType,jdbcType=VARCHAR},
      </if>
      <if test="reqSecondType != null">
        req_second_type = #{reqSecondType,jdbcType=VARCHAR},
      </if>
      <if test="rcvUserNum != null">
        rcv_user_num = #{rcvUserNum,jdbcType=VARCHAR},
      </if>
      <if test="rcvUser != null">
        rcv_user = #{rcvUser,jdbcType=VARCHAR},
      </if>
      <if test="rcvContactPhone != null">
        rcv_contact_phone = #{rcvContactPhone,jdbcType=VARCHAR},
      </if>
      <if test="rcvSiteAddress != null">
        rcv_site_address = #{rcvSiteAddress,jdbcType=VARCHAR},
      </if>
      <if test="misBody != null">
        mis_body = #{misBody,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="expType != null">
        exp_type = #{expType,jdbcType=VARCHAR},
      </if>
      <if test="reimbursementMode != null">
        reimbursement_mode = #{reimbursementMode,jdbcType=VARCHAR},
      </if>
      <if test="arrivalTimeModel != null">
        arrival_time_model = #{arrivalTimeModel,jdbcType=VARCHAR},
      </if>
      <if test="arrivalDays != null">
        arrival_days = #{arrivalDays,jdbcType=INTEGER},
      </if>
      <if test="organizationCode != null">
        organization_code = #{organizationCode,jdbcType=VARCHAR},
      </if>
      <if test="itemType != null">
        item_type = #{itemType,jdbcType=VARCHAR},
      </if>
      <if test="budgetId != null">
        budget_id = #{budgetId,jdbcType=VARCHAR},
      </if>
      <if test="budgetYear != null">
        budget_year = #{budgetYear,jdbcType=VARCHAR},
      </if>
      <if test="activityCode != null">
        activity_code = #{activityCode,jdbcType=VARCHAR},
      </if>
      <if test="costCenter != null">
        cost_center = #{costCenter,jdbcType=VARCHAR},
      </if>
      <if test="expenseAccount != null">
        expense_account = #{expenseAccount,jdbcType=VARCHAR},
      </if>
      <if test="costSubject != null">
        cost_subject = #{costSubject,jdbcType=VARCHAR},
      </if>
      <if test="manageActivity != null">
        manage_activity = #{manageActivity,jdbcType=VARCHAR},
      </if>
      <if test="manageMarket != null">
        manage_market = #{manageMarket,jdbcType=VARCHAR},
      </if>
      <if test="manageProduct != null">
        manage_product = #{manageProduct,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.iot.sc.pojo.entity.ProvinceContractInfoNew">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 11:12:31 CST 2025. by MyBatis Generator, do not modify.
    -->
    update province_contract_info_new
    set contract_type = #{contractType,jdbcType=VARCHAR},
      province_code = #{provinceCode,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      internet_contract_code = #{internetContractCode,jdbcType=VARCHAR},
      dept_code = #{deptCode,jdbcType=VARCHAR},
      dept_name = #{deptName,jdbcType=VARCHAR},
      created_id = #{createdId,jdbcType=VARCHAR},
      created_name = #{createdName,jdbcType=VARCHAR},
      mtl_type_code = #{mtlTypeCode,jdbcType=VARCHAR},
      small_item = #{smallItem,jdbcType=VARCHAR},
      contract_code = #{contractCode,jdbcType=VARCHAR},
      vendor_code = #{vendorCode,jdbcType=VARCHAR},
      req_type = #{reqType,jdbcType=VARCHAR},
      req_second_type = #{reqSecondType,jdbcType=VARCHAR},
      rcv_user_num = #{rcvUserNum,jdbcType=VARCHAR},
      rcv_user = #{rcvUser,jdbcType=VARCHAR},
      rcv_contact_phone = #{rcvContactPhone,jdbcType=VARCHAR},
      rcv_site_address = #{rcvSiteAddress,jdbcType=VARCHAR},
      mis_body = #{misBody,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=VARCHAR},
      exp_type = #{expType,jdbcType=VARCHAR},
      reimbursement_mode = #{reimbursementMode,jdbcType=VARCHAR},
      arrival_time_model = #{arrivalTimeModel,jdbcType=VARCHAR},
      arrival_days = #{arrivalDays,jdbcType=INTEGER},
      organization_code = #{organizationCode,jdbcType=VARCHAR},
      item_type = #{itemType,jdbcType=VARCHAR},
      budget_id = #{budgetId,jdbcType=VARCHAR},
      budget_year = #{budgetYear,jdbcType=VARCHAR},
      activity_code = #{activityCode,jdbcType=VARCHAR},
      cost_center = #{costCenter,jdbcType=VARCHAR},
      expense_account = #{expenseAccount,jdbcType=VARCHAR},
      cost_subject = #{costSubject,jdbcType=VARCHAR},
      manage_activity = #{manageActivity,jdbcType=VARCHAR},
      manage_market = #{manageMarket,jdbcType=VARCHAR},
      manage_product = #{manageProduct,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 11:12:31 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into province_contract_info_new
    (id, contract_type, province_code, province_name, city_code, city_name, internet_contract_code, 
      dept_code, dept_name, created_id, created_name, mtl_type_code, small_item, contract_code, 
      vendor_code, req_type, req_second_type, rcv_user_num, rcv_user, rcv_contact_phone, 
      rcv_site_address, mis_body, org_id, exp_type, reimbursement_mode, arrival_time_model, 
      arrival_days, organization_code, item_type, budget_id, budget_year, activity_code, 
      cost_center, expense_account, cost_subject, manage_activity, manage_market, manage_product, 
      create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.contractType,jdbcType=VARCHAR}, #{item.provinceCode,jdbcType=VARCHAR}, 
        #{item.provinceName,jdbcType=VARCHAR}, #{item.cityCode,jdbcType=VARCHAR}, #{item.cityName,jdbcType=VARCHAR}, 
        #{item.internetContractCode,jdbcType=VARCHAR}, #{item.deptCode,jdbcType=VARCHAR}, 
        #{item.deptName,jdbcType=VARCHAR}, #{item.createdId,jdbcType=VARCHAR}, #{item.createdName,jdbcType=VARCHAR}, 
        #{item.mtlTypeCode,jdbcType=VARCHAR}, #{item.smallItem,jdbcType=VARCHAR}, #{item.contractCode,jdbcType=VARCHAR}, 
        #{item.vendorCode,jdbcType=VARCHAR}, #{item.reqType,jdbcType=VARCHAR}, #{item.reqSecondType,jdbcType=VARCHAR}, 
        #{item.rcvUserNum,jdbcType=VARCHAR}, #{item.rcvUser,jdbcType=VARCHAR}, #{item.rcvContactPhone,jdbcType=VARCHAR}, 
        #{item.rcvSiteAddress,jdbcType=VARCHAR}, #{item.misBody,jdbcType=VARCHAR}, #{item.orgId,jdbcType=VARCHAR}, 
        #{item.expType,jdbcType=VARCHAR}, #{item.reimbursementMode,jdbcType=VARCHAR}, #{item.arrivalTimeModel,jdbcType=VARCHAR}, 
        #{item.arrivalDays,jdbcType=INTEGER}, #{item.organizationCode,jdbcType=VARCHAR}, 
        #{item.itemType,jdbcType=VARCHAR}, #{item.budgetId,jdbcType=VARCHAR}, #{item.budgetYear,jdbcType=VARCHAR}, 
        #{item.activityCode,jdbcType=VARCHAR}, #{item.costCenter,jdbcType=VARCHAR}, #{item.expenseAccount,jdbcType=VARCHAR}, 
        #{item.costSubject,jdbcType=VARCHAR}, #{item.manageActivity,jdbcType=VARCHAR}, 
        #{item.manageMarket,jdbcType=VARCHAR}, #{item.manageProduct,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Mar 17 11:12:31 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into province_contract_info_new (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'contract_type'.toString() == column.value">
          #{item.contractType,jdbcType=VARCHAR}
        </if>
        <if test="'province_code'.toString() == column.value">
          #{item.provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="'province_name'.toString() == column.value">
          #{item.provinceName,jdbcType=VARCHAR}
        </if>
        <if test="'city_code'.toString() == column.value">
          #{item.cityCode,jdbcType=VARCHAR}
        </if>
        <if test="'city_name'.toString() == column.value">
          #{item.cityName,jdbcType=VARCHAR}
        </if>
        <if test="'internet_contract_code'.toString() == column.value">
          #{item.internetContractCode,jdbcType=VARCHAR}
        </if>
        <if test="'dept_code'.toString() == column.value">
          #{item.deptCode,jdbcType=VARCHAR}
        </if>
        <if test="'dept_name'.toString() == column.value">
          #{item.deptName,jdbcType=VARCHAR}
        </if>
        <if test="'created_id'.toString() == column.value">
          #{item.createdId,jdbcType=VARCHAR}
        </if>
        <if test="'created_name'.toString() == column.value">
          #{item.createdName,jdbcType=VARCHAR}
        </if>
        <if test="'mtl_type_code'.toString() == column.value">
          #{item.mtlTypeCode,jdbcType=VARCHAR}
        </if>
        <if test="'small_item'.toString() == column.value">
          #{item.smallItem,jdbcType=VARCHAR}
        </if>
        <if test="'contract_code'.toString() == column.value">
          #{item.contractCode,jdbcType=VARCHAR}
        </if>
        <if test="'vendor_code'.toString() == column.value">
          #{item.vendorCode,jdbcType=VARCHAR}
        </if>
        <if test="'req_type'.toString() == column.value">
          #{item.reqType,jdbcType=VARCHAR}
        </if>
        <if test="'req_second_type'.toString() == column.value">
          #{item.reqSecondType,jdbcType=VARCHAR}
        </if>
        <if test="'rcv_user_num'.toString() == column.value">
          #{item.rcvUserNum,jdbcType=VARCHAR}
        </if>
        <if test="'rcv_user'.toString() == column.value">
          #{item.rcvUser,jdbcType=VARCHAR}
        </if>
        <if test="'rcv_contact_phone'.toString() == column.value">
          #{item.rcvContactPhone,jdbcType=VARCHAR}
        </if>
        <if test="'rcv_site_address'.toString() == column.value">
          #{item.rcvSiteAddress,jdbcType=VARCHAR}
        </if>
        <if test="'mis_body'.toString() == column.value">
          #{item.misBody,jdbcType=VARCHAR}
        </if>
        <if test="'org_id'.toString() == column.value">
          #{item.orgId,jdbcType=VARCHAR}
        </if>
        <if test="'exp_type'.toString() == column.value">
          #{item.expType,jdbcType=VARCHAR}
        </if>
        <if test="'reimbursement_mode'.toString() == column.value">
          #{item.reimbursementMode,jdbcType=VARCHAR}
        </if>
        <if test="'arrival_time_model'.toString() == column.value">
          #{item.arrivalTimeModel,jdbcType=VARCHAR}
        </if>
        <if test="'arrival_days'.toString() == column.value">
          #{item.arrivalDays,jdbcType=INTEGER}
        </if>
        <if test="'organization_code'.toString() == column.value">
          #{item.organizationCode,jdbcType=VARCHAR}
        </if>
        <if test="'item_type'.toString() == column.value">
          #{item.itemType,jdbcType=VARCHAR}
        </if>
        <if test="'budget_id'.toString() == column.value">
          #{item.budgetId,jdbcType=VARCHAR}
        </if>
        <if test="'budget_year'.toString() == column.value">
          #{item.budgetYear,jdbcType=VARCHAR}
        </if>
        <if test="'activity_code'.toString() == column.value">
          #{item.activityCode,jdbcType=VARCHAR}
        </if>
        <if test="'cost_center'.toString() == column.value">
          #{item.costCenter,jdbcType=VARCHAR}
        </if>
        <if test="'expense_account'.toString() == column.value">
          #{item.expenseAccount,jdbcType=VARCHAR}
        </if>
        <if test="'cost_subject'.toString() == column.value">
          #{item.costSubject,jdbcType=VARCHAR}
        </if>
        <if test="'manage_activity'.toString() == column.value">
          #{item.manageActivity,jdbcType=VARCHAR}
        </if>
        <if test="'manage_market'.toString() == column.value">
          #{item.manageMarket,jdbcType=VARCHAR}
        </if>
        <if test="'manage_product'.toString() == column.value">
          #{item.manageProduct,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>