<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.MiniProgramSceneRequirementsMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirements">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 16:23:16 GMT+08:00 2024. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="scene_id" jdbcType="VARCHAR" property="sceneId" />
    <result column="first_directory_id" jdbcType="VARCHAR" property="firstDirectoryId" />
    <result column="second_directory_id" jdbcType="VARCHAR" property="secondDirectoryId" />
    <result column="attachment_file_url" jdbcType="VARCHAR" property="attachmentFileUrl" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="contact" jdbcType="VARCHAR" property="contact" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="create_uid" jdbcType="VARCHAR" property="createUid" />
    <result column="partner_business_id" jdbcType="VARCHAR" property="partnerBusinessId" />
    <result column="audit_state" jdbcType="INTEGER" property="auditState" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 16:23:16 GMT+08:00 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 16:23:16 GMT+08:00 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 16:23:16 GMT+08:00 2024. by MyBatis Generator, do not modify.
    -->
    id, scene_id, first_directory_id, second_directory_id, attachment_file_url, province_name, 
    province_code, city_name, city_code, contact, phone, create_uid, partner_business_id, 
    audit_state, deleted, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirementsExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 16:23:16 GMT+08:00 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from `mini_program_scene_requirements`
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 16:23:16 GMT+08:00 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from `mini_program_scene_requirements`
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 16:23:16 GMT+08:00 2024. by MyBatis Generator, do not modify.
    -->
    delete from `mini_program_scene_requirements`
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirementsExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 16:23:16 GMT+08:00 2024. by MyBatis Generator, do not modify.
    -->
    delete from `mini_program_scene_requirements`
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirements">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 16:23:16 GMT+08:00 2024. by MyBatis Generator, do not modify.
    -->
    insert into `mini_program_scene_requirements` (id, scene_id, first_directory_id, 
      second_directory_id, attachment_file_url, province_name, 
      province_code, city_name, city_code, 
      contact, phone, create_uid, 
      partner_business_id, audit_state, deleted, 
      create_time, update_time)
    values (#{id,jdbcType=VARCHAR}, #{sceneId,jdbcType=VARCHAR}, #{firstDirectoryId,jdbcType=VARCHAR}, 
      #{secondDirectoryId,jdbcType=VARCHAR}, #{attachmentFileUrl,jdbcType=VARCHAR}, #{provinceName,jdbcType=VARCHAR}, 
      #{provinceCode,jdbcType=VARCHAR}, #{cityName,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, 
      #{contact,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{createUid,jdbcType=VARCHAR}, 
      #{partnerBusinessId,jdbcType=VARCHAR}, #{auditState,jdbcType=INTEGER}, #{deleted,jdbcType=BIT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirements">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 16:23:16 GMT+08:00 2024. by MyBatis Generator, do not modify.
    -->
    insert into `mini_program_scene_requirements`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sceneId != null">
        scene_id,
      </if>
      <if test="firstDirectoryId != null">
        first_directory_id,
      </if>
      <if test="secondDirectoryId != null">
        second_directory_id,
      </if>
      <if test="attachmentFileUrl != null">
        attachment_file_url,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="contact != null">
        contact,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="createUid != null">
        create_uid,
      </if>
      <if test="partnerBusinessId != null">
        partner_business_id,
      </if>
      <if test="auditState != null">
        audit_state,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="sceneId != null">
        #{sceneId,jdbcType=VARCHAR},
      </if>
      <if test="firstDirectoryId != null">
        #{firstDirectoryId,jdbcType=VARCHAR},
      </if>
      <if test="secondDirectoryId != null">
        #{secondDirectoryId,jdbcType=VARCHAR},
      </if>
      <if test="attachmentFileUrl != null">
        #{attachmentFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="contact != null">
        #{contact,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="createUid != null">
        #{createUid,jdbcType=VARCHAR},
      </if>
      <if test="partnerBusinessId != null">
        #{partnerBusinessId,jdbcType=VARCHAR},
      </if>
      <if test="auditState != null">
        #{auditState,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirementsExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 16:23:16 GMT+08:00 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from `mini_program_scene_requirements`
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 16:23:16 GMT+08:00 2024. by MyBatis Generator, do not modify.
    -->
    update `mini_program_scene_requirements`
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.sceneId != null">
        scene_id = #{record.sceneId,jdbcType=VARCHAR},
      </if>
      <if test="record.firstDirectoryId != null">
        first_directory_id = #{record.firstDirectoryId,jdbcType=VARCHAR},
      </if>
      <if test="record.secondDirectoryId != null">
        second_directory_id = #{record.secondDirectoryId,jdbcType=VARCHAR},
      </if>
      <if test="record.attachmentFileUrl != null">
        attachment_file_url = #{record.attachmentFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceName != null">
        province_name = #{record.provinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityName != null">
        city_name = #{record.cityName,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.contact != null">
        contact = #{record.contact,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.createUid != null">
        create_uid = #{record.createUid,jdbcType=VARCHAR},
      </if>
      <if test="record.partnerBusinessId != null">
        partner_business_id = #{record.partnerBusinessId,jdbcType=VARCHAR},
      </if>
      <if test="record.auditState != null">
        audit_state = #{record.auditState,jdbcType=INTEGER},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 16:23:16 GMT+08:00 2024. by MyBatis Generator, do not modify.
    -->
    update `mini_program_scene_requirements`
    set id = #{record.id,jdbcType=VARCHAR},
      scene_id = #{record.sceneId,jdbcType=VARCHAR},
      first_directory_id = #{record.firstDirectoryId,jdbcType=VARCHAR},
      second_directory_id = #{record.secondDirectoryId,jdbcType=VARCHAR},
      attachment_file_url = #{record.attachmentFileUrl,jdbcType=VARCHAR},
      province_name = #{record.provinceName,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      city_name = #{record.cityName,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      contact = #{record.contact,jdbcType=VARCHAR},
      phone = #{record.phone,jdbcType=VARCHAR},
      create_uid = #{record.createUid,jdbcType=VARCHAR},
      partner_business_id = #{record.partnerBusinessId,jdbcType=VARCHAR},
      audit_state = #{record.auditState,jdbcType=INTEGER},
      deleted = #{record.deleted,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirements">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 16:23:16 GMT+08:00 2024. by MyBatis Generator, do not modify.
    -->
    update `mini_program_scene_requirements`
    <set>
      <if test="sceneId != null">
        scene_id = #{sceneId,jdbcType=VARCHAR},
      </if>
      <if test="firstDirectoryId != null">
        first_directory_id = #{firstDirectoryId,jdbcType=VARCHAR},
      </if>
      <if test="secondDirectoryId != null">
        second_directory_id = #{secondDirectoryId,jdbcType=VARCHAR},
      </if>
      <if test="attachmentFileUrl != null">
        attachment_file_url = #{attachmentFileUrl,jdbcType=VARCHAR},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        province_code = #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="contact != null">
        contact = #{contact,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="createUid != null">
        create_uid = #{createUid,jdbcType=VARCHAR},
      </if>
      <if test="partnerBusinessId != null">
        partner_business_id = #{partnerBusinessId,jdbcType=VARCHAR},
      </if>
      <if test="auditState != null">
        audit_state = #{auditState,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.MiniProgramSceneRequirements">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 16:23:16 GMT+08:00 2024. by MyBatis Generator, do not modify.
    -->
    update `mini_program_scene_requirements`
    set scene_id = #{sceneId,jdbcType=VARCHAR},
      first_directory_id = #{firstDirectoryId,jdbcType=VARCHAR},
      second_directory_id = #{secondDirectoryId,jdbcType=VARCHAR},
      attachment_file_url = #{attachmentFileUrl,jdbcType=VARCHAR},
      province_name = #{provinceName,jdbcType=VARCHAR},
      province_code = #{provinceCode,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      contact = #{contact,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      create_uid = #{createUid,jdbcType=VARCHAR},
      partner_business_id = #{partnerBusinessId,jdbcType=VARCHAR},
      audit_state = #{auditState,jdbcType=INTEGER},
      deleted = #{deleted,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 16:23:16 GMT+08:00 2024. by MyBatis Generator, do not modify.
    -->
    insert into `mini_program_scene_requirements`
    (id, scene_id, first_directory_id, second_directory_id, attachment_file_url, province_name, 
      province_code, city_name, city_code, contact, phone, create_uid, partner_business_id, 
      audit_state, deleted, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.sceneId,jdbcType=VARCHAR}, #{item.firstDirectoryId,jdbcType=VARCHAR}, 
        #{item.secondDirectoryId,jdbcType=VARCHAR}, #{item.attachmentFileUrl,jdbcType=VARCHAR}, 
        #{item.provinceName,jdbcType=VARCHAR}, #{item.provinceCode,jdbcType=VARCHAR}, #{item.cityName,jdbcType=VARCHAR}, 
        #{item.cityCode,jdbcType=VARCHAR}, #{item.contact,jdbcType=VARCHAR}, #{item.phone,jdbcType=VARCHAR}, 
        #{item.createUid,jdbcType=VARCHAR}, #{item.partnerBusinessId,jdbcType=VARCHAR}, 
        #{item.auditState,jdbcType=INTEGER}, #{item.deleted,jdbcType=BIT}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Mon Dec 16 16:23:16 GMT+08:00 2024. by MyBatis Generator, do not modify.
    -->
    insert into `mini_program_scene_requirements` (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'scene_id'.toString() == column.value">
          #{item.sceneId,jdbcType=VARCHAR}
        </if>
        <if test="'first_directory_id'.toString() == column.value">
          #{item.firstDirectoryId,jdbcType=VARCHAR}
        </if>
        <if test="'second_directory_id'.toString() == column.value">
          #{item.secondDirectoryId,jdbcType=VARCHAR}
        </if>
        <if test="'attachment_file_url'.toString() == column.value">
          #{item.attachmentFileUrl,jdbcType=VARCHAR}
        </if>
        <if test="'province_name'.toString() == column.value">
          #{item.provinceName,jdbcType=VARCHAR}
        </if>
        <if test="'province_code'.toString() == column.value">
          #{item.provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="'city_name'.toString() == column.value">
          #{item.cityName,jdbcType=VARCHAR}
        </if>
        <if test="'city_code'.toString() == column.value">
          #{item.cityCode,jdbcType=VARCHAR}
        </if>
        <if test="'contact'.toString() == column.value">
          #{item.contact,jdbcType=VARCHAR}
        </if>
        <if test="'phone'.toString() == column.value">
          #{item.phone,jdbcType=VARCHAR}
        </if>
        <if test="'create_uid'.toString() == column.value">
          #{item.createUid,jdbcType=VARCHAR}
        </if>
        <if test="'partner_business_id'.toString() == column.value">
          #{item.partnerBusinessId,jdbcType=VARCHAR}
        </if>
        <if test="'audit_state'.toString() == column.value">
          #{item.auditState,jdbcType=INTEGER}
        </if>
        <if test="'deleted'.toString() == column.value">
          #{item.deleted,jdbcType=BIT}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>