<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ContractProvinceInfoMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.ContractProvinceInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 07 11:15:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    <id column="mall_code" jdbcType="VARCHAR" property="mallCode" />
    <result column="k3_code" jdbcType="VARCHAR" property="k3Code" />
    <result column="mall_name" jdbcType="VARCHAR" property="mallName" />
    <result column="capital_mall_code" jdbcType="VARCHAR" property="capitalMallCode" />
    <result column="capital_mall_name" jdbcType="VARCHAR" property="capitalMallName" />
    <result column="capital_k3_code" jdbcType="VARCHAR" property="capitalK3Code" />
    <result column="scm_code" jdbcType="VARCHAR" property="scmCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 07 11:15:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 07 11:15:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 07 11:15:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    mall_code, k3_code, mall_name, capital_mall_code, capital_mall_name, capital_k3_code, 
    scm_code
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.ContractProvinceInfoExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 07 11:15:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from contract_province_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 07 11:15:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from contract_province_info
    where mall_code = #{mallCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 07 11:15:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from contract_province_info
    where mall_code = #{mallCode,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.ContractProvinceInfoExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 07 11:15:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    delete from contract_province_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.ContractProvinceInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 07 11:15:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into contract_province_info (mall_code, k3_code, mall_name, 
      capital_mall_code, capital_mall_name, capital_k3_code, 
      scm_code)
    values (#{mallCode,jdbcType=VARCHAR}, #{k3Code,jdbcType=VARCHAR}, #{mallName,jdbcType=VARCHAR}, 
      #{capitalMallCode,jdbcType=VARCHAR}, #{capitalMallName,jdbcType=VARCHAR}, #{capitalK3Code,jdbcType=VARCHAR}, 
      #{scmCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.ContractProvinceInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 07 11:15:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into contract_province_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mallCode != null">
        mall_code,
      </if>
      <if test="k3Code != null">
        k3_code,
      </if>
      <if test="mallName != null">
        mall_name,
      </if>
      <if test="capitalMallCode != null">
        capital_mall_code,
      </if>
      <if test="capitalMallName != null">
        capital_mall_name,
      </if>
      <if test="capitalK3Code != null">
        capital_k3_code,
      </if>
      <if test="scmCode != null">
        scm_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mallCode != null">
        #{mallCode,jdbcType=VARCHAR},
      </if>
      <if test="k3Code != null">
        #{k3Code,jdbcType=VARCHAR},
      </if>
      <if test="mallName != null">
        #{mallName,jdbcType=VARCHAR},
      </if>
      <if test="capitalMallCode != null">
        #{capitalMallCode,jdbcType=VARCHAR},
      </if>
      <if test="capitalMallName != null">
        #{capitalMallName,jdbcType=VARCHAR},
      </if>
      <if test="capitalK3Code != null">
        #{capitalK3Code,jdbcType=VARCHAR},
      </if>
      <if test="scmCode != null">
        #{scmCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.ContractProvinceInfoExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 07 11:15:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    select count(*) from contract_province_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 07 11:15:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    update contract_province_info
    <set>
      <if test="record.mallCode != null">
        mall_code = #{record.mallCode,jdbcType=VARCHAR},
      </if>
      <if test="record.k3Code != null">
        k3_code = #{record.k3Code,jdbcType=VARCHAR},
      </if>
      <if test="record.mallName != null">
        mall_name = #{record.mallName,jdbcType=VARCHAR},
      </if>
      <if test="record.capitalMallCode != null">
        capital_mall_code = #{record.capitalMallCode,jdbcType=VARCHAR},
      </if>
      <if test="record.capitalMallName != null">
        capital_mall_name = #{record.capitalMallName,jdbcType=VARCHAR},
      </if>
      <if test="record.capitalK3Code != null">
        capital_k3_code = #{record.capitalK3Code,jdbcType=VARCHAR},
      </if>
      <if test="record.scmCode != null">
        scm_code = #{record.scmCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 07 11:15:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    update contract_province_info
    set mall_code = #{record.mallCode,jdbcType=VARCHAR},
      k3_code = #{record.k3Code,jdbcType=VARCHAR},
      mall_name = #{record.mallName,jdbcType=VARCHAR},
      capital_mall_code = #{record.capitalMallCode,jdbcType=VARCHAR},
      capital_mall_name = #{record.capitalMallName,jdbcType=VARCHAR},
      capital_k3_code = #{record.capitalK3Code,jdbcType=VARCHAR},
      scm_code = #{record.scmCode,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.ContractProvinceInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 07 11:15:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    update contract_province_info
    <set>
      <if test="k3Code != null">
        k3_code = #{k3Code,jdbcType=VARCHAR},
      </if>
      <if test="mallName != null">
        mall_name = #{mallName,jdbcType=VARCHAR},
      </if>
      <if test="capitalMallCode != null">
        capital_mall_code = #{capitalMallCode,jdbcType=VARCHAR},
      </if>
      <if test="capitalMallName != null">
        capital_mall_name = #{capitalMallName,jdbcType=VARCHAR},
      </if>
      <if test="capitalK3Code != null">
        capital_k3_code = #{capitalK3Code,jdbcType=VARCHAR},
      </if>
      <if test="scmCode != null">
        scm_code = #{scmCode,jdbcType=VARCHAR},
      </if>
    </set>
    where mall_code = #{mallCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.ContractProvinceInfo">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 07 11:15:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    update contract_province_info
    set k3_code = #{k3Code,jdbcType=VARCHAR},
      mall_name = #{mallName,jdbcType=VARCHAR},
      capital_mall_code = #{capitalMallCode,jdbcType=VARCHAR},
      capital_mall_name = #{capitalMallName,jdbcType=VARCHAR},
      capital_k3_code = #{capitalK3Code,jdbcType=VARCHAR},
      scm_code = #{scmCode,jdbcType=VARCHAR}
    where mall_code = #{mallCode,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 07 11:15:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into contract_province_info
    (mall_code, k3_code, mall_name, capital_mall_code, capital_mall_name, capital_k3_code, 
      scm_code)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.mallCode,jdbcType=VARCHAR}, #{item.k3Code,jdbcType=VARCHAR}, #{item.mallName,jdbcType=VARCHAR}, 
        #{item.capitalMallCode,jdbcType=VARCHAR}, #{item.capitalMallName,jdbcType=VARCHAR}, 
        #{item.capitalK3Code,jdbcType=VARCHAR}, #{item.scmCode,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 07 11:15:25 CST 2024. by MyBatis Generator, do not modify.
    -->
    insert into contract_province_info (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'mall_code'.toString() == column.value">
          #{item.mallCode,jdbcType=VARCHAR}
        </if>
        <if test="'k3_code'.toString() == column.value">
          #{item.k3Code,jdbcType=VARCHAR}
        </if>
        <if test="'mall_name'.toString() == column.value">
          #{item.mallName,jdbcType=VARCHAR}
        </if>
        <if test="'capital_mall_code'.toString() == column.value">
          #{item.capitalMallCode,jdbcType=VARCHAR}
        </if>
        <if test="'capital_mall_name'.toString() == column.value">
          #{item.capitalMallName,jdbcType=VARCHAR}
        </if>
        <if test="'capital_k3_code'.toString() == column.value">
          #{item.capitalK3Code,jdbcType=VARCHAR}
        </if>
        <if test="'scm_code'.toString() == column.value">
          #{item.scmCode,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>