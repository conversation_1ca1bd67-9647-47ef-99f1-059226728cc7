<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.UserMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.User">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 31 10:57:36 CST 2022. by MyBatis Generator, do not modify.
    -->
    <id column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="pwd" jdbcType="VARCHAR" property="pwd" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_admin" jdbcType="BIT" property="isAdmin" />
    <result column="role_id" jdbcType="VARCHAR" property="roleId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_cancel" jdbcType="BIT" property="isCancel" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="company" jdbcType="VARCHAR" property="company" />
    <result column="partner_name" jdbcType="VARCHAR" property="partnerName" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="is_primary" jdbcType="BIT" property="isPrimary" />
    <result column="is_send" jdbcType="BIT" property="isSend" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 31 10:57:36 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 31 10:57:36 CST 2022. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 31 10:57:36 CST 2022. by MyBatis Generator, do not modify.
    -->
    user_id, name, pwd, phone, remark, is_admin, role_id, create_time, update_time, is_cancel, 
    email, company, partner_name, creator, is_primary, is_send
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.UserExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 31 10:57:36 CST 2022. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 31 10:57:36 CST 2022. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from user
    where user_id = #{userId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 31 10:57:36 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from user
    where user_id = #{userId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.UserExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 31 10:57:36 CST 2022. by MyBatis Generator, do not modify.
    -->
    delete from user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.User">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 31 10:57:36 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into user (user_id, name, pwd, 
      phone, remark, is_admin, 
      role_id, create_time, update_time, 
      is_cancel, email, company, 
      partner_name, creator, is_primary, 
      is_send)
    values (#{userId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{pwd,jdbcType=VARCHAR}, 
      #{phone,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{isAdmin,jdbcType=BIT}, 
      #{roleId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{isCancel,jdbcType=BIT}, #{email,jdbcType=VARCHAR}, #{company,jdbcType=VARCHAR}, 
      #{partnerName,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, #{isPrimary,jdbcType=BIT}, 
      #{isSend,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.User">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 31 10:57:36 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="pwd != null">
        pwd,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isAdmin != null">
        is_admin,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isCancel != null">
        is_cancel,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="company != null">
        company,
      </if>
      <if test="partnerName != null">
        partner_name,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="isPrimary != null">
        is_primary,
      </if>
      <if test="isSend != null">
        is_send,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="pwd != null">
        #{pwd,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isAdmin != null">
        #{isAdmin,jdbcType=BIT},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isCancel != null">
        #{isCancel,jdbcType=BIT},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        #{company,jdbcType=VARCHAR},
      </if>
      <if test="partnerName != null">
        #{partnerName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="isPrimary != null">
        #{isPrimary,jdbcType=BIT},
      </if>
      <if test="isSend != null">
        #{isSend,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.UserExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 31 10:57:36 CST 2022. by MyBatis Generator, do not modify.
    -->
    select count(*) from user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 31 10:57:36 CST 2022. by MyBatis Generator, do not modify.
    -->
    update user
    <set>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.pwd != null">
        pwd = #{record.pwd,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isAdmin != null">
        is_admin = #{record.isAdmin,jdbcType=BIT},
      </if>
      <if test="record.roleId != null">
        role_id = #{record.roleId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isCancel != null">
        is_cancel = #{record.isCancel,jdbcType=BIT},
      </if>
      <if test="record.email != null">
        email = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.company != null">
        company = #{record.company,jdbcType=VARCHAR},
      </if>
      <if test="record.partnerName != null">
        partner_name = #{record.partnerName,jdbcType=VARCHAR},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.isPrimary != null">
        is_primary = #{record.isPrimary,jdbcType=BIT},
      </if>
      <if test="record.isSend != null">
        is_send = #{record.isSend,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 31 10:57:36 CST 2022. by MyBatis Generator, do not modify.
    -->
    update user
    set user_id = #{record.userId,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      pwd = #{record.pwd,jdbcType=VARCHAR},
      phone = #{record.phone,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_admin = #{record.isAdmin,jdbcType=BIT},
      role_id = #{record.roleId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      is_cancel = #{record.isCancel,jdbcType=BIT},
      email = #{record.email,jdbcType=VARCHAR},
      company = #{record.company,jdbcType=VARCHAR},
      partner_name = #{record.partnerName,jdbcType=VARCHAR},
      creator = #{record.creator,jdbcType=VARCHAR},
      is_primary = #{record.isPrimary,jdbcType=BIT},
      is_send = #{record.isSend,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.User">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 31 10:57:36 CST 2022. by MyBatis Generator, do not modify.
    -->
    update user
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="pwd != null">
        pwd = #{pwd,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isAdmin != null">
        is_admin = #{isAdmin,jdbcType=BIT},
      </if>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isCancel != null">
        is_cancel = #{isCancel,jdbcType=BIT},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        company = #{company,jdbcType=VARCHAR},
      </if>
      <if test="partnerName != null">
        partner_name = #{partnerName,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="isPrimary != null">
        is_primary = #{isPrimary,jdbcType=BIT},
      </if>
      <if test="isSend != null">
        is_send = #{isSend,jdbcType=BIT},
      </if>
    </set>
    where user_id = #{userId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.User">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 31 10:57:36 CST 2022. by MyBatis Generator, do not modify.
    -->
    update user
    set name = #{name,jdbcType=VARCHAR},
      pwd = #{pwd,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      is_admin = #{isAdmin,jdbcType=BIT},
      role_id = #{roleId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_cancel = #{isCancel,jdbcType=BIT},
      email = #{email,jdbcType=VARCHAR},
      company = #{company,jdbcType=VARCHAR},
      partner_name = #{partnerName,jdbcType=VARCHAR},
      creator = #{creator,jdbcType=VARCHAR},
      is_primary = #{isPrimary,jdbcType=BIT},
      is_send = #{isSend,jdbcType=BIT}
    where user_id = #{userId,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 31 10:57:36 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into user
    (user_id, name, pwd, phone, remark, is_admin, role_id, create_time, update_time, 
      is_cancel, email, company, partner_name, creator, is_primary, is_send)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.userId,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.pwd,jdbcType=VARCHAR}, 
        #{item.phone,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.isAdmin,jdbcType=BIT}, 
        #{item.roleId,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.isCancel,jdbcType=BIT}, #{item.email,jdbcType=VARCHAR}, #{item.company,jdbcType=VARCHAR}, 
        #{item.partnerName,jdbcType=VARCHAR}, #{item.creator,jdbcType=VARCHAR}, #{item.isPrimary,jdbcType=BIT}, 
        #{item.isSend,jdbcType=BIT})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Aug 31 10:57:36 CST 2022. by MyBatis Generator, do not modify.
    -->
    insert into user (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
        <if test="'name'.toString() == column.value">
          #{item.name,jdbcType=VARCHAR}
        </if>
        <if test="'pwd'.toString() == column.value">
          #{item.pwd,jdbcType=VARCHAR}
        </if>
        <if test="'phone'.toString() == column.value">
          #{item.phone,jdbcType=VARCHAR}
        </if>
        <if test="'remark'.toString() == column.value">
          #{item.remark,jdbcType=VARCHAR}
        </if>
        <if test="'is_admin'.toString() == column.value">
          #{item.isAdmin,jdbcType=BIT}
        </if>
        <if test="'role_id'.toString() == column.value">
          #{item.roleId,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'is_cancel'.toString() == column.value">
          #{item.isCancel,jdbcType=BIT}
        </if>
        <if test="'email'.toString() == column.value">
          #{item.email,jdbcType=VARCHAR}
        </if>
        <if test="'company'.toString() == column.value">
          #{item.company,jdbcType=VARCHAR}
        </if>
        <if test="'partner_name'.toString() == column.value">
          #{item.partnerName,jdbcType=VARCHAR}
        </if>
        <if test="'creator'.toString() == column.value">
          #{item.creator,jdbcType=VARCHAR}
        </if>
        <if test="'is_primary'.toString() == column.value">
          #{item.isPrimary,jdbcType=BIT}
        </if>
        <if test="'is_send'.toString() == column.value">
          #{item.isSend,jdbcType=BIT}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>