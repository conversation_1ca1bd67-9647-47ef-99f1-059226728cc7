<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.JkbanOrderMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.JkbanOrder">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 23 09:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="msisdn" jdbcType="VARCHAR" property="msisdn" />
    <result column="iccid" jdbcType="VARCHAR" property="iccid" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="order_provice_code" jdbcType="VARCHAR" property="orderProviceCode" />
    <result column="order_status" jdbcType="VARCHAR" property="orderStatus" />
    <result column="question_type" jdbcType="VARCHAR" property="questionType" />
    <result column="question" jdbcType="VARCHAR" property="question" />
    <result column="contact_phone" jdbcType="VARCHAR" property="contactPhone" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="region_code" jdbcType="VARCHAR" property="regionCode" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="user_phone" jdbcType="VARCHAR" property="userPhone" />
    <result column="question_time" jdbcType="TIMESTAMP" property="questionTime" />
    <result column="question_desc" jdbcType="VARCHAR" property="questionDesc" />
    <result column="sms_acpt_code" jdbcType="VARCHAR" property="smsAcptCode" />
    <result column="opposite_number" jdbcType="VARCHAR" property="oppositeNumber" />
    <result column="onelink_account" jdbcType="VARCHAR" property="onelinkAccount" />
    <result column="call_url" jdbcType="VARCHAR" property="callUrl" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 23 09:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 23 09:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 23 09:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    id, msisdn, iccid, order_id, order_provice_code, order_status, question_type, question, 
    contact_phone, province_code, city_code, region_code, address, user_phone, question_time, 
    question_desc, sms_acpt_code, opposite_number, onelink_account, call_url, create_time, 
    update_time, user_id
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.JkbanOrderExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 23 09:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from jkban_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 23 09:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from jkban_order
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 23 09:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from jkban_order
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.JkbanOrderExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 23 09:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    delete from jkban_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.JkbanOrder">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 23 09:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into jkban_order (id, msisdn, iccid, 
      order_id, order_provice_code, order_status, 
      question_type, question, contact_phone, 
      province_code, city_code, region_code, 
      address, user_phone, question_time, 
      question_desc, sms_acpt_code, opposite_number, 
      onelink_account, call_url, create_time, 
      update_time, user_id)
    values (#{id,jdbcType=VARCHAR}, #{msisdn,jdbcType=VARCHAR}, #{iccid,jdbcType=VARCHAR}, 
      #{orderId,jdbcType=VARCHAR}, #{orderProviceCode,jdbcType=VARCHAR}, #{orderStatus,jdbcType=VARCHAR}, 
      #{questionType,jdbcType=VARCHAR}, #{question,jdbcType=VARCHAR}, #{contactPhone,jdbcType=VARCHAR}, 
      #{provinceCode,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, #{regionCode,jdbcType=VARCHAR}, 
      #{address,jdbcType=VARCHAR}, #{userPhone,jdbcType=VARCHAR}, #{questionTime,jdbcType=TIMESTAMP}, 
      #{questionDesc,jdbcType=VARCHAR}, #{smsAcptCode,jdbcType=VARCHAR}, #{oppositeNumber,jdbcType=VARCHAR}, 
      #{onelinkAccount,jdbcType=VARCHAR}, #{callUrl,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{userId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.JkbanOrder">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 23 09:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into jkban_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="msisdn != null">
        msisdn,
      </if>
      <if test="iccid != null">
        iccid,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderProviceCode != null">
        order_provice_code,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="questionType != null">
        question_type,
      </if>
      <if test="question != null">
        question,
      </if>
      <if test="contactPhone != null">
        contact_phone,
      </if>
      <if test="provinceCode != null">
        province_code,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="regionCode != null">
        region_code,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="userPhone != null">
        user_phone,
      </if>
      <if test="questionTime != null">
        question_time,
      </if>
      <if test="questionDesc != null">
        question_desc,
      </if>
      <if test="smsAcptCode != null">
        sms_acpt_code,
      </if>
      <if test="oppositeNumber != null">
        opposite_number,
      </if>
      <if test="onelinkAccount != null">
        onelink_account,
      </if>
      <if test="callUrl != null">
        call_url,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="userId != null">
        user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="msisdn != null">
        #{msisdn,jdbcType=VARCHAR},
      </if>
      <if test="iccid != null">
        #{iccid,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderProviceCode != null">
        #{orderProviceCode,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=VARCHAR},
      </if>
      <if test="questionType != null">
        #{questionType,jdbcType=VARCHAR},
      </if>
      <if test="question != null">
        #{question,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="regionCode != null">
        #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="userPhone != null">
        #{userPhone,jdbcType=VARCHAR},
      </if>
      <if test="questionTime != null">
        #{questionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="questionDesc != null">
        #{questionDesc,jdbcType=VARCHAR},
      </if>
      <if test="smsAcptCode != null">
        #{smsAcptCode,jdbcType=VARCHAR},
      </if>
      <if test="oppositeNumber != null">
        #{oppositeNumber,jdbcType=VARCHAR},
      </if>
      <if test="onelinkAccount != null">
        #{onelinkAccount,jdbcType=VARCHAR},
      </if>
      <if test="callUrl != null">
        #{callUrl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.JkbanOrderExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 23 09:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    select count(*) from jkban_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 23 09:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    update jkban_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.msisdn != null">
        msisdn = #{record.msisdn,jdbcType=VARCHAR},
      </if>
      <if test="record.iccid != null">
        iccid = #{record.iccid,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderProviceCode != null">
        order_provice_code = #{record.orderProviceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orderStatus != null">
        order_status = #{record.orderStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.questionType != null">
        question_type = #{record.questionType,jdbcType=VARCHAR},
      </if>
      <if test="record.question != null">
        question = #{record.question,jdbcType=VARCHAR},
      </if>
      <if test="record.contactPhone != null">
        contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.provinceCode != null">
        province_code = #{record.provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.regionCode != null">
        region_code = #{record.regionCode,jdbcType=VARCHAR},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.userPhone != null">
        user_phone = #{record.userPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.questionTime != null">
        question_time = #{record.questionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.questionDesc != null">
        question_desc = #{record.questionDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.smsAcptCode != null">
        sms_acpt_code = #{record.smsAcptCode,jdbcType=VARCHAR},
      </if>
      <if test="record.oppositeNumber != null">
        opposite_number = #{record.oppositeNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.onelinkAccount != null">
        onelink_account = #{record.onelinkAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.callUrl != null">
        call_url = #{record.callUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 23 09:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    update jkban_order
    set id = #{record.id,jdbcType=VARCHAR},
      msisdn = #{record.msisdn,jdbcType=VARCHAR},
      iccid = #{record.iccid,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      order_provice_code = #{record.orderProviceCode,jdbcType=VARCHAR},
      order_status = #{record.orderStatus,jdbcType=VARCHAR},
      question_type = #{record.questionType,jdbcType=VARCHAR},
      question = #{record.question,jdbcType=VARCHAR},
      contact_phone = #{record.contactPhone,jdbcType=VARCHAR},
      province_code = #{record.provinceCode,jdbcType=VARCHAR},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      region_code = #{record.regionCode,jdbcType=VARCHAR},
      address = #{record.address,jdbcType=VARCHAR},
      user_phone = #{record.userPhone,jdbcType=VARCHAR},
      question_time = #{record.questionTime,jdbcType=TIMESTAMP},
      question_desc = #{record.questionDesc,jdbcType=VARCHAR},
      sms_acpt_code = #{record.smsAcptCode,jdbcType=VARCHAR},
      opposite_number = #{record.oppositeNumber,jdbcType=VARCHAR},
      onelink_account = #{record.onelinkAccount,jdbcType=VARCHAR},
      call_url = #{record.callUrl,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      user_id = #{record.userId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.JkbanOrder">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 23 09:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    update jkban_order
    <set>
      <if test="msisdn != null">
        msisdn = #{msisdn,jdbcType=VARCHAR},
      </if>
      <if test="iccid != null">
        iccid = #{iccid,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="orderProviceCode != null">
        order_provice_code = #{orderProviceCode,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=VARCHAR},
      </if>
      <if test="questionType != null">
        question_type = #{questionType,jdbcType=VARCHAR},
      </if>
      <if test="question != null">
        question = #{question,jdbcType=VARCHAR},
      </if>
      <if test="contactPhone != null">
        contact_phone = #{contactPhone,jdbcType=VARCHAR},
      </if>
      <if test="provinceCode != null">
        province_code = #{provinceCode,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="regionCode != null">
        region_code = #{regionCode,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="userPhone != null">
        user_phone = #{userPhone,jdbcType=VARCHAR},
      </if>
      <if test="questionTime != null">
        question_time = #{questionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="questionDesc != null">
        question_desc = #{questionDesc,jdbcType=VARCHAR},
      </if>
      <if test="smsAcptCode != null">
        sms_acpt_code = #{smsAcptCode,jdbcType=VARCHAR},
      </if>
      <if test="oppositeNumber != null">
        opposite_number = #{oppositeNumber,jdbcType=VARCHAR},
      </if>
      <if test="onelinkAccount != null">
        onelink_account = #{onelinkAccount,jdbcType=VARCHAR},
      </if>
      <if test="callUrl != null">
        call_url = #{callUrl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.JkbanOrder">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 23 09:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    update jkban_order
    set msisdn = #{msisdn,jdbcType=VARCHAR},
      iccid = #{iccid,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=VARCHAR},
      order_provice_code = #{orderProviceCode,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=VARCHAR},
      question_type = #{questionType,jdbcType=VARCHAR},
      question = #{question,jdbcType=VARCHAR},
      contact_phone = #{contactPhone,jdbcType=VARCHAR},
      province_code = #{provinceCode,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      region_code = #{regionCode,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      user_phone = #{userPhone,jdbcType=VARCHAR},
      question_time = #{questionTime,jdbcType=TIMESTAMP},
      question_desc = #{questionDesc,jdbcType=VARCHAR},
      sms_acpt_code = #{smsAcptCode,jdbcType=VARCHAR},
      opposite_number = #{oppositeNumber,jdbcType=VARCHAR},
      onelink_account = #{onelinkAccount,jdbcType=VARCHAR},
      call_url = #{callUrl,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      user_id = #{userId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 23 09:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into jkban_order
    (id, msisdn, iccid, order_id, order_provice_code, order_status, question_type, question, 
      contact_phone, province_code, city_code, region_code, address, user_phone, question_time, 
      question_desc, sms_acpt_code, opposite_number, onelink_account, call_url, create_time, 
      update_time, user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.msisdn,jdbcType=VARCHAR}, #{item.iccid,jdbcType=VARCHAR}, 
        #{item.orderId,jdbcType=VARCHAR}, #{item.orderProviceCode,jdbcType=VARCHAR}, #{item.orderStatus,jdbcType=VARCHAR}, 
        #{item.questionType,jdbcType=VARCHAR}, #{item.question,jdbcType=VARCHAR}, #{item.contactPhone,jdbcType=VARCHAR}, 
        #{item.provinceCode,jdbcType=VARCHAR}, #{item.cityCode,jdbcType=VARCHAR}, #{item.regionCode,jdbcType=VARCHAR}, 
        #{item.address,jdbcType=VARCHAR}, #{item.userPhone,jdbcType=VARCHAR}, #{item.questionTime,jdbcType=TIMESTAMP}, 
        #{item.questionDesc,jdbcType=VARCHAR}, #{item.smsAcptCode,jdbcType=VARCHAR}, #{item.oppositeNumber,jdbcType=VARCHAR}, 
        #{item.onelinkAccount,jdbcType=VARCHAR}, #{item.callUrl,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.userId,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Wed Jul 23 09:21:05 CST 2025. by MyBatis Generator, do not modify.
    -->
    insert into jkban_order (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'msisdn'.toString() == column.value">
          #{item.msisdn,jdbcType=VARCHAR}
        </if>
        <if test="'iccid'.toString() == column.value">
          #{item.iccid,jdbcType=VARCHAR}
        </if>
        <if test="'order_id'.toString() == column.value">
          #{item.orderId,jdbcType=VARCHAR}
        </if>
        <if test="'order_provice_code'.toString() == column.value">
          #{item.orderProviceCode,jdbcType=VARCHAR}
        </if>
        <if test="'order_status'.toString() == column.value">
          #{item.orderStatus,jdbcType=VARCHAR}
        </if>
        <if test="'question_type'.toString() == column.value">
          #{item.questionType,jdbcType=VARCHAR}
        </if>
        <if test="'question'.toString() == column.value">
          #{item.question,jdbcType=VARCHAR}
        </if>
        <if test="'contact_phone'.toString() == column.value">
          #{item.contactPhone,jdbcType=VARCHAR}
        </if>
        <if test="'province_code'.toString() == column.value">
          #{item.provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="'city_code'.toString() == column.value">
          #{item.cityCode,jdbcType=VARCHAR}
        </if>
        <if test="'region_code'.toString() == column.value">
          #{item.regionCode,jdbcType=VARCHAR}
        </if>
        <if test="'address'.toString() == column.value">
          #{item.address,jdbcType=VARCHAR}
        </if>
        <if test="'user_phone'.toString() == column.value">
          #{item.userPhone,jdbcType=VARCHAR}
        </if>
        <if test="'question_time'.toString() == column.value">
          #{item.questionTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'question_desc'.toString() == column.value">
          #{item.questionDesc,jdbcType=VARCHAR}
        </if>
        <if test="'sms_acpt_code'.toString() == column.value">
          #{item.smsAcptCode,jdbcType=VARCHAR}
        </if>
        <if test="'opposite_number'.toString() == column.value">
          #{item.oppositeNumber,jdbcType=VARCHAR}
        </if>
        <if test="'onelink_account'.toString() == column.value">
          #{item.onelinkAccount,jdbcType=VARCHAR}
        </if>
        <if test="'call_url'.toString() == column.value">
          #{item.callUrl,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'user_id'.toString() == column.value">
          #{item.userId,jdbcType=VARCHAR}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>