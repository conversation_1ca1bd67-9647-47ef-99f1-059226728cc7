<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.MiniProgramJkbanMapperExt">

    <select id="getJkbanList"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.JkbanListVO"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.JkbanListParam">
        SELECT
        jo.id id,
        jo.order_id orderId,
        jo.order_status orderStatus,
        jo.msisdn msisdn,
        jo.iccid iccid,
        jo.question_type questionType,
        jo.question question,
        jo.question_time questionTime,
        jo.question_desc questionDesc,
        jo.create_time createTime
        FROM
        jkban_order jo
        where
            jo.user_id = #{param.userId}
        <if test="param.orderId != null and param.orderId != ''">
            and jo.order_id = #{param.orderId}
        </if>
        <if test="param.orderStatus!=null and param.orderStatus.size()!=0">
            and jo.order_status in
            <foreach collection="param.orderStatus" item="status" index="index" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="param.number != null and param.number != ''">
            and (jo.msisdn like concat ('%',#{param.number},'%')
                     or jo.iccid like concat ('%',#{param.number},'%'))
        </if>
        <if test="param.contactPhone != null and param.contactPhone != ''">
            and jo.contact_phone = #{param.contactPhone}
        </if>
        <if test="param.startTime != null and param.startTime != ''">
            and jo.create_time <![CDATA[ >= ]]> #{param.startTime}
        </if>
        <if test="param.endTime != null and param.endTime != ''">
            and jo.create_time <![CDATA[ <= ]]> #{param.endTime}
        </if>

        <if test="param.sortType != null and param.sortType != ''">
            ORDER BY jo.create_time ${param.sortType}
        </if>

    </select>

</mapper>
