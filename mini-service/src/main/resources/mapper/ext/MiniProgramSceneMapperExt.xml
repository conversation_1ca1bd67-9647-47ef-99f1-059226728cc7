<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.MiniProgramSceneMapperExt">

    <select id="pageSceneFrontend"
        parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageSceneFrontendParam"
        resultType="com.chinamobile.retail.pojo.vo.miniprogram.SceneFrontendItemVO">

        select
            mps.id as id,
            mps.name as name,
            mps.head_image_url as headImage,
            mps.spu_code as spuCode,
            spu.offering_name as spuName
        from mini_program_scene mps
        left join spu_offering_info spu on mps.spu_code = spu.offering_code and spu.delete_time is null
        where mps.status = 1 and mps.deleted = false
        <if test="secondDirectoryId != null and secondDirectoryId != ''">
            and mps.second_directory_id = #{secondDirectoryId}
        </if>
        order by mps.create_time desc
        limit ${pageSize} OFFSET ${(pageNum - 1) * pageSize}
    </select>

    <select id="countSceneFrontend"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageSceneFrontendParam"
            resultType="java.lang.Long">

        select
            count(*)
        from mini_program_scene mps
        where mps.status = 1 and mps.deleted = false
        <if test="secondDirectoryId != null and secondDirectoryId != ''">
            and mps.second_directory_id = #{secondDirectoryId}
        </if>
    </select>

    <select id="selectRelatedSpu"
        parameterType="java.lang.String"
        resultType="com.chinamobile.retail.pojo.vo.miniprogram.SceneFrontendItemVO$RelatedSpuVO">

        select
            mpspu.spu_code as spuCode,
            spu.offering_name as spuName,
            spu.img_url AS spuImage,
            IFNULL(MIN(sku.price), 0) AS minPrice,
            mpspu.x as x,
            mpspu.y as y,
            sc.core_component_name as coreComponentName,
            sc.core_component_img as coreComponentImg
        from mini_program_scene_spu mpspu
        left join spu_offering_info spu on mpspu.spu_code = spu.offering_code and spu.delete_time is null
        left join spu_core_component sc on sc.spu_code = spu.offering_code and sc.is_delete = 0
        join sku_offering_info sku on sku.spu_code = spu.offering_code and sku.offering_status = 1 and sku.delete_time is null
        where mpspu.scene_id = #{sceneId}
        group by mpspu.spu_code
    </select>

    <select id="getSceneDetailFrontend"
        parameterType="com.chinamobile.retail.pojo.param.miniprogram.SceneDetailFrontendParam"
        resultType="com.chinamobile.retail.pojo.vo.miniprogram.SceneDetailFrontendVO">

        select
            mps.id as id,
            mps.name as name,
            mps.image_url as image,
            mps.spu_code as spuCode,
            spu.offering_name as spuName,
            mps.first_directory_id as firstDirectoryId,
            mps.second_directory_id as secondDirectoryId,
            mps.template_id as templateId
        from mini_program_scene mps
        left join spu_offering_info spu on mps.spu_code = spu.offering_code and spu.delete_time is null
        where mps.id = #{sceneId}
        limit 1
    </select>

    <!-- 分页查询小程序场景列表 -->
    <select id="pageSceneList"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageSceneListParam"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.SceneVO">
        select
        a.id as id,
        a.name as name,
        a.template_id as templateId,
        u.name as createUserName,
        md1.name as firstDirectoryName,
        md1.id as firstDirectoryId,
        md2.name as secondDirectoryName,
        md2.id as secondDirectoryId,
        u.name as createUserName,
        a.create_time as createTime,
        a.audit_status as auditStatus,
        a.status as status
        from mini_program_scene a
        left join user u on u.user_id = a.create_uid
        left join mini_program_scene_directory md1 on a.first_directory_id = md1.id
        left join mini_program_scene_directory md2 on a.second_directory_id = md2.id

        where a.deleted = false
        <if test="status != null">
            and a.status = #{status}
        </if>

        <if test="auditStatusList != null and auditStatusList.size() != 0">
            and a.audit_status in
            <foreach collection="auditStatusList" item="auditStatus" index="index" open="(" close=")" separator=",">
                #{auditStatus}
            </foreach>
        </if>

        <if test="sceneName != null and sceneName != ''">
            and a.name like concat('%',#{sceneName}, '%')
        </if>

        <if test="(dirName != null and dirName != '') ">
          and ((md1.name like concat('%',#{dirName}, '%')) or (md2.name like concat('%',#{dirName}, '%')))
        </if>
        <if test="createUserName != null and createUserName != ''">
            and u.name like concat('%',#{createUserName}, '%')
        </if>
        order by a.create_time desc
        limit ${pageSize} OFFSET ${(pageNum - 1) * pageSize}
    </select>

    <!-- 管理后台查询小程序首页总数 -->
    <select id="countSceneList"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageSceneListParam"
            resultType="java.lang.Long">
        select count(*)
        from (
        select a.id
        from mini_program_scene a
        left join user u on u.user_id = a.create_uid
        left join mini_program_scene_directory md1 on a.first_directory_id = md1.id
        left join mini_program_scene_directory md2 on a.second_directory_id = md2.id
        where a.deleted = false
        <if test="status != null">
            and a.status = #{status}
        </if>

        <if test="auditStatusList != null and auditStatusList.size() != 0">
            and a.audit_status in
            <foreach collection="auditStatusList" item="auditStatus" index="index" open="(" close=")" separator=",">
                #{auditStatus}
            </foreach>
        </if>

        <if test="sceneName != null and sceneName != ''">
            and a.name like concat('%',#{sceneName}, '%')
        </if>

        <if test="(dirName != null and dirName != '') ">
            and ((md1.name like concat('%',#{dirName}, '%')) or (md2.name like concat('%',#{dirName}, '%')))
        </if>
        <if test="createUserName != null and createUserName != ''">
            and u.name like concat('%',#{createUserName}, '%')
        </if>

        ) t
    </select>


    <select id="getSceneProduct" resultType="com.chinamobile.retail.pojo.vo.MiniProgramProductListVO"
            parameterType="String">

        SELECT spu.offering_code                  AS spuCode,
               spu.offering_name                  AS spuName,
               spu.img_url                        AS image,
               IFNULL(MIN(sku.price), 0)          AS price,
               spu.tag                            AS saleTag,
               spu.product_description            AS spuRemark,
               ms.x                               AS x,
               ms.y                               AS y,
               sc.core_component_name             AS coreComponentName,
               sc.core_component_img              AS coreComponentImg
        FROM mini_program_scene_spu ms
                 INNER JOIN spu_offering_info spu ON spu.offering_code = ms.spu_code
                 INNER JOIN sku_offering_info sku
                            ON sku.spu_code = spu.offering_code AND sku.offering_status = 1 AND sku.delete_time is null
                 LEFT JOIN sku_release_target srt ON srt.sku_offering_code = sku.offering_code
                 LEFT JOIN spu_core_component sc ON sc.spu_code = spu.offering_code and sc.is_delete = 0
        WHERE ms.scene_id = #{sceneId}
          AND spu.offering_status = 1
          AND spu.delete_time is null
        GROUP BY spu.offering_code
    </select>


    <!-- 分页查询小程序场景需求列表 -->
    <select id="pageRequirementList"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageRequirementListParam"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.SceneRequirementVO">
        select
        a.id as id,
        a.province_name as provinceName,
        a.city_name as cityName,
        a.contact as contact,
        a.phone as phone,
        s.name as sceneName,
        u.name as createUserName,
        up.name as partnerBusinessName,
        a.partner_business_id as partnerBusinessId,
        a.create_time as createTime,
        a.audit_state as auditState
        from mini_program_scene_requirements a
        left join mini_program_scene s on s.id = a.scene_id
        left join user_mini_program u on u.id = a.create_uid
        left join user_partner up on up.user_id = a.partner_business_id
        <if test="dirName != null and dirName != ''">
            left join mini_program_scene_directory md1 on a.first_directory_id = md1.id
            left join mini_program_scene_directory md2 on a.second_directory_id = md2.id
        </if>
        where a.deleted = false

        <if test="auditStatusList != null and auditStatusList.size() != 0">
            and a.audit_state in
            <foreach collection="auditStatusList" item="auditStatus" index="index" open="(" close=")" separator=",">
                #{auditStatus}
            </foreach>
        </if>

        <if test="sceneName != null and sceneName != ''">
            and s.name like concat('%',#{sceneName}, '%')
        </if>

        <if test="(dirName != null and dirName != '') ">
            and ((md1.name like concat('%',#{dirName}, '%')) or (md2.name like concat('%',#{dirName}, '%')))
        </if>
        <if test="createUserName != null and createUserName != ''">
            and u.name like concat('%',#{createUserName}, '%')
        </if>
        <if test="partnerBusinessId != null and partnerBusinessId != ''">
            and a.partner_business_id = #{partnerBusinessId}
        </if>
        order by a.create_time desc
        limit ${pageSize} OFFSET ${(pageNum - 1) * pageSize}
    </select>

    <!-- 管理后台查询小程序场景需求总数 -->
    <select id="countRequirementList"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageRequirementListParam"
            resultType="java.lang.Long">
        select count(*)
        from (
        select a.id
        from mini_program_scene_requirements a
        left join mini_program_scene s on s.id = a.scene_id
        left join user_mini_program u on u.id = a.create_uid
        left join user_partner up on up.user_id = a.partner_business_id
        <if test="dirName != null and dirName != ''">
            left join mini_program_scene_directory md1 on a.first_directory_id = md1.id
            left join mini_program_scene_directory md2 on a.second_directory_id = md2.id
        </if>
        where a.deleted = false
        <if test="auditStatusList != null and auditStatusList.size() != 0">
            and a.audit_state in
            <foreach collection="auditStatusList" item="auditStatus" index="index" open="(" close=")" separator=",">
                #{auditStatus}
            </foreach>
        </if>

        <if test="sceneName != null and sceneName != ''">
            and s.name like concat('%',#{sceneName}, '%')
        </if>

        <if test="(dirName != null and dirName != '') ">
            and ((md1.name like concat('%',#{dirName}, '%')) or (md2.name like concat('%',#{dirName}, '%')))
        </if>
        <if test="createUserName != null and createUserName != ''">
            and u.name like concat('%',#{createUserName}, '%')
        </if>
        <if test="partnerBusinessId != null and partnerBusinessId != ''">
            and a.partner_business_id = #{partnerBusinessId}
        </if>

        ) t
    </select>

    <!-- 分页查询小程序场景需求模板列表 -->
    <select id="pageTemplateList"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageTemplateListParam"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.TemplateVO">
        select
        a.id as id,
        a.name as name,
        u.name as createUserName,
        a.create_time as createTime
        from mini_program_scene_requirements_template a
        left join user u on u.user_id = a.create_uid

        where a.deleted = false
        <if test="name != null and name != ''">
            and a.name like concat('%',#{name}, '%')
        </if>

        <if test="createUserName != null and createUserName != ''">
            and u.name like concat('%',#{createUserName}, '%')
        </if>
        order by a.create_time desc
        limit ${pageSize} OFFSET ${(pageNum - 1) * pageSize}
    </select>

    <!-- 管理后台查询小程序场景需求模板总数 -->
    <select id="countTemplateList"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageTemplateListParam"
            resultType="java.lang.Long">
        select count(*)
        from (
        select a.id
        from mini_program_scene_requirements_template a
        left join user u on u.user_id = a.create_uid
        where a.deleted = false

        <if test="name != null and name != ''">
            and a.name like concat('%',#{name}, '%')
        </if>

        <if test="createUserName != null and createUserName != ''">
            and u.name like concat('%',#{createUserName}, '%')
        </if>

        ) t
    </select>

    <!-- 分页查询小程序场景需求模板列表 -->
    <select id="searchTemplateList"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageTemplateListParam"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.TemplateVO">
        select
        a.id as id,
        a.name as name,
        u.name as createUserName,
        a.create_time as createTime
        from mini_program_scene_requirements_template a
        left join user u on u.user_id = a.create_uid

        where a.deleted = false
        <if test="name != null and name != ''">
            and a.name like concat('%',#{name}, '%')
        </if>

        <if test="createUserName != null and createUserName != ''">
            and u.name like concat('%',#{createUserName}, '%')
        </if>
        order by a.create_time desc
    </select>

    <select id="getRequirementAnswers" resultType="com.chinamobile.retail.pojo.dto.RequirementsQuestionAndAnswerDTO"
            parameterType="String">

        SELECT ma.id                  AS id,
               ma.requirement_id                  AS requirementId,
               ma.question_id AS questionId,
               ma.answer AS answer,
               q.question                      AS question,
               q.required                      AS required,
               q.hint                  AS hint
        FROM mini_program_scene_requirements_answer ma
                 LEFT JOIN mini_program_scene_requirements_template_question q ON q.id = ma.question_id
        WHERE ma.requirement_id = #{requirementId}
    </select>

    <select id="getPartnerBusinessName" parameterType="String" resultType="String">
        select
            u.name
        from user_partner u
        where u.user_id = #{partnerBusinessId}
    </select>

</mapper>
