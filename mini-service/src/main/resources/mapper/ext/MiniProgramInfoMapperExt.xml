<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.MiniProgramInfoMapperExt">

    <!-- 分页查询小程序资讯列表 -->
    <select id="pageInfoList"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageInfoListParam"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.PageInfoVO">
        select
            a.id as id,
            a.name as name,
            a.category as category,
            a.info_type as infoType,
            a.content_type as contentType,
            a.content as content,
            a.audit_status as auditStatus,
            a.head_img_url_1 as headImgUrl1,
            a.head_img_url_2 as headImgUrl2,
            a.is_popular as isPopular,
            u.name as createUserName,
            a.create_time as createTime,
            a.knowledge_type as knowledgeType,
            a.word_type as wordType,
            a.status as status,
            a.audit_status as auditStatus
        from mini_program_info a
        left join user u on u.user_id = a.create_uid

        <where>
            and a.is_delete = 0
            <if test="status != null">
                and a.status = #{status}
            </if>

            <if test="categoryNum != null and isMini !=1">
                and a.category = #{categoryNum}
            </if>
            <if test="categoryNum != null and isMini ==1 ">
                and (a.category &amp; #{categoryNum}) != 0
            </if>

            <if test="infoType != null and infoType != ''">
                and a.info_type like concat('%',#{infoType}, '%')
            </if>

            <if test="contentType != null and contentType != ''">
                and a.content_type = #{contentType}
            </if>

            <if test="titleKey != null and titleKey != ''">
                and a.name like concat('%',#{titleKey}, '%')
            </if>

            <if test="keyWord != null and keyWord != ''">
                and ( a.name like concat('%',#{keyWord}, '%') or a.key_words like concat('%',#{keyWord}, '%') )
            </if>
            <if test="createUserName != null and createUserName != ''">
                and u.name like concat('%',#{createUserName}, '%')
            </if>
            <if test="wordType != null">
                and a.word_type = #{wordType}
            </if>
            <if test="isPopular != null">
                and a.is_popular = #{isPopular}
            </if>
            <if test="knowledgeType != null and knowledgeType!=''">
                and a.knowledge_type = #{knowledgeType}
            </if>
            <if test="auditedStatusList != null and auditedStatusList.size() != 0">
                and a.audit_status in
                <foreach collection="auditedStatusList" item="auditStatus" index="index" open="(" close=")" separator=",">
                    #{auditStatus}
                </foreach>
            </if>
        </where>
        order by a.create_time desc
        limit ${pageSize} OFFSET ${(pageNum - 1) * pageSize}
    </select>

    <!-- 管理后台查询小程序活动总数 -->
    <select id="countInfoList"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageInfoListParam"
            resultType="java.lang.Long">
        select count(*)
        from (
            select a.id
        from mini_program_info a
        left join user u on u.user_id = a.create_uid
            <where>
                 and a.is_delete = 0
                <if test="status != null">
                    and a.status = #{status}
                </if>

                <if test="categoryNum != null and isMini !=1">
                    and a.category = #{categoryNum}
                </if>
                <if test="categoryNum != null and isMini ==1 ">
                    and (a.category &amp; #{categoryNum}) != 0
                </if>
                <if test="infoType != null and infoType != ''">
                    and a.info_type like concat('%',#{infoType}, '%')
                </if>

                <if test="contentType != null and contentType != ''">
                    and a.content_type = #{contentType}
                </if>

                <if test="titleKey != null and titleKey != ''">
                    and a.name like concat('%',#{titleKey}, '%')
                </if>

                <if test="keyWord != null and keyWord != ''">
                    and ( a.name like concat('%',#{keyWord}, '%') or a.key_words like concat('%',#{keyWord}, '%') )
                </if>
                <if test="createUserName != null and createUserName != ''">
                    and u.name like concat('%',#{createUserName}, '%')
                </if>
                <if test="wordType != null">
                    and a.word_type = #{wordType}
                </if>
                <if test="isPopular != null">
                    and a.is_popular = #{isPopular}
                </if>
                <if test="knowledgeType != null and knowledgeType!=''">
                    and a.knowledge_type = #{knowledgeType}
                </if>
                <if test="auditedStatusList != null and auditedStatusList.size() != 0">
                    and a.audit_status in
                    <foreach collection="auditedStatusList" item="auditStatus" index="index" open="(" close=")" separator=",">
                        #{auditStatus}
                    </foreach>
                </if>
            </where>

        ) t
    </select>

    <!-- 分页查询小程序资讯spu列表 -->
    <select id="getInfoSpuList" parameterType="String"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.InfoSpuItemVO">
        select
            a.spu_offering_code as spuCode,
            s.offering_name as spuName,
            s.img_url as spuImgUrl,
            s.tag as saleTag,
            s.product_description as spuRemark,
            IFNULL(MIN(sku.price), 0) as price,
            sc.core_component_name as coreComponentName,
            sc.core_component_img as coreComponentImg,
            (SELECT GROUP_CONCAT(label) FROM spu_sale_label where (type = 0 or type is null) and spu_code = s.offering_code) mainSaleLabel,
            (SELECT GROUP_CONCAT(label) FROM spu_sale_label where type = 1 and spu_code = s.offering_code) subSaleLabel
        from mini_program_info_spu_code a
        left join spu_offering_info s on s.offering_code = a.spu_offering_code
        left join spu_core_component sc on sc.spu_code = s.offering_code and sc.is_delete = 0
        join sku_offering_info sku ON sku.spu_code = s.offering_code AND sku.offering_status = 1 AND sku.delete_time is null
        join sku_release_target srt ON srt.sku_offering_code = sku.offering_code
        <if test="provinceCode != null and provinceCode != ''">
            AND srt.province_code IN ('000', #{provinceCode})
        </if>
        <if test="cityCode != null and cityCode != ''">
            AND (srt.city_code = #{cityCode} OR srt.city_code IS NULL)
        </if>
        <if test="cityCode == null or cityCode == '' ">
            AND (srt.city_code IS NULL or exists (SELECT srt1.sku_offering_code
            FROM sku_release_target srt1
            JOIN contract_city_info cci
            ON srt1.city_code = cci.mall_code
            WHERE cci.province_mall_code = #{provinceCode} and srt1.sku_offering_code = srt.sku_offering_code
            GROUP BY srt1.sku_offering_code
            HAVING COUNT(DISTINCT cci.mall_code) =
            (SELECT COUNT(1)
            FROM contract_city_info
            WHERE province_mall_code = #{provinceCode})))
        </if>

        where a.info_id = #{infoId} and (
                s.delete_time is null or (select count(s1.id) from spu_offering_info s1 where s1.offering_code = a.spu_offering_code)=1
            )
        AND (s.secretly_listed = '1' or s.secretly_listed is null)
        group by s.offering_code
    </select>

    <select id="getInfoSpuListForWeb" parameterType="String"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.InfoSpuItemVO">
        select
            a.spu_offering_code as spuCode,
            s.offering_name as spuName,
            s.img_url as spuImgUrl,
            s.product_description as spuRemark,
            sc.core_component_name as coreComponentName,
            sc.core_component_img as coreComponentImg,
            (SELECT GROUP_CONCAT(label) FROM spu_sale_label where (type = 0 or type is null) and spu_code = s.offering_code) mainSaleLabel,
            (SELECT GROUP_CONCAT(label) FROM spu_sale_label where type = 1 and spu_code = s.offering_code) subSaleLabel
        from mini_program_info_spu_code a
        left join spu_offering_info s on s.offering_code = a.spu_offering_code
        left join spu_core_component sc on sc.spu_code = s.offering_code and sc.is_delete = 0
        where a.info_id = #{infoId} and (s.delete_time is null or
         (select count(s1.id)  from spu_offering_info s1 where s1.offering_code = a.spu_offering_code)=1)
    </select>

    <!-- 分页查询小程序资讯spu列表 -->
    <select id="getInfoActivityList"
            resultType="com.chinamobile.retail.pojo.entity.MiniProgramInfoActivity">
        select
        a.id as id,
        a.info_id as infoId,
        a.activity_id as activityId,
        a.activity_name as activityName,
        a.activity_img as activityImg
        from mini_program_info_activity a
        join  activity a1 on a1.id = a.activity_id
        <if test="provinceCode != null and provinceCode != ''">
            AND (a1.province_code = #{provinceCode} or a1.province_code is null)
        </if>
        where a.info_id = #{infoId}
    </select>


    <select id="getUserName" parameterType="String" resultType="String">
        select
            u.name
        from user u
        where u.user_id = #{userId}
    </select>
    <!-- 查询小程序知识库首页 -->
    <select id="getKnowlegeInfoList" parameterType="com.chinamobile.retail.pojo.param.miniprogram.KnowledgeHomeParam"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.PageInfoVO">
        select *
        from mini_program_info
        where 1 = 1
          and   4 &amp; category
          and status = 1
          and is_popular = 1
          and is_delete = 0
        <if test="param.knowledgeType != null and param.knowledgeType != ''">
            and knowledge_type  = #{param.knowledgeType}
        </if>
        <if test="param.contentType != null and param.contentType != ''">
            and content_type = #{param.contentType}
        </if>
        <if test="param.wordType != null">
            and word_type = #{param.wordType}
        </if>
        order by create_time desc limit #{param.limit};
    </select>

    <select id="searchInfo"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.PageInfoVO">
        select
        a.id as id,
        a.name as name,
        a.category as category,
        a.info_type as infoType,
        a.content_type as contentType,
        a.audit_status as auditStatus,
        a.head_img_url_1 as headImgUrl1,
        a.head_img_url_2 as headImgUrl2,
        a.create_time as createTime,
        a.status as status
        from mini_program_info a
        where a.status = 1
        <if test="contentType != null and contentType != ''">
            and a.content_type = #{contentType}
        </if>

        <if test="keyWord != null and keyWord != ''">
            and a.name like concat('%',#{keyWord}, '%')
        </if>

        <if test="category != null">
            and (a.category &amp; #{category}) != 0
        </if>

        order by a.create_time desc
    </select>
</mapper>
