<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.UserMinProgramMapperExt">
    <select id="selectSaleYearReportBasic"
        resultType="com.chinamobile.retail.pojo.vo.miniprogram.SaleYearReportVO"
        parameterType="java.lang.Integer">

        SELECT
            userId,
            name,
            '2024' AS year,
            @row:=@row+1 AS registerIndex,
            registerTime,
            roleType,
            provinceCode,
            provinceName,
            cityCode,
            cityName,
            firstOrderTime,
            firstOrderSpuCode,
            firstOrderSpuName
        FROM (
            SELECT
                u.name as name,
                u.user_id AS userId,
                u.create_time AS registerTime,
                @row_num := @row_num + 1 AS registration_sequence,
                u.role_type AS roleType,
                u.be_id AS provinceCode,
                u.province_name AS provinceName,
                u.location AS cityCode,
                u.city_name AS cityName,
                -- 第一笔订单发生时间
                CASE
                    WHEN u.role_type IN ('1', '2') THEN d.first_order_time
                    WHEN u.role_type = '3' THEN a.first_order_time
                    WHEN u.role_type = '4' THEN c.first_order_time
                    END AS firstOrderTime,
                CASE
                    WHEN u.role_type IN ('1', '2') THEN d.first_spu_code
                    WHEN u.role_type = '3' THEN a.first_spu_code
                    WHEN u.role_type = '4' THEN c.first_spu_code
                    END AS firstOrderSpuCode,
                CASE
                    WHEN u.role_type IN ('1', '2') THEN d.first_spu_name
                    WHEN u.role_type = '3' THEN a.first_spu_name
                    WHEN u.role_type = '4' THEN c.first_spu_name
                    END AS firstOrderSpuName
            FROM user_mini_program u
            LEFT JOIN (
                -- 分销员的第一笔订单时间
                SELECT
                    odi.distributor_user_id,
                    MIN(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) AS first_order_time,
                    spu.offering_code as first_spu_code,
                    spu.offering_name as first_spu_name
                FROM order_2c_distributor_info odi
                JOIN order_2c_info oi ON odi.order_id = oi.order_id
                join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
                WHERE odi.distributor_user_id IS NOT NULL
                GROUP BY odi.distributor_user_id, oi.order_id
                ORDER BY first_order_time
            ) d ON u.user_id = d.distributor_user_id AND u.role_type IN ('1', '2')
            LEFT JOIN (
                -- 代理商的第一笔订单时间
                SELECT
                    o2ai.agent_user_id,
                    MIN(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) AS first_order_time,
                    spu.offering_code as first_spu_code,
                    spu.offering_name as first_spu_name
                FROM order_2c_agent_info o2ai
                JOIN order_2c_info oi ON o2ai.order_id = oi.order_id
                join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
                WHERE o2ai.agent_user_id IS NOT NULL
                GROUP BY o2ai.agent_user_id, oi.order_id
                ORDER BY first_order_time
            ) a ON u.user_id = a.agent_user_id AND u.role_type = '3'
            LEFT JOIN (
                -- 客户经理的第一笔订单时间
                SELECT
                    oi.create_oper_user_id,
                    MIN(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) AS first_order_time,
                    spu.offering_code as first_spu_code,
                    spu.offering_name as first_spu_name
                FROM order_2c_info oi
                join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
                WHERE oi.create_oper_user_id IS NOT NULL
                GROUP BY oi.create_oper_user_id, oi.order_id
                ORDER BY first_order_time
            ) c ON u.user_id = c.create_oper_user_id AND u.role_type = '4'
            WHERE u.user_id is not null and u.status = '1' and u.role_type != '0'
            GROUP BY u.user_id, u.create_time
            order by u.create_time
        ) t, (select @row:=0) r
    </select>

    <select id="selectSaleYearReportOrder"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.SaleYearReportVO"
            parameterType="java.lang.Integer">

        select
            t.user_id as userId,
            t.role_type as roleType,
            t.order_total_count as orderTotalCount,
            t.order_total_point as orderTotalPoint,
            t.order_total_count_prepay as orderTotalCountPrepay,
            t.order_total_count_afterpay as orderTotalCountAfterpay,
            t.order_total_count_mix as orderTotalCountMix,
            t.name as name,
            @row:=@row+1 AS saleRanking
        from (
            select
                t1.user_id,
                t1.role_type,
                t1.order_total_count,
                t1.order_total_count_prepay,
                t1.order_total_count_afterpay,
                t1.order_total_count_mix,
                t1.order_total_point,
                t1.name
            from (
                -- 分销员年度订单数
                select
                    u.user_id,
                    u.role_type,
                    count(*) as order_total_count,
                    count(case when oi.order_type = '01' then 1 else null end) as order_total_count_prepay,
                    count(case when oi.order_type = '00' then 1 else null end) as order_total_count_afterpay,
                    count(case when oi.order_type = '02' or oi.order_type = '03' then 1 else null end) as order_total_count_mix,
                    sum(opi.point) as order_total_point,
                    u.name
                from user_mini_program u
                join order_2c_distributor_info odi on odi.distributor_user_id = u.user_id
                join order_2c_info oi on oi.order_id = odi.order_id and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} and oi.status in (1, 3, 9, 11, 12)
                join order_2c_point_info opi on opi.distributor_user_id = odi.distributor_user_id and opi.order_id = oi.order_id
                join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
                where u.user_id is not null AND u.status = '1' AND u.role_type in ('1', '2')
                group by u.user_id

                union all
                -- 渠道商年度订单数
                select
                    u.user_id,
                    u.role_type,
                    count(*) as order_total_count,
                    count(case when oi.order_type = '01' then 1 else null end) as order_total_count_prepay,
                    count(case when oi.order_type = '00' then 1 else null end) as order_total_count_afterpay,
                    count(case when oi.order_type = '02' or oi.order_type = '03' then 1 else null end) as order_total_count_mix,
                    sum(opi.point) as order_total_point,
                    u.name
                from user_mini_program u
                join order_2c_agent_info oai on oai.agent_user_id = u.user_id
                join order_2c_info oi on oi.order_id = oai.order_id and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} and oi.status in (1, 3, 9, 11, 12)
                join order_2c_point_info opi on opi.distributor_user_id = oai.agent_user_id and opi.order_id = oi.order_id
                join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
                where u.user_id is not null AND u.status = '1' AND u.role_type = '3'
                group by u.user_id

                union all
                -- 客户经理年度订单数
                select
                    u.user_id,
                    u.role_type,
                    count(*) as order_total_count,
                    count(case when oi.order_type = '01' then 1 else null end) as order_total_count_prepay,
                    count(case when oi.order_type = '00' then 1 else null end) as order_total_count_afterpay,
                    count(case when oi.order_type = '02' or oi.order_type = '03' then 1 else null end) as order_total_count_mix,
                    0,
                    u.name
                from user_mini_program u
                join order_2c_info oi on u.user_id = oi.create_oper_user_id and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} and oi.status in (1, 3, 9, 11, 12)
                join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
                where u.user_id is not null AND u.status = '1' AND u.role_type = '4'
                group by u.user_id
            ) t1
            order by t1.order_total_count desc
        ) t, (select @row:=0) r,
        (select count(*) as user_count from user_mini_program u where u.user_id is not null AND u.status = '1' AND u.role_type != '0') as uc;
    </select>

    <select id="selectSaleYearReportOrderPrice"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.SaleYearReportVO"
            parameterType="java.lang.Integer">

        select
            t.user_id as userId,
            t.role_type as roleType,
            t.order_total_price as orderTotalPrice,
            t.name as name
        from (
            -- 分销员年度订单金额
            select
                u.user_id,
                u.role_type,
                case when oai.order_status=9 then sum((oai.sku_quantity - oi.special_after_refunds_number) * oai.atom_price * oai.atom_quantity)
                     else sum(oai.sku_quantity * oai.atom_price * oai.atom_quantity) end order_total_price,
                u.name
            from user_mini_program u
            join order_2c_distributor_info odi on odi.distributor_user_id = u.user_id
            join order_2c_info oi on oi.order_id = odi.order_id and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} and oi.status in (1, 3, 9, 11, 12)
            join order_2c_atom_info oai on oai.order_id = oi.order_id
            join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
            where u.user_id is not null AND u.status = '1' AND u.role_type in ('1', '2')
            group by u.user_id
            union all
            -- 渠道商年度订单金额
            select
                u.user_id,
                u.role_type,
                case when oai.order_status=9 then sum((oai.sku_quantity - oi.special_after_refunds_number) * oai.atom_price * oai.atom_quantity)
                     else sum(oai.sku_quantity * oai.atom_price * oai.atom_quantity) end order_total_price,
                u.name
            from user_mini_program u
            join order_2c_agent_info o2ai on o2ai.agent_user_id = u.user_id
            join order_2c_info oi on oi.order_id = o2ai.order_id and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} and oi.status in (1, 3, 9, 11, 12)
            join order_2c_atom_info oai on oai.order_id = oi.order_id
            join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
            where u.user_id is not null AND u.status = '1' AND u.role_type = '3'
            group by u.user_id
            union all
            -- 客户经理年度订单金额
            select
                u.user_id,
                u.role_type,
                case when oai.order_status=9 then sum((oai.sku_quantity - oi.special_after_refunds_number) * oai.atom_price * oai.atom_quantity)
                     else sum(oai.sku_quantity * oai.atom_price * oai.atom_quantity) end order_total_price,
                u.name
            from user_mini_program u
            join order_2c_info oi on u.user_id = oi.create_oper_user_id and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} and oi.status in (1, 3, 9, 11, 12)
            join order_2c_atom_info oai on oai.order_id = oi.order_id
            join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
            where u.user_id is not null AND u.status = '1' AND u.role_type = '4'
            group by u.user_id
        ) t
        order by t.order_total_price desc;

    </select>

    <select id="selectSaleYearReportSpu"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.SaleYearReportVO"
            parameterType="java.lang.Integer">
        -- 年度最擅长销售商品
        select
            t.user_id as userId,
            t.role_type as roleType,
            t.offering_code as bestSaleSpuCode,
            t.offering_name as bestSaleSpuName,
            max(t.spu_count) as bestSaleSpuOrderCount,
            count(t.offering_code) as spuTotalCount,
            t.name as name
        from (
            -- 分销员年度订单数
            select
                u.user_id,
                u.role_type,
                u.name,
                spu.offering_code,
                spu.offering_name as offering_name,
                count(spu.offering_code) as spu_count
            from user_mini_program u
            join order_2c_distributor_info odi on odi.distributor_user_id = u.user_id
            join order_2c_info oi on oi.order_id = odi.order_id and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} and oi.status in (1, 3, 9, 11, 12)
            join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
            where u.user_id is not null AND u.status = '1' AND u.role_type in ('1', '2')
            group by u.user_id, spu.offering_code

            union all
            -- 渠道商年度订单数
            select
                u.user_id,
                u.role_type,
                u.name,
                spu.offering_code,
                spu.offering_name as offering_name,
                count(spu.offering_code) as spu_count
            from user_mini_program u
            join order_2c_agent_info o2ai on o2ai.agent_user_id = u.user_id
            join order_2c_info oi on oi.order_id = o2ai.order_id and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} and oi.status in (1, 3, 9, 11, 12)
            join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
            where u.user_id is not null AND u.status = '1' AND u.role_type = '3'
            group by u.user_id, spu.offering_code
            union all
            -- 客户经理年度订单数
            select
                u.user_id,
                u.role_type,
                u.name,
                spu.offering_code,
                spu.offering_name as offering_name,
                count(spu.offering_code) as spu_count
            from user_mini_program u
            join order_2c_info oi on u.user_id = oi.create_oper_user_id and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} and oi.status in (1, 3, 9, 11, 12)
            join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
            where u.user_id is not null AND u.status = '1' AND u.role_type = '4'
            group by u.user_id, spu.offering_code

            order by spu_count desc
        ) t
        group by t.user_id;
    </select>

    <select id="selectSaleYearReportSpuGroup"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.SaleYearReportVO"
            parameterType="java.lang.Integer">
        -- 年度最擅长销售商品
        select
            t.user_id as userId,
            t.role_type as roleType,
            t.offering_code as bestSaleSpuCodeGroup,
            t.offering_name as bestSaleSpuNameGroup,
            max(t.spu_count) as bestSaleSpuCountGroup,
            t.name as name
        from (
            -- 分销员年度订单数
            select
                u.user_id,
                u.role_type,
                u.name,
                spu.offering_code,
                spu.offering_name as offering_name,
                count(spu.offering_code) as spu_count
            from user_mini_program u
            join order_2c_distributor_info odi on odi.distributor_user_id = u.user_id
            join order_2c_info oi on oi.order_id = odi.order_id and oi.business_code in ('SyncGrpOrderInfo', 'SyncValetOrderInfo') and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} and oi.status in (1, 3, 9, 11, 12)
            join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
            where u.user_id is not null AND u.status = '1' AND u.role_type in ('1', '2')
            group by u.user_id, spu.offering_code

            union all

            -- 渠道商年度订单数
            select
                u.user_id,
                u.role_type,
                u.name,
                spu.offering_code,
                spu.offering_name as offering_name,
                count(spu.offering_code) as spu_count
            from user_mini_program u
            join order_2c_agent_info o2ai on o2ai.agent_user_id = u.user_id
            join order_2c_info oi on oi.order_id = o2ai.order_id and oi.business_code in ('SyncGrpOrderInfo', 'SyncValetOrderInfo') and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} and oi.status in (1, 3, 9, 11, 12)
            join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
            where u.user_id is not null AND u.status = '1' AND u.role_type = '3'
            group by u.user_id, spu.offering_code

            union all

            -- 客户经理年度订单数
            select
                u.user_id,
                u.role_type,
                u.name,
                spu.offering_code,
                spu.offering_name as offering_name,
                count(spu.offering_code) as spu_count
            from user_mini_program u
            join order_2c_info oi on oi.create_oper_user_id = u.user_id and oi.business_code in ('SyncGrpOrderInfo', 'SyncValetOrderInfo') and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} and oi.status in (1, 3, 9, 11, 12)
            join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
            where u.user_id is not null AND u.status = '1' AND u.role_type = '4'
            group by u.user_id, spu.offering_code

            order by spu_count desc
        ) t
        group by t.user_id;
    </select>

    <select id="selectSaleYearReportSpuIndividual"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.SaleYearReportVO"
            parameterType="java.lang.Integer">
        -- 年度最擅长销售商品
        select
            t.user_id as userId,
            t.role_type as roleType,
            t.offering_code as bestSaleSpuCodeIndividual,
            t.offering_name as bestSaleSpuNameIndividual,
            max(t.spu_count) as bestSaleSpuCountIndividual,
            t.name as name
        from (
            -- 分销员年度订单数
            select
                u.user_id,
                u.role_type,
                u.name,
                spu.offering_code,
                spu.offering_name as offering_name,
                count(spu.offering_code) as spu_count
            from user_mini_program u
            join order_2c_distributor_info odi on odi.distributor_user_id = u.user_id
            join order_2c_info oi on oi.order_id = odi.order_id and oi.business_code = 'SyncIndividualOrderInfo' and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} and oi.status in (1, 3, 9, 11, 12)
            join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
            where u.user_id is not null AND u.status = '1' AND u.role_type in ('1', '2')
            group by u.user_id, spu.offering_code

            union all

            -- 渠道商年度订单数
            select
                u.user_id,
                u.role_type,
                u.name,
                spu.offering_code,
                spu.offering_name as offering_name,
                count(spu.offering_code) as spu_count
            from user_mini_program u
            join order_2c_agent_info o2ai on o2ai.agent_user_id = u.user_id
            join order_2c_info oi on oi.order_id = o2ai.order_id and oi.business_code = 'SyncIndividualOrderInfo' and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} and oi.status in (1, 3, 9, 11, 12)
            join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
            where u.user_id is not null AND u.status = '1' AND u.role_type = '3'
            group by u.user_id, spu.offering_code

            union all

            -- 客户经理年度订单数
            select
                u.user_id,
                u.role_type,
                u.name,
                spu.offering_code,
                spu.offering_name as offering_name,
                count(spu.offering_code) as spu_count
            from user_mini_program u
            join order_2c_info oi on oi.create_oper_user_id = u.user_id and oi.business_code = 'SyncIndividualOrderInfo' and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} and oi.status in (1, 3, 9, 11, 12)
            join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
            where u.user_id is not null AND u.status = '1' AND u.role_type = '4'
            group by u.user_id, spu.offering_code

            order by spu_count desc
        ) t
        group by t.user_id;
    </select>

    <select id="selectSaleYearReportLastOrderTime"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.SaleYearReportVO"
            parameterType="java.lang.Integer">

        select
            t.user_id as userId,
            t.role_type as roleType,
            t.name as name,
            STR_TO_DATE(t.year_latest_order_time, '%Y%m%d%H%i%s') as yearLatestOrderTime,
            STR_TO_DATE(t.year_last_order_time, '%Y%m%d%H%i%s') as yearLastOrderTime
        from (
            -- 分销员年度最晚一笔订单成交时间
            select
                u.user_id,
                u.role_type,
                u.name,
                MAX(
                    CASE
                        WHEN TIME(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) <![CDATA[ >= ]]> '00:00:00' and TIME(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) <![CDATA[ < ]]> '06:00:00'
                        THEN oi.create_time
                        WHEN TIME(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) <![CDATA[ >= ]]> '20:00:00' and TIME(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) <![CDATA[ <= ]]> '23:59:59'
                        THEN oi.create_time
                    END
                ) AS year_latest_order_time,
                MAX(oi.create_time) AS year_last_order_time
            from user_mini_program u
            join order_2c_distributor_info odi on odi.distributor_user_id = u.user_id
            join order_2c_info oi on oi.order_id = odi.order_id and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} and oi.status in (1, 3, 9, 11, 12)
            join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
            where u.user_id is not null AND u.status = '1' AND u.role_type in ('1', '2')
            group by u.user_id

            union all

            -- 渠道商年度最晚一笔订单成交时间
            select
                u.user_id,
                u.role_type,
                u.name,
                MAX(
                    CASE
                    WHEN TIME(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) <![CDATA[ >= ]]> '00:00:00' and TIME(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) <![CDATA[ < ]]> '06:00:00'
                    THEN oi.create_time
                    WHEN TIME(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) <![CDATA[ >= ]]> '20:00:00' and TIME(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) <![CDATA[ <= ]]> '23:59:59'
                    THEN oi.create_time
                END
                ) AS year_latest_order_time,
                MAX(oi.create_time) AS year_last_order_time
            from user_mini_program u
            join order_2c_agent_info o2ai on o2ai.agent_user_id = u.user_id
            join order_2c_info oi on oi.order_id = o2ai.order_id and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} and oi.status in (1, 3, 9, 11, 12)
            join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
            where u.user_id is not null AND u.status = '1' AND u.role_type = '3'
            group by u.user_id

            union all

            -- 客户经理年度最晚一笔订单成交时间
            select
                u.user_id,
                u.role_type,
                u.name,
                MAX(
                    CASE
                    WHEN TIME(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) <![CDATA[ >= ]]> '00:00:00' and TIME(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) <![CDATA[ < ]]> '06:00:00'
                    THEN oi.create_time
                    WHEN TIME(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) <![CDATA[ >= ]]> '20:00:00' and TIME(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) <![CDATA[ <= ]]> '23:59:59'
                    THEN oi.create_time
                END
                ) AS year_latest_order_time,
                MAX(oi.create_time) AS year_last_order_time
            from user_mini_program u
            join order_2c_info oi on oi.create_oper_user_id = u.user_id and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} and oi.status in (1, 3, 9, 11, 12)
            join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
            where u.user_id is not null AND u.status = '1' AND u.role_type = '4'
            group by u.user_id
        ) t
        group by t.user_id;
    </select>

    <select id="selectSaleYearReportCustomerCount"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.SaleYearReportVO"
            parameterType="java.lang.Integer">
        select count(distinct case when YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} then oi.cust_code end) as customerCount,
               count(distinct
                     case
                         when oi.business_code = 'SyncIndividualOrderInfo' and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} then oi.cust_code
                         end) as customerCountIndividual,
               count(distinct
                     case
                         when oi.business_code in ('SyncGrpOrderInfo', 'SyncValetOrderInfo') and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} then oi.cust_code
                         end) as customerCountGroup,
               count(distinct oi.cust_code) as customerTotalCount,
               u.user_id as userId
        from user_mini_program u
        join order_2c_distributor_info odi on odi.distributor_user_id = u.user_id
        join order_2c_info oi on oi.order_id = odi.order_id
        join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
        where u.user_id is not null and u.status = '1' and u.role_type in ('1', '2')
        group by u.user_id

        union all

        select count(distinct case when YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} then oi.cust_code end) as customerCount,
               count(distinct
                     case
                         when oi.business_code = 'SyncIndividualOrderInfo' and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} then oi.cust_code
                         end) as customerCountIndividual,
               count(distinct
                     case
                         when oi.business_code in ('SyncGrpOrderInfo', 'SyncValetOrderInfo') and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} then oi.cust_code
                         end) as customerCountGroup,
               count(distinct oi.cust_code) as customerTotalCount,
               u.user_id as userId
        from user_mini_program u
        join order_2c_agent_info o2ai on o2ai.agent_user_id = u.user_id
        join order_2c_info oi on oi.order_id = o2ai.order_id
        join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
        where u.user_id is not null and u.status = '1' and u.role_type = '3'
        group by u.user_id

        union all

        select count(distinct case when YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} then oi.cust_code end) as customerCount,
               count(distinct
                     case
                         when oi.business_code = 'SyncIndividualOrderInfo' and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} then oi.cust_code
                         end) as customerCountIndividual,
               count(distinct
                     case
                         when oi.business_code in ('SyncGrpOrderInfo', 'SyncValetOrderInfo') and YEAR(STR_TO_DATE(oi.create_time, '%Y%m%d%H%i%s')) = #{year} then oi.cust_code
                         end) as customerCountGroup,
               count(distinct oi.cust_code) as customerTotalCount,
               u.user_id as userId
        from user_mini_program u
        join order_2c_info oi on oi.create_oper_user_id = u.user_id
        join spu_offering_info spu on spu.offering_code = oi.spu_offering_code and spu.offering_name not like '%测试%'
        where u.user_id is not null and u.status = '1' and u.role_type = '4'
        group by u.user_id
    </select>
</mapper>