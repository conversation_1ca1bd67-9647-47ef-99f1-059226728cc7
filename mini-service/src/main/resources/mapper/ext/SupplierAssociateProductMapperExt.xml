<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.SupplierAssociateProductMapperExt">
    <resultMap id="supplierProduct" type="com.chinamobile.retail.pojo.dto.SupplierProductDTO">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="skuId" jdbcType="VARCHAR" property="skuId"/>
        <result column="spuName" jdbcType="VARCHAR" property="spuName"/>
        <result column="skuName" jdbcType="VARCHAR" property="skuName"/>
        <result column="realName" jdbcType="VARCHAR" property="realName"/>
        <result column="skuCode" jdbcType="VARCHAR" property="skuCode"/>
        <result column="price" jdbcType="BIGINT" property="price"/>
        <result column="valid" jdbcType="TINYINT" property="valid"/>
        <collection property="regions" column="skuCode"
                    ofType="com.chinamobile.retail.pojo.dto.ProductRegionDTO" select="getRegion">
            <result column="provinceName" jdbcType="VARCHAR" property="provinceName"/>
            <result column="provinceCode" jdbcType="VARCHAR" property="provinceCode"/>
            <result column="cityName" jdbcType="VARCHAR" property="cityName"/>
            <result column="cityCode" jdbcType="VARCHAR" property="cityCode"/>
        </collection>
    </resultMap>

    <select id="getRegion" parameterType="String" resultType="com.chinamobile.retail.pojo.dto.ProductRegionDTO">
        SELECT cpi.mall_code provinceCode,
               cci.mall_code cityCode,
               cpi.mall_name provinceName,
               cci.mall_name cityName
        FROM sku_release_target srt
                 LEFT JOIN contract_province_info cpi ON cpi.mall_code = srt.province_code
                 LEFT JOIN contract_city_info cci ON cci.mall_code = srt.city_code
        WHERE srt.sku_offering_code = #{sku_offering_code}
    </select>

    <select id="queryAssociateProducts" resultMap="supplierProduct">
        select
        sap.id id,
        sku.id skuId,
        spu.offering_name spuName,
        sku.offering_name skuName,
        (SELECT min(st.real_product_name) FROM standard_service st JOIN atom_std_service asr ON asr.std_service_id =
        st.id JOIN atom_offering_info atom ON asr.atom_id = atom.id WHERE atom.sku_code = sku.offering_code AND
        sku.delete_time is null) realName,
        sku.offering_code skuCode,
        (sku.point_status is not null and sku.point_status = 2) valid
        from supplier_associate_product sap
        left join sku_offering_info sku on sap.product_id = sku.offering_code
        left join spu_offering_info spu on spu.id = sku.spu_id
        where sap.supplier_id = #{supplierId} and sap.delete_time is null AND spu.delete_time is NULL AND sku.delete_time is NULL AND sku.offering_status = '1'
        <if test="key != null and key != ''">
            and (sku.offering_name like concat('%',#{key},'%') or spu.offering_name like concat('%',#{key},'%'))
        </if>
        order by sap.create_time DESC


    </select>

    <select id="queryUnassociatedProducts" parameterType="com.chinamobile.retail.pojo.param.SupplierProductQueryParam"
            resultMap="supplierProduct">
        select
        sku.id skuId,
        spu.offering_name spuName,
        sku.offering_name skuName,
        (SELECT min(st.real_product_name) FROM standard_service st JOIN atom_std_service asr ON asr.std_service_id =
        st.id JOIN atom_offering_info atom ON asr.atom_id = atom.id WHERE atom.sku_code = sku.offering_code AND
        sku.delete_time is null) realName,
        sku.offering_code skuCode,
        sku.price price
        from sku_offering_info sku
        left join supplier_associate_product sap on sap.product_id = sku.offering_code
        left join spu_offering_info spu on spu.id = sku.spu_id
        left join category_info ci on ci.spu_id = spu.id
        left join sku_release_target srt on srt.sku_offering_code = sku.offering_code
        left join contract_province_info cpi on cpi.mall_code = srt.province_code
        left join contract_city_info cci on cci.mall_code = srt.city_code
        where (sap.supplier_id is null or sap.delete_time is not null ) and spu.delete_time is null and sku.delete_time is NULL and sku.offering_status = '1'
        <if test="param.types != null ">
            and ci.offering_class in
            <foreach item="item" index="index" collection="param.types" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.key != null and param.key != ''">
            and (sku.offering_name like concat('%',#{param.key},'%') or spu.offering_name like
            concat('%',#{param.key},'%'))
        </if>

        <if test="param.provinces != null and param.cities != null ">
            and ( srt.province_code in
            <foreach item="item" index="index" collection="param.provinces" open="(" separator="," close=")">
                #{item}
            </foreach>
            or srt.city_code in
            <foreach item="item" index="index" collection="param.cities" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>

        <if test="param.provinces != null and param.cities == null ">
            and srt.province_code in
            <foreach item="item" index="index" collection="param.provinces" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.provinces == null and param.cities != null ">
            and srt.city_code in
            <foreach item="item" index="index" collection="param.cities" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by sku.offering_code


    </select>

    <select id="queryCannotDelProduct" resultType="com.chinamobile.retail.pojo.dto.SupplierProductDTO">
        select
        sap.id id,
        sku.id skuId,
        spu.offering_name spuName,
        sku.offering_name skuName,
        (SELECT min(st.real_product_name) FROM standard_service st JOIN atom_std_service asr ON asr.std_service_id =
        st.id JOIN atom_offering_info atom ON asr.atom_id = atom.id WHERE atom.sku_code = sku.offering_code AND
        sku.delete_time is null) realName,
        sku.offering_code skuCode,
        sku.price price
        from sku_offering_info sku
        left join supplier_associate_product sap on sap.product_id = sku.offering_code
        left join spu_offering_info spu on spu.id = sku.spu_id
        where sku.point_status = 2 and spu.delete_time is null and sku.delete_time is null and sku.offering_status = '1'
        and sap.id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>


    </select>

</mapper>