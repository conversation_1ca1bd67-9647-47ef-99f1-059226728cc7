<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.PointExchangeMapperExt">
  <select id="getPointExchangeDetail" parameterType="java.lang.String" resultType="com.chinamobile.retail.pojo.mapper.PointDetailDO">
  SELECT
      pe.create_time createTime,
      pe.update_time updateTime,
      pe.point,
      pe.`status`,
      ps.full_name supplierName
  FROM
      point_exchange_partner pe
      LEFT JOIN point_supplier ps ON pe.supplier_id = ps.id
  WHERE
      pe.partner_id = #{partnerId}
      <if test="supplierId != null and supplierId != ''">
          AND pe.supplier_id = #{supplierId}
      </if>
      <if test="channel != null">
          AND pe.channel = #{channel}
      </if>
  ORDER BY
      pe.update_time DESC
  </select>
</mapper>