<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.Order2cAtomInfoMapperExt">

  <select id="pageQueryOrderBackList" resultType="com.chinamobile.retail.pojo.mapper.OrderBackListDO">
  SELECT
  atom.order_id orderId,
  atom.order_status originalOrderStatus,
  atom.create_time createTime,
  spu.offering_name spuOfferingName,
  sku.offering_name skuOfferingName,
  atom.sku_quantity count,
  sum(atom.sku_quantity * atom.atom_quantity * atom.atom_price) amount,
  sum(pi.point) point,
  u.`name` partnerName,
  u.phone phone,
  IF(atom.order_status = 7 ,TRUE,FALSE) available,
  sku.offering_code skuOfferingCode
  FROM
  order_2c_point_info pi
  LEFT JOIN   order_2c_atom_info atom on pi.atom_order_id = atom.id
  LEFT JOIN spu_offering_info spu ON spu.offering_code = atom.spu_offering_code
  left JOIN sku_offering_info sku ON sku.spu_code = spu.offering_code and 	sku.offering_code = atom.sku_offering_code
  LEFT JOIN user_retail u ON u.id = pi.distributor_user_id
  where  1=1
  <if test="orderId != null and orderId != ''">
    and atom.order_id like concat ('%', #{orderId},'%')
  </if>
  <if test="startTimeStr != null and startTimeStr != ''">
    and atom.create_time <![CDATA[ >= ]]> #{startTimeStr}
  </if>
  <if test="endTimeStr != null and endTimeStr != ''">
    and atom.create_time <![CDATA[ <= ]]> #{endTimeStr}
  </if>
  <if test="orderStatus != null">
    and atom.order_status = #{orderStatus}
  </if>
  <if test="partnerName != null and partnerName != ''">
    and u.name like concat ('%', #{partnerName},'%')
  </if>
  <if test="phone != null and phone != ''">
    and u.phone like concat ('%', #{phone},'%')
  </if>
  <if test="skuOfferingName != null and skuOfferingName != ''">
    and sku.offering_name like concat ('%', #{skuOfferingName},'%')
  </if>
  <if test="spuOfferingName != null and spuOfferingName != ''">
    and spu.offering_name like concat ('%', #{spuOfferingName},'%')
  </if>
  GROUP BY atom.order_id
  ORDER BY atom.update_time desc
  limit #{start},#{pageSize}
  </select>

    <select id="pageCountOrderBackList" resultType="java.lang.Integer">
    select count(*) from (
        SELECT
        count(*)
        FROM
        order_2c_point_info pi
        LEFT JOIN order_2c_atom_info atom on pi.atom_order_id = atom.id
        LEFT JOIN spu_offering_info spu ON spu.offering_code = atom.spu_offering_code
        left JOIN sku_offering_info sku ON sku.spu_code = spu.offering_code and sku.offering_code = atom.sku_offering_code
        LEFT JOIN user_retail u ON u.id = pi.distributor_user_id
        where 1=1
        <if test="orderId != null and orderId != ''">
            and atom.order_id like concat ('%', #{orderId},'%')
        </if>
        <if test="startTimeStr != null and startTimeStr != ''">
            and atom.create_time <![CDATA[ >= ]]> #{startTimeStr}
        </if>
        <if test="endTimeStr != null and endTimeStr != ''">
            and atom.create_time <![CDATA[ <= ]]> #{endTimeStr}
        </if>
        <if test="orderStatus != null">
            and atom.order_status = #{orderStatus}
        </if>
        <if test="partnerName != null and partnerName != ''">
            and u.name like concat ('%', #{partnerName},'%')
        </if>
        <if test="phone != null and phone != ''">
            and u.phone like concat ('%', #{phone},'%')
        </if>
        <if test="skuOfferingName != null and skuOfferingName != ''">
            and sku.offering_name like concat ('%', #{skuOfferingName},'%')
        </if>
        <if test="spuOfferingName != null and spuOfferingName != ''">
            and spu.offering_name like concat ('%', #{spuOfferingName},'%')
        </if>
        GROUP BY atom.order_id
        )temp
    </select>

    <select id="getOrderExportList" resultType="com.chinamobile.retail.pojo.mapper.OrderExportListDO">
    SELECT
    atom.order_id orderId,
    atom.order_status originalOrderStatus,
    atom.create_time createTime,
    spu.offering_name spuOfferingName,
    sku.offering_name skuOfferingName,
    atom.sku_quantity count,
    sum(atom.sku_quantity * atom.atom_quantity * atom.atom_price)/1000 amount,
    sum(pi.point)/1000 point,
    u.`name` partnerName,
    u.phone phone,
    IF(atom.order_status = 7 ,TRUE,FALSE) available,
    atom.sku_price/1000 price,
    u.province partnerProvince,
    u.city partnerCity,
    ps.full_name pointSupplier
    FROM
    order_2c_point_info pi
    LEFT JOIN   order_2c_atom_info atom on pi.atom_order_id = atom.id
    LEFT JOIN spu_offering_info spu ON spu.offering_code = atom.spu_offering_code
    left JOIN sku_offering_info sku ON sku.spu_code = spu.offering_code and 	sku.offering_code = atom.sku_offering_code
    LEFT JOIN user_retail u ON u.id = pi.distributor_user_id
    LEFT JOIN supplier_associate_product sap ON sap.product_id = sku.offering_code AND sap.delete_time is NULL
    LEFT JOIN point_supplier ps ON ps.id = sap.supplier_id
    where 1=1
    <if test="orderId != null and orderId != ''">
        and atom.order_id like concat ('%', #{orderId},'%')
    </if>
    <if test="startTimeStr != null and startTimeStr != ''">
        and atom.create_time <![CDATA[ >= ]]> #{startTimeStr}
    </if>
    <if test="endTimeStr != null and endTimeStr != ''">
        and atom.create_time <![CDATA[ <= ]]> #{endTimeStr}
    </if>
    <if test="orderStatus != null">
        and atom.order_status = #{orderStatus}
    </if>
    <if test="partnerName != null and partnerName != ''">
        and u.name like concat ('%', #{partnerName},'%')
    </if>
    <if test="phone != null and phone != ''">
        and u.phone like concat ('%', #{phone},'%')
    </if>
    <if test="skuOfferingName != null and skuOfferingName != ''">
        and sku.offering_name like concat ('%', #{skuOfferingName},'%')
    </if>
    <if test="spuOfferingName != null and spuOfferingName != ''">
        and spu.offering_name like concat ('%', #{spuOfferingName},'%')
    </if>
    GROUP BY atom.order_id
    ORDER BY atom.update_time desc
    </select>

    <select id="getExportDistributeOrderList" resultType="com.chinamobile.retail.pojo.mapper.ExportDistributeOrderDO">
    SELECT
        oi.create_oper_code createOperCode,
        oi.employee_num employeeNum,
        oi.cust_mg_name custMgName,
        oi.cust_mg_phone custMgPhone,
        oi.create_time createTime,
        oi.order_id orderId,
        di.distributor_phone distributorPhone,
        di.distributor_level distributorLevel
    FROM
        order_2c_info oi
    JOIN order_2c_distributor_info di ON oi.order_id = di.order_id
    WHERE 1=1
    <if test="startTimeStr != null and startTimeStr != ''">
        and oi.create_time <![CDATA[ >= ]]> #{startTimeStr}
    </if>
    <if test="endTimeStr != null and endTimeStr != ''">
        and oi.create_time <![CDATA[ <= ]]> #{endTimeStr}
    </if>
    ORDER BY oi.create_time DESC
    </select>

    <select id="selectOrderSince2024"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.OrderThisYearVO">
        select distinct
            oi.order_id as orderId,
            oai.id as atomOrderId,
            case
                when o2ai.id is not null then o2ai.agent_user_id
                when odi.id is not null then odi.distributor_user_id
                else oi.create_oper_user_id
                end as userId,
            oai.sku_price * oai.sku_quantity as amount,
            sap.supplier_id as supplierId,
            str_to_date(oi.create_time, '%Y%m%d%H%i%s') as orderCreateTime
        from order_2c_info oi
        inner join order_2c_atom_info oai on oi.order_id = oai.order_id
        left join order_2c_agent_info o2ai on o2ai.order_id = oi.order_id
        left join order_2c_distributor_info odi on odi.order_id = oi.order_id
        left join supplier_associate_product sap on sap.product_id = oai.sku_offering_code
        where str_to_date(oi.create_time, '%Y%m%d%H%i%s') >= str_to_date('20240101000000', '%Y%m%d%H%i%s') and sap.delete_time is null
    </select>

</mapper>
