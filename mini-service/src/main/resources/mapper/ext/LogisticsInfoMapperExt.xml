<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.LogisticsInfoMapperExt">

    <select id="getLogisticsInfo"
            resultType="com.chinamobile.retail.pojo.vo.aics.LogisticsVO">
        select
            li.logis_code as logisCode,
            li.supplier_name as supplierName,
            oi.contact_phone as contactPhone
        from logistics_info li
        left join order_2c_info oi on oi.order_id = li.order_id
        where li.order_atom_info_id = #{orderAtomInfoId}
        limit 1
    </select>

</mapper>
