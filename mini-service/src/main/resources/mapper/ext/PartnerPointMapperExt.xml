<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.PartnerPointMapperExt">
    <select id="queryPartnerPointList" parameterType="com.chinamobile.retail.pojo.param.PartnerPointQueryParam"
            resultType="com.chinamobile.retail.pojo.dto.PartnerPointItemDTO">
        select
        u.province province,
        u.city city,
        u.province_code provinceCode,
        u.city_code cityCode,
        u.role_type role,
        u.name name,
        u.phone phone,
        u.id partnerId,
        ps.full_name supplier,
        ps.id supplierId,
        pp.total total,
        pp.available available,
        pp.paying paying,
        pp.redeemed redeemed
        from user_retail u
        left join partner_point pp on u.id = pp.partner_id
        left join point_supplier ps on ps.id = pp.supplier_id
        <where>
            <if test="param.searchKey != null and param.searchKey != ''">
                and ( u.name like concat('%',#{param.searchKey},'%') or u.phone like concat('%',#{param.searchKey},'%') )
            </if>

            <if test="param.provinces != null and param.cities != null ">
                and ( u.province_code in
                <foreach item="item" index="index" collection="param.provinces" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or u.city_code in
                <foreach item="item" index="index" collection="param.cities" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )

            </if>

            <if test="param.provinces != null and param.cities == null ">
                and u.province_code in
                <foreach item="item" index="index" collection="param.provinces" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.provinces == null and param.cities != null ">
                and u.city_code in
                <foreach item="item" index="index" collection="param.cities" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.roleTypes != null ">
                and u.role_type in
                <foreach item="item" index="index" collection="param.roleTypes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.suppliers != null ">
                and pp.supplier_id in
                <foreach item="item" index="index" collection="param.suppliers" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="param.availablePoint != null and param.availablePoint == true">
                and pp.available <![CDATA[ > ]]> 0
            </if>
            <if test="param.channel != null ">
                and (pp.channel = #{param.channel} or pp.channel is null)
            </if>
           
        </where>

    </select>

    <update id="addAvailablePoint">
        UPDATE partner_point
        SET available = IFNULL(available,0) + #{orderPoint},update_time = now()
        WHERE partner_id = #{partnerId} AND supplier_id = #{supplierId} AND channel = #{channel}
    </update>

    <select id="getMinAvailablePoint" resultType="java.lang.Long">
        SELECT
        min( available )
        FROM
        partner_point
        WHERE
        supplier_id = #{supplierId}
        AND partner_id IN
        <foreach close=")" collection="partnerIdList" item="listItem" open="(" separator=",">
            #{listItem}
        </foreach>
    </select>

    <select id="getSumAvailablePoint" resultType="java.lang.Long">
        SELECT
        sum( available )
        FROM
        partner_point
        WHERE
        supplier_id = #{supplierId}
        AND partner_id IN
        <foreach close=")" collection="partnerIdList" item="listItem" open="(" separator=",">
            #{listItem}
        </foreach>
    </select>

    <select id="getAvailablePoint" resultType="java.lang.Long">
        SELECT available
        FROM partner_point
        WHERE supplier_id = #{supplierId}
          AND partner_id = #{partnerId}
    </select>

    <select id="getAllPartnerIdBySupplier"  resultType="java.lang.String">
    SELECT partner_id FROM partner_point WHERE available <![CDATA[ > ]]> 0 and supplier_id = #{supplierId} and channel = #{channel}
    </select>
    
    <select id="getPartnerStatistics" resultType="com.chinamobile.retail.pojo.mapper.PartnerStatisticsDO">
      SELECT
    (
        SELECT
        sum( atom.sku_quantity * atom.atom_quantity * atom.atom_price )
        FROM
        order_2c_atom_info atom
        JOIN order_2c_point_info point ON atom.id = point.atom_order_id
        WHERE
        point.distributor_user_id = #{userId}
        AND atom.order_status != 8
    ) orderAmount,
    (SELECT count(order_id)	FROM ( SELECT oc.order_id FROM order_2c_point_info oc  LEFT JOIN order_2c_atom_info atom ON atom.id = oc.atom_order_id WHERE distributor_user_id = #{userId} AND atom.order_status != 8 GROUP BY order_id)temp) orderCount,
    sum(pp.redeemed) totalRedeemedPoint,
    ( SELECT SUM(point) FROM point_exchange_partner WHERE partner_id = #{userId} AND create_time <![CDATA[ >= ]]> #{startTime} AND create_time <![CDATA[ <= ]]>  #{endTime} AND `status` = 2 ) lastMonthRedeemedPoint
    FROM
    partner_point pp
    WHERE
    pp.partner_id = #{userId}
    AND pp.channel = #{channel}
    GROUP BY pp.partner_id
    </select>

</mapper>