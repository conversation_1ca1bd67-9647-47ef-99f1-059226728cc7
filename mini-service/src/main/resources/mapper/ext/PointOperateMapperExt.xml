<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.PointOperateMapperExt">
    <select id="getPointInAndOutDetailMiniprogramFront" resultType="com.chinamobile.retail.pojo.dto.MiniprogramPointDetailDTO" >
        select
        po.create_time as createTime,
        po.type,
        po.point,
        ps.full_name as supplierName,
        mpa.name as activityName,
        mpa.start_time as activityStartTime,
        mpa.stop_time as activityEndTime
        from point_operate po
        left join mini_program_activity mpa on po.activity_id=mpa.id
        LEFT JOIN point_supplier ps ON po.supplier_id = ps.id
        WHERE po.channel=1
        <if test="partnerId!=null">
            and po.user_id=#{partnerId}
        </if>
        ORDER BY po.create_time DESC
    </select>



</mapper>