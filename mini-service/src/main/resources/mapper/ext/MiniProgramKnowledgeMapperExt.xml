<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.MiniProgramKnowledgeMapperExt">

    <!-- 分页查询小程序知识库列表 -->
    <select id="pageKnowledgeList"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageKnowledgeListParam"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.PageKnowledgeInfoVO">
        select
        m.id,
        m.topics,
        m.content,
        m.is_popular as isPopular,
        m.release_status as releaseStatus,
        m.audit_status as auditStatus,
        m.creator,
        m.create_time as createTime,
        u.name as creatorName
        from mini_program_knowledge m
        left join user u on u.user_id = m.creator
        <where>
            <if test="param.topics != null and param.topics != ''">
                and m.topics like concat('%',#{param.topics}, '%')
            </if>

            <if test="param.creator != null and param.creator != ''">
                and u.name = #{param.creator}
            </if>

            <if test="param.isPopular != null">
                and m.is_popular = #{param.isPopular}
            </if>
            <if test="param.releaseStatus != null">
                and m.release_status = #{param.releaseStatus}
            </if>
            <if test="param.auditStatusList != null and param.auditStatusList.size() != 0">
                and m.audit_status in
                <foreach collection="param.auditStatusList" item="auditStatus" index="index" open="(" close=")" separator=",">
                    #{auditStatus}
                </foreach>
            </if>
            <if test="param.searchkey != null and param.searchkey!=''">
                and (m.topics like concat('%',#{param.searchkey}, '%') or m.content like concat('%',#{param.searchkey}, '%'))
            </if>

        </where>
        order by m.create_time desc
    </select>

    <!-- 管理后台查询小程序活动总数 -->
    <select id="countInfoList"
            parameterType="com.chinamobile.retail.pojo.param.miniprogram.PageInfoListParam"
            resultType="java.lang.Long">
        select count(*)
        from (
            select a.id
        from mini_program_info a
        left join user u on u.user_id = a.create_uid
            <where>
                <if test="status != null">
                    and a.status = #{status}
                </if>

                <if test="infoType != null and infoType != ''">
                    and a.info_type like concat('%',#{infoType}, '%')
                </if>

                <if test="keyWord != null and keyWord != ''">
                    and ( a.name like concat('%',#{keyWord}, '%') or a.key_words like concat('%',#{keyWord}, '%') )
                </if>
                <if test="createUserName != null and createUserName != ''">
                    and u.name like concat('%',#{createUserName}, '%')
                </if>
                <if test="auditedStatusList != null and auditedStatusList.size() != 0">
                    and a.audit_status in
                    <foreach collection="auditedStatusList" item="auditStatus" index="index" open="(" close=")" separator=",">
                        #{auditStatus}
                    </foreach>
                </if>
            </where>

        ) t
    </select>

    <!-- 分页查询小程序资讯列表 -->
    <select id="getInfoSpuList" parameterType="String"
            resultType="com.chinamobile.retail.pojo.vo.miniprogram.InfoSpuItemVO">
        select
        a.spu_offering_code as spuCode,
        s.offering_name as spuName,
        s.img_url as spuImgUrl,
        sc.core_component_name as coreComponentName,
        sc.core_component_img as coreComponentImg
        from mini_program_info_spu_code a
        left join spu_offering_info s on s.offering_code = a.spu_offering_code
        left join spu_core_component sc on sc.spu_code = a.spu_offering_code
        where a.info_id = #{infoId} and (s.delete_time is null or
        (select count(s1.id)  from spu_offering_info s1 where s1.offering_code = a.spu_offering_code)=1)
    </select>

    <select id="getUserName" parameterType="String" resultType="String">
        select
            u.name
        from user u
        where u.user_id = #{userId}
    </select>

</mapper>
