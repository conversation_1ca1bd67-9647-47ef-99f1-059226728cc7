<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.ext.UserRetailMapperExt">

    <select id="queryUserInfoList"
            resultType="com.chinamobile.retail.pojo.mapper.UserInfoListDO">
        SELECT
        id id,
        phone phone,
        province province,
        city city,
        work_num workNum,
        name name,
        role_type roleType,
        case role_type when 3 then work_num
                       else cust_code
        end uniqueCode,
        DATE_FORMAT(reg_time,'%Y/%m/%d %H:%i:%s') regTime,
        DATE_FORMAT(latest_login_time,'%Y/%m/%d %H:%i:%s') latestLoginTime
        FROM
        user_retail
        where 1= 1
        <if test="userDataQuery.provinces != null and userDataQuery.cities != null ">
            and ( province_code in
            <foreach item="item" index="index" collection="userDataQuery.provinces" open="(" separator="," close=")">
                #{item}
            </foreach>
            or city_code in
            <foreach item="item" index="index" collection="userDataQuery.cities" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="userDataQuery.provinces != null and userDataQuery.cities == null ">
            and province_code in
            <foreach item="item" index="index" collection="userDataQuery.provinces" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="userDataQuery.provinces == null and userDataQuery.cities != null ">
            and city_code in
            <foreach item="item" index="index" collection="userDataQuery.cities" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="pageQueryUserInfoList"
            resultType="com.chinamobile.retail.pojo.mapper.UserInfoListDO">
        SELECT
        id id,
        phone phone,
        province province,
        city city,
        work_num workNum,
        name name,
        role_type roleType,
        case role_type when 3 then work_num
                       else cust_code
        end uniqueCode,
--         case
--         when role_type = 0 then work_num
--         when role_type = 1 then recommend_code
--         when role_type = 2 then recommend_code
--         else 'unknown'
--         end as uniqueCode,
        DATE_FORMAT(reg_time,'%Y/%m/%d %H:%i:%s') regTime,
        DATE_FORMAT(latest_login_time,'%Y/%m/%d %H:%i:%s') latestLoginTime
        FROM
        user_retail
        where 1= 1
<!--        <if test="userDataQuery.provinces != null and userDataQuery.name != ''">-->
<!--            and name like '%${userDataQuery.name}%'-->
<!--        </if>-->
        <if test="userDataQuery.provinces != null and userDataQuery.cities != null ">
            and ( province_code in
            <foreach item="item" index="index" collection="userDataQuery.provinces" open="(" separator="," close=")">
                #{item}
            </foreach>
            or city_code in
            <foreach item="item" index="index" collection="userDataQuery.cities" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="userDataQuery.provinces != null and userDataQuery.cities == null ">
            and province_code in
            <foreach item="item" index="index" collection="userDataQuery.provinces" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="userDataQuery.provinces == null and userDataQuery.cities != null ">
            and city_code in
            <foreach item="item" index="index" collection="userDataQuery.cities" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="countUsers"
            resultType="java.lang.Long">
        SELECT
            count(*)
        FROM
            user_retail
        where 1= 1
        <if test="userDataQuery.authStatus != null and userDataQuery.authStatus != ''">
            and auth_status like '%${userDataQuery.authStatus}%'
        </if>
        <if test="userDataQuery.name != null and userDataQuery.name != ''">
            and name like '%${userDataQuery.name}%'
        </if>
        <if test="userDataQuery.workNum != null and userDataQuery.workNum != ''">
            and order_province like '%${userDataQuery.workNum}%'
        </if>
        <if test="userDataQuery.phone != null and userDataQuery.phone != ''">
            and order_city like '%${userDataQuery.phone}%'
        </if>
    </select>

</mapper>