<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.chinamobile.retail.dao.PointExchangeMapper">
  <resultMap id="BaseResultMap" type="com.chinamobile.retail.pojo.entity.PointExchange">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 17:42:11 CST 2023. by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="operate_user_id" jdbcType="VARCHAR" property="operateUserId" />
    <result column="supplier_id" jdbcType="VARCHAR" property="supplierId" />
    <result column="partner_count" jdbcType="INTEGER" property="partnerCount" />
    <result column="success_partner_count" jdbcType="INTEGER" property="successPartnerCount" />
    <result column="point" jdbcType="BIGINT" property="point" />
    <result column="success_point" jdbcType="BIGINT" property="successPoint" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 17:42:11 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 17:42:11 CST 2023. by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 17:42:11 CST 2023. by MyBatis Generator, do not modify.
    -->
    id, batch_no, operate_user_id, supplier_id, partner_count, success_partner_count, 
    point, success_point, status, fail_reason, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.chinamobile.retail.pojo.entity.PointExchangeExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 17:42:11 CST 2023. by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from point_exchange
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 17:42:11 CST 2023. by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from point_exchange
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 17:42:11 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from point_exchange
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.chinamobile.retail.pojo.entity.PointExchangeExample">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 17:42:11 CST 2023. by MyBatis Generator, do not modify.
    -->
    delete from point_exchange
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.chinamobile.retail.pojo.entity.PointExchange">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 17:42:11 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into point_exchange (id, batch_no, operate_user_id, 
      supplier_id, partner_count, success_partner_count, 
      point, success_point, status, 
      fail_reason, create_time, update_time
      )
    values (#{id,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, #{operateUserId,jdbcType=VARCHAR}, 
      #{supplierId,jdbcType=VARCHAR}, #{partnerCount,jdbcType=INTEGER}, #{successPartnerCount,jdbcType=INTEGER}, 
      #{point,jdbcType=BIGINT}, #{successPoint,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, 
      #{failReason,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.chinamobile.retail.pojo.entity.PointExchange">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 17:42:11 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into point_exchange
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="operateUserId != null">
        operate_user_id,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="partnerCount != null">
        partner_count,
      </if>
      <if test="successPartnerCount != null">
        success_partner_count,
      </if>
      <if test="point != null">
        point,
      </if>
      <if test="successPoint != null">
        success_point,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="failReason != null">
        fail_reason,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="operateUserId != null">
        #{operateUserId,jdbcType=VARCHAR},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=VARCHAR},
      </if>
      <if test="partnerCount != null">
        #{partnerCount,jdbcType=INTEGER},
      </if>
      <if test="successPartnerCount != null">
        #{successPartnerCount,jdbcType=INTEGER},
      </if>
      <if test="point != null">
        #{point,jdbcType=BIGINT},
      </if>
      <if test="successPoint != null">
        #{successPoint,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="failReason != null">
        #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.chinamobile.retail.pojo.entity.PointExchangeExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 17:42:11 CST 2023. by MyBatis Generator, do not modify.
    -->
    select count(*) from point_exchange
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 17:42:11 CST 2023. by MyBatis Generator, do not modify.
    -->
    update point_exchange
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.operateUserId != null">
        operate_user_id = #{record.operateUserId,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierId != null">
        supplier_id = #{record.supplierId,jdbcType=VARCHAR},
      </if>
      <if test="record.partnerCount != null">
        partner_count = #{record.partnerCount,jdbcType=INTEGER},
      </if>
      <if test="record.successPartnerCount != null">
        success_partner_count = #{record.successPartnerCount,jdbcType=INTEGER},
      </if>
      <if test="record.point != null">
        point = #{record.point,jdbcType=BIGINT},
      </if>
      <if test="record.successPoint != null">
        success_point = #{record.successPoint,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.failReason != null">
        fail_reason = #{record.failReason,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 17:42:11 CST 2023. by MyBatis Generator, do not modify.
    -->
    update point_exchange
    set id = #{record.id,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      operate_user_id = #{record.operateUserId,jdbcType=VARCHAR},
      supplier_id = #{record.supplierId,jdbcType=VARCHAR},
      partner_count = #{record.partnerCount,jdbcType=INTEGER},
      success_partner_count = #{record.successPartnerCount,jdbcType=INTEGER},
      point = #{record.point,jdbcType=BIGINT},
      success_point = #{record.successPoint,jdbcType=BIGINT},
      status = #{record.status,jdbcType=INTEGER},
      fail_reason = #{record.failReason,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.chinamobile.retail.pojo.entity.PointExchange">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 17:42:11 CST 2023. by MyBatis Generator, do not modify.
    -->
    update point_exchange
    <set>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="operateUserId != null">
        operate_user_id = #{operateUserId,jdbcType=VARCHAR},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=VARCHAR},
      </if>
      <if test="partnerCount != null">
        partner_count = #{partnerCount,jdbcType=INTEGER},
      </if>
      <if test="successPartnerCount != null">
        success_partner_count = #{successPartnerCount,jdbcType=INTEGER},
      </if>
      <if test="point != null">
        point = #{point,jdbcType=BIGINT},
      </if>
      <if test="successPoint != null">
        success_point = #{successPoint,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="failReason != null">
        fail_reason = #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.chinamobile.retail.pojo.entity.PointExchange">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 17:42:11 CST 2023. by MyBatis Generator, do not modify.
    -->
    update point_exchange
    set batch_no = #{batchNo,jdbcType=VARCHAR},
      operate_user_id = #{operateUserId,jdbcType=VARCHAR},
      supplier_id = #{supplierId,jdbcType=VARCHAR},
      partner_count = #{partnerCount,jdbcType=INTEGER},
      success_partner_count = #{successPartnerCount,jdbcType=INTEGER},
      point = #{point,jdbcType=BIGINT},
      success_point = #{successPoint,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      fail_reason = #{failReason,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 17:42:11 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into point_exchange
    (id, batch_no, operate_user_id, supplier_id, partner_count, success_partner_count, 
      point, success_point, status, fail_reason, create_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.batchNo,jdbcType=VARCHAR}, #{item.operateUserId,jdbcType=VARCHAR}, 
        #{item.supplierId,jdbcType=VARCHAR}, #{item.partnerCount,jdbcType=INTEGER}, #{item.successPartnerCount,jdbcType=INTEGER}, 
        #{item.point,jdbcType=BIGINT}, #{item.successPoint,jdbcType=BIGINT}, #{item.status,jdbcType=INTEGER}, 
        #{item.failReason,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}
        )
    </foreach>
  </insert>
  <insert id="batchInsertSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated  This element was generated on Tue Apr 18 17:42:11 CST 2023. by MyBatis Generator, do not modify.
    -->
    insert into point_exchange (
    <foreach collection="selective" item="column" separator=",">
      ${column.escapedColumnName}
    </foreach>
    )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <foreach collection="selective" item="column" separator=",">
        <if test="'id'.toString() == column.value">
          #{item.id,jdbcType=VARCHAR}
        </if>
        <if test="'batch_no'.toString() == column.value">
          #{item.batchNo,jdbcType=VARCHAR}
        </if>
        <if test="'operate_user_id'.toString() == column.value">
          #{item.operateUserId,jdbcType=VARCHAR}
        </if>
        <if test="'supplier_id'.toString() == column.value">
          #{item.supplierId,jdbcType=VARCHAR}
        </if>
        <if test="'partner_count'.toString() == column.value">
          #{item.partnerCount,jdbcType=INTEGER}
        </if>
        <if test="'success_partner_count'.toString() == column.value">
          #{item.successPartnerCount,jdbcType=INTEGER}
        </if>
        <if test="'point'.toString() == column.value">
          #{item.point,jdbcType=BIGINT}
        </if>
        <if test="'success_point'.toString() == column.value">
          #{item.successPoint,jdbcType=BIGINT}
        </if>
        <if test="'status'.toString() == column.value">
          #{item.status,jdbcType=INTEGER}
        </if>
        <if test="'fail_reason'.toString() == column.value">
          #{item.failReason,jdbcType=VARCHAR}
        </if>
        <if test="'create_time'.toString() == column.value">
          #{item.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="'update_time'.toString() == column.value">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </if>
      </foreach>
      )
    </foreach>
  </insert>
</mapper>