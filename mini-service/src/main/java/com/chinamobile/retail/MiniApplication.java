package com.chinamobile.retail;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * created by <PERSON><PERSON><PERSON><PERSON> on 2022/8/31 10:47
 */
@EnableFeignClients(basePackages = {"com.chinamobile"})
@EnableDiscoveryClient
@EnableApolloConfig
@EnableScheduling
//这里使用scanBasePackages,是为了将全局异常处理器包含进来。使用services-utils和本服务共同的包名前缀
@SpringBootApplication(scanBasePackages = {"com.chinamobile"})
@MapperScan(basePackages = "com.chinamobile.retail.dao")
public class MiniApplication {
    public static void main(String[] args) {
        SpringApplication.run(MiniApplication.class,args);
    }
}
