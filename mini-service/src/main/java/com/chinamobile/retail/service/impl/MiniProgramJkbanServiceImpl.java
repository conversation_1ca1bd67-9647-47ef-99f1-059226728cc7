package com.chinamobile.retail.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.common.Constant;
import com.chinamobile.iot.sc.common.utils.BaseServiceUtils;
import com.chinamobile.iot.sc.exceptions.BusinessException;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.retail.config.RestTemplateConfig;
import com.chinamobile.retail.constant.StatusConstant;
import com.chinamobile.retail.dao.JkbanOrderMapper;
import com.chinamobile.retail.dao.JkbanProvinceInfoMapper;
import com.chinamobile.retail.dao.JkbanQuestionInfoMapper;
import com.chinamobile.retail.dao.ext.MiniProgramJkbanMapperExt;
import com.chinamobile.retail.pojo.entity.*;
import com.chinamobile.retail.pojo.param.miniprogram.CreateJkbanParam;
import com.chinamobile.retail.pojo.param.miniprogram.JkbanListParam;
import com.chinamobile.retail.pojo.vo.miniprogram.JkbanDetailVO;
import com.chinamobile.retail.pojo.vo.miniprogram.JkbanListVO;
import com.chinamobile.retail.pojo.vo.miniprogram.JkbanProvinceListVO;
import com.chinamobile.retail.request.jkban.CreateJkbanRequest;
import com.chinamobile.retail.response.jkban.CreateJkbanReponse;
import com.chinamobile.retail.response.jkban.JkbanDetailReponse;
import com.chinamobile.retail.response.jkban.JkbanTokenReponse;
import com.chinamobile.retail.service.IMiniProgramJkbanService;
import com.chinamobile.retail.util.IOTEncodeUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.chinamobile.iot.sc.exceptions.BaseErrorConstant.PARAM_ERROR;

@Slf4j
@Service
public class MiniProgramJkbanServiceImpl implements IMiniProgramJkbanService {
    @Value("${jkban.host: http://************:33351}")
    private String jkbanHost;

    @Value("${jkban.appId: 999036test}")
    private String jkbanAppId;

    @Value("${jkban.pwd: Tb2u2I9upzG}")
    private String jkbanPwd;

    @Value("${iot.encodeKey}")
    private String encryptKey;

    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private JkbanOrderMapper jkbanOrderMapper;

    @Resource
    private JkbanProvinceInfoMapper jkbanProvinceInfoMapper;

    @Resource
    private JkbanQuestionInfoMapper jkbanQuestionInfoMapper;

    @Resource
    private MiniProgramJkbanMapperExt miniProgramJkbanMapperExt;

    @Override
    public BaseAnswer<PageInfo<JkbanListVO>> getJkbanList(JkbanListParam param, LoginIfo4Redis loginIfo4Redis) {
        PageHelper.startPage(param.getPage(), param.getPageSize());

        param.setUserId(loginIfo4Redis.getUserId());
        param.setContactPhone(IOTEncodeUtils.encryptIOTMessage(param.getContactPhone(), encryptKey));
        List<JkbanListVO> jkbanList = miniProgramJkbanMapperExt.getJkbanList(param);
        PageInfo<JkbanListVO> pageInfo = new PageInfo<>(jkbanList);
        BaseAnswer<PageInfo<JkbanListVO>> result = new BaseAnswer<>();
        result.setData(pageInfo);
        return result;
    }

    @Override
    public BaseAnswer<Void> createJkban(CreateJkbanParam param, LoginIfo4Redis loginIfo4Redis) {
        BaseAnswer<Void> baseAnswer = new BaseAnswer<>();
        CreateJkbanRequest createJkbanRequest = new CreateJkbanRequest();
        BeanUtils.copyProperties(param,createJkbanRequest);

        String id = BaseServiceUtils.getId();
        String transid = jkbanAppId + 
                         new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + 
                         id.substring(0, 8);
        String token = getJkbanToken();
//        try {
//            MessageDigest digest = MessageDigest.getInstance("SHA-256");
//            byte[] hash = digest.digest((jkbanAppId + jkbanPwd + transid).getBytes());
//            token = Hex.encodeHexString(hash);
//            log.info("jkban生成的token：{}", token);
//        } catch (NoSuchAlgorithmException e) {
//            throw new RuntimeException("SHA-256 algorithm not found", e);
//        }

        createJkbanRequest.setTransid(transid);
        createJkbanRequest.setToken(token);
        createJkbanRequest.setPhone(param.getContactPhone());
        createJkbanRequest.setGovmFaultProvCode(param.getProvinceCode());
        createJkbanRequest.setRegionId(param.getCityCode());
        createJkbanRequest.setServiceNumber(param.getUserPhone());
        createJkbanRequest.setBizCntt(param.getQuestionDesc());
        createJkbanRequest.setStartTime(param.getQuestionTime());

        if(StringUtils.isNotEmpty(param.getRegionCode())){
            createJkbanRequest.setGovmFaultDistrtId(param.getRegionCode());
        }

        log.info("jkban生成的body: {}", JSONObject.toJSONString(createJkbanRequest));


        // 请求即刻办
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("content-type", "application/json;charset=utf-8");

            HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(createJkbanRequest), headers);
            String requestUrl = jkbanHost + "/v5/inner/insert/sim-order";
//            String requestUrl = jkbanHost + "/v5/inner/insert/sim-order" + "?transid=" + transid + "&token=" + token;
            log.info("请求jkban在线派单接口url:{}",JSON.toJSONString(requestUrl));
            log.info("请求jkban在线派单接口request:{}",JSON.toJSONString(requestEntity));
            RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
            ResponseEntity<String> response = restTemplateHttps.postForEntity(requestUrl, requestEntity, String.class);
            log.info("请求jkban在线派单接口response:{}",JSON.toJSONString(response));
            CreateJkbanReponse response1 = JSON.parseObject(response.getBody(), CreateJkbanReponse.class);
            if(!response1.getStatus().equals("0")){
                log.info("请求jkban在线派单接口失败:{}", response1.getMessage());
                baseAnswer.setMessage(response1.getMessage());
                baseAnswer.setStateCode("10004");
            }else{
                JkbanOrder jkbanOrder = new JkbanOrder();
                BeanUtils.copyProperties(param,jkbanOrder);
                jkbanOrder.setId(id);
                jkbanOrder.setOrderId(response1.getResult().get(0).getWrkfmShowSwftno());
                jkbanOrder.setOrderProviceCode(response1.getResult().get(0).getProvCode());
                jkbanOrder.setContactPhone(IOTEncodeUtils.encryptIOTMessage(param.getContactPhone(), encryptKey));
                jkbanOrder.setUserPhone(IOTEncodeUtils.encryptIOTMessage(param.getUserPhone(), encryptKey));
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                jkbanOrder.setQuestionTime(sdf.parse(param.getQuestionTime()));
                jkbanOrder.setOrderStatus("0");
                jkbanOrder.setUserId(loginIfo4Redis.getUserId());
                jkbanOrder.setCreateTime(new Date());
                jkbanOrder.setUpdateTime(new Date());

                jkbanOrderMapper.insertSelective(jkbanOrder);
            }
        }catch (Exception e) {
            throw new BusinessException(PARAM_ERROR, e.getMessage());
        }

        return baseAnswer;
    }

    @Override
    public BaseAnswer<JkbanDetailVO> getJkbanDetail(String id) {

        JkbanOrder jkbanOrder = jkbanOrderMapper.selectByPrimaryKey(id);
        if(jkbanOrder == null){
            throw new BusinessException(PARAM_ERROR, "对应订单不存在");
        }

        String transid = jkbanAppId +
                new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) +
                id.substring(0, 8);
        String token = getJkbanToken();
//        try {
//            MessageDigest digest = MessageDigest.getInstance("SHA-256");
//            byte[] hash = digest.digest((jkbanAppId + jkbanPwd + transid).getBytes());
//            token = Hex.encodeHexString(hash);
//            log.info("jkban生成的token：{}", token);
//        } catch (NoSuchAlgorithmException e) {
//            throw new RuntimeException("SHA-256 algorithm not found", e);
//        }

        JkbanDetailVO jkbanDetailVO = new JkbanDetailVO();
        // 请求即刻办
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("content-type", "application/json;charset=utf-8");

            String requestUrl = jkbanHost + "/v5/inner/query/online-detail" +
                    "?transid=" + transid + "&token=" + token + "&wrkfmShowSwftno=" + jkbanOrder.getOrderId()
                    + "&provCode=" + jkbanOrder.getOrderProviceCode();
            log.info("请求jkban工单详情requestURL:{}",JSON.toJSONString(requestUrl));
            RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
            ResponseEntity<String> response = restTemplateHttps.exchange(requestUrl, HttpMethod.GET, new HttpEntity<>(headers), String.class);
            log.info("请求jkban工单详情response:{}",JSON.toJSONString(response));
            JkbanDetailReponse jkbanDetailReponse = JSON.parseObject(response.getBody(), JkbanDetailReponse.class);
            if(!jkbanDetailReponse.getStatus().equals("0")){
                log.info("请求jkban工单详情接口失败:{}", jkbanDetailReponse.getMessage());
                throw new BusinessException(PARAM_ERROR, jkbanDetailReponse.getMessage());
            }else{
                BeanUtils.copyProperties(jkbanDetailReponse.getResult().get(0).getBean(),jkbanDetailVO);
                jkbanOrder.setOrderStatus(jkbanDetailReponse.getResult().get(0).getBean().getBean().getWrkfmSts());
                jkbanOrder.setUpdateTime(new Date());
                jkbanOrderMapper.updateByPrimaryKeySelective(jkbanOrder);
            }
        }catch (Exception e) {
            throw new BusinessException(PARAM_ERROR, e.getMessage());
        }

        jkbanDetailVO.setJkbanOrder(jkbanOrder);
        return BaseAnswer.success(jkbanDetailVO);
    }

    @Override
    public BaseAnswer<List<JkbanProvinceListVO>> getJkbanProvinceList() {
        List<JkbanProvinceInfo> allProvinces = jkbanProvinceInfoMapper.selectByExample(new JkbanProvinceInfoExample());
        // 1. 按省分组
        Map<String, List<JkbanProvinceInfo>> provinceMap = allProvinces.stream()
                .collect(Collectors.groupingBy(JkbanProvinceInfo::getProvinceCode));

        List<JkbanProvinceListVO> result = new ArrayList<>();

        for (Map.Entry<String, List<JkbanProvinceInfo>> entry : provinceMap.entrySet()) {
            List<JkbanProvinceInfo> provinceAreas = entry.getValue();
            if (provinceAreas.isEmpty()) continue;

            // 2. 判断是否为直辖市（省编码和市编码相同）
            JkbanProvinceInfo firstArea = provinceAreas.get(0);
            boolean isMunicipality = firstArea.getProvinceName().equals(firstArea.getCityName());

            // 3. 创建省DTO
            JkbanProvinceListVO province = new JkbanProvinceListVO();
            province.setProvinceCode(firstArea.getProvinceCode());
            province.setProvinceName(firstArea.getProvinceName());
            province.setCities(new ArrayList<>());

            if (isMunicipality) {
                // 4. 处理直辖市（单个市包含多个区）
                JkbanProvinceListVO.CityDTO city = new JkbanProvinceListVO.CityDTO();
                city.setCityCode(firstArea.getCityCode());
                city.setCityName(firstArea.getCityName());
                city.setRegions(new ArrayList<>());
                // 添加所有区（过滤掉null或空值）
                for (JkbanProvinceInfo area : provinceAreas) {
                    if (area.getRegionCode() != null && !area.getRegionCode().isEmpty()) {
                        JkbanProvinceListVO.RegionDTO region = new JkbanProvinceListVO.RegionDTO();
                        region.setRegionCode(area.getRegionCode());
                        region.setRegionName(area.getRegionName());
                        city.getRegions().add(region);
                    }
                }
                province.getCities().add(city);
            } else {
                // 5. 处理非直辖市（省包含多个市，每个市没有区信息）

                for (JkbanProvinceInfo area : provinceAreas) {
                    if (area.getCityCode() != null && !area.getCityName().isEmpty()) {
                        JkbanProvinceListVO.CityDTO city = new JkbanProvinceListVO.CityDTO();
                        city.setCityCode(area.getCityCode());
                        city.setCityName(area.getCityName());
                        province.getCities().add(city);
                    }
                }
            }
            result.add(province);
        }

        
        return BaseAnswer.success(result);
    }

    @Override
    public BaseAnswer<List<JkbanQuestionInfo>> getJkbanQuestionList() {

        return BaseAnswer.success(jkbanQuestionInfoMapper.selectByExample(new JkbanQuestionInfoExample()));
    }

    /**
     *@Description: 应收系统返回token
     */
    public String getJkbanToken() {
        String token = (String) redisTemplate.opsForValue().get(Constant.REDIS_KEY_JKBAN_TOKEN);
        if (null == token) {
            Random random = new Random();
            String randomDigits = String.format("%08d", random.nextInt(100000000));
            String transid = jkbanAppId +
                    new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) +
                    randomDigits;
            try {
                HttpHeaders headers = new HttpHeaders();
                headers.add("content-type", "application/json;charset=utf-8");

                String requestUrl = jkbanHost + "/v5/ec/get/token" +
                        "?appid=" + jkbanAppId + "&password=" + jkbanPwd + "&transid=" + transid;
                log.info("请求jkban获取TokenrequestURL:{}",JSON.toJSONString(requestUrl));
                RestTemplate restTemplateHttps = new RestTemplate(RestTemplateConfig.generateHttpRequestFactory());
                ResponseEntity<String> response = restTemplateHttps.exchange(requestUrl, HttpMethod.GET, new HttpEntity<>(headers), String.class);
                log.info("请求jkban获取Tokenresponse:{}",JSON.toJSONString(response));
                JkbanTokenReponse jkbanTokenReponse = JSON.parseObject(response.getBody(), JkbanTokenReponse.class);
                if(!jkbanTokenReponse.getStatus().equals("0")){
                    log.info("请求jkban获取Token接口失败:{}", jkbanTokenReponse.getMessage());
                    throw new BusinessException(PARAM_ERROR, jkbanTokenReponse.getMessage());
                }else{
                    token = jkbanTokenReponse.getResult().get(0).getToken();
                    redisTemplate.opsForValue().setIfAbsent(Constant.REDIS_KEY_JKBAN_TOKEN, token, 55, TimeUnit.MINUTES);
                }
            } catch (Exception e) {
                throw new BusinessException(StatusConstant.INTERNAL_ERROR, e.getMessage());
            }
        }
        return token;
    }
}