package com.chinamobile.retail.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.retail.pojo.entity.JkbanQuestionInfo;
import com.chinamobile.retail.pojo.param.miniprogram.CreateJkbanParam;
import com.chinamobile.retail.pojo.param.miniprogram.JkbanListParam;
import com.chinamobile.retail.pojo.vo.miniprogram.JkbanDetailVO;
import com.chinamobile.retail.pojo.vo.miniprogram.JkbanListVO;
import com.chinamobile.retail.pojo.vo.miniprogram.JkbanProvinceListVO;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface IMiniProgramJkbanService {
    BaseAnswer<PageInfo<JkbanListVO>> getJkbanList(JkbanListParam param,  LoginIfo4Redis loginIfo4Redis);
    BaseAnswer<Void> createJkban(CreateJkbanParam param, LoginIfo4Redis loginIfo4Redis);
    BaseAnswer<JkbanDetailVO> getJkbanDetail(String id);

    BaseAnswer<List<JkbanProvinceListVO>> getJkbanProvinceList();

    BaseAnswer<List<JkbanQuestionInfo>> getJkbanQuestionList();
}