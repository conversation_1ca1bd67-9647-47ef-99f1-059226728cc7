package com.chinamobile.retail.service;

import com.chinamobile.iot.sc.common.BaseAnswer;
import com.chinamobile.iot.sc.mode.LoginIfo4Redis;
import com.chinamobile.iot.sc.mode.PageData;
import com.chinamobile.retail.pojo.dto.Order2CInfoDTO;
import com.chinamobile.retail.pojo.entity.MiniProgramActivity;
import com.chinamobile.retail.pojo.entity.MiniProgramActivityWeeklyFunAward;
import com.chinamobile.retail.pojo.param.miniprogram.*;
import com.chinamobile.retail.pojo.vo.miniprogram.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16 09:43
 * @description TODO
 */
public interface IMiniActivityService {

    void publishRankActivity(PublishRankActivityParam param, String userId);

    void publishWeeklyActivity(PublishWeeklyActivityParam param, String userId,LoginIfo4Redis loginIfo4Redis);

    ActivityDetailVO getActivityDetail(String activityId, Integer flag, String userId, LoginIfo4Redis loginIfo4Redis);

    PageData<PageActivityVO> pageActivityList(PageActivityListParam param,LoginIfo4Redis loginIfo4Redis);

    PageData<ActivityDataRankVO> getDataRankList(ActivityDataRankParam param,String userId);
    PageData<ActivityDataRankMiniprogramVO> getDataRankListMiniProgram(ActivityDataRankParam param,String userId);
    void activate(ActivityActivateParam param, String userId);
    void offline(String id, String userId);
    void audit(ActivityAuditParam param, LoginIfo4Redis loginIfo4Redis);

    BaseAnswer<ActivityStatisticsVO> getRankActivityStatistics(String activityId,LoginIfo4Redis loginIfo4Redis);

    PageData<PageActivityVO> getListFrontPage(PageActivityListFrontParam param,LoginIfo4Redis loginIfo4Redis);
    PageFrontRankDetailVO getFrontPageRankDetail(String activityId,String userId) throws Exception;
    PageFrontWeeklyDetailVO getFrontPageWeeklyDetail(String activityId,String userId) throws Exception;
    MiniProgramActivityWeeklyFunAward weeklyLottery(String activityId,  String userId);
    BaseAnswer<PageData<ActivityAwardListVO>> getOwnAward(Integer pageNum,Integer pageSize, String userId);
    BaseAnswer<Integer> getOwnAwardNotReceived(String userId);
    BaseAnswer<Void> choiceAwardAddress(String activityId,String awardId,String addressId , String userId);
    BaseAnswer<Void> deliveryAward(String activityId,String awardId , String logisticsCode,String userId);
    BaseAnswer<Void> deleteDataRank(String id,String userId);
    void exportDataRank(String activityId,String userId);
    PageData<Order2CInfoDTO> getDataRankOrderInfo(ActivityDataRankParam param, Boolean isLog);

    void exportDataRankOrderInfo(String activityId,String userId);

    void saveRankActivityUsers(MiniProgramActivity activity);
    void saveRankActivityUserAwards(MiniProgramActivity activity);
    List<ActivityAccessVO> getDataRankVisitCount(String activityId,String userId);

    List<ActivityPointSupplierVO> getPointSupplierList();

    List<ActivitySearchSpuVO> searchSpuByCode(String spuCode);
    List<ActivitySearchSpuVO> searchSpuAllByCode(String spuCode);

    List<ActivitySearchUserVO> searchUserByPhone(String phone);

    List<ActivityProvinceVO> getAllRegions();

    List<PageActivityVO> searchMiniActivity(String keyWord, String provinceCode);

    void enrollRank(ActivityEnrollParam param,LoginIfo4Redis loginIfo4Redis);

    void loadAllActivity2Redis();
    void syncLoadUser2Redis(String activityId, String userId);
    void syncLoadActivity2Redis(String activityId);
    void delete(String id);
}
